
@font-face {
    font-family: "Helvetica Neue";
    font-style: normal;
    font-weight: normal;
    src: url("../fonts/HelveticaNeue.eot") format("eot"), url("../fonts/HelveticaNeue.ttf") format("truetype"), url("../fonts/HelveticaNeue.otf") format("otf"), url("../fonts/HelveticaNeue.svg") format("svg");
}
meta.foundation-version {
    font-family: "/5.1.0/";
}
meta.foundation-mq-small {
    font-family: "/only screen and (max-width: 40em)/";
    width: 0;
}
meta.foundation-mq-medium {
    font-family: "/only screen and (min-width:40.063em)/";
    width: 40.063em;
}
meta.foundation-mq-large {
    font-family: "/only screen and (min-width:64.063em)/";
    width: 64.063em;
}
meta.foundation-mq-xlarge {
    font-family: "/only screen and (min-width:90.063em)/";
    width: 90.063em;
}
meta.foundation-mq-xxlarge {
    font-family: "/only screen and (min-width:120.063em)/";
    width: 120.063em;
}
meta.foundation-data-attribute-namespace {
    font-family: false;
}
*, *::before, *::after {
    box-sizing: border-box;
}
html, body {
    font-size: 100%;
}
body {
    background: white none repeat scroll 0 0;
    color: #222222;
    cursor: default;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    margin: 0;
    padding: 0;
    position: relative;
}
a:hover {
    cursor: pointer;
}
img, object, embed {
    height: auto;
    max-width: 100%;
    outline: medium none;
}
object, embed {
    height: 100%;
}
img {
}
#map_canvas img, #map_canvas embed, #map_canvas object, .map_canvas img, .map_canvas embed, .map_canvas object {
    max-width: none !important;
}
.left {
    float: left !important;
}
.right {
    float: right !important;
}
.clearfix {
}
.clearfix::before, .clearfix::after {
    content: " ";
    display: table;
}
.clearfix::after {
    clear: both;
}
.hide {
    display: none;
}
.antialiased {
}
img {
    display: inline-block;
    vertical-align: middle;
}
textarea {
    height: auto;
    min-height: 50px;
}
select {
    width: 100%;
}
.row {
    margin: 0 auto;
    max-width: 62.5rem;
    width: 100%;
}
.row::before, .row::after {
    content: " ";
    display: table;
}
.row::after {
    clear: both;
}
.row.collapse > .column, .row.collapse > .columns {
    float: left;
    padding-left: 0;
    padding-right: 0;
}
.row.collapse .row {
    margin-left: 0;
    margin-right: 0;
}
.row .row {
    margin: 0 -0.9375rem;
    max-width: none;
    width: auto;
}
.row .row::before, .row .row::after {
    content: " ";
    display: table;
}
.row .row::after {
    clear: both;
}
.row .row.collapse {
    margin: 0;
    max-width: none;
    width: auto;
}
.row .row.collapse::before, .row .row.collapse::after {
    content: " ";
    display: table;
}
.row .row.collapse::after {
    clear: both;
}
.column, .columns {
    float: left;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    width: 100%;
}
@media only screen {
.column.small-centered, .columns.small-centered {
    float: none;
    margin-left: auto;
    margin-right: auto;
}
.column.small-uncentered, .columns.small-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
}
.column.small-uncentered.opposite, .columns.small-uncentered.opposite {
    float: right;
}
.small-push-0 {
    left: 0;
    right: auto;
}
.small-pull-0 {
    left: auto;
    right: 0;
}
.small-push-1 {
    left: 8.33333%;
    right: auto;
}
.small-pull-1 {
    left: auto;
    right: 8.33333%;
}
.small-push-2 {
    left: 16.6667%;
    right: auto;
}
.small-pull-2 {
    left: auto;
    right: 16.6667%;
}
.small-push-3 {
    left: 25%;
    right: auto;
}
.small-pull-3 {
    left: auto;
    right: 25%;
}
.small-push-4 {
    left: 33.3333%;
    right: auto;
}
.small-pull-4 {
    left: auto;
    right: 33.3333%;
}
.small-push-5 {
    left: 41.6667%;
    right: auto;
}
.small-pull-5 {
    left: auto;
    right: 41.6667%;
}
.small-push-6 {
    left: 50%;
    right: auto;
}
.small-pull-6 {
    left: auto;
    right: 50%;
}
.small-push-7 {
    left: 58.3333%;
    right: auto;
}
.small-pull-7 {
    left: auto;
    right: 58.3333%;
}
.small-push-8 {
    left: 66.6667%;
    right: auto;
}
.small-pull-8 {
    left: auto;
    right: 66.6667%;
}
.small-push-9 {
    left: 75%;
    right: auto;
}
.small-pull-9 {
    left: auto;
    right: 75%;
}
.small-push-10 {
    left: 83.3333%;
    right: auto;
}
.small-pull-10 {
    left: auto;
    right: 83.3333%;
}
.small-push-11 {
    left: 91.6667%;
    right: auto;
}
.small-pull-11 {
    left: auto;
    right: 91.6667%;
}
.column, .columns {
    float: left;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    position: relative;
}
.small-1 {
    width: 8.33333%;
}
.small-2 {
    width: 16.6667%;
}
.small-3 {
    width: 25%;
}
.small-4 {
    width: 33.3333%;
}
.small-5 {
    width: 41.6667%;
}
.small-6 {
    width: 50%;
}
.small-7 {
    width: 58.3333%;
}
.small-8 {
    width: 66.6667%;
}
.small-9 {
    width: 75%;
}
.small-10 {
    width: 83.3333%;
}
.small-11 {
    width: 91.6667%;
}
.small-12 {
    width: 100%;
}
[class*="column"] + [class*="column"]:last-child {
    float: right;
}
[class*="column"] + .end[class*="column"] {
    float: left;
}
.small-offset-0 {
    margin-left: 0 !important;
}
.small-offset-1 {
    margin-left: 8.33333% !important;
}
.small-offset-2 {
    margin-left: 16.6667% !important;
}
.small-offset-3 {
    margin-left: 25% !important;
}
.small-offset-4 {
    margin-left: 33.3333% !important;
}
.small-offset-5 {
    margin-left: 41.6667% !important;
}
.small-offset-6 {
    margin-left: 50% !important;
}
.small-offset-7 {
    margin-left: 58.3333% !important;
}
.small-offset-8 {
    margin-left: 66.6667% !important;
}
.small-offset-9 {
    margin-left: 75% !important;
}
.small-offset-10 {
    margin-left: 83.3333% !important;
}
.small-offset-11 {
    margin-left: 91.6667% !important;
}
.small-reset-order, .small-reset-order {
    float: left;
    left: auto;
    margin-left: 0;
    margin-right: 0;
    right: auto;
}
}
@media only screen and (min-width: 40.063em) {
.column.medium-centered, .columns.medium-centered {
    float: none;
    margin-left: auto;
    margin-right: auto;
}
.column.medium-uncentered, .columns.medium-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
}
.column.medium-uncentered.opposite, .columns.medium-uncentered.opposite {
    float: right;
}
.medium-push-0 {
    left: 0;
    right: auto;
}
.medium-pull-0 {
    left: auto;
    right: 0;
}
.medium-push-1 {
    left: 8.33333%;
    right: auto;
}
.medium-pull-1 {
    left: auto;
    right: 8.33333%;
}
.medium-push-2 {
    left: 16.6667%;
    right: auto;
}
.medium-pull-2 {
    left: auto;
    right: 16.6667%;
}
.medium-push-3 {
    left: 25%;
    right: auto;
}
.medium-pull-3 {
    left: auto;
    right: 25%;
}
.medium-push-4 {
    left: 33.3333%;
    right: auto;
}
.medium-pull-4 {
    left: auto;
    right: 33.3333%;
}
.medium-push-5 {
    left: 41.6667%;
    right: auto;
}
.medium-pull-5 {
    left: auto;
    right: 41.6667%;
}
.medium-push-6 {
    left: 50%;
    right: auto;
}
.medium-pull-6 {
    left: auto;
    right: 50%;
}
.medium-push-7 {
    left: 58.3333%;
    right: auto;
}
.medium-pull-7 {
    left: auto;
    right: 58.3333%;
}
.medium-push-8 {
    left: 66.6667%;
    right: auto;
}
.medium-pull-8 {
    left: auto;
    right: 66.6667%;
}
.medium-push-9 {
    left: 75%;
    right: auto;
}
.medium-pull-9 {
    left: auto;
    right: 75%;
}
.medium-push-10 {
    left: 83.3333%;
    right: auto;
}
.medium-pull-10 {
    left: auto;
    right: 83.3333%;
}
.medium-push-11 {
    left: 91.6667%;
    right: auto;
}
.medium-pull-11 {
    left: auto;
    right: 91.6667%;
}
.column, .columns {
    float: left;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    position: relative;
}
.medium-1 {
    width: 8.33333%;
}
.medium-2 {
    width: 16.6667%;
}
.medium-3 {
    width: 25%;
}
.medium-4 {
    width: 33.3333%;
}
.medium-5 {
    width: 41.6667%;
}
.medium-6 {
    width: 50%;
}
.medium-7 {
    width: 58.3333%;
}
.medium-8 {
    width: 66.6667%;
}
.medium-9 {
    width: 75%;
}
.medium-10 {
    width: 83.3333%;
}
.medium-11 {
    width: 91.6667%;
}
.medium-12 {
    width: 100%;
}
[class*="column"] + [class*="column"]:last-child {
    float: right;
}
[class*="column"] + .end[class*="column"] {
    float: left;
}
.medium-offset-0 {
    margin-left: 0 !important;
}
.medium-offset-1 {
    margin-left: 8.33333% !important;
}
.medium-offset-2 {
    margin-left: 16.6667% !important;
}
.medium-offset-3 {
    margin-left: 25% !important;
}
.medium-offset-4 {
    margin-left: 33.3333% !important;
}
.medium-offset-5 {
    margin-left: 41.6667% !important;
}
.medium-offset-6 {
    margin-left: 50% !important;
}
.medium-offset-7 {
    margin-left: 58.3333% !important;
}
.medium-offset-8 {
    margin-left: 66.6667% !important;
}
.medium-offset-9 {
    margin-left: 75% !important;
}
.medium-offset-10 {
    margin-left: 83.3333% !important;
}
.medium-offset-11 {
    margin-left: 91.6667% !important;
}
.medium-reset-order, .medium-reset-order {
    float: left;
    left: auto;
    margin-left: 0;
    margin-right: 0;
    right: auto;
}
.push-0 {
    left: 0;
    right: auto;
}
.pull-0 {
    left: auto;
    right: 0;
}
.push-1 {
    left: 8.33333%;
    right: auto;
}
.pull-1 {
    left: auto;
    right: 8.33333%;
}
.push-2 {
    left: 16.6667%;
    right: auto;
}
.pull-2 {
    left: auto;
    right: 16.6667%;
}
.push-3 {
    left: 25%;
    right: auto;
}
.pull-3 {
    left: auto;
    right: 25%;
}
.push-4 {
    left: 33.3333%;
    right: auto;
}
.pull-4 {
    left: auto;
    right: 33.3333%;
}
.push-5 {
    left: 41.6667%;
    right: auto;
}
.pull-5 {
    left: auto;
    right: 41.6667%;
}
.push-6 {
    left: 50%;
    right: auto;
}
.pull-6 {
    left: auto;
    right: 50%;
}
.push-7 {
    left: 58.3333%;
    right: auto;
}
.pull-7 {
    left: auto;
    right: 58.3333%;
}
.push-8 {
    left: 66.6667%;
    right: auto;
}
.pull-8 {
    left: auto;
    right: 66.6667%;
}
.push-9 {
    left: 75%;
    right: auto;
}
.pull-9 {
    left: auto;
    right: 75%;
}
.push-10 {
    left: 83.3333%;
    right: auto;
}
.pull-10 {
    left: auto;
    right: 83.3333%;
}
.push-11 {
    left: 91.6667%;
    right: auto;
}
.pull-11 {
    left: auto;
    right: 91.6667%;
}
}
@media only screen and (min-width: 64.063em) {
.column.large-centered, .columns.large-centered {
    float: none;
    margin-left: auto;
    margin-right: auto;
}
.column.large-uncentered, .columns.large-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
}
.column.large-uncentered.opposite, .columns.large-uncentered.opposite {
    float: right;
}
.large-push-0 {
    left: 0;
    right: auto;
}
.large-pull-0 {
    left: auto;
    right: 0;
}
.large-push-1 {
    left: 8.33333%;
    right: auto;
}
.large-pull-1 {
    left: auto;
    right: 8.33333%;
}
.large-push-2 {
    left: 16.6667%;
    right: auto;
}
.large-pull-2 {
    left: auto;
    right: 16.6667%;
}
.large-push-3 {
    left: 25%;
    right: auto;
}
.large-pull-3 {
    left: auto;
    right: 25%;
}
.large-push-4 {
    left: 33.3333%;
    right: auto;
}
.large-pull-4 {
    left: auto;
    right: 33.3333%;
}
.large-push-5 {
    left: 41.6667%;
    right: auto;
}
.large-pull-5 {
    left: auto;
    right: 41.6667%;
}
.large-push-6 {
    left: 50%;
    right: auto;
}
.large-pull-6 {
    left: auto;
    right: 50%;
}
.large-push-7 {
    left: 58.3333%;
    right: auto;
}
.large-pull-7 {
    left: auto;
    right: 58.3333%;
}
.large-push-8 {
    left: 66.6667%;
    right: auto;
}
.large-pull-8 {
    left: auto;
    right: 66.6667%;
}
.large-push-9 {
    left: 75%;
    right: auto;
}
.large-pull-9 {
    left: auto;
    right: 75%;
}
.large-push-10 {
    left: 83.3333%;
    right: auto;
}
.large-pull-10 {
    left: auto;
    right: 83.3333%;
}
.large-push-11 {
    left: 91.6667%;
    right: auto;
}
.large-pull-11 {
    left: auto;
    right: 91.6667%;
}
.column, .columns {
    float: left;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    position: relative;
}
.large-1 {
    width: 8.33333%;
}
.large-2 {
    width: 16.6667%;
}
.large-3 {
    width: 25%;
}
.large-4 {
    width: 33.3333%;
}
.large-5 {
    width: 41.6667%;
}
.large-6 {
    width: 50%;
}
.large-7 {
    width: 58.3333%;
}
.large-8 {
    width: 66.6667%;
}
.large-9 {
    width: 75%;
}
.large-10 {
    width: 83.3333%;
}
.large-11 {
    width: 91.6667%;
}
.large-12 {
    width: 100%;
}
[class*="column"] + [class*="column"]:last-child {
    float: right;
}
[class*="column"] + .end[class*="column"] {
    float: left;
}
.large-offset-0 {
    margin-left: 0 !important;
}
.large-offset-1 {
    margin-left: 8.33333% !important;
}
.large-offset-2 {
    margin-left: 16.6667% !important;
}
.large-offset-3 {
    margin-left: 25% !important;
}
.large-offset-4 {
    margin-left: 33.3333% !important;
}
.large-offset-5 {
    margin-left: 41.6667% !important;
}
.large-offset-6 {
    margin-left: 50% !important;
}
.large-offset-7 {
    margin-left: 58.3333% !important;
}
.large-offset-8 {
    margin-left: 66.6667% !important;
}
.large-offset-9 {
    margin-left: 75% !important;
}
.large-offset-10 {
    margin-left: 83.3333% !important;
}
.large-offset-11 {
    margin-left: 91.6667% !important;
}
.large-reset-order, .large-reset-order {
    float: left;
    left: auto;
    margin-left: 0;
    margin-right: 0;
    right: auto;
}
.push-0 {
    left: 0;
    right: auto;
}
.pull-0 {
    left: auto;
    right: 0;
}
.push-1 {
    left: 8.33333%;
    right: auto;
}
.pull-1 {
    left: auto;
    right: 8.33333%;
}
.push-2 {
    left: 16.6667%;
    right: auto;
}
.pull-2 {
    left: auto;
    right: 16.6667%;
}
.push-3 {
    left: 25%;
    right: auto;
}
.pull-3 {
    left: auto;
    right: 25%;
}
.push-4 {
    left: 33.3333%;
    right: auto;
}
.pull-4 {
    left: auto;
    right: 33.3333%;
}
.push-5 {
    left: 41.6667%;
    right: auto;
}
.pull-5 {
    left: auto;
    right: 41.6667%;
}
.push-6 {
    left: 50%;
    right: auto;
}
.pull-6 {
    left: auto;
    right: 50%;
}
.push-7 {
    left: 58.3333%;
    right: auto;
}
.pull-7 {
    left: auto;
    right: 58.3333%;
}
.push-8 {
    left: 66.6667%;
    right: auto;
}
.pull-8 {
    left: auto;
    right: 66.6667%;
}
.push-9 {
    left: 75%;
    right: auto;
}
.pull-9 {
    left: auto;
    right: 75%;
}
.push-10 {
    left: 83.3333%;
    right: auto;
}
.pull-10 {
    left: auto;
    right: 83.3333%;
}
.push-11 {
    left: 91.6667%;
    right: auto;
}
.pull-11 {
    left: auto;
    right: 91.6667%;
}
}
meta.foundation-mq-topbar {
    font-family: "/only screen and (min-width:40.063em)/";
    width: 40.063em;
}
.contain-to-grid {
    background: #333333 none repeat scroll 0 0;
    width: 100%;
}
.contain-to-grid .top-bar {
    margin-bottom: 0;
}
.fixed {
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 99;
}
.fixed.expanded:not(.top-bar) {
    height: auto;
    max-height: 100%;
    overflow-y: auto;
    width: 100%;
}
.fixed.expanded:not(.top-bar) .title-area {
    position: fixed;
    width: 100%;
    z-index: 99;
}
.fixed.expanded:not(.top-bar) .top-bar-section {
    margin-top: 45px;
    z-index: 98;
}
.top-bar {
    background: #333333 none repeat scroll 0 0;
    height: 45px;
    line-height: 45px;
    margin-bottom: 0;
    overflow: hidden;
    position: relative;
}
.top-bar ul {
    list-style: outside none none;
    margin-bottom: 0;
}
.top-bar .row {
    max-width: none;
}
.top-bar form, .top-bar input {
    margin-bottom: 0;
}
.top-bar input {
    font-size: 0.75rem;
    height: auto;
    padding-bottom: 0.35rem;
    padding-top: 0.35rem;
}
.top-bar .button {
    font-size: 0.75rem;
    margin-bottom: 0;
    padding-bottom: 0.35rem;
    padding-top: 0.45rem;
}
.top-bar .title-area {
    margin: 0;
    position: relative;
}
.top-bar .name {
    font-size: 16px;
    height: 45px;
    margin: 0;
}
.top-bar .name h1 {
    font-size: 1.0625rem;
    line-height: 45px;
    margin: 0;
}
.top-bar .name h1 a {
    color: white;
    display: block;
    font-weight: normal;
    padding: 0 15px;
    width: 50%;
}
.top-bar .toggle-topbar {
    position: absolute;
    right: 0;
    top: 0;
}
.top-bar .toggle-topbar a {
    color: white;
    display: block;
    font-size: 0.8125rem;
    font-weight: bold;
    height: 45px;
    line-height: 45px;
    padding: 0 15px;
    position: relative;
    text-transform: uppercase;
}
.top-bar .toggle-topbar.menu-icon {
    margin-top: -16px;
    padding-left: 40px;
    right: 15px;
    top: 50%;
}
.top-bar .toggle-topbar.menu-icon a {
    color: white;
    height: 34px;
    line-height: 33px;
    padding: 0 25px 0 0;
    position: relative;
}
.top-bar .toggle-topbar.menu-icon a::after {
    box-shadow: 0 10px 0 1px white, 0 16px 0 1px white, 0 22px 0 1px white;
    content: "";
    display: block;
    height: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 16px;
}
.top-bar.expanded {
    background: transparent none repeat scroll 0 0;
    height: auto;
}
.top-bar.expanded .title-area {
    background: #333333 none repeat scroll 0 0;
}
.top-bar.expanded .toggle-topbar a {
    color: #888888;
}
.top-bar.expanded .toggle-topbar a span {
    box-shadow: 0 10px 0 1px #888888, 0 16px 0 1px #888888, 0 22px 0 1px #888888;
}
.top-bar-section {
    left: 0;
    position: relative;
    transition: left 300ms ease-out 0s;
    width: auto;
}
.top-bar-section ul {
    background: #333333 none repeat scroll 0 0;
    display: block;
    font-size: 16px;
    height: auto;
    margin: 0;
    width: 100%;
}
.top-bar-section .divider, .top-bar-section [role="separator"] {
    border-top: 1px solid #1a1a1a;
    clear: both;
    height: 1px;
    width: 100%;
}
.top-bar-section ul li > a {
    background: #333333 none repeat scroll 0 0;
    color: white;
    display: block;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 0.8125rem;
    font-weight: normal;
    padding: 12px 0 12px 15px;
    width: 100%;
}
.top-bar-section ul li > a.button {
    background: #008cba none repeat scroll 0 0;
    font-size: 0.8125rem;
    padding-left: 15px;
    padding-right: 15px;
}
.top-bar-section ul li > a.button:hover {
    background: #006688 none repeat scroll 0 0;
}
.top-bar-section ul li > a.button.secondary {
    background: #e7e7e7 none repeat scroll 0 0;
}
.top-bar-section ul li > a.button.secondary:hover {
    background: #cecece none repeat scroll 0 0;
}
.top-bar-section ul li > a.button.success {
    background: #43ac6a none repeat scroll 0 0;
}
.top-bar-section ul li > a.button.success:hover {
    background: #358854 none repeat scroll 0 0;
}
.top-bar-section ul li > a.button.alert {
    background: #f04124 none repeat scroll 0 0;
}
.top-bar-section ul li > a.button.alert:hover {
    background: #d42b0f none repeat scroll 0 0;
}
.top-bar-section ul li:hover > a {
    background: #272727 none repeat scroll 0 0;
    color: white;
}
.top-bar-section ul li.active > a {
    background: #008cba none repeat scroll 0 0;
    color: white;
}
.top-bar-section ul li.active > a:hover {
    background: #0078a0 none repeat scroll 0 0;
    color: white;
}
.top-bar-section .has-form {
    padding: 15px;
}
.top-bar-section .has-dropdown {
    position: relative;
}
.top-bar-section .has-dropdown > a::after {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: transparent transparent transparent rgba(255, 255, 255, 0.4);
    border-image: none;
    border-style: inset inset inset solid;
    border-width: 5px;
    content: "";
    display: block;
    height: 0;
    margin-right: 15px;
    margin-top: -4.5px;
    position: absolute;
    right: 0;
    top: 50%;
    width: 0;
}
.top-bar-section .has-dropdown.moved {
    position: static;
}
.top-bar-section .has-dropdown.moved > .dropdown {
    display: block;
}
.top-bar-section .dropdown {
    display: none;
    left: 100%;
    position: absolute;
    top: 0;
    z-index: 99;
}
.top-bar-section .dropdown li {
    height: auto;
    width: 100%;
}
.top-bar-section .dropdown li a {
    font-weight: normal;
    padding: 8px 15px;
}
.top-bar-section .dropdown li a.parent-link {
    font-weight: normal;
}
.top-bar-section .dropdown li.title h5 {
    margin-bottom: 0;
}
.top-bar-section .dropdown li.title h5 a {
    color: white;
    display: block;
    line-height: 22.5px;
}
.top-bar-section .dropdown li.has-form {
    padding: 8px 15px;
}
.top-bar-section .dropdown li .button {
    top: auto;
}
.top-bar-section .dropdown label {
    color: #777777;
    font-size: 0.625rem;
    font-weight: bold;
    margin-bottom: 0;
    padding: 8px 15px 2px;
    text-transform: uppercase;
}
.js-generated {
    display: block;
}
@media only screen and (min-width: 40.063em) {
.top-bar {
    background: #333333 none repeat scroll 0 0;
    overflow: visible;
}
.top-bar::before, .top-bar::after {
    content: " ";
    display: table;
}
.top-bar::after {
    clear: both;
}
.top-bar .toggle-topbar {
    display: none;
}
.top-bar .title-area {
    float: left;
}
.top-bar .name h1 a {
    width: auto;
}
.top-bar input, .top-bar .button {
    font-size: 0.875rem;
    position: relative;
    top: 7px;
}
.top-bar.expanded {
    background: #333333 none repeat scroll 0 0;
}
.contain-to-grid .top-bar {
    margin: 0 auto;
    max-width: 62.5rem;
}
.top-bar-section {
    left: 0 !important;
}
.top-bar-section ul {
    display: inline;
    height: auto !important;
    width: auto;
}
.top-bar-section ul li {
    float: left;
}
.top-bar-section ul li .js-generated {
    display: none;
}
.top-bar-section li.hover > a:not(.button) {
    background: #272727 none repeat scroll 0 0;
    color: white;
}
.top-bar-section li:not(.has-form) a:not(.button) {
    background: #333333 none repeat scroll 0 0;
    line-height: 45px;
    padding: 0 15px;
}
.top-bar-section li:not(.has-form) a:hover:not(.button) {
    background: #272727 none repeat scroll 0 0;
}
.top-bar-section li.active:not(.has-form) a:not(.button) {
    background: #008cba none repeat scroll 0 0;
    color: white;
    line-height: 45px;
    padding: 0 15px;
}
.top-bar-section li.active:not(.has-form) a:hover:not(.button) {
    background: #0078a0 none repeat scroll 0 0;
}
.top-bar-section .has-dropdown > a {
    padding-right: 35px !important;
}
.top-bar-section .has-dropdown > a::after {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: rgba(255, 255, 255, 0.4) transparent transparent;
    border-image: none;
    border-style: solid inset inset;
    border-width: 5px;
    content: "";
    display: block;
    height: 0;
    margin-top: -2.5px;
    top: 22.5px;
    width: 0;
}
.top-bar-section .has-dropdown.moved {
    position: relative;
}
.top-bar-section .has-dropdown.moved > .dropdown {
    display: none;
}
.top-bar-section .has-dropdown.hover > .dropdown, .top-bar-section .has-dropdown.not-click:hover > .dropdown {
    display: block;
}
.top-bar-section .has-dropdown .dropdown li.has-dropdown > a::after {
    border: medium none;
    content: "»";
    line-height: 1.2;
    margin-top: -2px;
    right: 5px;
    top: 1rem;
}
.top-bar-section .dropdown {
    background: transparent none repeat scroll 0 0;
    left: 0;
    min-width: 100%;
    top: auto;
}
.top-bar-section .dropdown li a {
    background: #333333 none repeat scroll 0 0;
    color: white;
    line-height: 1;
    padding: 12px 15px;
    white-space: nowrap;
}
.top-bar-section .dropdown li label {
    background: #333333 none repeat scroll 0 0;
    white-space: nowrap;
}
.top-bar-section .dropdown li .dropdown {
    left: 100%;
    top: 0;
}
.top-bar-section > ul > .divider, .top-bar-section > ul > [role="separator"] {
    border-bottom: medium none;
    border-right: 1px solid #4e4e4e;
    border-top: medium none;
    clear: none;
    height: 45px;
    width: 0;
}
.top-bar-section .has-form {
    background: #333333 none repeat scroll 0 0;
    height: 45px;
    padding: 0 15px;
}
.top-bar-section .right li .dropdown {
    left: auto;
    right: 0;
}
.top-bar-section .right li .dropdown li .dropdown {
    right: 100%;
}
.top-bar-section .left li .dropdown {
    left: 0;
    right: auto;
}
.top-bar-section .left li .dropdown li .dropdown {
    left: 100%;
}
.no-js .top-bar-section ul li:hover > a {
    background: #272727 none repeat scroll 0 0;
    color: white;
}
.no-js .top-bar-section ul li:active > a {
    background: #008cba none repeat scroll 0 0;
    color: white;
}
.no-js .top-bar-section .has-dropdown:hover > .dropdown {
    display: block;
}
}
.breadcrumbs {
    background-color: #f4f4f4;
    border-color: gainsboro;
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    display: block;
    list-style: outside none none;
    margin-left: 0;
    overflow: hidden;
    padding: 0.5625rem 0.875rem;
}
.breadcrumbs > * {
    float: left;
    font-size: 0.6875rem;
    margin: 0;
    text-transform: uppercase;
}
.breadcrumbs > *:hover a, .breadcrumbs > *:focus a {
    text-decoration: underline;
}
.breadcrumbs > * a, .breadcrumbs > * span {
    color: #008cba;
    text-transform: uppercase;
}
.breadcrumbs > .current {
    color: #333333;
    cursor: default;
}
.breadcrumbs > .current a {
    color: #333333;
    cursor: default;
}
.breadcrumbs > .current:hover, .breadcrumbs > .current:hover a, .breadcrumbs > .current:focus, .breadcrumbs > .current:focus a {
    text-decoration: none;
}
.breadcrumbs > .unavailable {
    color: #999999;
}
.breadcrumbs > .unavailable a {
    color: #999999;
}
.breadcrumbs > .unavailable:hover, .breadcrumbs > .unavailable:hover a, .breadcrumbs > .unavailable:focus, .breadcrumbs > .unavailable a:focus {
    color: #999999;
    cursor: default;
    text-decoration: none;
}
.breadcrumbs > *::before {
    color: #aaaaaa;
    content: "/";
    margin: 0 0.75rem;
    position: relative;
    top: 1px;
}
.breadcrumbs > *:first-child::before {
    content: " ";
    margin: 0;
}
.alert-box {
    background-color: #008cba;
    border-color: #0078a0;
    border-style: solid;
    border-width: 1px;
    color: white;
    display: block;
    font-size: 0.8125rem;
    font-weight: normal;
    margin-bottom: 1.25rem;
    padding: 0.875rem 1.5rem 0.875rem 0.875rem;
    position: relative;
}
.alert-box .close {
    color: #333333;
    font-size: 1.375rem;
    line-height: 0;
    margin-top: -0.6875rem;
    opacity: 0.3;
    padding: 9px 6px 4px;
    position: absolute;
    right: 0.25rem;
    top: 50%;
}
.alert-box .close:hover, .alert-box .close:focus {
    opacity: 0.5;
}
.alert-box.radius {
    border-radius: 3px;
}
.alert-box.round {
    border-radius: 1000px;
}
.alert-box.success {
    background-color: #43ac6a;
    border-color: #3a945b;
    color: white;
}
.alert-box.alert {
    background-color: #f04124;
    border-color: #de2d0f;
    color: white;
}
.alert-box.secondary {
    background-color: #e7e7e7;
    border-color: #c7c7c7;
    color: #4f4f4f;
}
.alert-box.warning {
    background-color: #f08a24;
    border-color: #de770f;
    color: white;
}
.alert-box.info {
    background-color: #a0d3e8;
    border-color: #74bfdd;
    color: #4f4f4f;
}
.inline-list {
    list-style: outside none none;
    margin: 0 0 1.0625rem -1.375rem;
    overflow: hidden;
    padding: 0;
}
.inline-list > li {
    display: block;
    float: left;
    list-style: outside none none;
    margin-left: 1.375rem;
}
.inline-list > li > * {
    display: block;
}
button, .button {
    background-color: #e1671f;
    border: medium none;
    color: white;
    cursor: pointer;
    display: inline-block;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 1rem;
    font-weight: normal !important;
    line-height: normal;
    margin: 0 0 0rem;
    padding: 1.0625rem 2rem 1rem;
    position: relative;
    text-align: center;
    text-decoration: none;
    transition: background-color 300ms ease-out 0s;
}
button:hover, button:focus, .button:hover, .button:focus {
    background-color: #ce5e1c;
}
button:hover, button:focus, .button:hover, .button:focus {
    color: white;
}
button.secondary, .button.secondary {
    background-color: #e7e7e7;
    border-color: #b9b9b9;
    color: #333333;
}
button.secondary:hover, button.secondary:focus, .button.secondary:hover, .button.secondary:focus {
    background-color: #b9b9b9;
}
button.secondary:hover, button.secondary:focus, .button.secondary:hover, .button.secondary:focus {
    color: #333333;
}
button.success, .button.success {
    background-color: #368a55;
    border-color: #368a55;
    color: white;
}
button.success:hover, button.success:focus, .button.success:hover, .button.success:focus {
    background-color: #368a55;
}
button.success:hover, button.success:focus, .button.success:hover, .button.success:focus {
    color: white;
}
button.alert, .button.alert {
    background-color: #f04124;
    border-color: #cf2a0e;
    color: white;
}
button.alert:hover, button.alert:focus, .button.alert:hover, .button.alert:focus {
    background-color: #cf2a0e;
}
button.alert:hover, button.alert:focus, .button.alert:hover, .button.alert:focus {
    color: white;
}
button.large, .button.large {
    font-size: 1.25rem;
    padding: 1.125rem 2.25rem 1.1875rem;
}
button.small, .button.small {
    font-size: 0.8125rem;
    padding: 0.7rem 1.75rem 0.6rem;
}
button.tiny, .button.tiny {
    font-size: 0.6875rem;
    padding: 0.625rem 1.25rem 0.6875rem;
}
button.expand, .button.expand {
    padding-left: 0;
    padding-right: 0;
    width: 100%;
}
button.left-align, .button.left-align {
    text-align: left;
    text-indent: 0.75rem;
}
button.right-align, .button.right-align {
    padding-right: 0.75rem;
    text-align: right;
}
button.radius, .button.radius {
    border-radius: 3px;
}
button.round, .button.round {
    border-radius: 1000px;
}
button.disabled, button[disabled], .button.disabled, .button[disabled] {
    background-color: #008cba;
    border-color: #007095;
    box-shadow: none;
    color: white;
    cursor: default;
    opacity: 0.7;
}
button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
    background-color: #007095;
}
button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
    color: white;
}
button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
    background-color: #008cba;
}
button.disabled.secondary, button.secondary[disabled], .button.disabled.secondary, .button.secondary[disabled] {
    background-color: #e7e7e7;
    border-color: #b9b9b9;
    box-shadow: none;
    color: #333333;
    cursor: default;
    opacity: 0.7;
}
button.disabled.secondary:hover, button.disabled.secondary:focus, button.secondary[disabled]:hover, button.secondary[disabled]:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button.secondary[disabled]:hover, .button.secondary[disabled]:focus {
    background-color: #b9b9b9;
}
button.disabled.secondary:hover, button.disabled.secondary:focus, button.secondary[disabled]:hover, button.secondary[disabled]:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button.secondary[disabled]:hover, .button.secondary[disabled]:focus {
    color: #333333;
}
button.disabled.secondary:hover, button.disabled.secondary:focus, button.secondary[disabled]:hover, button.secondary[disabled]:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button.secondary[disabled]:hover, .button.secondary[disabled]:focus {
    background-color: #e7e7e7;
}
button.disabled.success, button.success[disabled], .button.disabled.success, .button.success[disabled] {
    background-color: #43ac6a;
    border-color: #368a55;
    box-shadow: none;
    color: white;
    cursor: default;
    opacity: 0.7;
}
button.disabled.success:hover, button.disabled.success:focus, button.success[disabled]:hover, button.success[disabled]:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button.success[disabled]:hover, .button.success[disabled]:focus {
    background-color: #368a55;
}
button.disabled.success:hover, button.disabled.success:focus, button.success[disabled]:hover, button.success[disabled]:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button.success[disabled]:hover, .button.success[disabled]:focus {
    color: white;
}
button.disabled.success:hover, button.disabled.success:focus, button.success[disabled]:hover, button.success[disabled]:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button.success[disabled]:hover, .button.success[disabled]:focus {
    background-color: #43ac6a;
}
button.disabled.alert, button.alert[disabled], .button.disabled.alert, .button.alert[disabled] {
    background-color: #f04124;
    border-color: #cf2a0e;
    box-shadow: none;
    color: white;
    cursor: default;
    opacity: 0.7;
}
button.disabled.alert:hover, button.disabled.alert:focus, button.alert[disabled]:hover, button.alert[disabled]:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button.alert[disabled]:hover, .button.alert[disabled]:focus {
    background-color: #cf2a0e;
}
button.disabled.alert:hover, button.disabled.alert:focus, button.alert[disabled]:hover, button.alert[disabled]:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button.alert[disabled]:hover, .button.alert[disabled]:focus {
    color: white;
}
button.disabled.alert:hover, button.disabled.alert:focus, button.alert[disabled]:hover, button.alert[disabled]:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button.alert[disabled]:hover, .button.alert[disabled]:focus {
    background-color: #f04124;
}
@media only screen and (min-width: 40.063em) {
button, .button {
    display: inline-block;
}
}
.button-group {
    left: 0;
    list-style: outside none none;
    margin: 0;
}
.button-group::before, .button-group::after {
    content: " ";
    display: table;
}
.button-group::after {
    clear: both;
}
.button-group li {
    float: left;
    margin: 0;
}
.button-group li > button, .button-group li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group li:first-child button, .button-group li:first-child .button {
    border-left: 0 none;
}
.button-group li:first-child {
    margin-left: 0;
}
.button-group.radius > * > button, .button-group.radius > * .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.radius > *:first-child button, .button-group.radius > *:first-child .button {
    border-left: 0 none;
}
.button-group.radius > *:first-child, .button-group.radius > *:first-child > a, .button-group.radius > *:first-child > button, .button-group.radius > *:first-child > .button {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
}
.button-group.radius > *:last-child, .button-group.radius > *:last-child > a, .button-group.radius > *:last-child > button, .button-group.radius > *:last-child > .button {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}
.button-group.round > * > button, .button-group.round > * .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.round > *:first-child button, .button-group.round > *:first-child .button {
    border-left: 0 none;
}
.button-group.round > *:first-child, .button-group.round > *:first-child > a, .button-group.round > *:first-child > button, .button-group.round > *:first-child > .button {
    border-bottom-left-radius: 1000px;
    border-top-left-radius: 1000px;
}
.button-group.round > *:last-child, .button-group.round > *:last-child > a, .button-group.round > *:last-child > button, .button-group.round > *:last-child > .button {
    border-bottom-right-radius: 1000px;
    border-top-right-radius: 1000px;
}
.button-group.even-2 li {
    width: 50%;
}
.button-group.even-2 li > button, .button-group.even-2 li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.even-2 li:first-child button, .button-group.even-2 li:first-child .button {
    border-left: 0 none;
}
.button-group.even-2 li button, .button-group.even-2 li .button {
    width: 100%;
}
.button-group.even-3 li {
    width: 33.3333%;
}
.button-group.even-3 li > button, .button-group.even-3 li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.even-3 li:first-child button, .button-group.even-3 li:first-child .button {
    border-left: 0 none;
}
.button-group.even-3 li button, .button-group.even-3 li .button {
    width: 100%;
}
.button-group.even-4 li {
    width: 25%;
}
.button-group.even-4 li > button, .button-group.even-4 li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.even-4 li:first-child button, .button-group.even-4 li:first-child .button {
    border-left: 0 none;
}
.button-group.even-4 li button, .button-group.even-4 li .button {
    width: 100%;
}
.button-group.even-5 li {
    width: 20%;
}
.button-group.even-5 li > button, .button-group.even-5 li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.even-5 li:first-child button, .button-group.even-5 li:first-child .button {
    border-left: 0 none;
}
.button-group.even-5 li button, .button-group.even-5 li .button {
    width: 100%;
}
.button-group.even-6 li {
    width: 16.6667%;
}
.button-group.even-6 li > button, .button-group.even-6 li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.even-6 li:first-child button, .button-group.even-6 li:first-child .button {
    border-left: 0 none;
}
.button-group.even-6 li button, .button-group.even-6 li .button {
    width: 100%;
}
.button-group.even-7 li {
    width: 14.2857%;
}
.button-group.even-7 li > button, .button-group.even-7 li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.even-7 li:first-child button, .button-group.even-7 li:first-child .button {
    border-left: 0 none;
}
.button-group.even-7 li button, .button-group.even-7 li .button {
    width: 100%;
}
.button-group.even-8 li {
    width: 12.5%;
}
.button-group.even-8 li > button, .button-group.even-8 li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.5);
}
.button-group.even-8 li:first-child button, .button-group.even-8 li:first-child .button {
    border-left: 0 none;
}
.button-group.even-8 li button, .button-group.even-8 li .button {
    width: 100%;
}
.button-bar {
}
.button-bar::before, .button-bar::after {
    content: " ";
    display: table;
}
.button-bar::after {
    clear: both;
}
.button-bar .button-group {
    float: left;
    margin-right: 0.625rem;
}
.button-bar .button-group div {
    overflow: hidden;
}
.panel {
    background: #fde59d none repeat scroll 0 0;
    border-color: #ddbd5a;
    border-style: solid;
    border-width: 1px;
    margin-bottom: 1.25rem;
    padding: 1.75rem;
}
.panel > *:first-child {
    margin-top: 0;
}
.panel > *:last-child {
    margin-bottom: 0;
}
.panel h1, .panel h2, .panel h3, .panel h4, .panel h5, .panel h6, .panel p {
    color: #333333;
}
.panel h1, .panel h2, .panel h3, .panel h4, .panel h5, .panel h6 {
    line-height: 1;
    margin-bottom: 0.625rem;
}
.panel h1.subheader, .panel h2.subheader, .panel h3.subheader, .panel h4.subheader, .panel h5.subheader, .panel h6.subheader {
    line-height: 1.4;
}
.panel.callout {
    background: #f9a47a none repeat scroll 0 0;
    border-color: #d87b50;
    border-style: solid;
    border-width: 1px;
    margin-bottom: 4rem;
    padding: 3.5rem;
}
.panel.callout.green {
    background: #7cbc70 none repeat scroll 0 0;
    border-color: #509b3f;
}
.panel.callout > *:first-child {
    margin-top: 0;
}
.panel.callout > *:last-child {
    margin-bottom: 0;
}
.panel.callout h1, .panel.callout h2, .panel.callout h3, .panel.callout h4, .panel.callout h5, .panel.callout h6, .panel.callout p {
    color: #333333;
}
.panel.callout h1, .panel.callout h2, .panel.callout h3, .panel.callout h4, .panel.callout h5, .panel.callout h6 {
    line-height: 1;
    margin-bottom: 0.625rem;
}
.panel.callout h1.subheader, .panel.callout h2.subheader, .panel.callout h3.subheader, .panel.callout h4.subheader, .panel.callout h5.subheader, .panel.callout h6.subheader {
    line-height: 1.4;
}
.panel.callout a {
    color: #008cba;
}
.panel.radius {
    border-radius: 3px;
}
.dropdown.button, button.dropdown {
    padding-right: 3.5625rem;
    position: relative;
}
.dropdown.button::before, button.dropdown::before {
    border-color: white transparent transparent;
    border-style: solid;
    content: "";
    display: block;
    height: 0;
    position: absolute;
    top: 50%;
    width: 0;
}
.dropdown.button::before, button.dropdown::before {
    border-width: 0.375rem;
    margin-top: -0.15625rem;
    right: 1.40625rem;
}
.dropdown.button::before, button.dropdown::before {
    border-color: white transparent transparent;
}
.dropdown.button.tiny, button.dropdown.tiny {
    padding-right: 2.625rem;
}
.dropdown.button.tiny::before, button.dropdown.tiny::before {
    border-width: 0.375rem;
    margin-top: -0.125rem;
    right: 1.125rem;
}
.dropdown.button.tiny::before, button.dropdown.tiny::before {
    border-color: white transparent transparent;
}
.dropdown.button.small, button.dropdown.small {
    padding-right: 3.0625rem;
}
.dropdown.button.small::before, button.dropdown.small::before {
    border-width: 0.4375rem;
    margin-top: -0.15625rem;
    right: 1.3125rem;
}
.dropdown.button.small::before, button.dropdown.small::before {
    border-color: white transparent transparent;
}
.dropdown.button.large, button.dropdown.large {
    padding-right: 3.625rem;
}
.dropdown.button.large::before, button.dropdown.large::before {
    border-width: 0.3125rem;
    margin-top: -0.15625rem;
    right: 1.71875rem;
}
.dropdown.button.large::before, button.dropdown.large::before {
    border-color: white transparent transparent;
}
.dropdown.button.secondary::before, button.dropdown.secondary::before {
    border-color: #333333 transparent transparent;
}
div.switch {
    background: white none repeat scroll 0 0;
    border-color: #cccccc;
    border-style: solid;
    border-width: 1px;
    display: block;
    height: 2.25rem;
    margin-bottom: 1.25rem;
    overflow: hidden;
    padding: 0;
    position: relative;
}
div.switch label {
    float: left;
    font-weight: bold;
    height: 100%;
    left: 0;
    margin: 0;
    position: relative;
    text-align: left;
    transition: all 0.1s ease-out 0s;
    width: 50%;
    z-index: 2;
}
div.switch input {
    -moz-appearance: none;
    height: 100%;
    opacity: 0;
    position: absolute;
    width: 100%;
    z-index: 3;
}
div.switch input:hover, div.switch input:focus {
    cursor: pointer;
}
div.switch span:last-child {
    border-style: solid;
    border-width: 1px;
    display: block;
    left: -1px;
    padding: 0;
    position: absolute;
    top: -1px;
    transition: all 0.1s ease-out 0s;
    z-index: 1;
}
div.switch input:not(:checked) + label {
    opacity: 0;
}
div.switch input:checked {
    display: none !important;
}
div.switch input {
    display: block !important;
    left: 0;
}
div.switch input:first-of-type + label, div.switch input:first-of-type + span + label {
    left: -50%;
}
div.switch input:first-of-type:checked + label, div.switch input:first-of-type:checked + span + label {
    left: 0;
}
div.switch input:last-of-type + label, div.switch input:last-of-type + span + label {
    left: auto;
    right: -50%;
    text-align: right;
}
div.switch input:last-of-type:checked + label, div.switch input:last-of-type:checked + span + label {
    left: auto;
    right: 0;
}
div.switch span.custom {
    display: none !important;
}
form.custom div.switch .hidden-field {
    margin-left: auto;
    position: absolute;
    visibility: visible;
}
div.switch label {
    font-size: 0.875rem;
    line-height: 2.3rem;
    padding: 0;
}
div.switch input:first-of-type:checked ~ span:last-child {
    left: 100%;
    margin-left: -2.1875rem;
}
div.switch span:last-child {
    height: 2.25rem;
    width: 2.25rem;
}
div.switch span:last-child {
    background: rgba(0, 0, 0, 0) linear-gradient(to bottom, white 0%, #f2f2f2 100%) repeat scroll 0 0;
    border-color: #b3b3b3;
    box-shadow: 2px 0 10px 0 rgba(0, 0, 0, 0.07), 1000px 0 0 980px #f3faf6, -2px 0 10px 0 rgba(0, 0, 0, 0.07), -1000px 0 0 1000px whitesmoke;
}
div.switch:hover span:last-child, div.switch:focus span:last-child {
    background: rgba(0, 0, 0, 0) linear-gradient(to bottom, white 0%, #e6e6e6 100%) repeat scroll 0 0;
}
div.switch:active {
    background: transparent none repeat scroll 0 0;
}
div.switch.large {
    height: 2.75rem;
}
div.switch.large label {
    font-size: 1.0625rem;
    line-height: 2.3rem;
    padding: 0;
}
div.switch.large input:first-of-type:checked ~ span:last-child {
    left: 100%;
    margin-left: -2.6875rem;
}
div.switch.large span:last-child {
    height: 2.75rem;
    width: 2.75rem;
}
div.switch.small {
    height: 1.75rem;
}
div.switch.small label {
    font-size: 0.75rem;
    line-height: 2.1rem;
    padding: 0;
}
div.switch.small input:first-of-type:checked ~ span:last-child {
    left: 100%;
    margin-left: -1.6875rem;
}
div.switch.small span:last-child {
    height: 1.75rem;
    width: 1.75rem;
}
div.switch.tiny {
    height: 1.375rem;
}
div.switch.tiny label {
    font-size: 0.6875rem;
    line-height: 1.9rem;
    padding: 0;
}
div.switch.tiny input:first-of-type:checked ~ span:last-child {
    left: 100%;
    margin-left: -1.3125rem;
}
div.switch.tiny span:last-child {
    height: 1.375rem;
    width: 1.375rem;
}
div.switch.radius {
    border-radius: 4px;
}
div.switch.radius span:last-child {
    border-radius: 3px;
}
div.switch.round {
    border-radius: 1000px;
}
div.switch.round span:last-child {
    border-radius: 999px;
}
div.switch.round label {
    padding: 0 0.5625rem;
}
.th {
    border: 4px solid white;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
    display: inline-block;
    line-height: 0;
    max-width: 100%;
    transition: all 200ms ease-out 0s;
}
.th:hover, .th:focus {
    box-shadow: 0 0 6px 1px rgba(0, 140, 186, 0.5);
}
.th.radius {
    border-radius: 3px;
}
.pricing-table {
    border: 1px solid #dddddd;
    margin-bottom: 1.25rem;
    margin-left: 0;
}
.pricing-table * {
    line-height: 1;
    list-style: outside none none;
}
.pricing-table .title {
    background-color: #333333;
    color: #eeeeee;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 1rem;
    font-weight: normal;
    padding: 0.9375rem 1.25rem;
    text-align: center;
}
.pricing-table .price {
    background-color: #f6f6f6;
    color: #333333;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 2rem;
    font-weight: normal;
    padding: 0.9375rem 1.25rem;
    text-align: center;
}
.pricing-table .description {
    background-color: white;
    border-bottom: 1px dotted #dddddd;
    color: #777777;
    font-size: 0.75rem;
    font-weight: normal;
    line-height: 1.4;
    padding: 0.9375rem;
    text-align: center;
}
.pricing-table .bullet-item {
    background-color: white;
    border-bottom: 1px dotted #dddddd;
    color: #333333;
    font-size: 0.875rem;
    font-weight: normal;
    padding: 0.9375rem;
    text-align: center;
}
.pricing-table .cta-button {
    background-color: white;
    padding: 1.25rem 1.25rem 0;
    text-align: center;
}
@keyframes rotate {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes rotate {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.slideshow-wrapper {
    position: relative;
}
.slideshow-wrapper ul {
    list-style-type: none;
    margin: 0;
}
.slideshow-wrapper ul li, .slideshow-wrapper ul li .orbit-caption {
    display: none;
}
.slideshow-wrapper ul li:first-child {
    display: block;
}
.slideshow-wrapper .orbit-container {
    background-color: transparent;
}
.slideshow-wrapper .orbit-container li {
    display: block;
}
.slideshow-wrapper .orbit-container li .orbit-caption {
    display: block;
}
.preloader {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    animation-duration: 1.5s;
    animation-iteration-count: infinite;
    animation-name: rotate;
    animation-timing-function: linear;
    border-color: #555555 white;
    border-image: none;
    border-radius: 1000px;
    border-style: solid;
    border-width: 3px;
    display: block;
    height: 40px;
    left: 50%;
    margin-left: -20px;
    margin-top: -20px;
    position: absolute;
    top: 50%;
    width: 40px;
}
.orbit-container {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
.orbit-container .orbit-slides-container {
    list-style: outside none none;
    margin: 0;
    padding: 0;
    position: relative;
}
.orbit-container .orbit-slides-container img {
    display: block;
    max-width: 100%;
}
.orbit-container .orbit-slides-container > * {
    margin-left: 100%;
    position: absolute;
    top: 0;
    width: 100%;
}
.orbit-container .orbit-slides-container > *:first-child {
    margin-left: 0;
}
.orbit-container .orbit-slides-container > * .orbit-caption {
    background-color: rgba(51, 51, 51, 0.8);
    bottom: 0;
    color: white;
    font-size: 0.875rem;
    padding: 0.625rem 0.875rem;
    position: absolute;
    width: 100%;
}
.orbit-container .orbit-slide-number {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    color: white;
    font-size: 12px;
    left: 10px;
    position: absolute;
    top: 10px;
    z-index: 10;
}
.orbit-container .orbit-slide-number span {
    font-weight: 700;
    padding: 0.3125rem;
}
.orbit-container .orbit-timer {
    height: 6px;
    position: absolute;
    right: 10px;
    top: 12px;
    width: 100px;
    z-index: 10;
}
.orbit-container .orbit-timer .orbit-progress {
    background-color: rgba(255, 255, 255, 0.3);
    display: block;
    height: 3px;
    position: relative;
    right: 20px;
    top: 5px;
    width: 0;
}
.orbit-container .orbit-timer > span {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: -moz-use-text-color white;
    border-image: none;
    border-style: none solid;
    border-width: medium 4px;
    display: none;
    height: 14px;
    position: absolute;
    right: 0;
    top: 0;
    width: 11px;
}
.orbit-container .orbit-timer.paused > span {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: transparent transparent transparent white;
    border-image: none;
    border-style: inset solid inset inset;
    border-width: 8px;
    height: 14px;
    right: -4px;
    top: 0;
    width: 11px;
}
.orbit-container .orbit-timer.paused > span.dark {
    border-color: transparent transparent transparent #333333;
}
.orbit-container:hover .orbit-timer > span {
    display: block;
}
.orbit-container .orbit-prev, .orbit-container .orbit-next {
    color: white;
    height: 60px;
    line-height: 50px;
    margin-top: -25px;
    position: absolute;
    text-indent: -9999px !important;
    top: 45%;
    width: 36px;
    z-index: 10;
}
.orbit-container .orbit-prev:hover, .orbit-container .orbit-next:hover {
    background-color: rgba(0, 0, 0, 0.3);
}
.orbit-container .orbit-prev > span, .orbit-container .orbit-next > span {
    border: 10px inset;
    display: block;
    height: 0;
    margin-top: -10px;
    position: absolute;
    top: 50%;
    width: 0;
}
.orbit-container .orbit-prev {
    left: 0;
}
.orbit-container .orbit-prev > span {
    border-color: transparent white transparent transparent;
    border-right-style: solid;
}
.orbit-container .orbit-prev:hover > span {
    border-right-color: white;
}
.orbit-container .orbit-next {
    right: 0;
}
.orbit-container .orbit-next > span {
    border-color: transparent transparent transparent white;
    border-left-style: solid;
    left: 50%;
    margin-left: -4px;
}
.orbit-container .orbit-next:hover > span {
    border-left-color: white;
}
.orbit-bullets-container {
    text-align: center;
}
.orbit-bullets {
    display: block;
    float: none;
    margin: 0 auto 30px;
    overflow: hidden;
    position: relative;
    text-align: center;
    top: 10px;
}
.orbit-bullets li {
    background: #cccccc none repeat scroll 0 0;
    border-radius: 1000px;
    display: inline-block;
    float: none;
    height: 0.5625rem;
    margin-right: 6px;
    width: 0.5625rem;
}
.orbit-bullets li.active {
    background: #999999 none repeat scroll 0 0;
}
.orbit-bullets li:last-child {
    margin-right: 0;
}
.touch .orbit-container .orbit-prev, .touch .orbit-container .orbit-next {
    display: none;
}
.touch .orbit-bullets {
    display: none;
}
@media only screen and (min-width: 40.063em) {
.touch .orbit-container .orbit-prev, .touch .orbit-container .orbit-next {
    display: inherit;
}
.touch .orbit-bullets {
    display: block;
}
}
@media only screen and (max-width: 40em) {
.orbit-stack-on-small .orbit-slides-container {
    height: auto !important;
}
.orbit-stack-on-small .orbit-slides-container > * {
    margin-left: 0 !important;
    position: relative;
}
.orbit-stack-on-small .orbit-timer, .orbit-stack-on-small .orbit-next, .orbit-stack-on-small .orbit-prev, .orbit-stack-on-small .orbit-bullets {
    display: none;
}
}
[data-magellan-expedition] {
    background: white none repeat scroll 0 0;
    min-width: 100%;
    padding: 10px;
    z-index: 50;
}
[data-magellan-expedition] .sub-nav {
    margin-bottom: 0;
}
[data-magellan-expedition] .sub-nav dd {
    margin-bottom: 0;
}
[data-magellan-expedition] .sub-nav a {
    line-height: 1.8em;
}
.tabs {
    margin-bottom: 0 !important;
}
.tabs::before, .tabs::after {
    content: " ";
    display: table;
}
.tabs::after {
    clear: both;
}
.tabs dd {
    float: left;
    margin-bottom: 0 !important;
    position: relative;
    top: 1px;
}
.tabs dd > a {
    background: #efefef none repeat scroll 0 0;
    color: #222222;
    display: block;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 1rem;
    padding: 1rem 2rem 1.0625rem;
}
.tabs dd > a:hover {
    background: #e1e1e1 none repeat scroll 0 0;
}
.tabs dd.active a {
    background: white none repeat scroll 0 0;
}
.tabs.radius dd:first-child a {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
}
.tabs.radius dd:last-child a {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}
.tabs.vertical dd {
    display: block;
    float: none;
    position: inherit;
    top: auto;
}
.tabs-content {
    margin-bottom: 1.5rem;
    width: 100%;
}
.tabs-content::before, .tabs-content::after {
    content: " ";
    display: table;
}
.tabs-content::after {
    clear: both;
}
.tabs-content > .content {
    display: none;
    float: left;
    padding: 0.9375rem 0;
    width: 100%;
}
.tabs-content > .content.active {
    display: block;
}
.tabs-content > .content.contained {
    padding: 0.9375rem;
}
.tabs-content.vertical {
    display: block;
}
.tabs-content.vertical > .content {
    padding: 0 0.9375rem;
}
@media only screen and (min-width: 40.063em) {
.tabs.vertical {
    float: left;
    margin-bottom: 1.25rem;
    width: 20%;
}
.tabs-content.vertical {
    float: left;
    margin-left: -1px;
    width: 80%;
}
}
ul.pagination {
    display: block;
    height: 1.5rem;
    margin-left: -0.3125rem;
}
ul.pagination li {
    color: #222222;
    font-size: 0.875rem;
    height: 1.5rem;
    margin-left: 0.3125rem;
}
ul.pagination li a {
    border-radius: 3px;
    color: #ce5e1c;
    display: block;
    padding: 0.0625rem 0.625rem;
}
ul.pagination li:hover a, ul.pagination li a:focus {
    color: #8c0527;
}
ul.pagination li.unavailable a {
    color: #999999;
    cursor: default;
}
ul.pagination li.unavailable:hover a, ul.pagination li.unavailable a:focus {
    background: transparent none repeat scroll 0 0;
}
ul.pagination li.current a {
    background: #008cba none repeat scroll 0 0;
    color: white;
    cursor: default;
    font-weight: bold;
}
ul.pagination li.current a:hover, ul.pagination li.current a:focus {
    background: #008cba none repeat scroll 0 0;
}
ul.pagination li {
    display: block;
    float: left;
}
.pagination-centered {
    text-align: center;
}
.pagination-centered ul.pagination li {
    display: inline-block;
    float: none;
}
.side-nav {
    display: block;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    list-style-position: inside;
    list-style-type: none;
    margin: 0;
    padding: 0.875rem 0;
}
.side-nav li {
    font-size: 0.875rem;
    margin: 0 0 0.4375rem;
}
.side-nav li a:not(.button) {
    color: #008cba;
    display: block;
}
.side-nav li a:hover:not(.button), .side-nav li a:focus:not(.button) {
    color: #1cc7ff;
}
.side-nav li.active > a:first-child:not(.button) {
    color: #1cc7ff;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-weight: normal;
}
.side-nav li.divider {
    border-top: 1px solid white;
    height: 0;
    list-style: outside none none;
    padding: 0;
}
.accordion {
    margin-bottom: 0;
}
.accordion::before, .accordion::after {
    content: " ";
    display: table;
}
.accordion::after {
    clear: both;
}
.accordion dd {
    display: block;
    margin-bottom: 0 !important;
}
.accordion dd.active a {
    background: #e8e8e8 none repeat scroll 0 0;
}
.accordion dd > a {
    background: #efefef none repeat scroll 0 0;
    color: #222222;
    display: block;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 1rem;
    padding: 1rem;
}
.accordion dd > a:hover {
    background: #e3e3e3 none repeat scroll 0 0;
}
.accordion .content {
    display: none;
    padding: 0.9375rem;
}
.accordion .content.active {
    background: white none repeat scroll 0 0;
    display: block;
}
.text-left {
    text-align: left !important;
}
.text-right {
    text-align: right !important;
}
.text-center {
    text-align: center !important;
}
.text-justify {
    text-align: justify !important;
}
@media only screen and (max-width: 40em) {
.small-only-text-left {
    text-align: left !important;
}
.small-only-text-right {
    text-align: right !important;
}
.small-only-text-center {
    text-align: center !important;
}
.small-only-text-justify {
    text-align: justify !important;
}
}
@media only screen {
.small-text-left {
    text-align: left !important;
}
.small-text-right {
    text-align: right !important;
}
.small-text-center {
    text-align: center !important;
}
.small-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 40.063em) and (max-width: 64em) {
.medium-only-text-left {
    text-align: left !important;
}
.medium-only-text-right {
    text-align: right !important;
}
.medium-only-text-center {
    text-align: center !important;
}
.medium-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 40.063em) {
.medium-text-left {
    text-align: left !important;
}
.medium-text-right {
    text-align: right !important;
}
.medium-text-center {
    text-align: center !important;
}
.medium-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 64.063em) and (max-width: 90em) {
.large-only-text-left {
    text-align: left !important;
}
.large-only-text-right {
    text-align: right !important;
}
.large-only-text-center {
    text-align: center !important;
}
.large-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 64.063em) {
.large-text-left {
    text-align: left !important;
}
.large-text-right {
    text-align: right !important;
}
.large-text-center {
    text-align: center !important;
}
.large-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 90.063em) and (max-width: 120em) {
.xlarge-only-text-left {
    text-align: left !important;
}
.xlarge-only-text-right {
    text-align: right !important;
}
.xlarge-only-text-center {
    text-align: center !important;
}
.xlarge-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 90.063em) {
.xlarge-text-left {
    text-align: left !important;
}
.xlarge-text-right {
    text-align: right !important;
}
.xlarge-text-center {
    text-align: center !important;
}
.xlarge-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 120.063em) and (max-width: 1e+8em) {
.xxlarge-only-text-left {
    text-align: left !important;
}
.xxlarge-only-text-right {
    text-align: right !important;
}
.xxlarge-only-text-center {
    text-align: center !important;
}
.xxlarge-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 120.063em) {
.xxlarge-text-left {
    text-align: left !important;
}
.xxlarge-text-right {
    text-align: right !important;
}
.xxlarge-text-center {
    text-align: center !important;
}
.xxlarge-text-justify {
    text-align: justify !important;
}
}
div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, p, blockquote, th, td {
    margin: 0;
    padding: 0;
}
a {
    color: #008cba;
    line-height: inherit;
    text-decoration: none;
}
a:hover, a:focus {
    color: #0078a0;
}
a img {
    border: medium none;
}
p {
    font-family: Varela Round;
    font-size: 1rem;
    font-weight: normal;
    line-height: 1.6;
    padding-top: 0.8rem;
    text-rendering: optimizelegibility;
}
p.lead {
    font-size: 1.21875rem;
    line-height: 1.6;
}
p aside {
    font-size: 0.875rem;
    font-style: italic;
    line-height: 1.35;
}
.ptitle {
    color: #9e1c20;
    font-size: 2.6rem;
    font-weight: 400;
    text-align: center;
}
h1, h2, h3, h4, h5, h6 {
    color: #222222;
    font-family: Arial,sans-serif;
    font-style: normal;
    font-weight: normal;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    margin-top: 0.2rem;
    text-rendering: optimizelegibility;
}
h1 small, h2 small, h3 small, h4 small, h5 small, h6 small {
    color: #6f6f6f;
    font-size: 60%;
    line-height: 0;
}
h1 {
    font-family: "Varela Round",Arial,Helvetica,sans-serif !important;
    font-size: 4.125rem;
}
h2 {
    font-family: "Istok Web" !important;
    font-size: 3.6875rem;
}
h3 {
    font-family: "Istok Web" !important;
    font-size: 2.375rem;
}
h4 {
    font-family: "Istok Web" !important;
    font-size: 1.125rem;
}
h5 {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1.125rem;
}
h6 {
    font-size: 1rem;
}
.subheader {
    color: #6f6f6f;
    font-weight: normal;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    margin-top: 0.2rem;
}
hr {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: #0080ff;
    border-image: none;
    border-style: solid;
    border-width: 2px 0 0;
    clear: both;
    height: 0;
    margin: 0.1rem 0 1.5rem;
}
em, i {
    font-style: italic;
    line-height: inherit;
}
strong, b {
    font-weight: bold;
    line-height: inherit;
}
small {
    font-size: 60%;
    line-height: inherit;
}
code {
    color: #bd260d;
    font-family: Consolas,"Liberation Mono",Courier,monospace;
    font-weight: bold;
}
ul, ol, dl {
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.6;
    list-style-position: outside;
    margin-bottom: 1.25rem;
}
ul {
    margin-left: 1.1rem;
}
ul.no-bullet {
    margin-left: 0;
}
ul.no-bullet li ul, ul.no-bullet li ol {
    list-style: outside none none;
    margin-bottom: 0;
    margin-left: 1.25rem;
}
ul li ul, ul li ol {
    margin-bottom: 0;
    margin-left: 1.25rem;
}
ul.square li ul, ul.circle li ul, ul.disc li ul {
    list-style: inherit;
}
ul.square {
    list-style-type: square;
    margin-left: 1.1rem;
}
ul.circle {
    list-style-type: circle;
    margin-left: 1.1rem;
}
ul.disc {
    list-style-type: disc;
    margin-left: 1.1rem;
}
ul.no-bullet {
    list-style: outside none none;
}
ol {
    margin-left: 1.4rem;
}
ol li ul, ol li ol {
    margin-bottom: 0;
    margin-left: 1.25rem;
}
dl dt {
    font-weight: bold;
    margin-bottom: 0.3rem;
}
dl dd {
    margin-bottom: 0.75rem;
}
abbr, acronym {
    border-bottom: 1px dotted #dddddd;
    color: #222222;
    cursor: help;
    font-size: 90%;
    text-transform: uppercase;
}
abbr {
    text-transform: none;
}
blockquote {
    border-left: 1px solid #dddddd;
    margin: 0 0 1.25rem;
    padding: 0.5625rem 1.25rem 0 1.1875rem;
}
blockquote cite {
    color: #555555;
    display: block;
    font-size: 0.8125rem;
}
blockquote cite::before {
    content: "— ";
}
blockquote cite a, blockquote cite a:visited {
    color: #555555;
}
blockquote, blockquote p {
    color: #6f6f6f;
    line-height: 1.6;
}
.vcard {
    border: 1px solid #dddddd;
    display: inline-block;
    margin: 0 0 1.25rem;
    padding: 0.625rem 0.75rem;
}
.vcard li {
    display: block;
    margin: 0;
}
.vcard .fn {
    font-size: 0.9375rem;
    font-weight: bold;
}
.vevent .summary {
    font-weight: bold;
}
.vevent abbr {
    border: medium none;
    cursor: default;
    font-weight: bold;
    padding: 0 0.0625rem;
    text-decoration: none;
}
@media only screen and (min-width: 40.063em) {
h1, h2, h3, h4, h5, h6 {
    line-height: 1.4;
}
h1 {
    font-size: 4.75rem;
}
h2 {
    font-size: 4.3125rem;
}
h3 {
    font-size: 2rem;
}
h4 {
    font-size: 1.4375rem;
    padding-left: 1rem;
    text-decoration: underline;
}
}
.print-only {
    display: none !important;
}
@media print {
* {
    background: transparent none repeat scroll 0 0 !important;
    box-shadow: none !important;
    color: black !important;
    text-shadow: none !important;
}
a, a:visited {
    text-decoration: underline;
}
a[href]::after {
    content: " (" attr(href) ")";
}
abbr[title]::after {
    content: " (" attr(title) ")";
}
.ir a::after, a[href^="javascript:"]::after, a[href^="#"]::after {
    content: "";
}
pre, blockquote {
    border: 1px solid #999999;
    page-break-inside: avoid;
}
thead {
    display: table-header-group;
}
tr, img {
    page-break-inside: avoid;
}
img {
    max-width: 100% !important;
}
@page {
    margin: 0.5cm;
}
p, h2, h3 {
    orphans: 3;
    widows: 3;
}
h2, h3 {
    page-break-after: avoid;
}
.hide-on-print {
    display: none !important;
}
.print-only {
    display: block !important;
}
.hide-for-print {
    display: none !important;
}
.show-for-print {
    display: inherit !important;
}
}
.split.button {
    float: right;
    padding-left: 1em;
    padding-right: 3.1rem;
    position: relative;
}
.split.button span {
    border-left: 1px solid;
    display: block;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
}
.split.button span::before {
    border-style: inset;
    content: "";
    display: block;
    height: 0;
    left: 50%;
    position: absolute;
    top: 50%;
    width: 0;
}
.split.button span:active {
    background-color: rgba(0, 0, 0, 0.1);
}
.split.button span {
    border-left-color: rgba(255, 255, 255, 0.5);
}
.split.button span {
    width: 3.09375rem;
}
.split.button span::before {
    border-top-style: solid;
    border-width: 0.375rem;
    margin-left: -0.375rem;
    top: 48%;
}
.split.button span::before {
    border-color: white transparent transparent;
}
.split.button.secondary span {
    border-left-color: rgba(255, 255, 255, 0.5);
}
.split.button.secondary span::before {
    border-color: white transparent transparent;
}
.split.button.alert span {
    border-left-color: rgba(255, 255, 255, 0.5);
}
.split.button.success span {
    border-left-color: rgba(255, 255, 255, 0.5);
}
.split.button.tiny {
    padding-right: 3.75rem;
}
.split.button.tiny span {
    width: 2.25rem;
}
.split.button.tiny span::before {
    border-top-style: solid;
    border-width: 0.375rem;
    margin-left: -0.375rem;
    top: 48%;
}
.split.button.small span {
    width: 2.625rem;
}
.split.button.small span::before {
    border-top-style: solid;
    border-width: 0.4375rem;
    margin-left: -0.375rem;
    top: 48%;
}
.split.button.large {
    padding-right: 5.5rem;
}
.split.button.large span {
    width: 3.4375rem;
}
.split.button.large span::before {
    border-top-style: solid;
    border-width: 0.3125rem;
    margin-left: -0.375rem;
    top: 48%;
}
.split.button.expand {
    padding-left: 2rem;
}
.split.button.secondary span::before {
    border-color: #333333 transparent transparent;
}
.split.button.radius span {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}
.split.button.round span {
    border-bottom-right-radius: 1000px;
    border-top-right-radius: 1000px;
}
.reveal-modal-bg {
    background: rgba(0, 0, 0, 0.45) none repeat scroll 0 0;
    display: none;
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 98;
}
dialog, .reveal-modal {
    background-color: white;
    border: 1px solid #666666;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
    display: none;
    height: auto;
    left: 50%;
    margin-left: -40%;
    padding: 1.25rem;
    position: absolute;
    top: 6.25rem;
    visibility: hidden;
    width: 80%;
    z-index: 99;
}
dialog .column, dialog .columns, .reveal-modal .column, .reveal-modal .columns {
    min-width: 0;
}
dialog > *:first-child, .reveal-modal > *:first-child {
    margin-top: 0;
}
dialog > *:last-child, .reveal-modal > *:last-child {
    margin-bottom: 0;
}
dialog .close-reveal-modal, .reveal-modal .close-reveal-modal {
    color: #aaaaaa;
    cursor: pointer;
    font-size: 1.375rem;
    font-weight: bold;
    line-height: 1;
    position: absolute;
    right: 0.6875rem;
    top: 0.5rem;
}
dialog[open] {
    display: block;
    visibility: visible;
}
@media only screen and (min-width: 40.063em) {
dialog, .reveal-modal {
    padding: 1.875rem;
    top: 6.25rem;
}
dialog.tiny, .reveal-modal.tiny {
    margin-left: -15%;
    width: 30%;
}
dialog.small, .reveal-modal.small {
    margin-left: -20%;
    width: 40%;
}
dialog.medium, .reveal-modal.medium {
    margin-left: -30%;
    width: 60%;
}
dialog.large, .reveal-modal.large {
    margin-left: -35%;
    width: 70%;
}
dialog.xlarge, .reveal-modal.xlarge {
    margin-left: -47.5%;
    width: 95%;
}
}
@media print {
dialog, .reveal-modal {
    background: white none repeat scroll 0 0 !important;
}
}
.has-tip {
    border-bottom: 1px dotted #cccccc;
    color: #333333;
    cursor: help;
    font-weight: bold;
}
.has-tip:hover, .has-tip:focus {
    border-bottom: 1px dotted #003f54;
    color: #008cba;
}
.has-tip.tip-left, .has-tip.tip-right {
    float: none !important;
}
.tooltip {
    background: #333333 none repeat scroll 0 0;
    color: white;
    display: none;
    font-size: 0.875rem;
    font-weight: normal;
    left: 50%;
    line-height: 1.3;
    max-width: 85%;
    padding: 0.4rem;
    position: absolute;
    width: 100%;
    z-index: 999;
}
.tooltip > .nub {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: transparent transparent #333333;
    border-image: none;
    border-style: solid;
    border-width: 5px;
    display: block;
    height: 0;
    left: 5px;
    position: absolute;
    top: -10px;
    width: 0;
}
.tooltip.radius {
    border-radius: 3px;
}
.tooltip.round {
    border-radius: 1000px;
}
.tooltip.round > .nub {
    left: 2rem;
}
.tooltip.opened {
    border-bottom: 1px dotted #003f54 !important;
    color: #008cba !important;
}
.tap-to-close {
    color: #777777;
    display: block;
    font-size: 0.625rem;
    font-weight: normal;
}
@media only screen and (min-width: 40.063em) {
.tooltip > .nub {
    border-color: transparent transparent #333333;
    top: -10px;
}
.tooltip.tip-top > .nub {
    border-color: #333333 transparent transparent;
    bottom: -10px;
    top: auto;
}
.tooltip.tip-left, .tooltip.tip-right {
    float: none !important;
}
.tooltip.tip-left > .nub {
    border-color: transparent transparent transparent #333333;
    left: auto;
    margin-top: -5px;
    right: -10px;
    top: 50%;
}
.tooltip.tip-right > .nub {
    border-color: transparent #333333 transparent transparent;
    left: -10px;
    margin-top: -5px;
    right: auto;
    top: 50%;
}
}
.clearing-thumbs, [data-clearing] {
    list-style: outside none none;
    margin-bottom: 0;
    margin-left: 0;
}
.clearing-thumbs::before, .clearing-thumbs::after, [data-clearing]::before, [data-clearing]::after {
    content: " ";
    display: table;
}
.clearing-thumbs::after, [data-clearing]::after {
    clear: both;
}
.clearing-thumbs li, [data-clearing] li {
    float: left;
    margin-right: 10px;
}
.clearing-thumbs[class*="block-grid-"] li, [data-clearing][class*="block-grid-"] li {
    margin-right: 0;
}
.clearing-blackout {
    background: #333333 none repeat scroll 0 0;
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 998;
}
.clearing-blackout .clearing-close {
    display: block;
}
.clearing-container {
    height: 100%;
    margin: 0;
    overflow: hidden;
    position: relative;
    z-index: 998;
}
.clearing-touch-label {
    color: #aaa;
    font-size: 0.6em;
    left: 50%;
    position: absolute;
    top: 50%;
}
.visible-img {
    height: 95%;
    position: relative;
}
.visible-img img {
    left: 50%;
    margin-left: -50%;
    max-height: 100%;
    max-width: 100%;
    position: absolute;
    top: 50%;
}
.clearing-caption {
    background: #333333 none repeat scroll 0 0;
    bottom: 0;
    color: #cccccc;
    font-size: 0.875em;
    left: 0;
    line-height: 1.3;
    margin-bottom: 0;
    padding: 10px 30px 20px;
    position: absolute;
    text-align: center;
    width: 100%;
}
.clearing-close {
    color: #cccccc;
    display: none;
    font-size: 30px;
    line-height: 1;
    padding-left: 20px;
    padding-top: 10px;
    z-index: 999;
}
.clearing-close:hover, .clearing-close:focus {
    color: #ccc;
}
.clearing-assembled .clearing-container {
    height: 100%;
}
.clearing-assembled .clearing-container .carousel > ul {
    display: none;
}
.clearing-feature li {
    display: none;
}
.clearing-feature li.clearing-featured-img {
    display: block;
}
@media only screen and (min-width: 40.063em) {
.clearing-main-prev, .clearing-main-next {
    height: 100%;
    position: absolute;
    top: 0;
    width: 40px;
}
.clearing-main-prev > span, .clearing-main-next > span {
    border: 12px solid;
    display: block;
    height: 0;
    position: absolute;
    top: 50%;
    width: 0;
}
.clearing-main-prev > span:hover, .clearing-main-next > span:hover {
    opacity: 0.8;
}
.clearing-main-prev {
    left: 0;
}
.clearing-main-prev > span {
    border-color: transparent #cccccc transparent transparent;
    left: 5px;
}
.clearing-main-next {
    right: 0;
}
.clearing-main-next > span {
    border-color: transparent transparent transparent #cccccc;
}
.clearing-main-prev.disabled, .clearing-main-next.disabled {
    opacity: 0.3;
}
.clearing-assembled .clearing-container .carousel {
    background: rgba(51, 51, 51, 0.8) none repeat scroll 0 0;
    height: 120px;
    margin-top: 10px;
    text-align: center;
}
.clearing-assembled .clearing-container .carousel > ul {
    display: inline-block;
    float: none;
    height: 100%;
    position: relative;
    z-index: 999;
}
.clearing-assembled .clearing-container .carousel > ul li {
    cursor: pointer;
    display: block;
    float: left;
    margin-right: 0;
    min-height: inherit;
    opacity: 0.4;
    overflow: hidden;
    padding: 0;
    position: relative;
    width: 120px;
}
.clearing-assembled .clearing-container .carousel > ul li.fix-height img {
    height: 100%;
    max-width: none;
}
.clearing-assembled .clearing-container .carousel > ul li a.th {
    border: medium none;
    box-shadow: none;
    display: block;
}
.clearing-assembled .clearing-container .carousel > ul li img {
    cursor: pointer !important;
    width: 100% !important;
}
.clearing-assembled .clearing-container .carousel > ul li.visible {
    opacity: 1;
}
.clearing-assembled .clearing-container .carousel > ul li:hover {
    opacity: 0.8;
}
.clearing-assembled .clearing-container .visible-img {
    background: #333333 none repeat scroll 0 0;
    height: 85%;
    overflow: hidden;
}
.clearing-close {
    padding-left: 0;
    padding-top: 0;
    position: absolute;
    right: 20px;
    top: 10px;
}
}
.progress {
    background-color: #f6f6f6;
    border: 1px solid white;
    height: 1.5625rem;
    margin-bottom: 0.625rem;
    padding: 0.125rem;
}
.progress .meter {
    background: #008cba none repeat scroll 0 0;
    display: block;
    height: 100%;
}
.progress.secondary .meter {
    background: #e7e7e7 none repeat scroll 0 0;
    display: block;
    height: 100%;
}
.progress.success .meter {
    background: #43ac6a none repeat scroll 0 0;
    display: block;
    height: 100%;
}
.progress.alert .meter {
    background: #f04124 none repeat scroll 0 0;
    display: block;
    height: 100%;
}
.progress.radius {
    border-radius: 3px;
}
.progress.radius .meter {
    border-radius: 2px;
}
.progress.round {
    border-radius: 1000px;
}
.progress.round .meter {
    border-radius: 999px;
}
.sub-nav {
    display: block;
    margin: -0.25rem 0 1.125rem -0.75rem;
    overflow: hidden;
    padding-top: 0.25rem;
    width: auto;
}
.sub-nav dt {
    text-transform: uppercase;
}
.sub-nav dt, .sub-nav dd, .sub-nav li {
    color: #999999;
    display: inline;
    float: left;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 0.875rem;
    font-weight: normal;
    margin-bottom: 0.625rem;
    margin-left: 1rem;
}
.sub-nav dt a, .sub-nav dd a, .sub-nav li a {
    color: #999999;
    padding: 0.1875rem 1rem;
    text-decoration: none;
}
.sub-nav dt a:hover, .sub-nav dd a:hover, .sub-nav li a:hover {
    color: #737373;
}
.sub-nav dt.active a, .sub-nav dd.active a, .sub-nav li.active a {
    background: #008cba none repeat scroll 0 0;
    border-radius: 3px;
    color: white;
    cursor: default;
    font-weight: normal;
    padding: 0.1875rem 1rem;
}
.sub-nav dt.active a:hover, .sub-nav dd.active a:hover, .sub-nav li.active a:hover {
    background: #0078a0 none repeat scroll 0 0;
}
.joyride-list {
    display: none;
}
.joyride-tip-guide {
    background: #333333 none repeat scroll 0 0;
    color: white;
    display: none;
    font-family: inherit;
    font-weight: normal;
    left: 2.5%;
    position: absolute;
    top: 0;
    width: 95%;
    z-index: 101;
}
.lt-ie9 .joyride-tip-guide {
    left: 50%;
    margin-left: -400px;
    max-width: 800px;
}
.joyride-content-wrapper {
    padding: 1.125rem 1.25rem 1.5rem;
    width: 100%;
}
.joyride-content-wrapper .button {
    margin-bottom: 0 !important;
}
.joyride-tip-guide .joyride-nub {
    border: 10px solid #333333;
    display: block;
    height: 0;
    left: 22px;
    position: absolute;
    width: 0;
}
.joyride-tip-guide .joyride-nub.top {
    border-bottom-color: #333333;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
    border-top-color: transparent !important;
    border-top-style: solid;
    top: -20px;
}
.joyride-tip-guide .joyride-nub.bottom {
    border-bottom-style: solid;
    border-color: #333333 transparent transparent !important;
    bottom: -20px;
}
.joyride-tip-guide .joyride-nub.right {
    right: -20px;
}
.joyride-tip-guide .joyride-nub.left {
    left: -20px;
}
.joyride-tip-guide h1, .joyride-tip-guide h2, .joyride-tip-guide h3, .joyride-tip-guide h4, .joyride-tip-guide h5, .joyride-tip-guide h6 {
    color: white;
    font-weight: bold;
    line-height: 1.25;
    margin: 0;
}
.joyride-tip-guide p {
    font-size: 0.875rem;
    line-height: 1.3;
    margin: 0 0 1.125rem;
}
.joyride-timer-indicator-wrap {
    border: 1px solid #555555;
    bottom: 1rem;
    height: 3px;
    position: absolute;
    right: 1.0625rem;
    width: 50px;
}
.joyride-timer-indicator {
    background: #666666 none repeat scroll 0 0;
    display: block;
    height: inherit;
    width: 0;
}
.joyride-close-tip {
    color: #777777 !important;
    font-size: 24px;
    font-weight: normal;
    line-height: 0.5 !important;
    position: absolute;
    right: 12px;
    text-decoration: none;
    top: 10px;
}
.joyride-close-tip:hover, .joyride-close-tip:focus {
    color: #eeeeee !important;
}
.joyride-modal-bg {
    background: rgba(0, 0, 0, 0.5) none repeat scroll 0 0;
    cursor: pointer;
    display: none;
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 100;
}
.joyride-expose-wrapper {
    background-color: #ffffff;
    border-radius: 3px;
    box-shadow: 0 0 15px white;
    position: absolute;
    z-index: 102;
}
.joyride-expose-cover {
    background: transparent none repeat scroll 0 0;
    border-radius: 3px;
    left: 0;
    position: absolute;
    top: 0;
    z-index: 9999;
}
@media only screen and (min-width: 40.063em) {
.joyride-tip-guide {
    left: inherit;
    width: 300px;
}
.joyride-tip-guide .joyride-nub.bottom {
    border-color: #333333 transparent transparent !important;
    bottom: -20px;
}
.joyride-tip-guide .joyride-nub.right {
    border-color: transparent transparent transparent #333333 !important;
    left: auto;
    right: -20px;
    top: 22px;
}
.joyride-tip-guide .joyride-nub.left {
    border-color: transparent #333333 transparent transparent !important;
    left: -20px;
    right: auto;
    top: 22px;
}
}
.label {
    background-color: #008cba;
    color: white;
    display: inline-block;
    font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
    font-size: 0.6875rem;
    font-weight: normal;
    line-height: 1;
    margin-bottom: inherit;
    padding: 0.25rem 0.5rem 0.375rem;
    position: relative;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
}
.label.radius {
    border-radius: 3px;
}
.label.round {
    border-radius: 1000px;
}
.label.alert {
    background-color: #f04124;
    color: white;
}
.label.success {
    background-color: #43ac6a;
    color: white;
}
.label.secondary {
    background-color: #e7e7e7;
    color: #333333;
}
.text-left {
    text-align: left !important;
}
.text-right {
    text-align: right !important;
}
.text-center {
    text-align: center !important;
}
.text-justify {
    text-align: justify !important;
}
@media only screen and (max-width: 40em) {
.small-only-text-left {
    text-align: left !important;
}
.small-only-text-right {
    text-align: right !important;
}
.small-only-text-center {
    text-align: center !important;
}
.small-only-text-justify {
    text-align: justify !important;
}
}
@media only screen {
.small-text-left {
    text-align: left !important;
}
.small-text-right {
    text-align: right !important;
}
.small-text-center {
    text-align: center !important;
}
.small-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 40.063em) and (max-width: 64em) {
.medium-only-text-left {
    text-align: left !important;
}
.medium-only-text-right {
    text-align: right !important;
}
.medium-only-text-center {
    text-align: center !important;
}
.medium-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 40.063em) {
.medium-text-left {
    text-align: left !important;
}
.medium-text-right {
    text-align: right !important;
}
.medium-text-center {
    text-align: center !important;
}
.medium-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 64.063em) and (max-width: 90em) {
.large-only-text-left {
    text-align: left !important;
}
.large-only-text-right {
    text-align: right !important;
}
.large-only-text-center {
    text-align: center !important;
}
.large-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 64.063em) {
.large-text-left {
    text-align: left !important;
}
.large-text-right {
    text-align: right !important;
}
.large-text-center {
    text-align: center !important;
}
.large-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 90.063em) and (max-width: 120em) {
.xlarge-only-text-left {
    text-align: left !important;
}
.xlarge-only-text-right {
    text-align: right !important;
}
.xlarge-only-text-center {
    text-align: center !important;
}
.xlarge-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 90.063em) {
.xlarge-text-left {
    text-align: left !important;
}
.xlarge-text-right {
    text-align: right !important;
}
.xlarge-text-center {
    text-align: center !important;
}
.xlarge-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 120.063em) and (max-width: 1e+8em) {
.xxlarge-only-text-left {
    text-align: left !important;
}
.xxlarge-only-text-right {
    text-align: right !important;
}
.xxlarge-only-text-center {
    text-align: center !important;
}
.xxlarge-only-text-justify {
    text-align: justify !important;
}
}
@media only screen and (min-width: 120.063em) {
.xxlarge-text-left {
    text-align: left !important;
}
.xxlarge-text-right {
    text-align: right !important;
}
.xxlarge-text-center {
    text-align: center !important;
}
.xxlarge-text-justify {
    text-align: justify !important;
}
}
.off-canvas-wrap {
    overflow-x: hidden;
    position: relative;
    width: 100%;
}
.off-canvas-wrap.move-right, .off-canvas-wrap.move-left {
    height: 100%;
}
.inner-wrap {
    position: relative;
    transition: transform 500ms ease 0s;
    width: 100%;
}
.inner-wrap::before, .inner-wrap::after {
    content: " ";
    display: table;
}
.inner-wrap::after {
    clear: both;
}
nav.tab-bar {
    background: #333333 none repeat scroll 0 0;
    color: white;
    height: 2.8125rem;
    line-height: 2.8125rem;
    position: relative;
}
nav.tab-bar h1, nav.tab-bar h2, nav.tab-bar h3, nav.tab-bar h4, nav.tab-bar h5, nav.tab-bar h6 {
    color: white;
    font-weight: bold;
    line-height: 2.8125rem;
    margin: 0;
}
nav.tab-bar h1, nav.tab-bar h2, nav.tab-bar h3, nav.tab-bar h4 {
    font-size: 1.125rem;
}
section.left-small {
    border-right: 1px solid #1a1a1a;
    box-shadow: 1px 0 0 #4e4e4e;
    height: 2.8125rem;
    left: 0;
    position: absolute;
    top: 0;
    width: 2.8125rem;
}
section.right-small {
    border-left: 1px solid #4e4e4e;
    box-shadow: -1px 0 0 #1a1a1a;
    height: 2.8125rem;
    position: absolute;
    right: 0;
    top: 0;
    width: 2.8125rem;
}
section.tab-bar-section {
    height: 2.8125rem;
    padding: 0 0.625rem;
    position: absolute;
    text-align: center;
    top: 0;
}
@media only screen and (min-width: 40.063em) {
section.tab-bar-section {
    text-align: left;
}
}
section.tab-bar-section.left {
    left: 0;
    right: 2.8125rem;
}
section.tab-bar-section.right {
    left: 2.8125rem;
    right: 0;
}
section.tab-bar-section.middle {
    left: 2.8125rem;
    right: 2.8125rem;
}
a.menu-icon {
    color: white;
    display: block;
    height: 2.8125rem;
    line-height: 2.0625rem;
    padding: 0;
    position: relative;
    text-indent: 2.1875rem;
    width: 2.8125rem;
}
a.menu-icon span {
    box-shadow: 0 10px 0 1px white, 0 16px 0 1px white, 0 22px 0 1px white;
    display: block;
    height: 0;
    left: 0.8125rem;
    position: absolute;
    top: 0.3125rem;
    width: 1rem;
}
a.menu-icon:hover span {
    box-shadow: 0 10px 0 1px #b3b3b3, 0 16px 0 1px #b3b3b3, 0 22px 0 1px #b3b3b3;
}
.left-off-canvas-menu {
    background: #333333 none repeat scroll 0 0;
    bottom: 0;
    box-sizing: content-box;
    left: 0;
    overflow-y: auto;
    position: absolute;
    top: 0;
    transform: translate3d(-100%, 0px, 0px);
    width: 250px;
    z-index: 1001;
}
.left-off-canvas-menu * {
}
.right-off-canvas-menu {
    background: #333333 none repeat scroll 0 0;
    bottom: 0;
    box-sizing: content-box;
    overflow-y: auto;
    position: absolute;
    right: 0;
    top: 0;
    transform: translate3d(100%, 0px, 0px);
    width: 250px;
    z-index: 1001;
}
ul.off-canvas-list {
    list-style-type: none;
    margin: 0;
    padding: 0;
}
ul.off-canvas-list li label {
    background: #444444 none repeat scroll 0 0;
    border-bottom: medium none;
    border-top: 1px solid #5e5e5e;
    color: #999999;
    font-weight: bold;
    margin: 0;
    padding: 0.3rem 0.9375rem;
    text-transform: uppercase;
}
ul.off-canvas-list li a {
    border-bottom: 1px solid #262626;
    color: rgba(255, 255, 255, 0.698);
    display: block;
    padding: 0.66667rem;
}
.move-right > .inner-wrap {
    transform: translate3d(250px, 0px, 0px);
}
.move-right a.exit-off-canvas {
    background: rgba(255, 255, 255, 0.2) none repeat scroll 0 0;
    bottom: 0;
    box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    display: block;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: background 300ms ease 0s;
    z-index: 1002;
}
@media only screen and (min-width: 40.063em) {
.move-right a.exit-off-canvas:hover {
    background: rgba(255, 255, 255, 0.05) none repeat scroll 0 0;
}
}
.move-left > .inner-wrap {
    transform: translate3d(-250px, 0px, 0px);
}
.move-left a.exit-off-canvas {
    background: rgba(255, 255, 255, 0.2) none repeat scroll 0 0;
    bottom: 0;
    box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    display: block;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: background 300ms ease 0s;
    z-index: 1002;
}
@media only screen and (min-width: 40.063em) {
.move-left a.exit-off-canvas:hover {
    background: rgba(255, 255, 255, 0.05) none repeat scroll 0 0;
}
}
.csstransforms.no-csstransforms3d .left-off-canvas-menu {
    transform: translate(-100%, 0px);
}
.csstransforms.no-csstransforms3d .right-off-canvas-menu {
    transform: translate(100%, 0px);
}
.csstransforms.no-csstransforms3d .move-left > .inner-wrap {
    transform: translate(-250px, 0px);
}
.csstransforms.no-csstransforms3d .move-right > .inner-wrap {
    transform: translate(250px, 0px);
}
.no-csstransforms .left-off-canvas-menu {
    left: -250px;
}
.no-csstransforms .right-off-canvas-menu {
    right: -250px;
}
.no-csstransforms .move-left > .inner-wrap {
    right: 250px;
}
.no-csstransforms .move-right > .inner-wrap {
    left: 250px;
}
@media only screen and (max-width: 40em) {
.f-dropdown {
    left: 0;
    max-width: 100%;
}
}
.f-dropdown {
    background: white none repeat scroll 0 0;
    border: 1px solid #cccccc;
    font-size: 16px;
    height: auto;
    left: -9999px;
    list-style: outside none none;
    margin-left: 0;
    margin-top: 2px;
    max-height: none;
    max-width: 200px;
    position: absolute;
    width: 100%;
    z-index: 99;
}
.f-dropdown > *:first-child {
    margin-top: 0;
}
.f-dropdown > *:last-child {
    margin-bottom: 0;
}
.f-dropdown::before {
    border-bottom-style: solid;
    content: "";
    display: block;
    height: 0;
    position: absolute;
    right: 10px;
    top: -12px;
    width: 0;
    z-index: 99;
}
.f-dropdown::after {
    border-bottom-style: solid;
    content: "";
    display: block;
    height: 0;
    position: absolute;
    right: 9px;
    top: -14px;
    width: 0;
    z-index: 98;
}
.f-dropdown.right::before {
    left: auto;
    right: 10px;
}
.f-dropdown.right::after {
    left: auto;
    right: 9px;
}
.f-dropdown li {
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1.125rem;
    margin: 0;
}
.f-dropdown li:hover, .f-dropdown li:focus {
    background: #eeeeee none repeat scroll 0 0;
}
.f-dropdown li a {
    color: #555555;
    display: block;
    padding: 0.5rem;
}
.f-dropdown.content {
    background: white none repeat scroll 0 0;
    border: 1px solid #cccccc;
    font-size: 16px;
    height: auto;
    left: -9999px;
    list-style: outside none none;
    margin-left: 0;
    max-height: none;
    max-width: 200px;
    padding: 1.25rem;
    position: absolute;
    width: 100%;
    z-index: 99;
}
.f-dropdown.content > *:first-child {
    margin-top: 0;
}
.f-dropdown.content > *:last-child {
    margin-bottom: 0;
}
.f-dropdown.tiny {
    max-width: 200px;
}
.f-dropdown.small {
    max-width: 300px;
}
.f-dropdown.medium {
    max-width: 500px;
}
.f-dropdown.large {
    max-width: 800px;
}
table {
    background: white none repeat scroll 0 0;
    border: 1px solid #dddddd;
    margin-bottom: 1.25rem;
}
table thead, table tfoot {
    background: whitesmoke none repeat scroll 0 0;
}
table thead tr th, table thead tr td, table tfoot tr th, table tfoot tr td {
    color: #222222;
    font-size: 0.875rem;
    font-weight: bold;
    padding: 0.5rem 0.625rem 0.625rem;
    text-align: left;
}
table tr th, table tr td {
    color: #222222;
    font-size: 0.875rem;
    padding: 0.5625rem 0.625rem;
}
table tr.even, table tr.alt, table tr:nth-of-type(2n) {
    background: #f9f9f9 none repeat scroll 0 0;
}
table thead tr th, table tfoot tr th, table tbody tr td, table tr td, table tfoot tr td {
    display: table-cell;
    line-height: 1.125rem;
}
form {
    margin: 0 0 1rem;
}
form .row .row {
    margin: 0 -0.5rem;
}
form .row .row .column, form .row .row .columns {
    padding: 0 0.5rem;
}
form .row .row.collapse {
    margin: 0;
}
form .row .row.collapse .column, form .row .row.collapse .columns {
    padding: 0;
}
form .row .row.collapse input {
}
form .row input.column, form .row input.columns, form .row textarea.column, form .row textarea.columns {
    padding-left: 0.5rem;
}
label {
    color: #4d4d4d;
    cursor: pointer;
    display: block;
    font-size: 0.875rem;
    font-weight: normal;
    line-height: 1.5;
    margin-bottom: 0;
}
label.right {
    float: none;
    text-align: right;
}
label.inline {
    margin: 0 0 1rem;
    padding: 0.625rem 0;
}
label small {
    color: #676767;
    text-transform: capitalize;
}
select {
    background: #fafafa url("data:image/svg+xml;base64, PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iM3B4IiB2aWV3Qm94PSIwIDAgNiAzIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA2IDMiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwb2x5Z29uIHBvaW50cz0iNS45OTIsMCAyLjk5MiwzIC0wLjAwOCwwICIvPjwvc3ZnPg==") no-repeat scroll 0 0;
    border: 1px solid #cccccc;
    border-radius: 0;
    font-size: 0.875rem;
    padding: 0.5rem;
}
select.radius {
    border-radius: 3px;
}
select:hover {
    background: #f3f3f3 url("data:image/svg+xml;base64, PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iM3B4IiB2aWV3Qm94PSIwIDAgNiAzIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA2IDMiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwb2x5Z29uIHBvaW50cz0iNS45OTIsMCAyLjk5MiwzIC0wLjAwOCwwICIvPjwvc3ZnPg==") no-repeat scroll 0 0;
    border-color: #999999;
}
@-moz-document url-prefix("") {
select {
    background: #fafafa none repeat scroll 0 0;
}
select:hover {
    background: #f3f3f3 none repeat scroll 0 0;
}
}
.prefix, .postfix {
    border-style: solid;
    border-width: 1px;
    display: block;
    font-size: 0.875rem;
    height: 2.3125rem;
    line-height: 2.3125rem;
    overflow: hidden;
    padding-bottom: 0;
    padding-top: 0;
    position: relative;
    text-align: center;
    width: 100%;
    z-index: 2;
}
.postfix.button {
    border: medium none;
    line-height: 2.125rem;
    padding: 0;
    text-align: center;
}
.prefix.button {
    border: medium none;
    line-height: 2.125rem;
    padding: 0;
    text-align: center;
}
.prefix.button.radius {
    border-radius: 3px 0 0 3px;
}
.postfix.button.radius {
    border-radius: 0 3px 3px 0;
}
.prefix.button.round {
    border-radius: 1000px 0 0 1000px;
}
.postfix.button.round {
    border-radius: 0 1000px 1000px 0;
}
span.prefix, label.prefix {
    background: #f2f2f2 none repeat scroll 0 0;
    border-color: #cccccc;
    border-right: medium none #cccccc;
    color: #333333;
}
span.prefix.radius, label.prefix.radius {
    border-radius: 3px 0 0 3px;
}
span.postfix, label.postfix {
    background: #f2f2f2 none repeat scroll 0 0;
    border-color: #cccccc;
    border-left: medium none #cccccc;
    color: #333333;
}
span.postfix.radius, label.postfix.radius {
    border-radius: 0 3px 3px 0;
}
input[type="text"], input[type="password"], input[type="date"], input[type="datetime"], input[type="datetime-local"], input[type="month"], input[type="week"], input[type="email"], input[type="number"], input[type="search"], input[type="tel"], input[type="time"], input[type="url"], textarea {
    background-color: white;
    border: 1px solid #cccccc;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.75);
    display: block;
    font-family: inherit;
    font-size: 0.875rem;
    height: 2.3125rem;
    margin: 0 0 1rem;
    padding: 0.5rem;
    transition: box-shadow 0.45s ease 0s, border-color 0.45s ease-in-out 0s;
    width: 100%;
}
input[type="text"]:focus, input[type="password"]:focus, input[type="date"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="time"]:focus, input[type="url"]:focus, textarea:focus {
    border-color: #999999;
    box-shadow: 0 0 5px #999999;
}
input[type="text"]:focus, input[type="password"]:focus, input[type="date"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="time"]:focus, input[type="url"]:focus, textarea:focus {
    background: #fafafa none repeat scroll 0 0;
    border-color: #999999;
    outline: medium none;
}
input[type="text"][disabled], input[type="password"][disabled], input[type="date"][disabled], input[type="datetime"][disabled], input[type="datetime-local"][disabled], input[type="month"][disabled], input[type="week"][disabled], input[type="email"][disabled], input[type="number"][disabled], input[type="search"][disabled], input[type="tel"][disabled], input[type="time"][disabled], input[type="url"][disabled], textarea[disabled] {
    background-color: #dddddd;
}
input.radius[type="text"], input.radius[type="password"], input.radius[type="date"], input.radius[type="datetime"], input.radius[type="datetime-local"], input.radius[type="month"], input.radius[type="week"], input.radius[type="email"], input.radius[type="number"], input.radius[type="search"], input.radius[type="tel"], input.radius[type="time"], input.radius[type="url"], textarea.radius {
    border-radius: 3px;
}
select {
    height: 2.3125rem;
}
input[type="file"], input[type="checkbox"], input[type="radio"], select {
    margin: 0 0 1rem;
}
input[type="checkbox"] + label, input[type="radio"] + label {
    display: inline-block;
    margin-bottom: 0;
    margin-left: 0.5rem;
    margin-right: 1rem;
    vertical-align: baseline;
}
input[type="file"] {
    width: 100%;
}
fieldset {
    border: 1px solid #dddddd;
    margin: 1.125rem 0;
    padding: 1.25rem;
}
fieldset legend {
    background: white none repeat scroll 0 0;
    font-weight: bold;
    margin: 0 0 0 -0.1875rem;
    padding: 0 0.1875rem;
}
[data-abide] .error small.error, [data-abide] span.error, [data-abide] small.error {
    background: #f04124 none repeat scroll 0 0;
    color: white;
    display: block;
    font-size: 0.75rem;
    font-style: italic;
    font-weight: normal;
    margin-bottom: 1rem;
    margin-top: -1px;
    padding: 0.375rem 0.5625rem 0.5625rem;
}
[data-abide] span.error, [data-abide] small.error {
    display: none;
}
span.error, small.error {
    background: #f04124 none repeat scroll 0 0;
    color: white;
    display: block;
    font-size: 0.75rem;
    font-style: italic;
    font-weight: normal;
    margin-bottom: 1rem;
    margin-top: -1px;
    padding: 0.375rem 0.5625rem 0.5625rem;
}
.error input, .error textarea, .error select {
    margin-bottom: 0;
}
.error input[type="checkbox"], .error input[type="radio"] {
    margin-bottom: 1rem;
}
.error label, .error label.error {
    color: #f04124;
}
.error small.error {
    background: #f04124 none repeat scroll 0 0;
    color: white;
    display: block;
    font-size: 0.75rem;
    font-style: italic;
    font-weight: normal;
    margin-bottom: 1rem;
    margin-top: -1px;
    padding: 0.375rem 0.5625rem 0.5625rem;
}
.error > label > small {
    background: transparent none repeat scroll 0 0;
    color: #676767;
    display: inline;
    font-size: 60%;
    font-style: normal;
    margin: 0;
    padding: 0;
    text-transform: capitalize;
}
.error span.error-message {
    display: block;
}
input.error, textarea.error {
    margin-bottom: 0;
}
label.error {
    color: #f04124;
}
[class*="block-grid-"] {
    display: block;
    margin: 0 -0.625rem;
    padding: 0;
}
[class*="block-grid-"]::before, [class*="block-grid-"]::after {
    content: " ";
    display: table;
}
[class*="block-grid-"]::after {
    clear: both;
}
[class*="block-grid-"] > li {
    display: block;
    float: left;
    height: auto;
    padding: 0 0.625rem 1.25rem;
}
@media only screen {
.small-block-grid-1 > li {
    list-style: outside none none;
    width: 100%;
}
.small-block-grid-1 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-1 > li:nth-of-type(n+1) {
    clear: both;
}
.small-block-grid-2 > li {
    list-style: outside none none;
    width: 50%;
}
.small-block-grid-2 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
}
.small-block-grid-3 > li {
    list-style: outside none none;
    width: 33.3333%;
}
.small-block-grid-3 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
}
.small-block-grid-4 > li {
    list-style: outside none none;
    width: 25%;
}
.small-block-grid-4 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
}
.small-block-grid-5 > li {
    list-style: outside none none;
    width: 20%;
}
.small-block-grid-5 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
}
.small-block-grid-6 > li {
    list-style: outside none none;
    width: 16.6667%;
}
.small-block-grid-6 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
}
.small-block-grid-7 > li {
    list-style: outside none none;
    width: 14.2857%;
}
.small-block-grid-7 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-7 > li:nth-of-type(7n+1) {
    clear: both;
}
.small-block-grid-8 > li {
    list-style: outside none none;
    width: 12.5%;
}
.small-block-grid-8 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-8 > li:nth-of-type(8n+1) {
    clear: both;
}
.small-block-grid-9 > li {
    list-style: outside none none;
    width: 11.1111%;
}
.small-block-grid-9 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-9 > li:nth-of-type(9n+1) {
    clear: both;
}
.small-block-grid-10 > li {
    list-style: outside none none;
    width: 10%;
}
.small-block-grid-10 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-10 > li:nth-of-type(10n+1) {
    clear: both;
}
.small-block-grid-11 > li {
    list-style: outside none none;
    width: 9.09091%;
}
.small-block-grid-11 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-11 > li:nth-of-type(11n+1) {
    clear: both;
}
.small-block-grid-12 > li {
    list-style: outside none none;
    width: 8.33333%;
}
.small-block-grid-12 > li:nth-of-type(n) {
    clear: none;
}
.small-block-grid-12 > li:nth-of-type(12n+1) {
    clear: both;
}
}
@media only screen and (min-width: 40.063em) {
.medium-block-grid-1 > li {
    list-style: outside none none;
    width: 100%;
}
.medium-block-grid-1 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-1 > li:nth-of-type(n+1) {
    clear: both;
}
.medium-block-grid-2 > li {
    list-style: outside none none;
    width: 50%;
}
.medium-block-grid-2 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
}
.medium-block-grid-3 > li {
    list-style: outside none none;
    width: 33.3333%;
}
.medium-block-grid-3 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
}
.medium-block-grid-4 > li {
    list-style: outside none none;
    width: 25%;
}
.medium-block-grid-4 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
}
.medium-block-grid-5 > li {
    list-style: outside none none;
    width: 20%;
}
.medium-block-grid-5 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
}
.medium-block-grid-6 > li {
    list-style: outside none none;
    width: 16.6667%;
}
.medium-block-grid-6 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
}
.medium-block-grid-7 > li {
    list-style: outside none none;
    width: 14.2857%;
}
.medium-block-grid-7 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-7 > li:nth-of-type(7n+1) {
    clear: both;
}
.medium-block-grid-8 > li {
    list-style: outside none none;
    width: 12.5%;
}
.medium-block-grid-8 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-8 > li:nth-of-type(8n+1) {
    clear: both;
}
.medium-block-grid-9 > li {
    list-style: outside none none;
    width: 11.1111%;
}
.medium-block-grid-9 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-9 > li:nth-of-type(9n+1) {
    clear: both;
}
.medium-block-grid-10 > li {
    list-style: outside none none;
    width: 10%;
}
.medium-block-grid-10 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-10 > li:nth-of-type(10n+1) {
    clear: both;
}
.medium-block-grid-11 > li {
    list-style: outside none none;
    width: 9.09091%;
}
.medium-block-grid-11 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-11 > li:nth-of-type(11n+1) {
    clear: both;
}
.medium-block-grid-12 > li {
    list-style: outside none none;
    width: 8.33333%;
}
.medium-block-grid-12 > li:nth-of-type(n) {
    clear: none;
}
.medium-block-grid-12 > li:nth-of-type(12n+1) {
    clear: both;
}
}
@media only screen and (min-width: 64.063em) {
.large-block-grid-1 > li {
    list-style: outside none none;
    width: 100%;
}
.large-block-grid-1 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-1 > li:nth-of-type(n+1) {
    clear: both;
}
.large-block-grid-2 > li {
    list-style: outside none none;
    width: 50%;
}
.large-block-grid-2 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
}
.large-block-grid-3 > li {
    list-style: outside none none;
    width: 33.3333%;
}
.large-block-grid-3 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
}
.large-block-grid-4 > li {
    list-style: outside none none;
    width: 25%;
}
.large-block-grid-4 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
}
.large-block-grid-5 > li {
    list-style: outside none none;
    width: 20%;
}
.large-block-grid-5 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
}
.large-block-grid-6 > li {
    list-style: outside none none;
    width: 16.6667%;
}
.large-block-grid-6 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
}
.large-block-grid-7 > li {
    list-style: outside none none;
    width: 14.2857%;
}
.large-block-grid-7 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-7 > li:nth-of-type(7n+1) {
    clear: both;
}
.large-block-grid-8 > li {
    list-style: outside none none;
    width: 12.5%;
}
.large-block-grid-8 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-8 > li:nth-of-type(8n+1) {
    clear: both;
}
.large-block-grid-9 > li {
    list-style: outside none none;
    width: 11.1111%;
}
.large-block-grid-9 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-9 > li:nth-of-type(9n+1) {
    clear: both;
}
.large-block-grid-10 > li {
    list-style: outside none none;
    width: 10%;
}
.large-block-grid-10 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-10 > li:nth-of-type(10n+1) {
    clear: both;
}
.large-block-grid-11 > li {
    list-style: outside none none;
    width: 9.09091%;
}
.large-block-grid-11 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-11 > li:nth-of-type(11n+1) {
    clear: both;
}
.large-block-grid-12 > li {
    list-style: outside none none;
    width: 8.33333%;
}
.large-block-grid-12 > li:nth-of-type(n) {
    clear: none;
}
.large-block-grid-12 > li:nth-of-type(12n+1) {
    clear: both;
}
}
.flex-video {
    height: 0;
    margin-bottom: 1rem;
    overflow: hidden;
    padding-bottom: 67.5%;
    padding-top: 1.5625rem;
    position: relative;
}
.flex-video.widescreen {
    padding-bottom: 56.55%;
}
.flex-video.vimeo {
    padding-top: 0;
}
.flex-video iframe, .flex-video object, .flex-video embed, .flex-video video {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}
.keystroke, kbd {
    background-color: #ededed;
    border-color: #dddddd;
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    color: #222222;
    font-family: "Consolas","Menlo","Courier",monospace;
    font-size: 0.875rem;
    margin: 0;
    padding: 0.125rem 0.25rem 0;
}
.show-for-small, .show-for-small-only, .show-for-medium-down, .show-for-large-down, .hide-for-medium, .hide-for-medium-up, .hide-for-medium-only, .hide-for-large, .hide-for-large-up, .hide-for-large-only, .hide-for-xlarge, .hide-for-xlarge-up, .hide-for-xlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge-only {
    display: inherit !important;
}
.hide-for-small, .hide-for-small-only, .hide-for-medium-down, .show-for-medium, .show-for-medium-up, .show-for-medium-only, .hide-for-large-down, .show-for-large, .show-for-large-up, .show-for-large-only, .show-for-xlarge, .show-for-xlarge-up, .show-for-xlarge-only, .show-for-xxlarge-up, .show-for-xxlarge-only {
    display: none !important;
}
table.show-for-small, table.show-for-small-only, table.show-for-medium-down, table.show-for-large-down, table.hide-for-medium, table.hide-for-medium-up, table.hide-for-medium-only, table.hide-for-large, table.hide-for-large-up, table.hide-for-large-only, table.hide-for-xlarge, table.hide-for-xlarge-up, table.hide-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
    display: table;
}
thead.show-for-small, thead.show-for-small-only, thead.show-for-medium-down, thead.show-for-large-down, thead.hide-for-medium, thead.hide-for-medium-up, thead.hide-for-medium-only, thead.hide-for-large, thead.hide-for-large-up, thead.hide-for-large-only, thead.hide-for-xlarge, thead.hide-for-xlarge-up, thead.hide-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
    display: table-header-group !important;
}
tbody.show-for-small, tbody.show-for-small-only, tbody.show-for-medium-down, tbody.show-for-large-down, tbody.hide-for-medium, tbody.hide-for-medium-up, tbody.hide-for-medium-only, tbody.hide-for-large, tbody.hide-for-large-up, tbody.hide-for-large-only, tbody.hide-for-xlarge, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
    display: table-row-group !important;
}
tr.show-for-small, tr.show-for-small-only, tr.show-for-medium-down, tr.show-for-large-down, tr.hide-for-medium, tr.hide-for-medium-up, tr.hide-for-medium-only, tr.hide-for-large, tr.hide-for-large-up, tr.hide-for-large-only, tr.hide-for-xlarge, tr.hide-for-xlarge-up, tr.hide-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
    display: table-row !important;
}
td.show-for-small, td.show-for-small-only, td.show-for-medium-down, td.show-for-large-down, td.hide-for-medium, td.hide-for-medium-up, td.hide-for-large, td.hide-for-large-up, td.hide-for-xlarge, td.hide-for-xlarge-up, td.hide-for-xxlarge-up, th.show-for-small, th.show-for-small-only, th.show-for-medium-down, th.show-for-large-down, th.hide-for-medium, th.hide-for-medium-up, th.hide-for-large, th.hide-for-large-up, th.hide-for-xlarge, th.hide-for-xlarge-up, th.hide-for-xxlarge-up {
    display: table-cell !important;
}
@media only screen and (min-width: 40.063em) {
.hide-for-small, .hide-for-small-only, .show-for-medium, .show-for-medium-down, .show-for-medium-up, .show-for-medium-only, .hide-for-large, .hide-for-large-up, .hide-for-large-only, .hide-for-xlarge, .hide-for-xlarge-up, .hide-for-xlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge-only {
    display: inherit !important;
}
.show-for-small, .show-for-small-only, .hide-for-medium, .hide-for-medium-down, .hide-for-medium-up, .hide-for-medium-only, .hide-for-large-down, .show-for-large, .show-for-large-up, .show-for-large-only, .show-for-xlarge, .show-for-xlarge-up, .show-for-xlarge-only, .show-for-xxlarge-up, .show-for-xxlarge-only {
    display: none !important;
}
table.hide-for-small, table.hide-for-small-only, table.show-for-medium, table.show-for-medium-down, table.show-for-medium-up, table.show-for-medium-only, table.hide-for-large, table.hide-for-large-up, table.hide-for-large-only, table.hide-for-xlarge, table.hide-for-xlarge-up, table.hide-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
    display: table;
}
thead.hide-for-small, thead.hide-for-small-only, thead.show-for-medium, thead.show-for-medium-down, thead.show-for-medium-up, thead.show-for-medium-only, thead.hide-for-large, thead.hide-for-large-up, thead.hide-for-large-only, thead.hide-for-xlarge, thead.hide-for-xlarge-up, thead.hide-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
    display: table-header-group !important;
}
tbody.hide-for-small, tbody.hide-for-small-only, tbody.show-for-medium, tbody.show-for-medium-down, tbody.show-for-medium-up, tbody.show-for-medium-only, tbody.hide-for-large, tbody.hide-for-large-up, tbody.hide-for-large-only, tbody.hide-for-xlarge, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
    display: table-row-group !important;
}
tr.hide-for-small, tr.hide-for-small-only, tr.show-for-medium, tr.show-for-medium-down, tr.show-for-medium-up, tr.show-for-medium-only, tr.hide-for-large, tr.hide-for-large-up, tr.hide-for-large-only, tr.hide-for-xlarge, tr.hide-for-xlarge-up, tr.hide-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
    display: table-row !important;
}
td.hide-for-small, td.hide-for-small-only, td.show-for-medium, td.show-for-medium-down, td.show-for-medium-up, td.show-for-medium-only, td.hide-for-large, td.hide-for-large-up, td.hide-for-large-only, td.hide-for-xlarge, td.hide-for-xlarge-up, td.hide-for-xlarge-only, td.hide-for-xxlarge-up, td.hide-for-xxlarge-only, th.hide-for-small, th.hide-for-small-only, th.show-for-medium, th.show-for-medium-down, th.show-for-medium-up, th.show-for-medium-only, th.hide-for-large, th.hide-for-large-up, th.hide-for-large-only, th.hide-for-xlarge, th.hide-for-xlarge-up, th.hide-for-xlarge-only, th.hide-for-xxlarge-up, th.hide-for-xxlarge-only {
    display: table-cell !important;
}
}
@media only screen and (min-width: 64.063em) {
.hide-for-small, .hide-for-small-only, .hide-for-medium, .hide-for-medium-down, .hide-for-medium-only, .show-for-medium-up, .show-for-large, .show-for-large-up, .show-for-large-only, .hide-for-xlarge, .hide-for-xlarge-up, .hide-for-xlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge-only {
    display: inherit !important;
}
.show-for-small-only, .show-for-medium, .show-for-medium-down, .show-for-medium-only, .hide-for-large, .hide-for-large-up, .hide-for-large-only, .show-for-xlarge, .show-for-xlarge-up, .show-for-xlarge-only, .show-for-xxlarge-up, .show-for-xxlarge-only {
    display: none !important;
}
table.hide-for-small, table.hide-for-small-only, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-medium-only, table.show-for-medium-up, table.show-for-large, table.show-for-large-up, table.show-for-large-only, table.hide-for-xlarge, table.hide-for-xlarge-up, table.hide-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
    display: table;
}
thead.hide-for-small, thead.hide-for-small-only, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.show-for-large, thead.show-for-large-up, thead.show-for-large-only, thead.hide-for-xlarge, thead.hide-for-xlarge-up, thead.hide-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
    display: table-header-group !important;
}
tbody.hide-for-small, tbody.hide-for-small-only, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.show-for-large, tbody.show-for-large-up, tbody.show-for-large-only, tbody.hide-for-xlarge, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
    display: table-row-group !important;
}
tr.hide-for-small, tr.hide-for-small-only, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.show-for-large, tr.show-for-large-up, tr.show-for-large-only, tr.hide-for-xlarge, tr.hide-for-xlarge-up, tr.hide-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
    display: table-row !important;
}
td.hide-for-small, td.hide-for-small-only, td.hide-for-medium, td.hide-for-medium-down, td.hide-for-medium-only, td.show-for-medium-up, td.show-for-large, td.show-for-large-up, td.show-for-large-only, td.hide-for-xlarge, td.hide-for-xlarge-up, td.hide-for-xlarge-only, td.hide-for-xxlarge-up, td.hide-for-xxlarge-only, th.hide-for-small, th.hide-for-small-only, th.hide-for-medium, th.hide-for-medium-down, th.hide-for-medium-only, th.show-for-medium-up, th.show-for-large, th.show-for-large-up, th.show-for-large-only, th.hide-for-xlarge, th.hide-for-xlarge-up, th.hide-for-xlarge-only, th.hide-for-xxlarge-up, th.hide-for-xxlarge-only {
    display: table-cell !important;
}
}
@media only screen and (min-width: 90.063em) {
.hide-for-small, .hide-for-small-only, .hide-for-medium, .hide-for-medium-down, .hide-for-medium-only, .show-for-medium-up, .show-for-large-up, .hide-for-large-only, .show-for-xlarge, .show-for-xlarge-up, .show-for-xlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge-only {
    display: inherit !important;
}
.show-for-small-only, .show-for-medium, .show-for-medium-down, .show-for-medium-only, .show-for-large, .show-for-large-only, .show-for-large-down, .hide-for-xlarge, .hide-for-xlarge-up, .hide-for-xlarge-only, .show-for-xxlarge-up, .show-for-xxlarge-only {
    display: none !important;
}
table.hide-for-small, table.hide-for-small-only, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-medium-only, table.show-for-medium-up, table.show-for-large-up, table.hide-for-large-only, table.show-for-xlarge, table.show-for-xlarge-up, table.show-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
    display: table;
}
thead.hide-for-small, thead.hide-for-small-only, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.show-for-large-up, thead.hide-for-large-only, thead.show-for-xlarge, thead.show-for-xlarge-up, thead.show-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
    display: table-header-group !important;
}
tbody.hide-for-small, tbody.hide-for-small-only, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.show-for-large-up, tbody.hide-for-large-only, tbody.show-for-xlarge, tbody.show-for-xlarge-up, tbody.show-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
    display: table-row-group !important;
}
tr.hide-for-small, tr.hide-for-small-only, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.show-for-large-up, tr.hide-for-large-only, tr.show-for-xlarge, tr.show-for-xlarge-up, tr.show-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
    display: table-row !important;
}
td.hide-for-small, td.hide-for-small-only, td.hide-for-medium, td.hide-for-medium-down, td.hide-for-medium-only, td.show-for-medium-up, td.show-for-large-up, td.hide-for-large-only, td.show-for-xlarge, td.show-for-xlarge-up, td.show-for-xlarge-only, td.hide-for-xxlarge-up, td.hide-for-xxlarge-only, th.hide-for-small, th.hide-for-small-only, th.hide-for-medium, th.hide-for-medium-down, th.hide-for-medium-only, th.show-for-medium-up, th.show-for-large-up, th.hide-for-large-only, th.show-for-xlarge, th.show-for-xlarge-up, th.show-for-xlarge-only, th.hide-for-xxlarge-up, th.hide-for-xxlarge-only {
    display: table-cell !important;
}
}
@media only screen and (min-width: 120.063em) {
.hide-for-small, .hide-for-small-only, .hide-for-medium, .hide-for-medium-down, .hide-for-medium-only, .show-for-medium-up, .show-for-large-up, .hide-for-large-only, .hide-for-xlarge-only, .show-for-xlarge-up, .show-for-xxlarge-up, .show-for-xxlarge-only {
    display: inherit !important;
}
.show-for-small-only, .show-for-medium, .show-for-medium-down, .show-for-medium-only, .show-for-large, .show-for-large-only, .show-for-large-down, .hide-for-xlarge, .show-for-xlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge-only {
    display: none !important;
}
table.hide-for-small, table.hide-for-small-only, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-medium-only, table.show-for-medium-up, table.show-for-large-up, table.hide-for-xlarge-only, table.show-for-xlarge-up, table.show-for-xxlarge-up, table.show-for-xxlarge-only {
    display: table;
}
thead.hide-for-small, thead.hide-for-small-only, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.show-for-large-up, thead.hide-for-xlarge-only, thead.show-for-xlarge-up, thead.show-for-xxlarge-up, thead.show-for-xxlarge-only {
    display: table-header-group !important;
}
tbody.hide-for-small, tbody.hide-for-small-only, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.show-for-large-up, tbody.hide-for-xlarge-only, tbody.show-for-xlarge-up, tbody.show-for-xxlarge-up, tbody.show-for-xxlarge-only {
    display: table-row-group !important;
}
tr.hide-for-small, tr.hide-for-small-only, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.show-for-large-up, tr.hide-for-xlarge-only, tr.show-for-xlarge-up, tr.show-for-xxlarge-up, tr.show-for-xxlarge-only {
    display: table-row !important;
}
td.hide-for-small, td.hide-for-small-only, td.hide-for-medium, td.hide-for-medium-down, td.hide-for-medium-only, td.show-for-medium-up, td.show-for-large-up, td.hide-for-xlarge-only, td.show-for-xlarge-up, td.show-for-xxlarge-up, td.show-for-xxlarge-only, th.hide-for-small, th.hide-for-small-only, th.hide-for-medium, th.hide-for-medium-down, th.hide-for-medium-only, th.show-for-medium-up, th.show-for-large-up, th.hide-for-xlarge-only, th.show-for-xlarge-up, th.show-for-xxlarge-up, th.show-for-xxlarge-only {
    display: table-cell !important;
}
}
.show-for-landscape, .hide-for-portrait {
    display: inherit !important;
}
.hide-for-landscape, .show-for-portrait {
    display: none !important;
}
table.hide-for-landscape, table.show-for-portrait {
    display: table;
}
thead.hide-for-landscape, thead.show-for-portrait {
    display: table-header-group !important;
}
tbody.hide-for-landscape, tbody.show-for-portrait {
    display: table-row-group !important;
}
tr.hide-for-landscape, tr.show-for-portrait {
    display: table-row !important;
}
td.hide-for-landscape, td.show-for-portrait, th.hide-for-landscape, th.show-for-portrait {
    display: table-cell !important;
}
@media only screen and (orientation: landscape) {
.show-for-landscape, .hide-for-portrait {
    display: inherit !important;
}
.hide-for-landscape, .show-for-portrait {
    display: none !important;
}
table.show-for-landscape, table.hide-for-portrait {
    display: table;
}
thead.show-for-landscape, thead.hide-for-portrait {
    display: table-header-group !important;
}
tbody.show-for-landscape, tbody.hide-for-portrait {
    display: table-row-group !important;
}
tr.show-for-landscape, tr.hide-for-portrait {
    display: table-row !important;
}
td.show-for-landscape, td.hide-for-portrait, th.show-for-landscape, th.hide-for-portrait {
    display: table-cell !important;
}
}
@media only screen and (orientation: portrait) {
.show-for-portrait, .hide-for-landscape {
    display: inherit !important;
}
.hide-for-portrait, .show-for-landscape {
    display: none !important;
}
table.show-for-portrait, table.hide-for-landscape {
    display: table;
}
thead.show-for-portrait, thead.hide-for-landscape {
    display: table-header-group !important;
}
tbody.show-for-portrait, tbody.hide-for-landscape {
    display: table-row-group !important;
}
tr.show-for-portrait, tr.hide-for-landscape {
    display: table-row !important;
}
td.show-for-portrait, td.hide-for-landscape, th.show-for-portrait, th.hide-for-landscape {
    display: table-cell !important;
}
}
.show-for-touch {
    display: none !important;
}
.hide-for-touch {
    display: inherit !important;
}
.touch .show-for-touch {
    display: inherit !important;
}
.touch .hide-for-touch {
    display: none !important;
}
table.hide-for-touch {
    display: table;
}
.touch table.show-for-touch {
    display: table;
}
thead.hide-for-touch {
    display: table-header-group !important;
}
.touch thead.show-for-touch {
    display: table-header-group !important;
}
tbody.hide-for-touch {
    display: table-row-group !important;
}
.touch tbody.show-for-touch {
    display: table-row-group !important;
}
tr.hide-for-touch {
    display: table-row !important;
}
.touch tr.show-for-touch {
    display: table-row !important;
}
td.hide-for-touch {
    display: table-cell !important;
}
.touch td.show-for-touch {
    display: table-cell !important;
}
th.hide-for-touch {
    display: table-cell !important;
}
.touch th.show-for-touch {
    display: table-cell !important;
}

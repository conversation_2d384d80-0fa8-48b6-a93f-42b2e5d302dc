@charset "utf-8";
/* CSS Document */

@font-face {
	font-family: 'Maven Pro';
	font-style: normal;
	font-weight: 400;
	src: local('Maven Pro Regular'), local('MavenProRegular'), url(../fonts/Maven.woff) format('woff');
}

.clearBoth25 {
	height: 25px;
	width: 100%;
	clear: both;
}
.clearBoth20 {
	height: 20px;
	width: 100%;
	clear: both;
}
.clearBoth15 {
	height: 15px;
	width: 100%;
	clear: both;
}
.clearBoth10 {
	height: 10px;
	width: 100%;
	clear: both;
}
.deliverSearch {
	margin: 0 auto;
	width: 250px;
}
.searchDelivery {
	margin: 10px auto;
	width: 100px;
}
.component {
	line-height: 1.5em;
	margin: 0 auto;
	padding: 2em 0 3em;
	width: 90%;
	max-width: 1000px;
	overflow: hidden;
}
.component .filler {
	font-family: "Blokk", Arial, sans-serif;
	color: #d3d3d3;
}
table tr th, table tr td, table.dataTable tbody th, table.dataTable tbody td {
  border-bottom: 1px solid #f1f1f1;
  padding: 10px 10px;
  font-size: 16px;
  color: #999;
}
button, .button {
	  background: #FC6E51;
  margin: 0px 0 5px 0;
  padding: 10px;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
button:hover, button:focus, .button:hover, .button:focus {
  background: #E9573F;
}

/* For appearance */
.sticky-wrap {
	overflow-x: auto;
	overflow-y: hidden;
	position: relative;
	margin: 0em 0;
	width: 100%;
}
.sticky-wrap .sticky-thead, .sticky-wrap .sticky-col, .sticky-wrap .sticky-intersect {
	opacity: 0;
	position: absolute;
	top: 0;
	left: 0;
	transition: all .125s ease-in-out;
	z-index: 50;
	width: auto; /* Prevent table from stretching to full size */
}
.sticky-wrap .sticky-thead {
	box-shadow: 0 0.25em 0.1em -0.1em rgba(0,0,0,.125);
	z-index: 100;
	width: 100%; /* Force stretch */
}
.sticky-wrap .sticky-intersect {
	opacity: 1;
	z-index: 150;
}
.sticky-wrap .sticky-intersect th {
	background-color: #666;
	color: #eee;
}
.sticky-wrap td, .sticky-wrap th {
	box-sizing: border-box;
}

/* Not needed for sticky header/column functionality */
td.user-name {
	text-transform: capitalize;
}
.sticky-wrap.overflow-y {
	overflow-y: auto;
	max-height: 50vh;
}

.margin-top {
	margin-top: 1%;
}
.portel-top {
	font-size: 1.5 rem;
	text-align: right;
	margin: 0px;
	padding: 5px 0 0 0;
}
.fleft {
	float: left;
}
.yellow {
	background-color: #008CBA !important;
}
.header {
	height: 60px;
}
.success {
	color: #A0D468;
	font-size: 1.200em;
}
.error {
	color: #ED5565;
	font-size: 1.200em;
}

.social {
	background: #343434;
	margin: 0px 0 0;
}
.social p {
	color: #fff;
	font-weight: normal;
	line-height: 28px;
	margin: 0px;
	font-family: 'Alegreya Sans', sans-serif;
	font-size: 11px;
	margin: 0px 10px 0 0;
	padding: 0px;
}
.social-info {
	padding: 16px 0 0 0;
	display: table;
	float: right;
}
.social-info li {
	float: left;
	list-style: none;
	color: #fff;
	font-family: 'Alegreya Sans', sans-serif;
	margin-left: 28px;
	line-height: 32px;
	font-size: 20px;
}
.social-info li a {
	color: #fff;
}
.social-info li a:hover {
	color: #fff;
	text-decoration: none;
}
.social-info li a span {
	background: #fff;
	color: #9F0016;
	border-radius: 3px;
	width: 34px;
	height: 32px;
	font-size: 23px;
	display: table;
	text-align: center;
	float: left;
	margin-right: 10px;
}
.social-info li a:hover span {
	background: #fff;
}
.social-info-mob {
	padding: 16px 20px 28px 0;
	display: table;
	float: right;
}
.social-info-mob li {
	float: left;
	list-style: none;
	color: #fff;
	font-family: 'Alegreya Sans', sans-serif;
	margin-left: 28px;
	line-height: 32px;
	font-size: 16px;
}
.social-info-mob li a {
	color: #333333;
}
.social-info-mob li a:hover {
	color: #fff;
	text-decoration: none;
}
.social-info-mob li a span {
	background: #333333;
	color: #d4dd20;
	border-radius: 3px;
	width: 34px;
	height: 32px;
	font-size: 23px;
	display: table;
	text-align: center;
	float: left;
	margin-right: 10px;
}
.social-info-mob li a:hover span {
	background: #fff;
}
header {
	background: #f1f1f1;
	float: left;
	width: 100%;
}

.ftr-bg {
	background: #f1f1f1;
	border-top: 1px solid #0080ff;
	color: #222;
	font-family: 'Alegreya Sans', sans-serif;
	height: 32px;
	font-size: 12px;
	padding: 8px 2px 8px 12px;
	float: left;
	width: 100%;
}
.ftr-bg img {
	margin: -9px 0 0;
	padding: 2px;
	position: relative;
}

.ftr-bg a {
	color: #222;
	font-size: 12px;
}

/*CSS Written By Sagar*/
.top-bar {
	height: 80px;
	border-bottom: 0 !important;
	background: #FFFFFF;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
	padding-left: 90px;
	position: relative;
	width: 100%;
	top: 0;
	z-index: 999;
}
.top-bar .columns {
	padding: 0;
	/*height: 56px*/
}
.dropdown.button, button.dropdown {
	height: 36px;
	margin: 20px 15px 0;
	padding: 7px;
	border: 1px solid #f1f1f1;
	background-color: transparent;
	opacity: 1;
	color: #999;
}
.dropdown.button:after, button.dropdown:after, .dropdown.button:before, button.dropdown:before {
	border-color: #fff transparent transparent transparent;
	margin-top: 0;
	top: 45%;
}
.top-bar .dropdown.button:before {
	border-color: #999 transparent transparent transparent;
}
.has-tip:hover, .has-tip:focus, .has-tip {
	border-bottom: 0;
}
/*.logo {
	padding: 15px 15px 0;
	display: inline-block;
}*/
.logo {
	width: 252px;
	/*margin: 5px auto 0 auto;*/
	display: block;
	height: 80px;
	padding: 10px
}
.logo img{
	max-width:100%;
	max-height:100%;
}
body {
  margin: 0;
  background: #f5f5f5;
  padding-top: 0px;
  font-family: "Maven Pro", sans-serif;
}
a.logOut {
	height: 45px;
	top: 0px !important;
	padding-left: 1em;
	padding-right: 3em !important;
}
a.logOut:after {
	top: 44% !important;
}
.f-dropdown .divider {
	background-color: #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	height: 1px;
	margin: 5px 1px;
	overflow: hidden;
}
.wid118 {
	max-width: 118px;
}
.f-dropdown li a {
	padding: 7px 12px;
	color: #999;
	font-size: 14px;
}
.f-dropdown {
	border: solid 1px #f1f1f1;
}
.f-dropdown:after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border: inset 7px;
  border-color: transparent transparent #cccccc transparent;
  border-bottom-style: solid;
  position: absolute;
  top: -14px;
  left: 9px;
  z-index: 98;
}
.footer {
	text-align: left;
	color: #999;
	padding: 10px 15px;
	font-size: 16px;
	width: 100%;
	margin: 0px;
	line-height: 30px;
	background: #FFFFFF;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.32);
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.32);
	padding-left: 240px;
}
.footer a {
	color: #FC6E51;
}
.footer img {
	margin: -9px 0 0 0;
	padding: 2px;
	position: relative;
}
body {
	position: static;
}
/* Sticky footer styles
-------------------------------------------------- */
html {
  position: relative;
  min-height: 100%;
}
body {
  /* Margin bottom by footer height */
  margin-bottom: 60px;
}
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height: 60px;
}
ul.pagination li a {
	background: #FC6E51;
  color: white;
  font-weight: bold;
  border: 0;
}
ul.pagination li a:hover {
	color: #fff;
}
ul.pagination li.unavailable a, ul.pagination li.unavailable a:hover {
	  cursor: default;
  color: #FFF;
  border: 0;
  background: #ADADAD;
  box-shadow: none;
}
/*Media Queries Start*/

.logOutSys {
	float: left;
}
.logOutSys a {
	color: #F00;
	font-size: 30px;
	line-height: 30px;
	margin: 10px 12px 0 0;
	display: block;
}
.f-dropdown li a {
	cursor: pointer;
}
table thead tr:nth-of-type(odd) {
	background: #F1EDED;
}
table thead tr th {
	padding: 15px 10px;
}

.tabs button, .print .btn.dropdown {
	height: 38px;
}
ul.pagination li.unavailable a {
	cursor: default;
	color: #FFFFFF;
}
ul.pagination li.current a, ul.pagination li.current a:hover, .dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
	background: #FC6E51 !important;
	color: white !important;
	font-weight: bold;
	cursor: default;
	border: 0;
}
ul.pagination > li > a:hover, .pagination > li > span:hover, .pagination > li > a:focus, .pagination > li > span:focus {
	color: #fff;
}
#drop.f-dropdown.open{
	height: 220px;
    overflow-y: scroll;
}
@media (min-width: 320px) {/* smartphones, portrait iPhone, portrait 480x320 phones (Android) */
	.portel-top {
		text-align: center;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}
}
@media (min-width: 480px) {/* smartphones, Android phones, landscape iPhone */
	.portel-top {
		text-align: center;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}

}
@media (min-width: 600px) {/* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
	.portel-top {
		text-align: center;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}
}
@media (min-width: 801px) {/* tablet, landscape iPad, lo-res laptops ands desktops */
	.portel-top {
		text-align: right;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}
}
@media (min-width: 1025px) {/* big landscape tablets, laptops, and desktops */
	.portel-top {
		text-align: right;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}
}
@media (min-width: 1200px) {/* MAC */
	.portel-top {
		text-align: right;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}

}
@media (min-width: 1281px) {/* hi-res laptops and desktops */
	.portel-top {
		text-align: right;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}
}

@media (min-width: 1366px) {/* big laptops and desktops */
	.portel-top {
		text-align: right;
	}
	.logo {
		width: 252px;
		/*margin: 5px 0 0 0;*/
	}
}

/*Media Queries Ends*/

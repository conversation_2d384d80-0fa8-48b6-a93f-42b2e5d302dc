<?php
/**
 * This is a debug version of index.php to identify the issue
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    echo '<h1>Debug Application Bootstrap</h1>';
    echo '<p>PHP Version: ' . PHP_VERSION . '</p>';
    
    // Load configuration
    echo '<h2>Loading Configuration</h2>';
    $config = include APPLICATION_PATH . '/config/application.config.php';
    echo '<p>Configuration loaded successfully</p>';
    
    // Initialize Zend Application
    echo '<h2>Initializing Zend Application</h2>';
    $application = Zend\Mvc\Application::init($config);
    echo '<p>Zend Application initialized successfully</p>';
    
    // Run the application
    echo '<h2>Running Application</h2>';
    echo '<p>Application is ready to run</p>';
    
    // Don't actually run the application
    // $application->run();
    
} catch (Exception $e) {
    echo '<h2>Error</h2>';
    echo '<p>Error: ' . $e->getMessage() . '</p>';
    echo '<p>File: ' . $e->getFile() . ' (line ' . $e->getLine() . ')</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

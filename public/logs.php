<?php
/**
 * Log Viewer
 * 
 * This script displays the application logs
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php')) {
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php';
    \Lib\QuickServe\Env\EnvLoader::load();
}

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = false;
$userRole = '';

if (isset($_SESSION['user']) && is_array($_SESSION['user'])) {
    $isLoggedIn = true;
    $userRole = isset($_SESSION['user']['rolename']) ? $_SESSION['user']['rolename'] : '';
} elseif (isset($_SESSION['storage'])) {
    try {
        $storage = $_SESSION['storage'];
        
        if (is_object($storage) && isset($storage->pk_user_code)) {
            $isLoggedIn = true;
            $userRole = isset($storage->rolename) ? $storage->rolename : '';
        }
    } catch (\Exception $e) {
        // Ignore
    }
}

// Check if user has admin role
$isAdmin = $isLoggedIn && ($userRole === 'Admin' || $userRole === 'admin');

// Check if this is a development environment
$isDevelopment = true;
if (isset($_SERVER['DEVELOPMENT_MODE'])) {
    $isDevelopment = $_SERVER['DEVELOPMENT_MODE'] === 'true';
} elseif (function_exists('\Lib\QuickServe\Env\EnvLoader::get')) {
    $isDevelopment = \Lib\QuickServe\Env\EnvLoader::get('DEVELOPMENT_MODE', 'true') === 'true';
}

// Only allow access to logs in development mode or for admin users
if (!$isDevelopment && !$isAdmin) {
    header('HTTP/1.1 403 Forbidden');
    echo '<h1>Access Denied</h1>';
    echo '<p>You do not have permission to view logs.</p>';
    exit;
}

// Get log type
$logType = isset($_GET['type']) ? $_GET['type'] : 'auth';
$validLogTypes = ['auth', 'token', 'navigation', 'error', 'all'];
if (!in_array($logType, $validLogTypes)) {
    $logType = 'auth';
}

// Get log file path
$logDir = APPLICATION_PATH . '/data/logs';
$logFiles = [
    'auth' => $logDir . '/auth.log',
    'token' => $logDir . '/token.log',
    'navigation' => $logDir . '/navigation.log',
    'error' => $logDir . '/error.log'
];

// Get log entries
$logEntries = [];

if ($logType === 'all') {
    foreach ($logFiles as $type => $logFile) {
        if (file_exists($logFile)) {
            $entries = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($entries as $entry) {
                $logEntries[] = [
                    'type' => $type,
                    'data' => json_decode($entry, true)
                ];
            }
        }
    }
    
    // Sort by timestamp
    usort($logEntries, function($a, $b) {
        $aTime = isset($a['data']['timestamp']) ? strtotime($a['data']['timestamp']) : 0;
        $bTime = isset($b['data']['timestamp']) ? strtotime($b['data']['timestamp']) : 0;
        return $bTime - $aTime; // Descending order
    });
} else {
    $logFile = $logFiles[$logType];
    
    if (file_exists($logFile)) {
        $entries = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($entries as $entry) {
            $logEntries[] = [
                'type' => $logType,
                'data' => json_decode($entry, true)
            ];
        }
        
        // Sort by timestamp
        usort($logEntries, function($a, $b) {
            $aTime = isset($a['data']['timestamp']) ? strtotime($a['data']['timestamp']) : 0;
            $bTime = isset($b['data']['timestamp']) ? strtotime($b['data']['timestamp']) : 0;
            return $bTime - $aTime; // Descending order
        });
    }
}

// Limit entries
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;
if ($limit <= 0) {
    $limit = 100;
}
$logEntries = array_slice($logEntries, 0, $limit);

// Filter entries
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
if (!empty($filter)) {
    $filteredEntries = [];
    foreach ($logEntries as $entry) {
        $entryJson = json_encode($entry['data']);
        if (stripos($entryJson, $filter) !== false) {
            $filteredEntries[] = $entry;
        }
    }
    $logEntries = $filteredEntries;
}

// Output format
$format = isset($_GET['format']) ? $_GET['format'] : 'html';
if ($format === 'json') {
    header('Content-Type: application/json');
    echo json_encode($logEntries, JSON_PRETTY_PRINT);
    exit;
}

// HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>Log Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .log-nav {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .log-nav a {
            display: inline-block;
            margin-right: 10px;
            padding: 5px 10px;
            background-color: #f0f0f0;
            color: #333;
            text-decoration: none;
            border-radius: 3px;
        }
        .log-nav a.active {
            background-color: #007bff;
            color: #fff;
        }
        .log-filter {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .log-filter input[type="text"] {
            padding: 5px;
            width: 300px;
        }
        .log-filter button {
            padding: 5px 10px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .log-entries {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .log-entry {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .log-entry-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .log-entry-type {
            font-weight: bold;
            text-transform: uppercase;
        }
        .log-entry-timestamp {
            color: #666;
        }
        .log-entry-message {
            margin-bottom: 5px;
        }
        .log-entry-details {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .log-entry-auth {
            border-left: 5px solid #28a745;
        }
        .log-entry-token {
            border-left: 5px solid #fd7e14;
        }
        .log-entry-navigation {
            border-left: 5px solid #007bff;
        }
        .log-entry-error {
            border-left: 5px solid #dc3545;
        }
        .no-logs {
            padding: 20px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Log Viewer</h1>
        
        <div class="log-nav">
            <a href="?type=auth" <?php echo $logType === 'auth' ? 'class="active"' : ''; ?>>Auth Logs</a>
            <a href="?type=token" <?php echo $logType === 'token' ? 'class="active"' : ''; ?>>Token Logs</a>
            <a href="?type=navigation" <?php echo $logType === 'navigation' ? 'class="active"' : ''; ?>>Navigation Logs</a>
            <a href="?type=error" <?php echo $logType === 'error' ? 'class="active"' : ''; ?>>Error Logs</a>
            <a href="?type=all" <?php echo $logType === 'all' ? 'class="active"' : ''; ?>>All Logs</a>
        </div>
        
        <div class="log-filter">
            <form method="get">
                <input type="hidden" name="type" value="<?php echo htmlspecialchars($logType); ?>">
                <input type="text" name="filter" value="<?php echo htmlspecialchars($filter); ?>" placeholder="Filter logs...">
                <button type="submit">Filter</button>
                <?php if (!empty($filter)): ?>
                    <a href="?type=<?php echo htmlspecialchars($logType); ?>">Clear Filter</a>
                <?php endif; ?>
            </form>
        </div>
        
        <div class="log-entries">
            <?php if (empty($logEntries)): ?>
                <div class="no-logs">No logs found</div>
            <?php else: ?>
                <?php foreach ($logEntries as $entry): ?>
                    <div class="log-entry log-entry-<?php echo $entry['type']; ?>">
                        <div class="log-entry-header">
                            <span class="log-entry-type"><?php echo htmlspecialchars($entry['type']); ?></span>
                            <span class="log-entry-timestamp"><?php echo isset($entry['data']['timestamp']) ? htmlspecialchars($entry['data']['timestamp']) : 'Unknown'; ?></span>
                        </div>
                        <div class="log-entry-message">
                            <?php
                            $message = '';
                            if (isset($entry['data']['message'])) {
                                $message = $entry['data']['message'];
                            } elseif (isset($entry['data']['user_id'])) {
                                $message = 'User ID: ' . $entry['data']['user_id'];
                                if (isset($entry['data']['username'])) {
                                    $message .= ' (' . $entry['data']['username'] . ')';
                                }
                            } elseif (isset($entry['data']['path'])) {
                                $message = 'Path: ' . $entry['data']['path'];
                            }
                            echo htmlspecialchars($message);
                            ?>
                        </div>
                        <div class="log-entry-details">
                            <?php echo htmlspecialchars(json_encode($entry['data'], JSON_PRETTY_PRINT)); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

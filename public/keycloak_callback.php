<?php
// This is a mock Keycloak callback page
// In a real implementation, this would exchange the authorization code for tokens

// Start session
session_start();

// Check if this is a form submission (our simulation) or a real callback
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // This is our simulation
    $username = isset($_POST['username']) ? $_POST['username'] : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $state = isset($_POST['state']) ? $_POST['state'] : '';
    
    // Verify state
    if (!isset($_SESSION['keycloak_state']) || $_SESSION['keycloak_state'] !== $state) {
        die('Invalid state parameter');
    }
    
    // Mock user data
    $validUsers = [
        '<EMAIL>' => [
            'password' => 'admin123',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'role' => 'admin',
            'sub' => 'f81d4fae-7dec-11d0-a765-00a0c91e6bf6'
        ],
        '<EMAIL>' => [
            'password' => 'admin123',
            'first_name' => 'Regular',
            'last_name' => 'User',
            'role' => 'user',
            'sub' => '7d793789-2b23-4ab0-9b9a-534098211487'
        ]
    ];
    
    // Check if user exists and password is correct
    if (isset($validUsers[$username]) && $validUsers[$username]['password'] === $password) {
        // Authentication successful
        $userData = $validUsers[$username];
        
        // Mock tokens
        $tokens = [
            'access_token' => 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfMjdGOEgwbGZvQzBpSzBvNkNyZXpqNE1rQ2xMaWIyX0FpRzlIWUZMUFdVIn0...',
            'refresh_token' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            'id_token' => 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfMjdGOEgwbGZvQzBpSzBvNkNyZXpqNE1rQ2xMaWIyX0FpRzlIWUZMUFdVIn0...',
            'expires_in' => 300,
            'refresh_expires_in' => 1800
        ];
        
        // Store user data in session
        $_SESSION['user'] = [
            'username' => $username,
            'first_name' => $userData['first_name'],
            'last_name' => $userData['last_name'],
            'role' => $userData['role'],
            'sub' => $userData['sub'],
            'auth_type' => 'keycloak',
            'tokens' => $tokens
        ];
        
        // Redirect to dashboard
        header('Location: dashboard.php');
        exit;
    } else {
        // Authentication failed
        header('Location: keycloak_login.php?error=invalid_credentials');
        exit;
    }
} else {
    // This would be a real callback from Keycloak
    $code = isset($_GET['code']) ? $_GET['code'] : '';
    $state = isset($_GET['state']) ? $_GET['state'] : '';
    
    // Verify state
    if (!isset($_SESSION['keycloak_state']) || $_SESSION['keycloak_state'] !== $state) {
        die('Invalid state parameter');
    }
    
    // In a real implementation, we would exchange the code for tokens
    // For now, we'll just show a message
    echo '<h1>Keycloak Callback</h1>';
    echo '<p>This is where we would exchange the authorization code for tokens.</p>';
    echo '<p>Code: ' . htmlspecialchars($code) . '</p>';
    echo '<p>State: ' . htmlspecialchars($state) . '</p>';
    echo '<p><a href="login.php">Back to Login</a></p>';
}
?>

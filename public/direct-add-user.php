<?php
/**
 * This is a direct script to add test users without using the Zend Framework
 */

// Start session
session_start();

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define SQLite database path
$dbPath = __DIR__ . '/../data/db/test.db';
$dbDir = dirname($dbPath);

// Create database directory if it doesn't exist
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0777, true);
}

// Function to initialize the database
function initializeDatabase($dbPath) {
    // Create or open the database
    $db = new SQLite3($dbPath);

    // Check if users table exists
    $result = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
    $tableExists = $result->fetchArray();

    if (!$tableExists) {
        // Create users table
        $db->exec("CREATE TABLE users (
            pk_user_code INTEGER PRIMARY KEY,
            email_id TEXT,
            password TEXT,
            first_name TEXT,
            last_name TEXT,
            phone TEXT,
            gender TEXT,
            city TEXT,
            role_id INTEGER,
            status INTEGER,
            third_party_id TEXT
        )");

        echo "<p>Users table created successfully.</p>";
    }

    // Check if roles table exists
    $result = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='roles'");
    $tableExists = $result->fetchArray();

    if (!$tableExists) {
        // Create roles table
        $db->exec("CREATE TABLE roles (
            pk_role_id INTEGER PRIMARY KEY,
            role_name TEXT,
            description TEXT,
            status INTEGER
        )");

        // Add default roles
        $roles = [
            ['pk_role_id' => 1, 'role_name' => 'Admin', 'description' => 'Administrator', 'status' => 1],
            ['pk_role_id' => 2, 'role_name' => 'User', 'description' => 'Regular User', 'status' => 1],
            ['pk_role_id' => 3, 'role_name' => 'Chef', 'description' => 'Kitchen Staff', 'status' => 1],
            ['pk_role_id' => 4, 'role_name' => 'Delivery Person', 'description' => 'Delivery Staff', 'status' => 1]
        ];

        $stmt = $db->prepare("INSERT INTO roles (pk_role_id, role_name, description, status) VALUES (?, ?, ?, ?)");

        foreach ($roles as $role) {
            $stmt->reset();
            $stmt->bindValue(1, $role['pk_role_id'], SQLITE3_INTEGER);
            $stmt->bindValue(2, $role['role_name'], SQLITE3_TEXT);
            $stmt->bindValue(3, $role['description'], SQLITE3_TEXT);
            $stmt->bindValue(4, $role['status'], SQLITE3_INTEGER);
            $stmt->execute();
        }

        echo "<p>Roles table created and populated successfully.</p>";
    }

    return $db;
}

// Function to add test users
function addTestUsers($db) {
    // Clear existing users
    $db->exec("DELETE FROM users");

    // Add test users
    $users = [
        ['pk_user_code' => 1, 'email_id' => '<EMAIL>', 'password' => 'password', 'first_name' => 'Admin', 'last_name' => 'User', 'role_id' => 1, 'status' => 1],
        ['pk_user_code' => 2, 'email_id' => '<EMAIL>', 'password' => 'password', 'first_name' => 'Regular', 'last_name' => 'User', 'role_id' => 2, 'status' => 1],
        ['pk_user_code' => 3, 'email_id' => '<EMAIL>', 'password' => 'password', 'first_name' => 'Chef', 'last_name' => 'User', 'role_id' => 3, 'status' => 1],
        ['pk_user_code' => 4, 'email_id' => '<EMAIL>', 'password' => 'password', 'first_name' => 'Delivery', 'last_name' => 'User', 'role_id' => 4, 'status' => 1]
    ];

    $stmt = $db->prepare("INSERT INTO users (pk_user_code, email_id, password, first_name, last_name, role_id, status) VALUES (?, ?, ?, ?, ?, ?, ?)");

    foreach ($users as $user) {
        $stmt->reset();
        $stmt->bindValue(1, $user['pk_user_code'], SQLITE3_INTEGER);
        $stmt->bindValue(2, $user['email_id'], SQLITE3_TEXT);
        $stmt->bindValue(3, $user['password'], SQLITE3_TEXT);
        $stmt->bindValue(4, $user['first_name'], SQLITE3_TEXT);
        $stmt->bindValue(5, $user['last_name'], SQLITE3_TEXT);
        $stmt->bindValue(6, $user['role_id'], SQLITE3_INTEGER);
        $stmt->bindValue(7, $user['status'], SQLITE3_INTEGER);
        $stmt->execute();
    }

    echo "<p>Test users added successfully.</p>";
}

// Function to display users
function displayUsers($db) {
    $result = $db->query("SELECT u.*, r.role_name FROM users u LEFT JOIN roles r ON u.role_id = r.pk_role_id");

    echo "<h2>Available Users</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Email</th><th>First Name</th><th>Last Name</th><th>Role</th><th>Status</th><th>Password</th></tr>";

    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['pk_user_code']) . "</td>";
        echo "<td>" . htmlspecialchars($row['email_id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['first_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['last_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['role_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['status']) . "</td>";
        echo "<td>" . htmlspecialchars($row['password']) . "</td>";
        echo "</tr>";
    }

    echo "</table>";
}

// Main execution
try {
    echo "<h1>Adding Test Users to SQLite Database</h1>";

    // Initialize database
    $db = initializeDatabase($dbPath);

    // Add test users
    addTestUsers($db);

    // Display users
    displayUsers($db);

    // Close database connection
    $db->close();

    echo "<h2>Test Credentials</h2>";
    echo "<p>You can use the following credentials to test the login:</p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> Email: <EMAIL>, Password: password</li>";
    echo "<li><strong>User:</strong> Email: <EMAIL>, Password: password</li>";
    echo "<li><strong>Chef:</strong> Email: <EMAIL>, Password: password</li>";
    echo "<li><strong>Delivery:</strong> Email: <EMAIL>, Password: password</li>";
    echo "</ul>";

    echo "<p>Note: In development mode, the password is not hashed, so you can use the password as-is.</p>";

    echo "<p><a href='/auth'>Go to Login Page</a></p>";

} catch (Exception $e) {
    echo "<h1>Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

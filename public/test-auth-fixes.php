<?php
/**
 * Test Authentication Fixes
 *
 * This script tests the fixes we've made to the authentication system.
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Start session
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Set global variables
$GLOBALS['company_id'] = 1;
$GLOBALS['unit_id'] = 1;

// HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication Fixes</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <style>
        body {
            padding: 20px;
        }
        .container {
            max-width: 1200px;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 3px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Authentication Fixes</h1>

        <div class="section">
            <h2>1. APPLICATION_PATH Constant</h2>
            <?php
            if (defined('APPLICATION_PATH')) {
                echo "<p class='success'>APPLICATION_PATH is defined: " . APPLICATION_PATH . "</p>";
            } else {
                echo "<p class='error'>APPLICATION_PATH is not defined</p>";
            }
            ?>
        </div>

        <div class="section">
            <h2>2. Global Variables</h2>
            <?php
            if (isset($GLOBALS['company_id'])) {
                echo "<p class='success'>company_id is defined: " . $GLOBALS['company_id'] . "</p>";
            } else {
                echo "<p class='error'>company_id is not defined</p>";
            }

            if (isset($GLOBALS['unit_id'])) {
                echo "<p class='success'>unit_id is defined: " . $GLOBALS['unit_id'] . "</p>";
            } else {
                echo "<p class='error'>unit_id is not defined</p>";
            }
            ?>
        </div>

        <div class="section">
            <h2>3. Mock QSelect Class</h2>
            <?php
            $qSelectPath = realpath(APPLICATION_PATH . '/vendor/Lib/QuickServe/Db/Sql/QSelect.php');
            $mockQSelectPath = realpath(APPLICATION_PATH . '/vendor/Lib/QuickServe/Db/Sql/MockQSelect.php');

            if (file_exists($qSelectPath)) {
                echo "<p class='success'>QSelect class exists: " . $qSelectPath . "</p>";
            } elseif (file_exists($mockQSelectPath)) {
                echo "<p class='success'>MockQSelect class exists: " . $mockQSelectPath . "</p>";
            } else {
                echo "<p class='error'>Neither QSelect nor MockQSelect class exists</p>";
            }

            // Try to load the class
            try {
                require_once APPLICATION_PATH . '/module/QuickServe/src/QuickServe/Autoloader.php';
                \QuickServe\Autoloader::register();

                if (class_exists('Lib\QuickServe\Db\Sql\QSelect')) {
                    echo "<p class='success'>QSelect class is loadable</p>";

                    // Try to instantiate the class
                    $qSelect = new \Lib\QuickServe\Db\Sql\QSelect();
                    echo "<p class='success'>QSelect class is instantiable</p>";
                    echo "<p>Company ID: " . $qSelect->_companyId . "</p>";
                    echo "<p>Unit ID: " . $qSelect->_unitId . "</p>";
                } else {
                    echo "<p class='error'>QSelect class is not loadable</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>Error loading QSelect class: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>

        <div class="section">
            <h2>4. Session Handling</h2>
            <?php
            // Check if session is active
            if (session_status() === PHP_SESSION_ACTIVE) {
                echo "<p class='success'>Session is active</p>";

                // Create a test user
                $testUser = [
                    'pk_user_code' => 999,
                    'first_name' => 'Test',
                    'last_name' => 'User',
                    'email_id' => '<EMAIL>',
                    'rolename' => 'Admin',
                    'auth_type' => 'legacy'
                ];

                // Test storing in session
                $_SESSION['user'] = $testUser;
                echo "<p class='success'>Test user stored in session</p>";

                // Test retrieving from session
                if (isset($_SESSION['user']) && $_SESSION['user']['pk_user_code'] === 999) {
                    echo "<p class='success'>Test user retrieved from session</p>";
                } else {
                    echo "<p class='error'>Failed to retrieve test user from session</p>";
                }

                // Test session serialization
                $serialized = serialize($_SESSION);
                $unserialized = unserialize($serialized);

                if (isset($unserialized['user']) && $unserialized['user']['pk_user_code'] === 999) {
                    echo "<p class='success'>Session serialization works</p>";
                } else {
                    echo "<p class='error'>Session serialization failed</p>";
                }

                // Show session data
                echo "<h3>Session Data</h3>";
                echo "<pre>" . print_r($_SESSION, true) . "</pre>";
            } else {
                echo "<p class='error'>Session is not active</p>";
            }
            ?>
        </div>

        <div class="section">
            <h2>5. Logging</h2>
            <?php
            $logDir = realpath(APPLICATION_PATH . '/data/logs');

            if (is_dir($logDir)) {
                echo "<p class='success'>Log directory exists: " . $logDir . "</p>";

                // Check log files
                $logFiles = ['auth.log', 'navigation.log', 'token.log', 'error.log'];
                foreach ($logFiles as $logFile) {
                    $logPath = $logDir . '/' . $logFile;
                    if (file_exists($logPath)) {
                        echo "<p class='success'>" . $logFile . " exists</p>";

                        // Show log file size
                        $size = filesize($logPath);
                        echo "<p>Size: " . number_format($size) . " bytes</p>";

                        // Show last few lines
                        $lines = file($logPath);
                        $lastLines = array_slice($lines, -5);

                        echo "<h4>Last few lines of " . $logFile . ":</h4>";
                        echo "<pre>";
                        foreach ($lastLines as $line) {
                            echo htmlspecialchars($line);
                        }
                        echo "</pre>";
                    } else {
                        echo "<p class='error'>" . $logFile . " does not exist</p>";
                    }
                }
            } else {
                echo "<p class='error'>Log directory does not exist</p>";
            }
            ?>
        </div>

        <div class="section">
            <h2>Navigation</h2>
            <p><a href="/auth" class="btn btn-primary">Login Page</a></p>
            <p><a href="/auth-logs.php" class="btn btn-info">Auth Logs</a></p>
            <p><a href="/auth-debug.php" class="btn btn-default">Auth Debug</a></p>
            <p><a href="/dashboard" class="btn btn-success">Dashboard</a></p>
            <p><a href="/test-quickserve.php" class="btn btn-warning">Test QuickServe</a></p>
            <p><a href="/direct-logout.php" class="btn btn-danger">Logout</a></p>
        </div>
    </div>
</body>
</html>

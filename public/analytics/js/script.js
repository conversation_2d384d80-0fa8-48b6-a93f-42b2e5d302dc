var avgMealChart;
var chart;
var salesChart; 
var glCurrencySymbol;

function getMonthName(month){
    
    var monthNames = ["January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ];

    return monthNames[month-1];
}


function avgMealPerCustomer(kitchen_id, year, month ){
    
    if (typeof month === "undefined" || month === null) { 
        month = ''; 
    }
    
    var monthName = getMonthName(month);

    var title = 'Average meal purchased by customers in '+monthName+', '+year;
    
    $.ajax({
        url: "/analytics/sales/avgMealPerCustomer",
        method: 'POST',
        data:{kitchen_id:kitchen_id, year: year, month: month},
        dataType:"json",
        async: false,
        success : function(result){
            var data =[]; var column = []; var qty = []; 
            
            $.each(result,function(key,value){              
                qty.push(parseFloat(value.qty));
                column.push(value.meal_name); 
            });

            var data = [{
                        name: 'Average quantity/month',
                        data:qty
                    }];
                
            if(!jQuery.isEmptyObject(result)){

                avgMealChart = new Highcharts.Chart({
                    chart : {
                        renderTo : "Average_meal",
                        type : 'column',
                    },
                    credits : {
                        enabled : false
                    },
                    title : {
                        text : title
                    },

                    xAxis : {
                        categories : column,
                        title : {
                            text : 'Meals'
                        }
                    },
                    yAxis : {
                        allowDecimals : false,
                        min : 0,
                        title : {
                            text : 'Quantity'
                        }
                    }, 
                    legend: {
                        enabled: true
                    },
                    tooltip: {
                        formatter: function() {
                            return '<b>' + this.x + '</b><br/>' + this.series.name + ': ' + this.y;
                        }
                    },
                    series : data
                });
                
            }else{
                $('#Average_meal').empty().html("There is no data to display");
            }
        }
    })
}

function avgMealChangeEvent(kitchen_id, select){
    
    var period  = $('#avgMealperiod').val();
    var year    = $('#avgMealyear').val();
    var month   = $('#avgMealmonth').val();
   
    var label = (period == 'yearly')? 'year': 'month';
    
    if(year != $('#avgMealyear').data('oldyear')){
        month = getMonths('avgMealyear', 'avgMealmonth',kitchen_id, year);
    }

    if(period == 'yearly'){
        $('#avgMealmonth').addClass('hide').removeClass('show'); month = null;
    }else{
        $('#avgMealmonth').addClass('show').removeClass('hide');
    }
    
//        console.log('period:'+period+' year:'+year+' month:'+month); debugger;
    
        $.ajax({
        url: "/analytics/sales/avgMealPerCustomer",
        method: 'POST',
        data:{kitchen_id:kitchen_id, year: year, month: month},
        dataType:"json",
        async: false,
        success : function(result){
                if(!jQuery.isEmptyObject(result)){
                    
                    $('#Average_meal').empty();
                    var data =[]; var column = []; var qty = []; 

                    $.each(result,function(key, value){              
                        qty.push(parseFloat(value.qty));
                        column.push(value.meal_name); 
                    });

                    var monthName = getMonthName(month);

                    var title = '';

                    if(month == '' || month == null){
                        title = 'Average meal purchased by customers in '+year;
                    }else{
                        title = 'Average meal purchased by customers in '+monthName+', '+year;
                    }

                    avgMealChart.setTitle({text: title});
                    avgMealChart.series[0].setData(qty);
                    avgMealChart.series[0].update({
                                        name: 'Average quantity/'+label,
                                    });     
                    avgMealChart.xAxis[0].setCategories(column);
                }else{
                    $('#Average_meal').empty().html("There is no data to display");
                }
            }
        });
}

function getMonths(yearId, monthId, kitchen_id, year)
{
    $.ajax({
        url: "/analytics/sales/avgMealGetMonths",
        method: 'POST',
        data:{kitchen_id:kitchen_id, year: year},
        dataType:"json",
        async: false,
        success : function(result){

            $('#'+monthId).html('');
            month = Object.keys(result)[0];

            $.each(result, function (i, item) {
                $('#'+monthId).append($('<option>', { 
                    value: i,
                    text : item 
                }));
            });
        }
    });

    $('#'+yearId).data('oldyear',year);
    
    return month;
}

function commonPaymentMode(kitchen_id){
    $.ajax({
        url: "/analytics/sales/commonPaymentMode",
        method: 'POST',
        data:{kitchen_id:kitchen_id},
        dataType:"json",
        async: false,
        success : function(result){
            var data = result;
                   
            if(!jQuery.isEmptyObject(data)){
                
                $('#Common_payment').highcharts({
                            chart : {
                                    plotBackgroundColor : null,
                                    plotBorderWidth : 0,
                                    plotShadow : false
                            },
                            credits : {
                                    enabled : false
                            },
                            title : {
                                    text : 'Payment <br>mode <br>shares',
                                    align : 'center',
                                    verticalAlign : 'middle',
                                    y : 40
                            },
                            tooltip : {
                                    pointFormat : '{series.name}: <b>{point.percentage:.1f}%</b>'
                            },
                            plotOptions : {
                                pie : {
                                    dataLabels : {
                                        enabled : true,
                                        distance : -50,
                                        style : {
                                            fontWeight : 'bold',
                                            color : 'white',
                                            textShadow : '0px 1px 2px black'
                                        }
                                    },
                                    startAngle : -90,
                                    endAngle : 90,
                                    center : ['50%', '75%']
                                }
                            },
                            series : [{
                                type : 'pie',
                                name : 'Payment mode share',
                                innerSize : '50%',
                                data : result
                            }]
                    });
            }else{
                $('#Common_payment').empty().html("There is no data to display");
            }
        }
    })
}
//    Highcharts.setOptions({
//     colors: ['#50B432', '#ED561B', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#FFF263',  '#6AF9C4']
//    });


function revenueShare(kitchen_id, param, year, month, currencySymbol) {
    
    var currencySymbol = currencySymbol;
    if (typeof month === "undefined" || month === null) { 
        month = ''; 
    }
    var period  = $('#revenueSharePeriod').val();
    var year    = $('#revenueShareYear').val();
    var month   = $('#revenueShareMonth').val();
   
    var label   = (period == 'yearly')? 'year': 'month';
    
    $.ajax({
       url: "/analytics/sales/revenueShare",
       method: 'POST',
       data:{kitchen_id:kitchen_id, param: param, year: year, month: month},
       dataType:"json",
       async: false,
       success : function(result){
       
           if(!jQuery.isEmptyObject(result)){
               
                chart = new Highcharts.Chart({
                     chart : {
                            renderTo : "Revenue_share",
                            type : 'pie',
                            options3d : {
                                    enabled : true,
                                    alpha : 45,
                                    beta : 0
                            }
                    },
                    credits : {
                            enabled : false
                    },
                    title : {
                        text : 'Revenue Share'
                    },
                    tooltip: {
                         pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                    '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
                    footerFormat: '</table>',
                    },
                    plotOptions: {
                        pie: {
                            allowPointSelect: true,
                            cursor: 'pointer',
                            dataLabels: {
                                enabled: true,
                            }
                        }
                    },
                    series: [{
                        name: 'Revenue Share ('+currencySymbol+')',
                        data: [{
                            name: 'Actual Amount',
                            y: parseInt(result['Actual Amount'])
                        }, {
                            name: 'Gross Amount',
                            y: parseInt(result['Gross Amount'])
                        }, {
                            name: 'Applied Discount',
                            y: parseInt(result['Applied Discount'])
                        }, {
                            name: 'Tax',
                            y: parseInt(result['Tax'])
                        }, {
                            name: 'Service Charges',
                            y: parseInt(result['Service Charges'])
                        }, {
                            name: 'Delivery Charges',
                            y: parseInt(result['Delivery Charges'])
                        }]
                    }]

                });
           
            }else{
                $('#Revenue_share').empty().html("There is no data to display");
            }
        }
       
   });

}

function revenueShareChangeEvent(kitchen_id) {
    
    var param   = $('#revenueSharePeriod').val();
    var year    = $('#revenueShareYear').val();
    var month   = $('#revenueShareMonth').val();
   
    if(year != $('#revenueShareYear').data('oldyear')){
        month = getMonths('revenueShareYear','revenueShareMonth', kitchen_id, year);
    }
    
    if(param == 'yearly'){
        $('#revenueShareMonth').addClass('hide').removeClass('show'); month = null;
    }else{
        $('#revenueShareMonth').addClass('show').removeClass('hide');
    }
    
    $.ajax({
        url: "/analytics/sales/revenueShare",
        method: 'POST',
        data:{kitchen_id:kitchen_id, param: param, year: year, month: month},
        dataType:"json",
        async: false,
        success : function(data){
            
            if(!jQuery.isEmptyObject(data)){
                
                $('#Revenue_share').empty();
                var gross = data.shift(); 

                chart.series[0].setData(data);

                chart.tooltip.options.formatter = function(){
                        return this.series.name+':<b> '+(this.y*100/gross[1]).toFixed(2)+'%</b><br>Sales: ('+glCurrencySymbol+') <b> '+this.y+'</b>' ;
                }
            }else{
                $('#Revenue_share').empty().html("There is no data to display");
            }
        }
    });

}



function salesComparison(kitchen_id, param, year,currencySymbol){
   
    glCurrencySymbol = currencySymbol;
    
    var label = (param === 'monthly') ? 'month' : 'year';
    label = label + '(' + glCurrencySymbol + ') ';
    $.ajax({
        url: "/analytics/sales/salesComparison",
        method: 'POST',
        data:{kitchen_id:kitchen_id, param: param, year: year},
        dataType:"json",
        async: false,
        success : function(result){

            var categories  = result.period;

            var series = [
                {
                    name : 'Gross sales/'+ label,
                    data : [parseInt(result.gross[0])],
                }, 
                {
                    name : 'Net sales/'+ label,
                    data : [parseInt(result.net[0])],
                }
            ];
         
            if(!jQuery.isEmptyObject(result)){
               
                salesChart = new Highcharts.Chart({
                    chart : {
                                renderTo : "Sales_comparison",
                                type : 'column',
                            },
                    title : {
                            text : 'Sales Comparison'
                    },
                    credits : {
                            enabled : false
                    },
                    xAxis : {
                            categories : categories,
                            crosshair : true
                    },
                    yAxis : {
                            min : 0,
                            title : {
                                    text : 'Sales ('+currencySymbol+')'
                            }
                    },
                    tooltip : {
                            headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                            pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                            '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
                            footerFormat: '</table>',
                            shared: true,
                            useHTML: true
                    },
                    plotOptions : {
                            column : {
                                    pointPadding : 0.2,
                                    borderWidth : 0
                            }
                    },
                    series : series
                });           
            }else{
                $('#Sales_comparison').empty().html("There is no data to display");
            }
    }
       
   });
}

function salesComparisonChangeEvent(kitchen_id, select) {
    
    var period  = $('#salesComparisonPeriod').val();
    var year    = $('#salesComparisonYear').val();
    
    var label = (period == 'monthly') ? 'month' : 'year';
    
    label = label + '(' + glCurrencySymbol + ') ';
    
    if(period == 'yearly'){
        $('#salesComparisonYear').addClass('hide').removeClass('show'); year = null;
    }else{
        $('#salesComparisonYear').addClass('show').removeClass('hide');
    }
    
    $.ajax({
        url: "/analytics/sales/salesComparison",
        method: 'POST',
        data:{kitchen_id:kitchen_id, param: period, year: year},
        dataType:"json",
        async: false,
        success : function(result){
//            console.debug(result); debugger;
//                chart.series[0].setData([['No. of Society Registrar', 6.2], ['No. of Society Expiry', 8.5], ['Safari', 45.0], ['Opera', 26.8], ['Others', 15.7]]);
            if(!jQuery.isEmptyObject(result)){
                
                $('#Sales_comparison').empty();
                salesChart.series[0].setData(result.gross);
                salesChart.series[1].setData(result.net);
                salesChart.series[0].update({name: 'Gross sales/'+ label});     
                salesChart.series[1].update({name: 'Net sales/'+ label});     
                salesChart.xAxis[0].setCategories(result.period);
            }else{
                $('#Sales_comparison').empty().html("There is no data to display");
            }
        }
    });

}

var foodChart;
function foodBestWorstMeals(kitchen_id, food_meal_type, food_param, food_type, year, month){
    
    $.ajax({
        url: "/analytics/food/bestWorstMeal",
        method: 'POST',
        data:{kitchen_id:kitchen_id, food_meal_type:food_meal_type, food_param: food_param, food_type: food_type, year: year, month: month},
        dataType:"json",
        async: false,
        success : function(result){
            
            var data = [{
                        name: 'Sales for the current month',
                        data:result.qty
                    }];
                
            if(!jQuery.isEmptyObject(result)){

                foodChart = new Highcharts.Chart({
                    chart : {
                        renderTo : "Best_worst",
                        type    : 'column',
                            },
                    credits : {
                        enabled : false
                    },
                    title : {
                        text : 'Best and worst selling meals'
                    },

                    xAxis : {
                        categories : result.meal,
                        title : {
                            text : 'Meals'
                        }
                    },
                    yAxis : {
                        allowDecimals : false,
                        min : 0,
                        title : {
                            text : 'Quantity'
                        }
                    }, 
                    legend: {
                        enabled: true
                    },
                    tooltip: {
                        formatter: function() {
                            return '<b>' + this.x + '</b><br/>' + this.series.name + ': ' + this.y;
                        }
                    },
                    series : data
                });
                
            }else{
                $('#Best_worst').empty().html("There is no data to display");
            }
        }
    })
}

function foodBestWorstMealsChangeEvent(kitchen_id) {
    
    var food_param      = $('#food_param').val();
    var food_type       = $('#food_type').val();
    var food_meal_type  = $('#food_meal_type').val();
    var year            = $('#foodYear').val();
    var month           = $('#foodMonth').val();
    
    var label = (food_param === 'monthly') ? 'month' : 'year';
    
    if(year != $('#foodYear').data('oldyear')){
        month = getMonths('foodYear','foodMonth', kitchen_id, year);
    }
    
    if(food_param == 'yearly'){
        $('#foodMonth').addClass('hide').removeClass('show'); month = null;
    }else{
        $('#foodMonth').addClass('show').removeClass('hide');
    }
    
    $.ajax({
        url: "/analytics/food/bestWorstMeal",
        method: 'POST',
        data:{kitchen_id:kitchen_id, food_meal_type:food_meal_type, food_param: food_param, food_type: food_type, year: year, month: month},
        dataType:"json",
        async: false,
        success : function(result){
            
            if(!jQuery.isEmptyObject(result)){
                
                $('#Best_worst').empty();
                foodChart.series[0].setData(result.qty);
                foodChart.series[0].update({
                                name: 'Sales for the current '+ label,
                            });     
                foodChart.xAxis[0].setCategories(result.meal);
            }else{
                $('#Best_worst').empty().html("There is no data to display");
            }
        }
    });

}
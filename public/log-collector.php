<?php
/**
 * Browser Console Log Collector
 * This script receives and stores browser console logs
 */

// Display all errors during development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Define log file path
$logFile = APPLICATION_PATH . '/data/log/browser-console.log';

// Ensure log directory exists
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Function to write log entry
function writeLog($logFile, $entry) {
    $timestamp = date('Y-m-d H:i:s');
    $formattedEntry = "[$timestamp] $entry" . PHP_EOL;
    file_put_contents($logFile, $formattedEntry, FILE_APPEND);
}

// Handle POST request with JSON data
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get JSON data from request body
    $jsonData = file_get_contents('php://input');
    $data = json_decode($jsonData, true);
    
    // Check if data is valid
    if ($data && isset($data['logs']) && is_array($data['logs'])) {
        // Get request metadata
        $userAgent = isset($data['userAgent']) ? $data['userAgent'] : 'Unknown';
        $url = isset($data['url']) ? $data['url'] : 'Unknown';
        $ip = $_SERVER['REMOTE_ADDR'];
        
        // Write metadata to log
        writeLog($logFile, "=== Browser Log Submission ===");
        writeLog($logFile, "IP: $ip");
        writeLog($logFile, "URL: $url");
        writeLog($logFile, "User Agent: $userAgent");
        
        // Process each log entry
        foreach ($data['logs'] as $log) {
            if (isset($log['type']) && isset($log['message']) && isset($log['timestamp'])) {
                $type = strtoupper($log['type']);
                $message = $log['message'];
                $timestamp = $log['timestamp'];
                
                // Write log entry
                writeLog($logFile, "[$timestamp] [$type] $message");
            }
        }
        
        // Send success response
        header('Content-Type: application/json');
        echo json_encode(['status' => 'success', 'message' => 'Logs received']);
    } else {
        // Send error response for invalid data
        header('HTTP/1.1 400 Bad Request');
        header('Content-Type: application/json');
        echo json_encode(['status' => 'error', 'message' => 'Invalid log data']);
    }
} else {
    // Send error response for invalid request method
    header('HTTP/1.1 405 Method Not Allowed');
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
}

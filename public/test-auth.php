<?php
/**
 * This is a test script to check the SanAuth module
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    echo '<h1>Debug SanAuth Module</h1>';
    echo '<p>PHP Version: ' . PHP_VERSION . '</p>';
    
    // Load configuration
    echo '<h2>Loading Configuration</h2>';
    $config = include APPLICATION_PATH . '/config/application.config.php';
    echo '<p>Configuration loaded successfully</p>';
    
    // Initialize Zend Application
    echo '<h2>Initializing Zend Application</h2>';
    $application = Zend\Mvc\Application::init($config);
    echo '<p>Zend Application initialized successfully</p>';
    
    // Get service manager
    echo '<h2>Getting Service Manager</h2>';
    $serviceManager = $application->getServiceManager();
    echo '<p>Service Manager retrieved successfully</p>';
    
    // Check if SanAuth module is loaded
    echo '<h2>Checking SanAuth Module</h2>';
    $moduleManager = $serviceManager->get('ModuleManager');
    $loadedModules = $moduleManager->getLoadedModules();
    if (isset($loadedModules['SanAuth'])) {
        echo '<p>SanAuth module is loaded</p>';
    } else {
        echo '<p>SanAuth module is not loaded</p>';
    }
    
    // Check if AuthService is available
    echo '<h2>Checking AuthService</h2>';
    if ($serviceManager->has('AuthService')) {
        echo '<p>AuthService is available</p>';
        $authService = $serviceManager->get('AuthService');
        echo '<p>AuthService class: ' . get_class($authService) . '</p>';
    } else {
        echo '<p>AuthService is not available</p>';
    }
    
    // Check if SanAuth\Model\Storage is available
    echo '<h2>Checking SanAuth\Model\Storage</h2>';
    if ($serviceManager->has('SanAuth\Model\Storage')) {
        echo '<p>SanAuth\Model\Storage is available</p>';
        $storage = $serviceManager->get('SanAuth\Model\Storage');
        echo '<p>Storage class: ' . get_class($storage) . '</p>';
    } else {
        echo '<p>SanAuth\Model\Storage is not available</p>';
    }
    
    // Check if ConfigService is available
    echo '<h2>Checking ConfigService</h2>';
    if ($serviceManager->has('ConfigService')) {
        echo '<p>ConfigService is available</p>';
        $configService = $serviceManager->get('ConfigService');
        echo '<p>ConfigService class: ' . get_class($configService) . '</p>';
    } else {
        echo '<p>ConfigService is not available</p>';
    }
    
    // Check if SanAuth\Controller\Auth is available
    echo '<h2>Checking SanAuth\Controller\Auth</h2>';
    $controllerManager = $serviceManager->get('ControllerManager');
    if ($controllerManager->has('SanAuth\Controller\Auth')) {
        echo '<p>SanAuth\Controller\Auth is available</p>';
        $authController = $controllerManager->get('SanAuth\Controller\Auth');
        echo '<p>AuthController class: ' . get_class($authController) . '</p>';
    } else {
        echo '<p>SanAuth\Controller\Auth is not available</p>';
    }
    
} catch (Exception $e) {
    echo '<h2>Error</h2>';
    echo '<p>Error: ' . $e->getMessage() . '</p>';
    echo '<p>File: ' . $e->getFile() . ' (line ' . $e->getLine() . ')</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

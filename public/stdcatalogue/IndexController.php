<?php
/**
 * This file manages the delivery locations on fooddialer system
 * The activity includes add,update and delete delivery locations
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: IndexController.php 2016-01-11 $
 * @package Payment/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Payment>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Payment\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\JsonModel;
use Zend\View\Model\ViewModel;

use Zend\Db\Sql\Select;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Payment as QSPayment;
use Lib\QuickServe\Wallet as QSWallet;
use Lib\Utility;


class IndexController extends AbstractActionController
{

	/**
	 * Default Index Action ...
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{

		$layoutviewModel = $this->layout();
		
		$sm = $this->getServiceLocator();

		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
		
		$formData = array();
		$errorMessage = array();

		$selectedGateway = "";

		$request = $this->getRequest();
		
		try{
			
			$encTransactionId = $this->params()->fromQuery("tid");
					
			if(empty($encTransactionId)){
				throw new \Exception("No Payment Specified");
			}
			
			$libPayment = QSPayment::getInstance($sm, $settings);
			
			$transactionId = $libPayment->decryptTransaction($encTransactionId);
			
			$transaction = $libPayment->getTransaction($transactionId);

			if($transaction->status !='initiated'){
				
				if(empty($encTransactionId)){
					throw new \Exception("Invalid transaction found");
				}
			}
			
			// Check how many payment gateway available
			
			$gateways = $libPayment->getAvailableGateways();

			//echo "<pre>";print_r($gateways);echo "</pre>";die;
			
			if(count($gateways) == 1){
				
				$selectedGateway = $gateways[0];
				$libPayment->setPaymentGateway($selectedGateway,$transaction);
				$formData = $libPayment->getAdapter()->getFormData($transaction);
				//\Lib\Utility::pr($formData);
			}

			if($request->isPost()){

				$data = $request->getPost();
				
				if(!empty($data['selGateway'])){
                    
					$libPayment->setPaymentGateway($data['selGateway'],$transaction);
					$formData = $libPayment->getAdapter()->getFormData($transaction);
					//\Lib\Utility::pr($formData);die;

				}else{

					throw new \Exception("Payment Gateway not found");
					
				}
			}
			
		 }catch(\Exception $e){
			
			$errorMessage = $e->getMessage();
			//\Lib\Utility::pr($error_message);
		} 
		
		return new ViewModel(array(
			'error_message'=>$errorMessage,
			'transaction'=>$transaction,
			'form_data'=>$formData,
			'gateways'=>$gateways,
			'selected_gateway'=>$selectedGateway	
		));
	
	}
	
	public function successAction(){       
		$request = $this->getRequest();
		$errorMessage = array();
		// below variables send by instamojo in response....
		$payment_request_id = $request->getQuery('payment_request_id',null);
		$payment_id = $request->getQuery('payment_id',null);
		$tid = $request->getQuery('tid',null);        
        
		$data = $request->getPost();        
        //dd($data);
		$queryData = $request->getQuery()->toArray();
		//echo '<pre>query Data Resp...'; print_r($queryData); echo '</pre>'; 
		//dd($queryData);
		if($request->isPost() || ( !empty($payment_request_id) && !empty($payment_id) ) || !empty($queryData) ){
			
			try{
				
				if($request->isPost()){
					
					$data = $request->getPost(); // Payu sends response data as post.

				}elseif(!empty($queryData)){
					
					// Instamojo, sends request in get method.
					// Mobikwik also sends data in get method when orderid is already processed (duplicate orderid).
					// Paypal sends data with get method.

					if($request->getQuery('payment_request_id')){
						$data['payment_request_id'] = $request->getQuery('payment_request_id');
					}
					if($request->getQuery('payment_id')){
						$data['payment_id'] = $request->getQuery('payment_id');
					}                                       
                    
					$data = $queryData;
                    
					if($request->getQuery('tid')){
						$data['transaction_id'] = $request->getQuery('tid');
						unset($data['tid']);
					}

					//dd($data);

				}else{

					$data['payment_request_id'] = $payment_request_id;
					$data['payment_id'] = $payment_id;
					$data['transaction_id'] = $tid;
				}
                
				//\Lib\Utility::pr($data);
				$sm = $this->getServiceLocator();
							
				$libCommon = QSCommon::getInstance($sm);
				$libWallet = QSWallet::getInstance($sm);
				$libCustomer = QSCustomer::getInstance($sm);
				$libOrder = QSOrder::getInstance($sm);
				$utility = Utility::getInstance();
				
				$settings = $libCommon->getSettings();
				$libPayment = QSPayment::getInstance($sm, $settings);
                
			
				$decodedData = $libPayment->decodeResponseData($data);
				
				//\Lib\Utility::pr($decodedData);
    			$utility = Utility::getInstance();
				                
				$transactionId = $decodedData['transaction_id'];
				
				$transaction = $libPayment->getTransaction($transactionId);

				$logged_in = $transaction['description'];
                
				$returnUrl = $transaction['success_url']."?tid=".$transactionId;

				if(empty($transaction)){
					
					throw new \Exception("Invalid transaction found");
				}
				
				if($transaction['status'] !='initiated'){
						
					throw new \Exception("Invalid state of transaction found");
				}
				
				$transaction = $transaction->getArrayCopy();
				
				if($settings['PAYU_IPN'] == 'no'){
					
					if($transaction['gateway'] == 'paytm'){
						$statusResponse = $libPayment->getAdapter()->validateStatus($transaction['pk_transaction_id']);
						//dd($statusResponse);	
						if($statusResponse['STATUS'] == 'TXN_SUCCESS'){
							$statusmsg = 'success';
						}else{
							$statusmsg = 'failure';
						}
						$transaction['status'] = $statusmsg;

					}elseif($transaction['gateway'] == 'mobikwik'){

						$statusmsg = 'failure';

						if($decodedData['status'] =='success'){
							$arrStatus = $libPayment->getAdapter()->verifyTransaction($data['orderid'], $transaction['payment_amount']);
							
							if($arrStatus['flag']==true){
								$statusmsg = 'success';
								$transaction['gateway_transaction_id'] = $arrStatus['refid'];
								$data = json_decode($transaction['description'],true);
								$data['verify_transaction'] = $arrStatus;
								$decodedData['description'] = json_encode($data);
							}
						}

						$transaction['status'] = $statusmsg;
						
					}elseif($transaction['gateway'] == 'paypal') {

						//Calling DoExpressCheckoutPayment method to make a payment
						//echo '<pre> Decoded Data: '; print_r($decodedData); echo '</pre>';

			            $payload = array(
			                'TOKEN' => $decodedData['token_paypal'],
			                'PAYERID' => $decodedData['payer_id'],
			                'PAYMENTREQUEST_0_PAYMENTACTION' => 'Sale',
			                'PAYMENTREQUEST_0_AMT' => $decodedData['payment_amount'],
			                'PAYMENTREQUEST_0_CURRENCYCODE' => $decodedData['currency_code'],
			            );

						$statusResponse = $libPayment->getAdapter()->DoExpressCheckoutPayment($payload);

						//dd($statusResponse);

						if(is_array($statusResponse) && $statusResponse['ACK'] == 'Success' && $statusResponse['PAYMENTINFO_0_ACK'] == 'Success' && $statusResponse['PAYMENTINFO_0_PAYMENTSTATUS'] == 'Completed' ) {

							$statusmsg = 'success';
							$decodedData['gateway_transaction_id'] = $statusResponse['PAYMENTINFO_0_TRANSACTIONID'];
							$decodedData['description'] = json_encode($data);
						}	
						else {
							$statusmsg = 'failure';
						}
						$transaction['status'] = $statusmsg;
                    }elseif($transaction['gateway'] == 'payu'){/*payu Validation part check payment status pradeep maurya*/
                   
                        $newResponse = json_decode($libPayment->getAdapter()->validateStatus($data['txnid']));                        
                        $success = false;
                        foreach ($newResponse->result as $results) {
                            foreach($results as $result ) {                               
                                if($result->status == 'success') {
                                    $success = true;
                                }                                
                            }                            
                        }
                        if($success) {
                            $statusmsg = 'success';
                            $transaction['status'] = $statusmsg; 
                        }else {                            
                            header('Location:'.$settings['ADMIN_WEB_URL'].'/payment/failure');                            
                        }						                       
                    }else if($transaction['gateway'] == 'converge'){
                        
                        $ssl_result = $data['ssl_result'];
                        if ($ssl_result == 0 ) {  
                            $statusmsg = 'success';
                            $transaction['status'] = $statusmsg;                             
                        } else if ($ssl_result==1) {                            
                           header('Location:'.$settings['ADMIN_WEB_URL'].'/payment/failure'); 
                        }
                    }
                    else{
						$transaction['status'] = strtolower($decodedData['status']);	
					}
					
					$transaction['gateway_transaction_id'] = $decodedData['gateway_transaction_id'];
					$transaction['description'] = $decodedData['description'];
					
                   /* this is where system can get hacked. Request can be captured 
                     * and edited. This edited request can be dispatched forward by 
                     * replacing status=failure with status=success. 
                     */
                    /* $data[status] !== payu api verify status 
                     * 
                     * return/ redirect to error page
                     */
                    //dd($transaction);
					$transaction = $libPayment->saveTransaction($transaction);
                    
				}

				$strPreOrderIds = $transaction['pre_order_id'];
				
				$alreadyProccessed = 0;
				
				if(!empty($strPreOrderIds)){
					
					$aPreOrderIds = explode(",",$strPreOrderIds);
				
					foreach($aPreOrderIds as $oPreOrderId){
						
						$tPayment = $libOrder->getOrderConfirmTable()->getTempOrderPayment($oPreOrderId,'preorder');
						
						if(!empty($tPayment)){
							
							if($tPayment['status']=='success'){
								$alreadyProccessed = 1;
							}
						}
						
					}
				
				}
				
				if($alreadyProccessed){
					
					goto SHOWDETAIL;
				}
				

				if($transaction['status'] == 'success'){

					//Saving Transarmor Token in Customers Table.
					/*	
					$trans_response = json_decode($transaction['description'], true);

					if(isset($trans_response['x_response_code']) && $trans_response['x_response_code'] == '1') {
						
						$customerId = $trans_response['x_cust_id']; 
						$taToken = $trans_response['Card_Number'];
						$ccExpiry = $trans_response['Expiry_Date'];

						$libCustomer->saveTransarmorToken($customerId, $taToken, $ccExpiry);

						//echo "<pre> trans_response: ".$cust_id." ".$cc_no ; die;
					}
					*/
                    
					if($settings['PAYU_IPN'] == 'no'){
						
						$customer = (array)$libCustomer->getCustomer($transaction['customer_id'],'id');
                        
						$customer['customer_address'] = $libCustomer->getCustomerAddress($transaction['customer_id']); // 7 june 16 - sankalp
						
						$details = array();
							
						// Removing transaction amount if applied from total paid amount.
						$amount = round((float)$transaction['payment_amount'] - (float)$transaction['transaction_charges'],2);
							
						$details['amount'] = $amount;
						$details['recurring_status'] = $tPayment['recurring_status'];
						
                        $wallet_amount = (float)$transaction['payment_amount'];
                        
						$walletData = array();
						$walletData['amount'] = $transaction['payment_amount'];
						$walletData['id'] = $customer['pk_customer_code'];
						$walletData['description'] = $utility->getLocalCurrency($walletData['amount'])." received by online payment ( {$transaction['gateway_transaction_id']} ) .";
						$walletData['payment_date'] = date('Y-m-d');
						$walletData['created_date'] = date('Y-m-d');
						$walletData['amount_type'] = "cr";
						$walletData['reference_no'] = $transaction['gateway_transaction_id'];
						$walletData['bank_name'] = $libPayment->getPaymentGateway();
						$walletData['updated_by'] = $customer['pk_customer_code']; 
                       	
						$libWallet->saveWalletTransaction($walletData,'online','customer');
						
						if($transaction['transaction_charges'] > 0){
                            
                            $wallet_amount -= (float)$transaction['transaction_charges'];
                            
							$walletData = array(
								'amount' =>$transaction['transaction_charges'],
								'id' =>$customer['pk_customer_code'],
                                'updated_by' => $customer['pk_customer_code'],
								'reference_no' => $transaction['gateway_transaction_id'],
								'bank_name' => $libPayment->getPaymentGateway(),
								'description' => $utility->getLocalCurrency($transaction['transaction_charges']).' transaction charges deducted against amount '.$utility->getLocalCurrency($transaction['payment_amount']),
                                
                                );
                            
							$libWallet->saveWalletTransaction($walletData,'debit','customer');
								
						}
						
                        /* wallet promocode - 30Nov. sankalp */
                        
                        if( ($transaction['context'] == 'wallet') && ($transaction['promo_code']) ){
                            
                            $wallet_amount += (float)$transaction['discount'];
                            
                            $walletpromoData = array();
                            $walletpromoData['amount'] = $transaction['discount'];
                            $walletpromoData['id'] = $customer['pk_customer_code'];
                            $walletpromoData['description'] = $utility->getLocalCurrency($walletpromoData['amount'])." cashback availed with promocode '".$transaction['promo_code']."' ( {$transaction['gateway_transaction_id']} ) .";
                            $walletpromoData['payment_date'] = date('Y-m-d');
                            $walletpromoData['created_date'] = date('Y-m-d');
                            $walletpromoData['amount_type'] = "cr";
                            $walletpromoData['reference_no'] = $transaction['gateway_transaction_id'];
                            $walletpromoData['bank_name'] = $libPayment->getPaymentGateway();
                            $walletpromoData['updated_by'] = $customer['pk_customer_code'];
                            $libWallet->saveWalletTransaction($walletpromoData,'online','customer');
                            
                            /* decrease promo limit quantity */
                            $libOrder->updatePromoLimit($transaction['promo_code']);
                            
                        }
                        
                        /* partial wallet payment - 25jan17 sankalp */
                       
                        if( $transaction['wallet_amount']){
                            
                            $partialWalletData = array();
                            $partialWalletData['amount'] = $transaction['wallet_amount'];
                            $partialWalletData['id'] = $customer['pk_customer_code'];
                            $partialWalletData['description'] = $utility->getLocalCurrency($partialWalletData['amount'])." partially redeemed for online trasaction reference id ( {$transaction['gateway_transaction_id']} ) .";
                            $partialWalletData['payment_date'] = date('Y-m-d');
                            $partialWalletData['created_date'] = date('Y-m-d');
                            $partialWalletData['amount_type'] = ""; 
                            $partialWalletData['reference_no'] = $transaction['gateway_transaction_id'];
                            $partialWalletData['updated_by'] = $customer['pk_customer_code'];
                            $libWallet->saveWalletTransaction($partialWalletData,'partial','customer');
                           
                            
                        }
                        
                        /* send sms on successful transactions */
                         
                        if($customer['phone'] && $customer['phone_verified'] == 1){
                            
                            $mailer = new \Lib\Email\Email();
                            $sm->get("Write_Adapter");
                            $mailer->setAdapter($sm);

                            //get sms configuration
                            $sms_config = $sm->get('config')['sms_configuration'];
                            $sms_common = $libCommon->getSmsConfig($settings);
                            
                            //SET sms configuration to mailer
                            $mailer->setSMSConfiguration($sms_config);
                            //check for mobile no and give it to
                            $mailer->setMobileNo($customer['phone']);
                            $mailer->setMerchantData($sms_common);
                            $sms_array = array(
                                'wallet_amount' => $utility->getLocalCurrency($wallet_amount,'','','SMS'),
                            );
                            
                            $message = $libCommon->getSMSTemplateMsg('wallet_update',$sms_array);

                            if($message){
                                $mailer->setSMSMessage($message);
                                $sms_returndata = $mailer->sendmessage();
                            }
                        }
                        
                        
						$preOrderIds = $transaction['pre_order_id'];
						$count=0;
						if(!empty($preOrderIds)){
							$arrPreOrderIds = explode(",",$preOrderIds);
						
							foreach($arrPreOrderIds as $preOrderId){

								switch ($transaction['context']) {

									case 'swap':
										
										// fetch swappable items from order_swappable_item
										$swappableItem = $libOrder->getSwappableItemById($preOrderId);

										$uOrder = array();
										$uOrder['data']['product_code'] = $swappableItem[0]['swapped_product_code'];
										$uOrder['data']['product_name'] = $swappableItem[0]['swapped_product_name'];
										$uOrder['data']['product_description'] = $swappableItem[0]['swapped_product_description'];
										$uOrder['data']['product_type'] = $swappableItem[0]['swapped_product_type'];
										$uOrder['data']['food_type'] = $swappableItem[0]['swapped_product_food_type'];
										$uOrder['data']['items'] = $swappableItem[0]['swapped_product_items'];
										$uOrder['data']['tax'] = $swappableItem[0]['tax'];
										$uOrder['data']['tax_details'] = $swappableItem[0]['tax_details'];
										$uOrder['data']['amount'] = $swappableItem[0]['amount'];
										$uOrder['data']['service_charges'] = $swappableItem[0]['swap_charges'];
										$uOrder['data']['quantity'] = $swappableItem[0]['order_meal_qty'];

										$uOrder['cond'] = array();
										$uOrder['cond']['order_no'] = $swappableItem[0]['order_no'];
										$uOrder['cond']['order_date'] = $swappableItem[0]['order_date'];
										$uOrder['cond']['product_code'] = $swappableItem[0]['product_code'];
										$uOrder['cond']['kitchen_code'] = $swappableItem[0]['kitchen_code'];
										$uOrder['cond']['order_menu'] = $swappableItem[0]['order_menu'];
										$uOrder['cond']['order_bill_no'] = $swappableItem[0]['order_bill_no'];

										$libOrder->getOrderTable()->updateOrder($uOrder);	

										break;
									
									default:

										//Passing Transactional data .
										$trans_response = json_decode($transaction['description'], true);
										$libOrder->confirmOrderByTempOrder($preOrderId,$customer,"withpayment",$details,$count,$trans_response);

										//Adding activity log

                                        $activity_log_data=array();
                                        $activity_log_data['context_ref_id']= $customer['pk_customer_code'];
                                        $activity_log_data['context_name']= $customer['customer_name'];
                                        $activity_log_data['context_type']= 'user';
                                        $activity_log_data['controller']= 'customer';
                                        $activity_log_data['action']= 'paymentoption';

										if($logged_in == 'admin') {

	                                        $activity_log_data['description']= "Order: Admin placed order of ".$utility->getLocalCurrency($transaction['payment_amount'])." on behalf of ".$transaction['customer_name'].".";
    	                                    $libCommon->saveActivityLog($activity_log_data);										
										}
										else {
	                                        $activity_log_data['description']= "Order: Order of ".$utility->getLocalCurrency($transaction['payment_amount'])." placed by ".$transaction['customer_name'].".";
    	                                    $libCommon->saveActivityLog($activity_log_data);										
										}

										break;
								}
								$count++;
							}
						
						}
						
					}
					
				}
				
			
			 }catch(\Exception $e){
				
				$errorMessage = $e->getMessage();
			} 
			
		}

		SHOWDETAIL:
		return new ViewModel(array(
			'error_message'=>$errorMessage,
			'transaction'=>$transaction,
			'return_url'=>$returnUrl
		));
		
	}
	
	public function failureAction(){
		
		
		$request = $this->getRequest();
		$errorMessage = array();

		$queryData = $request->getQuery()->toArray();

		//echo '<pre>queryData :'; print_r($queryData); echo '</pre>';
		
		if($request->isPost() || !empty($queryData)){
			
			try{

				if($request->isPost()){
					
					$data = $request->getPost(); // Payu sends response data as post.

				}elseif(!empty($queryData)){
					
					// Instamojo, sends request in get method.
					// Mobikwik also sends data in get method when orderid is already processed (duplicate orderid).
					// Paypal sends data with get method.
					$data = $queryData;

					if($request->getQuery('tid')){
						$data['transaction_id'] = $request->getQuery('tid');
						unset($data['tid']);
					}

					//dd($data);

				}
				
				//$data = $request->getPost();
				//echo '<pre>data :'; print_r($data); echo '</pre>';
				//\Lib\Utility::pr($data);
				$sm = $this->getServiceLocator();
				
				$libCommon = QSCommon::getInstance($sm);
				$libWallet = QSWallet::getInstance($sm);
				$libCustomer = QSCustomer::getInstance($sm);
				$libOrder = QSOrder::getInstance($sm);
				
				$settings = $libCommon->getSettings();
				$libPayment = QSPayment::getInstance($sm, $settings);
				
				$decodedData = $libPayment->decodeResponseData($data);
				//echo '<pre>decodedData :'; print_r($decodedData); echo '</pre>';
				//\Lib\Utility::pr($decodedData);
				
				$transactionId = $decodedData['transaction_id'];

				$transaction = $libPayment->getTransaction($transactionId);

				//echo '<pre>transaction :'; print_r($transaction); echo '</pre>'; die;
				
				if(empty($transaction)){
					
					throw new \Exception("Invalid transaction found");
				}
				
				if($transaction['status'] !='initiated'){
						
					throw new \Exception("Invalid state of transaction found");
				}
				
				$transaction = $transaction->getArrayCopy();
				
				if($settings['PAYU_IPN'] == 'no'){

					$transaction['status'] = strtolower($decodedData['status']);
					$transaction['gateway_transaction_id'] = $decodedData['gateway_transaction_id'];
					$transaction['description'] = $decodedData['description'];

					if(isset($decodedData['token_paypal']) && !empty($decodedData['token_paypal'])) {
						$transaction['status'] = $decodedData['status'];
					}					
					
					$transaction = $libPayment->saveTransaction($transaction);
					//echo '<pre>transaction :'; print_r($transaction); echo '</pre>'; die;
				}
				
				$returnUrl = $transaction['failure_url']."?tid=".$transactionId;
				
				
			}catch(\Exception $e){
				
				$errorMessage = $e->getMessage();
			}
			
		}
		
		return new ViewModel(array(
			'error_message'=>$errorMessage,
			'transaction'=>$transaction,
			'return_url'=>$returnUrl
		));
	}
	
 	public function payuResponseAction()
    {

    	$request = $this->getRequest();

        if ($request->isPost()){
			
            $paramsJson = $request->getContent();
            
            $file_name = $_SERVER['DOCUMENT_ROOT']."/data/payu_ipn.txt";
            
            $handle = fopen($file_name, 'w') or die('Cannot open file:  '.$file_name);
            $wrStr = $paramsJson;
            
            $data = json_decode($paramsJson,true);
            
            $tempOrderId = $data['udf2'];
            $transactionId = $data['udf1'];
            $transactionamt = $data['udf3'];
            	
            $sm = $this->getServiceLocator();
            
            $libCommon = QSCommon::getInstance($sm);
            $libOrder = QSOrder::getInstance($sm);
            $libWallet = QSWallet::getInstance($sm);
            	
            $settings = $libCommon->getSettings();
            
            $utility = Utility::getInstance();
            
            try{
            	
            	$transaction = $libOrder->getTransaction($transactionId);
            	$transaction = $transaction->getArrayCopy();
            		
            	$transaction['status'] = strtolower($data['status']);
            	$transaction['gateway_transaction_id'] = $data['merchantTransactionId'];
            	$transaction['description'] = json_encode($data);
            	
            	$transaction = $libOrder->saveTransaction($transaction);
            	
            	if(strtolower($data['status']) == 'success'){
            			
            		$details = array();
            	
            		// Removing transaction amount if applied from total paid amount.
            		$amount = round((float)$data['amount'] - (float)$data["udf3"],2);
            	
            		$details['amount'] = $amount;
            	
            		$walletData = array();
            		$walletData['amount'] = $amount;
            		$walletData['id'] = $transaction['customer_id'];
            		$walletData['description'] = $utility->getLocalCurrency($walletData['amount']).' received by online payment.';
            		$walletData['payment_date'] = date('Y-m-d');
            		$walletData['created_date'] = date('Y-m-d');
            		$walletData['amount_type'] = "cr";
                    $walletData['updated_by'] = $transaction['customer_id'];
                    
            		$libWallet->saveWalletTransaction($walletData,'online','customer');
            		
            		if($transactionamt > 0)
            		{
            			
            			$walletData = array(
            				'amount' =>$transactionamt,
            				'id' =>$transaction['customer_id'],
            				'description' => $utility->getLocalCurrency($transactionamt).' transaction charges deducted against amount '.$utility->getLocalCurrency($amount)
            			);
            		
            			$wrStr  .= "\n\n adding debit entry of amount ".$utility->getLocalCurrency($transactionamt)." and customer {$transaction['customer_id']}";
            			$libWallet->saveWalletTransaction($walletData,'debit','customer');
            		
            		}
            		
            	}
            	
            	fwrite($handle, $wrStr);
            	fclose($handle);
            	chmod($file_name,0777);
            	
            }catch(\Exception $e){
			
				$file_name = $_SERVER['DOCUMENT_ROOT']."/data/payu_error_ipn.txt";
				
				$handle = fopen($file_name, 'w') or die('Cannot open file:  '.$file_name);
				fwrite($handle, $e->getMessage());
				fclose($handle);
				
				chmod($file_name,0777);
			
			}


        }

        die;
    }
	
	
}

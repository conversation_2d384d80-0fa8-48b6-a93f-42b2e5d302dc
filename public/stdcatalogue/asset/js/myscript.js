function remove_from_cart() {
	$(document).on('click', '.shoppinCart table a.close', function() {

		$(this).parent().parent().remove();

	});
}

function plus_cart() {
	$(document).on('click', '.btn.plus', function() {

		$(this).prev().text(parseInt($(this).prev().text()) + 1);

	});

}

function minus_cart() {
	$(document).on('click', '.btn.minus', function() {
		if ($(this).next().text() - 1 == 0) {
			alert("Quantity can not be less than 1");
		} else {
			$(this).next().text($(this).next().text() - 1);
		}

	});

}

function assign_parent() {
	$('.list-group').each(function() {
		$(this).find('.sort-btn').attr('data-parent', $(this).attr('data-parent'));
	});
}

function select_tab_new() {
	$('.customeseletbox').on('change', function() {
		$('.' + $(this).attr('data-tab') + ' li:eq(' + $(this).val() + ') a').tab('show');
	});
}

function sortlist() {
	assign_parent();
	$(document).on('click', '.sort-btn', function() {
		$(this).parents('.list-group').find('.sort-btn').removeClass('active');
		$(this).addClass('active');
		var dp = $(this).attr('data-path');
		if (dp == '.default') {
			$($(this).attr('data-parent')).find('.no-results').hide();
			$($(this).attr('data-parent') + ' .list-item').show();
		} else {
			if ($($(this).attr('data-parent') + ' .list-item' + dp).length < 1) {
				$($(this).attr('data-parent')).find('.list-item').hide();
				$($(this).attr('data-parent')).find('.no-results').show();
			} else {
				$($(this).attr('data-parent')).find('.no-results').hide();
				$($(this).attr('data-parent') + ' .list-item').each(function() {
					if ($(this).hasClass(dp.slice(1)) == true) {
						$(this).show();
					} else {
						$(this).hide();
					}
				});
			}

		}

		/*
		 $($(this).attr('data-parent')).find('.list-item').css('display','none');
		 $($(this).attr('data-parent')).find($(this).attr('data-path')).css('display','block');*/

	});
}

function call_select() {
	$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
		$('.styled').trigger('render');

	});
	$('.styled').customSelect();
}

function icheck_call() {
	$('input').iCheck({
		checkboxClass : 'icheckbox_flat-blue',
		radioClass : 'iradio_flat-blue',
		increaseArea : '20%' // optional
	});
}

function select_tab() {
	$('.dropbox').on('change', function() {
		var value = $(this).val();
		$('#tab_list_li li:eq(' + value + ') a').tab('show');

	});

}

function active_tab() {
	$('.my_tab_checkout a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
		$('.my_tab_checkout li a i').removeClass('fa-square');
		$('.my_tab_checkout li a i').addClass('fa-square-o');
		$(this).find('i').removeClass('fa-square-o');
		$(this).find('i').addClass('fa-square');
	});
}

function deletHistory() {

	$(document).on('click', '.booking a.close', function() {
		$(this).parent().parent().remove();
	});
}

function assign_box_size() {
	var mh = 0;
	$('.concept-box-parent-row .concept-box-parent .middle-box').css('min-height', '0');
	if ($(window).width() > 767) {
		$('.concept-box-parent-row .concept-box-parent').each(function() {
			if (mh < $(this).find('.middle-box').innerHeight()) {
				mh = $(this).find('.middle-box').innerHeight();
			}
		});
		$('.concept-box-parent-row .concept-box-parent .middle-box').css('min-height', mh + 'px');
	}
}

function assign_box_size_menu() {
	var mh = 0;
	$('.grid-parent .list-item .caption h4').css('min-height', '0');
	if ($(window).width() > 767) {
		$('.grid-parent .list-item').each(function() {
			if (mh < $(this).find('.caption h4').innerHeight()) {
				mh = $(this).find('.caption h4').innerHeight();
			}
		});
		$('.grid-parent .list-item .caption h4').css('min-height', mh + 'px');
	}
}

function assign_box_size() {
	var mh = 0;
	$('.concept-box-parent-row .concept-box-parent .middle-box').css('min-height', '0');
	if ($(window).width() > 767) {
		$('.concept-box-parent-row .concept-box-parent').each(function() {
			if (mh < $(this).find('.middle-box').innerHeight()) {
				mh = $(this).find('.middle-box').innerHeight();
			}
		});
		$('.concept-box-parent-row .concept-box-parent .middle-box').css('min-height', mh + 'px');
	}
}

function calHeight() {
	var winH = $(window).height();
	var popHt = winH - (100 * 2);
	$('.monthly_calender').css('height', popHt + 'px');
}

function openScroll() {
	$('#myModal').on('show.bs.modal', function(e) {
		calHeight();
		$(".monthly_calender").niceScroll({
			cursorwidth : "12px",
			cursorcolor : "#000"
		});
	});
	$('#myModal').on('shown.bs.modal', function(e) {
		$(".monthly_calender").getNiceScroll().remove();
		$(".monthly_calender").niceScroll({
			cursorwidth : "12px",
			cursorcolor : "#000"
		});
	});
}

function closeScroll() {
	$('#myModal').on('hidden.bs.modal', function(e) {
		$(".monthly_calender").getNiceScroll().remove();
	});
}

$.validator.addMethod("valueNotEquals", function(value, element, arg) {
	return arg != value;
}, "Value must not equal arg.");

function validation() {
	$("#contact-form").validate({
		rules : {
			name : {
				required : true
			},
			number : {
				number : true,
				minlength : 7,
				maxlength : 10
			},
			email : {
				required : true,
				email : true
			},
			subject : {
				required : true
			},
			message : {
				required : true
			},
			code : {
				required : true,
				number : true
			}
		},
		submitHandler : function(form) {
			$('.form-btn').css('display', 'none');
			$('.loader').css('display', 'inline-block');
			$('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var name = $('#name').val();
			var number = $('#number').val();
			var email = $('#email').val();
			var subject = $('#subject').val();
			var message = $('#message').val();

			$.ajax({
				type : 'POST',
				data : {
					name : name,
					number : number,
					email : email,
					subject : subject,
					message : message
				},
				url : 'sendmail.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('.form-control').val('');
					} else {
						$('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('.form-btn').css('display', 'block');
					$('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});

	$("#register-form").validate({
		rules : {
			name : {
				required : true
			},
			number : {
				number : true,
				minlength : 7,
				maxlength : 10
			},
			altnumber : {
				number : true,
				minlength : 7,
				maxlength : 10
			},
			email : {
				required : true,
				email : true
			},
			address : {
				required : true
			},
			username : {
				required : true
			},
			password : {
				required : true
			},
			repassword : {
				required : true,
				equalTo : "#password"
			},
			agree : {
				required : true
			}
		},
		submitHandler : function(form) {
			$('.form-btn').css('display', 'none');
			$('.loader').css('display', 'inline-block');
			$('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var name = $('#name').val();
			var number = $('#number').val();
			var altnumber = $('#altnumber').val();
			var email = $('#email').val();
			var address = $('#address').val();
			var username = $('#username').val();
			var password = $('#password').val();

			$.ajax({
				type : 'POST',
				data : {
					name : name,
					number : number,
					altnumber : altnumber,
					email : email,
					address : address,
					username : username,
					password : password
				},
				url : 'sendmail.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('.form-control').val('');
					} else {
						$('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('.form-btn').css('display', 'block');
					$('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});
	$("#sign-form").validate({
		rules : {
			name : {
				required : true
			},
			number : {
				number : true,
				minlength : 7,
				maxlength : 10
			},

			email : {
				required : true,
				email : true
			},
			location : {
				valueNotEquals : "default"
			},
			city : {
				valueNotEquals : "default"
			},
			cname : {
				required : true
			},
			saddress : {
				required : true
			},
			agree : {
				required : true
			}
		},
		messages : {
			location : {
				valueNotEquals : "Please select location"
			},
			city : {
				valueNotEquals : "Please select city"
			}
		},
		submitHandler : function(form) {
			$('.form-btn').css('display', 'none');
			$('.loader').css('display', 'inline-block');
			$('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var name = $('#name').val();
			var number = $('#number').val();
			var email = $('#email').val();
			var location = $("#location option:selected").text();
			var city = $("#city option:selected").text();
			var cname = $('#cname').val();
			var saddress = $('#saddress').val();
			$.ajax({
				type : 'POST',
				data : {
					name : name,
					number : number,
					email : email,
					location : location,
					city : city,
					cname : cname,
					saddress : saddress
				},
				url : 'sendmail.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('.form-control').val('');
					} else {
						$('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('.form-btn').css('display', 'block');
					$('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});

	$("#checkoutform-form").validate({
		rules : {

			location : {
				valueNotEquals : "default"
			},
			city : {
				valueNotEquals : "default"
			},
			address : {
				valueNotEquals : "default"
			}
		},
		messages : {
			location : {
				valueNotEquals : "Please select location"
			},
			city : {
				valueNotEquals : "Please select city"
			},
			address : {
				valueNotEquals : "Please select address"
			}
		},
		submitHandler : function(form) {
			$('.form-btn').css('display', 'none');
			$('.loader').css('display', 'inline-block');
			$('.error-message').html('Please wait your message is sending..').css('display', 'block');

			var location = $("#location option:selected").text();
			var city = $("#city option:selected").text();
			var address = $("#address option:selected").text();

			$.ajax({
				type : 'POST',
				data : {
					location : location,
					city : city,
					address : address
				},
				url : 'sendmail.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('.form-control').val('');
					} else {
						$('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('.form-btn').css('display', 'block');
					$('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});
	$("#checkout-form").validate({
		rules : {
			available : {
				required : true,
				number : true
			},
			locked : {
				required : true,
				number : true
			},
			addmoreamount : {
				required : true,
				number : true
			}
		},
		submitHandler : function(form) {
			$('#checkout-form').find('.form-btn').css('display', 'none');
			$('#checkout-form').find('.loader').css('display', 'inline-block');
			$('#checkout-form').find('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var available = $('#available').val();
			var locked = $('#locked').val();
			var addmoreamount = $('#addmoreamount').val();

			$.ajax({
				type : 'POST',
				data : {
					available : available,
					locked : locked,
					addmoreamount : addmoreamount
				},
				url : 'sendmail1.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('#checkout-form').find('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('#checkout-form').find('.form-control').val('');
					} else {
						$('#checkout-form').find('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('#checkout-form').find('.form-btn').css('display', 'inline-block');
					$('#checkout-form').find('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});
	$("#neft-form").validate({
		rules : {
			neft : {
				required : true,
				number : true
			}
		},
		submitHandler : function(form) {
			$('#neft-form').find('.form-btn').css('display', 'none');
			$('#neft-form').find('.loader').css('display', 'inline-block');
			$('#neft-form').find('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var neft = $('#neft').val();

			$.ajax({
				type : 'POST',
				data : {
					neft : neft
				},
				url : 'sendmail1.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('#neft-form').find('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('#neft-form').find('.form-control').val('');
					} else {
						$('#neft-form').find('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('#neft-form').find('.form-btn').css('display', 'inline-block');
					$('#neft-form').find('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});
	$("#cheque-form").validate({
		rules : {
			cheque : {
				required : true,
				number : true
			}
		},
		submitHandler : function(form) {
			$('#cheque-form').find('.form-btn').css('display', 'none');
			$('#cheque-form').find('.loader').css('display', 'inline-block');
			$('#cheque-form').find('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var cheque = $('#cheque').val();

			$.ajax({
				type : 'POST',
				data : {
					cheque : cheque
				},
				url : 'sendmail.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('#cheque-form').find('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('#cheque-form').find('.form-control').val('');
					} else {
						$('#cheque-form').find('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('#cheque-form').find('.form-btn').css('display', 'inline-block');
					$('#cheque-form').find('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});

	$("#login-form").validate({
		rules : {
			email : {
				required : true
			},
			password : {
				required : true
			}
		},
		submitHandler : function(form) {
			$('.form-btn').css('display', 'none');
			$('.loader').css('display', 'inline-block');
			$('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var email = $('#email').val();
			var password = $('#password').val();

			$.ajax({
				type : 'POST',
				data : {
					email : email,
					password : password
				},
				url : 'sendmail.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('.form-control').val('');
					} else {
						$('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('.form-btn').css('display', 'inline-block');
					$('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});
	$("#forgot-form").validate({
		rules : {
			email : {
				required : true
			}
		},
		submitHandler : function(form) {
			$('.form-btn').css('display', 'none');
			$('.loader').css('display', 'inline-block');
			$('.error-message').html('Please wait your message is sending..').css('display', 'block');
			var email = $('#email').val();

			$.ajax({
				type : 'POST',
				data : {
					email : email
				},
				url : 'sendmail.php',
				success : function(result) {
					if (result == 'Your Message Send Successfully.') {
						$('.error-message').html('Our Administrative call you.').addClass('text-success').css('display', 'block');
						$('.form-control').val('');
					} else {
						$('.error-message').html('Please Try again..').addClass('text-danger').removeClass('text-success').css('display', 'block');
					}
					$('.form-btn').css('display', 'inline-block');
					$('.loader').css('display', 'none');
				}
			});
			return false;
		}
	});
}

function img_in_middle() {

}

function changePaybtn() {
	$('.btnplace').show();
	$('.btnpay').hide();

	$('.selectPaymentOption').on('change', function() {
		var btnVal = $(this).val();

		if (btnVal == 'payonline') {

			$('.btnpay').show();
			$('.btnplace').hide();
		} else {
			$('.btnpay').hide();
			$('.btnplace').show();
		}
	});
}

function displayplan() {

	$('.planBased .selectDays').on('change', function() {
		var selectDays = $(this).val();
		//console.log(selectDays);
		if (selectDays != '0') {
			$(this).parent().find('.rptmeal').show();
		}
		if (selectDays == '1day') {
			$(this).parent().parent().parent().find('.infoPlan').find('p').hide();
			$(this).parent().parent().parent().find('.infoPlan').find('.1day').show();
		}
		if (selectDays == '40days') {
			$(this).parent().parent().parent().find('.infoPlan').find('p').hide();
			$(this).parent().parent().parent().find('.infoPlan').find('.40days').show();
		}
		if (selectDays == '20days') {
			$(this).parent().parent().parent().find('.infoPlan').find('p').hide();
			$(this).parent().parent().parent().find('.infoPlan').find('.20days').show();
		}
		if (selectDays == '60days') {
			$(this).parent().parent().parent().find('.infoPlan').find('p').hide();
			$(this).parent().parent().parent().find('.infoPlan').find('.20days').show();
		}
	});
	$('.datebased .selectDays').on('change', function() {
		var selectDays = $(this).val();
		if (selectDays != '0') {
			$(this).parent().parent().find('.dateContainer').show();
		}
		if (selectDays == '0') {
			$(this).parent().parent().find('.dateContainer').hide();
		}
	});

	$('.rptmeal').on('change', function() {
		var repeatMeal = $(this).val();
		if (repeatMeal == 'mf' || repeatMeal == 'ms' || repeatMeal == 'msu') {
			$(this).parent().parent().find('.calDiv').show();
			$(this).parent().find('.wkdays').hide();
		}
		if (repeatMeal == 'yourChoice') {
			$(this).parent().parent().find('.calDiv').show();
			$(this).parent().find('.wkdays').show();
		}
	});
}

function show_PaymentDetails() {
	$('.selectPaymentOption').on('change', function() {
		var option = $(this).val();
		console.log("option : " + option);

		if (option == 'wallet') {
			$('.Paymentdetails_container div').hide();
			$(this).parent().parent().parent().find('.Paymentdetails_container .wallet_details').show();
		}
		if (option == 'cod') {
			$('.Paymentdetails_container div').hide();
			$(this).parent().parent().parent().find('.Paymentdetails_container .cod_details').show();
		}
		if (option == 'neft') {
			$('.Paymentdetails_container div').hide();
			$(this).parent().parent().parent().find('.Paymentdetails_container .neft_details').show();
		}
		if (option == 'chq') {
			$('.Paymentdetails_container div').hide();
			$(this).parent().parent().parent().find('.Paymentdetails_container .chq_details').show();
		}
		if (option == 'payonline') {
			$('.Paymentdetails_container div').hide();
			$(this).parent().parent().parent().find('.Paymentdetails_container .payonline_details').show();
		}
		if (option == 'cwp') {
			$('.Paymentdetails_container div').hide();
			$(this).parent().parent().parent().find('.Paymentdetails_container .cwp_details').show();
		}

	});

}

function fly_cart() {
	$('.add-to-cart').on('click', function() {

		//alert();
		var cart = $('.shopping-cart');

		var position = $('.shopping-cart').position().left;
		console.log(position);

		//alert('cart ='+cart);

		var imgtodrag = $(this).parent().parent().parent('.thumbnail').find('.thumbnail-img-parent').find("img").eq(0);

		if ($(window).width() < 767) {
			cart = $('.cart_mob');
			var imgclone = imgtodrag.clone().offset({
				top : imgtodrag.offset().top,
				left : imgtodrag.offset().left
			}).css({
				'opacity' : '0.5',
				'position' : 'absolute',
				'height' : '150px',
				'width' : '150px',
				'z-index' : '100'
			}).appendTo($('body')).animate({
				'top' : cart.offset().top,
				'left' : cart.offset().left + 50,
				'width' : 25,
				'height' : 25
			}, 1000, 'easeInOutExpo');

			setTimeout(function() {
				cart.effect("shake", {
					times : 2
				}, 200);
			}, 1500);

			imgclone.animate({
				'width' : 0,
				'height' : 0
			}, function() {
				$(this).detach()
			});

			// change functionality for smaller screens
		} else if (imgtodrag) {

			$('html, body').animate({
				scrollTop : '0px'
			}, 500);
			var imgclone = imgtodrag.clone().offset({
				top : imgtodrag.offset().top,
				left : imgtodrag.offset().left
			}).css({
				'opacity' : '0.5',
				'position' : 'absolute',
				'height' : '150px',
				'width' : '150px',
				'z-index' : '100'
			}).appendTo($('body')).animate({
				'top' : cart.offset().top + 10,
				'left' : cart.offset().left + 50,
				'width' : 75,
				'height' : 75
			}, 1000, 'easeInOutExpo');

			setTimeout(function() {
				cart.effect("shake", {
					times : 2
				}, 200);
			}, 1500);

			imgclone.animate({
				'width' : 0,
				'height' : 0
			}, function() {
				$(this).detach()
			});
		}

		return false;
	});
}

function fly_cart_text() {
    
	$('.add-to-cart-text').on('click', function() {

		//alert('fly..');
		var cart = $('.shopping-cart');

		var position = $('.shopping-cart').position().left;
		console.log(position);

		//alert('cart ='+cart);
		
		var flyText = $(this).parent().parent().siblings().children('.text-fly');

		if ($(window).width() < 767) {
			cart = $('.cart_mob');
			var imgclone = flyText.clone().offset({
				top : flyText.offset().top,
				left : flyText.offset().left
			}).css({
				'opacity' : '0.5',
				'position' : 'absolute',
				'height' : '150px',
				'width' : '150px',
				'z-index' : '100'
			}).appendTo($('body')).animate({
				'top' : cart.offset().top,
				'left' : cart.offset().left + 50,
				'width' : 25,
				'height' : 25
			}, 1000, 'easeInOutExpo');

			setTimeout(function() {
				cart.effect("shake", {
					times : 2
				}, 200);
			}, 1500);

			imgclone.animate({
				'width' : 0,
				'height' : 0
			}, function() {
				$(this).detach()
			});

			// change functionality for smaller screens
		} else if (flyText) {

			$('html, body').animate({
				scrollTop : '0px'
			}, 500);
			var imgclone = flyText.clone().offset({
				top : flyText.offset().top,
				left : flyText.offset().left
			}).css({
				'opacity' : '0.5',
				'position' : 'absolute',
				'height' : '150px',
				'width' : '150px',
				'z-index' : '100'
			}).appendTo($('body')).animate({
				'top' : cart.offset().top + 10,
				'left' : cart.offset().left + 50,
				'width' : 75,
				'height' : 75
			}, 1000, 'easeInOutExpo');

			setTimeout(function() {
				cart.effect("shake", {
					times : 2
				}, 200);
			}, 1500);

			imgclone.animate({
				'width' : 0,
				'height' : 0
			}, function() {
				$(this).detach()
			});
		}

		return false;
	});
}

function checked_hide_show() {
	$('input').on('ifChecked', function(event) {
		$($(this).attr('data-show')).show();
		$('.customeseletbox').trigger('render');
	});
	$('input').on('ifUnchecked', function(event) {

		$($(this).attr('data-show')).hide();
	});
}

function custom_scrollar() {
	/**
	 * This part does the "fixed navigation after scroll" functionality
	 * We use the jQuery function scroll() to recalculate our variables as the
	 * page is scrolled/
	 */
	$(window).scroll(function() {
		var window_top = $(window).scrollTop();
		// the "12" should equal the margin-top value for nav.stick
		var div_top = $('#nav-anchor').offset().top;
		if (window_top > div_top) {
			$('nav').addClass('stick');

		} else {
			$('nav').removeClass('stick');

		}
	});

	/**
	 * This part causes smooth scrolling using scrollto.js
	 * We target all a tags inside the nav, and apply the scrollto.js to it.
	 */

	$("nav.scroll-design a").click(function(evn) {
		evn.preventDefault();
		$('html,body').scrollTo(this.hash, this.hash);

	});

}

function addactiveClass() {
	$('.scroll-design li a').click(function() {
		$('.scroll-design li a').removeClass('active');
		$(this).addClass('active');
	});
}

function selectCategories() {
	$('.select_categories').on('change', function() {
		$('.scroll-design li:nth-of-type(' + this.value + ') a').attr('href');
		var id_ele = $('.scroll-design li:nth-of-type(' + this.value + ') a').attr('href');
		if ($(window).width() < 768) {
			$('html, body').animate({
				scrollTop : ($(id_ele).offset().top - ($('header').innerHeight() + $('.mobile-select').innerHeight())) + 'px'
			}, 1000);
		} else {
			$('html, body').animate({
				scrollTop : ($(id_ele).offset().top - $('.mobile-select').innerHeight()) + 'px'
			}, 1000);
		}
	});
	$(window).bind('scroll', function(e) {
		if ($(window).width() < 992) {
			var top = (document.documentElement && document.documentElement.scrollTop) || document.body.scrollTop;
			if (top < $('#content').offset().top) {
				$('#content').css('padding-top', '0');
				$('.mobile-select').removeClass('mobile-select-fixed');
			} else {
				$('#content').css('padding-top', '100px');
				$('.mobile-select').addClass('mobile-select-fixed');
			}
		}

	});
	$('.my-tabs-menu a[href="#dinner"]').on('shown.bs.tab', function(e) {
		$('#content').css('padding-top', '0');
		$('.mobile-select').removeClass('mobile-select-fixed');
	});

}

var active_class = function(click_class) {
		$('.scroll-design ul li a').removeClass('active');
		$(click_class).addClass('active');
	}
var make_active = function() {
		$(window).bind('scroll', function(e) {
			var top = (document.documentElement && document.documentElement.scrollTop) || document.body.scrollTop;
			
			if (top >= $('#offer').offset().top) {
				active_class('.offer');
				
				console.log();
			}
			if (top >= $('#soup').offset().top) {
				active_class('.soup');
			}
			
			if (top >= $('#dim').offset().top) {
				active_class('.dim');
				
			}	
			if (top >= $('#starters').offset().top) {
				active_class('.starters');
				
			}	
			if (top >= $('#appetizer').offset().top) {
				active_class('.appetizer');
				
			}	
			if (top >= $('#bao').offset().top) {
				active_class('.bao');
				
			}	
			if (top >= $('#soupy').offset().top) {
				active_class('.soupy');
			}
			if (top >= $('#big_stirfry').offset().top) {
				active_class('.big_stirfry');
			}			
		});
	}


function on_modal_show(){
	$('.home-modal').on('shown.bs.modal', function (e) {
  		$('.customeseletbox').trigger('render');
	});
}
function showModel() {
	
	if ($(".search-modal-body").length == 1 ){
		$('.home-modal').modal('show');
		
	}   
	
}
jQuery(document).ready(function($) {
	//alert();

	checked_hide_show();
	select_tab_new();
	$(".chosen").chosen({
		width : "95%"
	});
	addactiveClass();
	openScroll();
	closeScroll();
	make_active();
	on_modal_show();
	
	sortlist();
	call_select();
	select_tab();
	deletHistory();
	assign_box_size();
	assign_box_size_menu();
	img_in_middle();
	changePaybtn();

	custom_scrollar();
	fly_cart_text();
	selectCategories();

	/*************** js for custom select***********/
	$('.customeseletbox').customSelect();

	/*************** js for date picker***********/

	//$( "#minDate" ).datepicker({autoSize: true, minDate: 0});

	$(".Datepicker").datepicker({
		minDate : 0,
		dateFormat : "dd-mm-yy",
		orientation : "top left",
	});

	$(".Datepickersingle").datepicker({
		minDate : 0,
		dateFormat : "dd-mm-yy",
		orientation : "top left",
	});

	/*
	$(".Datepickermulti").datepicker({
	dateFormat : "dd-mm-yy",
	multidate : true,
	todayHighlight : true
	});*/

	//validation();
	active_tab();
	remove_from_cart();
	plus_cart();
	minus_cart();
	icheck_call();
	$(window).resize(function() {
		assign_box_size();
		assign_box_size_menu();
	});

	var today = new Date();
	$('#with-altField').multiDatesPicker({
		minDate : 0,
		altField : '#altField',
		dateFormat : "d-m-yy"
	});

	$('.bookingdate_picker').multiDatesPicker({
		minDate : 0,
		altField : '#altField',
		dateFormat : "d-m-yy"
	});

	$('.my-cart').addClass('active');
	$('.testSelAll2').SumoSelect();

	$('.msgbox p').on('click', function() {
		$(this).parent().find('p').hide();
		$(this).parent().find('.select.input-wrapper').show();
	});

	displayplan();
	show_PaymentDetails()

	/***************************************
	 * Function for flying effect to cart *
	 * *************************************/
	fly_cart();
});
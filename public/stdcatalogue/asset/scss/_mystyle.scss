//mixins for font face
@mixin font-face($style-name, $file, $family, $category:"") {
    $filepath:  $family + "/" + $file;
    @font-face {
        font-family: "#{$style-name}";
        src: url($filepath + ".eot");
        src: url($filepath + ".eot?#iefix") format('embedded-opentype'), url($filepath + ".woff") format('woff'), url($filepath + ".ttf")  format('truetype'), url($filepath + ".svg#" + $style-name + "") format('svg');
    }
    %#{$style-name} {
        font: {
            @if $category != "" {
                family: "#{$style-name}", #{$category};
            }
            @else {
                family: "#{$style-name}";
                weight: normal;
            }
        }
    }
}
@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
     -moz-border-radius: $radius;
      -ms-border-radius: $radius;
          border-radius: $radius;
}
@mixin rotate( $degrees ) {
  -webkit-transform: rotate(#{$degrees}deg);
  -moz-transform: rotate(#{$degrees}deg);
  -ms-transform: rotate(#{$degrees}deg);
  -o-transform: rotate(#{$degrees}deg);
  transform: rotate(#{$degrees}deg);

  filter:  progid:DXImageTransform.Microsoft.Matrix(sizingMethod='auto expand', M11=#{cos($degrees)}, M12=-#{sin($degrees)}, M21=#{sin($degrees)}, M22=#{cos($degrees)});
  -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(sizingMethod='auto expand', M11=#{cos($degrees)}, M12=-#{sin($degrees)}, M21=#{sin($degrees)}, M22=#{cos($degrees)})";
  zoom: 1;
 }

@mixin transition($transition-property, $transition-time, $method) {
    -webkit-transition: $transition-property $transition-time $method;
       -moz-transition: $transition-property $transition-time $method;
        -ms-transition: $transition-property $transition-time $method;
         -o-transition: $transition-property $transition-time $method;
            transition: $transition-property $transition-time $method;
}
@mixin colors($colors,$name) {
  #{$name}_color{
      color:$colors;
    } 
  #{$name}_bg{
      background:$colors; 
  }
  #{$name}_border{
      border:1px solid darken($colors,10%); 
  }
}
@mixin box-shadow($top, $left, $blur, $color, $inset: false) {
  @if $inset {
    -webkit-box-shadow:inset $top $left $blur $color;
    -moz-box-shadow:inset $top $left $blur $color;
    box-shadow:inset $top $left $blur $color;
  } @else {
    -webkit-box-shadow: $top $left $blur $color;
    -moz-box-shadow: $top $left $blur $color;
    box-shadow: $top $left $blur $color;
  }
}

@mixin animate-dalay($time) {
  -webkit-animation-delay: $time;
     -moz-animation-delay: $time;
       -o-animation-delay: $time;
      -ms-animation-delay: $time;
}
@mixin backface-visibility($backface) {
  -webkit-backface-visibility: $backface;
     -moz-backface-visibility: $backface;
       -o-backface-visibility: $backface;
      -ms-backface-visibility: $backface;
}
@mixin transform-translate($translate) {
  -webkit-backface-visibility: $translate;
     -moz-backface-visibility: $translate;
       -o-backface-visibility: $translate;
      -ms-backface-visibility: $translate;
}
@mixin transition-duration($duration) {
  -webkit-transition-duration: $duration;
     -moz-transition-duration: $duration;
       -o-transition-duration: $duration;
      -ms-transition-duration: $duration;
}
@mixin opacity($opacity) {
  opacity: $opacity;
  $opacity-ie: $opacity * 100;
  filter: alpha(opacity=$opacity-ie); //IE8
}


@include font-face('opensans-light-webfont', 'opensans-light-webfont','../fonts/');
@include font-face('opensans-bold-webfont', 'opensans-bold-webfont','../fonts/');
@include font-face('opensans-regular-webfont', 'opensans-regular-webfont','../fonts/');


/*****************************************************************************************************************/

body{
  font-family:'opensans-regular-webfont', sans-serif;
  color:#707074; font: 16px opensans-regular-webfont,sans-serif;
  background:#f5f5f5;
}
.ml15 {
	margin-left: 15px;
}
readonly.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{
  background-color:transparent;
}


li{
  list-style:none;
}
a:hover {
    text-decoration: none;
}

.btn.common-btn-theme-light{
       transition: all 0.5s linear 0s;
       background-color: $logo-color;
       border-radius:0;
       border: 1px solid $logo-color;       
         &:hover{
          background-color:$zoro-balance;
        }
     }
.marginzero{margin: 0;}
.paddingzero{padding: 0;}

.right {
    float: right;
}
.left {
    float: left;
}
 table{
  width: 100%;
 }
 .pageContainer{
  background: #f5f5f5/*$page-bg-color*/;
 }
 .modal-lg {
    width: 60%;
}
.cart_mob {
    display: block;
    text-align: center;
}
 
.tooltip-inner{
  background-color:#000;
  border-radius: 0;
  padding:7px 12px;
} 
.tooltip.top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000;
    font:12px opensans-regular-webfont,sans-serif;
    color:#222;
    
}
 /**********************css for model box********************/ 


.modal-content {
    padding: 25px;
    
    h4.monthTitle {
     text-align: center;
     color: $text-color;
     font-family: opensans-bold-webfont;
  }
}
.monthly_calender {
   // border: 1px solid $dark-grey;
    padding: 20px;
    height: 500px;
    
    .calDark_bg{
      background: darken($calender-bg, 3%);
    }
    .calLight_bg{
      background:$calender-bg;
    }
    .dayContainer {
      
      /*display: inline-block;*/
      padding: 0 7px 5px 0;
      margin: 0;
      height:245px;
      
      span{
        background:lighten($orange, 10%);
        color: #FFF;
        display: inline-block;
        font-family: opensans-bold-webfont;
        margin: 0 0 8px;
        padding: 2px 7px;
      }
      ul {
        padding-left: 25px;
        
        li {
          color: #666;
          list-style: outside none disc;
      }
    }
  }
}

.modal-content {
  
  .dish_details{
    overflow:hidden;    
       
       .imgContainer{
           float: left;
           width:30%;
       }  
       .menuContains{
           float: left;
           width:70%;
           
           ul{
           	
           	padding:0 0 0 15px;
	           	
	          li{
	          		color: #222;
	          		list-style: outside none disc;
	          }
           }
         
       }  
    }
}

#mydates{
  
  table.orderDetails_table{
    
      border: 1px solid #bbb0ae;
      tr{
        &:nth-child(2n) {
            background: none repeat scroll 0 0 #f9f9f9;
        }
        &:nth-child(odd) {
            background: none repeat scroll 0 0 #FFFFFF;
        }

      }
      
      td{
        
        padding:10px;
        
        .chosen-container-multi .chosen-choices{
          background-image: none;
        }
        .chosen-container-multi .chosen-choices li.search-choice{
           background: #a0d468;
           border-color: #84b84c;
           box-shadow: none;
           color: #fff;
           font-weight: 600;   
           border-radius:0;        
        }
        
      }
        .alert.alert-danger {
          display: inline-block;
          margin: 0 10px 2px 0;
          padding: 8px 15px;
          background:#FBDFB9;
          border-radius:0;
      }
      .alert.alert-info{
        
         display: inline-block;
          margin: 0 10px 2px 0;
          padding: 8px 15px;
         background-color:#D8F4FF;
         border-radius:0;
      }
  }
  
}
 .alert{
   margin-top:20px;
 }
 
 
 /*********************css for custom select***************************/
.selectContainer{
  .customSelectFocus, .customSelectOpen{
    
    background-color: #fff;
  }
  
  
   span.customSelect {
    text-align: center;
    background-color: #fff;
    color:#7c7c7c;
    padding:3px 7px;
    width:100% !important;
  }
  span.customSelect.changed {
    background-color: #fff;
  }
  
}
 
 /*****************************************************/
 
body {
	background-color: #F5F5F5;
}



header {
    padding: 15px 0 0;
    background-color: #FFF;
    border-bottom:2px solid $logo-color;
    
    .logoContainer {
      img{
        display:inline-block;
      }
      padding: 0 0 15px;
      text-align: center;
      h1{
         margin: 0;
        font-size: 25px;
        color: black;
      }
    }    
    .accountLinks ul{       
     
          li{
         display: inline-block;
         margin: 0 25px 0 0;
            &:last-child{
              margin: 0;
            }
         
         a{
          color:$text-color;
            &:hover{
            color:lighten($text-color , 20%)
            }
          
           i {
            display: block;
            font-size: 22px;
            text-align: center;
        }
        span {
            font-size: 14px;
            font-weight: 600;
        }
        
         }
         
        
      }
  }
  
.callContainer {
      margin: 0px 0 19px;
      
      span {
        font-size: 23px;
        margin: 0 0 20px;
    }
  }
  .amtContainer{
      
    i.fa-money{
      font-size: 40px;
    } 
    
    .amtContent {
        display: inline-block;
        position: relative;
        top: 9px;       
        
        input[type="text"] {
          height: 24px;
      } 
      
      a{
        display: inline-block;
        
          i.fa.fa-plus-square {
            font-size: 25px;
            position: absolute;
            right: 2px;
            top: -1px;
            color:$theme-color;
        }    
      }       
        
        span {
        display: block;
        text-align: center;
         font-size: 13px;
      }
    } 
  }
    
 
    .navbar-default{
      background-color: #FFF;
      margin-bottom: 0;
      height: 55px;
      border-bottom:2px solid $logo-color;
            
      .navbar-collapse {
        text-align: center;
        
        .navbar-nav {
          display: inline-block;
          float: none;
          width: 100%;
          
          
          li{
          .active {
            border-bottom: 4px solid $theme-color;
        }
          
            a{
              background: none;
              color: #FFFFFF;
              font-weight: bold;
              
              &:hover{
                background: none;
              }
              &:focus{
                background: none;
              }
            }
            
          }
          
          .active{
            a{
              background: none;
              color: #FFFFFF;
              font-weight: bold;
              
              &:hover{
                background: none;
              }
              &:focus{
                background: none;
              }
            }
          }
          
      }
                
    }     
      
    }
    
    .navbar-fixed-top, .navbar-fixed-bottom{
      z-index: 3;
    }
}
/*shake issue effect*/
header .accountLinks ul li.my-cart{
  	position:relative; 
  	/*width: 126px;*/
  	width:142px;
  	height: 44px;
  	a{
  		position: absolute;
		top: 4px;
		left: 0;
		width: 100%;
  	}
 } 


.sliderContainer{
  margin-bottom: 10px;
  
  .carousel img {
      width: 100%;
  }
  
}

.customizeDiv {
    margin-bottom: 25px;
    overflow: hidden;
  
   a {
    
      display: block;
      overflow: hidden;
  }

}
.message {
      background-color: $nav-bg;
      padding:0;
      color:#fff;
      border: 1px solid #fc6e51;
  }
footer{
  .container {
      padding: 15px 15px;
      
      .social_links{
        
        a{
          display: inline-block;
          font-size: 18px;
          margin: 0 5px;
          color:$heading-color
      }
        /*.fb {
          color: #0e5592;
      }
      .twt{
        color:#1DC6FF;
      }
      .gplus{
        color:#F63E28;
      }*/
    }
    
  }
      
}


/*cart page*/

.innerpage{

  .pageTitle {
       
        padding: 40px 0 20px;     
        overflow: hidden;
       
        h4 {
          border-bottom: 1px solid #707074;
          margin: 0;
          text-align: center;
          
          span{
              background: #3d3230;
                color: white;
              display: inline-block;
              font-size: $heading-font-size;
              padding: 5px 25px;
              font-family: opensans-bold-webfont;
          }
      }
      
              
    }
    
  
    
.pageTitle {
      
      .pgheading {
         color: #707074;
         font-size: $page-heading;
          font-family: opensans-bold-webfont;
          text-align: center;
           margin-top: 20px;
      }
      
    }
    
    
}


 .cart{
  
  
  .shoppinCart{
    
    background: #ffffff;
     padding-bottom: 3em;
      overflow: hidden;
    
    table{
      margin-bottom: 30px;
        
    
      thead {
         background-color: $sub-heading-color;       
          color: white;
        
        th {
            text-align: center;
             padding: 10px 0;
        } 
      }
      
      tbody{
        
        background-color: #ffffff;
        
        tr{
          
          td {                
              text-align: center;
          } 
          
          &:last-child {
            td{
              border-bottom: 2px solid $heading-color;
            }
          }       
          
        }
              
              
        td {
                  
            text-align: center;
            
        } 
      }
.menuList {
          width: 20%;
          
          .menuImg_cart {
               display: inline-block;
               height: 146px;
               margin: 20px 15px;
               width: 240px;
               position: relative;
            
             
             img {
              width: 100%;
              height: 100%;
          }
          .dishName {
              background:  rgba(0, 0, 0, 0.6);
              bottom: 0;
              color: white;
              display: inline-block;
              font-family: opensans-bold-webfont;
              left: 0;
              padding: 3px 0;
              position: absolute;
              width: 100%;
          }
        }
}
      .date {
          width: 20%;
          
          .calDiv {
            position: relative;   
            
            input.Datepicker{
              padding-right: 45px;
            }         
            
           i.fa-calendar{ 
                font-size: 25px;
              position: absolute;
              right: 7px;
              top: 3px;
              color: $logo-color;
            }           
        }
          
      }
      .menuList{ 
        width: 5%;
      }
      .cart_qty {
          width: 15%;
           button.btn-success.minus{
              background-color: $theme-color;
            border-color:$theme-color; 
            border-radius:0; 
            margin:0px !important;
            padding:0px 4px;
            &:hover{
              background-color: lighten($theme-color, 5%);
            } 
          }
          button.btn-danger.plus{
            background-color: #0066b1;
            border-color: lighten(#0066b1, 15%); 
            border-radius:0;
            margin-left:-4px !important;
            padding:0px 4px;
            
            &:hover{
              background-color: lighten($theme-color, 20%);
          
            }
          }
      }
      .cart_Total {
          width: 10%;
      }
      .cart_menuPrice{
        width: 5%;  
      }
      .cart_close {
          width: 5%;
          
         .close {
            float: none;
        
        }
      }
    
      label {
          border: 1px solid #ccc;
          padding: 5px 12px;
         //margin: 0 5px 0 10px;
         color: $text-color;
      }
    }
    
    
    .promocodeContainer {
        background: $menuBg-color;
        /*padding: 16px 40px;*/
        
       .promocodeTitle{
         background: $sub-heading-color;
        
          p{
            color: white;
            font-family: opensans-bold-webfont;
            padding: 15px 0;
            text-align: center;
          }
       }
        
        .form-group.updateCart_div {
          padding: 10px 100px 25px;
          text-align: center;
          
          input[type="text"]{
            margin-bottom: 10px;
          }
      }
        
        button.btn{         
           font-family: opensans-bold-webfont;
           border-radius:0;
          
          i{
            	font-size:20px;
          }
        }
    }
    
    
    
    .totalAmtCart {
        float: right;
        width: 50%;
        
        .cartContent {
                    
          &:last-child {
            border: 2px solid $heading-color;
            padding: 8px 0 0 2px;
        }         
          
          p{
            display: inline-block;
            width: 65%;
        }
        label {
            width: 30%;
        }
        
      }
        
        
    }
    
  }
  
  .cartCheckout button.btn{
    float: right;
      margin: 24px 0 20px;
      
      i{
       font-size: $page-heading;
      }
  }
  
 }
 
 
 /****************************
  * OrderSummary page
  ****************************/
 .summaryContainer .bg-success {
    background-color: #F5F5F5;    
}
.requirement select {
    background:  #fff;
    border: 1px solid #a4a4a4;
    padding: 5px 20px;
}
.requirement select.selectDays{
      margin: 0 20px 20px 0;
  }
  
.cartContent p {
    display: inline-block;
    width: 65%;
}

 .shoppinCart .summaryContainer {
    margin-bottom: 30px;
}
.cartCheckout {
    background:  #f1f1f1;
    overflow: hidden;
    padding: 24px 0;
 
}
.cartCheckout.orderSummary button.btn {
    margin: 0;
}
.cart .cartCheckout.row .col-lg-4.col-md-4.col-sm-12.col-xm-12.common-form > div {
    padding: 10px 0 5px;
}

.SumoSelect > .CaptionCont > span.placeholder {
    color: #222;
    font-style: normal;
}
.requirement .calDiv input {
    background:  #FFF;
    border: 1px solid #a4a4a4;
    border-radius: 0;
    box-shadow: none;
    position: relative;
    z-index: 2;
}
.requirement .form-control::-moz-placeholder{
  color:#222;
}
.cart .shoppinCart table .type {
    width: 5%;
}
.summaryContainer h1.totalAmt {
    font-size: 25px;
    margin-top: 0;
}
.summaryContainer, 
//.chooseDay{display:none;}


 /****************************
  * End OrderSummary page
  ****************************/
 
 /****************************
  * Start Widget
  ****************************/
.widget-box{
  @include box-shadow(0,2px,0, rgba(0, 0, 0, 0.01));
  background: none repeat scroll 0 0 #fff;
  padding: 15px;
  .widget-header {
      background: #F1EDED; /*#bbb0ae;*/
      padding: 5px 10px;
      
      h5{
        color: #222;/*#fff;*/
        font: 16px opensans-regular-webfont,sans-serif;
        margin:0;
       
       .ace-icon {
            margin: 0 10px 0 0;
        }
      }    
  }
  .widget-body {
      border: 1px solid #F1EDED; /*#bbb0ae;*/     
      
      
      .widget-main.no-padding {
        table{
          tr:nth-child(2n+1) {
             background: $oddTr;
          }
          tr:nth-child(2n){
             background: $evenTr;
          }
          tr{
            
            td{
              color: #999;
              padding:10px;
              i.fa {
                //color: #707074;
                width: 15%;
              }
              i.fa.greenColor, i.fa.redColor {
                //color: #707074;
                width: auto;
              }
              .greenColor{
                color:#8cc152; 
              }
              .redColor{
                color:#ed5565; 
              }
              .btn{
                font-family:opensans-regular-webfont,sans-serif;
                padding:2px 12px;
              }
            }
            td:first-child {
               /*
                padding-bottom: 5px;
                               padding-left: 10px;
                               padding-top: 5px;*/
               
                width: 30%;
            } 
            td:last-child {
              // padding: 5px 10px 5px 0;
              width: 65%;
              
              button {
                  margin-left: 10px;
              }
              i {
                  margin: 0 5px 0 10px;
              }
            }
          }
        }
      }
  }
}

.wallet_widget{
    ul{
        padding: 0 0 0 15px;
        li {
            margin-bottom: 15px;
            &:last-child {
            margin-bottom: 0;
           }
        }
    }
    
    .bal_overlay{
        //background: none repeat scroll 0 0 rgba(0, 0, 0, 0.75);
        height: 100%;
        opacity: 0;
        position: absolute;
        right: 0;
        text-align: center;
        top: 0;
        transition: opacity 0.25s ease 0s;
        -webkit-transition: opacity 0.25s ease 0s;
        -moz-transition: opacity 0.25s ease 0s;
        width: 100%;
        padding:42px 0 30px;
        font: 18px opensans-regular-webfont;
        color:#222;
     }
     
    .available_balance{
      border:1px solid #3bafda;
      
      .iconImg{
        background: #3bafda;
        color: #fff;
      }
      .bal_overlay{
        background: rgba(173, 233, 255, 0.8);
        padding:30px 0 30px;
      }
      
    }
    .available_balance:hover .bal_overlay{
      opacity:1;
    }
    
    .user_balance {
        border: 1px solid  #8cc152;
        .iconImg {
            background: #8cc152;
            color: #fff;
        }
        .bal_overlay{
          background: rgba(206, 237, 170, 0.8);
        }
    }
    .user_balance:hover .bal_overlay{
      opacity:1;
    }
    .lock_balance {
        border: 1px solid #ed5565;
       .iconImg {
          background: #ed5565;
          color: #fff;
      }
      .bal_overlay{
        background: rgba(237, 85, 101, 0.8);
      }
    }
    .lock_balance:hover .bal_overlay{
      opacity:1;
    }
    
   .available_balance, .user_balance,.lock_balance {
      color:#707074;
      overflow: hidden;
      position:relative;
      
     .iconImg {
        float: left;
        padding: 35px 0;
        text-align: center;
        width: 33%;
        
        i{
          font-size:40px;
        }
    }
    .balDetail {
        float: left;
        width: 67%;
        padding: 0 15px;
        text-align: right;
    }
  }
 
}

.order_summary.common_table {
    border: 1px solid #f1f1f1;/*#bbb0ae*/
   margin-bottom:0;
    tr{
      &:nth-child(2n) {
        background: $evenTr;
      }
      &:nth-child(2n+1) {
        background:  $oddTr;
      }
      th {
        //background-color: #bbb0ae;
        border-top: 1px solid #f1f1f1;
        background-color: #f1eded;
        color: #222;
        text-transform: capitalize;
      }
      td{
       // border:none;
        color: #999;
       // font-size: 16px;
        padding: 10px 10px;
        border-bottom: 1px solid #f1f1f1;
        border-top: 1px solid #f1f1f1;
      }
    } 
}

/****************************
  * End Widget
  ****************************/

/****************************
  * Start Summary Container
  ****************************/
 .ui-datepicker,.ui-datepicker-header.ui-widget-header.ui-helper-clearfix.ui-corner-all{ border-radius: 0;}
 .ui-datepicker th{padding:0;}
  .ui-datepicker td span, .ui-datepicker td a{ padding: 0 5px;}
  .ui-datepicker{
    display:none;
  }
 
 .bgtable{
    background: #fff;
    padding: 15px;
    margin-bottom: 20px;
 }
 
.summaryContainer .menuinfo_plan .planContainer .datebased table.table-condensed tbody tr td, .summary_container .menuinfo_plan .planContainer .datebased table.table-condensed tbody tr td.active.day {
    background:  #7e7e7e;
    text-shadow: none;
}
.common-select-parent{
	position:relative;
	display:inline-block;  
	.right-arrow{
		position: absolute;
    	right: 12px;
    	top: 6px;
	}
}
.common_select{
	background: white;
    border: 1px solid #a4a4a4;
    
}
 .summary_container{
   font-family: opensans-regular-webfont,sans-serif;
   
   .SumoSelect > .optWrapper.open {
      top: 30px;
    }
   .bg-success {
      background-color: #fff;
      padding: 12px 0 10px;
    }
   
   .table-responsive{
      overflow: visible;
   }
    table.table{
      tbody{
        tr{
           td {
              border-bottom: 1px solid #ddd;
              border-top: medium none;
              padding-top: 25px;
          }
        }
      }  
      
      td.menuList {
        width: 23%;
        .menuImg_cart {
            height: 174px;
            margin: 0px 0px 20px;
            position: relative;
            width: 240px;
            
            img {
              height: 100%;
              width: 100%;
          }
          .dishName {
              background:  rgba(0, 0, 0, 0.6);
              bottom: 0;
              color: white;
              display: inline-block;
              font-family: opensans-bold-webfont;
              left: 0;
              padding: 3px 0;
              position: absolute;
              width: 100%;
              text-align:center;
          }
        }
      }
       td.menuinfo_plan {
          width: 57%;
      }
      .menu_subtotal {
            text-align: center;
			  width: 14%;
			  vertical-align: top;
			  padding-top: 40px;
      }
      .cart_close{
        width: 6%;
      }
   }
   .menuinfo_plan{
      .menuSummary {
          margin: 0 0 12px;
          overflow: hidden;
          
          span.mealType {
              font-size: 20px;
              margin: 0 25px 0 0;
          }
          span.price {
              font-size: 20px;
          }
          
        .qtyContainer {
           display: inline-block;
           float: right;
           padding-top: 8px;
           .btn {
              border-radius: 0;
              color: #fff;
              margin: 0 18px 0 15px;
              padding: 4px 8px;
         }
         .btn-success{
            background-color: #8cc152;
            border:1px solid #8cc152;
            &:hover{
              background-color: #A0D468;
            }
         }
         .btn-danger {
            background-color: $logo-color/*;#DA4453*/;
            border:1px solid $logo-color /*#DA4453*/;
            &:hover{
              background-color:$zoro-balance /*#ED5565*/;
            }
         }
       }
     }
     .planContainer{
          
         .planBased{
           .form-group {
                margin-bottom: 0px;
            }
            
          
         }
         .datebased{
           .form-group {
                margin-bottom: 0px;
            }
            
          
            #withAltField > input#altField {
                border: 1px solid #ccc;
                border-radius: 0;
                height: 35px;
                margin: 10px 0 0;
                width: 42%;
                padding: 0 5px;
            }
           .datepicker-inline{
             display:block !important;
             
             border: 1px solid #a4a4a4;
              border-radius: 0;
            
           }
           
            table.table-condensed{
              tbody{
                tr td {
                  padding-top: 7px;
                  border: 1px solid #fff;
                  border-radius: 0;
              }
            }
         }
         }
         
         
        .form-group{
          .selectPlan{
             display: block;
             width: 30%;
          }          
           select {
              background:  white;
              border: 1px solid #a4a4a4;
              margin-right: 15px;
              margin-bottom: 8px;
              padding: 4px 5px 3px;
              width: 30%;
          }
          .SumoSelect{
              margin-bottom: 8px;
          }
        }
       .calDiv{
         
          input.Datepickersingle {
            border: 1px solid #a4a4a4;
            border-radius: 0;
            box-shadow: none;
            display: inline-block;
            width: 30%;
            background-color:#fff;
          }
          .form-control::-moz-placeholder {
            color: #222;
         }
       }
     }
   } 
   
   .promocodeContainer{
     .promocodeTitle {
       // background:  #a4a4a4;
         p {
          color: #222;
          font: 20px opensans-regular-webfont,sans-serif;
          //padding: 2px 0 0;
      }
    }
    .updateCart_div {
        overflow: hidden;
        
        input[type="text"] {
          margin-bottom: 10px;
          width: 70%;
      }
      button.btn {
          border-radius: 0;
          float:none;
          margin:0;
          font-family: opensans-bold-webfont,sans-serif;
      }
      .btn-info {
        transition: all 0.5s linear 0s;
        background-color: $logo-color;
        border: 1px solid $logo-color;
        &:hover{
          background-color:$zoro-balance;
        }
     }
    }
   }  
   .cartCheckout{
      background: #fff;
      //margin: 0 0 20px;
      overflow: hidden;
      padding: 12px 0 10px;
       h4 {
          font: 20px opensans-regular-webfont;
          margin-bottom: 16px;
          margin-top: 0;
      }
      
       .selectPaymentOption, .selectThirdparty{
          background: #fff;
          border: 1px solid #a4a4a4;
          padding: 4px 5px 3px;
      }
        .paymentDetails{
          font-size: 17px;
          
        }
        /*
        .radioContent {
                    position: relative;
                    top: 7px;
                }*/
     
     .btn.common-btn-theme-light{
       transition: all 0.5s linear 0s;
       background-color: $logo-color;
        border: 1px solid $logo-color;
         &:hover{
          background-color:$zoro-balance;
        }
     }
   } 
   .common-btn-theme-light {
      background-color: $logo-color;
       &:hover{
          background-color:$zoro-balance;
        }
    }
    .cartContent.totalAmt{
      font: 20px opensans-regular-webfont;
      p{
        font-weight:bold;
      }
    }
    
    .editAddress {
      clear: both;
      font-family: opensans-regular-webfont;
      overflow: hidden;
      
      h4{
       font: 20px opensans-regular-webfont;
      }
      
      i.fa.fa-pencil-square-o{
        font-size:16px;
        color:#3BAFDA;
      }
      button{
        i.fa.fa-pencil-square-o{
          color:#fff;
        }
      }
      
   .common-form{
      &:last-child{
          /*padding-right:0;*/
         /*padding-right:15px !important;*/
       }
   }
   .common-form{
       &:nth-child(2) {
          /*padding-left: 0;*/
      }
    }
    .common-form{
         &:nth-child(3) {
          /*padding: 0;*/
      } 
     }
     
     textarea.form-control {
        margin-bottom: 10px;
        text-align:left;
    }
    
    .msgbox {
          margin: 0 0 20px;
          padding: 10px 27px;
          
         input {
              display: inline-block !important;
              width: 60%;
          }
         button {
              display: inline-block;
              float: right;
          }
           .selectCityOption {
              background: #fff;
              border: 1px solid #a4a4a4;
              padding: 4px 5px 3px;
          }
          h3{
            font-size: 18px;
          }
          
     }
    .lunch-msgbox {
        background-color: #ceedaa;
        border: 1px solid #c0df9c;
        
        .lunchAdd_input{
          overflow:hidden;
        }
    }
    .dinner-msgbox {
        background-color: #f9c7cc;
        border: 1px solid #dba9ae;
        
        .dinnerAdd_input{
           overflow:hidden;
        }
    }
    .break-msgbox.msgbox {
        background: #ade9ff;
        border: 1px solid #91cde3;
        
        .breakAdd_input{
          overflow:hidden;
        }
    }
   }
 }
 
 
 /**************************************
  * end Summary Container
  *************************************/
 
 
 
 
 
 
 .booking {
    margin-bottom: 40px;
    
    .bookingContainer{
    
      .historyTab{
        
      .nav-justified {
        
        li.active{
          background: $heading-color;
          
           a{
            background: $heading-color;
            
           }
           }
           
        li{
            
            a{
              
              background: $sub-heading-color;  
              border-radius: 0;
              color:#fff;
             font-family: opensans-bold-webfont;
             border:1px solid $sub-heading-color;
             &:hover{
              background: #bbb0ae;
              border:1px solid #bbb0ae;
             }
            }
          }               
        } 
        
        .common_table.sub_heading{          
          .dropdown {
            display: inline-block;
          .dropdown-menu{
            padding: 7px 10px;
             
            
            .ui-datepicker-calendar{
              tr{
                th{
                  //padding:0 0 0 5px;
                  background-color: #eee;
                  color: #82858e;
                  span{
                    color:#82858e;
                    font-family: opensans-regular-webfont,sans-serif;
                  }
                }
              }
            }
            
           input {
                border: 1px solid #a4a4a4;
                margin-top: 5px;
                width: 100%;
            }
          }
          }
        }
        
      }  
     
      .bookorderBtn{
        
         button {
            margin: 24px 0 20px;
            background:$logo-color;
            &:hover{
             background: $zoro-balance;
            }
            
            i{
              font-size: $page-heading;
            }
          }
      } 
    }   
   }     

    
.common_table {
      border: 1px solid  $sub-heading-color;/*lighten($nav-bg, 40%);*/
      tbody{
        background: #ffffff;
      }
     
        
      tr {
        th {
            background-color: $sub-heading-color;/*lighten($nav-bg, 40%);*/
            color: #fff;
            padding:10px;
            font-family: opensans-bold-webfont;
            text-transform: uppercase;
            text-align: left; 
        }
        td {
          padding:10px;
          border-bottom: $sub-heading-color;/*lighten($nav-bg, 40%);*/
         color:#999;
          
          span {
            font-family: opensans-bold-webfont;
            color: #000;
          }
        }
      }    
  }    
   
   
 .bookingContainer .historyTab .common_table.sub_heading a i.fa{
    color:#3BAFDA;
    &:hover{
      color:#4FC1E9;
    }
  }
 .bookingContainer .historyTab .common_table.sub_heading {
    border-top: 1px solid $heading-color;    
 }
  .bookingContainer .historyTab .common_table.sub_heading tr:nth-child(2n+1) {
    background:$oddTr;
}
.bookingContainer .historyTab .common_table.sub_heading tr:nth-child(2n) {
    background: $evenTr;
}
 .bookingContainer .historyTab .common_table.sub_heading tr th{
    background-color: $heading-color;
 }
  .bookingContainer .historyTab .common_table.sub_heading tr td {
    border-bottom: none;
  }
  
  
form#register-form
  {
    .selectContainer{    
      border: 0px solid #a9b1bc;
    }
  }  
  
  
 .loginPage{
  overflow: hidden;
  
  .pageTitle {
      padding: 40px 0 20px;
  }
  
  form{
    .form-group{
      input{
        text-align: center;         
      }
    } 
    .checkbox{
    a{
       color: #8cc152; /*#282ea0;*/
       &:hover{
           color: #a0d468; /*#5258ca;*/
       }
     }
   }
     .forgotCheck{
       margin-bottom: 10px;
         overflow: hidden;
         font-family: opensans-bold-webfont;
         
         a{
           color:  #8cc152; /*#282ea0;*/
           &:hover{
               color: #a0d468;/*#5258ca;*/
           }
         }
                  
      .checkbox {
          margin: 0;
      }
         
    }
    button.login-btn{
      font-family: opensans-bold-webfont;
    }
    h4{
       color: #222;
      font-family: opensans-bold-webfont;
    }
    .createBtn {
        background: $logo-color;
        color: #fff;
        font-family: opensans-bold-webfont;
        margin: 10px 0 50px 0;
        &:hover{
          background: $zoro-balance;
          color: #fff;
        }
    }
  }
   .loginPage form .bs-searchbox a{
    	color: #555;
    }  
   .btnContainer {
      margin: 10px 0 50px;
      
      .pull-left button {
        //  background: none repeat scroll 0 0 #8cc152;
      }
      button{
        font-family: opensans-bold-webfont;
      }
  }
  .custnote {
      display: block;
      font: 12px opensans-light-webfont;
      margin: 5px 0 0;
      text-align: center;
  }
  .selectContainer{
    position: relative;
    border: 2px solid #a9b1bc;
    .arrowspan{
       background-color: #a9b1bc;
        display: inline-block;
        height: 40px;
        position: absolute;
        right: 0;
        width:40px;
        /*z-index: 9;*/
      i{
            color: #ffffff;
         font-size: $page-heading;
          left: 12px;
          position: absolute;
          top: 11px;
      }
    }
  }
  .terms{
     label {
       
        input[type="checkbox"]{
          margin-top:2px;
        }
    }
  }
  
  
  .form-horizontal .control-label{
    padding-top:0;
  }
 }
 .checkbox-parent label{
  padding-left:5px;
 }
 


/*CSS For Sagar*/
 .wallet {
    color: #FFFFFF;
    font-family: opensans-bold-webfont;
    background: $logo-color;
    border: 1px solid $logo-color;
    /*padding: 5px 10px;*/
   padding: 5px 4px;
    margin: 0 0 0px 0;
     
     a {
      color: #fff;
     }
}
.wallet_addmore {
  cursor: pointer;
  &:hover {
    background: $zoro-balance; ;
    text-decoration: none;
    border: 1px solid $zoro-balance;
  }
}
.navbar-brand {
  padding: 0 10px;
  img{
    max-height:100%; 
  }
}
header .navbar-default .navbar-collapse .navbar-nav li a {
  text-transform: uppercase;
    border-bottom: 4px solid transparent;
}
.inner_pagr {
  padding: 30px 0 0 0;
}
.check_out {
      border-bottom: 1px solid #707175;
      text-align: center;
      span {
        background-color: #707175;
        color: #fff;
        padding: 5px 12px;
        display: inline-block;
        font-family: opensans-bold-webfont;
      }
      
      
}
.check_out_tabs > li.active > a, .check_out_tabs > li.active > a:focus {
    color: #555555;
    background-color: transparent;
    border: 0;
    border-bottom-color: transparent;
    cursor: default;
}
.check_out_tabs.nav-tabs > li.active > a, .check_out_tabs.nav-tabs > li.active > a:hover, .check_out_tabs.nav-tabs > li.active > a:focus, 
.check_out_tabs.nav-tabs > li > a {
  border: 0;  
}
.check_out_tabs {
      
    li {
      font-family: opensans-bold-webfont;
  
        a {
          color: #fff;
        } 
      }
      li.active {
        
        a {
          color: $select-tab;
          
        }
      }
}
.check_out_tabs.nav-tabs.nav-justified {
 // border-bottom: 1px solid $heading-color;
   background:  #BBB0AE;
}

.check_out_tabs.nav-tabs > li.active > a, .check_out_tabs.nav-tabs > li:hover a, .check_out_tabs.nav-tabs > li.active:hover a {
  //border-bottom: 1px solid #222 !important;
  padding: 10px 15px 11px 15px;
  color: #222 !important;
  background-color:transparent;  
}
      
.common_table {
    
    
  tr {
    th {
        background-color: $sub-heading-color;/*lighten ($nav-bg, 50%);*/
        color: #fff;
        padding:10px;
        font-family: opensans-regular-webfont;
        text-transform: uppercase;
        text-align: left; 
    }
    
    td {
      padding:10px;
      border-bottom: 1px solid  $sub-heading-color;/*lighten ($nav-bg, 50%);*/
      
      strong {
        font-family: opensans-regular-webfont;
        color: #000;
      }
    }
  }
}
.pgheading {
         //color: #222;
        font-size: $page-heading;
         font-weight: 800;
         text-align: center;
         margin-top: 20px;
           font-family: opensans-bold-webfont;
}

/********************************************************** VSS **************************/
.thumbnail {
    background-color: #FFF;
    display: block;
    line-height: 1.42857;
    margin-bottom: 20px;
    padding: 0px;
    transition: border 0.2s ease-in-out 0s;
    width: 255px;
  	display: inline-block; 
  	border:1px solid #f1f1f1;
  	border-radius:0;
      
      .caption
        {
          color:#666;
          h4{
              font-family: opensans-regular-webfont;
          }
          
          .menuDetails {
    margin-bottom: 15px;
    overflow: hidden;
    input 
    {
        display: inline-block;
        width: 85%;
      }
.chooseMeal.text-center {
    border: 1px solid #ffffff;
    margin-left: 7px;
    padding: 8px;
    width: 95%;
    
}
      }
      
        }
        
        a
        {
          color:#fff;
        }
      
}




.chooseMeal.text-center {
    border: 1px solid #a9b1bc;
    margin-left: 7px;
    
    width: 95%;
    background-color: #fff;
    font-weight: bold;
    a {
    		color: $orange;
    		font-family: opensans-regular-webfont;
    		padding: 8px;
    		 width: 100%;
    		 display: block;
    i {
      font-size:20px;
      padding-left:6px;
    }
    }
}

.nav-tabs {
    border-top: 1px solid #dddddd;
    border-bottom: none;
    li.active a
    {
     
      border:none !important;
      
    }
    
}



.tabNav{
  border-top: 1px solid #A9B1BC;
   /*margin: 5px 0 20px;*/
  margin: 10px 0 20px;

.btn {
  border-top:none;
   border-radius: 0px;
   background: #bbb0ae;
   font-weight: bold;
    border-color: none;
   color: #ffffff;
   padding: 2px 12px;
   font-size: 16px;
 
}

.common-btn-theme-light{
  padding: 5px 12px;
  background: $orange; 
  color:#fff;
  font-family: opensans-bold-webfont;
  
  &:hover,&:focus,&:active{
    background: lighten($orange, 25%); 
    color:#fff;
  }
}

.btn-primary {
    background-color: #8FC234 ;
    color: #ffffff;
    border-color: #8FC234 !important;
   }

}

 .navLinks
 {
   text-align:center;
   
   .btn-link {
    border-radius: 0;
    color: #282425;
    font-weight: bold;
 }
 .btn-link:hover, .btn-link:focus, .btn-link:active {
    color: $theme-color important;
 }
span {
    color: $theme-color;
    font-weight:bold;
  }
}  

/**********************************************asif *************************************/
.clear{
  clear:both;
}
.font16{
  font-size:16px;
}
.ob{
  font-family:'opensans-bold-webfont', sans-serif;
}
.mt10{
  margin-top:10px;
}
.mb10{
  margin-bottom:10px;
}
.mb15{
  margin-bottom:15px;
}
.mb20{
  margin-bottom:20px;
}
.mb30{
  margin-bottom:30px;
}
.mb0 {
	margin-bottom: 0;
}
hr {
    border-color: #afafaf;
}
.menu-sub-head{
    color: $text-color;
    border-bottom: 1px solid $text-color;
}
.common-theme-anchor{
  color: #666;
  border-bottom: 1px solid $menuBg-color; 
  &:hover,&:focus,&:active{
    color:$select-tab;
  }
}
.menu-tabs-parent{  
  .my-tabs-menu{
    
    display: inline-block;
    border:0;
    margin-top: 15px;
    li{
      a{
          transition: all 0.5s linear 0s;
          background-color: $logo-color;
           border-radius: 0;
          color: #FFF;
          border:0;
          font-family:'opensans-bold-webfont', sans-serif;
        
          &:hover{
            border:0;
            color: #fff;
           background-color: $zoro-balance;
          }
      }                                          
      &:before{
        /*content:'|';*/
        position:absolute;
        top: 8px;
          right: 0;
      }
      &:last-of-type:before{
        content:''; 
      }
    }   
    li.active{
      a{
          background-color: #8cc152;
          color: #FFF;
          &:hover{
            color: #fff;
          }
      }
    }
  }
}

.radio-parent {
  position: relative;
  float:left; 
  padding-right:5px;
}
.jplist-panel .jplist-group input[type="radio"] {
  height: 100%;
  position: absolute;
  width: 100%;
  left: 0;
  opacity: 0;
  cursor:pointer; 
}
.common_grey_btn.active{
  background:$logo-color;
  color:#fff;
}.common_grey_btn:hover{
  background: $zoro-balance;
    color:#fff;
}
.jplist-panel button {
  margin: 0;
}
.jplist-panel .jplist-group{
  margin: 0;
    border: 0;
    padding:0; 
}
.time{
    margin-top: 7px;
    color:$heading-color;
}
.common_grey_btn{
    background:$logo-color;
    color:#fff;
  }
.common_grey_btn.btn{
      font-weight: normal;
       box-shadow: none;
    border: 0;  
  &:focus{
    outline:none; 
  }
  @include transition(all,0.5s, linear);
  
}
.no-results{
  display:none;
}
.rupee-label{
  border:1px solid #a9b1bc;
  padding:5px 7px;
}
.ruppes-icon{
  font-size: 20px;
    vertical-align: middle;
    padding:0;
    margin-right: 5px;
}

.menuDetails .qty span{
  margin-right:5px;
  font-family: opensans-regular-webfont;
}

.select-parent{
  position:relative; 
  display:inline-block;
  span.customSelect {     
      border:1px solid #a9b1bc;    
      padding:0 9px;    
      width:65px;
      z-index:1;
      cursor:pointer;
      background:transparent;  
      cursor: pointer;
  }
  select{
    color:$heading-color;
    border: 1px solid #a9b1bc;
  }
  .arrow{
      position: absolute;
      background-color: #a9b1bc;
      color: #fff;
      padding:12px 8px;
      top: 0;
      height: 100%;
      right: 0;
       
  }
}
.common-btn{
  @include border-radius(0);
  @include transition(all,0.5s, linear);
  
}
.common-btn-theme-light{
  background: $logo-color; 
  color:#fff;
  font-family: opensans-bold-webfont;
  &:hover,&:focus,&:active{
    background: lighten($logo-color, 15%) ; 
    color:#fff;
  }
}
.pull-left  .common-btn-theme-light {
//	background-color: $theme-color;
}
.common-btn-theme-dark{
  background: $logo-color; 
  color:#fff;
  &:hover,&:focus,&:active{
    background: $zoro-balance; 
    color:#fff;
  }
}
.show-btn{
  background: #FFD3B6/*lighten($nav-bg, 30%)*/;
  color: $text-color;
}
.color_white{
  color:#fff; 
}
.show-btn:hover{
  background:/*$nav-bg*/$zoro-balance;
  @extend .color_white;
}
.list-group{
  margin-bottom:0;
}
.common-form .form-control{
  @include border-radius(0);
  @include box-shadow(0, 0px, 0px, rgba(0, 0, 0, 0));
  border: 1px solid #ccc;
    height: 40px;
}
.bg-white-form .form-control{
  border:1px solid $sub-heading-color;
}
.customSelectInner{
    line-height: 34px;
}
/*---------------------------Sagar Monday-----------------------*/
.loginPage {
    .check_out_form {
      
      .form-group {
          margin: 15px 0;
          color: #000;
          padding: 10px 0;
          font-family: opensans-light-webfont;
          font-size: $heading-font-size;
        
          
        
        
        label {
          text-transform: uppercase;
          margin: 0;
            padding: 0;
            font-size: $heading-font-size;
            color: $text-color;
        }
        input {
          text-align: left;
        }
        }
      
    }
}
.tab-pane {
  
  .tab_text {
    color: #000;
    padding: 10px 0;
    font-family: opensans-light-webfont;
    font-size: $heading-font-size;
    font-weight: bold;
    
  }
}
.ml0 {
  padding-left: 0 !important;
}
.ml15 {
  margin-left: 15px !important;
}
.mt20 {
  padding-top: 20px !important;
} 
.check_out_select .selectContainer span.customSelect {
  text-align: left;
}
.table-responsive {
  width: 100%;
}
.select_textbox1 .dropbox {
   
}
header .accountLinks ul li a:hover,header .accountLinks ul li.active a {
  color: #222;
}
header .navbar-default .navbar-collapse .navbar-nav .active a, header .navbar-default .navbar-collapse .navbar-nav li a:hover,header .navbar-default .navbar-collapse .navbar-nav li.active a {
  border-bottom: 4px solid $theme-color;
}
.customizeDiv a img.img-responsive {
   margin: 0 auto;
}
.check_out_tabs.nav-tabs.nav-justified {
  //border-bottom: 1px solid $heading-color;
    border-top: 0;
}
/***************************Ruchita********************************************/

.custmized_meal_container{
  margin-bottom: 60px;
  position: relative;
   .common_table{
    tr{
      td{
        padding:8px;
        
        .form-group {
          margin-bottom:0;
        }
      }
      .menuList {
          width: 3%;
          
          .menuImg_cart {
            height: 130px;
            width: 175px;
          
          img{
            width: 100%;
            height:100%;
          }
        }
        
      }
      .cart_menuPrice {
          width: 8%;
          padding-left:12px;
      }
      .qty_dropdwn {
          width: 5%;
      }     
    }
  }
  .customized_calDiv {
      position: absolute;
      right: 21px;
      margin-top: 15px;
        
     input[type="text"]{
         float: left;
         text-align: center;
         width: auto;
         border-radius:4px 0 0 4px;
      }
      
     .btn.datearrow_left {        
        background: $theme-color;
        height: 33px;
        position: absolute;
        top: 0;
        width: 33px;
        left:0;
    } 
    .btn.datearrow_right {
      background: $theme-color;
        height: 33px;
        position: absolute;
        top: 0;
        width: 33px;
        right:0;
    } 
  }
}

.custSelect .select-parent{
  border: 1px solid #ccc;
}
.cart .shoppinCart table .date .calDiv i.fa-calendar {
  right: 10px;
  top: 8px;
  
}
.user {
  margin: 0 10px 10px 0;
 font-size: $common-font-size;
   
   .fa-user {
    color: #707074;
    font-size: 25px;
      padding: 0 0 0 5px;
   }
}
.addAmtContainer ul{
	padding-left:0;
	
	li{
	  div{
	    display:inline-block;
	  }
	}
}
footer .top_container {
	padding-bottom: 0px;
}


.wallet.balance {
    background-color: transparent;
    color: $zoro-balance;
}
.navbar-default {
	border-color: transparent;
  	border-radius: 0;
} 
.footer_links {
	
	a {
		color: #e1671f; /*$heading-color;*/
		font-family: 'opensans-bold-webfont', sans-serif;
		padding: 0 10px 0 0;
		&.active {
			color: $theme-color;
		}
	}
}
/****************************************** concept **********************************************/
.common-bg{
	background:#eff0f2; 
}
.common_inner{
	padding:30px 0;
}
.common_content{
	color:$text-color;
	font-size:18px;
	font-family:'opensans-regular-webfont', sans-serif;
}
.col-md-20{
	width:20%;
	float:left;
	padding-left:15px;
	padding-right:15px;
}
.box_bg1 , .below_box1{
	background:#519751;
}
.box_bg2 , .below_box2{
	background:#049072; 
}
.concept-box{
	.box-top{
		color:#fff;
		font-size:50px;
		font-family:'opensans-bold-webfont', sans-serif;
		position:relative; 
		.spoon-left{
			position:absolute;
			top:50%;
			color:#8ec331;
			top: 12px;
  			left: 15px;
			@include rotate(90);		
		}
		.spoon-right{
			left:inherit;
			right:14px;
			@include rotate(270);	
		}
	}
	.middle-box{
		padding:15px;
		@extend .common_content;
		color:lighten($text-color,25%);
		background:#ffffff; 
	}
}
.below_box{
	height:20px;
}
/****************************************** contact **********************************************/
.get-in-touch{
	background:$logo-color; 
}
.get-in-touch-heading{
	font-size:30px;
	color:#fff;
	border-bottom:2px solid $theme-color;
}
.get-in-touch-circle{
	height: 120px;
	line-height: 120px;
  	width: 120px;
  	border: 1px solid #ffffff;
  	font-size: 100px;
  	color: #fff;
  	text-align: center;
  	display:inline-block;
}
.get-in-touch-circle-env{
	font-size:60px;	
}
.below-circle{
	font-size:18px;
	font-family:'opensans-regular-webfont', sans-serif;
	color:#fff;
	
	a{
	  color:#fff;
	}
}
.disinbl{
	display:inline-block;
}
.common-form .form-control{
	//text-align:center; 
}
.common-form textarea.form-control{
	height:auto;
	text-align:center;
}

.common-form .form-control.contact-text{
	  height: 110px;
}
.captcha-img{
	background:#ffffff;
	height:40px;
	img{
		display:inline-block;
	} 
}
.common_anchor,.common_anchor:active,.common_anchor:focus{
	color:#8CC152;
}
.common_anchor:hover{
  color:darken(#8CC152, 10%);
}
.btn-info {
	border: 1px solid $logo-color;
	background-color: $logo-color;
}
.btn-info:hover, .btn-info:focus, .btn-info.focus, .btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
	background-color: lighten($logo-color, 15%);
	border: 1px solid lighten($logo-color, 15%);
}
.theme_color {
	color: #707074 /*$theme-color*/;
}
.common_table.sub_heading tr th {
	background-color: $theme-color;
}
.common-theme-anchor:hover {
	//color: lighten($theme-color, 15%);
	color:#222;
	border-bottom:1px solid #222;
}
/* Sticky footer styles
-------------------------------------------------- */
html {
  position: relative;
  min-height: 100%;
}
body {
  /* Margin bottom by footer height */
    margin-bottom: 60px;
}
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height:60px;
  background-color: #fff;
}
.footerContainer{
	background:#fff; 
}
.top_row {
	padding-top: 25px;
	border-bottom: 1px solid lighten($text-color, 25%);
}
.about-head {
	font-family: 'opensans-regular-webfont', sans-serif;
	font-size: 30px;
	color: $theme-color;
}
.para {
	font-size: 17px;
	color: $text-color;
	text-align:justify; 
}
.highlited {
	background-color: $logo-color;
	color: #fff;
	text-align: center;
	padding: 15px 10px;
	font-size: $page-heading;
	
	strong {
		font-family: 'opensans-regular-webfont', sans-serif;
		display: block;
		font-size: 25px;
	}
}

.foundar {
	text-align: center;
	
	img {
		padding: 10px 0;
	}
	strong {
		display: block;
		font-size: 20px;
		padding: 10px 0;
		
	}
	span {
		line-height: 20px;
	}
	a {
		color: $text-color;
		padding: 10px 0;
	}
}
.pt0 {
	padding-top: 0;
	padding-bottom: 25px;
}
.pb25 {
	padding-bottom: 25px;
}
.highlited.text-left {
	text-align: left;
	font-size: 17px;
}
.para strong {
	display: block;
}
.corporate {
	
	img {
		
	}
	strong {
		  text-align: center;
		  display: block;
		  background-color: $nav-bg;
		  color: #fff;
		  padding: 10px 0;
	}
}
.ptb15 {
	padding: 15px 0;
  width: 100%;
  overflow: hidden;
}
.color {
	color: $theme-color;
	font-family: 'opensans-bold-webfont', sans-serif;
}
.description_code ol {
	padding: 0 0 0 20px;
	 li {
		      margin-left: 0;
			  list-style-type: lower-hexadecimal;
			  font-size: 15px;
			  padding: 5px 0;
		}
}
.description_code {
	
	ul {
		padding: 0 0 0 0px;
		li {
			margin-left: 0;
			  
			  font-size: 15px;
			  padding: 5px 16px;
			    display: inline-block;
			a {
				color: $text-color;
				font-size: 15px;
			}
		}
	}
}
.about-subhead {
	color: $theme-color;
	display: block;
}
table.welcome_page {
    empty-cells: show;
    border: 1px solid $text-color;;
}
/********************* validation *******************************************/
.mt10{
	margin-top:10px;
}
.mt5{
  margin:5px;
}
.dn{
	display:none;
}
.form_tooltip .tooltip.top .tooltip-arrow {
	border-top-color: #b94a48;
}
.form_tooltip .tooltip-inner {
	background: #fee;
	color: #555555;
	border: 1px solid #b94a48;
	padding: 5px 15px;
	border-radius: 0;
}
.welcome_page td, table.welcome_page th {
    min-width: 2em;
    min-height: 2em;
    border: 1px solid $text-color;;
    padding: 5px 10px;
}
.loginPage {
  overflow: inherit;
}
.pr0{
	padding-right:0;
}
.checkbox {
	div{
	  float: left;
	}
	label{
		display: block;
		padding-left: 29px;
	}
}
.grey{
	background-color: transparent!important;
	color:$text-color!important;
}
.thumbnail-img-parent{
	height:155px;
	width:253px;
	 cursor: pointer;
	img{
		max-height:100%;
		width:100%;
		display:inline-block;
	}
}
.calDiv{
	input{
		background: transparent;
  		z-index: 2;
  		position: relative;
	}
	.fa{
		  z-index: 1;
	}
}
.list-item{
	text-align:center; 	
}
.mobile-select{
	position:relative; 
}
.mobile-select-fixed{
	position: fixed;
	top: 55px;
	z-index: 2;
  	width: 100%;
    background-color: #fff;
    left: 0;
    padding:10px 0;
}
 .dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
    background-color: #F07746 ;
    color: #fff;
    }
@media only screen and (max-width: 1500px) { 
  
  .addAmtContainer ul li div.user{
      display:block;
  }
  
}

@media only screen and (max-width: 1024px) { 
 /*.summary_container .editAddress .common-form:last-child, .summaryContainer .editAddress .common-form:last-child{
      padding:0 !important;  
    }
  .summaryContainer .editAddress .common-form:nth-child(2), .summary_container .editAddress .common-form:nth-child(2){
    padding:0 !important;
  } */ 
  .summaryContainer .menuinfo_plan .planContainer .form-group select, .summary_container .menuinfo_plan .planContainer .form-group select{
    width: 60%;
  }
  .summaryContainer .menuinfo_plan .planContainer .calDiv input.Datepickersingle, .summary_container .menuinfo_plan .planContainer .calDiv input.Datepickersingle{
    width: 60%;
  }
  .summaryContainer .widget-body .widget-main.no-padding table tr td:last-child, .widget-box .widget-body .widget-main.no-padding table tr td:last-child{
    width:50%;
  }
  .wallet_widget ul li {
      margin-bottom: 35px;
  }
}

@media (min-width: 768px) and (max-width: 992px) {  
  
  /*.summaryContainer .editAddress .common-form:nth-child(2), .summary_container .editAddress .common-form:last-child{
    padding:0;  
  }*/
  .summaryContainer .widget-body .widget-main.no-padding table tr td:last-child, .widget-box .widget-body .widget-main.no-padding table tr td:last-child{
    width:60%;
  }
  .wallet_widget ul {
    padding: 0 0 0 0;
    margin-top:15px;
  }
  
  header .accountLinks ul li {
    display: inline-block;
    margin: 16px 14px 0 0;
  }
  
  .wallet.balance{margin: 0 0 10px;}
  
  li.pre-order a {
     padding-bottom: 31px !important;
  }
  li.pre-order.active a {
    padding-bottom: 29px !important;
  }     
  .col-sm-50{
  	width:50%;
  } 
  header .navbar-default .navbar-collapse{
  	padding-left:0;
  	padding-right:0;
  }
  header .navbar-default .navbar-collapse .navbar-nav li a{
  	font-size:13px; 
  }  
  .bookingContainer .historyTab .nav li a {
    padding: 11px 15px;
  }
  li.pre-order a {
    padding-bottom: /*30px*/34px !important;
  }
  .mobile-select-fixed{
  	    top: 0;
  } 
}
@media only screen and (max-width: 767px) { 
  
  header {
    background-color: #fff;
    padding: 50px 0 0!important;
  }
  .summary_container table.table td.menuList .menuImg_cart{
    height: 150px;
     width: 200px;
  }
.summary_container .table-responsive{
      overflow-x: auto !important;
   } 
   
  /*.summaryContainer .editAddress .common-form:nth-child(2), .summary_container .editAddress .common-form:last-child{
    padding:0;  
  }*/
   
	.pr0{
		padding-right:15px;
	}
  .footerContainer{
    .right{
      float:none;
    }
    text-align: center;
  }
  .radio-parent{
    width:33.33%;
    margin-bottom:5px;
    padding-right:0;
    .btn{
      display:block;
      width:100%;
    }
  }
   .col-xs-100{
  	width:100%;
  }    
  .mb20-xs{
  	margin-bottom:20px;
  }
  header .navbar-default .navbar-collapse {
      background-color: rgba(0, 0, 0, 0.8);
      padding: 0;
      position: absolute;
      text-align: center;
      width: 100%;
      z-index: 99999;
      left: 15px;
  }
  form.check_out_form .form-group div {
    padding-left: 0;
  }
  header .navbar-default .navbar-collapse .navbar-nav .active a, header .navbar-default .navbar-collapse .navbar-nav li a:hover, header .navbar-default .navbar-collapse .navbar-nav li.active a {
   
    color: $theme-color;
    padding-bottom:10px;
    
  }
  .corporate .col-sm-4 {
  	padding-bottom: 15px;
  }
  .ptb15 {
  	display: none;
  }
  
  header {
    padding: 0px 0 0;
}
  
  /*************Ruchita*****************/
  .customized_caldate{
    color: #707074;
      margin-bottom: 15px;
      margin-top: 68px;
  }
  .custmized_meal_container .common_table tr .menuList .menuImg_cart {
    width: auto;
    height: auto;
  }
  .custmized_meal_container .common_table tr .menuList {
    width: 22%;
    padding-right: 5px;
  }
  .modal-lg {
      width: 93%;
  }
  footer .top_container{
 	padding: 15px;
  }
}


@media only screen and (max-width: 640px) { 

.cart .shoppinCart table .menuList .menuImg_cart {
    display: inline-block;
    height: 75px;
    margin: 20px 15px;
    position: relative;
    width: 90px;
}
.cart .shoppinCart table label {
    border: 1px solid #ccc;
    color: #707074;
    margin: 0 7px;
    padding: 6px 13px;
}
.user{color:white;}
.wallet_widget ul {
    padding: 0 0 0 0;
    margin-top:15px;
  }
  .summary_container .menuinfo_plan .menuSummary .qtyContainer{
    float:none;
  }
  .summary_container .promocodeContainer .updateCart_div input[type="text"] {
      float: left;
      height: 35px;
      margin-bottom: 10px;
      margin-right: 20px;
      width: 60%;
  }
  .summaryContainer .menuinfo_plan .planContainer .form-group select, .summary_container .menuinfo_plan .planContainer .form-group select {
    display: block;}
}

@media only screen and (max-width: 480px) { 

  .cart .shoppinCart table .date .calDiv input.Datepicker{
    width: 170px;
  }
  .cart .shoppinCart table .date .calDiv{
     margin-right: 20px;
  }
  .cart .shoppinCart table .cart_close .close {
      margin: 0 20px;
  }
  .cart .shoppinCart .totalAmtCart{
    width:100%;
  }
  .summary_container table.table td.menuList .menuImg_cart {
    height: 115px;
    width: 140px;
  }
  .summary_container .menuinfo_plan .menuSummary .qtyContainer{
    float: none;
  }
  .wallet_widget ul {
    padding: 0 0 0 0;
    margin-top:15px;
  }
  .tabNav{border-top:none;}
  
  .summary_container .promocodeContainer .updateCart_div input[type="text"]{
    height: 35px;
    float: left;
    margin: 0 20px 0 0;
    width: 50%;
  }
  .summary_container .promocodeContainer .updateCart_div .btn-info{
    float: left;
  }
}

@media only screen and (max-width: 500px) { 
  
 .summary_container .promocodeContainer .updateCart_div input[type="text"] {
    margin: 0 10px 10px 0;
    width: 58%;
  }
  .summaryContainer .widget-body .widget-main.no-padding table tr td:first-child, .widget-box .widget-body .widget-main.no-padding table tr td:first-child i {
    display: none;
  }
  .summaryContainer .widget-body .widget-main.no-padding table tr td:first-child, .widget-box .widget-body .widget-main.no-padding table tr td i {
     display: block;
     margin: 0 0 10px;
  }
  .summary_container .totalAmt {
    font-size: 25px;
  } 
 .jplist-panel .radio-parent {
      margin-bottom: 5px;
      margin-left: 2px;
      padding-right: 0;
      width: 32.5%;
  }
  .jplist-panel .radio-parent:nth-child(4){
    margin-left:55px;
  }
}
@media only screen and (max-width: 320px) { 
  .summary_container .promocodeContainer .updateCart_div input[type="text"] {
    margin: 0 10px 10px 0;
    width: 65%;
  }
  .summary_container h1.totalAmt {
    font-size: 22px;
  } 
  .nav > li > a {
      display: block;
      padding: 10px 7px;
      position: relative;
  }
}

header .navbar-default .navbar-collapse .navbar-nav li a .wallet_xs {
  font-weight: normal;
}
.custom_model {
	text-align: center;
}
.pree_thum li a img {

				width: 250px;
  				border: 2px solid #656D78;
			
}
.description_code ul#video li {
	display: block;
}


.icheckbox_flat-blue{
  border: 1px solid #ccc;
}
.icheckbox_flat-blue.checked{  background-position: -23px 0; border: none;}
.addamt form i {
    font-size: 20px;
    margin: 0 5px 0 0;
}
.addamt form {
    overflow: hidden;
}

input[type="text"]:focus, input[type="password"]:focus, input[type="date"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="time"]:focus, input[type="url"]:focus, textarea:focus {
    box-shadow: none;
}
.ui-datepicker-inline {
    border-color: #bdc3d1;
    max-width: 280px;
}
.ui-datepicker, .ui-widget-content {
    background-color: #fff;
    border: 1px solid #9fa8bc;
    border-radius: 2px;
    font-family: inherit;
    font-size: inherit;
    margin: 1px 0 0;
    padding: 10px;
    width: auto !important;
}
.ui-datepicker .ui-datepicker-header {
    background: none repeat scroll 0 0 rgba(0, 0, 0, 0);
    border: 0 none;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    color: #696c74;
    font-weight: 700;
    letter-spacing: 1px;
    padding: 0 0 5px;
    text-transform: uppercase;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
    color: #dee1e7;
    font-size: 11px;
    top: 6px;
}
.ui-datepicker .ui-datepicker-header .ui-state-hover.ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-header .ui-state-hover.ui-datepicker-prev-hover{
  top: 6px;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev::before {
    left: 0;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next, .ui-datepicker .ui-datepicker-header .ui-datepicker-next::before {
    right: 0;
}
.ui-datepicker .ui-datepicker-title {
    color: #d3cac1;
    font-size: 14px;
}
.ui-datepicker .ui-datepicker-title .ui-datepicker-month, .ui-datepicker .ui-datepicker-title .ui-datepicker-year {
    color: #d3cac1;
}
.ui-datepicker .ui-datepicker-title {
    color: #d3cac1;
}
.ui-datepicker .ui-datepicker-calendar {
    background-color: transparent;
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
    margin: 0;
}
.ui-datepicker .ui-datepicker-calendar thead {
    background: #eee;
}
.ui-datepicker .ui-datepicker-calendar th {
    color: #82858e;
    font-size: 11px;
    font-weight: 700;
    letter-spacing: 1px;
    padding: 6px 10px;
    text-transform: uppercase;
}
.ui-datepicker .ui-datepicker-calendar td {
    background-color: #fcfcfd;
    border: 1px solid #fff;
    padding: 0;
}
.ui-datepicker .ui-datepicker-calendar td a:hover {
    background-color: #d8dce3;
    color: #696c74;
}
.ui-datepicker .ui-datepicker-calendar td span, .ui-datepicker .ui-datepicker-calendar td a {
    background-color: #f6f7f8;
    font-family: opensans-bold-webfont,sans-serif;
    border: 0 none;
    border-radius: 1px;
    color: #696c74;
    display: block;
    font-size: 12px;
    font-weight: 400;
    padding: 6px 10px;
    transition: all 0.2s ease-out 0s;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev::before {
    content: "";
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next::before {
    content: "";
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next::before, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev::before {
    font-family: "FontAwesome";
    position: absolute;
    top: 2px;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev-hover {
    background-color: transparent;
    border: 0 none;
    color: #c0c7d2;
    cursor: pointer;
    top: 1px;
}
.ui-datepicker {
    background-color: #fff;
    border: 1px solid #9fa8bc;
    border-radius: 2px;
    font-family: inherit;
    font-size: inherit;
    margin: 1px 0 0;
    padding: 10px;
    width: auto !important;
}
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a{
  border: 1px solid #8cc152/*e1671f*/ !important;
}

table.ui-datepicker-calendar tr.odd, table.ui-datepicker-calendar tr.alt, table.ui-datepicker-calendar tr:nth-of-type(2n+1) {
    background: none repeat scroll 0 0 transparent;
}

.ui-widget-content{
  background-image:none !important;
}
.ui-datepicker .ui-datepicker-calendar td a:hover{
  background:#d8dce3;
}
.cart_mob {
  color: #222;
  font-weight: 600;
  display: inline-block;
  font: 14px opensans-regular-webfont,sans-serif;
  text-align: center;
  margin-top: 7px;
   
  i {
    display: block;
  }
}
.cart_mob_parent{
  display:inline-block;
}
.shopping-cart{
 // float:left;
}


.container .text-muted {
  margin: 20px 0;
}
#order-wrap {
  width: 100%;
  position: relative;
  margin: 10px 0 0 0;
}
.scroll-design h3 {
  color: inherit;
  font-size: 17px;
  text-transform: uppercase;
  padding: 10px;
  color: #fff;
}
.scroll-design h3 {
  background: #bbb0ae;
  color: #fff;
  margin: 0;
  padding: 10px;
}
.main-category-list, .menu-block-wrapper {
  	background: none repeat scroll 0% 0% rgba(255,255,255, 0.9) !important;
 }
nav ul {
	list-style-type: none;
	margin: 0;
	padding: 0 0 15px 0;
	background: none repeat scroll 0% 0% rgba(255, 255, 255, 0.9) !important;
	
	li {
	padding: 0px 10px;
	  display: block;
	  
	a {
		color: #707074;
		padding: 10px 0;
		display: block;
		border-bottom: 1px solid #fff;
  		font-size: 16px;
	}
	a:focus, a:hover {
		outline: none;
		text-decoration: none;
		color: #fc6e51;
	}
	a.active {
		border-bottom: 1px solid #707074;
		color: #fc6e51;
  		
	}
	}
}
.category-block-heading h3 {
	  margin: 0;
	  padding: 0;
	  background-color: #bbb0ae;
	  color: #fff;
	  font-size: 17px;
	  padding: 10px;
	  text-transform: uppercase;
}
.menu-block-wrapper h4 {
	  font-weight: 700;
	  border-bottom: 1px solid rgba(0, 0, 0, 0.6);
	  /*padding: 20px 10px 5px 10px;*/
	  padding: 13px 10px 11px 10px;
	  font-size: 16px;
	  margin: 0;
}
.menu-block-wrapper {
	
	ul {
		  margin: 0;
		  overflow: hidden;
		  padding: 0;
		  
		li {
			  margin: 0;
			  padding: 0px 15px;
			  font-size: 14px;
			  display: block;
			  overflow: hidden;
			
			div {
				padding: 5px 0 0 0;
				  line-height: 33px;
				select {
					  background: white;
					  border: 1px solid #a4a4a4;
					  margin-right: 15px;
					  margin-bottom: 8px;
					  padding: 4px 5px 3px;
  
				}
			}
			a {
				  color: #707074;
				  font-size: 20px;
			}
		}
		
		li:nth-child(odd) {background: #F1F1F1}
		li:nth-child(even) {background: #F9F9F9}
	}
}
.backtpproject {
	    float: right;
		  color: #eb4c4b;
		  margin: 30px 0 0 0;
		  position: absolute;
		  right: 19px;
		  /*top: -10px;*/
		  top: -10px;
		  font-size: 17px;
		  /* border: 1px solid #FC6E51; */
		  padding: 0;
	  
	  &:hover {
	  	color: #5b9121;
	  }
}

.home-page-select {
 	
 	.form-group {
 		
 		.bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
 			width: 100%;
 			
 			.btn-default {
 				  border-radius: 0;
				  text-align: center;
				  /*padding: 10px 10px;*/
				 padding: 9px 10px;
				  
				  &:hover {
 					  color: #333;
					  background-color: #fff;
					  border-color: #ccc;
 				}
 			}
 			.open {
 				
 				.btn-default:hover {
 					  color: #333;
					  background-color: #fff;
					  border-color: #ccc;
 				}
 			}  
 			.dropdown-menu {
 				margin: 0 0 0 0;	
 			}
 		}	
 	}
 	.bootstrap-select.btn-group .dropdown-toggle .filter-option {
		  display: inline-block;
		  overflow: hidden;
		  width: 100%;
		  text-align: center;
		  text-transform: uppercase;
		  color: #707074;
	}
	
	.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
		background-color: transparent;
		color: #707074;
	}
	.btn-group.open .dropdown-toggle {
  		box-shadow: none;
	}
	.btn-default:hover, .btn-default:focus, .btn-default.focus, .btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
		color: #333;
					  background-color: #fff;
					  border-color: #ccc;
	}
 }











@media screen  and (max-width: 500px) { 
	.backtpproject {
	    
		  top: -20px;
		}
}
.radio, .checkbox {
	width: 100%;
}
.dinnerAdd {
	 overflow: hidden;
  	 width: 100%;	
}
.menu_name {
	  font-family: opensans-bold-webfont;
	  text-transform: uppercase;
	  font-size: 17px;
}
.fa-credit-card {
	  padding-right: 5px;
}
.add_amount_para p {
	  /*text-align: center;
  padding: 10px 0 30px 0;*/
}
.terms_conditions {
	  font-size: 15px;
}
.home-modal{
  .modal-header{
    border:0;
    padding-bottom:0;
  }
  .modal-content{
    padding:0;
     background-color: #eeeeee;
     
     .loginPage {
     	
     	.selectContainer {
     		border: 0;
     	}
     } 
  }
  .modal-footer{
    border:0;
    padding-top: 0;
  }
} 
.termspopup {
	
	p {
		  line-height: 25px;
	  	  margin-bottom: 15px;
	  	  text-rendering: optimizeLegibility;
	}
}
.grid_list_view {
	  text-align: center;
	  a {
	  	    color: $logo-color;
			  font-size: 20px;
			  padding: 0 5px;
			  margin: 5px 0 0 0;
			  display: inline-block;
	  }
}
.home-modal .close {
	display: none;
}


@media screen and (min-device-width: 768px) and (max-device-width: 1000px) {
	.list_view {
	
	.thumbnail {
		width: 100%;
		
		.thumbnail-img-parent {
			height: auto;
			float: left;
			 width: 30%;
			
			img {
				max-width: 100%;
			}
		}
		.caption {
			float: left;
			width: 70%;
			margin: 2% 0 0 0;
			  
			  h4 {
			  	float: left;
			  	
			  }
			  hr {
			  	display: none;
			  }
			  .menuDetails {
			  	float: left;
			  	margin: 5px 0 0 20px;
			  	
			  	
			  }
			  .chooseMeal.text-center {
			  	border: 0 none;
			    clear: both;
			    margin: 0;
			    width: 100%;
			  	
			  	a {
			  		 padding: 0;
				     text-align: left;
			  	}
			  }
		} 
	}
}    
}
@media screen and (min-device-width: 1000px) and (max-device-width: 1920px) { 
   .list_view {
	
	.thumbnail {
		width: 100%;
		
		.thumbnail-img-parent {         
			height: auto;
			float: left;
			 width: 10%;
			
			img {
				max-width: 100%;
			}
		}
		.caption {
			float: left;
			width: 90%;
			margin: 0 0 0 0;
			
			  h4 {
			  	float: left;
			  	width: 33%;
			  }
			  hr {
			  	display: none;
			  }
			  .menuDetails {
			  	display: inline-block;
			  	/*float:right;*/
			  	margin: 5px 0 0 20px;
			  	
			  	
			  }
			  .chooseMeal.text-center {
			  	border: 0;
			  	float: right;
    			width: auto;
			  	
			  	a {
			  		text-align: right;
			  	}
			  }
		} 
	}
}
}
 /**********************Pallavi*******************************/
span.error, small.error {
    color: #f04124;
    display: block;
    font-weight: normal;
    margin-bottom: 0.5rem;
    margin-top: 0;
    padding: 0.2rem;
}

.return-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
   background:$logo-color;
    width: 50px;
    height: 50px;
    display: block;
    text-decoration: none;
    display: none;
    text-align: center;
   
}
.return-to-top i {
    color: #fff;
    padding: 10px;
    font-size: 25px;
    text-align: center;
    
}


.opt_textbox{
	border: 1px solid #ddd;
    border-radius: 0;
    display: inline;
    line-height: 22px;
    vertical-align: middle;
    width: 110px;
}
.caret{
	height: 40px;
	width: 40px;
	border: none;
	/*background: #A9B1BC;*/
	background-image: url(../img/combo.png);
	
}
.bootstrap-select.btn-group .dropdown-toggle .caret {
	right: 0;
	top:5%;
	/*content: "\f003";
    font-family: font-awesome;*/
}
a:focus, button:focus, input:focus, select:focus, .bootstrap-select .dropdown-toggle:focus { 
 
  outline: 0 !important;
}

@media screen and (min-width: 320px) and (max-width: 757px) { 
  .menu-block-wrapper ul li div select{
    margin:0 0px 0 5px !important;
    padding:0;
  }
  .menu-block-wrapper ul li {
        padding: 0px;
  }
  .menu-block-wrapper ul li div{
    line-height:21px;
    padding:7px 0;
  }
}
@media screen and (min-width: 1025px) and (max-width: 1280px) { 
     header .accountLinks ul{   
          margin-top: 0px;
        }
     
}
@media screen and (min-width: 800px) and (max-width: 1300px) { 
     
     .accountLinks ul{       
     
          li{
         
            &:last-child{
              margin: 9px 0 0 0 !important;
            }
          }
       }
}

@media screen and (min-width: 981px) and (max-width: 1280px) { 
     .wallet.balance {
    	margin-bottom: 5px;
    	
	}
}
@media screen and (min-width: 980px) and (max-width: 1075px) { 
     header .accountLinks ul{   
          margin-top: 6px;
        }
}
@media screen and (min-width: 801px) and (max-width: 980px) { 
     header .accountLinks ul{   
          margin-top: -4px;
        }
    .logoContainer img {
          margin-top: 24px;
        }
}
@media screen and (min-width: 757px) and (max-width: 800px) { 
     header .accountLinks ul{   
          margin-top: -11px;
        }
     .logoContainer img {
          margin-top: 21px;
        }
}
@media screen and (min-width: 320px) and (max-width: 700px){
	.wallet.balance {
   
    color: #FFFFFF;
    border: 1px solid #FFFFFF;
	}
  .user{
      color: #FFFFFF;
  } 
  .user .fa-user{
      color: #FFFFFF;
  }
  /*header .navbar-default .navbar-collapse .navbar-nav li a{
            &:last-child{
              border: none;
            }
  }*/
 header .navbar-default .navbar-collapse .navbar-nav .addAmt a{
 	 border: none !important;
 }
}
header .navbar-default .navbar-collapse .navbar-nav .active a, header .navbar-default .navbar-collapse .navbar-nav li a:hover, header .navbar-default .navbar-collapse .navbar-nav li.active a{
  color:#FFFFFF;
  border-bottom: 4px solid #969696;
  /*padding: 1px 0;*/
}
header .navbar-default .navbar-collapse .navbar-nav li a{
	color: #FFFFFF;
	
}
nav ul{
	background: transparent !important;
}
/*.dinner_menu*/#main{
	padding-top: 10px !important;
}

/**login**/
.remember_login{
	width: 50%;
}
@media screen and (min-width: 765px) and (max-width: 991px) { 
	.login_page{
		margin-left: -15px;
	}
}

.form-control.otp_txt {
    display: inline-block;
    width: 78%;
}
.form-group .otp_btn{
		float: right;
		margin-top: 4px;
	}
@media screen and (max-width: 700px) { 
	.form-control.otp_txt {
	    width: 50%;
	}
	
}
/**register**/
	.dinner_loc{
		padding-right: /*58px;*/63px;
	}
@media screen and (min-width: 300px) and (max-width: 1270px) { 
     .dinner_loc{
		padding-right: 0px;
	}
}
@media screen and (max-width: 360px) { 
     .snacks_loc{
		padding-right: 16px;
	}
	
}
@media screen and (min-width: 765px) and (max-width: 991px) { 
	.register_page{
		margin-left: -15px;
	}
}
@media (-o-min-device-pixel-ratio: 5/4), (-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 120dpi) {
    .icheckbox_flat-blue,
    .iradio_flat-blue {
    	 background: url(../img/icheck.png) no-repeat;
    }
 }

/**booking history**/
	.historyTab .table-responsive{
		overflow-x: visible !important;
	}
	
	.btnPopup{
		padding: 10px 0 20px 0;
	}
	.btnPopup .btnSave{
		padding-right: 10px;
	}
	.historyTab 
		{
			.ui-state-highlight a/*, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a*/{
  				border: 1px solid #e1671f/*8cc152*/ !important;
		}
		.ui-datepicker-inline {
   			max-width: 294px;
		}
	}
@media only screen and (max-width: 360px){
	.booking .bookingContainer .bookorderBtn button {
    	margin: 6px 0 20px;
	}
}
	.bookingContainer .ui-datepicker-calendar .ui-state-disabled span{
		border: none !important;
		
	}
	
	
/**My meal**/
		/*.summary_container table .ui-state-disabled span,   .ui-widget-content*/
	/*.summary_container .ui-datepicker-prev span{
		border: none !important;
	}*/
	 /*.summary_container */.ui-datepicker-calendar .ui-state-disabled span{
		border: 1px solid red !important;
		
	}
	
	.summary_container .menuinfo_plan .planContainer .datebased #withAltField > input#altField {
	    padding: 0 5px;
	    width: 44%;
	}
	.summary_container .ui-datepicker {
    	padding: 6px 4px;
   }
   
@media only screen and (min-width: 760px) and (max-width: 1200px){  
   .thirdparty{
   		float: right ;
   }
}
   
 /**Order Confirm**/
   .order_content, .btnOrder button{
   	text-align:center;
   }
   
   /**Add Amount**/
@media screen and (min-width: 1200px) and (max-width: 1920px) { 
     .btnaddAmt{
     	margin-right: 8px;
     }
}
@media screen and (min-width: 990px) and (max-width: 1200px) { 
     .btnaddAmt{
     	margin-right: 26px;
     }
}
@media screen and (min-width: 600px) and (max-width: 990px) { 
     .btnaddAmt{
     	margin-right: 16px;
     }
}
@media screen and (min-width: 300px) and (max-width: 600px) { 
     .btnaddAmt{
     	margin-right: 13px;
     }
}
   
  
   /**My Account**/
@media only screen and (max-width: 360px){
	/*.summaryContainer .widget-body .widget-main.no-padding table tr td:last-child i,*/ .widget-box .widget-body .widget-main.no-padding table tr td:last-child i{
		margin: 5px 5px 10px 10px;
	}
	
}
@media screen and (min-width: 768px) and (max-width: 990px) { 
	.balance_col{
		margin-left: 180px;
	}
}

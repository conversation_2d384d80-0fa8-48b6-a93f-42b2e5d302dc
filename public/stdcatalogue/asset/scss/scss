/*
Error: Invalid property: "Backtrace:" (no value).
        on line 4 of scss

1: /*
2: Errno::ENOENT: No such file or directory - scss
3: 
4: Backtrace:
5: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:484:in `read'
6: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:484:in `update_stylesheet'
7: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:215:in `block in update_stylesheets'
8: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:209:in `each'
9: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:209:in `update_stylesheets'

Backtrace:
scss:4
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:735:in `parse_property'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:692:in `parse_property_or_rule'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:658:in `parse_line'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:536:in `build_tree'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:555:in `block in append_children'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:554:in `each'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:554:in `append_children'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:409:in `_to_tree'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:381:in `_render_with_sourcemap'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/engine.rb:298:in `render_with_sourcemap'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:492:in `update_stylesheet'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:215:in `block in update_stylesheets'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:209:in `each'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:209:in `update_stylesheets'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:294:in `watch'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin.rb:109:in `method_missing'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/exec/sass_scss.rb:360:in `watch_or_update'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/exec/sass_scss.rb:51:in `process_result'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/exec/base.rb:52:in `parse'
/var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/exec/base.rb:19:in `parse!'
/var/lib/gems/1.9.1/gems/sass-3.4.23/bin/scss:13:in `<top (required)>'
/usr/local/bin/scss:23:in `load'
/usr/local/bin/scss:23:in `<main>'
*/
body:before {
  white-space: pre;
  font-family: monospace;
  content: "Error: Invalid property: \"Backtrace:\" (no value).\A         on line 4 of scss\A \A 1: /*\A 2: Errno::ENOENT: No such file or directory - scss\A 3: \A 4: Backtrace:\A 5: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:484:in `read'\A 6: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:484:in `update_stylesheet'\A 7: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:215:in `block in update_stylesheets'\A 8: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:209:in `each'\A 9: /var/lib/gems/1.9.1/gems/sass-3.4.23/lib/sass/plugin/compiler.rb:209:in `update_stylesheets'"; }

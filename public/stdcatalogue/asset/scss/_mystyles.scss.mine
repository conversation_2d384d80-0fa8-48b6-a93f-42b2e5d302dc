@mixin font-face($style-name, $file, $family, $category:"") {
    $filepath:  $family + "/" + $file;
    @font-face {
        font-family: "#{$style-name}";
        src: url($filepath + ".eot");
        src: url($filepath + ".eot?#iefix") format('embedded-opentype'), url($filepath + ".woff") format('woff'), url($filepath + ".ttf")  format('truetype'), url($filepath + ".svg#" + $style-name + "") format('svg');
    }
    %#{$style-name} {
        font: {
            @if $category != "" {
                family: "#{$style-name}", #{$category};
            }
            @else {
                family: "#{$style-name}";
                weight: normal;
            }
        }
    }
}
@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
     -moz-border-radius: $radius;
      -ms-border-radius: $radius;
          border-radius: $radius;
}
@mixin rotate( $degrees ) {
  -webkit-transform: rotate(#{$degrees}deg);
  -moz-transform: rotate(#{$degrees}deg);
  -ms-transform: rotate(#{$degrees}deg);
  -o-transform: rotate(#{$degrees}deg);
  transform: rotate(#{$degrees}deg);

  filter:  progid:DXImageTransform.Microsoft.Matrix(sizingMethod='auto expand', M11=#{cos($degrees)}, M12=-#{sin($degrees)}, M21=#{sin($degrees)}, M22=#{cos($degrees)});
  -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(sizingMethod='auto expand', M11=#{cos($degrees)}, M12=-#{sin($degrees)}, M21=#{sin($degrees)}, M22=#{cos($degrees)})";
  zoom: 1;
 }

@mixin transition($transition-property, $transition-time, $method) {
    -webkit-transition: $transition-property $transition-time $method;
       -moz-transition: $transition-property $transition-time $method;
        -ms-transition: $transition-property $transition-time $method;
         -o-transition: $transition-property $transition-time $method;
            transition: $transition-property $transition-time $method;
}
@mixin colors($colors,$name) {
  #{$name}_color{
        color:$colors;
    } 
  #{$name}_bg{
        background:$colors; 
  }
  #{$name}_border{
        border:1px solid darken($colors,10%); 
  }
}
@mixin box-shadow($top, $left, $blur, $color, $inset: false) {
  @if $inset {
    -webkit-box-shadow:inset $top $left $blur $color;
    -moz-box-shadow:inset $top $left $blur $color;
    box-shadow:inset $top $left $blur $color;
  } @else {
    -webkit-box-shadow: $top $left $blur $color;
    -moz-box-shadow: $top $left $blur $color;
    box-shadow: $top $left $blur $color;
  }
}
@mixin animate-dalay($time) {
  -webkit-animation-delay: $time;
     -moz-animation-delay: $time;
       -o-animation-delay: $time;
      -ms-animation-delay: $time;
}
@mixin backface-visibility($backface) {
  -webkit-backface-visibility: $backface;
     -moz-backface-visibility: $backface;
       -o-backface-visibility: $backface;
      -ms-backface-visibility: $backface;
}
@mixin transform-translate($translate) {
  -webkit-backface-visibility: $translate;
     -moz-backface-visibility: $translate;
       -o-backface-visibility: $translate;
      -ms-backface-visibility: $translate;
}
@mixin transition-duration($duration) {
  -webkit-transition-duration: $duration;
     -moz-transition-duration: $duration;
       -o-transition-duration: $duration;
      -ms-transition-duration: $duration;
}
@mixin opacity($opacity) {
  opacity: $opacity;
  $opacity-ie: $opacity * 100;
  filter: alpha(opacity=$opacity-ie); /*IE8*/
}
@include colors($primary-color,'.theme');

@include font-face('JosefinSans', 'JosefinSans','../fonts/');



a.wallet-edit-btn h4{
   font-size: 14px !important;
}
.ac-detail-box .lunch-editable .clearfix{
    margin: 0 5px 0 !important;
    padding: 10px 0px !important;
}

.main-wrapper{
	margin-left: 50px;
	margin-right: 50px;
	/*@include opacity(0.5);*/
	/*@include transition(all,0.5s, linear);*/
	/*@include border-radius(0);*/
}
body {
	font-family: JosefinSans;
	font-size: 14px;
	position: relative;
	color:  $text-color;
	position:relative;
}
body.bodybg {
  background-image: url("../images/background.png");
}
body.sticky-footer{
	padding-bottom: 60px;
}
.navbar-inverse .navbar-nav > li > a.call-us{
  cursor: text;
}
header{
	 background-color: $white-color;
	}
a:hover, a:focus {
    text-decoration: none;
}
a {
    color: $text-color;
    text-decoration: none;
}
b, strong {
    font-weight: bold;
    font-size: 16px;
}
label b{
    font-size: 14px;
}
.pt{
	padding-top: 10px;
}
.pb{
	padding-bottom: 10px;
}
.pl{
	padding-left: 10px;
}
.pr{
	padding-right: 10px;
}
.mt5{
  margin-top:5px;
}
.mt10{
	margin-top: 10px;
}
.mb{
	margin-bottom: 10px;
}
.ml{
	margin-left: 10px;
}
.mr{
	margin-right: 10px;
}
.pt-15{
  padding-top:15px;
}
.pt-5{
  padding-top:5px;
}
.pd-0{
  padding:0;
}
.pd-5{
  padding:5px;
}
.ptb-10{
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
.mb-0{
	margin-bottom:0 !important;
}
.wd-175{
  width: 175px !important;
}
.sub-head-mrtp{
  margin-top: 20px;
}
.affix {
	top: 0;
	width: 100%;
	z-index: 3 !important;	
}

.affix ~
.container-fluid {
	position: relative;
	top: 50px;
}
.container-fluid {
	margin-right: auto;
	margin-left: auto;
	padding-left: 50px;
	padding-right: 50px;
}
#icon-menu {
	display: table;
}
.ti-location-pin{
	color: $primary-color;
}
.navbar-inverse {
	border-radius: none;
	background-color: transparent;
	border: none;
	border-radius: none;
	border-bottom: 1px solid $header-border;
}
.navbar-brand {
    float: left;
    padding: 15px 15px;
    font-size: 18px;
    line-height: 20px;
    height: 50px;
    margin-top: 8px;
}
.navbar {
	position: relative;
	min-height: 60px;
	padding-top: 0px;
	margin-bottom: 0;
	border-radius: none !important;
}
.navbar-inverse .navbar-brand {
	color:  $secondary-color;
}
.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus, .navbar-inverse .navbar-brand:active {
	color:  $primary-color;
	transition: 0.5s;
}
.navbar-inverse .navbar-nav > li > a {
	color:  $text-color;
	line-height: 3em;
	font-size: 16px;
}
.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus, .navbar-inverse .navbar-nav > li > a:active {
	color:  $primary-color;
	transition: 0.5s;
}
.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
	border-color: #e8e8e8;
}
#shopping-cart .ti-shopping-cart{
	font-size: 25px;
	color: $primary-color;
}
#shopping-cart .badge, #show_cart .badge{
    display: inline-block;
    min-width: 10px;
    padding: 5px 8px;
    font-size: 12px;
    font-weight: bold;
    color: $white-color;
    line-height: 1;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    background-color: $cart-count-color;
    border-radius: 50%;
    top: -20px;
    position: relative;
    right: 32px;
}
#cart-count {
    font-size: 12px;
    background: #ff0000;
    color: #fff;
    padding: 3px 7px;
    vertical-align: top;
    border-radius: 50%;
}
.nav > li {
	position: relative;
	display: block;
	text-align: center;
}
.navbar-inverse .navbar-toggle {
	border: none;
	color:  $primary-color;
}
.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:active, .navbar-inverse .navbar-toggle:focus {
	border: none;
	color:  $primary-color;
	background-color: transparent;
}
.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
    background-color: transparent;
    color: $primary-color;
}
.dropdown-menu > li > a {
    display: block;
    padding: 5px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.42857;
    color: #333333;
    white-space: nowrap;
    font-size: 15px;
}
.ti-more{
	-ms-transform: rotate(90deg); /* IE 9 */
    -webkit-transform: rotate(90deg); /* Chrome, Safari, Opera */
    transform: rotate(90deg);
}
.sidenav {
	height: 100%;
	width: 0;
	position: fixed;
	z-index: 1;
	top: 0;
	left: 0; 
	background-color:  $side-panel-color;
	overflow-x: hidden;
	transition: 0.5s;
}

.sidenav a.list {
	padding: 25px 10px 25px 10px;
	text-decoration: none;
	font-size: 18px;
	color: #aab2bd;
	display: block;
	transition: 0.3s;
	border-bottom: 1px solid #434a54;
	text-align: center;
	width: 250px;
}

.sidenav a.closebtn {
	border-bottom: none;
	padding: 10px 10px 10px 10px;
	width: 0px;
	color:  $secondary-color;
}

.sidenav a:hover, .sidenav a:focus, .sidenav a:active {
	color:  $secondary-color-hover;
	text-decoration:  $text-dec;
}
#logo img {
	display: block;
	vertical-align: middle;
	top: 0px;
  max-height: 70px;
}
#logo{
    padding: 10px 10px;
}
.followbtn {
	color:  $secondary-color;
	border: 1px solid #aab2bd;
	margin: 10px 0 10px 0;
	padding: 5px 5px 0px 0px;
	position: relative;
	display: inline-block;
	width: 150px;
	text-align: left;
}
.followbtn:hover {
	color:  $secondary-color;
	text-decoration:  $text-dec;
}
footer.side-footer {
	position: absolute;
	bottom: 0;
	text-align: center;
	color:  $secondary-color;
	width: 250px;
	margin-bottom: 20px;
	background-color:  $side-panel-color;
}
footer.side-footer a span {
	font-size: 14px;
	width: 13em;
	word-wrap: break-word;
}
footer.side-footer a {
	line-height: 1em;
	font-size: 20px;
}
footer.side-footer {h4{
	color: $secondary-color;
	}
}

footer {
	background-color:  $footer-main;
	color:  $secondary-color;
}
footer a span {
	color:  $secondary-color;
	text-decoration:  $text-dec;
	font-size: 20px;
}
footer .col-sm-4 {
	padding: 20px;
	line-height: 2em;
}
.ti-facebook:hover {
	color:  $fb-hover;
	transition: 0.5s ease-in-out;
}
.ti-twitter-alt:hover {
	color:  $twitter-hover;
	transition: 0.5s ease-in-out;
}
.ti-google:hover {
	color:  $google-hover;
	transition: 0.5s ease-in-out;
}
.social-icon {
	word-spacing: 20px;
	text-align: center;
}
.fooddialer, .fooddialer:hover, .fooddialer:active, .fooddialer:focus {
	color: $primary-color;
	text-decoration:  $text-dec;
}
.copyright {
	text-align: right;
}
.bar1, .bar2, .bar3 {
	width: 25px;
	height: 2px;
	background-color: #656d78;
	margin: 6px 0;
	transition: 0.4s;
}

.change .bar1 {
	-webkit-transform: rotate(-48deg) translate(-4px, 6px);
	transform: rotate(-48deg) translate(-4px, 6px);
}

.change .bar2 {
	opacity: 0;
}

.change .bar3 {
	-webkit-transform: rotate(43deg) translate(-5px, -8px);
	transform: rotate(43deg) translate(-5px, -8px);
}
.btn-default {
    color: $text-color;
    background-color: $white-color;
    border-color: #dfdfdf;
}
.btn-default:hover, .btn-default:focus, .btn-default:active {
    color: $white-color;
    background-color: $primary-color;
    outline: none;
}
.bread-filter{
	margin-top: 10px;
}
.breadcrumb > li + li::before {
    padding: 0 5px;
    color: #ccc;
    content: "/\00a0";
}
.breadcrumb > .active {
    color: $primary-color;
}
section.bread-filter .back{
	text-align: right;
}
.active-filter {
    background-color: $primary-color;
    color: $white-color;
    transition: 1s ease-in-out;
}
.breadcrumb {
    padding: 0px 8px 8px 0px;
    margin-bottom: 0px;
    margin-bottom: 0px;
    list-style: none;
    background-color: transparent;
    border-radius: 0px;
}
.breadcrumb > li a{
    color: $text-color;
}
.breadcrumb > li a:hover,  .breadcrumb > li a:focus{
    color: $primary-color;
    text-decoration: $text-dec;
    transition-duration: 1s;
}
.fa {
	display: inline-block;
	padding: 0 0.5 rem 0 0;
	font-size: 20px;
}
footer.side-footer a span.ti-apple, footer.side-footer a span.ti-android{
	width:auto;
	font-size: 27px;
	float: left;
	vertical-align: middle;
	text-align: left;
	padding: 10px;
}
.ti-apple, .ti-android {
	font-size: 30px;
	float: left;
	vertical-align: middle;
	text-align: left;
	padding: 10px;
}
ul#myTabs span.ti-search {
	color:  $primary-color;
	line-height: 2.35em;
    font-weight: bold;
    font-size: 19px;
    cursor: pointer;
}
.sidenav .closebtn {
	position: absolute;
	top: 0;
	right: 25px;
	font-size: 36px;
	margin-left: 50px;
}
.visible-xs {
    text-align: center;
    padding: 5px;
    background-color: $primary-color;
}
.visible-xs a{
	color: $white-color;
	text-decoration: $text-dec;
}
.visible-xs ul.dropdown-menu{
	min-width: 100%;
	border-radius: 0px;
}
.visible-xs ul.dropdown-menu > li > a {
    clear: both;
    color: $text-color;
    padding-bottom: 5px;
    padding-top: 5px;
    text-align: center;
	font-size: 15px;
	cursor: pointer;
	text-transform: uppercase;
}
.visible-xs ul.dropdown-menu .location{
	background-color: $header-background-color;
}
.visible-xs ul.dropdown-menu li {
	border-bottom: 1px solid $header-background-color;
}
#main {
	transition: all .5s;
	padding: 0px;
}
.nav-tabs {
	border-bottom: none;
}
.nav-tabs > li {
	margin-right: 0px;
	line-height: 1.42857;
	border-right: 1px solid $secondary-color;
}
.nav-tabs > li:hover {
	border-radius: none;
}
.nav-tabs > li#serch-btn {
	border-right: none;
	cursor: pointer;
}
.nav-tabs > a#search{
	cursor: pointer;
}
.nav-tabs > li > a {
	margin-right: 0px;
	line-height: 1.42857;
	border: none;
	border-radius: 0 0 0 0;
	position: relative;
	display: block;
	padding: 15px 68px;
	right: 0;
	left: 0;
	cursor:pointer;
}
.nav-tabs > li.active > a, .nav-tabs > li > a:hover, .nav-tabs > li > a:focus {
	margin-right: 0px;
	line-height: 1.42857;
	border: none;
	border-radius: none;
	position: relative;
	display: block;
}
.nav-tabs > li {
    float: left;
    margin-bottom: 0px;
}
#location{
    color: $text-color;
    font-size: 16px;
}
#location:hover, #location:focus, #location:active{
    color: $primary-color;
}
ul#myTabs li:hover {
	margin-right: 0px;
	line-height: 1.42857;
}
ul#myTabs {
	text-transform: uppercase;
	margin-bottom: 10px;
}
ul#myTabs span {
	color:  $text-color;
	font-size: 14px;
    text-transform: capitalize;
}
ul#myTabs li a {
	background-color:  $header-background-color;
	color:  $text-color;
	border-top: 3px solid $header-background-color;
	font-size: 16px;
}
ul#myTabs li a:hover, ul#myTabs li a:active, ul#myTabs li a:focus {
	background-color:  $header-background-color;
	color:  $primary-color;
	border-top: 3px solid $primary-color;
	transition-duration: 1s;
}
ul#myTabs li a.active {
	background-color:  $header-background-color;
	color:  $primary-color;
	border-top: 3px solid $primary-color;
	transition-duration: 1s;
}
ul#myTabs li a.active:after {
	top: 0;
	left: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(213, 107, 26, 0);
	border-top-color: $primary-color;
	border-width: 8px;
	margin-left: -12px;
}
ul#myTabs li#serch-btn a:hover, ul#myTabs li#serch-btn a:active, ul#myTabs li#serch-btn a:focus {
	border-top: 3px solid $header-background-color;
}
.nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a, .nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:focus {
    border: 0;
}
.tab-content{
	margin-top:10px;
}
.dropdown-search .input-group-btn button{
	margin-left: 0;
	top: 4.2px;
	background-color: $primary-color;
	color: #fff;
	position: relative;
}
.nonveg-icon{
	padding: 5px 5px;
	border: 1px solid $nonveg;
	color: $nonveg;
	position: absolute;
	margin-top: 10px;
  margin-left: 10px;
	font-size: 12px;
	z-index: 2;
	background-color: $white-color;
}
.veg-icon{
	padding: 5px 5px;
	border: 1px solid $veg;
	color: $veg;
	position: absolute;
	margin-top: 10px;
  margin-left: 10px;
	font-size: 12px;
	z-index: 2;
	background-color: $white-color;
}
.veg-circle{
	background-color: $veg;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}
.nonveg-circle{
	background-color: $nonveg;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}
.overlay {
  transition: .5s ease;
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
}
.meal-bg{
	background-color: #000;
}
.swap-pos{
  position: relative;
}
.item-swap-ico{
    position: absolute;
    float: right;
    right: 0;
    padding: 5px;
    line-height: 0.5em;
    margin-top: 10px;
    margin-right: 10px;
    background: $primary-color;
    color: $white-color !important;
}
.item-swap-ico .ti-reload{
    color: $white-color !important;
}
.popover.bottom {
    margin-top: 10px;
    left: inherit !important;
    right: -13px!important;
}
.popover{
    top: 35px !important;
}
.popover.bottom > .arrow {
    right: 10% !important;
 }
.image-overlay {
	opacity: 0.5;
	color: #fff;
}

.meal-img:hover .overlay {
  opacity: 1;
}

.img-text {
  color: white;
  font-size: 18px;
  text-align: center;
}
.login-box{
	background-color: $header-background-color;
	width: 50%;
	margin-left: 25%;
	padding: 0px;
	text-align: center;
	margin-top: 100px;
	margin-bottom: 100px;
}
.control {
		font-size: 14px;
	}
.login-button{
	background-color: #CCD1D9;
	color: $white-color;
	padding: 15px;
	font-size: 16px;
}
.signin-button{
	background-color: #CCD1D9;
	color: $white-color;
	padding: 15px;
	font-size: 16px;
}
.login-button:hover, .login-button:focus, .login-button:active{
	text-decoration: $text-dec; 
	color: $white-color;
}
.signin-button:hover, .signin-button:focus, .signin-button:active{
	text-decoration: $text-dec; 
	color: $white-color;
}
.bg-col{
	background-color: $primary-color;
	color: $white-color;
}
fieldset {
    border: 1px solid #c0c0c0;
    margin: 0;
    padding: 15px 15px 0px;
}
legend {
    display: block;
    width: auto !important;
    padding: 0px 20px;
    margin-bottom: 0;
    font-size: 21px;
    line-height: inherit;
    color: $text-color;
    border: 0;
    border-bottom: none;
    z-index: 5;
    background-color: $header-background-color;
}
.input-icon1 {
    float: right;
    right: 15px;
    margin-top: -30px;
    position: relative;
    z-index: 2;
	color: $text-color;
}
.input-icon2 {
    float: right;
    right: 15px;
    margin-top: -37px;
    position: relative;
    z-index: 2;
	color: $text-color;
}
.load-more{
	text-align: center;
    margin-bottom: 100px;
}
.load-more a{
	background-color: $primary-color;
    padding: 8px 20px;
    color: #fff;
    text-align: center;
    font-size: 17px;
    display: block;
}
.load-more a:hover, .load-more a:focus, .load-more a:active{
	text-decoration: $text-dec;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
}
.text-error{
  color: #cc0000;
  font-size: 13px;
}
// login-page

.hidden{
	visibility:hidden;
}
input[type=text], input[type=password] {
    width: 100%;
    padding: 10px 12px 10px 12px;
    display: inline-block;
    border: 1px solid #ddd;
    color: black;
}
button.big-Login-button {
    background-color: $primary-color;
    color: $white-color;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    cursor: pointer;
    width: 100%;
    font-size: 16px;
}
button.big-Login-button:hover, button.big-Login-button:focus, button.big-Login-button:active{
	text-decoration: $text-dec;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
}
button.small-btn{
    background-color: $primary-color;
    color: #ffffff;
    padding: 5px 20px;
    margin: 8px 0;
    border: none;
    cursor: pointer;
}
button.border-btn{
    border: 1px solid $primary-color;
    padding: 5px 20px;
    margin: 8px 0;
    cursor: pointer;
    color: $primary-color;
    background: transparent;
}
button.border-btn:hover, button.border-btn:active, button.border-btn:focus{
    border: 1px solid $footer-main;
    padding: 5px 20px;
    margin: 8px 0;
    cursor: pointer;
    color: $white-color;
    background: $footer-main;
    transition: 0.5s ease-in-out;
}
button.resendotp-btn{
    background-color: $primary-color;
    color: #ffffff;
    padding: 9px 15px !important;
    border: none;
    cursor: pointer;
    width: 100%;
}
button.small-btn:hover, button.small-btn:focus, button.small-btn:active{
	text-decoration: $text-dec;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
}
span.psw {
    float: right;
    padding-top: 16px;
}


.otp-alert .alert-success{
    text-align: left;
}
.otp-alert .close{
    font-size: 22px;
}
.login-from{
	padding:35px;
	position: relative;
	margin-top: 50px;
}
#login .login-from input[type="text"], #login .login-from input[type="password"] {
    width: 100%;
    padding: 12px 43px 12px 12px;
    display: inline-block;
    border: 1px solid #ddd;
    color: black;
}
.login-from input[type="password"] {
    width: 100%;
}
#signup form .form-control {
    display: block;
    width: 100%;
    height: 45px;
    padding: 6px 12px;
    font-size: 16px;
    line-height: 1.42857;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ddd;
    border-radius: 0;
    box-shadow: none;
    transition: none;
}
.form-location .form-control {
    display: block;
    width: 100%;
    height: 25px;
    line-height: 1.42857;
    color: #555555;
    background-color: transparent;
    background-image: none;
    border: none;
    text-transform: uppercase;
    text-align: center;
    box-shadow: none;
   	transition: none;
}
.form-location .select__arrow {
    top: 15px;
    right: 0px;
}
label{
	float: left;
	text-transform: uppercase;
	text-align: left;
}
.login-box span.loc{
	margin-right: 40px;
	display: inline-block;
}
.filter{
	color: $primary-color;
	float: right;
	position: relative;
	margin: 0px;
	margin-bottom: 0px;
	text-decoration: $text-dec;
	text-transform: uppercase;
	text-align: right;
}
/* modal 1 */
#myModal form{
	width: 30%;
	left: 35%;
	position: relative;
}

.modal-header .close {
    margin-top: -28px;
	position: relative;
	right: -25px;
}
.modal button.close {
    padding: 5px 6px;
    cursor: pointer;
    background: $primary-color;
    border: 2px solid #fff;
    -webkit-appearance: none;
    border-radius: 50%;
    font-weight: bold;
}
.modal button.close:hover, .modal button.close:focus, .modal button.close:active{
	text-decoration: $text-dec;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
}
.close {
    float: right;
    font-size: 15px;
    line-height: 1;
    color: $white-color;
    text-shadow: none;
    opacity:1;
    filter: alpha(opacity=20);
}
.close:hover {
    color: $white-color;
    transition: 0.5s ease-in-out;
    opacity:1;
}
.modal {
    position: fixed;
    z-index: 999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
    padding-top: 40px;
}
.modal-body{
	text-align:center;
	font-weight: normal;
}
.modal-content{
	background-color: #f5f3f4;
	border-radius: 0;
	background-size: cover;
	background-position: 50%;
	box-shadow: none;
}
.modal-bg{
	background-color: #f5f3f4;
	background-image: url("../images/popup.jpg");
	border-radius: 0;
	background-size: cover;
	background-position: 50%;
}
.modal-body h4, .modal-body h5{
	text-transform: uppercase;
	color: $primary-color;
}
.modal-body h5.order-btn{
	text-transform: uppercase;
	color: $primary-color;
	text-decoration: $text-dec;
	padding: 10px;
	background-color: $primary-color;
	color: $white-color;
}
.modal-body h5.order-btn:hover, .modal-body h5.order-btn:active, .modal-body h5.order-btn:focus{
	text-decoration: $text-dec;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
}
.modal-body h5.modal-login-btn{
	margin-top: 20px;
    text-decoration: $text-dec;
}
form a:hover, form a:focus {
	text-decoration: $text-dec;
}
.modal-header {
    border-bottom: 0;
    padding-bottom: 0px;
}
.modal-footer {
    border-top: 0;
}
/* end modal 1 */

/* modal 2 (weekly menu) */
.modal-title {
    text-align: center;
    font-weight: bold;
}
ul, ol {
    margin-top: 0;
    margin-bottom: 10px;
    text-align: left;
}
.modal-body h4.date{
    background-color: #656D78;
    color: $white-color;
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 7px;
    padding-bottom: 5px;
    text-transform: capitalize;
    font-weight: bold;
    font-size: 17px;
}
.modal-body ul.day-item{
    margin-top: 0;
    margin-bottom: 0;
    text-align: left;
}
.modal-body .week-item{
    border: 1px solid #E6E9ED;
    margin-bottom: 5px;
}
.modal-body .mb-15{
	margin-bottom: 15px;
}
.modal-content .modal-hg{
	max-height: 465px;
    overflow-y: auto;
    overflow-x: hidden;
}
.week-item .list-group-item {
    position: relative;
    display: block;
    padding: 5px 15px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #ddd;
}
.modal-footer {
    padding: 0;
}
.weekly-add-meal-btn{
	text-align: center;
}
/* end modal 2 (weekly menu) */

/* modal 3 (View Item) */
.vw-item-img{
	width: 100%;
}
.vw-item p{
	text-align: justify;
}
#myModal3 .list-group-item {
    position: relative;
    display: block;
    padding: 5px 15px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #ddd;
}
.vw-item button.small-btn {
    background-color: $primary-color;
    color: $white-color;
    padding: 5px 20px;
    margin-top:0;
    margin-bottom:0;
    border: none;
    cursor: pointer;
}
.vw-item button.small-btn:hover {
    background-color: $footer-main;
    transition: 0.5 ease-in-out;
}
/* end modal 3 (View Item) */

/* modal 4 (rating) */
#myModal4 a.rating {
    color: $text-color;
    padding: 0;
    font-size: 25px;
    background-color: transparent;
}
#myModal4 a.rating:hover {
   color: $primary-color;
}
#myModal4 a.filled{
	color: $primary-color;
}
#myModal4 .modal-body .rateus{
	padding: 0 10px 10px;
}
#myModal4 .rateus b{
	font-size: 25px;
    padding: 5px;
}
#myModal4 .lunch-textarea{
    width: 100%;
    padding: 6px 12px;
    color: #000;
    border: none;
    height: 100px;
}
/*
	Ratings Stars
	(with as little code as possible)
*/
.rating1 {
  unicode-bidi: bidi-override;
  direction: rtl;
  text-align: center;
}
.rating1 > span {
  display: inline-block;
  position: relative;
  font-size: 30px;
  color: $primary-color;
  cursor: pointer;
}
.rating1 > span:hover,
.rating1 > span:hover ~ span,.rating1 > span.rate-act,.rating1 > span.rate-act   {
  color: transparent;
}
/*.rating1 > span.rate-act {
   color: transparent;
}*/
.rating1 > span:hover:before,.rating1 > span:hover ~ span:before ,.rating1 > span.rate-act:before,.rating1 > span.rate-act:before{
   content: "\2605";
   position: absolute;
   left: 0; 
   color: $primary-color;
}
/* end modal 4 (rating) */

.form-control {
    border-radius: 0px;
 }
.meal-container{
	margin-bottom: 30px;
}
.meal-box {
    margin: 10px 0;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
}
h4.meal-type {
	text-align: center;
}
.meal-box:hover {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
}
.rigth-panel{
	float: right;
	text-align: right;
}
.meal-box .clearfix{
	padding: 0 15px 10px;
	background-color: $header-background-color;
}
.meal-box .clearfix h3{
	margin-top: 10px;
	text-align: right;
	color: $primary-color;
}
.meal-box .clearfix h4.item-name{
	color: $text-color;
	font-size: 16px;
	margin-top: 12px;
}
.meal-box .clearfix .pull-left h4.price{
	margin-top: 20px;
	margin-bottom: 10px;
	font-size: 16px;
}
.meal-box .clearfix .pull-right a.add-btn{
	background-color: $primary-color;
	padding: 6px 20px;
	color: #fff;
	float: right;
}
.meal-box .clearfix .pull-right a.add-btn:hover, .meal-box .clearfix .pull-right a.add-btn:focus, .meal-box .clearfix .pull-right a.add-btn:active{
	text-decoration: $text-dec;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
	/*box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);*/
}
.meal-box .clearfix .pull-right a.add-item {
    background-color: transparent;
    padding: 0px;
    color: #fff;
}
.meal-box .clearfix .pull-right a.rating {
    color: $primary-color;
    padding: 0;
    background-color: transparent;
}
.view-item-box a{
	color: $primary-color;
	font-size: 16px;
}
.single-item{
  text-align: center;
}
.meal-box .view-item-box  {
    padding-top: 5px;
    padding-bottom: 5px;
}
.view-item-box a:hover, .view-item-box a:focus, .view-item-box a:active{
	text-decoration: $text-dec;
	color: $primary-color;
}
section .cart-owl-box .owl-carousel .offer{
	padding: 5px 15px 5px;
    margin: 10px 0;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
    background-color: $header-background-color;
}
section .cart-owl-box .owl-carousel .offer .offer-discount{
	color: $primary-color;
}
.well-sm{
	background-color: #f5f7fa;
	margin-top: 10px;
	border-radius: none;
	border: none;
	box-shadow: none;
	text-align: center;
	color: $primary-color;
}
hr.view-item-div {
    margin-top: 0;
    margin-bottom: 0;
    border: 0;
    border-top: 1px solid #dfdfdf;
}
h4.meal-type {
    position: relative;
    z-index: 1;
    
    &:before {
        border-top: 1px solid #dfdfdf;
        content:"";
        margin: 0 0px; /* this centers the line to the full width specified */
        position: absolute; /* positioning must be absolute here, and relative positioning must be applied to the parent */
        top: 50%; left: 0; right: 0; bottom: 0;
        width: 100%;
        z-index: -1;
    }

    span { 
        /* to hide the lines from behind the text, you have to set the background color the same as the container */ 
        background: #fff; 
        padding: 0 20px; 
    }
}
h4.meal-type span.see-more{
	float: right;
    border-radius: 50px;
    color: $white-color;
    border: 1px solid #656D78;
    background-color: #656D78;
    font-size: 15px;
    padding: 5px 12px;
    margin-top: -5px;
}
h4.meal-type span.see-more:hover{
    color: #656D78;
    background-color: $white-color;
    border: 1px solid #656D78;
    transition: 0.5s ease-in-out;
}
span.customSelect { 
    font:12px sans-serif;
    border: none;
    color:#555;
    padding:7px 9px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: none;
    width:175px;
}
.tab-panel{
	margin-bottom: 50px;
}

/* search bar */

.dropdown-search{
	display: none;
    margin-top: 0px;
    border: none;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    float: right;
    left: auto;
    min-width: 0;
    padding: 15px;
    right: 0;
    width: 250px;
    top: 100%;
    font-size: 12px;
    font-weight: normal;
    text-shadow: none;
    text-transform: none !important;
    border-top: 2px solid $primary-color;
    background-color: $header-background-color;
    z-index: 10;
}
.dropdown-menu .divider {
    background-color: #e5e5e5;
    height: 1px;
    margin-bottom: 0px;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0px;
    overflow-x: hidden;
    overflow-y: hidden;
}
.dropdown-menu {
	padding-top: 0;
	padding-bottom:0;
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate;
}
.input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0;
}
.dropdown-menu-right {
    left: auto;
    right: 0;
    text-align: right;
    text-transform: uppercase;
}
section #location{
	position: relative;
	margin-top: 20px;
}
.lunch-field, .dinner-field{
	display: none;
}
.lunch-textarea, .dinner-textarea{
	width: 100%;
	padding: 6px 12px;
	color: #000;
	border: 1px solid #ddd;
}
.copy-address, .subscribe, .agree-terms{
	float: left;
}
#term-conditions{
	color: $primary-color;
}


/* cart-page */
.sub-footer{
	background-color: $header-background-color;
	position: relative;
	padding: 15px 0;
}
.foot-price{
	top: 10px;
    position: relative;
    color: $text-color;
}
.sub-footer .clearfix .pull-right a {
    background-color: $primary-color;
    padding: 8px 20px;
    color: #fff;
    float: right;
    font-weight: normal;
    font-size: 14px;
}
.sub-footer .clearfix .pull-right a:hover, .sub-footer .clearfix .pull-right a:focus, .sub-footer .clearfix .pull-right a:active{
	text-decoration: $text-dec;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
	/*box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);*/
}
.checkout-main-panel h4{
	margin: 5px 0px 25px;
	text-align: center;
}
.checkout-main-panel h4.meal-type{
	margin: 25px 0px 25px;
}
.padding-15-0{
	padding: $padding-15-0;
}
.padding-0{
	padding: $padding-0;
}
.choose-plan{
	padding: 15px 0;
}
.checkout-box{
	background-color: $header-background-color;
	margin: 15px 0px;
	margin-bottom: 0;
}
.chechout-box .pull-left .veg-icon{
	position: relative;
	margin: 10px 0;
	left: 0px;
}
.set-item-name{
  margin-bottom: 5px;
}
.set-pref-div .control {
    margin-bottom: 7px !important;
    line-height: 1.3em !important;
    padding-left: 23px !important;
}
.set-pref-div .control__indicator {
    height: 15px;
    width: 15px;
}
.set-pref-div .control--radio .control__indicator:after {
    left: 4px;
    top: 4px;
    height: 5px;
    width: 5px;
    border-radius: 50%;
    background: #fff;
}
.fl-rgt {
    float: right;
}
.fl-lft {
    float: left;
}
.done-up1, .done-up2{
    font-size: 15px;
    text-align: center;
    top: -8px;
    position: relative;
    color: #fff;
    padding: 6px 5px 4px;
    z-index: 1;
    background: gray;
    border-radius: 50%;
}
.done-down1, .done-down2{
    font-size: 15px;
    text-align: center;
    top: -8px;
    position: relative;
    color: #fff;
    padding: 6px 5px 4px;
    z-index: 1;
    background: gray;
    border-radius: 50%;
}
.minus-icon, .plus-icon {
    padding: 7px 7px;
    border: 1px solid $primary-color;
    color: $primary-color;
    position: relative;
    top: 0;
    left: 0;
    font-size: 12px;
    z-index: 0;
    background-color: transparent;
    text-decoration: $text-dec;
    font-weight: bold;
}
.minus-icon:hover, .minus-icon:active, .minus-icon:focus, .plus-icon:hover, .plus-icon:focus, .plus-icon:active {
    color: $white-color;
    background-color: $primary-color;
    transition-duration: 0.5s;
    text-decoration: $text-dec;
}
.count .count-icon{
	padding: 7px 9px;
    border: 1px solid $header-background-color;
    color: $text-color;
    position: relative;
    top: 0;
    left: 0;
    z-index: 0;
    text-align:center;
    background-color: transparent;
}
.count{
	text-align: center;
	margin-bottom: 12px;
}
.side-cart a.cart-trash, .side-cart a.cart-trash:hover, .side-cart a.cart-trash:active{
    color: $primary-color !important;
    float: right;
    margin-top: 5px;
}
.cart-price h4{
	margin: 0;
}
.item-type-l-d{
   font-weight: normal;
}
.sub-total{
	text-align: right;
}
.item-labels label{
    font-size: 13px;
    font-weight: normal;
    text-transform: capitalize;
    padding: 5px 10px;
    border: 1px solid #ccc;
    line-height: 1em;
    margin-right: 5px;
}
.item-labels label.add-extra-items{
    border: 1px solid #ddd;
}
.item-labels label a span.ti-close {
    margin-left: 5px;
    font-size: 10px;
    color: $primary-color;
}
.txt-center{
  	text-align: center;
}
.txt-left{
	text-align: left;
}
.txt-right{
	text-align: right;
}
.week-head{
	padding: 10px 0;
}
.week-head button.border-btn{
	margin:0;
}
.week-head button.border-btn span{
	position: relative;
    top: 2px;
}
.modify-date{
	border: 1px solid $primary-color;
    padding: 5px;
    width: 100px;
    text-align: center;
    background: $white-color;
}
.cart-veg-icon {
    padding: 5px 5px;
    border: 1px solid $veg;
    color: $veg;
	  background-color: #ffffff;
    top: 0;
    float: left;
    font-size: 12px;
    z-index: 0;
    position: relative;
    margin-right: 10px;
}
.cart-nonveg-icon {
    padding: 5px 5px;
    border: 1px solid $nonveg;
    color: $nonveg;
    top: 0;
    float: left;
    font-size: 12px;
    z-index: 0;
    background-color: #ffffff;
    position: relative;
    margin-right: 10px;
}
.view-info .ti-info{
    padding: 2px;
    border: 1px solid $primary-color;
    border-radius: 50%;
    margin-left: 5px;
    background: $primary-color;
    color: #fff;
    font-size: 15px;
}
.add-item-name{
  text-align:left;
}
.add-extra-items{
  border-bottom: 1px solid #ddd;
}
.add-extra-items .add-item-name, .add-extra-items .add-item-price{
   margin: 13px 0;
}
.margin-top-bottom{
}
.cart-total{
	text-align: right;
	padding-top: 20px;
    padding-bottom: 15px;
}
#show-loc, #show_cart{
	display: none;
}

#cart-panel1, #cart-panel2{
    padding: 5px;	
    background-color: $header-background-color;
    text-align: center;
}

#cart-panel1, #cart-panel2 {
    padding: 20px 15px 15px;
    display: none;
}
.done-up1, .done-down1, .done-up2, .done-down2{
    display: none;
}
#cart-panel1 .form-group .select, #cart-panel2 .form-group .select{
	margin-bottom: 0;
}
#cart-panel1 .form-group, #cart-panel2 .form-group  {
    margin-bottom: 0;
}
#cart-panel1 input[type=text], input[type=password], #cart-panel2 input[type=text], input[type=password]  {
    margin: 0;
}
#cart-panel1 p , #cart-panel2 p {
    margin: 10px 0 10px;
    text-align: left;
}
/* end cart-page */

/* swap meal page */
.swap-main-panel {
    text-align: center;
    margin-bottom: 20px;
}
.swap-payment {
    padding: 20px;
    background-color: $header-background-color;
    margin: 0;
}
.swap-payment .green-txt{
  color: rgb(0, 179, 0);
}
.swap-meal-box fieldset {
    border: 1px solid #ddd;
    margin: 0;
    padding: 0;
    background: #f5f7fa;
    margin-bottom: 15px;
}
#myModal8 .changed-meal .veg-icon{
    position: static;
}
#myModal8 .changed-meal .control__indicator{
    top: 36px;
}
.changed-meal{
    border-top: 1px solid #ddd;
 }
#myModal8 .changed-meal {
    border-top: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 0;
}
 .checked-meal{
    position: absolute;
    left: 3px;
    padding: 7px;
    background: #8CC152;
    border-radius: 50%;
    bottom: 60px;
    font-weight: bold;
    font-size: 13px;
    color: $white-color;
 }
.pay-extra{
    color: #b30000;
    padding: 7px 0;
}
.swap-meal-box legend{
    border: 1px solid #ddd;
    font-size: 17px;
}
.swap-meal-box .address-box{
      margin-bottom: 0;
}
/* end swap meal page */

/* start payonline-page */
.payonline-main-panel
{
	text-align:center;
	margin-bottom: 20px;
}
.payonline-main-panel h4.page-title{
	margin: 5px 0px 25px;
}
.payonline-main-panel h4.meal-type::before {
    border-top: 1px solid #dfdfdf;
    content: "";
    margin: 0px;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    z-index: -1;
}
.address-box{
	background-color: $header-background-color;
	padding: 20px;
	min-height: 115px;
	margin-bottom: 15px;
}
.box-title-name{
	text-align: left;
}
.address-box h4.box-title-name{
	margin-top:0;
}
.address-box p{
	margin-bottom:0;
}
.address{
	max-width: 300px;
	text-align: left;
}
.fa-pencil, .add-address-btn i  {
	font-size: 15px;
}
.add-address-btn{
	color:$primary-color;
}
.add-address-btn:hover,  .add-address-btn:focus, .add-address-btn:active{
    color: $primary-color;
}
.address-box .pull-right a{
	color: $primary-color;
}
.address-box .pull-left{
	text-align: left;
}
.address-box .pull-right a:hover, .address-box .pull-right a:active, .address-box .pull-right a:focus, .address-box .pull-left a:hover, .address-box .pull-left a:focus, .address-box .pull-left a:active{
	text-decoration: $text-dec;
}
.promo-box {
    background-color: $header-background-color;
    padding: 0;
    margin: 0;
}
.promo-box input[type="text"], input[type="password"] {
    padding: 10px 12px 9px 12px;
}
.promo-code-btn{
	position: absolute !important;
	top: 0;
	background-color: $primary-color;
	color: $white-color;
	padding: 8.5px 12px;
	font-size: 14px;
	border-radius: 0;
	border: 2px solid #e9573f;
}
.promo-code-btn:hover, .promo-code-btn:focus, .promo-code-btn:active{
	color: $white-color;
	background-color: $footer-main;
	transition: 0.5s ease-in-out;
}
.item-rate-box{
	background-color: $header-background-color;
    padding: 20px;
    margin-bottom: 15px;
}
.main-rate-box{
	margin-bottom: 10px;
	margin-top:20px;
}
.main-rate-box .list-group{
	text-align: left;
}
.main-rate-box .list-group-item{
	background-color: $header-background-color;
	border: none;
}
.main-rate-box .list-head{
	background-color: #e6e9ed;
}
.main-rate-box .list-group-item:first-child, .list-group-item:first-child{
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
}
.list-group-item:last-child, .main-rate-box .list-group-item:nth-last-of-type(4) {
    margin-bottom: 0;
    border-top: 1px solid #dfdfdf;	
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
}
.main-rate-box .list-group-item {
    position: relative;
    display: block;
    padding: 10px 20px;
}
.main-rate-box .badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 15px;
    font-weight: normal;
    color: $text-color;
    line-height: 1;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    background-color: transparent;
    border-radius: 0px;
}
.main-rate-box .badge .fa-inr{
	font-size: 12px;
}
.meal-box .price .fa-inr{
	font-size: 16px;
}
.fa-inr{
	font-size: 15px;
}
.panel-title{
	text-align:left;
}

.panel-default > .panel-heading {
    color: $text-color;
    background-color: $header-background-color;
    border-color: none;
    border-bottom: 1px solid #dfdfdf;
}
.panel-group .panel-heading + .panel-collapse > .panel-body, .panel-group .panel-heading + .panel-collapse > .list-group {
    border-top: none;
    border-bottom: 1px solid #dfdfdf;
}
.panel-title > a, .panel-title > small, .panel-title > .small, .panel-title > small > a, .panel-title > .small > a {
	text-decoration: $text-dec;
}
.main-payment-box #list-head {
    position: relative;
    display: block;
    background-color: #e6e9ed;
    padding: 10px 20px;
}
.main-payment-box .list-group-item {
    position: relative;
    display: block;
    padding: 0px 0px;
}
.panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: none;
    border-radius: 0px;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.panel-heading {
    padding: 15px 15px;
    border-bottom: 1px solid transparent;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
}
.main-payment-box .panel-default > .panel-heading:last-child  {
    border-bottom: 1px solid $header-background-color;
}
.panel-group .panel + .panel {
    margin-top: 0px;
}
.main-payment-box .cheque input[type="text"]{
    width: 200px;
    padding: 12px 12px 12px 12px;
    margin: 8px 0 0 0;
    display: inline-block;
    border: 1px solid #ccc;
}
.main-payment-box .panel-heading .fa-circle {
    font-size: 15px;
    padding-right: 5px;
}

.open-payment-mode{
	color: $primary-color;
}
.address-box .form-group {
    margin-bottom: 0;
}
.address-box .form-group .select {
    position: relative;
    display: inline-block;
    margin-bottom: 15px;
    width: 100%;
    margin-top: 15px;
}
.sub-footer {
    position: fixed !important;
    transition: top .5s linear;
    bottom: 0;
    z-index: 2;
    width: 100%;
    border-top: 1px solid #e8e8e8;
}

.panel-title a:not(.collapsed) .ti-control-record:after {
    content: "";
    background: $primary-color;
    position: absolute;
    top: 50%;
    margin-top: -4px;
    left: 6px;
    height: 8px;
    width: 8px;
    border-radius: 50%;
}

.ti-control-record:before {
    content: "";
    height: 20px;
    width: 20px;
    background: #ffffff;
    border-radius: 50%;
   	position: relative;
    display: inline-block;
    border: 1px solid $primary-color;
    margin-right: 6px;
    vertical-align: middle;
}

.ti-control-record{
	position: relative;
    height: 20px;
    width: 20px;
    display: block;
    float:left;
}
span.choose-mod{
  display: block;
  padding-left: 26px;
  padding-top: 4px;
}
/* end payonline-page */

/* My Account */
.myaccount-main-panel {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
}
.ac-detail-box{
	margin-bottom: 20px;
	background-color: $header-background-color;
}
.ac-info{
	text-align: left;
	padding: 10px 10px;
}
.ac-info p{
	margin-bottom: 0;
}
.common-heading-big b{
	font-size: 25px;
}
.info-bg{
	background-color: $header-background-color;
}
.bg-lightGray, .wallet-table tbody tr:nth-child(odd) {
    background: $header-background-color;
    position: relative;
}
.left-my-account-icon {
    width: 100px;
    height: 100px;
}
.theme-green-bg {
    background: #acbf41;
}
.valign-wrapper {
    display: -webkit-flex;
    display: -ms-flexbox;
    /* display: flex; */
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}
.center {
    text-align: center;
    width: 100%;
}
.clearfix:before, .clearfix:after {
    content: " ";
    display: table;
}
.available_bal {
    width: 68px;
    height: 77px;
    background-position: -14px -174px;
    display: inline-block;
}
.sprite_img {
    background: url("../images/sprite.png") no-repeat;
    display: block;
}
.available_bal {
    width: 68px;
    height: 77px;
    background-position: -14px -174px;
    display: inline-block;
}
.usable_bal {
    width: 80px;
    height: 79px;
    background-position: -98px -174px;
    display: inline-block;
}
.locked_bal {
    width: 68px;
    height: 77px;
    background-position: -188px -174px;
    display: inline-block;
}
.right-my-account {
    height: 100px;
}
.bg-lightGray, .wallet-table tbody tr:nth-child(odd) {
    background: #eceef3;
}
.blue-bg {
    background: #4FC1E9;
}
.left-my-account-icon {
    width: 100px;
    height: 100px;
}
.right-my-account {
    height: 100px;
}
.clearfix:after {
    clear: both;
}
.red-bg {
    background: #ED5565;
}
.right-my-account .center {
    padding-left: 15px;
    text-align: left;
}
.common-heading-big, .heading {
    font-size: 27px;
}
.mb20{
	margin-bottom: 20px;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}
thead{
	background-color: #d6dbe2;
}
th, td {
    border: none;
    text-align: left;
    padding: 8px;
}
tr td{
	border-left: 1px solid #ffffff;
}
tr:nth-child(even){background-color: #f2f2f2;}

.info-table{
	overflow-x:auto;
	margin-bottom: 20px;
}
h4.meal-type span.cr-dr{
	float: right;
    color: $text-color;
    font-size: 15px;
    padding: 0 15px 0 0;
    margin-top: -10px;
    position: absolute;
    right: 0;
    border: 1px solid $text-color;
    /*background-color: $header-background-color;*/
}
span.cr-dr .select__arrow {
    position: absolute;
    top: 13px;
    right: 0px;
}
.cr-dr .select select {
    border: none;
}
.cr-dr .select{
	margin-bottom: 0;
	background-color: $header-background-color;
}
.wallet-inp input[type=text], input[type=password]
{
  margin-bottom: 0;
}
.ti-pencil-alt{
  color: $primary-color;
}
.ti-reload {
    color: $primary-color;
}
.wallet-inp{
  display: none;
}
#wallet-update-btn{
  display: none;
}
.dropdown-menu > li > a.dd-pd .wallet-amt{
  text-align: center;
  margin-left: 35px;
  color: $primary-color;
  font-size: 12px;
}
/* end My Account */

/* Offers */
.offers-main-panel {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px !important;
}
.offer-box-div{
	border: 1px solid #ddd;
	margin-bottom: 20px;
	background-color: $white-color;
}
.promo-code{
	color: $text-color;
}
.share-link{
	color: $primary-color;
}
.share-link:hover, .share-link:focus, .share-link:active{
	color: $primary-color;
}
.ti-user, .ti-key, .ti-book, .ti-wallet{
	padding-right: 15px;
}
.dropdown-menu > li > a.dd-pd{
	padding: 10px 20px;
	color: $text-color;
}
.dropdown-menu {
    border-radius: 0;
    box-shadow: none;
}
/* end Offers */

/* list view */
.list-view-panel{
	margin: 25px 0;
}
.list-view-panel .nav > li {
    text-align: left;
}	
.list-view-panel ul.nav-pills {
    background-color: $header-background-color;
    text-align: left;
    padding: 10px 20px;
}
.list-view-panel .nav-pills > li > a {
    border-radius: 0;
    border-bottom: 1px solid $header-background-color;
}
.list-view-panel .nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
    color: $primary-color;
    background-color: $header-background-color;
    border-bottom: 1px solid $primary-color;
}
.list-view-panel .nav-pills > li a.active{
    color: $primary-color;
    background-color: $header-background-color;
    border-bottom: 1px solid $primary-color;
}
.list-view-panel .nav-stacked > li + li {
    margin-top: 0;
    margin-left: 0;
}
.list-view-panel #section1 h4{
	text-align: center;
}
.list-icon{
	float: left;
	margin-right: 15px;
}
.small-border-btn{
	border: 1px solid $primary-color;
	padding: 3px 25px;
	background-color: $header-background-color;
	color: $primary-color;
}
.small-border-btn:hover, .small-border-btn:focus, .small-border-btn:active{
	color: $white-color;
	background-color: $primary-color;
	transition: 0.5s ease-in-out;
}
.mt-5{
  margin-top: 5px !important;
}

.list-view-panel b.lv-price{
	 margin-right: 20px;
}
.list-view-panel .lv-item{
	border-bottom: 1px solid #ddd;
	padding: 15px 20px;
}
.list-view-panel .mt-25{
	margin-top: 25px;
	text-align: center;
}
.list-view-panel .count-icon{
	padding: 5px 9px;
    border: 1px solid $header-background-color;
    color: $text-color;
    position: relative;
    top: 0;
    left: 0;
    z-index: 0;
    text-align:center;
    background-color: transparent;
   }
.list-view-panel .minus-icon, .list-view-panel .plus-icon {
  	padding: 5px 5px;
    border: 1px solid #e9573f;
    color: #e9573f;
    position: relative;
    top: 0;
    left: 0;
    font-size: 12px;
    z-index: 0;
    background-color: transparent;
    text-decoration: none;
    font-weight: bold;
}
.list-view-panel .minus-icon:hover{
    color: $white-color;
    background-color: $primary-color;
}
.list-view-panel .list-title{
	padding: 16px 20px;
    background-color: #e6e9ed;
    margin: 0;
}
#your-order{
	background-color:$header-background-color;
	margin-bottom: 10px;
}
.list-view-panel #your-order .lv-price {
    margin-left: 20px;
    margin-right: 0;
}
.list-view-panel .lv-tt{
	padding: 10px 20px;
	background-color:$header-background-color;
}
.lv-tt input[type="text"], input[type="password"] {
    margin: 8px 0 8px 0;
}
.lv-tt .promo-code-btn {
    padding: 8.5px 25px;
    background-color: $text-color;
    color: $white-color;
    margin: 8px 0;
    border: 2px solid $text-color;
}
.lv-tt .total{
	font-size: 18px;
}
.lv-tt .big-Login-button{
	font-size: 18px;
	padding: 10px 20px;
}
#ct-panel{
	background-color: #e6e9ed;
}
#ct-panel .control{
	margin-top:15px;
}
#list-search
{
	display: flex;
}
#list-search .input-group{
	padding: 8px;
}
.dt{
	padding: 15px 20px;
	background-color:$header-background-color;
}
.sec-top{
	padding-top: 50px;
	background-color:$header-background-color;
}

.list-view-panel.ps-fx  {
	#myScrollspy{
		position: fixed;
		top:0;
		/*asif*/
		/*@include transition(width,0.5s,linear);*/
		/*asif end*/
	}
	/*asif*/
	
	/*
	
	*/
	/*asif end*/
	.center-panel{
		
		/*@include transition(all,0.5s,linear);*/
		
	}
}
/*asif*/
.dropdown-parent-category{
	margin: 9px 15px;
	    width: 90px;
}
/*asif end*/
.list-view-panel.ps-fx{
	#your-order-panel{
		position: fixed;
		top:0;
		right: 36px;
		
	}
}
.list-view-panel.ps-fx{
	#ct-panel{
		position: fixed;
		top:0;
		z-index: 5;
		/*asif*/
		/*@include transition(all,0.5s,linear);*/
		/*end*/
	}
}

#items-count{
  background-color: $header-background-color;
  position: fixed;
  padding: 0 0 0 0;
  bottom: 0;
  width: 100%;
  z-index: 3;
}
#items-count .clearfix .pull-right a.crt-pr, #items-count .clearfix .pull-right a.crt-pr:hover, #items-count .clearfix .pull-right a.crt-pr:focus, #items-count .clearfix .pull-right a.crt-pr:active{
    background-color: #e6e9ed;
    padding: 8px 20px;
    color: $primary-color;
}

#items-count .clearfix .pull-right a {
    background-color: $header-background-color;
    padding: 8px 20px;
    color: $primary-color;
}
#items-count .clearfix .pull-right a:hover, #items-count .clearfix .pull-right a:focus, #items-count .clearfix .pull-right a:active{
  text-decoration: $text-dec;
  background-color: $header-background-color;
  transition: 0.5s ease-in-out;
  /*box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);*/
}
#items-count a.fooddialer {
    color: $primary-color;
    text-decoration: none;
}
.itm-in-crt-panel{
  display: none;
}
.itm-in-crt-btn{
  background-color: #e6e9ed;
}
.list-hg{
   height: 415px;
   overflow-y: scroll;
   margin-top: 25px;
   margin-bottom: 15px;
}
/* end list view */

/* list view with img */
.list-view-panel .img-lv-item{
	border-bottom: 1px solid #ddd;
	padding: 15px 0px;
	margin:0;
}
.list-view-panel b.img-lv-price{
	 margin-right: 0px;
	 float: right;
	 padding-top: 10px;
   padding-bottom: 10px;
}

/* end list view with img */

/* cart side panel */
.side-cart {
    height: 100%;
    position: fixed;
    z-index: 6;
    top: 0;
    right:-300px;
    background-color: $header-background-color;
    overflow-x: hidden;
   /*asif*/
    /*transition: 0.5s;*/
   /*asif end*/
    padding-top: 10px;
    border-left: 1px solid #ddd;
        width: 300px;
}


.side-cart a:hover, .offcanvas a:focus{
    color: #f1f1f1;
}

.side-cart .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
}
.side-cart .closebtn:hover,{
    color: #656d78;
}
.cart-open .side-cart{
    right: 0;
      
  }
  .cart-open .main-container {
    margin-right:300px;
  }
  .cart-open .main-container {
    margin-right:300px;
  }
  
.cart-close-btn{
  float: right;
}
#your-order-panel .minus-icon, #your-order-panel .plus-icon {
    padding: 4px 4px;
}
#your-order-panel .plus-minus{
  margin-top: 10px;
}
#your-order-panel .lv-item{
  padding: 10px 10px 10px 0;
  border-bottom: 1px solid #ddd;
}
#your-order-panel .lv-price{
  float: right;
}
#your-order-panel span.count-icon{
      padding: 0 7px;
}
/*.stickyfoo-cart-panel{
    position: fixed;
    bottom: 0;
    right: 0;
    width: 300px;
    padding: 0 15px;
}*/
/*asif */
.tp-dottedoverlay.twoxtwo{
	    background: rgba(0, 0, 0, 0.5);
}
.home{
	position:relative; 
}

/*asif end*/
@media screen and (max-height: 450px) {
  .side-cart {padding-top: 15px;}
  .side-cart a {font-size: 18px;}
}
/* end cart side panel */

/* home page*/

      /* Slideshow container */
     
     .mySlides {display:none}
      .slideshow-container {
        max-height: 500px;
        position: relative;
        margin: auto;
        background-color: #000;
      }
      .slideshow-container img{
        height: 500px;
        opacity: 0.6;
      }

      /* Next & previous buttons */
      .home-prev, .home-prev:hover, .home-prev:active, .home-prev:focus {
        cursor: pointer;
        position: absolute;
        top: 50%;
        width: auto;
        padding: 17px 9px 13px 4px;
        margin-top: -22px;
        color: $primary-color;
        transition: 0.6s ease;
        background-color: $white-color;
        border-radius: 0 50px 50px 0;
      }
        .home-next, .home-next:hover, .home-next:active, .home-next:focus {
        cursor: pointer;
        position: absolute;
        top: 50%;
        right: 0;
        width: auto;
        padding: 17px 4px 13px 9px;
        margin-top: -22px;
        color: $primary-color;
        transition: 0.6s ease;
       border-radius: 50px 0 0 50px;
        background-color: $white-color;
      }
     
      /* Caption text */
      .text b{
        color: $white-color;
        font-size: 65px;
        padding: 8px 12px;
        position: absolute;
        top: 27%;
        width: 100%;
        text-align: center;
      }

      /* Fading animation */
      /*.fade {
        -webkit-animation-name: fade;
        -webkit-animation-duration: 1.5s;
        animation-name: fade;
        animation-duration: 1.5s;
      }*/

      @-webkit-keyframes fade {
      from {
        opacity: .4
      }
      to {
        opacity: 1
      }
      }

      @keyframes
      fade {from {
        opacity: .4
      }
      to {
        opacity: 1
      }
      }

      /* On smaller screens, decrease text size */
      @media only screen and (max-width: 300px) {
        .prev, .next, .text {
          font-size: 11px
        }
      }
      h4.homepage-title{
        text-align: center;
        color:$primary-color;
        margin-top: 35px;
        margin-bottom: 35px;
      }
      .homepage-title b{
        text-align: center;
        font-size: 25px;
        color:$primary-color;
      }
      .homepage-title span b{
        text-align: center;
        font-size: 25px;
        color:$text-color;
      }
      .about-us-img img{
        width: 100%;
        margin-bottom:20px;
      }
      .about-us-box{
        margin-bottom: 30px;
      }
      .about-us-text{
        background-color: $header-background-color;
        text-align:left;
        padding: 20px;
        border: 1px solid #ddd;
      }
      .about-us-text .homepage-title{
         text-align:left;
         margin-top: 8px;
         margin-bottom: 8px;
      }
     .about-us-text .homepage-title b{
         font-size: 17px;
         text-align:left;
         color:$primary-color;
      }
       .about-us-text .homepage-title b span{
         color:$text-color;
       }
       .how-it-works-box .homepage-title b{
         font-size: 25px;
         text-align:left;
         color:$primary-color;
      }
       .how-it-works-box .homepage-title b span{
         color:$text-color;
       }
       .order-timing-box .homepage-title b span{
         color:$text-color;
       }
       .app-box-main .homepage-title b span {
          color:$text-color;
      }
       .about-info-remove{
            position: absolute;
            right: 15%;
            padding-top:30px;
            padding-bottom:30px;
       }
       .about-info-add{
            position: relative;
            padding-top:0px;
            padding-bottom:0px;
       }
       .our-services-box{
         background-color: $footer-main;
         padding-top: 20px;
         padding-bottom: 30px;  
       }
       b span.white-word{
         color: $white-color;
       }
       .our-services{
         text-align: center;
         color: $white-color;
         padding-top: 10px;
         padding-bottom: 10px;
       }
       .our-services img{
           width: 60px;
       }
       .howitworks-step{
            padding: 15px;
		    width: 50px;
		    margin: auto;
		    background: $primary-color;
		    color: $white-color;
		    border-radius: 50%;
		    font-weight: bold;
       }
       .how-it-works-box{
         background-color: $header-background-color;
         padding-top: 20px;
         padding-bottom: 30px; 
       }
       .how-it-works{
         text-align: center;
         color: $text-color;
         margin-bottom: 20px;
       }
       .how-it-works-number{
            color: #ffffff;
            text-align: center;
            font-size: 15px;
            margin-bottom: 30px;
       }
       .what-client-say-box{
         background-color: #000;
         background-image: url("../images/banner2.png");
         z-index:1;
         position: relative;
       }
       .img-container{
         background-color: rgba(0,0,0,0.3);
       }
       .wcs-box{
         text-align: center;
         color: $text-color;
         padding: 20px;
         position: relative;
         background-color: $white-color;
         margin-bottom: 15px;
       }
       .wcs-box:after {
          top: 99%;
          left: 50%;
          border: solid transparent;
          content: " ";
          height: 0;
          width: 0;
          position: absolute;
          pointer-events: none;
          border-top-color: $white-color;
          border-width: 15px;
          margin-left: -15px;
        }
       b.client-name{
         color: $primary-color;
       }
       .owl-carousel .owl-item  .wcs-main-box{
         text-align:center;
         margin-bottom: 20px;
       }
       .owl-carousel .owl-item .wcs-main-box img {
          width: 50px;
          margin-bottom: 20px;
          display: table;
          margin: auto;
        }
        .app-box-main{
          background-color: $header-background-color;
          padding-top: 70px;
          z-index: 1;
          position: relative;
        }
        h4.app-title{
          text-align: center;
          color:$primary-color;
          margin-top: 35px;
          margin-bottom: 10px !important;
        }
        .app-box{
          text-align: center;
        }
        .app-img{
          text-align: center;
          margin-top: 15px;
          margin-bottom: 15px;
        }
        .mobile-left{
          text-align: left;
          z-index: 1;
        }
        
        img.playstore-right{
            margin-right: 10px;
            margin-bottom: 15px;
            margin-left: 10px;
        }
        .order-timing-box {
            background-color: $white-color;
            padding-top: 20px;
            padding-bottom: 30px;
            z-index: 9;
            position: relative;
        }
        .step-border-bottom{
           position: relative;
        }
        .step-border-bottom:before {
            content: "";
            height: 5px;
            background: $text-color;
            width: 75%;
            position: absolute;
            left: 12%;
            top: 22px;
        }
       .sl-bottom-img{
          text-align: center;
          bottom: 0px;
          position: absolute;
       }
       .sl-text {
          text-align: center;
          font-size: 60px;
          color: #fff;
      }
      .fullwidthbanner-container{
        /*max-height: 500px;*/
      }
      .common-arrow{
        color: $primary-color;
        font-size: 31px;
        /* text-align: center; */
        /* opacity: 0.7; */
        filter: alpha(opacity=70);
        /* line-height: 57px; */
        z-index: 100;
        cursor: pointer;
        background-color: #fff;
        top: 50%!important;
        margin-top: -32px!important;
      }
      .tp-leftarrow.default{
        
        @extend .common-arrow;
        padding: 10px 15px 10px 5px;        
        border-radius: 0px 50px 50px 0;
        
    }
     .tp-rightarrow.default {
        
        @extend .common-arrow;
        padding: 10px 5px 10px 15px;
        border-radius: 50px 0px 0px 50px;
    }
    .tparrows.hidearrows {
      // -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
      // -moz-opacity: 0;
      // -khtml-opacity: 0;
      // opacity: 0;
      // top: 300px !important;
  }
    .sl-locaion{
        position: absolute;
        z-index: 99;
	left: 0;
	bottom: 50px;
        text-align: center;
	width: 100%;
    }
    .sl-input-fld{
        
    }
    .sl-locaion input[type=text], input[type=password] {
      padding: 10px 12px 10px 40px;
      margin: 0;
    }
    .input-icon3 {
      float: left;
      left: 12px;
      margin-top: -27px;
      position: relative;
      z-index: 2;
      color: #656d78;
  }
  /*.sl-form{
       display: flex;
  }*/
  .your-loc-fld{
    margin-right: 5px;
  }
  .owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
    background-color: $primary-color !important;
}
  button.order-button {
    background-color: $primary-color;
    color: #ffffff;
    padding: 9px 20px;
    border: none;
    cursor: pointer;
    font-size: 16px;
  }
 	/*asif
  .sl-input-fld .form-group {
      margin-right: 5px;
      margin-bottom: 0;
      width: 125px;
  }
 asif end*/
  .sl-input-fld .form-group .select{
      margin-bottom: 0;
  }
/* end home page*/

/* footer - 2 */
.footer-2 {
    background-color: $footer-main;
    background-position: top;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 9;
    position: relative;
}
footer .footer-overlay {
    color: #fff;
}
.mb20 {
    margin-bottom: 20px;
}
footer .footer-overlay .footer_heading {
    font-size: 16px;
}
.mb10 {
    margin-bottom: 10px;
}
footer .footer-overlay .icons {
    font-size: 18px;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    padding: 8px 6px 8px 6px;
    border: 1px solid #ffffff;
    text-align: center;
    transition: all 0.3s;
    margin-right: 8px;
    margin-bottom: 10px;
}
.disilbk {
    display: inline-block;
    vertical-align: top;
}
.footer-2 a {
    color: #fff;
}
.footer-2 a.fooddialer, .footer-2 a.fooddialer:hover, .footer-2 a.fooddialer:active, .footer-2 a.fooddialer:focus {
    color: $primary-color;
    text-decoration: none;
}
.footer-2 .social-icon {
    word-spacing: 10px;
    text-align:left;
}
.footer-2  a span, .footer-2  a span:hover, .footer-2  a span:active, .footer-2  a span:focus {
    color: $white-color;
    text-decoration: none;
    font-size: 15px;
}
footer .footer-overlay ul {
    padding: 0;
    list-style: none;
}
footer .footer-overlay ul li {
    margin-bottom: 10px;
}
footer .footer-overlay .form-group {
    margin: 0 0 15px 0;
}
footer .footer-overlay .form-group input, footer .footer-overlay .form-group textarea {
    border: 1px solid #fff;
    border-radius: 0;
    background: transparent;
    color: #fff;
    filter: alpha(opacity=70);
}
footer .footer-overlay .form-group textarea {
    resize: vertical;
}
.full-width{
    width: 100%;
    margin-bottom: 0 ! important;
}
.footer-2 .btn-green {
    background: $primary-color;
    border-color: $primary-color;
}
.footer-2 .btn-small {
    border-radius: 0;
    padding: 7px 30px;
    position: relative;
    transition: all 0.3s;
    z-index: 1;
    overflow: hidden;
    display: block;
}
.footer-2 .p15 {
    padding: 15px;
}
.footer-2 .visible-xs {
    text-align: center;
    padding: 5px;
    background-color: $footer-main;
}
.white-placeholder::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: $white-color;
}
.white-placeholder::-moz-placeholder { /* Firefox 19+ */
  color: $white-color;
}
.white-placeholder:-ms-input-placeholder { /* IE 10+ */
  color: $white-color;
}
.white-placeholder:-moz-placeholder { /* Firefox 18- */
  color: $white-color;
}
/* end footer - 2 */

/* rounded progress bar */
.roated-pb{
  text-align: center;
}

/* end rounded progress bar */
// owl //
.cart-owl-box{
	padding: 0 30px;
	margin-bottom: 35px;
	z-index: 1;
}
.owl-carousel .owl-stage-outer .owl-nav .owl-next{
	position: absolute;
	right: 0;
	bottom: 0;
	z-index: 2;
	background: $primary-color;
	padding: 3px 10px;
	color: $white-color;
}
.owl-carousel .owl-stage-outer .owl-nav .owl-prev{
	position: absolute;
	left: 0;
	bottom: 0;
	z-index: 2;
	background: $primary-color;
	padding: 3px 10px;
	color: $white-color;
}
.owl-prev{
	position: absolute;
	top: 50%;
	left: -30px;
    height: 25px;
    margin-top: -10px;
    padding: 1px 5px;
    border-radius: 3px;
	color: $primary-color;
	font-weight: bold;
}
.owl-next{
	position: absolute;
	top: 50%;
	right: -30px;
    height: 25px;
    margin-top: -10px;
    padding: 1px 5px;
    border-radius: 3px;
	color: $primary-color;
	font-weight: bold;
}
.ti-angle-right, .ti-angle-left{
	font-size: 20px;
}
.checkout-main-panel .cart-owl-box h4 {
    margin: 5px 0px 5px !important;
    text-align: center;
}
.checkout-main-panel .cart-owl-box h4.item-name, .checkout-main-panel .cart-owl-box h4.price {
    margin: 18px 0px 5px !important;
    text-align: center;
}

//end owl //

// website maintenance //
body.frame {
    padding: 40px;
    overflow: hidden;
    position: relative;
    color: white;
    background: #191919;
    height: 100%;
}
#content {
    text-align: center;
    height: auto;
}
#outer-wrapper #inner-wrapper #table-wrapper {
    display: table;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: rgba(0,0,0,0.5);
    
}
#outer-wrapper #inner-wrapper #table-wrapper #row-content {
    display: table;
    height: 100%;
    width: 100%;
}
#outer-wrapper #inner-wrapper #table-wrapper .container {
    height: 100%;
    display: table;
}
#outer-wrapper {
    -moz-transition: 1s;
    -webkit-transition: 1s;
    transition: 1s;
    height: 100%;
    position: relative;
    -moz-box-shadow: 0 0 50px rgba(0, 0, 0, 0.4);
    -webkit-box-shadow: 0 0 50px rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.4);
}
.animate.translate-z-in.in {
    filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
    opacity: 1;
    -moz-transform: translateZ(0px);
    -ms-transform: translateZ(0px);
    -webkit-transform: translateZ(0px);
    transform: translateZ(0px);
}
#outer-wrapper #inner-wrapper {
    position: relative;
    height: 100%;
    overflow: hidden;
    background-image: url("../images/web.jpg");
    background-position: 50% 50%;
}
#outer-wrapper #inner-wrapper #table-wrapper #row-content h1 {
    text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.65);
    text-transform: uppercase;
    font-size: 90px;
    font-weight: 700;
}
#outer-wrapper #inner-wrapper #table-wrapper #row-content #content-wrapper {
    display: table-cell;
    vertical-align: middle;
    -moz-perspective: 1000px;
    -webkit-perspective: 1000px;
    perspective: 1000px;
    z-index: 1;
}
.opacity-70 {
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);
    opacity: 0.7;
}
// end website maintenance //

// 404 error page//
.error-main{
  text-align: center;
  margin: 50px 0 20px;
}
.error-main img{
  margin: auto !important;
}
// end 404 error page//

// wallet history //
.wallet-panel{
  margin-top:20px;
}
.wallet-box {
    background-color: $header-background-color;
    width: 50%;
    margin-left: 25%;
    padding: 0px;
    text-align: center;
    margin-bottom: 50px;
}
.wallet-from {
    padding: 30px;
    position: relative;
    text-align: left;
}
.load-more-2{
    text-align: center;
    margin-bottom: 25px;
    margin-top: 25px;
}
.load-more-2 a{
    background-color: $primary-color;
    padding: 8px 20px;
    color: #fff;
    text-align: center;
    font-size: 17px;
    display: block;
}
.load-more-2 a:hover, .load-more-2 a:focus, .load-more-2 a:active{
    text-decoration: $text-dec;
    background-color: $footer-main;
    transition: 0.5s ease-in-out;
}
.unsub{
      padding: 10px 25px;
}
.otp input[type=text], input[type=password]{
    margin-bottom: 0;
    padding: 5px 12px 5px 12px;
    width: 100px;
    margin-top: 7px;
}
.Resend-otp, .otp{
  margin-right: 5px;
}
.ac-detail-box button.small-btn {
    background-color: $primary-color;
    color: $white-color;
    padding: 5px 8px;
    margin: 8px 0;
    border: none;
    cursor: pointer;
}
// end wallet history //

// booking history //
.booking-history{
    margin-top: 0;
}
.booking-history table{
    background-color: $white-color;
}
.booking-history-tab ul#myTabs {
    text-transform: uppercase;
    margin-bottom: 0;
}
.booking-main-panel {
    text-align: center;
    margin-bottom: 0;
}
.booking-history .load-more {
    text-align: center;
    margin-bottom: 20px;
}
.booking-history a.action-pen{
  margin-right: 15px;
}
.booking-history a.action-close{
    color: $text-color;
}
.booking-history a.view-dates{
  color: $primary-color;
}
// end booking history //

// plan meal //
.radio-btns{
    text-align: center;
}
.text-center{
   text-align: center;
}
.plan-meal-div{
  margin-bottom: 20px;
}
.productadd button, .productadd button:hover, .productadd button:active, .productadd button:focus{
  background: none;
  border: none;
  outline: none;
}
.trash-icon{
    padding: 7px 7px;
    border: 1px solid $primary-color;
    color: $primary-color;
    position: relative;
    top: 0;
    left: 0;
    font-size: 12px;
    z-index: 0;
    background-color: transparent;
    text-decoration: none;
    font-weight: bold;
}
.trash-icon:hover, .trash-icon:focus, .trash-icon:active{
    color: $white-color;
    background-color: $primary-color;
    transition-duration: 0.5s;
    text-decoration: none;
}
.productadd label {
    float: none;
    text-transform: uppercase;
    text-align: center;
}
.multi-field{
  padding: 20px;
}
.productadd .form-group {
    margin-bottom: 0;
}
.productadd .select {
    margin-bottom: 0;
}
.btn_click{
    margin-top: 10px;
    margin-bottom: 4px;
}
.productadd .cart-price{
    margin-top: 10px;
    margin-bottom: 4px;
}
.productadd .add-meal-bn{
    margin-top: 10px;
    margin-bottom: 4px;
    text-align: right;
}
.choose-promocode .list-group-item {
    background-color: $header-background-color;
    border: none;
    height: 40px;
    font-weight: normal;
}

.choose-promocode .badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 15px;
    font-weight: normal;
    color: #656d78;
    line-height: 1;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    background-color: transparent;
    border-radius: 0px;
}
span .rs-input{
    margin: 0;
    padding: 0;
    height: 20px;
    width: 100px;
}
.list-group .hg-app{
  height: 75px;
}
 .date-selecion{
     margin-top: 20px;
  }
  .date-selecion-box{
        padding: 20px;
        background-color: $header-background-color;
  }
  .date-pick-div{
    margin-top: 20px;
  }
  .date-pick-div button.small-btn{
     margin: 33px 0 0;
  }
  .choose-promocode h4.meal-type {
    margin: 25px 0px 25px;
  }
  .check-box-center{
    padding: 20px 10px 0px;
    text-align: center;
  }
  .check-margin-left{
     margin: auto;
     display: table;
  }
  button.tip-top{
    float: right;
    margin-right: 15px;
    border: none;
    background: none;
    outline: none;
  }
  .date-selecion-box input[type=text], input[type=password] {
    padding: 8px 12px 7px 12px;
  }
  .balance-overlay-1, .balance-overlay-2, .balance-overlay-3{
    height: 100%;
      opacity: 0;
      position: absolute;
      right: 0;
      text-align: center;
      top: 0;
      width: 100%;
      font: 18px opensans-regular-webfont;
      color: #222;
  }
  .green-trans-bg:hover .balance-overlay-1{
    opacity: 1;
    background: rgba(172, 191, 65, 0.8);
    padding: 30px 0 30px;
  }
   .blue-trans-bg:hover .balance-overlay-2{
    opacity: 1;
    background: rgba(79, 193, 233, 0.8);
    padding: 42px 0 30px;
  }
   .pink-trans-bg:hover .balance-overlay-3{
    opacity: 1;
    background: rgba(237, 85, 101, 0.8);
    padding: 42px 0 30px;
  }
// end plan meal //

// contact us page //
.contact_page {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
}
.contact_page form {
    background: $header-background-color;
    width: 100%;
    height: auto;
    display: block;
    padding: 40px 30px;
}
.contact-from .ti-user{
  padding-right: 0;
}
.contact-textarea{
    width: 100%;
    padding: 6px 12px;
    color: #000;
    border: none;
    height: 100px;
}
.company-info-panel{
  background: $header-background-color;
   height: auto;
    display: block;
    padding: 25px;
    text-align: left; 
}
.map{
  margin-top: 20px;
  margin-bottom: 20px;
}
.map iframe{
  width: 100%;
  height: 478px;
  display: block;
  pointer-events: none;
  position: relative; /* IE needs a position other than static */
}
.map iframe.clicked{
  pointer-events: auto;
}
// end contact us page //

/*********************** asif *******************************/
.affix{
  @include transition(all,0.5s,linear);
}
#myTabs.nav-tabs > li {
  border-right:0;
  border-left: 1px solid #aab2bd;  
  > a{
    padding: 15px 15px;
    border-bottom:0;
    @include border-radius(0);
    
  }
  
}
#myTabs.nav-tabs > li:first-child{
      border-left:0;  
  }
.header-nav-parent.affix .time{
  display:none;
}
.progress-bar{
	display: inline-block;
    padding-left: 0;
    width: 200px;
}
.menu-open .header-nav-parent.affix{
	width: calc(100% - 250px);
}
/****************** asif end ***************************************/
//media starts from here//
@media screen and (max-width: 1200px ) {
	.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption{
		width:100%;	
	}

}
@media screen and (max-width: 992px ) {
	#list-search{
		display:none;
	}
	.sl-locaion{
		position: inherit;
    	bottom: 35px;
	}
	.sl-input-fld{
		background: #ddd;
    	padding-top: 15px;
	}
	ul#myTabs span.time{
		display: none;
	}
	
}

@media screen and (max-width: 767px ) {
  .sl-text{
    width:100%;
  }
  .sl-bottom-img{
    width:100%;
    left: 150px;
  }
  .sl-locaion{
  	    bottom: inherit;
  }
  /*asif.sl-input-fld {
    position: absolute;
    z-index: 99;
    left: 17.1%;
    top: 1.8%;
    text-align: center;
}*/
.checked-meal{
	bottom: inherit;
}
#myModal8 .changed-meal .control__indicator {
    top: -9px;
    left: -15px;
}
.promo-code-btn {
    top: 8px;
}
#myModal6 .modal-content .modal-hg {
    max-height: 460px;
    overflow-y: scroll;
    overflow-x: hidden;
}
#myModal2 .modal-content .modal-hg {
    max-height: 460px;
    overflow-y: scroll;
    overflow-x: hidden;
}
#myCartPanel{
  display: none;
}
.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
    border-color: #e8e8e8;
    height: 265px !important;
    overflow-y: scroll;
}
#logo {
    padding: 0;
}
.sl-form {
    display: block;
}
/*asif.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption{
  display:none;
}*/
    button.order-button {
      width: 100%;
  }
  /*asif.sl-input-fld .form-group {
    margin-right: 5px;
    margin-bottom: 5px;
    width: 100%;
}*/
.your-loc-fld {
    margin-right: 0;
    margin-bottom: 5px;
}
	footer .foot-sec {
		padding: 10px;
		text-align: center;
	}
	.navbar-brand {
    	margin-top: 0px;
	}
	.navbar-nav .open .dropdown-menu {
	    text-align: center;
	}
	.navbar-inverse .navbar-nav .open .dropdown-menu .divider {
	    background-color: transparent;
	}
	.login-box {
	    margin-top: 0px;
	    margin-bottom: 30px;
	}
	.wallet-box {
    width: 100%;
    margin-left: 0;
  }
	.breadcrumb {
	    padding: 8px 8px 8px 0px;
	   }
	#icon-menu {
		background-color:  $primary-color;
	}
	.sub-head-mrtp {
    margin-top: 80px;
}
	.cart-owl-box{
		padding: 0 0;
	}
	.cart-price{
		float:right;
	}
	.bar1, .bar2, .bar3 {
		background-color:  $white-color;
	}
	.container-fluid {
		margin-right: auto;
		margin-left: auto;
		padding-left: 15px;
		padding-right: 15px;
	}
	.navbar-inverse .navbar-nav > li > a {
		color: #aab2bd;
		font-weight: bold;
	}
	#logo img {
	  top: -3px;
    position: absolute;
    padding-top: 0px;
    left: 80px;
    height: 50px;
	}
	header {
	    background-color: $white-color;
	    position: fixed;
	    width: 100%;
	    z-index: 999;
	    top: 0;
	}
	body.pad-60{
		padding-top: 60px;
	}
	body.pad-80{
		padding-top: 80px;
	}
	body.pad-100{
		padding-top: 100px;
	}
	section #location {
		margin-top: 25%;
	}
	#myModal form{
		width: 100%;
		left: 0;
		position: relative;
	}
	input[type="text"], input[type="password"] {
    	padding: 12px 12px 12px 12px;
	}
	.login-from {
	    padding: 20px;
	    margin-top: 30px;
	}
	
	#location {
	    position: relative;
	    margin-left: 50px;
	    margin-top: 0px;
	    color: #656d78;
	}
	.modal-content {
	    background-color: #f5f3f4;
	    background-image: none;
	    border-radius: 0;
	}
	.well-sm {
    	margin-top: 25px;
   }
   .navbar-inverse .navbar-nav > li > a {
	    line-height: 1em;
	}
	#your-order-panel{
		display: none;
	}
	/*.owl-next {
	    position: absolute;
	    top: 33%;
	    right: 0px;
	    height: 30px;
	    margin-top: -15px;
	    padding: 0px 5px;
	    border-radius: 0;
	    color: #fff;
	    font-weight: 600;
	    background-color: #e9573f;
	}
	.owl-prev {
	    position: absolute;
	    top: 33%;
	    left: 0px;
	    height: 30px;
	    margin-top: -15px;
	    padding: 0px 5px;
	    border-radius: 0;
	    color: #fff;
	    font-weight: 600;
	    background-color: #e9573f;
	}*/
	#show-loc{
		display: block;
	}
	#your-order{
		display: none;
	}
	#show_cart{
		display: block;
		color: $primary-color;
		float: right;
		padding-top: 12px;
		padding-right: 0;
	}
	#shopping-cart, #location{
		display: none;
	}
	.count {
	    text-align: left;
	}
	.navbar-inverse .navbar-toggle {
	    position: relative;
	    float: right;
	    margin-right: 15px;
	    padding: 9px 10px;
	    margin-top: 12px;
	    margin-bottom: 0px;
	    background-color: transparent;
	    background-image: none;
	    border: 1px solid transparent;
	    border-radius: 4px;
	}
	#shopping-cart .badge, #show_cart .badge {
	    top: -17px;
	    right: 27px;
	}
	.sidenav{
		z-index:9999; 
	}
	/*asif*/
	#main{
		margin:0!important;
	}
	/*asif end*/
	#icon-menu{
		position: absolute;
    	left: 0;
    	z-index: 99;
	}
	.menu-open #icon-menu{
		left: 250px;
	}
	.navbar-collapse {
	    margin-top: 0px;
	}
	.promo-box {
	    padding: 8px 0;
	}
	.vw-item p {
	    margin-top: 15px;
	}
	.cart-total {
	    padding-top: 8px;
	    padding-bottom: 0px;
	}
	
	.promo-box input[type="text"], input[type="password"] {
	    margin: 8px 0 8px 0;
	    width: 195px;
	}
	#cart-panel1 input[type=text], input[type=password], #cart-panel2 input[type=text], input[type=password] {
	    margin: 8px 0 2px 0;
	}
	#cart-panel1 .form-group .select, #cart-panel2 .form-group .select {
	    margin-bottom: 10px;
	    width: 92%;
	}
	.load-more {
	    margin-bottom: 50px;
	}
	/*asif	*/
	.list-view-panel.ps-fx #ct-panel {
	    position: fixed;
	    top: 60px;
	}
	/*asif end*/
	.sec-top {
	    padding-top: 5px;
	}
	.list-view-panel .ml-name{
   padding-top: 10px;
  }
  .slideshow-container img {
      height: 260px;
  }
  .text b {
    font-size: 35px;
    top: 18%;
  }
  .home-next, .home-next:hover, .home-next:active, .home-next:focus {
    padding: 13px 0px 10px 8px;
  }
  .home-prev, .home-prev:hover, .home-prev:active, .home-prev:focus { 
    padding: 13px 8px 10px 0px;
  }
  .text-center-xs {
    text-align: center;
  }
  .mobile-left img {
    width: 100%;
  }
  .productadd .add-meal-bn {
    margin-top: 10px;
    margin-bottom: 4px;
    text-align: right;
  }

  .btn_click {
      margin-top: 10px;
      margin-bottom: 4px;
      text-align: left;
  }
  .productadd .select {
    margin-bottom: 10px;
  }
  .choose-promocode input[type="text"], input[type="password"] {
    padding: 9.5px;
  }
  .map iframe{
    width: 100%;
    height: 300px;
 }
}

@media screen and (min-width: 320px) and (max-width: 390px ) {
	section #location {
		margin-top: 65%;
	}
	.login-box {
	    width: 100%;
	    margin-left: 0;
	}
}

@media screen and (max-height: 530px) {
	.sidenav a.list {
		padding: 15px 10px 15px 10px;
		font-size: 16px;
	}
	.fa-home, .fa-user, .fa-phone, .fa-lightbulb-o {
		padding: 0 0.5 rem 0 0;
		font-size: 17px;
	}

}
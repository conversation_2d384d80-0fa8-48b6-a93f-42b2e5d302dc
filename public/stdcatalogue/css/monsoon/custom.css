h4.meal-type span {
    text-transform: capitalize;
}
.add-extra-items{
	padding-bottom: 0px;
}

.font-12{
	font-size: 12px;
}
.cursor-default{
	cursor: default;
}
.ui-widget.ui-widget-content {
	margin: auto;
}
/*
.ui-state-highlight, .ui-widget-content .ui-state-highlight{
	background: #f0ad4e !important; 
    color: #000 !important;
}
*/
/*.modal-open #myModal {
  display: flex;
  justify-content: center;
  align-items: center; 
}*/
.modal-dialog {
  /*display: flex;*/
  justify-content: center;
  align-items: center;
  height: 100%;
  margin: auto !important;
}

.product-description .meal_items{
  color: #333;
} 

.offered-plan-box{
  display: inline-table;
}

.offered-plan-box > span{
  padding-right: 8px;
} 

.more-time-slot:hover, .time-slot:focus, .time-slot:active {
    border: 1px solid #ddd;
    background: #f9f7f7;
    cursor: pointer;
}
.more-time-slot {
    padding: 20px;
    border: 1px dashed #ddd;
    text-align: center;
    margin-bottom: 15px;
}
.remove_extra .ti-close {
    color: #ff0000;
}
.cart-destroy .ti-close {
    color: #ff0000;
}
.order-summury .add-extra {
    color: #949494;
    padding: 0px !important; 
    width: fit-content;
}

.choose-days {
  height: 30px;
  width: 30px;
  background: #bfbcbc;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  line-height: 2.5rem;
  margin-right: 7px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center; }

.choose-days.selected {
  background: #4caf50;
  color: #fff; }

.order-now-btn {
    background: #9e2681;
    color: #fff;
    padding: 6px 15px;
    margin-top: 25px;
    font-weight: 600;
    border: none;
}

.row.foot-style {
    display: flex;
    align-items: center;
}    

/*
.choose-days {
    height: 25px;
    width: 25px;
    background: #bfbcbc;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    line-height: 2.5rem;
    margin-right: 7px;
    cursor: pointer;
}
.choose-days.selected {
    height: 25px;
    width: 25px;
    background: #4caf50;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    line-height: 2.5rem;
    margin-right: 7px;
}
.choosedays {
    margin-bottom: 30px;
}
*/
.fs-item-offer {
/*color: #6542ea;*/
text-transform: capitalize;
padding: 5px 0;
line-height: 14px;
font-weight: 600;
font-size: 12px;
}

/*footer*/

.shownew {
    display: flex !important;
    align-items: center;
}

.fooddialer, .fooddialer:hover, .fooddialer:active, .fooddialer:focus{
    color: #232122 !important;
}

@media screen and (max-width: 767px){
  .add-extra-items {
    border-bottom: 0px solid #ddd !important; 
  }

  .modal-dialog {
    display: flex;
    position: relative;
    width: 70%;
    margin: 10px;
  }    
}

h4.meal-type span {
    text-transform: capitalize;
}
.add-extra-items{
	padding-bottom: 0px;
}

.font-12{
	font-size: 12px;
}
.cursor-default{
	cursor: default;
}
.ui-widget.ui-widget-content {
	margin: auto;
}
/*
.ui-state-highlight, .ui-widget-content .ui-state-highlight{
	background: #f0ad4e !important; 
    color: #000 !important;
}
*/
/*.modal-open #myModal {
  display: flex;
  justify-content: center;
  align-items: center; 
}*/
.modal-dialog {
  /*display: flex;*/
  justify-content: center;
  align-items: center;
  height: 100%;
  margin: auto !important;
}

.product-description .meal_items{
  color: #333;
} 

.offered-plan-box{
  display: inline-table;
}

.offered-plan-box > span{
  padding-right: 8px;
} 

.more-time-slot:hover, .time-slot:focus, .time-slot:active {
    border: 1px solid #ddd;
    background: #f9f7f7;
    cursor: pointer;
}
.more-time-slot {
    padding: 20px;
    border: 1px dashed #ddd;
    text-align: center;
    margin-bottom: 15px;
}
.remove_extra .ti-close {
    color: #ff0000;
}
.cart-destroy .ti-close {
    color: #ff0000;
}
.order-summury .add-extra {
    color: #949494;
    padding: 0px !important; 
    width: fit-content;
}

.choose-days {
  height: 30px;
  width: 30px;
  background: #bfbcbc;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  line-height: 2.5rem;
  margin-right: 7px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center; }

.choose-days.selected {
  background: #4caf50;
  color: #fff; }

/*
.choose-days {
    height: 25px;
    width: 25px;
    background: #bfbcbc;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    line-height: 2.5rem;
    margin-right: 7px;
    cursor: pointer;
}
.choose-days.selected {
    height: 25px;
    width: 25px;
    background: #4caf50;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    line-height: 2.5rem;
    margin-right: 7px;
}
.choosedays {
    margin-bottom: 30px;
}
*/
.fs-item-offer {
color: #6542ea;
text-transform: capitalize;
padding: 5px 0;
line-height: 14px;
font-weight: 600;
font-size: 12px;
}

@media screen and (max-width: 767px){
  .add-extra-items {
    border-bottom: 0px solid #ddd !important; 
  }

  .modal-dialog {
    display: flex;
    position: relative;
    width: 70%;
    margin: 10px;
  }    
}



/*new css changes*/


/*menu footer*/

.footer-4 {
    position: fixed;
    width: 100%;
    bottom: 0;
}

.shownew {
    display: flex !important;
    align-items: center;
}

.pd-14-10 {
    padding: 14px 10px !important;
    width: 40%;
}

.back-to-top, .back-to-top:hover {
    bottom: 1px;
    right: 10px;
    background: #e41e25;
}

@media screen and (max-width: 992px){
.back-to-top{
    display: none !important;
}}



/*menu background*/


#main {
    background: url(https://i.pinimg.com/originals/7c/67/96/7c679658c9a587ffd6b0e66c9ab0d0fe.jpg) repeat fixed 0 0/cover;
    background-repeat: no-repeat;
    -webkit-background-size: cover;
    background-attachment: fixed;
}


/*menu page*/


.address-box {
    background-color: transparent;
}

.primary-color, ul.stepper li.active a .circle, ul.stepper li.completed a .circle {
    background: #e41e25 !important;
}

.box-styling.selected, .box-styling.selected:hover {
    border: 2px solid #ffffff;
    background: #e41e25;
    cursor: default;
    color: #fff;
}

.box-styling{
    border: 2px dashed #e41e25;
}

.btn-default:hover, .btn-default:focus, .btn-default:active{
    background: #e41e25 !important;
}

.meal-box-main {
    border: 2px solid #e41e25;
}

.meal-box-main.selected, .meal-box-main:hover{
    border: 2px solid #ffffff;   
    background: #e41e25 !important;
}

.order-summary-box .order-summary-title {
    background: linear-gradient(to right, #e41e25, #e41e25);
    -webkit-background-clip: text;
}

.box-styling:hover, .box-styling:focus, .box-styling:active {
    border: 2px solid #ffffff;
    background: linear-gradient(95deg, #e41e25, #e41e25);
    cursor: pointer;
}

.meal-box-main.selected .meal-box{
    background: linear-gradient(95deg, #e41e25, #e41e25);
}

.box-styling, .meal-box, .meal-box-main:hover .meal-box{
    background: transparent;
}

ul.stepper li a .circle {
    background: rgba(255, 255, 255, 0.5) !important;
}

ul.stepper li a .label, ul.stepper li a .label {
    font-weight: 600;
    color: #fff !important;
    font-size: 17px;
}

ul.stepper li.active a .label {
    color: #fff !important;
}

.stepper-horizontal li:not(:first-child):before {
    background-color: rgba(255, 255, 255, 0.3) !important;
}

.stepper-horizontal li:not(:last-child):after {
    background-color: rgba(255, 255, 255, 0.3) !important;
}

.product-subtitles {
    color: #fff;
}

.btn-primary-color {
    background: #e41e25;
    color: #fff;
}

.order-summary {
    /*font-size: 13px;*/
    /* align-self: flex-start; */
    /* height: calc(100vh - 232px); */
    position: sticky;
    top: 98px;
}






span.text-center.msg{
    color: #fff !important;
}


/*navbar*/

.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus, .navbar-inverse .navbar-nav > li > a:active{
    color: #e41e25;
    border-bottom: 4px solid #e41e25;
}


.navbar{
    z-index: 199 !important;
}

@media screen and (max-width: 767px){
.navbar-inverse .navbar-toggle {
    color: #e43630 !important;
}}


/*home*/

.form-row{
    margin-left: -15px;
    margin-right: -15px;
    display: flex;
    justify-content: center;
}

.progress-bar .rotate{
    background-color: #e41e25 !important;
}

.progress-bar .right{
    background-color: #e41e25 !important;   
}

.howitworks-step{
    background: #e41e25;    
}

.how-it-works-box .homepage-title b{
    color: #232122;
}

.homepage-title b{
 color: #fff;   
}

.home, .about-us-box{
    background: #fff;
}

button.order-button{
    background-color: #e41e25;
    border: 1px solid #ddd;
}

.owl-carousel {
    display: block !important;
}

.client-profile {
    width: 50px;
}

.wcs-main-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
}

.wcs-box {
    width: 100%;
}

.our-services img {
    width: 60px;
    height: 60px;
    object-fit: contain;
}

.img-works{
    width: 200px;
    height: 130px;
    object-fit: contain;
}

.client-profile {
    width: 50px;
    height: 50px;
    object-fit: contain;
    border-radius: 50%;
}

.wcs-box h4.client-name{
    color: #e41e25;
}

#location, .ti-location-pin, .address-box a{
    color: #e41e25 ;
}

.review-cal{
    color: #e41e25;
}

.review-cal:hover{
    color: #e41e25;
    transition: all .1s ease-in-out;
    transform: translateY(-2px);
    border-bottom: 2px solid #e41e25;
}

.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a{
    background: #e41e25 !important;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight{
    background: #e41e25 !important;   
}

.text-muted{
    color: #fff;
}

.align-items-center{
    margin-top: 10px;
}

/*input[type=text], input[type=password]{
    width: 150px;
}*/

.product-plan-date p{
    color: #fff;
}



/*cart page*/



.address-info.selected button{
    background: #e41e25 ;
}

.add-title-name span{
    color: #e41e25 !important;
}

h4, .h4{
    color: #fff;
}

.payment .tab-content{
    background: transparent !important;
}

h4 small, h4 .small, .h4 small, .h4 .small, h5 small, h5 .small, .h5 small, .h5 .small, h6 small, h6 .small, .h6 small, .h6 .small{
    color: #fff;
}

.main-payment-box{
    padding: 20px;
}

button.small-btn{
    background-color: #e41e25;
}

button.small-btn:hover, button.resendotp-btn:hover{
 background-color: #ad1c21;   
}

.control input:checked ~ .control__indicator{
    background: #e41e25;
}

.control:hover input:not([disabled]):checked ~ .control__indicator, .control input:checked:focus ~ .control__indicator{
 background: #e41e25;   
}

/*.cart-address-box {
    background-color: transparent;
    height: 430px !important;
    overflow-y: auto !important;
}*/

.promo-code-btn {
    top: auto;
    right: 18px;
}

.text-warning{
    color: #e41e25;
}

p{
    color: #fff;
}

button.pay-btn, button.pay-btn:hover{
    background: #e41e25;
}

button.other-btn, button.other-btn:hover{
    background: #e41e25;
    color: #fff;
}

.order-summary .add-extra span{
    color: #e41e25;
}

.order-summary .add-extra{
    color: #e41e25;
}
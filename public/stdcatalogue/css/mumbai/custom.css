@charset "UTF-8";
/*! normalize.css v3.0.2 | MIT License | git.io/normalize */

.dn {
    display: none;
}

/* captcha on contact us page */
.captcha{
    position: absolute;
    z-index: 9;
    padding: 8px 10px 5px;
    width: 100%;
    font-size: 23px;
    text-decoration: line-through;
}
.cms-height{
  min-height: 350px;
}
/* order timing on home page */
.progress-bar div span { 
    position: absolute;
    font-size: 22px;
    line-height: 35px;
    height: 180px;
    width: 180px;
    left: 10px;
    top: 10px;
    text-align: center;
    border-radius: 50%;
    background-color: white;
    padding-top: 57px; 
}

.promo-checked{
    background: #ee2e34;
    border: 2px solid #ee2e34;
}

.promo-code-btn1 {
    position: absolute !important;
    top: 0px;
    background-color: #656d78;
    color: #ffffff;
    padding: 8.5px 12px;
    font-size: 14px;
    border-radius: 0;
    border: 2px solid #656d78;
    right: -60px;    
}

.promo-btn {
    top: 0px;
    background-color: #656d78;
    color: #ffffff;
    padding: 8.5px 12px;
    font-size: 14px;
    border-radius: 0;
    border: 2px solid #656d78;
    right: -60px;    
}

.capitalize{
    text-transform: capitalize;
}

.static-box{
  background-color: #f5f7fa; 
  text-align: left; 
  padding: 20px; 
  border: 1px solid #ddd;
}

/*Commented by hemant as this rule is already defined in combine.css*/

/*.what-client-say-box {
background-color: #000;
background-image: url(../sandwich/images/banner3.jpg) !important;
z-index: 1;
position: relative;
border-radius: 0;
background-attachment: fixed !important;
background-position: center;
background-repeat: no-repeat;
background-size: cover;
padding-top: -50px !important; 
}*/

.footerPad{
    padding-bottom: 50px;
}

button.small-btn.itemAdded{
    background-color: #656d78;
}

.error-border{
    border: 1px solid red !important;
}

.add-amount-modal h4,.add-amount-modal h5{
    color: #656d78 !important;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
    border: 1px solid #c5c5c5 !important;
    background: #f6f6f6 !important;
    font-weight: normal !important;
    color: #454545 !important;  
   }

.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
   color: #f6f6f6 !important; 
  background: #20a464 !important;
  border: none !important;
 }

.sub-footer a.footer-btn {
    background-color: #656d78 !important;
    font-size: 16px !important;
}

@media screen and (max-width: 767px){
.add-extra-items {
    border-bottom: none !important;
    padding-bottom: 0;
}
.select2-container{ 
    width: 200px !important;
}
}

.ui-datepicker-calendar tr {
  background: #f6f6f6 !important;
}  

/*-----------------------------EXTRA STYLE START BY ABDUL-----------------------------*/

header {
  position: sticky;
  top: 0px;
  z-index: 99;
}

html{scroll-behavior:smooth}

.navbar-custom{
    background-color: #ffffff;
    -webkit-box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 20%);
    -moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 20%);
    min-height: auto;
}
.navbar-custom .navbar-toggle .icon-bar {
    background-color: #ffffff;
}
.navbar-custom .navbar-logo{
    height: 50px;
    padding: 0px;
    margin: 5px 0px !important;
}
.navbar-custom .navbar-logo img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.topbar{
    /*background: #f5f5f5;*/
    /*padding: 5px 0;*/
    /*border-bottom: 1px solid #eaeaea;*/
}
.topbar .topbar-inner{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
}
/*.topbar .navbar-right{
    margin-right: 0px !important;
    display: flex;
    align-items: center;
    justify-content: center;
}*/
.topbar .h-divider{
    height: 22px;
    width: 1px;
    background: rgba(0, 0, 0, 0.2);
    margin: 0 15px;
}
.topbar .topbar-buttonwrap{
    /*padding-left: 15px;*/
}
.topbar .topbar-contactwrap{
    /*padding-left: 15px;*/
    display: flex;
    align-items: center;
}
.topbar .topbar-contactwrap{
}
.topbar .topbar-contactwrap .btn{
    color: #3b4037;
    text-decoration: none;
    font-weight: 400;
    display: flex;
    align-items: center;
}
.topbar .topbar-contactwrap i{
    padding-right: 10px;
    font-size: 14px;
    color: #656d78;
}

.social-links{
    list-style: none;
    display: inline-flex;
    padding: 0 0 0 0;
    margin: 0;
}
.social-links li{
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.social-links li:not(:last-child){
    margin-right: 10px;
}
.social-links li a{
    padding: 10px;
    color: #656d78;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.navbar-custom .navbar-nav{}

.navbar-custom .navbar-nav li.active a{
    color: #ee2e34;
    font-weight: 600;
}
.navbar-custom .navbar-nav li a{
    color: #494848;
    letter-spacing: 1px;
    padding: 5px 15px;
}

.btn-order{
    background-color: #ee2e34;
    color: #ffffff !important;
    padding: 7px 10px !important;
    border: none;
    cursor: pointer;
    font-size: 14px;
    border-radius: 0px;
    letter-spacing: 1px;
}
.btn-order:hover{
    background-color: #75a83c !important;
    color: #fff !important;    
}
.btn-login, .btn-signup{
    /*text-transform: uppercase;*/
    color: #ffffff;
    font-weight: 500;
    letter-spacing: 1px;
    border-radius: 0px;
    padding: 3px 10px;
}
.btn-login{
    background-color: #ee2e34;
}
.btn-signup{
    background-color: #6c757d;
}
.btn-login:hover{
    background-color: #75a83c;
    color: #fff;    
}
.btn-signup:hover{
    background-color: #525960;
    color: #fff;
}
.navwrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.navwrap-inner{}

@media screen and (min-width: 1200px){

/*.navbar-collapse.collapse{
    padding: 0px;
}*/
}

@media screen and (min-width: 768px){
    .navbar-collapse.collapse{
        padding: 0px;
    }
    .navbar-toggle{
        display: none;
    }
    .navbar-custom .navbar-nav li{
        height: 50px;
        display: flex;
        align-items: center;
        position: relative;
    }
    
    .navbar-collapse.collapse {
        display: block !important;
        visibility: visible !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important;
    }
    .navbar-custom .navbar-nav{
        float: right;
        margin: 0px; 
    }
    .navbar-custom .navbar-nav li{
        float: left;
    }
    .navbar-header{
        float: left;
    }
    .topbar{
        padding: 5px 0;
        border-bottom: 1px solid #eaeaea;
    }
    .mealplan-card{
        border-radius: 10px 0 0 10px;
    }
    .mealplan-card .mealplan-image{
        flex-basis: 55%;        
    }
    .mealplan-card .mealplan-content{
        flex-basis: 45%;
        min-height: 200px;
    }
}
@media screen and (max-width: 767px){
    .navbar-custom .navwrap{
        flex-direction: column;
        width: 100%;
    }
    .navbar-custom .navwrap-inner{
        width: 100%;
    }
    .navbar-custom .topbar{
        padding: 5px 10px; 
        border-top: 1px solid #eaeaea;
    }
    .navbar-custom .navbar-header{
        margin: 0px;
        padding-left: 10px;
        width: 100%;
    }
    .navbar-custom .container-fluid{
        padding: 0px;
    }
    .navbar-custom .navbar-nav li a{
        padding: 10px 15px;
    }
    .navbar-custom .navbar-toggle{
        margin-right: 0px;
        margin-top: 0px;
        margin-bottom: 0px;
        height: 50px;
        background: #ee2e34;
        border: 0;
        border-radius: 0;
    }
    .topbar .topbar-buttonwrap{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .topbar .topbar-contactwrap{
        display: flex;
        align-items: center;
        justify-content: center;
        
    }
    
    .topbar .topbar-buttonwrap .btn + .btn{
        margin-left: 10px;
    }
    .navbar-custom .navbar-logo{
        height: 40px;
    }
   .navbar-collapse.in{
       display: block !important;
   }
   .navbar-collapse{
       border-top: 1px solid #eaeaea;
   }
   .navbar-custom .navbar-nav{
        padding-left: 10px;
        padding-right: 10px;
        margin: 7.5px 0px;
   }
   .navbar-custom .navbar-nav li a{
        width: 100%;
   }
   .mealplan-card{
        border-radius: 10px 10px 0 0;
        flex-direction: column;
   }
   .mealplan-image{
        flex-basis: auto;
        height: 180px;
   }
   .mealplan-content{
        flex-basis: auto;
   }
}
@media screen and (max-width: 567px){
    .topbar .topbar-inner{
        justify-content: center;
    }
}

.mealplan-box{
    padding-top: 50px;
    padding-bottom: 70px;
}

.gallery-box {
    background-color: #ffffff;
    padding-top: 90px;
    padding-bottom: 30px;
}
.gallery-card{
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    margin-bottom: 25px;
    border-radius: 10px;
}
.gallery-card:hover .gallery-info{
    transform: translate(0px, 0px);
    transition: all 0.2s linear;
}
.gallery-card:hover .gallery-thumb img{
    transform: scale(1.2);
    transition: all 0.25s linear;
}
.gallery-card .gallery-thumb{
    overflow: hidden;
    position: relative;
    width: 100%;
    height: 250px;

}
.gallery-card .gallery-thumb img{
    width: 100%;
    height: 100%;
    object-fit: cover; 
    transition: all 0.25s linear;
}
.gallery-card .gallery-info{
    transition: all 0.25s linear;
    position: absolute;
    bottom: 0px;
    height: 100%;
    width: 100%;
    background: rgba(0,0,0,0.5);
    transform: translate(0px, 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}
.gallery-card .gallery-info a{
    background: #ffffff;
    width: 54px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
}
.gallery-card .gallery-info a i{
    font-size: 2rem;
    color: #000;
}
.mealplan-card{
    position: relative;
    display: flex;
    align-items: stretch;
    flex-wrap: wrap;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0px 0px 13px 7px rgb(0 0 0 / 10%);
}
.mealplan-card .mealplan-image{
    background-color: #545454;
    overflow: hidden;
}
.mealplan-card .mealplan-image img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.mealplan-card .mealplan-content{
    background: white;
    padding: 4px;
    border: 5px solid #ee2e34;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
}
.mealplan-card .mealplan-content .mealplan-text{

}
.mealplan-title{
    font-weight: bold;
    font-size: 22px;
    letter-spacing: 1px;
    line-height: normal;
    color: #424242;
}
.mealplan-subtitle{
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 1px;
    line-height: normal;
}
.mealplan-amount{
    border-top: 1px solid #eaeaea;
    padding: 10px 0 0 0;
    font-size: 26px;
    letter-spacing: 1px;
    font-weight: bold;
    color: #424242;
    width: 100%;
}
.mealplan-action{
    border-top: 1px solid #eaeaea;
    padding: 10px 0 0 0;
    width: 100%;
    /*font-size: 26px;
    letter-spacing: 1px;
    font-weight: bold;
    color: #424242;*/
}
.appdownload-box{
    padding-top: 225px;
    background-color: #ed2025;    
}
.appdownload-inner{
    background-color: #ed2025;    
}
.appmockup{
    margin-top: -110px;
    position: relative;
    width: 350px;
    margin-left: inherit;
}
.appmockup img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}
.app-info{
    padding: 20px 0;
}
.app-heading{
    text-transform: uppercase;
    color: #ffffff;
    font-size: 26px;
    font-weight: bold;
    letter-spacing: 1px;
    margin-bottom: 15px;
}
.app-desc{
    color: #ffffff;
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 1px;
    line-height: normal;
}
.actionbutton-wrap{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 25px 0;
}
.actionbutton-wrap .btn-appdownload{
    margin: 0 10px 0 0;
    padding: 0;
}
.actionbutton-wrap .btn-appdownload img{
    height: 45
}
/*-----------------------------EXTRA STYLE START BY ABDUL-----------------------------*/

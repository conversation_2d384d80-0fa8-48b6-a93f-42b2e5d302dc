@charset "UTF-8";
/*! normalize.css v3.0.2 | MIT License | git.io/normalize */

.otp-field input[type=text], input[type=password] {
  margin: 8px 0 8px 0 !important; }

.otp-alert .alert-success {
  text-align: left; }

.otp-alert .close {
  font-size: 22px; }


.otp_btn{
    background-color: #e9573f;
    color: #ffffff;
    padding: 9px 15px !important;
    margin: 8px 0 20px !important;
    border: none;
    cursor: pointer;
    width: 25%; 
    display:right;
}

.cart-panel{
  padding: 5px;
  background-color: #f5f7fa;
  text-align: center; }

.close-panel{
  float: right; }

.side-cart a.cart-trash, .side-cart a.cart-trash:hover, .side-cart a.cart-trash:active {
  color: #e9573f !important;
  float: right;
  margin-top: 5px; }

.innerpage .pgheading {
    color: #707074;
    font-size: 20px;
    font-family: opensans-bold-webfont;
    text-align: center;
    margin-top: 20px;
}
.greencheck{
    color: #8CC152;
    margin-left: 5px;
}
.redcheck{
    color:#DA4453;
     margin-left: 5px;
}

.chosen-container{position:relative;display:inline-block;vertical-align:middle;font-size:13px;zoom:1;*display:inline;-webkit-user-select:none;-moz-user-select:none;user-select:none}.chosen-container *{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}

.dn {
    display: none;
}

.bookhis{
    color: white;
}
/*# sourceMappingURL=custom.css.map */
.cart-panel{
  padding: 20px 15px 15px;
  display: none; }

.cart-panel .form-group .select {
  margin-bottom: 0; }

.cart-panel .form-group{
  margin-bottom: 0; }

.cart-panel input[type=text], input[type=password]{
  margin: 0; }

.cart-panel p{
  margin: 10px 0 10px;
  text-align: left; }

.mealsnot{
   margin: auto;
   display: table;
}
.cart-total-box{
    background-color: #f5f7fa;
    padding: 0;
    margin: 0;
    margin-bottom: 100px;
}
.main-payment-box .control{
    padding-right: 0;
}

#myModal8 .changed-meal .nonveg-icon {
  position: static;
  margin-top:0; 
}
<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20161116 at Sun Jul 20 06:58:00 2014
 By <PERSON> W<PERSON>rting
Copyright (c) 2010 by Typemade. All rights reserved.
</metadata>
<defs>
<font id="JosefinSans" horiz-adv-x="482" >
  <font-face 
    font-family="Josefin Sans"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 0 0 0 0 0 0 0 0"
    ascent="750"
    descent="-250"
    x-height="400"
    cap-height="714"
    bbox="-59 -242 949 955"
    underline-thickness="50"
    underline-position="-100"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="300" 
 />
    <glyph glyph-name=".notdef" horiz-adv-x="300" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="300" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="246" 
 />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="286" 
d="M99 575h55v-179h119v-50h-119v-346h-55v346h-80v50h80v179z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="531" 
d="M491 200q0 -44 -15.5 -82t-43.5 -66t-66 -44t-83 -16q-56 0 -99.5 26t-60.5 64q0 -10 0.5 -19.5t0.5 -20.5v-42h-55v766h55v-450q19 37 61 65t100 28q45 0 82.5 -15.5t65 -43t43 -66t15.5 -84.5zM432 200q0 33 -11 61.5t-31.5 49t-48.5 32.5t-61 12t-61.5 -12
t-49.5 -32.5t-33 -48.5t-12 -61t12 -61t33 -49t49.5 -33t61.5 -12t61 12t48.5 33t31.5 49t11 60z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="195" 
d="M125 0h-55v766h55v-766z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="770" 
d="M69 400h47q1 -9 1.5 -23.5t1.5 -30t1.5 -29t0.5 -20.5q22 50 62.5 80.5t100.5 31.5q56 0 90 -29.5t40 -77.5q23 48 62.5 77t98.5 30q60 0 94.5 -33t36.5 -87v-289h-55v270q-2 34 -24 58t-66 26q-31 0 -57.5 -10.5t-46 -29t-30.5 -44t-12 -55.5v-215h-55v270q-2 34 -24 58
t-65 26q-32 0 -58.5 -10.5t-46.5 -29.5t-31 -45t-11 -57v-212h-55v400z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="191" 
d="M56 545q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29zM123 0h-55v400h55v-400z" />
    <glyph glyph-name="h" unicode="h" 
d="M68 766h55v-463q20 46 61.5 75.5t101.5 30.5q60 0 95 -33t37 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v766z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="414" 
d="M348 79l34 -41q-27 -22 -60.5 -34t-71.5 -12q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 66.5t67 44.5t82.5 16.5q38 0 72 -12t61 -34q-8 -11 -17 -20.5t-18 -20.5q-41 33 -98 33q-33 0 -61.5 -12t-49.5 -32.5t-33 -49t-12 -61.5q0 -32 12 -60t33 -49
t49.5 -33t61.5 -12q57 0 98 33z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="531" 
d="M95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM40 200q0 45 16 83.5t43.5 66t65 43.5t81.5 16q31 0 57 -8t46.5 -21t35 -30t22.5 -34q0 10 0.5 23
t0.5 25.5t0.5 22.5t0.5 13h53v-592h-55v233q0 11 1 21.5t1 20.5q-20 -37 -62 -64t-102 -27q-46 0 -83.5 16.5t-64.5 44.5t-42 66t-15 81z" />
    <glyph glyph-name="u" unicode="u" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="294" 
d="M276 340q-30 0 -57.5 -9.5t-48.5 -26.5t-33.5 -41.5t-12.5 -54.5v-208h-55v400h47l5 -103q12 26 31 46.5t41.5 35t47 22.5t46.5 8z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="531" 
d="M432 200q0 33 -11 61.5t-31.5 49t-48.5 32.5t-61 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61t12 -61t33 -49t49.5 -33t61.5 -12t61 12t48.5 33t31.5 49t11 60zM491 200q0 -44 -15.5 -82t-43.5 -66t-66 -44t-83 -16q-56 0 -99.5 26t-60.5 64q0 -10 0.5 -19.5t0.5 -20.5
v-234h-55v592h53q0 -3 0.5 -13t0.5 -22.5t0.5 -25.5t0.5 -23q19 37 61 65t100 28q45 0 82.5 -15.5t65 -43t43 -66t15.5 -84.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="529" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM230 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM215 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="191" 
d="M123 0h-55v400h55v-400zM69 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5zM248 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM221 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="529" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM317 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM138 517q0 17 12 29t28 12
q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM287 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM108 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29
q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5zM308 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM129 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5
t-12 29.5z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM305 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5
zM126 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="529" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM187 617q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM163 617q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="191" 
d="M123 0h-55v400h55v-400zM36 617q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5zM169 617q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM201 617q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="230" 
d="M155 87l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="208" 
d="M64 41q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="296" 
d="M28 345v54h69v118q0 122 58.5 183.5t155.5 65.5v-53q-74 -2 -116.5 -53.5t-42.5 -142.5v-118h131v-54h-131v-345h-55v345h-69z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="530" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM179 512q0 31 22 53t54 22t54 -22t22 -53q0 -32 -22 -54t-54 -22t-54 22t-22 54zM214 511q0 -17 12 -29t29 -12t29 12t12 29t-12 29t-29 12
t-29 -12t-12 -29z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="427" 
d="M214 -24q-28 53 -53 106t-49 106t-48.5 106t-51.5 106h59q30 -60 51 -105.5t36.5 -82t28.5 -68t27 -63.5q14 32 26.5 63.5t28 68t36.5 82t51 105.5h59q-54 -107 -100.5 -212t-100.5 -212z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="191" 
d="M49 -192q-11 10 -20 19.5t-19 20.5q27 26 42.5 62.5t15.5 86.5v403h55v-403q0 -63 -20 -110.5t-54 -78.5zM57 545q0 17 12.5 28.5t27.5 11.5q16 0 28 -11.5t12 -28.5q0 -18 -12 -29t-28 -11q-15 0 -27.5 11t-12.5 29z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="332" 
d="M279 362l-30 -39q-8 11 -31.5 20.5t-47.5 9.5q-10 0 -21.5 -3t-21 -9.5t-16 -17t-6.5 -25.5q0 -23 14 -36.5t35 -23.5t45.5 -19t45.5 -22t35 -33.5t14 -52.5q0 -50 -34 -84.5t-97 -34.5q-34 0 -69.5 12t-57.5 38q4 4 10 11t11 13t8.5 10.5t4.5 4.5q3 -5 13 -11t23 -11.5
t28 -9.5t29 -4q12 0 25.5 3.5t25 11.5t18.5 20t7 29q0 23 -14 36.5t-35 23.5t-46 18.5t-46 21.5t-35 34t-14 54q0 30 11 50.5t28 34t38.5 19.5t41.5 6q26 0 57 -11t54 -34z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="413" 
d="M339 410l33 -34q-38 -35 -72.5 -68.5t-72.5 -68.5l174 -239h-64l-149 202l-65 -60v-142h-55v766h55v-558q56 52 108 100.5t108 101.5z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="448" 
d="M160 -192h-58q13 27 23.5 50.5t20.5 47.5t21.5 50.5t26.5 59.5q-46 92 -90 191.5t-90 192.5h59q30 -60 52 -108t39.5 -87.5t31.5 -73.5t28 -66q14 32 27.5 66t31 73.5t39.5 87.5t52 108h59z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="497" 
d="M38 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5q32 0 61 -9t54 -25l33 39l30 -25l-33 -39q30 -29 47 -67.5t17 -83.5q0 -43 -16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-36 0 -69 10.5t-59 30.5q-13 -15 -23.5 -27t-17.5 -21l-28 27q7 8 17 20.5t23 26.5q-25 28 -39 63.5
t-14 77.5zM156 75q19 -14 42 -21.5t49 -7.5q32 0 60.5 12t49.5 33t33 49t12 60t-12 60t-33 49q-23 -26 -48.5 -56t-51.5 -60.5t-52 -60.5t-49 -57zM93 200q0 -57 35 -98q23 26 48.5 55.5t51.5 60t51.5 60t47.5 55.5q-36 22 -80 22q-32 0 -60 -12t-49 -33t-33 -49.5
t-12 -60.5z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="646" 
d="M295 314l-39 86h49q50 -111 82.5 -186.5t56.5 -132.5q12 36 25.5 70t29.5 71t35.5 80t43.5 98h55l-189 -424q-34 72 -62.5 142t-62.5 142l-120 -284q-47 107 -92.5 212t-91.5 212h55q24 -55 42.5 -98.5t33.5 -80.5t28 -70.5t25 -69.5q8 21 17 43t20 49t25 61.5t34 79.5z
" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="401" 
d="M166 207q-34 48 -70 96.5t-71 96.5h68q28 -42 54 -76.5t55 -75.5q28 41 54 75.5t54 75.5h62q-18 -26 -34.5 -50t-33.5 -47t-34.5 -47t-35.5 -51q35 -48 75 -102t74 -102h-69q-30 41 -59.5 78.5t-58.5 78.5q-29 -41 -56 -78t-56 -79h-62q35 52 72 103.5t72 103.5z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="404" 
d="M371 0h-345l249 352h-169q-33 0 -52.5 14.5t-19.5 44.5v27h50v-13q0 -14 8 -19.5t25 -5.5h258l-245 -346h241v-54z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="667" 
d="M357 97l-3 -10q20 -20 47 -30.5t58 -10.5q32 0 61 11t49 30l41 -39q-29 -26 -68 -41t-81 -15q-39 0 -74 12.5t-62 35.5q-25 -24 -62 -36t-81 -12q-38 0 -65.5 10t-45.5 26.5t-26.5 37.5t-8.5 44q0 28 12 48t30.5 33.5t41.5 20t46 6.5h84q6 62 41 107q-26 29 -93 29
q-44 0 -72 -9t-50 -23l-24 44q28 20 67 31.5t88 11.5q44 0 75 -11.5t49 -32.5q26 21 59 33t69 12q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q8 -19 19 -33zM576 282q-17 34 -48 53.5t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5
t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM88 115q0 -20 9 -33.5t23 -21.5t32 -11t36 -3q70 0 102 32q-31 40 -39 96h-51q-33 0 -54.5 -5t-34 -13.5t-18 -19t-5.5 -21.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="646" 
d="M563 0q-20 46 -47 113t-56 142h-274q-29 -75 -56 -142t-47 -113h-68q15 36 40 95.5t54 130.5t60 147.5t60 147.5t53.5 130t40.5 96q15 -37 40 -96t54 -130t60.5 -147.5t60.5 -147.5t53.5 -130.5t40.5 -95.5h-69zM313 572q-20 -47 -47.5 -115t-56.5 -143h228
q-30 75 -57 143t-47 115q-2 4 -5 13.5t-5 17.5q-2 -8 -5 -17.5t-5 -13.5z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="191" 
d="M123 0h-55v400h55v-400zM147 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM-32 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="448" 
d="M160 -192h-58q13 27 23.5 50.5t20.5 47.5t21.5 50.5t26.5 59.5q-46 92 -90 191.5t-90 192.5h59q30 -60 52 -108t39.5 -87.5t31.5 -73.5t28 -66q14 32 27.5 66t31 73.5t39.5 87.5t52 108h59zM210 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="448" 
d="M160 -192h-58q13 27 23.5 50.5t20.5 47.5t21.5 50.5t26.5 59.5q-46 92 -90 191.5t-90 192.5h59q30 -60 52 -108t39.5 -87.5t31.5 -73.5t28 -66q14 32 27.5 66t31 73.5t39.5 87.5t52 108h59zM280 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5
t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM101 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="581" 
d="M170 652v-263h120q65 0 98 38.5t33 97.5q0 67 -40 97t-104 30h-107zM170 330v-268h122q34 0 64 8.5t52.5 25t35.5 41.5t13 58q0 36 -15 62t-40.5 42t-58 23.5t-66.5 7.5h-107zM102 0v714h167q50 0 90.5 -10t69.5 -32t44.5 -57t15.5 -85q0 -29 -6 -55.5t-19 -49t-34 -39
t-52 -25.5q28 -6 54.5 -19t47 -34t33 -50.5t12.5 -68.5q0 -48 -19 -84t-50.5 -59t-72.5 -34.5t-85 -11.5h-196z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="714" 
d="M103 714h166q108 0 183 -33t122 -85t68 -116.5t21 -127.5q0 -81 -29 -146t-78 -111t-114 -70.5t-138 -24.5h-201v714zM297 65q62 0 116.5 20t95 57.5t63.5 91.5t23 122q0 62 -21.5 115.5t-61.5 92.5t-96 61.5t-126 22.5h-118v-583h125z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="612" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="748" 
d="M102 0v714h69v-322h406v322h70v-91.5v-124t-0.5 -141t-0.5 -141t-0.5 -124.5t-0.5 -92h-68v327h-406v-327h-69z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="273" 
d="M102 714h69v-714h-69v714z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="297" 
d="M-36 -146v64q36 2 66 15.5t51.5 38.5t34 62t12.5 85v595h69v-598q0 -62 -18 -110.5t-49.5 -81.5t-74 -51t-91.5 -19z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="623" 
d="M537 0q-75 95 -144 184.5t-144 185.5q-18 -18 -36 -35t-37 -34q0 -78 0.5 -150.5t0.5 -150.5h-75v714h75v-300q0 -8 -1 -16t-2 -17q5 7 9.5 12t9.5 12q80 75 157 154t157 155h88q-73 -74 -146 -148.5t-147 -148.5q84 -107 161 -208.5t160 -208.5h-86z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="898" 
d="M102 746q19 -28 47 -68.5t61 -87.5t68 -96l67.5 -94.5t59.5 -83.5t44 -62q9 13 32.5 47t55 79t68.5 97.5t72.5 103t67 94t51.5 71.5v-746h-72v509q0 9 1 19t1 17q-17 -24 -40.5 -57.5t-49.5 -71.5t-53 -77.5t-52 -76.5t-46.5 -69t-35.5 -53q-14 18 -38 51.5t-52.5 75
t-59.5 86t-57.5 83.5t-47 68.5t-27.5 40.5q0 -7 1.5 -17t1.5 -19v-509h-68v746z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="812" 
d="M710 714v-747q-29 35 -71.5 83.5t-92 103.5t-102 112.5t-101.5 112.5t-89.5 102.5t-66.5 82.5q-3 4 -9 11.5t-11 14.5l3 -32v-558h-68v747q29 -37 71 -85t91.5 -102t101.5 -111t100.5 -110.5t90.5 -101t69 -82.5q2 -5 8 -12.5t11 -14.5l-3 33v553h68z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="551" 
d="M171 351h106q48 0 80 15.5t50.5 39t26 50t7.5 48.5q0 27 -10.5 53.5t-30.5 47t-51 33t-71 12.5h-107v-299zM103 0v714h173q45 0 87 -12.5t74.5 -38.5t52 -65t19.5 -93q0 -41 -13 -80.5t-40.5 -70t-71.5 -49.5t-106 -19h-107v-286h-68z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="603" 
d="M102 0v714h173q45 0 87 -12.5t74.5 -38.5t52 -65t19.5 -93q0 -60 -27 -114t-89 -83l24 -40t33 -54.5t37.5 -61.5t37.5 -61t33 -53t24 -38h-83q-8 12 -21 33t-29 47t-34 55t-35 57t-32.5 53t-26.5 43q-10 -2 -21 -2h-22h-107v-286h-68zM170 351h106q48 0 80 16t50.5 39.5
t26 50t7.5 48.5q0 27 -10.5 53t-30.5 46.5t-51 33t-71 12.5h-107v-299z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="636" 
d="M353 249v-249h-69v249q-13 22 -34.5 58.5t-46.5 81t-52.5 93t-52.5 93t-46 81t-34 58.5h77q18 -30 44 -76t55.5 -97.5t59 -104.5t53.5 -97l12 -30l12 30q25 44 54 97t57.5 104.5t54 97.5t43.5 76h78z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="472" 
d="M228 491q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5
q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="483" 
d="M69 400h47l5 -103q22 50 65 81t103 31q59 0 93.5 -33t36.5 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v400zM250 499q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35
t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="553" 
d="M103 0v714h409v-63h-341v-270h301v-64h-301v-317h-68z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="564" 
d="M102 0v714h68v-649h380v-65h-448z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="645" 
d="M618 48q-32 -20 -85 -38t-123 -18q-83 0 -149.5 29t-113 78.5t-72 114.5t-25.5 138q0 79 29 147t79 117.5t117 77.5t143 28q27 0 54.5 -3.5t52.5 -10.5t46 -16t36 -20q-5 -8 -8 -13t-6 -10.5t-7 -12.5t-10 -18q-32 20 -74.5 30t-81.5 10q-63 0 -118 -24t-95.5 -65.5
t-64 -96t-23.5 -115.5q0 -58 21 -111.5t60 -94t93 -65t120 -24.5q29 0 56 4.5t50 12t40.5 15.5t27.5 16z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="833" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5q0 -93 -43.5 -170.5t-115.5 -125.5l182 5v-65h-313q-19 -4 -37.5 -6t-38.5 -2q-75 0 -141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64
t115 -23.5t115.5 23.5t95 64t64 95t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5t-115 -23.5t-94 -64.5t-63.5 -95.5t-23.5 -116.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="537" 
d="M492 714v-65h-190v-649h-69v649h-188v65h447z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="280" 
d="M216 62l-172 179q13 12 36.5 37t49.5 52t49.5 52t36.5 37v-67q-11 -11 -24 -25.5t-26.5 -30t-27 -30t-24.5 -25.5q11 -11 24.5 -25.5t27 -30.5t26.5 -31t24 -26v-66z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="559" 
d="M83 -15v79l306 176l-306 176v78q111 -63 220 -127t220 -127z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="559" 
d="M476 64v-79l-440 255q111 63 220 127t220 127v-78q-76 -44 -152.5 -87.5t-152.5 -88.5q76 -45 152.5 -88.5t152.5 -87.5z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="652" 
d="M581 184v-57h-510v57h510zM581 380v-58h-510v58h510z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="936" 
d="M238 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-349h55q19 0 38 9t34.5 29.5t25 52.5t9.5 78q0 67 -25 129t-70.5 108.5t-110.5 74.5t-145 28q-88 0 -156.5 -31.5t-116 -82.5t-72.5 -116.5t-25 -133.5
q0 -74 28.5 -139.5t77 -114.5t114.5 -77t141 -28q60 0 114.5 18t100.5 50l32 -38q-51 -40 -116.5 -61t-132.5 -21q-85 0 -160 32.5t-131.5 88.5t-89 131t-32.5 161q0 80 30 155t85.5 132.5t134 92t175.5 34.5q92 0 167 -32t127.5 -86t81 -125t28.5 -148q0 -59 -13 -100.5
t-35.5 -68.5t-52.5 -39.5t-63 -12.5h-107v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM293 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49
t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="477" 
d="M87 573l-51 18q8 24 23 50t38.5 47.5t56.5 35.5t78 14q42 0 78 -15.5t62.5 -42.5t42 -63t15.5 -76q0 -55 -26.5 -96.5t-72.5 -75.5q-16 -12 -32.5 -22t-30 -23t-22 -32t-8.5 -48v-79h-59v98q0 32 10.5 52t28 35t40 28.5t45.5 33.5q35 27 51 59.5t16 69.5q0 30 -10.5 55.5
t-28.5 44.5t-43.5 29.5t-55.5 10.5q-58 0 -93 -28.5t-52 -79.5zM208 9q-15 0 -27.5 11.5t-12.5 29.5q0 17 12.5 29t27.5 12q14 0 27 -12t13 -29q0 -18 -13 -29.5t-27 -11.5z" />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="253" 
d="M97 756h59v-589h-59v589zM127 13q-16 0 -28 12t-12 29q0 19 12 30t28 11q15 0 27.5 -11t12.5 -30q0 -17 -12.5 -29t-27.5 -12z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="528" 
d="M202 57h288v-57h-430q63 62 122.5 122t105.5 116t74 108.5t28 100.5q0 44 -13.5 74.5t-34 49t-44.5 27t-45 8.5q-42 0 -69.5 -15t-43.5 -36.5t-22 -46.5t-6 -44q0 -35 10.5 -62t29.5 -47l-35 -35q-15 11 -27 27.5t-20 36t-12.5 40t-4.5 40.5q0 40 13 76.5t38.5 63.5
t63 43t85.5 16q44 0 80 -16t62 -43.5t40 -65t14 -79.5q0 -53 -22 -104.5t-57 -101t-79 -98.5t-89 -98z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="298" 
d="M42 598v57h145v-655h-60v598h-85z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="474" 
d="M135 341q42 65 87.5 128.5t87.5 128.5h-239v57h336q-42 -61 -84.5 -122t-84.5 -123q37 -7 69.5 -24.5t56.5 -44.5t37.5 -61.5t13.5 -74.5q0 -44 -17 -82.5t-46.5 -67.5t-68.5 -46t-83 -17q-47 0 -87.5 19t-69.5 51l43 41q20 -25 49 -39.5t64 -14.5q32 0 61 12.5t50.5 34
t34 49.5t12.5 60q0 33 -12.5 61.5t-34 49.5t-50.5 33t-61 12q-35 0 -64 -20z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="528" 
d="M330 503q-24 -31 -50.5 -65.5t-53.5 -69.5t-53.5 -70t-50.5 -66h208v271zM8 175q93 121 190.5 244.5t190.5 243.5v-431h98v-57h-98v-175h-59v175h-322z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="465" 
d="M87 320v335h290v-57h-239v-195q14 6 28.5 10t32.5 4q47 0 86.5 -16.5t68 -45t44.5 -67t16 -81.5q0 -45 -17 -84t-46.5 -68.5t-69 -46t-83.5 -16.5q-42 0 -80 15.5t-67 44.5l43 39q20 -20 46.5 -31t57.5 -11q32 0 61 12.5t50.5 34t34 49.5t12.5 60t-12.5 60.5t-34 49.5
t-50.5 33t-61 12q-30 0 -59.5 -9t-51.5 -31z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="535" 
d="M53 205q0 51 15.5 97t41.5 86.5t60 76.5t71 67.5t73.5 58.5t68.5 50l37 -46q-45 -30 -86 -62t-74 -62t-57 -56.5t-35 -46.5q17 22 43 35.5t56 13.5q44 0 83 -16.5t68.5 -45.5t46.5 -67.5t17 -82.5t-17 -82.5t-46 -67.5t-68 -46t-83 -17q-40 0 -78.5 14.5t-69 41.5
t-49 66.5t-18.5 90.5zM113 205q0 -32 12 -60.5t33 -49.5t49 -33.5t60 -12.5t60.5 12.5t50 33.5t34 49.5t12.5 60.5t-12.5 60.5t-34 49.5t-50 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="461" 
d="M35 598v57h412q-71 -169 -138.5 -329.5t-135.5 -325.5h-68q60 143 123.5 294.5t126.5 303.5h-320z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="534" 
d="M168 506q0 -38 29 -63.5t70 -25.5t69 25.5t28 63.5q0 20 -7.5 38.5t-20.5 32t-31 21.5t-38 8t-38 -8t-31.5 -21.5t-21.5 -32t-8 -38.5zM113 205q0 -32 12 -60t32.5 -49.5t48 -34t59.5 -12.5t61 12.5t50.5 34t34 49.5t12.5 60t-12.5 60t-33.5 49t-49.5 33.5t-60.5 12.5
t-60 -12t-49 -33t-33 -49t-12 -61zM53 203q0 36 10 67t27.5 55t41 41.5t50.5 26.5q-35 18 -53 48.5t-18 64.5t12 62.5t33 49.5t49 33t60 12t60 -12t49 -33t33.5 -50t12.5 -62q0 -35 -18 -64.5t-52 -48.5q27 -9 51 -26.5t42 -42t28.5 -55t10.5 -66.5q0 -43 -16 -81t-45 -67
t-68.5 -46t-86.5 -17t-86 17t-67.5 46t-44 67t-15.5 81z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="209" 
d="M64 305q0 17 12.5 29t28.5 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28.5 11.5t-12.5 29.5zM64 41q0 17 12.5 29t28.5 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28.5 11.5t-12.5 29.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="249" 
d="M94 293q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM157 87q-23 -53 -44 -104t-43 -104h-53q20 53 39 104t39 104h62z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="336" 
d="M72 476v250h60l-4 -250h-56zM207 476v250h59l-4 -250h-55z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="575" 
d="M290 0l24 203h-126q-6 -51 -12.5 -101.5t-11.5 -101.5h-59q6 51 12 101.5t11 101.5h-101q2 14 3.5 28.5t2.5 28.5h103q4 32 7 63.5t8 63.5h-101l5 57h102l26 204h58q-6 -51 -12 -101.5t-13 -102.5h127l25 204h59q-7 -51 -13 -101.5t-12 -102.5h115l-6 -57h-116l-15 -127
h115l-7 -57h-115l-24 -203h-59zM210 387q-4 -32 -7.5 -63.5t-8.5 -63.5h127l15 127h-126z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="674" 
d="M448 112q0 -27 18 -45.5t46 -18.5t46 18.5t18 45.5t-18 45.5t-46 18.5t-46 -18.5t-18 -45.5zM512 -9q-25 0 -47.5 9.5t-39 26t-26 38.5t-9.5 48q0 25 9.5 46.5t26 38t39 26t47.5 9.5t47 -9.5t38.5 -26t26.5 -38t10 -46.5q0 -26 -10 -48t-26.5 -38.5t-38.5 -26t-47 -9.5z
M102 392q0 -27 18 -45.5t46 -18.5q27 0 45 18.5t18 45.5t-18 45t-45 18q-28 0 -46 -18t-18 -45zM166 271q-25 0 -47.5 9.5t-39 25.5t-26.5 38t-10 48q0 25 10 47t26.5 38.5t39 26t47.5 9.5q24 0 46.5 -9.5t39 -26t26.5 -38.5t10 -47q0 -26 -10 -48t-26.5 -38t-39 -25.5
t-46.5 -9.5zM154 0q75 127 150 252t150 252h64q-75 -127 -150 -252t-150 -252h-64z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="185" 
d="M82 573l-29 22q22 20 31.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="278" 
d="M45 294q0 75 14 143.5t37.5 127.5t53 106.5t61.5 80.5l48 -23q-29 -33 -57 -78.5t-50 -101t-35 -119.5t-13 -136t13 -136.5t35 -120t50 -101t57 -78.5l-48 -23q-32 34 -61.5 81.5t-53 106t-37.5 127t-14 144.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="278" 
d="M234 292q0 -76 -14 -144.5t-37 -127t-53 -106t-62 -81.5q-12 6 -24 11.5t-24 10.5q29 33 57 78.5t49.5 101t35 119.5t13.5 136t-13.5 136.5t-35 120t-49.5 101t-57 78.5l24 12t24 13q32 -34 62 -81.5t53 -106.5t37 -127.5t14 -143.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="391" 
d="M236 575q23 -13 46.5 -26t46.5 -27q-6 -9 -11.5 -18.5t-11.5 -19.5l-91 54l-2 -104h-40v104q-23 -13 -45 -25.5t-45 -26.5l-23 39l92 51l-91 54q8 17 20 35l92 -54v109h44q0 -27 -0.5 -54t-0.5 -55l94 53q5 -8 10 -17l10 -18z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="457" 
d="M199 51v154h-152v56h152v153h59v-153h152v-56h-152v-154h-59z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="535" 
d="M423 428q0 32 -12 60.5t-33 49.5t-49 33.5t-60 12.5t-60.5 -12.5t-50 -33.5t-34 -49.5t-12.5 -60.5t12.5 -60.5t34 -49.5t50 -33t60.5 -12t60 12t49 33t33 49.5t12 60.5zM482 428q0 -77 -33.5 -141.5t-83.5 -118.5t-107.5 -97.5t-105.5 -78.5q-9 11 -18.5 23t-17.5 23
q45 30 86 61.5t74 61.5t57 57t35 47q-17 -23 -43 -36t-57 -13q-44 0 -83 16.5t-68.5 45t-46.5 67.5t-17 83t17 82.5t46.5 67.5t68.5 46t83 17q39 0 77.5 -14.5t69 -41.5t49 -66.5t18.5 -90.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="246" 
d="M94 -234v940h58v-940h-58z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="307" 
d="M33 269v61q25 1 40.5 15t24.5 34t12 43.5t3 43.5v167q0 46 8.5 70.5t23.5 35.5t34.5 12t40.5 1h54v-57h-49q-31 0 -42 -22t-11 -85v-138q0 -37 -6 -62t-15.5 -42.5t-21 -27.5t-22.5 -17q11 -7 22.5 -17.5t21 -28t15.5 -42.5t6 -61v-138q0 -63 11 -85t42 -22h49v-57h-54
q-22 0 -41.5 1t-34 13t-23 39t-8.5 79v151q0 21 -3 44t-12 43.5t-24.5 34t-40.5 14.5z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="306" 
d="M273 330v-61q-27 -1 -42.5 -17t-24 -39t-10.5 -48.5t-2 -44.5v-151q0 -47 -9 -71t-24 -35t-34.5 -12t-40.5 -1h-53v57h49q15 0 25 5t16 17t8.5 32.5t2.5 52.5v152q0 32 7 55t17 38.5t21.5 25.5t20.5 16q-11 7 -22.5 17t-21.5 27.5t-16 42.5t-6 62v138q0 31 -2.5 52
t-8.5 33t-16 17t-25 5h-49v57h53q21 0 40.5 -1t34.5 -11.5t24 -35t9 -71.5v-153q0 -20 2 -45t10.5 -48t24 -39.5t42.5 -17.5z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="655" 
d="M57 330q0 66 18 126.5t53 106.5t86 73t116 27q67 0 117 -29t84 -76t50.5 -107.5t15.5 -124.5q0 -75 -21.5 -136.5t-58.5 -105.5t-86 -68t-106 -24q-56 0 -104.5 24.5t-85 68.5t-57.5 106t-21 139zM116 329q0 -60 17 -111.5t45.5 -88.5t66.5 -58.5t80 -21.5t80.5 20.5
t68 57.5t47.5 88.5t18 111.5q0 52 -13.5 102.5t-40 89.5t-65.5 63t-91 24q-53 0 -92.5 -24t-66 -63.5t-40.5 -89t-14 -100.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="314" 
d="M64 178v57h186v-57h-186z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="417" 
d="M39 -19q71 192 140.5 380.5t139.5 380.5h59q-71 -192 -140 -380.5t-140 -380.5h-59z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="424" 
d="M104 742l281 -761h-65l-281 761h65z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="683" 
d="M618 327v-284q-41 -24 -93 -37.5t-109 -13.5q-84 0 -151.5 27.5t-115 75.5t-73.5 113t-26 140q0 81 29.5 149.5t80 118.5t118 78t142.5 28q51 0 103.5 -13t93.5 -38l-34 -56q-33 20 -77.5 31.5t-86.5 11.5q-61 0 -115.5 -24t-96 -65t-65.5 -97t-24 -119q0 -60 21.5 -114
t60.5 -94.5t94 -64t122 -23.5q33 0 69.5 7.5t69.5 22.5v180h-154v60h217z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="648" 
d="M84 714q14 -34 32.5 -80t39.5 -98t43 -107.5t43 -107.5t39.5 -98t32.5 -80l10 -32l10 32q14 34 32.5 80t39.5 98t43 107.5t43 107.5t39.5 98t32.5 80h69q-16 -37 -40.5 -96t-53.5 -130t-60.5 -147.5t-60.5 -147t-54 -130t-40 -96.5q-15 37 -40 96.5t-54 130t-60 147
t-60 147.5t-54 130t-40 96h68z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="551" 
d="M477 677l-25 -55q-35 14 -77.5 24t-82.5 10q-36 0 -64.5 -9t-48.5 -25t-30.5 -36.5t-10.5 -44.5q0 -29 16 -49.5t42 -35.5t59 -27.5t67.5 -25.5t67.5 -29.5t59 -40.5t42 -57t16 -80t-17 -84.5t-49 -64t-78.5 -41t-105.5 -14.5q-42 0 -77.5 7.5t-63 18t-48 21.5t-32.5 19
l31 60q18 -11 40.5 -22t47 -19t50.5 -13t51 -5q27 0 59.5 7.5t60 24.5t46 43.5t18.5 64.5q0 47 -27.5 76t-68.5 49.5t-89 36.5t-89 37.5t-68.5 53.5t-27.5 83q0 36 14.5 69.5t42.5 58.5t68 40t90 15q28 0 57.5 -4.5t55.5 -11t47 -13.5t32 -12z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="634" 
d="M140 714h449l-458 -648h477v-66h-596q25 36 62 89.5t80 115.5t88.5 128t88.5 127.5t80 113.5t62 86h-352q-32 0 -53.5 13t-21.5 42v35h63v-13q0 -14 7 -18.5t24 -4.5z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="944" 
d="M926 712q-25 -62 -53.5 -134t-57.5 -146.5t-57 -146.5t-52 -134t-42.5 -110t-27.5 -73l-156 403q-2 4 -4.5 10t-3.5 11q-1 -2 -2 -6t-2 -7l-2 -8q-30 -78 -59 -152q-12 -32 -25.5 -65.5t-26.5 -66.5t-25 -64t-21 -56q-75 188 -145 373t-145 372h77q19 -49 41 -105.5
t44 -113.5t42.5 -111.5t37.5 -100t28.5 -78.5t15.5 -47l8 -30q0 3 4 14.5t5 14.5t6.5 18t13.5 37.5t18.5 50.5t21.5 57q26 68 57 152q-22 62 -40 113q-8 22 -15.5 43t-13.5 39t-10.5 30.5t-5.5 16.5h66q47 -121 87 -229q17 -46 35 -94t34 -91.5t29.5 -80.5t21.5 -61
q2 -8 4 -15t4 -15l8 30q3 15 15 48.5t29 79t38 100.5t43 111.5t44.5 112.5t41.5 104h72z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="595" 
d="M253 366q-11 17 -29 44.5t-39 61t-44.5 69t-44.5 68.5t-39 60.5t-29 44.5h84q15 -24 38.5 -61t49 -77t49 -77.5t39.5 -61.5l12 -24l12 24q15 24 38 61.5t48 77.5t48 77t38 61h81l-223 -351q12 -18 30.5 -47t41 -63.5t46.5 -71.5t46.5 -71.5t41 -63t30.5 -46.5h-85
l-163 256q-7 10 -17.5 28.5t-18.5 29.5q-3 -7 -6.5 -14t-5.5 -9l-180 -291h-84q12 18 30.5 47t41 64t46.5 72t46 72t41 64t30 47z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="684" 
d="M310 0v255h-123q-29 -75 -56 -142t-47 -113h-68q15 36 39 93t51.5 124.5t57 139.5t57 139.5t51 124t38.5 93.5h318v-62h-253v-261h213v-64h-213v-263h267v-64h-332zM310 562q-20 -47 -46.5 -113.5t-55.5 -138.5h102v252z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="813" 
d="M710 714v-747q-29 35 -71.5 83.5t-92 103.5t-102 112.5t-101.5 112.5t-89.5 102.5t-66.5 82.5q-3 4 -9 11.5t-11 14.5l3 -32v-558h-68v747q29 -37 71 -85t91.5 -102t101.5 -111t100.5 -110.5t90.5 -101t69 -82.5q2 -5 8 -12.5t11 -14.5l-3 33v553h68zM403 790
q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5
t-34 25.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="646" 
d="M563 0q-20 46 -47 113t-56 142h-274q-29 -75 -56 -142t-47 -113h-68q15 36 40 95.5t54 130.5t60 147.5t60 147.5t53.5 130t40.5 96q15 -37 40 -96t54 -130t60.5 -147.5t60.5 -147.5t53.5 -130.5t40.5 -95.5h-69zM313 572q-20 -47 -47.5 -115t-56.5 -143h228
q-30 75 -57 143t-47 115q-2 4 -5 13.5t-5 17.5q-2 -8 -5 -17.5t-5 -13.5zM248 843q0 31 22 53t54 22t54 -22t22 -53q0 -32 -22 -54t-54 -22t-54 22t-22 54zM283 842q0 -17 12 -29t29 -12t29 12t12 29t-12 29t-29 12t-29 -12t-12 -29z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="646" 
d="M632 0q-16 36 -40.5 95.5t-53.5 130.5t-60.5 147.5t-60.5 147.5t-54 130t-40 96q-16 -37 -40.5 -96t-53.5 -130t-60 -147.5t-60 -147.5t-54 -130.5t-40 -95.5h68q20 46 47 113t56 142h274q29 -75 56 -142t47 -113h69zM313 572q2 4 5 13.5t5 17.5q2 -8 5 -17.5t5 -13.5
q20 -47 47 -115t57 -143h-228q29 75 56.5 143t47.5 115zM293 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="613" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM294 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="274" 
d="M102 714h69v-714h-69v714zM99 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5zM370 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM312 760l91 134h59q-26 -34 -53 -67.5
t-54 -66.5h-43z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="646" 
d="M632 0q-16 36 -40.5 95.5t-53.5 130.5t-60.5 147.5t-60.5 147.5t-54 130t-40 96q-16 -37 -40.5 -96t-53.5 -130t-60 -147.5t-60 -147.5t-54 -130.5t-40 -95.5h68q20 46 47 113t56 142h274q29 -75 56 -142t47 -113h69zM313 572q2 4 5 13.5t5 17.5q2 -8 5 -17.5t5 -13.5
q20 -47 47 -115t57 -143h-228q29 75 56.5 143t47.5 115zM373 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM194 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5
t-12 29.5z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="613" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM389 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM210 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5
q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="274" 
d="M102 714h69v-714h-69v714zM188 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM9 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5zM461 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM282 813q0 17 12 29t28 12q15 0 27.5 -12
t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM408 813q0 17 12 29t28 12q15 0 27.5 -12
t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM229 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="646" 
d="M632 0q-16 36 -40.5 95.5t-53.5 130.5t-60.5 147.5t-60.5 147.5t-54 130t-40 96q-16 -37 -40.5 -96t-53.5 -130t-60 -147.5t-60 -147.5t-54 -130.5t-40 -95.5h68q20 46 47 113t56 142h274q29 -75 56 -142t47 -113h69zM313 572q2 4 5 13.5t5 17.5q2 -8 5 -17.5t5 -13.5
q20 -47 47 -115t57 -143h-228q29 75 56.5 143t47.5 115zM264 894q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="613" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM301 894q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="274" 
d="M102 714h69v-714h-69v714zM83 894q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5zM365 894q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM304 894q23 -34 45.5 -67.5t45.5 -66.5h-44
l-107 134h60z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="636" 
d="M353 249v-249h-69v249q-13 22 -34.5 58.5t-46.5 81t-52.5 93t-52.5 93t-46 81t-34 58.5h77q18 -30 44 -76t55.5 -97.5t59 -104.5t53.5 -97l12 -30l12 30q25 44 54 97t57.5 104.5t54 97.5t43.5 76h78zM289 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="249" 
d="M95 377h59v-589h-59v589zM125 531q15 0 27.5 -12t12.5 -29q0 -19 -12.5 -30t-27.5 -11q-16 0 -28 11t-12 30q0 17 12 29t28 12z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29q60 0 113.5 -18.5t98.5 -50.5q18 20 33 38.5t28 33.5l33 -30l-59 -70q55 -51 87 -120.5t32 -148.5q0 -75 -29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5q-64 0 -121 20.5t-104 56.5l-66 -78l-33 30l66 78
q-50 50 -78 116t-28 141zM231 120q38 -30 84 -46.5t98 -16.5q61 0 115.5 23.5t95 64t64 95t23.5 116.5q0 64 -25 120t-69 98zM117 356q0 -60 21.5 -112.5t59.5 -93.5l384 452q-36 26 -78.5 40t-90.5 14q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="477" 
d="M390 -34l51 -18q-8 -24 -23 -50t-38.5 -47.5t-56.5 -35.5t-78 -14q-42 0 -78 15.5t-62.5 42.5t-42 62.5t-15.5 76.5q0 55 26.5 96.5t72.5 75.5q16 12 32.5 22t30 23t22 32t8.5 48v79h59v-98q0 -32 -10.5 -52t-28 -35t-40 -29t-45.5 -33q-35 -27 -51 -59.5t-16 -69.5
q0 -30 10.5 -55.5t28.5 -44.5t43.5 -29.5t55.5 -10.5q58 0 93 28.5t52 79.5zM269 530q15 0 27.5 -11.5t12.5 -29.5q0 -17 -12.5 -29t-27.5 -12q-14 0 -27 12t-13 29q0 18 13 29.5t27 11.5z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="646" 
d="M632 0q-16 36 -40.5 95.5t-53.5 130.5t-60.5 147.5t-60.5 147.5t-54 130t-40 96q-16 -37 -40.5 -96t-53.5 -130t-60 -147.5t-60 -147.5t-54 -130.5t-40 -95.5h68q20 46 47 113t56 142h274q29 -75 56 -142t47 -113h69zM313 572q2 4 5 13.5t5 17.5q2 -8 5 -17.5t5 -13.5
q20 -47 47 -115t57 -143h-228q29 75 56.5 143t47.5 115zM315 793q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2
q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5zM398 824q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5
t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="529" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM268 664q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM235 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="191" 
d="M123 0h-55v400h55v-400zM97 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5zM249 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM257 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5
t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="529" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM262 491q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9
t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="202" 
d="M22 617q23 -34 45.5 -67.5t45.5 -66.5h-44l-107 134h60z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="385" 
d="M193 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5zM246 491q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25
l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="203" 
d="M72 476v250h61l-4 -250h-57z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="531" 
d="M467 0h-403v57h403v-57z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="310" 
d="M95 -150v902h183v-57h-124v-788h124v-57h-183z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="310" 
d="M215 752v-902h-183v57h124v788h-124v57h183z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="296" 
d="M149 510q23 0 38.5 15.5t15.5 38.5t-15.5 38.5t-38.5 15.5t-38.5 -15.5t-15.5 -38.5t15.5 -38.5t38.5 -15.5zM55 564q0 39 27 65.5t67 26.5q19 0 36 -7t29.5 -19.5t19.5 -29.5t7 -36q0 -20 -7 -36.5t-19.5 -29t-29.5 -19.5t-36 -7q-40 0 -67 26.5t-27 65.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="414" 
d="M348 79l34 -41q-27 -22 -60.5 -34t-70.5 -12q0 -14 4 -22t10 -13.5t13.5 -9.5t13.5 -8q22 -16 31 -36t9 -41q0 -46 -26.5 -75t-71.5 -29q-31 0 -57.5 17t-38.5 52q19 10 35 17q5 -18 20 -29.5t37 -11.5q26 0 42 18t16 44q0 23 -11.5 35.5t-26 22.5t-26 22t-11.5 35v15
q-37 6 -69 24.5t-55 45.5t-36.5 61.5t-13.5 73.5q0 43 16.5 81.5t45 66.5t67 44.5t82.5 16.5q38 0 72 -12t61 -34q-8 -11 -17 -20.5t-18 -20.5q-41 33 -98 33q-33 0 -61.5 -12t-49.5 -32.5t-33 -49t-12 -61.5q0 -32 12 -60t33 -49t49.5 -33t61.5 -12q57 0 98 33z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="645" 
d="M618 48q-30 -20 -79.5 -37t-115.5 -19q0 -14 4 -22t10 -13.5t13.5 -9.5t13.5 -8q22 -16 31 -36t9 -41q0 -46 -26.5 -75t-71.5 -29q-31 0 -57.5 17t-38.5 52q19 10 35 17q5 -18 20 -29.5t37 -11.5q26 0 42 18t16 44q0 23 -11.5 35.5t-26 22.5t-26 22t-11.5 35v13
q-77 4 -139 34.5t-105.5 79.5t-67 112t-23.5 133q0 79 29 147t79 117.5t117 77.5t143 28q27 0 54.5 -3.5t52.5 -10.5t46 -16t36 -20q-5 -8 -8 -13t-6 -10.5t-7 -12.5t-10 -18q-32 20 -74.5 30t-81.5 10q-63 0 -118 -24t-95.5 -65.5t-64 -96t-23.5 -115.5q0 -58 21 -111.5
t60 -94t93 -65t120 -24.5q29 0 56 4.5t50 12t40.5 15.5t27.5 16z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="436" 
d="M66 173v57h303v-57h-303z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="639" 
d="M66 173v57h506v-57h-506z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="456" 
d="M189 0h38v-7q0 -14 4 -22.5t10 -14t13.5 -9.5t13.5 -8q22 -16 31 -36t9 -41q0 -46 -26.5 -75t-71.5 -29q-31 0 -57.5 17t-38.5 52q19 10 35 17q5 -18 20 -29.5t37 -11.5q26 0 42 18t16 44q0 23 -11.5 36t-26 22.5t-26 20t-11.5 28.5v28z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="256" 
d="M101 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="373" 
d="M241 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM62 517q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="720" 
d="M44 378h65v336h166q108 0 183 -33t122 -85t68 -116.5t21 -127.5q0 -81 -29 -146t-78 -111t-114 -70.5t-138 -24.5h-201v330h-65v48zM303 65q62 0 116.5 20t95 57.5t63.5 91.5t23 122q0 62 -21.5 115.5t-61.5 92.5t-96 61.5t-126 22.5h-118v-270h158v-48h-158v-265h125z
" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="530" 
d="M490 200q0 -44 -15.5 -82t-43.5 -66t-66 -44t-83 -16q-56 0 -99.5 26t-60.5 64q0 -10 0.5 -19.5t0.5 -20.5v-234h-55v958h55v-450q19 37 61 65t100 28q45 0 82.5 -15.5t65 -43t43 -66t15.5 -84.5zM431 200q0 33 -11 61.5t-31.5 49t-48.5 32.5t-61 12t-61.5 -12
t-49.5 -32.5t-33 -48.5t-12 -61t12 -61t33 -49t49.5 -33t61.5 -12t61 12t48.5 33t31.5 49t11 60z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="502" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5q33 0 63 -10t55 -27q-16 51 -48.5 113t-86.5 128l-67 -71l-31 29q34 39 71 77q-23 26 -48 50t-51 45l32 31q26 -20 50.5 -42.5t48.5 -48.5q16 17 31.5 34t31.5 35l31 -28l-67 -74q39 -48 74.5 -103t62 -113.5t42.5 -117
t16 -113.5q0 -4 -0.5 -7.5t-0.5 -5.5q-2 -42 -18.5 -78t-44.5 -63t-66 -42.5t-81 -15.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5t-49.5 33t-60.5 12t-60 -12t-49 -33
t-33 -49.5t-12 -60.5z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="540" 
d="M103 0v706h68v-139h105q45 0 87 -12.5t74.5 -38.5t52 -65t19.5 -93q0 -41 -13 -80.5t-40.5 -70t-71.5 -49.5t-106 -19h-107v-139h-68zM171 204h106q48 0 80 15.5t50.5 39t26 50t7.5 48.5q0 27 -10.5 53.5t-30.5 47t-51 33t-71 12.5h-107v-299z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="646" 
d="M632 0q-16 36 -40.5 95.5t-53.5 130.5t-60.5 147.5t-60.5 147.5t-54 130t-40 96q-16 -37 -40.5 -96t-53.5 -130t-60 -147.5t-60 -147.5t-54 -130.5t-40 -95.5h68q20 46 47 113t56 142h274q29 -75 56 -142t47 -113h69zM313 572q2 4 5 13.5t5 17.5q2 -8 5 -17.5t5 -13.5
q20 -47 47 -115t57 -143h-228q29 75 56.5 143t47.5 115zM349 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="613" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM336 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="274" 
d="M102 714h69v-714h-69v714zM138 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5zM411 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM355 955q30 -45 60 -91t61 -91h-44
q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="521" 
d="M231 213v153h-152v57h152v152h59v-152h152v-57h-152v-153h-59zM442 106h-363v57h363v-57z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="552" 
d="M479 677l-25 -55q-35 14 -77 23.5t-81 10.5v-243q37 -14 75 -30.5t69 -40.5t50 -58.5t19 -85.5q0 -88 -58 -141.5t-155 -62.5v-62h-42v60q-42 0 -76.5 8t-62 18.5t-47 21.5t-30.5 18l31 60q35 -23 85.5 -40.5t99.5 -18.5v303q-35 13 -67.5 27t-58 34t-41 47t-15.5 65
q0 33 12.5 63.5t36 55t57 41t76.5 21.5v64h42v-62q27 -1 55.5 -5.5t53 -11t44 -13t30.5 -11.5zM442 199q0 30 -12 52.5t-32 40t-46.5 30.5t-55.5 24v-284q25 4 51 14t47 26.5t34.5 40.5t13.5 56zM140 541q0 -22 9 -38.5t24 -30t36 -24t45 -19.5v223q-54 -9 -84 -40t-30 -71z
" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="429" 
d="M349 79l34 -41q-26 -21 -58.5 -33t-68.5 -13v-60h-42v63q-37 6 -69 24.5t-55 45.5t-36.5 61.5t-13.5 73.5t13.5 73.5t36.5 62t55 46t69 24.5v59h42v-56q37 -1 69.5 -13t58.5 -33q-8 -11 -17 -20.5t-18 -20.5q-39 31 -93 33v-309q54 1 93 33zM95 200q0 -56 33 -97t86 -53
v301q-53 -12 -86 -53t-33 -98z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="377" 
d="M83 302q0 22 8 41.5t22.5 34t33.5 23t41 8.5t41.5 -8.5t33.5 -23t22.5 -34t8.5 -41.5t-8.5 -41t-22.5 -33t-33.5 -22t-41.5 -8t-41 8t-33.5 22t-22.5 33t-8 41z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="289" 
d="M132 595l-29 -22q-29 27 -41 64.5t-4 79.5l40 -7q-7 -35 3 -65t31 -50zM236 595q-13 -11 -28 -22q-29 27 -41 64.5t-4 79.5l40 -7q-7 -35 2.5 -65t30.5 -50z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="290" 
d="M186 573l-28 22q21 20 30.5 50t2.5 65l41 7q8 -42 -4.5 -79.5t-41.5 -64.5zM82 573l-29 22q22 20 31.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="185" 
d="M132 595l-28 -22q-29 27 -41.5 64.5t-4.5 79.5l41 -7q-7 -35 2.5 -65t30.5 -50z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="300" 
d="M178 -51l-29 22q22 20 31.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5zM73 -51q-15 11 -28 22q21 20 30.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="195" 
d="M73 -51q-15 11 -28 22q21 20 30.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="280" 
d="M64 128q10 11 23.5 26t27 31t27 30.5t24.5 25.5q-11 11 -24.5 25.5t-27 30t-27 30t-23.5 25.5v67q13 -12 36.5 -37t49.5 -52t49.5 -52t36.5 -37l-172 -179v66z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="453" 
d="M389 62l-173 179q13 12 36.5 37t49.5 52t50 52t37 37v-66q-11 -11 -24.5 -25.5t-27 -30.5t-27 -30.5t-23.5 -25.5q10 -11 23.5 -25.5t27 -30.5t27 -31t24.5 -26v-66zM216 62l-172 179q13 12 36.5 37t49.5 52t49.5 52t36.5 37v-67q-11 -11 -24 -25.5t-26.5 -30t-27 -30
t-24.5 -25.5q11 -11 24.5 -25.5t27 -30.5t26.5 -31t24 -26v-66z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="453" 
d="M64 128q11 11 24.5 26t27 31t26.5 30.5t24 25.5q-11 11 -24 25.5t-26.5 30.5t-27 30.5t-24.5 25.5v66q13 -12 37 -37t50 -52t49.5 -52t36.5 -37l-173 -179v66zM237 128q10 11 23.5 26t27 31t27 30.5t24.5 25.5q-11 11 -24.5 25.5t-27 30t-27 30t-23.5 25.5v67
q13 -12 36.5 -37t49.5 -52t49.5 -52t36.5 -37l-172 -179v66z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="662" 
d="M366 249v-108h132v-41h-132v-100h-69v100h-132v41h132v108l-9 14h-123v41h99q-17 30 -38 66.5t-43.5 76t-44.5 79.5t-42.5 75.5t-37 65t-27.5 47.5h77q18 -30 44 -76t55.5 -97.5t59 -104.5t53.5 -97l12 -30l12 30q25 44 54 97t57.5 104.5t54 97.5t43.5 76h78l-234 -410
h101v-41h-124z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="646" 
d="M322 138q49 0 92 17.5t74.5 49t50 74t18.5 92.5q0 51 -18.5 94t-50 74.5t-74.5 49.5t-92 18q-48 0 -90.5 -18t-74 -49.5t-50 -74.5t-18.5 -94q0 -50 18.5 -92.5t50 -74t74 -49t90.5 -17.5zM323 115q-54 0 -101 19.5t-82 54t-55 81.5t-20 102q0 56 20 103t55 81.5t82 54
t101 19.5t101 -19.5t82 -54t55 -81.5t20 -103q0 -55 -20 -102t-55 -81.5t-82 -54t-101 -19.5zM422 235q-14 -9 -38 -17.5t-56 -8.5q-37 0 -67 13t-51.5 35.5t-33 52t-11.5 62.5q0 36 13 66.5t36 53t53 35.5t65 13q25 0 48 -6.5t37 -16.5q-5 -7 -6.5 -10.5t-7.5 -13.5
q-14 9 -33.5 13.5t-36.5 4.5q-29 0 -54 -11t-43 -29.5t-28.5 -43.5t-10.5 -52t9.5 -51t27 -42.5t41.5 -29.5t54 -11q26 0 48 7.5t31 14.5z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="646" 
d="M323 115q-54 0 -101 19.5t-82 54t-55 81.5t-20 102q0 56 20 103t55 81.5t82 54t101 19.5t101 -19.5t82 -54t55 -81.5t20 -103q0 -55 -20 -102t-55 -81.5t-82 -54t-101 -19.5zM322 138q49 0 92 17.5t74.5 49t50 74t18.5 92.5q0 51 -18.5 94t-50 74.5t-74.5 49.5t-92 18
q-48 0 -90.5 -18t-74 -49.5t-50 -74.5t-18.5 -94q0 -50 18.5 -92.5t50 -74t74 -49t90.5 -17.5zM236 210v338h82q21 0 41 -6t35 -18.5t24.5 -31t9.5 -43.5q0 -29 -13 -54.5t-42 -39.5q6 -11 18.5 -31t26 -42t26 -42t18.5 -30h-39q-5 8 -16 26t-23 38l-24 40t-20 32
q-5 -1 -10.5 -1h-10.5h-51v-135h-32zM268 376h50q23 0 38 7.5t24 18.5t12.5 23.5t3.5 23.5q0 26 -19.5 47t-57.5 21h-51v-141z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="504" 
d="M433 209h-363v57h363v-57zM214 376q0 17 12.5 29t28.5 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28.5 11.5t-12.5 29.5zM214 100q0 17 12.5 29t28.5 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28.5 11.5t-12.5 29.5z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="834" 
d="M592 40h204v-40h-305q45 44 87 86.5t75 82.5t52.5 77t19.5 70q0 31 -9.5 52.5t-24 35t-31.5 19.5t-32 6q-30 0 -49.5 -10.5t-30.5 -26t-15.5 -33t-4.5 -31.5q0 -24 7.5 -43.5t21.5 -33.5l-25 -24q-21 16 -33 45t-12 56q0 29 9 54.5t27 45t44.5 30.5t60.5 11
q31 0 56.5 -11.5t44 -31t28.5 -45.5t10 -56q0 -38 -15.5 -74.5t-40.5 -71.5t-56.5 -70t-62.5 -69zM197 -19l280 760h59q-71 -191 -140 -379.5t-140 -380.5h-59zM39 658v41h103v-465h-43v424h-60z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="795" 
d="M403 130q69 90 141.5 182t141.5 182v-321h72v-43h-72v-130h-44v130h-239zM189 -19l280 760h59q-71 -191 -140 -379.5t-140 -380.5h-59zM41 658v41h103v-465h-43v424h-60zM642 375l-155 -202h155v202z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="430" 
d="M50 449q0 31 12 58.5t32.5 48.5t48 33t58.5 12q22 0 40.5 -6t33.5 -15.5t25 -21t15 -23.5l3 59h37v-288h-40v30q0 8 0.5 15t0.5 13q-11 -23 -40.5 -43.5t-74.5 -20.5q-33 0 -60.5 12t-47.5 32t-31.5 47.5t-11.5 57.5zM90 451q0 -48 32 -80t80 -32q24 0 45 8.5t36 23.5
t23.5 35.5t8.5 44.5q0 23 -8.5 43.5t-23.5 35.5t-36 23.5t-45 8.5q-48 0 -80 -31.5t-32 -79.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="591" 
d="M176 57h201q31 0 52 2.5t33 8.5t17 16t5 26v49h59v-73q0 -18 -3.5 -33.5t-15.5 -27t-35.5 -18.5t-64.5 -7h-382v57h44q17 0 27.5 5t16 18t7.5 36.5t2 59.5v113h-66v57h66v178q0 71 52.5 111t137.5 40q70 0 110.5 -26t63.5 -71l-44 -25q-8 12 -17.5 24t-23.5 21t-35 14.5
t-52 5.5q-71 0 -102 -29.5t-31 -77.5v-165h122v-57h-122v-144q0 -29 -3.5 -51.5t-18.5 -36.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="188" 
d="M48 676v26h66v-301h-27v275h-39z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="986" 
d="M593 130q69 90 141.5 182t141.5 182v-321h73v-43h-73v-130h-44v130h-239zM356 -19q71 192 140.5 380.5t139.5 380.5h59q-71 -192 -140 -380.5t-140 -380.5h-59zM115 469q32 51 67 100t68 99h-185v44h259q-32 -48 -65 -94.5t-65 -94.5q29 -5 54 -19t43.5 -34.5t29 -47.5
t10.5 -58q0 -34 -13.5 -63.5t-36 -52t-52.5 -35.5t-64 -13q-36 0 -67 14.5t-54 39.5l33 32q16 -20 38 -31t49 -11q25 0 47 9.5t38.5 26t26.5 38.5t10 46q0 25 -10 47.5t-26.5 38.5t-38.5 25.5t-47 9.5q-29 0 -49 -16zM832 375l-155 -202h155v202z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="344" 
d="M139 403h143v-28h-213l60.5 60.5t52.5 57t36.5 53.5t13.5 50q0 22 -6.5 37t-16.5 24t-22 13t-22 4q-21 0 -34.5 -7.5t-21.5 -18t-11 -22.5t-3 -22q0 -32 20 -54l-18 -17q-14 11 -22.5 31t-8.5 40q0 41 25.5 69.5t73.5 28.5q44 0 70.5 -29t26.5 -71q0 -26 -11 -51.5
t-28.5 -50.5t-39.5 -49z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="540" 
d="M260 0h-58v426q-35 0 -65 13t-52 35t-35 52t-13 65q0 34 13 64.5t35 52.5t52 35t65 13h222v-756h-58v699h-106v-699z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="515" 
d="M385 149v55h-304v43h363v-98h-59z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="293" 
d="M100 548q20 32 43 63t43 63h-118v29h166l-83 -121q37 -7 62 -34t25 -67q0 -22 -8.5 -41t-23 -33.5t-33.5 -22.5t-41 -8q-23 0 -43 9.5t-34 25.5l21 20q21 -27 55 -27q16 0 30.5 6.5t25 17t16.5 24.5t6 29q0 32 -23 54.5t-55 22.5q-16 0 -31 -10z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="248" 
d="M153 -235h-58v353h58v-353zM153 353h-58v353h58v-353z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="651" 
d="M614 0h-66q-1 0 -19.5 22.5t-47.5 59.5q-31 -40 -84.5 -65t-119.5 -25q-51 0 -93.5 15.5t-73.5 43.5t-48 65t-17 81q0 40 14.5 75t39.5 62t59 45.5t72 26.5q-23 31 -39 54t-22 35q-14 27 -24 52t-10 57q0 34 13.5 62t36 47.5t52.5 30t64 10.5q44 1 81 -12.5t60 -28.5
q0 -1 -4.5 -7.5t-10 -14t-10 -13.5t-4.5 -7q-20 11 -42 19.5t-48 10.5q-25 1 -48 -5t-41 -19t-29 -32.5t-11 -45.5q0 -16 4 -30.5t8.5 -26t8.5 -19t5 -8.5l252 -330l86 225h59l-106 -275zM268 355q-30 -1 -60 -12.5t-54 -32.5t-38.5 -49t-14.5 -62q0 -37 15 -65.5t39.5 -48
t57 -29.5t67.5 -10q25 0 50.5 6t47.5 16.5t40 25t29 31.5q-42 53 -89.5 113.5t-89.5 116.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="551" 
d="M203 14l24 51q31 -17 69 -17q33 0 61.5 12.5t50 34t34 50t12.5 60.5t-12.5 60t-34 49t-50 33t-60.5 12h-1v57q42 0 69.5 24.5t27.5 66.5q0 39 -27.5 69t-81.5 30q-68 0 -99.5 -49t-31.5 -131v-426h-58v350h-65v57h65v21q0 55 13.5 98.5t37.5 73.5t57.5 46t73.5 17
q46 0 79 -13t54 -34t31 -48t10 -56q0 -41 -19 -73t-55 -46q25 -6 49.5 -23t43.5 -42t30.5 -56.5t11.5 -67.5q0 -44 -17 -82.5t-46.5 -67.5t-69 -46t-83.5 -17q-50 0 -93 23z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="457" 
d="M85 361q0 23 7 44.5t20 38.5l-32 33l30 29l32 -32q18 14 39.5 22t46.5 8t47 -8.5t39 -22.5l32 33l30 -30l-32 -31q28 -37 28 -84q0 -48 -30 -86l34 -35l-29 -29l-35 34q-17 -13 -39 -20t-45 -7t-44.5 7t-38.5 19l-35 -34l-30 29l35 35q-30 38 -30 87zM127 361
q0 -21 8 -40t22 -33t32 -22t39 -8t40 8t32.5 22t21.5 33t8 40t-8 39.5t-21.5 32.5t-32.5 22t-40 8t-39 -8t-32 -22t-22 -32.5t-8 -39.5z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="374" 
d="M157 152q-22 35 -36 58.5t-23 39t-14.5 25t-9.5 17.5q-12 22 -16 47.5t0.5 50t17 46t33.5 36.5q39 24 87 16q-14 20 -25 41t-24 43q-17 30 -23.5 52t-5 38t10 27.5t20.5 20.5q15 14 35.5 24.5t38.5 21.5l29 -49q-11 -6 -21.5 -12t-21.5 -13q-25 -14 -25 -35.5t26 -66.5h1
l89 -151q13 -23 17 -47.5t-1 -48.5t-18.5 -45t-34.5 -36q-39 -26 -86 -18l50 -85q18 -30 24.5 -52t4.5 -38.5t-10.5 -28t-20.5 -20.5q-14 -14 -34.5 -24.5t-38.5 -21.5q-7 12 -14.5 24.5t-14.5 25.5l43 25q13 7 19 16t5 22t-9 31t-24 44zM116 345q4 -27 26 -43.5t49 -15.5
q32 2 53 26t19 55q-2 33 -26 53.5t-55 20.5q-19 -2 -34 -10.5t-24 -22t-11.5 -30t3.5 -33.5z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="208" 
d="M64 284q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="403" 
d="M50 450q0 31 12 59t32.5 48.5t48 32.5t59.5 12t59.5 -12t48 -32.5t32 -48.5t11.5 -59t-11.5 -58.5t-32 -47.5t-48 -32t-59.5 -12t-59.5 11.5t-48 31.5t-32.5 47.5t-12 59.5zM90 450q0 -23 8.5 -43.5t23.5 -35.5t35.5 -23.5t43.5 -8.5t43.5 8.5t35.5 23.5t24 35.5t9 43.5
t-9 43.5t-24 35.5t-35.5 24t-43.5 9t-43.5 -9t-35.5 -24t-23.5 -35.5t-8.5 -43.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="403" 
d="M86 487v39h232v-39h-232z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="535" 
d="M439 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-43 0 -75 19v-211h-54v298q-1 4 -1 14v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="437" 
d="M324 0l-106 105q-51 -53 -104 -104l-49 48l105 106l-106 105l50 50l106 -106q26 27 52.5 53t52.5 52q12 -11 24 -24t24 -25l-52 -52l-52 -52q27 -26 53 -53t53 -53z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="531" 
d="M40 199q0 45 16 83t44 66.5t65.5 44.5t79.5 16q33 0 60 -9t47.5 -22.5t34.5 -29.5t20 -31q0 20 1.5 41t2.5 42h51v-393q0 -43 -13 -79.5t-39 -63t-66 -41.5t-94 -15q-57 0 -99.5 21t-86.5 62l36 38q39 -38 74.5 -53t73.5 -15q42 0 71.5 10.5t48.5 29t28 45t10 58.5v14
t1 23q0 3 0.5 9.5t0.5 14t0.5 14t0.5 8.5q-19 -38 -60.5 -66.5t-102.5 -28.5q-44 0 -81.5 16t-65.5 44t-43.5 65.5t-15.5 81.5zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5
t-33 -48.5t-12 -61z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="531" 
d="M40 200q0 45 16 83.5t43.5 66t65 43.5t81.5 16q31 0 57 -8t46.5 -21t35 -30t22.5 -34v450h55v-766h-55v41q0 11 1 21.5t1 20.5q-20 -37 -62 -64t-102 -27q-46 0 -83.5 16.5t-64.5 44.5t-42 66t-15 81zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33
t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="530" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="483" 
d="M69 400h47l5 -103q22 50 65 81t103 31q59 0 93.5 -33t36.5 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v400z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="660" 
d="M234 -28q0 -18 -11.5 -28.5t-26 -20t-26 -22.5t-11.5 -36q0 -26 15.5 -44t42.5 -18q21 0 36 11.5t20 29.5q16 -7 35 -17q-11 -35 -37.5 -52t-58.5 -17q-45 0 -71.5 29t-26.5 75q0 21 9 41t31 36q6 4 13.5 8t14 9.5t10.5 14t4 22.5v7h38v-28z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="445" 
d="M113 665h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M300 483q23 33 45.5 66.5t45.5 67.5h60q-26 -34 -53 -67.5t-54 -66.5h-44zM81 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="219" 
d="M68 545q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="639" 
d="M263 500q-33 0 -62.5 12t-51.5 33t-35 50t-14 62h34q1 -26 11 -48t27 -39t40.5 -26.5t50.5 -9.5q56 0 90 36t36 87h35q0 -33 -13 -62t-35 -50t-51 -33t-62 -12z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="645" 
d="M601 0v-28q0 -18 -11.5 -28.5t-26 -20t-26 -22.5t-11.5 -36q0 -26 15.5 -44t42.5 -18q21 0 36 11.5t20 29.5q16 -7 35 -17q-11 -35 -37.5 -52t-58.5 -17q-45 0 -71.5 29t-26.5 75q0 21 9 41t31 36q6 4 13.5 8t14 9.5t10.5 14t4 22.5v7h-1q-20 46 -47 113t-56 142h-274
q-29 -75 -56 -142t-47 -113h-68q15 36 40 95.5t54 130.5t60 147.5t60 147.5t53.5 130t40.5 96q15 -37 40 -96t54 -130t60.5 -147.5t60.5 -147.5t53.5 -130.5t40.5 -95.5h-30zM312 572q-20 -47 -47.5 -115t-56.5 -143h228q-30 75 -57 143t-47 115q-2 4 -5 13.5t-5 17.5
q-2 -8 -5 -17.5t-5 -13.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="572" 
d="M110 0v280l-71 -23v57l71 23v377h68v-354l220 74v-57l-220 -74v-238h380v-65h-448z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="564" 
d="M102 0v714h68v-649h380v-65h-448zM330 573l-29 22q22 20 31.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="551" 
d="M477 677l-25 -55q-35 14 -77.5 24t-82.5 10q-36 0 -64.5 -9t-48.5 -25t-30.5 -36.5t-10.5 -44.5q0 -29 16 -49.5t42 -35.5t59 -27.5t67.5 -25.5t67.5 -29.5t59 -40.5t42 -57t16 -80t-17 -84.5t-49 -64t-78.5 -41t-105.5 -14.5q-42 0 -77.5 7.5t-63 18t-48 21.5t-32.5 19
l31 60q18 -11 40.5 -22t47 -19t50.5 -13t51 -5q27 0 59.5 7.5t60 24.5t46 43.5t18.5 64.5q0 47 -27.5 76t-68.5 49.5t-89 36.5t-89 37.5t-68.5 53.5t-27.5 83q0 36 14.5 69.5t42.5 58.5t68 40t90 15q28 0 57.5 -4.5t55.5 -11t47 -13.5t32 -12zM224 760l91 134h59
q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="551" 
d="M477 677l-25 -55q-35 14 -77.5 24t-82.5 10q-36 0 -64.5 -9t-48.5 -25t-30.5 -36.5t-10.5 -44.5q0 -29 16 -49.5t42 -35.5t59 -27.5t67.5 -25.5t67.5 -29.5t59 -40.5t42 -57t16 -80t-17 -84.5t-49 -64t-78.5 -41t-105.5 -14.5q-42 0 -77.5 7.5t-63 18t-48 21.5t-32.5 19
l31 60q18 -11 40.5 -22t47 -19t50.5 -13t51 -5q27 0 59.5 7.5t60 24.5t46 43.5t18.5 64.5q0 47 -27.5 76t-68.5 49.5t-89 36.5t-89 37.5t-68.5 53.5t-27.5 83q0 36 14.5 69.5t42.5 58.5t68 40t90 15q28 0 57.5 -4.5t55.5 -11t47 -13.5t32 -12zM156 919h43l78 -110l77 110h44
q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="551" 
d="M477 677l-25 -55q-35 14 -77.5 24t-82.5 10q-36 0 -64.5 -9t-48.5 -25t-30.5 -36.5t-10.5 -44.5q0 -29 16 -49.5t42 -35.5t59 -27.5t67.5 -25.5t67.5 -29.5t59 -40.5t42 -57t16 -80q0 -89 -59 -143.5t-167 -59.5q0 -14 4 -22.5t10 -14t13.5 -9.5t13.5 -8q22 -16 31 -36
t9 -41q0 -46 -26.5 -75t-71.5 -29q-31 0 -57.5 17t-38.5 52q19 10 35 17q5 -18 20 -29.5t37 -11.5q26 0 42 18t16 44q0 23 -11.5 36t-26 22.5t-26 20t-11.5 28.5v20q-40 1 -73 9t-59 18.5t-44.5 21t-30.5 17.5l31 60q18 -11 40.5 -22t47 -19t50.5 -13t51 -5q27 0 59.5 7.5
t60 24.5t46 43.5t18.5 64.5q0 47 -27.5 76t-68.5 49.5t-89 36.5t-89 37.5t-68.5 53.5t-27.5 83q0 36 14.5 69.5t42.5 58.5t68 40t90 15q28 0 57.5 -4.5t55.5 -11t47 -13.5t32 -12z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="537" 
d="M492 714v-65h-190v-649h-69v649h-188v65h447zM147 919h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="634" 
d="M140 714h449l-458 -648h477v-66h-596q25 36 62 89.5t80 115.5t88.5 128t88.5 127.5t80 113.5t62 86h-352q-32 0 -53.5 13t-21.5 42v35h63v-13q0 -14 7 -18.5t24 -4.5zM292 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="634" 
d="M140 714h449l-458 -648h477v-66h-596q25 36 62 89.5t80 115.5t88.5 128t88.5 127.5t80 113.5t62 86h-352q-32 0 -53.5 13t-21.5 42v35h63v-13q0 -14 7 -18.5t24 -4.5zM198 919h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="634" 
d="M140 714h449l-458 -648h477v-66h-596q25 36 62 89.5t80 115.5t88.5 128t88.5 127.5t80 113.5t62 86h-352q-32 0 -53.5 13t-21.5 42v35h63v-13q0 -14 7 -18.5t24 -4.5zM289 794q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="530" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM445 0h-38v-7q0 -14 -4 -22.5t-10.5 -14t-14 -9.5t-13.5 -8q-22 -16 -31 -36t-9 -41q0 -46 26.5 -75t71.5 -29q32 0 58.5 17t37.5 52
q-19 10 -35 17q-5 -18 -20 -29.5t-36 -11.5q-27 0 -42.5 18t-15.5 44q0 23 11.5 36t26 22.5t26 20t11.5 28.5v28z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="221" 
d="M132 0h-55v274l-52 -17v57l52 17v435h55v-416l73 25v-57l-73 -25v-293z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="195" 
d="M125 0h-55v766h55v-766zM204 573l-29 22q22 20 31.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="332" 
d="M279 362l-30 -39q-8 11 -31.5 20.5t-47.5 9.5q-10 0 -21.5 -3t-21 -9.5t-16 -17t-6.5 -25.5q0 -23 14 -36.5t35 -23.5t45.5 -19t45.5 -22t35 -33.5t14 -52.5q0 -50 -34 -84.5t-97 -34.5q-34 0 -69.5 12t-57.5 38q4 4 10 11t11 13t8.5 10.5t4.5 4.5q3 -5 13 -11t23 -11.5
t28 -9.5t29 -4q12 0 25.5 3.5t25 11.5t18.5 20t7 29q0 23 -14 36.5t-35 23.5t-46 18.5t-46 21.5t-35 34t-14 54q0 30 11 50.5t28 34t38.5 19.5t41.5 6q26 0 57 -11t54 -34zM139 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="332" 
d="M279 362l-30 -39q-8 11 -31.5 20.5t-47.5 9.5q-10 0 -21.5 -3t-21 -9.5t-16 -17t-6.5 -25.5q0 -23 14 -36.5t35 -23.5t45.5 -19t45.5 -22t35 -33.5t14 -52.5q0 -50 -34 -84.5t-97 -34.5q-34 0 -69.5 12t-57.5 38q4 4 10 11t11 13t8.5 10.5t4.5 4.5q3 -5 13 -11t23 -11.5
t28 -9.5t29 -4q12 0 25.5 3.5t25 11.5t18.5 20t7 29q0 23 -14 36.5t-35 23.5t-46 18.5t-46 21.5t-35 34t-14 54q0 30 11 50.5t28 34t38.5 19.5t41.5 6q26 0 57 -11t54 -34zM42 665h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="332" 
d="M279 362l-30 -39q-8 11 -31.5 20.5t-47.5 9.5q-10 0 -21.5 -3t-21 -9.5t-16 -17t-6.5 -25.5q0 -23 14 -36.5t35 -23.5t45.5 -19t45.5 -22t35 -33.5t14 -52.5q0 -46 -29 -79t-82 -39q0 -14 4 -22.5t10 -14t13.5 -9.5t13.5 -8q22 -16 31 -36t9 -41q0 -46 -26.5 -75
t-71.5 -29q-31 0 -57.5 17t-38.5 52q19 10 35 17q5 -18 20 -29.5t37 -11.5q26 0 42 18t16 44q0 23 -11.5 36t-26 22.5t-26 20t-11.5 28.5v21q-31 3 -60.5 14.5t-48.5 34.5q4 4 10 11t11 13t8.5 10.5t4.5 4.5q3 -5 13 -11t23 -11.5t28 -9.5t29 -4q12 0 25.5 3.5t25 11.5
t18.5 20t7 29q0 23 -14 36.5t-35 23.5t-46 18.5t-46 21.5t-35 34t-14 54q0 30 11 50.5t28 34t38.5 19.5t41.5 6q26 0 57 -11t54 -34z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="286" 
d="M99 575h55v-179h119v-50h-119v-346h-55v346h-80v50h80v179zM254 573l-29 22q22 20 31.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="404" 
d="M371 0h-345l249 352h-169q-33 0 -52.5 14.5t-19.5 44.5v27h50v-13q0 -14 8 -19.5t25 -5.5h258l-245 -346h241v-54zM171 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="404" 
d="M371 0h-345l249 352h-169q-33 0 -52.5 14.5t-19.5 44.5v27h50v-13q0 -14 8 -19.5t25 -5.5h258l-245 -346h241v-54zM90 665h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="404" 
d="M371 0h-345l249 352h-169q-33 0 -52.5 14.5t-19.5 44.5v27h50v-13q0 -14 8 -19.5t25 -5.5h258l-245 -346h241v-54zM159 545q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="603" 
d="M102 0v714h173q45 0 87 -12.5t74.5 -38.5t52 -65t19.5 -93q0 -60 -27 -114t-89 -83l24 -40t33 -54.5t37.5 -61.5t37.5 -61t33 -53t24 -38h-83q-8 12 -21 33t-29 47t-34 55t-35 57t-32.5 53t-26.5 43q-10 -2 -21 -2h-22h-107v-286h-68zM170 351h106q48 0 80 16t50.5 39.5
t26 50t7.5 48.5q0 27 -10.5 53t-30.5 46.5t-51 33t-71 12.5h-107v-299zM254 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="646" 
d="M632 0q-16 36 -40.5 95.5t-53.5 130.5t-60.5 147.5t-60.5 147.5t-54 130t-40 96q-16 -37 -40.5 -96t-53.5 -130t-60 -147.5t-60 -147.5t-54 -130.5t-40 -95.5h68q20 46 47 113t56 142h274q29 -75 56 -142t47 -113h69zM313 572q2 4 5 13.5t5 17.5q2 -8 5 -17.5t5 -13.5
q20 -47 47 -115t57 -143h-228q29 75 56.5 143t47.5 115zM324 762q-33 0 -62.5 12t-51.5 33t-35 50t-14 62h34q1 -26 11 -48t27 -39t40.5 -26.5t50.5 -9.5q56 0 90 36t36 87h35q0 -33 -13 -62t-35 -50t-51 -33t-62 -12z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="564" 
d="M102 0v714h68v-649h380v-65h-448zM268 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="645" 
d="M618 48q-32 -20 -85 -38t-123 -18q-83 0 -149.5 29t-113 78.5t-72 114.5t-25.5 138q0 79 29 147t79 117.5t117 77.5t143 28q27 0 54.5 -3.5t52.5 -10.5t46 -16t36 -20q-5 -8 -8 -13t-6 -10.5t-7 -12.5t-10 -18q-32 20 -74.5 30t-81.5 10q-63 0 -118 -24t-95.5 -65.5
t-64 -96t-23.5 -115.5q0 -58 21 -111.5t60 -94t93 -65t120 -24.5q29 0 56 4.5t50 12t40.5 15.5t27.5 16zM353 760l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="645" 
d="M618 48q-32 -20 -85 -38t-123 -18q-83 0 -149.5 29t-113 78.5t-72 114.5t-25.5 138q0 79 29 147t79 117.5t117 77.5t143 28q27 0 54.5 -3.5t52.5 -10.5t46 -16t36 -20q-5 -8 -8 -13t-6 -10.5t-7 -12.5t-10 -18q-32 20 -74.5 30t-81.5 10q-63 0 -118 -24t-95.5 -65.5
t-64 -96t-23.5 -115.5q0 -58 21 -111.5t60 -94t93 -65t120 -24.5q29 0 56 4.5t50 12t40.5 15.5t27.5 16zM290 919h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="612" 
d="M567 0h-464v714h450v-62h-382v-261h342v-64h-342v-263h396v-64zM566 0h-38v-7q0 -14 -4 -22.5t-10.5 -14t-14 -9.5t-13.5 -8q-22 -16 -31 -36t-9 -41q0 -46 26.5 -75t71.5 -29q32 0 58.5 17t37.5 52q-19 10 -35 17q-5 -18 -20 -29.5t-36 -11.5q-27 0 -42.5 18t-15.5 44
q0 23 11.5 36t26 22.5t26 20t11.5 28.5v28z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="612" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM231 919h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="714" 
d="M103 714h166q108 0 183 -33t122 -85t68 -116.5t21 -127.5q0 -81 -29 -146t-78 -111t-114 -70.5t-138 -24.5h-201v714zM297 65q62 0 116.5 20t95 57.5t63.5 91.5t23 122q0 62 -21.5 115.5t-61.5 92.5t-96 61.5t-126 22.5h-118v-583h125zM214 919h43l78 -110l77 110h44
q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="719" 
d="M43 406h65v308h166q108 0 183 -33t122 -85t68 -116.5t21 -127.5q0 -81 -29 -146t-78 -111t-114 -70.5t-138 -24.5h-201v358h-65v48zM302 65q62 0 116.5 20t95 57.5t63.5 91.5t23 122q0 62 -21.5 115.5t-61.5 92.5t-96 61.5t-126 22.5h-118v-242h158v-48h-158v-293h125z
" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="812" 
d="M710 714v-747q-29 35 -71.5 83.5t-92 103.5t-102 112.5t-101.5 112.5t-89.5 102.5t-66.5 82.5q-3 4 -9 11.5t-11 14.5l3 -32v-558h-68v747q29 -37 71 -85t91.5 -102t101.5 -111t100.5 -110.5t90.5 -101t69 -82.5q2 -5 8 -12.5t11 -14.5l-3 33v553h68zM371 760l91 134h59
q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="812" 
d="M710 714v-747q-29 35 -71.5 83.5t-92 103.5t-102 112.5t-101.5 112.5t-89.5 102.5t-66.5 82.5q-3 4 -9 11.5t-11 14.5l3 -32v-558h-68v747q29 -37 71 -85t91.5 -102t101.5 -111t100.5 -110.5t90.5 -101t69 -82.5q2 -5 8 -12.5t11 -14.5l-3 33v553h68zM283 919h43l78 -110
l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5zM495 758q23 33 45.5 66.5t45.5 67.5h60q-26 -34 -53 -67.5t-54 -66.5h-44zM276 758l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="603" 
d="M102 0v714h173q45 0 87 -12.5t74.5 -38.5t52 -65t19.5 -93q0 -60 -27 -114t-89 -83l24 -40t33 -54.5t37.5 -61.5t37.5 -61t33 -53t24 -38h-83q-8 12 -21 33t-29 47t-34 55t-35 57t-32.5 53t-26.5 43q-10 -2 -21 -2h-22h-107v-286h-68zM170 351h106q48 0 80 16t50.5 39.5
t26 50t7.5 48.5q0 27 -10.5 53t-30.5 46.5t-51 33t-71 12.5h-107v-299zM168 919h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="696" 
d="M313 833q0 -17 12 -29t29 -12t29 12t12 29t-12 29t-29 12t-29 -12t-12 -29zM278 834q0 31 22 53t54 22t53.5 -22t21.5 -53q0 -32 -21.5 -54t-53.5 -22t-54 22t-22 54zM348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5
t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM419 758q23 33 45.5 66.5t45.5 67.5h60
q-26 -34 -53 -67.5t-54 -66.5h-44zM200 758l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="537" 
d="M492 714v-65h-190v-649h-69v649h-188v65h447zM303 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="294" 
d="M276 340q-30 0 -57.5 -9.5t-48.5 -26.5t-33.5 -41.5t-12.5 -54.5v-208h-55v400h47l5 -103q12 26 31 46.5t41.5 35t47 22.5t46.5 8zM126 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="530" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM272 500q-33 0 -62.5 12t-51.5 33t-35 50t-14 62h34q1 -26 11 -48t27 -39t40.5 -26.5t50.5 -9.5q56 0 90 36t36 87h35q0 -33 -13 -62
t-35 -50t-51 -33t-62 -12z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="195" 
d="M125 0h-55v766h55v-766zM59 784l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="414" 
d="M348 79l34 -41q-27 -22 -60.5 -34t-71.5 -12q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 66.5t67 44.5t82.5 16.5q38 0 72 -12t61 -34q-8 -11 -17 -20.5t-18 -20.5q-41 33 -98 33q-33 0 -61.5 -12t-49.5 -32.5t-33 -49t-12 -61.5q0 -32 12 -60t33 -49
t49.5 -33t61.5 -12q57 0 98 33zM196 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="414" 
d="M348 79l34 -41q-27 -22 -60.5 -34t-71.5 -12q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 66.5t67 44.5t82.5 16.5q38 0 72 -12t61 -34q-8 -11 -17 -20.5t-18 -20.5q-41 33 -98 33q-33 0 -61.5 -12t-49.5 -32.5t-33 -49t-12 -61.5q0 -32 12 -60t33 -49
t49.5 -33t61.5 -12q57 0 98 33zM123 665h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="456" 
d="M269 0h-38v-7q-41 3 -76 20.5t-60.5 45.5t-40.5 64t-15 77q0 43 16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30l41 -39q-26 -23 -59.5 -37.5t-70.5 -17.5v7z
M365 282q-17 34 -48 53.5t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM250 -8h-9.5t-9.5 1q0 -14 -4 -22.5t-10.5 -14t-14 -9.5t-13.5 -8q-22 -16 -31 -36t-9 -41q0 -46 26.5 -75t71.5 -29q32 0 58.5 17
t37.5 52q-19 10 -35 17q-5 -18 -20 -29.5t-36 -11.5q-27 0 -42.5 18t-15.5 44q0 23 11.5 36t26 22.5t26 20t11.5 28.5v21q-5 -1 -9.5 -1h-9.5z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM115 665h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="530" 
d="M40 200q0 45 16 83.5t43.5 66t65 43.5t81.5 16q31 0 57 -8t46.5 -21t35 -30t22.5 -34v450h55v-766h-55v41q0 11 1 21.5t1 20.5q-20 -37 -62 -64t-102 -27q-46 0 -83.5 16.5t-64.5 44.5t-42 66t-15 81zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33
t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM541 573l-29 22q22 20 31.5 50t2.5 65l40 7q8 -42 -4 -79.5t-41 -64.5z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="530" 
d="M40 200q0 45 16 83.5t43.5 66t65 43.5t81.5 16q31 0 57 -8t46.5 -21t35 -30t22.5 -34v294h55v-610h-55v41q0 11 1 21.5t1 20.5q-20 -37 -62 -64t-102 -27q-46 0 -83.5 16.5t-64.5 44.5t-42 66t-15 81zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33
t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM232 610h175v48h-175v-48zM407 766h55v-108h-55v108zM524 610v48h-62v-48h62z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="483" 
d="M69 400h47l5 -103q22 50 65 81t103 31q59 0 93.5 -33t36.5 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v400zM215 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="483" 
d="M69 400h47l5 -103q22 50 65 81t103 31q59 0 93.5 -33t36.5 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v400zM127 665h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5zM319 483q23 33 45.5 66.5t45.5 67.5h60q-26 -34 -53 -67.5t-54 -66.5h-44zM100 483l91 134h59q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="294" 
d="M276 340q-30 0 -57.5 -9.5t-48.5 -26.5t-33.5 -41.5t-12.5 -54.5v-208h-55v400h47l5 -103q12 26 31 46.5t41.5 35t47 22.5t46.5 8zM49 665h43l78 -110l77 110h44q-31 -45 -61 -91t-60 -91z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M201 511q0 -17 12 -29t29 -12t29 12t12 29t-12 29t-29 12t-29 -12t-12 -29zM166 512q0 31 22 53t54 22t54 -22t22 -53q0 -32 -22 -54t-54 -22t-54 22t-22 54zM414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26
q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM309 483q23 33 45.5 66.5t45.5 67.5h60q-26 -34 -53 -67.5t-54 -66.5h-44zM90 483l91 134h59
q-26 -34 -53 -67.5t-54 -66.5h-43z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="286" 
d="M99 575h55v-179h119v-50h-119v-346h-55v346h-80v50h80v179zM168 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="765" 
d="M110 0v507h-79v39h79v168h69v-168h406v168h70v-168h76v-39h-76q0 -68 -0.5 -142.5t-1 -143.5t-0.5 -127v-94h-68v327h-406v-327h-69zM585 392v115h-406v-115h406z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="748" 
d="M102 0v714h69v-322h406v322h70v-91.5v-124t-0.5 -141t-0.5 -141t-0.5 -124.5t-0.5 -92h-68v327h-406v-327h-69zM375 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="273" 
d="M102 714h69v-714h-69v714zM96 803q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="683" 
d="M618 327v-284q-41 -24 -93 -37.5t-109 -13.5q-84 0 -151.5 27.5t-115 75.5t-73.5 113t-26 140q0 81 29.5 149.5t80 118.5t118 78t142.5 28q51 0 103.5 -13t93.5 -38l-34 -56q-33 20 -77.5 31.5t-86.5 11.5q-61 0 -115.5 -24t-96 -65t-65.5 -97t-24 -119q0 -60 21.5 -114
t60.5 -94.5t94 -64t122 -23.5q33 0 69.5 7.5t69.5 22.5v180h-154v60h217zM403 759q-33 0 -62.5 12t-51.5 33t-35 50t-14 62h34q1 -26 11 -48t27 -39t40.5 -26.5t50.5 -9.5q56 0 90 36t36 87h35q0 -33 -13 -62t-35 -50t-51 -33t-62 -12z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="297" 
d="M-36 -146v64q36 2 66 15.5t51.5 38.5t34 62t12.5 85v595h69v-598q0 -62 -18 -110.5t-49.5 -81.5t-74 -51t-91.5 -19zM162 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="hbar" unicode="&#x127;" 
d="M68 766h55v-169h154v-39h-154v-255q20 46 61.5 75.5t101.5 30.5q60 0 95 -33t37 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v558h-59v39h59v169z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" 
d="M68 766h55v-463q20 46 61.5 75.5t101.5 30.5q60 0 95 -33t37 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v766zM282 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5
t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="191" 
d="M123 0h-55v400h55v-400z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="531" 
d="M40 199q0 45 16 83t44 66.5t65.5 44.5t79.5 16q33 0 60 -9t47.5 -22.5t34.5 -29.5t20 -31q0 20 1.5 41t2.5 42h51v-393q0 -43 -13 -79.5t-39 -63t-66 -41.5t-94 -15q-57 0 -99.5 21t-86.5 62l36 38q39 -38 74.5 -53t73.5 -15q42 0 71.5 10.5t48.5 29t28 45t10 58.5v14
t1 23q0 3 0.5 9.5t0.5 14t0.5 14t0.5 8.5q-19 -38 -60.5 -66.5t-102.5 -28.5q-44 0 -81.5 16t-65.5 44t-43.5 65.5t-15.5 81.5zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5
t-33 -48.5t-12 -61zM273 500q-33 0 -62.5 12t-51.5 33t-35 50t-14 62h34q1 -26 11 -48t27 -39t40.5 -26.5t50.5 -9.5q56 0 90 36t36 87h35q0 -33 -13 -62t-35 -50t-51 -33t-62 -12z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="191" 
d="M49 -192q-11 10 -20 19.5t-19 20.5q27 26 42.5 62.5t15.5 86.5v403h55v-403q0 -63 -20 -110.5t-54 -78.5zM95 664q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="645" 
d="M618 48q-32 -20 -85 -38t-123 -18q-83 0 -149.5 29t-113 78.5t-72 114.5t-25.5 138q0 79 29 147t79 117.5t117 77.5t143 28q27 0 54.5 -3.5t52.5 -10.5t46 -16t36 -20q-5 -8 -8 -13t-6 -10.5t-7 -12.5t-10 -18q-32 20 -74.5 30t-81.5 10q-63 0 -118 -24t-95.5 -65.5
t-64 -96t-23.5 -115.5q0 -58 21 -111.5t60 -94t93 -65t120 -24.5q29 0 56 4.5t50 12t40.5 15.5t27.5 16zM359 799q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="645" 
d="M618 48q-32 -20 -85 -38t-123 -18q-83 0 -149.5 29t-113 78.5t-72 114.5t-25.5 138q0 79 29 147t79 117.5t117 77.5t143 28q27 0 54.5 -3.5t52.5 -10.5t46 -16t36 -20q-5 -8 -8 -13t-6 -10.5t-7 -12.5t-10 -18q-32 20 -74.5 30t-81.5 10q-63 0 -118 -24t-95.5 -65.5
t-64 -96t-23.5 -115.5q0 -58 21 -111.5t60 -94t93 -65t120 -24.5q29 0 56 4.5t50 12t40.5 15.5t27.5 16zM405 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="683" 
d="M618 327v-284q-41 -24 -93 -37.5t-109 -13.5q-84 0 -151.5 27.5t-115 75.5t-73.5 113t-26 140q0 81 29.5 149.5t80 118.5t118 78t142.5 28q51 0 103.5 -13t93.5 -38l-34 -56q-33 20 -77.5 31.5t-86.5 11.5q-61 0 -115.5 -24t-96 -65t-65.5 -97t-24 -119q0 -60 21.5 -114
t60.5 -94.5t94 -64t122 -23.5q33 0 69.5 7.5t69.5 22.5v180h-154v60h217zM352 799q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="683" 
d="M618 327v-284q-41 -24 -93 -37.5t-109 -13.5q-84 0 -151.5 27.5t-115 75.5t-73.5 113t-26 140q0 81 29.5 149.5t80 118.5t118 78t142.5 28q51 0 103.5 -13t93.5 -38l-34 -56q-33 20 -77.5 31.5t-86.5 11.5q-61 0 -115.5 -24t-96 -65t-65.5 -97t-24 -119q0 -60 21.5 -114
t60.5 -94.5t94 -64t122 -23.5q33 0 69.5 7.5t69.5 22.5v180h-154v60h217zM409 942q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM353 759q-33 0 -62.5 12t-51.5 33t-35 50
t-14 62h34q1 -26 11 -48t27 -39t40.5 -26.5t50.5 -9.5q56 0 90 36t36 87h35q0 -33 -13 -62t-35 -50t-51 -33t-62 -12z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="551" 
d="M477 677l-25 -55q-35 14 -77.5 24t-82.5 10q-36 0 -64.5 -9t-48.5 -25t-30.5 -36.5t-10.5 -44.5q0 -29 16 -49.5t42 -35.5t59 -27.5t67.5 -25.5t67.5 -29.5t59 -40.5t42 -57t16 -80t-17 -84.5t-49 -64t-78.5 -41t-105.5 -14.5q-42 0 -77.5 7.5t-63 18t-48 21.5t-32.5 19
l31 60q18 -11 40.5 -22t47 -19t50.5 -13t51 -5q27 0 59.5 7.5t60 24.5t46 43.5t18.5 64.5q0 47 -27.5 76t-68.5 49.5t-89 36.5t-89 37.5t-68.5 53.5t-27.5 83q0 36 14.5 69.5t42.5 58.5t68 40t90 15q28 0 57.5 -4.5t55.5 -11t47 -13.5t32 -12zM283 942q30 -45 60 -91t61 -91
h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="414" 
d="M348 79l34 -41q-27 -22 -60.5 -34t-71.5 -12q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 66.5t67 44.5t82.5 16.5q38 0 72 -12t61 -34q-8 -11 -17 -20.5t-18 -20.5q-41 33 -98 33q-33 0 -61.5 -12t-49.5 -32.5t-33 -49t-12 -61.5q0 -32 12 -60t33 -49
t49.5 -33t61.5 -12q57 0 98 33zM211 545q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="414" 
d="M348 79l34 -41q-27 -22 -60.5 -34t-71.5 -12q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 66.5t67 44.5t82.5 16.5q38 0 72 -12t61 -34q-8 -11 -17 -20.5t-18 -20.5q-41 33 -98 33q-33 0 -61.5 -12t-49.5 -32.5t-33 -49t-12 -61.5q0 -32 12 -60t33 -49
t49.5 -33t61.5 -12q57 0 98 33zM243 664q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="531" 
d="M40 199q0 45 16 83t44 66.5t65.5 44.5t79.5 16q33 0 60 -9t47.5 -22.5t34.5 -29.5t20 -31q0 20 1.5 41t2.5 42h51v-393q0 -43 -13 -79.5t-39 -63t-66 -41.5t-94 -15q-57 0 -99.5 21t-86.5 62l36 38q39 -38 74.5 -53t73.5 -15q42 0 71.5 10.5t48.5 29t28 45t10 58.5v14
t1 23q0 3 0.5 9.5t0.5 14t0.5 14t0.5 8.5q-19 -38 -60.5 -66.5t-102.5 -28.5q-44 0 -81.5 16t-65.5 44t-43.5 65.5t-15.5 81.5zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5
t-33 -48.5t-12 -61zM232 545q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="531" 
d="M40 199q0 45 16 83t44 66.5t65.5 44.5t79.5 16q33 0 60 -9t47.5 -22.5t34.5 -29.5t20 -31q0 20 1.5 41t2.5 42h51v-393q0 -43 -13 -79.5t-39 -63t-66 -41.5t-94 -15q-57 0 -99.5 21t-86.5 62l36 38q39 -38 74.5 -53t73.5 -15q42 0 71.5 10.5t48.5 29t28 45t10 58.5v14
t1 23q0 3 0.5 9.5t0.5 14t0.5 14t0.5 8.5q-19 -38 -60.5 -66.5t-102.5 -28.5q-44 0 -81.5 16t-65.5 44t-43.5 65.5t-15.5 81.5zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5
t-33 -48.5t-12 -61zM258 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM240 500q-33 0 -62.5 12t-51.5 33t-35 50t-14 62h34q1 -26 11 -48t27 -39t40.5 -26.5t50.5 -9.5q56 0 90 36
t36 87h35q0 -33 -13 -62t-35 -50t-51 -33t-62 -12z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="332" 
d="M279 362l-30 -39q-8 11 -31.5 20.5t-47.5 9.5q-10 0 -21.5 -3t-21 -9.5t-16 -17t-6.5 -25.5q0 -23 14 -36.5t35 -23.5t45.5 -19t45.5 -22t35 -33.5t14 -52.5q0 -50 -34 -84.5t-97 -34.5q-34 0 -69.5 12t-57.5 38q4 4 10 11t11 13t8.5 10.5t4.5 4.5q3 -5 13 -11t23 -11.5
t28 -9.5t29 -4q12 0 25.5 3.5t25 11.5t18.5 20t7 29q0 23 -14 36.5t-35 23.5t-46 18.5t-46 21.5t-35 34t-14 54q0 30 11 50.5t28 34t38.5 19.5t41.5 6q26 0 57 -11t54 -34zM163 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5
t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="692" 
d="M635 48q-29 -26 -68 -41t-81 -15q-33 0 -63 9t-55 26q-26 -17 -56 -26t-63 -9q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83q0 43 16.5 81.5t45 67t67 45t82.5 16.5q33 0 62.5 -9.5t55.5 -26.5q25 17 54.5 26.5t61.5 9.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5
t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM248 355q-32 0 -60 -12t-49 -33t-33 -49.5t-12 -60.5t12 -60t33 -49t49 -33t60 -12q41 0 76 19q-23 27 -36 62t-13 73t13 72.5t36 62.5q-17 9 -36.5 14.5t-39.5 5.5zM601 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="950" 
d="M413 -8q-75 0 -141.5 28.5t-116 78t-78 116t-28.5 141.5t28.5 141.5t78 116.5t116 79t141.5 29q38 0 76 -8h402v-62h-351v-261h311v-64h-311v-263h365v-64h-415q-40 -8 -77 -8zM413 656q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5t23.5 -116.5t63.5 -95
t94 -64t115 -23.5q15 0 29.5 1t28.5 4v588q-29 6 -58 6z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="636" 
d="M353 249v-249h-69v249q-13 22 -34.5 58.5t-46.5 81t-52.5 93t-52.5 93t-46 81t-34 58.5h77q18 -30 44 -76t55.5 -97.5t59 -104.5t53.5 -97l12 -30l12 30q25 44 54 97t57.5 104.5t54 97.5t43.5 76h78zM189 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5
t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM368 813q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5z" />
    <glyph glyph-name="Etilde" unicode="&#x1ebc;" horiz-adv-x="612" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM335 806q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5
t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="603" 
d="M102 0v714h173q45 0 87 -12.5t74.5 -38.5t52 -65t19.5 -93q0 -60 -27 -114t-89 -83l24 -40t33 -54.5t37.5 -61.5t37.5 -61t33 -53t24 -38h-83q-8 12 -21 33t-29 47t-34 55t-35 57t-32.5 53t-26.5 43q-10 -2 -21 -2h-22h-107v-286h-68zM170 351h106q48 0 80 16t50.5 39.5
t26 50t7.5 48.5q0 27 -10.5 53t-30.5 46.5t-51 33t-71 12.5h-107v-299zM339 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="273" 
d="M102 714h69v-714h-69v714zM128 793q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35
t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="564" 
d="M102 0v714h68v-649h380v-65h-448zM387 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="612" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM216 763v39h232v-39h-232z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="683" 
d="M618 327v-284q-41 -24 -93 -37.5t-109 -13.5q-84 0 -151.5 27.5t-115 75.5t-73.5 113t-26 140q0 81 29.5 149.5t80 118.5t118 78t142.5 28q51 0 103.5 -13t93.5 -38l-34 -56q-33 20 -77.5 31.5t-86.5 11.5q-61 0 -115.5 -24t-96 -65t-65.5 -97t-24 -119q0 -60 21.5 -114
t60.5 -94.5t94 -64t122 -23.5q33 0 69.5 7.5t69.5 22.5v180h-154v60h217zM425 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="537" 
d="M152 463v39h81v147h-188v65h447v-65h-190v-147h82v-39h-82v-463h-69v463h-81z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="294" 
d="M276 340q-30 0 -57.5 -9.5t-48.5 -26.5t-33.5 -41.5t-12.5 -54.5v-208h-55v400h47l5 -103q12 26 31 46.5t41.5 35t47 22.5t46.5 8zM189 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM132 487v39h232v-39h-232z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="531" 
d="M40 199q0 45 16 83t44 66.5t65.5 44.5t79.5 16q33 0 60 -9t47.5 -22.5t34.5 -29.5t20 -31q0 20 1.5 41t2.5 42h51v-393q0 -43 -13 -79.5t-39 -63t-66 -41.5t-94 -15q-57 0 -99.5 21t-86.5 62l36 38q39 -38 74.5 -53t73.5 -15q42 0 71.5 10.5t48.5 29t28 45t10 58.5v14
t1 23q0 3 0.5 9.5t0.5 14t0.5 14t0.5 8.5q-19 -38 -60.5 -66.5t-102.5 -28.5q-44 0 -81.5 16t-65.5 44t-43.5 65.5t-15.5 81.5zM95 201q0 -33 12 -61t33 -49t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5
t-33 -48.5t-12 -61zM232 507l86 208h54q-20 -53 -39 -104t-39 -104h-62z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="286" 
d="M29 487v39h70v49h55v-49h107v-39h-107v-91h119v-50h-119v-346h-55v346h-80v50h80v91h-70z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="646" 
d="M632 0q-16 36 -40.5 95.5t-53.5 130.5t-60.5 147.5t-60.5 147.5t-54 130t-40 96q-16 -37 -40.5 -96t-53.5 -130t-60 -147.5t-60 -147.5t-54 -130.5t-40 -95.5h68q20 46 47 113t56 142h274q29 -75 56 -142t47 -113h69zM313 572q2 4 5 13.5t5 17.5q2 -8 5 -17.5t5 -13.5
q20 -47 47 -115t57 -143h-228q29 75 56.5 143t47.5 115zM207 771v39h232v-39h-232z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="273" 
d="M171 0h-69v714h69v-714zM142 0h-38v-7q0 -14 -4 -22.5t-10.5 -14t-14 -9.5t-13.5 -8q-22 -16 -31 -36t-9 -41q0 -46 26.5 -75t71.5 -29q32 0 58.5 17t37.5 52q-19 10 -35 17q-5 -18 -20 -29.5t-36 -11.5q-27 0 -42.5 18t-15.5 44q0 23 11.5 36t26 22.5t26 20t11.5 28.5
v28z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="191" 
d="M123 0h-55v400h55v-400zM86 499q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5
t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="195" 
d="M125 0h-55v766h55v-766zM138 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="612" 
d="M103 0v714h450v-62h-382v-261h342v-64h-342v-263h396v-64h-464zM297 799q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="273" 
d="M102 714h69v-714h-69v714zM23 771v39h232v-39h-232z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="812" 
d="M710 714v-747q-29 35 -71.5 83.5t-92 103.5t-102 112.5t-101.5 112.5t-89.5 102.5t-66.5 82.5q-3 4 -9 11.5t-11 14.5l3 -32v-558h-68v747q29 -37 71 -85t91.5 -102t101.5 -111t100.5 -110.5t90.5 -101t69 -82.5q2 -5 8 -12.5t11 -14.5l-3 33v553h68zM434 -21l-86 -208
h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="623" 
d="M537 0q-75 95 -144 184.5t-144 185.5q-18 -18 -36 -35t-37 -34q0 -78 0.5 -150.5t0.5 -150.5h-75v714h75v-300q0 -8 -1 -16t-2 -17q5 7 9.5 12t9.5 12q80 75 157 154t157 155h88q-73 -74 -146 -148.5t-147 -148.5q84 -107 161 -208.5t160 -208.5h-86zM382 -21l-86 -208
h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="483" 
d="M69 400h47l5 -103q22 50 65 81t103 31q59 0 93.5 -33t36.5 -87v-289h-55v270q-2 34 -24 58t-66 26q-32 0 -59 -10.5t-47.5 -29.5t-32 -45t-11.5 -57v-212h-55v400zM283 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="413" 
d="M339 410l33 -34q-38 -35 -72.5 -68.5t-72.5 -68.5l174 -239h-64l-149 202l-65 -60v-142h-55v766h55v-558q56 52 108 100.5t108 101.5zM282 -21l-86 -208h-54q20 53 39 104t39 104h62z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="828" 
d="M49 356q0 75 28.5 141.5t78 116.5t116 79t141.5 29t142 -29t116.5 -79t78.5 -116.5t29 -141.5t-29 -141.5t-78.5 -116t-116.5 -78t-142 -28.5t-141.5 28.5t-116 78t-78 116t-28.5 141.5zM117 356q0 -62 23.5 -116.5t63.5 -95t94 -64t115 -23.5t115.5 23.5t95 64t64 95
t23.5 116.5t-23.5 116.5t-64 95.5t-95 64.5t-115.5 23.5q-62 0 -115.5 -23.5t-94 -64.5t-63.5 -95.5t-23 -116.5zM300 771v39h232v-39h-232z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM240 771v39h232v-39h-232z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="498" 
d="M39 200q0 43 16.5 81.5t45 67t67 45t82.5 16.5t82.5 -16.5t66.5 -45t44 -67t16 -81.5t-16 -81t-44.5 -66t-66.5 -44.5t-83 -16.5q-44 0 -82 15.5t-66.5 43.5t-45 66t-16.5 83zM94 200q0 -32 12 -60t33 -49t49 -33t60 -12t60.5 12t49.5 33t33 49t12 60t-12 60.5t-33 49.5
t-49.5 33t-60.5 12t-60 -12t-49 -33t-33 -49.5t-12 -60.5zM137 487v39h232v-39h-232z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM123 487v39h232v-39h-232z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="530" 
d="M39 198q0 43 16.5 81.5t45 67.5t66.5 45.5t81 16.5q30 0 56 -8t46.5 -21.5t34.5 -30t21 -32.5l4 83h51v-400h-55v41q0 11 0.5 21t0.5 19q-7 -17 -21 -32.5t-34.5 -28.5t-46.5 -20.5t-57 -7.5q-45 0 -83.5 16.5t-66 44.5t-43.5 65.5t-16 79.5zM94 201q0 -33 12 -61t33 -49
t49.5 -33t61.5 -12t61.5 12t49.5 33t33 49t12 61q0 32 -12 60t-33 49t-49.5 33t-61.5 12t-61.5 -12t-49.5 -32.5t-33 -48.5t-12 -61zM155 487v39h232v-39h-232z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="695" 
d="M388 0h-38v-7v-1h-2q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -53 -16.5 -96t-45 -75t-67.5 -51.5t-85 -25.5v5zM350 -8q0 -14 -4.5 -22
t-10.5 -13.5t-13.5 -9.5t-13.5 -8q-22 -16 -31 -36t-9 -41q0 -46 26.5 -75t71.5 -29q32 0 58.5 17t37.5 52q-19 10 -35 17q-5 -18 -20 -29.5t-36 -11.5q-27 0 -42.5 18t-15.5 44q0 23 11.5 36t26 22.5t26 20t11.5 28.5v23q-10 -1 -19 -2t-19 -1z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="191" 
d="M123 0h-55v400h55v-400zM-20 487v39h232v-39h-232z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="483" 
d="M404 0h-37l-5 103q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405h-10v5zM367 0h-1v-7q0 -14 -4 -22.5t-10.5 -14t-14 -9.5t-13.5 -8q-22 -16 -31 -36t-9 -41q0 -46 26.5 -75
t71.5 -29q32 0 58.5 17t37.5 52q-19 10 -35 17q-5 -18 -20 -29.5t-36 -11.5q-27 0 -42.5 18t-15.5 44q0 23 11.5 36t26 22.5t26 20t11.5 28.5v23h-36z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="696" 
d="M348 -8q-54 0 -100.5 17.5t-80.5 49.5t-53.5 78.5t-19.5 105.5v471h68v-468q0 -43 14 -78t39 -59.5t59 -38t74 -13.5q39 0 73 13.5t59 38t39.5 59.5t14.5 78v468h68v-471q0 -59 -19.5 -105.5t-54 -78.5t-81 -49.5t-99.5 -17.5zM348 793q-16 17 -27.5 22.5t-25.5 5.5
q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M414 -5h-46l-6 108q-21 -50 -64 -81t-104 -31q-59 0 -93.5 33t-36.5 87v289h55v-270q2 -34 24 -58t66 -26q31 0 58.5 10.5t47.5 29t32 44.5t12 57v213h55v-405zM238 499q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35
t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="456" 
d="M399 48q-29 -26 -68 -41t-81 -15q-44 0 -82.5 16.5t-67 44.5t-45 66t-16.5 81t16.5 81.5t45 67t66.5 45t81 16.5q66 0 114.5 -40t71.5 -102q-34 -13 -75.5 -29.5t-85 -34t-86.5 -35t-80 -32.5q18 -41 55.5 -66t85.5 -25q32 0 61 11t49 30zM365 282q-17 34 -48 53.5
t-69 19.5q-32 0 -60 -12.5t-49 -33.5t-33 -48.5t-12 -58.5q0 -16 3 -28q31 13 66 27t70 28.5t69 28t63 24.5zM204 545q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="191" 
d="M56 545q0 17 12.5 28.5t27.5 11.5t27 -11.5t12 -28.5q0 -18 -12 -29t-27 -11t-27.5 11t-12.5 29zM123 0h-55v400h55v-400zM106 0h-38v-7q0 -14 -4 -22.5t-10.5 -14t-14 -9.5t-13.5 -8q-22 -16 -31 -36t-9 -41q0 -46 26.5 -75t71.5 -29q32 0 58.5 17t37.5 52
q-19 10 -35 17q-5 -18 -20 -29.5t-36 -11.5q-27 0 -42.5 18t-15.5 44q0 23 11.5 36t26 22.5t26 20t11.5 28.5v28z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="539" 
d="M64 41q0 17 12 29t28 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28 11.5t-12 29.5zM228 41q0 17 12.5 29t28.5 12q15 0 27.5 -12t12.5 -29q0 -18 -12.5 -29.5t-27.5 -11.5q-16 0 -28.5 11.5t-12.5 29.5zM393 41q0 17 12.5 29t28.5 12q14 0 27 -12
t13 -29q0 -18 -13 -29.5t-27 -11.5q-16 0 -28.5 11.5t-12.5 29.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="982" 
d="M448 112q0 -27 18 -45.5t46 -18.5t46 18.5t18 45.5t-18 45.5t-46 18.5t-46 -18.5t-18 -45.5zM512 -9q-25 0 -47.5 9.5t-39 26t-26 38.5t-9.5 48q0 25 9.5 46.5t26 38t39 26t47.5 9.5t47 -9.5t38.5 -26t26.5 -38t10 -46.5q0 -26 -10 -48t-26.5 -38.5t-38.5 -26t-47 -9.5z
M102 392q0 -27 18 -45.5t46 -18.5q27 0 45 18.5t18 45.5t-18 45t-45 18q-28 0 -46 -18t-18 -45zM166 271q-25 0 -47.5 9.5t-39 25.5t-26.5 38t-10 48q0 25 10 47t26.5 38.5t39 26t47.5 9.5q24 0 46.5 -9.5t39 -26t26.5 -38.5t10 -47q0 -26 -10 -48t-26.5 -38t-39 -25.5
t-46.5 -9.5zM154 0q75 127 150 252t150 252h64q-75 -127 -150 -252t-150 -252h-64zM758 112q0 -27 18 -45.5t46 -18.5t46 18.5t18 45.5t-18 45.5t-46 18.5t-46 -18.5t-18 -45.5zM822 -9q-25 0 -47.5 9.5t-39 26t-26 38.5t-9.5 48q0 25 9.5 46.5t26 38t39 26t47.5 9.5
t47 -9.5t38.5 -26t26.5 -38t10 -46.5q0 -26 -10 -48t-26.5 -38.5t-38.5 -26t-47 -9.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="450" 
d="M234 665q30 -45 60 -91t61 -91h-44q-8 12 -18 26t-20.5 29t-20.5 29.5t-18 25.5q-8 -11 -18 -25.5t-21 -29.5t-21 -29t-18 -26h-43z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="596" 
d="M310 499q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5
q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="519" 
d="M28 346v54h69v117q0 122 58.5 183.5t155.5 65.5v-53q-74 -2 -116.5 -53.5t-42.5 -142.5v-117h298v-400h-55v346h-243v-346h-55v346h-69z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="513" 
d="M28 345v54h69v118q0 122 58.5 183.5t155.5 65.5h132v-766h-55v713h-77q-74 -2 -116.5 -53.5t-42.5 -142.5v-118h131v-54h-131v-345h-55v345h-69z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="478" 
d="M217 511q0 -17 12 -29t29 -12t29 12t12 29t-12 29t-29 12t-29 -12t-12 -29zM182 512q0 31 22 53t54 22t54 -22t22 -53q0 -32 -22 -54t-54 -22t-54 22t-22 54z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="340" 
d="M0 0l281 761h59l-281 -761h-59z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="361" 
d="M177 345v-266q0 -122 -58.5 -183.5t-155.5 -64.5v52q74 3 116.5 54t42.5 142v266h-69v54h69v118q0 122 58.5 183.5t155.5 65.5v-53q-74 -2 -116.5 -53.5t-42.5 -142.5v-118h131v-54h-131z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="342" 
d="M142 32v367h-79v57h79v140h58v-140h79v-57h-79v-367h-58z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="401" 
d="M171 32v209h-121v57h121v101h-79v57h79v140h58v-140h79v-57h-79v-101h122v-57h-122v-209h-58z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="586" 
d="M252 768q10 -15 27.5 -40t36 -51t35.5 -50t26 -37l126 178v-270h-26v186q0 3 0.5 6.5t0.5 5.5q-9 -13 -23 -33t-28.5 -41.5t-28 -40.5t-21.5 -31q-7 10 -22.5 31.5t-31.5 45t-29.5 43.5t-17.5 26q0 -2 0.5 -5.5t0.5 -6.5v-186h-25v270zM204 757v-24h-69v-235h-23v235h-69
v24h161z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="505" 
d="M434 172h-363v57h363v-57z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="323" 
d="M132 633q0 46 8.5 70.5t23.5 35.5t34.5 12t40.5 1h54v-57h-49q-31 0 -42 -22t-11 -85v-606q0 -52 -8.5 -79t-22.5 -39t-34 -13t-42 -1h-54v57h49q31 0 42 22t11 85v619z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="462" 
d="M166 -66h-58v822h246v-822h-58v765h-130v-765z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="400" 
d="M147 282v-282h-59v282h-53v57h328v-57h-48v-282h-58v282h-110z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="544" 
d="M41 400h69q9 55 30.5 103t55.5 83.5t79.5 56t102.5 20.5q38 0 67 -9.5t44 -17.5l-26 -51q-20 11 -40.5 16t-46.5 5q-44 0 -79.5 -14.5t-62 -42t-43 -65.5t-23.5 -84h225v-33h-228q-1 -7 -1 -13.5v-13.5q0 -13 0.5 -24.5t1.5 -22.5h227v-33h-222q9 -53 30 -92.5t49 -65.5
t60 -39.5t64 -13.5q23 0 46 7t44 19l31 -51q-56 -32 -122 -32q-49 0 -93 19t-79 54t-59 84t-33 111h-67v33h64q-1 9 -1 18v19v18t1 19h-65v33z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="560" 
d="M489 297v-79q-112 64 -221 127.5t-220 127.5q111 63 220 127t221 127v-78q-77 -44 -153.5 -87.5t-152.5 -88.5q76 -45 152.5 -88.5t153.5 -87.5zM450 74h-363v57h363v-57z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="560" 
d="M81 297q76 44 152.5 87.5t152.5 88.5q-76 45 -152.5 88.5t-152.5 87.5v78q111 -63 220 -127t221 -127q-112 -64 -221 -127.5t-220 -127.5v79zM80 131h363v-57h-363v57z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="672" 
d="M590 380v-58h-192q-29 -35 -58 -69.5t-58 -68.5h308v-57h-357l-115 -136l-33 30l90 106h-95v57h143q29 34 58.5 68.5t58.5 69.5h-260v58h309l130 154l33 -31l-105 -123h143z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="453" 
d="M218 199q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5t-27.5 -15.5t-30 -5
q-23 0 -41 9.5t-34 25.5zM219 349q-16 17 -27.5 22.5t-25.5 5.5q-8 0 -17 -2t-18 -7.5t-15.5 -15.5t-8.5 -25l-33 2q1 20 9.5 35t21 25.5t27.5 15.5t30 5q23 0 41 -9t34 -26t27.5 -22.5t25.5 -5.5q7 0 16.5 2t18.5 7.5t15.5 15.5t8.5 25l33 -2q-1 -20 -9.5 -35t-21 -25.5
t-27.5 -15.5t-30 -5q-23 0 -41 9.5t-34 25.5z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="472" 
d="M140 -16h273l22 -56h-372l114 414l-114 414h372l-22 -57h-273l100 -357z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="679" 
d="M66 57h149q-29 15 -55.5 36.5t-46.5 51.5t-31.5 70.5t-11.5 93.5q0 61 18 114.5t52.5 93t85 62t115.5 22.5q72 0 123.5 -26.5t84.5 -69t48 -96.5t13 -111q-2 -50 -13.5 -88.5t-30 -67.5t-42 -49.5t-49.5 -35.5h136v-57h-212v57q31 13 59 34.5t48.5 51.5t33 68t12.5 85
q0 54 -13.5 99t-40 78t-65.5 52t-91 19q-53 0 -92.5 -19t-66 -52t-40.5 -77t-14 -95q0 -48 13 -87.5t35.5 -69.5t51.5 -52t60 -35v-57h-223v57z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="540" 
d="M32 361h111l110 -237l222 485h43l-265 -585l-137 300h-84v37z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="414" 
d="M207 735l162 -465h-324zM101 306h212l-106 313z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="416" 
d="M46 380l162 355l162 -355l-162 -356zM90 380l118 -256l119 256l-119 255z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="500" 
d="M340 225q-26 0 -50 9t-39 26q-16 -17 -40 -26t-50 -9q-23 0 -40.5 7.5t-28.5 18.5t-16.5 25t-5.5 28t5.5 28t17 25.5t28.5 18.5t40 7q26 0 50 -9.5t40 -26.5q15 17 39.5 26.5t50.5 9.5q23 0 39.5 -7.5t27.5 -18.5t16.5 -25.5t5.5 -27.5q0 -14 -5 -28t-16 -25t-28 -18.5
t-41 -7.5zM232 306q-2 17 -23.5 28.5t-46.5 11.5q-28 0 -40.5 -13t-12.5 -29q0 -15 12 -28.5t41 -13.5q24 0 46 11t24 29v2v2zM270 302q2 -18 24 -29t46 -11q29 0 41 13.5t12 28.5q0 16 -13 29t-40 13q-26 0 -48 -12t-22 -30v-2z" />
    <hkern u1="&#x20;" u2="W" k="37" />
    <hkern u1="&#x20;" u2="V" k="39" />
    <hkern u1="&#x20;" u2="w" k="22" />
    <hkern u1="&#x20;" u2="v" k="23" />
    <hkern u1="t" u2="&#xd0;" k="32" />
    <hkern u1="b" u2="&#xd0;" k="36" />
    <hkern u1="l" u2="&#x129;" k="-12" />
    <hkern u1="l" u2="&#xd0;" k="24" />
    <hkern u1="m" u2="&#xd0;" k="26" />
    <hkern u1="i" u2="&#xd0;" k="25" />
    <hkern u1="h" u2="&#xd0;" k="26" />
    <hkern u1="c" u2="&#xd0;" k="38" />
    <hkern u1="q" u2="&#xd0;" k="24" />
    <hkern u1="u" u2="&#xd0;" k="24" />
    <hkern u1="r" u2="&#xd0;" k="30" />
    <hkern u1="e" u2="&#xd0;" k="42" />
    <hkern u1="p" u2="&#xd0;" k="36" />
    <hkern u1="&#xed;" u2="&#x201d;" k="-6" />
    <hkern u1="&#xed;" u2="&#x2a;" k="-14" />
    <hkern u1="&#xed;" u2="&#x2019;" k="-6" />
    <hkern u1="&#xed;" u2="&#x3f;" k="-28" />
    <hkern u1="&#x2c;" u2="&#x126;" k="17" />
    <hkern u1="&#x2c;" u2="&#x110;" k="16" />
    <hkern u1="&#x2c;" u2="&#x141;" k="17" />
    <hkern u1="&#x2c;" u2="&#xd0;" k="17" />
    <hkern u1="&#x2e;" u2="&#x126;" k="17" />
    <hkern u1="&#x2e;" u2="&#x110;" k="16" />
    <hkern u1="&#x2e;" u2="&#x141;" k="17" />
    <hkern u1="&#x2e;" u2="&#xd0;" k="17" />
    <hkern u1="f" u2="&#x2122;" k="-35" />
    <hkern u1="f" u2="&#xd0;" k="18" />
    <hkern u1="f" u2="]" k="-26" />
    <hkern u1="f" u2="W" k="-9" />
    <hkern u1="f" u2="V" k="-14" />
    <hkern u1="f" u2="\" k="-22" />
    <hkern u1="f" u2="&#x2f;" k="10" />
    <hkern u1="f" u2="&#x7d;" k="-25" />
    <hkern u1="f" u2="&#x29;" k="-36" />
    <hkern u1="f" u2="&#x20;" k="19" />
    <hkern u1="v" u2="&#x2122;" k="9" />
    <hkern u1="v" u2="&#xd0;" k="37" />
    <hkern u1="v" u2="]" k="38" />
    <hkern u1="v" u2="X" k="59" />
    <hkern u1="v" u2="W" k="69" />
    <hkern u1="v" u2="V" k="75" />
    <hkern u1="v" u2="\" k="29" />
    <hkern u1="v" u2="&#x2f;" k="12" />
    <hkern u1="v" u2="&#x7d;" k="32" />
    <hkern u1="v" u2="&#x29;" k="44" />
    <hkern u1="v" u2="&#x3f;" k="13" />
    <hkern u1="v" u2="&#xe6;" k="7" />
    <hkern u1="v" u2="&#x20;" k="23" />
    <hkern u1="j" u2="&#x129;" k="-7" />
    <hkern u1="j" u2="&#xd0;" k="25" />
    <hkern u1="s" u2="&#xd0;" k="42" />
    <hkern u1="k" u2="&#xd0;" k="40" />
    <hkern u1="y" u2="&#xd0;" k="38" />
    <hkern u1="w" u2="&#x2122;" k="9" />
    <hkern u1="w" u2="&#xd0;" k="37" />
    <hkern u1="w" u2="]" k="38" />
    <hkern u1="w" u2="X" k="57" />
    <hkern u1="w" u2="W" k="69" />
    <hkern u1="w" u2="V" k="74" />
    <hkern u1="w" u2="\" k="29" />
    <hkern u1="w" u2="&#x2f;" k="10" />
    <hkern u1="w" u2="&#x7d;" k="31" />
    <hkern u1="w" u2="&#x29;" k="43" />
    <hkern u1="w" u2="&#x3f;" k="12" />
    <hkern u1="w" u2="&#xe6;" k="7" />
    <hkern u1="w" u2="&#x20;" k="23" />
    <hkern u1="x" u2="&#x2122;" k="12" />
    <hkern u1="x" u2="&#xd0;" k="37" />
    <hkern u1="x" u2="]" k="34" />
    <hkern u1="x" u2="W" k="86" />
    <hkern u1="x" u2="V" k="91" />
    <hkern u1="x" u2="\" k="33" />
    <hkern u1="x" u2="&#x7d;" k="25" />
    <hkern u1="x" u2="&#x2a;" k="8" />
    <hkern u1="x" u2="&#x29;" k="22" />
    <hkern u1="x" u2="&#x3f;" k="19" />
    <hkern u1="z" u2="&#xd0;" k="38" />
    <hkern u1="o" u2="&#xd0;" k="36" />
    <hkern u1="&#xe6;" u2="&#xd0;" k="42" />
    <hkern u1="A" u2="&#x126;" k="9" />
    <hkern u1="A" u2="&#x141;" k="9" />
    <hkern u1="A" u2="&#xd0;" k="9" />
    <hkern u1="&#xef;" u2="&#xba;" k="-17" />
    <hkern u1="&#xef;" u2="&#xaa;" k="-20" />
    <hkern u1="&#xef;" u2="&#x27;" k="-6" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-37" />
    <hkern u1="&#xef;" u2="&#x22;" k="-6" />
    <hkern u1="B" u2="&#x129;" k="12" />
    <hkern u1="B" u2="&#x126;" k="7" />
    <hkern u1="B" u2="&#x142;" k="26" />
    <hkern u1="B" u2="&#x141;" k="7" />
    <hkern u1="B" u2="&#xd0;" k="7" />
    <hkern u1="B" u2="]" k="21" />
    <hkern u1="B" u2="W" k="14" />
    <hkern u1="B" u2="V" k="16" />
    <hkern u1="B" u2="&#x7d;" k="15" />
    <hkern u1="B" u2="&#x29;" k="19" />
    <hkern u1="B" u2="&#xe6;" k="18" />
    <hkern u1="B" u2="x" k="36" />
    <hkern u1="B" u2="w" k="31" />
    <hkern u1="B" u2="v" k="34" />
    <hkern u1="D" u2="&#x129;" k="-12" />
    <hkern u1="D" u2="&#x126;" k="10" />
    <hkern u1="D" u2="&#x110;" k="8" />
    <hkern u1="D" u2="&#x141;" k="10" />
    <hkern u1="D" u2="&#xd0;" k="10" />
    <hkern u1="E" u2="&#x142;" k="19" />
    <hkern u1="H" u2="&#x142;" k="18" />
    <hkern u1="I" u2="&#x142;" k="18" />
    <hkern u1="J" u2="&#x142;" k="19" />
    <hkern u1="M" u2="&#x142;" k="18" />
    <hkern u1="N" u2="&#x142;" k="18" />
    <hkern u1="P" u2="&#x12b;" k="-4" />
    <hkern u1="P" u2="&#x129;" k="-42" />
    <hkern u1="P" u2="&#x135;" k="-8" />
    <hkern u1="P" u2="&#x131;" k="46" />
    <hkern u1="P" u2="&#x26;" k="11" />
    <hkern u1="P" u2="&#xee;" k="-6" />
    <hkern u1="P" u2="&#x2f;" k="31" />
    <hkern u1="P" u2="&#x36;" k="11" />
    <hkern u1="P" u2="&#x34;" k="33" />
    <hkern u1="P" u2="&#xef;" k="-16" />
    <hkern u1="P" u2="&#xe6;" k="52" />
    <hkern u1="P" u2="x" k="14" />
    <hkern u1="P" u2="w" k="7" />
    <hkern u1="P" u2="v" k="6" />
    <hkern u1="P" u2="&#xed;" k="21" />
    <hkern u1="P" u2="&#x20;" k="29" />
    <hkern u1="R" u2="&#x129;" k="11" />
    <hkern u1="R" u2="&#x126;" k="10" />
    <hkern u1="R" u2="&#x110;" k="8" />
    <hkern u1="R" u2="&#x142;" k="27" />
    <hkern u1="R" u2="&#x141;" k="10" />
    <hkern u1="R" u2="&#xd0;" k="10" />
    <hkern u1="U" u2="&#x129;" k="9" />
    <hkern u1="Y" u2="&#x12b;" k="53" />
    <hkern u1="Y" u2="&#x129;" k="46" />
    <hkern u1="Y" u2="&#x16d;" k="102" />
    <hkern u1="Y" u2="&#x135;" k="48" />
    <hkern u1="Y" u2="&#x11f;" k="135" />
    <hkern u1="Y" u2="&#x131;" k="95" />
    <hkern u1="Y" u2="&#x126;" k="13" />
    <hkern u1="Y" u2="&#x159;" k="69" />
    <hkern u1="Y" u2="&#x11b;" k="136" />
    <hkern u1="Y" u2="&#x10d;" k="144" />
    <hkern u1="Y" u2="&#x103;" k="134" />
    <hkern u1="Y" u2="&#x110;" k="10" />
    <hkern u1="Y" u2="&#x161;" k="63" />
    <hkern u1="Y" u2="&#x142;" k="24" />
    <hkern u1="Y" u2="&#x141;" k="13" />
    <hkern u1="Y" u2="&#xdf;" k="96" />
    <hkern u1="Y" u2="&#xf0;" k="132" />
    <hkern u1="Y" u2="&#xd0;" k="13" />
    <hkern u1="Y" u2="&#xee;" k="48" />
    <hkern u1="Y" u2="&#xef;" k="47" />
    <hkern u1="Y" u2="&#xec;" k="26" />
    <hkern u1="Y" u2="&#xed;" k="93" />
    <hkern u1="F" u2="&#x131;" k="55" />
    <hkern u1="F" u2="&#x2f;" k="31" />
    <hkern u1="F" u2="&#x36;" k="11" />
    <hkern u1="F" u2="&#x34;" k="29" />
    <hkern u1="F" u2="&#x40;" k="13" />
    <hkern u1="F" u2="&#xe6;" k="69" />
    <hkern u1="F" u2="x" k="44" />
    <hkern u1="F" u2="w" k="31" />
    <hkern u1="F" u2="v" k="30" />
    <hkern u1="F" u2="&#xed;" k="56" />
    <hkern u1="F" u2="&#x20;" k="23" />
    <hkern u1="C" u2="&#x142;" k="17" />
    <hkern u1="Q" u2="&#x129;" k="-16" />
    <hkern u1="Q" u2="&#x126;" k="10" />
    <hkern u1="Q" u2="&#x110;" k="8" />
    <hkern u1="Q" u2="&#x141;" k="10" />
    <hkern u1="Q" u2="&#xd0;" k="10" />
    <hkern u1="T" u2="&#x16d;" k="96" />
    <hkern u1="T" u2="&#x159;" k="69" />
    <hkern u1="T" u2="&#x161;" k="63" />
    <hkern u1="T" u2="&#x142;" k="39" />
    <hkern u1="T" u2="&#xdf;" k="94" />
    <hkern u1="&#x40;" u2="&#xd0;" k="10" />
    <hkern u1="&#x40;" u2="W" k="43" />
    <hkern u1="&#x40;" u2="V" k="47" />
    <hkern u1="&#x32;" u2="]" k="15" />
    <hkern u1="&#x32;" u2="W" k="23" />
    <hkern u1="&#x32;" u2="V" k="25" />
    <hkern u1="&#x31;" u2="]" k="13" />
    <hkern u1="&#x31;" u2="W" k="15" />
    <hkern u1="&#x31;" u2="V" k="16" />
    <hkern u1="&#x31;" u2="&#x7d;" k="10" />
    <hkern u1="&#x31;" u2="&#x29;" k="11" />
    <hkern u1="&#x33;" u2="W" k="12" />
    <hkern u1="&#x33;" u2="V" k="13" />
    <hkern u1="&#x34;" u2="]" k="24" />
    <hkern u1="&#x34;" u2="W" k="28" />
    <hkern u1="&#x34;" u2="V" k="30" />
    <hkern u1="&#x34;" u2="\" k="12" />
    <hkern u1="&#x34;" u2="&#x7d;" k="18" />
    <hkern u1="&#x34;" u2="&#x29;" k="25" />
    <hkern u1="&#x35;" u2="]" k="11" />
    <hkern u1="&#x35;" u2="W" k="13" />
    <hkern u1="&#x35;" u2="V" k="15" />
    <hkern u1="&#x36;" u2="]" k="27" />
    <hkern u1="&#x36;" u2="W" k="27" />
    <hkern u1="&#x36;" u2="V" k="29" />
    <hkern u1="&#x36;" u2="\" k="12" />
    <hkern u1="&#x36;" u2="&#x7d;" k="21" />
    <hkern u1="&#x36;" u2="&#x29;" k="26" />
    <hkern u1="&#x36;" u2="&#x37;" k="11" />
    <hkern u1="&#x37;" u2="&#x2212;" k="20" />
    <hkern u1="&#x37;" u2="&#x2044;" k="32" />
    <hkern u1="&#x37;" u2="&#xd7;" k="47" />
    <hkern u1="&#x37;" u2="&#xb7;" k="21" />
    <hkern u1="&#x37;" u2="&#xf7;" k="34" />
    <hkern u1="&#x37;" u2="&#xa2;" k="40" />
    <hkern u1="&#x37;" u2="&#x2f;" k="35" />
    <hkern u1="&#x37;" u2="&#x2b;" k="44" />
    <hkern u1="&#x37;" u2="&#x23;" k="11" />
    <hkern u1="&#x37;" u2="&#x38;" k="10" />
    <hkern u1="&#x37;" u2="&#x36;" k="31" />
    <hkern u1="&#x37;" u2="&#x34;" k="35" />
    <hkern u1="&#x38;" u2="]" k="29" />
    <hkern u1="&#x38;" u2="W" k="31" />
    <hkern u1="&#x38;" u2="V" k="33" />
    <hkern u1="&#x38;" u2="\" k="15" />
    <hkern u1="&#x38;" u2="&#x7d;" k="23" />
    <hkern u1="&#x38;" u2="&#x29;" k="29" />
    <hkern u1="&#x22;" u2="&#x129;" k="-27" />
    <hkern u1="&#x23;" u2="&#x37;" k="11" />
    <hkern u1="&#x28;" u2="&#x12f;" k="-21" />
    <hkern u1="&#x28;" u2="&#x126;" k="15" />
    <hkern u1="&#x28;" u2="&#x110;" k="13" />
    <hkern u1="&#x28;" u2="&#x141;" k="15" />
    <hkern u1="&#x28;" u2="&#xd0;" k="15" />
    <hkern u1="&#x28;" u2="&#x30;" k="31" />
    <hkern u1="&#x28;" u2="&#x39;" k="25" />
    <hkern u1="&#x28;" u2="&#x28;" k="15" />
    <hkern u1="&#x28;" u2="&#x38;" k="28" />
    <hkern u1="&#x28;" u2="&#x36;" k="35" />
    <hkern u1="&#x28;" u2="&#x34;" k="32" />
    <hkern u1="&#x28;" u2="&#xe6;" k="36" />
    <hkern u1="&#x28;" u2="x" k="22" />
    <hkern u1="&#x28;" u2="w" k="43" />
    <hkern u1="&#x28;" u2="v" k="43" />
    <hkern u1="&#x29;" u2="]" k="14" />
    <hkern u1="&#x29;" u2="&#x7d;" k="11" />
    <hkern u1="&#x29;" u2="&#x29;" k="15" />
    <hkern u1="&#x2a;" u2="&#x12b;" k="-20" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-46" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-9" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-7" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-32" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="19" />
    <hkern u1="&#x2a;" u2="&#xec;" k="-11" />
    <hkern u1="&#x2b;" u2="&#x39;" k="12" />
    <hkern u1="&#x2b;" u2="&#x37;" k="44" />
    <hkern u1="&#x2b;" u2="&#x33;" k="13" />
    <hkern u1="&#x2b;" u2="&#x31;" k="20" />
    <hkern u1="&#x2b;" u2="&#x32;" k="15" />
    <hkern u1="&#x39;" u2="&#x2044;" k="14" />
    <hkern u1="&#x39;" u2="&#xd7;" k="12" />
    <hkern u1="&#x39;" u2="]" k="33" />
    <hkern u1="&#x39;" u2="X" k="23" />
    <hkern u1="&#x39;" u2="W" k="19" />
    <hkern u1="&#x39;" u2="V" k="20" />
    <hkern u1="&#x39;" u2="&#x2f;" k="17" />
    <hkern u1="&#x39;" u2="&#x7d;" k="28" />
    <hkern u1="&#x39;" u2="&#x2b;" k="11" />
    <hkern u1="&#x39;" u2="&#x29;" k="32" />
    <hkern u1="&#x39;" u2="&#x33;" k="12" />
    <hkern u1="&#x7c;" u2="&#x129;" k="-8" />
    <hkern u1="&#x7c;" u2="W" k="12" />
    <hkern u1="&#x7c;" u2="V" k="13" />
    <hkern u1="&#x7b;" u2="&#x12f;" k="-9" />
    <hkern u1="&#x7b;" u2="&#x126;" k="15" />
    <hkern u1="&#x7b;" u2="&#x110;" k="13" />
    <hkern u1="&#x7b;" u2="&#x141;" k="15" />
    <hkern u1="&#x7b;" u2="&#xd0;" k="15" />
    <hkern u1="&#x7b;" u2="&#x30;" k="26" />
    <hkern u1="&#x7b;" u2="&#x39;" k="23" />
    <hkern u1="&#x7b;" u2="&#x28;" k="12" />
    <hkern u1="&#x7b;" u2="&#x38;" k="23" />
    <hkern u1="&#x7b;" u2="&#x36;" k="29" />
    <hkern u1="&#x7b;" u2="&#x34;" k="21" />
    <hkern u1="&#x7b;" u2="&#xe6;" k="32" />
    <hkern u1="&#x7b;" u2="x" k="25" />
    <hkern u1="&#x7b;" u2="w" k="32" />
    <hkern u1="&#x7b;" u2="v" k="33" />
    <hkern u1="&#x30;" u2="]" k="30" />
    <hkern u1="&#x30;" u2="X" k="13" />
    <hkern u1="&#x30;" u2="W" k="26" />
    <hkern u1="&#x30;" u2="V" k="28" />
    <hkern u1="&#x30;" u2="\" k="11" />
    <hkern u1="&#x30;" u2="&#x7d;" k="26" />
    <hkern u1="&#x30;" u2="&#x29;" k="31" />
    <hkern u1="&#x30;" u2="&#x37;" k="12" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="38" />
    <hkern u1="&#x2f;" u2="&#x30;" k="11" />
    <hkern u1="&#x2f;" u2="&#x38;" k="14" />
    <hkern u1="&#x2f;" u2="&#x36;" k="35" />
    <hkern u1="&#x2f;" u2="&#x34;" k="37" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="43" />
    <hkern u1="&#x2f;" u2="x" k="29" />
    <hkern u1="&#x2f;" u2="w" k="30" />
    <hkern u1="&#x2f;" u2="v" k="29" />
    <hkern u1="\" u2="W" k="50" />
    <hkern u1="\" u2="V" k="53" />
    <hkern u1="\" u2="w" k="10" />
    <hkern u1="\" u2="v" k="12" />
    <hkern u1="G" u2="&#x126;" k="6" />
    <hkern u1="G" u2="&#x141;" k="6" />
    <hkern u1="G" u2="&#xd0;" k="6" />
    <hkern u1="V" u2="&#x2122;" k="-16" />
    <hkern u1="V" u2="&#x12b;" k="40" />
    <hkern u1="V" u2="&#x129;" k="20" />
    <hkern u1="V" u2="&#x135;" k="40" />
    <hkern u1="V" u2="&#x131;" k="84" />
    <hkern u1="V" u2="&#x126;" k="10" />
    <hkern u1="V" u2="&#x159;" k="61" />
    <hkern u1="V" u2="&#x161;" k="54" />
    <hkern u1="V" u2="&#x141;" k="10" />
    <hkern u1="V" u2="&#xba;" k="14" />
    <hkern u1="V" u2="&#xdf;" k="71" />
    <hkern u1="V" u2="&#x26;" k="24" />
    <hkern u1="V" u2="&#xaa;" k="14" />
    <hkern u1="V" u2="&#xae;" k="26" />
    <hkern u1="V" u2="&#xa9;" k="26" />
    <hkern u1="V" u2="&#xd0;" k="9" />
    <hkern u1="V" u2="&#xee;" k="40" />
    <hkern u1="V" u2="&#x2f;" k="53" />
    <hkern u1="V" u2="&#x30;" k="24" />
    <hkern u1="V" u2="&#x39;" k="19" />
    <hkern u1="V" u2="&#x38;" k="27" />
    <hkern u1="V" u2="&#x36;" k="50" />
    <hkern u1="V" u2="&#x34;" k="55" />
    <hkern u1="V" u2="&#x32;" k="16" />
    <hkern u1="V" u2="&#x40;" k="49" />
    <hkern u1="V" u2="&#xef;" k="34" />
    <hkern u1="V" u2="&#xe6;" k="106" />
    <hkern u1="V" u2="x" k="81" />
    <hkern u1="V" u2="w" k="76" />
    <hkern u1="V" u2="v" k="74" />
    <hkern u1="V" u2="&#xec;" k="8" />
    <hkern u1="V" u2="&#xed;" k="66" />
    <hkern u1="V" u2="&#x20;" k="39" />
    <hkern u1="S" u2="&#x126;" k="6" />
    <hkern u1="S" u2="&#x141;" k="6" />
    <hkern u1="S" u2="&#xd0;" k="6" />
    <hkern u1="Z" u2="&#x126;" k="6" />
    <hkern u1="Z" u2="&#x141;" k="6" />
    <hkern u1="Z" u2="&#xd0;" k="6" />
    <hkern u1="W" u2="&#x2122;" k="-17" />
    <hkern u1="W" u2="&#x12b;" k="38" />
    <hkern u1="W" u2="&#x129;" k="20" />
    <hkern u1="W" u2="&#x135;" k="38" />
    <hkern u1="W" u2="&#x131;" k="79" />
    <hkern u1="W" u2="&#x126;" k="8" />
    <hkern u1="W" u2="&#x159;" k="60" />
    <hkern u1="W" u2="&#x161;" k="55" />
    <hkern u1="W" u2="&#x141;" k="8" />
    <hkern u1="W" u2="&#xba;" k="11" />
    <hkern u1="W" u2="&#xdf;" k="67" />
    <hkern u1="W" u2="&#x26;" k="22" />
    <hkern u1="W" u2="&#xaa;" k="12" />
    <hkern u1="W" u2="&#xae;" k="24" />
    <hkern u1="W" u2="&#xa9;" k="24" />
    <hkern u1="W" u2="&#xd0;" k="8" />
    <hkern u1="W" u2="&#xee;" k="38" />
    <hkern u1="W" u2="&#x2f;" k="50" />
    <hkern u1="W" u2="&#x30;" k="22" />
    <hkern u1="W" u2="&#x39;" k="18" />
    <hkern u1="W" u2="&#x38;" k="25" />
    <hkern u1="W" u2="&#x36;" k="48" />
    <hkern u1="W" u2="&#x34;" k="51" />
    <hkern u1="W" u2="&#x32;" k="15" />
    <hkern u1="W" u2="&#x40;" k="46" />
    <hkern u1="W" u2="&#xef;" k="34" />
    <hkern u1="W" u2="&#xe6;" k="103" />
    <hkern u1="W" u2="x" k="77" />
    <hkern u1="W" u2="w" k="72" />
    <hkern u1="W" u2="v" k="70" />
    <hkern u1="W" u2="&#xec;" k="8" />
    <hkern u1="W" u2="&#xed;" k="63" />
    <hkern u1="W" u2="&#x20;" k="37" />
    <hkern u1="X" u2="&#x126;" k="7" />
    <hkern u1="X" u2="&#x141;" k="7" />
    <hkern u1="X" u2="&#xba;" k="20" />
    <hkern u1="X" u2="&#xaa;" k="21" />
    <hkern u1="X" u2="&#xae;" k="29" />
    <hkern u1="X" u2="&#xa9;" k="29" />
    <hkern u1="X" u2="&#xd0;" k="6" />
    <hkern u1="X" u2="&#x30;" k="10" />
    <hkern u1="X" u2="&#xe6;" k="15" />
    <hkern u1="X" u2="w" k="57" />
    <hkern u1="X" u2="v" k="59" />
    <hkern u1="&#xc6;" u2="&#x166;" k="18" />
    <hkern u1="&#xc5;" u2="&#x126;" k="9" />
    <hkern u1="&#xc5;" u2="&#x141;" k="9" />
    <hkern u1="&#xc5;" u2="&#xd0;" k="9" />
    <hkern u1="&#xc1;" u2="&#x126;" k="9" />
    <hkern u1="&#xc1;" u2="&#x141;" k="9" />
    <hkern u1="&#xc1;" u2="&#xd0;" k="9" />
    <hkern u1="&#xd3;" u2="&#x126;" k="10" />
    <hkern u1="&#xd3;" u2="&#x110;" k="8" />
    <hkern u1="&#xd3;" u2="&#x141;" k="10" />
    <hkern u1="&#xd3;" u2="&#xd0;" k="10" />
    <hkern u1="&#xc4;" u2="&#x126;" k="9" />
    <hkern u1="&#xc4;" u2="&#x141;" k="9" />
    <hkern u1="&#xc4;" u2="&#xd0;" k="9" />
    <hkern u1="&#xd6;" u2="&#x126;" k="10" />
    <hkern u1="&#xd6;" u2="&#x110;" k="8" />
    <hkern u1="&#xd6;" u2="&#x141;" k="10" />
    <hkern u1="&#xd6;" u2="&#xd0;" k="10" />
    <hkern u1="&#xc0;" u2="&#x126;" k="9" />
    <hkern u1="&#xc0;" u2="&#x141;" k="9" />
    <hkern u1="&#xc0;" u2="&#xd0;" k="9" />
    <hkern u1="&#xd2;" u2="&#x126;" k="10" />
    <hkern u1="&#xd2;" u2="&#x110;" k="8" />
    <hkern u1="&#xd2;" u2="&#x141;" k="10" />
    <hkern u1="&#xd2;" u2="&#xd0;" k="10" />
    <hkern u1="&#xdd;" u2="&#x126;" k="13" />
    <hkern u1="&#xdd;" u2="&#x110;" k="10" />
    <hkern u1="&#xdd;" u2="&#x141;" k="13" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="96" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="132" />
    <hkern u1="&#xdd;" u2="&#xd0;" k="13" />
    <hkern u1="&#xa1;" u2="&#x129;" k="-18" />
    <hkern u1="&#xa1;" u2="&#x166;" k="45" />
    <hkern u1="&#xa1;" u2="W" k="26" />
    <hkern u1="&#xa1;" u2="V" k="29" />
    <hkern u1="&#xd8;" u2="&#x166;" k="17" />
    <hkern u1="&#xd8;" u2="&#x126;" k="10" />
    <hkern u1="&#xd8;" u2="&#x110;" k="8" />
    <hkern u1="&#xd8;" u2="&#x141;" k="10" />
    <hkern u1="&#xd8;" u2="&#xd0;" k="10" />
    <hkern u1="&#xbf;" u2="&#x126;" k="19" />
    <hkern u1="&#xbf;" u2="&#x110;" k="17" />
    <hkern u1="&#xbf;" u2="&#x142;" k="21" />
    <hkern u1="&#xbf;" u2="&#x141;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd0;" k="19" />
    <hkern u1="&#xbf;" u2="W" k="55" />
    <hkern u1="&#xbf;" u2="V" k="57" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="31" />
    <hkern u1="&#xbf;" u2="w" k="33" />
    <hkern u1="&#xbf;" u2="v" k="35" />
    <hkern u1="&#xc3;" u2="&#x126;" k="9" />
    <hkern u1="&#xc3;" u2="&#x141;" k="9" />
    <hkern u1="&#xc3;" u2="&#xd0;" k="9" />
    <hkern u1="&#xd5;" u2="&#x126;" k="10" />
    <hkern u1="&#xd5;" u2="&#x110;" k="8" />
    <hkern u1="&#xd5;" u2="&#x141;" k="10" />
    <hkern u1="&#xd5;" u2="&#xd0;" k="10" />
    <hkern u1="&#xee;" u2="&#xba;" k="-15" />
    <hkern u1="&#xee;" u2="&#xaa;" k="-15" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-11" />
    <hkern u1="&#x27;" u2="&#x129;" k="-27" />
    <hkern u1="[" u2="&#x12f;" k="-10" />
    <hkern u1="[" u2="&#x126;" k="16" />
    <hkern u1="[" u2="&#x110;" k="14" />
    <hkern u1="[" u2="&#x141;" k="16" />
    <hkern u1="[" u2="&#xd0;" k="16" />
    <hkern u1="[" u2="&#x30;" k="30" />
    <hkern u1="[" u2="&#x39;" k="28" />
    <hkern u1="[" u2="&#x28;" k="15" />
    <hkern u1="[" u2="&#x38;" k="28" />
    <hkern u1="[" u2="&#x36;" k="35" />
    <hkern u1="[" u2="&#x34;" k="30" />
    <hkern u1="[" u2="&#x32;" k="15" />
    <hkern u1="[" u2="&#xe6;" k="40" />
    <hkern u1="[" u2="x" k="33" />
    <hkern u1="[" u2="w" k="38" />
    <hkern u1="[" u2="v" k="39" />
    <hkern u1="&#xb0;" u2="&#x36;" k="26" />
    <hkern u1="&#xb0;" u2="&#x34;" k="50" />
    <hkern u1="&#xd0;" u2="&#x126;" k="10" />
    <hkern u1="&#xd0;" u2="&#x110;" k="8" />
    <hkern u1="&#xd0;" u2="&#x141;" k="10" />
    <hkern u1="&#xd0;" u2="&#xd0;" k="10" />
    <hkern u1="&#xfe;" u2="&#xd0;" k="36" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf0;" u2="&#xd0;" k="31" />
    <hkern u1="&#xf0;" u2="]" k="33" />
    <hkern u1="&#xf0;" u2="X" k="42" />
    <hkern u1="&#xf0;" u2="W" k="85" />
    <hkern u1="&#xf0;" u2="V" k="90" />
    <hkern u1="&#xf0;" u2="\" k="27" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="28" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="12" />
    <hkern u1="&#xf0;" u2="&#x29;" k="34" />
    <hkern u1="&#xf0;" u2="&#x3f;" k="22" />
    <hkern u1="&#xf0;" u2="x" k="7" />
    <hkern u1="&#xf0;" u2="v" k="4" />
    <hkern u1="&#xde;" u2="&#x2122;" k="11" />
    <hkern u1="&#xde;" u2="&#x166;" k="49" />
    <hkern u1="&#xde;" u2="&#x126;" k="8" />
    <hkern u1="&#xde;" u2="&#x110;" k="6" />
    <hkern u1="&#xde;" u2="&#x141;" k="8" />
    <hkern u1="&#xde;" u2="&#xd0;" k="7" />
    <hkern u1="&#xde;" u2="]" k="26" />
    <hkern u1="&#xde;" u2="X" k="24" />
    <hkern u1="&#xde;" u2="W" k="23" />
    <hkern u1="&#xde;" u2="V" k="26" />
    <hkern u1="&#xde;" u2="&#x7d;" k="19" />
    <hkern u1="&#xde;" u2="&#x29;" k="29" />
    <hkern u1="&#xde;" u2="&#x37;" k="13" />
    <hkern u1="&#xde;" u2="&#xe6;" k="9" />
    <hkern u1="&#xc2;" u2="&#x126;" k="9" />
    <hkern u1="&#xc2;" u2="&#x141;" k="9" />
    <hkern u1="&#xc2;" u2="&#xd0;" k="9" />
    <hkern u1="&#xd4;" u2="&#x126;" k="10" />
    <hkern u1="&#xd4;" u2="&#x110;" k="8" />
    <hkern u1="&#xd4;" u2="&#x141;" k="10" />
    <hkern u1="&#xd4;" u2="&#xd0;" k="10" />
    <hkern u1="&#x201e;" u2="&#x126;" k="17" />
    <hkern u1="&#x201e;" u2="&#x110;" k="16" />
    <hkern u1="&#x201e;" u2="&#x141;" k="17" />
    <hkern u1="&#x201e;" u2="&#xd0;" k="17" />
    <hkern u1="&#x201a;" u2="&#x126;" k="17" />
    <hkern u1="&#x201a;" u2="&#x110;" k="16" />
    <hkern u1="&#x201a;" u2="&#x141;" k="17" />
    <hkern u1="&#x201a;" u2="&#xd0;" k="17" />
    <hkern u1="&#x203a;" u2="&#x126;" k="18" />
    <hkern u1="&#x203a;" u2="&#x110;" k="15" />
    <hkern u1="&#x203a;" u2="&#x141;" k="18" />
    <hkern u1="&#x203a;" u2="&#xd0;" k="17" />
    <hkern u1="&#xbb;" u2="&#x126;" k="18" />
    <hkern u1="&#xbb;" u2="&#x110;" k="15" />
    <hkern u1="&#xbb;" u2="&#x141;" k="18" />
    <hkern u1="&#xbb;" u2="&#xd0;" k="17" />
    <hkern u1="&#xf7;" u2="&#x39;" k="10" />
    <hkern u1="&#xf7;" u2="&#x37;" k="33" />
    <hkern u1="&#xf7;" u2="&#x33;" k="13" />
    <hkern u1="&#xf7;" u2="&#x31;" k="17" />
    <hkern u1="&#xf7;" u2="&#x32;" k="12" />
    <hkern u1="&#x26;" u2="&#x129;" k="-8" />
    <hkern u1="&#x26;" u2="W" k="28" />
    <hkern u1="&#x26;" u2="V" k="31" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="12" />
    <hkern u1="&#xdf;" u2="&#xd0;" k="35" />
    <hkern u1="&#xdf;" u2="]" k="31" />
    <hkern u1="&#xdf;" u2="X" k="40" />
    <hkern u1="&#xdf;" u2="W" k="71" />
    <hkern u1="&#xdf;" u2="V" k="75" />
    <hkern u1="&#xdf;" u2="\" k="25" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="27" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="8" />
    <hkern u1="&#xdf;" u2="&#x29;" k="33" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="11" />
    <hkern u1="&#xdf;" u2="x" k="14" />
    <hkern u1="&#xdf;" u2="w" k="11" />
    <hkern u1="&#xdf;" u2="v" k="13" />
    <hkern u1="&#xb7;" u2="&#x37;" k="25" />
    <hkern u1="&#xb7;" u2="&#x33;" k="10" />
    <hkern u1="&#xd7;" u2="&#x37;" k="26" />
    <hkern u1="&#xd7;" u2="&#x31;" k="16" />
    <hkern u1="O" u2="&#x129;" k="-16" />
    <hkern u1="O" u2="&#x126;" k="10" />
    <hkern u1="O" u2="&#x110;" k="8" />
    <hkern u1="O" u2="&#x141;" k="10" />
    <hkern u1="O" u2="&#xd0;" k="10" />
    <hkern u1="g" u2="&#xd0;" k="24" />
    <hkern u1="d" u2="&#x129;" k="-13" />
    <hkern u1="d" u2="&#xd0;" k="25" />
    <hkern u1="a" u2="&#xd0;" k="24" />
    <hkern u1="n" u2="&#xd0;" k="26" />
    <hkern u1="&#x104;" u2="&#x12e;" k="-43" />
    <hkern u1="&#x104;" u2="&#x126;" k="9" />
    <hkern u1="&#x104;" u2="&#x141;" k="9" />
    <hkern u1="&#x104;" u2="&#xd0;" k="9" />
    <hkern u1="&#x104;" u2="&#x29;" k="-12" />
    <hkern u1="&#x104;" u2="j" k="-24" />
    <hkern u1="&#x13d;" u2="&#x166;" k="67" />
    <hkern u1="&#x15a;" u2="&#x126;" k="6" />
    <hkern u1="&#x15a;" u2="&#x141;" k="6" />
    <hkern u1="&#x15a;" u2="&#xd0;" k="6" />
    <hkern u1="&#x160;" u2="&#x126;" k="6" />
    <hkern u1="&#x160;" u2="&#x141;" k="6" />
    <hkern u1="&#x160;" u2="&#xd0;" k="6" />
    <hkern u1="&#x15e;" u2="&#x126;" k="6" />
    <hkern u1="&#x15e;" u2="&#x141;" k="6" />
    <hkern u1="&#x15e;" u2="&#xd0;" k="6" />
    <hkern u1="&#x164;" u2="&#xdf;" k="94" />
    <hkern u1="&#x179;" u2="&#x126;" k="6" />
    <hkern u1="&#x179;" u2="&#x141;" k="6" />
    <hkern u1="&#x179;" u2="&#xd0;" k="6" />
    <hkern u1="&#x17d;" u2="&#x126;" k="6" />
    <hkern u1="&#x17d;" u2="&#x141;" k="6" />
    <hkern u1="&#x17d;" u2="&#xd0;" k="6" />
    <hkern u1="&#x17b;" u2="&#x126;" k="6" />
    <hkern u1="&#x17b;" u2="&#x141;" k="6" />
    <hkern u1="&#x17b;" u2="&#xd0;" k="6" />
    <hkern u1="&#x142;" u2="&#x142;" k="7" />
    <hkern u1="&#x142;" u2="&#x20;" k="18" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-54" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-21" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-69" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x13e;" u2="&#x201d;" k="-7" />
    <hkern u1="&#x13e;" u2="&#x201c;" k="-30" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-44" />
    <hkern u1="&#x13e;" u2="]" k="-53" />
    <hkern u1="&#x13e;" u2="&#x27;" k="-16" />
    <hkern u1="&#x13e;" u2="\" k="-40" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-52" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-49" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-54" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="-7" />
    <hkern u1="&#x13e;" u2="&#x22;" k="-16" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-25" />
    <hkern u1="&#x13e;" u2="k" k="-44" />
    <hkern u1="&#x13e;" u2="h" k="-44" />
    <hkern u1="&#x13e;" u2="l" k="-42" />
    <hkern u1="&#x13e;" u2="b" k="-44" />
    <hkern u1="&#x165;" u2="&#x2122;" k="-13" />
    <hkern u1="&#x165;" u2="&#x161;" k="-19" />
    <hkern u1="&#x165;" u2="&#x2018;" k="-4" />
    <hkern u1="&#x165;" u2="&#x201c;" k="-4" />
    <hkern u1="&#x165;" u2="]" k="-27" />
    <hkern u1="&#x165;" u2="\" k="-11" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-25" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-7" />
    <hkern u1="&#x165;" u2="&#x29;" k="-28" />
    <hkern u1="&#x154;" u2="&#x126;" k="10" />
    <hkern u1="&#x154;" u2="&#x110;" k="8" />
    <hkern u1="&#x154;" u2="&#x141;" k="10" />
    <hkern u1="&#x154;" u2="&#xd0;" k="10" />
    <hkern u1="&#x102;" u2="&#x126;" k="9" />
    <hkern u1="&#x102;" u2="&#x141;" k="9" />
    <hkern u1="&#x102;" u2="&#xd0;" k="9" />
    <hkern u1="&#x118;" u2="&#x12e;" k="-42" />
    <hkern u1="&#x118;" u2="&#x29;" k="-19" />
    <hkern u1="&#x118;" u2="j" k="-23" />
    <hkern u1="&#x10e;" u2="&#x126;" k="10" />
    <hkern u1="&#x10e;" u2="&#x110;" k="8" />
    <hkern u1="&#x10e;" u2="&#x141;" k="10" />
    <hkern u1="&#x10e;" u2="&#xd0;" k="10" />
    <hkern u1="&#x110;" u2="&#x126;" k="10" />
    <hkern u1="&#x110;" u2="&#x110;" k="8" />
    <hkern u1="&#x110;" u2="&#x141;" k="10" />
    <hkern u1="&#x110;" u2="&#xd0;" k="10" />
    <hkern u1="&#x150;" u2="&#x126;" k="10" />
    <hkern u1="&#x150;" u2="&#x110;" k="8" />
    <hkern u1="&#x150;" u2="&#x141;" k="10" />
    <hkern u1="&#x150;" u2="&#xd0;" k="10" />
    <hkern u1="&#x158;" u2="&#x126;" k="10" />
    <hkern u1="&#x158;" u2="&#x110;" k="8" />
    <hkern u1="&#x158;" u2="&#x141;" k="10" />
    <hkern u1="&#x158;" u2="&#xd0;" k="10" />
    <hkern u1="&#x162;" u2="&#xdf;" k="94" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-22" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="-32" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-9" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="-32" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-46" />
    <hkern u1="&#x10f;" u2="]" k="-55" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-18" />
    <hkern u1="&#x10f;" u2="\" k="-42" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-54" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-51" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-9" />
    <hkern u1="&#x10f;" u2="&#x22;" k="-18" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-27" />
    <hkern u1="&#x10f;" u2="k" k="-46" />
    <hkern u1="&#x10f;" u2="h" k="-46" />
    <hkern u1="&#x10f;" u2="l" k="-44" />
    <hkern u1="&#x10f;" u2="b" k="-46" />
    <hkern u1="&#x151;" u2="&#x3f;" k="23" />
    <hkern u1="&#x159;" u2="]" k="25" />
    <hkern u1="&#x159;" u2="&#x29;" k="28" />
    <hkern u1="&#x171;" u2="&#x3f;" k="17" />
    <hkern u1="&#x126;" u2="&#xfb02;" k="39" />
    <hkern u1="&#x126;" u2="&#xfb01;" k="39" />
    <hkern u1="&#x126;" u2="&#x2026;" k="17" />
    <hkern u1="&#x126;" u2="&#x14c;" k="10" />
    <hkern u1="&#x126;" u2="&#x100;" k="10" />
    <hkern u1="&#x126;" u2="&#x122;" k="10" />
    <hkern u1="&#x126;" u2="&#x178;" k="13" />
    <hkern u1="&#x126;" u2="&#x152;" k="10" />
    <hkern u1="&#x126;" u2="&#x153;" k="37" />
    <hkern u1="&#x126;" u2="&#x121;" k="37" />
    <hkern u1="&#x126;" u2="&#x15c;" k="7" />
    <hkern u1="&#x126;" u2="&#x11c;" k="10" />
    <hkern u1="&#x126;" u2="&#x120;" k="10" />
    <hkern u1="&#x126;" u2="&#x108;" k="10" />
    <hkern u1="&#x126;" u2="&#x10a;" k="10" />
    <hkern u1="&#x126;" u2="&#x134;" k="13" />
    <hkern u1="&#x126;" u2="&#x11e;" k="10" />
    <hkern u1="&#x126;" u2="&#x126;" k="13" />
    <hkern u1="&#x126;" u2="&#x150;" k="10" />
    <hkern u1="&#x126;" u2="&#x110;" k="11" />
    <hkern u1="&#x126;" u2="&#x10c;" k="10" />
    <hkern u1="&#x126;" u2="&#x106;" k="10" />
    <hkern u1="&#x126;" u2="&#x102;" k="10" />
    <hkern u1="&#x126;" u2="&#x17c;" k="38" />
    <hkern u1="&#x126;" u2="&#x17b;" k="7" />
    <hkern u1="&#x126;" u2="&#x17d;" k="7" />
    <hkern u1="&#x126;" u2="&#x179;" k="7" />
    <hkern u1="&#x126;" u2="&#x15e;" k="7" />
    <hkern u1="&#x126;" u2="&#x160;" k="7" />
    <hkern u1="&#x126;" u2="&#x15a;" k="7" />
    <hkern u1="&#x126;" u2="&#x141;" k="13" />
    <hkern u1="&#x126;" u2="&#x104;" k="10" />
    <hkern u1="&#x126;" u2="n" k="24" />
    <hkern u1="&#x126;" u2="a" k="37" />
    <hkern u1="&#x126;" u2="d" k="37" />
    <hkern u1="&#x126;" u2="g" k="37" />
    <hkern u1="&#x126;" u2="O" k="10" />
    <hkern u1="&#x126;" u2="&#xdf;" k="39" />
    <hkern u1="&#x126;" u2="&#xab;" k="18" />
    <hkern u1="&#x126;" u2="&#x201a;" k="17" />
    <hkern u1="&#x126;" u2="&#x201e;" k="17" />
    <hkern u1="&#x126;" u2="&#xd4;" k="10" />
    <hkern u1="&#x126;" u2="&#xc2;" k="10" />
    <hkern u1="&#x126;" u2="&#xf0;" k="37" />
    <hkern u1="&#x126;" u2="&#xfe;" k="25" />
    <hkern u1="&#x126;" u2="&#xd0;" k="13" />
    <hkern u1="&#x126;" u2="&#xc7;" k="10" />
    <hkern u1="&#x126;" u2="]" k="12" />
    <hkern u1="&#x126;" u2="&#xd5;" k="10" />
    <hkern u1="&#x126;" u2="&#xc3;" k="10" />
    <hkern u1="&#x126;" u2="&#xd8;" k="10" />
    <hkern u1="&#x126;" u2="&#xdd;" k="13" />
    <hkern u1="&#x126;" u2="&#xd2;" k="10" />
    <hkern u1="&#x126;" u2="&#xc0;" k="10" />
    <hkern u1="&#x126;" u2="&#xd6;" k="10" />
    <hkern u1="&#x126;" u2="&#xc4;" k="10" />
    <hkern u1="&#x126;" u2="&#xd3;" k="10" />
    <hkern u1="&#x126;" u2="&#xc1;" k="10" />
    <hkern u1="&#x126;" u2="&#xc5;" k="10" />
    <hkern u1="&#x126;" u2="&#xc6;" k="10" />
    <hkern u1="&#x126;" u2="X" k="6" />
    <hkern u1="&#x126;" u2="W" k="8" />
    <hkern u1="&#x126;" u2="Z" k="7" />
    <hkern u1="&#x126;" u2="S" k="7" />
    <hkern u1="&#x126;" u2="V" k="10" />
    <hkern u1="&#x126;" u2="G" k="10" />
    <hkern u1="&#x126;" u2="&#x7d;" k="10" />
    <hkern u1="&#x126;" u2="&#x29;" k="11" />
    <hkern u1="&#x126;" u2="&#x2039;" k="18" />
    <hkern u1="&#x126;" u2="Q" k="10" />
    <hkern u1="&#x126;" u2="C" k="10" />
    <hkern u1="&#x126;" u2="Y" k="13" />
    <hkern u1="&#x126;" u2="J" k="13" />
    <hkern u1="&#x126;" u2="A" k="10" />
    <hkern u1="&#x126;" u2="&#xe6;" k="43" />
    <hkern u1="&#x126;" u2="o" k="37" />
    <hkern u1="&#x126;" u2="z" k="38" />
    <hkern u1="&#x126;" u2="x" k="36" />
    <hkern u1="&#x126;" u2="w" k="38" />
    <hkern u1="&#x126;" u2="y" k="37" />
    <hkern u1="&#x126;" u2="k" k="25" />
    <hkern u1="&#x126;" u2="s" k="44" />
    <hkern u1="&#x126;" u2="j" k="25" />
    <hkern u1="&#x126;" u2="v" k="38" />
    <hkern u1="&#x126;" u2="f" k="39" />
    <hkern u1="&#x126;" u2="&#x2e;" k="17" />
    <hkern u1="&#x126;" u2="&#x2c;" k="17" />
    <hkern u1="&#x126;" u2="p" k="24" />
    <hkern u1="&#x126;" u2="e" k="37" />
    <hkern u1="&#x126;" u2="r" k="24" />
    <hkern u1="&#x126;" u2="u" k="26" />
    <hkern u1="&#x126;" u2="q" k="37" />
    <hkern u1="&#x126;" u2="c" k="37" />
    <hkern u1="&#x126;" u2="h" k="25" />
    <hkern u1="&#x126;" u2="i" k="25" />
    <hkern u1="&#x126;" u2="m" k="24" />
    <hkern u1="&#x126;" u2="l" k="25" />
    <hkern u1="&#x126;" u2="b" k="25" />
    <hkern u1="&#x126;" u2="t" k="37" />
    <hkern u1="&#x11e;" u2="&#x126;" k="6" />
    <hkern u1="&#x11e;" u2="&#x141;" k="6" />
    <hkern u1="&#x11e;" u2="&#xd0;" k="6" />
    <hkern u1="&#x135;" u2="&#xba;" k="-13" />
    <hkern u1="&#x135;" u2="&#xaa;" k="-13" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x120;" u2="&#x126;" k="6" />
    <hkern u1="&#x120;" u2="&#x141;" k="6" />
    <hkern u1="&#x120;" u2="&#xd0;" k="6" />
    <hkern u1="&#x11c;" u2="&#x126;" k="6" />
    <hkern u1="&#x11c;" u2="&#x141;" k="6" />
    <hkern u1="&#x11c;" u2="&#xd0;" k="6" />
    <hkern u1="&#x15c;" u2="&#x126;" k="6" />
    <hkern u1="&#x15c;" u2="&#x141;" k="6" />
    <hkern u1="&#x15c;" u2="&#xd0;" k="6" />
    <hkern u1="&#x153;" u2="&#xd0;" k="42" />
    <hkern u1="&#x178;" u2="&#x126;" k="13" />
    <hkern u1="&#x178;" u2="&#x110;" k="10" />
    <hkern u1="&#x178;" u2="&#x141;" k="13" />
    <hkern u1="&#x178;" u2="&#xdf;" k="96" />
    <hkern u1="&#x178;" u2="&#xf0;" k="132" />
    <hkern u1="&#x178;" u2="&#xd0;" k="13" />
    <hkern u1="&#x156;" u2="&#x126;" k="10" />
    <hkern u1="&#x156;" u2="&#x110;" k="8" />
    <hkern u1="&#x156;" u2="&#x141;" k="10" />
    <hkern u1="&#x156;" u2="&#xd0;" k="10" />
    <hkern u1="&#x122;" u2="&#x126;" k="6" />
    <hkern u1="&#x122;" u2="&#x141;" k="6" />
    <hkern u1="&#x122;" u2="&#xd0;" k="6" />
    <hkern u1="&#x166;" u2="&#xdf;" k="87" />
    <hkern u1="&#x166;" u2="&#xf0;" k="78" />
    <hkern u1="&#x166;" u2="z" k="97" />
    <hkern u1="&#x166;" u2="j" k="85" />
    <hkern u1="&#x166;" u2="i" k="84" />
    <hkern u1="&#x166;" u2="t" k="91" />
    <hkern u1="&#x100;" u2="&#x126;" k="9" />
    <hkern u1="&#x100;" u2="&#x141;" k="9" />
    <hkern u1="&#x100;" u2="&#xd0;" k="9" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-9" />
    <hkern u1="&#x129;" u2="&#xba;" k="-30" />
    <hkern u1="&#x129;" u2="&#xaa;" k="-33" />
    <hkern u1="&#x129;" u2="&#xfe;" k="-13" />
    <hkern u1="&#x129;" u2="&#x27;" k="-28" />
    <hkern u1="&#x129;" u2="&#x7c;" k="-7" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-57" />
    <hkern u1="&#x129;" u2="&#x22;" k="-28" />
    <hkern u1="&#x129;" u2="&#x3f;" k="-21" />
    <hkern u1="&#x129;" u2="k" k="-13" />
    <hkern u1="&#x129;" u2="j" k="-24" />
    <hkern u1="&#x129;" u2="h" k="-13" />
    <hkern u1="&#x129;" u2="i" k="-25" />
    <hkern u1="&#x129;" u2="l" k="-11" />
    <hkern u1="&#x129;" u2="b" k="-13" />
    <hkern u1="&#x14c;" u2="&#x126;" k="10" />
    <hkern u1="&#x14c;" u2="&#x110;" k="8" />
    <hkern u1="&#x14c;" u2="&#x141;" k="10" />
    <hkern u1="&#x14c;" u2="&#xd0;" k="10" />
    <hkern u1="&#x12b;" u2="&#xba;" k="-9" />
    <hkern u1="&#x12b;" u2="&#xaa;" k="-9" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-23" />
    <hkern u1="&#xfb02;" u2="&#x129;" k="-12" />
    <hkern u1="&#x2044;" u2="&#x30;" k="13" />
    <hkern u1="&#x2044;" u2="&#x38;" k="17" />
    <hkern u1="&#x2044;" u2="&#x36;" k="38" />
    <hkern u1="&#x2044;" u2="&#x34;" k="41" />
    <hkern u1="&#x2212;" u2="&#x37;" k="13" />
    <hkern u1="&#x20ac;" u2="&#x34;" k="11" />
    <hkern g1="space"
	g2="t,tcaron,uni0163,tbar"
	k="10" />
    <hkern g1="space"
	g2="y,yacute,ydieresis"
	k="23" />
    <hkern g1="space"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="29" />
    <hkern g1="space"
	g2="J,Jcircumflex"
	k="47" />
    <hkern g1="space"
	g2="Y,Yacute,Ydieresis"
	k="42" />
    <hkern g1="space"
	g2="T,Tcaron,uni0162,Tbar"
	k="29" />
    <hkern g1="space"
	g2="quoteright,quotedblright"
	k="34" />
    <hkern g1="space"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="t,tcaron,uni0163,tbar"
	k="8" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="y,yacute,ydieresis"
	k="36" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="J,Jcircumflex"
	k="21" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="Y,Yacute,Ydieresis"
	k="90" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="T,Tcaron,uni0162,Tbar"
	k="62" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="quoteright,quotedblright"
	k="92" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="quotedbl,quotesingle"
	k="101" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="f,germandbls,fi,fl"
	k="8" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="v"
	k="37" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="w"
	k="34" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="9" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="23" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="one"
	k="20" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="seven"
	k="19" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="nine"
	k="18" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="zero"
	k="21" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="V"
	k="83" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="W"
	k="78" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="quotedblleft,quoteleft"
	k="92" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="34" />
    <hkern g1="guilsinglleft,guillemotleft"
	g2="J,Jcircumflex"
	k="21" />
    <hkern g1="guilsinglleft,guillemotleft"
	g2="Y,Yacute,Ydieresis"
	k="69" />
    <hkern g1="guilsinglleft,guillemotleft"
	g2="T,Tcaron,uni0162,Tbar"
	k="67" />
    <hkern g1="guilsinglleft,guillemotleft"
	g2="quoteright,quotedblright"
	k="76" />
    <hkern g1="guilsinglleft,guillemotleft"
	g2="V"
	k="47" />
    <hkern g1="guilsinglleft,guillemotleft"
	g2="W"
	k="43" />
    <hkern g1="at"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="14" />
    <hkern g1="at"
	g2="J,Jcircumflex"
	k="21" />
    <hkern g1="at"
	g2="Y,Yacute,Ydieresis"
	k="67" />
    <hkern g1="at"
	g2="T,Tcaron,uni0162,Tbar"
	k="61" />
    <hkern g1="at"
	g2="quoteright,quotedblright"
	k="34" />
    <hkern g1="at"
	g2="quotedbl,quotesingle"
	k="15" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ydieresis"
	k="65" />
    <hkern g1="colon,semicolon"
	g2="T,Tcaron,uni0162,Tbar"
	k="49" />
    <hkern g1="colon,semicolon"
	g2="quoteright,quotedblright"
	k="92" />
    <hkern g1="colon,semicolon"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="45" />
    <hkern g1="colon,semicolon"
	g2="W"
	k="41" />
    <hkern g1="quoteright,quotedblright"
	g2="t,tcaron,uni0163,tbar"
	k="9" />
    <hkern g1="quoteright,quotedblright"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="71" />
    <hkern g1="quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="15" />
    <hkern g1="quoteright,quotedblright"
	g2="f,germandbls,fi,fl"
	k="8" />
    <hkern g1="quoteright,quotedblright"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="13" />
    <hkern g1="quoteright,quotedblright"
	g2="space"
	k="34" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="92" />
    <hkern g1="quoteright,quotedblright"
	g2="z,zacute,zcaron,zdotaccent"
	k="12" />
    <hkern g1="quoteright,quotedblright"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="quoteright,quotedblright"
	g2="guilsinglleft,guillemotleft"
	k="92" />
    <hkern g1="quoteright,quotedblright"
	g2="at"
	k="57" />
    <hkern g1="quoteright,quotedblright"
	g2="colon,semicolon"
	k="92" />
    <hkern g1="quoteright,quotedblright"
	g2="hyphen,endash,emdash"
	k="92" />
    <hkern g1="quoteright,quotedblright"
	g2="slash"
	k="46" />
    <hkern g1="quoteright,quotedblright"
	g2="guilsinglright,guillemotright"
	k="80" />
    <hkern g1="quoteright,quotedblright"
	g2="ampersand"
	k="13" />
    <hkern g1="asterisk"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="56" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex"
	k="15" />
    <hkern g1="asterisk"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="28" />
    <hkern g1="asterisk"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="9" />
    <hkern g1="asterisk"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="20" />
    <hkern g1="asterisk"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="11" />
    <hkern g1="bar"
	g2="Y,Yacute,Ydieresis"
	k="18" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ydieresis"
	k="79" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcaron,uni0162,Tbar"
	k="49" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteright,quotedblright"
	k="92" />
    <hkern g1="hyphen,endash,emdash"
	g2="quotedbl,quotesingle"
	k="90" />
    <hkern g1="hyphen,endash,emdash"
	g2="v"
	k="8" />
    <hkern g1="hyphen,endash,emdash"
	g2="seven"
	k="23" />
    <hkern g1="hyphen,endash,emdash"
	g2="V"
	k="51" />
    <hkern g1="hyphen,endash,emdash"
	g2="W"
	k="47" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zcaron,zdotaccent"
	k="16" />
    <hkern g1="slash"
	g2="t,tcaron,uni0163,tbar"
	k="14" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="slash"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="54" />
    <hkern g1="slash"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="slash"
	g2="f,germandbls,fi,fl"
	k="10" />
    <hkern g1="slash"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="10" />
    <hkern g1="slash"
	g2="z,zacute,zcaron,zdotaccent"
	k="24" />
    <hkern g1="slash"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="52" />
    <hkern g1="slash"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="36" />
    <hkern g1="slash"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="44" />
    <hkern g1="slash"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="39" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis"
	k="11" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ydieresis"
	k="65" />
    <hkern g1="backslash"
	g2="T,Tcaron,uni0162,Tbar"
	k="47" />
    <hkern g1="backslash"
	g2="quoteright,quotedblright"
	k="42" />
    <hkern g1="backslash"
	g2="quotedbl,quotesingle"
	k="34" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ydieresis"
	k="39" />
    <hkern g1="exclamdown"
	g2="T,Tcaron,uni0162,Tbar"
	k="52" />
    <hkern g1="questiondown"
	g2="t,tcaron,uni0163,tbar"
	k="23" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis"
	k="15" />
    <hkern g1="questiondown"
	g2="J,Jcircumflex"
	k="-33" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ydieresis"
	k="68" />
    <hkern g1="questiondown"
	g2="T,Tcaron,uni0162,Tbar"
	k="69" />
    <hkern g1="questiondown"
	g2="f,germandbls,fi,fl"
	k="24" />
    <hkern g1="questiondown"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="24" />
    <hkern g1="questiondown"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="28" />
    <hkern g1="questiondown"
	g2="z,zacute,zcaron,zdotaccent"
	k="10" />
    <hkern g1="questiondown"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="39" />
    <hkern g1="questiondown"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="28" />
    <hkern g1="questiondown"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="24" />
    <hkern g1="questiondown"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="18" />
    <hkern g1="questiondown"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="15" />
    <hkern g1="questiondown"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="16" />
    <hkern g1="questiondown"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="15" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="17" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="58" />
    <hkern g1="quotedbl,quotesingle"
	g2="J,Jcircumflex"
	k="15" />
    <hkern g1="quotedbl,quotesingle"
	g2="space"
	k="14" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="101" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="21" />
    <hkern g1="quotedbl,quotesingle"
	g2="guilsinglleft,guillemotleft"
	k="55" />
    <hkern g1="quotedbl,quotesingle"
	g2="at"
	k="26" />
    <hkern g1="quotedbl,quotesingle"
	g2="colon,semicolon"
	k="41" />
    <hkern g1="quotedbl,quotesingle"
	g2="hyphen,endash,emdash"
	k="91" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="35" />
    <hkern g1="quotedbl,quotesingle"
	g2="ampersand"
	k="14" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="12" />
    <hkern g1="quotedbl,quotesingle"
	g2="ae"
	k="12" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="50" />
    <hkern g1="quotedbl,quotesingle"
	g2="six"
	k="25" />
    <hkern g1="quotedblleft,quoteleft"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="67" />
    <hkern g1="quotedblleft,quoteleft"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="quotedblleft,quoteleft"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="92" />
    <hkern g1="quotedblleft,quoteleft"
	g2="z,zacute,zcaron,zdotaccent"
	k="8" />
    <hkern g1="quotedblleft,quoteleft"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="9" />
    <hkern g1="guilsinglright,guillemotright"
	g2="y,yacute,ydieresis"
	k="10" />
    <hkern g1="guilsinglright,guillemotright"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="12" />
    <hkern g1="guilsinglright,guillemotright"
	g2="J,Jcircumflex"
	k="36" />
    <hkern g1="guilsinglright,guillemotright"
	g2="Y,Yacute,Ydieresis"
	k="94" />
    <hkern g1="guilsinglright,guillemotright"
	g2="T,Tcaron,uni0162,Tbar"
	k="77" />
    <hkern g1="guilsinglright,guillemotright"
	g2="quoteright,quotedblright"
	k="92" />
    <hkern g1="guilsinglright,guillemotright"
	g2="quotedbl,quotesingle"
	k="55" />
    <hkern g1="guilsinglright,guillemotright"
	g2="v"
	k="10" />
    <hkern g1="guilsinglright,guillemotright"
	g2="V"
	k="71" />
    <hkern g1="guilsinglright,guillemotright"
	g2="W"
	k="66" />
    <hkern g1="guilsinglright,guillemotright"
	g2="z,zacute,zcaron,zdotaccent"
	k="25" />
    <hkern g1="guilsinglright,guillemotright"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="19" />
    <hkern g1="guilsinglright,guillemotright"
	g2="x"
	k="19" />
    <hkern g1="guilsinglright,guillemotright"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="26" />
    <hkern g1="guilsinglright,guillemotright"
	g2="X"
	k="32" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ydieresis"
	k="44" />
    <hkern g1="ampersand"
	g2="T,Tcaron,uni0162,Tbar"
	k="43" />
    <hkern g1="ampersand"
	g2="quoteright,quotedblright"
	k="45" />
    <hkern g1="two"
	g2="Y,Yacute,Ydieresis"
	k="37" />
    <hkern g1="two"
	g2="T,Tcaron,uni0162,Tbar"
	k="20" />
    <hkern g1="one"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="12" />
    <hkern g1="one"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="one"
	g2="Y,Yacute,Ydieresis"
	k="22" />
    <hkern g1="three"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="15" />
    <hkern g1="three"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="three"
	g2="Y,Yacute,Ydieresis"
	k="14" />
    <hkern g1="four"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="15" />
    <hkern g1="four"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="four"
	g2="Y,Yacute,Ydieresis"
	k="38" />
    <hkern g1="four"
	g2="T,Tcaron,uni0162,Tbar"
	k="18" />
    <hkern g1="five"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="14" />
    <hkern g1="five"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="five"
	g2="Y,Yacute,Ydieresis"
	k="19" />
    <hkern g1="six"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="14" />
    <hkern g1="six"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="six"
	g2="Y,Yacute,Ydieresis"
	k="38" />
    <hkern g1="six"
	g2="T,Tcaron,uni0162,Tbar"
	k="19" />
    <hkern g1="seven"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="52" />
    <hkern g1="seven"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="seven"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="65" />
    <hkern g1="seven"
	g2="hyphen,endash,emdash"
	k="27" />
    <hkern g1="eight"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="14" />
    <hkern g1="eight"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="eight"
	g2="Y,Yacute,Ydieresis"
	k="44" />
    <hkern g1="eight"
	g2="T,Tcaron,uni0162,Tbar"
	k="25" />
    <hkern g1="parenleft"
	g2="t,tcaron,uni0163,tbar"
	k="29" />
    <hkern g1="parenleft"
	g2="y,yacute,ydieresis"
	k="25" />
    <hkern g1="parenleft"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="15" />
    <hkern g1="parenleft"
	g2="J,Jcircumflex"
	k="-53" />
    <hkern g1="parenleft"
	g2="f,germandbls,fi,fl"
	k="23" />
    <hkern g1="parenleft"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="29" />
    <hkern g1="parenleft"
	g2="z,zacute,zcaron,zdotaccent"
	k="22" />
    <hkern g1="parenleft"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="46" />
    <hkern g1="parenleft"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="38" />
    <hkern g1="parenleft"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="34" />
    <hkern g1="parenleft"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="31" />
    <hkern g1="parenleft"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="16" />
    <hkern g1="parenleft"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="10" />
    <hkern g1="nine"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="36" />
    <hkern g1="nine"
	g2="J,Jcircumflex"
	k="23" />
    <hkern g1="nine"
	g2="Y,Yacute,Ydieresis"
	k="35" />
    <hkern g1="nine"
	g2="T,Tcaron,uni0162,Tbar"
	k="23" />
    <hkern g1="nine"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="56" />
    <hkern g1="nine"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="31" />
    <hkern g1="braceleft"
	g2="t,tcaron,uni0163,tbar"
	k="24" />
    <hkern g1="braceleft"
	g2="y,yacute,ydieresis"
	k="22" />
    <hkern g1="braceleft"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="23" />
    <hkern g1="braceleft"
	g2="J,Jcircumflex"
	k="-40" />
    <hkern g1="braceleft"
	g2="f,germandbls,fi,fl"
	k="20" />
    <hkern g1="braceleft"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="25" />
    <hkern g1="braceleft"
	g2="z,zacute,zcaron,zdotaccent"
	k="25" />
    <hkern g1="braceleft"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="38" />
    <hkern g1="braceleft"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="32" />
    <hkern g1="braceleft"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="34" />
    <hkern g1="braceleft"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="31" />
    <hkern g1="braceleft"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="15" />
    <hkern g1="braceleft"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="12" />
    <hkern g1="zero"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="19" />
    <hkern g1="zero"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="zero"
	g2="Y,Yacute,Ydieresis"
	k="42" />
    <hkern g1="zero"
	g2="T,Tcaron,uni0162,Tbar"
	k="30" />
    <hkern g1="zero"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="21" />
    <hkern g1="zero"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="18" />
    <hkern g1="bracketleft"
	g2="t,tcaron,uni0163,tbar"
	k="28" />
    <hkern g1="bracketleft"
	g2="y,yacute,ydieresis"
	k="25" />
    <hkern g1="bracketleft"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="32" />
    <hkern g1="bracketleft"
	g2="J,Jcircumflex"
	k="-42" />
    <hkern g1="bracketleft"
	g2="f,germandbls,fi,fl"
	k="23" />
    <hkern g1="bracketleft"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="28" />
    <hkern g1="bracketleft"
	g2="z,zacute,zcaron,zdotaccent"
	k="33" />
    <hkern g1="bracketleft"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="45" />
    <hkern g1="bracketleft"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="39" />
    <hkern g1="bracketleft"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="40" />
    <hkern g1="bracketleft"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="38" />
    <hkern g1="bracketleft"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="17" />
    <hkern g1="bracketleft"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="18" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="t,tcaron,uni0163,tbar"
	k="23" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="y,yacute,ydieresis"
	k="25" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="Y,Yacute,Ydieresis"
	k="66" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="T,Tcaron,uni0162,Tbar"
	k="65" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="quoteright,quotedblright"
	k="67" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="quotedbl,quotesingle"
	k="57" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="f,germandbls,fi,fl"
	k="22" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="v"
	k="26" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="w"
	k="23" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="14" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="one"
	k="18" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="seven"
	k="16" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="nine"
	k="18" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="zero"
	k="17" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="V"
	k="53" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="W"
	k="49" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="quotedblleft,quoteleft"
	k="72" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="25" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="space"
	k="29" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="guilsinglleft,guillemotleft"
	k="12" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="ampersand"
	k="14" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="10" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="5" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="5" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="5" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="5" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="four"
	k="10" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="six"
	k="14" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="j,jcircumflex"
	k="5" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="question"
	k="30" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="two"
	k="10" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="eight"
	k="14" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="parenright"
	k="13" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="asterisk"
	k="55" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="braceright"
	k="20" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="backslash"
	k="54" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="bracketright"
	k="28" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="copyright"
	k="31" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="registered"
	k="31" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="ordfeminine"
	k="41" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="ordmasculine"
	k="41" />
    <hkern g1="A,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	g2="trademark"
	k="66" />
    <hkern g1="B"
	g2="t,tcaron,uni0163,tbar"
	k="32" />
    <hkern g1="B"
	g2="y,yacute,ydieresis"
	k="31" />
    <hkern g1="B"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="5" />
    <hkern g1="B"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="B"
	g2="Y,Yacute,Ydieresis"
	k="27" />
    <hkern g1="B"
	g2="T,Tcaron,uni0162,Tbar"
	k="12" />
    <hkern g1="B"
	g2="f,germandbls,fi,fl"
	k="33" />
    <hkern g1="B"
	g2="z,zacute,zcaron,zdotaccent"
	k="39" />
    <hkern g1="B"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="12" />
    <hkern g1="B"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="19" />
    <hkern g1="B"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="25" />
    <hkern g1="B"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="20" />
    <hkern g1="B"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="20" />
    <hkern g1="B"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="19" />
    <hkern g1="B"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="20" />
    <hkern g1="B"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="6" />
    <hkern g1="B"
	g2="j,jcircumflex"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="Y,Yacute,Ydieresis"
	k="43" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="T,Tcaron,uni0162,Tbar"
	k="31" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="f,germandbls,fi,fl"
	k="8" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="seven"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="V"
	k="26" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="W"
	k="24" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="quotedblleft,quoteleft"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="32" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="z,zacute,zcaron,zdotaccent"
	k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="19" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="17" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="17" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="17" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="17" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="17" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="6" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="ae"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="x"
	k="11" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="X"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="j,jcircumflex"
	k="17" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="parenright"
	k="30" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="braceright"
	k="24" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="bracketright"
	k="28" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="t,tcaron,uni0163,tbar"
	k="22" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="y,yacute,ydieresis"
	k="31" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="f,germandbls,fi,fl"
	k="27" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="v"
	k="31" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="w"
	k="31" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="16" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="z,zacute,zcaron,zdotaccent"
	k="7" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="26" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="guilsinglleft,guillemotleft"
	k="22" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="23" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="10" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="19" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="13" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="19" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="13" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="ae"
	k="10" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="x"
	k="6" />
    <hkern g1="E,AE,Eacute,Edieresis,Egrave,Ecircumflex,Eogonek,Ecaron,OE,Etilde,Emacron,Edotaccent"
	g2="j,jcircumflex"
	k="19" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="t,tcaron,uni0163,tbar"
	k="24" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="y,yacute,ydieresis"
	k="24" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="f,germandbls,fi,fl"
	k="25" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="v"
	k="25" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="w"
	k="25" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="9" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="z,zacute,zcaron,zdotaccent"
	k="25" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="23" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="12" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="30" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="11" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="11" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="11" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="11" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="ae"
	k="29" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="x"
	k="23" />
    <hkern g1="H,I,M,N,Ntilde,Iacute,Idieresis,Igrave,Icircumflex,Nacute,Ncaron,Hbar,Hcircumflex,Idotaccent,Itilde,Iogonek,Imacron,uni0145"
	g2="j,jcircumflex"
	k="11" />
    <hkern g1="J,Jcircumflex"
	g2="t,tcaron,uni0163,tbar"
	k="24" />
    <hkern g1="J,Jcircumflex"
	g2="y,yacute,ydieresis"
	k="23" />
    <hkern g1="J,Jcircumflex"
	g2="f,germandbls,fi,fl"
	k="26" />
    <hkern g1="J,Jcircumflex"
	g2="v"
	k="23" />
    <hkern g1="J,Jcircumflex"
	g2="w"
	k="24" />
    <hkern g1="J,Jcircumflex"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="10" />
    <hkern g1="J,Jcircumflex"
	g2="z,zacute,zcaron,zdotaccent"
	k="27" />
    <hkern g1="J,Jcircumflex"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="23" />
    <hkern g1="J,Jcircumflex"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="12" />
    <hkern g1="J,Jcircumflex"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="31" />
    <hkern g1="J,Jcircumflex"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="11" />
    <hkern g1="J,Jcircumflex"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="12" />
    <hkern g1="J,Jcircumflex"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="11" />
    <hkern g1="J,Jcircumflex"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="12" />
    <hkern g1="J,Jcircumflex"
	g2="ae"
	k="29" />
    <hkern g1="J,Jcircumflex"
	g2="x"
	k="25" />
    <hkern g1="J,Jcircumflex"
	g2="j,jcircumflex"
	k="11" />
    <hkern g1="K,uni0136"
	g2="t,tcaron,uni0163,tbar"
	k="30" />
    <hkern g1="K,uni0136"
	g2="y,yacute,ydieresis"
	k="60" />
    <hkern g1="K,uni0136"
	g2="f,germandbls,fi,fl"
	k="29" />
    <hkern g1="K,uni0136"
	g2="v"
	k="60" />
    <hkern g1="K,uni0136"
	g2="w"
	k="58" />
    <hkern g1="K,uni0136"
	g2="zero"
	k="12" />
    <hkern g1="K,uni0136"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="45" />
    <hkern g1="K,uni0136"
	g2="space"
	k="13" />
    <hkern g1="K,uni0136"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="27" />
    <hkern g1="K,uni0136"
	g2="guilsinglleft,guillemotleft"
	k="31" />
    <hkern g1="K,uni0136"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="18" />
    <hkern g1="K,uni0136"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="6" />
    <hkern g1="K,uni0136"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="6" />
    <hkern g1="K,uni0136"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="6" />
    <hkern g1="K,uni0136"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="6" />
    <hkern g1="K,uni0136"
	g2="ae"
	k="6" />
    <hkern g1="K,uni0136"
	g2="j,jcircumflex"
	k="6" />
    <hkern g1="K,uni0136"
	g2="copyright"
	k="38" />
    <hkern g1="K,uni0136"
	g2="registered"
	k="38" />
    <hkern g1="K,uni0136"
	g2="ordfeminine"
	k="35" />
    <hkern g1="K,uni0136"
	g2="ordmasculine"
	k="34" />
    <hkern g1="P"
	g2="t,tcaron,uni0163,tbar"
	k="10" />
    <hkern g1="P"
	g2="y,yacute,ydieresis"
	k="6" />
    <hkern g1="P"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="56" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="P"
	g2="Y,Yacute,Ydieresis"
	k="13" />
    <hkern g1="P"
	g2="f,germandbls,fi,fl"
	k="12" />
    <hkern g1="P"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="102" />
    <hkern g1="P"
	g2="z,zacute,zcaron,zdotaccent"
	k="8" />
    <hkern g1="P"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="79" />
    <hkern g1="P"
	g2="guilsinglleft,guillemotleft"
	k="44" />
    <hkern g1="P"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="P"
	g2="hyphen,endash,emdash"
	k="63" />
    <hkern g1="P"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="42" />
    <hkern g1="P"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="61" />
    <hkern g1="P"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="50" />
    <hkern g1="P"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="11" />
    <hkern g1="P"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="9" />
    <hkern g1="P"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="11" />
    <hkern g1="P"
	g2="j,jcircumflex"
	k="9" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="t,tcaron,uni0163,tbar"
	k="35" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="y,yacute,ydieresis"
	k="48" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="Y,Yacute,Ydieresis"
	k="39" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="T,Tcaron,uni0162,Tbar"
	k="23" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="f,germandbls,fi,fl"
	k="37" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="v"
	k="48" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="w"
	k="48" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="6" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="V"
	k="27" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="W"
	k="26" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="10" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="z,zacute,zcaron,zdotaccent"
	k="7" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="42" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="guilsinglleft,guillemotleft"
	k="30" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="33" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="14" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="20" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="21" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="21" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="21" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="ae"
	k="18" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="x"
	k="6" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="j,jcircumflex"
	k="21" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="bracketright"
	k="15" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="t,tcaron,uni0163,tbar"
	k="22" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="y,yacute,ydieresis"
	k="19" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="15" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="f,germandbls,fi,fl"
	k="28" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="v"
	k="19" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="w"
	k="20" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="23" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="z,zacute,zcaron,zdotaccent"
	k="31" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="34" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="24" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="34" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="20" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="21" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="21" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="21" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="ae"
	k="34" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="x"
	k="30" />
    <hkern g1="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	g2="j,jcircumflex"
	k="21" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="t,tcaron,uni0163,tbar"
	k="84" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="y,yacute,ydieresis"
	k="115" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="66" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="f,germandbls,fi,fl"
	k="67" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="v"
	k="116" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="w"
	k="117" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="nine"
	k="31" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="zero"
	k="41" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="42" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="space"
	k="41" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="z,zacute,zcaron,zdotaccent"
	k="105" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="152" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guilsinglleft,guillemotleft"
	k="94" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="at"
	k="68" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="colon,semicolon"
	k="65" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="hyphen,endash,emdash"
	k="79" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="slash"
	k="65" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guilsinglright,guillemotright"
	k="69" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="ampersand"
	k="33" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="123" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="132" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="128" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="18" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="62" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="18" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="23" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="ae"
	k="127" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="four"
	k="76" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="six"
	k="72" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="x"
	k="112" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="j,jcircumflex"
	k="63" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="two"
	k="23" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="eight"
	k="42" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="copyright"
	k="45" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="registered"
	k="45" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="ordfeminine"
	k="26" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="ordmasculine"
	k="25" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="trademark"
	k="-14" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="five"
	k="14" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="bar"
	k="13" />
    <hkern g1="F"
	g2="t,tcaron,uni0163,tbar"
	k="27" />
    <hkern g1="F"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="F"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="51" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="5" />
    <hkern g1="F"
	g2="f,germandbls,fi,fl"
	k="33" />
    <hkern g1="F"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="98" />
    <hkern g1="F"
	g2="z,zacute,zcaron,zdotaccent"
	k="47" />
    <hkern g1="F"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="64" />
    <hkern g1="F"
	g2="guilsinglleft,guillemotleft"
	k="35" />
    <hkern g1="F"
	g2="colon,semicolon"
	k="15" />
    <hkern g1="F"
	g2="hyphen,endash,emdash"
	k="33" />
    <hkern g1="F"
	g2="guilsinglright,guillemotright"
	k="16" />
    <hkern g1="F"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="57" />
    <hkern g1="F"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="58" />
    <hkern g1="F"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="55" />
    <hkern g1="F"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="22" />
    <hkern g1="F"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="41" />
    <hkern g1="F"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="22" />
    <hkern g1="F"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="11" />
    <hkern g1="F"
	g2="j,jcircumflex"
	k="42" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="t,tcaron,uni0163,tbar"
	k="32" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="y,yacute,ydieresis"
	k="84" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="Y,Yacute,Ydieresis"
	k="105" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="T,Tcaron,uni0162,Tbar"
	k="90" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="quoteright,quotedblright"
	k="92" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="quotedbl,quotesingle"
	k="101" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="f,germandbls,fi,fl"
	k="31" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="v"
	k="85" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="w"
	k="76" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="15" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="nine"
	k="11" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="zero"
	k="12" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="V"
	k="97" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="W"
	k="94" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="quotedblleft,quoteleft"
	k="92" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="38" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="space"
	k="33" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="28" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="guilsinglleft,guillemotleft"
	k="57" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="hyphen,endash,emdash"
	k="57" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="15" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="5" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="5" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="5" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="5" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="four"
	k="14" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="j,jcircumflex"
	k="6" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="question"
	k="27" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="asterisk"
	k="110" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="backslash"
	k="62" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="bracketright"
	k="17" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="copyright"
	k="78" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="registered"
	k="78" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="ordfeminine"
	k="109" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="ordmasculine"
	k="109" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="trademark"
	k="112" />
    <hkern g1="L,Lslash,Lcaron,Lacute,uni013B"
	g2="periodcentered"
	k="83" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="t,tcaron,uni0163,tbar"
	k="38" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="y,yacute,ydieresis"
	k="64" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="f,germandbls,fi,fl"
	k="38" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="v"
	k="65" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="w"
	k="62" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="nine"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="23" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="z,zacute,zcaron,zdotaccent"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="guilsinglleft,guillemotleft"
	k="26" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="j,jcircumflex"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="copyright"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="registered"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="ordfeminine"
	k="26" />
    <hkern g1="C,Ccedilla,Cacute,Ccaron,Cdotaccent,Ccircumflex"
	g2="ordmasculine"
	k="26" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="t,tcaron,uni0163,tbar"
	k="98" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="y,yacute,ydieresis"
	k="109" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="65" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="f,germandbls,fi,fl"
	k="79" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="v"
	k="110" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="w"
	k="111" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="nine"
	k="20" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="zero"
	k="31" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="31" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="space"
	k="29" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="63" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="z,zacute,zcaron,zdotaccent"
	k="105" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="113" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="guilsinglleft,guillemotleft"
	k="77" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="at"
	k="60" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="hyphen,endash,emdash"
	k="50" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="slash"
	k="48" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="guilsinglright,guillemotright"
	k="68" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="ampersand"
	k="14" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="114" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="109" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="115" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="33" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="94" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="33" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="ae"
	k="108" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="four"
	k="57" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="six"
	k="63" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="x"
	k="103" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="j,jcircumflex"
	k="94" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="eight"
	k="21" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="copyright"
	k="37" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="registered"
	k="37" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="ordfeminine"
	k="13" />
    <hkern g1="T,Tcaron,uni0162,Tbar"
	g2="ordmasculine"
	k="12" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="t,tcaron,uni0163,tbar"
	k="15" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="y,yacute,ydieresis"
	k="18" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="Y,Yacute,Ydieresis"
	k="14" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="f,germandbls,fi,fl"
	k="18" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="v"
	k="18" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="w"
	k="17" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="V"
	k="13" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="W"
	k="12" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="z,zacute,zcaron,zdotaccent"
	k="18" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="15" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="11" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="15" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="15" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="16" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="15" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="ae"
	k="11" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="x"
	k="14" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcircumflex,uni0122"
	g2="j,jcircumflex"
	k="16" />
    <hkern g1="V"
	g2="t,tcaron,uni0163,tbar"
	k="59" />
    <hkern g1="V"
	g2="y,yacute,ydieresis"
	k="74" />
    <hkern g1="V"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="53" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="9" />
    <hkern g1="V"
	g2="f,germandbls,fi,fl"
	k="59" />
    <hkern g1="V"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="25" />
    <hkern g1="V"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="83" />
    <hkern g1="V"
	g2="z,zacute,zcaron,zdotaccent"
	k="71" />
    <hkern g1="V"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="105" />
    <hkern g1="V"
	g2="guilsinglleft,guillemotleft"
	k="71" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="45" />
    <hkern g1="V"
	g2="hyphen,endash,emdash"
	k="52" />
    <hkern g1="V"
	g2="guilsinglright,guillemotright"
	k="46" />
    <hkern g1="V"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="85" />
    <hkern g1="V"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="104" />
    <hkern g1="V"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="87" />
    <hkern g1="V"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="12" />
    <hkern g1="V"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="52" />
    <hkern g1="V"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="12" />
    <hkern g1="V"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="16" />
    <hkern g1="V"
	g2="j,jcircumflex"
	k="52" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="t,tcaron,uni0163,tbar"
	k="18" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="y,yacute,ydieresis"
	k="20" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="Y,Yacute,Ydieresis"
	k="13" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="f,germandbls,fi,fl"
	k="22" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="v"
	k="21" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="w"
	k="20" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="V"
	k="13" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="W"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="z,zacute,zcaron,zdotaccent"
	k="28" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="6" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="11" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="ae"
	k="9" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="x"
	k="22" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="j,jcircumflex"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="ordfeminine"
	k="10" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex"
	g2="ordmasculine"
	k="10" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="t,tcaron,uni0163,tbar"
	k="31" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="y,yacute,ydieresis"
	k="62" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="f,germandbls,fi,fl"
	k="31" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="v"
	k="63" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="w"
	k="60" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="21" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="z,zacute,zcaron,zdotaccent"
	k="5" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="16" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="guilsinglleft,guillemotleft"
	k="13" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="15" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="5" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="11" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="11" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="11" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="11" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="ae"
	k="5" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="j,jcircumflex"
	k="11" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="copyright"
	k="32" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="registered"
	k="32" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="ordfeminine"
	k="31" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="ordmasculine"
	k="31" />
    <hkern g1="W"
	g2="t,tcaron,uni0163,tbar"
	k="58" />
    <hkern g1="W"
	g2="y,yacute,ydieresis"
	k="74" />
    <hkern g1="W"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="50" />
    <hkern g1="W"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="W"
	g2="f,germandbls,fi,fl"
	k="58" />
    <hkern g1="W"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="24" />
    <hkern g1="W"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="78" />
    <hkern g1="W"
	g2="z,zacute,zcaron,zdotaccent"
	k="67" />
    <hkern g1="W"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="99" />
    <hkern g1="W"
	g2="guilsinglleft,guillemotleft"
	k="67" />
    <hkern g1="W"
	g2="colon,semicolon"
	k="42" />
    <hkern g1="W"
	g2="hyphen,endash,emdash"
	k="48" />
    <hkern g1="W"
	g2="guilsinglright,guillemotright"
	k="44" />
    <hkern g1="W"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="80" />
    <hkern g1="W"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="100" />
    <hkern g1="W"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="81" />
    <hkern g1="W"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="11" />
    <hkern g1="W"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="51" />
    <hkern g1="W"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="11" />
    <hkern g1="W"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="15" />
    <hkern g1="W"
	g2="j,jcircumflex"
	k="51" />
    <hkern g1="X"
	g2="t,tcaron,uni0163,tbar"
	k="45" />
    <hkern g1="X"
	g2="y,yacute,ydieresis"
	k="59" />
    <hkern g1="X"
	g2="f,germandbls,fi,fl"
	k="44" />
    <hkern g1="X"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="20" />
    <hkern g1="X"
	g2="z,zacute,zcaron,zdotaccent"
	k="5" />
    <hkern g1="X"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="38" />
    <hkern g1="X"
	g2="guilsinglleft,guillemotleft"
	k="32" />
    <hkern g1="X"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="32" />
    <hkern g1="X"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="11" />
    <hkern g1="X"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="19" />
    <hkern g1="X"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="17" />
    <hkern g1="X"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="19" />
    <hkern g1="X"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="17" />
    <hkern g1="X"
	g2="j,jcircumflex"
	k="19" />
    <hkern g1="Thorn"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="25" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ydieresis"
	k="48" />
    <hkern g1="Thorn"
	g2="T,Tcaron,uni0162,Tbar"
	k="63" />
    <hkern g1="Thorn"
	g2="quoteright,quotedblright"
	k="24" />
    <hkern g1="Thorn"
	g2="quotedblleft,quoteleft"
	k="31" />
    <hkern g1="Thorn"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="42" />
    <hkern g1="Thorn"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="Thorn"
	g2="hyphen,endash,emdash"
	k="9" />
    <hkern g1="Thorn"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="8" />
    <hkern g1="Thorn"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="8" />
    <hkern g1="Thorn"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="9" />
    <hkern g1="Thorn"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="8" />
    <hkern g1="Thorn"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="9" />
    <hkern g1="Thorn"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="8" />
    <hkern g1="Thorn"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="5" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="30" />
    <hkern g1="Thorn"
	g2="j,jcircumflex"
	k="9" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="25" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="Y,Yacute,Ydieresis"
	k="42" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="T,Tcaron,uni0162,Tbar"
	k="30" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="f,germandbls,fi,fl"
	k="7" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="seven"
	k="10" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="V"
	k="26" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="W"
	k="24" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="quotedblleft,quoteleft"
	k="10" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="34" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="z,zacute,zcaron,zdotaccent"
	k="11" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="20" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="u,uacute,udieresis,ugrave,ucircumflex,uring,uhungarumlaut,ubreve,umacron,uogonek,utilde"
	k="17" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="17" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="m,r,p,ntilde,n,racute,nacute,ncaron,rcaron,uni0157,uni0146"
	k="19" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="l,lslash,lcaron,lacute,uni013C"
	k="16" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek"
	k="17" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="b,h,k,thorn,hbar,hcircumflex,uni0137"
	k="16" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="6" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="ae"
	k="20" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="x"
	k="10" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="26" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="X"
	k="20" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="j,jcircumflex"
	k="17" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="parenright"
	k="29" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="braceright"
	k="24" />
    <hkern g1="Q,Oacute,Odieresis,Ograve,Oslash,Otilde,Ocircumflex,O,Ohungarumlaut,Omacron"
	g2="bracketright"
	k="28" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="37" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="J,Jcircumflex"
	k="38" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="Y,Yacute,Ydieresis"
	k="96" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="T,Tcaron,uni0162,Tbar"
	k="85" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="quoteright,quotedblright"
	k="12" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="quotedbl,quotesingle"
	k="9" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="23" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="18" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="V"
	k="65" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="W"
	k="62" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="quotedblleft,quoteleft"
	k="14" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="space"
	k="19" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="21" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="12" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="guilsinglleft,guillemotleft"
	k="11" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="hyphen,endash,emdash"
	k="12" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="slash"
	k="10" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="36" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="47" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="X"
	k="56" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="parenright"
	k="36" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="asterisk"
	k="9" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="braceright"
	k="28" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="backslash"
	k="19" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="bracketright"
	k="32" />
    <hkern g1="t,tcaron,uni0163,tbar"
	g2="trademark"
	k="14" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="5" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="J,Jcircumflex"
	k="23" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="Y,Yacute,Ydieresis"
	k="19" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="T,Tcaron,uni0162,Tbar"
	k="33" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="11" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="21" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="V"
	k="12" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="W"
	k="11" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="16" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="15" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="13" />
    <hkern g1="l,lslash,lcaron,lacute,uni013C,fl"
	g2="X"
	k="17" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="5" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="J,Jcircumflex"
	k="24" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="Y,Yacute,Ydieresis"
	k="62" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="T,Tcaron,uni0162,Tbar"
	k="92" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="11" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="21" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="V"
	k="52" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="W"
	k="51" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="17" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="14" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="13" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="X"
	k="20" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="parenright"
	k="16" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="braceright"
	k="15" />
    <hkern g1="i,iacute,igrave,idieresis,icircumflex,dotlessi,itilde,imacron,iogonek,fi"
	g2="bracketright"
	k="17" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="J,Jcircumflex"
	k="38" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="Y,Yacute,Ydieresis"
	k="134" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="T,Tcaron,uni0162,Tbar"
	k="103" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="26" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="35" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="V"
	k="98" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="W"
	k="92" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="15" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="guilsinglleft,guillemotleft"
	k="18" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="hyphen,endash,emdash"
	k="12" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="13" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="X"
	k="9" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="question"
	k="24" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="parenright"
	k="33" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="asterisk"
	k="18" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="braceright"
	k="31" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="backslash"
	k="38" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="bracketright"
	k="38" />
    <hkern g1="c,ccedilla,cacute,ccaron,cdotaccent,ccircumflex"
	g2="trademark"
	k="16" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="40" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="J,Jcircumflex"
	k="38" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="Y,Yacute,Ydieresis"
	k="98" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="T,Tcaron,uni0162,Tbar"
	k="84" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="21" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="17" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="V"
	k="65" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="W"
	k="62" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="space"
	k="21" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="35" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="14" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="guilsinglleft,guillemotleft"
	k="12" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="slash"
	k="15" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="36" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="61" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="X"
	k="59" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="parenright"
	k="41" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="braceright"
	k="30" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="backslash"
	k="21" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="bracketright"
	k="36" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="y,yacute,ydieresis"
	k="6" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="9" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="J,Jcircumflex"
	k="40" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="Y,Yacute,Ydieresis"
	k="133" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="T,Tcaron,uni0162,Tbar"
	k="106" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="quotedbl,quotesingle"
	k="16" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="v"
	k="6" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="w"
	k="5" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="29" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="31" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="V"
	k="111" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="W"
	k="104" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="quotedblleft,quoteleft"
	k="9" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="12" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="24" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="ae"
	k="4" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="18" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="X"
	k="26" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="question"
	k="32" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="parenright"
	k="41" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="asterisk"
	k="23" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="braceright"
	k="38" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="backslash"
	k="45" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="bracketright"
	k="42" />
    <hkern g1="e,eacute,edieresis,egrave,ae,ecircumflex,eogonek,ecaron,oe,emacron,edotaccent"
	g2="trademark"
	k="22" />
    <hkern g1="f"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="37" />
    <hkern g1="f"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="f"
	g2="Y,Yacute,Ydieresis"
	k="-12" />
    <hkern g1="f"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="9" />
    <hkern g1="f"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="7" />
    <hkern g1="f"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="25" />
    <hkern g1="f"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="f"
	g2="guilsinglleft,guillemotleft"
	k="12" />
    <hkern g1="f"
	g2="hyphen,endash,emdash"
	k="16" />
    <hkern g1="f"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="21" />
    <hkern g1="v"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="26" />
    <hkern g1="v"
	g2="J,Jcircumflex"
	k="42" />
    <hkern g1="v"
	g2="Y,Yacute,Ydieresis"
	k="114" />
    <hkern g1="v"
	g2="T,Tcaron,uni0162,Tbar"
	k="108" />
    <hkern g1="v"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="24" />
    <hkern g1="v"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="19" />
    <hkern g1="v"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="37" />
    <hkern g1="v"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="v"
	g2="guilsinglleft,guillemotleft"
	k="11" />
    <hkern g1="v"
	g2="hyphen,endash,emdash"
	k="9" />
    <hkern g1="v"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="7" />
    <hkern g1="v"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="38" />
    <hkern g1="v"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="57" />
    <hkern g1="j,jcircumflex"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="5" />
    <hkern g1="j,jcircumflex"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="j,jcircumflex"
	g2="Y,Yacute,Ydieresis"
	k="61" />
    <hkern g1="j,jcircumflex"
	g2="T,Tcaron,uni0162,Tbar"
	k="92" />
    <hkern g1="j,jcircumflex"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="11" />
    <hkern g1="j,jcircumflex"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="21" />
    <hkern g1="j,jcircumflex"
	g2="V"
	k="52" />
    <hkern g1="j,jcircumflex"
	g2="W"
	k="51" />
    <hkern g1="j,jcircumflex"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="16" />
    <hkern g1="j,jcircumflex"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="14" />
    <hkern g1="j,jcircumflex"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="13" />
    <hkern g1="j,jcircumflex"
	g2="X"
	k="20" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="y,yacute,ydieresis"
	k="7" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="J,Jcircumflex"
	k="41" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="Y,Yacute,Ydieresis"
	k="126" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="T,Tcaron,uni0162,Tbar"
	k="107" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="quotedbl,quotesingle"
	k="12" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="v"
	k="7" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="w"
	k="7" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="29" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="34" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="V"
	k="102" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="W"
	k="96" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="20" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="10" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="9" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="X"
	k="15" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="question"
	k="27" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="parenright"
	k="36" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="asterisk"
	k="19" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="braceright"
	k="31" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="backslash"
	k="44" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="bracketright"
	k="40" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex"
	g2="trademark"
	k="18" />
    <hkern g1="k,uni0137"
	g2="J,Jcircumflex"
	k="31" />
    <hkern g1="k,uni0137"
	g2="Y,Yacute,Ydieresis"
	k="112" />
    <hkern g1="k,uni0137"
	g2="T,Tcaron,uni0162,Tbar"
	k="104" />
    <hkern g1="k,uni0137"
	g2="quoteright,quotedblright"
	k="36" />
    <hkern g1="k,uni0137"
	g2="quotedbl,quotesingle"
	k="26" />
    <hkern g1="k,uni0137"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="27" />
    <hkern g1="k,uni0137"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="32" />
    <hkern g1="k,uni0137"
	g2="V"
	k="99" />
    <hkern g1="k,uni0137"
	g2="W"
	k="94" />
    <hkern g1="k,uni0137"
	g2="quotedblleft,quoteleft"
	k="36" />
    <hkern g1="k,uni0137"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="22" />
    <hkern g1="k,uni0137"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="18" />
    <hkern g1="k,uni0137"
	g2="guilsinglleft,guillemotleft"
	k="23" />
    <hkern g1="k,uni0137"
	g2="hyphen,endash,emdash"
	k="8" />
    <hkern g1="k,uni0137"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="10" />
    <hkern g1="k,uni0137"
	g2="question"
	k="25" />
    <hkern g1="k,uni0137"
	g2="parenright"
	k="19" />
    <hkern g1="k,uni0137"
	g2="asterisk"
	k="22" />
    <hkern g1="k,uni0137"
	g2="braceright"
	k="24" />
    <hkern g1="k,uni0137"
	g2="backslash"
	k="37" />
    <hkern g1="k,uni0137"
	g2="bracketright"
	k="31" />
    <hkern g1="k,uni0137"
	g2="trademark"
	k="31" />
    <hkern g1="y,yacute,ydieresis"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="26" />
    <hkern g1="y,yacute,ydieresis"
	g2="J,Jcircumflex"
	k="42" />
    <hkern g1="y,yacute,ydieresis"
	g2="Y,Yacute,Ydieresis"
	k="116" />
    <hkern g1="y,yacute,ydieresis"
	g2="T,Tcaron,uni0162,Tbar"
	k="108" />
    <hkern g1="y,yacute,ydieresis"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="25" />
    <hkern g1="y,yacute,ydieresis"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="V"
	k="75" />
    <hkern g1="y,yacute,ydieresis"
	g2="W"
	k="70" />
    <hkern g1="y,yacute,ydieresis"
	g2="space"
	k="23" />
    <hkern g1="y,yacute,ydieresis"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="36" />
    <hkern g1="y,yacute,ydieresis"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="y,yacute,ydieresis"
	g2="guilsinglleft,guillemotleft"
	k="10" />
    <hkern g1="y,yacute,ydieresis"
	g2="slash"
	k="12" />
    <hkern g1="y,yacute,ydieresis"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="7" />
    <hkern g1="y,yacute,ydieresis"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="40" />
    <hkern g1="y,yacute,ydieresis"
	g2="ae"
	k="7" />
    <hkern g1="y,yacute,ydieresis"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="57" />
    <hkern g1="y,yacute,ydieresis"
	g2="X"
	k="59" />
    <hkern g1="y,yacute,ydieresis"
	g2="question"
	k="13" />
    <hkern g1="y,yacute,ydieresis"
	g2="parenright"
	k="41" />
    <hkern g1="y,yacute,ydieresis"
	g2="braceright"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="backslash"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="bracketright"
	k="36" />
    <hkern g1="y,yacute,ydieresis"
	g2="trademark"
	k="8" />
    <hkern g1="w"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="24" />
    <hkern g1="w"
	g2="J,Jcircumflex"
	k="39" />
    <hkern g1="w"
	g2="Y,Yacute,Ydieresis"
	k="115" />
    <hkern g1="w"
	g2="T,Tcaron,uni0162,Tbar"
	k="107" />
    <hkern g1="w"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="24" />
    <hkern g1="w"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="19" />
    <hkern g1="w"
	g2="comma,period,quotedblbase,quotesinglbase,ellipsis"
	k="34" />
    <hkern g1="w"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="12" />
    <hkern g1="w"
	g2="s,sacute,scaron,scedilla,scircumflex"
	k="7" />
    <hkern g1="w"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="37" />
    <hkern g1="w"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="54" />
    <hkern g1="x"
	g2="J,Jcircumflex"
	k="35" />
    <hkern g1="x"
	g2="Y,Yacute,Ydieresis"
	k="113" />
    <hkern g1="x"
	g2="T,Tcaron,uni0162,Tbar"
	k="103" />
    <hkern g1="x"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="25" />
    <hkern g1="x"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="34" />
    <hkern g1="x"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="13" />
    <hkern g1="x"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="13" />
    <hkern g1="x"
	g2="guilsinglleft,guillemotleft"
	k="19" />
    <hkern g1="x"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="11" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="J,Jcircumflex"
	k="36" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="Y,Yacute,Ydieresis"
	k="122" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="T,Tcaron,uni0162,Tbar"
	k="102" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="25" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="34" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="V"
	k="92" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="W"
	k="86" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="13" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="c,q,e,aacute,eacute,oacute,adieresis,edieresis,odieresis,agrave,egrave,ograve,aring,oslash,o,acircumflex,ecircumflex,ocircumflex,atilde,otilde,ccedilla,eth,g,d,a,aogonek,abreve,cacute,ccaron,eogonek,ecaron,dcaron,dcroat,ohungarumlaut,gbreve,cdotaccent,ccircumflex,gdotaccent,gcircumflex,oe,emacron,uni0123,omacron,amacron,edotaccent"
	k="8" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="guilsinglleft,guillemotleft"
	k="19" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="hyphen,endash,emdash"
	k="8" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="9" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="X"
	k="5" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="question"
	k="20" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="parenright"
	k="24" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="asterisk"
	k="8" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="braceright"
	k="26" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="backslash"
	k="34" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="bracketright"
	k="34" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="trademark"
	k="13" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="t,tcaron,uni0163,tbar"
	k="6" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="y,yacute,ydieresis"
	k="13" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="13" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="J,Jcircumflex"
	k="36" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="Y,Yacute,Ydieresis"
	k="151" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="T,Tcaron,uni0162,Tbar"
	k="112" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="quoteright,quotedblright"
	k="9" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="quotedbl,quotesingle"
	k="21" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="f,germandbls,fi,fl"
	k="8" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="v"
	k="13" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="w"
	k="12" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="23" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="34" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="V"
	k="105" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="W"
	k="97" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="quotedblleft,quoteleft"
	k="13" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="19" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="z,zacute,zcaron,zdotaccent"
	k="15" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="25" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="x"
	k="13" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="28" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="X"
	k="38" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="question"
	k="37" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="parenright"
	k="47" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="asterisk"
	k="27" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="braceright"
	k="37" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="backslash"
	k="52" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="bracketright"
	k="45" />
    <hkern g1="b,p,oacute,odieresis,ograve,oslash,o,ocircumflex,otilde,thorn,ohungarumlaut,omacron"
	g2="trademark"
	k="26" />
    <hkern g1="eth"
	g2="y,yacute,ydieresis"
	k="4" />
    <hkern g1="eth"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="15" />
    <hkern g1="eth"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="eth"
	g2="Y,Yacute,Ydieresis"
	k="106" />
    <hkern g1="eth"
	g2="T,Tcaron,uni0162,Tbar"
	k="91" />
    <hkern g1="eth"
	g2="quoteright,quotedblright"
	k="18" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="11" />
    <hkern g1="eth"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="17" />
    <hkern g1="eth"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="30" />
    <hkern g1="eth"
	g2="quotedblleft,quoteleft"
	k="18" />
    <hkern g1="eth"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="15" />
    <hkern g1="eth"
	g2="z,zacute,zcaron,zdotaccent"
	k="7" />
    <hkern g1="eth"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="26" />
    <hkern g1="eth"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="28" />
    <hkern g1="germandbls"
	g2="t,tcaron,uni0163,tbar"
	k="7" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis"
	k="13" />
    <hkern g1="germandbls"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="14" />
    <hkern g1="germandbls"
	g2="J,Jcircumflex"
	k="35" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ydieresis"
	k="96" />
    <hkern g1="germandbls"
	g2="T,Tcaron,uni0162,Tbar"
	k="89" />
    <hkern g1="germandbls"
	g2="quoteright,quotedblright"
	k="9" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="8" />
    <hkern g1="germandbls"
	g2="f,germandbls,fi,fl"
	k="8" />
    <hkern g1="germandbls"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="22" />
    <hkern g1="germandbls"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="34" />
    <hkern g1="germandbls"
	g2="quotedblleft,quoteleft"
	k="12" />
    <hkern g1="germandbls"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="19" />
    <hkern g1="germandbls"
	g2="z,zacute,zcaron,zdotaccent"
	k="15" />
    <hkern g1="germandbls"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="25" />
    <hkern g1="germandbls"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="28" />
    <hkern g1="d,dcaron,dcroat"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="6" />
    <hkern g1="d,dcaron,dcroat"
	g2="J,Jcircumflex"
	k="23" />
    <hkern g1="d,dcaron,dcroat"
	g2="Y,Yacute,Ydieresis"
	k="18" />
    <hkern g1="d,dcaron,dcroat"
	g2="T,Tcaron,uni0162,Tbar"
	k="31" />
    <hkern g1="d,dcaron,dcroat"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="11" />
    <hkern g1="d,dcaron,dcroat"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="20" />
    <hkern g1="d,dcaron,dcroat"
	g2="V"
	k="13" />
    <hkern g1="d,dcaron,dcroat"
	g2="W"
	k="11" />
    <hkern g1="d,dcaron,dcroat"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="16" />
    <hkern g1="d,dcaron,dcroat"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="15" />
    <hkern g1="d,dcaron,dcroat"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="13" />
    <hkern g1="d,dcaron,dcroat"
	g2="X"
	k="17" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="A,AE,Aring,Aacute,Adieresis,Agrave,Atilde,Acircumflex,Aogonek,Abreve,Amacron"
	k="5" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="J,Jcircumflex"
	k="25" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="Y,Yacute,Ydieresis"
	k="126" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="T,Tcaron,uni0162,Tbar"
	k="114" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="11" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="19" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="V"
	k="86" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="W"
	k="80" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="19" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="20" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="13" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="X"
	k="20" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="question"
	k="24" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="parenright"
	k="33" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="asterisk"
	k="10" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="braceright"
	k="30" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="backslash"
	k="38" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="bracketright"
	k="38" />
    <hkern g1="q,u,aacute,uacute,adieresis,udieresis,agrave,ugrave,aring,acircumflex,ucircumflex,atilde,g,a,aogonek,abreve,uring,uhungarumlaut,gbreve,gdotaccent,gcircumflex,ubreve,uni0123,umacron,amacron,uogonek,utilde"
	g2="trademark"
	k="13" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="J,Jcircumflex"
	k="25" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="Y,Yacute,Ydieresis"
	k="145" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="T,Tcaron,uni0162,Tbar"
	k="115" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="quotedbl,quotesingle"
	k="13" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="f,germandbls,fi,fl"
	k="4" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="v"
	k="6" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="w"
	k="4" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="B,D,E,H,I,K,M,N,P,R,F,L,Ntilde,Eacute,Iacute,Edieresis,Idieresis,Egrave,Igrave,Eth,Thorn,Ecircumflex,Icircumflex,Lslash,Lcaron,Racute,Lacute,Eogonek,Ecaron,Dcaron,Dcroat,Nacute,Ncaron,Rcaron,Hbar,Hcircumflex,Idotaccent,Etilde,uni0156,Itilde,uni013B,Emacron,Iogonek,Edotaccent,Imacron,uni0145,uni0136"
	k="12" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="U,Uacute,Udieresis,Ugrave,Ucircumflex,Uring,Uhungarumlaut,Ubreve,Umacron,Uogonek,Utilde"
	k="21" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="V"
	k="102" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="W"
	k="95" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="C,Q,G,Oacute,Odieresis,Ograve,Oslash,Otilde,Ccedilla,Ocircumflex,O,Cacute,Ccaron,Ohungarumlaut,Gbreve,Cdotaccent,Ccircumflex,Gdotaccent,Gcircumflex,OE,uni0122,Omacron"
	k="24" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex"
	k="19" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="11" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="X"
	k="17" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="question"
	k="30" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="parenright"
	k="33" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="asterisk"
	k="20" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="braceright"
	k="32" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="backslash"
	k="47" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="bracketright"
	k="40" />
    <hkern g1="m,h,ntilde,n,nacute,ncaron,hbar,hcircumflex,uni0146"
	g2="trademark"
	k="18" />
  </font>
</defs></svg>

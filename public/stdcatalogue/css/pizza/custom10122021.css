h4.meal-type span {
    text-transform: capitalize;
}
.add-extra-items{
	padding-bottom: 0px;
}

.font-12{
	font-size: 12px;
}
.cursor-default{
	cursor: default;
}
.ui-widget.ui-widget-content {
	margin: auto;
}
/*
.ui-state-highlight, .ui-widget-content .ui-state-highlight{
	background: #f0ad4e !important; 
    color: #000 !important;
}
*/
/*.modal-open #myModal {
  display: flex;
  justify-content: center;
  align-items: center; 
}*/
.modal-dialog {
  /*display: flex;*/
  justify-content: center;
  align-items: center;
  height: 100%;
  margin: auto !important;
}

.product-description .meal_items{
  color: #333;
} 

.offered-plan-box{
  display: inline-table;
}

.offered-plan-box > span{
  padding-right: 8px;
} 

.more-time-slot:hover, .time-slot:focus, .time-slot:active {
    border: 1px solid #ddd;
    background: #f9f7f7;
    cursor: pointer;
}
.more-time-slot {
    padding: 20px;
    border: 1px dashed #ddd;
    text-align: center;
    margin-bottom: 15px;
}
.remove_extra .ti-close {
    color: #ff0000;
}
.cart-destroy .ti-close {
    color: #ff0000;
}
.order-summury .add-extra {
    color: #949494;
    padding: 0px !important; 
    width: fit-content;
}

.choose-days {
  height: 30px;
  width: 30px;
  background: #bfbcbc;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  line-height: 2.5rem;
  margin-right: 7px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center; }

.choose-days.selected {
  background: #4caf50;
  color: #fff; }

/*
.choose-days {
    height: 25px;
    width: 25px;
    background: #bfbcbc;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    line-height: 2.5rem;
    margin-right: 7px;
    cursor: pointer;
}
.choose-days.selected {
    height: 25px;
    width: 25px;
    background: #4caf50;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    line-height: 2.5rem;
    margin-right: 7px;
}
.choosedays {
    margin-bottom: 30px;
}
*/
.fs-item-offer {
color: #6542ea;
text-transform: capitalize;
padding: 5px 0;
line-height: 14px;
font-weight: 600;
font-size: 12px;
}

/* pooja css changes 20082021 */
.pagination > .active > a, .pagination > .active > a:hover, .pagination > .active > a:focus, .pagination > .active > span, .pagination > .active > span:hover, .pagination > .active > span:focus {
    z-index: 2;
    color: #fff;
    background-color: #24292d;
    border-color: #24292d;
    cursor: default;
}
.back-to-top, .back-to-top:hover{
        background: #dc2326 !important;
}
.plan-discount{
    padding-right: 5px;
}
.pattern-bg{
    background-image: url(./images/bg-2.png);
}
.address-info{
    padding: 10px 20px 15px !important;
    max-height: 160px !important;
    margin: 10px 0 !important;
}
.address-box{
    height: 380px !important;
    overflow-y: auto !important;
    padding: 10px 20px 0 !important;
}
.swapmeal-box {
    overflow-y: auto !important;
    padding: 10px 20px 10px !important;
}
.add-address-button{
   margin: 10px 0 20px !important;
}
.company-info-panel{
    height: 484px !important;
}
.pay-now-btn{
    background: #4CAF50;
    margin-top: 20px;
}

@media screen and (max-width: 767px){
  .add-extra-items {
    border-bottom: 0px solid #ddd !important; 
  }
  .modal-dialog {
    display: flex;
    position: relative;
    width: 70%;
    margin: 10px;
  }
  .modal-body{
    padding: 20px !important;
  }
  .modal-open .modal{
    padding-top: 0 !important;
  }      
}
/* pooja css changes 20082021 */

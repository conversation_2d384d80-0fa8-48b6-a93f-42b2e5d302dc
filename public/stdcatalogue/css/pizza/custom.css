.pagination > .active > a,
.pagination > .active > a:hover,
.pagination > .active > a:focus,
.pagination > .active > span,
.pagination > .active > span:hover,
.pagination > .active > span:focus {
  z-index: 2;
  color: #fff;
  background-color: #24292d;
  border-color: #24292d;
  cursor: default;
}
.back-to-top,
.back-to-top:hover {
  background: #dc2326 !important;
}
.plan-discount {
  padding-right: 5px;
}
.pattern-bg {
  /*background-image: url(./images/bg-5.png);*/
}

.address-info {
  padding: 10px 20px 15px !important;
  max-height: 160px !important;
  margin: 10px 0 !important;
  background: #fff;
}

.address-box {
  height: 380px !important;
  overflow-y: auto !important;
  padding: 10px 20px 0 !important;
}
.add-address-button {
  margin: 10px 0 20px !important;
  /*height: 157px;*/
  background: #fff;
}
.company-info-panel {
  height: 484px !important;
}
.pay-now-btn {
  background: #4caf50;
  margin-top: 20px;
}
.fooddialer,
.fooddialer:hover,
.fooddialer:active,
.fooddialer:focus {
  color: #232122 !important;
}
.partial-order-summary-box {
  margin-bottom: 35px;
}
.partial-order-summary-wrap-mob {
  display: none;
}
.back-to-top,
.back-to-top {
  bottom: 8px;
  right: 10px;
}
.back-to-top,
.back-to-top:hover {
  bottom: 8px;
  right: 10px;
}
.fs-item-offer {
  color: #6542ea;
  text-transform: capitalize;
  padding: 5px 0;
  line-height: 14px;
  font-weight: 600;
  font-size: 12px;
}
.meal-box > .clearfix {
  padding: 0 10px 10px;
  background-color: #fff;
  min-height: 132px;
}
.order-summary .badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 15px;
  font-weight: normal;
  color: #232122;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: right;
  background-color: transparent;
  border-radius: 0px;
  font-size: 13px;
  float: right;
  font-weight: normal;
}

/*missing css code*/
.choose-days {
  height: 30px;
  width: 30px;
  background: #bfbcbc;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  line-height: 2.5rem;
  margin-right: 7px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.choose-days.selected {
  background: #4caf50;
  color: #fff;
}

@media screen and (max-width: 767px) {
  /*.modal-body{
        padding: 0 !important;
    }*/
  .modal-open .modal {
    padding-top: 0 !important;
  }
  .partial-order-summary-wrap {
    display: none;
  }
  .modal-dialog {
    width: 90%;
  }
  h4.meal-type.wallet-title {
    text-align: left;
  }
}
@media screen and (max-width: 992px) {
  .partial-order-summary-wrap-mob {
    display: block;
  }
  .partial-order-summary-wrap {
    display: none;
  }
}
@media screen and (max-width: 1200px) {
  #shopping-cart .badge,
  #show_cart .badge {
    top: 0 !important;
    right: 0 !important;
  }
}

/**new css code**/

/*meal image*/

.addimg {
  height: 240px !important;
  object-fit: contain;
}

@media screen and (max-width: 767px) {
  .addimg {
    height: auto !important;
  }
}

/*lunch dinner center*/

@media screen and (max-width: 767px) {
  ul#myTabs {
    margin: 25px auto;
    margin-top: 10px;
  }
}

/*plan height*/

.meal-plan {
  padding: 15px 8px 5px 14px;
  border: 1px dashed #ddd;
  margin-bottom: 15px;
  height: 130px;
  display: flex;
  align-items: center;
}

@media screen and (max-width: 767px) {
  .meal-plan {
    padding: 10px 8px 0px 11px;
    height: fit-content !important;
  }
}

/*navbar options*/

@media screen and (max-width: 767px) {
  .navbar-inverse .navbar-nav > li > a {
    line-height: 1em;
    border-bottom: none;
    border-top: none;
    text-decoration: none;
  }
}

.nav > li {
  position: relative;
  display: block;
  text-align: center;
}

@media screen and (max-width: 1200px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #353535;
    text-decoration: none;
  }
}

@media screen and (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    text-align: center;
  }
}

.dropdown-menu {
  text-align: left;
}

/*food preference*/

@media screen and (max-width: 767px) {
  .modal-dialog {
    display: flex;
    width: 70%;
    position: relative;
  }
}

.modal-dialog {
  height: 100%;
  margin: auto !important;
  align-items: center;
  justify-content: center;
}

@media screen and (max-width: 767px) {
  .modal-content {
    background-color: #f5f3f4;
    background-image: none;
    border-radius: 0;
    width: 100%;
  }
}

/*footer*/

.shownew {
  display: flex !important;
  align-items: center;
}

.back-to-top,
.back-to-top:hover {
  bottom: 3px;
  right: 10px;
}

/*homepage*/

.navbar {
  z-index: 199;
}

@media screen and (max-width: 767px) {
  .container_full {
    margin-top: -8px;
  }
}

#main {
  margin-top: 75px;
}

button.order-button {
  border: 1px solid #ddd;
}

.about-us-text {
  background-color: #fff;
  text-align: left;
  padding: 20px;
  border: 1px solid #ddd;
  overflow-y: auto;
  max-height: 440px;
  height: 100%;
}

.about-us-img img {
  width: 100%;
  margin-bottom: 0;
  max-width: 780px;
  max-height: 520px;
  object-fit: contain;
  background-color: #fff !important;
}

@media screen and (max-width: 767px) {
  .about-us-img img {
    margin-bottom: 10px;
    background-color: #fff !important;
  }
  .about-us-image {
    padding: 0;
    margin: 0 15px;
  }
  .about-us-image {
    padding: 0;
    left: 0 !important;
  }
}

.about-us-image {
  padding: 0;
  left: 3%;
}

@media screen and (max-width: 1199px) {
  .about-us-text {
    max-height: 380px;
  }
}

@media screen and (max-width: 999px) {
  .about-us-text {
    max-height: 280px;
  }
}

@media screen and (max-width: 991px) {
  .about-us-text {
    max-height: 200px;
  }
}

@media screen and (max-width: 767px) {
  .about-us-text {
    max-height: 430px;
  }
  .about-info-remove {
    position: relative;
    right: 0;
    padding: 0;
    top: 0;
  }
}

.about-info-remove {
  position: relative;
  right: 3%;
  padding: 0;
  top: 0;
}

.about-us-row {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media screen and (max-width: 767px) {
  .about-us-row {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
}

@media screen and (max-width: 767px) {
  .about-info-remove {
    position: relative;
    right: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
    margin: 0 15px;
  }
}

.homepage-title b {
  text-align: center;
  font-size: 25px;
  color: #ffffff;
  text-transform: capitalize;
}

.owl-carousel {
  display: block !important;
}

.client-profile {
  width: 50px;
}

.wcs-main-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.wcs-box {
  width: 100%;
}

.our-services img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.img-works {
  width: 200px;
  height: 130px;
  object-fit: contain;
}

.client-profile {
  width: 50px;
  height: 50px;
  object-fit: contain;
  border-radius: 50%;
}

@media screen and (max-width: 767px) {
  .footer-2 {
    display: block;
  }
}

/*cart page*/

.summary-mob {
  padding: 0 !important;
  margin-bottom: 30px;
  display: none;
}

@media screen and (max-width: 992px) {
  .summary-mob {
    display: block;
  }
  /*.summary-web{
     display: none;
 }*/
}

.mb {
  margin-top: 10px !important;
}

@media screen and (max-width: 767px) {
  .btn-block {
    display: block;
    width: 100%;
  }
}

@media screen and (max-width: 767px) {
  .btn-check-new {
    width: 100% !important;
    position: relative;
  }
}

@media screen and (max-width: 767px) {
  .address-info {
    max-height: 250px !important;
    height: fit-content;
  }
}

@media screen and (max-width: 767px) {
  .add-extra-items {
    border-bottom: none !important;
  }
}

@media screen and (max-width: 1199px) {
  .add-extra-items p {
    margin: 15px 0 !important;
  }
}

@media screen and (max-width: 767px) {
  .add-extra-items .count {
    display: flex;
    align-items: center;
    margin-top: -10px;
  }
}

.add-extra-items {
  padding-bottom: 0 !important;
}

@media screen and (max-width: 400px) {
  .txt-right {
    padding-left: 0;
  }
}

.tab-content > .active > .row > .col-sm-12 {
  padding: 0 !important;
}

.fooicon {
  font-weight: 800 !important;
  font-size: 25px;
}

#myModal8 .changed-meal {
  height: fit-content !important;
}

@media screen and (max-width: 767px) {
  #myModal8 .changed-meal .control__indicator {
    top: 80px !important;
    left: -28px !important;
    z-index: 299 !important;
  }
}

.container-fluid > .center {
  width: fit-content !important;
}

/*buy more meal page*/

@media screen and (max-width: 767px) {
  .btn-check {
    width: 100% !important;
    display: block;
    position: relative;
    left: 0;
    margin-left: 0;
  }
}

@media screen and (max-width: 767px) {
  .button-row {
    display: flex !important;
    flex-direction: column;
  }
}

/*wallet history*/

@media screen and (max-width: 450px) {
  h4.meal-type span.cr-dr {
    width: 25% !important;
    padding: 0 8px 0 0;
  }
}

/*my account*/

.dropdown-menu li:first-child {
  display: none;
}

/*mobile view menu changes starts*/

.mobile-view {
  display: none;
}

@media screen and (max-width: 500px) {
  .meal-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
  }

  .meal-box > .clearfix {
    width: 75%;
  }

  .pull-right {
    margin-top: -10px;
    background: white;
    width: 85%;
    box-shadow: 0 1px 21px 1px rgb(0 0 0 / 40%);
    border-radius: 5px;
    z-index: 299 !important;
  }

  .checkout {
    width: auto !important;
  }

  .add-btn {
    padding: 3px;
    border-radius: 5px;
  }

  .checkout .add-btn {
    padding: 6px 10px !important;
  }

  .meal-img {
    width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    padding: 5px 0;
  }

  /*.meal-box .meal-bg{
        width: 150px;
    }*/

  .addimg {
    border-radius: 5px;
    border: 1px solid #ddd;
    background: #fff !important;
  }

  .mobile-view {
    display: flex;
  }

  .web-view {
    display: none;
  }

  .overlay {
    top: 26%;
    width: 100px;
    left: unset;
    transform: unset;
  }

  .img-text span {
    font-size: 15px;
  }

  .meal-bg {
    border-radius: 5px;
  }
}

/*mobile view menu changes ends*/

/*menu selection changes starts*/

#myTabs.nav-tabs > li:first-child > a:first-child {
  border-left: 0 !important;
}

#myTabs.nav-tabs > li > a {
  border-left: 2px solid #231f20;
}

ul#myTabs li a.active {
  border-left: 0 !important;
  border-right: 0 !important;
}

ul#myTabs li a.active:after {
  height: 0 !important;
}

/*menu selection changes ends*/

/*upcoming meals description alignment*/

.week-description,
.product-description p {
  text-align: justify;
}

/*upcoming meals description alignment ends*/

/*add to wallet response page*/

.cart-content {
  color: #4caf50;
  font-size: 22px;
  margin-bottom: 20px;
}

/*add to wallet response page ends*/

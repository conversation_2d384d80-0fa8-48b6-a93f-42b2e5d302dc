@charset "UTF-8";
/*! normalize.css v3.0.2 | MIT License | git.io/normalize */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%; }

body {
  margin: 0; }

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block; }

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline; }

audio:not([controls]) {
  display: none;
  height: 0; }

[hidden],
template {
  display: none; }

a {
  background-color: transparent; }

a:active,
a:hover {
  outline: 0; }

abbr[title] {
  border-bottom: 1px dotted; }

b,
strong {
  font-weight: bold; }

dfn {
  font-style: italic; }

h1 {
  font-size: 2em;
  margin: 0.67em 0; }

mark {
  background: #ff0;
  color: #000; }

small {
  font-size: 80%; }

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

img {
  border: 0; }

svg:not(:root) {
  overflow: hidden; }

figure {
  margin: 1em 40px; }

hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0; }

pre {
  overflow: auto; }

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em; }

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0; }

button {
  overflow: visible; }

button,
select {
  text-transform: none; }

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer; }

button[disabled],
html input[disabled] {
  cursor: default; }

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

input {
  line-height: normal; }

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0; }

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto; }

input[type="search"] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box; }

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em; }

legend {
  border: 0;
  padding: 0; }

textarea {
  overflow: auto; }

optgroup {
  font-weight: bold; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

td,
th {
  padding: 0; }

/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important;
    box-shadow: none !important;
    text-shadow: none !important; }

  a,
  a:visited {
    text-decoration: underline; }

  a[href]:after {
    content: " (" attr(href) ")"; }

  abbr[title]:after {
    content: " (" attr(title) ")"; }

  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: ""; }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid; }

  thead {
    display: table-header-group; }

  tr,
  img {
    page-break-inside: avoid; }

  img {
    max-width: 100% !important; }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3; }

  h2,
  h3 {
    page-break-after: avoid; }

  select {
    background: #fff !important; }

  .navbar {
    display: none; }

  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important; }

  .label {
    border: 1px solid #000; }

  .table {
    border-collapse: collapse !important; }
    .table td,
    .table th {
      background-color: #fff !important; }

  .table-bordered th,
  .table-bordered td {
    border: 1px solid #ddd !important; } }
@font-face {
  font-family: 'Glyphicons Halflings';
  src: url("../fonts/bootstrap/glyphicons-halflings-regular.eot");
  src: url("../fonts/bootstrap/glyphicons-halflings-regular.eot?#iefix") format("embedded-opentype"), url("../fonts/bootstrap/glyphicons-halflings-regular.woff2") format("woff2"), url("../fonts/bootstrap/glyphicons-halflings-regular.woff") format("woff"), url("../fonts/bootstrap/glyphicons-halflings-regular.ttf") format("truetype"), url("../fonts/bootstrap/glyphicons-halflings-regular.svg#glyphicons_halflingsregular") format("svg"); }
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.glyphicon-asterisk:before {
  content: "\2a"; }

.glyphicon-plus:before {
  content: "\2b"; }

.glyphicon-euro:before,
.glyphicon-eur:before {
  content: \20ac; }

.glyphicon-minus:before {
  content: \2212; }

.glyphicon-cloud:before {
  content: \2601; }

.glyphicon-envelope:before {
  content: \2709; }

.glyphicon-pencil:before {
  content: \270f; }

.glyphicon-glass:before {
  content: \e001; }

.glyphicon-music:before {
  content: \e002; }

.glyphicon-search:before {
  content: \e003; }

.glyphicon-heart:before {
  content: \e005; }

.glyphicon-star:before {
  content: \e006; }

.glyphicon-star-empty:before {
  content: \e007; }

.glyphicon-user:before {
  content: \e008; }

.glyphicon-film:before {
  content: \e009; }

.glyphicon-th-large:before {
  content: \e010; }

.glyphicon-th:before {
  content: \e011; }

.glyphicon-th-list:before {
  content: \e012; }

.glyphicon-ok:before {
  content: \e013; }

.glyphicon-remove:before {
  content: \e014; }

.glyphicon-zoom-in:before {
  content: \e015; }

.glyphicon-zoom-out:before {
  content: \e016; }

.glyphicon-off:before {
  content: \e017; }

.glyphicon-signal:before {
  content: \e018; }

.glyphicon-cog:before {
  content: \e019; }

.glyphicon-trash:before {
  content: \e020; }

.glyphicon-home:before {
  content: \e021; }

.glyphicon-file:before {
  content: \e022; }

.glyphicon-time:before {
  content: \e023; }

.glyphicon-road:before {
  content: \e024; }

.glyphicon-download-alt:before {
  content: \e025; }

.glyphicon-download:before {
  content: \e026; }

.glyphicon-upload:before {
  content: \e027; }

.glyphicon-inbox:before {
  content: \e028; }

.glyphicon-play-circle:before {
  content: \e029; }

.glyphicon-repeat:before {
  content: \e030; }

.glyphicon-refresh:before {
  content: \e031; }

.glyphicon-list-alt:before {
  content: \e032; }

.glyphicon-lock:before {
  content: \e033; }

.glyphicon-flag:before {
  content: \e034; }

.glyphicon-headphones:before {
  content: \e035; }

.glyphicon-volume-off:before {
  content: \e036; }

.glyphicon-volume-down:before {
  content: \e037; }

.glyphicon-volume-up:before {
  content: \e038; }

.glyphicon-qrcode:before {
  content: \e039; }

.glyphicon-barcode:before {
  content: \e040; }

.glyphicon-tag:before {
  content: \e041; }

.glyphicon-tags:before {
  content: \e042; }

.glyphicon-book:before {
  content: \e043; }

.glyphicon-bookmark:before {
  content: \e044; }

.glyphicon-print:before {
  content: \e045; }

.glyphicon-camera:before {
  content: \e046; }

.glyphicon-font:before {
  content: \e047; }

.glyphicon-bold:before {
  content: \e048; }

.glyphicon-italic:before {
  content: \e049; }

.glyphicon-text-height:before {
  content: \e050; }

.glyphicon-text-width:before {
  content: \e051; }

.glyphicon-align-left:before {
  content: \e052; }

.glyphicon-align-center:before {
  content: \e053; }

.glyphicon-align-right:before {
  content: \e054; }

.glyphicon-align-justify:before {
  content: \e055; }

.glyphicon-list:before {
  content: \e056; }

.glyphicon-indent-left:before {
  content: \e057; }

.glyphicon-indent-right:before {
  content: \e058; }

.glyphicon-facetime-video:before {
  content: \e059; }

.glyphicon-picture:before {
  content: \e060; }

.glyphicon-map-marker:before {
  content: \e062; }

.glyphicon-adjust:before {
  content: \e063; }

.glyphicon-tint:before {
  content: \e064; }

.glyphicon-edit:before {
  content: \e065; }

.glyphicon-share:before {
  content: \e066; }

.glyphicon-check:before {
  content: \e067; }

.glyphicon-move:before {
  content: \e068; }

.glyphicon-step-backward:before {
  content: \e069; }

.glyphicon-fast-backward:before {
  content: \e070; }

.glyphicon-backward:before {
  content: \e071; }

.glyphicon-play:before {
  content: \e072; }

.glyphicon-pause:before {
  content: \e073; }

.glyphicon-stop:before {
  content: \e074; }

.glyphicon-forward:before {
  content: \e075; }

.glyphicon-fast-forward:before {
  content: \e076; }

.glyphicon-step-forward:before {
  content: \e077; }

.glyphicon-eject:before {
  content: \e078; }

.glyphicon-chevron-left:before {
  content: \e079; }

.glyphicon-chevron-right:before {
  content: \e080; }

.glyphicon-plus-sign:before {
  content: \e081; }

.glyphicon-minus-sign:before {
  content: \e082; }

.glyphicon-remove-sign:before {
  content: \e083; }

.glyphicon-ok-sign:before {
  content: \e084; }

.glyphicon-question-sign:before {
  content: \e085; }

.glyphicon-info-sign:before {
  content: \e086; }

.glyphicon-screenshot:before {
  content: \e087; }

.glyphicon-remove-circle:before {
  content: \e088; }

.glyphicon-ok-circle:before {
  content: \e089; }

.glyphicon-ban-circle:before {
  content: \e090; }

.glyphicon-arrow-left:before {
  content: \e091; }

.glyphicon-arrow-right:before {
  content: \e092; }

.glyphicon-arrow-up:before {
  content: \e093; }

.glyphicon-arrow-down:before {
  content: \e094; }

.glyphicon-share-alt:before {
  content: \e095; }

.glyphicon-resize-full:before {
  content: \e096; }

.glyphicon-resize-small:before {
  content: \e097; }

.glyphicon-exclamation-sign:before {
  content: \e101; }

.glyphicon-gift:before {
  content: \e102; }

.glyphicon-leaf:before {
  content: \e103; }

.glyphicon-fire:before {
  content: \e104; }

.glyphicon-eye-open:before {
  content: \e105; }

.glyphicon-eye-close:before {
  content: \e106; }

.glyphicon-warning-sign:before {
  content: \e107; }

.glyphicon-plane:before {
  content: \e108; }

.glyphicon-calendar:before {
  content: \e109; }

.glyphicon-random:before {
  content: \e110; }

.glyphicon-comment:before {
  content: \e111; }

.glyphicon-magnet:before {
  content: \e112; }

.glyphicon-chevron-up:before {
  content: \e113; }

.glyphicon-chevron-down:before {
  content: \e114; }

.glyphicon-retweet:before {
  content: \e115; }

.glyphicon-shopping-cart:before {
  content: \e116; }

.glyphicon-folder-close:before {
  content: \e117; }

.glyphicon-folder-open:before {
  content: \e118; }

.glyphicon-resize-vertical:before {
  content: \e119; }

.glyphicon-resize-horizontal:before {
  content: \e120; }

.glyphicon-hdd:before {
  content: \e121; }

.glyphicon-bullhorn:before {
  content: \e122; }

.glyphicon-bell:before {
  content: \e123; }

.glyphicon-certificate:before {
  content: \e124; }

.glyphicon-thumbs-up:before {
  content: \e125; }

.glyphicon-thumbs-down:before {
  content: \e126; }

.glyphicon-hand-right:before {
  content: \e127; }

.glyphicon-hand-left:before {
  content: \e128; }

.glyphicon-hand-up:before {
  content: \e129; }

.glyphicon-hand-down:before {
  content: \e130; }

.glyphicon-circle-arrow-right:before {
  content: \e131; }

.glyphicon-circle-arrow-left:before {
  content: \e132; }

.glyphicon-circle-arrow-up:before {
  content: \e133; }

.glyphicon-circle-arrow-down:before {
  content: \e134; }

.glyphicon-globe:before {
  content: \e135; }

.glyphicon-wrench:before {
  content: \e136; }

.glyphicon-tasks:before {
  content: \e137; }

.glyphicon-filter:before {
  content: \e138; }

.glyphicon-briefcase:before {
  content: \e139; }

.glyphicon-fullscreen:before {
  content: \e140; }

.glyphicon-dashboard:before {
  content: \e141; }

.glyphicon-paperclip:before {
  content: \e142; }

.glyphicon-heart-empty:before {
  content: \e143; }

.glyphicon-link:before {
  content: \e144; }

.glyphicon-phone:before {
  content: \e145; }

.glyphicon-pushpin:before {
  content: \e146; }

.glyphicon-usd:before {
  content: \e148; }

.glyphicon-gbp:before {
  content: \e149; }

.glyphicon-sort:before {
  content: \e150; }

.glyphicon-sort-by-alphabet:before {
  content: \e151; }

.glyphicon-sort-by-alphabet-alt:before {
  content: \e152; }

.glyphicon-sort-by-order:before {
  content: \e153; }

.glyphicon-sort-by-order-alt:before {
  content: \e154; }

.glyphicon-sort-by-attributes:before {
  content: \e155; }

.glyphicon-sort-by-attributes-alt:before {
  content: \e156; }

.glyphicon-unchecked:before {
  content: \e157; }

.glyphicon-expand:before {
  content: \e158; }

.glyphicon-collapse-down:before {
  content: \e159; }

.glyphicon-collapse-up:before {
  content: \e160; }

.glyphicon-log-in:before {
  content: \e161; }

.glyphicon-flash:before {
  content: \e162; }

.glyphicon-log-out:before {
  content: \e163; }

.glyphicon-new-window:before {
  content: \e164; }

.glyphicon-record:before {
  content: \e165; }

.glyphicon-save:before {
  content: \e166; }

.glyphicon-open:before {
  content: \e167; }

.glyphicon-saved:before {
  content: \e168; }

.glyphicon-import:before {
  content: \e169; }

.glyphicon-export:before {
  content: \e170; }

.glyphicon-send:before {
  content: \e171; }

.glyphicon-floppy-disk:before {
  content: \e172; }

.glyphicon-floppy-saved:before {
  content: \e173; }

.glyphicon-floppy-remove:before {
  content: \e174; }

.glyphicon-floppy-save:before {
  content: \e175; }

.glyphicon-floppy-open:before {
  content: \e176; }

.glyphicon-credit-card:before {
  content: \e177; }

.glyphicon-transfer:before {
  content: \e178; }

.glyphicon-cutlery:before {
  content: \e179; }

.glyphicon-header:before {
  content: \e180; }

.glyphicon-compressed:before {
  content: \e181; }

.glyphicon-earphone:before {
  content: \e182; }

.glyphicon-phone-alt:before {
  content: \e183; }

.glyphicon-tower:before {
  content: \e184; }

.glyphicon-stats:before {
  content: \e185; }

.glyphicon-sd-video:before {
  content: \e186; }

.glyphicon-hd-video:before {
  content: \e187; }

.glyphicon-subtitles:before {
  content: \e188; }

.glyphicon-sound-stereo:before {
  content: \e189; }

.glyphicon-sound-dolby:before {
  content: \e190; }

.glyphicon-sound-5-1:before {
  content: \e191; }

.glyphicon-sound-6-1:before {
  content: \e192; }

.glyphicon-sound-7-1:before {
  content: \e193; }

.glyphicon-copyright-mark:before {
  content: \e194; }

.glyphicon-registration-mark:before {
  content: \e195; }

.glyphicon-cloud-download:before {
  content: \e197; }

.glyphicon-cloud-upload:before {
  content: \e198; }

.glyphicon-tree-conifer:before {
  content: \e199; }

.glyphicon-tree-deciduous:before {
  content: \e200; }

.glyphicon-cd:before {
  content: \e201; }

.glyphicon-save-file:before {
  content: \e202; }

.glyphicon-open-file:before {
  content: \e203; }

.glyphicon-level-up:before {
  content: \e204; }

.glyphicon-copy:before {
  content: \e205; }

.glyphicon-paste:before {
  content: \e206; }

.glyphicon-alert:before {
  content: \e209; }

.glyphicon-equalizer:before {
  content: \e210; }

.glyphicon-king:before {
  content: \e211; }

.glyphicon-queen:before {
  content: \e212; }

.glyphicon-pawn:before {
  content: \e213; }

.glyphicon-bishop:before {
  content: \e214; }

.glyphicon-knight:before {
  content: \e215; }

.glyphicon-baby-formula:before {
  content: \e216; }

.glyphicon-tent:before {
  content: \26fa; }

.glyphicon-blackboard:before {
  content: \e218; }

.glyphicon-bed:before {
  content: \e219; }

.glyphicon-apple:before {
  content: \f8ff; }

.glyphicon-erase:before {
  content: \e221; }

.glyphicon-hourglass:before {
  content: \231b; }

.glyphicon-lamp:before {
  content: \e223; }

.glyphicon-duplicate:before {
  content: \e224; }

.glyphicon-piggy-bank:before {
  content: \e225; }

.glyphicon-scissors:before {
  content: \e226; }

.glyphicon-bitcoin:before {
  content: \e227; }

.glyphicon-yen:before {
  content: \00a5; }

.glyphicon-ruble:before {
  content: \20bd; }

.glyphicon-scale:before {
  content: \e230; }

.glyphicon-ice-lolly:before {
  content: \e231; }

.glyphicon-ice-lolly-tasted:before {
  content: \e232; }

.glyphicon-education:before {
  content: \e233; }

.glyphicon-option-horizontal:before {
  content: \e234; }

.glyphicon-option-vertical:before {
  content: \e235; }

.glyphicon-menu-hamburger:before {
  content: \e236; }

.glyphicon-modal-window:before {
  content: \e237; }

.glyphicon-oil:before {
  content: \e238; }

.glyphicon-grain:before {
  content: \e239; }

.glyphicon-sunglasses:before {
  content: \e240; }

.glyphicon-text-size:before {
  content: \e241; }

.glyphicon-text-color:before {
  content: \e242; }

.glyphicon-text-background:before {
  content: \e243; }

.glyphicon-object-align-top:before {
  content: \e244; }

.glyphicon-object-align-bottom:before {
  content: \e245; }

.glyphicon-object-align-horizontal:before {
  content: \e246; }

.glyphicon-object-align-left:before {
  content: \e247; }

.glyphicon-object-align-vertical:before {
  content: \e248; }

.glyphicon-object-align-right:before {
  content: \e249; }

.glyphicon-triangle-right:before {
  content: \e250; }

.glyphicon-triangle-left:before {
  content: \e251; }

.glyphicon-triangle-bottom:before {
  content: \e252; }

.glyphicon-triangle-top:before {
  content: \e253; }

.glyphicon-console:before {
  content: \e254; }

.glyphicon-superscript:before {
  content: \e255; }

.glyphicon-subscript:before {
  content: \e256; }

.glyphicon-menu-left:before {
  content: \e257; }

.glyphicon-menu-right:before {
  content: \e258; }

.glyphicon-menu-down:before {
  content: \e259; }

.glyphicon-menu-up:before {
  content: \e260; }

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

html {
  font-size: 10px;
  -webkit-tap-highlight-color: transparent; }

body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857;
  color: #656d78;
  background-color: #fff; }

input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

a {
  color: #337ab7;
  text-decoration: none; }
  a:hover, a:focus {
    color: #23527c;
    text-decoration: underline; }
  a:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px; }

figure {
  margin: 0; }

img {
  vertical-align: middle; }

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto; }

.img-rounded {
  border-radius: 6px; }

.img-thumbnail {
  padding: 4px;
  line-height: 1.42857;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto; }

.img-circle {
  border-radius: 50%; }

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eeeeee; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto; }

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit; }
  h1 small,
  h1 .small, h2 small,
  h2 .small, h3 small,
  h3 .small, h4 small,
  h4 .small, h5 small,
  h5 .small, h6 small,
  h6 .small,
  .h1 small,
  .h1 .small, .h2 small,
  .h2 .small, .h3 small,
  .h3 .small, .h4 small,
  .h4 .small, .h5 small,
  .h5 .small, .h6 small,
  .h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #777777; }

h1, .h1,
h2, .h2,
h3, .h3 {
  margin-top: 20px;
  margin-bottom: 10px; }
  h1 small,
  h1 .small, .h1 small,
  .h1 .small,
  h2 small,
  h2 .small, .h2 small,
  .h2 .small,
  h3 small,
  h3 .small, .h3 small,
  .h3 .small {
    font-size: 65%; }

h4, .h4,
h5, .h5,
h6, .h6 {
  margin-top: 10px;
  margin-bottom: 10px; }
  h4 small,
  h4 .small, .h4 small,
  .h4 .small,
  h5 small,
  h5 .small, .h5 small,
  .h5 .small,
  h6 small,
  h6 .small, .h6 small,
  .h6 .small {
    font-size: 75%; }

h1, .h1 {
  font-size: 36px; }

h2, .h2 {
  font-size: 30px; }

h3, .h3 {
  font-size: 24px; }

h4, .h4 {
  font-size: 18px; }

h5, .h5 {
  font-size: 14px; }

h6, .h6 {
  font-size: 12px; }

p {
  margin: 0 0 10px; }

.lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4; }
  @media (min-width: 768px) {
    .lead {
      font-size: 21px; } }

small,
.small {
  font-size: 85%; }

mark,
.mark {
  background-color: #fcf8e3;
  padding: .2em; }

.text-left {
  text-align: left; }

.text-right {
  text-align: right; }

.text-center {
  text-align: center; }

.text-justify {
  text-align: justify; }

.text-nowrap {
  white-space: nowrap; }

.text-lowercase {
  text-transform: lowercase; }

.text-uppercase {
  text-transform: uppercase; }

.text-capitalize {
  text-transform: capitalize; }

.text-muted {
  color: #777777; }

.text-primary {
  color: #337ab7; }

a.text-primary:hover {
  color: #286090; }

.text-success {
  color: #3c763d; }

a.text-success:hover {
  color: #2b542c; }

.text-info {
  color: #31708f; }

a.text-info:hover {
  color: #245269; }

.text-warning {
  color: #8a6d3b; }

a.text-warning:hover {
  color: #66512c; }

.text-danger {
  color: #a94442; }

a.text-danger:hover {
  color: #843534; }

.bg-primary {
  color: #fff; }

.bg-primary {
  background-color: #337ab7; }

a.bg-primary:hover {
  background-color: #286090; }

.bg-success {
  background-color: #dff0d8; }

a.bg-success:hover {
  background-color: #c1e2b3; }

.bg-info {
  background-color: #d9edf7; }

a.bg-info:hover {
  background-color: #afd9ee; }

.bg-warning {
  background-color: #fcf8e3; }

a.bg-warning:hover {
  background-color: #f7ecb5; }

.bg-danger {
  background-color: #f2dede; }

a.bg-danger:hover {
  background-color: #e4b9b9; }

.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eeeeee; }

ul,
ol {
  margin-top: 0;
  margin-bottom: 10px; }
  ul ul,
  ul ol,
  ol ul,
  ol ol {
    margin-bottom: 0; }

.list-unstyled {
  padding-left: 0;
  list-style: none; }

.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px; }
  .list-inline > li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px; }

dl {
  margin-top: 0;
  margin-bottom: 20px; }

dt,
dd {
  line-height: 1.42857; }

dt {
  font-weight: bold; }

dd {
  margin-left: 0; }

.dl-horizontal dd:before, .dl-horizontal dd:after {
  content: " ";
  display: table; }
.dl-horizontal dd:after {
  clear: both; }
@media (min-width: 1200px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
  .dl-horizontal dd {
    margin-left: 180px; } }

abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #777777; }

.initialism {
  font-size: 90%;
  text-transform: uppercase; }

blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  font-size: 17.5px;
  border-left: 5px solid #eeeeee; }
  blockquote p:last-child,
  blockquote ul:last-child,
  blockquote ol:last-child {
    margin-bottom: 0; }
  blockquote footer,
  blockquote small,
  blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.42857;
    color: #777777; }
    blockquote footer:before,
    blockquote small:before,
    blockquote .small:before {
      content: '\2014 \00A0'; }

.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
  text-align: right; }
  .blockquote-reverse footer:before,
  .blockquote-reverse small:before,
  .blockquote-reverse .small:before,
  blockquote.pull-right footer:before,
  blockquote.pull-right small:before,
  blockquote.pull-right .small:before {
    content: ''; }
  .blockquote-reverse footer:after,
  .blockquote-reverse small:after,
  .blockquote-reverse .small:after,
  blockquote.pull-right footer:after,
  blockquote.pull-right small:after,
  blockquote.pull-right .small:after {
    content: '\00A0 \2014'; }

address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.42857; }

code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace; }

code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px; }

kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #fff;
  background-color: #333;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25); }
  kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: bold;
    box-shadow: none; }

pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.42857;
  word-break: break-all;
  word-wrap: break-word;
  color: #333333;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px; }
  pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0; }

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll; }

.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px; }
  .container:before, .container:after {
    content: " ";
    display: table; }
  .container:after {
    clear: both; }
  @media (min-width: 768px) {
    .container {
      width: 750px; } }
  @media (min-width: 992px) {
    .container {
      width: 970px; } }
  @media (min-width: 1200px) {
    .container {
      width: 1170px; } }

.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px; }
  .container-fluid:before, .container-fluid:after {
    content: " ";
    display: table; }
  .container-fluid:after {
    clear: both; }

.row {
  margin-left: -15px;
  margin-right: -15px; }
  .row:before, .row:after {
    content: " ";
    display: table; }
  .row:after {
    clear: both; }

.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px; }

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
  float: left; }

.col-xs-1 {
  width: 8.33333%; }

.col-xs-2 {
  width: 16.66667%; }

.col-xs-3 {
  width: 25%; }

.col-xs-4 {
  width: 33.33333%; }

.col-xs-5 {
  width: 41.66667%; }

.col-xs-6 {
  width: 50%; }

.col-xs-7 {
  width: 58.33333%; }

.col-xs-8 {
  width: 66.66667%; }

.col-xs-9 {
  width: 75%; }

.col-xs-10 {
  width: 83.33333%; }

.col-xs-11 {
  width: 91.66667%; }

.col-xs-12 {
  width: 100%; }

.col-xs-pull-0 {
  right: auto; }

.col-xs-pull-1 {
  right: 8.33333%; }

.col-xs-pull-2 {
  right: 16.66667%; }

.col-xs-pull-3 {
  right: 25%; }

.col-xs-pull-4 {
  right: 33.33333%; }

.col-xs-pull-5 {
  right: 41.66667%; }

.col-xs-pull-6 {
  right: 50%; }

.col-xs-pull-7 {
  right: 58.33333%; }

.col-xs-pull-8 {
  right: 66.66667%; }

.col-xs-pull-9 {
  right: 75%; }

.col-xs-pull-10 {
  right: 83.33333%; }

.col-xs-pull-11 {
  right: 91.66667%; }

.col-xs-pull-12 {
  right: 100%; }

.col-xs-push-0 {
  left: auto; }

.col-xs-push-1 {
  left: 8.33333%; }

.col-xs-push-2 {
  left: 16.66667%; }

.col-xs-push-3 {
  left: 25%; }

.col-xs-push-4 {
  left: 33.33333%; }

.col-xs-push-5 {
  left: 41.66667%; }

.col-xs-push-6 {
  left: 50%; }

.col-xs-push-7 {
  left: 58.33333%; }

.col-xs-push-8 {
  left: 66.66667%; }

.col-xs-push-9 {
  left: 75%; }

.col-xs-push-10 {
  left: 83.33333%; }

.col-xs-push-11 {
  left: 91.66667%; }

.col-xs-push-12 {
  left: 100%; }

.col-xs-offset-0 {
  margin-left: 0%; }

.col-xs-offset-1 {
  margin-left: 8.33333%; }

.col-xs-offset-2 {
  margin-left: 16.66667%; }

.col-xs-offset-3 {
  margin-left: 25%; }

.col-xs-offset-4 {
  margin-left: 33.33333%; }

.col-xs-offset-5 {
  margin-left: 41.66667%; }

.col-xs-offset-6 {
  margin-left: 50%; }

.col-xs-offset-7 {
  margin-left: 58.33333%; }

.col-xs-offset-8 {
  margin-left: 66.66667%; }

.col-xs-offset-9 {
  margin-left: 75%; }

.col-xs-offset-10 {
  margin-left: 83.33333%; }

.col-xs-offset-11 {
  margin-left: 91.66667%; }

.col-xs-offset-12 {
  margin-left: 100%; }

@media (min-width: 768px) {
  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
    float: left; }

  .col-sm-1 {
    width: 8.33333%; }

  .col-sm-2 {
    width: 16.66667%; }

  .col-sm-3 {
    width: 25%; }

  .col-sm-4 {
    width: 33.33333%; }

  .col-sm-5 {
    width: 41.66667%; }

  .col-sm-6 {
    width: 50%; }

  .col-sm-7 {
    width: 58.33333%; }

  .col-sm-8 {
    width: 66.66667%; }

  .col-sm-9 {
    width: 75%; }

  .col-sm-10 {
    width: 83.33333%; }

  .col-sm-11 {
    width: 91.66667%; }

  .col-sm-12 {
    width: 100%; }

  .col-sm-pull-0 {
    right: auto; }

  .col-sm-pull-1 {
    right: 8.33333%; }

  .col-sm-pull-2 {
    right: 16.66667%; }

  .col-sm-pull-3 {
    right: 25%; }

  .col-sm-pull-4 {
    right: 33.33333%; }

  .col-sm-pull-5 {
    right: 41.66667%; }

  .col-sm-pull-6 {
    right: 50%; }

  .col-sm-pull-7 {
    right: 58.33333%; }

  .col-sm-pull-8 {
    right: 66.66667%; }

  .col-sm-pull-9 {
    right: 75%; }

  .col-sm-pull-10 {
    right: 83.33333%; }

  .col-sm-pull-11 {
    right: 91.66667%; }

  .col-sm-pull-12 {
    right: 100%; }

  .col-sm-push-0 {
    left: auto; }

  .col-sm-push-1 {
    left: 8.33333%; }

  .col-sm-push-2 {
    left: 16.66667%; }

  .col-sm-push-3 {
    left: 25%; }

  .col-sm-push-4 {
    left: 33.33333%; }

  .col-sm-push-5 {
    left: 41.66667%; }

  .col-sm-push-6 {
    left: 50%; }

  .col-sm-push-7 {
    left: 58.33333%; }

  .col-sm-push-8 {
    left: 66.66667%; }

  .col-sm-push-9 {
    left: 75%; }

  .col-sm-push-10 {
    left: 83.33333%; }

  .col-sm-push-11 {
    left: 91.66667%; }

  .col-sm-push-12 {
    left: 100%; }

  .col-sm-offset-0 {
    margin-left: 0%; }

  .col-sm-offset-1 {
    margin-left: 8.33333%; }

  .col-sm-offset-2 {
    margin-left: 16.66667%; }

  .col-sm-offset-3 {
    margin-left: 25%; }

  .col-sm-offset-4 {
    margin-left: 33.33333%; }

  .col-sm-offset-5 {
    margin-left: 41.66667%; }

  .col-sm-offset-6 {
    margin-left: 50%; }

  .col-sm-offset-7 {
    margin-left: 58.33333%; }

  .col-sm-offset-8 {
    margin-left: 66.66667%; }

  .col-sm-offset-9 {
    margin-left: 75%; }

  .col-sm-offset-10 {
    margin-left: 83.33333%; }

  .col-sm-offset-11 {
    margin-left: 91.66667%; }

  .col-sm-offset-12 {
    margin-left: 100%; } }
@media (min-width: 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left; }

  .col-md-1 {
    width: 8.33333%; }

  .col-md-2 {
    width: 16.66667%; }

  .col-md-3 {
    width: 25%; }

  .col-md-4 {
    width: 33.33333%; }

  .col-md-5 {
    width: 41.66667%; }

  .col-md-6 {
    width: 50%; }

  .col-md-7 {
    width: 58.33333%; }

  .col-md-8 {
    width: 66.66667%; }

  .col-md-9 {
    width: 75%; }

  .col-md-10 {
    width: 83.33333%; }

  .col-md-11 {
    width: 91.66667%; }

  .col-md-12 {
    width: 100%; }

  .col-md-pull-0 {
    right: auto; }

  .col-md-pull-1 {
    right: 8.33333%; }

  .col-md-pull-2 {
    right: 16.66667%; }

  .col-md-pull-3 {
    right: 25%; }

  .col-md-pull-4 {
    right: 33.33333%; }

  .col-md-pull-5 {
    right: 41.66667%; }

  .col-md-pull-6 {
    right: 50%; }

  .col-md-pull-7 {
    right: 58.33333%; }

  .col-md-pull-8 {
    right: 66.66667%; }

  .col-md-pull-9 {
    right: 75%; }

  .col-md-pull-10 {
    right: 83.33333%; }

  .col-md-pull-11 {
    right: 91.66667%; }

  .col-md-pull-12 {
    right: 100%; }

  .col-md-push-0 {
    left: auto; }

  .col-md-push-1 {
    left: 8.33333%; }

  .col-md-push-2 {
    left: 16.66667%; }

  .col-md-push-3 {
    left: 25%; }

  .col-md-push-4 {
    left: 33.33333%; }

  .col-md-push-5 {
    left: 41.66667%; }

  .col-md-push-6 {
    left: 50%; }

  .col-md-push-7 {
    left: 58.33333%; }

  .col-md-push-8 {
    left: 66.66667%; }

  .col-md-push-9 {
    left: 75%; }

  .col-md-push-10 {
    left: 83.33333%; }

  .col-md-push-11 {
    left: 91.66667%; }

  .col-md-push-12 {
    left: 100%; }

  .col-md-offset-0 {
    margin-left: 0%; }

  .col-md-offset-1 {
    margin-left: 8.33333%; }

  .col-md-offset-2 {
    margin-left: 16.66667%; }

  .col-md-offset-3 {
    margin-left: 25%; }

  .col-md-offset-4 {
    margin-left: 33.33333%; }

  .col-md-offset-5 {
    margin-left: 41.66667%; }

  .col-md-offset-6 {
    margin-left: 50%; }

  .col-md-offset-7 {
    margin-left: 58.33333%; }

  .col-md-offset-8 {
    margin-left: 66.66667%; }

  .col-md-offset-9 {
    margin-left: 75%; }

  .col-md-offset-10 {
    margin-left: 83.33333%; }

  .col-md-offset-11 {
    margin-left: 91.66667%; }

  .col-md-offset-12 {
    margin-left: 100%; } }
@media (min-width: 1200px) {
  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    float: left; }

  .col-lg-1 {
    width: 8.33333%; }

  .col-lg-2 {
    width: 16.66667%; }

  .col-lg-3 {
    width: 25%; }

  .col-lg-4 {
    width: 33.33333%; }

  .col-lg-5 {
    width: 41.66667%; }

  .col-lg-6 {
    width: 50%; }

  .col-lg-7 {
    width: 58.33333%; }

  .col-lg-8 {
    width: 66.66667%; }

  .col-lg-9 {
    width: 75%; }

  .col-lg-10 {
    width: 83.33333%; }

  .col-lg-11 {
    width: 91.66667%; }

  .col-lg-12 {
    width: 100%; }

  .col-lg-pull-0 {
    right: auto; }

  .col-lg-pull-1 {
    right: 8.33333%; }

  .col-lg-pull-2 {
    right: 16.66667%; }

  .col-lg-pull-3 {
    right: 25%; }

  .col-lg-pull-4 {
    right: 33.33333%; }

  .col-lg-pull-5 {
    right: 41.66667%; }

  .col-lg-pull-6 {
    right: 50%; }

  .col-lg-pull-7 {
    right: 58.33333%; }

  .col-lg-pull-8 {
    right: 66.66667%; }

  .col-lg-pull-9 {
    right: 75%; }

  .col-lg-pull-10 {
    right: 83.33333%; }

  .col-lg-pull-11 {
    right: 91.66667%; }

  .col-lg-pull-12 {
    right: 100%; }

  .col-lg-push-0 {
    left: auto; }

  .col-lg-push-1 {
    left: 8.33333%; }

  .col-lg-push-2 {
    left: 16.66667%; }

  .col-lg-push-3 {
    left: 25%; }

  .col-lg-push-4 {
    left: 33.33333%; }

  .col-lg-push-5 {
    left: 41.66667%; }

  .col-lg-push-6 {
    left: 50%; }

  .col-lg-push-7 {
    left: 58.33333%; }

  .col-lg-push-8 {
    left: 66.66667%; }

  .col-lg-push-9 {
    left: 75%; }

  .col-lg-push-10 {
    left: 83.33333%; }

  .col-lg-push-11 {
    left: 91.66667%; }

  .col-lg-push-12 {
    left: 100%; }

  .col-lg-offset-0 {
    margin-left: 0%; }

  .col-lg-offset-1 {
    margin-left: 8.33333%; }

  .col-lg-offset-2 {
    margin-left: 16.66667%; }

  .col-lg-offset-3 {
    margin-left: 25%; }

  .col-lg-offset-4 {
    margin-left: 33.33333%; }

  .col-lg-offset-5 {
    margin-left: 41.66667%; }

  .col-lg-offset-6 {
    margin-left: 50%; }

  .col-lg-offset-7 {
    margin-left: 58.33333%; }

  .col-lg-offset-8 {
    margin-left: 66.66667%; }

  .col-lg-offset-9 {
    margin-left: 75%; }

  .col-lg-offset-10 {
    margin-left: 83.33333%; }

  .col-lg-offset-11 {
    margin-left: 91.66667%; }

  .col-lg-offset-12 {
    margin-left: 100%; } }
table {
  background-color: transparent; }

caption {
  padding-top: 8px;
  padding-bottom: 8px;
  color: #777777;
  text-align: left; }

th {
  text-align: left; }

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px; }
  .table > thead > tr > th,
  .table > thead > tr > td,
  .table > tbody > tr > th,
  .table > tbody > tr > td,
  .table > tfoot > tr > th,
  .table > tfoot > tr > td {
    padding: 8px;
    line-height: 1.42857;
    vertical-align: top;
    border-top: 1px solid #ddd; }
  .table > thead > tr > th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd; }
  .table > caption + thead > tr:first-child > th,
  .table > caption + thead > tr:first-child > td,
  .table > colgroup + thead > tr:first-child > th,
  .table > colgroup + thead > tr:first-child > td,
  .table > thead:first-child > tr:first-child > th,
  .table > thead:first-child > tr:first-child > td {
    border-top: 0; }
  .table > tbody + tbody {
    border-top: 2px solid #ddd; }
  .table .table {
    background-color: #fff; }

.table-condensed > thead > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > th,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > th,
.table-condensed > tfoot > tr > td {
  padding: 5px; }

.table-bordered {
  border: 1px solid #ddd; }
  .table-bordered > thead > tr > th,
  .table-bordered > thead > tr > td,
  .table-bordered > tbody > tr > th,
  .table-bordered > tbody > tr > td,
  .table-bordered > tfoot > tr > th,
  .table-bordered > tfoot > tr > td {
    border: 1px solid #ddd; }
  .table-bordered > thead > tr > th,
  .table-bordered > thead > tr > td {
    border-bottom-width: 2px; }

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9; }

.table-hover > tbody > tr:hover {
  background-color: #f5f5f5; }

table col[class*="col-"] {
  position: static;
  float: none;
  display: table-column; }

table td[class*="col-"],
table th[class*="col-"] {
  position: static;
  float: none;
  display: table-cell; }

.table > thead > tr > td.active,
.table > thead > tr > th.active, .table > thead > tr.active > td, .table > thead > tr.active > th,
.table > tbody > tr > td.active,
.table > tbody > tr > th.active,
.table > tbody > tr.active > td,
.table > tbody > tr.active > th,
.table > tfoot > tr > td.active,
.table > tfoot > tr > th.active,
.table > tfoot > tr.active > td,
.table > tfoot > tr.active > th {
  background-color: #f5f5f5; }

.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover, .table-hover > tbody > tr.active:hover > td, .table-hover > tbody > tr:hover > .active, .table-hover > tbody > tr.active:hover > th {
  background-color: #e8e8e8; }

.table > thead > tr > td.success,
.table > thead > tr > th.success, .table > thead > tr.success > td, .table > thead > tr.success > th,
.table > tbody > tr > td.success,
.table > tbody > tr > th.success,
.table > tbody > tr.success > td,
.table > tbody > tr.success > th,
.table > tfoot > tr > td.success,
.table > tfoot > tr > th.success,
.table > tfoot > tr.success > td,
.table > tfoot > tr.success > th {
  background-color: #dff0d8; }

.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover, .table-hover > tbody > tr.success:hover > td, .table-hover > tbody > tr:hover > .success, .table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6; }

.table > thead > tr > td.info,
.table > thead > tr > th.info, .table > thead > tr.info > td, .table > thead > tr.info > th,
.table > tbody > tr > td.info,
.table > tbody > tr > th.info,
.table > tbody > tr.info > td,
.table > tbody > tr.info > th,
.table > tfoot > tr > td.info,
.table > tfoot > tr > th.info,
.table > tfoot > tr.info > td,
.table > tfoot > tr.info > th {
  background-color: #d9edf7; }

.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover, .table-hover > tbody > tr.info:hover > td, .table-hover > tbody > tr:hover > .info, .table-hover > tbody > tr.info:hover > th {
  background-color: #c4e3f3; }

.table > thead > tr > td.warning,
.table > thead > tr > th.warning, .table > thead > tr.warning > td, .table > thead > tr.warning > th,
.table > tbody > tr > td.warning,
.table > tbody > tr > th.warning,
.table > tbody > tr.warning > td,
.table > tbody > tr.warning > th,
.table > tfoot > tr > td.warning,
.table > tfoot > tr > th.warning,
.table > tfoot > tr.warning > td,
.table > tfoot > tr.warning > th {
  background-color: #fcf8e3; }

.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover, .table-hover > tbody > tr.warning:hover > td, .table-hover > tbody > tr:hover > .warning, .table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc; }

.table > thead > tr > td.danger,
.table > thead > tr > th.danger, .table > thead > tr.danger > td, .table > thead > tr.danger > th,
.table > tbody > tr > td.danger,
.table > tbody > tr > th.danger,
.table > tbody > tr.danger > td,
.table > tbody > tr.danger > th,
.table > tfoot > tr > td.danger,
.table > tfoot > tr > th.danger,
.table > tfoot > tr.danger > td,
.table > tfoot > tr.danger > th {
  background-color: #f2dede; }

.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover, .table-hover > tbody > tr.danger:hover > td, .table-hover > tbody > tr:hover > .danger, .table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc; }

.table-responsive {
  overflow-x: auto;
  min-height: 0.01%; }
  @media screen and (max-width: 767px) {
    .table-responsive {
      width: 100%;
      margin-bottom: 15px;
      overflow-y: hidden;
      -ms-overflow-style: -ms-autohiding-scrollbar;
      border: 1px solid #ddd; }
      .table-responsive > .table {
        margin-bottom: 0; }
        .table-responsive > .table > thead > tr > th,
        .table-responsive > .table > thead > tr > td,
        .table-responsive > .table > tbody > tr > th,
        .table-responsive > .table > tbody > tr > td,
        .table-responsive > .table > tfoot > tr > th,
        .table-responsive > .table > tfoot > tr > td {
          white-space: nowrap; }
      .table-responsive > .table-bordered {
        border: 0; }
        .table-responsive > .table-bordered > thead > tr > th:first-child,
        .table-responsive > .table-bordered > thead > tr > td:first-child,
        .table-responsive > .table-bordered > tbody > tr > th:first-child,
        .table-responsive > .table-bordered > tbody > tr > td:first-child,
        .table-responsive > .table-bordered > tfoot > tr > th:first-child,
        .table-responsive > .table-bordered > tfoot > tr > td:first-child {
          border-left: 0; }
        .table-responsive > .table-bordered > thead > tr > th:last-child,
        .table-responsive > .table-bordered > thead > tr > td:last-child,
        .table-responsive > .table-bordered > tbody > tr > th:last-child,
        .table-responsive > .table-bordered > tbody > tr > td:last-child,
        .table-responsive > .table-bordered > tfoot > tr > th:last-child,
        .table-responsive > .table-bordered > tfoot > tr > td:last-child {
          border-right: 0; }
        .table-responsive > .table-bordered > tbody > tr:last-child > th,
        .table-responsive > .table-bordered > tbody > tr:last-child > td,
        .table-responsive > .table-bordered > tfoot > tr:last-child > th,
        .table-responsive > .table-bordered > tfoot > tr:last-child > td {
          border-bottom: 0; } }

fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0; }

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5; }

label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: bold; }

input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  line-height: normal; }

input[type="file"] {
  display: block; }

input[type="range"] {
  display: block;
  width: 100%; }

select[multiple],
select[size] {
  height: auto; }

input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px; }

output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.42857;
  color: #555555; }

.form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857;
  color: #555555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s; }
  .form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6); }
  .form-control::-moz-placeholder {
    color: #999;
    opacity: 1; }
  .form-control:-ms-input-placeholder {
    color: #999; }
  .form-control::-webkit-input-placeholder {
    color: #999; }
  .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    cursor: not-allowed;
    background-color: #eeeeee;
    opacity: 1; }

textarea.form-control {
  height: auto; }

input[type="search"] {
  -webkit-appearance: none; }

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"],
  input[type="month"] {
    line-height: 34px; }
    input[type="date"].input-sm, .input-group-sm > input[type="date"].form-control,
    .input-group-sm > input[type="date"].input-group-addon,
    .input-group-sm > .input-group-btn > input[type="date"].btn, .input-group-sm input[type="date"],
    input[type="time"].input-sm,
    .input-group-sm > input[type="time"].form-control,
    .input-group-sm > input[type="time"].input-group-addon,
    .input-group-sm > .input-group-btn > input[type="time"].btn, .input-group-sm
    input[type="time"],
    input[type="datetime-local"].input-sm,
    .input-group-sm > input[type="datetime-local"].form-control,
    .input-group-sm > input[type="datetime-local"].input-group-addon,
    .input-group-sm > .input-group-btn > input[type="datetime-local"].btn, .input-group-sm
    input[type="datetime-local"],
    input[type="month"].input-sm,
    .input-group-sm > input[type="month"].form-control,
    .input-group-sm > input[type="month"].input-group-addon,
    .input-group-sm > .input-group-btn > input[type="month"].btn, .input-group-sm
    input[type="month"] {
      line-height: 30px; }
    input[type="date"].input-lg, .input-group-lg > input[type="date"].form-control,
    .input-group-lg > input[type="date"].input-group-addon,
    .input-group-lg > .input-group-btn > input[type="date"].btn, .input-group-lg input[type="date"],
    input[type="time"].input-lg,
    .input-group-lg > input[type="time"].form-control,
    .input-group-lg > input[type="time"].input-group-addon,
    .input-group-lg > .input-group-btn > input[type="time"].btn, .input-group-lg
    input[type="time"],
    input[type="datetime-local"].input-lg,
    .input-group-lg > input[type="datetime-local"].form-control,
    .input-group-lg > input[type="datetime-local"].input-group-addon,
    .input-group-lg > .input-group-btn > input[type="datetime-local"].btn, .input-group-lg
    input[type="datetime-local"],
    input[type="month"].input-lg,
    .input-group-lg > input[type="month"].form-control,
    .input-group-lg > input[type="month"].input-group-addon,
    .input-group-lg > .input-group-btn > input[type="month"].btn, .input-group-lg
    input[type="month"] {
      line-height: 46px; } }
.form-group {
  margin-bottom: 15px; }

.radio,
.checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px; }
  .radio label,
  .checkbox label {
    min-height: 20px;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer; }

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-left: -20px;
  margin-top: 4px \9; }

.radio + .radio,
.checkbox + .checkbox {
  margin-top: -5px; }

.radio-inline,
.checkbox-inline {
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer; }

.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px; }

input[type="radio"][disabled], input[type="radio"].disabled, fieldset[disabled] input[type="radio"],
input[type="checkbox"][disabled],
input[type="checkbox"].disabled, fieldset[disabled]
input[type="checkbox"] {
  cursor: not-allowed; }

.radio-inline.disabled, fieldset[disabled] .radio-inline,
.checkbox-inline.disabled, fieldset[disabled]
.checkbox-inline {
  cursor: not-allowed; }

.radio.disabled label, fieldset[disabled] .radio label,
.checkbox.disabled label, fieldset[disabled]
.checkbox label {
  cursor: not-allowed; }

.form-control-static {
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 0; }
  .form-control-static.input-lg, .input-group-lg > .form-control-static.form-control,
  .input-group-lg > .form-control-static.input-group-addon,
  .input-group-lg > .input-group-btn > .form-control-static.btn, .form-control-static.input-sm, .input-group-sm > .form-control-static.form-control,
  .input-group-sm > .form-control-static.input-group-addon,
  .input-group-sm > .input-group-btn > .form-control-static.btn {
    padding-left: 0;
    padding-right: 0; }

.input-sm, .input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

select.input-sm, .input-group-sm > select.form-control,
.input-group-sm > select.input-group-addon,
.input-group-sm > .input-group-btn > select.btn {
  height: 30px;
  line-height: 30px; }

textarea.input-sm, .input-group-sm > textarea.form-control,
.input-group-sm > textarea.input-group-addon,
.input-group-sm > .input-group-btn > textarea.btn,
select[multiple].input-sm,
.input-group-sm > select[multiple].form-control,
.input-group-sm > select[multiple].input-group-addon,
.input-group-sm > .input-group-btn > select[multiple].btn {
  height: auto; }

.form-group-sm .form-control {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }
.form-group-sm select.form-control {
  height: 30px;
  line-height: 30px; }
.form-group-sm textarea.form-control,
.form-group-sm select[multiple].form-control {
  height: auto; }
.form-group-sm .form-control-static {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5; }

.input-lg, .input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33333;
  border-radius: 6px; }

select.input-lg, .input-group-lg > select.form-control,
.input-group-lg > select.input-group-addon,
.input-group-lg > .input-group-btn > select.btn {
  height: 46px;
  line-height: 46px; }

textarea.input-lg, .input-group-lg > textarea.form-control,
.input-group-lg > textarea.input-group-addon,
.input-group-lg > .input-group-btn > textarea.btn,
select[multiple].input-lg,
.input-group-lg > select[multiple].form-control,
.input-group-lg > select[multiple].input-group-addon,
.input-group-lg > .input-group-btn > select[multiple].btn {
  height: auto; }

.form-group-lg .form-control {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33333;
  border-radius: 6px; }
.form-group-lg select.form-control {
  height: 46px;
  line-height: 46px; }
.form-group-lg textarea.form-control,
.form-group-lg select[multiple].form-control {
  height: auto; }
.form-group-lg .form-control-static {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33333; }

.has-feedback {
  position: relative; }
  .has-feedback .form-control {
    padding-right: 42.5px; }

.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  pointer-events: none; }

.input-lg + .form-control-feedback, .input-group-lg > .form-control + .form-control-feedback,
.input-group-lg > .input-group-addon + .form-control-feedback,
.input-group-lg > .input-group-btn > .btn + .form-control-feedback {
  width: 46px;
  height: 46px;
  line-height: 46px; }

.input-sm + .form-control-feedback, .input-group-sm > .form-control + .form-control-feedback,
.input-group-sm > .input-group-addon + .form-control-feedback,
.input-group-sm > .input-group-btn > .btn + .form-control-feedback {
  width: 30px;
  height: 30px;
  line-height: 30px; }

.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline, .has-success.radio label, .has-success.checkbox label, .has-success.radio-inline label, .has-success.checkbox-inline label {
  color: #3c763d; }
.has-success .form-control {
  border-color: #3c763d;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-success .form-control:focus {
    border-color: #2b542c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168; }
.has-success .input-group-addon {
  color: #3c763d;
  border-color: #3c763d;
  background-color: #dff0d8; }
.has-success .form-control-feedback {
  color: #3c763d; }

.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline, .has-warning.radio label, .has-warning.checkbox label, .has-warning.radio-inline label, .has-warning.checkbox-inline label {
  color: #8a6d3b; }
.has-warning .form-control {
  border-color: #8a6d3b;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-warning .form-control:focus {
    border-color: #66512c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b; }
.has-warning .input-group-addon {
  color: #8a6d3b;
  border-color: #8a6d3b;
  background-color: #fcf8e3; }
.has-warning .form-control-feedback {
  color: #8a6d3b; }

.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline, .has-error.radio label, .has-error.checkbox label, .has-error.radio-inline label, .has-error.checkbox-inline label {
  color: #a94442; }
.has-error .form-control {
  border-color: #a94442;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-error .form-control:focus {
    border-color: #843534;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483; }
.has-error .input-group-addon {
  color: #a94442;
  border-color: #a94442;
  background-color: #f2dede; }
.has-error .form-control-feedback {
  color: #a94442; }

.has-feedback label ~ .form-control-feedback {
  top: 25px; }
.has-feedback label.sr-only ~ .form-control-feedback {
  top: 0; }

.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #a7adb5; }

@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle; }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle; }
  .form-inline .form-control-static {
    display: inline-block; }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle; }
    .form-inline .input-group .input-group-addon,
    .form-inline .input-group .input-group-btn,
    .form-inline .input-group .form-control {
      width: auto; }
  .form-inline .input-group > .form-control {
    width: 100%; }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle; }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle; }
    .form-inline .radio label,
    .form-inline .checkbox label {
      padding-left: 0; }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0; }
  .form-inline .has-feedback .form-control-feedback {
    top: 0; } }

.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px; }
.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 27px; }
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px; }
  .form-horizontal .form-group:before, .form-horizontal .form-group:after {
    content: " ";
    display: table; }
  .form-horizontal .form-group:after {
    clear: both; }
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
    margin-bottom: 0;
    padding-top: 7px; } }
.form-horizontal .has-feedback .form-control-feedback {
  right: 15px; }
@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 14.33333px; } }
@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 6px; } }

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }
  .btn:focus, .btn.focus, .btn:active:focus, .btn:active.focus, .btn.active:focus, .btn.active.focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px; }
  .btn:hover, .btn:focus, .btn.focus {
    color: #333;
    text-decoration: none; }
  .btn:active, .btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }
  .btn.disabled, .btn[disabled], fieldset[disabled] .btn {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.65;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none; }

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc; }
  .btn-default:hover, .btn-default:focus, .btn-default.focus, .btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad; }
  .btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
    background-image: none; }
  .btn-default.disabled, .btn-default.disabled:hover, .btn-default.disabled:focus, .btn-default.disabled.focus, .btn-default.disabled:active, .btn-default.disabled.active, .btn-default[disabled], .btn-default[disabled]:hover, .btn-default[disabled]:focus, .btn-default[disabled].focus, .btn-default[disabled]:active, .btn-default[disabled].active, fieldset[disabled] .btn-default, fieldset[disabled] .btn-default:hover, fieldset[disabled] .btn-default:focus, fieldset[disabled] .btn-default.focus, fieldset[disabled] .btn-default:active, fieldset[disabled] .btn-default.active {
    background-color: #fff;
    border-color: #ccc; }
  .btn-default .badge {
    color: #fff;
    background-color: #333; }

.btn-primary {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4; }
  .btn-primary:hover, .btn-primary:focus, .btn-primary.focus, .btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #286090;
    border-color: #204d74; }
  .btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
    background-image: none; }
  .btn-primary.disabled, .btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled.focus, .btn-primary.disabled:active, .btn-primary.disabled.active, .btn-primary[disabled], .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled].focus, .btn-primary[disabled]:active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary, fieldset[disabled] .btn-primary:hover, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary.focus, fieldset[disabled] .btn-primary:active, fieldset[disabled] .btn-primary.active {
    background-color: #337ab7;
    border-color: #2e6da4; }
  .btn-primary .badge {
    color: #337ab7;
    background-color: #fff; }

.btn-success {
  color: #fff;
  background-color: #5cb85c;
  border-color: #4cae4c; }
  .btn-success:hover, .btn-success:focus, .btn-success.focus, .btn-success:active, .btn-success.active, .open > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #449d44;
    border-color: #398439; }
  .btn-success:active, .btn-success.active, .open > .btn-success.dropdown-toggle {
    background-image: none; }
  .btn-success.disabled, .btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success.disabled:active, .btn-success.disabled.active, .btn-success[disabled], .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled].focus, .btn-success[disabled]:active, .btn-success[disabled].active, fieldset[disabled] .btn-success, fieldset[disabled] .btn-success:hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success.focus, fieldset[disabled] .btn-success:active, fieldset[disabled] .btn-success.active {
    background-color: #5cb85c;
    border-color: #4cae4c; }
  .btn-success .badge {
    color: #5cb85c;
    background-color: #fff; }

.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da; }
  .btn-info:hover, .btn-info:focus, .btn-info.focus, .btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #31b0d5;
    border-color: #269abc; }
  .btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
    background-image: none; }
  .btn-info.disabled, .btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled.focus, .btn-info.disabled:active, .btn-info.disabled.active, .btn-info[disabled], .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled].focus, .btn-info[disabled]:active, .btn-info[disabled].active, fieldset[disabled] .btn-info, fieldset[disabled] .btn-info:hover, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info.focus, fieldset[disabled] .btn-info:active, fieldset[disabled] .btn-info.active {
    background-color: #5bc0de;
    border-color: #46b8da; }
  .btn-info .badge {
    color: #5bc0de;
    background-color: #fff; }

.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236; }
  .btn-warning:hover, .btn-warning:focus, .btn-warning.focus, .btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512; }
  .btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
    background-image: none; }
  .btn-warning.disabled, .btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled.focus, .btn-warning.disabled:active, .btn-warning.disabled.active, .btn-warning[disabled], .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled].focus, .btn-warning[disabled]:active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning, fieldset[disabled] .btn-warning:hover, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning.focus, fieldset[disabled] .btn-warning:active, fieldset[disabled] .btn-warning.active {
    background-color: #f0ad4e;
    border-color: #eea236; }
  .btn-warning .badge {
    color: #f0ad4e;
    background-color: #fff; }

.btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a; }
  .btn-danger:hover, .btn-danger:focus, .btn-danger.focus, .btn-danger:active, .btn-danger.active, .open > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #c9302c;
    border-color: #ac2925; }
  .btn-danger:active, .btn-danger.active, .open > .btn-danger.dropdown-toggle {
    background-image: none; }
  .btn-danger.disabled, .btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled.focus, .btn-danger.disabled:active, .btn-danger.disabled.active, .btn-danger[disabled], .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled].focus, .btn-danger[disabled]:active, .btn-danger[disabled].active, fieldset[disabled] .btn-danger, fieldset[disabled] .btn-danger:hover, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger.focus, fieldset[disabled] .btn-danger:active, fieldset[disabled] .btn-danger.active {
    background-color: #d9534f;
    border-color: #d43f3a; }
  .btn-danger .badge {
    color: #d9534f;
    background-color: #fff; }

.btn-link {
  color: #337ab7;
  font-weight: normal;
  border-radius: 0; }
  .btn-link, .btn-link:active, .btn-link.active, .btn-link[disabled], fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none; }
  .btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active {
    border-color: transparent; }
  .btn-link:hover, .btn-link:focus {
    color: #23527c;
    text-decoration: underline;
    background-color: transparent; }
  .btn-link[disabled]:hover, .btn-link[disabled]:focus, fieldset[disabled] .btn-link:hover, fieldset[disabled] .btn-link:focus {
    color: #777777;
    text-decoration: none; }

.btn-lg, .btn-group-lg > .btn {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33333;
  border-radius: 6px; }

.btn-sm, .btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

.btn-xs, .btn-group-xs > .btn {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

.btn-block {
  display: block;
  width: 100%; }

.btn-block + .btn-block {
  margin-top: 5px; }

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%; }

.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear; }
  .fade.in {
    opacity: 1; }

.collapse {
  display: none;
  visibility: hidden; }
  .collapse.in {
    display: block;
    visibility: visible; }

tr.collapse.in {
  display: table-row; }

tbody.collapse.in {
  display: table-row-group; }

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease; }

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px solid;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent; }

.dropup,
.dropdown {
  position: relative; }

.dropdown-toggle:focus {
  outline: 0; }

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  text-align: left;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box; }
  .dropdown-menu.pull-right {
    right: 0;
    left: auto; }
  .dropdown-menu .divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5; }
  .dropdown-menu > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.42857;
    color: #333333;
    white-space: nowrap; }

.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5; }

.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #337ab7; }

.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  color: #777777; }
.dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed; }

.open > .dropdown-menu {
  display: block; }
.open > a {
  outline: 0; }

.dropdown-menu-right {
  left: auto;
  right: 0; }

.dropdown-menu-left {
  left: 0;
  right: auto; }

.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.42857;
  color: #777777;
  white-space: nowrap; }

.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990; }

.pull-right > .dropdown-menu {
  right: 0;
  left: auto; }

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px solid;
  content: ""; }
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px; }

@media (min-width: 1200px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto; }
  .navbar-right .dropdown-menu-left {
    left: 0;
    right: auto; } }
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle; }
  .btn-group > .btn,
  .btn-group-vertical > .btn {
    position: relative;
    float: left; }
    .btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,
    .btn-group-vertical > .btn:hover,
    .btn-group-vertical > .btn:focus,
    .btn-group-vertical > .btn:active,
    .btn-group-vertical > .btn.active {
      z-index: 2; }

.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px; }

.btn-toolbar {
  margin-left: -5px; }
  .btn-toolbar:before, .btn-toolbar:after {
    content: " ";
    display: table; }
  .btn-toolbar:after {
    clear: both; }
  .btn-toolbar .btn-group,
  .btn-toolbar .input-group {
    float: left; }
  .btn-toolbar > .btn,
  .btn-toolbar > .btn-group,
  .btn-toolbar > .input-group {
    margin-left: 5px; }

.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0; }

.btn-group > .btn:first-child {
  margin-left: 0; }
  .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0; }

.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.btn-group > .btn-group {
  float: left; }

.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0; }

.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0; }

.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0; }

.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px; }

.btn-group > .btn-lg + .dropdown-toggle, .btn-group-lg.btn-group > .btn + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px; }

.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }
  .btn-group.open .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    box-shadow: none; }

.btn .caret {
  margin-left: 0; }

.btn-lg .caret, .btn-group-lg > .btn .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0; }

.dropup .btn-lg .caret, .dropup .btn-group-lg > .btn .caret {
  border-width: 0 5px 5px; }

.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group,
.btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%; }
.btn-group-vertical > .btn-group:before, .btn-group-vertical > .btn-group:after {
  content: " ";
  display: table; }
.btn-group-vertical > .btn-group:after {
  clear: both; }
.btn-group-vertical > .btn-group > .btn {
  float: none; }
.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0; }

.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0; }
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0; }

.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate; }
  .btn-group-justified > .btn,
  .btn-group-justified > .btn-group {
    float: none;
    display: table-cell;
    width: 1%; }
  .btn-group-justified > .btn-group .btn {
    width: 100%; }
  .btn-group-justified > .btn-group .dropdown-menu {
    left: auto; }

[data-toggle="buttons"] > .btn input[type="radio"],
[data-toggle="buttons"] > .btn input[type="checkbox"],
[data-toggle="buttons"] > .btn-group > .btn input[type="radio"],
[data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none; }

.input-group {
  position: relative;
  display: table;
  border-collapse: separate; }
  .input-group[class*="col-"] {
    float: none;
    padding-left: 0;
    padding-right: 0; }
  .input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0; }

.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell; }
  .input-group-addon:not(:first-child):not(:last-child),
  .input-group-btn:not(:first-child):not(:last-child),
  .input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0; }

.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle; }

.input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #555555;
  text-align: center;
  background-color: #eeeeee;
  border: 1px solid #ccc;
  border-radius: 4px; }
  .input-group-addon.input-sm,
  .input-group-sm > .input-group-addon,
  .input-group-sm > .input-group-btn > .input-group-addon.btn {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 3px; }
  .input-group-addon.input-lg,
  .input-group-lg > .input-group-addon,
  .input-group-lg > .input-group-btn > .input-group-addon.btn {
    padding: 10px 16px;
    font-size: 18px;
    border-radius: 6px; }
  .input-group-addon input[type="radio"],
  .input-group-addon input[type="checkbox"] {
    margin-top: 0; }

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0; }

.input-group-addon:first-child {
  border-right: 0; }

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.input-group-addon:last-child {
  border-left: 0; }

.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap; }
  .input-group-btn > .btn {
    position: relative; }
    .input-group-btn > .btn + .btn {
      margin-left: -1px; }
    .input-group-btn > .btn:hover, .input-group-btn > .btn:focus, .input-group-btn > .btn:active {
      z-index: 2; }
  .input-group-btn:first-child > .btn,
  .input-group-btn:first-child > .btn-group {
    margin-right: -1px; }
  .input-group-btn:last-child > .btn,
  .input-group-btn:last-child > .btn-group {
    margin-left: -1px; }

.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none; }
  .nav:before, .nav:after {
    content: " ";
    display: table; }
  .nav:after {
    clear: both; }
  .nav > li {
    position: relative;
    display: block; }
    .nav > li > a {
      position: relative;
      display: block;
      padding: 10px 15px; }
      .nav > li > a:hover, .nav > li > a:focus {
        text-decoration: none;
        background-color: #eeeeee; }
    .nav > li.disabled > a {
      color: #777777; }
      .nav > li.disabled > a:hover, .nav > li.disabled > a:focus {
        color: #777777;
        text-decoration: none;
        background-color: transparent;
        cursor: not-allowed; }
  .nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
    background-color: #eeeeee;
    border-color: #337ab7; }
  .nav .nav-divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5; }
  .nav > li > a > img {
    max-width: none; }

.nav-tabs {
  border-bottom: 1px solid #ddd; }
  .nav-tabs > li {
    float: left;
    margin-bottom: -1px; }
    .nav-tabs > li > a {
      margin-right: 2px;
      line-height: 1.42857;
      border: 1px solid transparent;
      border-radius: 4px 4px 0 0; }
      .nav-tabs > li > a:hover {
        border-color: #eeeeee #eeeeee #ddd; }
    .nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
      color: #555555;
      background-color: #fff;
      border: 1px solid #ddd;
      border-bottom-color: transparent;
      cursor: default; }

.nav-pills > li {
  float: left; }
  .nav-pills > li > a {
    border-radius: 4px; }
  .nav-pills > li + li {
    margin-left: 2px; }
  .nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
    color: #fff;
    background-color: #337ab7; }

.nav-stacked > li {
  float: none; }
  .nav-stacked > li + li {
    margin-top: 2px;
    margin-left: 0; }

.nav-justified, .nav-tabs.nav-justified {
  width: 100%; }
  .nav-justified > li, .nav-tabs.nav-justified > li {
    float: none; }
    .nav-justified > li > a, .nav-tabs.nav-justified > li > a {
      text-align: center;
      margin-bottom: 5px; }
  .nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto; }
  @media (min-width: 768px) {
    .nav-justified > li, .nav-tabs.nav-justified > li {
      display: table-cell;
      width: 1%; }
      .nav-justified > li > a, .nav-tabs.nav-justified > li > a {
        margin-bottom: 0; } }

.nav-tabs-justified, .nav-tabs.nav-justified {
  border-bottom: 0; }
  .nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {
    margin-right: 0;
    border-radius: 4px; }
  .nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs-justified > .active > a:focus,
  .nav-tabs.nav-justified > .active > a:focus {
    border: 1px solid #ddd; }
  @media (min-width: 768px) {
    .nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {
      border-bottom: 1px solid #ddd;
      border-radius: 4px 4px 0 0; }
    .nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a,
    .nav-tabs-justified > .active > a:hover,
    .nav-tabs.nav-justified > .active > a:hover,
    .nav-tabs-justified > .active > a:focus,
    .nav-tabs.nav-justified > .active > a:focus {
      border-bottom-color: #fff; } }

.tab-content > .tab-pane {
  display: none;
  visibility: hidden; }
.tab-content > .active {
  display: block;
  visibility: visible; }

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.navbar {
  position: relative;
  min-height: 50px;
  margin-bottom: 20px;
  border: 1px solid transparent; }
  .navbar:before, .navbar:after {
    content: " ";
    display: table; }
  .navbar:after {
    clear: both; }
  @media (min-width: 1200px) {
    .navbar {
      border-radius: 4px; } }

.navbar-header:before, .navbar-header:after {
  content: " ";
  display: table; }
.navbar-header:after {
  clear: both; }
@media (min-width: 1200px) {
  .navbar-header {
    float: left; } }

.navbar-collapse {
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch; }
  .navbar-collapse:before, .navbar-collapse:after {
    content: " ";
    display: table; }
  .navbar-collapse:after {
    clear: both; }
  .navbar-collapse.in {
    overflow-y: auto; }
  @media (min-width: 1200px) {
    .navbar-collapse {
      width: auto;
      border-top: 0;
      box-shadow: none; }
      .navbar-collapse.collapse {
        display: block !important;
        visibility: visible !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important; }
      .navbar-collapse.in {
        overflow-y: visible; }
      .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
        padding-left: 0;
        padding-right: 0; } }

.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
  max-height: 340px; }
  @media (max-device-width: 480px) and (orientation: landscape) {
    .navbar-fixed-top .navbar-collapse,
    .navbar-fixed-bottom .navbar-collapse {
      max-height: 200px; } }

.container > .navbar-header,
.container > .navbar-collapse,
.container-fluid > .navbar-header,
.container-fluid > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px; }
  @media (min-width: 1200px) {
    .container > .navbar-header,
    .container > .navbar-collapse,
    .container-fluid > .navbar-header,
    .container-fluid > .navbar-collapse {
      margin-right: 0;
      margin-left: 0; } }

.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px; }
  @media (min-width: 1200px) {
    .navbar-static-top {
      border-radius: 0; } }

.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030; }
  @media (min-width: 1200px) {
    .navbar-fixed-top,
    .navbar-fixed-bottom {
      border-radius: 0; } }

.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px; }

.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0; }

.navbar-brand {
  float: left;
  padding: 15px 15px;
  font-size: 18px;
  line-height: 20px;
  height: 50px; }
  .navbar-brand:hover, .navbar-brand:focus {
    text-decoration: none; }
  .navbar-brand > img {
    display: block; }
  @media (min-width: 1200px) {
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand {
      margin-left: -15px; } }

.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px; }
  .navbar-toggle:focus {
    outline: 0; }
  .navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px; }
  .navbar-toggle .icon-bar + .icon-bar {
    margin-top: 4px; }
  @media (min-width: 1200px) {
    .navbar-toggle {
      display: none; } }

.navbar-nav {
  margin: 7.5px -15px; }
  .navbar-nav > li > a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 20px; }
  @media (max-width: 1199px) {
    .navbar-nav .open .dropdown-menu {
      position: static;
      float: none;
      width: auto;
      margin-top: 0;
      background-color: transparent;
      border: 0;
      box-shadow: none; }
      .navbar-nav .open .dropdown-menu > li > a,
      .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 15px 5px 25px; }
      .navbar-nav .open .dropdown-menu > li > a {
        line-height: 20px; }
        .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-nav .open .dropdown-menu > li > a:focus {
          background-image: none; } }
  @media (min-width: 1200px) {
    .navbar-nav {
      float: left;
      margin: 0; }
      .navbar-nav > li {
        float: left; }
        .navbar-nav > li > a {
          padding-top: 15px;
          padding-bottom: 15px; } }

.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 8px;
  margin-bottom: 8px; }
  @media (min-width: 768px) {
    .navbar-form .form-group {
      display: inline-block;
      margin-bottom: 0;
      vertical-align: middle; }
    .navbar-form .form-control {
      display: inline-block;
      width: auto;
      vertical-align: middle; }
    .navbar-form .form-control-static {
      display: inline-block; }
    .navbar-form .input-group {
      display: inline-table;
      vertical-align: middle; }
      .navbar-form .input-group .input-group-addon,
      .navbar-form .input-group .input-group-btn,
      .navbar-form .input-group .form-control {
        width: auto; }
    .navbar-form .input-group > .form-control {
      width: 100%; }
    .navbar-form .control-label {
      margin-bottom: 0;
      vertical-align: middle; }
    .navbar-form .radio,
    .navbar-form .checkbox {
      display: inline-block;
      margin-top: 0;
      margin-bottom: 0;
      vertical-align: middle; }
      .navbar-form .radio label,
      .navbar-form .checkbox label {
        padding-left: 0; }
    .navbar-form .radio input[type="radio"],
    .navbar-form .checkbox input[type="checkbox"] {
      position: relative;
      margin-left: 0; }
    .navbar-form .has-feedback .form-control-feedback {
      top: 0; } }
  @media (max-width: 1199px) {
    .navbar-form .form-group {
      margin-bottom: 5px; }
      .navbar-form .form-group:last-child {
        margin-bottom: 0; } }
  @media (min-width: 1200px) {
    .navbar-form {
      width: auto;
      border: 0;
      margin-left: 0;
      margin-right: 0;
      padding-top: 0;
      padding-bottom: 0;
      -webkit-box-shadow: none;
      box-shadow: none; } }

.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  margin-bottom: 0;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.navbar-btn {
  margin-top: 8px;
  margin-bottom: 8px; }
  .navbar-btn.btn-sm, .btn-group-sm > .navbar-btn.btn {
    margin-top: 10px;
    margin-bottom: 10px; }
  .navbar-btn.btn-xs, .btn-group-xs > .navbar-btn.btn {
    margin-top: 14px;
    margin-bottom: 14px; }

.navbar-text {
  margin-top: 15px;
  margin-bottom: 15px; }
  @media (min-width: 1200px) {
    .navbar-text {
      float: left;
      margin-left: 15px;
      margin-right: 15px; } }

@media (min-width: 1200px) {
  .navbar-left {
    float: left !important; }

  .navbar-right {
    float: right !important;
    margin-right: -15px; }
    .navbar-right ~ .navbar-right {
      margin-right: 0; } }
.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7; }
  .navbar-default .navbar-brand {
    color: #777; }
    .navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
      color: #5e5e5e;
      background-color: transparent; }
  .navbar-default .navbar-text {
    color: #777; }
  .navbar-default .navbar-nav > li > a {
    color: #777; }
    .navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
      color: #333;
      background-color: transparent; }
  .navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
    color: #555;
    background-color: #e7e7e7; }
  .navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:hover, .navbar-default .navbar-nav > .disabled > a:focus {
    color: #ccc;
    background-color: transparent; }
  .navbar-default .navbar-toggle {
    border-color: #ddd; }
    .navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
      background-color: #ddd; }
    .navbar-default .navbar-toggle .icon-bar {
      background-color: #888; }
  .navbar-default .navbar-collapse,
  .navbar-default .navbar-form {
    border-color: #e7e7e7; }
  .navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
    background-color: #e7e7e7;
    color: #555; }
  @media (max-width: 1199px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
      color: #777; }
      .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #333;
        background-color: transparent; }
    .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
      color: #555;
      background-color: #e7e7e7; }
    .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
      color: #ccc;
      background-color: transparent; } }
  .navbar-default .navbar-link {
    color: #777; }
    .navbar-default .navbar-link:hover {
      color: #333; }
  .navbar-default .btn-link {
    color: #777; }
    .navbar-default .btn-link:hover, .navbar-default .btn-link:focus {
      color: #333; }
    .navbar-default .btn-link[disabled]:hover, .navbar-default .btn-link[disabled]:focus, fieldset[disabled] .navbar-default .btn-link:hover, fieldset[disabled] .navbar-default .btn-link:focus {
      color: #ccc; }

.navbar-inverse {
  background-color: #222;
  border-color: #090909; }
  .navbar-inverse .navbar-brand {
    color: #9d9d9d; }
    .navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
      color: #fff;
      background-color: transparent; }
  .navbar-inverse .navbar-text {
    color: #9d9d9d; }
  .navbar-inverse .navbar-nav > li > a {
    color: #9d9d9d; }
    .navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {
      color: #fff;
      background-color: transparent; }
  .navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
    color: #fff;
    background-color: #090909; }
  .navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:hover, .navbar-inverse .navbar-nav > .disabled > a:focus {
    color: #444;
    background-color: transparent; }
  .navbar-inverse .navbar-toggle {
    border-color: #333; }
    .navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {
      background-color: #333; }
    .navbar-inverse .navbar-toggle .icon-bar {
      background-color: #fff; }
  .navbar-inverse .navbar-collapse,
  .navbar-inverse .navbar-form {
    border-color: #101010; }
  .navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
    background-color: #090909;
    color: #fff; }
  @media (max-width: 1199px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
      border-color: #090909; }
    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
      background-color: #090909; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
      color: #9d9d9d; }
      .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #fff;
        background-color: transparent; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
      color: #fff;
      background-color: #090909; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
      color: #444;
      background-color: transparent; } }
  .navbar-inverse .navbar-link {
    color: #9d9d9d; }
    .navbar-inverse .navbar-link:hover {
      color: #fff; }
  .navbar-inverse .btn-link {
    color: #9d9d9d; }
    .navbar-inverse .btn-link:hover, .navbar-inverse .btn-link:focus {
      color: #fff; }
    .navbar-inverse .btn-link[disabled]:hover, .navbar-inverse .btn-link[disabled]:focus, fieldset[disabled] .navbar-inverse .btn-link:hover, fieldset[disabled] .navbar-inverse .btn-link:focus {
      color: #444; }

.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 4px; }
  .breadcrumb > li {
    display: inline-block; }
    .breadcrumb > li + li:before {
      content: /\00a0;
      padding: 0 5px;
      color: #ccc; }
  .breadcrumb > .active {
    color: #777777; }

.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px; }
  .pagination > li {
    display: inline; }
    .pagination > li > a,
    .pagination > li > span {
      position: relative;
      float: left;
      padding: 6px 12px;
      line-height: 1.42857;
      text-decoration: none;
      color: #337ab7;
      background-color: #fff;
      border: 1px solid #ddd;
      margin-left: -1px; }
    .pagination > li:first-child > a,
    .pagination > li:first-child > span {
      margin-left: 0;
      border-bottom-left-radius: 4px;
      border-top-left-radius: 4px; }
    .pagination > li:last-child > a,
    .pagination > li:last-child > span {
      border-bottom-right-radius: 4px;
      border-top-right-radius: 4px; }
  .pagination > li > a:hover, .pagination > li > a:focus,
  .pagination > li > span:hover,
  .pagination > li > span:focus {
    color: #23527c;
    background-color: #eeeeee;
    border-color: #ddd; }
  .pagination > .active > a, .pagination > .active > a:hover, .pagination > .active > a:focus,
  .pagination > .active > span,
  .pagination > .active > span:hover,
  .pagination > .active > span:focus {
    z-index: 2;
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
    cursor: default; }
  .pagination > .disabled > span,
  .pagination > .disabled > span:hover,
  .pagination > .disabled > span:focus,
  .pagination > .disabled > a,
  .pagination > .disabled > a:hover,
  .pagination > .disabled > a:focus {
    color: #777777;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed; }

.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 10px 16px;
  font-size: 18px; }
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px; }
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px; }

.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 12px; }
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px; }
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px; }

.pager {
  padding-left: 0;
  margin: 20px 0;
  list-style: none;
  text-align: center; }
  .pager:before, .pager:after {
    content: " ";
    display: table; }
  .pager:after {
    clear: both; }
  .pager li {
    display: inline; }
    .pager li > a,
    .pager li > span {
      display: inline-block;
      padding: 5px 14px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 15px; }
    .pager li > a:hover,
    .pager li > a:focus {
      text-decoration: none;
      background-color: #eeeeee; }
  .pager .next > a,
  .pager .next > span {
    float: right; }
  .pager .previous > a,
  .pager .previous > span {
    float: left; }
  .pager .disabled > a,
  .pager .disabled > a:hover,
  .pager .disabled > a:focus,
  .pager .disabled > span {
    color: #777777;
    background-color: #fff;
    cursor: not-allowed; }

.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em; }
  .label:empty {
    display: none; }
  .btn .label {
    position: relative;
    top: -1px; }

a.label:hover, a.label:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer; }

.label-default {
  background-color: #777777; }
  .label-default[href]:hover, .label-default[href]:focus {
    background-color: #5e5e5e; }

.label-primary {
  background-color: #337ab7; }
  .label-primary[href]:hover, .label-primary[href]:focus {
    background-color: #286090; }

.label-success {
  background-color: #5cb85c; }
  .label-success[href]:hover, .label-success[href]:focus {
    background-color: #449d44; }

.label-info {
  background-color: #5bc0de; }
  .label-info[href]:hover, .label-info[href]:focus {
    background-color: #31b0d5; }

.label-warning {
  background-color: #f0ad4e; }
  .label-warning[href]:hover, .label-warning[href]:focus {
    background-color: #ec971f; }

.label-danger {
  background-color: #d9534f; }
  .label-danger[href]:hover, .label-danger[href]:focus {
    background-color: #c9302c; }

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #777777;
  border-radius: 10px; }
  .badge:empty {
    display: none; }
  .btn .badge {
    position: relative;
    top: -1px; }
  .btn-xs .badge, .btn-group-xs > .btn .badge {
    top: 0;
    padding: 1px 5px; }
  .list-group-item.active > .badge, .nav-pills > .active > a > .badge {
    color: #337ab7;
    background-color: #fff; }
  .list-group-item > .badge {
    float: right; }
  .list-group-item > .badge + .badge {
    margin-right: 5px; }
  .nav-pills > li > a > .badge {
    margin-left: 3px; }

a.badge:hover, a.badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer; }

.jumbotron {
  padding: 30px 15px;
  margin-bottom: 30px;
  color: inherit;
  background-color: #eeeeee; }
  .jumbotron h1,
  .jumbotron .h1 {
    color: inherit; }
  .jumbotron p {
    margin-bottom: 15px;
    font-size: 21px;
    font-weight: 200; }
  .jumbotron > hr {
    border-top-color: #d5d5d5; }
  .container .jumbotron, .container-fluid .jumbotron {
    border-radius: 6px; }
  .jumbotron .container {
    max-width: 100%; }
  @media screen and (min-width: 768px) {
    .jumbotron {
      padding: 48px 0; }
      .container .jumbotron, .container-fluid .jumbotron {
        padding-left: 60px;
        padding-right: 60px; }
      .jumbotron h1,
      .jumbotron .h1 {
        font-size: 63px; } }

.thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 20px;
  line-height: 1.42857;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: border 0.2s ease-in-out;
  -o-transition: border 0.2s ease-in-out;
  transition: border 0.2s ease-in-out; }
  .thumbnail > img,
  .thumbnail a > img {
    display: block;
    max-width: 100%;
    height: auto;
    margin-left: auto;
    margin-right: auto; }
  .thumbnail .caption {
    padding: 9px;
    color: #656d78; }

a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
  border-color: #337ab7; }

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px; }
  .alert h4 {
    margin-top: 0;
    color: inherit; }
  .alert .alert-link {
    font-weight: bold; }
  .alert > p,
  .alert > ul {
    margin-bottom: 0; }
  .alert > p + p {
    margin-top: 5px; }

.alert-dismissable,
.alert-dismissible {
  padding-right: 35px; }
  .alert-dismissable .close,
  .alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit; }

.alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #3c763d; }
  .alert-success hr {
    border-top-color: #c9e2b3; }
  .alert-success .alert-link {
    color: #2b542c; }

.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f; }
  .alert-info hr {
    border-top-color: #a6e1ec; }
  .alert-info .alert-link {
    color: #245269; }

.alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #8a6d3b; }
  .alert-warning hr {
    border-top-color: #f7e1b5; }
  .alert-warning .alert-link {
    color: #66512c; }

.alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #a94442; }
  .alert-danger hr {
    border-top-color: #e4b9c0; }
  .alert-danger .alert-link {
    color: #843534; }

.media {
  margin-top: 15px; }
  .media:first-child {
    margin-top: 0; }

.media,
.media-body {
  zoom: 1;
  overflow: hidden; }

.media-body {
  width: 10000px; }

.media-object {
  display: block; }

.media-right,
.media > .pull-right {
  padding-left: 10px; }

.media-left,
.media > .pull-left {
  padding-right: 10px; }

.media-left,
.media-right,
.media-body {
  display: table-cell;
  vertical-align: top; }

.media-middle {
  vertical-align: middle; }

.media-bottom {
  vertical-align: bottom; }

.media-heading {
  margin-top: 0;
  margin-bottom: 5px; }

.media-list {
  padding-left: 0;
  list-style: none; }

.list-group {
  margin-bottom: 20px;
  padding-left: 0; }

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd; }
  .list-group-item:first-child {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px; }
  .list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px; }

a.list-group-item {
  color: #555; }
  a.list-group-item .list-group-item-heading {
    color: #333; }
  a.list-group-item:hover, a.list-group-item:focus {
    text-decoration: none;
    color: #555;
    background-color: #f5f5f5; }

.list-group-item.disabled, .list-group-item.disabled:hover, .list-group-item.disabled:focus {
  background-color: #eeeeee;
  color: #777777;
  cursor: not-allowed; }
  .list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading {
    color: inherit; }
  .list-group-item.disabled .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text {
    color: #777777; }
.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
  z-index: 2;
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7; }
  .list-group-item.active .list-group-item-heading,
  .list-group-item.active .list-group-item-heading > small,
  .list-group-item.active .list-group-item-heading > .small, .list-group-item.active:hover .list-group-item-heading,
  .list-group-item.active:hover .list-group-item-heading > small,
  .list-group-item.active:hover .list-group-item-heading > .small, .list-group-item.active:focus .list-group-item-heading,
  .list-group-item.active:focus .list-group-item-heading > small,
  .list-group-item.active:focus .list-group-item-heading > .small {
    color: inherit; }
  .list-group-item.active .list-group-item-text, .list-group-item.active:hover .list-group-item-text, .list-group-item.active:focus .list-group-item-text {
    color: #c7ddef; }

.list-group-item-success {
  color: #3c763d;
  background-color: #dff0d8; }

a.list-group-item-success {
  color: #3c763d; }
  a.list-group-item-success .list-group-item-heading {
    color: inherit; }
  a.list-group-item-success:hover, a.list-group-item-success:focus {
    color: #3c763d;
    background-color: #d0e9c6; }
  a.list-group-item-success.active, a.list-group-item-success.active:hover, a.list-group-item-success.active:focus {
    color: #fff;
    background-color: #3c763d;
    border-color: #3c763d; }

.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7; }

a.list-group-item-info {
  color: #31708f; }
  a.list-group-item-info .list-group-item-heading {
    color: inherit; }
  a.list-group-item-info:hover, a.list-group-item-info:focus {
    color: #31708f;
    background-color: #c4e3f3; }
  a.list-group-item-info.active, a.list-group-item-info.active:hover, a.list-group-item-info.active:focus {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f; }

.list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3; }

a.list-group-item-warning {
  color: #8a6d3b; }
  a.list-group-item-warning .list-group-item-heading {
    color: inherit; }
  a.list-group-item-warning:hover, a.list-group-item-warning:focus {
    color: #8a6d3b;
    background-color: #faf2cc; }
  a.list-group-item-warning.active, a.list-group-item-warning.active:hover, a.list-group-item-warning.active:focus {
    color: #fff;
    background-color: #8a6d3b;
    border-color: #8a6d3b; }

.list-group-item-danger {
  color: #a94442;
  background-color: #f2dede; }

a.list-group-item-danger {
  color: #a94442; }
  a.list-group-item-danger .list-group-item-heading {
    color: inherit; }
  a.list-group-item-danger:hover, a.list-group-item-danger:focus {
    color: #a94442;
    background-color: #ebcccc; }
  a.list-group-item-danger.active, a.list-group-item-danger.active:hover, a.list-group-item-danger.active:focus {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442; }

.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px; }

.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3; }

.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05); }

.panel-body {
  padding: 15px; }
  .panel-body:before, .panel-body:after {
    content: " ";
    display: table; }
  .panel-body:after {
    clear: both; }

.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px; }
  .panel-heading > .dropdown .dropdown-toggle {
    color: inherit; }

.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
  color: inherit; }
  .panel-title > a,
  .panel-title > small,
  .panel-title > .small,
  .panel-title > small > a,
  .panel-title > .small > a {
    color: inherit; }

.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px; }

.panel > .list-group,
.panel > .panel-collapse > .list-group {
  margin-bottom: 0; }
  .panel > .list-group .list-group-item,
  .panel > .panel-collapse > .list-group .list-group-item {
    border-width: 1px 0;
    border-radius: 0; }
  .panel > .list-group:first-child .list-group-item:first-child,
  .panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
    border-top: 0;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px; }
  .panel > .list-group:last-child .list-group-item:last-child,
  .panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
    border-bottom: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px; }

.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0; }

.list-group + .panel-footer {
  border-top-width: 0; }

.panel > .table,
.panel > .table-responsive > .table,
.panel > .panel-collapse > .table {
  margin-bottom: 0; }
  .panel > .table caption,
  .panel > .table-responsive > .table caption,
  .panel > .panel-collapse > .table caption {
    padding-left: 15px;
    padding-right: 15px; }
.panel > .table:first-child,
.panel > .table-responsive:first-child > .table:first-child {
  border-top-right-radius: 3px;
  border-top-left-radius: 3px; }
  .panel > .table:first-child > thead:first-child > tr:first-child,
  .panel > .table:first-child > tbody:first-child > tr:first-child,
  .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child,
  .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
    border-top-left-radius: 3px;
    border-top-right-radius: 3px; }
    .panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
    .panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
      border-top-left-radius: 3px; }
    .panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
    .panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
      border-top-right-radius: 3px; }
.panel > .table:last-child,
.panel > .table-responsive:last-child > .table:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px; }
  .panel > .table:last-child > tbody:last-child > tr:last-child,
  .panel > .table:last-child > tfoot:last-child > tr:last-child,
  .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child,
  .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px; }
    .panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
    .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
      border-bottom-left-radius: 3px; }
    .panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
    .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
      border-bottom-right-radius: 3px; }
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive,
.panel > .table + .panel-body,
.panel > .table-responsive + .panel-body {
  border-top: 1px solid #ddd; }
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0; }
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0; }
  .panel > .table-bordered > thead > tr > th:first-child,
  .panel > .table-bordered > thead > tr > td:first-child,
  .panel > .table-bordered > tbody > tr > th:first-child,
  .panel > .table-bordered > tbody > tr > td:first-child,
  .panel > .table-bordered > tfoot > tr > th:first-child,
  .panel > .table-bordered > tfoot > tr > td:first-child,
  .panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
  .panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0; }
  .panel > .table-bordered > thead > tr > th:last-child,
  .panel > .table-bordered > thead > tr > td:last-child,
  .panel > .table-bordered > tbody > tr > th:last-child,
  .panel > .table-bordered > tbody > tr > td:last-child,
  .panel > .table-bordered > tfoot > tr > th:last-child,
  .panel > .table-bordered > tfoot > tr > td:last-child,
  .panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
  .panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0; }
  .panel > .table-bordered > thead > tr:first-child > td,
  .panel > .table-bordered > thead > tr:first-child > th,
  .panel > .table-bordered > tbody > tr:first-child > td,
  .panel > .table-bordered > tbody > tr:first-child > th,
  .panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
  .panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
  .panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
  .panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
    border-bottom: 0; }
  .panel > .table-bordered > tbody > tr:last-child > td,
  .panel > .table-bordered > tbody > tr:last-child > th,
  .panel > .table-bordered > tfoot > tr:last-child > td,
  .panel > .table-bordered > tfoot > tr:last-child > th,
  .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
  .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
    border-bottom: 0; }
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0; }

.panel-group {
  margin-bottom: 20px; }
  .panel-group .panel {
    margin-bottom: 0;
    border-radius: 4px; }
    .panel-group .panel + .panel {
      margin-top: 5px; }
  .panel-group .panel-heading {
    border-bottom: 0; }
    .panel-group .panel-heading + .panel-collapse > .panel-body,
    .panel-group .panel-heading + .panel-collapse > .list-group {
      border-top: 1px solid #ddd; }
  .panel-group .panel-footer {
    border-top: 0; }
    .panel-group .panel-footer + .panel-collapse .panel-body {
      border-bottom: 1px solid #ddd; }

.panel-default {
  border-color: #ddd; }
  .panel-default > .panel-heading {
    color: #333333;
    background-color: #f5f5f5;
    border-color: #ddd; }
    .panel-default > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ddd; }
    .panel-default > .panel-heading .badge {
      color: #f5f5f5;
      background-color: #333333; }
  .panel-default > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ddd; }

.panel-primary {
  border-color: #337ab7; }
  .panel-primary > .panel-heading {
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7; }
    .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #337ab7; }
    .panel-primary > .panel-heading .badge {
      color: #337ab7;
      background-color: #fff; }
  .panel-primary > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #337ab7; }

.panel-success {
  border-color: #d6e9c6; }
  .panel-success > .panel-heading {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6; }
    .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #d6e9c6; }
    .panel-success > .panel-heading .badge {
      color: #dff0d8;
      background-color: #3c763d; }
  .panel-success > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #d6e9c6; }

.panel-info {
  border-color: #bce8f1; }
  .panel-info > .panel-heading {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1; }
    .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #bce8f1; }
    .panel-info > .panel-heading .badge {
      color: #d9edf7;
      background-color: #31708f; }
  .panel-info > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #bce8f1; }

.panel-warning {
  border-color: #faebcc; }
  .panel-warning > .panel-heading {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc; }
    .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #faebcc; }
    .panel-warning > .panel-heading .badge {
      color: #fcf8e3;
      background-color: #8a6d3b; }
  .panel-warning > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #faebcc; }

.panel-danger {
  border-color: #ebccd1; }
  .panel-danger > .panel-heading {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1; }
    .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ebccd1; }
    .panel-danger > .panel-heading .badge {
      color: #f2dede;
      background-color: #a94442; }
  .panel-danger > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ebccd1; }

.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden; }
  .embed-responsive .embed-responsive-item,
  .embed-responsive iframe,
  .embed-responsive embed,
  .embed-responsive object,
  .embed-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    border: 0; }
  .embed-responsive.embed-responsive-16by9 {
    padding-bottom: 56.25%; }
  .embed-responsive.embed-responsive-4by3 {
    padding-bottom: 75%; }

.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05); }
  .well blockquote {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, 0.15); }

.well-lg {
  padding: 24px;
  border-radius: 6px; }

.well-sm {
  padding: 9px;
  border-radius: 3px; }

.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: alpha(opacity=20); }
  .close:hover, .close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: 0.5;
    filter: alpha(opacity=50); }

button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none; }

.modal-open {
  overflow: hidden; }

.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  -webkit-overflow-scrolling: touch;
  outline: 0; }
  .modal.fade .modal-dialog {
    -webkit-transform: translate(0, -25%);
    -ms-transform: translate(0, -25%);
    -o-transform: translate(0, -25%);
    transform: translate(0, -25%);
    -webkit-transition: -webkit-transform 0.3s ease-out;
    -moz-transition: -moz-transform 0.3s ease-out;
    -o-transition: -o-transform 0.3s ease-out;
    transition: transform 0.3s ease-out; }
  .modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0); }

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto; }

.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px; }

.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0; }

.modal-backdrop {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  background-color: #000; }
  .modal-backdrop.fade {
    opacity: 0;
    filter: alpha(opacity=0); }
  .modal-backdrop.in {
    opacity: 0.5;
    filter: alpha(opacity=50); }

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  min-height: 16.42857px; }

.modal-header .close {
  margin-top: -2px; }

.modal-title {
  margin: 0;
  line-height: 1.42857; }

.modal-body {
  position: relative;
  padding: 15px; }

.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5; }
  .modal-footer:before, .modal-footer:after {
    content: " ";
    display: table; }
  .modal-footer:after {
    clear: both; }
  .modal-footer .btn + .btn {
    margin-left: 5px;
    margin-bottom: 0; }
  .modal-footer .btn-group .btn + .btn {
    margin-left: -1px; }
  .modal-footer .btn-block + .btn-block {
    margin-left: 0; }

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll; }

@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto; }

  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5); }

  .modal-sm {
    width: 300px; } }
@media (min-width: 992px) {
  .modal-lg {
    width: 900px; } }
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  visibility: visible;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: normal;
  line-height: 1.4;
  opacity: 0;
  filter: alpha(opacity=0); }
  .tooltip.in {
    opacity: 0.9;
    filter: alpha(opacity=90); }
  .tooltip.top {
    margin-top: -3px;
    padding: 5px 0; }
  .tooltip.right {
    margin-left: 3px;
    padding: 0 5px; }
  .tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0; }
  .tooltip.left {
    margin-left: -3px;
    padding: 0 5px; }

.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  text-decoration: none;
  background-color: #000;
  border-radius: 4px; }

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid; }

.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  right: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000; }
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000; }
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }

.popover {
  position: absolute;
  top: 44px;
  right: 0;
  width: 250px;
  z-index: 1;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857;
  text-align: left;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  white-space: normal; }
  .popover.top {
    margin-top: -10px; }
  .popover.right {
    margin-left: 10px; }
  .popover.bottom {
    margin-top: 10px; }
  .popover.left {
    margin-left: -10px; }

.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0; }

.popover-content {
  padding: 9px 14px; }

.popover > .arrow, .popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid; }

.popover > .arrow {
  border-width: 11px; }

.popover > .arrow:after {
  border-width: 10px;
  content: ""; }

.popover.top > .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px; }
  .popover.top > .arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #fff; }
.popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25); }
  .popover.right > .arrow:after {
    content: " ";
    left: 1px;
    bottom: -10px;
    border-left-width: 0;
    border-right-color: #fff; }
.popover.bottom > .arrow {
  right: 16%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px; }
  .popover.bottom > .arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #fff; }
.popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25); }
  .popover.left > .arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-color: #fff;
    bottom: -10px; }

.carousel {
  position: relative; }

.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%; }
  .carousel-inner > .item {
    display: none;
    position: relative;
    -webkit-transition: 0.6s ease-in-out left;
    -o-transition: 0.6s ease-in-out left;
    transition: 0.6s ease-in-out left; }
    .carousel-inner > .item > img,
    .carousel-inner > .item > a > img {
      display: block;
      max-width: 100%;
      height: auto;
      line-height: 1; }
    @media all and (transform-3d), (-webkit-transform-3d) {
      .carousel-inner > .item {
        -webkit-transition: -webkit-transform 0.6s ease-in-out;
        -moz-transition: -moz-transform 0.6s ease-in-out;
        -o-transition: -o-transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out;
        -webkit-backface-visibility: hidden;
        -moz-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000;
        -moz-perspective: 1000;
        perspective: 1000; }
        .carousel-inner > .item.next, .carousel-inner > .item.active.right {
          -webkit-transform: translate3d(100%, 0, 0);
          transform: translate3d(100%, 0, 0);
          left: 0; }
        .carousel-inner > .item.prev, .carousel-inner > .item.active.left {
          -webkit-transform: translate3d(-100%, 0, 0);
          transform: translate3d(-100%, 0, 0);
          left: 0; }
        .carousel-inner > .item.next.left, .carousel-inner > .item.prev.right, .carousel-inner > .item.active {
          -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
          left: 0; } }
  .carousel-inner > .active,
  .carousel-inner > .next,
  .carousel-inner > .prev {
    display: block; }
  .carousel-inner > .active {
    left: 0; }
  .carousel-inner > .next,
  .carousel-inner > .prev {
    position: absolute;
    top: 0;
    width: 100%; }
  .carousel-inner > .next {
    left: 100%; }
  .carousel-inner > .prev {
    left: -100%; }
  .carousel-inner > .next.left,
  .carousel-inner > .prev.right {
    left: 0; }
  .carousel-inner > .active.left {
    left: -100%; }
  .carousel-inner > .active.right {
    left: 100%; }

.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6); }
  .carousel-control.left {
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1); }
  .carousel-control.right {
    left: auto;
    right: 0;
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1); }
  .carousel-control:hover, .carousel-control:focus {
    outline: 0;
    color: #fff;
    text-decoration: none;
    opacity: 0.9;
    filter: alpha(opacity=90); }
  .carousel-control .icon-prev,
  .carousel-control .icon-next,
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right {
    position: absolute;
    top: 50%;
    z-index: 5;
    display: inline-block; }
  .carousel-control .icon-prev,
  .carousel-control .glyphicon-chevron-left {
    left: 50%;
    margin-left: -10px; }
  .carousel-control .icon-next,
  .carousel-control .glyphicon-chevron-right {
    right: 50%;
    margin-right: -10px; }
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 20px;
    height: 20px;
    margin-top: -10px;
    line-height: 1;
    font-family: serif; }
  .carousel-control .icon-prev:before {
    content: '\2039'; }
  .carousel-control .icon-next:before {
    content: '\203a'; }

.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center; }
  .carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #fff;
    border-radius: 10px;
    cursor: pointer;
    background-color: #000 \9;
    background-color: transparent; }
  .carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #fff; }

.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6); }
  .carousel-caption .btn {
    text-shadow: none; }

@media screen and (min-width: 768px) {
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 30px; }
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .icon-prev {
    margin-left: -15px; }
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next {
    margin-right: -15px; }

  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px; }

  .carousel-indicators {
    bottom: 20px; } }
.clearfix:before, .clearfix:after {
  content: " ";
  display: table; }
.clearfix:after {
  clear: both; }

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto; }

.pull-right {
  float: right !important; }

.pull-left {
  float: left !important; }

.hide {
  display: none !important; }

.show {
  display: block !important; }

.invisible {
  visibility: hidden; }

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0; }

.hidden {
  display: none !important;
  visibility: hidden !important; }

.affix {
  position: fixed; }

@-ms-viewport {
  width: device-width; }
.visible-xs {
  display: none !important; }

.visible-sm {
  display: none !important; }

.visible-md {
  display: none !important; }

.visible-lg {
  display: none !important; }

.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
  display: none !important; }

@media (max-width: 767px) {
  .visible-xs {
    display: block !important; }

  table.visible-xs {
    display: table; }

  tr.visible-xs {
    display: table-row !important; }

  th.visible-xs,
  td.visible-xs {
    display: table-cell !important; } }
@media (max-width: 767px) {
  .visible-xs-block {
    display: block !important; } }

@media (max-width: 767px) {
  .visible-xs-inline {
    display: inline !important; } }

@media (max-width: 767px) {
  .visible-xs-inline-block {
    display: inline-block !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important; }

  table.visible-sm {
    display: table; }

  tr.visible-sm {
    display: table-row !important; }

  th.visible-sm,
  td.visible-sm {
    display: table-cell !important; } }
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-block {
    display: block !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline {
    display: inline !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline-block {
    display: inline-block !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important; }

  table.visible-md {
    display: table; }

  tr.visible-md {
    display: table-row !important; }

  th.visible-md,
  td.visible-md {
    display: table-cell !important; } }
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-block {
    display: block !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline {
    display: inline !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline-block {
    display: inline-block !important; } }

@media (min-width: 1200px) {
  .visible-lg {
    display: block !important; }

  table.visible-lg {
    display: table; }

  tr.visible-lg {
    display: table-row !important; }

  th.visible-lg,
  td.visible-lg {
    display: table-cell !important; } }
@media (min-width: 1200px) {
  .visible-lg-block {
    display: block !important; } }

@media (min-width: 1200px) {
  .visible-lg-inline {
    display: inline !important; } }

@media (min-width: 1200px) {
  .visible-lg-inline-block {
    display: inline-block !important; } }

@media (max-width: 767px) {
  .hidden-xs {
    display: none !important; } }
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important; } }
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important; } }
@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important; } }
.visible-print {
  display: none !important; }

@media print {
  .visible-print {
    display: block !important; }

  table.visible-print {
    display: table; }

  tr.visible-print {
    display: table-row !important; }

  th.visible-print,
  td.visible-print {
    display: table-cell !important; } }
.visible-print-block {
  display: none !important; }
  @media print {
    .visible-print-block {
      display: block !important; } }

.visible-print-inline {
  display: none !important; }
  @media print {
    .visible-print-inline {
      display: inline !important; } }

.visible-print-inline-block {
  display: none !important; }
  @media print {
    .visible-print-inline-block {
      display: inline-block !important; } }

@media print {
  .hidden-print {
    display: none !important; } }
/*!
 *  Font Awesome 4.2.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
/*@import "../../libraries/scss/font-awesome/variables";
@import "../../libraries/scss/font-awesome/mixins";
@import "../../libraries/scss/font-awesome/path";
@import "../../libraries/scss/font-awesome/core";
@import "../../libraries/scss/font-awesome/larger";
@import "../../libraries/scss/font-awesome/fixed-width";
@import "../../libraries/scss/font-awesome/list";
@import "../../libraries/scss/font-awesome/bordered-pulled";
@import "../../libraries/scss/font-awesome/spinning";
@import "../../libraries/scss/font-awesome/rotated-flipped";
@import "../../libraries/scss/font-awesome/stacked";
@import "../../libraries/scss/font-awesome/icons";*/
.control-group {
  display: inline-block;
  vertical-align: top;
  background: #fff;
  text-align: left;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 30px;
  width: 200px;
  height: 210px;
  margin: 10px; }

.control {
  display: block;
  position: relative;
  padding-left: 30px;
  padding-right: 15px;
  margin-bottom: 15px;
  cursor: pointer;
  font-size: 16px;
  font-weight: normal;
  text-transform: capitalize; }

.control input {
  position: absolute;
  z-index: -1;
  opacity: 0; }

.control__indicator {
  position: absolute;
  top: 0px;
  left: 0;
  height: 20px;
  width: 20px;
  border: 1px solid #F6BB42;
  background-color: #fff; }

.control--radio .control__indicator {
  border-radius: 50%; }

.control:hover input ~ .control__indicator,
.control input:focus ~ .control__indicator {
  background: #fff; }

.control input:checked ~ .control__indicator {
  background: #F6BB42; }

.control:hover input:not([disabled]):checked ~ .control__indicator,
.control input:checked:focus ~ .control__indicator {
  background: #F6BB42; }

.control input:disabled ~ .control__indicator {
  background: #e6e6e6;
  opacity: 0.6;
  pointer-events: none; }

.control__indicator:after {
  content: '';
  position: absolute;
  display: none; }

.control input:checked ~ .control__indicator:after {
  display: block; }

.control--checkbox .control__indicator:after {
  left: 6px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg); }

.control--checkbox input:disabled ~ .control__indicator:after {
  border-color: #7b7b7b; }

.control--radio .control__indicator:after {
  left: 6px;
  top: 6px;
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background: #fff; }

.control--radio input:disabled ~ .control__indicator:after {
  background: #7b7b7b; }

.select {
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
  width: 100%; }

.select select {
  display: inline-block;
  width: 100%;
  cursor: pointer;
  padding: 10px 35px 10px 10px;
  outline: 0;
  border: 0;
  border-radius: 0;
  background: #fff;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 1px solid #ddd; }

.select select::-ms-expand {
  display: none; }

.select select:hover,
.select select:focus {
  background-color: #ffffff; }

.select select:disabled {
  opacity: 0.5;
  pointer-events: none; }

.select__arrow {
  position: absolute;
  top: 16px;
  right: 15px;
  width: 0;
  height: 0;
  pointer-events: none;
  border-style: solid;
  border-width: 8px 5px 0 5px;
  border-color: #7b7b7b transparent transparent transparent; }

.select select:disabled ~ .select__arrow {
  border-top-color: #ccc; }

/**
 * Owl Carousel v2.2.0
 * Copyright 2013-2016 David Deutsch
 * Licensed under MIT (https://github.com/OwlCarousel2/OwlCarousel2/blob/master/LICENSE)
 */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative; }

.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1p;
  position: relative; }

.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y; }

.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0; }

.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0); }

.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none; }

.owl-carousel .owl-item img {
  display: block;
  width: 100%;
  -webkit-transform-style: preserve-3d; }

.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none; }

.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block; }

.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.owl-carousel.owl-loading {
  opacity: 0;
  display: block; }

.owl-carousel.owl-hidden {
  opacity: 0; }

.owl-carousel.owl-refresh .owl-item {
  visibility: hidden; }

.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab; }

.owl-carousel.owl-rtl {
  direction: rtl; }

.owl-carousel.owl-rtl .owl-item {
  float: right; }

.owl-carousel .animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both; }

.owl-carousel .owl-animated-in {
  z-index: 0; }

.owl-carousel .owl-animated-out {
  z-index: 1; }

.owl-carousel .fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut; }

@-webkit-keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
@keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
.owl-height {
  transition: height 0.5s ease-in-out; }

.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity 0.4s ease; }

.owl-carousel .owl-item img.owl-lazy {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d; }

.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000; }

.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: -webkit-transform .1s ease;
  transition: transform 0.1s ease; }

.owl-carousel .owl-video-play-icon:hover {
  -webkit-transform: scale(1.3, 1.3);
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3); }

.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none; }

.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity 0.4s ease; }

.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%; }

.owl-theme .owl-nav {
  margin-top: 10px;
  text-align: center;
  -webkit-tap-highlight-color: transparent; }

.owl-theme .owl-nav [class*='owl-'] {
  color: #FFF;
  font-size: 14px;
  margin: 5px;
  padding: 4px 7px;
  background: #D6D6D6;
  display: inline-block;
  cursor: pointer;
  border-radius: 3px; }

.owl-theme .owl-nav [class*='owl-']:hover {
  background: #869791;
  color: #FFF;
  text-decoration: none; }

.owl-theme .owl-nav .disabled {
  opacity: 0.5;
  cursor: default; }

.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px; }

.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent; }

.owl-theme .owl-dots .owl-dot {
  display: inline-block;
  zoom: 1;
  *display: inline; }

.owl-theme .owl-dots .owl-dot span {
  width: 10px;
  height: 10px;
  margin: 5px 7px;
  background: #D6D6D6;
  display: block;
  -webkit-backface-visibility: visible;
  transition: opacity 200ms ease;
  border-radius: 30px; }

.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
  background: #869791; }

@font-face {
  font-family: 'themify';
  src: url("../fonts/themify.eot?-fvbane");
  src: url("../fonts/themify.eot?#iefix-fvbane") format("embedded-opentype"), url("../fonts/themify.woff?-fvbane") format("woff"), url("../fonts/themify.ttf?-fvbane") format("truetype"), url("../fonts/themify.svg?-fvbane#themify") format("svg");
  font-weight: normal;
  font-style: normal; }
[class^="ti-"], [class*=" ti-"] {
  font-family: 'themify';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.ti-wand:before {
  content: "\e600"; }

.ti-volume:before {
  content: "\e601"; }

.ti-user:before {
  content: "\e602"; }

.ti-unlock:before {
  content: "\e603"; }

.ti-unlink:before {
  content: "\e604"; }

.ti-trash:before {
  content: "\e605"; }

.ti-thought:before {
  content: "\e606"; }

.ti-target:before {
  content: "\e607"; }

.ti-tag:before {
  content: "\e608"; }

.ti-tablet:before {
  content: "\e609"; }

.ti-star:before {
  content: "\e60a"; }

.ti-spray:before {
  content: "\e60b"; }

.ti-signal:before {
  content: "\e60c"; }

.ti-shopping-cart:before {
  content: "\e60d"; }

.ti-shopping-cart-full:before {
  content: "\e60e"; }

.ti-settings:before {
  content: "\e60f"; }

.ti-search:before {
  content: "\e610"; }

.ti-zoom-in:before {
  content: "\e611"; }

.ti-zoom-out:before {
  content: "\e612"; }

.ti-cut:before {
  content: "\e613"; }

.ti-ruler:before {
  content: "\e614"; }

.ti-ruler-pencil:before {
  content: "\e615"; }

.ti-ruler-alt:before {
  content: "\e616"; }

.ti-bookmark:before {
  content: "\e617"; }

.ti-bookmark-alt:before {
  content: "\e618"; }

.ti-reload:before {
  content: "\e619"; }

.ti-plus:before {
  content: "\e61a"; }

.ti-pin:before {
  content: "\e61b"; }

.ti-pencil:before {
  content: "\e61c"; }

.ti-pencil-alt:before {
  content: "\e61d"; }

.ti-paint-roller:before {
  content: "\e61e"; }

.ti-paint-bucket:before {
  content: "\e61f"; }

.ti-na:before {
  content: "\e620"; }

.ti-mobile:before {
  content: "\e621"; }

.ti-minus:before {
  content: "\e622"; }

.ti-medall:before {
  content: "\e623"; }

.ti-medall-alt:before {
  content: "\e624"; }

.ti-marker:before {
  content: "\e625"; }

.ti-marker-alt:before {
  content: "\e626"; }

.ti-arrow-up:before {
  content: "\e627"; }

.ti-arrow-right:before {
  content: "\e628"; }

.ti-arrow-left:before {
  content: "\e629"; }

.ti-arrow-down:before {
  content: "\e62a"; }

.ti-lock:before {
  content: "\e62b"; }

.ti-location-arrow:before {
  content: "\e62c"; }

.ti-link:before {
  content: "\e62d"; }

.ti-layout:before {
  content: "\e62e"; }

.ti-layers:before {
  content: "\e62f"; }

.ti-layers-alt:before {
  content: "\e630"; }

.ti-key:before {
  content: "\e631"; }

.ti-import:before {
  content: "\e632"; }

.ti-image:before {
  content: "\e633"; }

.ti-heart:before {
  content: "\e634"; }

.ti-heart-broken:before {
  content: "\e635"; }

.ti-hand-stop:before {
  content: "\e636"; }

.ti-hand-open:before {
  content: "\e637"; }

.ti-hand-drag:before {
  content: "\e638"; }

.ti-folder:before {
  content: "\e639"; }

.ti-flag:before {
  content: "\e63a"; }

.ti-flag-alt:before {
  content: "\e63b"; }

.ti-flag-alt-2:before {
  content: "\e63c"; }

.ti-eye:before {
  content: "\e63d"; }

.ti-export:before {
  content: "\e63e"; }

.ti-exchange-vertical:before {
  content: "\e63f"; }

.ti-desktop:before {
  content: "\e640"; }

.ti-cup:before {
  content: "\e641"; }

.ti-crown:before {
  content: "\e642"; }

.ti-comments:before {
  content: "\e643"; }

.ti-comment:before {
  content: "\e644"; }

.ti-comment-alt:before {
  content: "\e645"; }

.ti-close:before {
  content: "\e646"; }

.ti-clip:before {
  content: "\e647"; }

.ti-angle-up:before {
  content: "\e648"; }

.ti-angle-right:before {
  content: "\e649"; }

.ti-angle-left:before {
  content: "\e64a"; }

.ti-angle-down:before {
  content: "\e64b"; }

.ti-check:before {
  content: "\e64c"; }

.ti-check-box:before {
  content: "\e64d"; }

.ti-camera:before {
  content: "\e64e"; }

.ti-announcement:before {
  content: "\e64f"; }

.ti-brush:before {
  content: "\e650"; }

.ti-briefcase:before {
  content: "\e651"; }

.ti-bolt:before {
  content: "\e652"; }

.ti-bolt-alt:before {
  content: "\e653"; }

.ti-blackboard:before {
  content: "\e654"; }

.ti-bag:before {
  content: "\e655"; }

.ti-move:before {
  content: "\e656"; }

.ti-arrows-vertical:before {
  content: "\e657"; }

.ti-arrows-horizontal:before {
  content: "\e658"; }

.ti-fullscreen:before {
  content: "\e659"; }

.ti-arrow-top-right:before {
  content: "\e65a"; }

.ti-arrow-top-left:before {
  content: "\e65b"; }

.ti-arrow-circle-up:before {
  content: "\e65c"; }

.ti-arrow-circle-right:before {
  content: "\e65d"; }

.ti-arrow-circle-left:before {
  content: "\e65e"; }

.ti-arrow-circle-down:before {
  content: "\e65f"; }

.ti-angle-double-up:before {
  content: "\e660"; }

.ti-angle-double-right:before {
  content: "\e661"; }

.ti-angle-double-left:before {
  content: "\e662"; }

.ti-angle-double-down:before {
  content: "\e663"; }

.ti-zip:before {
  content: "\e664"; }

.ti-world:before {
  content: "\e665"; }

.ti-wheelchair:before {
  content: "\e666"; }

.ti-view-list:before {
  content: "\e667"; }

.ti-view-list-alt:before {
  content: "\e668"; }

.ti-view-grid:before {
  content: "\e669"; }

.ti-uppercase:before {
  content: "\e66a"; }

.ti-upload:before {
  content: "\e66b"; }

.ti-underline:before {
  content: "\e66c"; }

.ti-truck:before {
  content: "\e66d"; }

.ti-timer:before {
  content: "\e66e"; }

.ti-ticket:before {
  content: "\e66f"; }

.ti-thumb-up:before {
  content: "\e670"; }

.ti-thumb-down:before {
  content: "\e671"; }

.ti-text:before {
  content: "\e672"; }

.ti-stats-up:before {
  content: "\e673"; }

.ti-stats-down:before {
  content: "\e674"; }

.ti-split-v:before {
  content: "\e675"; }

.ti-split-h:before {
  content: "\e676"; }

.ti-smallcap:before {
  content: "\e677"; }

.ti-shine:before {
  content: "\e678"; }

.ti-shift-right:before {
  content: "\e679"; }

.ti-shift-left:before {
  content: "\e67a"; }

.ti-shield:before {
  content: "\e67b"; }

.ti-notepad:before {
  content: "\e67c"; }

.ti-server:before {
  content: "\e67d"; }

.ti-quote-right:before {
  content: "\e67e"; }

.ti-quote-left:before {
  content: "\e67f"; }

.ti-pulse:before {
  content: "\e680"; }

.ti-printer:before {
  content: "\e681"; }

.ti-power-off:before {
  content: "\e682"; }

.ti-plug:before {
  content: "\e683"; }

.ti-pie-chart:before {
  content: "\e684"; }

.ti-paragraph:before {
  content: "\e685"; }

.ti-panel:before {
  content: "\e686"; }

.ti-package:before {
  content: "\e687"; }

.ti-music:before {
  content: "\e688"; }

.ti-music-alt:before {
  content: "\e689"; }

.ti-mouse:before {
  content: "\e68a"; }

.ti-mouse-alt:before {
  content: "\e68b"; }

.ti-money:before {
  content: "\e68c"; }

.ti-microphone:before {
  content: "\e68d"; }

.ti-menu:before {
  content: "\e68e"; }

.ti-menu-alt:before {
  content: "\e68f"; }

.ti-map:before {
  content: "\e690"; }

.ti-map-alt:before {
  content: "\e691"; }

.ti-loop:before {
  content: "\e692"; }

.ti-location-pin:before {
  content: "\e693"; }

.ti-list:before {
  content: "\e694"; }

.ti-light-bulb:before {
  content: "\e695"; }

.ti-Italic:before {
  content: "\e696"; }

.ti-info:before {
  content: "\e697"; }

.ti-infinite:before {
  content: "\e698"; }

.ti-id-badge:before {
  content: "\e699"; }

.ti-hummer:before {
  content: "\e69a"; }

.ti-home:before {
  content: "\e69b"; }

.ti-help:before {
  content: "\e69c"; }

.ti-headphone:before {
  content: "\e69d"; }

.ti-harddrives:before {
  content: "\e69e"; }

.ti-harddrive:before {
  content: "\e69f"; }

.ti-gift:before {
  content: "\e6a0"; }

.ti-game:before {
  content: "\e6a1"; }

.ti-filter:before {
  content: "\e6a2"; }

.ti-files:before {
  content: "\e6a3"; }

.ti-file:before {
  content: "\e6a4"; }

.ti-eraser:before {
  content: "\e6a5"; }

.ti-envelope:before {
  content: "\e6a6"; }

.ti-download:before {
  content: "\e6a7"; }

.ti-direction:before {
  content: "\e6a8"; }

.ti-direction-alt:before {
  content: "\e6a9"; }

.ti-dashboard:before {
  content: "\e6aa"; }

.ti-control-stop:before {
  content: "\e6ab"; }

.ti-control-shuffle:before {
  content: "\e6ac"; }

.ti-control-play:before {
  content: "\e6ad"; }

.ti-control-pause:before {
  content: "\e6ae"; }

.ti-control-forward:before {
  content: "\e6af"; }

.ti-control-backward:before {
  content: "\e6b0"; }

.ti-cloud:before {
  content: "\e6b1"; }

.ti-cloud-up:before {
  content: "\e6b2"; }

.ti-cloud-down:before {
  content: "\e6b3"; }

.ti-clipboard:before {
  content: "\e6b4"; }

.ti-car:before {
  content: "\e6b5"; }

.ti-calendar:before {
  content: "\e6b6"; }

.ti-book:before {
  content: "\e6b7"; }

.ti-bell:before {
  content: "\e6b8"; }

.ti-basketball:before {
  content: "\e6b9"; }

.ti-bar-chart:before {
  content: "\e6ba"; }

.ti-bar-chart-alt:before {
  content: "\e6bb"; }

.ti-back-right:before {
  content: "\e6bc"; }

.ti-back-left:before {
  content: "\e6bd"; }

.ti-arrows-corner:before {
  content: "\e6be"; }

.ti-archive:before {
  content: "\e6bf"; }

.ti-anchor:before {
  content: "\e6c0"; }

.ti-align-right:before {
  content: "\e6c1"; }

.ti-align-left:before {
  content: "\e6c2"; }

.ti-align-justify:before {
  content: "\e6c3"; }

.ti-align-center:before {
  content: "\e6c4"; }

.ti-alert:before {
  content: "\e6c5"; }

.ti-alarm-clock:before {
  content: "\e6c6"; }

.ti-agenda:before {
  content: "\e6c7"; }

.ti-write:before {
  content: "\e6c8"; }

.ti-window:before {
  content: "\e6c9"; }

.ti-widgetized:before {
  content: "\e6ca"; }

.ti-widget:before {
  content: "\e6cb"; }

.ti-widget-alt:before {
  content: "\e6cc"; }

.ti-wallet:before {
  content: "\e6cd"; }

.ti-video-clapper:before {
  content: "\e6ce"; }

.ti-video-camera:before {
  content: "\e6cf"; }

.ti-vector:before {
  content: "\e6d0"; }

.ti-themify-logo:before {
  content: "\e6d1"; }

.ti-themify-favicon:before {
  content: "\e6d2"; }

.ti-themify-favicon-alt:before {
  content: "\e6d3"; }

.ti-support:before {
  content: "\e6d4"; }

.ti-stamp:before {
  content: "\e6d5"; }

.ti-split-v-alt:before {
  content: "\e6d6"; }

.ti-slice:before {
  content: "\e6d7"; }

.ti-shortcode:before {
  content: "\e6d8"; }

.ti-shift-right-alt:before {
  content: "\e6d9"; }

.ti-shift-left-alt:before {
  content: "\e6da"; }

.ti-ruler-alt-2:before {
  content: "\e6db"; }

.ti-receipt:before {
  content: "\e6dc"; }

.ti-pin2:before {
  content: "\e6dd"; }

.ti-pin-alt:before {
  content: "\e6de"; }

.ti-pencil-alt2:before {
  content: "\e6df"; }

.ti-palette:before {
  content: "\e6e0"; }

.ti-more:before {
  content: "\e6e1"; }

.ti-more-alt:before {
  content: "\e6e2"; }

.ti-microphone-alt:before {
  content: "\e6e3"; }

.ti-magnet:before {
  content: "\e6e4"; }

.ti-line-double:before {
  content: "\e6e5"; }

.ti-line-dotted:before {
  content: "\e6e6"; }

.ti-line-dashed:before {
  content: "\e6e7"; }

.ti-layout-width-full:before {
  content: "\e6e8"; }

.ti-layout-width-default:before {
  content: "\e6e9"; }

.ti-layout-width-default-alt:before {
  content: "\e6ea"; }

.ti-layout-tab:before {
  content: "\e6eb"; }

.ti-layout-tab-window:before {
  content: "\e6ec"; }

.ti-layout-tab-v:before {
  content: "\e6ed"; }

.ti-layout-tab-min:before {
  content: "\e6ee"; }

.ti-layout-slider:before {
  content: "\e6ef"; }

.ti-layout-slider-alt:before {
  content: "\e6f0"; }

.ti-layout-sidebar-right:before {
  content: "\e6f1"; }

.ti-layout-sidebar-none:before {
  content: "\e6f2"; }

.ti-layout-sidebar-left:before {
  content: "\e6f3"; }

.ti-layout-placeholder:before {
  content: "\e6f4"; }

.ti-layout-menu:before {
  content: "\e6f5"; }

.ti-layout-menu-v:before {
  content: "\e6f6"; }

.ti-layout-menu-separated:before {
  content: "\e6f7"; }

.ti-layout-menu-full:before {
  content: "\e6f8"; }

.ti-layout-media-right-alt:before {
  content: "\e6f9"; }

.ti-layout-media-right:before {
  content: "\e6fa"; }

.ti-layout-media-overlay:before {
  content: "\e6fb"; }

.ti-layout-media-overlay-alt:before {
  content: "\e6fc"; }

.ti-layout-media-overlay-alt-2:before {
  content: "\e6fd"; }

.ti-layout-media-left-alt:before {
  content: "\e6fe"; }

.ti-layout-media-left:before {
  content: "\e6ff"; }

.ti-layout-media-center-alt:before {
  content: "\e700"; }

.ti-layout-media-center:before {
  content: "\e701"; }

.ti-layout-list-thumb:before {
  content: "\e702"; }

.ti-layout-list-thumb-alt:before {
  content: "\e703"; }

.ti-layout-list-post:before {
  content: "\e704"; }

.ti-layout-list-large-image:before {
  content: "\e705"; }

.ti-layout-line-solid:before {
  content: "\e706"; }

.ti-layout-grid4:before {
  content: "\e707"; }

.ti-layout-grid3:before {
  content: "\e708"; }

.ti-layout-grid2:before {
  content: "\e709"; }

.ti-layout-grid2-thumb:before {
  content: "\e70a"; }

.ti-layout-cta-right:before {
  content: "\e70b"; }

.ti-layout-cta-left:before {
  content: "\e70c"; }

.ti-layout-cta-center:before {
  content: "\e70d"; }

.ti-layout-cta-btn-right:before {
  content: "\e70e"; }

.ti-layout-cta-btn-left:before {
  content: "\e70f"; }

.ti-layout-column4:before {
  content: "\e710"; }

.ti-layout-column3:before {
  content: "\e711"; }

.ti-layout-column2:before {
  content: "\e712"; }

.ti-layout-accordion-separated:before {
  content: "\e713"; }

.ti-layout-accordion-merged:before {
  content: "\e714"; }

.ti-layout-accordion-list:before {
  content: "\e715"; }

.ti-ink-pen:before {
  content: "\e716"; }

.ti-info-alt:before {
  content: "\e717"; }

.ti-help-alt:before {
  content: "\e718"; }

.ti-headphone-alt:before {
  content: "\e719"; }

.ti-hand-point-up:before {
  content: "\e71a"; }

.ti-hand-point-right:before {
  content: "\e71b"; }

.ti-hand-point-left:before {
  content: "\e71c"; }

.ti-hand-point-down:before {
  content: "\e71d"; }

.ti-gallery:before {
  content: "\e71e"; }

.ti-face-smile:before {
  content: "\e71f"; }

.ti-face-sad:before {
  content: "\e720"; }

.ti-credit-card:before {
  content: "\e721"; }

.ti-control-skip-forward:before {
  content: "\e722"; }

.ti-control-skip-backward:before {
  content: "\e723"; }

.ti-control-record:before {
  content: "\e724"; }

.ti-control-eject:before {
  content: "\e725"; }

.ti-comments-smiley:before {
  content: "\e726"; }

.ti-brush-alt:before {
  content: "\e727"; }

.ti-youtube:before {
  content: "\e728"; }

.ti-vimeo:before {
  content: "\e729"; }

.ti-twitter:before {
  content: "\e72a"; }

.ti-time:before {
  content: "\e72b"; }

.ti-tumblr:before {
  content: "\e72c"; }

.ti-skype:before {
  content: "\e72d"; }

.ti-share:before {
  content: "\e72e"; }

.ti-share-alt:before {
  content: "\e72f"; }

.ti-rocket:before {
  content: "\e730"; }

.ti-pinterest:before {
  content: "\e731"; }

.ti-new-window:before {
  content: "\e732"; }

.ti-microsoft:before {
  content: "\e733"; }

.ti-list-ol:before {
  content: "\e734"; }

.ti-linkedin:before {
  content: "\e735"; }

.ti-layout-sidebar-2:before {
  content: "\e736"; }

.ti-layout-grid4-alt:before {
  content: "\e737"; }

.ti-layout-grid3-alt:before {
  content: "\e738"; }

.ti-layout-grid2-alt:before {
  content: "\e739"; }

.ti-layout-column4-alt:before {
  content: "\e73a"; }

.ti-layout-column3-alt:before {
  content: "\e73b"; }

.ti-layout-column2-alt:before {
  content: "\e73c"; }

.ti-instagram:before {
  content: "\e73d"; }

.ti-google:before {
  content: "\e73e"; }

.ti-github:before {
  content: "\e73f"; }

.ti-flickr:before {
  content: "\e740"; }

.ti-facebook:before {
  content: "\e741"; }

.ti-dropbox:before {
  content: "\e742"; }

.ti-dribbble:before {
  content: "\e743"; }

.ti-apple:before {
  content: "\e744"; }

.ti-android:before {
  content: "\e745"; }

.ti-save:before {
  content: "\e746"; }

.ti-save-alt:before {
  content: "\e747"; }

.ti-yahoo:before {
  content: "\e748"; }

.ti-wordpress:before {
  content: "\e749"; }

.ti-vimeo-alt:before {
  content: "\e74a"; }

.ti-twitter-alt:before {
  content: "\e74b"; }

.ti-tumblr-alt:before {
  content: "\e74c"; }

.ti-trello:before {
  content: "\e74d"; }

.ti-stack-overflow:before {
  content: "\e74e"; }

.ti-soundcloud:before {
  content: "\e74f"; }

.ti-sharethis:before {
  content: "\e750"; }

.ti-sharethis-alt:before {
  content: "\e751"; }

.ti-reddit:before {
  content: "\e752"; }

.ti-pinterest-alt:before {
  content: "\e753"; }

.ti-microsoft-alt:before {
  content: "\e754"; }

.ti-linux:before {
  content: "\e755"; }

.ti-jsfiddle:before {
  content: "\e756"; }

.ti-joomla:before {
  content: "\e757"; }

.ti-html5:before {
  content: "\e758"; }

.ti-flickr-alt:before {
  content: "\e759"; }

.ti-email:before {
  content: "\e75a"; }

.ti-drupal:before {
  content: "\e75b"; }

.ti-dropbox-alt:before {
  content: "\e75c"; }

.ti-css3:before {
  content: "\e75d"; }

.ti-rss:before {
  content: "\e75e"; }

.ti-rss-alt:before {
  content: "\e75f"; }

/*
* FooTable v3 - FooTable is a jQuery plugin that aims to make HTML tables on smaller devices look awesome.
* @version 3.1.4
* @link http://fooplugins.com
* @copyright Steven Usher & Brad Vincent 2015
* @license Released under the GPLv3 license.
*/
table.footable,
table.footable-details {
  position: relative;
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  background-color: #ffffff; }

table.footable-details {
  margin-bottom: 0; }

table.footable-hide-fouc {
  display: none; }

table > tbody > tr > td > span.footable-toggle {
  margin-right: 8px; }

table > tbody > tr > td > span.footable-toggle.last-column {
  margin-left: 8px;
  float: right; }

table.table-condensed > tbody > tr > td > span.footable-toggle {
  margin-right: 5px; }

table.footable-details > tbody > tr > th:nth-child(1) {
  min-width: 40px;
  width: 120px; }

table.footable-details > tbody > tr > td:nth-child(2) {
  word-break: break-all; }

table.footable-details > thead > tr:first-child > th,
table.footable-details > thead > tr:first-child > td,
table.footable-details > tbody > tr:first-child > th,
table.footable-details > tbody > tr:first-child > td,
table.footable-details > tfoot > tr:first-child > th,
table.footable-details > tfoot > tr:first-child > td {
  border-top-width: 0; }

table.footable-details.table-bordered > thead > tr:first-child > th,
table.footable-details.table-bordered > thead > tr:first-child > td,
table.footable-details.table-bordered > tbody > tr:first-child > th,
table.footable-details.table-bordered > tbody > tr:first-child > td,
table.footable-details.table-bordered > tfoot > tr:first-child > th,
table.footable-details.table-bordered > tfoot > tr:first-child > td {
  border-top-width: 1px; }

div.footable-loader {
  vertical-align: middle;
  text-align: center;
  height: 300px;
  position: relative; }

div.footable-loader > span.fooicon {
  display: inline-block;
  opacity: 0.3;
  font-size: 30px;
  line-height: 32px;
  width: 32px;
  height: 32px;
  margin-top: -16px;
  margin-left: -16px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-animation: fooicon-spin-r 2s infinite linear;
  animation: fooicon-spin-r 2s infinite linear; }

table.footable > tbody > tr.footable-empty > td {
  vertical-align: middle;
  text-align: center;
  font-size: 30px; }

table.footable > tbody > tr > td,
table.footable > tbody > tr > th {
  display: none; }

table.footable > tbody > tr.footable-empty > td,
table.footable > tbody > tr.footable-empty > th,
table.footable > tbody > tr.footable-detail-row > td,
table.footable > tbody > tr.footable-detail-row > th {
  display: table-cell; }

@-webkit-keyframes fooicon-spin-r {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg); } }
@keyframes fooicon-spin-r {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg); } }
.fooicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings' !important;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.fooicon:before,
.fooicon:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.fooicon-loader:before {
  content: "\e030"; }

.fooicon-plus:before {
  content: "\2b"; }

.fooicon-minus:before {
  content: "\2212"; }

.fooicon-search:before {
  content: "\e003"; }

.fooicon-remove:before {
  content: "\e014"; }

.fooicon-sort:before {
  content: "\e150"; }

.fooicon-sort-asc:before {
  content: "\e155"; }

.fooicon-sort-desc:before {
  content: "\e156"; }

.fooicon-pencil:before {
  content: "\270f"; }

.fooicon-trash:before {
  content: "\e020"; }

.fooicon-eye-close:before {
  content: "\e106"; }

.fooicon-flash:before {
  content: "\e162"; }

.fooicon-cog:before {
  content: "\e019"; }

.fooicon-stats:before {
  content: "\e185"; }

table.footable > thead > tr.footable-filtering > th {
  border-bottom-width: 1px;
  font-weight: normal; }

table.footable > thead > tr.footable-filtering > th,
table.footable.footable-filtering-right > thead > tr.footable-filtering > th {
  text-align: right; }

table.footable.footable-filtering-left > thead > tr.footable-filtering > th {
  text-align: left; }

table.footable.footable-filtering-center > thead > tr.footable-filtering > th {
  text-align: center; }

table.footable > thead > tr.footable-filtering > th div.form-group {
  margin-bottom: 0; }

table.footable > thead > tr.footable-filtering > th div.form-group + div.form-group {
  margin-top: 5px; }

table.footable > thead > tr.footable-filtering > th div.input-group {
  width: 100%; }

table.footable > thead > tr.footable-filtering > th ul.dropdown-menu > li > a.checkbox {
  margin: 0;
  display: block;
  position: relative; }

table.footable > thead > tr.footable-filtering > th ul.dropdown-menu > li > a.checkbox > label {
  display: block;
  padding-left: 20px; }

table.footable > thead > tr.footable-filtering > th ul.dropdown-menu > li > a.checkbox input[type="checkbox"] {
  position: absolute;
  margin-left: -20px; }

@media (min-width: 768px) {
  table.footable > thead > tr.footable-filtering > th div.input-group {
    width: auto; }

  table.footable > thead > tr.footable-filtering > th div.form-group {
    margin-left: 2px;
    margin-right: 2px; }

  table.footable > thead > tr.footable-filtering > th div.form-group + div.form-group {
    margin-top: 0; } }
table.footable > thead > tr > td.footable-sortable,
table.footable > thead > tr > th.footable-sortable,
table.footable > tbody > tr > td.footable-sortable,
table.footable > tbody > tr > th.footable-sortable,
table.footable > tfoot > tr > td.footable-sortable,
table.footable > tfoot > tr > th.footable-sortable {
  position: relative;
  padding-right: 30px;
  cursor: pointer; }

td.footable-sortable > span.fooicon,
th.footable-sortable > span.fooicon {
  position: absolute;
  right: 6px;
  top: 50%;
  margin-top: -7px;
  opacity: 0;
  transition: opacity 0.3s ease-in; }

td.footable-sortable:hover > span.fooicon,
th.footable-sortable:hover > span.fooicon {
  opacity: 1; }

td.footable-sortable.footable-asc > span.fooicon,
th.footable-sortable.footable-asc > span.fooicon,
td.footable-sortable.footable-desc > span.fooicon,
th.footable-sortable.footable-desc > span.fooicon {
  opacity: 1; }

/* hides the sort icons when sorting is not allowed */
table.footable-sorting-disabled td.footable-sortable.footable-asc > span.fooicon,
table.footable-sorting-disabled td.footable-sortable.footable-desc > span.fooicon,
table.footable-sorting-disabled td.footable-sortable:hover > span.fooicon,
table.footable-sorting-disabled th.footable-sortable.footable-asc > span.fooicon,
table.footable-sorting-disabled th.footable-sortable.footable-desc > span.fooicon,
table.footable-sorting-disabled th.footable-sortable:hover > span.fooicon {
  opacity: 0;
  visibility: hidden; }

table.footable > tfoot > tr.footable-paging > td > ul.pagination {
  margin: 10px 0 0 0; }

table.footable > tfoot > tr.footable-paging > td > span.label {
  display: inline-block;
  margin: 0 0 10px 0;
  padding: 4px 10px; }

table.footable > tfoot > tr.footable-paging > td,
table.footable-paging-center > tfoot > tr.footable-paging > td {
  text-align: center; }

table.footable-paging-left > tfoot > tr.footable-paging > td {
  text-align: left; }

table.footable-paging-right > tfoot > tr.footable-paging > td {
  text-align: right; }

ul.pagination > li.footable-page {
  display: none; }

ul.pagination > li.footable-page.visible {
  display: inline; }

td.footable-editing {
  width: 90px;
  max-width: 90px; }

table.footable-editing-no-edit td.footable-editing,
table.footable-editing-no-delete td.footable-editing,
table.footable-editing-no-view td.footable-editing {
  width: 70px;
  max-width: 70px; }

table.footable-editing-no-edit.footable-editing-no-delete td.footable-editing,
table.footable-editing-no-edit.footable-editing-no-view td.footable-editing,
table.footable-editing-no-delete.footable-editing-no-view td.footable-editing {
  width: 50px;
  max-width: 50px; }

table.footable-editing-no-edit.footable-editing-no-delete.footable-editing-no-view td.footable-editing,
table.footable-editing-no-edit.footable-editing-no-delete.footable-editing-no-view th.footable-editing {
  width: 0;
  max-width: 0;
  display: none !important; }

table.footable-editing-right td.footable-editing,
table.footable-editing-right tr.footable-editing {
  text-align: right; }

table.footable-editing-left td.footable-editing,
table.footable-editing-left tr.footable-editing {
  text-align: left; }

table.footable-editing button.footable-add,
table.footable-editing button.footable-hide,
table.footable-editing-show button.footable-show,
table.footable-editing.footable-editing-always-show button.footable-show,
table.footable-editing.footable-editing-always-show button.footable-hide,
table.footable-editing.footable-editing-always-show.footable-editing-no-add tr.footable-editing {
  display: none; }

table.footable-editing.footable-editing-show button.footable-add,
table.footable-editing.footable-editing-show button.footable-hide,
table.footable-editing.footable-editing-always-show button.footable-add {
  display: inline-block; }

/*! jQuery UI - v1.12.1 - 2017-01-22
* http://jqueryui.com
* Includes: draggable.css, core.css, resizable.css, selectable.css, sortable.css, accordion.css, autocomplete.css, menu.css, button.css, controlgroup.css, checkboxradio.css, datepicker.css, dialog.css, progressbar.css, selectmenu.css, slider.css, spinner.css, tabs.css, tooltip.css, theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?scope=&folderName=base&cornerRadiusShadow=8px&offsetLeftShadow=0px&offsetTopShadow=0px&thicknessShadow=5px&opacityShadow=30&bgImgOpacityShadow=0&bgTextureShadow=flat&bgColorShadow=666666&opacityOverlay=30&bgImgOpacityOverlay=0&bgTextureOverlay=flat&bgColorOverlay=aaaaaa&iconColorError=cc0000&fcError=5f3f3f&borderColorError=f1a899&bgTextureError=flat&bgColorError=fddfdf&iconColorHighlight=777620&fcHighlight=777620&borderColorHighlight=dad55e&bgTextureHighlight=flat&bgColorHighlight=fffa90&iconColorActive=ffffff&fcActive=ffffff&borderColorActive=003eff&bgTextureActive=flat&bgColorActive=007fff&iconColorHover=555555&fcHover=2b2b2b&borderColorHover=cccccc&bgTextureHover=flat&bgColorHover=ededed&iconColorDefault=777777&fcDefault=454545&borderColorDefault=c5c5c5&bgTextureDefault=flat&bgColorDefault=f6f6f6&iconColorContent=444444&fcContent=333333&borderColorContent=dddddd&bgTextureContent=flat&bgColorContent=ffffff&iconColorHeader=444444&fcHeader=333333&borderColorHeader=dddddd&bgTextureHeader=flat&bgColorHeader=e9e9e9&cornerRadius=3px&fwDefault=normal&fsDefault=1em&ffDefault=Arial%2CHelvetica%2Csans-serif
* Copyright jQuery Foundation and other contributors; Licensed MIT */
.ui-draggable-handle {
  -ms-touch-action: none;
  touch-action: none; }

/* Layout helpers
----------------------------------*/
.ui-helper-hidden {
  display: none; }

.ui-helper-hidden-accessible {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px; }

.ui-helper-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  line-height: 1.3;
  text-decoration: none;
  font-size: 100%;
  list-style: none; }

.ui-helper-clearfix:before,
.ui-helper-clearfix:after {
  content: "";
  display: table;
  border-collapse: collapse; }

.ui-helper-clearfix:after {
  clear: both; }

.ui-helper-zfix {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  opacity: 0;
  filter: Alpha(Opacity=0);
  /* support: IE8 */ }

.ui-front {
  z-index: 100; }

/* Interaction Cues
----------------------------------*/
.ui-state-disabled {
  cursor: default !important;
  pointer-events: none; }

/* Icons
----------------------------------*/
.ui-icon {
  display: inline-block;
  vertical-align: middle;
  margin-top: -.25em;
  position: relative;
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat; }

.ui-widget-icon-block {
  left: 50%;
  margin-left: -8px;
  display: block; }

/* Misc visuals
----------------------------------*/
/* Overlays */
.ui-widget-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

.ui-resizable {
  position: relative; }

.ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none; }

.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
  display: none; }

.ui-resizable-n {
  cursor: n-resize;
  height: 7px;
  width: 100%;
  top: -5px;
  left: 0; }

.ui-resizable-s {
  cursor: s-resize;
  height: 7px;
  width: 100%;
  bottom: -5px;
  left: 0; }

.ui-resizable-e {
  cursor: e-resize;
  width: 7px;
  right: -5px;
  top: 0;
  height: 100%; }

.ui-resizable-w {
  cursor: w-resize;
  width: 7px;
  left: -5px;
  top: 0;
  height: 100%; }

.ui-resizable-se {
  cursor: se-resize;
  width: 12px;
  height: 12px;
  right: 1px;
  bottom: 1px; }

.ui-resizable-sw {
  cursor: sw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  bottom: -5px; }

.ui-resizable-nw {
  cursor: nw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  top: -5px; }

.ui-resizable-ne {
  cursor: ne-resize;
  width: 9px;
  height: 9px;
  right: -5px;
  top: -5px; }

.ui-selectable {
  -ms-touch-action: none;
  touch-action: none; }

.ui-selectable-helper {
  position: absolute;
  z-index: 100;
  border: 1px dotted black; }

.ui-sortable-handle {
  -ms-touch-action: none;
  touch-action: none; }

.ui-accordion .ui-accordion-header {
  display: block;
  cursor: pointer;
  position: relative;
  margin: 2px 0 0 0;
  padding: .5em .5em .5em .7em;
  font-size: 100%; }

.ui-accordion .ui-accordion-content {
  padding: 1em 2.2em;
  border-top: 0;
  overflow: auto; }

.ui-autocomplete {
  position: absolute;
  top: 0;
  left: 0;
  cursor: default; }

.ui-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: block;
  outline: 0; }

.ui-menu .ui-menu {
  position: absolute; }

.ui-menu .ui-menu-item {
  margin: 0;
  cursor: pointer;
  /* support: IE10, see #8844 */
  list-style-image: url("data:../../image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"); }

.ui-menu .ui-menu-item-wrapper {
  position: relative;
  padding: 3px 1em 3px .4em; }

.ui-menu .ui-menu-divider {
  margin: 5px 0;
  height: 0;
  font-size: 0;
  line-height: 0;
  border-width: 1px 0 0 0; }

.ui-menu .ui-state-focus,
.ui-menu .ui-state-active {
  margin: -1px; }

/* icon support */
.ui-menu-icons {
  position: relative; }

.ui-menu-icons .ui-menu-item-wrapper {
  padding-left: 2em; }

/* left-aligned */
.ui-menu .ui-icon {
  position: absolute;
  top: 0;
  bottom: 0;
  left: .2em;
  margin: auto 0; }

/* right-aligned */
.ui-menu .ui-menu-icon {
  left: auto;
  right: 0; }

.ui-button {
  padding: .4em 1em;
  display: inline-block;
  position: relative;
  line-height: normal;
  margin-right: .1em;
  cursor: pointer;
  vertical-align: middle;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Support: IE <= 11 */
  overflow: visible; }

.ui-button,
.ui-button:link,
.ui-button:visited,
.ui-button:hover,
.ui-button:active {
  text-decoration: none; }

/* to make room for the icon, a width needs to be set here */
.ui-button-icon-only {
  width: 2em;
  box-sizing: border-box;
  text-indent: -9999px;
  white-space: nowrap; }

/* no icon support for input elements */
input.ui-button.ui-button-icon-only {
  text-indent: 0; }

/* button icon element(s) */
.ui-button-icon-only .ui-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px; }

.ui-button.ui-icon-notext .ui-icon {
  padding: 0;
  width: 2.1em;
  height: 2.1em;
  text-indent: -9999px;
  white-space: nowrap; }

input.ui-button.ui-icon-notext .ui-icon {
  width: auto;
  height: auto;
  text-indent: 0;
  white-space: normal;
  padding: .4em 1em; }

/* workarounds */
/* Support: Firefox 5 - 40 */
input.ui-button::-moz-focus-inner,
button.ui-button::-moz-focus-inner {
  border: 0;
  padding: 0; }

.ui-controlgroup {
  vertical-align: middle;
  display: inline-block; }

.ui-controlgroup > .ui-controlgroup-item {
  float: left;
  margin-left: 0;
  margin-right: 0; }

.ui-controlgroup > .ui-controlgroup-item:focus,
.ui-controlgroup > .ui-controlgroup-item.ui-visual-focus {
  z-index: 9999; }

.ui-controlgroup-vertical > .ui-controlgroup-item {
  display: block;
  float: none;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  text-align: left; }

.ui-controlgroup-vertical .ui-controlgroup-item {
  box-sizing: border-box; }

.ui-controlgroup .ui-controlgroup-label {
  padding: .4em 1em; }

.ui-controlgroup .ui-controlgroup-label span {
  font-size: 80%; }

.ui-controlgroup-horizontal .ui-controlgroup-label + .ui-controlgroup-item {
  border-left: none; }

.ui-controlgroup-vertical .ui-controlgroup-label + .ui-controlgroup-item {
  border-top: none; }

.ui-controlgroup-horizontal .ui-controlgroup-label.ui-widget-content {
  border-right: none; }

.ui-controlgroup-vertical .ui-controlgroup-label.ui-widget-content {
  border-bottom: none; }

/* Spinner specific style fixes */
.ui-controlgroup-vertical .ui-spinner-input {
  /* Support: IE8 only, Android < 4.4 only */
  width: 75%;
  width: calc( 100% - 2.4em ); }

.ui-controlgroup-vertical .ui-spinner .ui-spinner-up {
  border-top-style: solid; }

.ui-checkboxradio-label .ui-icon-background {
  box-shadow: inset 1px 1px 1px #ccc;
  border-radius: .12em;
  border: none; }

.ui-checkboxradio-radio-label .ui-icon-background {
  width: 16px;
  height: 16px;
  border-radius: 1em;
  overflow: visible;
  border: none; }

.ui-checkboxradio-radio-label.ui-checkboxradio-checked .ui-icon,
.ui-checkboxradio-radio-label.ui-checkboxradio-checked:hover .ui-icon {
  background-image: none;
  width: 8px;
  height: 8px;
  border-width: 4px;
  border-style: solid; }

.ui-checkboxradio-disabled {
  pointer-events: none; }

.ui-datepicker {
  width: 17em;
  padding: .2em .2em 0;
  display: none; }

.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: .2em 0; }

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em; }

.ui-datepicker .ui-datepicker-prev-hover,
.ui-datepicker .ui-datepicker-next-hover {
  top: 1px; }

.ui-datepicker .ui-datepicker-prev {
  left: 2px; }

.ui-datepicker .ui-datepicker-next {
  right: 2px; }

.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px; }

.ui-datepicker .ui-datepicker-next-hover {
  right: 1px; }

.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px; }

.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center; }

.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0; }

.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
  width: 45%; }

.ui-datepicker table {
  width: 100%;
  font-size: .9em;
  border-collapse: collapse;
  margin: 0 0 .4em; }

.ui-datepicker th {
  padding: .7em .3em;
  text-align: center;
  font-weight: bold;
  border: 0; }

.ui-datepicker td {
  border: 0;
  padding: 1px; }

.ui-datepicker td span,
.ui-datepicker td a {
  display: block;
  padding: .2em;
  text-align: right;
  text-decoration: none; }

.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: .7em 0 0 0;
  padding: 0 .2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0; }

.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: .5em .2em .4em;
  cursor: pointer;
  padding: .2em .6em .3em .6em;
  width: auto;
  overflow: visible; }

.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
  float: left; }

/* with multiple calendars */
.ui-datepicker.ui-datepicker-multi {
  width: auto; }

.ui-datepicker-multi .ui-datepicker-group {
  float: left; }

.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto .4em; }

.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%; }

.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%; }

.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%; }

.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0; }

.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left; }

.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0; }

/* RTL support */
.ui-datepicker-rtl {
  direction: rtl; }

.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto; }

.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto; }

.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto; }

.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto; }

.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right; }

.ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left; }

.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,
.ui-datepicker-rtl .ui-datepicker-group {
  float: right; }

.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px; }

/* Icons */
.ui-datepicker .ui-icon {
  display: block;
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
  left: .5em;
  top: .3em; }

.ui-dialog {
  position: absolute;
  top: 0;
  left: 0;
  padding: .2em;
  outline: 0; }

.ui-dialog .ui-dialog-titlebar {
  padding: .4em 1em;
  position: relative; }

.ui-dialog .ui-dialog-title {
  float: left;
  margin: .1em 0;
  white-space: nowrap;
  width: 90%;
  overflow: hidden;
  text-overflow: ellipsis; }

.ui-dialog .ui-dialog-titlebar-close {
  position: absolute;
  right: .3em;
  top: 50%;
  width: 20px;
  margin: -10px 0 0 0;
  padding: 1px;
  height: 20px; }

.ui-dialog .ui-dialog-content {
  position: relative;
  border: 0;
  padding: .5em 1em;
  background: none;
  overflow: auto; }

.ui-dialog .ui-dialog-buttonpane {
  text-align: left;
  border-width: 1px 0 0 0;
  background-image: none;
  margin-top: .5em;
  padding: .3em 1em .5em .4em; }

.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
  float: right; }

.ui-dialog .ui-dialog-buttonpane button {
  margin: .5em .4em .5em 0;
  cursor: pointer; }

.ui-dialog .ui-resizable-n {
  height: 2px;
  top: 0; }

.ui-dialog .ui-resizable-e {
  width: 2px;
  right: 0; }

.ui-dialog .ui-resizable-s {
  height: 2px;
  bottom: 0; }

.ui-dialog .ui-resizable-w {
  width: 2px;
  left: 0; }

.ui-dialog .ui-resizable-se,
.ui-dialog .ui-resizable-sw,
.ui-dialog .ui-resizable-ne,
.ui-dialog .ui-resizable-nw {
  width: 7px;
  height: 7px; }

.ui-dialog .ui-resizable-se {
  right: 0;
  bottom: 0; }

.ui-dialog .ui-resizable-sw {
  left: 0;
  bottom: 0; }

.ui-dialog .ui-resizable-ne {
  right: 0;
  top: 0; }

.ui-dialog .ui-resizable-nw {
  left: 0;
  top: 0; }

.ui-draggable .ui-dialog-titlebar {
  cursor: move; }

.ui-progressbar {
  height: 2em;
  text-align: left;
  overflow: hidden; }

.ui-progressbar .ui-progressbar-value {
  margin: -1px;
  height: 100%; }

.ui-progressbar .ui-progressbar-overlay {
  background: url("data:../../image/gif;base64,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");
  height: 100%;
  filter: alpha(opacity=25);
  /* support: IE8 */
  opacity: 0.25; }

.ui-progressbar-indeterminate .ui-progressbar-value {
  background-image: none; }

.ui-selectmenu-menu {
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
  display: none; }

.ui-selectmenu-menu .ui-menu {
  overflow: auto;
  overflow-x: hidden;
  padding-bottom: 1px; }

.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
  font-size: 1em;
  font-weight: bold;
  line-height: 1.5;
  padding: 2px 0.4em;
  margin: 0.5em 0 0 0;
  height: auto;
  border: 0; }

.ui-selectmenu-open {
  display: block; }

.ui-selectmenu-text {
  display: block;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis; }

.ui-selectmenu-button.ui-button {
  text-align: left;
  white-space: nowrap;
  width: 14em; }

.ui-selectmenu-icon.ui-icon {
  float: right;
  margin-top: 0; }

.ui-slider {
  position: relative;
  text-align: left; }

.ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1.2em;
  height: 1.2em;
  cursor: default;
  -ms-touch-action: none;
  touch-action: none; }

.ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  font-size: .7em;
  display: block;
  border: 0;
  background-position: 0 0; }

/* support: IE8 - See #6727 */
.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
  filter: inherit; }

.ui-slider-horizontal {
  height: .8em; }

.ui-slider-horizontal .ui-slider-handle {
  top: -.3em;
  margin-left: -.6em; }

.ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%; }

.ui-slider-horizontal .ui-slider-range-min {
  left: 0; }

.ui-slider-horizontal .ui-slider-range-max {
  right: 0; }

.ui-slider-vertical {
  width: .8em;
  height: 100px; }

.ui-slider-vertical .ui-slider-handle {
  left: -.3em;
  margin-left: 0;
  margin-bottom: -.6em; }

.ui-slider-vertical .ui-slider-range {
  left: 0;
  width: 100%; }

.ui-slider-vertical .ui-slider-range-min {
  bottom: 0; }

.ui-slider-vertical .ui-slider-range-max {
  top: 0; }

.ui-spinner {
  position: relative;
  display: inline-block;
  overflow: hidden;
  padding: 0;
  vertical-align: middle; }

.ui-spinner-input {
  border: none;
  background: none;
  color: inherit;
  padding: .222em 0;
  margin: .2em 0;
  vertical-align: middle;
  margin-left: .4em;
  margin-right: 2em; }

.ui-spinner-button {
  width: 1.6em;
  height: 50%;
  font-size: .5em;
  padding: 0;
  margin: 0;
  text-align: center;
  position: absolute;
  cursor: default;
  display: block;
  overflow: hidden;
  right: 0; }

/* more specificity required here to override default borders */
.ui-spinner a.ui-spinner-button {
  border-top-style: none;
  border-bottom-style: none;
  border-right-style: none; }

.ui-spinner-up {
  top: 0; }

.ui-spinner-down {
  bottom: 0; }

.ui-tabs {
  position: relative;
  /* position: relative prevents IE scroll bug (element with position: relative inside container with overflow: auto appear as "fixed") */
  padding: .2em; }

.ui-tabs .ui-tabs-nav {
  margin: 0;
  padding: .2em .2em 0; }

.ui-tabs .ui-tabs-nav li {
  list-style: none;
  float: left;
  position: relative;
  top: 0;
  margin: 1px .2em 0 0;
  border-bottom-width: 0;
  padding: 0;
  white-space: nowrap; }

.ui-tabs .ui-tabs-nav .ui-tabs-anchor {
  float: left;
  padding: .5em 1em;
  text-decoration: none; }

.ui-tabs .ui-tabs-nav li.ui-tabs-active {
  margin-bottom: -1px;
  padding-bottom: 1px; }

.ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
  cursor: text; }

.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor {
  cursor: pointer; }

.ui-tabs .ui-tabs-panel {
  display: block;
  border-width: 0;
  padding: 1em 1.4em;
  background: none; }

.ui-tooltip {
  padding: 8px;
  position: absolute;
  z-index: 9999;
  max-width: 300px; }

body .ui-tooltip {
  border-width: 2px; }

/* Component containers
----------------------------------*/
.ui-widget {
  font-family: Arial,Helvetica,sans-serif;
  font-size: 1em; }

.ui-widget .ui-widget {
  font-size: 1em; }

.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
  font-family: Arial,Helvetica,sans-serif;
  font-size: 1em; }

.ui-widget.ui-widget-content {
  border: 1px solid #c5c5c5; }

.ui-widget-content {
  border: 1px solid #dddddd;
  background: #ffffff;
  color: #333333; }

.ui-widget-content a {
  color: #333333; }

.ui-widget-header {
  border: 1px solid #dddddd;
  background: #e9e9e9;
  color: #333333;
  font-weight: bold; }

.ui-widget-header a {
  color: #333333; }

/* Interaction states
----------------------------------*/
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
  border: 1px solid #c5c5c5;
  background: #f6f6f6;
  font-weight: normal;
  color: #454545; }

.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
  color: #454545;
  text-decoration: none; }

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
  border: 1px solid #cccccc;
  background: #ededed;
  font-weight: normal;
  color: #2b2b2b; }

.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited,
a.ui-button:hover,
a.ui-button:focus {
  color: #2b2b2b;
  text-decoration: none; }

.ui-visual-focus {
  box-shadow: 0 0 3px 1px #5e9ed6; }

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border: 1px solid #003eff;
  background: #007fff;
  font-weight: normal;
  color: #ffffff; }

.ui-icon-background,
.ui-state-active .ui-icon-background {
  border: #003eff;
  background-color: #ffffff; }

.ui-state-active a,
.ui-state-active a:link,
.ui-state-active a:visited {
  color: #ffffff;
  text-decoration: none; }

/* Interaction Cues
----------------------------------*/
.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
  border: 1px solid #dad55e;
  background: #fffa90;
  color: #777620; }

.ui-state-checked {
  border: 1px solid #dad55e;
  background: #fffa90; }

.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
  color: #777620; }

.ui-state-error,
.ui-widget-content .ui-state-error,
.ui-widget-header .ui-state-error {
  border: 1px solid #f1a899;
  background: #fddfdf;
  color: #5f3f3f; }

.ui-state-error a,
.ui-widget-content .ui-state-error a,
.ui-widget-header .ui-state-error a {
  color: #5f3f3f; }

.ui-state-error-text,
.ui-widget-content .ui-state-error-text,
.ui-widget-header .ui-state-error-text {
  color: #5f3f3f; }

.ui-priority-primary,
.ui-widget-content .ui-priority-primary,
.ui-widget-header .ui-priority-primary {
  font-weight: bold; }

.ui-priority-secondary,
.ui-widget-content .ui-priority-secondary,
.ui-widget-header .ui-priority-secondary {
  opacity: .7;
  filter: Alpha(Opacity=70);
  /* support: IE8 */
  font-weight: normal; }

.ui-state-disabled,
.ui-widget-content .ui-state-disabled,
.ui-widget-header .ui-state-disabled {
  opacity: .35;
  filter: Alpha(Opacity=35);
  /* support: IE8 */
  background-image: none; }

.ui-state-disabled .ui-icon {
  filter: Alpha(Opacity=35);
  /* support: IE8 - See #6059 */ }

/* Icons
----------------------------------*/
/* states and images */
.ui-icon {
  width: 16px;
  height: 16px; }

.ui-icon,
.ui-widget-content .ui-icon {
  background-image: url("../images/ui-icons_444444_256x240.png"); }

.ui-widget-header .ui-icon {
  background-image: url("../images/ui-icons_444444_256x240.png"); }

.ui-state-hover .ui-icon,
.ui-state-focus .ui-icon,
.ui-button:hover .ui-icon,
.ui-button:focus .ui-icon {
  background-image: url("../images/ui-icons_555555_256x240.png"); }

.ui-state-active .ui-icon,
.ui-button:active .ui-icon {
  background-image: url("../images/ui-icons_ffffff_256x240.png"); }

.ui-state-highlight .ui-icon,
.ui-button .ui-state-highlight.ui-icon {
  background-image: url("../images/ui-icons_777620_256x240.png"); }

.ui-state-error .ui-icon,
.ui-state-error-text .ui-icon {
  background-image: url("../images/ui-icons_cc0000_256x240.png"); }

.ui-button .ui-icon {
  background-image: url("../images/ui-icons_777777_256x240.png"); }

/* positioning */
.ui-icon-blank {
  background-position: 16px 16px; }

.ui-icon-caret-1-n {
  background-position: 0 0; }

.ui-icon-caret-1-ne {
  background-position: -16px 0; }

.ui-icon-caret-1-e {
  background-position: -32px 0; }

.ui-icon-caret-1-se {
  background-position: -48px 0; }

.ui-icon-caret-1-s {
  background-position: -65px 0; }

.ui-icon-caret-1-sw {
  background-position: -80px 0; }

.ui-icon-caret-1-w {
  background-position: -96px 0; }

.ui-icon-caret-1-nw {
  background-position: -112px 0; }

.ui-icon-caret-2-n-s {
  background-position: -128px 0; }

.ui-icon-caret-2-e-w {
  background-position: -144px 0; }

.ui-icon-triangle-1-n {
  background-position: 0 -16px; }

.ui-icon-triangle-1-ne {
  background-position: -16px -16px; }

.ui-icon-triangle-1-e {
  background-position: -32px -16px; }

.ui-icon-triangle-1-se {
  background-position: -48px -16px; }

.ui-icon-triangle-1-s {
  background-position: -65px -16px; }

.ui-icon-triangle-1-sw {
  background-position: -80px -16px; }

.ui-icon-triangle-1-w {
  background-position: -96px -16px; }

.ui-icon-triangle-1-nw {
  background-position: -112px -16px; }

.ui-icon-triangle-2-n-s {
  background-position: -128px -16px; }

.ui-icon-triangle-2-e-w {
  background-position: -144px -16px; }

.ui-icon-arrow-1-n {
  background-position: 0 -32px; }

.ui-icon-arrow-1-ne {
  background-position: -16px -32px; }

.ui-icon-arrow-1-e {
  background-position: -32px -32px; }

.ui-icon-arrow-1-se {
  background-position: -48px -32px; }

.ui-icon-arrow-1-s {
  background-position: -65px -32px; }

.ui-icon-arrow-1-sw {
  background-position: -80px -32px; }

.ui-icon-arrow-1-w {
  background-position: -96px -32px; }

.ui-icon-arrow-1-nw {
  background-position: -112px -32px; }

.ui-icon-arrow-2-n-s {
  background-position: -128px -32px; }

.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px; }

.ui-icon-arrow-2-e-w {
  background-position: -160px -32px; }

.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px; }

.ui-icon-arrowstop-1-n {
  background-position: -192px -32px; }

.ui-icon-arrowstop-1-e {
  background-position: -208px -32px; }

.ui-icon-arrowstop-1-s {
  background-position: -224px -32px; }

.ui-icon-arrowstop-1-w {
  background-position: -240px -32px; }

.ui-icon-arrowthick-1-n {
  background-position: 1px -48px; }

.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px; }

.ui-icon-arrowthick-1-e {
  background-position: -32px -48px; }

.ui-icon-arrowthick-1-se {
  background-position: -48px -48px; }

.ui-icon-arrowthick-1-s {
  background-position: -64px -48px; }

.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px; }

.ui-icon-arrowthick-1-w {
  background-position: -96px -48px; }

.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px; }

.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px; }

.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px; }

.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px; }

.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px; }

.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px; }

.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px; }

.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px; }

.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px; }

.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px; }

.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px; }

.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px; }

.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px; }

.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px; }

.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px; }

.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px; }

.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px; }

.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px; }

.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px; }

.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px; }

.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px; }

.ui-icon-arrow-4 {
  background-position: 0 -80px; }

.ui-icon-arrow-4-diag {
  background-position: -16px -80px; }

.ui-icon-extlink {
  background-position: -32px -80px; }

.ui-icon-newwin {
  background-position: -48px -80px; }

.ui-icon-refresh {
  background-position: -64px -80px; }

.ui-icon-shuffle {
  background-position: -80px -80px; }

.ui-icon-transfer-e-w {
  background-position: -96px -80px; }

.ui-icon-transferthick-e-w {
  background-position: -112px -80px; }

.ui-icon-folder-collapsed {
  background-position: 0 -96px; }

.ui-icon-folder-open {
  background-position: -16px -96px; }

.ui-icon-document {
  background-position: -32px -96px; }

.ui-icon-document-b {
  background-position: -48px -96px; }

.ui-icon-note {
  background-position: -64px -96px; }

.ui-icon-mail-closed {
  background-position: -80px -96px; }

.ui-icon-mail-open {
  background-position: -96px -96px; }

.ui-icon-suitcase {
  background-position: -112px -96px; }

.ui-icon-comment {
  background-position: -128px -96px; }

.ui-icon-person {
  background-position: -144px -96px; }

.ui-icon-print {
  background-position: -160px -96px; }

.ui-icon-trash {
  background-position: -176px -96px; }

.ui-icon-locked {
  background-position: -192px -96px; }

.ui-icon-unlocked {
  background-position: -208px -96px; }

.ui-icon-bookmark {
  background-position: -224px -96px; }

.ui-icon-tag {
  background-position: -240px -96px; }

.ui-icon-home {
  background-position: 0 -112px; }

.ui-icon-flag {
  background-position: -16px -112px; }

.ui-icon-calendar {
  background-position: -32px -112px; }

.ui-icon-cart {
  background-position: -48px -112px; }

.ui-icon-pencil {
  background-position: -64px -112px; }

.ui-icon-clock {
  background-position: -80px -112px; }

.ui-icon-disk {
  background-position: -96px -112px; }

.ui-icon-calculator {
  background-position: -112px -112px; }

.ui-icon-zoomin {
  background-position: -128px -112px; }

.ui-icon-zoomout {
  background-position: -144px -112px; }

.ui-icon-search {
  background-position: -160px -112px; }

.ui-icon-wrench {
  background-position: -176px -112px; }

.ui-icon-gear {
  background-position: -192px -112px; }

.ui-icon-heart {
  background-position: -208px -112px; }

.ui-icon-star {
  background-position: -224px -112px; }

.ui-icon-link {
  background-position: -240px -112px; }

.ui-icon-cancel {
  background-position: 0 -128px; }

.ui-icon-plus {
  background-position: -16px -128px; }

.ui-icon-plusthick {
  background-position: -32px -128px; }

.ui-icon-minus {
  background-position: -48px -128px; }

.ui-icon-minusthick {
  background-position: -64px -128px; }

.ui-icon-close {
  background-position: -80px -128px; }

.ui-icon-closethick {
  background-position: -96px -128px; }

.ui-icon-key {
  background-position: -112px -128px; }

.ui-icon-lightbulb {
  background-position: -128px -128px; }

.ui-icon-scissors {
  background-position: -144px -128px; }

.ui-icon-clipboard {
  background-position: -160px -128px; }

.ui-icon-copy {
  background-position: -176px -128px; }

.ui-icon-contact {
  background-position: -192px -128px; }

.ui-icon-image {
  background-position: -208px -128px; }

.ui-icon-video {
  background-position: -224px -128px; }

.ui-icon-script {
  background-position: -240px -128px; }

.ui-icon-alert {
  background-position: 0 -144px; }

.ui-icon-info {
  background-position: -16px -144px; }

.ui-icon-notice {
  background-position: -32px -144px; }

.ui-icon-help {
  background-position: -48px -144px; }

.ui-icon-check {
  background-position: -64px -144px; }

.ui-icon-bullet {
  background-position: -80px -144px; }

.ui-icon-radio-on {
  background-position: -96px -144px; }

.ui-icon-radio-off {
  background-position: -112px -144px; }

.ui-icon-pin-w {
  background-position: -128px -144px; }

.ui-icon-pin-s {
  background-position: -144px -144px; }

.ui-icon-play {
  background-position: 0 -160px; }

.ui-icon-pause {
  background-position: -16px -160px; }

.ui-icon-seek-next {
  background-position: -32px -160px; }

.ui-icon-seek-prev {
  background-position: -48px -160px; }

.ui-icon-seek-end {
  background-position: -64px -160px; }

.ui-icon-seek-start {
  background-position: -80px -160px; }

/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.ui-icon-seek-first {
  background-position: -80px -160px; }

.ui-icon-stop {
  background-position: -96px -160px; }

.ui-icon-eject {
  background-position: -112px -160px; }

.ui-icon-volume-off {
  background-position: -128px -160px; }

.ui-icon-volume-on {
  background-position: -144px -160px; }

.ui-icon-power {
  background-position: 0 -176px; }

.ui-icon-signal-diag {
  background-position: -16px -176px; }

.ui-icon-signal {
  background-position: -32px -176px; }

.ui-icon-battery-0 {
  background-position: -48px -176px; }

.ui-icon-battery-1 {
  background-position: -64px -176px; }

.ui-icon-battery-2 {
  background-position: -80px -176px; }

.ui-icon-battery-3 {
  background-position: -96px -176px; }

.ui-icon-circle-plus {
  background-position: 0 -192px; }

.ui-icon-circle-minus {
  background-position: -16px -192px; }

.ui-icon-circle-close {
  background-position: -32px -192px; }

.ui-icon-circle-triangle-e {
  background-position: -48px -192px; }

.ui-icon-circle-triangle-s {
  background-position: -64px -192px; }

.ui-icon-circle-triangle-w {
  background-position: -80px -192px; }

.ui-icon-circle-triangle-n {
  background-position: -96px -192px; }

.ui-icon-circle-arrow-e {
  background-position: -112px -192px; }

.ui-icon-circle-arrow-s {
  background-position: -128px -192px; }

.ui-icon-circle-arrow-w {
  background-position: -144px -192px; }

.ui-icon-circle-arrow-n {
  background-position: -160px -192px; }

.ui-icon-circle-zoomin {
  background-position: -176px -192px; }

.ui-icon-circle-zoomout {
  background-position: -192px -192px; }

.ui-icon-circle-check {
  background-position: -208px -192px; }

.ui-icon-circlesmall-plus {
  background-position: 0 -208px; }

.ui-icon-circlesmall-minus {
  background-position: -16px -208px; }

.ui-icon-circlesmall-close {
  background-position: -32px -208px; }

.ui-icon-squaresmall-plus {
  background-position: -48px -208px; }

.ui-icon-squaresmall-minus {
  background-position: -64px -208px; }

.ui-icon-squaresmall-close {
  background-position: -80px -208px; }

.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px; }

.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px; }

.ui-icon-grip-solid-vertical {
  background-position: -32px -224px; }

.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px; }

.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px; }

.ui-icon-grip-diagonal-se {
  background-position: -80px -224px; }

/* Misc visuals
----------------------------------*/
/* Corner radius */
.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
  border-top-left-radius: 3px; }

.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
  border-top-right-radius: 3px; }

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
  border-bottom-left-radius: 3px; }

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
  border-bottom-right-radius: 3px; }

/* Overlays */
.ui-widget-overlay {
  background: #aaaaaa;
  opacity: .3;
  filter: Alpha(Opacity=30);
  /* support: IE8 */ }

.ui-widget-shadow {
  -webkit-box-shadow: 0px 0px 5px #666666;
  box-shadow: 0px 0px 5px #666666; }

.progress-bar {
  position: relative;
  height: 200px;
  text-align: center;
  padding-left: 75px; }

.progress-bar div {
  position: absolute;
  height: 200px;
  width: 200px;
  border-radius: 50%; }

.progress-bar div span {
  position: absolute;
  font-size: 25px;
  line-height: 180px;
  height: 180px;
  width: 180px;
  left: 10px;
  top: 10px;
  text-align: center;
  border-radius: 50%;
  background-color: white; }

.progress-bar .background {
  background-color: #b3cef6; }

.progress-bar .rotate {
  clip: rect(0 100px 200px 0);
  background-color: #656D78; }

.progress-bar .left {
  clip: rect(0 100px 200px 0);
  opacity: 1;
  background-color: #b3cef6; }

.progress-bar .right {
  clip: rect(0 100px 200px 0);
  transform: rotate(180deg);
  opacity: 0;
  background-color: #656D78; }

@keyframes toggle {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
/*-----------------------------------------------------------------------------

    -   Revolution Slider 4.1 Captions -

        Screen Stylesheet

version:    1.4.5
date:       27/11/13
author:     themepunch
email:      <EMAIL>
website:    http://www.themepunch.com
-----------------------------------------------------------------------------*/
/*************************
    -   CAPTIONS    -
**************************/
.tp-hide-revslider, .tp-caption.tp-hidden-caption {
  visibility: hidden !important;
  display: none !important; }

.tp-caption {
  z-index: 1;
  white-space: nowrap; }

.tp-caption-demo .tp-caption {
  position: relative !important;
  display: inline-block;
  margin-bottom: 10px;
  margin-right: 20px !important; }

.tp-caption.whitedivider3px {
  color: #000000;
  text-shadow: none;
  background-color: white;
  background-color: white;
  text-decoration: none;
  min-width: 408px;
  min-height: 3px;
  background-position: initial initial;
  background-repeat: initial initial;
  border-width: 0px;
  border-color: #000000;
  border-style: none; }

.tp-caption.finewide_large_white {
  color: #ffffff;
  text-shadow: none;
  font-size: 60px;
  line-height: 60px;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
  background-color: transparent;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 8px;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.whitedivider3px {
  color: #000000;
  text-shadow: none;
  background-color: white;
  background-color: white;
  text-decoration: none;
  font-size: 0px;
  line-height: 0;
  min-width: 468px;
  min-height: 3px;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.finewide_medium_white {
  color: #ffffff;
  text-shadow: none;
  font-size: 37px;
  line-height: 37px;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
  background-color: transparent;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 5px;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.boldwide_small_white {
  font-size: 25px;
  line-height: 25px;
  font-weight: 800;
  font-family: "Open Sans", sans-serif;
  color: white;
  text-decoration: none;
  background-color: transparent;
  text-shadow: none;
  text-transform: uppercase;
  letter-spacing: 5px;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.whitedivider3px_vertical {
  color: #000000;
  text-shadow: none;
  background-color: white;
  background-color: white;
  text-decoration: none;
  font-size: 0px;
  line-height: 0;
  min-width: 3px;
  min-height: 130px;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.finewide_small_white {
  color: #ffffff;
  text-shadow: none;
  font-size: 25px;
  line-height: 25px;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
  background-color: transparent;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 5px;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.finewide_verysmall_white_mw {
  font-size: 13px;
  line-height: 25px;
  font-weight: 400;
  font-family: "Open Sans", sans-serif;
  color: #ffffff;
  text-decoration: none;
  background-color: transparent;
  text-shadow: none;
  text-transform: uppercase;
  letter-spacing: 5px;
  max-width: 470px;
  white-space: normal !important;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.lightgrey_divider {
  text-decoration: none;
  background-color: #ebebeb;
  background-color: #ebebeb;
  width: 370px;
  height: 3px;
  background-position: initial initial;
  background-repeat: initial initial;
  border-width: 0px;
  border-color: #222222;
  border-style: none; }

.tp-caption.finewide_large_white {
  color: #FFF;
  text-shadow: none;
  font-size: 60px;
  line-height: 60px;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
  background-color: transparent;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 8px;
  border-width: 0px;
  border-color: #000;
  border-style: none; }

.tp-caption.finewide_medium_white {
  color: #FFF;
  text-shadow: none;
  font-size: 34px;
  line-height: 34px;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
  background-color: transparent;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 5px;
  border-width: 0px;
  border-color: #000;
  border-style: none; }

.tp-caption.huge_red {
  position: absolute;
  color: #df4b6b;
  font-weight: 400;
  font-size: 150px;
  line-height: 130px;
  font-family: 'Oswald', sans-serif;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap;
  background-color: #2d3136;
  padding: 0px; }

.tp-caption.middle_yellow {
  position: absolute;
  color: #fbd572;
  font-weight: 600;
  font-size: 50px;
  line-height: 50px;
  font-family: 'Open Sans', sans-serif;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.huge_thin_yellow {
  position: absolute;
  color: #fbd572;
  font-weight: 300;
  font-size: 90px;
  line-height: 90px;
  font-family: 'Open Sans', sans-serif;
  margin: 0px;
  letter-spacing: 20px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.big_dark {
  position: absolute;
  color: #333;
  font-weight: 700;
  font-size: 70px;
  line-height: 70px;
  font-family: "Open Sans";
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.medium_dark {
  position: absolute;
  color: #333;
  font-weight: 300;
  font-size: 40px;
  line-height: 40px;
  font-family: "Open Sans";
  margin: 0px;
  letter-spacing: 5px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.medium_grey {
  position: absolute;
  color: #fff;
  text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
  font-weight: 700;
  font-size: 20px;
  line-height: 20px;
  font-family: Arial;
  padding: 2px 4px;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  background-color: #888;
  white-space: nowrap; }

.tp-caption.small_text {
  position: absolute;
  color: #fff;
  text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
  font-family: Arial;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.medium_text {
  position: absolute;
  color: #fff;
  text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
  font-weight: 700;
  font-size: 20px;
  line-height: 20px;
  font-family: Arial;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.large_bold_white_25 {
  font-size: 55px;
  line-height: 65px;
  font-weight: 700;
  font-family: "Open Sans";
  color: #fff;
  text-decoration: none;
  background-color: transparent;
  text-align: center;
  text-shadow: #000 0px 5px 10px;
  border-width: 0px;
  border-color: white;
  border-style: none; }

.tp-caption.medium_text_shadow {
  font-size: 25px;
  line-height: 25px;
  font-weight: 600;
  font-family: "Open Sans";
  color: #fff;
  text-decoration: none;
  background-color: transparent;
  text-align: center;
  text-shadow: #000 0px 5px 10px;
  border-width: 0px;
  border-color: white;
  border-style: none; }

.tp-caption.large_text {
  position: absolute;
  color: #fff;
  text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
  font-weight: 700;
  font-size: 40px;
  line-height: 40px;
  font-family: Arial;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.medium_bold_grey {
  font-size: 30px;
  line-height: 30px;
  font-weight: 800;
  font-family: "Open Sans";
  color: #666666;
  text-decoration: none;
  background-color: transparent;
  text-shadow: none;
  margin: 0px;
  padding: 1px 4px 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.very_large_text {
  position: absolute;
  color: #fff;
  text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
  font-weight: 700;
  font-size: 60px;
  line-height: 60px;
  font-family: Arial;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap;
  letter-spacing: -2px; }

.tp-caption.very_big_white {
  position: absolute;
  color: #fff;
  text-shadow: none;
  font-weight: 800;
  font-size: 60px;
  line-height: 60px;
  font-family: Arial;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap;
  padding: 0px 4px;
  padding-top: 1px;
  background-color: #000; }

.tp-caption.very_big_black {
  position: absolute;
  color: #000;
  text-shadow: none;
  font-weight: 700;
  font-size: 60px;
  line-height: 60px;
  font-family: Arial;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap;
  padding: 0px 4px;
  padding-top: 1px;
  background-color: #fff; }

.tp-caption.modern_medium_fat {
  position: absolute;
  color: #000;
  text-shadow: none;
  font-weight: 800;
  font-size: 24px;
  line-height: 20px;
  font-family: "Open Sans", sans-serif;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.modern_medium_fat_white {
  position: absolute;
  color: #fff;
  text-shadow: none;
  font-weight: 800;
  font-size: 24px;
  line-height: 20px;
  font-family: "Open Sans", sans-serif;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.modern_medium_light {
  position: absolute;
  color: #000;
  text-shadow: none;
  font-weight: 300;
  font-size: 24px;
  line-height: 20px;
  font-family: "Open Sans", sans-serif;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.modern_big_bluebg {
  position: absolute;
  color: #fff;
  text-shadow: none;
  font-weight: 800;
  font-size: 30px;
  line-height: 36px;
  font-family: "Open Sans", sans-serif;
  padding: 3px 10px;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  background-color: #4e5b6c;
  letter-spacing: 0; }

.tp-caption.modern_big_redbg {
  position: absolute;
  color: #fff;
  text-shadow: none;
  font-weight: 300;
  font-size: 30px;
  line-height: 36px;
  font-family: "Open Sans", sans-serif;
  padding: 3px 10px;
  padding-top: 1px;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  background-color: #de543e;
  letter-spacing: 0; }

.tp-caption.modern_small_text_dark {
  position: absolute;
  color: #555;
  text-shadow: none;
  font-size: 14px;
  line-height: 22px;
  font-family: Arial;
  margin: 0px;
  border-width: 0px;
  border-style: none;
  white-space: nowrap; }

.tp-caption.boxshadow {
  -moz-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5); }

.tp-caption.black {
  color: #000;
  text-shadow: none; }

.tp-caption.noshadow {
  text-shadow: none; }

.tp-caption a {
  color: #ff7302;
  text-shadow: none;
  -webkit-transition: all 0.2s ease-out;
  -moz-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  -ms-transition: all 0.2s ease-out; }

.tp-caption a:hover {
  color: #ffa902; }

.tp-caption.thinheadline_dark {
  position: absolute;
  color: rgba(0, 0, 0, 0.85);
  text-shadow: none;
  font-weight: 300;
  font-size: 30px;
  line-height: 30px;
  font-family: "Open Sans";
  background-color: transparent; }

.tp-caption.thintext_dark {
  position: absolute;
  color: rgba(0, 0, 0, 0.85);
  text-shadow: none;
  font-weight: 300;
  font-size: 16px;
  line-height: 26px;
  font-family: "Open Sans";
  background-color: transparent; }

.tp-caption.medium_bg_red a {
  color: #fff;
  text-decoration: none; }

.tp-caption.medium_bg_red a:hover {
  color: #fff;
  text-decoration: underline; }

.tp-caption.smoothcircle {
  font-size: 30px;
  line-height: 75px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: black;
  background-color: rgba(0, 0, 0, 0.49804);
  padding: 50px 25px;
  text-align: center;
  border-radius: 500px 500px 500px 500px;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.largeblackbg {
  font-size: 50px;
  line-height: 70px;
  font-weight: 300;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: black;
  padding: 0px 20px 5px;
  text-shadow: none;
  border-width: 0px;
  border-color: white;
  border-style: none; }

.tp-caption.largepinkbg {
  position: absolute;
  color: #fff;
  text-shadow: none;
  font-weight: 300;
  font-size: 50px;
  line-height: 70px;
  font-family: "Open Sans";
  background-color: #db4360;
  padding: 0px 20px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px; }

.tp-caption.largewhitebg {
  position: absolute;
  color: #000;
  text-shadow: none;
  font-weight: 300;
  font-size: 50px;
  line-height: 70px;
  font-family: "Open Sans";
  background-color: #fff;
  padding: 0px 20px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px; }

.tp-caption.largegreenbg {
  position: absolute;
  color: #fff;
  text-shadow: none;
  font-weight: 300;
  font-size: 50px;
  line-height: 70px;
  font-family: "Open Sans";
  background-color: #67ae73;
  padding: 0px 20px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px; }

.tp-caption.excerpt {
  font-size: 36px;
  line-height: 36px;
  font-weight: 700;
  font-family: Arial;
  color: #ffffff;
  text-decoration: none;
  background-color: black;
  text-shadow: none;
  margin: 0px;
  letter-spacing: -1.5px;
  padding: 1px 4px 0px 4px;
  width: 150px;
  white-space: normal !important;
  height: auto;
  border-width: 0px;
  border-color: white;
  border-style: none; }

.tp-caption.large_bold_grey {
  font-size: 60px;
  line-height: 60px;
  font-weight: 800;
  font-family: "Open Sans";
  color: #666666;
  text-decoration: none;
  background-color: transparent;
  text-shadow: none;
  margin: 0px;
  padding: 1px 4px 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_thin_grey {
  font-size: 34px;
  line-height: 30px;
  font-weight: 300;
  font-family: "Open Sans";
  color: #666666;
  text-decoration: none;
  background-color: transparent;
  padding: 1px 4px 0px;
  text-shadow: none;
  margin: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.small_thin_grey {
  font-size: 18px;
  line-height: 26px;
  font-weight: 300;
  font-family: "Open Sans";
  color: #757575;
  text-decoration: none;
  background-color: transparent;
  padding: 1px 4px 0px;
  text-shadow: none;
  margin: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.lightgrey_divider {
  text-decoration: none;
  background-color: #ebebeb;
  width: 370px;
  height: 3px;
  background-position: initial initial;
  background-repeat: initial initial;
  border-width: 0px;
  border-color: #222222;
  border-style: none; }

.tp-caption.large_bold_darkblue {
  font-size: 58px;
  line-height: 60px;
  font-weight: 800;
  font-family: "Open Sans";
  color: #34495e;
  text-decoration: none;
  background-color: transparent;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_bg_darkblue {
  font-size: 20px;
  line-height: 20px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: #34495e;
  padding: 10px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_bold_red {
  font-size: 24px;
  line-height: 30px;
  font-weight: 800;
  font-family: "Open Sans";
  color: #e33a0c;
  text-decoration: none;
  background-color: transparent;
  padding: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_light_red {
  font-size: 21px;
  line-height: 26px;
  font-weight: 300;
  font-family: "Open Sans";
  color: #e33a0c;
  text-decoration: none;
  background-color: transparent;
  padding: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_bg_red {
  font-size: 20px;
  line-height: 20px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: #e33a0c;
  padding: 10px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_bold_orange {
  font-size: 24px;
  line-height: 30px;
  font-weight: 800;
  font-family: "Open Sans";
  color: #f39c12;
  text-decoration: none;
  background-color: transparent;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_bg_orange {
  font-size: 20px;
  line-height: 20px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: #f39c12;
  padding: 10px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.grassfloor {
  text-decoration: none;
  background-color: #a0b397;
  width: 4000px;
  height: 150px;
  border-width: 0px;
  border-color: #222222;
  border-style: none; }

.tp-caption.large_bold_white {
  font-size: 58px;
  line-height: 60px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: transparent;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_light_white {
  font-size: 30px;
  line-height: 36px;
  font-weight: 300;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: transparent;
  padding: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.mediumlarge_light_white {
  font-size: 34px;
  line-height: 40px;
  font-weight: 300;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: transparent;
  padding: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.mediumlarge_light_white_center {
  font-size: 34px;
  line-height: 40px;
  font-weight: 300;
  font-family: "Open Sans";
  color: #ffffff;
  text-decoration: none;
  background-color: transparent;
  padding: 0px 0px 0px 0px;
  text-align: center;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_bg_asbestos {
  font-size: 20px;
  line-height: 20px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: #7f8c8d;
  padding: 10px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.medium_light_black {
  font-size: 30px;
  line-height: 36px;
  font-weight: 300;
  font-family: "Open Sans";
  color: black;
  text-decoration: none;
  background-color: transparent;
  padding: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.large_bold_black {
  font-size: 58px;
  line-height: 60px;
  font-weight: 800;
  font-family: "Open Sans";
  color: black;
  text-decoration: none;
  background-color: transparent;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.mediumlarge_light_darkblue {
  font-size: 34px;
  line-height: 40px;
  font-weight: 300;
  font-family: "Open Sans";
  color: #34495e;
  text-decoration: none;
  background-color: transparent;
  padding: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.small_light_white {
  font-size: 17px;
  line-height: 28px;
  font-weight: 300;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: transparent;
  padding: 0px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.roundedimage {
  border-width: 0px;
  border-color: #222222;
  border-style: none; }

.tp-caption.large_bg_black {
  font-size: 40px;
  line-height: 40px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: black;
  padding: 10px 20px 15px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.mediumwhitebg {
  font-size: 30px;
  line-height: 30px;
  font-weight: 300;
  font-family: "Open Sans";
  color: black;
  text-decoration: none;
  background-color: white;
  padding: 5px 15px 10px;
  text-shadow: none;
  border-width: 0px;
  border-color: black;
  border-style: none; }

.tp-caption.medium_bg_orange_new1 {
  font-size: 20px;
  line-height: 20px;
  font-weight: 800;
  font-family: "Open Sans";
  color: white;
  text-decoration: none;
  background-color: #f39c12;
  padding: 10px;
  border-width: 0px;
  border-color: #ffd658;
  border-style: none; }

.tp-caption.boxshadow {
  -moz-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5); }

.tp-caption.black {
  color: #000;
  text-shadow: none;
  font-weight: 300;
  font-size: 19px;
  line-height: 19px;
  font-family: 'Open Sans', sans; }

.tp-caption.noshadow {
  text-shadow: none; }

.tp_inner_padding {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  max-height: none !important; }

/*.tp-caption           {   transform:none !important}*/
/*********************************
    -   SPECIAL TP CAPTIONS -
**********************************/
.tp-caption .frontcorner {
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 0px solid transparent;
  border-top: 40px solid #00A8FF;
  position: absolute;
  left: -40px;
  top: 0px; }

.tp-caption .backcorner {
  width: 0;
  height: 0;
  border-left: 0px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 40px solid #00A8FF;
  position: absolute;
  right: 0px;
  top: 0px; }

.tp-caption .frontcornertop {
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 0px solid transparent;
  border-bottom: 40px solid #00A8FF;
  position: absolute;
  left: -40px;
  top: 0px; }

.tp-caption .backcornertop {
  width: 0;
  height: 0;
  border-left: 0px solid transparent;
  border-right: 40px solid transparent;
  border-top: 40px solid #00A8FF;
  position: absolute;
  right: 0px;
  top: 0px; }

/******************************
    -   BUTTONS -
*******************************/
.tp-simpleresponsive .button {
  padding: 6px 13px 5px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  height: 30px;
  cursor: pointer;
  color: #fff !important;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.6) !important;
  font-size: 15px;
  line-height: 45px !important;
  background: url(../images/gradient/g30.png) repeat-x top;
  font-family: arial, sans-serif;
  font-weight: bold;
  letter-spacing: -1px; }

.tp-simpleresponsive .button.big {
  color: #fff;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.6);
  font-weight: bold;
  padding: 9px 20px;
  font-size: 19px;
  line-height: 57px !important;
  background: url(../images/gradient/g40.png) repeat-x top; }

.tp-simpleresponsive .purchase:hover,
.tp-simpleresponsive .button:hover,
.tp-simpleresponsive .button.big:hover {
  background-position: bottom, 15px 11px; }

@media only screen and (min-width: 480px) and (max-width: 767px) {
  .tp-simpleresponsive .button {
    padding: 4px 8px 3px;
    line-height: 25px !important;
    font-size: 11px !important;
    font-weight: normal; }

  .tp-simpleresponsive a.button {
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    -ms-transition: none; } }
@media only screen and (min-width: 0px) and (max-width: 479px) {
  .tp-simpleresponsive .button {
    padding: 2px 5px 2px;
    line-height: 20px !important;
    font-size: 10px !important; }

  .tp-simpleresponsive a.button {
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    -ms-transition: none; } }
/*  BUTTON COLORS   */
.tp-simpleresponsive .button.green, .tp-simpleresponsive .button:hover.green,
.tp-simpleresponsive .purchase.green, .tp-simpleresponsive .purchase:hover.green {
  background-color: #21a117;
  -webkit-box-shadow: 0px 3px 0px 0px #104d0b;
  -moz-box-shadow: 0px 3px 0px 0px #104d0b;
  box-shadow: 0px 3px 0px 0px #104d0b; }

.tp-simpleresponsive .button.blue, .tp-simpleresponsive .button:hover.blue,
.tp-simpleresponsive .purchase.blue, .tp-simpleresponsive .purchase:hover.blue {
  background-color: #1d78cb;
  -webkit-box-shadow: 0px 3px 0px 0px #0f3e68;
  -moz-box-shadow: 0px 3px 0px 0px #0f3e68;
  box-shadow: 0px 3px 0px 0px #0f3e68; }

.tp-simpleresponsive .button.red, .tp-simpleresponsive .button:hover.red,
.tp-simpleresponsive .purchase.red, .tp-simpleresponsive .purchase:hover.red {
  background-color: #cb1d1d;
  -webkit-box-shadow: 0px 3px 0px 0px #7c1212;
  -moz-box-shadow: 0px 3px 0px 0px #7c1212;
  box-shadow: 0px 3px 0px 0px #7c1212; }

.tp-simpleresponsive .button.orange, .tp-simpleresponsive .button:hover.orange,
.tp-simpleresponsive .purchase.orange, .tp-simpleresponsive .purchase:hover.orange {
  background-color: #ff7700;
  -webkit-box-shadow: 0px 3px 0px 0px #a34c00;
  -moz-box-shadow: 0px 3px 0px 0px #a34c00;
  box-shadow: 0px 3px 0px 0px #a34c00; }

.tp-simpleresponsive .button.darkgrey, .tp-simpleresponsive .button.grey,
.tp-simpleresponsive .button:hover.darkgrey, .tp-simpleresponsive .button:hover.grey,
.tp-simpleresponsive .purchase.darkgrey, .tp-simpleresponsive .purchase:hover.darkgrey {
  background-color: #555;
  -webkit-box-shadow: 0px 3px 0px 0px #222;
  -moz-box-shadow: 0px 3px 0px 0px #222;
  box-shadow: 0px 3px 0px 0px #222; }

.tp-simpleresponsive .button.lightgrey, .tp-simpleresponsive .button:hover.lightgrey,
.tp-simpleresponsive .purchase.lightgrey, .tp-simpleresponsive .purchase:hover.lightgrey {
  background-color: #888;
  -webkit-box-shadow: 0px 3px 0px 0px #555;
  -moz-box-shadow: 0px 3px 0px 0px #555;
  box-shadow: 0px 3px 0px 0px #555; }

/****************************************************************

    -   SET THE ANIMATION EVEN MORE SMOOTHER ON ANDROID   -

******************************************************************/
/*.tp-simpleresponsive              {   -webkit-perspective: 1500px;
                                        -moz-perspective: 1500px;
                                        -o-perspective: 1500px;
                                        -ms-perspective: 1500px;
                                        perspective: 1500px;
                                    }*/
/**********************************************
    -   FULLSCREEN AND FULLWIDHT CONTAINERS -
**********************************************/
.fullscreen-container {
  width: 100%;
  position: relative;
  padding: 0; }

.fullwidthbanner-container {
  width: 100%;
  position: relative;
  padding: 0;
  overflow: hidden; }

.fullwidthbanner-container .fullwidthbanner {
  width: 100%;
  position: relative; }

/************************************************
      - SOME CAPTION MODIFICATION AT START  -
*************************************************/
.tp-simpleresponsive .caption,
.tp-simpleresponsive .tp-caption {
  /*-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";     -moz-opacity: 0;    -khtml-opacity: 0;  opacity: 0; */
  position: absolute;
  visibility: hidden;
  -webkit-font-smoothing: antialiased !important;
  left: 175px;
  bottom: 71px;
  text-align: center; }

.tp-simpleresponsive img {
  max-width: none; }

/******************************
    -   IE8 HACKS   -
*******************************/
.noFilterClass {
  filter: none !important; }

/******************************
    -   SHADOWS     -
******************************/
.tp-bannershadow {
  position: absolute;
  margin-left: auto;
  margin-right: auto;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none; }

.tp-bannershadow.tp-shadow1 {
  background: url(../assets/shadow1.png) no-repeat;
  background-size: 100% 100%;
  width: 890px;
  height: 60px;
  bottom: -60px; }

.tp-bannershadow.tp-shadow2 {
  background: url(../assets/shadow2.png) no-repeat;
  background-size: 100% 100%;
  width: 890px;
  height: 60px;
  bottom: -60px; }

.tp-bannershadow.tp-shadow3 {
  background: url(../assets/shadow3.png) no-repeat;
  background-size: 100% 100%;
  width: 890px;
  height: 60px;
  bottom: -60px; }

/********************************
    -   FULLSCREEN VIDEO    -
*********************************/
.caption.fullscreenvideo {
  left: 0px;
  top: 0px;
  position: absolute;
  width: 100%;
  height: 100%; }

.caption.fullscreenvideo iframe,
.caption.fullscreenvideo video {
  width: 100% !important;
  height: 100% !important;
  display: none; }

.tp-caption.fullscreenvideo {
  left: 0px;
  top: 0px;
  position: absolute;
  width: 100%;
  height: 100%; }

.tp-caption.fullscreenvideo iframe,
.tp-caption.fullscreenvideo iframe video {
  width: 100% !important;
  height: 100% !important;
  display: none; }

.fullcoveredvideo video,
.fullscreenvideo video {
  background: #000; }

.fullcoveredvideo .tp-poster {
  background-position: center center;
  background-size: cover;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px; }

.html5vid.videoisplaying .tp-poster {
  display: none; }

.tp-video-play-button {
  background: #000;
  background: rgba(0, 0, 0, 0.3);
  padding: 5px;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 40px;
  color: #FFF;
  z-index: 3;
  margin-top: -27px;
  margin-left: -28px;
  text-align: center;
  cursor: pointer; }

.html5vid .tp-revstop {
  width: 6px;
  height: 20px;
  border-left: 5px solid #fff;
  border-right: 5px solid #fff;
  position: relative;
  margin: 10px 20px; }

.html5vid .tp-revstop {
  display: none; }

.html5vid.videoisplaying .revicon-right-dir {
  display: none; }

.html5vid.videoisplaying .tp-revstop {
  display: block; }

.html5vid.videoisplaying .tp-video-play-button {
  display: none; }

.html5vid:hover .tp-video-play-button {
  display: block; }

.fullcoveredvideo .tp-video-play-button {
  display: none !important; }

/********************************
    -   FULLSCREEN VIDEO ENDS   -
*********************************/
/********************************
    -   DOTTED OVERLAYS -
*********************************/
.tp-dottedoverlay {
  background-repeat: repeat;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 1; }

.tp-dottedoverlay.twoxtwo {
  background: url(../assets/gridtile.png); }

.tp-dottedoverlay.twoxtwowhite {
  background: url(../assets/gridtile_white.png); }

.tp-dottedoverlay.threexthree {
  background: url(../assets/gridtile_3x3.png); }

.tp-dottedoverlay.threexthreewhite {
  background: url(../assets/gridtile_3x3_white.png); }

/********************************
    -   DOTTED OVERLAYS ENDS    -
*********************************/
/************************
    -   NAVIGATION  -
*************************/
/** BULLETS **/
.tpclear {
  clear: both; }

.tp-bullets {
  z-index: 1000;
  position: absolute;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  -moz-opacity: 1;
  -khtml-opacity: 1;
  opacity: 1;
  -webkit-transition: opacity 0.2s ease-out;
  -moz-transition: opacity 0.2s ease-out;
  -o-transition: opacity 0.2s ease-out;
  -ms-transition: opacity 0.2s ease-out;
  -webkit-transform: translateZ(5px); }

.tp-bullets.hidebullets {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  -moz-opacity: 0;
  -khtml-opacity: 0;
  opacity: 0; }

.tp-bullets.simplebullets.navbar {
  border: 1px solid #666;
  border-bottom: 1px solid #444;
  background: url(../assets/boxed_bgtile.png);
  height: 40px;
  padding: 0px 10px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px; }

.tp-bullets.simplebullets.navbar-old {
  background: url(../assets/navigdots_bgtile.png);
  height: 35px;
  padding: 0px 10px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px; }

.tp-bullets.simplebullets.round .bullet {
  cursor: pointer;
  position: relative;
  background: url(../assets/bullet.png) no-Repeat top left;
  width: 20px;
  height: 20px;
  margin-right: 0px;
  float: left;
  margin-top: 0px;
  margin-left: 3px; }

.tp-bullets.simplebullets.round .bullet.last {
  margin-right: 3px; }

.tp-bullets.simplebullets.round-old .bullet {
  cursor: pointer;
  position: relative;
  background: url(../assets/bullets.png) no-Repeat bottom left;
  width: 23px;
  height: 23px;
  margin-right: 0px;
  float: left;
  margin-top: 0px; }

.tp-bullets.simplebullets.round-old .bullet.last {
  margin-right: 0px; }

/** SQUARE BULLETS **/
.tp-bullets.simplebullets.square .bullet {
  cursor: pointer;
  position: relative;
  background: url(../assets/bullets2.png) no-Repeat bottom left;
  width: 19px;
  height: 19px;
  margin-right: 0px;
  float: left;
  margin-top: 0px; }

.tp-bullets.simplebullets.square .bullet.last {
  margin-right: 0px; }

/** SQUARE BULLETS **/
.tp-bullets.simplebullets.square-old .bullet {
  cursor: pointer;
  position: relative;
  background: url(../assets/bullets2.png) no-Repeat bottom left;
  width: 19px;
  height: 19px;
  margin-right: 0px;
  float: left;
  margin-top: 0px; }

.tp-bullets.simplebullets.square-old .bullet.last {
  margin-right: 0px; }

/** navbar NAVIGATION VERSION **/
.tp-bullets.simplebullets.navbar .bullet {
  cursor: pointer;
  position: relative;
  background: url(../assets/bullet_boxed.png) no-Repeat top left;
  width: 18px;
  height: 19px;
  margin-right: 5px;
  float: left;
  margin-top: 0px; }

.tp-bullets.simplebullets.navbar .bullet.first {
  margin-left: 0px !important; }

.tp-bullets.simplebullets.navbar .bullet.last {
  margin-right: 0px !important; }

/** navbar NAVIGATION VERSION **/
.tp-bullets.simplebullets.navbar-old .bullet {
  cursor: pointer;
  position: relative;
  background: url(../assets/navigdots.png) no-Repeat bottom left;
  width: 15px;
  height: 15px;
  margin-left: 5px !important;
  margin-right: 5px !important;
  float: left;
  margin-top: 10px; }

.tp-bullets.simplebullets.navbar-old .bullet.first {
  margin-left: 0px !important; }

.tp-bullets.simplebullets.navbar-old .bullet.last {
  margin-right: 0px !important; }

.tp-bullets.simplebullets .bullet:hover,
.tp-bullets.simplebullets .bullet.selected {
  background-position: top left; }

.tp-bullets.simplebullets.round .bullet:hover,
.tp-bullets.simplebullets.round .bullet.selected,
.tp-bullets.simplebullets.navbar .bullet:hover,
.tp-bullets.simplebullets.navbar .bullet.selected {
  background-position: bottom left; }

/*************************************
    -   TP ARROWS   -
**************************************/
.tparrows {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  -moz-opacity: 1;
  -khtml-opacity: 1;
  opacity: 1;
  -webkit-transition: opacity 0.2s ease-out;
  -moz-transition: opacity 0.2s ease-out;
  -o-transition: opacity 0.2s ease-out;
  -ms-transition: opacity 0.2s ease-out;
  -webkit-transform: translateZ(5000px);
  -webkit-transform-style: flat;
  -webkit-backface-visibility: hidden;
  z-index: 600;
  position: relative; }

.tparrows.hidearrows {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  -moz-opacity: 0;
  -khtml-opacity: 0;
  opacity: 0; }

/*.tp-leftarrow                                           {   z-index:100;cursor:pointer; position:relative;  background:url(../assets/large_left.png) no-Repeat top left;    width:40px; height:40px;   }
.tp-rightarrow                                          {   z-index:100;cursor:pointer; position:relative;  background:url(../assets/large_right.png) no-Repeat top left;   width:40px; height:40px;   }*/
.tp-leftarrow.round {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/small_left.png) no-Repeat top left;
  width: 19px;
  height: 14px;
  margin-right: 0px;
  float: left;
  margin-top: 0px; }

.tp-rightarrow.round {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/small_right.png) no-Repeat top left;
  width: 19px;
  height: 14px;
  margin-right: 0px;
  float: left;
  margin-top: 0px; }

.tp-leftarrow.round-old {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrow_left.png) no-Repeat top left;
  width: 26px;
  height: 26px;
  margin-right: 0px;
  float: left;
  margin-top: 0px; }

.tp-rightarrow.round-old {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrow_right.png) no-Repeat top left;
  width: 26px;
  height: 26px;
  margin-right: 0px;
  float: left;
  margin-top: 0px; }

.tp-leftarrow.navbar {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/small_left_boxed.png) no-Repeat top left;
  width: 20px;
  height: 15px;
  float: left;
  margin-right: 6px;
  margin-top: 12px; }

.tp-rightarrow.navbar {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/small_right_boxed.png) no-Repeat top left;
  width: 20px;
  height: 15px;
  float: left;
  margin-left: 6px;
  margin-top: 12px; }

.tp-leftarrow.navbar-old {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrowleft.png) no-Repeat top left;
  width: 9px;
  height: 16px;
  float: left;
  margin-right: 6px;
  margin-top: 10px; }

.tp-rightarrow.navbar-old {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrowright.png) no-Repeat top left;
  width: 9px;
  height: 16px;
  float: left;
  margin-left: 6px;
  margin-top: 10px; }

.tp-leftarrow.navbar-old.thumbswitharrow {
  margin-right: 10px; }

.tp-rightarrow.navbar-old.thumbswitharrow {
  margin-left: 0px; }

.tp-leftarrow.square {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrow_left2.png) no-Repeat top left;
  width: 12px;
  height: 17px;
  float: left;
  margin-right: 0px;
  margin-top: 0px; }

.tp-rightarrow.square {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrow_right2.png) no-Repeat top left;
  width: 12px;
  height: 17px;
  float: left;
  margin-left: 0px;
  margin-top: 0px; }

.tp-leftarrow.square-old {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrow_left2.png) no-Repeat top left;
  width: 12px;
  height: 17px;
  float: left;
  margin-right: 0px;
  margin-top: 0px; }

.tp-rightarrow.square-old {
  z-index: 100;
  cursor: pointer;
  position: relative;
  background: url(../assets/arrow_right2.png) no-Repeat top left;
  width: 12px;
  height: 17px;
  float: left;
  margin-left: 0px;
  margin-top: 0px; }

/*.tp-leftarrow.default                                   {   z-index:100;cursor:pointer; position:relative;  background:url(../assets/large_left.png) no-Repeat 0 0; width:40px; height:40px;

                                                        }*/
/*.tp-rightarrow.default                                  {   z-index:100;cursor:pointer; position:relative;  background:url(../assets/large_right.png) no-Repeat 0 0;    width:40px; height:40px;

                                                        }*/
.tp-leftarrow:hover,
.tp-rightarrow:hover {
  background-position: bottom left; }

/****************************************************************************************************
    -   TP THUMBS   -
*****************************************************************************************************

 - tp-thumbs & tp-mask Width is the width of the basic Thumb Container (500px basic settings)

 - .bullet width & height is the dimension of a simple Thumbnail (basic 100px x 50px)

 *****************************************************************************************************/
.tp-bullets.tp-thumbs {
  z-index: 1000;
  position: absolute;
  padding: 3px;
  background-color: #fff;
  width: 500px;
  height: 50px;
  /* THE DIMENSIONS OF THE THUMB CONTAINER */
  margin-top: -50px; }

.fullwidthbanner-container .tp-thumbs {
  padding: 3px; }

.tp-bullets.tp-thumbs .tp-mask {
  width: 500px;
  height: 50px;
  /* THE DIMENSIONS OF THE THUMB CONTAINER */
  overflow: hidden;
  position: relative; }

.tp-bullets.tp-thumbs .tp-mask .tp-thumbcontainer {
  width: 5000px;
  position: absolute; }

.tp-bullets.tp-thumbs .bullet {
  width: 100px;
  height: 50px;
  /* THE DIMENSION OF A SINGLE THUMB */
  cursor: pointer;
  overflow: hidden;
  background: none;
  margin: 0;
  float: left;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
  /*filter: alpha(opacity=50);    */
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
  -webkit-transition: all 0.2s ease-out;
  -moz-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  -ms-transition: all 0.2s ease-out; }

.tp-bullets.tp-thumbs .bullet:hover,
.tp-bullets.tp-thumbs .bullet.selected {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  -moz-opacity: 1;
  -khtml-opacity: 1;
  opacity: 1; }

.tp-thumbs img {
  width: 100%; }

/************************************
        -   TP BANNER TIMER     -
*************************************/
.tp-bannertimer {
  width: 100%;
  height: 10px;
  /*background:url(../assets/timer.png);*/
  position: absolute;
  z-index: 200;
  top: 0px; }

.tp-bannertimer.tp-bottom {
  bottom: 0px;
  height: 5px;
  top: auto; }

/***************************************
    -   RESPONSIVE SETTINGS     -
****************************************/
@media only screen and (min-width: 0px) and (max-width: 479px) {
  .responsive .tp-bullets {
    display: none; }

  .responsive .tparrows {
    display: none; } }
/*********************************************

    -   BASIC SETTINGS FOR THE BANNER   -

***********************************************/
.tp-simpleresponsive img {
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none; }

.tp-simpleresponsive a {
  text-decoration: none; }

.tp-simpleresponsive ul {
  list-style: none;
  padding: 0;
  margin: 0; }

.tp-simpleresponsive > ul > li {
  list-stye: none;
  position: absolute;
  visibility: hidden; }

/*  CAPTION SLIDELINK   **/
.caption.slidelink a div,
.tp-caption.slidelink a div {
  width: 3000px;
  height: 1500px;
  background: url(../assets/coloredbg.png) repeat; }

/******************************
    -   LOADER FORMS    -
********************************/
.tp-loader {
  top: 50%;
  left: 50%;
  z-index: 10000;
  position: absolute; }

.tp-loader.spinner0 {
  width: 40px;
  height: 40px;
  /*background:url(../assets/loader.gif) no-repeat center center;*/
  background-color: #fff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  margin-top: -20px;
  margin-left: -20px;
  -webkit-animation: tp-rotateplane 1.2s infinite ease-in-out;
  animation: tp-rotateplane 1.2s infinite ease-in-out;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px; }

.tp-loader.spinner1 {
  width: 40px;
  height: 40px;
  background-color: #fff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  margin-top: -20px;
  margin-left: -20px;
  -webkit-animation: tp-rotateplane 1.2s infinite ease-in-out;
  animation: tp-rotateplane 1.2s infinite ease-in-out;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px; }

.tp-loader.spinner5 {
  background: url(../assets/loader.gif) no-repeat 10px 10px;
  background-color: #fff;
  margin: -22px -22px;
  width: 44px;
  height: 44px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px; }

@-webkit-keyframes tp-rotateplane {
  0% {
    -webkit-transform: perspective(120px); }
  50% {
    -webkit-transform: perspective(120px) rotateY(180deg); }
  100% {
    -webkit-transform: perspective(120px) rotateY(180deg) rotateX(180deg); } }
@keyframes tp-rotateplane {
  0% {
    transform: perspective(120px) rotateX(0deg) rotateY(0deg);
    -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg); }
  50% {
    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
    -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg); }
  100% {
    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
    -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg); } }
.tp-loader.spinner2 {
  width: 40px;
  height: 40px;
  margin-top: -20px;
  margin-left: -20px;
  background-color: #ff0000;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  border-radius: 100%;
  -webkit-animation: tp-scaleout 1.0s infinite ease-in-out;
  animation: tp-scaleout 1.0s infinite ease-in-out; }

@-webkit-keyframes tp-scaleout {
  0% {
    -webkit-transform: scale(0); }
  100% {
    -webkit-transform: scale(1);
    opacity: 0; } }
@keyframes tp-scaleout {
  0% {
    transform: scale(0);
    -webkit-transform: scale(0); }
  100% {
    transform: scale(1);
    -webkit-transform: scale(1);
    opacity: 0; } }
.tp-loader.spinner3 {
  margin: -9px 0px 0px -35px;
  width: 70px;
  text-align: center; }

.tp-loader.spinner3 .bounce1,
.tp-loader.spinner3 .bounce2,
.tp-loader.spinner3 .bounce3 {
  width: 18px;
  height: 18px;
  background-color: #fff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  border-radius: 100%;
  display: inline-block;
  -webkit-animation: tp-bouncedelay 1.4s infinite ease-in-out;
  animation: tp-bouncedelay 1.4s infinite ease-in-out;
  /* Prevent first frame from flickering when animation starts */
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both; }

.tp-loader.spinner3 .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s; }

.tp-loader.spinner3 .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s; }

@-webkit-keyframes tp-bouncedelay {
  0%, 80%, 100% {
    -webkit-transform: scale(0); }
  40% {
    -webkit-transform: scale(1); } }
@keyframes tp-bouncedelay {
  0%, 80%, 100% {
    transform: scale(0);
    -webkit-transform: scale(0); }
  40% {
    transform: scale(1);
    -webkit-transform: scale(1); } }
.tp-loader.spinner4 {
  margin: -20px 0px 0px -20px;
  width: 40px;
  height: 40px;
  text-align: center;
  -webkit-animation: tp-rotate 2.0s infinite linear;
  animation: tp-rotate 2.0s infinite linear; }

.tp-loader.spinner4 .dot1,
.tp-loader.spinner4 .dot2 {
  width: 60%;
  height: 60%;
  display: inline-block;
  position: absolute;
  top: 0;
  background-color: #fff;
  border-radius: 100%;
  -webkit-animation: tp-bounce 2.0s infinite ease-in-out;
  animation: tp-bounce 2.0s infinite ease-in-out;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15); }

.tp-loader.spinner4 .dot2 {
  top: auto;
  bottom: 0px;
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s; }

@-webkit-keyframes tp-rotate {
  100% {
    -webkit-transform: rotate(360deg); } }
@keyframes tp-rotate {
  100% {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg); } }
@-webkit-keyframes tp-bounce {
  0%, 100% {
    -webkit-transform: scale(0); }
  50% {
    -webkit-transform: scale(1); } }
@keyframes tp-bounce {
  0%, 100% {
    transform: scale(0);
    -webkit-transform: scale(0); }
  50% {
    transform: scale(1);
    -webkit-transform: scale(1); } }
.tp-transparentimg {
  content: "url(../assets/transparent.png)"; }

.tp-3d {
  -webkit-transform-style: preserve-3d;
  -webkit-transform-origin: 50% 50%; }

.tp-caption img {
  background: transparent;
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF,endColorstr=#00FFFFFF)";
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF,endColorstr=#00FFFFFF);
  zoom: 1; }

@font-face {
  font-family: 'revicons';
  src: url("../font/revicons.eot?5510888");
  src: url("../font/revicons.eot?5510888#iefix") format("embedded-opentype"), url("../font/revicons.woff?5510888") format("woff"), url("../font/revicons.ttf?5510888") format("truetype"), url("../font/revicons.svg?5510888#revicons") format("svg");
  font-weight: normal;
  font-style: normal; }
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'revicons';
    src: url('../font/revicons.svg?5510888#revicons') format('svg');
  }
}
*/
[class^="revicon-"]:before, [class*=" revicon-"]:before {
  font-family: "revicons";
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */ }

.revicon-search-1:before {
  content: '\e802'; }

/* '' */
.revicon-pencil-1:before {
  content: '\e831'; }

/* '' */
.revicon-picture-1:before {
  content: '\e803'; }

/* '' */
.revicon-cancel:before {
  content: '\e80a'; }

/* '' */
.revicon-info-circled:before {
  content: '\e80f'; }

/* '' */
.revicon-trash:before {
  content: '\e801'; }

/* '' */
.revicon-left-dir:before {
  content: '\e817'; }

/* '' */
.revicon-right-dir:before {
  content: '\e818'; }

/* '' */
.revicon-down-open:before {
  content: '\e83b'; }

/* '' */
.revicon-left-open:before {
  content: '\e819'; }

/* '' */
.revicon-right-open:before {
  content: '\e81a'; }

/* '' */
.revicon-angle-left:before {
  content: '\e820'; }

/* '' */
.revicon-angle-right:before {
  content: '\e81d'; }

/* '' */
.revicon-left-big:before {
  content: '\e81f'; }

/* '' */
.revicon-right-big:before {
  content: '\e81e'; }

/* '' */
.revicon-magic:before {
  content: '\e807'; }

/* '' */
.revicon-picture:before {
  content: '\e800'; }

/* '' */
.revicon-export:before {
  content: '\e80b'; }

/* '' */
.revicon-cog:before {
  content: '\e832'; }

/* '' */
.revicon-login:before {
  content: '\e833'; }

/* '' */
.revicon-logout:before {
  content: '\e834'; }

/* '' */
.revicon-video:before {
  content: '\e805'; }

/* '' */
.revicon-arrow-combo:before {
  content: '\e827'; }

/* '' */
.revicon-left-open-1:before {
  content: '\e82a'; }

/* '' */
.revicon-right-open-1:before {
  content: '\e82b'; }

/* '' */
.revicon-left-open-mini:before {
  content: '\e822'; }

/* '' */
.revicon-right-open-mini:before {
  content: '\e823'; }

/* '' */
.revicon-left-open-big:before {
  content: '\e824'; }

/* '' */
.revicon-right-open-big:before {
  content: '\e825'; }

/* '' */
.revicon-left:before {
  content: '\e836'; }

/* '' */
.revicon-right:before {
  content: '\e826'; }

/* '' */
.revicon-ccw:before {
  content: '\e808'; }

/* '' */
.revicon-arrows-ccw:before {
  content: '\e806'; }

/* '' */
.revicon-palette:before {
  content: '\e829'; }

/* '' */
.revicon-list-add:before {
  content: '\e80c'; }

/* '' */
.revicon-doc:before {
  content: '\e809'; }

/* '' */
.revicon-left-open-outline:before {
  content: '\e82e'; }

/* '' */
.revicon-left-open-2:before {
  content: '\e82c'; }

/* '' */
.revicon-right-open-outline:before {
  content: '\e82f'; }

/* '' */
.revicon-right-open-2:before {
  content: '\e82d'; }

/* '' */
.revicon-equalizer:before {
  content: '\e83a'; }

/* '' */
.revicon-layers-alt:before {
  content: '\e804'; }

/* '' */
.revicon-popup:before {
  content: '\e828'; }

/* '' */
/*-----------------------------------------------------------------------------

REVOLUTION RESPONSIVE BASIC STYLES OF HTML DOCUMENT

Screen Stylesheet

version:   	1.0
date:      	26/06/12
author:		themepunch
email:     	<EMAIL>
website:   	http://www.themepunch.com
-----------------------------------------------------------------------------*/
  /*********************************************************************************************
		-	SET THE SCREEN SIZES FOR THE BANNER IF YOU WISH TO MAKE THE BANNER RESOPONSIVE 	-
  **********************************************************************************************/
/*	-	THE BANNER CONTAINER (Padding, Shadow, Border etc. )	-	*/
.fullwidthbanner-container {
  width: 100% !important;
  position: relative;
  padding: 0;
  margin-top: 0px;
  overflow: hidden;
  z-index: 98;
  background-color: #b2b1b7; }

.fullwidthbanner-container .fullwidthbanner {
  width: 100% !important;
  position: relative;
  padding: 0;
  margin-top: 0px;
  overflow: hidden; }

                 /*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 Daniel Eden
*/
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both; }

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite; }

.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s; }

.animated.bounceIn, .animated.bounceOut, .animated.flipOutX, .animated.flipOutY {
  -webkit-animation-duration: .75s;
  animation-duration: .75s; }

@-webkit-keyframes bounce {
  0%,100%,20%,53%,80% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  40%,43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0); }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0); }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0); } }
@keyframes bounce {
  0%,100%,20%,53%,80% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  40%,43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0); }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0); }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0); } }
.bounce {
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom; }

@-webkit-keyframes flash {
  0%,100%,50% {
    opacity: 1; }
  25%,75% {
    opacity: 0; } }
@keyframes flash {
  0%,100%,50% {
    opacity: 1; }
  25%,75% {
    opacity: 0; } }
.flash {
  -webkit-animation-name: flash;
  animation-name: flash; }

@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05); }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
@keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05); }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse; }

@-webkit-keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1); }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1); }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1); }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1); }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1); }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
@keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1); }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1); }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1); }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1); }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1); }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand; }

@-webkit-keyframes shake {
  0%,100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  10%,30%,50%,70%,90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  20%,40%,60%,80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); } }
@keyframes shake {
  0%,100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  10%,30%,50%,70%,90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  20%,40%,60%,80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); } }
.shake {
  -webkit-animation-name: shake;
  animation-name: shake; }

@-webkit-keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg); }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg); }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg); }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg); }
  100% {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg); } }
@keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg); }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg); }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg); }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg); }
  100% {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg); } }
.swing {
  -webkit-transform-origin: top center;
  transform-origin: top center;
  -webkit-animation-name: swing;
  animation-name: swing; }

@-webkit-keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); }
  10%,20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg); }
  30%,50%,70%,90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg); }
  40%,60%,80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg); }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
@keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); }
  10%,20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg); }
  30%,50%,70%,90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg); }
  40%,60%,80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg); }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
.tada {
  -webkit-animation-name: tada;
  animation-name: tada; }

@-webkit-keyframes wobble {
  0% {
    -webkit-transform: none;
    transform: none; }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }
  100% {
    -webkit-transform: none;
    transform: none; } }
@keyframes wobble {
  0% {
    -webkit-transform: none;
    transform: none; }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }
  100% {
    -webkit-transform: none;
    transform: none; } }
.wobble {
  -webkit-animation-name: wobble;
  animation-name: wobble; }

@-webkit-keyframes jello {
  0%,100%,11.1% {
    -webkit-transform: none;
    transform: none; }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg); }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg); }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg); }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg); }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg); }
  77.7% {
    -webkit-transform: skewX(0.39063deg) skewY(0.39063deg);
    transform: skewX(0.39063deg) skewY(0.39063deg); }
  88.8% {
    -webkit-transform: skewX(-0.19531deg) skewY(-0.19531deg);
    transform: skewX(-0.19531deg) skewY(-0.19531deg); } }
@keyframes jello {
  0%,100%,11.1% {
    -webkit-transform: none;
    transform: none; }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg); }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg); }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg); }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg); }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg); }
  77.7% {
    -webkit-transform: skewX(0.39063deg) skewY(0.39063deg);
    transform: skewX(0.39063deg) skewY(0.39063deg); }
  88.8% {
    -webkit-transform: skewX(-0.19531deg) skewY(-0.19531deg);
    transform: skewX(-0.19531deg) skewY(-0.19531deg); } }
.jello {
  -webkit-animation-name: jello;
  animation-name: jello;
  -webkit-transform-origin: center;
  transform-origin: center; }

@-webkit-keyframes bounceIn {
  0%,100%,20%,40%,60%,80% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03); }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97); }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
@keyframes bounceIn {
  0%,100%,20%,40%,60%,80% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03); }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97); }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1); } }
.bounceIn {
  -webkit-animation-name: bounceIn;
  animation-name: bounceIn; }

@-webkit-keyframes bounceInDown {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0); }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0); }
  100% {
    -webkit-transform: none;
    transform: none; } }
@keyframes bounceInDown {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0); }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0); }
  100% {
    -webkit-transform: none;
    transform: none; } }
.bounceInDown {
  -webkit-animation-name: bounceInDown;
  animation-name: bounceInDown; }

@-webkit-keyframes bounceInLeft {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0); }
  100% {
    -webkit-transform: none;
    transform: none; } }
@keyframes bounceInLeft {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0); }
  100% {
    -webkit-transform: none;
    transform: none; } }
.bounceInLeft {
  -webkit-animation-name: bounceInLeft;
  animation-name: bounceInLeft; }

@-webkit-keyframes bounceInRight {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0); }
  100% {
    -webkit-transform: none;
    transform: none; } }
@keyframes bounceInRight {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0); }
  100% {
    -webkit-transform: none;
    transform: none; } }
.bounceInRight {
  -webkit-animation-name: bounceInRight;
  animation-name: bounceInRight; }

@-webkit-keyframes bounceInUp {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0); }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@keyframes bounceInUp {
  0%,100%,60%,75%,90% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0); }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0); }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
.bounceInUp {
  -webkit-animation-name: bounceInUp;
  animation-name: bounceInUp; }

@-webkit-keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  50%,55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  100% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); } }
@keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  50%,55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  100% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); } }
.bounceOut {
  -webkit-animation-name: bounceOut;
  animation-name: bounceOut; }

@-webkit-keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  40%,45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0); } }
@keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  40%,45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0); } }
.bounceOutDown {
  -webkit-animation-name: bounceOutDown;
  animation-name: bounceOutDown; }

@-webkit-keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0); } }
@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0); } }
.bounceOutLeft {
  -webkit-animation-name: bounceOutLeft;
  animation-name: bounceOutLeft; }

@-webkit-keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0); } }
@keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0); } }
.bounceOutRight {
  -webkit-animation-name: bounceOutRight;
  animation-name: bounceOutRight; }

@-webkit-keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  40%,45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0); } }
@keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  40%,45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0); }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0); } }
.bounceOutUp {
  -webkit-animation-name: bounceOutUp;
  animation-name: bounceOutUp; }

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn; }

@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown; }

@-webkit-keyframes fadeInDownBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInDownBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig; }

@-webkit-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft; }

@-webkit-keyframes fadeInLeftBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInLeftBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInLeftBig {
  -webkit-animation-name: fadeInLeftBig;
  animation-name: fadeInLeftBig; }

@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight; }

@-webkit-keyframes fadeInRightBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInRightBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInRightBig {
  -webkit-animation-name: fadeInRightBig;
  animation-name: fadeInRightBig; }

@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp; }

@-webkit-keyframes fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.fadeInUpBig {
  -webkit-animation-name: fadeInUpBig;
  animation-name: fadeInUpBig; }

@-webkit-keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
@keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut; }

@-webkit-keyframes fadeOutDown {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0); } }
@keyframes fadeOutDown {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0); } }
.fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown; }

@-webkit-keyframes fadeOutDownBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0); } }
@keyframes fadeOutDownBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0); } }
.fadeOutDownBig {
  -webkit-animation-name: fadeOutDownBig;
  animation-name: fadeOutDownBig; }

@-webkit-keyframes fadeOutLeft {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0); } }
@keyframes fadeOutLeft {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0); } }
.fadeOutLeft {
  -webkit-animation-name: fadeOutLeft;
  animation-name: fadeOutLeft; }

@-webkit-keyframes fadeOutLeftBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0); } }
@keyframes fadeOutLeftBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0); } }
.fadeOutLeftBig {
  -webkit-animation-name: fadeOutLeftBig;
  animation-name: fadeOutLeftBig; }

@-webkit-keyframes fadeOutRight {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0); } }
@keyframes fadeOutRight {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0); } }
.fadeOutRight {
  -webkit-animation-name: fadeOutRight;
  animation-name: fadeOutRight; }

@-webkit-keyframes fadeOutRightBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0); } }
@keyframes fadeOutRightBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0); } }
.fadeOutRightBig {
  -webkit-animation-name: fadeOutRightBig;
  animation-name: fadeOutRightBig; }

@-webkit-keyframes fadeOutUp {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0); } }
@keyframes fadeOutUp {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0); } }
.fadeOutUp {
  -webkit-animation-name: fadeOutUp;
  animation-name: fadeOutUp; }

@-webkit-keyframes fadeOutUpBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0); } }
@keyframes fadeOutUpBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0); } }
.fadeOutUpBig {
  -webkit-animation-name: fadeOutUpBig;
  animation-name: fadeOutUpBig; }

@-webkit-keyframes flip {
  0% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; } }
@keyframes flip {
  0% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; } }
.animated.flip {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
  -webkit-animation-name: flip;
  animation-name: flip; }

@-webkit-keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0; }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1; }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg); }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }
@keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0; }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1; }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg); }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }
.flipInX {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInX;
  animation-name: flipInX; }

@-webkit-keyframes flipInY {
  0% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0; }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1; }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg); }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }
@keyframes flipInY {
  0% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0; }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1; }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg); }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }
.flipInY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInY;
  animation-name: flipInY; }

@-webkit-keyframes flipOutX {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1; }
  100% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0; } }
@keyframes flipOutX {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1; }
  100% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0; } }
.flipOutX {
  -webkit-animation-name: flipOutX;
  animation-name: flipOutX;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important; }

@-webkit-keyframes flipOutY {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1; }
  100% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0; } }
@keyframes flipOutY {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1; }
  100% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0; } }
.flipOutY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipOutY;
  animation-name: flipOutY; }

@-webkit-keyframes lightSpeedIn {
  0% {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0; }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1; }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1; }
  100% {
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
@keyframes lightSpeedIn {
  0% {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0; }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1; }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1; }
  100% {
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
.lightSpeedIn {
  -webkit-animation-name: lightSpeedIn;
  animation-name: lightSpeedIn;
  -webkit-animation-timing-function: ease-out;
  animation-timing-function: ease-out; }

@-webkit-keyframes lightSpeedOut {
  0% {
    opacity: 1; }
  100% {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0; } }
@keyframes lightSpeedOut {
  0% {
    opacity: 1; }
  100% {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0; } }
.lightSpeedOut {
  -webkit-animation-name: lightSpeedOut;
  animation-name: lightSpeedOut;
  -webkit-animation-timing-function: ease-in;
  animation-timing-function: ease-in; }

@-webkit-keyframes rotateIn {
  0% {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
@keyframes rotateIn {
  0% {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
.rotateIn {
  -webkit-animation-name: rotateIn;
  animation-name: rotateIn; }

@-webkit-keyframes rotateInDownLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
@keyframes rotateInDownLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
.rotateInDownLeft {
  -webkit-animation-name: rotateInDownLeft;
  animation-name: rotateInDownLeft; }

@-webkit-keyframes rotateInDownRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
@keyframes rotateInDownRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
.rotateInDownRight {
  -webkit-animation-name: rotateInDownRight;
  animation-name: rotateInDownRight; }

@-webkit-keyframes rotateInUpLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
@keyframes rotateInUpLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
.rotateInUpLeft {
  -webkit-animation-name: rotateInUpLeft;
  animation-name: rotateInUpLeft; }

@-webkit-keyframes rotateInUpRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
@keyframes rotateInUpRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1; } }
.rotateInUpRight {
  -webkit-animation-name: rotateInUpRight;
  animation-name: rotateInUpRight; }

@-webkit-keyframes rotateOut {
  0% {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1; }
  100% {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0; } }
@keyframes rotateOut {
  0% {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1; }
  100% {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0; } }
.rotateOut {
  -webkit-animation-name: rotateOut;
  animation-name: rotateOut; }

@-webkit-keyframes rotateOutDownLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; } }
@keyframes rotateOutDownLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; } }
.rotateOutDownLeft {
  -webkit-animation-name: rotateOutDownLeft;
  animation-name: rotateOutDownLeft; }

@-webkit-keyframes rotateOutDownRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; } }
@keyframes rotateOutDownRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; } }
.rotateOutDownRight {
  -webkit-animation-name: rotateOutDownRight;
  animation-name: rotateOutDownRight; }

@-webkit-keyframes rotateOutUpLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; } }
@keyframes rotateOutUpLeft {
  0% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; } }
.rotateOutUpLeft {
  -webkit-animation-name: rotateOutUpLeft;
  animation-name: rotateOutUpLeft; }

@-webkit-keyframes rotateOutUpRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0; } }
@keyframes rotateOutUpRight {
  0% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1; }
  100% {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0; } }
.rotateOutUpRight {
  -webkit-animation-name: rotateOutUpRight;
  animation-name: rotateOutUpRight; }

@-webkit-keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out; }
  20%,60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out; }
  40%,80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1; }
  100% {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0; } }
@keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out; }
  20%,60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out; }
  40%,80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1; }
  100% {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0; } }
.hinge {
  -webkit-animation-name: hinge;
  animation-name: hinge; }

@-webkit-keyframes rollIn {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
@keyframes rollIn {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg); }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }
.rollIn {
  -webkit-animation-name: rollIn;
  animation-name: rollIn; }

@-webkit-keyframes rollOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg); } }
@keyframes rollOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg); } }
.rollOut {
  -webkit-animation-name: rollOut;
  animation-name: rollOut; }

@-webkit-keyframes zoomIn {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  50% {
    opacity: 1; } }
@keyframes zoomIn {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  50% {
    opacity: 1; } }
.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn; }

@-webkit-keyframes zoomInDown {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
@keyframes zoomInDown {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
.zoomInDown {
  -webkit-animation-name: zoomInDown;
  animation-name: zoomInDown; }

@-webkit-keyframes zoomInLeft {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
@keyframes zoomInLeft {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
.zoomInLeft {
  -webkit-animation-name: zoomInLeft;
  animation-name: zoomInLeft; }

@-webkit-keyframes zoomInRight {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
@keyframes zoomInRight {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
.zoomInRight {
  -webkit-animation-name: zoomInRight;
  animation-name: zoomInRight; }

@-webkit-keyframes zoomInUp {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
@keyframes zoomInUp {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
.zoomInUp {
  -webkit-animation-name: zoomInUp;
  animation-name: zoomInUp; }

@-webkit-keyframes zoomOut {
  0% {
    opacity: 1; }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  100% {
    opacity: 0; } }
@keyframes zoomOut {
  0% {
    opacity: 1; }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3); }
  100% {
    opacity: 0; } }
.zoomOut {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut; }

@-webkit-keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  100% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
@keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  100% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
.zoomOutDown {
  -webkit-animation-name: zoomOutDown;
  animation-name: zoomOutDown; }

@-webkit-keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center; } }
@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center; } }
.zoomOutLeft {
  -webkit-animation-name: zoomOutLeft;
  animation-name: zoomOutLeft; }

@-webkit-keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center; } }
@keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0); }
  100% {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center; } }
.zoomOutRight {
  -webkit-animation-name: zoomOutRight;
  animation-name: zoomOutRight; }

@-webkit-keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  100% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
@keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  100% {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
.zoomOutUp {
  -webkit-animation-name: zoomOutUp;
  animation-name: zoomOutUp; }

@-webkit-keyframes slideInDown {
  0% {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@keyframes slideInDown {
  0% {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
.slideInDown {
  -webkit-animation-name: slideInDown;
  animation-name: slideInDown; }

@-webkit-keyframes slideInLeft {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@keyframes slideInLeft {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
.slideInLeft {
  -webkit-animation-name: slideInLeft;
  animation-name: slideInLeft; }

@-webkit-keyframes slideInRight {
  0% {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@keyframes slideInRight {
  0% {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
.slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight; }

@-webkit-keyframes slideInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@keyframes slideInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible; }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
.slideInUp {
  -webkit-animation-name: slideInUp;
  animation-name: slideInUp; }

@-webkit-keyframes slideOutDown {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0); } }
@keyframes slideOutDown {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0); } }
.slideOutDown {
  -webkit-animation-name: slideOutDown;
  animation-name: slideOutDown; }

@-webkit-keyframes slideOutLeft {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0); } }
@keyframes slideOutLeft {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0); } }
.slideOutLeft {
  -webkit-animation-name: slideOutLeft;
  animation-name: slideOutLeft; }

@-webkit-keyframes slideOutRight {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0); } }
@keyframes slideOutRight {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0); } }
.slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight; }

@-webkit-keyframes slideOutUp {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0); } }
@keyframes slideOutUp {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  100% {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0); } }
.slideOutUp {
  -webkit-animation-name: slideOutUp;
  animation-name: slideOutUp; }

/* Responsive CSS Styles Table of Contents */
@media only screen and (min-width: 1000px) and (max-width: 1169px) {
  .container {
    width: 1000px;
    margin: auto auto auto auto; }

  a.button_slider {
    padding: 13px 18px; }

  .our_team .one_fourth img {
    width: 100%; }

  .four_col_fusection .one_fourth {
    margin-right: 0.7%; }

  .portfolio_page .imgWrap {
    position: relative;
    width: 325px; }

  .portfolio_page h3 {
    width: 325px; }

  .portfolio_page .imgDescription i {
    left: 140px; }

  ul.post_meta_links {
    margin: -10px 0px 0px 10.3%;
    width: 80%; }

  .our_team ul.people_soci {
    margin: 0px 0px 0px 50px; }

  .punch_text {
    font-size: 20px; }

  .layout1_fusection1 .one_third img {
    width: 100%; } }
@media only screen and (min-width: 768px) and (max-width: 999px) {
  .container {
    width: 747px;
    margin: auto auto auto auto; }

  a.button_slider {
    padding: 9px 10px;
    font-size: 14px; }

  .our_team .one_fourth img {
    width: 100%; }

  .our_team ul.people_soci {
    margin: 0px 0px 0px 30px; }

  .parallax_sec1 {
    padding: 50px 0px 0px 0px; }

  .four_col_fusection .one_fourth {
    margin-right: 0.7%; }

  .portfolio_page .imgWrap {
    position: relative;
    width: 239px;
    height: 200px; }

  .portfolio_page .imgWrap img {
    float: left;
    width: 100%;
    height: 200px; }

  .portfolio_page h3 {
    width: 239px; }

  .portfolio_page .imgDescription i {
    top: 80px;
    left: 100px; }

  .footer_social_links li a {
    width: 107px; }

  .punch_text {
    padding: 10px 0px;
    font-size: 14px; }

  .punch_text a {
    padding: 10px 18px;
    font-size: 14px;
    margin-top: 16px; }

  .punch_text b em {
    font-size: 12px;
    margin-top: 0px; }

  .punch_text b {
    font-weight: normal; }

  .layout1_fusection1 h1 {
    font-size: 30px; }

  .layout1_fusection1 .one_third h3 {
    font-size: 18px; }

  .layout1_fusection1 .one_third img {
    width: 100%; } }
@media only screen and (min-width: 480px) and (max-width: 767px) {
  #trueHeader #logo {
    background: url(../images/logo.png) no-repeat center bottom; }

  a.button_slider {
    padding: 5px 5px;
    font-size: 10px;
    border-radius: 2px; }

  #header .one_fourth {
    margin-bottom: 10px; }

  #top-nav {
    float: left; }

  #top-nav ul {
    float: left; }

  #top-nav #fixed-nav li a {
    height: 50px;
    line-height: 50px;
    padding: 0 10px; }

  .our_team .one_fourth {
    width: 95%;
    margin-bottom: 70px;
    margin-right: 0%; }

  .our_team ul.people_soci {
    margin: 0px 0px 0px 160px; }

  .parallax_sec1 {
    height: 320px;
    padding: 80px 0px 0px 0px; }

  .parallax_sec1 h1 {
    font-size: 40px;
    line-height: 40px; }

  .four_col_fusection .one_fourth {
    width: 99%;
    border-right: none;
    margin-bottom: 40px; }

  .one_full {
    width: 100%; }

  .one_half {
    width: 100%;
    margin-bottom: 30px; }

  .one_third {
    width: 100%;
    margin-bottom: 30px; }

  .one_fourth {
    width: 100%;
    margin-bottom: 30px; }

  .one_fifth {
    width: 100%;
    margin-bottom: 30px; }

  .two_third {
    width: 100%;
    margin-bottom: 30px; }

  .three_fourth {
    width: 100%;
    margin-bottom: 30px; }

  .last {
    margin-right: 0 !important;
    clear: right;
    margin-bottom: 0 !important; }

  .punch_text {
    padding: 10px 0px 20px 0px; }

  .punch_text b {
    line-height: 27px; }

  .punch_text a {
    float: left;
    font-size: 18px;
    padding: 13px 22px; }

  .layout1_fusection1 .one_third {
    width: 100%;
    margin-right: 0%; } }
@media only screen and (max-width: 479px) {
  #trueHeader #logo {
    background: url(../images/logo.png) no-repeat center bottom; }

  a.button_slider {
    padding: 3px 2px;
    font-size: 7px;
    border-radius: 2px; }

  #header .one_fourth {
    margin-bottom: 10px; }

  #top-nav {
    float: left; }

  #top-nav ul {
    float: left; }

  #top-nav #fixed-nav li a {
    height: 50px;
    line-height: 50px;
    padding: 0 5px;
    font-size: 12px; }

  .our_team .one_fourth {
    width: 95%;
    margin-bottom: 50px;
    margin-right: 0%; }

  .our_team ul.people_soci {
    margin: 0px 0px 0px 90px; }

  .parallax_sec1 {
    height: 350px;
    padding: 50px 0px 0px 0px; }

  .parallax_sec1 h1 {
    font-size: 40px;
    line-height: 40px; }

  .four_col_fusection .one_fourth {
    width: 99%;
    border-right: none;
    margin-bottom: 40px; }

  .one_full {
    width: 100%; }

  .one_half {
    width: 100%;
    margin-bottom: 30px; }

  .one_third {
    width: 100%;
    margin-bottom: 30px; }

  .one_fourth {
    width: 100%;
    margin-bottom: 30px; }

  .one_fifth {
    width: 100%;
    margin-bottom: 30px; }

  .two_third {
    width: 100%;
    margin-bottom: 30px; }

  .three_fourth {
    width: 100%;
    margin-bottom: 30px; }

  .last {
    margin-right: 0 !important;
    clear: right;
    margin-bottom: 0 !important; }

  .top_contact_info {
    min-height: 90px; }

  .punch_text {
    padding: 10px 0px 20px 0px; }

  .punch_text b {
    line-height: 27px; }

  .punch_text a {
    float: left;
    font-size: 18px;
    padding: 13px 22px; }

  .layout1_fusection1 .one_third {
    width: 100%;
    margin-right: 0%; }

  .layout1_fusection1 .one_third img {
    width: 100%; } }
.toast-title {
  font-weight: bold; }

.toast-message {
  -ms-word-wrap: break-word;
  word-wrap: break-word; }

.toast-message a,
.toast-message label {
  color: #FFFFFF; }

.toast-message a:hover {
  color: #CCCCCC;
  text-decoration: none; }

.toast-close-button {
  position: relative;
  right: -0.3em;
  top: -0.3em;
  float: right;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  -webkit-text-shadow: 0 1px 0 #ffffff;
  text-shadow: 0 1px 0 #ffffff;
  opacity: 0.8;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
  filter: alpha(opacity=80);
  line-height: 1; }

.toast-close-button:hover,
.toast-close-button:focus {
  color: #000000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.4;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
  filter: alpha(opacity=40); }

.rtl .toast-close-button {
  left: -0.3em;
  float: left;
  right: 0.3em; }

/*Additional properties for button version
 iOS requires the button element instead of an anchor tag.
 If you want the anchor version, it requires `href="#"`.*/
button.toast-close-button {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none; }

.toast-top-center {
  top: 0;
  right: 0;
  width: 100%; }

.toast-bottom-center {
  bottom: 0;
  right: 0;
  width: 100%; }

.toast-top-full-width {
  top: 0;
  right: 0;
  width: 100%; }

.toast-bottom-full-width {
  bottom: 0;
  right: 0;
  width: 100%; }

.toast-top-left {
  top: 12px;
  left: 12px; }

.toast-top-right {
  top: 12px;
  right: 12px; }

.toast-bottom-right {
  right: 12px;
  bottom: 12px; }

.toast-bottom-left {
  bottom: 12px;
  left: 12px; }

#toast-container {
  position: fixed;
  z-index: 999999;
  pointer-events: none;
  /*overrides*/ }

#toast-container * {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

#toast-container > div {
  position: relative;
  pointer-events: auto;
  overflow: hidden;
  margin: 0 0 6px;
  padding: 15px 15px 15px 50px;
  width: 300px;
  -moz-border-radius: 3px 3px 3px 3px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
  background-position: 15px center;
  background-repeat: no-repeat;
  -moz-box-shadow: 0 0 12px #999999;
  -webkit-box-shadow: 0 0 12px #999999;
  box-shadow: 0 0 12px #999999;
  color: #FFFFFF;
  opacity: 0.8;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
  filter: alpha(opacity=80); }

#toast-container > div.rtl {
  direction: rtl;
  padding: 15px 50px 15px 15px;
  background-position: right 15px center; }

#toast-container > div:hover {
  -moz-box-shadow: 0 0 12px #000000;
  -webkit-box-shadow: 0 0 12px #000000;
  box-shadow: 0 0 12px #000000;
  opacity: 1;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  filter: alpha(opacity=100);
  cursor: pointer; }

#toast-container > .toast-info {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=") !important; }

#toast-container > .toast-error {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=") !important; }

#toast-container > .toast-success {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==") !important; }

#toast-container > .toast-warning {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=") !important; }

#toast-container.toast-top-center > div,
#toast-container.toast-bottom-center > div {
  width: 300px;
  margin-left: auto;
  margin-right: auto; }

#toast-container.toast-top-full-width > div,
#toast-container.toast-bottom-full-width > div {
  width: 96%;
  margin-left: auto;
  margin-right: auto; }

.toast {
  background-color: #030303; }

.toast-success {
  background-color: #51A351; }

.toast-error {
  background-color: #BD362F; }

.toast-info {
  background-color: #2F96B4; }

.toast-warning {
  background-color: #F89406; }

.toast-progress {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 4px;
  background-color: #000000;
  opacity: 0.4;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
  filter: alpha(opacity=40); }

/*Responsive Design*/
@media all and (max-width: 240px) {
  #toast-container > div {
    padding: 8px 8px 8px 50px;
    width: 11em; }

  #toast-container > div.rtl {
    padding: 8px 50px 8px 8px; }

  #toast-container .toast-close-button {
    right: -0.2em;
    top: -0.2em; }

  #toast-container .rtl .toast-close-button {
    left: -0.2em;
    right: 0.2em; } }
@media all and (min-width: 241px) and (max-width: 480px) {
  #toast-container > div {
    padding: 8px 8px 8px 50px;
    width: 18em; }

  #toast-container > div.rtl {
    padding: 8px 50px 8px 8px; }

  #toast-container .toast-close-button {
    right: -0.2em;
    top: -0.2em; }

  #toast-container .rtl .toast-close-button {
    left: -0.2em;
    right: 0.2em; } }
@media all and (min-width: 481px) and (max-width: 768px) {
  #toast-container > div {
    padding: 15px 15px 15px 50px;
    width: 25em; }

  #toast-container > div.rtl {
    padding: 15px 50px 15px 15px; } }
.select2-container {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  position: relative;
  vertical-align: middle;
  width: 100% !important; }

.select2-container .select2-selection--single {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  height: 28px;
  user-select: none;
  -webkit-user-select: none; }

.select2-container .select2-selection--single .select2-selection__rendered {
  display: block;
  padding-left: 8px;
  padding-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }

.select2-container .select2-selection--single .select2-selection__clear {
  position: relative; }

.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
  padding-right: 8px;
  padding-left: 20px; }

.select2-container .select2-selection--multiple {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  min-height: 32px;
  user-select: none;
  -webkit-user-select: none; }

.select2-container .select2-selection--multiple .select2-selection__rendered {
  display: inline-block;
  overflow: hidden;
  padding-left: 8px;
  text-overflow: ellipsis;
  white-space: nowrap; }

.select2-container .select2-search--inline {
  float: left; }

.select2-container .select2-search--inline .select2-search__field {
  box-sizing: border-box;
  border: none;
  font-size: 100%;
  margin-top: 5px;
  padding: 0; }

.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none; }

.select2-dropdown {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  box-sizing: border-box;
  display: block;
  position: absolute;
  left: -100000px;
  width: 100%;
  z-index: 1051; }

.select2-results {
  display: block; }

.select2-results__options {
  list-style: none;
  margin: 0;
  padding: 0; }

.select2-results__option {
  padding: 6px;
  user-select: none;
  -webkit-user-select: none; }

.select2-results__option[aria-selected] {
  cursor: pointer; }

.select2-container--open .select2-dropdown {
  left: 0; }

.select2-container--open .select2-dropdown--above {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0; }

.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0; }

.select2-search--dropdown {
  display: block;
  padding: 4px; }

.select2-search--dropdown .select2-search__field {
  padding: 4px;
  width: 100%;
  box-sizing: border-box; }

.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none; }

.select2-search--dropdown.select2-search--hide {
  display: none; }

.select2-close-mask {
  border: 0;
  margin: 0;
  padding: 0;
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  min-height: 100%;
  min-width: 100%;
  height: auto;
  width: auto;
  opacity: 0;
  z-index: 99;
  background-color: #fff;
  filter: alpha(opacity=0); }

.select2-hidden-accessible {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  height: 1px !important;
  margin: -1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important; }

.select2-container--default .select2-selection--single {
  background-color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px; }

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #444;
  line-height: 28px; }

.select2-container--default .select2-selection--single .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold; }

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #999; }

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 26px;
  position: absolute;
  top: 1px;
  right: 1px;
  width: 20px; }

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #888 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0; }

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
  float: left; }

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
  left: 1px;
  right: auto; }

.select2-container--default.select2-container--disabled .select2-selection--single {
  background-color: #eee;
  cursor: default; }

.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
  display: none; }

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #888 transparent;
  border-width: 0 4px 5px 4px; }

.select2-container--default .select2-selection--multiple {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 0;
  cursor: text; }

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  box-sizing: border-box;
  list-style: none;
  margin: 0;
  padding: 0 5px;
  width: 100%; }

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
  color: #999;
  margin-top: 5px;
  float: left; }

.select2-container--default .select2-selection--multiple .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
  margin-top: 5px;
  margin-right: 10px; }

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f5f7fa;
  border: 1px solid #ddd;
  color: #656d78;
  border-radius: 0;
  cursor: default;
  float: left;
  margin-right: 5px;
  margin-top: 5px;
  padding: 5px; }

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: red;
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  margin-left: 7px;
  font-size: 22px;
  float: right;
  line-height: 0.7em; }

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #333; }

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
  float: right; }

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto; }

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  margin-left: 2px;
  margin-right: auto; }

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: solid #ddd 1px;
  outline: 0;
  border-radius: 0; }

.select2-container--default.select2-container--disabled .select2-selection--multiple {
  background-color: #eee;
  cursor: default; }

.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
  display: none; }

.select2-container--default.select2-container--open.select2-container--above .select2-selection--single, .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
  border-top-left-radius: 0;
  border-top-right-radius: 0; }

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0; }

.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa; }

.select2-container--default .select2-search--inline .select2-search__field {
  background: transparent;
  border: none;
  outline: 0;
  box-shadow: none;
  -webkit-appearance: textfield; }

.select2-container--default .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto; }

.select2-container--default .select2-results__option[role=group] {
  padding: 0; }

.select2-container--default .select2-results__option[aria-disabled=true] {
  color: #999; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #ddd; }

.select2-container--default .select2-results__option .select2-results__option {
  padding-left: 1em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
  padding-left: 0; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -1em;
  padding-left: 2em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -2em;
  padding-left: 3em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -3em;
  padding-left: 4em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -4em;
  padding-left: 5em; }

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -5em;
  padding-left: 6em; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #5897fb;
  color: white; }

.select2-container--default .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px; }

.select2-container--classic .select2-selection--single {
  background-color: #f7f7f7;
  border: 1px solid #aaa;
  border-radius: 4px;
  outline: 0;
  background-image: -webkit-linear-gradient(top, white 50%, #eeeeee 100%);
  background-image: -o-linear-gradient(top, white 50%, #eeeeee 100%);
  background-image: linear-gradient(to bottom, white 50%, #eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0); }

.select2-container--classic .select2-selection--single:focus {
  border: 1px solid #5897fb; }

.select2-container--classic .select2-selection--single .select2-selection__rendered {
  color: #444;
  line-height: 28px; }

.select2-container--classic .select2-selection--single .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
  margin-right: 10px; }

.select2-container--classic .select2-selection--single .select2-selection__placeholder {
  color: #999; }

.select2-container--classic .select2-selection--single .select2-selection__arrow {
  background-color: #ddd;
  border: none;
  border-left: 1px solid #aaa;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  height: 26px;
  position: absolute;
  top: 1px;
  right: 1px;
  width: 20px;
  background-image: -webkit-linear-gradient(top, #eeeeee 50%, #cccccc 100%);
  background-image: -o-linear-gradient(top, #eeeeee 50%, #cccccc 100%);
  background-image: linear-gradient(to bottom, #eeeeee 50%, #cccccc 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFCCCCCC', GradientType=0); }

.select2-container--classic .select2-selection--single .select2-selection__arrow b {
  border-color: #888 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0; }

.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
  float: left; }

.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
  border: none;
  border-right: 1px solid #aaa;
  border-radius: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  left: 1px;
  right: auto; }

.select2-container--classic.select2-container--open .select2-selection--single {
  border: 1px solid #5897fb; }

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
  background: transparent;
  border: none; }

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #888 transparent;
  border-width: 0 4px 5px 4px; }

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  background-image: -webkit-linear-gradient(top, white 0%, #eeeeee 50%);
  background-image: -o-linear-gradient(top, white 0%, #eeeeee 50%);
  background-image: linear-gradient(to bottom, white 0%, #eeeeee 50%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0); }

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background-image: -webkit-linear-gradient(top, #eeeeee 50%, white 100%);
  background-image: -o-linear-gradient(top, #eeeeee 50%, white 100%);
  background-image: linear-gradient(to bottom, #eeeeee 50%, white 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFFFFFFF', GradientType=0); }

.select2-container--classic .select2-selection--multiple {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: text;
  outline: 0; }

.select2-container--classic .select2-selection--multiple:focus {
  border: 1px solid #5897fb; }

.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
  list-style: none;
  margin: 0;
  padding: 0 5px; }

.select2-container--classic .select2-selection--multiple .select2-selection__clear {
  display: none; }

.select2-container--classic .select2-selection--multiple .select2-selection__choice {
  background-color: #e4e4e4;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: default;
  float: left;
  margin-right: 5px;
  margin-top: 5px;
  padding: 0 5px; }

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
  color: #888;
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  margin-right: 2px; }

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #555; }

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  float: right; }

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto; }

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  margin-left: 2px;
  margin-right: auto; }

.select2-container--classic.select2-container--open .select2-selection--multiple {
  border: 1px solid #5897fb; }

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0; }

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0; }

.select2-container--classic .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
  outline: 0; }

.select2-container--classic .select2-search--inline .select2-search__field {
  outline: 0;
  box-shadow: none; }

.select2-container--classic .select2-dropdown {
  background-color: white;
  border: 1px solid transparent; }

.select2-container--classic .select2-dropdown--above {
  border-bottom: none; }

.select2-container--classic .select2-dropdown--below {
  border-top: none; }

.select2-container--classic .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto; }

.select2-container--classic .select2-results__option[role=group] {
  padding: 0; }

.select2-container--classic .select2-results__option[aria-disabled=true] {
  color: grey; }

.select2-container--classic .select2-results__option--highlighted[aria-selected] {
  background-color: #3875d7;
  color: white; }

.select2-container--classic .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px; }

.select2-container--classic.select2-container--open .select2-dropdown {
  border-color: #5897fb; }

/******************* BASIC STYLING **********************/
.scroll_tabs_container {
  position: relative;
  top: 0px;
  left: 0px;
  right: 0px;
  text-align: left;
  height: 40px;
  margin-bottom: 10px; }

ul.scroll_tabs_container {
  list-style: none; }

.scroll_tabs_container div.scroll_tab_inner {
  height: 40px; }

.scroll_tabs_container div.scroll_tab_inner span, .scroll_tabs_container div.scroll_tab_inner li {
  padding-left: 20px;
  padding-right: 20px;
  line-height: 40px;
  font-size: 14px;
  background-color: #CCCCCC;
  color: #333333;
  cursor: pointer; }

.scroll_tabs_container div.scroll_tab_inner li {
  display: -moz-inline-stack;
  display: inline-block;
  *display: inline;
  list-style-type: none; }

.scroll_tabs_container div.scroll_tab_inner span.scroll_tab_left_finisher {
  padding: 0px;
  width: 0px; }

.scroll_tabs_container div.scroll_tab_inner span.scroll_tab_right_finisher {
  padding: 0px;
  width: 0px; }

.scroll_tabs_container .scroll_tab_left_button {
  height: 40px;
  background-color: #CCCCCC;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.scroll_tabs_container .scroll_tab_left_button::before {
  content: "\25C0";
  line-height: 40px;
  padding-left: 5px; }

.scroll_tabs_container .scroll_tab_left_button_over {
  background-color: #999999; }

.scroll_tabs_container .scroll_tab_left_button_disabled {
  color: #AAAAAA;
  background-color: #CCCCCC; }

.scroll_tabs_container .scroll_tab_right_button {
  height: 40px;
  background-color: #CCCCCC;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.scroll_tabs_container .scroll_tab_right_button::before {
  content: "\25B6";
  line-height: 40px;
  padding-left: 5px; }

.scroll_tabs_container .scroll_tab_right_button_over {
  background-color: #999999; }

.scroll_tabs_container .scroll_tab_right_button_disabled {
  color: #AAAAAA;
  background-color: #CCCCCC; }

/****************** LIGHT THEME **************************/
.scroll_tabs_theme_light {
  height: 42px; }

.scroll_tabs_theme_light div.scroll_tab_inner {
  height: 42px; }

.scroll_tabs_theme_light div.scroll_tab_inner span, .scroll_tabs_theme_light div.scroll_tab_inner li {
  padding-left: 20px;
  padding-right: 20px;
  line-height: 40px;
  font-size: 14px;
  background-color: #CCCCCC;
  border-left: 1px solid #999999;
  border-top: 1px solid #999999;
  border-bottom: 1px solid #999999;
  color: #333333;
  cursor: pointer; }

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_first, .scroll_tabs_theme_light div.scroll_tab_inner li.scroll_tab_first {
  border-left: 0px; }

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_left_finisher {
  padding: 0px;
  width: 10px;
  background-color: #CCCCCC;
  -webkit-border-top-left-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-bottomleft: 5px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px; }

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_right_finisher {
  padding: 0px;
  width: 10px;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -moz-border-radius-topright: 5px;
  -moz-border-radius-bottomright: 5px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  background-color: #CCCCCC;
  border-left: 0px;
  border-right: 1px solid #999999; }

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_over, .scroll_tabs_theme_light div.scroll_tab_inner li.scroll_tab_over {
  background-color: #999999; }

/*.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_first_over {
  background-color: #999999;
}

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_left_finisher_over {
  background-color: #999999;
}

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_right_finisher_over {
  background-color: #999999;
}*/
/*.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_left_finisher_selected {
  background-color: #AAAAAA;
}

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_right_finisher_selected {
  background-color: #AAAAAA;
}*/
.scroll_tabs_theme_light .scroll_tab_left_button {
  height: 42px;
  background-color: #CCCCCC;
  -webkit-border-top-left-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-bottomleft: 5px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border: 1px solid #999999;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.scroll_tabs_theme_light .scroll_tab_left_button::before {
  content: "\25C0";
  line-height: 40px;
  padding-left: 5px; }

.scroll_tabs_theme_light .scroll_tab_left_button_over {
  background-color: #999999; }

.scroll_tabs_theme_light .scroll_tab_left_button_disabled {
  color: #AAAAAA;
  background-color: #CCCCCC; }

.scroll_tabs_theme_light .scroll_tab_right_button {
  height: 42px;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -moz-border-radius-topright: 5px;
  -moz-border-radius-bottomright: 5px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  background-color: #CCCCCC;
  border: 1px solid #999999;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.scroll_tabs_theme_light .scroll_tab_right_button::before {
  content: "\25B6";
  line-height: 40px;
  padding-left: 5px; }

.scroll_tabs_theme_light .scroll_tab_right_button_over {
  background-color: #999999; }

.scroll_tabs_theme_light .scroll_tab_right_button_disabled {
  color: #AAAAAA;
  background-color: #CCCCCC; }

.scroll_tabs_theme_light div.scroll_tab_inner span.tab_selected, .scroll_tabs_theme_light div.scroll_tab_inner li.tab_selected {
  background-color: #AAAAAA; }

/*.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_first_selected {
  background-color: #AAAAAA;
}*/
/****************** DARK THEME **************************/
.scroll_tabs_theme_dark {
  height: 42px; }

.scroll_tabs_theme_dark div.scroll_tab_inner {
  height: 42px; }

.scroll_tabs_theme_dark div.scroll_tab_inner span, .scroll_tabs_theme_dark div.scroll_tab_inner li {
  padding-left: 20px;
  padding-right: 20px;
  line-height: 40px;
  font-size: 14px;
  background-color: #333333;
  border-left: 1px solid #222222;
  border-top: 1px solid #222222;
  border-bottom: 1px solid #222222;
  color: #FFFFFF;
  cursor: pointer; }

.scroll_tabs_theme_dark div.scroll_tab_inner span.scroll_tab_first, .scroll_tabs_theme_dark div.scroll_tab_inner li.scroll_tab_first {
  border-left: 0px; }

.scroll_tabs_theme_dark div.scroll_tab_inner span.scroll_tab_left_finisher {
  padding: 0px;
  width: 10px;
  -webkit-border-top-left-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-bottomleft: 5px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px; }

.scroll_tabs_theme_dark div.scroll_tab_inner span.scroll_tab_right_finisher {
  padding: 0px;
  width: 10px;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -moz-border-radius-topright: 5px;
  -moz-border-radius-bottomright: 5px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left: 0px;
  border-right: 1px solid #222222; }

.scroll_tabs_theme_dark div.scroll_tab_inner span.scroll_tab_over, .scroll_tabs_theme_dark div.scroll_tab_inner li.scroll_tab_over {
  background-color: #555555; }

.scroll_tabs_theme_dark .scroll_tab_left_button {
  height: 42px;
  background-color: #333333;
  color: #FFFFFF;
  -webkit-border-top-left-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-bottomleft: 5px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border: 1px solid #222222;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.scroll_tabs_theme_dark .scroll_tab_left_button::before {
  content: "\25C0";
  line-height: 40px;
  padding-left: 5px; }

.scroll_tabs_theme_dark .scroll_tab_left_button_over {
  background-color: #666666; }

.scroll_tabs_theme_dark .scroll_tab_left_button_disabled {
  color: #444444;
  background-color: #333333; }

.scroll_tabs_theme_dark .scroll_tab_right_button {
  height: 42px;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -moz-border-radius-topright: 5px;
  -moz-border-radius-bottomright: 5px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  background-color: #333333;
  border: 1px solid #222222;
  color: #FFFFFF;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.scroll_tabs_theme_dark .scroll_tab_right_button::before {
  content: "\25B6";
  line-height: 40px;
  padding-left: 5px; }

.scroll_tabs_theme_dark .scroll_tab_right_button_over {
  background-color: #666666; }

.scroll_tabs_theme_dark .scroll_tab_right_button_disabled {
  color: #444444;
  background-color: #333333; }

.scroll_tabs_theme_dark div.scroll_tab_inner span.tab_selected, .scroll_tabs_theme_dark div.scroll_tab_inner li.tab_selected {
  background-color: #666666; }

.theme_color {
  color: #F6BB42; }

.theme_bg {
  background: #F6BB42; }

.theme_border {
  border: 1px solid #f4a911; }

@font-face {
  font-family: "JosefinSans";
  src: url("../fonts//JosefinSans.eot");
  src: url("../fonts//JosefinSans.eot?#iefix") format("embedded-opentype"), url("../fonts//JosefinSans.woff") format("woff"), url("../fonts//JosefinSans.ttf") format("truetype"), url("../fonts//JosefinSans.svg#JosefinSans") format("svg"); }
html {
  height: 100%; }

a.wallet-edit-btn h4 {
  font-size: 14px !important; }

.ac-detail-box .lunch-editable .clearfix {
  margin: 0 5px 0 !important;
  padding: 10px 0px !important; }

.main-wrapper {
  margin-left: 50px;
  margin-right: 50px;
  /*@include opacity(0.5);*/
  /*@include transition(all,0.5s, linear);*/
  /*@include border-radius(0);*/ }

body {
  /*font-family: JosefinSans;*/
  font-size: 14px;
  position: relative;
  color: #656d78;
  position: relative; }

body.bodybg {
  background-image: url("../images/background.png"); }

body.sticky-footer {
  padding-bottom: 60px; }

.navbar-inverse .navbar-nav > li > a.call-us {
  cursor: text;
  color: #656d78; }

.navbar-header button .ti-user {
  font-size: 20px; }

header {
  background-color: #ffffff; }

a:hover, a:focus {
  text-decoration: none; }

a {
  color: #656d78;
  text-decoration: none; }

b, strong {
  font-weight: bold;
  font-size: 16px; }

label b {
  font-size: 14px; }

.pt {
  padding-top: 10px; }

.pb {
  padding-bottom: 10px; }

.pl {
  padding-left: 10px; }

.pr {
  padding-right: 10px; }

.mt5 {
  margin-top: 5px; }

.mt6 {
  margin-top: 6px; }

.mt10 {
  margin-top: 10px; }

.mb {
  margin-bottom: 10px; }

.ml {
  margin-left: 10px; }

.mr {
  margin-right: 10px; }

.pt-15 {
  padding-top: 15px; }

.pt-5 {
  padding-top: 5px; }

.pd-0 {
  padding: 0; }

.pd-5 {
  padding: 5px; }

.ptb-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important; }

.mg {
  margin-bottom: 10px;
  margin-top: 10px; }

.mb-0 {
  margin-bottom: 0 !important; }

.mt-0 {
  margin-top: 0 !important; }

.wd-175 {
  width: 175px !important; }

.wd-100 {
  width: 100%; }

.col-row {
  margin-left: -15px;
  margin-right: -15px; }

.col-col {
  padding-left: 15px;
  padding-right: 15px; }

.sub-head-mrtp {
  margin-top: 20px; }

.affix {
  top: 0;
  width: 100%;
  z-index: 3 !important; }

.affix ~
.container-fluid {
  position: relative;
  top: 50px; }

.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 50px;
  padding-right: 50px; }

#icon-menu {
  display: table; }

.ti-location-pin {
  color: #F6BB42; }

.single-tab {
  text-align: center;
  font-size: 17px;
  margin-bottom: 10px;
  margin-top: 10px; }

.navbar-inverse {
  border-radius: none;
  background-color: transparent;
  border: none;
  border-radius: none;
  border-bottom: 1px solid #e8e8e8; }

.navbar-brand {
  float: left;
  padding: 15px 15px;
  font-size: 18px;
  line-height: 20px;
  height: 50px;
  margin-top: 8px; }

.navbar {
  position: relative;
  min-height: 60px;
  padding-top: 0px;
  margin-bottom: 0;
  border-radius: none !important; }

.navbar-inverse .navbar-brand {
  color: #aab2bd; }

.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus, .navbar-inverse .navbar-brand:active {
  color: #F6BB42;
  transition: 0.5s; }

.navbar-inverse .navbar-nav > li > a {
  color: #656d78;
  line-height: 3em;
  font-size: 16px; }

.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus, .navbar-inverse .navbar-nav > li > a:active {
  color: #F6BB42;
  transition: 0.5s; }

.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
  border-color: #e8e8e8; }

#shopping-cart .ti-shopping-cart {
  font-size: 25px;
  color: #F6BB42; }

#shopping-cart .badge, #show_cart .badge {
  display: inline-block;
  min-width: 10px;
  padding: 5px 8px;
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #8CC152;
  border-radius: 50%;
  top: -20px;
  position: relative;
  right: 32px; }

#cart-count {
  font-size: 12px;
  background: #ff0000;
  color: #fff;
  padding: 3px 7px;
  vertical-align: top;
  border-radius: 50%; }

.nav > li {
  position: relative;
  display: block;
  text-align: center; }

.navbar-inverse .navbar-toggle {
  border: none;
  color: #F6BB42; }

.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:active, .navbar-inverse .navbar-toggle:focus {
  border: none;
  color: #F6BB42;
  background-color: transparent; }

.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
  background-color: transparent;
  color: #F6BB42; }

.dropdown-menu > li > a {
  display: block;
  padding: 8px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857;
  color: #656d78;
  white-space: nowrap;
  font-size: 15px; }

.ti-more {
  -ms-transform: rotate(90deg);
  /* IE 9 */
  -webkit-transform: rotate(90deg);
  /* Chrome, Safari, Opera */
  transform: rotate(90deg); }

#mySidenav .ti-user {
  padding-right: 0; }

.sidenav {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: #111;
  overflow-x: hidden;
  transition: 0.5s; }

.sidenav a.list {
  padding: 25px 10px 25px 10px;
  text-decoration: none;
  font-size: 18px;
  color: #aab2bd;
  display: block;
  transition: 0.3s;
  border-bottom: 1px solid #434a54;
  text-align: center;
  width: 250px; }

.sidenav a.closebtn {
  border-bottom: none;
  padding: 10px 10px 10px 10px;
  width: 0px;
  color: #aab2bd; }

.sidenav a:hover, .sidenav a:focus, .sidenav a:active {
  color: #f1f1f1;
  text-decoration: none; }

#logo img {
  display: block;
  vertical-align: middle;
  top: 0px;
  max-height: 57px; }

#logo {
  padding: 10px 10px; }

.followbtn {
  color: #aab2bd;
  border: 1px solid #aab2bd;
  margin: 10px 0 10px 0;
  padding: 5px 5px 0px 0px;
  position: relative;
  display: inline-block;
  width: 150px;
  text-align: left; }

.followbtn:hover {
  color: #aab2bd;
  text-decoration: none; }

footer.side-footer {
  position: absolute;
  bottom: 0;
  text-align: center;
  color: #aab2bd;
  width: 250px;
  margin-bottom: 20px;
  background-color: #111; }

footer.side-footer a span {
  font-size: 14px;
  width: 13em;
  word-wrap: break-word; }

footer.side-footer a {
  line-height: 1em;
  font-size: 20px; }

footer.side-footer h4 {
  color: #aab2bd; }

.footer-3 {
  position: fixed;
  width: 100%;
  bottom: 0; }

footer {
  background-color: #24292d;
  color: #aab2bd; }

footer a span {
  color: #aab2bd;
  text-decoration: none;
  font-size: 20px; }

footer .col-sm-4 {
  padding: 20px;
  line-height: 2em; }

.ti-facebook:hover {
  color: #3b5998;
  transition: 0.5s ease-in-out; }

.ti-twitter-alt:hover {
  color: #4099FF;
  transition: 0.5s ease-in-out; }

.ti-google:hover {
  color: #d34836;
  transition: 0.5s ease-in-out; }

.ti-instagram:hover {
  color: #ff33ff;
  transition: 0.5s ease-in-out; }

.social-icon {
  word-spacing: 20px;
  text-align: center; }

.fooddialer, .fooddialer:hover, .fooddialer:active, .fooddialer:focus {
  color: #F6BB42;
  text-decoration: none; }

.copyright {
  text-align: right; }

.bar1, .bar2, .bar3 {
  width: 25px;
  height: 2px;
  background-color: #656d78;
  margin: 6px 0;
  transition: 0.4s; }

.change .bar1 {
  -webkit-transform: rotate(-48deg) translate(-4px, 6px);
  transform: rotate(-48deg) translate(-4px, 6px); }

.change .bar2 {
  opacity: 0; }

.change .bar3 {
  -webkit-transform: rotate(43deg) translate(-5px, -8px);
  transform: rotate(43deg) translate(-5px, -8px); }

.btn-default {
  color: #656d78;
  background-color: #ffffff;
  border-color: #dfdfdf; }

.btn-default:hover, .btn-default:focus, .btn-default:active {
  color: #ffffff;
  background-color: #F6BB42;
  outline: none; }

.bread-filter {
  margin-top: 10px; }

.breadcrumb > li + li::before {
  padding: 0 5px;
  color: #ccc;
  content: "/\00a0"; }

.breadcrumb > .active {
  color: #F6BB42; }

section.bread-filter .back {
  text-align: right;
  padding: 0px 0px 8px 8px; }

.active-filter {
  background-color: #F6BB42;
  color: #ffffff;
  transition: 1s ease-in-out; }

.breadcrumb {
  padding: 0px 8px 8px 0px;
  margin-bottom: 0px;
  margin-bottom: 0px;
  list-style: none;
  background-color: transparent;
  border-radius: 0px; }

.breadcrumb > li a {
  color: #656d78; }

.breadcrumb > li a:hover, .breadcrumb > li a:focus {
  color: #F6BB42;
  text-decoration: none;
  transition-duration: 1s; }

.fa {
  display: inline-block;
  padding: 0 0.5 rem 0 0;
  font-size: 20px; }

footer.side-footer a span.ti-apple, footer.side-footer a span.ti-android {
  width: auto;
  font-size: 27px;
  float: left;
  vertical-align: middle;
  text-align: left;
  padding: 10px; }

.ti-apple, .ti-android {
  font-size: 30px;
  float: left;
  vertical-align: middle;
  text-align: left;
  padding: 10px; }

ul#myTabs span.ti-search {
  color: #F6BB42;
  line-height: 2.35em;
  font-weight: bold;
  font-size: 19px;
  cursor: pointer; }

.sidenav .closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px;
  margin-left: 50px; }

.visible-xs {
  text-align: center;
  padding: 5px; }

.visible-xs a {
  color: #ffffff;
  text-decoration: none; }

.visible-xs ul.dropdown-menu {
  min-width: 100%;
  border-radius: 0px;
  z-index: 5; }

.visible-xs ul.dropdown-menu > li > a {
  clear: both;
  color: #656d78;
  padding-bottom: 5px;
  padding-top: 5px;
  text-align: center;
  font-size: 15px;
  cursor: pointer;
  text-transform: uppercase; }

.visible-xs ul.dropdown-menu .location {
  background-color: pr #f5f7fa; }

.visible-xs ul.dropdown-menu li {
  border-bottom: 1px solid #f5f7fa; }

#main {
  transition: all .5s;
  padding: 0px; }

.nav-tabs {
  border-bottom: none; }

.nav-tabs > li {
  margin-right: 0px;
  line-height: 1.42857;
  border-right: 1px solid #aab2bd; }

.nav-tabs > li:hover {
  border-radius: none; }

.nav-tabs > li#serch-btn {
  border-right: none;
  cursor: pointer; }

.nav-tabs > a#search {
  cursor: pointer; }

.nav-tabs > li > a {
  margin-right: 0px;
  line-height: 1.42857;
  border: none;
  border-radius: 0 0 0 0;
  position: relative;
  display: block;
  padding: 15px 68px;
  right: 0;
  left: 0;
  cursor: pointer; }

.nav-tabs > li.active > a, .nav-tabs > li > a:hover, .nav-tabs > li > a:focus {
  margin-right: 0px;
  line-height: 1.42857;
  border: none;
  border-radius: none;
  position: relative;
  display: block; }

.nav-tabs > li {
  float: left;
  margin-bottom: 0px; }

#location {
  color: #656d78;
  font-size: 16px; }

#location:hover, #location:focus, #location:active {
  color: #F6BB42; }

ul#myTabs li:hover {
  margin-right: 0px;
  line-height: 1.42857; }

ul#myTabs {
  text-transform: uppercase;
  margin-bottom: 10px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 5px 0px; }

ul#myTabs span {
  color: #656d78;
  font-size: 14px;
  text-transform: capitalize; }

ul#myTabs li a {
  background-color: #f5f7fa;
  color: #656d78;
  border: 3px solid #f5f7fa;
  font-size: 16px; }

ul#myTabs li a:hover, ul#myTabs li a:active, ul#myTabs li a:focus {
  background-color: #f5f7fa;
  color: #F6BB42;
  border-top: 3px solid #F6BB42;
  transition-duration: 1s; }

ul#myTabs li a.active {
  background-color: #f5f7fa;
  color: #F6BB42;
  border: 3px solid #F6BB42;
  transition-duration: 1s; }

ul#myTabs li a.active:after {
  top: 0;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(213, 107, 26, 0);
  border-top-color: #F6BB42;
  border-width: 8px;
  margin-left: -12px; }

ul#myTabs li#serch-btn a:hover, ul#myTabs li#serch-btn a:active, ul#myTabs li#serch-btn a:focus {
  border-top: 3px solid #f5f7fa; }

.nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a, .nav-tabs.nav-justified > .active > a, .nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:focus {
  border: 0; }

.tab-content {
  margin-top: 10px;
  margin-bottom: 45px; }

.dropdown-search .input-group-btn button {
  margin-left: 0;
  top: 4.2px;
  background-color: #F6BB42;
  color: #fff;
  position: relative; }

.nonveg-icon {
  padding: 5px 5px;
  border: 1px solid #da4453;
  color: #da4453;
  position: absolute;
  margin-top: 10px;
  margin-left: 10px;
  font-size: 12px;
  z-index: 2;
  background-color: #ffffff; }

.veg-icon {
  padding: 5px 5px;
  border: 1px solid #8cc152;
  color: #8cc152;
  position: absolute;
  margin-top: 10px;
  margin-left: 10px;
  font-size: 12px;
  z-index: 2;
  background-color: #ffffff; }

.veg-circle {
  background-color: #8cc152;
  width: 10px;
  height: 10px;
  border-radius: 50%; }

.nonveg-circle {
  background-color: #da4453;
  width: 10px;
  height: 10px;
  border-radius: 50%; }

.overlay {
  transition: .5s ease;
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%); }

.meal-bg {
  background-color: #000; }

.no-meal {
  text-align: center;
  background: #f5f7fa;
  padding: 50px 10px 30px; }

.swap-pos {
  position: relative; }

.item-swap-ico {
  position: absolute;
  float: right;
  right: 0;
  padding: 5px;
  line-height: 0.5em;
  margin-top: 10px;
  margin-right: 10px;
  background: #F6BB42;
  color: #ffffff !important;
  z-index: 1; }

.item-swap-ico .ti-reload {
  color: #ffffff !important; }

.popover.bottom {
  margin-top: 10px;
  left: inherit !important;
  right: -13px !important; }

.popover {
  top: 35px !important; }

.popover.bottom > .arrow {
  right: 10% !important; }

.image-overlay {
  opacity: 0.5;
  color: #fff; }

.meal-img:hover .overlay {
  opacity: 1; }

.img-text {
  color: white;
  font-size: 18px;
  text-align: center; }

.img-text b {
  font-size: 47px; }

.img-text span {
  font-size: 25px; }

.login-box {
  background-color: #f5f7fa;
  width: 50%;
  margin-left: 25%;
  padding: 0px;
  text-align: center;
  margin-top: 100px;
  margin-bottom: 100px; }

.control {
  font-size: 14px; }

.login-button {
  background-color: #CCD1D9;
  color: #ffffff;
  padding: 15px;
  font-size: 16px; }

.log-button {
  background-color: #CCD1D9;
  color: #ffffff;
  padding: 15px;
  font-size: 16px; }

.guest-button {
  background-color: #CCD1D9;
  color: #ffffff;
  padding: 15px;
  font-size: 16px; }

.link-color, .link-color:hover, .link-color:active, .link-color:focus {
  color: #F6BB42;
  padding: 10px 0; }

.click-here, .click-here:hover, .click-here:active, .click-here:focus {
  margin-left: 2px;
  text-decoration: underline;
  color: #F6BB42; }

.signin-button {
  background-color: #CCD1D9;
  color: #ffffff;
  padding: 15px;
  font-size: 16px; }

.login-button:hover, .login-button:focus, .login-button:active {
  text-decoration: none;
  color: #ffffff; }

.signin-button:hover, .signin-button:focus, .signin-button:active {
  text-decoration: none;
  color: #ffffff; }

.guest-button:hover, .guest-button:focus, .guest-button:active {
  text-decoration: none;
  color: #ffffff; }

.log-button:hover, .log-button:focus, .log-button:active {
  text-decoration: none;
  color: #ffffff; }

.bg-col {
  background-color: #F6BB42;
  color: #ffffff; }

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0;
  padding: 15px 15px 0px; }

legend {
  display: block;
  width: auto !important;
  padding: 0px 20px;
  margin-bottom: 0;
  font-size: 21px;
  line-height: inherit;
  color: #656d78;
  border: 0;
  border-bottom: none;
  z-index: 5;
  background-color: #f5f7fa; }

.input-icon1 {
  float: right;
  right: 15px;
  margin-top: -30px;
  position: relative;
  z-index: 2;
  color: #656d78; }

.input-icon2 {
  float: right;
  right: 15px;
  margin-top: -30px;
  position: relative;
  z-index: 2;
  color: #656d78; }

.load-more {
  text-align: center;
  margin-bottom: 45px; }

.load-more a {
  background-color: #F6BB42;
  padding: 8px 20px;
  color: #fff;
  text-align: center;
  font-size: 17px;
  display: block; }

.load-more a:hover, .load-more a:focus, .load-more a:active {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out; }

.text-error {
  color: #cc0000;
  font-size: 13px; }

.hidden {
  visibility: hidden; }

input[type=text], input[type=password] {
  width: 100%;
  padding: 10px 12px 10px 12px;
  display: inline-block;
  border: 1px solid #ddd;
  color: black; }

button.big-Login-button {
  background-color: #F6BB42;
  color: #ffffff;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  width: 100%;
  font-size: 16px; }

button.big-Login-button:hover, button.big-Login-button:focus, button.big-Login-button:active {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out; }

button.small-btn {
  background-color: #F6BB42;
  color: #ffffff;
  padding: 5px 15px;
  margin: 8px 0;
  border: none;
  cursor: pointer; }

button.border-btn {
  border: 1px solid #F6BB42;
  padding: 5px 20px;
  margin: 0 0 8px;
  cursor: pointer;
  color: #F6BB42;
  background: transparent; }

button.border-btn:hover, button.border-btn:active, button.border-btn:focus {
  border: 1px solid #24292d;
  color: #ffffff;
  background: #24292d;
  transition: 0.5s ease-in-out; }

button.resendotp-btn {
  background-color: #F6BB42;
  color: #ffffff;
  padding: 12px 15px !important;
  border: none;
  cursor: pointer;
  width: 100%; }

button.small-btn:hover, button.small-btn:focus, button.small-btn:active {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out; }

span.psw {
  float: right;
  padding-top: 16px; }

.wallet-box .login-from {
  padding: 35px;
  position: relative;
  margin-top: 0; }

.otp-alert .alert-success {
  text-align: left; }

.otp-alert .close {
  font-size: 22px; }

.login-from {
  padding: 35px;
  position: relative;
  margin-top: 50px; }

.login-from input[type="text"], .login-from input[type="password"] {
  width: 100%;
  padding: 12px 43px 12px 12px;
  display: inline-block;
  border: 1px solid #ddd;
  color: black; }

.login-from input[type="password"] {
  width: 100%; }

#signup form .form-control {
  display: block;
  width: 100%;
  height: 45px;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.42857;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ddd;
  border-radius: 0;
  box-shadow: none;
  transition: none; }

.form-location .form-control {
  display: block;
  width: 100%;
  height: 25px;
  line-height: 1.42857;
  color: #555555;
  background-color: transparent;
  background-image: none;
  border: none;
  text-transform: uppercase;
  text-align: center;
  box-shadow: none;
  transition: none; }

.form-location .select__arrow {
  top: 15px;
  right: 0px; }

label {
  float: left;
  text-transform: uppercase;
  text-align: left; }

.login-box span.loc {
  margin-right: 40px;
  display: inline-block; }

.filter {
  color: #F6BB42;
  float: right;
  position: relative;
  margin: 0px;
  margin-bottom: 0px;
  text-decoration: none;
  text-transform: uppercase;
  text-align: right; }

.all-filter {
  color: #F6BB42;
  background: #fff;
  font-weight: bold; }

.all-filter:hover, .all-filter:active, .all-filter:visited, .all-filter:focus {
  background: #F6BB42;
  color: #fff; }

.veg-filter {
  color: #A0D468;
  background: #fff;
  font-weight: bold; }

.veg-filter:hover, .veg-filter:active, .veg-filter:visited, .veg-filter:focus {
  background: #A0D468;
  color: #fff; }

.non-veg-filter {
  color: #da4453;
  background: #fff;
  font-weight: bold; }

.non-veg-filter:hover, .non-veg-filter:active, .non-veg-filter:visited, .non-veg-filter:focus {
  background: #da4453;
  color: #fff; }

/* modal 1 */
#myModal form {
  width: 30%;
  left: 35%;
  position: relative; }

.modal-header .close {
  margin-top: -28px;
  position: relative;
  right: -25px; }

.modal button.close {
  padding: 5px 6px;
  cursor: pointer;
  background: #ff3333;
  border: 2px solid #fff;
  -webkit-appearance: none;
  border-radius: 50%;
  font-weight: bold; }

.modal button.close:hover, .modal button.close:focus, .modal button.close:active {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out; }

.close {
  float: right;
  font-size: 15px;
  line-height: 1;
  color: #ffffff;
  text-shadow: none;
  opacity: 1;
  filter: alpha(opacity=20); }

.close:hover {
  color: #ffffff;
  transition: 0.5s ease-in-out;
  opacity: 1; }

.modal {
  position: fixed;
  z-index: 999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: black;
  background-color: rgba(0, 0, 0, 0.75);
  padding-top: 40px; }

.modal-body {
  text-align: center;
  font-weight: normal; }

.modal-content {
  background-color: #f5f3f4;
  border-radius: 0;
  background-size: cover;
  background-position: 50%;
  box-shadow: none; }

.modal-bg {
  background-color: #f5f3f4;
  background-image: url("../images/popup.jpg");
  border-radius: 0;
  background-size: cover;
  background-position: 50%; }

.modal-body h4, .modal-body h5 {
  text-transform: uppercase;
  color: #F6BB42; }

.modal-body h5.order-btn {
  text-transform: uppercase;
  color: #F6BB42;
  text-decoration: none;
  padding: 10px;
  background-color: #F6BB42;
  color: #ffffff; }

.modal-body h5.order-btn:hover, .modal-body h5.order-btn:active, .modal-body h5.order-btn:focus {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out; }

.modal-body h5.modal-login-btn {
  margin-top: 20px;
  text-decoration: none; }

form a:hover, form a:focus {
  text-decoration: none; }

.modal-header {
  border-bottom: 0;
  padding-bottom: 0px; }

.modal-footer {
  border-top: 0; }

/* end modal 1 */
/* modal 2 (weekly menu) */
.modal-title {
  text-align: center;
  font-weight: bold; }

ul, ol {
  margin-top: 0;
  margin-bottom: 10px;
  text-align: left; }

.modal-body h4.date {
  background-color: #656D78;
  color: #ffffff;
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px;
  padding-bottom: 5px;
  text-transform: capitalize;
  font-weight: bold;
  font-size: 17px; }

.modal-body ul.day-item {
  margin-top: 0;
  margin-bottom: 0;
  text-align: left; }

.modal-body .week-item {
  border: 1px solid #E6E9ED;
  margin-bottom: 15px; }

.modal-body .mb-15 {
  margin-bottom: 15px; }

.modal-content .modal-hg {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden; }

.modal-content .modal-hg-week {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden; }

.week-item .list-group-item > .badge {
  float: right;
  background: none;
  color: #656d78;
  font-weight: normal;
  font-size: 15px; }

.week-item .list-group-item {
  position: relative;
  display: block;
  padding: 5px 5px 5px 10px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd; }

.modal-footer {
  padding: 0; }

.weekly-add-meal-btn {
  text-align: center; }

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.weeklymenu-date {
  display: none; }

/* end modal 2 (weekly menu) */
/* modal 3 (View Item) */
.vw-item-img {
  width: 100%; }

.vw-item p {
  text-align: justify; }

#myModal3 .list-group-item {
  position: relative;
  display: block;
  padding: 5px 5px 5px 10px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd; }

.vw-item button.small-btn {
  background-color: #F6BB42;
  color: #ffffff;
  padding: 5px 20px;
  margin-top: 0;
  margin-bottom: 0;
  border: none;
  cursor: pointer; }

.vw-item button.small-btn:hover {
  background-color: #24292d;
  transition: 0.5 ease-in-out; }

.vw-item h4.price {
  color: #656d78;
  text-align: left; }

/* end modal 3 (View Item) */
/* modal 4 (rating) */
#myModal4 a.rating {
  color: #656d78;
  padding: 0;
  font-size: 25px;
  background-color: transparent; }

#myModal4 a.rating:hover {
  color: #F6BB42; }

#myModal4 a.filled {
  color: #F6BB42; }

#myModal4 .modal-body .rateus {
  padding: 0 10px 10px; }

#myModal4 .rateus b {
  font-size: 25px;
  padding: 5px; }

#myModal4 .lunch-textarea {
  width: 100%;
  padding: 6px 12px;
  color: #000;
  border: none;
  height: 100px; }

/*
	Ratings Stars
	(with as little code as possible)
*/
.rating1 {
  unicode-bidi: bidi-override;
  direction: rtl;
  text-align: center; }

.rating1 > span {
  display: inline-block;
  position: relative;
  font-size: 30px;
  color: #F6BB42;
  cursor: pointer; }

.rating1 > span:hover,
.rating1 > span:hover ~ span, .rating1 > span.rate-act, .rating1 > span.rate-act {
  color: transparent; }

/*.rating1 > span.rate-act {
   color: transparent;
}*/
.rating1 > span:hover:before, .rating1 > span:hover ~ span:before, .rating1 > span.rate-act:before, .rating1 > span.rate-act:before {
  content: "\2605";
  position: absolute;
  left: 0;
  color: #F6BB42; }

/* end modal 4 (rating) */
.form-control {
  border-radius: 0px; }

.meal-container {
  margin-bottom: 30px; }

.meal-box {
  margin: 10px 0;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2); }

h4.meal-type {
  text-align: center; }

.meal-box:hover {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2); }

.rigth-panel {
  float: right;
  text-align: right; }

.meal-box .clearfix {
  padding: 0 15px 10px;
  background-color: #f5f7fa; }

.meal-box .clearfix h3 {
  margin-top: 10px;
  text-align: right;
  color: #F6BB42; }

.meal-box .clearfix h4.item-name {
  color: #656d78;
  font-size: 16px;
  margin-top: 12px;
  text-transform: capitalize; }

.meal-box .clearfix .pull-left h4.price {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px; }

.meal-box .clearfix .pull-right a.add-btn {
  background-color: #F6BB42;
  padding: 6px 20px;
  color: #fff;
  float: right; }

.meal-box .clearfix .pull-right a.add-btn:hover, .meal-box .clearfix .pull-right a.add-btn:focus, .meal-box .clearfix .pull-right a.add-btn:active {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out;
  /*box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);*/ }

.meal-box .clearfix .pull-right a.add-item {
  background-color: transparent;
  padding: 0px;
  color: #fff; }

.meal-box .clearfix .pull-right a.rating {
  color: #F6BB42;
  padding: 0;
  background-color: transparent; }

.view-item-box a {
  color: #F6BB42;
  font-size: 16px; }

.single-item {
  text-align: center; }

.meal-box .view-item-box {
  padding-top: 5px;
  padding-bottom: 5px; }

.view-item-box a:hover, .view-item-box a:focus, .view-item-box a:active {
  text-decoration: none;
  color: #F6BB42; }

section .cart-owl-box .owl-carousel .offer {
  padding: 5px 15px 5px;
  margin: 10px 0;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
  background-color: #f5f7fa; }

section .cart-owl-box .owl-carousel .offer .offer-discount {
  color: #F6BB42; }

.well-sm {
  background-color: #f5f7fa;
  margin-top: 10px;
  border-radius: none;
  border: none;
  box-shadow: none;
  text-align: center;
  color: #F6BB42; }

hr.view-item-div {
  margin-top: 0;
  margin-bottom: 0;
  border: 0;
  border-top: 1px solid #dfdfdf; }

h4.meal-type {
  position: relative;
  z-index: 1; }
  h4.meal-type:before {
    border-top: 1px solid #dfdfdf;
    content: "";
    margin: 0 0px;
    /* this centers the line to the full width specified */
    position: absolute;
    /* positioning must be absolute here, and relative positioning must be applied to the parent */
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    z-index: -1; }
  h4.meal-type span {
    /* to hide the lines from behind the text, you have to set the background color the same as the container */
    background: #fff;
    padding: 0 20px; }

h4.meal-type span.see-more {
  float: right;
  border-radius: 50px;
  color: #ffffff;
  border: 1px solid #656D78;
  background-color: #656D78;
  font-size: 15px;
  padding: 5px 12px;
  margin-top: -5px; }

h4.meal-type span.see-more:hover {
  color: #656D78;
  background-color: #ffffff;
  border: 1px solid #656D78;
  transition: 0.5s ease-in-out; }

span.customSelect {
  font: 12px sans-serif;
  border: none;
  color: #555;
  padding: 7px 9px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: none;
  width: 175px; }

.tab-panel {
  margin-bottom: 50px; }

/* search bar */
.dropdown-search {
  display: none;
  margin-top: 0px;
  border: none;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  float: right;
  left: auto;
  min-width: 0;
  padding: 15px;
  right: 0;
  width: 250px;
  top: 100%;
  font-size: 12px;
  font-weight: normal;
  text-shadow: none;
  text-transform: none !important;
  border-top: 2px solid #F6BB42;
  background-color: #f5f7fa;
  z-index: 10; }

.dropdown-menu .divider {
  background-color: #e5e5e5;
  height: 1px;
  margin-bottom: 0px;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0px;
  overflow-x: hidden;
  overflow-y: hidden; }

.dropdown-menu {
  padding-top: 0;
  padding-bottom: 0; }

.input-group {
  position: relative;
  display: table;
  border-collapse: separate; }

.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0; }

.dropdown-menu-right {
  left: auto;
  right: 0;
  text-align: right;
  text-transform: uppercase; }

section #location {
  position: relative;
  margin-top: 20px; }

.lunch-field, .dinner-field {
  display: none; }

.lunch-textarea, .dinner-textarea {
  width: 100%;
  padding: 6px 12px;
  color: #000;
  border: 1px solid #ddd; }

.copy-address, .subscribe, .agree-terms {
  float: left; }

#term-conditions {
  color: #F6BB42; }

.static-box {
  background-color: #f5f7fa;
  text-align: left;
  padding: 20px;
  border: 1px solid #ddd; }

/* product page */
.product-detail-panel {
  margin-top: 25px; }

.product-box {
  padding: 20px;
  background-color: #f5f7fa; }

.product-description {
  margin-top: 20px; }

.product-detail {
  margin-top: 10px; }

.product-description p {
  padding-top: 10px; }

.product-box .product-info .list-head {
  background: #f5f7fa; }

.product-box .list-group-item {
  position: relative;
  display: block;
  padding: 5px 5px 5px 10px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd; }

.product-rate {
  padding: 5px 7px;
  background: #656d78;
  color: #ffffff; }

.product-rating-count a:hover .product-rate {
  background: #F6BB42; }
.product-rating-count a:hover .rating-review-link {
  color: #F6BB42; }

.product-nm {
  padding: 10px 0; }

.rating-review-link {
  margin-left: 10px; }

.product-rating-count {
  margin-top: 5px;
  margin-bottom: 20px; }

.product-price span {
  font-size: 16px;
  margin-left: 10px;
  text-decoration: line-through; }

.product-count .count-icon {
  padding: 7px 9px;
  border: 1px solid #f5f7fa;
  color: #656d78;
  position: relative;
  top: 0;
  left: 0;
  z-index: 0;
  background-color: transparent; }

.product-count {
  padding: 15px 0; }

.addtocart-button {
  margin-bottom: 10px; }

.sub-total1 {
  float: right; }

.set-pref {
  float: right; }

.product-weeklymenu {
  margin-top: 20px;
  margin-bottom: 20px; }

.product-weeklymenu-box {
  padding: 20px;
  background: #f5f7fa; }

.product-weeklymenu-box h4.date {
  background-color: #656D78;
  color: #ffffff;
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px;
  padding-bottom: 5px;
  text-transform: capitalize;
  font-weight: bold;
  text-align: center;
  font-size: 17px; }

.review-box {
  padding: 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #ddd; }

.rating-review-panel h4.meal-type span span.ti-pencil {
  background: transparent;
  padding: 0 0; }

.green-rate {
  background: #8CC152;
  color: #ffffff;
  padding: 5px 10px; }

.red-rate {
  background: #DA4453;
  color: #ffffff;
  padding: 5px 10px; }

.orange-rate {
  background: #F6BB42;
  color: #ffffff;
  padding: 5px 10px; }

.rating-review-panel {
  margin-top: 20px; }

.reviewer-nm {
  margin-left: 10px; }

.review-text {
  padding-top: 10px;
  margin-bottom: 0; }

.review-box:last-child {
  border-bottom: 0; }

/* end product page */
/* cart-page */
.sub-footer {
  background-color: #f5f7fa;
  position: relative;
  padding: 15px 0; }

.foot-price, .foot-price:hover, .foot-price:focus {
  top: 10px;
  position: relative;
  color: #656d78; }

.sub-footer a.footer-btn {
  background-color: #F6BB42;
  padding: 8px 20px;
  color: #fff;
  float: right;
  font-weight: normal;
  font-size: 14px; }

.sub-footer a.footer-btn:hover, .sub-footer a.footer-btn:focus, .sub-footer a.footer-btn:active {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out;
  /*box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);*/ }

.checkout-main-panel h4 {
  margin: 5px 0px 25px;
  text-align: center; }

.checkout-main-panel h4.meal-type {
  margin: 25px 0px 25px; }

.padding-15-0 {
  padding: 15px 0; }

.padding-0 {
  padding: 0; }

.choose-plan {
  padding: 15px 0; }

.checkout-box {
  background-color: #f5f7fa;
  margin: 15px 0px;
  margin-bottom: 0; }

.checkout-box .count {
  margin-bottom: 0; }

.chechout-box .pull-left .veg-icon {
  position: relative;
  margin: 10px 0;
  left: 0px; }

.set-item-name {
  margin-bottom: 5px; }

.set-pref-div .control {
  margin-bottom: 7px !important;
  line-height: 1.3em !important;
  padding-left: 23px !important; }

.set-pref-div .control__indicator {
  height: 15px;
  width: 15px; }

.set-pref-div .control--radio .control__indicator:after {
  left: 4px;
  top: 4px;
  height: 5px;
  width: 5px;
  border-radius: 50%;
  background: #fff; }

.fl-rgt {
  float: right; }

.fl-lft {
  float: left; }

.done-up1, .done-up2 {
  font-size: 15px;
  text-align: center;
  top: -8px;
  position: relative;
  color: #fff;
  padding: 6px 5px 4px;
  z-index: 1;
  background: gray;
  border-radius: 50%; }

.done-down1, .done-down2 {
  font-size: 15px;
  text-align: center;
  top: -8px;
  position: relative;
  color: #fff;
  padding: 6px 5px 4px;
  z-index: 1;
  background: gray;
  border-radius: 50%; }

.done-up3, .done-down3 {
  font-size: 15px;
  text-align: center;
  top: 14px;
  position: relative;
  color: #fff;
  padding: 6px 5px 4px;
  z-index: 1;
  background: gray;
  border-radius: 50%; }

.minus-icon, .plus-icon {
  padding: 7px 7px;
  border: 1px solid #F6BB42;
  color: #F6BB42;
  position: relative;
  top: 0;
  left: 0;
  font-size: 12px;
  z-index: 5;
  background-color: transparent;
  text-decoration: none;
  font-weight: bold; }

.minus-icon:hover, .minus-icon:active, .minus-icon:focus, .plus-icon:hover, .plus-icon:focus, .plus-icon:active {
  color: #ffffff;
  background-color: #F6BB42;
  transition-duration: 0.5s;
  text-decoration: none; }

.count .count-icon {
  padding: 7px 9px;
  border: 1px solid transparent;
  color: #656d78;
  position: relative;
  top: 0;
  left: 0;
  z-index: 0;
  text-align: center;
  background-color: transparent; }

.count {
  text-align: center;
  margin-bottom: 12px; }

.side-cart a.cart-trash, .side-cart a.cart-trash:hover, .side-cart a.cart-trash:active {
  color: #F6BB42 !important;
  float: right;
  margin-top: 5px; }

.cart-price h4 {
  margin: 5px 0;
  text-align: left; }

.cart-edit-link, .cart-edit-link:hover, .cart-edit-link:focus, .cart-edit-link:active {
  color: #F6BB42;
  margin-right: 5px; }

.cart-trash-link {
  color: red;
  margin-left: 5px; }

.item-type-l-d {
  font-weight: normal; }

.sub-total {
  text-align: right; }

.item-labels label {
  font-size: 13px;
  font-weight: normal;
  text-transform: capitalize;
  padding: 10px;
  border: 1px solid #ccc;
  line-height: 1em;
  margin-right: 5px; }

.item-labels label.add-extra-div {
  border: 1px solid #ddd;
  display: -webkit-inline-box; }

.item-labels label a span.ti-close {
  margin-left: 10px;
  font-size: 10px;
  color: red;
  z-index: 10; }

.item-labels .add-extra-div .minus-icon, .item-labels .add-extra-div .plus-icon {
  padding: 2px; }

.extra-price {
  margin-right: 10px; }

#myModal3 .list-head {
  background: #f5f7fa; }

.vw-item .list-group-item > .badge {
  float: right;
  background: none;
  color: #656d78;
  font-weight: normal;
  font-size: 15px; }

.close-btn .ti-close, .close-btn .ti-close:hover, .close-btn .ti-close:active, .close-btn .ti-close:focus {
  font-size: 10px;
  color: red; }

.txt-center {
  text-align: center; }

.txt-left {
  text-align: left !important; }

.txt-right {
  text-align: right !important; }

.week-head {
  padding: 10px 0; }

.week-head button.border-btn {
  margin: 0; }

.week-head button.border-btn span {
  position: relative;
  top: 2px; }

.modify-date {
  border: 1px solid #F6BB42;
  padding: 5px;
  width: 100px;
  text-align: center;
  background: #ffffff; }

.cart-veg-icon {
  padding: 5px 5px;
  border: 1px solid #8cc152;
  color: #8cc152;
  background-color: #ffffff;
  top: 0;
  float: left;
  font-size: 12px;
  z-index: 0;
  position: relative;
  margin-right: 10px; }

.cart-nonveg-icon {
  padding: 5px 5px;
  border: 1px solid #da4453;
  color: #da4453;
  top: 0;
  float: left;
  font-size: 12px;
  z-index: 0;
  background-color: #ffffff;
  position: relative;
  margin-right: 10px; }

.view-info .ti-info {
  padding: 2px;
  border: 1px solid #F6BB42;
  border-radius: 50%;
  margin-left: 5px;
  background: #F6BB42;
  color: #fff;
  font-size: 15px; }

.add-item-name {
  text-align: left; }

.add-extra-div {
  border-bottom: 1px solid #ddd; }

.add-extra-div:last-child {
  border-bottom: 0; }

.add-extra-div .add-item-name, .add-extra-div .add-item-price {
  margin: 13px 0;
  text-transform: capitalize; }

.add-extra-items p {
  margin: 15px 0 0; }

.add-extra-items {
  padding-bottom: 10px; }

.cart-total {
  text-align: right;
  padding-top: 25px;
  padding-bottom: 23px; }

.remark-box {
  background-color: #f5f7fa;
  padding: 0;
  margin: 0;
  margin-top: 10px; }

.remark-fiels, .recuring-fiels {
  padding-top: 15px;
  padding-bottom: 15px;
  width: 100%; }

#show-loc, #show_cart {
  display: none; }

#cart-panel1, #cart-panel2 {
  padding: 5px;
  background-color: #f5f7fa;
  text-align: center; }

#cart-panel1, #cart-panel2 {
  padding: 0 15px 15px;
  display: none; }

.done-up1, .done-down1, .done-up2, .done-down2, .done-down3 {
  display: none; }

#cart-panel1 .form-group .select, #cart-panel2 .form-group .select {
  margin-bottom: 0; }

#cart-panel1 .form-group, #cart-panel2 .form-group {
  margin-bottom: 0; }

#cart-panel1 input[type=text], input[type=password], #cart-panel2 input[type=text], input[type=password] {
  margin: 0; }

#cart-panel1 p, #cart-panel2 p {
  margin: 10px 0 10px;
  text-align: left; }

.checkout-box button.close {
  padding: 5px 8px;
  cursor: pointer;
  background: #fff;
  border: 1px solid red;
  -webkit-appearance: none;
  border-radius: 50%;
  font-weight: bold;
  font-size: 15px;
  margin-top: -27px;
  position: relative;
  right: -8px;
  color: red;
  z-index: 2; }

.checkout-box button.close .ti-close {
  font-size: 10px;
  color: red; }

.close-btn, .close-btn:hover, .close-btn:active, .close-btn:focus {
  color: red; }

.item-labels ul {
  padding: 0;
  display: inline-block;
  margin-bottom: 0; }

.instant-item-labels ul {
  display: inherit; }

.item-labels ul li {
  list-style: none; }

/* end cart-page */
/* swap meal page */
.swap-main-panel {
  text-align: center;
  margin-bottom: 20px; }

.swap-payment {
  padding: 20px;
  background-color: #f5f7fa;
  margin: 0; }

.swap-payment .green-txt {
  color: #00b300; }

.swap-meal-box fieldset {
  border: 1px solid #ddd;
  margin: 0;
  padding: 0;
  background: #f5f7fa;
  margin-bottom: 15px; }

#myModal8 .changed-meal .veg-icon {
  position: static; }

#myModal8 .changed-meal .control__indicator {
  top: 36px; }

.changed-meal {
  border-top: 1px solid #ddd; }

#myModal8 .changed-meal {
  border-top: 1px solid #ddd;
  padding: 15px;
  margin-bottom: 0; }

.checked-meal {
  position: absolute;
  left: 3px;
  padding: 7px;
  background: #8CC152;
  border-radius: 50%;
  bottom: 60px;
  font-weight: bold;
  font-size: 13px;
  color: #ffffff; }

.pay-extra {
  color: #b30000;
  padding: 7px 0; }

.swap-meal-box legend {
  border: 1px solid #ddd;
  font-size: 17px; }

.swap-meal-box .address-box {
  margin-bottom: 0; }

/* end swap meal page */
/* start payonline-page */
.payonline-main-panel {
  text-align: center;
  margin-bottom: 20px; }

.payonline-main-panel h4.page-title {
  margin: 5px 0px 15px;
  text-transform: uppercase; }

.payonline-main-panel h4.meal-type::before {
  border-top: 1px solid #dfdfdf;
  content: "";
  margin: 0px;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: -1; }

.address-box {
  background-color: #f5f7fa;
  padding: 20px;
  min-height: 125px;
  margin-bottom: 15px; }

.box-title-name {
  text-align: left; }

.add-location {
  text-transform: uppercase; }

.address-box h4.box-title-name {
  margin-top: 0; }

.address-box p {
  margin-bottom: 0; }

.address {
  max-width: 300px;
  text-align: left; }

.fa-pencil, .add-address-btn i {
  font-size: 15px; }

.add-address-btn {
  color: #F6BB42; }

.add-address-btn:hover, .add-address-btn:focus, .add-address-btn:active {
  color: #F6BB42; }

.address-box .pull-right a {
  color: #F6BB42; }

.address-box .pull-left {
  text-align: left; }

.address-box .pull-right a:hover, .address-box .pull-right a:active, .address-box .pull-right a:focus, .address-box .pull-left a:hover, .address-box .pull-left a:focus, .address-box .pull-left a:active {
  text-decoration: none; }

.promo-box {
  background-color: #f5f7fa; }

.promo-code-btn {
  position: absolute !important;
  top: 0;
  background-color: #656d78;
  color: #ffffff;
  padding: 8.5px 12px;
  font-size: 14px;
  border-radius: 0;
  border: 2px solid #656d78;
  right: 0; }

.promo-code-btn:hover, .promo-code-btn:focus, .promo-code-btn:active {
  color: #ffffff;
  background-color: #24292d;
  border: 2px solid #24292d;
  transition: 0.5s ease-in-out; }

.promo-checked {
  background: #8cc152;
  border: 2px solid #8cc152; }

.applied {
  background-color: #8CC152 !important;
  padding: 5px 15px !important; }

button.applied {
  background-color: #8CC152;
  border: 1px solid #8cc152;
  color: #fff;
  padding: 5px 8px !important; }

.input-icon4 {
  float: right;
  right: 70px;
  margin-top: 13px;
  margin-right: 10px;
  position: absolute;
  z-index: 2;
  color: #e9573f; }

.modal-body .input-group {
  width: 100%; }

.modal-body .promo-box input[type="text"] {
  padding: 10px 90px 9px 12px !important; }

.sub-footer .promo-box input[type="text"], input[type="password"] {
  padding: 10px 90px 9px 12px; }

.item-rate-box {
  background-color: #f5f7fa;
  padding: 20px;
  margin-bottom: 15px; }

.main-rate-box {
  margin-bottom: 10px;
  margin-top: 20px; }

.main-rate-box .list-group {
  text-align: left; }

.main-rate-box .list-group-item {
  background-color: #f5f7fa;
  border: none; }

.main-rate-box .list-head {
  background-color: #e6e9ed; }

.main-rate-box .list-group-item:first-child, .list-group-item:first-child {
  border-top-right-radius: 0px;
  border-top-left-radius: 0px; }

.main-rate-box .border-btm {
  margin-bottom: 0;
  border-bottom: 1px solid #dfdfdf;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px; }

.main-rate-box .border-tp {
  margin-bottom: 0;
  border-top: 1px solid #dfdfdf;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px; }

.main-rate-box .list-group-item {
  position: relative;
  display: block;
  padding: 10px 20px; }

.main-rate-box .badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 15px;
  font-weight: normal;
  color: #656d78;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: transparent;
  border-radius: 0px; }

.main-rate-box .badge .fa-inr {
  font-size: 12px; }

.meal-box .price .fa-inr {
  font-size: 16px; }

.main-rate-box .ti-plus, .main-rate-box .ti-minus {
  margin-right: 5px;
  font-size: 12px; }

.fa-inr {
  font-size: 15px; }

.panel-title {
  text-align: left; }

.panel-default > .panel-heading {
  color: #656d78;
  background-color: #f5f7fa;
  border-color: none;
  border-bottom: 1px solid #dfdfdf; }

.panel-group .panel-heading + .panel-collapse > .panel-body, .panel-group .panel-heading + .panel-collapse > .list-group {
  border-top: none;
  border-bottom: 1px solid #dfdfdf; }

.panel-title > a, .panel-title > small, .panel-title > .small, .panel-title > small > a, .panel-title > .small > a {
  text-decoration: none; }

.main-payment-box #list-head {
  position: relative;
  display: block;
  background-color: #e6e9ed;
  padding: 10px 20px; }

.main-payment-box .list-group-item {
  position: relative;
  display: block;
  padding: 0px 0px; }

.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: none;
  border-radius: 0px;
  -webkit-box-shadow: none;
  box-shadow: none; }

.panel-heading {
  padding: 15px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 0px;
  border-top-left-radius: 0px; }

.main-payment-box .panel-default > .panel-heading:last-child {
  border-bottom: 1px solid #f5f7fa; }

.panel-group .panel + .panel {
  margin-top: 0px; }

.main-payment-box .cheque input[type="text"] {
  width: 200px;
  padding: 12px 12px 12px 12px;
  margin: 8px 0 0 0;
  display: inline-block;
  border: 1px solid #ccc; }

.main-payment-box .panel-heading .fa-circle {
  font-size: 15px;
  padding-right: 5px; }

.main-payment-box .cheque input[class*="redeem-wallet-field"] {
  display: none; }

.open-payment-mode {
  color: #F6BB42; }

.open-payment-mode .pull-right{
  color: #F6BB42; }

.address-box .form-group {
  margin-bottom: 0; }

.address-box .form-group .select {
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
  width: 100%;
  margin-top: 15px; }

.sub-footer {
  position: fixed !important;
  transition: top .5s linear;
  bottom: 0;
  z-index: 10;
  width: 100%;
  border-top: 1px solid #e8e8e8; }

.panel-title a:not(.collapsed) .ti-control-record:after {
  content: "";
  background: #F6BB42;
  position: absolute;
  top: 50%;
  margin-top: -4px;
  left: 6px;
  height: 8px;
  width: 8px;
  border-radius: 50%; }

.ti-control-record:before {
  content: "";
  height: 20px;
  width: 20px;
  background: #ffffff;
  border-radius: 50%;
  position: relative;
  display: inline-block;
  border: 1px solid #F6BB42;
  margin-right: 6px;
  vertical-align: middle; }

.ti-control-record {
  position: relative;
  height: 20px;
  width: 20px;
  display: block;
  float: left; }

span.choose-mod {
  display: block;
  padding-left: 26px;
  padding-top: 4px; }

.recuring-status {
  padding: 10px;
  text-align: left;
  background: #f5f7fa;
  border-left: 3px solid #656d78;
  margin-bottom: 20px; }

.alert-warning {
  text-align: left; }

.alert-dismissable .close, .alert-dismissible .close {
  top: 0px;
  font-size: 20px; }

/* end payonline-page */
/* My Account */
.myaccount-main-panel {
  text-align: center;
  margin-bottom: 20px;
  margin-top: 20px; }

.ac-detail-box {
  margin-bottom: 20px;
  background-color: #f5f7fa; }

.ac-info {
  text-align: left;
  padding: 10px 10px; }

.ac-info p {
  margin-bottom: 0; }

.common-heading-big b {
  font-size: 25px; }

.info-bg {
  background-color: #f5f7fa; }

.bg-lightGray, .wallet-table tbody tr:nth-child(odd) {
  background: #f5f7fa;
  position: relative; }

.left-my-account-icon {
  width: 100px;
  height: 100px; }

.theme-green-bg {
  background: #acbf41; }

.account-details-edit {
  font-size: 15px; }

.valign-wrapper {
  display: -webkit-flex;
  display: -ms-flexbox;
  /* display: flex; */
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center; }

.center {
  text-align: center;
  width: 100%; }

.clearfix:before, .clearfix:after {
  content: " ";
  display: table; }

.available_bal {
  width: 68px;
  height: 77px;
  background-position: -14px -174px;
  display: inline-block; }

.sprite_img {
  background: url("../images/sprite.png") no-repeat;
  display: block; }

.available_bal {
  width: 68px;
  height: 77px;
  background-position: -14px -174px;
  display: inline-block; }

.usable_bal {
  width: 80px;
  height: 79px;
  background-position: -98px -174px;
  display: inline-block; }

.locked_bal {
  width: 68px;
  height: 77px;
  background-position: -188px -174px;
  display: inline-block; }

.right-my-account {
  height: 100px; }

.bg-lightGray, .wallet-table tbody tr:nth-child(odd) {
  background: #eceef3; }

.blue-bg {
  background: #4FC1E9; }

.left-my-account-icon {
  width: 100px;
  height: 100px; }

.right-my-account {
  height: 100px; }

.clearfix:after {
  clear: both; }

.red-bg {
  background: #ED5565; }

.right-my-account .center {
  padding-left: 15px;
  text-align: left; }

.common-heading-big, .heading {
  font-size: 27px; }

.mb20 {
  margin-bottom: 20px; }

table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%; }

thead {
  background-color: #d6dbe2; }

th, td {
  border: none;
  text-align: left;
  padding: 8px; }

tr td {
  border-left: 1px solid #ffffff; }

tr:nth-child(even) {
  background-color: #f2f2f2; }

tr:nth-child(odd) {
  background-color: #fff; }

thead tr:nth-child(odd) {
  background-color: #d6dbe2; }

.info-table {
  overflow-x: auto;
  margin-bottom: 20px; }

h4.meal-type span.cr-dr {
  float: right;
  color: #656d78;
  font-size: 15px;
  padding: 0 15px 0 0;
  margin-top: -10px;
  position: absolute;
  right: 0;
  border: 1px solid #656d78;
  /*background-color: $header-background-color;*/ }

span.cr-dr .select__arrow {
  position: absolute;
  top: 13px;
  right: 0px; }

.cr-dr .select select {
  border: none; }

.cr-dr .select {
  margin-bottom: 0;
  background-color: #f5f7fa; }

.wallet-inp input[type=text], input[type=password] {
  margin-bottom: 0; }

.myaccount-main-panel .profile-edit {
  color: #F6BB42;
  font-weight: normal; }

.ti-pencil-alt {
  color: #F6BB42; }

.ti-reload {
  color: #F6BB42; }

.wallet-inp, .wallet-inp2 {
  display: none; }

#wallet-update-btn1, #wallet-update-btn2 {
  display: none; }

.dropdown-menu > li > a.dd-pd .wallet-amt {
  text-align: center;
  margin-left: 35px;
  color: #F6BB42;
  font-size: 15px; }

.profile-info {
  padding: 0 20px 20px;
  background: #f5f7fa; }

.user-profile span.ti-user {
  font-size: 35px;
  border: 1px solid #d3d9e0;
  padding: 20px;
  border-radius: 50%;
  background: #d3d9e0;
  color: #fff; }

.profile-table span.pro-icon {
  padding-right: 10px;
  color: #656d7c; }

.pro-title {
  padding: 3px 0; }

.pro-title .verified {
  padding-left: 10px;
  color: #8CC152; }

.pro-title .unverified {
  padding-left: 10px;
  color: #DA4453; }

.profile-table input {
  width: 200px;
  transition: 0.5s ease-in-out; }

.profile-table .select {
  width: 200px;
  transition: 0.5s ease-in-out; }

.resend-link, .resend-link:hover, .resend-link:active, .resend-link:focus {
  color: #F6BB42;
  margin-left: 10px; }

.user-profile-main {
  margin-top: 10px;
  background: #fff !important;
  margin-bottom: 20px !important; }

.user-profile {
  left: 42%;
  padding: 20px;
  background: #f5f7fa; }

.profile-table tr td:first-child {
  border-left: #ddd; }

.profile-btn {
  padding: 10px 15px !important;
  background: #656d78 !important; }

.upadate-panel {
  display: none; }

.unsubscribe-btn {
  margin-top: 15px; }

.open-otp-field input, .open-otp-field button {
  display: none; }

.hidden-field input, .hidden-field .select {
  display: none; }

.upadate-panel {
  padding: 0 10px; }

/* end My Account */
/* Offers */
.offers-main-panel {
  text-align: center;
  margin-bottom: 20px;
  margin-top: 20px !important; }

.offer-box-div {
  border: 1px solid #ddd;
  margin-bottom: 20px;
  background-color: #ffffff; }

.promo-code {
  color: #656d78; }

.share-link {
  color: #F6BB42; }

.share-link:hover, .share-link:focus, .share-link:active {
  color: #F6BB42; }

.dropdown-menu .ti-user, .dropdown-menu .ti-key, .dropdown-menu .ti-book, .dropdown-menu .ti-wallet, .dropdown-menu .ti-lock, .dropdown-menu .ti-receipt {
  padding-right: 15px; }

.dropdown-menu {
  border-radius: 0;
  box-shadow: none; }

/* end Offers */
/* term&condition */
.term-box-div {
  border: 1px solid #ddd;
  margin-bottom: 20px;
  padding: 30px;
  background-color: #ffffff; }

.term-box-div p {
  text-align: left;
  font-size: 16px; }

.term-box-div h4.term-title {
  text-align: left;
  font-weight: bold;
  color: #F6BB42;
  font-size: 15px; }

.term-box-div h4.term-sub-title {
  text-align: left;
  font-weight: bold;
  font-size: 15px; }

.term-box-div hr {
  margin-bottom: 10px;
  margin-top: 10px; }

.term-box-div a {
  color: #F6BB42; }

/* end term&condition */
/* alert modal */
.alert-text {
  text-align: left;
  font-size: 17px; }

/* end alert modal */
/* change password */
.wallet-from input[type=password] {
  width: 100%;
  padding: 10px 12px 10px 12px;
  display: inline-block;
  border: 1px solid #ddd;
  color: black; }

/* end change password */
/* payment gateways */
.check-margin-left {
  margin: auto;
  display: table;
  padding: 20px; }

.payment-gateways-box {
  background-color: #f5f7fa;
  padding: 0px;
  text-align: center;
  margin-bottom: 50px; }

.payment-gateways-box .control__indicator {
  position: absolute;
  top: 8px;
  left: 0;
  height: 20px;
  width: 20px; }

button.proceed-btn {
  margin-bottom: 40px !important; }

.payment-gateways-box .control {
  margin-top: 15px; }

/* end payment gateways */
/* loader with overlay */
.hg-100 {
  height: 100%; }

.loader-div {
  background: rgba(0, 0, 0, 0.41);
  height: 100%;
  position: fixed;
  width: 100%;
  z-index: 9999;
  top: 0; }

.loader {
  position: relative;
  height: 100%;
  width: 100%; }

.loader-div .loader img {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -32px;
  margin-left: -32px;
  background: #fff;
  padding: 5px;
  border-radius: 5px; }

/* end loader with overlay */
/* thank you page*/
.thnks-bg {
  background: transparent; }

.ty-bgcol {
  color: #F2ECBF;
  padding: 50px 0 0; }

.center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
  background-color: #fff;
  border-radius: 50%;
  padding: 20px;
  border: 2px solid #8CC152; }

.thanks-subtitle {
  font-size: 24px;
  font-weight: lighter;
  color: #fff;
  padding-top: 15px; }

.ty-bgcol h4 {
  color: #8CC152;
  font-weight: bold;
  font-size: 40px; }

.p10-0 {
  padding: 10px 0; }

.thnks-content {
  line-height: 38px;
  font-size: 20px;
  margin-bottom: 10px; }

.p30-0 {
  padding: 30px 0; }

.text-center {
  text-align: center; }

.back-menu-btn, .back-menu-btn:hover, .back-menu-btn:focus, .back-menu-btn:active, .back-menu-btn.active, .open .dropdown-toggle.back-menu-btn {
  background-color: #F6BB42;
  border-color: #F6BB42;
  color: #fff;
  border-radius: 0;
  margin-bottom: 30px; }

/* end thank you page*/
/* list view */
.list-view-panel {
  margin: 25px 0; }

.list-view-panel .nav > li {
  text-align: left; }

.list-view-panel ul.nav-pills {
  background-color: #f5f7fa;
  text-align: left;
  padding: 10px 20px; }

.list-view-panel .nav-pills > li > a {
  border-radius: 0;
  border-bottom: 1px solid #f5f7fa; }

.list-view-panel .nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
  color: #F6BB42;
  background-color: #f5f7fa;
  border-bottom: 1px solid #F6BB42; }

.list-view-panel .nav-pills > li a.active {
  color: #F6BB42;
  background-color: #f5f7fa;
  border-bottom: 1px solid #F6BB42; }

.list-view-panel .nav-stacked > li + li {
  margin-top: 0;
  margin-left: 0; }

.list-view-panel #section1 h4 {
  text-align: center; }

.list-icon {
  float: left;
  margin-right: 15px;
  margin-bottom: 5px; }

.small-border-btn {
  border: 1px solid #F6BB42;
  padding: 5px 15px;
  background-color: #f5f7fa;
  color: #F6BB42; }

.small-border-btn:hover, .small-border-btn:focus, .small-border-btn:active {
  color: #ffffff;
  background-color: #F6BB42;
  transition: 0.5s ease-in-out; }

.mt-5 {
  margin-top: 5px !important; }

.list-view-panel b.lv-price {
  margin-right: 20px; }

.list-view-panel .lv-item {
  border-bottom: 1px solid #ddd;
  padding: 15px 20px; }

.list-view-panel .mt-25 {
  margin-top: 25px;
  text-align: center; }

.list-view-panel .count-icon {
  padding: 5px 9px;
  border: 1px solid #f5f7fa;
  color: #656d78;
  position: relative;
  top: 0;
  left: 0;
  z-index: 0;
  text-align: center;
  background-color: transparent; }

.list-view-panel .minus-icon, .list-view-panel .plus-icon {
  padding: 5px 5px;
  border: 1px solid #e9573f;
  color: #e9573f;
  position: relative;
  top: 0;
  left: 0;
  font-size: 12px;
  z-index: 0;
  background-color: transparent;
  text-decoration: none;
  font-weight: bold; }

.list-view-panel .minus-icon:hover {
  color: #ffffff;
  background-color: #F6BB42; }

.list-view-panel .list-title {
  padding: 16px 20px;
  background-color: #e6e9ed;
  margin: 0; }

.instant-meal-name {
  text-transform: capitalize; }

#your-order {
  background-color: #f5f7fa;
  margin-bottom: 10px; }

.list-view-panel #your-order .lv-price {
  margin-left: 20px;
  margin-right: 0; }

.list-view-panel .lv-tt {
  padding: 10px 20px;
  background-color: #f5f7fa; }

.lv-tt .total {
  font-size: 18px; }

.lv-tt .big-Login-button {
  font-size: 18px;
  padding: 10px 20px; }

#ct-panel {
  background-color: #e6e9ed; }

#ct-panel .control {
  margin-top: 15px; }

#list-search {
  display: flex; }

#list-search .input-group {
  padding: 8px; }

.dt {
  padding: 15px 20px;
  background-color: #f5f7fa; }

.sec-top {
  padding-top: 50px;
  background-color: #f5f7fa; }

.instant-veg-nonveg {
  margin: 0 !important; }

.list-view-panel.ps-fx {
  /*asif*/
  /*
  
  */
  /*asif end*/ }
  .list-view-panel.ps-fx #myScrollspy {
    position: fixed;
    top: 0;
    /*asif*/
    /*@include transition(width,0.5s,linear);*/
    /*asif end*/ }
  .list-view-panel.ps-fx .center-panel {
    /*@include transition(all,0.5s,linear);*/ }

.dropdown-parent-category {
  margin: 0;
  width: 100%;
  background: #F6BB42;
  padding: 8px 5px;
  border: 2px solid #f5f7fa; }
  .dropdown-parent-category a {
    font-size: 18px;
    font-weight: bold; }

.list-view-panel.ps-fx #your-order-panel {
  position: fixed;
  top: 0;
  right: 36px; }

.list-view-panel.ps-fx #ct-panel {
  position: fixed;
  top: 0;
  z-index: 5;
  /*asif*/
  /*@include transition(all,0.5s,linear);*/
  /*end*/ }

#items-count {
  background-color: #f5f7fa;
  position: fixed;
  padding: 0 0 0 0;
  bottom: 0;
  width: 100%;
  z-index: 3; }

#items-count .clearfix .pull-right a.crt-pr, #items-count .clearfix .pull-right a.crt-pr:hover, #items-count .clearfix .pull-right a.crt-pr:focus, #items-count .clearfix .pull-right a.crt-pr:active {
  background-color: #e6e9ed;
  padding: 8px 20px;
  color: #F6BB42; }

#items-count .clearfix .pull-right a:hover, #items-count .clearfix .pull-right a:focus, #items-count .clearfix .pull-right a:active {
  text-decoration: none;
  background-color: #f5f7fa;
  transition: 0.5s ease-in-out;
  /*box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);*/ }

#items-count a.fooddialer {
  color: #F6BB42;
  text-decoration: none; }

.itm-in-crt-panel {
  display: none; }

.itm-in-crt-btn {
  background-color: #e6e9ed;
  padding: 20px 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0 -3px 7px 0; }

.itm-in-crt-btn .badge {
  background: #8CC152;
  font-size: 16px;
  margin-left: 5px; }

.list-hg {
  height: 400px;
  overflow-y: auto;
  margin-top: 25px;
  margin-bottom: 15px; }

.mob-list-hg {
  height: 140px;
  overflow-y: auto;
  margin-top: 10px;
  margin-bottom: 10px; }

/* end list view */
/* list view with img */
.list-view-panel .img-lv-item {
  border-bottom: 1px solid #ddd;
  padding: 15px 0px;
  margin: 0; }

.list-view-panel b.img-lv-price {
  margin-right: 0px;
  float: right;
  padding-top: 10px;
  padding-bottom: 10px; }

/* end list view with img */
/* cart side panel */
.side-cart {
  height: 100%;
  position: fixed;
  z-index: 6;
  top: 0;
  right: -300px;
  background-color: #f5f7fa;
  overflow-x: hidden;
  /*asif*/
  /*transition: 0.5s;*/
  /*asif end*/
  padding-top: 10px;
  border-left: 1px solid #ddd;
  width: 300px; }

.side-cart a.link, .side-cart a.link:hover, .side-cart a.link:focus, .side-cart a.link:active {
  color: #F6BB42; }

.side-cart a:hover, .offcanvas a:focus {
  color: #f1f1f1; }

.side-cart .closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px;
  margin-left: 50px; }

.side-cart .closebtn:hover {
  color: #656d78; }

.cart-open .side-cart {
  right: 0; }

.cart-open .main-container {
  margin-right: 300px; }

.cart-open .main-container {
  margin-right: 300px; }

.cart-close-btn {
  float: right; }

.your-order-panel .lv-item {
  padding: 10px 10px 10px 0;
  border-bottom: 1px solid #ddd; }

.your-order .lv-item {
  padding: 10px 10px 10px 0;
  border-bottom: 1px solid #ddd; }

.your-order .plus-minus {
  margin-top: 0;
  float: left; }

.your-order-panel .minus-icon, .your-order .plus-icon {
  padding: 4px 4px; }

.your-order-panel span.count-icon {
  padding: 0 7px; }

.your-order-panel .lv-price {
  float: right; }

.your-order .cart-trash {
  background-color: #f5f7fa;
  color: red;
  float: right;
  margin-top: 5px; }

/*.stickyfoo-cart-panel{
    position: fixed;
    bottom: 0;
    right: 0;
    width: 300px;
    padding: 0 15px;
}*/
/* date picker */
.ui-datepicker {
  width: 28em !important;
  z-index: 6 !important; }

.ui-datepicker td span, .ui-datepicker td a {
  padding: 15px !important; }

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  background: #656d78 !important;
  color: #ffffff !important;
  border: #F6BB42 !important; }

.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active, a.ui-button:active, .ui-button:active, .ui-button.ui-state-active:hover {
  background: #F6BB42 !important;
  color: #ffffff !important;
  border: #F6BB42 !important; }

.ui-datepicker-div {
  z-index: 2 !important; }

/*asif */
.tp-dottedoverlay.twoxtwo {
  background: rgba(0, 0, 0, 0.5); }

.home {
  position: relative; }

/*asif end*/
@media screen and (max-height: 450px) {
  .side-cart {
    padding-top: 15px; }

  .side-cart a {
    font-size: 18px; } }
/* end cart side panel */
/* home page*/
/* Slideshow container */
.mySlides {
  display: none; }

.slideshow-container {
  max-height: 500px;
  position: relative;
  margin: auto;
  background-color: #000; }

.slideshow-container img {
  height: 500px;
  opacity: 0.6; }

/* Next & previous buttons */
.home-prev, .home-prev:hover, .home-prev:active, .home-prev:focus {
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: auto;
  padding: 17px 9px 13px 4px;
  margin-top: -22px;
  color: #F6BB42;
  transition: 0.6s ease;
  background-color: #ffffff;
  border-radius: 0 50px 50px 0; }

.home-next, .home-next:hover, .home-next:active, .home-next:focus {
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: 0;
  width: auto;
  padding: 17px 4px 13px 9px;
  margin-top: -22px;
  color: #F6BB42;
  transition: 0.6s ease;
  border-radius: 50px 0 0 50px;
  background-color: #ffffff; }

/* Caption text */
.text b {
  color: #ffffff;
  font-size: 65px;
  padding: 8px 12px;
  position: absolute;
  top: 27%;
  width: 100%;
  text-align: center; }

/* Fading animation */
/*.fade {
  -webkit-animation-name: fade;
  -webkit-animation-duration: 1.5s;
  animation-name: fade;
  animation-duration: 1.5s;
}*/
@-webkit-keyframes fade {
  from {
    opacity: 0.4; }
  to {
    opacity: 1; } }
@keyframes fade {
  from {
    opacity: 0.4; }
  to {
    opacity: 1; } }
/* On smaller screens, decrease text size */
@media only screen and (max-width: 300px) {
  .prev, .next, .text {
    font-size: 11px; } }
h4.homepage-title {
  text-align: center;
  color: #F6BB42;
  margin-top: 35px;
  margin-bottom: 35px; }

.homepage-title b {
  text-align: center;
  font-size: 25px;
  color: #F6BB42;
  text-transform: capitalize; }

.homepage-title span b {
  text-align: center;
  font-size: 25px;
  color: #656d78; }

.about-us-img img {
  width: 100%;
  margin-bottom: 20px; }

.about-us-box {
  margin-bottom: 30px; }

.about-us-text {
  background-color: #f5f7fa;
  text-align: left;
  padding: 20px;
  border: 1px solid #ddd;
  overflow-y: auto;
  max-height: 390px; }

.about-us-text .homepage-title {
  text-align: left;
  margin-top: 8px;
  margin-bottom: 8px; }

.about-us-text .homepage-title b {
  font-size: 17px;
  text-align: left;
  color: #F6BB42; }

.about-us-text .homepage-title b span {
  color: #656d78; }

.how-it-works-box .homepage-title b {
  font-size: 25px;
  text-align: left;
  color: #F6BB42; }

.how-it-works-box .homepage-title b span {
  color: #656d78; }

.order-timing-box .homepage-title b span {
  color: #656d78; }

.app-box-main .homepage-title b span {
  color: #656d78; }

.about-info-remove {
  position: absolute;
  right: 12%;
  padding-top: 55px;
  padding-bottom: 30px; }

.about-info-add {
  position: relative;
  padding-top: 0px;
  padding-bottom: 0px; }

.our-services-box {
  background-color: #24292d;
  padding-top: 20px;
  padding-bottom: 30px; }

b span.white-word {
  color: #ffffff; }

.our-services {
  text-align: center;
  color: #ffffff;
  padding-top: 10px;
  padding-bottom: 10px; }

.our-services img {
  width: 60px; }

.howitworks-step {
  padding: 15px;
  width: 50px;
  margin: auto;
  background: #F6BB42;
  color: #ffffff;
  border-radius: 50%;
  font-weight: bold; }

.how-it-works-box {
  background-color: #f5f7fa;
  padding-top: 20px;
  padding-bottom: 30px; }

.how-it-works {
  text-align: center;
  color: #656d78;
  margin-bottom: 20px; }

.how-it-works-number {
  color: #ffffff;
  text-align: center;
  font-size: 15px;
  margin-bottom: 30px; }

.what-client-say-box {
  background-color: #000;
  background-image: url("../../images/banner3.jpg");
  z-index: 1;
  position: relative;
  border-radius: 0;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover; }

.img-container {
  background-color: rgba(0, 0, 0, 0.5); }

.wcs-box {
  text-align: center;
  color: #656d78;
  padding: 20px 20px 10px;
  position: relative;
  background-color: #ffffff;
  margin-bottom: 15px; }

.wcs-box:after {
  top: 99%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-top-color: #ffffff;
  border-width: 15px;
  margin-left: -15px; }

.wcs-box .testimonial {
  height: 110px;
  overflow-y: auto;
  text-align: justify;
  padding-right: 7px;
  font-size: 14px; }

.wcs-box h4.client-name {
  color: #F6BB42;
  margin-top: 15px;
  text-transform: capitalize; }

.owl-carousel .owl-item .wcs-main-box {
  text-align: center;
  margin-bottom: 20px; }

.owl-carousel .owl-item .wcs-main-box img {
  width: 50px;
  margin-bottom: 20px;
  display: table;
  margin: auto;
  border-radius: 50%; }

.app-box-main {
  background-color: #f5f7fa;
  padding-top: 70px;
  z-index: 1;
  position: relative; }

h4.app-title {
  text-align: center;
  color: #F6BB42;
  margin-top: 35px;
  margin-bottom: 10px !important; }

.app-box {
  text-align: center; }

.app-img {
  text-align: center;
  margin-top: 15px;
  margin-bottom: 15px; }

.mobile-left {
  text-align: left;
  z-index: 1; }

img.playstore-right {
  margin-right: 10px;
  margin-bottom: 15px;
  margin-left: 10px; }

.order-timing-box {
  background-color: #ffffff;
  padding-top: 20px;
  padding-bottom: 30px;
  z-index: 9;
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 5px 0px; }

.step-border-bottom {
  position: relative; }

.step-border-bottom:before {
  content: "";
  height: 5px;
  background: #656d78;
  width: 75%;
  position: absolute;
  left: 12%;
  top: 22px; }

.sl-bottom-img {
  text-align: center;
  bottom: 0px;
  position: absolute; }

.sl-text {
  text-align: center;
  font-size: 60px;
  color: #fff; }

.sl-text .container {
  text-shadow: 2px 2px black;
  text-transform: capitalize; }

.fullwidthbanner-container {
  /*max-height: 500px;*/ }

.common-arrow, .tp-leftarrow.default, .tp-rightarrow.default {
  color: #F6BB42;
  font-size: 31px;
  /* text-align: center; */
  /* opacity: 0.7; */
  filter: alpha(opacity=70);
  /* line-height: 57px; */
  z-index: 100;
  cursor: pointer;
  background-color: #fff;
  top: 50% !important;
  margin-top: -32px !important; }

.tp-leftarrow.default {
  padding: 10px 15px 10px 5px;
  border-radius: 0px 50px 50px 0; }

.tp-rightarrow.default {
  padding: 10px 5px 10px 15px;
  border-radius: 50px 0px 0px 50px; }

.sl-locaion {
  position: absolute;
  z-index: 99;
  left: 0;
  bottom: 50px;
  text-align: center;
  width: 100%; }

.sl-locaion input[type=text], input[type=password] {
  padding: 10px 12px 10px 40px;
  margin: 0; }

.input-icon3 {
  float: left;
  left: 12px;
  margin-top: -27px;
  position: relative;
  z-index: 2;
  color: #656d78; }

/*.sl-form{
     display: flex;
}*/
.your-loc-fld {
  margin-right: 5px; }

.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
  background-color: #F6BB42 !important; }

button.order-button {
  background-color: #F6BB42;
  color: #ffffff;
  padding: 9px 20px;
  border: none;
  cursor: pointer;
  font-size: 16px; }

	/*asif
 .sl-input-fld .form-group {
     margin-right: 5px;
     margin-bottom: 0;
     width: 125px;
 }
asif end*/
.sl-input-fld .form-group .select {
  margin-bottom: 0; }

/* end home page*/
/* footer - 2 */
.footer-2 {
  background-color: #24292d;
  background-position: top;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 9;
  position: relative; }

footer .footer-overlay {
  color: #fff; }

.mb20 {
  margin-bottom: 20px; }

footer .footer-overlay .footer_heading {
  font-size: 16px; }

.mb10 {
  margin-bottom: 10px; }

footer .footer-overlay .icons {
  font-size: 18px;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  padding: 8px 6px 8px 6px;
  border: 1px solid #ffffff;
  text-align: center;
  transition: all 0.3s;
  margin-right: 8px;
  margin-bottom: 10px; }

.disilbk {
  display: inline-block;
  vertical-align: top; }

.footer-2 a {
  color: #fff; }

.footer-2 a.fooddialer, .footer-2 a.fooddialer:hover, .footer-2 a.fooddialer:active, .footer-2 a.fooddialer:focus {
  color: #F6BB42;
  text-decoration: none; }

.footer-2 .social-icon {
  word-spacing: 10px;
  text-align: left; }

.footer-2 a span, .footer-2 a span:hover, .footer-2 a span:active, .footer-2 a span:focus {
  color: #ffffff;
  text-decoration: none;
  font-size: 15px; }

footer .footer-overlay ul {
  padding: 0;
  list-style: none; }

footer .footer-overlay ul li {
  margin-bottom: 10px; }

footer .footer-overlay .form-group {
  margin: 0 0 15px 0; }

footer .footer-overlay .form-group input, footer .footer-overlay .form-group textarea {
  border: 1px solid #fff;
  border-radius: 0;
  background: transparent;
  color: #fff;
  filter: alpha(opacity=70); }

footer .footer-overlay .form-group textarea {
  resize: vertical; }

.full-width {
  width: 100%;
  margin-bottom: 0 !important; }

.footer-2 .btn-green {
  background: #F6BB42;
  border-color: #F6BB42; }

.footer-2 .btn-small {
  border-radius: 0;
  padding: 7px 30px;
  position: relative;
  transition: all 0.3s;
  z-index: 1;
  overflow: hidden;
  display: block; }

.footer-2 .p15 {
  padding: 15px; }

.footer-2 .visible-xs {
  text-align: center;
  padding: 5px;
  background-color: #24292d; }

.white-placeholder::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #ffffff; }

.white-placeholder::-moz-placeholder {
  /* Firefox 19+ */
  color: #ffffff; }

.white-placeholder:-ms-input-placeholder {
  /* IE 10+ */
  color: #ffffff; }

.white-placeholder:-moz-placeholder {
  /* Firefox 18- */
  color: #ffffff; }

/* end footer - 2 */
/* rounded progress bar */
.roated-pb {
  text-align: center; }

.progress-bar .rotate {
  clip: rect(0 100px 200px 0);
  background-color: #F6BB42 !important; }

.progress-bar .right {
  clip: rect(0 100px 200px 0);
  transform: rotate(180deg);
  opacity: 0;
  background-color: #F6BB42 !important; }

/* end rounded progress bar */
.cart-owl-box {
  padding: 0 30px;
  margin-bottom: 35px;
  z-index: 1; }

.owl-carousel .owl-stage-outer .owl-nav .owl-next {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 2;
  background: #F6BB42;
  padding: 3px 10px;
  color: #ffffff; }

.owl-carousel .owl-stage-outer .owl-nav .owl-prev {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 2;
  background: #F6BB42;
  padding: 3px 10px;
  color: #ffffff; }

.owl-prev {
  position: absolute;
  top: 50%;
  left: -30px;
  height: 25px;
  margin-top: -10px;
  padding: 1px 5px;
  border-radius: 3px;
  color: #F6BB42;
  font-weight: bold; }

.owl-next {
  position: absolute;
  top: 50%;
  right: -30px;
  height: 25px;
  margin-top: -10px;
  padding: 1px 5px;
  border-radius: 3px;
  color: #F6BB42;
  font-weight: bold; }

.ti-angle-right, .ti-angle-left {
  font-size: 20px; }

.checkout-main-panel .cart-owl-box h4 {
  margin: 5px 0px 5px !important;
  text-align: center; }

.checkout-main-panel .cart-owl-box h4.item-name, .checkout-main-panel .cart-owl-box h4.price {
  margin: 18px 0px 5px !important;
  text-align: center; }

body.frame {
  padding: 40px;
  overflow: hidden;
  position: relative;
  color: white;
  background: #191919;
  height: 100%; }

#content {
  text-align: center;
  height: auto; }

#outer-wrapper #inner-wrapper #table-wrapper {
  display: table;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.5); }

#outer-wrapper #inner-wrapper #table-wrapper #row-content {
  display: table;
  height: 100%;
  width: 100%; }

#outer-wrapper #inner-wrapper #table-wrapper .container {
  height: 100%;
  display: table; }

#outer-wrapper {
  -moz-transition: 1s;
  -webkit-transition: 1s;
  transition: 1s;
  height: 100%;
  position: relative;
  -moz-box-shadow: 0 0 50px rgba(0, 0, 0, 0.4);
  -webkit-box-shadow: 0 0 50px rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.4); }

.animate.translate-z-in.in {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1;
  -moz-transform: translateZ(0px);
  -ms-transform: translateZ(0px);
  -webkit-transform: translateZ(0px);
  transform: translateZ(0px); }

#outer-wrapper #inner-wrapper {
  position: relative;
  height: 100%;
  overflow: hidden;
  background-image: url("../images/web.jpg");
  background-position: 50% 50%; }

#outer-wrapper #inner-wrapper #table-wrapper #row-content h1 {
  text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.65);
  text-transform: uppercase;
  font-size: 90px;
  font-weight: 700; }

#outer-wrapper #inner-wrapper #table-wrapper #row-content #content-wrapper {
  display: table-cell;
  vertical-align: middle;
  -moz-perspective: 1000px;
  -webkit-perspective: 1000px;
  perspective: 1000px;
  z-index: 1; }

.opacity-70 {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);
  opacity: 0.7; }

.error-main {
  text-align: center;
  margin: 50px 0 20px; }

.error-main img {
  margin: auto !important; }

.wallet-panel {
  margin-top: 20px; }

.wallet-box {
  background-color: #f5f7fa;
  width: 50%;
  margin-left: 25%;
  padding: 0px;
  text-align: center;
  margin-bottom: 50px; }

.wallet-from {
  padding: 30px;
  position: relative;
  text-align: left; }

.load-more-2 {
  text-align: center;
  margin-bottom: 25px;
  margin-top: 25px; }

.load-more-2 a {
  background-color: #F6BB42;
  padding: 8px 20px;
  color: #fff;
  text-align: center;
  font-size: 17px;
  display: block; }

.load-more-2 a:hover, .load-more-2 a:focus, .load-more-2 a:active {
  text-decoration: none;
  background-color: #24292d;
  transition: 0.5s ease-in-out; }

.unsub {
  padding: 10px 25px; }

.otp input[type=text], input[type=password] {
  margin-bottom: 0;
  padding: 5px 12px 5px 12px;
  width: 100px;
  margin-top: 7px; }

.Resend-otp, .otp {
  margin-right: 5px; }

.ac-detail-box button.small-btn {
  background-color: #F6BB42;
  color: #ffffff;
  padding: 5px 8px;
  margin: 8px 0;
  border: none;
  cursor: pointer; }

.paid {
  padding: 3px 14px;
  background: #A0D468;
  color: #fff;
  font-size: 12px; }

.unpaid {
  padding: 3px 7px;
  background: #ED5565;
  color: #fff;
  font-size: 12px; }

.view-btn, .view-btn:hover, .view-btn:focus, .view-btn:active {
  padding: 5px 10px;
  background: #AAB2BD;
  color: #fff;
  margin-right: 5px; }

.action-btn, .action-btn:hover, .action-btn:active, .action-btn:focus {
  padding: 5px 10px;
  background: #F6BB42;
  color: #fff;
  margin-right: 5px; }

.booking-history {
  margin-top: 0; }

.booking-history table {
  background-color: #ffffff; }

.booking-history-tab ul#myTabs {
  text-transform: uppercase;
  margin-bottom: 0; }

.booking-main-panel {
  text-align: center;
  margin-bottom: 0; }

.booking-history .load-more {
  text-align: center;
  margin-bottom: 20px; }

.booking-history a.action-pen {
  margin-right: 15px;
  color: #F6BB42; }

.booking-history a.action-close {
  color: red; }

.booking-history a.view-dates {
  color: #F6BB42; }

.book-order, .book-order:hover, .book-order:focus, .book-order:active {
  float: right;
  padding: 7px 10px;
  background: #F6BB42;
  color: #fff;
  margin: 20px 0 10px; }

.order-status-dd {
  background: #F6BB42; }

.radio-btns {
  text-align: center; }

.text-center {
  text-align: center; }

.plan-meal-div {
  margin-bottom: 20px; }

.productadd button, .productadd button:hover, .productadd button:active, .productadd button:focus {
  background: none;
  border: none;
  outline: none; }

.trash-icon {
  padding: 7px 7px;
  border: 1px solid #F6BB42;
  color: #F6BB42;
  position: relative;
  top: 0;
  left: 0;
  font-size: 12px;
  z-index: 0;
  background-color: transparent;
  text-decoration: none;
  font-weight: bold; }

.trash-icon:hover, .trash-icon:focus, .trash-icon:active {
  color: #ffffff;
  background-color: #F6BB42;
  transition-duration: 0.5s;
  text-decoration: none; }

.productadd label {
  float: none;
  text-transform: uppercase;
  text-align: center; }

.multi-field {
  padding: 10px 20px; }

.productadd .form-group {
  margin-bottom: 0; }

.productadd .select {
  margin-bottom: 0; }

.btn_click {
  margin-top: 10px;
  margin-bottom: 4px; }

.productadd .cart-price {
  margin-top: 10px;
  margin-bottom: 4px; }

.productadd .add-meal-bn {
  margin-top: 10px;
  margin-bottom: 4px;
  text-align: right; }

.choose-promocode .list-group-item {
  background-color: #f5f7fa;
  border: none;
  height: 40px;
  font-weight: normal; }

.choose-promocode .badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 15px;
  font-weight: normal;
  color: #656d78;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: transparent;
  border-radius: 0px; }

span .rs-input {
  margin: 0;
  padding: 0;
  height: 20px;
  width: 100px; }

.list-group .hg-app {
  height: 75px; }

.date-selecion {
  margin-top: 20px; }

.date-selecion-box {
  padding: 20px;
  background-color: #f5f7fa; }

.date-pick-div {
  margin-top: 20px; }

.date-pick-div button.small-btn {
  margin: 26px 0 0; }

.choose-promocode h4.meal-type {
  margin: 25px 0px 25px; }

.check-box-center {
  padding: 20px 10px 0px;
  text-align: center; }

.check-margin-left {
  margin: auto;
  display: table; }

button.tip-top {
  float: right;
  margin-right: 15px;
  border: none;
  background: none;
  outline: none; }

.date-selecion-box input[type=text], .date-selecion-box input[type=password] {
  padding: 8px 12px 7px 12px; }

.balance-overlay-1, .balance-overlay-2, .balance-overlay-3 {
  height: 100%;
  opacity: 0;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  width: 100%;
  font: 18px opensans-regular-webfont;
  color: #222; }

.green-trans-bg:hover .balance-overlay-1 {
  opacity: 1;
  background: rgba(172, 191, 65, 0.8);
  padding: 30px 0 30px; }

.blue-trans-bg:hover .balance-overlay-2 {
  opacity: 1;
  background: rgba(79, 193, 233, 0.8);
  padding: 42px 0 30px; }

.pink-trans-bg:hover .balance-overlay-3 {
  opacity: 1;
  background: rgba(237, 85, 101, 0.8);
  padding: 42px 0 30px; }

.multi-field-wrapper {
  padding-bottom: 10px; }

.input-group input {
  padding: 10px 90px 9px 12px; }

.contact_page {
  text-align: center;
  margin-bottom: 20px;
  margin-top: 20px; }

.contact_page form {
  background: #f5f7fa;
  width: 100%;
  height: auto;
  display: block;
  padding: 40px 30px; }

.contact-from .ti-user {
  padding-right: 0; }

.contact-textarea {
  width: 100%;
  padding: 6px 12px;
  color: #000;
  border: none;
  height: 100px;
  border: 1px solid #ddd; }

.company-info-panel {
  background: #f5f7fa;
  height: auto;
  display: block;
  padding: 25px;
  text-align: left; }

.map {
  margin-top: 20px;
  margin-bottom: 20px; }

.map iframe {
  width: 100%;
  height: 478px;
  display: block;
  pointer-events: none;
  position: relative;
  /* IE needs a position other than static */ }

.map iframe.clicked {
  pointer-events: auto; }

/*********************** asif *******************************/
.affix {
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear; }

#myTabs.nav-tabs > li {
  border-right: 0;
  border-left: 1px solid #aab2bd; }
  #myTabs.nav-tabs > li > a {
    padding: 15px 15px;
    border-bottom: 3px solid #f5f7fa;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    border-radius: 0; }

#myTabs.nav-tabs > li:first-child {
  border-left: 0; }

.header-nav-parent.affix .time {
  display: none; }

.progress-bar {
  display: inline-block;
  padding-left: 0;
  width: 200px; }

.menu-open .header-nav-parent.affix {
  width: calc(100% - 250px); }

/****************** asif end ***************************************/
/* Maurya start */
.cart-panel {
  padding: 5px;
  background-color: #f5f7fa;
  text-align: center; }

.close-panel {
  float: right; }

.side-cart a.cart-trash, .side-cart a.cart-trash:hover, .side-cart a.cart-trash:active {
  color: #e9573f !important;
  float: right;
  margin-top: 5px; }

.innerpage .pgheading {
  color: #707074;
  font-size: 20px;
  font-family: opensans-bold-webfont;
  text-align: center;
  margin-top: 20px; }

.greencheck {
  color: #8CC152;
  margin-left: 5px; }

.redcheck {
  color: #DA4453;
  margin-left: 5px; }

.chosen-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 13px;
  zoom: 1;
  *display: inline;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none; }

.chosen-container * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.dn {
  display: none; }

.bookhis {
  color: white; }

/*# sourceMappingURL=custom.css.map */
.cart-panel {
  padding: 20px 15px 15px;
  display: none; }

.cart-panel .form-group .select {
  margin-bottom: 0; }

.cart-panel .form-group {
  margin-bottom: 0; }

.cart-panel p {
  margin: 10px 0 10px;
  text-align: left; }

.cart-panel .form-group .select {
  margin-bottom: 10px;
  width: 92%; }

.check-meal-ckbx {
  margin: 0 0 20px; }

.mealsnot {
  margin: auto;
  display: table; }

.cart-total-box {
  background-color: #f5f7fa;
  padding: 0;
  margin: 0;
  margin-bottom: 100px; }

.main-payment-box a .control {
  padding-right: 0; }

#myModal8 .changed-meal .nonveg-icon {
  position: static;
  margin-top: 0; }

/* captcha on contact us page */
.captcha {
  position: absolute;
  z-index: 9;
  padding: 8px 10px 5px;
  width: 100%;
  font-size: 23px;
  text-decoration: line-through; }

.cms-height {
  min-height: 350px; }

/* order timing on home page */
.progress-bar div span {
  position: absolute;
  font-size: 22px;
  line-height: 35px;
  height: 180px;
  width: 180px;
  left: 10px;
  top: 10px;
  text-align: center;
  border-radius: 50%;
  background-color: white;
  padding-top: 57px; }

.add-extra-div-div:last-child {
  border-bottom: 0; }

button.big-btn {
  background: #e9573f;
  color: #fff;
  border-radius: 0;
  display: block;
  border: 0;
  padding: 8px 0; }

.main-payment-box label.panel-subtitle {
  padding-right: 0; }

.item-labels .add-extra-div {
  border: 0; }

.add-extra-div {
  border-bottom: 1px solid #ddd; }

.promo-checked {
  background: #8cc152;
  border: 2px solid #8cc152; }

.login-from input[type="text"]:disabled {
  cursor: not-allowed; }

/* Maurya end */
.scroll_tabs_theme_light div.scroll_tab_inner span, .scroll_tabs_theme_light div.scroll_tab_inner li {
  padding-left: 24px;
  padding-right: 24px;
  line-height: 40px;
  font-size: 20px;
  text-transform: capitalize;
  background-color: #fff;
  border-left: 1px solid #F6BB42;
  border-top: 1px solid #F6BB42;
  border-bottom: 1px solid #F6BB42;
  color: #656d78; }

.scroll_tabs_theme_light .scroll_tab_left_button_disabled {
  color: #fff;
  background-color: #fdf0d4 !important;
  border: 1px solid #fdf0d4 !important; }

.scroll_tabs_theme_light .scroll_tab_right_button_disabled {
  color: #fff;
  background-color: #fdf0d4 !important;
  border: 1px solid #fdf0d4 !important; }

.scroll_tabs_theme_light .scroll_tab_left_button {
  background-color: #F6BB42;
  color: #fff;
  border: 1px solid #F6BB42; }

.scroll_tabs_theme_light .scroll_tab_right_button {
  background-color: #F6BB42;
  color: #fff;
  border: 1px solid #F6BB42; }

.scroll_tabs_theme_light div.scroll_tab_inner span.tab_selected, .scroll_tabs_theme_light div.scroll_tab_inner li.tab_selected {
  background-color: #F6BB42;
  color: #fff; }

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_left_finisher {
  width: 0; }

.scroll_tabs_theme_light div.scroll_tab_inner span.scroll_tab_right_finisher {
  width: 0; }

.scroll_tab_inner {
  text-align: center; }

@media screen and (max-width: 1400px) {
  #myTabs.nav-tabs > li > a {
    padding: 10px 10px 5px; }

  ul#myTabs {
    margin-bottom: 0 !important; }

  .sub-head-mrtp {
    margin-top: 10px; }

  .meal-box .view-item-box {
    padding-top: 0;
    padding-bottom: 0; } }
@media screen and (max-width: 1200px) {
  .tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption {
    width: 100%; }

  #show-loc {
    display: block; }

  #location, #shopping-cart {
    display: none; }

  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #9d9d9d;
    background-color: transparent; }

  #logo img {
    float: left; }

  #logo {
    padding: 0; }

  .navbar-toggle {
    position: relative;
    float: right;
    margin-right: 5px;
    padding: 9px 10px;
    margin-top: 17px;
    margin-bottom: 8px;
    background-color: transparent;
    background-image: none;
    border-radius: 4px; }

  .navbar-inverse .navbar-nav > li > a {
    line-height: 1em; }

  #show_cart {
    display: block;
    color: #F6BB42;
    float: right;
    padding-right: 0;
    margin-top: 3px; }

  #shopping-cart, #location {
    display: none; }

  #shopping-cart .badge, #show_cart .badge {
    top: -19px;
    right: 35px; }

  .shoppingcart .ti-shopping-cart {
    font-size: 25px; } }
@media screen and (max-width: 992px) {
  #list-search {
    display: none; }

  .sl-locaion {
    position: inherit;
    bottom: 35px; }

  .sl-input-fld {
    background: #ddd;
    padding-top: 15px; }

  ul#myTabs span.time {
    display: none; } }
@media screen and (max-width: 991px) {
  .mob-margin-left30 {
    margin-left: 30px; } }
@media screen and (max-width: 767px) {
  body.frame {
    padding: 25px; }

  .scroll_tabs_container {
    margin-bottom: 0px; }

  .add-extra-items {
    border-bottom: 1px solid #ddd; }

  .add-extra-items .count {
    text-align: left !important; }

  .step-border-bottom:before {
    display: none; }

  .profile-table span.pro-icon {
    padding-right: 5px; }

  .item-labels ul, .item-labels label.add-extra-div {
    width: 100%;
    display: inherit;
    margin-right: 0; }

  .add-extra-div:last-child {
    margin-bottom: 10px; }

  .item-labels label a span.ti-close {
    float: right;
    padding: 5px;
    background: #ff3333 !important;
    color: #fff;
    border-radius: 50%;
    top: -13px;
    position: relative;
    font-weight: bold;
    cursor: pointer; }

  .add-extra-div .cart-p-m {
    margin-top: 10px; }

  .cart-panel input[type="text"] {
    margin: 8px 0 2px 0; }

  .mob-padding-10 {
    padding: 10px !important; }

  .list-view-panel .mt-25 {
    margin-top: 75px;
    text-align: center; }

  .weeklymenu-date {
    display: block; }

  .mob-pd-0 {
    padding: 0; }

  .set-pref {
    float: left; }

  .ui-datepicker {
    width: 17em !important;
    padding: .2em .2em 0;
    display: none; }

  .ui-datepicker td span, .ui-datepicker td a {
    display: block;
    padding: .2em !important;
    text-align: right;
    text-decoration: none; }

  .owl-next {
    right: 0px;
    padding: 2px 2px;
    background: #fff;
    border-radius: 0;
    -webkit-box-shadow: -5px 0px 30px 0px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: -5px 0px 30px 0px rgba(0, 0, 0, 0.75);
    box-shadow: -5px 0px 30px 0px rgba(0, 0, 0, 0.75); }

  .owl-prev {
    left: 0px;
    padding: 2px 2px;
    background: #fff;
    border-radius: 0;
    -webkit-box-shadow: 5px 0px 30px 0px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: 5px 0px 30px 0px rgba(0, 0, 0, 0.75);
    box-shadow: 5px 0px 30px 0px rgba(0, 0, 0, 0.75); }

  .about-info-remove {
    position: relative;
    right: 0;
    padding-top: 0;
    padding-bottom: 0; }

  h4.homepage-title {
    margin-top: 20px;
    margin-bottom: 20px; }

  .input-group-btn {
    top: -8px; }

  .date-pick-div button.small-btn {
    margin: 0;
    display: block;
    width: 100%; }

  .sl-text {
    width: 100%; }

  .sl-bottom-img {
    width: 100%;
    left: 150px; }

  .sl-locaion {
    bottom: inherit; }

  .week-head {
    padding: 10px 0 0; }

  .xs-none {
    display: none; }

  .disc-amount {
    display: none; }

  .pull-left-xs {
    float: left !important; }

  .pull-right-xs {
    float: right !important; }

  .about-us-text {
    overflow: auto;
    max-height: none; }

  #outer-wrapper #inner-wrapper #table-wrapper #row-content h1 {
    font-size: 35px; }

  #outer-wrapper #inner-wrapper #table-wrapper #row-content h2 {
    font-size: 20px; }

  .tp-rightarrow.default {
    padding: 5px 5px 5px 10px; }

  .tp-leftarrow.default {
    padding: 5px 10px 5px 5px; }

  .mob-width {
    width: 100%; }

  /*asif.sl-input-fld {
    position: absolute;
    z-index: 99;
    left: 17.1%;
    top: 1.8%;
    text-align: center;
}*/
  .checked-meal {
    bottom: inherit; }

  #myModal8 .changed-meal .control__indicator {
    top: -9px;
    left: -15px; }

  .modal-content .modal-hg {
    max-height: 400px;
    overflow-y: scroll;
    overflow-x: hidden; }

  .modal-content .modal-hg-week {
    max-height: 330px;
    overflow-y: scroll;
    overflow-x: hidden; }

  #myCartPanel {
    display: none; }

  .navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
    border-color: #e8e8e8;
    max-height: 265px !important;
    overflow-y: scroll; }

  #logo {
    padding: 0; }

  .sl-form {
    display: block; }

  /*asif.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption{
    display:none;
  }*/
  button.order-button {
    width: 100%; }

  /*asif.sl-input-fld .form-group {
    margin-right: 5px;
    margin-bottom: 5px;
    width: 100%;
}*/
  .your-loc-fld {
    margin-right: 0;
    margin-bottom: 5px; }

  footer .foot-sec {
    padding: 10px;
    text-align: center; }

  .navbar-brand {
    margin-top: 0px; }

  .navbar-nav .open .dropdown-menu {
    text-align: center; }

  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: transparent; }

  .login-box {
    margin-top: 40px;
    margin-bottom: 30px; }

  .wallet-box {
    width: 100%;
    margin-left: 0; }

  .breadcrumb {
    padding: 0px 8px 8px 0px; }

  #icon-menu {
    background-color: #F6BB42; }

  .sub-head-mrtp {
    margin-top: 10px; }

  .cart-owl-box {
    padding: 0 0; }

  .bar1, .bar2, .bar3 {
    background-color: #ffffff; }

  .container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px; }

  .navbar-inverse .navbar-nav > li > a {
    color: #aab2bd;
    font-weight: bold; }

  #logo img {
    top: -3px;
    position: absolute;
    padding-top: 0px;
    left: 80px;
    height: 50px;
    max-width: 130px; }

  header {
    background-color: #ffffff;
    position: fixed;
    width: 100%;
    z-index: 999;
    top: 0; }

  body.pad-60 {
    padding-top: 60px; }

  body.pad-80 {
    padding-top: 80px; }

  body.pad-100 {
    padding-top: 100px; }

  section #location {
    margin-top: 25%; }

  #myModal form {
    width: 100%;
    left: 0;
    position: relative; }

  input[type="text"], input[type="password"] {
    padding: 12px 12px 12px 12px; }

  .promo-box .input-group input {
    padding: 10px 90px 9px 12px; }

  .login-from {
    padding: 20px;
    margin-top: 30px; }

  #location {
    position: relative;
    margin-left: 50px;
    margin-top: 0px;
    color: #656d78; }

  .modal-content {
    background-color: #f5f3f4;
    background-image: none;
    border-radius: 0; }

  .well-sm {
    margin-top: 25px; }

  .navbar-inverse .navbar-nav > li > a {
    line-height: 1em; }

  #your-order-panel {
    display: none; }

  /*.owl-next {
      position: absolute;
      top: 33%;
      right: 0px;
      height: 30px;
      margin-top: -15px;
      padding: 0px 5px;
      border-radius: 0;
      color: #fff;
      font-weight: 600;
      background-color: #e9573f;
  }
  .owl-prev {
      position: absolute;
      top: 33%;
      left: 0px;
      height: 30px;
      margin-top: -15px;
      padding: 0px 5px;
      border-radius: 0;
      color: #fff;
      font-weight: 600;
      background-color: #e9573f;
  }*/
  #show-loc {
    display: block; }

  #your-order {
    display: none; }

  .count {
    text-align: right; }

  .vw-item .count {
    text-align: left; }

  .nav > li > a.shoppingcart {
    padding: 5px 0px; }

  .navbar-inverse .navbar-toggle {
    position: relative;
    float: right;
    margin-right: 15px;
    padding: 5px 10px;
    margin-top: 12px;
    margin-bottom: 0px;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px; }

  .sidenav {
    z-index: 9999; }

  .center-block {
    width: 100px; }

  .ty-bgcol h4 {
    font-size: 25px; }

  .thnks-content {
    padding-bottom: 10px !important;
    font-size: 17px; }

  .ty-bgcol {
    padding: 30px 0 0; }

  .instant-checkout-btn, .instant-checkout-btn:hover, .instant-checkout-btn:active {
    padding: 10px 15px;
    background: #F6BB42 !important;
    box-shadow: none;
    outline: none;
    z-index: 9; }

  /*asif*/
  #main {
    margin: 0 !important; }

  /*asif end*/
  #icon-menu {
    position: absolute;
    left: 0;
    z-index: 99; }

  .menu-open #icon-menu {
    left: 250px; }

  .navbar-collapse {
    margin-top: 0px; }

  .modal-body .promo-box {
    padding: 8px 0;
    margin-left: 15px;
    margin-right: 15px;
    background-color: transparent; }

  .vw-item p {
    margin-top: 15px; }

  .cart-total {
    padding-top: 15px;
    padding-bottom: 5px;
    width: 100%;
    border-bottom: 1px solid #ddd; }

  #cart-panel1 input[type=text], input[type=password], #cart-panel2 input[type=text], input[type=password] {
    margin: 8px 0 2px 0; }

  #cart-panel1 .form-group .select, #cart-panel2 .form-group .select {
    margin-bottom: 10px;
    width: 92%; }

  .load-more {
    margin-bottom: 50px; }

  /*asif	*/
  .list-view-panel.ps-fx #ct-panel {
    position: fixed;
    top: 60px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 5px 0px; }

  /*asif end*/
  .sec-top {
    padding-top: 5px; }

  .list-view-panel .ml-name {
    padding-top: 10px; }

  .slideshow-container img {
    height: 260px; }

  .text b {
    font-size: 35px;
    top: 18%; }

  .home-next, .home-next:hover, .home-next:active, .home-next:focus {
    padding: 13px 0px 10px 8px; }

  .home-prev, .home-prev:hover, .home-prev:active, .home-prev:focus {
    padding: 13px 8px 10px 0px; }

  .text-center-xs {
    text-align: center; }

  .mobile-left img {
    width: 100%; }

  .productadd .add-meal-bn {
    margin-top: 10px;
    margin-bottom: 4px;
    text-align: right; }

  .btn_click {
    margin-top: 10px;
    margin-bottom: 4px;
    text-align: left; }

  .productadd .select {
    margin-bottom: 10px; }

  .choose-promocode input[type="text"], input[type="password"] {
    padding: 9.5px; }

  .map iframe {
    width: 100%;
    height: 300px; }

 /*.mob-side-nav{
    max-height: 350px;
	overflow-y: auto;
	}*/ }
@media screen and (min-width: 320px) and (max-width: 390px) {
  section #location {
    margin-top: 65%; }

  .login-box {
    width: 100%;
    margin-left: 0; }

  .user-profile {
    left: 40%; } }
@media screen and (max-height: 530px) {
  .sidenav a.list {
    padding: 15px 10px 15px 10px;
    font-size: 16px; }

  .fa-home, .fa-user, .fa-phone, .fa-lightbulb-o {
    padding: 0 0.5 rem 0 0;
    font-size: 17px; } }

/*# sourceMappingURL=combine-sunflower.css.map */

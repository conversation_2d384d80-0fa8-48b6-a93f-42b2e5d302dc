@charset "UTF-8";
/*! normalize.css v3.0.2 | MIT License | git.io/normalize */

.dn {
  display: none;
}

/* captcha on contact us page */
.captcha {
  position: absolute;
  z-index: 9;
  padding: 8px 10px 5px;
  width: 100%;
  font-size: 23px;
  text-decoration: line-through;
}
.cms-height {
  min-height: 350px;
}
/* order timing on home page */
.progress-bar div span {
  position: absolute;
  font-size: 22px;
  line-height: 35px;
  height: 180px;
  width: 180px;
  left: 10px;
  top: 10px;
  text-align: center;
  border-radius: 50%;
  background-color: white;
  padding-top: 57px;
}

.promo-checked {
  background: #8cc152;
  border: 2px solid #8cc152;
}

.promo-code-btn1 {
  position: absolute !important;
  top: 0px;
  background-color: #656d78;
  color: #ffffff;
  padding: 8.5px 12px;
  font-size: 14px;
  border-radius: 0;
  border: 2px solid #656d78;
  right: -60px;
}

.promo-btn {
  top: 0px;
  background-color: #656d78;
  color: #ffffff;
  padding: 8.5px 12px;
  font-size: 14px;
  border-radius: 0;
  border: 2px solid #656d78;
  right: -60px;
}

.capitalize {
  text-transform: capitalize;
}

.static-box {
  background-color: #f5f7fa;
  text-align: left;
  padding: 20px;
  border: 1px solid #ddd;
}

.what-client-say-box {
  background-color: #000;
  background-image: url(../sandwich/images/banner3.jpg) !important;
  z-index: 1;
  position: relative;
  border-radius: 0;
  background-attachment: fixed !important;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.footerPad {
  padding-bottom: 50px;
}

button.small-btn.itemAdded {
  background-color: #656d78;
}

.error-border {
  border: 1px solid red !important;
}

.add-amount-modal h4,
.add-amount-modal h5 {
  color: #656d78 !important;
}

.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
  border: 1px solid #c5c5c5 !important;
  background: #f6f6f6 !important;
  font-weight: normal !important;
  color: #454545 !important;
}

.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
  color: #f6f6f6 !important;
  background: #20a464 !important;
  border: none !important;
}

.sub-footer a.footer-btn {
  background-color: #656d78 !important;
  font-size: 16px !important;
}

@media screen and (max-width: 767px) {
  .add-extra-items {
    border-bottom: none !important;
    padding-bottom: 0;
  }
  .select2-container {
    width: 200px !important;
  }
}

.ui-datepicker-calendar tr {
  background: #f6f6f6 !important;
}

/*Anand*/
/*Web view changes*/

.mobile-view {
  display: none;
}

.tab-content {
  margin-bottom: 100px;
}

.meal-box .meal-img {
  height: auto !important;
}

h4.meal-type span.see-more {
  cursor: pointer;
}

.meal-box .addbtn {
  display: flex !important;
}

.meal-box .addbtn .pull-left {
  flex: 1 !important;
}

@media screen and (min-width: 767px) {
  .left-arrow {
    display: none !important;
  }
}
/*Anand*/
/*Mobile view changes*/

@media screen and (max-width: 767px) {
  .promo-code-btn1 {
    top: 8px !important;
    height: 46px !important;
  }

  .meal-box .addbtn {
    display: flex !important;
    margin-top: 35px;
  }

  .meal-box .addbtn .pull-left {
    flex: 1 !important;
  }

  .footer-3 {
    display: none !important;
  }

  .toast-bottom-center {
    bottom: 60px !important;
  }

  .foot-price,
  .foot-price:hover,
  .foot-price:focus {
    display: none !important;
  }

  #icon-menu {
    display: none !important;
  }

  #logo img {
    left: 30px !important;
  }

  .modal {
    padding-top: 0 !important;
  }

  .modal-dialog {
    display: flex;
    height: 100%;
    margin: auto !important;
    align-items: center;
    justify-content: center;
    width: 90% !important;
  }

  .modal-content .modal-hg {
    width: 100%;
  }

  .breadcrumb {
    display: none !important;
  }

  .back .fooddialer {
    display: none !important;
  }

  .sub-footer {
    z-index: 399 !important;
  }

  .myaccount-main-panel .row .col-sm-12,
  .booking-history .row .col-sm-12 {
    overflow: scroll;
  }

  .no-meal {
    margin: 0 !important;
  }

  .meal-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    background-color: #f5f7fa;
  }

  .meal-box > .clearfix {
    width: 75%;
  }

  .mobile-view .pull-right {
    margin-top: -10px;
    background: white;
    width: 85%;
    box-shadow: 0 1px 21px 1px rgb(0 0 0 / 40%);
    border-radius: 5px;
    z-index: 299 !important;
    align-items: center;
    justify-content: center;
  }

  .checkout {
    width: auto !important;
  }

  .add-btn {
    padding: 3px;
    border-radius: 5px;
    color: #fff !important;
  }

  .checkout .add-btn {
    padding: 6px 10px !important;
  }

  .meal-img {
    width: 150px !important;
    height: auto !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    padding: 5px 0;
  }

  .addimg {
    border-radius: 5px;
    border: 1px solid #ddd;
    background: #fff !important;
  }

  .mobile-view {
    display: flex;
  }

  .web-view {
    display: none;
  }

  .chooseMeal {
    padding: 8px;
    border-radius: 5px;
    background: rgb(233, 87, 63);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chooseMeal > h3,
  .mobile-view .count > h3 {
    display: none;
  }

  .chooseMeal > p {
    margin-bottom: 0 !important;
    padding: 3px;
  }

  .overlay {
    top: 6%;
    width: 100px;
    left: unset;
    transform: unset;
  }

  .img-text b {
    display: none;
  }

  .img-text span {
    font-size: 15px;
  }

  .mobile-view .count {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0 !important;
    padding: 3px;
  }

  .mobile-view .nonveg-icon,
  .mobile-view .veg-icon {
    position: absolute;
    left: 20px;
    top: 15px;
  }

  .meal-bg {
    border-radius: 5px;
    background-color: #f5f7fa;
  }

  hr.view-item-div,
  .meal-box .view-item-box {
    display: none;
  }

  .tab-content {
    margin-bottom: 45px;
  }

  .left-arrow {
    position: absolute;
    font-size: 20px;
    padding: 17px 30px 12px 25px;
    color: #e9573f;
    border: none;
    background: #fff;
    z-index: 99;
  }
}

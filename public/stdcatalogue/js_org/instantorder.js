var sitehost = window.location.protocol+"//"+window.location.host;

var instantApp = angular.module('quickserveApp', []);

instantApp.directive('loading', ['$http' ,function ($http){

    return {
        restrict: 'A',
        link: function (scope, elm, attrs){
            scope.isLoading = function () {

                return $http.pendingRequests.length > 0;
            };
            scope.$watch(scope.isLoading, function (v){
                if(v){
                    elm.show();
                }else{
                    elm.hide();
                }
            });
        }
    };
}]);

instantApp.config(function($httpProvider){
    var interceptor = [
    '$q',
    function ($q) {
        return function (promise) {
            return promise.then(function (response) {
                return response;
            }, function (response) {
                if (response.status === 0) {
                        toastr.error("You seem to be offilne.");
                }
                return $q.reject(response);
            });
        };
    }];
    $httpProvider.responseInterceptors.push(interceptor);

});

instantApp.controller('instantorderCtrl',function($scope,$rootScope,$location,$window,$parse,$filter,$http) {

    $scope.cart = {};
    $scope.loggedInUser = loggedInUser;
    $scope.mode = "add";
    $scope.orderDate = orderDate;
    $scope.meal_quantity = 1;
    $scope.arrDefaultMenu = arrDefaultMenu;
    $scope.products = products;
    $scope.foodtypes = foodtypes;
    $scope.locations = locations;
    $scope.selLocation = selLocation;
    $scope.selectedFoodType = foodtypes[0];

    $scope.menu = $scope.arrDefaultMenu.menu;
    $scope.kitchen = $scope.arrDefaultMenu.fk_kitchen_code;
    
    $scope.promocodeStatus = "";
    $scope.promocodeMsg = "";

    $scope.currencySymbol = currencySymbol;

    $scope.initCart = function(){

        $scope.cart.items = items;
        $scope.cart.subtotal = subtotal;	
    }

    $scope.initCart();	

    $scope.checkItem = function(product_id){
        return $scope.cart['items'].hasOwnProperty($scope.menu+'_'+product_id);
    }

    $scope.checkItemEmpty = function(products){
        var data = {};
        data.products = $scope.products;
       return Object.keys(products).length;
    }
    
    $scope.addItem = function(prod){

        console.log("added in cart");
        //console.log($scope.cart);
        var texits = false;

        //console.log(prod);

        $.each($scope.cart.items,function(k,v){

            if(v.pk_product_code==prod.pk_product_code){
                texits = true;	
                return false;
            }
        });

        if(!texits){
            var key = $scope.menu+"_"+prod.pk_product_code;
            prod.order_qty = 1;
            console.log($scope.cart.items);
            // add item to session cart ...
            $scope.addToCart(prod);
            console.log($scope.cart.items);
            toastr.success(prod.name+'Item added to cart');
        }
    }
    $scope.removeItem = function(prod,key){

        $.each($scope.cart.items,function(k,v){
            
            if(v.pk_product_code==prod.pk_product_code){
                delete $scope.cart.items[k];
                $scope.updateSessCart(prod,k,'remove');
                if(Object.keys($scope.cart.items).length == 0) {
                    closeCart();
                    $window.location.reload()
                }                
                //return false;
            }
        });			
    }

    $scope.addToCart = function(prod){

        var data = {product_id:prod.pk_product_code,menu:$scope.menu,purpose:'restaurant',kitchen:$scope.kitchen,img:prod.image_path,location_active: $scope.location_active,'from_other_location':$scope.selLocation};

        $.ajax({
            url :"/menu/addtocart",
            type:"POST",
            data:data,
            dataType:'json',
            beforeSend: function(){
                
            },
            success:function(data){
                if(data.status=='success'){

                    $scope.updateInstantCart(data.data);

                    var extraItem =0, mainItems = 0, cust_items = 0 ,key;

                    for (key in data.data.items) {
                        if (data.data.items.hasOwnProperty(key)) mainItems++;
                    }
                    for (key in data.data.extra) {
                        if (data.data.extra.hasOwnProperty(key)) extraItem++;
                    }
                    $.each(data.data.custom_items,function(index,value){
                        if(data.data.custom_items.hasOwnProperty(index) && value.product_type=="Main")cust_items++;
                    });
                    //fly(element);
                    totalItems = mainItems;
                    items = data.data.custom_items;                        
                        $('.my-cart').show().find('.badge').html(totalItems);                                                                 
                }
                if(data.status=='error'){
                    toastr.error(data.msg);
                }
            },
            error:function(t1,t2){
                toastr.error(t1.responseText);
            }
        });
    }

    $scope.updateSessCart = function(prod,key,purpose,quantity){

        var data = {key:key,menu:$scope.menu,purpose:purpose,kitchen:$scope.kitchen,type:'custom_items',quantity:quantity};

        $.ajax({
            url : sitehost+"/cart/ajx-restaurant",
            type:"POST",
            data:data,
            dataType:'json',
            beforeSend: function(){
            },
            success:function(data){

                if(data.status){
                    $scope.updateInstantCart(data.data);
                    var extraItem =0, mainItems = 0, cust_items = 0 ,key;
                    for (key in data.data.items) {
                        if (data.data.items.hasOwnProperty(key)) mainItems++;
                    }
                    for (key in data.data.extra) {
                        if (data.data.extra.hasOwnProperty(key)) extraItem++;
                    }
                    $.each(data.data.custom_items,function(index,value){
                        if(data.data.custom_items.hasOwnProperty(index) && value.product_type=="Main")cust_items++;
                    });
                    //fly(element);
                    totalItems = mainItems;                    
                        $('.my-cart').show().find('.badge').html(totalItems);                                       
                }					
                if(data.status=='error'){
                    toastr.error(data.msg);
                }
            },
            error:function(t1,t2){
                toastr.error(t1.responseText);
            },
            complete:function(){                             
            }
        });
    }

    $scope.updateInstantCart = function(cart){

        var k = $scope.menu+'#0';

        $scope.cart.items = cart.custom_items;

//        console.log(Object.keys(cart.custom_items).length);
//        if(Object.keys(cart.custom_items).length == 0){
//            $('.my-cart').addClass('hide');
//        }
        var keys = Object.keys($scope.cart.items).length;

        if(keys > 0){
            $scope.cart.subtotal = cart.items[k].linePrice;	
        }else{
            $scope.cart.subtotal = 0;
        }
        $scope.$apply();
    }

    $scope.updateItemQty = function(key,item,mode){

        console.log(mode);
        console.log(item);
        var type = "custom_items";
        var quantity = item.quantity;	

        if(mode=='inc'){
            quantity = parseInt(quantity) + parseInt(1);
        }

        if(mode=='dec'){
            quantity = parseInt(quantity) - parseInt(1);
        }

        if(quantity < 1){
            toastr.error('Quantity should not less than one, update item quantity')				
            return false;
        }
        $scope.updateSessCart(item,key,'qnty_dec_inc',quantity);

    }

    $scope.isCartEmpty = function(){

        var keys = Object.keys($scope.cart.items);
        if(keys.length > 0){
                return false;
        }else{
                return true;
        }
    }        
    
    $scope.totalCartItems = function(){

        var keys = Object.keys($scope.cart.items);
        return keys.length;
    }

    $scope.gotoCheckout = function(){

        location.href = "/cart";
/*
        if($scope.loggedInUser){
                location.href = "/cart";	
        }else{
                location.href = "/customers/index";	
        }
*/        
    }

    if(!$scope.isCartEmpty()){
        openCart();
    }
    
    $scope.selectFoodType = function(foodtype){
        
        $scope.selectedFoodType = foodtype;
        var data = {};
        data.kitchen = $scope.kitchen;
        data.food_type = $scope.selectedFoodType;
        data.menu = $scope.menu;
        data.strategy = 'json';
        
        var responsePromise = $http({
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            url: "/menu/ajx-product", 
            method: "POST",
            data: $.param(data)
        });

        responsePromise.success(function(data, status, headers, config) {
            $scope.products = data.data             
        });
        
        responsePromise.error(function(data, status, headers, config) {
            console.log(data);
        });
    }
});

$(document).ready(function(){
    
    var $body   = $(document.body);
    $body.scrollspy({
        target: '#leftCol',   
    });
    
});
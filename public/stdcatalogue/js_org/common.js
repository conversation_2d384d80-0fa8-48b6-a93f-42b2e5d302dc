function showModel() {
    
	if ($(".search-modal-body").length == 1 ){
		if ( location.pathname.split("/")[1] == "menu" || location.pathname.split("/")[1] == "" ){
			$('.home-modal').modal('show');
		}
	}   
	
}

function setNavigation() {

    var path = window.location.pathname;
    path = path.replace(/\/$/, "");
    path = decodeURIComponent(path);

    $(".accountLinks a").each(function () {
        if (path == '' || path == '/menu') {
           $(".menu").addClass('active');
        }
        else  if (path == '' || path == '/planmeal') {
           $(".planmeal").addClass('active');
        }
        else if (path === '/customers/my-account') {
          $(".my-account").addClass('active');
        }
        else if (path === '/customers/bookinghistory') {
           $(".boking_hishtory").addClass('active');
        }
        else if (path === '/menu/extra-product') {
           $(".my-cart").addClass('active');
        }
        else if (path === '/cart') {
           $(".my-cart").addClass('active');
        }else if (path === '/menu/instant-order') {
           $(".instantorder").addClass('active');
        }   				     				    	
    });

    $(".navbar a").each(function () {
        if (path == '' || path == '/menu') {
           $(".menu").addClass('active');
        }
        else if (path === '/customers/my-account') {
          $(".my-account").addClass('active');
        }
        else if (path === '/customers/bookinghistory') {
           $(".boking_hishtory").addClass('active');
        }
        else if (path === '/menu/extra-product') {
           $(".cart_mob").addClass('active');
        }
        else if (path === '/cart') {
           $(".cart_mob").addClass('active');
        }else if (path === '/menu/instant-order') {
           $(".instantorder").addClass('active');
        }   				     				    	
    });			    
}			

function unset_user() {

    $.ajax({
        type: "POST",
        url: "/customers/unsetsession",
      //  data: {custdetails:custdetails},
        dataType: 'json',
        success: function(data) {
            if(data.status =="success"){
                window.location.href = "/";
            }

        },
    });
}	

var cities = {};
var locations = {};
var selectedCity = "";

function getAllCities(){

    $.ajax({
        url:"/menu/city",
        type : "POST",
        dataType:'json',
        async : false,
        success:function(data){
            cities = data.data;
            if(cities.length==1){
                selectedCity = cities[0].pk_city_id;
            }
        },
        error:function(){
                
        },
        complete:function(){
            
        }
    });
}

function getAllLocations(city){

    $.ajax({
        url:"/menu/location",
        type : "POST",
        async : false,
        data : {city:city},
        success:function(data){

            locations = data.data;
        },
        error:function(){

        },
        complete:function(){
 
        }
    });
}	

function renderCityOptions(id,selectedCity){
    var strHtml = '<option value="">Select City</option>';
    $.each( cities, function( key, value ) {
        var sel = "";       
        if(selectedCity !=undefined && selectedCity!=""){
            if(selectedCity == value.pk_city_id){
                sel = "selected";
            }
        }
        strHtml+= '<option value="'+value.pk_city_id+'#'+value.city+'" '+sel+'>'+value.city.substring(0,1).toUpperCase() + value.city.substring(1,value.city.length);+'</option>';
    });
    $("#"+id).html(strHtml);
}	

function renderLocationOptions(id,menu , label,selected){
    //class="bs-title-option"
    if(locations!=""){
        var strHtml = '<option value="" >Select Your Location</option>';
        $.each( locations, function( key, value ) {
            var sel = "";
            if(selected==value.pk_location_code){
                sel = "selected";
            }
            strHtml += '<option data-kitchen='+value.fk_kitchen_code+' value="'+value.pk_location_code+'#'+value.location+'#'+value.pin+'"'+sel+'>'+value.location.substring(0,1).toUpperCase() + value.location.substring(1,value.location.length)+'</option>';
        });
    }else{
        var strHtml = '<option value="" >No Location Found</option>';
    }
    $("#"+id).html(strHtml);
}

function setDefaultLocation(redirectUrl){

    var location = $("#default_location").val();
    var city = $("#default_city").val();
    var kitchen = $("#default_location option:selected").data("kitchen");

    $.ajax({
        url:"/menu/set-default-location",
        type : "POST",
        async : false,
        dataType : 'json',
        data : {location:location,city:city,kitchen:kitchen},
        success:function(data){
            if(data.status=='success'){
                $("#default_location_modal").modal('hide');
                if(redirectUrl==undefined){
                    window.location.reload();
                }else{
                    window.location.href = redirectUrl;
                }
            }
        },
        error:function(){
        }
    });                
}

toastr.options = {
    "closeButton": false,
    "debug": false,
    "newestOnTop": false,
    "progressBar": false,
    "positionClass": "toast-top-right",
    "preventDuplicates": false,
    "onclick": null,
    "timeOut": "3000",
    "showEasing": "swing",
    "hideEasing": "linear",
    "showMethod": "fadeIn",
    "hideMethod": "fadeOut"
}

 $('#default_city').on('change',function(){
    var strCity = $(this).val();
    if(strCity !=""){
        var arrCity = $(this).val().split("#");
        getAllLocations(arrCity[0]);
        renderLocationOptions('default_location','');
        $('.selectpicker').selectpicker('refresh');
    }
});

$("#btn_default_location").on('click',function(){
    if($.trim($("#default_city").val()) == ""){
        toastr.error("Please select city,Select city");
        return false;
    }
    if($.trim($("#default_location").val()) == ""){
        toastr.error("Please select location,Select location");
        return false;
    }
    setDefaultLocation();
}); 
    	

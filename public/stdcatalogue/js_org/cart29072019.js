var sitehost = window.location.protocol+"//"+window.location.host;
var holidays = [];
var weekOff = {};
var cart = {};
var menu_absent_flag = "";
var disabledDates = [];

function updateAmount(cart,refkey,domEle){

    //console.log(cart);

    var total_amount = parseFloat(cart.total_amount)
    var net_amount = parseFloat(cart.net_amount);
    var applied_discount = parseFloat(cart.applied_discount);

    var discountedAmount = net_amount - applied_discount;

    $(".lblnetamount").html(formatter.format(discountedAmount));
    $(".lbltotalamount").html(formatter.format(net_amount));

    if(applied_discount > 0){
        $(".disc-amount").show();
        $(".disc-amount span").html(formatter.format(applied_discount));
    }else{
        $(".disc-amount").hide();
    }

    if(refkey != undefined){
        domEle.find('.meal_price').html(formatter.format(cart.items[refkey].net_amount));

        var extraAmount = parseFloat(cart.items[refkey].extra_amount);        
        domEle.find('.extra_item_price').html(formatter.format(extraAmount));

        var net_original_amount = cart.items[refkey].net_original_amount;

        if(typeof net_original_amount !== 'undefined'){
            net_original_amount = parseFloat(net_original_amount);

            if(net_original_amount > net_amount) 
                domEle.find('.meal_original_price').removeClass('hide').addClass('show').html(formatter.format(net_original_amount));
            else  
                domEle.find('.meal_original_price').removeClass('show').addClass('hide');
        }
    } 
}

function updateCartContainer(purpose,product_code,menu,quantity,promo, payment_mode, aggregator, value){

    if(quantity=="" || quantity=="undefined"){
        quantity=false;
    }
    if(promo=="" || promo=="undefined"){
        promo = false;
    }
    var url = sitehost+"/cart/ajax-update-cart";
    $.ajax({
        url: url,
        type:"POST",
        async : false,
        data: {purpose:purpose,product_code:product_code,menu:menu,quantity:quantity,promo:promo,payment_mode: payment_mode, aggregator: aggregator, value: value},
        success:function(result){            
            if(purpose == 'remove'){
                if(result.total_cart_items == 0){                                  
                }
            }

            if(purpose == 'plus-quantity' || purpose == 'minus-quantity'){
                var mealKey = menu+'#'+product_code;                                    
                var ele = $('#price'+menu+'_'+product_code).closest('.cart_items');
            }

            if(purpose == 'aggregator'){
                if(result.hasOwnProperty('item_keys')){
                    $.each(result.item_keys, function(key,value){
                        $('#iq'+key+'ss').html( + value.unit_price);
                        $('#price'+key).val(value.base_price);
                    })
                }
                if(result.item_keys.charges_type== 'exclusive'){
                    $('#exclusive-msg').removeClass('hide').addClass('show');
                    $('#exclusive-commission').html(result.item_keys.charges_type);
                }else{
                    $('#exclusive-msg').removeClass('show').addClass('hide');
                }
            }
            updateAmount(result.cart,mealKey,ele);

        }
    });
}

var getStartDate = function(order_menu,weekOff,kitchen){

    var startdate = "";

    $.ajax({
        type:"POST",
        url: sitehost+'/cart/ajax-get-start-date',
        data:{menu:order_menu, weekoff : weekOff,kitchen: kitchen},
        async:false,
        success:function(data){
            if(data.status){
                startdate = data.sdate;
            }
        }
    });

    return startdate;

}

var loadCartItems = function(cart,holidays,weekOff,loadTimeSlot){
    //showing timeslots on load
    //if(cart['items'][Object.keys(cart.items)]['single_order_date'] != "" && loadTimeSlot == 1) {
    if(loadTimeSlot == 1) {   
        $.each(cart.items, function (key,meal_data) {  
            var menu = meal_data['menu'];
            var pk_product_code = meal_data['pk_product_code'];
            var kitchen = meal_data['kitchen'];
            var plan_days = meal_data['planDays'];
            if(plan_days != null){
                plan_days = (plan_days == 'mf' || plan_days == 'ms' || plan_days == 'msu') ? 'all' : plan_days;
            }else{
                plan_days = 'all';
            }  
            //var single_order_date = meal_data['single_order_date'];
	    var single_order_date = '';

            if(meal_data.hasOwnProperty('order_dates')) {
               single_order_date = meal_data['order_dates'][0];
            }
            
            var selected_slot = meal_data['orderdeliverytime'];
            
            $.ajax({
                url: sitehost+'/cart/ajax-timeslots',
                type:"POST",
                data:{menu:menu,kitchen:kitchen,plan_days:plan_days,single_order_date:single_order_date},
                success:function(result) {
                    //$('#delivery_time_'++menu+'_'+pk_product_code+' option[value!="0"]').remove().end();
                    //$('#delivery_time_'++menu+'_'+pk_product_code).closest('.time-slot').show();
                    var str = '';
                    if(result.status == 'success') {
                        if(Object.keys(result.data).length == 0) {
                            $('#delivery_time_'+menu+'_'+pk_product_code+' option[value="0"]').empty();
                            $('#delivery_time_'+menu+'_'+pk_product_code).find("option:selected").text('No Timeslot Available');
                        }
                        else {
                            $('#delivery_time_'+menu+'_'+pk_product_code).find("option:selected").text('Choose Time Slot');
                            $.each(result.data, function(k, v) {
                                var current_slot = v.starttime+'-'+v.endtime;

                                var selected = (current_slot == selected_slot) ? 'selected' : '';
                                str += '<option value='+current_slot+' '+selected+'>'+current_slot+'</option>';
                                if (selected_slot!=null) { 
                                    $('.orderbtn').removeClass('hidden');
                                }
                            });
                        }
                        $('#delivery_time_'+menu+'_'+pk_product_code).append(str);    
                    }
                    
                }
            });        
        });
    }

    $.each(holidays, function(i, holiday){
        disabledDates.push(new Date(holiday));
    });

    if(Object.keys(cart.items).length > 0){

        var key_i = 0;
        var weekoff1Obj = {};

        $.each(cart.items,function(s_key,s_value){
            var key_value = s_key;
            var key_modified_val = key_value.replace("#", "_");

            
            $('#iq'+s_value.menu+'_'+s_value.pk_product_code+'ss').text(formatter.format(s_value.net_amount));
            //hide timeslot
            //$('#delivery_time_'+key_modified_val).closest('.time-slot').hide();
            $('#id_'+key_modified_val).closest('.cart_items').find('.set_preference').hide();
            $('#id_'+key_modified_val).closest('.cart_items').find('.add-extra-btn').hide();

            // new code- added by sankalp. overriding global setting with kitchen local weekoffs
            var weekOff1 = '';

            if(s_value.hasOwnProperty('weekOff1')){
                weekOff1 = s_value.weekOff1;
            }else{
                weekOff1 = weekOff;
            }
            
            var week_off1 = weekOff1.split(",");

            var weekoffs1= new Array();

            $.each(week_off1,function(key1,value1){
                weekoffs1.push(parseInt(value1));
            });

            var weekoffkey = s_key;

            weekoff1Obj[weekoffkey] = weekoffs1;
            

            if(s_value.hasOwnProperty('plantype') && s_value.plantype !=null && s_value.plantype !=''){

                var ptype = s_value.plantype.split('%');
                if(ptype[1]=='periodbased'){

                    var date = new Date();
                    var plan_days = (s_value.hasOwnProperty('planDays')) ? s_value.planDays : "";
                    var order_menu = $("."+key_modified_val+'_menu').val();
                    var startdate ;
                    
                    $("#Datepickersingle_"+key_modified_val).closest(".cart_items").find(".calDiv").show();
                    $("#Datepickersingle_"+key_modified_val).datepicker("destroy");

                    var weekOff1 = $('#'+key_modified_val).data('weekoff1');
                    startdate = getStartDate(order_menu,weekOff1,s_value.kitchen);
                    
                    if(plan_days == 'mf'){
                        var keyUsed = key_modified_val;
                        $("#wk_day"+keyUsed).css("display","none");
                        $("#Datepickersingle_"+keyUsed).datepicker({
                            //minDate : new Date(startdate),
                            minDate: new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
                            dateFormat : "dd-mm-yy",
                            orientation : "top left",
                            beforeShowDay: function(date){
                                show = true;
                                if(date.getDay() == 0 || date.getDay() == 6){ show = false; }//No Weekends
                                for (var i = 0; i < holidays.length; i++) {
                                    if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
                                }
                                var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                                return display;
                            },
                            onSelect: function(dateText, inst) {

                                var date_selected = $(this).val();
                                var time_selected = $('#time').val();
                                var split_val = $(this).attr('id').split('_');
                                var s_id = split_val[1]+"_"+split_val[2];

                                if(loadTimeSlot == 1) {
                                
                                    s_value.planDays = (s_value.planDays == 'mf' || s_value.planDays == 'ms' || s_value.planDays == 'msu') ? 'all' : s_value.planDays;

                                    $.ajax({
                                        url: sitehost+'/cart/ajax-timeslots',
                                        type:"POST",
                                        data:{menu:s_value.menu,kitchen:s_value.kitchen,plan_days:s_value.planDays,single_order_date:date_selected},
                                        success:function(result) {
                                            $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                                            $('#delivery_time_'+key_modified_val).closest('.time-slot').show();
                                            var str = '';
                                            if(result.status == 'success') {
                                                if(Object.keys(result.data).length == 0) {
                                                    //$('#delivery_time_'+key_modified_val+' option[value="0"]').empty();
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('No Timeslot Available');
                                                }
                                                else {
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('Choose Time Slot');
                                                    $.each(result.data, function(k, v) {
                                                        str += '<option value='+v.starttime+'-'+v.endtime+'>'+v.starttime+' to '+v.endtime+'</option>';
                                                    });
                                                }
                                                $('#delivery_time_'+key_modified_val).append(str);    
                                            }
                                            
                                        }
                                    });                                
                                }                                

                                $.ajax({
                                    url: sitehost+'/cart/ajax-update-cartdate',
                                    type:"POST",
                                    async:false,
                                    data:{key_update:s_id,date_select:date_selected,purpose:"single_order_date"},
                                    success:function(result){
                                        var showdate= new Date(result.enddate.toString());
                                        $('.enddate'+s_id).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );
                                        $('#id_'+s_id).closest('.cart_items').find('.add-extra-div').hide();
                                    }

                                });

                            }

                        });

                    }else if(plan_days == 'ms'){

                        var keyUsed= key_modified_val;
                        $("#wk_day"+keyUsed).css("display","none");

                        $("#Datepickersingle_"+key_modified_val).datepicker({
                            //minDate :new Date(startdate),
                            minDate: new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
                            dateFormat : "dd-mm-yy",
                            orientation : "top left",
                            beforeShowDay: function(date){
                                show = true;
                                if(date.getDay() == 0){ show = false; }//No Weekends
                                for (var i = 0; i < holidays.length; i++) {
                                    if (new Date(holidays[i]).toString() == date.toString()) {
                                        show = false;
                                    }//No Holidays
                                }
                                var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                                return display;
                            },
                            onSelect: function(dateText, inst) {

                                var date_selected = $(this).val();
                                var time_selected = $('#time').val();
                                var split_val = $(this).attr('id').split('_');
                                var s_id = split_val[1]+"_"+split_val[2];

                                if(loadTimeSlot == 1) {
                                
                                    s_value.planDays = (s_value.planDays == 'mf' || s_value.planDays == 'ms' || s_value.planDays == 'msu') ? 'all' : s_value.planDays;

                                    $.ajax({
                                        url: sitehost+'/cart/ajax-timeslots',
                                        type:"POST",
                                        data:{menu:s_value.menu,kitchen:s_value.kitchen,plan_days:s_value.planDays,single_order_date:date_selected},
                                        success:function(result) {
                                            $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                                            $('#delivery_time_'+key_modified_val).closest('.time-slot').show();
                                            var str = '';
                                            if(result.status == 'success') {
                                                if(Object.keys(result.data).length == 0) {
                                                    //$('#delivery_time_'+key_modified_val+' option[value="0"]').empty();
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('No Timeslot Available');
                                                }
                                                else {
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('Choose Time Slot');
                                                    $.each(result.data, function(k, v) {
                                                        str += '<option value='+v.starttime+'-'+v.endtime+'>'+v.starttime+' to '+v.endtime+'</option>';
                                                    });
                                                }
                                                $('#delivery_time_'+key_modified_val).append(str);    
                                            }
                                            
                                        }
                                    });                                
                                }                                

                                $.ajax({
                                    url: sitehost+'/cart/ajax-update-cartdate',
                                    type:"POST",
                                    async:false,
                                    data:{key_update:s_id,date_select:date_selected,purpose:"single_order_date"},
                                    success:function(result){
                                        var showdate= new Date(result.enddate.toString());
                                        $('.enddate'+s_id).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );
                                    }

                                });

                            }
                        });

                    }else if(plan_days == 'msu'){

                        var keyUsed= key_modified_val;
                        $("#wk_day"+keyUsed).css("display","none");
                        $("#Datepickersingle_"+key_modified_val).datepicker({
                            //minDate : new Date(startdate),
                            minDate: new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
                            dateFormat : "dd-mm-yy",
                            orientation : "top left",
                            beforeShowDay: function(date){
                                show = true;
                                for (var i = 0; i < holidays.length; i++) {
                                    if (new Date(holidays[i]).toString() == date.toString()) { show = false;}//No Holidays
                                }
                                var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                                return display;
                            },
                            onSelect: function(dateText, inst) {

                                var date_selected = $(this).val();
                                var time_selected = $('#time').val();
                                var split_val = $(this).attr('id').split('_');
                                var s_id = split_val[1]+"_"+split_val[2];

                                if(loadTimeSlot == 1) {
                                
                                    s_value.planDays = (s_value.planDays == 'mf' || s_value.planDays == 'ms' || s_value.planDays == 'msu') ? 'all' : s_value.planDays;

                                    $.ajax({
                                        url: sitehost+'/cart/ajax-timeslots',
                                        type:"POST",
                                        data:{menu:s_value.menu,kitchen:s_value.kitchen,plan_days:s_value.planDays,single_order_date:date_selected},
                                        success:function(result) {
                                            $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                                            $('#delivery_time_'+key_modified_val).closest('.time-slot').show();
                                            var str = '';
                                            if(result.status == 'success') {
                                                if(Object.keys(result.data).length == 0) {
                                                    //$('#delivery_time_'+key_modified_val+' option[value="0"]').empty();
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('No Timeslot Available');
                                                }
                                                else {
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('Choose Time Slot');
                                                    $.each(result.data, function(k, v) {
                                                        str += '<option value='+v.starttime+'-'+v.endtime+'>'+v.starttime+' to '+v.endtime+'</option>';
                                                    });
                                                }
                                                $('#delivery_time_'+key_modified_val).append(str);    
                                            }
                                            
                                        }
                                    });                                
                                }

                                $.ajax({
                                    url: sitehost+'/cart/ajax-update-cartdate',
                                    type:"POST",
                                    async:false,
                                    data:{key_update:s_id,date_select:date_selected,purpose:"single_order_date"},
                                    success:function(result)
                                    {
                                        var showdate= new Date(result.enddate.toString());
                                        $('.enddate'+s_id).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );
                                    }

                                });

                            }
                        });

                    }else if(plan_days == 'yourChoice'){

                        var tempwkdays=$(".wkday"+key_modified_val).val();

                        if(tempwkdays!=null && tempwkdays!="undefined" && tempwkdays!=""){

                            var mealdays = $(".wkday"+key_modified_val).val().toString();
                            var mealdays_array= mealdays.split(",");

                            $("#Datepickersingle_"+key_modified_val).datepicker({
                                //minDate : new Date(startdate),
                                minDate: new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
                                dateFormat : "dd-mm-yy",
                                orientation : "top left",
                                beforeShowDay: function(date){

                                    var show = true;
                                    var pick=date.getDay().toString();

                                    for(i=0;i< mealdays_array.length;i++){

                                        if( pick == mealdays_array[i].toString()){
                                            show = true;
                                            break;
                                        }else{
                                            show = false;
                                        }
                                    }
                                    for (var i = 0; i < holidays.length; i++) {
                                        if (new Date(holidays[i]).toString() == date.toString()) { show = false;}//No Holidays
                                    }

                                    var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                                    return display;

                                },
                                onSelect: function(dateText, inst) {

                                    var date_selected = $(this).val();
                                    var time_selected = $('#time').val();
                                    var split_val=$(this).attr('id').split('_');
                                    var s_id=split_val[1]+"_"+split_val[2];

                                    $.ajax({
                                        url: sitehost+'/cart/ajax-update-cartdate',
                                        type:"POST",
                                        async:false,
                                        data:{key_update:s_id,date_select:date_selected,purpose:"single_order_date"},
                                        success:function(result)
                                        {
                                            var showdate= new Date(result.enddate.toString());
                                            $('.enddate'+s_id).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );

                                        }

                                    });

                                }
                            });

                        }else{
                            
                            $("#wkdayid"+key_modified_val).css("display","block");
                            $("#Datepickersingle_"+key_modified_val).css("display","none");
                        }
                    }

                    if(s_value.hasOwnProperty('single_order_date') && s_value.single_order_date !=""){
                        var str_date = s_value.single_order_date;
                        var plan_days_array = str_date.toString();
                        var monthDates = [];
                        var val_arr = plan_days_array.split('-');
                        var single_date  = new Date();
                        single_date.setMonth(val_arr[1]-1);
                        single_date.setDate(val_arr[0]);
                        single_date.setYear(val_arr[2]);
                        $("#Datepickersingle_"+key_modified_val ).datepicker('setDate', single_date);
                        $('#id_'+key_modified_val).closest('.cart_items').find('.set_preference').show();
                        $('#id_'+key_modified_val).closest('.cart_items').find('.add-extra-btn').show();
                    }

                }else if( ptype[1]=="datebased" ){

                    var date = new Date();
                    var order_menu = $("."+key_modified_val+"_menu").val();

                    var type = ptype[1];
                    var days_no =  ptype[0];

                    var new_data = "plan_period=1&kitchen="+s_value.kitchen+"&type="+type+"&"+"days="+days_no+"&"+"menu="+order_menu+"&"+"weekoff="+weekOff1;
                    
                    $("#dtc"+key_modified_val).show();
                    $("#wk_day"+key_modified_val).css("display","none");

                    var k2val = key_modified_val;

                    var w2 = $('#with-altField'+k2val).data('weekoffkey').replace("_", "#");
                    if(s_value.hasOwnProperty('order_dates') && Object.keys(s_value.order_dates).length){
        
                        $('#id_'+key_modified_val).closest('.cart_items').find('.set_preference').show();
                        $('#id_'+key_modified_val).closest('.cart_items').find('.add-extra-btn').show();
                        
                        var str_values = s_value.order_dates.toString();
                        var max_date = (s_value.hasOwnProperty('max_date')) ? s_value.max_date : null;
                        var date    = new Date(max_date),
                        yr          = date.getFullYear(),
                        month       = date.getMonth()+1,
                        day         = date.getDate(),
                        max_date    =  day + '-' + month + '-' + yr;


                        var str_array_vals = (str_values.length === 0 )?[]:str_values.toString().split(',');

                        var monthDate = [];
                        
                        $.each(str_array_vals, function( index, value ){

                            var val_arr = value.split('-');

                            var multiple  = new Date();
                            multiple.setMonth(val_arr[1]-1);
                            multiple.setDate(val_arr[0]);
                            multiple.setYear(val_arr[2]);
                            monthDate.push(multiple);
                        });
                        var mindate =  (monthDate.length === 0 )? new Date():monthDate[0];

                        $.ajax({
                            //url: sitehost+'/cart/ajaxplanperiod',
                            url: sitehost+'/cart/ajax-get-start-date',
                            type:"POST",
                            data:new_data,
                            async:false,
                            success:function(result){

                                $('#with-altField'+k2val).datepicker("destroy");

                                $('#with-altField'+k2val).multiDatesPicker({
                                    minDate : mindate, //new Date(result.sdate),
                                    altField : '#altField'+k2val,
                                    dateFormat : "d-m-yy",
                                    pickableRange: parseInt(result.result),
                                    adjustRangeToDisabled: true,
                                    addDisabledDates : disabledDates,
                                    maxPicks : days_no,
                                    weekoff : weekoff1Obj[w2],
                                    beforeShowDay: function(date)  {

                                        show = true;
                                        if(jQuery.inArray(date.getDay(), weekoff1Obj[w2]) != -1){

                                            show = false; }//No Weekends
                                        for (var i = 0; i < holidays.length; i++) {
                                            if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
                                        }
                                        var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!

                                        return display;
                                    } ,

                                    addDates : monthDate,
                                    onSelect: function(dateText, inst) {
//console.log(s_value);
//$('#delivery_time_'+s_value.menu+'_'+s_value.pk_product_code).trigger("click");
                                        var max_date = inst.settings.maxDate;
                                        var w1 = $(this).data('weekoffkey');

                                        var date = $(this).val();
                                        var time = $('#time').val();
                                        var date_selected = $(this).val();
                                        var s_id =$(this).attr('pid');

                                        /* sankalp .6 sept */
                                        var ord_dates = $(this).multiDatesPicker( 'getDates');

                                        var arr_ord_dates = [];

                                        $.each(ord_dates,function(k,d){
                                            var arr_d = d.split("-");
                                            var day = arr_d[0];
                                            var month = arr_d[1];
                                            var year = arr_d[2];
                                            if(day <10 && day > 0){
                                                day = "0"+day;
                                            }

                                            if(month <10 && month > 0){
                                                month = "0"+month;
                                            }

                                            var str_d = day+"-"+month+"-"+year;

                                            arr_ord_dates.push(str_d);

                                        });

                                        /*delivery_timedelivery_time
                                        if(loadTimeSlot == 1) {

                                            var today = new Date();
                                            var dd = today.getDate();
                                            var mm = today.getMonth()+1;   
                                            var yyyy = today.getFullYear(); 
                                            var cur_date = dd+'-'+mm+'-'+yyyy;                                        
                                            console.log('order dates....');
                                            console.log(cur_date);

                                            ord_dates.forEach(function(k,v) {
                                                console.log(k);
                                                if(k == cur_date) {
                                                    console.log('gotcha curr date...');
                                                    alert('Please select different date..');
                                                }
                                            });
                                            
                                           // console.log(ord_dates.hasOwnProperty(cur_date));
                                        } 
                                        */                                       

                                        //$('#altField'+s_id).val(ord_dates.join(', '));
                                        $('#altField'+s_id).val(arr_ord_dates.join(', '));

                                        var order_dates = $('#altField'+s_id).val();
                                        /* ends */
                                        $.ajax({
                                            url: sitehost+'/cart/ajax-update-cartdate',
                                            type:'POST',
                                            data:{key_update:s_id,order_dates:order_dates, max_date: max_date, purpose:"order_dates"},
                                            success:function(result){      
                                            }
                                        });
                                    }
                                });

                                $('#with-altField'+k2val).datepicker("option", "maxDate", max_date); // !imp -> setting max_date
                                $('#altField'+k2val).val(str_values);
                            }
                        });

                    }else{

                        $.ajax({
                            //url: sitehost+'/cart/ajaxplanperiod',
                            url: sitehost+'/cart/ajax-get-start-date',
                            type:"POST",
                            data:new_data,
                            async:false,
                            success:function(result){

                                //console.log('!isset#with-altField'+k2val);

                                $('#with-altField'+k2val).multiDatesPicker({
                                    minDate : new Date(result.sdate.split('-')[0], result.sdate.split('-')[1]-1, result.sdate.split('-')[2]),
                                    altField : '#altField'+k2val,
                                    dateFormat : "d-m-yy",
                                    pickableRange: parseInt(result.result),
                                    adjustRangeToDisabled: true,
                                    addDisabledDates : disabledDates,
                                    maxPicks : days_no,
                                    weekoff : weekoff1Obj[w2],
                                    beforeShowDay: function(date)  {
                                        show = true;
                                        if(jQuery.inArray(date.getDay(), weekoff1Obj[w2]) != -1){ show = false; }//No Weekends
                                        for (var i = 0; i < holidays.length; i++) {
                                            if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
                                        }
                                        var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!

                                        return display;
                                    } ,
                                    onSelect: function(dateText, inst) {

                                        var max_date = inst.settings.maxDate;
                                        var date = $(this).val();
                                        var time = $('#time').val();
                                        var date_selected = $(this).val();
                                        var s_id =$(this).attr('pid');

                                        /* sankalp .6 sept */
                                        var ord_dates = $(this).multiDatesPicker('getDates');
                                        var arr_ord_dates = [];

                                        $.each(ord_dates,function(k,d){
                                            var arr_d = d.split("-");
                                            var day = arr_d[0];
                                            var month = arr_d[1];
                                            var year = arr_d[2];
                                            if(day <10 && day > 0){
                                                day = "0"+day;
                                            }

                                            if(month <10 && month > 0){
                                                month = "0"+month;
                                            }

                                            var str_d = day+"-"+month+"-"+year;

                                            arr_ord_dates.push(str_d);

                                        });

                                        //$('#altField'+s_id).val(ord_dates.join(', '));
                                        $('#altField'+s_id).val(arr_ord_dates.join(', '));
                                        var order_dates = $('#altField'+s_id).val();
                                        /* ends */
                                        $.ajax({
                                            url: sitehost+'/cart/ajax-update-cartdate',
                                            type:"POST",
                                            data:{key_update:s_id,order_dates:order_dates, max_date: max_date , purpose:"order_dates"},
                                            success:function(result){           
                                            }
                                        });
                                    }
                                });

                            }

                        });

                    }

                }// periodbased

            } //plantype

        });    

    }    

}


$(document).ready(function(){
    
    var theme = $("#theme").val();

    $(".close-panel2").click(function() {
        $(this).closest('.cart_items_row_toggle').prev().find(".cart-panel").slideUp("slow");
        $(this).closest('.cart_items_row_toggle').find(".donedown").show();
        $(this).closest('.cart_items_row_toggle').find(".doneup").hide();
    });

    $(".open-panel2").click(function() {
        $(this).closest('.cart_items_row_toggle').prev().find(".cart-panel").slideDown("slow");
        $(this).closest('.cart_items_row_toggle').find(".donedown").hide();
        $(this).closest('.cart_items_row_toggle').find(".doneup").show();
    });

    $(".cart_items").each(function(e) {
        var selectDays = $(this).find(".selectDays").val();
        if(selectDays !=0){
            $(this).next().find(".donedown").show();
        }
    });

    $(document).on('change','.selectDays',function() {
        if(theme!='burger'){
            var sid = $(this).attr('id');
            $("#cart_panel_"+sid).slideDown("slow");
            $(this).closest('.cart_items').next().find(".doneup").show();
        }
     });

     $('.view-meal-info').click(function() {        
        var products_key = $(this).data('products');  
        var str ="";  
        str += '<div class="row">';
        str +='<div class="col-sm-4"><div class="vw-item mb"><img src="'+products_key.image_path+'" class="vw-item-img">'+'<h4 class="price">'+formatter.format(products_key.unique_price)+'</h4></div></div>';
        str += '<div class="col-sm-8"><div class="vw-item"><ul class="list-group"><li class="list-group-item justify-content-between list-head"><b>Item</b><span class="badge badge-default badge-pill"><b>Qty</b></span></li>';
        for (var key in products_key.meal_details) {  
            
            str += '<li class="list-group-item">'+products_key.meal_details[key]['name']+'<span class="badge badge-default badge-pill">'+products_key.meal_details[key]['quantity']+'</span></li>';
        }
        str += '</ul></div></div></div>';
        str +='<div class="row"><div class="col-sm-12 txt-left"><b>Meal Description: </b><p>'+products_key.description+'</p></div></div>';               
        $('#mealInfoModel .vw-item').html(str);
        $('#mealInfoModel').modal('show');
        return false;
    });

    $('.selectpicker').selectpicker({
        style: 'btn-info',
        size: 4
    });
    

    $(document).on('click', '.plus', function() {

        var extra_code = $(this).attr("data-extra");
        var ids = $(this).attr('id');
        var st = ids.split("q");
        
        var selecttype = $( "#"+st[1]+" option:selected" ).val();

        var prod_id = $(this).attr('pid');
        var menu = $(this).attr('menu');

        $(this).prev().text(parseInt($(this).prev().text()) + 1);

        var menu = $(this).attr('menu');
        var qnt = parseInt($(this).prev().text());

        var purpose = (typeof(extra_code) != "undefined") ? "plus-quantity-extra": "plus-quantity";   

        var newPromoCode ='';
        if($("#apply_promocode").attr('data-flag==true')){
            newPromoCode = $('#apply_promocode').val();
        }

        updateCartContainer(purpose,prod_id,menu,qnt,newPromoCode);
       
        if(typeof(extra_code) != "undefined"){

            var arrExtra = extra_code.split("_");
            var menu = arrExtra[0];

            if(menu=='instantorder'){
                var selecttype = [];
                selecttype[0] = 1;
            }else{
                var selecttype = $( "#"+extra_code+" option:selected" ).val().split("%");
            }

        }else{

            if(menu=='instantorder'){
                var selecttype = [];
                selecttype[0] = 1;
            }else{

                selecttype = selecttype.split("%");
            }
        }

        if(typeof(extra_code) != "undefined"){
            e_id = $(this).attr("data-id");
            $("#iq"+e_id+"s").val(parseInt($(this).prev().text()));
            $("#iq"+e_id+"s").text(parseInt($(this).next().text()));

        }

        $("#i"+ids+"s").val(parseInt($(this).prev().text()));
        $("#i"+ids+"s").text(parseInt($(this).next().text()));
        
        if(typeof(extra_code) == "undefined" && menu!='instantorder'){
            var split_vals = $( ".selectDays"+st[1]+" option:selected" ).text().split(",");           
            var temp_split = split_vals[1].split(" ");
            $(".infoPlan"+st[1]).text((selecttype[0]*qnt)+" meals to be consumed in "+temp_split[1]+" days.");
        }
    });


    $(document).on('click', '.minus', function() {

        var extra_code = $(this).attr("data-extra");
        var ids = $(this).attr('data-key');
        var st = ids.split("_");
        
        var prod_id = st[1];
        var menu = st[0];

        var prod_id = $(this).attr('pid');
        var menu = $(this).attr('menu');

        var purpose = (typeof(extra_code) != "undefined") ? "minus-quantity-extra": "minus-quantity";   

        if(typeof(extra_code) != "undefined"){
            var arrExtra = extra_code.split("_");
            var menu = arrExtra[0];

            if(menu=='instantorder'){
                var selecttype = [];
                selecttype[0] = 1;
            }else{
                var selecttype = $( "#"+extra_code+" option:selected" ).val().split("%");
            }

        }else{
            if(menu=='instantorder'){
                
                var selecttype = [];
                selecttype[0] = 1;
            }else{
                var selecttype=$( "#"+ids+" option:selected" ).val();
                selecttype = selecttype.split("%");
            }
        }

        if ($(this).next().text() - 1 == 0) {

            toastr.error("Quantity can not be less than 1");

        } else {
            
            $(this).next().text($(this).next().text() - 1);
            $("#i"+ids).val(parseInt($(this).next().text()));
            
            var qt = parseInt($(this).next().text());
            
            var newPromoCode ='';
            if($("#apply_promocode").attr("data-flag==true")){
                 newPromoCode = $("#apply_promocode").val();
            }
            updateCartContainer(purpose,prod_id,menu,qt,newPromoCode);

            if(typeof(extra_code) != "undefined"){
                $('#iq'+prod_id+'s').val(parseInt($(this).next().text()));

            }
            
            $('#iq'+ids+'s').val(parseInt($(this).next().text()));

            if(typeof(extra_code) == "undefined" && menu !='instantorder'){

                var split_vals = $( ".selectDays"+ids+" option:selected" ).text().split(",");
                var temp_split = split_vals[1].split(" ");
                $(".infoPlan"+ids).text((selecttype[0]*qt)+" meals to be consumed in "+temp_split[1]+" days.");
            }

        }

    });

    /* On selection of plans */
    $('.selectDays').on('change', function() {

        var element = this;
        var id = $(this).attr('id');
        var splitval = id.split('_');
        var menu = splitval[0];
        var product_code = splitval[1];    
        
        var vals = $(this).attr("vals");

        var selectDays = $(this).val();
        var selecttype = $(this).val().split("%");

        var key_modified_val = $(this).attr("id");

        $(this).closest('.cart_items').find('.wkdays').hide();
        $(this).closest('.cart_items').find('.dateContainer').hide();

        $('.enddate'+vals).html('');
        $('.rp'+vals).val('0');
        $("#Datepickersingle_"+vals).datepicker("setDate", "");
        
        if(theme != 'burger'){
            $('#Datepickersingle_'+vals).hide();
        }
        
        var order_menu = $("."+vals+"_menu").val();
        var kitchen = $(this).closest('.cart_items').data('kitchen');
        
        if(selecttype[1] == "periodbased"){
           
            $(this).closest('.cart_items').find('.calDiv').show();
            if (selectDays == '0') {

                //$(this).parent().find('.rptmeal').hide();
                $(this).closest('.cart_items').find('.rptmeal_container').hide();
                $(this).closest('.cart_items').find('.infoPlan'+id).hide();
                
            }

            if (selectDays != '0') {
                
                if(theme!='burger'){
                    $(this).closest('.cart_items').find('.rptmeal_container').show();
                    $(this).closest('.cart_items').find('.infoPlan'+id).show();
                }
            }

        }
        else if(selecttype[1]=="datebased") {

            var weekOff = $(this).data('weekoff1')+"";

            var weekoffsOnchange= new Array();

            if(weekOff.length <= 1){
                weekoffsOnchange.push(parseInt(weekOff));
            }else{
                var week_off=weekOff.split(",");

                $.each(week_off,function(key1,value1){
                    weekoffsOnchange.push(parseInt(value1));
                });

            }
            $(this).closest('.cart_items').find('.wkdays').hide();
            $(this).closest('.cart_items').find('.calDiv').hide();
            $(this).closest('.cart_items').find('.rptmeal_container').hide();

            var data="plan_period=1&kitchen="+kitchen+"&type="+selecttype[1]+'&'+"days="+selecttype[0]+'&'+"menu="+order_menu+'&'+"weekoff="+weekOff;

            $('#with-altField'+vals).multiDatesPicker('destroy');

            $.ajax({
                //url: sitehost+'/cart/ajaxplanperiod',
                url: sitehost+'/cart/ajax-get-start-date',
                type:"POST",
                data:data,
                success:function(result){
                   
                   $('#with-altField'+vals).multiDatesPicker({
                        minDate : new Date(result.sdate.split('-')[0], result.sdate.split('-')[1]-1, result.sdate.split('-')[2]),
                        altField : '#altField'+vals,
                        dateFormat : "dd-mm-yy",
                        pickableRange: parseInt(result.result),
                        adjustRangeToDisabled: true,
                        addDisabledDates : disabledDates,
                        maxPicks : selecttype[0],
                        weekoff : weekoffsOnchange,
                        beforeShowDay: function(date) {
                            show = true;
                            if(jQuery.inArray(date.getDay(), weekoffsOnchange) != -1){ show = false; }//No Weekends
                            for (var i = 0; i < holidays.length; i++) {
                                if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
                            }
                            var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                            return display;
                        },
                        onSelect: function(dateText, inst) {
                            var max_date =  inst.settings.maxDate;
                            var date = $(this).val();
                            var time = $('#time').val();
                            /* sankalp .6 sept */
                            var ord_dates = $(this).multiDatesPicker( 'getDates');
                            $('#altField'+vals).val(ord_dates.join(', '));

                            var order_dates = $('#altField'+vals).val();
                           
                            /* ends */
                            $.ajax({
                                url: sitehost+'/cart/ajax-update-cartdate',
                                type:"POST",
                                data:{key_update:vals,order_dates:order_dates, max_date : max_date, purpose:"order_dates"},
                                success:function(result)
                                {                                    
                                    $('#id_'+vals).closest('.cart_items').find('.set_preference').show();
                                    $('#id_'+vals).closest('.cart_items').find('.add-extra-btn').show();
                                     var d = new Date();
                                    var todays_date = d.getDate() + "-" + (d.getMonth()+1) + "-" + d.getFullYear();   
                                    var single_order_date = "";
                                    if(jQuery.inArray(todays_date,ord_dates)==0){
                                        single_order_date = todays_date;
                                    }else{
                                        single_order_date = date;
                                    }
                                    $('#id_'+vals).closest('.cart_items').find('.add-extra-div').hide();
                                     $.ajax({
                                        url: sitehost+'/cart/ajax-timeslots',
                                        type:"POST",
                                        data:{menu:menu,kitchen:kitchen,single_order_date:single_order_date},
                                        success:function(result) {
                                            $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                                            $('#delivery_time_'+key_modified_val).closest('.time-slot').show();
                                            var str = '';
                                            if(result.status == 'success') {
                                                if(Object.keys(result.data).length == 0) {
                                                    //$('#delivery_time_'+key_modified_val+' option[value="0"]').empty();
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('No Timeslot Available');
                                                }
                                                else {
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('Choose Time Slot');
                                                    $.each(result.data, function(k, v) {
                                                        str += '<option value='+v.starttime+'-'+v.endtime+'>'+v.starttime+' to '+v.endtime+'</option>';
                                                    });
                                                }
                                                $('#delivery_time_'+key_modified_val).append(str);    
                                            }
                                            
                                        }
                                    });
                                }
                            });
                        }
                    });

                    $('#with-altField'+vals).multiDatesPicker('resetDates', 'picked');
                }
            });

            var selectDays = $(this).val();
            if (selectDays != '0') {
                $(this).closest('.cart_items').find('.dateContainer').show();
                $(this).closest('.cart_items').find('.infoPlan'+id).show();
            }
            if (selectDays == '0') {
                $(this).closest('.cart_items').find('.dateContainer').hide();
                $(this).closest('.cart_items').find('.infoPlan'+id).hide();
            }

        }
        else{
            
            if (selectDays == '0') {
                $(this).closest('.cart_items').find('.rptmeal_container').hide();
                $(this).closest('.cart_items').find('.infoPlan'+id).hide();
            }
        }
        
        if(selecttype[0]>0){

            data="selecttype="+selectDays+"&"+"id="+menu+"#"+product_code+"&"+"purpose="+"planchange"+"&"+"concat1="+selecttype[0]+"&"+"concat2="+selecttype[1];
            $.ajax({
                    url: sitehost+'/cart/ajax-update-cartdate',
                    type:"POST",
                    async:false,
                    data:data,
                    success:function(result){
                      
                       var qt = $("#iq"+id+"s").val();
                       
                       $(element).closest('.cart_items').find('.btnDone').show();
                       
                       var total_value= selecttype[0]*qt; 
                      
                       var mealKey = menu+"#"+product_code;
                       updateAmount(result.cart,mealKey,$(element).closest('.cart_items'));
                       
                       var split_vals = $( ".selectDays"+id+" option:selected" ).text().split(",");
                       var temp_split = split_vals[1].split(" ");
                       $(".infoPlan"+id).text((total_value)+" meals to be consumed in "+temp_split[1]+" days.");
                       $('#id_'+id).closest('.cart_items').find('.set_preference').hide();
                        $('#id_'+id).closest('.cart_items').find('.add-extra-btn').hide();
                        $('#id_'+id).closest('.cart_items').find('.add-extra-div').hide();
                   }
           }); 
        }else{
            $("#iq"+id+"ss").text("");
            $("#"+id+"_original_price").text("");
        }

    });/* Select days ends here */

    /* On selecting of days slot mon-sat , mon- fri etc...  */
    $('.rptmeal').on('change', function() {
        
        var datepickerid = $(this).attr("id");

        var menu = $(this).data("menu");    

        var id = $(this).attr("id").split("_");
        
        var key_modified_val = $(this).attr("id");

        var repeatMeal = $(this).val();

        var startdate;

        var weekOff1 = $(this).closest('.cart_items').find('.selectDays').data('weekoff1');

        var kitchen = $(this).closest('.cart_items').data('kitchen');        

        $.ajax({
            type:"POST",
            url: sitehost+'/cart/ajax-get-start-date',
            data:{menu:menu, weekoff: weekOff1, kitchen: kitchen},
            async:false,
            success:function(data){
                if(data.status){
                    startdate = data.sdate;
                }
            }

        });
        
        $('.enddate'+datepickerid).html('');
        $("#Datepickersingle_"+datepickerid).datepicker("setDate", "");

        if (repeatMeal == 'mf' || repeatMeal == 'ms' || repeatMeal == 'msu') {

            $('#Datepickersingle_'+datepickerid).css("display","inline block");

            if(repeatMeal == 'mf'){

                $("#Datepickersingle_"+datepickerid).datepicker("destroy");
                
                $("#Datepickersingle_"+datepickerid).datepicker({
                    minDate : new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
                    dateFormat : "dd-mm-yy",
                    orientation : "top left",
                    beforeShowDay: function(date){
                        show = true;
                        if(date.getDay() == 0 || date.getDay() == 6){ show = false; }//No Weekends
                        for (var i = 0; i < holidays.length; i++) {
                            if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
                        }
                        var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                        return display;
                    },
                    onSelect: function(dateText, inst) {
                        var date = $(this).val();
                        var time = $('#time').val();
                        $.ajax({
                            url: sitehost+'/cart/ajax-update-cartdate',
                            type:"POST",
                            data:{key_update:datepickerid,date_select:date,purpose:"single_order_date"},
                            success:function(result){   //alert(result.enddate);  
                                $("#Datepickersingle_"+datepickerid).closest('.cart_items').find('.set_preference').show();
                                $("#Datepickersingle_"+datepickerid).closest('.cart_items').find('.add-extra-btn').show();
                                 $('#id_'+datepickerid).closest('.cart_items').find('.add-extra-div').hide();
                                var showdate= new Date(result.enddate.toString());
                                $('.enddate'+datepickerid).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );
                                    
                                    $.ajax({
                                        url: sitehost+'/cart/ajax-timeslots',
                                        type:"POST",
                                        data:{menu:menu,kitchen:kitchen,plan_days:repeatMeal,single_order_date:date},
                                        success:function(result) {
                                            $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                                            $('#delivery_time_'+key_modified_val).closest('.time-slot').show();
                                            var str = '';
                                            if(result.status == 'success') {
                                                if(Object.keys(result.data).length == 0) {
                                                    //$('#delivery_time_'+key_modified_val+' option[value="0"]').empty();
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('No Timeslot Available');
                                                }
                                                else {
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('Choose Time Slot');
                                                    $.each(result.data, function(k, v) {
                                                        str += '<option value='+v.starttime+'-'+v.endtime+'>'+v.starttime+' to '+v.endtime+'</option>';
                                                    });
                                                }
                                                $('#delivery_time_'+key_modified_val).append(str);    
                                            }
                                            
                                        }
                                    }); 
                            }

                        });
                    }
                });

            }else if(repeatMeal == 'ms'){

                MDP=$("#Datepickersingle_"+datepickerid).datepicker("destroy");

                MDP=$("#Datepickersingle_"+datepickerid).datepicker({
                    minDate : new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
                    dateFormat : "dd-mm-yy",
                    orientation : "top left",
                    beforeShowDay: function(date){
                        show = true;
                        if(date.getDay() == 0){ show = false; }//No Weekends
                        for (var i = 0; i < holidays.length; i++) {
                            if (new Date(holidays[i]).toString() == date.toString()) {

                                show = false;
                            }//No Holidays
                        }
                        var display = [show,'',(show)?'':'Weekends or Holidays'];//With Fancy hover tooltip!
                        return display;
                    },
                    onSelect: function(dateText, inst) {
                        var date = $(this).val();
                        var time = $('#time').val();
                        $.ajax({
                            url: sitehost+'/cart/ajax-update-cartdate',
                            type:"POST",
                            data:{key_update:datepickerid,date_select:date,purpose:"single_order_date"},
                            success:function(result){
                                $("#Datepickersingle_"+datepickerid).closest('.cart_items').find('.set_preference').show();
                                $("#Datepickersingle_"+datepickerid).closest('.cart_items').find('.add-extra-btn').show();
                                var showdate= new Date(result.enddate.toString());
                                $('.enddate'+datepickerid).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );

                                    $.ajax({
                                        url: sitehost+'/cart/ajax-timeslots',
                                        type:"POST",
                                        data:{menu:menu,kitchen:kitchen,plan_days:repeatMeal,single_order_date:date},
                                        success:function(result) {
                                            $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                                            $('#delivery_time_'+key_modified_val).closest('.time-slot').show();
                                            var str = '';
                                            if(result.status == 'success') {
                                                if(Object.keys(result.data).length == 0) {
                                                    //$('#delivery_time_'+key_modified_val+' option[value="0"]').empty();
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('No Timeslot Available');
                                                }
                                                else {
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('Choose Time Slot');
                                                    $.each(result.data, function(k, v) {
                                                        str += '<option value='+v.starttime+'-'+v.endtime+'>'+v.starttime+' to '+v.endtime+'</option>';
                                                    });
                                                }
                                                $('#delivery_time_'+key_modified_val).append(str);    
                                            }
                                            
                                        }
                                    }); 

                            }

                        });

                    }
                });

            }else if(repeatMeal == 'msu'){

                    MDP=$("#Datepickersingle_"+datepickerid).datepicker("destroy");

                    MDP=$("#Datepickersingle_"+datepickerid).datepicker({
                        minDate : new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
                        dateFormat : "dd-mm-yy",
                        orientation : "top left",
                        beforeShowDay: function(date){
                        show = true;
                        for (var i = 0; i < holidays.length; i++) {
                            if (new Date(holidays[i]).toString() == date.toString()) { show = false;}//No Holidays
                        }
                        var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                        return display;
                    },
                    onSelect: function(dateText, inst) {
                        var date = $(this).val();
                        var time = $('#time').val();
                        $.ajax({
                            url: sitehost+'/cart/ajax-update-cartdate',
                            type:"POST",
                            data:{key_update:datepickerid,date_select:date,purpose:"single_order_date"},
                            success:function(result){
                                $("#Datepickersingle_"+datepickerid).closest('.cart_items').find('.set_preference').show();
                                $("#Datepickersingle_"+datepickerid).closest('.cart_items').find('.add-extra-btn').show();
                                var showdate= new Date(result.enddate.toString());
                                $('.enddate'+datepickerid).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );
                                 $.ajax({
                                        url: sitehost+'/cart/ajax-timeslots',
                                        type:"POST",
                                        data:{menu:menu,kitchen:kitchen,plan_days:repeatMeal,single_order_date:date},
                                        success:function(result) {
                                            $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                                            $('#delivery_time_'+key_modified_val).closest('.time-slot').show();
                                            var str = '';
                                            if(result.status == 'success') {
                                                if(Object.keys(result.data).length == 0) {
                                                    //$('#delivery_time_'+key_modified_val+' option[value="0"]').empty();
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('No Timeslot Available');
                                                }
                                                else {
                                                    $('#delivery_time_'+key_modified_val).find("option:selected").text('Choose Time Slot');
                                                    $.each(result.data, function(k, v) {
                                                        str += '<option value='+v.starttime+'-'+v.endtime+'>'+v.starttime+' to '+v.endtime+'</option>';
                                                    });
                                                }
                                                $('#delivery_time_'+key_modified_val).append(str);    
                                            }
                                            
                                        }
                                    }); 

                            }

                        });
                    }
                });
            }

           $(this).closest('.cart_items').find('.calDiv').show();
           $(this).closest('.cart_items').find('.wkdays').hide();

        }

        if (repeatMeal == 'yourChoice') {
            $(this).closest('.cart_items').find('.calDiv').show();
            $(this).closest('.cart_items').find('.wkdays').show();

            $('#Datepickersingle_'+datepickerid).css("display","none");

        }

        if(repeatMeal!="0"){

            data="repeatMeal="+repeatMeal+"&"+"id="+id[0]+"#"+id[1]+"&"+"purpose="+"dayschange";

            $.ajax({
                url: sitehost+'/cart/ajax-update-cartdate',
                type:"POST",
                data:data,
                success:function(result){
                    $('#delivery_time_'+key_modified_val+' option[value!="0"]').remove().end();
                        $('#id_'+key_modified_val).closest('.cart_items').find('.add-extra-btn').hide();
                        $('#id_'+key_modified_val).closest('.cart_items').find('.add-extra-div').hide();
                }

            });
        }                               
                                

    });/* days selection ends here 


    /* choose days between weekdays if not in days slots */
    $('.chooseDay').on('change', function() {

        var datepickerid = $(this).attr("id");

        var id = $(this).attr("id").split("_");

        var repeatMeal = $(this).val().toString();

        var selecttype= repeatMeal.split(",");

        $('.enddate'+datepickerid).html('');

        var dateTypeVar = $("#Datepickersingle_"+datepickerid).datepicker('getDate');

        var order_menu = $("."+datepickerid+'_menu').val();

        var startdate ;

        var weekOff1 = $('#'+$(this).attr("id")).data('weekoff1');

        $.ajax({
            type:"POST",
            url: sitehost+'/cart/ajax-get-start-date',
            data:{menu:order_menu, weekoff: weekOff1},
            async:false,
            success:function(data){
                if(data.status){
                    startdate = data.sdate;
                }
            }

        });


        $("#Datepickersingle_"+datepickerid).datepicker("setDate", "");

        $("#Datepickersingle_"+datepickerid).datepicker("destroy");

        MDP=$("#Datepickersingle_"+datepickerid).datepicker({
            minDate : new Date(startdate.split('-')[0], startdate.split('-')[1]-1, startdate.split('-')[2]),
            dateFormat : "dd-mm-yy",
            orientation : "top left",
            beforeShowDay: function(date){

                var show = true;
                var pick=date.getDay().toString();

                for(i=0;i< selecttype.length;i++){

                    if( pick == selecttype[i].toString()){
                        show = true;
                        break;
                    }else{
                        show = false;
                    }
                }
                //No Weekends
                for (var i = 0; i < holidays.length; i++) {
                    if (new Date(holidays[i]).toString() == date.toString()) { show = false;}//No Holidays
                }

                var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
                return display;

            },
            onSelect: function(dateText, inst) {

                var date = $(this).val();
                var time = $('#time').val();

                $.ajax({
                    url: sitehost+'/cart/ajax-update-cartdate',
                    type:"POST",
                    data:{key_update:datepickerid,date_select:date,purpose:"single_order_date"},
                    success:function(result){
                         $('.set_preference').show();
                         $('.add-extra-btn').show();
                        var showdate= new Date(result.enddate.toString());
                        $('.enddate'+datepickerid).html(" To "+ showdate.getDate()+"-"+(showdate.getMonth()+1)+"-"+showdate.getFullYear() );

                    }

                });

            }
        });


        if(repeatMeal!=""){

            $('#Datepickersingle_'+datepickerid).css("display","inline block");
            data="repeatMeal="+repeatMeal+"&"+"id="+id[0]+"#"+id[1]+"&"+"purpose="+"ChooseDays";

            $.ajax({
                url: sitehost+'/cart/ajax-update-cartdate',
                type:"POST",
                data:data,
                success:function(result){

                }

            });
        }

    });/* choose days ends here */
    

    $(document).on("click",".cart-destroy",function(){       
        $(".loader-div").show();
        var purpose = "remove";
        var product_code = $(this).attr('data-pid');
        var menu = $(this).attr('data-menu');        
        var tr_id = "id_"+menu+"_"+product_code;
        $('table#chktbl tr#'+tr_id).remove();
        updateCartContainer(purpose,product_code,menu,false,false);        
        window.location.reload();
    });



    /* instant- save delivery time slot in session - Pratik */
    $('.instant').on('change', function() {

        var itemid  = $(this).attr("data-id");
        var kitchen = $(this).attr("data-kitchen");
        var delivery_time  = $(this).val();
        var flag = true;
        $.ajax({
            data:{itemid : itemid, deliverytime: delivery_time, kitchen: kitchen, menu: itemid.split('_')[0]},
            url: sitehost+'/cart/ajax-update-deliverytime',
            type: "POST",
            async: false,
            success:function(result){
                if(result.status == "success") {
                    //$('.orderbtn').removeClass('hide');
                    flag = true;
                }else{
                    //$('.orderbtn').addClass('hide');
                    toastr.error(result.message);
                    flag = false;
                }      
            }
        });
        return flag;          
    });
    /* end instant- save delivery time slot in session - Pratik */

    /* On click on set preference */
    $(document).on('click',".set_preference",function(){

        var data = {};

        var menu = $(this).data('menu');
        var id = $(this).data('id');
        var kitchen = $(this).data('kitchen');

        data['menu'] = menu;
        data['id'] = id;
        data['kitchen'] = kitchen;

        $.ajax({
            data:data,
            url: sitehost+'/cart/get-planned-meal-items',
            type: "POST",
            async: false,
            beforeSend: function(){
                $(".loader-div").show();
            },
            success:function(data){
                $("#plannedMeals").html(data);
                $('#swappMealsModel').modal('show');
            },
            complete:function(){                  
                $(".loader-div").hide();                  
            }
        });

    });

    var currentCartItemDiv = "";

    $(document).on('click', '.addExtra', function() {
       
        var refkey = $(this).attr('data-key');
        currentCartItemDiv = $(this).closest('.cart_items');
        refkey = refkey.replace(/_/g, '#');
        //mealInfoModel
        
        // set customer preferences into session ...
        $.ajax({
            data:{content_type:'json',refMeal:refkey},
            url: sitehost+'/menu/ajx-product-extra',
            type: "POST",
            async: false,
            beforeSend: function(){                
            },
            success:function(html){                
                $('#extraModel .modal-body .row').html(html);
                $('#extraModel').modal('show');         
            }
        });        
        return false;
    });

    $(document).on('click', '.add-extra-to-cart', function() {

        var refkey = $(this).attr('data-key');
        var extraDiv = $(this).closest(".add-extra-items"); 

        var extra_item = {
            'pk_product_code': extraDiv.find(".product_id").val(),
            'name': extraDiv.find(".product_name").val(),
            'unit_price': extraDiv.find(".rate").val(),            
            'quantity': extraDiv.find(".product_quantity").html(),
            'product_category': extraDiv.find(".product_category").val(),
            'kitchen': extraDiv.find(".screen").val(),
            'product_type':extraDiv.find(".type").val(),
            'food_type':extraDiv.find(".foodtype").val(),
            'refmeal':extraDiv.find(".key").val(),
            'extra_order_date':extraDiv.find(".extra_order").val(),
        }
        
        // add extra item to meal ...
        $.ajax({
            data:{refMeal:refkey,purpose:'addExtra',extra:extra_item,id:extra_item.pk_product_code},
            url: sitehost+'/cart/ajax-update-cartdate',
            type: "POST",
            async: false,
            //contentType: "json",
            beforeSend: function(){              
            },
            success:function(data){                                
                
                if(data.api_status=='success'){
                    var arrRefkey = refkey.split('#');
                    var meal_id = arrRefkey[1]; 
                    var refMeal = refkey.replace(/#/g, '_');   
                    if(data.cart.items[refkey].items){                        
                        $.each(data.cart.items[refkey].items,function(arrayKey , arrayValue){                            
                            if(meal_id == arrayValue.pk_product_code){
                                extra_item.linePrice = arrayValue.extra[extra_item.pk_product_code].linePrice;                               
                            }                           
                        });                        
                    }  
                    var str = '<li class="count add-extra-items add-extra-div">\n\
                                    <input type = "hidden" class = "product_id" value =' +extra_item.pk_product_code+'>\n\
                                    <input type = "hidden" class = "product_name" value =' +extra_item.name+'>\n\
                                    <input type = "hidden" class = "product_category" value =' +extra_item.product_category+'>\n\
                                    <input type = "hidden" class = "max_quantity_per_meal" value =' +extra_item.quantity+'>\n\
                                    <input type = "hidden" class = "rate" value =' +extra_item.unit_price+'>\n\
                                    <input type = "hidden" class = "screen" value =' +extra_item.kitchen+'>\n\
                                    <input type = "hidden" class = "type" value =' +extra_item.product_type+'>\n\
                                    <input type = "hidden" class = "foodtype" value =' +extra_item.food_type+'>\n\
                                    <input type = "hidden" class = "key" value =' +extra_item.refmeal+'>\n\
                                    <input type = "hidden" class = "order_date" value =' +extra_item.extra_order_date+'>\n\
                                    <label class="add-extra-div extra_'+refMeal+'_'+extra_item.pk_product_code+' "><span>'+extra_item.name+'- </span><span class="extra-price">'+formatter.format(extra_item.linePrice)+'</span> \n\
                                    <div class="cart-p-m"><a href="javascript:void(0);" class="update-extra-quantity" data-act="dec"><span class="ti-minus minus-icon"></span></a>\n\
                                    <span class="count-icon product_quantity">'+extra_item.quantity+'</span><a href="javascript:void(0);" class="update-extra-quantity" data-act="inc"><span class="ti-plus plus-icon"></span></a>\n\
                                    <a href="javascript:void(0);" data-key="'+refMeal+'" data-id="'+extra_item.pk_product_code+'" class="remove_extra"><span class="ti-close"></span></a></div></label></li>\n\
                                ';
                    
                    var mealExtraItemsDiv = currentCartItemDiv.find('.extra_items ul');
                    
                    if(mealExtraItemsDiv.find('label').length == 0){
                       mealExtraItemsDiv.html(str);
                    }else{
                       mealExtraItemsDiv.append(str);
                    }
                    
                    extraDiv.find('.add-extra-to-cart').addClass('applied');
                    extraDiv.find('.add-extra-to-cart').html('Added');
                    extraDiv.find('.add-extra-to-cart').addClass('update-extra-quantity');
                    extraDiv.find('.update-extra-quantity').removeClass('add-extra-to-cart');
                    extraDiv.find('.update-extra-quantity').prop('disabled',true);
                    

                    updateAmount(data.cart,refkey,currentCartItemDiv);
                    
                    toastr.success(extra_item.name+" item has been added successfully.");
                }

                if(data.status=='error'){
                    toastr.error(data.msg);
                }
            }
        });        
        return false;
    }); 

    $(document).on('click','.remove_extra',function(){
        
        var productId = $(this).data('id');
        var refMeal = $(this).data('key');
        var ele = this;
        
        var refMeal = refMeal.replace(/_/g, '#');

        $.ajax({
            data:{refMeal:refMeal,purpose:'remove_extra',id:productId},
            url: sitehost+'/cart/ajax-update-cartdate',
            type: "POST",
            async: false,
            //contentType: "json",
            beforeSend: function(){
            },
            success:function(data){
                
                if(data.api_status=='success'){                   
                    updateAmount(data.cart,refMeal,$(ele).closest('.cart_items'));
                    $(ele).closest(".add-extra-items").remove();
                    toastr.success(" item has been removed successfully.");
                }
                if(data.status=='error'){
                    toastr.error(data.msg);
                }
            },
            complete:function(){                
            }
        });        
    });

    $(document).on('click', '.inc-dec', function() {

        var extraDiv = $(this).closest(".add-extra-items"); 

        extraDiv.find(".update-extra-quantity").prop('disabled',false);
        extraDiv.find('.update-extra-quantity').removeClass('applied');
        extraDiv.find('.update-extra-quantity').addClass('itemAdded');
        extraDiv.find('.update-extra-quantity').html('Update');

        var act =  $(this).attr('data-act');
        extraDiv.find(".update-extra-quantity").attr('data-act',act);
        var extra_quantity =extraDiv.find(".product_quantity").html();
        if(act=='inc'){
            extra_quantity = parseInt(extra_quantity) + parseInt(1);
        }else if(act=='dec'){
            extra_quantity = parseInt(extra_quantity) - parseInt(1);
        }
        if(extra_quantity==0){
            toastr.error("Item quantity can not be less than one");
            return false;
        }
       extraDiv.find(".product_quantity").html(extra_quantity);
    });

    $(document).on('click','.update-extra-quantity',function(){
        
        var extraDiv = $(this).closest(".add-extra-items"); 
        extraDiv.find('.extra-modal').addClass('applied');
        extraDiv.find('.extra-modal').html('Added');
        extraDiv.find('.extra-modal').prop('disabled',true);

        var productId = $(this).closest(".add-extra-items").find(".product_id").val();
        var productName = $(this).closest(".add-extra-items").find(".product_name").val();
        var refMeal = $(this).closest(".add-extra-items").find(".key").val();
        var act = $(this).data('act');
        var extra_quantity = $(this).closest(".add-extra-items").find(".product_quantity").html();  
        if(act=='inc'){
            extra_quantity = parseInt(extra_quantity) + parseInt(1);
        }else if(act=='dec'){
            extra_quantity = parseInt(extra_quantity) - parseInt(1);
        }  
        var extra_order_date = $(this).closest(".add-extra-items").find(".extra_order").val();
        if(currentCartItemDiv==""){
            currentCartItemDiv = $(this).closest('.cart_items');
        }                                 
        var ele = this;

        var refMeal = refMeal.replace(/_/g, '#');

        $.ajax({
            data:{refMeal:refMeal,purpose:'extra-quantity',id:productId,quantity:extra_quantity,extra_order_date:extra_order_date},
            url: sitehost+'/cart/ajax-update-cartdate',
            type: "POST",
            async: false,           
            beforeSend: function(){               
            },
            success:function(data){
                
                if(data.api_status=='success'){                    
                    var arrRefkey = refMeal.split('#');
                    var meal_id = arrRefkey[1];                     
                    var refMealOrg = refMeal.replace(/#/g, '_');       
                    var linePrice = "";
                    var qty = "";
                    
                    if(data.cart.items[refMeal].items){                          
                        $.each(data.cart.items[refMeal].items,function(arrayKey , arrayValue){          
                            if(meal_id == arrayValue.pk_product_code){                                
                                linePrice = arrayValue.extra[productId].linePrice; 
                                qty = arrayValue.extra[productId].quantity; 
                            }                           
                        });                        
                    } 
                    
                    updateAmount(data.cart,refMeal,currentCartItemDiv);
                    $('.update-extra-quantity .product_quantity').val();
                    toastr.success(productName + " item details has been updated successfully.");

                    var eid = 'extra_'+refMealOrg+'_'+productId;                    
                    $('.'+eid).find('.extra-price').html(formatter.format(linePrice));
                    $('.'+eid).find('.product_quantity').html(qty);
                }

                if(data.status=='error'){
                    toastr.error(data.msg);
                }
            }
        });        

    })


    /* When set preference selection is done */
    $(document).on('click','#setPreferenceDone',function(){

        var mealId = $("#mealId").val();
        var menu = $("#menu").val();
        var key = menu+"#"+mealId;
        var unit_price = $("#meal_unit_price").val();

        var customerPreferences = {};
        customerPreferences[key] = {};
        customerPreferences[key]['item_details'] = {};

        $(".plannedProducts").each(function(){
            var date = $(this).find(".pdate").data("date");
            customerPreferences[key]['item_details'][date] = {};

            $(this).find(".list_view").each(function(){

            var gpid = $(this).data('gpid'); // generic product id

            var k = "gn_"+date+"_"+gpid;
            var spid = $("#"+k+":checked").val();
            var itemDetail = $("#"+k+":checked").data('prod');
            var swap_charges = $("#"+k+":checked").data('swap_charges');
            unit_price = parseFloat(unit_price) + parseFloat(swap_charges);
                customerPreferences[key]['item_details'][date][spid] = itemDetail;
            });

        });

        data = {
            'itemdetails':customerPreferences[key]['item_details'],
            key:key,
            purpose:'meal_items',
            rate: unit_price
        }       

        // set customer preferences into session ...
        $.ajax({
            data:data,
            url: sitehost+'/cart/ajx-update-cart',
            type: "POST",
            async: false,
            beforeSend: function(){
                $("#plannedMeals").html("<center><i class='fa fa-spinner fa-spin' style='font-size:40px;'></i></center>");
            },
            success:function(data){
                //console.log(data);
            }
        });
    });
    
    /* mobile view Promocode */
    $(document).on('click','.promo-code-mobile',function(){ 
       var promo_code = $(".promovalue").val();
        if(promo_code=="" || promo_code==null){            
        }
        var output = [];
        $(".selectDays").each(function(){
            var id = $(this).attr("id");
            if($(this).val() != 0 && $(this).val() != "0" ){
                var obj = {};
                obj[$(this).attr('id')] = $(this).val();
                output.push(obj);
            }
        });       
       if(output.length>0){
            var prod_type_count = JSON.stringify(output);
            var data = $.param({promo_code:promo_code,cart_date:prod_type_count});
            $.ajax({
                url: sitehost+'/cart/ajax-applypromocode-cart',
                type:"POST",                
                data:data,
                beforeSend:function(){
                    $(this).hide();
                    $(".loader-div").show();
                },
                success:function(result){                    
                    if(result.status){
                        updateAmount(result);                        
                        $(".promo-code-mobile").addClass('promo-checked').html('Applied').attr('data-flag', 'true');                        
                        $('.promo-code-mobile').closest('.modal-body').find('.cart_mobile').text('Proceed');
                        $(".remove_promo_code").show();
                        toastr.success("Promo code applied successfully.");                        
                    }else{                        
                        toastr.error(result.msg);                                                
                    }
                },
                error:function(t1,t2){
                    toastr.error(t1.responseText);
                },
                complete:function(){
                    $(this).show();
                    $(".loader-div").hide();                  
                }
            });
        }else{
            toastr.error("Please Select the plan");
        }
    }); /* mobile view Promocode end*/           

    $(document).on('click','#apply_promocode', function(){                
        
        var promo_code = $("#promovalue").val();

        if(promo_code=="" || promo_code==null){
            //window.location.reload();
        }

        var output = [];

        $(".selectDays").each(function(){

            var id = $(this).attr("id");
            if($(this).val() != 0 && $(this).val() != "0" ){

                var obj = {};
                obj[$(this).attr('id')] = $(this).val();
                output.push(obj);
            }

        });
        
       if(output.length>0){

            var prod_type_count = JSON.stringify(output);

            var data = $.param({promo_code:promo_code,cart_date:prod_type_count});

            $.ajax({
                url: sitehost+'/cart/ajax-applypromocode-cart',
                type:"POST",
                //contentType: "application/json; charset=utf-8",
                data:data,
                beforeSend:function(){
                    $(this).hide();
                    $(".loader-div").show();
                },
                success:function(result){
                    
                    if(result.status){

                        updateAmount(result);
                        
                        $("#apply_promocode").addClass('promo-checked').html('Applied').attr('data-flag', 'true');
                        $(".remove_promo_code").show();

                        toastr.success("Promo code applied successfully.");
                        
                    }else{                                  
                        toastr.error(result.msg);                         
                        //window.location.reload();
                    }
                },
                error:function(t1,t2){
                    toastr.error(t1.responseText);
                },
                complete:function(){
                    $(this).show();
                    $(".loader-div").hide();                  
                }

            });
        }else{
            toastr.error("Please Select the plan");
        }

    });/* end document on click apply promo code */

    $('.remove_promo_code').on('click',function(e) {
        updateCartContainer('remove_promo_code');
        $(this).parent().find('.promovalue').val('');
        $(this).parent().find('#promovalue').val('');
        $(this).parent().find('#apply_promocode').html('Apply').removeClass('promo-checked');
        $(this).parent().find('.promo-code-mobile').html('Apply').removeClass('promo-checked');
        $('.promo-code-mobile').closest('.modal-body').find('.cart_mobile').text('Skip');
        $('.remove_promo_code').hide();
        toastr.success("Promo code has been removed successfully");
    });
    
    $(document).on('blur','.remark',function(){
        var data = {};
        var remark = $(this).val();
        data = {
            remark:remark,
            purpose:'remark',
            
        }
        $.ajax({
            data:data,
            url: sitehost+'/cart/ajax-update-cartdate',
            type: "POST",
            async: false,
            beforeSend: function(){
                $("#plannedMeals").html("<center><i class='fa fa-spinner fa-spin' style='font-size:40px;'></i></center>");
            },
            success:function(data){
                //console.log(data);
            }
            
            
        });
    });
    
     /* order place action ...
    $(document).on('click','.orderbtn', function(){
            
        var element_btn = this;
        $('#loading_img').css('display','block');
        $(this).hide();
        console.log(cart); debugger;
        return false;
        $.each(cart,function(check_keys,check_vals){
            var p_id = check_keys;
            var key_check_modified = p_id.replace("#", "_");
            
            var selecttype = $(".selectDays"+key_check_modified+" option:selected" ).val();
            var instanttype = $("#delivery_time_"+key_check_modified+" option:selected" ).val(); // instant order by pratik
            //console.log(selecttype); debugger;
            if(instanttype==0 || instanttype=="0"){
                toastr.error("Please Select Time Slot for Meal "+check_vals['name']);
                $('#loading_img').css('display','none');
                $(this).show();
                return false;
            }
            if(selecttype==0 || selecttype=="0"){
                toastr.error("Please Select Plan for Meal "+check_vals['name']);
                $('#loading_img').css('display','none');
                $(this).show();
                return false;

            }else if(selecttype != null){

                plan_type_split = selecttype.split("%");

                if(plan_type_split[1]=="periodbased"){

                    var rpMeal=$(".rp"+key_check_modified+" option:selected").val();

                    if(rpMeal==0 || rpMeal=="0"){
                        toastr.error("Please Select Plan Days for "+check_vals['name']);
                        $('#loading_img').css('display','none');
                        $(this).show();
                        return false;

                    }else {

                        if(rpMeal=="yourChoice"){
                            var tempwkdays = $(".wkday"+key_check_modified).val();

                            if(!tempwkdays){
                                toastr.error("Please Choose Days for Meal "+check_vals['name']);
                                $('#loading_img').css('display','none');
                                $(this).show();
                                return false;
                             }
                        }
                        var s_date=$("#Datepickersingle_"+key_check_modified).val();

                        if(!s_date){
                            toastr.error("Please Select Single Date for Meal "+check_vals['name']);
                            $('#loading_img').css('display','none');
                            $(this).show();
                            return false;
                        }

                    }
                }

                if(plan_type_split[1]=="datebased"){
                    var date_selected = $("#altField"+key_check_modified).val();
                    if(!date_selected){
                       toastr.error("Please select Dates for Meal "+check_vals['name']);
                       $('#loading_img').css('display','none');
                       $(this).show();
                       return false;
                    }
                    else{
                        count=date_selected.split(",");
                        if(count.length!=parseInt(plan_type_split[0])){
                            toastr.error("Order dates should be equal to "+plan_type_split[0]+" for Meal "+check_vals['name']);
                            $('#loading_img').css('display','none');
                            $(this).show();
                            return false;
                        }
                    }
                }
                var i_qt=$("#iq"+key_check_modified+"s").val();

                if(i_qt==0){

                    toastr.error("Please increase quantity for Meal "+check_vals['name']);
                    $('#loading_img').css('display','none');
                    $(this).show();
                    return false;

                }
            }

        });    


    });
     end document on click orderbtn */

});

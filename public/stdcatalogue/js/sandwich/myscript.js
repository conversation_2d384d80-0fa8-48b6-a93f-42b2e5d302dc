var viewportWidth;
var viewportHeight;
function get_window_width() {
  viewportWidth = $(window).width();
  viewportHeight = $(window).height();
}

function change_dom_position() {
  if (viewportWidth <= 767) {
    $(".sub-total").each(function () {
      $(this).insertAfter(
        $(this).closest(".cart_items").find(".add-extra-btn")
      );
      $(this).prepend($(this).closest(".cart_items").find(".set-pref"));
    });
    $(".plan-dropdown1").each(function () {
      $(this).insertAfter($(this).closest(".cart_items").find(".sub-total"));
    });
  } else {
    //        $(".sub-total1").each(function(){
    //            $(this).insertAfter($(this).closest(".cart_items").find('.cart-count'));
    //        });
    $(".plan-dropdown1").each(function () {
      $(this).insertBefore($(this).closest(".cart_items").find(".sub-total"));
    });
  }
}

$(document).ready(function () {
  // popover
  $('[data-toggle="popover"]').popover();

  // rounded progress bar
  $(".progress-bar").loading();
  // remove "about-info-remove" class when window width is less than 767px

  $(".slide-nav li a:first").addClass("active");

  // multi select box
  $(".js-example-basic-multiple").select2();

  // remove "about-info-remove" class when window width is less than 767px
  $(window).resize(function () {
    var viewportWidth = $(window).width();
    if (viewportWidth <= 767) {
      $("#about-info").removeClass("about-info-remove");
      $("#about-info").addClass("about-info-add");
      $(".sub-head").removeClass("sub-head-mrtp");
    } else {
      $("#about-info").addClass("about-info-remove");
      $("#about-info").removeClass("about-info-add");
      $(".sub-head").addClass("sub-head-mrtp");
    }
    if (viewportWidth <= 991) {
      $("#step-border").removeClass("step-border-bottom");
    } else {
      $("#step-border").addClass("step-border-bottom");
    }
  });

  $(".slide-nav li a:first").addClass("active");

  // modal box

  /*
	$('#location, #show-loc').click(function() {
		$('#myModal').modal('show');
	});

        //loader
	$('.loader-div').delay(1000).fadeOut();

	$('.weekly-menu').click(function() {
		$('#myModal2').modal('show');
		return false;
	});
	

	$('.view-item').click(function() {
		$('#myModal3').modal('show');
		return false;
	});

	$('.set-pref').click(function() {
		$('#myModal6').modal('show');
		return false;
	});
	
	$('.view-info').click(function() {
		$('#myModal3').modal('show');
		return false;
	});
	
	$('.change-meal-btn').click(function() {
		$('#myModal8').modal('show');
		return false;
	});
	
	$('.modify-order-btn').click(function() {
		$('#myModal9').modal('show');
		return false;
	});*/

  $(".rating").click(function () {
    $("#myModal4").modal("show");
    return false;
  });

  $(".share-link").click(function () {
    $("#myModal5").modal("show");
    return false;
  });

  //        $('.unsubscribe-btn').click(function() {
  //		$('#myModal10').modal('show');
  //		return false;
  //	});

  //        $('.promo-modal-btn').click(function() {
  //		$('#myModal11').modal('show');
  //		return false;
  //	});

  //        $('.add-extra-btn').click(function() {
  //		$('#myModal7').modal('show');
  //		return false;
  //	});

  //secondary header
  $(".dropdown-menu").on("click", "a", function () {
    $('#myTabs a[href="#' + $(this).attr("class") + '"]').tab("show");
  });

  $(window).scroll(function () {
    if ($(this).scrollTop() > 0) {
      $("ul#myTabs").css("box-shadow", "0 2px 5px 0 rgba(0, 0, 0, 0.2)");
      $(".sub-head").removeClass("sub-head-mrtp");
    } else {
      $("ul#myTabs").css("box-shadow", "none");
      $(".sub-head").addClass("sub-head-mrtp");
    }
  });

  $("#serch-btn").click(function () {
    $(".dropdown-search").toggle();
  });

  $(".signin-button").click(function () {
    $("#signup").removeClass("hidden");
    $("#login").addClass("hidden");
    $(".signin-button").addClass("bg-col");
    $(".login-button").removeClass("bg-col");
  });

  $(".login-button").click(function () {
    $("#login").removeClass("hidden bg-col");
    $("#guest").addClass("hidden");
    $(".login-button").addClass("bg-col");
    $(".guest-button").removeClass("bg-col");
  });

  $(".guest-button").click(function () {
    $("#guest").removeClass("hidden");
    $("#login").addClass("hidden");
    $(".guest-button").addClass("bg-col");
    $(".login-button").removeClass("bg-col");
  });

  $(".log-button").click(function () {
    $("#log").removeClass("hidden bg-col");
    $("#guest").addClass("hidden");
    $(".log-button").addClass("bg-col");
    $(".guest-button").removeClass("bg-col");
  });

  $(".filter button").on("click", function () {
    $(".filter button").removeClass("active-filter");
    $(this).addClass("active-filter");
  });

  $('input[type="checkbox"]').click(function () {
    if ($(this).attr("value") == "lunch") {
      $(".lunch-field").toggle();
    }
    if ($(this).attr("value") == "dinner") {
      $(".dinner-field").toggle();
    }
  });

  $(".panel-title").click(function () {
    $("this").addClass("open-payment-mode");
  });

  $(".panel-heading .panel-title").on("click", function () {
    $(".panel-heading .panel-title").removeClass("open-payment-mode");
    $(this).addClass("open-payment-mode");
  });

  // cart page //

  $("#flip1").change(function () {
    $("#cart-panel1").slideDown("slow");
    $(".done-up1").show();
  });

  $(".close-panel1").click(function () {
    $("#cart-panel1").slideUp("slow");
    $(".done-down1").show();
    $(".done-up1").hide();
  });

  $(".done-down1").click(function () {
    $("#cart-panel1").slideDown("slow");
    $(".done-down1").hide();
    $(".done-up1").show();
  });

  $("#flip2").change(function () {
    $("#cart-panel2").slideDown("slow");
    $(".done-up2").show();
  });

  // end cart page //

  $("ul#myTabs li a").click(function (e) {
    e.preventDefault();
    $("a").removeClass("active");
    $(this).addClass("active");
  });

  $("ul#myTabs li a").click(function (e) {
    e.preventDefault();
    $("li").removeClass("active");
    $(this).addClass("active");
  });

  $(".slide-nav li a").click(function (e) {
    e.preventDefault();
    $("a").removeClass("active");
    $(this).addClass("active");
  });

  $(".done-up3, .itm-in-crt-btn").click(function () {
    $(".itm-in-crt-panel").toggle();
    $(".done-up3").toggleClass("ti-angle-down");
  });

  $("#wallet-edit-btn").click(function () {
    $(".wallet-inp").slideDown("slow");
    $("#wallet-edit-btn").hide();
    $("#wallet-update-btn").show();
  });

  $("#wallet-update-btn").click(function () {
    $(".wallet-inp").slideUp("hide");
    $("#wallet-update-btn").hide();
    $("#wallet-edit-btn").show();
  });

  $(".rating1 > span").click(function () {
    $(".rating1 > span").removeClass("rate-act");
    $(this).nextAll().andSelf().addClass("rate-act");
  });

  $(".profile-edit").click(function () {
    $(".hidden-field input, .hidden-field .select, .upadate-panel").show();
    $(
      ".profile-edit, .pro-title, .open-otp-field input, .open-otp-field button"
    ).hide();
  });

  $(".cancel-btn1").click(function () {
    $(".hidden-field input, .hidden-field .select, .upadate-panel").hide();
    $(".profile-edit, .pro-title").show();
  });

  // datepicker
  $(function () {
    $(".datepicker").datepicker();
  });

  function carousal(
    class_name,
    item_no,
    speed,
    item_pg,
    nav_0,
    nav_767,
    nav_992,
    nav_1200
  ) {
    $(class_name).owlCarousel({
      items: item_no,
      margin: 15,
      autoplay: true,
      autoplayHoverPause: true,
      loop: true,
      margin: 15,
      padding: 10,
      responsiveClass: true,
      autoplayTimeout: speed,
      dots: item_pg,
      navText: [
        '<span class="ti-angle-left"></span>',
        '<span class="ti-angle-right"></span>',
      ],
      responsive: {
        0: {
          items: 1,
          nav: nav_0,
        },
        767: {
          items: 2,
          nav: nav_767,
        },
        992: {
          items: 3,
          nav: true,
        },
        1200: {
          items: item_no,
          nav: nav_1200,
          loop: true,
        },
      },
    });
  }

  carousal(".owl-carousel-cart", 4, 3000, false, false, false, true, true);
  carousal(".owl-carousel-offer", 3, 3000, false, false, false, true, true);
  carousal(
    ".owl-carousel-client",
    4,
    3000,
    true,
    false,
    false,
    false,
    false,
    false
  );

  function fix_order() {
    var footerht = $("footer").innerHeight();

    if ($(".sub-footer").length > 0) {
      // if ($('.sub-footer').offset().top + $('.sub-footer').height() >= $('footer').offset().top - 10) {
      //
      // }

      if ($(window).height() < $("footer").offset().top) {
        $(".sub-footer").css("position", "fixed");
        $(".sub-footer").css("bottom", 0 + "px");
        // restore when you scroll up
      } else {
        $(".sub-footer").css("position", "relative");
        //$('.sub-footer').css('bottom', footerht +'px');
      }
    }
  }

  fix_order();
  $(window).on("scroll", function () {
    fix_order();
  });
});

//function refresh() { location.reload(); }

// side panel //
var cnt = 1;
function openNav() {
  if (cnt % 2 == 0) {
    $("body").addClass("menu-open");
    document.getElementById("mySidenav").style.width = "250px";
    document.getElementById("main").style.marginLeft = "250px";
  } else {
    $("body").removeClass("menu-open");
    document.getElementById("mySidenav").style.width = "0";
    document.getElementById("main").style.marginLeft = "0";
  }
  cnt++;
}
// end side panel //

function iconbar(x) {
  x.classList.toggle("change");
}
function common_apply_css() {
  if ($(window).width() > 992) {
    //$('.list-view-panel.ps-fx .center-panel').css('margin-left',($('.list-view-panel').width()/4)+10);
    $(".list-view-panel.ps-fx .center-panel").css(
      "margin-left",
      ($(".list-view-panel").width() - $(".side-cart").width()) / 4 + 80 + "px"
    );
  }

  $(".list-view-panel.ps-fx #ct-panel").width(
    $(".list-view-panel .center-panel").width()
  );
  $(".list-view-panel.ps-fx #myScrollspy").width(
    $(".list-view-panel").width() / 4 - 22 + "px"
  );

  //$('.list-view-panel.ps-fx #ct-panel').width($('.list-view-panel .center-panel').width());
  //$('.list-view-panel.ps-fx #myScrollspy').width(((($('.list-view-panel').width()-$('.side-cart').width())/4)-22)+'px');
}
// cart side panel
function openCart() {
  $("body").addClass("cart-open");

  //$('.list-view-panel.ps-fx .center-panel').css('margin-left',((($('.list-view-panel').width()-$('.side-cart').width())/4)+6)+'px');
  //$('.list-view-panel.ps-fx #ct-panel').width($('.list-view-panel .center-panel').width()-226);
  //$('.list-view-panel.ps-fx #myScrollspy').width(((($('.list-view-panel').width()-$('.side-cart').width())/4)-22)+'px');
  timer2 = setTimeout(function () {
    common_apply_css();
  }, 500);
}
function closeCart() {
  $("body").removeClass("cart-open");
  timer2 = setTimeout(function () {
    common_apply_css();
  }, 500);
}
// list view //
function anchor_click() {
  $(".slide-nav li a").click(function () {
    var id_sec = $(this).attr("href");

    if ($(window).width() < 767) {
      $("html, body").animate(
        {
          scrollTop: $(id_sec).offset().top - 94,
        },
        1000
      );
    } else {
      $("html, body").animate(
        {
          scrollTop: $(id_sec).offset().top,
        },
        1000
      );
    }
    return false;
  });
}
/*asif*/
function view_cart_dropdown() {
  $(".mobile-dropdown li a").click(function () {
    $(this).parent().parent().dropdown("toggle");

    $(this)
      .parent()
      .parent()
      .prev()
      .html($(this).text() + ' <span class="caret"></span> ');
    $(".slide-nav li:eq(" + $(this).parent("li").index() + ") a").trigger(
      "click"
    );
    return false;
  });
}
function make_active() {
  var leftwidth = $(".list-view-panel #myScrollspy").width();
  var middlewidth = $(".list-view-panel .center-panel").width();
  var rightwidth = $(".list-view-panel #your-order-panel").width();
  //console.log(leftwidth);
  $(window).bind("scroll", function (e) {
    if ($(".list-view-panel").length == 1) {
      var top =
        (document.documentElement && document.documentElement.scrollTop) ||
        document.body.scrollTop;
      /*active tabs*/
      $(".sec-top").each(function () {
        if (top >= $(this).offset().top) {
          //console.log($(this).attr('id'));
          $(".slide-nav li a").removeClass("active");

          $(".slide-nav li a[href=#" + $(this).attr("id") + "]").addClass(
            "active"
          );
        }
      });
      /*active tabs ends*/
      //			if ($(window).width() < 992){
      //
      //				if (top > $('.dropdown-parent-category').offset().top) {
      //					//alert();
      //				}
      //			}

      if ($(window).width() > 0) {
        if (top > $(".list-view-panel").offset().top) {
          $(".list-view-panel").addClass("ps-fx");
          common_apply_css();
          $(".list-view-panel.ps-fx #your-order-panel").width(rightwidth);
        }
        if (top < $(".list-view-panel").offset().top) {
          $(".list-view-panel").removeClass("ps-fx");
          //$('.list-view-panel #myScrollspy,.list-view-panel .center-panel,.list-view-panel.ps-fx #your-order-panel').removeAttr('style');
          $(
            ".list-view-panel #myScrollspy,.list-view-panel .center-panel,.list-view-panel.ps-fx #ct-panel,.list-view-panel.ps-fx #your-order-panel"
          ).removeAttr("style");
        }
      }
    }
  });
}

// responsive foo-table //
function footable_call() {
  $(".bh-table").footable({});
  $(".preorder-table").footable();
  $(".unbilledorder-table").footable();
  $(".cancelledorder-table").footable();
  $(".deliveredorder-table").footable();

  $(".foo-table").footable({
    expandFirst: true,
    columns: [
      {
        name: "date",
        title: "Date",
      },
      {
        name: "description",
        title: "Description",
      },
      {
        name: "amount",
        title: "Amount",
      },
      {
        name: "crdr",
        title: "Credit/Debit",
        breakpoints: "xs sm",
      },
      {
        name: "paymentmode",
        title: "Payment Mode",
        breakpoints: "xs sm",
      },
      {
        name: "transactedby",
        title: "Transacted By",
        breakpoints: "xs sm",
      },
    ],
    rows: [
      {
        date: "20/12/16",
        description: "Lorem ipsum dolor sit",
        amount: 200,
        crdr: "Credit",
        paymentmode: "Cash",
        transactedby: "Lorem ipsum",
      },
      {
        date: "20/12/16",
        description: "Lorem ipsum dolor sit",
        amount: 200,
        crdr: "Credit",
        paymentmode: "Cash",
        transactedby: "Lorem ipsum",
      },
      {
        date: "20/12/16",
        description: "Lorem ipsum dolor sit",
        amount: 200,
        crdr: "Credit",
        paymentmode: "Cash",
        transactedby: "Lorem ipsum",
      },
      {
        date: "20/12/16",
        description: "Lorem ipsum dolor sit",
        amount: 200,
        crdr: "Credit",
        paymentmode: "Cash",
        transactedby: "Lorem ipsum",
      },
    ],
  });
}

function add_arrow() {
  $(".tp-leftarrow").append('<span class="ti-angle-left"></span>');
  $(".tp-rightarrow").append('<span class="ti-angle-right"></span>');
}

function call_slider() {
  if ($.fn.cssOriginal != undefined) $.fn.css = $.fn.cssOriginal;
  var api = $(".fullwidthbanner").revolution({
    delay: 5000,
    startwidth: 1170,
    startheight: 600,
    soloArrow: "false",
    onHoverStop: "on", // Stop Banner Timet at Hover on Slide on/off
    thumbWidth: 100, // Thumb With and Height and Amount (only if navigation Tyope set to thumb !)
    thumbHeight: 50,
    thumbAmount: 3,
    hideThumbs: 200,
    navigationType: "none", // bullet, thumb, none
    navigationStyle: "round", // round,square,navbar,round-old,square-old,navbar-old, or any from the list in the docu (choose between 50+ different item), custom
    navigationHAlign: "center", // Vertical Align top,center,bottom
    navigationVAlign: "bottom", // Horizontal Align left,center,right
    navigationHOffset: 30,
    navigationVOffset: -40,
    soloArrowLeftHalign: "left",
    soloArrowLeftValign: "center",
    soloArrowLeftHOffset: 0,
    soloArrowLeftVOffset: 0,
    soloArrowRightHalign: "right",
    soloArrowRightValign: "center",
    soloArrowRightHOffset: 0,
    soloArrowRightVOffset: 0,
    touchenabled: "on", // Enable Swipe Function : on/off
    stopAtSlide: -1, // Stop Timer if Slide "x" has been Reached. If stopAfterLoops set to 0, then it stops already in the first Loop at slide X which defined. -1 means do not stop at any slide. stopAfterLoops has no sinn in this case.
    stopAfterLoops: -1, // Stop Timer if All slides has been played "x" times. IT will stop at THe slide which is defined via stopAtSlide:x, if set to -1 slide never stop automatic
    hideCaptionAtLimit: 0, // It Defines if a caption should be shown under a Screen Resolution ( Basod on The Width of Browser)
    hideAllCaptionAtLilmit: 0, // Hide all The Captions if Width of Browser is less then this value
    hideSliderAtLimit: 0, // Hide the whole slider, and stop also functions if Width of Browser is less than this value
    fullWidth: "on",
    forceFullWidth: "on",
    shadow: 0, //0 = no Shadow, 1,2,3 = 3 Different Art of Shadows -  (No Shadow in Fullwidth Version !)
    dots: "true",
  });
  api.bind("revolution.slide.onloaded", function (e, data) {
    add_arrow();
  });
}
var cnt = 0;
function order_timing() {
  var top =
    (document.documentElement && document.documentElement.scrollTop) ||
    document.body.scrollTop;
  //console.log(top + "  "+ $('.order-timing-box').offset().top);
  if ($("#order-timing").length == 1) {
    if (top > $("#order-timing").position().top - 600) {
      if (cnt < 1) {
        console.log(top + "  " + $("#order-timing").offset().top);
        $(".progress-bar").loading();
        cnt++;
      }
    }
  }
}
function open_modal() {
  $(document).on("click", ".foo-modal-open", function () {
    alert("modal open");
  });
}
$(document).ready(function () {
  get_window_width();
  open_modal();
  view_cart_dropdown();
  anchor_click();
  make_active();
  //footable_call();
  call_slider();
  order_timing();
  new WOW().init();
  change_dom_position();
});

$(window).on("scroll", function () {
  order_timing();
});

$(window).resize(function () {
  get_window_width();
  change_dom_position();
  if (viewportWidth <= 767) {
    $("#about-info").removeClass("about-info-remove");
    $("#about-info").addClass("about-info-add");
    $(".sub-head").removeClass("sub-head-mrtp");
  } else {
    $("#about-info").addClass("about-info-remove");
    $("#about-info").removeClass("about-info-add");
    $(".sub-head").addClass("sub-head-mrtp");
  }
  if (viewportWidth <= 991) {
    $("#step-border").removeClass("step-border-bottom");
  } else {
    $("#step-border").addClass("step-border-bottom");
  }
});

/*Anand*/
/*Back button in header changes*/

$(document).ready(function () {
  var currentURL = window.location.href;

  var logo = $("#logo");
  var arrow = $(".left-arrow");

  logo.show();
  arrow.hide();

  if (
    currentURL == "https://foodmonks.ca/cart" ||
    currentURL == "https://foodmonks.ca/menu" ||
    currentURL == "https://foodmonks.ca/cart/payment" ||
    currentURL == "https://foodmonks.ca/customers/index" ||
    currentURL == "https://foodmonks.cubeonebiz.com/customers/index" ||
    currentURL == "https://foodmonks.cubeonebiz.com/cart" ||
    currentURL == "https://foodmonks.cubeonebiz.com/menu" ||
    currentURL == "https://foodmonks.cubeonebiz.com/cart/payment"
  ) {
    logo.hide();
    arrow.show();
  }
});

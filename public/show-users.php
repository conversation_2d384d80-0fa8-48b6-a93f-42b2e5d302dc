<?php
/**
 * This script displays the available users in the mock database
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';
    
    // Initialize Zend Application
    $application = Zend\Mvc\Application::init($config);
    
    // Get service manager
    $serviceManager = $application->getServiceManager();
    
    // Get database adapter
    $dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');
    
    // Display available users
    echo '<h1>Available Users in Mock Database</h1>';
    
    try {
        // Query the users table
        $sql = 'SELECT * FROM users';
        $statement = $dbAdapter->query($sql);
        $results = $statement->execute();
        
        if ($results->count() > 0) {
            echo '<table border="1" cellpadding="5">';
            echo '<tr><th>ID</th><th>Email</th><th>First Name</th><th>Last Name</th><th>Role ID</th><th>Status</th><th>Password (for testing)</th></tr>';
            
            foreach ($results as $row) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($row['pk_user_code']) . '</td>';
                echo '<td>' . htmlspecialchars($row['email_id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['first_name']) . '</td>';
                echo '<td>' . htmlspecialchars($row['last_name']) . '</td>';
                echo '<td>' . htmlspecialchars($row['role_id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['status']) . '</td>';
                echo '<td>' . htmlspecialchars($row['password']) . '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
            
            echo '<h2>Test Credentials</h2>';
            echo '<p>You can use the following credentials to test the login:</p>';
            echo '<ul>';
            foreach ($results as $row) {
                if ($row['status'] == 1) {
                    echo '<li><strong>Email:</strong> ' . htmlspecialchars($row['email_id']) . ', <strong>Password:</strong> ' . htmlspecialchars($row['password']) . '</li>';
                }
            }
            echo '</ul>';
            
            echo '<p>Note: In development mode, the password is not hashed, so you can use the password as-is.</p>';
        } else {
            echo '<p>No users found in the database.</p>';
            
            // Create a default admin user
            echo '<h2>Creating Default Admin User</h2>';
            
            $sql = "INSERT INTO users (pk_user_code, email_id, password, first_name, last_name, role_id, status) 
                    VALUES (1, '<EMAIL>', 'admin123', 'Admin', 'User', 1, 1)";
            
            try {
                $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
                echo '<p>Default admin user created successfully.</p>';
                echo '<p>You can now use the following credentials to log in:</p>';
                echo '<ul>';
                echo '<li><strong>Email:</strong> <EMAIL>, <strong>Password:</strong> admin123</li>';
                echo '</ul>';
            } catch (\Exception $e) {
                echo '<p>Error creating default admin user: ' . $e->getMessage() . '</p>';
            }
        }
    } catch (\Exception $e) {
        echo '<p>Error querying users table: ' . $e->getMessage() . '</p>';
        
        // Check if the users table exists
        echo '<h2>Checking Database Schema</h2>';
        
        try {
            $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='users'";
            $statement = $dbAdapter->query($sql);
            $results = $statement->execute();
            
            if ($results->count() > 0) {
                echo '<p>The users table exists in the database.</p>';
                
                // Check the structure of the users table
                $sql = "PRAGMA table_info(users)";
                $statement = $dbAdapter->query($sql);
                $columns = $statement->execute();
                
                echo '<p>Columns in the users table:</p>';
                echo '<ul>';
                foreach ($columns as $column) {
                    echo '<li>' . $column['name'] . ' (' . $column['type'] . ')</li>';
                }
                echo '</ul>';
            } else {
                echo '<p>The users table does not exist in the database.</p>';
                
                // Create the users table
                echo '<h2>Creating Users Table</h2>';
                
                $sql = "CREATE TABLE users (
                    pk_user_code INTEGER PRIMARY KEY,
                    email_id TEXT,
                    password TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    phone TEXT,
                    gender TEXT,
                    city TEXT,
                    role_id INTEGER,
                    status INTEGER,
                    third_party_id TEXT
                )";
                
                try {
                    $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
                    echo '<p>Users table created successfully.</p>';
                    
                    // Create a default admin user
                    $sql = "INSERT INTO users (pk_user_code, email_id, password, first_name, last_name, role_id, status) 
                            VALUES (1, '<EMAIL>', 'admin123', 'Admin', 'User', 1, 1)";
                    
                    $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
                    echo '<p>Default admin user created successfully.</p>';
                    echo '<p>You can now use the following credentials to log in:</p>';
                    echo '<ul>';
                    echo '<li><strong>Email:</strong> <EMAIL>, <strong>Password:</strong> admin123</li>';
                    echo '</ul>';
                } catch (\Exception $e) {
                    echo '<p>Error creating users table: ' . $e->getMessage() . '</p>';
                }
            }
        } catch (\Exception $e) {
            echo '<p>Error checking database schema: ' . $e->getMessage() . '</p>';
        }
    }
} catch (\Exception $e) {
    echo '<h1>Error</h1>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

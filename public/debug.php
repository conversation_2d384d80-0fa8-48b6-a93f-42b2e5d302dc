<?php
/**
 * Debug Menu
 * 
 * This script provides access to various debug tools
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php')) {
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php';
    \Lib\QuickServe\Env\EnvLoader::load();
}

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = false;
$userRole = '';
$userName = '';

if (isset($_SESSION['user']) && is_array($_SESSION['user'])) {
    $isLoggedIn = true;
    $userRole = isset($_SESSION['user']['rolename']) ? $_SESSION['user']['rolename'] : '';
    $userName = isset($_SESSION['user']['first_name']) ? $_SESSION['user']['first_name'] . ' ' . $_SESSION['user']['last_name'] : '';
} elseif (isset($_SESSION['storage'])) {
    try {
        $storage = $_SESSION['storage'];
        
        if (is_object($storage) && isset($storage->pk_user_code)) {
            $isLoggedIn = true;
            $userRole = isset($storage->rolename) ? $storage->rolename : '';
            $userName = isset($storage->first_name) ? $storage->first_name . ' ' . $storage->last_name : '';
        }
    } catch (\Exception $e) {
        // Ignore
    }
}

// Check if user has admin role
$isAdmin = $isLoggedIn && ($userRole === 'Admin' || $userRole === 'admin');

// Check if this is a development environment
$isDevelopment = true;
if (isset($_SERVER['DEVELOPMENT_MODE'])) {
    $isDevelopment = $_SERVER['DEVELOPMENT_MODE'] === 'true';
} elseif (function_exists('\Lib\QuickServe\Env\EnvLoader::get')) {
    $isDevelopment = \Lib\QuickServe\Env\EnvLoader::get('DEVELOPMENT_MODE', 'true') === 'true';
}

// Only allow access to debug menu in development mode or for admin users
if (!$isDevelopment && !$isAdmin) {
    header('HTTP/1.1 403 Forbidden');
    echo '<h1>Access Denied</h1>';
    echo '<p>You do not have permission to access the debug menu.</p>';
    exit;
}

// Get system information
$systemInfo = [
    'php_version' => PHP_VERSION,
    'server_software' => isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : 'Unknown',
    'server_name' => isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'Unknown',
    'server_port' => isset($_SERVER['SERVER_PORT']) ? $_SERVER['SERVER_PORT'] : 'Unknown',
    'document_root' => isset($_SERVER['DOCUMENT_ROOT']) ? $_SERVER['DOCUMENT_ROOT'] : 'Unknown',
    'request_time' => isset($_SERVER['REQUEST_TIME']) ? date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']) : 'Unknown',
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'session_id' => session_id()
];

// Get database information
$dbInfo = [
    'status' => 'Unknown',
    'file' => 'Unknown',
    'tables' => []
];

try {
    $dbFile = APPLICATION_PATH . '/data/db/mock.sqlite';
    
    if (file_exists($dbFile)) {
        $dbInfo['status'] = 'Available';
        $dbInfo['file'] = $dbFile;
        
        $pdo = new \PDO('sqlite:' . $dbFile);
        $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
        
        // Get tables
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
        $tables = $stmt->fetchAll(\PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            // Get table info
            $stmt = $pdo->query("PRAGMA table_info({$table})");
            $columns = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Get row count
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $rowCount = $stmt->fetchColumn();
            
            $dbInfo['tables'][$table] = [
                'columns' => count($columns),
                'rows' => $rowCount
            ];
        }
    } else {
        $dbInfo['status'] = 'Not Found';
    }
} catch (\Exception $e) {
    $dbInfo['status'] = 'Error: ' . $e->getMessage();
}

// Get log information
$logInfo = [
    'directory' => APPLICATION_PATH . '/data/logs',
    'files' => []
];

try {
    $logDir = $logInfo['directory'];
    
    if (is_dir($logDir)) {
        $logFiles = glob($logDir . '/*.log');
        
        foreach ($logFiles as $logFile) {
            $fileName = basename($logFile);
            $fileSize = filesize($logFile);
            $lineCount = count(file($logFile));
            
            $logInfo['files'][$fileName] = [
                'size' => $fileSize,
                'size_formatted' => $fileSize < 1024 ? $fileSize . ' B' : ($fileSize < 1048576 ? round($fileSize / 1024, 2) . ' KB' : round($fileSize / 1048576, 2) . ' MB'),
                'lines' => $lineCount,
                'last_modified' => date('Y-m-d H:i:s', filemtime($logFile))
            ];
        }
    } else {
        $logInfo['status'] = 'Directory Not Found';
    }
} catch (\Exception $e) {
    $logInfo['status'] = 'Error: ' . $e->getMessage();
}

// HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Menu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .debug-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .debug-user {
            text-align: right;
        }
        .debug-section {
            margin-bottom: 30px;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .debug-tools {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        .debug-tool {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
        }
        .debug-tool a {
            display: block;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            text-decoration: none;
            margin-bottom: 10px;
        }
        .debug-tool p {
            color: #666;
            margin: 0;
        }
        .debug-info {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .debug-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .debug-table th, .debug-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .debug-table th {
            background-color: #f2f2f2;
        }
        .debug-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="debug-header">
            <div>
                <h1>Debug Menu</h1>
                <p>Development Mode: <?php echo $isDevelopment ? 'Enabled' : 'Disabled'; ?></p>
            </div>
            <div class="debug-user">
                <?php if ($isLoggedIn): ?>
                <p>Logged in as: <?php echo htmlspecialchars($userName); ?></p>
                <p>Role: <?php echo htmlspecialchars($userRole); ?></p>
                <?php else: ?>
                <p>Not logged in</p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="debug-section">
            <h2>Debug Tools</h2>
            <div class="debug-tools">
                <div class="debug-tool">
                    <a href="logs.php">Log Viewer</a>
                    <p>View application logs</p>
                </div>
                <div class="debug-tool">
                    <a href="token-debug.php">Token Debugger</a>
                    <p>Debug authentication tokens</p>
                </div>
                <div class="debug-tool">
                    <a href="navigation.php">Navigation Viewer</a>
                    <p>View navigation history</p>
                </div>
                <div class="debug-tool">
                    <a href="health/quickserve.php">Health Check</a>
                    <p>Check system health</p>
                </div>
                <div class="debug-tool">
                    <a href="db-migrate.php">Database Migration</a>
                    <p>Manage database schema</p>
                </div>
                <div class="debug-tool">
                    <a href="test-quickserve-init.php">Test QuickServe</a>
                    <p>Test QuickServe initialization</p>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h2>System Information</h2>
            <div class="debug-info"><?php echo htmlspecialchars(json_encode($systemInfo, JSON_PRETTY_PRINT)); ?></div>
        </div>
        
        <div class="debug-section">
            <h2>Database Information</h2>
            <p>Status: <?php echo htmlspecialchars($dbInfo['status']); ?></p>
            <p>File: <?php echo htmlspecialchars($dbInfo['file']); ?></p>
            
            <?php if (!empty($dbInfo['tables'])): ?>
            <h3>Tables</h3>
            <table class="debug-table">
                <tr>
                    <th>Table Name</th>
                    <th>Columns</th>
                    <th>Rows</th>
                </tr>
                <?php foreach ($dbInfo['tables'] as $table => $info): ?>
                <tr>
                    <td><?php echo htmlspecialchars($table); ?></td>
                    <td><?php echo htmlspecialchars($info['columns']); ?></td>
                    <td><?php echo htmlspecialchars($info['rows']); ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
            <?php endif; ?>
        </div>
        
        <div class="debug-section">
            <h2>Log Information</h2>
            <p>Directory: <?php echo htmlspecialchars($logInfo['directory']); ?></p>
            
            <?php if (!empty($logInfo['files'])): ?>
            <h3>Log Files</h3>
            <table class="debug-table">
                <tr>
                    <th>File Name</th>
                    <th>Size</th>
                    <th>Lines</th>
                    <th>Last Modified</th>
                    <th>Actions</th>
                </tr>
                <?php foreach ($logInfo['files'] as $file => $info): ?>
                <tr>
                    <td><?php echo htmlspecialchars($file); ?></td>
                    <td><?php echo htmlspecialchars($info['size_formatted']); ?></td>
                    <td><?php echo htmlspecialchars($info['lines']); ?></td>
                    <td><?php echo htmlspecialchars($info['last_modified']); ?></td>
                    <td>
                        <?php if (strpos($file, 'auth') !== false): ?>
                        <a href="logs.php?type=auth">View</a>
                        <?php elseif (strpos($file, 'token') !== false): ?>
                        <a href="logs.php?type=token">View</a>
                        <?php elseif (strpos($file, 'navigation') !== false): ?>
                        <a href="logs.php?type=navigation">View</a>
                        <?php elseif (strpos($file, 'error') !== false): ?>
                        <a href="logs.php?type=error">View</a>
                        <?php else: ?>
                        <a href="logs.php">View</a>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </table>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

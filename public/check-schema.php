<?php
/**
 * Database Schema Check Script
 * 
 * This script checks the schema of the users table
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Include Zend autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Get application configuration
$config = include APPLICATION_PATH . '/config/application.config.php';
$dbConfig = include APPLICATION_PATH . '/config/autoload/database.local.php';

echo "<h1>Database Schema Check</h1>";

try {
    // Create PDO connection
    $dsn = 'sqlite:' . APPLICATION_PATH . '/data/db/tenant.db';
    $pdo = new PDO($dsn);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if users table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
    $tableExists = $stmt->fetchColumn();
    
    if (!$tableExists) {
        echo "<p>Error: The 'users' table does not exist.</p>";
        exit;
    }
    
    // Get table schema
    $stmt = $pdo->query("PRAGMA table_info(users)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Users Table Schema</h2>";
    echo "<table border='1'>";
    echo "<tr><th>CID</th><th>Name</th><th>Type</th><th>NotNull</th><th>Default</th><th>PK</th></tr>";
    
    $hasAuthTokenColumn = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['cid'] . "</td>";
        echo "<td>" . $column['name'] . "</td>";
        echo "<td>" . $column['type'] . "</td>";
        echo "<td>" . $column['notnull'] . "</td>";
        echo "<td>" . $column['dflt_value'] . "</td>";
        echo "<td>" . $column['pk'] . "</td>";
        echo "</tr>";
        
        if ($column['name'] === 'auth_token') {
            $hasAuthTokenColumn = true;
        }
    }
    
    echo "</table>";
    
    if (!$hasAuthTokenColumn) {
        echo "<p>The 'auth_token' column does not exist in the 'users' table.</p>";
        echo "<h3>Adding auth_token column</h3>";
        
        // Add auth_token column
        try {
            $pdo->exec("ALTER TABLE users ADD COLUMN auth_token TEXT");
            echo "<p>Successfully added 'auth_token' column to 'users' table.</p>";
        } catch (PDOException $e) {
            echo "<p>Error adding 'auth_token' column: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>The 'auth_token' column exists in the 'users' table.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>Database Error: " . $e->getMessage() . "</p>";
}

<?php
/**
 * Auth Logs Viewer
 *
 * This is a standalone script to view authentication and navigation logs
 * without requiring the full Zend Framework.
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Check if user is logged in as admin
$isAdmin = false;
if (isset($_SESSION['storage']) && is_object($_SESSION['storage'])) {
    if (isset($_SESSION['storage']->rolename) && $_SESSION['storage']->rolename === 'Admin') {
        $isAdmin = true;
    }
} elseif (isset($_SESSION['user']) && is_array($_SESSION['user'])) {
    if (isset($_SESSION['user']['rolename']) && $_SESSION['user']['rolename'] === 'Admin') {
        $isAdmin = true;
    }
}

// Redirect non-admin users
if (!$isAdmin) {
    header('Location: /auth');
    exit;
}

// Define log directory
$logDir = realpath(dirname(__FILE__) . '/../data/logs');

// Create log directory if it doesn't exist
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Define log types
$logTypes = [
    'auth' => 'auth.log',
    'navigation' => 'navigation.log',
    'token' => 'token.log',
    'error' => 'error.log'
];

// Get log type from query parameter
$logType = isset($_GET['type']) && isset($logTypes[$_GET['type']]) ? $_GET['type'] : 'auth';
$limit = isset($_GET['limit']) ? (int) $_GET['limit'] : 100;
$offset = isset($_GET['offset']) ? (int) $_GET['offset'] : 0;

// Handle clear logs action
if (isset($_GET['action']) && $_GET['action'] === 'clear' && isset($_GET['type']) && isset($logTypes[$_GET['type']])) {
    $logFile = $logDir . '/' . $logTypes[$_GET['type']];
    if (file_exists($logFile)) {
        file_put_contents($logFile, '');
        header('Location: /auth-logs.php?type=' . $_GET['type'] . '&cleared=1');
        exit;
    }
}

// Get logs
$logs = [];
$logFile = $logDir . '/' . $logTypes[$logType];

if (file_exists($logFile)) {
    $lines = file($logFile);

    // Apply offset and limit
    $lines = array_slice($lines, $offset, $limit);

    foreach ($lines as $line) {
        $logs[] = json_decode($line, true);
    }

    // Reverse logs to show newest first
    $logs = array_reverse($logs);
}

// HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>Auth Logs Viewer</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .container {
            max-width: 1200px;
        }
        pre {
            max-height: 400px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>Auth Logs Viewer</h1>
        </div>

        <?php if (isset($_GET['cleared']) && $_GET['cleared'] === '1'): ?>
            <div class="alert alert-success">
                Logs cleared successfully.
            </div>
        <?php endif; ?>

        <div class="panel panel-default">
            <div class="panel-heading">
                <div class="row">
                    <div class="col-md-6">
                        <h3 class="panel-title">
                            <?php echo ucfirst($logType); ?> Logs
                        </h3>
                    </div>
                    <div class="col-md-6 text-right">
                        <div class="btn-group">
                            <?php foreach ($logTypes as $type => $file): ?>
                                <a href="?type=<?php echo $type; ?>" class="btn btn-sm <?php echo $type === $logType ? 'btn-primary' : 'btn-default'; ?>">
                                    <?php echo ucfirst($type); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        <a href="?type=<?php echo $logType; ?>&action=clear" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to clear all <?php echo $logType; ?> logs?');">
                            Clear Logs
                        </a>
                    </div>
                </div>
            </div>
            <div class="panel-body">
                <?php if (empty($logs)): ?>
                    <div class="alert alert-info">
                        No logs found.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>User ID</th>
                                    <th>IP Address</th>
                                    <th>Message</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td><?php echo isset($log['timestamp']) ? $log['timestamp'] : 'N/A'; ?></td>
                                        <td><?php echo isset($log['user_id']) ? $log['user_id'] : 'N/A'; ?></td>
                                        <td><?php echo isset($log['ip']) ? $log['ip'] : 'N/A'; ?></td>
                                        <td><?php echo isset($log['message']) ? $log['message'] : 'N/A'; ?></td>
                                        <td>
                                            <button class="btn btn-xs btn-info view-details" data-details='<?php echo json_encode(isset($log['data']) ? $log['data'] : []); ?>'>
                                                View Details
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-inline">
                                <div class="form-group">
                                    <label for="limit">Limit:</label>
                                    <select id="limit" class="form-control input-sm">
                                        <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50</option>
                                        <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100</option>
                                        <option value="200" <?php echo $limit === 200 ? 'selected' : ''; ?>>200</option>
                                        <option value="500" <?php echo $limit === 500 ? 'selected' : ''; ?>>500</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="btn-group">
                                <?php if ($offset > 0): ?>
                                    <a href="?type=<?php echo $logType; ?>&limit=<?php echo $limit; ?>&offset=<?php echo max(0, $offset - $limit); ?>" class="btn btn-default btn-sm">
                                        <i class="glyphicon glyphicon-chevron-left"></i> Previous
                                    </a>
                                <?php endif; ?>
                                <?php if (count($logs) >= $limit): ?>
                                    <a href="?type=<?php echo $logType; ?>&limit=<?php echo $limit; ?>&offset=<?php echo $offset + $limit; ?>" class="btn btn-default btn-sm">
                                        Next <i class="glyphicon glyphicon-chevron-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">Navigation</h3>
            </div>
            <div class="panel-body">
                <a href="/auth" class="btn btn-default">Login Page</a>
                <a href="/auth-debug.php" class="btn btn-default">Auth Debug</a>
                <a href="/dashboard" class="btn btn-default">Dashboard</a>
                <a href="/direct-logout.php" class="btn btn-default">Logout</a>
            </div>
        </div>
    </div>

    <!-- Details Modal -->
    <div class="modal fade" id="details-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Log Details</h4>
                </div>
                <div class="modal-body">
                    <pre id="details-content"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // View details button
            $('.view-details').click(function() {
                var details = $(this).data('details');
                $('#details-content').text(JSON.stringify(details, null, 2));
                $('#details-modal').modal('show');
            });

            // Limit change
            $('#limit').change(function() {
                window.location.href = '?type=<?php echo $logType; ?>&limit=' + $(this).val() + '&offset=0';
            });
        });
    </script>
</body>
</html>

<?php
/**
 * Token Debugger
 * 
 * This script helps debug authentication tokens
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php')) {
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php';
    \Lib\QuickServe\Env\EnvLoader::load();
}

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = false;
$userRole = '';

if (isset($_SESSION['user']) && is_array($_SESSION['user'])) {
    $isLoggedIn = true;
    $userRole = isset($_SESSION['user']['rolename']) ? $_SESSION['user']['rolename'] : '';
} elseif (isset($_SESSION['storage'])) {
    try {
        $storage = $_SESSION['storage'];
        
        if (is_object($storage) && isset($storage->pk_user_code)) {
            $isLoggedIn = true;
            $userRole = isset($storage->rolename) ? $storage->rolename : '';
        }
    } catch (\Exception $e) {
        // Ignore
    }
}

// Check if user has admin role
$isAdmin = $isLoggedIn && ($userRole === 'Admin' || $userRole === 'admin');

// Check if this is a development environment
$isDevelopment = true;
if (isset($_SERVER['DEVELOPMENT_MODE'])) {
    $isDevelopment = $_SERVER['DEVELOPMENT_MODE'] === 'true';
} elseif (function_exists('\Lib\QuickServe\Env\EnvLoader::get')) {
    $isDevelopment = \Lib\QuickServe\Env\EnvLoader::get('DEVELOPMENT_MODE', 'true') === 'true';
}

// Only allow access to token debugger in development mode or for admin users
if (!$isDevelopment && !$isAdmin) {
    header('HTTP/1.1 403 Forbidden');
    echo '<h1>Access Denied</h1>';
    echo '<p>You do not have permission to access the token debugger.</p>';
    exit;
}

// Get token from request
$token = isset($_POST['token']) ? $_POST['token'] : '';
$tokenType = isset($_POST['token_type']) ? $_POST['token_type'] : 'jwt';

// Get session token
$sessionToken = '';
if (isset($_SESSION['user']) && is_array($_SESSION['user']) && isset($_SESSION['user']['auth_token'])) {
    $sessionToken = $_SESSION['user']['auth_token'];
} elseif (isset($_SESSION['storage']) && is_object($_SESSION['storage']) && isset($_SESSION['storage']->auth_token)) {
    $sessionToken = $_SESSION['storage']->auth_token;
}

// Process token
$tokenData = [];
$tokenValid = false;
$tokenIssues = [];

if (!empty($token)) {
    if ($tokenType === 'jwt') {
        // Try to decode JWT token
        try {
            // Check if we have the JWT utility
            if (file_exists(APPLICATION_PATH . '/vendor/Lib/QuickServe/Auth/JwtTokenUtil.php')) {
                require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Auth/JwtTokenUtil.php';
                
                // Create JWT utility
                $jwtUtil = new \Lib\QuickServe\Auth\JwtTokenUtil([
                    'jwt_secret' => \Lib\QuickServe\Env\EnvLoader::get('JWT_SECRET', 'quickserve-jwt-secret')
                ]);
                
                // Decode token
                $claims = $jwtUtil->decodeToken($token);
                
                if ($claims) {
                    $tokenData = $claims;
                    $tokenValid = true;
                    
                    // Check for required claims
                    $requiredClaims = ['iss', 'sub', 'exp'];
                    foreach ($requiredClaims as $claim) {
                        if (!isset($claims[$claim])) {
                            $tokenIssues[] = "Missing required claim: {$claim}";
                            $tokenValid = false;
                        }
                    }
                    
                    // Check expiration
                    if (isset($claims['exp'])) {
                        $expiration = $claims['exp'];
                        $now = time();
                        
                        if ($expiration < $now) {
                            $tokenIssues[] = "Token has expired at " . date('Y-m-d H:i:s', $expiration);
                            $tokenValid = false;
                        } elseif ($expiration - $now < 300) { // Less than 5 minutes left
                            $tokenIssues[] = "Token is about to expire at " . date('Y-m-d H:i:s', $expiration);
                        }
                    }
                } else {
                    $tokenIssues[] = "Failed to decode JWT token";
                }
            } else {
                // Manual JWT decoding
                $parts = explode('.', $token);
                if (count($parts) === 3) {
                    $header = json_decode(base64_decode($parts[0]), true);
                    $payload = json_decode(base64_decode($parts[1]), true);
                    
                    if ($header && $payload) {
                        $tokenData = [
                            'header' => $header,
                            'payload' => $payload
                        ];
                        
                        // Check expiration
                        if (isset($payload['exp'])) {
                            $expiration = $payload['exp'];
                            $now = time();
                            
                            if ($expiration < $now) {
                                $tokenIssues[] = "Token has expired at " . date('Y-m-d H:i:s', $expiration);
                            } elseif ($expiration - $now < 300) { // Less than 5 minutes left
                                $tokenIssues[] = "Token is about to expire at " . date('Y-m-d H:i:s', $expiration);
                            }
                        }
                    } else {
                        $tokenIssues[] = "Failed to decode JWT token parts";
                    }
                } else {
                    $tokenIssues[] = "Invalid JWT token format";
                }
            }
        } catch (\Exception $e) {
            $tokenIssues[] = "Error decoding JWT token: " . $e->getMessage();
        }
    } elseif ($tokenType === 'session') {
        // Try to decode session token
        try {
            // For session tokens, we just show the token
            $tokenData = [
                'token' => $token,
                'length' => strlen($token)
            ];
            
            // Check if token exists in database
            if (file_exists(APPLICATION_PATH . '/data/db/mock.sqlite')) {
                $pdo = new \PDO('sqlite:' . APPLICATION_PATH . '/data/db/mock.sqlite');
                $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
                
                $stmt = $pdo->prepare("SELECT * FROM users WHERE auth_token = ?");
                $stmt->execute([$token]);
                $user = $stmt->fetch(\PDO::FETCH_ASSOC);
                
                if ($user) {
                    $tokenData['user'] = [
                        'pk_user_code' => $user['pk_user_code'],
                        'username' => $user['username'],
                        'email_id' => $user['email_id'],
                        'rolename' => $user['rolename'],
                        'auth_type' => $user['auth_type']
                    ];
                    $tokenValid = true;
                } else {
                    $tokenIssues[] = "Token not found in database";
                }
            } else {
                $tokenIssues[] = "Database not available";
            }
        } catch (\Exception $e) {
            $tokenIssues[] = "Error checking session token: " . $e->getMessage();
        }
    }
}

// HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>Token Debugger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .token-form {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .token-form textarea {
            width: 100%;
            height: 100px;
            margin-bottom: 10px;
            font-family: monospace;
        }
        .token-form select, .token-form button {
            padding: 5px 10px;
        }
        .token-form button {
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .token-result {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .token-valid {
            color: #28a745;
            font-weight: bold;
        }
        .token-invalid {
            color: #dc3545;
            font-weight: bold;
        }
        .token-issues {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 3px;
            color: #721c24;
        }
        .token-data {
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .session-token {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9f5ff;
            border: 1px solid #b8daff;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Token Debugger</h1>
        
        <div class="token-form">
            <form method="post">
                <div>
                    <label for="token">Enter Token:</label>
                    <textarea name="token" id="token"><?php echo htmlspecialchars($token); ?></textarea>
                </div>
                <div>
                    <label for="token_type">Token Type:</label>
                    <select name="token_type" id="token_type">
                        <option value="jwt" <?php echo $tokenType === 'jwt' ? 'selected' : ''; ?>>JWT Token</option>
                        <option value="session" <?php echo $tokenType === 'session' ? 'selected' : ''; ?>>Session Token</option>
                    </select>
                    <button type="submit">Decode Token</button>
                </div>
            </form>
        </div>
        
        <?php if (!empty($sessionToken)): ?>
        <div class="session-token">
            <h2>Current Session Token</h2>
            <p>Your current session has the following token:</p>
            <div class="token-data"><?php echo htmlspecialchars($sessionToken); ?></div>
            <form method="post">
                <input type="hidden" name="token" value="<?php echo htmlspecialchars($sessionToken); ?>">
                <input type="hidden" name="token_type" value="session">
                <button type="submit">Decode Session Token</button>
            </form>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($token)): ?>
        <div class="token-result">
            <h2>Token Analysis</h2>
            
            <p>
                Status: 
                <?php if ($tokenValid): ?>
                <span class="token-valid">Valid</span>
                <?php else: ?>
                <span class="token-invalid">Invalid</span>
                <?php endif; ?>
            </p>
            
            <?php if (!empty($tokenIssues)): ?>
            <div class="token-issues">
                <strong>Issues:</strong>
                <ul>
                    <?php foreach ($tokenIssues as $issue): ?>
                    <li><?php echo htmlspecialchars($issue); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($tokenData)): ?>
            <h3>Token Data</h3>
            <div class="token-data"><?php echo htmlspecialchars(json_encode($tokenData, JSON_PRETTY_PRINT)); ?></div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>

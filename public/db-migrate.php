<?php
/**
 * Database Migration Script
 *
 * This script performs a complete database migration, recreating tables if necessary
 */

// Start output buffering
ob_start();

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Create data directory if it doesn't exist
$dataDir = APPLICATION_PATH . '/data';
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Create db directory if it doesn't exist
$dbDir = $dataDir . '/db';
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
}

// Define database file path
$dbFile = $dbDir . '/mock.sqlite';

echo "<html><head><title>Database Migration</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1, h2 { color: #333; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
</style>";
echo "</head><body>";
echo "<h1>Database Migration</h1>";

try {
    // Create PDO connection
    $pdo = new PDO('sqlite:' . $dbFile);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get list of tables
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "<h2>Current Database Tables</h2>";
    if (count($tables) > 0) {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No tables found in database.</p>";
    }

    // Ask for confirmation to recreate tables
    echo "<h2>Migration Actions</h2>";

    $recreateTables = isset($_GET['recreate']) && $_GET['recreate'] === 'true';

    if (!$recreateTables) {
        echo "<p>To recreate all tables, <a href='?recreate=true'>click here</a>.</p>";
        echo "<p class='warning'>Warning: This will delete all existing data!</p>";
    } else {
        echo "<p class='warning'>Recreating all tables. All existing data will be lost!</p>";

        // Drop existing tables
        foreach ($tables as $table) {
            if ($table !== 'sqlite_sequence') {
                $pdo->exec("DROP TABLE IF EXISTS $table");
                echo "<p>Dropped table: $table</p>";
            }
        }

        // Create users table
        echo "<p>Creating users table...</p>";
        $pdo->exec("CREATE TABLE users (
            pk_user_code INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            email_id TEXT,
            password TEXT,
            first_name TEXT,
            last_name TEXT,
            rolename TEXT,
            status INTEGER DEFAULT 1,
            auth_token TEXT,
            auth_type TEXT DEFAULT 'legacy',
            company_id INTEGER DEFAULT 1,
            unit_id INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        echo "<p class='success'>Users table created successfully.</p>";

        // Create activity_log table
        echo "<p>Creating activity_log table...</p>";
        $pdo->exec("CREATE TABLE activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT,
            description TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            modified_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            status INTEGER DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users(pk_user_code)
        )");
        echo "<p class='success'>Activity log table created successfully.</p>";

        // Create orders table
        echo "<p>Creating orders table...</p>";
        $pdo->exec("CREATE TABLE orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_number TEXT,
            user_id INTEGER,
            company_id INTEGER,
            unit_id INTEGER,
            status TEXT DEFAULT 'pending',
            total_amount REAL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(pk_user_code)
        )");
        echo "<p class='success'>Orders table created successfully.</p>";

        // Generate auth tokens
        function generateAuthToken($username, $secret = 'auth-token-secret') {
            return md5($username . time() . $secret);
        }

        // Insert sample users
        echo "<p>Adding sample users...</p>";
        $sampleUsers = [
            [
                'username' => 'admin',
                'email_id' => '<EMAIL>',
                'password' => md5('admin123'),
                'first_name' => 'Admin',
                'last_name' => 'User',
                'rolename' => 'Admin',
                'auth_type' => 'legacy',
                'auth_token' => generateAuthToken('admin'),
                'company_id' => 1,
                'unit_id' => 1
            ],
            [
                'username' => 'manager',
                'email_id' => '<EMAIL>',
                'password' => md5('manager123'),
                'first_name' => 'Manager',
                'last_name' => 'User',
                'rolename' => 'Manager',
                'auth_type' => 'legacy',
                'auth_token' => generateAuthToken('manager'),
                'company_id' => 1,
                'unit_id' => 1
            ],
            [
                'username' => 'user',
                'email_id' => '<EMAIL>',
                'password' => md5('user123'),
                'first_name' => 'Regular',
                'last_name' => 'User',
                'rolename' => 'User',
                'auth_type' => 'legacy',
                'auth_token' => generateAuthToken('user'),
                'company_id' => 1,
                'unit_id' => 1
            ]
        ];

        $stmt = $pdo->prepare("INSERT INTO users (
            username, email_id, password, first_name, last_name, rolename, status, auth_type, auth_token, company_id, unit_id
        ) VALUES (
            :username, :email_id, :password, :first_name, :last_name, :rolename, 1, :auth_type, :auth_token, :company_id, :unit_id
        )");

        foreach ($sampleUsers as $user) {
            try {
                $stmt->execute([
                    ':username' => $user['username'],
                    ':email_id' => $user['email_id'],
                    ':password' => $user['password'],
                    ':first_name' => $user['first_name'],
                    ':last_name' => $user['last_name'],
                    ':rolename' => $user['rolename'],
                    ':auth_type' => $user['auth_type'],
                    ':auth_token' => $user['auth_token'],
                    ':company_id' => $user['company_id'],
                    ':unit_id' => $user['unit_id']
                ]);
                echo "<p class='success'>Added user: " . $user['username'] . "</p>";
            } catch (PDOException $e) {
                echo "<p class='error'>Error adding user " . $user['username'] . ": " . $e->getMessage() . "</p>";
            }
        }
    }

    // Display table schema
    echo "<h2>Current Table Schema</h2>";

    foreach ($tables as $table) {
        if ($table !== 'sqlite_sequence') {
            $stmt = $pdo->query("PRAGMA table_info($table)");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<h3>Table: $table</h3>";
            echo "<table>";
            echo "<tr><th>CID</th><th>Name</th><th>Type</th><th>NotNull</th><th>Default</th><th>PK</th></tr>";

            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['cid'] . "</td>";
                echo "<td>" . $column['name'] . "</td>";
                echo "<td>" . $column['type'] . "</td>";
                echo "<td>" . $column['notnull'] . "</td>";
                echo "<td>" . $column['dflt_value'] . "</td>";
                echo "<td>" . $column['pk'] . "</td>";
                echo "</tr>";
            }

            echo "</table>";
        }
    }

    // Display users
    if (in_array('users', $tables)) {
        $stmt = $pdo->query("SELECT * FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<h2>Current Users</h2>";

        if (count($users) > 0) {
            echo "<table>";
            echo "<tr>";
            foreach (array_keys($users[0]) as $key) {
                echo "<th>" . $key . "</th>";
            }
            echo "</tr>";

            foreach ($users as $user) {
                echo "<tr>";
                foreach ($user as $key => $value) {
                    if ($key === 'password') {
                        echo "<td>[HIDDEN]</td>";
                    } else {
                        echo "<td>" . $value . "</td>";
                    }
                }
                echo "</tr>";
            }

            echo "</table>";
        } else {
            echo "<p>No users found.</p>";
        }
    }

    echo "<p class='success'>Database migration completed successfully.</p>";
    echo "<p><a href='/auth'>Go to Login Page</a> | <a href='/test-quickserve-init.php'>Test QuickServe Initialization</a></p>";

} catch (PDOException $e) {
    echo "<p class='error'>Database Error: " . $e->getMessage() . "</p>";
}

// End output buffering
ob_end_flush();
?>

<?php
// This is a mock Keycloak login page
// In a real implementation, this would redirect to the Keycloak server

// Start session
session_start();

// Mock Keycloak configuration
$keycloakConfig = [
    'auth_server_url' => 'http://localhost:8080/auth',
    'realm' => 'tenant',
    'client_id' => 'tenant-app',
    'client_secret' => 'your-client-secret',
    'redirect_uri' => 'http://localhost:8888/keycloak_callback.php',
];

// In a real implementation, this would generate a state parameter and redirect to Keycloak
$state = md5(uniqid(rand(), true));
$_SESSION['keycloak_state'] = $state;

// Build the authorization URL
$authUrl = $keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/auth';
$authUrl .= '?client_id=' . urlencode($keycloakConfig['client_id']);
$authUrl .= '&redirect_uri=' . urlencode($keycloakConfig['redirect_uri']);
$authUrl .= '&response_type=code';
$authUrl .= '&scope=' . urlencode('openid profile email');
$authUrl .= '&state=' . urlencode($state);

// Since we don't have a real Keycloak server, we'll simulate the flow
// In a real implementation, we would redirect to the Keycloak server
?>
<?php echo '<?xml version="1.0" encoding="UTF-8" ?>'; ?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
<!--[if IE]><meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'><![endif]-->
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
<title>OneSso Login</title>
<link rel="shortcut icon" type="text/css" href="/admin/images/favicon.png">
<link rel="stylesheet" type="text/css" href="/admin/css/foundation.css">
<link rel="stylesheet" type="text/css" href="/admin/css/default.css">
<link rel="stylesheet" type="text/css" href="/admin/css/font-awesome.css">

<!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
  <script src="js/html5shiv.js"></script>
  <script src="js/respond.min.js"></script>
<![endif]-->

</head>
<body class="login">
<!-- BEGIN LOGO -->
<div class="logo">
    <img src="/admin/images/logo.png" alt="Logo" />
    <br>
    <span style="color: #4A89DC; font-weight: bold;">OneSso Authentication</span>
</div>
<!-- END LOGO -->
<!-- BEGIN LOGIN -->
<div class="content clearfix">
  <!-- BEGIN LOGIN FORM -->
  <form method="post" action="keycloak_callback.php" class="login-form">
    <h3 class="form-title">Sign in with OneSso</h3>
    <input type="hidden" name="state" value="<?php echo $state; ?>">

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
      <button class="close" data-dismiss="alert"></button>
      <span>
        <?php
        $error = $_GET['error'];
        if ($error === 'invalid_credentials') {
            echo 'Invalid username or password.';
        } else {
            echo 'An error occurred. Please try again.';
        }
        ?>
      </span>
    </div>
    <?php endif; ?>

    <div class="control-group">
      <div class="controls">
        <div class="input-icon">
          <i class="fa fa-user"></i>
          <input type="text" class="m-wrap" placeholder="Username" name="username" required>
        </div>
      </div>
    </div>

    <div class="control-group">
      <div class="controls">
        <div class="input-icon">
          <i class="fa fa-lock"></i>
          <input type="password" class="m-wrap" placeholder="Password" name="password" required>
        </div>
      </div>
    </div>

    <div class="form-actions clearfix">
      <button type="submit" class="button expand" style="background-color: #4A89DC;">Sign In</button>
    </div>

    <div class="form-actions clearfix">
      <div class="text-center">
        <p style="text-align: center; margin: 15px 0; color: #656D78;">- OR -</p>
        <a href="login.php" class="button expand secondary">
          <i class="fa fa-arrow-left"></i> Back to Login
        </a>
      </div>
    </div>

    <div class="clearBoth5"></div>
  </form>
  <!-- END LOGIN FORM -->

  <div style="margin-top: 20px; padding: 10px; background-color: #f9f9f9; border-radius: 4px; font-size: 12px;">
    <h4 style="margin-top: 0;">Debug Information</h4>
    <p>In a real implementation, you would be redirected to:</p>
    <div style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; font-family: monospace; font-size: 11px;">
      <?php echo htmlspecialchars($authUrl); ?>
    </div>
  </div>
</div>
<!-- END LOGIN -->
<!-- BEGIN FOOTER -->
<div id="footer" class="footer clearfix">Powered by :<a target="_blank" href="#">
    PROSIMERP
</a></div>
<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

<!-- wrapper End -->
<script src="/admin/js/vendor/modernizr.js"></script>
<script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
<script src="/admin/js/jquery.cookie.js"></script>
<script src="/admin/js/foundation.min.js"></script>

<script>
  $(document).foundation();
</script>

<script src="/admin/js/customforms.js"></script>
<script src="/admin/js/script.js"></script>
<script>
$(document).ready(function() {
    $('.login-form input').keypress(function (e) {
        if (e.which == 13) {
            $('.login-form').submit();
            return false;
        }
    });
});
</script>
</body>
</html>

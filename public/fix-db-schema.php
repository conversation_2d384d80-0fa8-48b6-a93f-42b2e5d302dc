<?php
/**
 * Database Schema Fix Script
 *
 * This script fixes the database schema by adding missing columns
 */

// Start output buffering
ob_start();

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Create data directory if it doesn't exist
$dataDir = APPLICATION_PATH . '/data';
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Create db directory if it doesn't exist
$dbDir = $dataDir . '/db';
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
}

// Define database file path
$dbFile = $dbDir . '/mock.sqlite';

echo "<h1>Database Schema Fix</h1>";

try {
    // Create PDO connection
    $pdo = new PDO('sqlite:' . $dbFile);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if users table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "<p>Creating users table...</p>";

        // Create users table
        $pdo->exec("CREATE TABLE users (
            pk_user_code INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            email_id TEXT,
            password TEXT,
            first_name TEXT,
            last_name TEXT,
            rolename TEXT,
            status INTEGER DEFAULT 1,
            auth_token TEXT,
            auth_type TEXT DEFAULT 'legacy',
            company_id INTEGER DEFAULT 1,
            unit_id INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");

        echo "<p>Users table created successfully.</p>";
    } else {
        echo "<p>Users table already exists.</p>";

        // Check if columns exist
        $stmt = $pdo->query("PRAGMA table_info(users)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasAuthTokenColumn = false;
        $hasRolenameColumn = false;

        foreach ($columns as $column) {
            if ($column['name'] === 'auth_token') {
                $hasAuthTokenColumn = true;
            }
            if ($column['name'] === 'rolename') {
                $hasRolenameColumn = true;
            }
        }

        if (!$hasAuthTokenColumn) {
            echo "<p>Adding auth_token column to users table...</p>";

            // Add auth_token column
            $pdo->exec("ALTER TABLE users ADD COLUMN auth_token TEXT");

            echo "<p>auth_token column added successfully.</p>";
        } else {
            echo "<p>auth_token column already exists.</p>";
        }

        if (!$hasRolenameColumn) {
            echo "<p>Adding rolename column to users table...</p>";

            // Add rolename column
            $pdo->exec("ALTER TABLE users ADD COLUMN rolename TEXT");

            echo "<p>rolename column added successfully.</p>";
        } else {
            echo "<p>rolename column already exists.</p>";
        }
    }

    // Check if orders table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "<p>Creating orders table...</p>";

        // Create orders table
        $pdo->exec("CREATE TABLE orders (
            pk_order_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            order_no TEXT NOT NULL,
            order_date DATE NOT NULL,
            customer_id INTEGER,
            order_status TEXT DEFAULT 'pending',
            payment_status TEXT DEFAULT 'pending',
            order_type TEXT DEFAULT 'standard',
            status TEXT DEFAULT 'active',
            total_amount DECIMAL(10,2) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        echo "<p>Orders table created successfully.</p>";
    } else {
        echo "<p>Orders table already exists.</p>";

        // Check if columns exist
        $stmt = $pdo->query("PRAGMA table_info(orders)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasPaymentStatusColumn = false;
        $hasOrderTypeColumn = false;
        $hasStatusColumn = false;

        foreach ($columns as $column) {
            if ($column['name'] === 'payment_status') {
                $hasPaymentStatusColumn = true;
            }
            if ($column['name'] === 'order_type') {
                $hasOrderTypeColumn = true;
            }
            if ($column['name'] === 'status') {
                $hasStatusColumn = true;
            }
        }

        if (!$hasPaymentStatusColumn) {
            echo "<p>Adding payment_status column to orders table...</p>";

            // Add payment_status column
            $pdo->exec("ALTER TABLE orders ADD COLUMN payment_status TEXT DEFAULT 'pending'");

            echo "<p>payment_status column added successfully.</p>";
        } else {
            echo "<p>payment_status column already exists.</p>";
        }

        if (!$hasOrderTypeColumn) {
            echo "<p>Adding order_type column to orders table...</p>";

            // Add order_type column
            $pdo->exec("ALTER TABLE orders ADD COLUMN order_type TEXT DEFAULT 'standard'");

            echo "<p>order_type column added successfully.</p>";
        } else {
            echo "<p>order_type column already exists.</p>";
        }

        if (!$hasStatusColumn) {
            echo "<p>Adding status column to orders table...</p>";

            // Add status column
            $pdo->exec("ALTER TABLE orders ADD COLUMN status TEXT DEFAULT 'active'");

            echo "<p>status column added successfully.</p>";
        } else {
            echo "<p>status column already exists.</p>";
        }
    }

    // Check if activity_log table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='activity_log'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "<p>Creating activity_log table...</p>";

        // Create activity_log table
        $pdo->exec("CREATE TABLE activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            context_ref_id INTEGER,
            context_name TEXT,
            context_type TEXT,
            controller TEXT,
            action TEXT,
            description TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        echo "<p>Activity log table created successfully.</p>";
    } else {
        echo "<p>Activity log table already exists.</p>";

        // Check if columns exist
        $stmt = $pdo->query("PRAGMA table_info(activity_log)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasControllerColumn = false;
        $hasUserAgentColumn = false;

        foreach ($columns as $column) {
            if ($column['name'] === 'controller') {
                $hasControllerColumn = true;
            }
            if ($column['name'] === 'user_agent') {
                $hasUserAgentColumn = true;
            }
        }

        if (!$hasControllerColumn) {
            echo "<p>Adding controller column to activity_log table...</p>";

            // Add controller column
            $pdo->exec("ALTER TABLE activity_log ADD COLUMN controller TEXT");

            echo "<p>controller column added successfully.</p>";
        } else {
            echo "<p>controller column already exists.</p>";
        }

        if (!$hasUserAgentColumn) {
            echo "<p>Adding user_agent column to activity_log table...</p>";

            // Add user_agent column
            $pdo->exec("ALTER TABLE activity_log ADD COLUMN user_agent TEXT");

            echo "<p>user_agent column added successfully.</p>";
        } else {
            echo "<p>user_agent column already exists.</p>";
        }
    }

    // Check if admin user exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $adminExists = (int)$stmt->fetchColumn();

    if (!$adminExists) {
        echo "<p>Adding admin user...</p>";

        // Insert admin user
        $stmt = $pdo->prepare("INSERT INTO users (
            username, email_id, password, first_name, last_name, rolename, status, auth_type, company_id, unit_id
        ) VALUES (
            :username, :email_id, :password, :first_name, :last_name, :rolename, :status, :auth_type, :company_id, :unit_id
        )");

        $stmt->execute([
            ':username' => 'admin',
            ':email_id' => '<EMAIL>',
            ':password' => md5('admin123'),
            ':first_name' => 'Admin',
            ':last_name' => 'User',
            ':rolename' => 'Admin',
            ':status' => 1,
            ':auth_type' => 'legacy',
            ':company_id' => 1,
            ':unit_id' => 1
        ]);

        echo "<p>Admin user added successfully.</p>";
    } else {
        echo "<p>Admin user already exists.</p>";
    }

    // Display table schema
    $stmt = $pdo->query("PRAGMA table_info(users)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Users Table Schema</h2>";
    echo "<table border='1'>";
    echo "<tr><th>CID</th><th>Name</th><th>Type</th><th>NotNull</th><th>Default</th><th>PK</th></tr>";

    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['cid'] . "</td>";
        echo "<td>" . $column['name'] . "</td>";
        echo "<td>" . $column['type'] . "</td>";
        echo "<td>" . $column['notnull'] . "</td>";
        echo "<td>" . $column['dflt_value'] . "</td>";
        echo "<td>" . $column['pk'] . "</td>";
        echo "</tr>";
    }

    echo "</table>";

    // Display users
    $stmt = $pdo->query("SELECT * FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Users</h2>";

    if (count($users) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($users[0]) as $key) {
            echo "<th>" . $key . "</th>";
        }
        echo "</tr>";

        foreach ($users as $user) {
            echo "<tr>";
            foreach ($user as $value) {
                echo "<td>" . $value . "</td>";
            }
            echo "</tr>";
        }

        echo "</table>";
    } else {
        echo "<p>No users found.</p>";
    }

    // Check if city table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='city'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "<p>Creating city table...</p>";

        // Create city table
        $pdo->exec("CREATE TABLE city (
            pk_city_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            city_name TEXT NOT NULL,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        echo "<p>City table created successfully.</p>";

        // Insert sample data
        $pdo->exec("INSERT INTO city (city_name) VALUES ('New York')");
        $pdo->exec("INSERT INTO city (city_name) VALUES ('Los Angeles')");
        $pdo->exec("INSERT INTO city (city_name) VALUES ('Chicago')");

        echo "<p>Sample city data inserted successfully.</p>";
    } else {
        echo "<p>City table already exists.</p>";
    }

    // Check if tax table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='tax'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "<p>Creating tax table...</p>";

        // Create tax table
        $pdo->exec("CREATE TABLE tax (
            pk_tax_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            tax_name TEXT NOT NULL,
            tax_percentage DECIMAL(5,2) NOT NULL,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        echo "<p>Tax table created successfully.</p>";

        // Insert sample data
        $pdo->exec("INSERT INTO tax (tax_name, tax_percentage) VALUES ('GST', 5.00)");
        $pdo->exec("INSERT INTO tax (tax_name, tax_percentage) VALUES ('VAT', 7.50)");

        echo "<p>Sample tax data inserted successfully.</p>";
    } else {
        echo "<p>Tax table already exists.</p>";
    }

    // Check if sms_set table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sms_set'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "<p>Creating sms_set table...</p>";

        // Create sms_set table
        $pdo->exec("CREATE TABLE sms_set (
            pk_sms_set_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            template_name TEXT NOT NULL,
            template_content TEXT NOT NULL,
            template_type TEXT DEFAULT 'order',
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        echo "<p>SMS template table created successfully.</p>";

        // Insert sample data
        $pdo->exec("INSERT INTO sms_set (template_name, template_content, template_type)
                    VALUES ('Order Confirmation', 'Your order #{{order_no}} has been confirmed. Thank you for your purchase!', 'order')");
        $pdo->exec("INSERT INTO sms_set (template_name, template_content, template_type)
                    VALUES ('Order Delivery', 'Your order #{{order_no}} is out for delivery and will arrive shortly.', 'delivery')");

        echo "<p>Sample SMS template data inserted successfully.</p>";
    } else {
        echo "<p>SMS template table already exists.</p>";
    }

    echo "<p>Database schema fix completed successfully.</p>";
    echo "<p><a href='/auth'>Go to Login Page</a></p>";

} catch (PDOException $e) {
    echo "<p>Database Error: " . $e->getMessage() . "</p>";
}

// End output buffering
ob_end_flush();
?>

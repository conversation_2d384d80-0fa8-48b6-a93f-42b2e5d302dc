<?php
/**
 * Initialize and Manage Log Files
 *
 * This script ensures that all required log files exist and have proper permissions.
 * It also provides options to view, clear, and test logging functionality.
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Define log directory
$logDir = realpath(dirname(__FILE__) . '/../data/logs');

// Create log directory if it doesn't exist
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
    echo "<div class='alert alert-success'>Created log directory: $logDir</div>";
}

// Define log types
$logTypes = [
    'auth' => 'auth.log',
    'navigation' => 'navigation.log',
    'token' => 'token.log',
    'error' => 'error.log'
];

// Process actions
$message = '';
$action = isset($_GET['action']) ? $_GET['action'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';

if ($action === 'clear' && isset($logTypes[$type])) {
    $logFile = $logDir . '/' . $logTypes[$type];
    file_put_contents($logFile, '');
    $message = "<div class='alert alert-success'>Cleared {$type} log file</div>";
} elseif ($action === 'clear_all') {
    foreach ($logTypes as $logType => $file) {
        $logFile = $logDir . '/' . $file;
        file_put_contents($logFile, '');
    }
    $message = "<div class='alert alert-success'>Cleared all log files</div>";
} elseif ($action === 'test') {
    // Test logging functionality
    foreach ($logTypes as $logType => $file) {
        $logFile = $logDir . '/' . $file;
        $timestamp = date('Y-m-d H:i:s');
        $logData = [
            'timestamp' => $timestamp,
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'session_id' => session_id(),
            'user_id' => 'Test User',
            'message' => "Test log entry for {$logType}",
            'data' => ['test' => true, 'time' => time()]
        ];
        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND);
    }
    $message = "<div class='alert alert-success'>Added test entries to all log files</div>";
}

// Create log files if they don't exist
$logStatus = [];
foreach ($logTypes as $type => $file) {
    $logFile = $logDir . '/' . $file;

    if (!file_exists($logFile)) {
        touch($logFile);
        chmod($logFile, 0644);
        $logStatus[$type] = "Created";
    } else {
        $logStatus[$type] = "Exists";
    }

    // Get file size and last modified time
    $logStatus[$type . '_size'] = filesize($logFile);
    $logStatus[$type . '_modified'] = date('Y-m-d H:i:s', filemtime($logFile));

    // Count number of entries
    $logStatus[$type . '_entries'] = count(file($logFile));
}

// HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>Log Management</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <style>
        body {
            padding: 20px;
        }
        .log-status {
            margin-bottom: 20px;
        }
        .actions {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Log Management</h1>

        <?php if ($message): ?>
            <?php echo $message; ?>
        <?php endif; ?>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">Log Status</h3>
            </div>
            <div class="panel-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Log Type</th>
                            <th>Status</th>
                            <th>Size</th>
                            <th>Entries</th>
                            <th>Last Modified</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logTypes as $type => $file): ?>
                            <tr>
                                <td><?php echo ucfirst($type); ?></td>
                                <td><?php echo $logStatus[$type]; ?></td>
                                <td><?php echo number_format($logStatus[$type . '_size'] / 1024, 2); ?> KB</td>
                                <td><?php echo $logStatus[$type . '_entries']; ?></td>
                                <td><?php echo $logStatus[$type . '_modified']; ?></td>
                                <td>
                                    <a href="?action=clear&type=<?php echo $type; ?>" class="btn btn-xs btn-danger" onclick="return confirm('Are you sure you want to clear this log?');">Clear</a>
                                    <a href="/auth-logs.php?type=<?php echo $type; ?>" class="btn btn-xs btn-info">View</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="actions">
            <a href="?action=clear_all" class="btn btn-danger" onclick="return confirm('Are you sure you want to clear ALL log files?');">Clear All Logs</a>
            <a href="?action=test" class="btn btn-primary">Add Test Log Entries</a>
            <a href="/auth-logs.php" class="btn btn-info">View Logs</a>
            <a href="/auth" class="btn btn-default">Go to Login</a>
            <a href="/init-db.php" class="btn btn-success">Initialize Database</a>
        </div>
    </div>
</body>
</html>

// Initialize session variables
if (!isset($_SESSION)) {
    session_start();
}

// Set company ID in session if not set
if (!isset($_SESSION['tenant']) || !isset($_SESSION['tenant']['company_id'])) {
    $_SESSION['tenant'] = [
        'company_id' => 1,
        'unit_id' => 1,
        'company_details' => [
            'company_name' => 'Demo Company',
            'company_address' => '123 Main St',
            'company_phone' => '555-1234',
            'company_email' => '<EMAIL>',
            'domain' => $_SERVER['HTTP_HOST']
        ]
    ];

    echo "Initialized session with company ID: " . $_SESSION['tenant']['company_id'] . "\n";
}

// Set global variables
$GLOBALS['company_id'] = $_SESSION['tenant']['company_id'];
$GLOBALS['unit_id'] = $_SESSION['tenant']['unit_id'];

// Initialize settings
if (!isset($_SESSION['setting'])) {
    $_SESSION['setting'] = [
        'setting' => [
            'WEBSITE_MAINTENANCE_ADMIN_PORTAL' => 'no',
            'GLOBAL_AUTH_METHOD' => 'legacy',
            'WIZARD_SETUP' => '1,1',
            'GLOBAL_LOCALE' => 'en_US',
            'GLOBAL_CURRENCY' => 'USD',
            'GLOBAL_CURRENCY_ENTITY' => '$',
            'GLOBAL_THEME' => 'default',
            'MERCHANT_COMPANY_NAME' => 'Demo Company'
        ]
    ];

    echo "Initialized settings\n";
}

echo "Log initialization complete\n";

// Redirect to dashboard if requested
if (isset($_GET['redirect']) && $_GET['redirect'] === 'dashboard') {
    header('Location: /dashboard');
    exit;
}

// Show success message
?>
<!DOCTYPE html>
<html>
<head>
    <title>Log Initialization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Log Initialization Complete</h1>
        <p class="success">All log files have been initialized successfully.</p>

        <h2>Log Files:</h2>
        <ul>
            <?php foreach ($logTypes as $type => $file): ?>
                <li><?php echo $file; ?> - <?php echo file_exists($logDir . '/' . $file) ? 'OK' : 'Missing'; ?></li>
            <?php endforeach; ?>
        </ul>

        <h2>Session Information:</h2>
        <ul>
            <li>Company ID: <?php echo $_SESSION['tenant']['company_id']; ?></li>
            <li>Unit ID: <?php echo $_SESSION['tenant']['unit_id']; ?></li>
            <li>Company Name: <?php echo $_SESSION['tenant']['company_details']['company_name']; ?></li>
        </ul>

        <a href="/dashboard" class="btn">Go to Dashboard</a>
        <a href="/auth" class="btn" style="background-color: #2196F3;">Go to Login</a>
        <a href="/auth-logs.php" class="btn" style="background-color: #FF9800;">View Auth Logs</a>
    </div>
</body>
</html>

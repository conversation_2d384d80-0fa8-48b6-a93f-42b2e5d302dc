<?php
/**
 * Debug script for authentication
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';
    
    echo "<h1>Authentication Debug</h1>";
    
    // Check development mode
    echo "<h2>Development Mode</h2>";
    echo "<p>Development mode: " . ($config['development_mode'] ? 'Enabled' : 'Disabled') . "</p>";
    
    // Initialize Zend Application
    echo "<h2>Initializing Application</h2>";
    $application = Zend\Mvc\Application::init($config);
    echo "<p>Application initialized successfully</p>";
    
    // Get service manager
    $serviceManager = $application->getServiceManager();
    echo "<p>Service manager retrieved successfully</p>";
    
    // Check if ConfigService is registered
    echo "<h2>ConfigService</h2>";
    if ($serviceManager->has('ConfigService')) {
        echo "<p>ConfigService is registered</p>";
        $configService = $serviceManager->get('ConfigService');
        echo "<p>ConfigService class: " . get_class($configService) . "</p>";
        
        // Get auth_mode
        $authMode = $configService->getConfig('auth_mode', 'legacy');
        echo "<p>Auth mode: " . $authMode . "</p>";
    } else {
        echo "<p>ConfigService is not registered</p>";
    }
    
    // Check if AuthService is registered
    echo "<h2>AuthService</h2>";
    if ($serviceManager->has('AuthService')) {
        echo "<p>AuthService is registered</p>";
        $authService = $serviceManager->get('AuthService');
        echo "<p>AuthService class: " . get_class($authService) . "</p>";
        
        // Get adapter
        $adapter = $authService->getAdapter();
        echo "<p>Adapter class: " . get_class($adapter) . "</p>";
        
        // Test authentication with hardcoded credentials
        echo "<h2>Testing Authentication</h2>";
        $adapter->setIdentity('<EMAIL>');
        $adapter->setCredential('password');
        
        $result = $authService->authenticate();
        echo "<p>Authentication result: " . ($result->isValid() ? 'Valid' : 'Invalid') . "</p>";
        
        if ($result->isValid()) {
            echo "<p>Authentication successful!</p>";
            
            // Get user details
            $returnData = array("pk_user_code", "first_name", "last_name", "phone", "gender", "email_id", "city", "role_id", "status", "third_party_id");
            $userDetails = $authService->getAdapter()->getResultRowObject($returnData);
            
            echo "<h3>User Details</h3>";
            echo "<pre>" . print_r($userDetails, true) . "</pre>";
        } else {
            echo "<p>Authentication failed</p>";
            echo "<h3>Authentication Messages</h3>";
            echo "<pre>" . print_r($result->getMessages(), true) . "</pre>";
        }
    } else {
        echo "<p>AuthService is not registered</p>";
    }
    
    // Check database adapter
    echo "<h2>Database Adapter</h2>";
    if ($serviceManager->has('Zend\Db\Adapter\Adapter')) {
        echo "<p>Database adapter is registered</p>";
        $dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');
        echo "<p>Adapter class: " . get_class($dbAdapter) . "</p>";
        
        // Test database connection
        try {
            $sql = "SELECT 1";
            $statement = $dbAdapter->query($sql);
            $result = $statement->execute();
            echo "<p>Database connection test: Successful</p>";
            
            // Check if users table exists
            $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='users'";
            $statement = $dbAdapter->query($sql);
            $result = $statement->execute();
            
            if ($result->count() > 0) {
                echo "<p>Users table exists</p>";
                
                // Check users in the table
                $sql = "SELECT * FROM users";
                $statement = $dbAdapter->query($sql);
                $users = $statement->execute();
                
                echo "<h3>Users in Database</h3>";
                echo "<table border='1' cellpadding='5'>";
                echo "<tr><th>ID</th><th>Email</th><th>First Name</th><th>Last Name</th><th>Role ID</th><th>Status</th><th>Password</th></tr>";
                
                foreach ($users as $user) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($user['pk_user_code']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['email_id']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['first_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['last_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['role_id']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['status']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['password']) . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p>Users table does not exist</p>";
            }
        } catch (\Exception $e) {
            echo "<p>Database connection test: Failed</p>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>Database adapter is not registered</p>";
    }
    
    // Check session
    echo "<h2>Session</h2>";
    echo "<p>Session ID: " . session_id() . "</p>";
    echo "<p>Session status: " . session_status() . "</p>";
    
    // Check if session is started
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "<p>Session is active</p>";
        echo "<h3>Session Data</h3>";
        echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    } else {
        echo "<p>Session is not active</p>";
    }
    
} catch (\Exception $e) {
    echo "<h1>Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

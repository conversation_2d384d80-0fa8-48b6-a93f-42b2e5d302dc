<?php
/**
 * Test JWT Token Utility
 * 
 * This script tests the JWT token utility
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Include QuickServe autoloader
require_once APPLICATION_PATH . '/module/QuickServe/src/QuickServe/Autoloader.php';
\QuickServe\Autoloader::register();

// Set content type
header('Content-Type: text/html');

// Start output buffering
ob_start();

echo "<html><head><title>JWT Token Utility Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #333; }
    h2 { color: #666; margin-top: 20px; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
</style>";
echo "</head><body>";
echo "<h1>JWT Token Utility Test</h1>";

try {
    // Set environment variables
    $demoCompanyId = 'abc123-demo';
    $jwtSecret = 'test-jwt-secret';
    
    // Create JWT token utility
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Auth/JwtTokenUtil.php';
    $jwtUtil = new \Lib\QuickServe\Auth\JwtTokenUtil([
        'jwt_secret' => $jwtSecret
    ]);
    
    // Test generating a token
    echo "<h2>Generate Token</h2>";
    echo "<pre>";
    echo "Generating token with company ID: $demoCompanyId and roles: [admin]\n";
    $token = $jwtUtil->generateToken($demoCompanyId, ['admin']);
    echo "Generated token: $token\n";
    echo "</pre>";
    
    // Test decoding a token
    echo "<h2>Decode Token</h2>";
    echo "<pre>";
    echo "Decoding token...\n";
    $payload = $jwtUtil->decodeToken($token);
    echo "Decoded payload: \n";
    echo json_encode($payload, JSON_PRETTY_PRINT) . "\n";
    echo "</pre>";
    
    // Test validating a token
    echo "<h2>Validate Token</h2>";
    echo "<pre>";
    echo "Validating token for QuickServe...\n";
    $isValid = $jwtUtil->validateTokenForQuickServe($token, $demoCompanyId);
    echo "Token is " . ($isValid ? "<span class=\"success\">valid</span>" : "<span class=\"error\">invalid</span>") . " for QuickServe initialization\n";
    echo "</pre>";
    
    // Test invalid tokens
    echo "<h2>Test Invalid Tokens</h2>";
    echo "<pre>";
    
    // Test with wrong company ID
    echo "Testing with wrong company ID...\n";
    $wrongCompanyToken = $jwtUtil->generateToken('wrong-company-id', ['admin']);
    $isValid = $jwtUtil->validateTokenForQuickServe($wrongCompanyToken, $demoCompanyId);
    echo "Token with wrong company ID is " . ($isValid ? "<span class=\"success\">valid</span>" : "<span class=\"error\">invalid</span>") . "\n";
    
    // Test with missing admin role
    echo "\nTesting with missing admin role...\n";
    $noAdminToken = $jwtUtil->generateToken($demoCompanyId, ['user']);
    $isValid = $jwtUtil->validateTokenForQuickServe($noAdminToken, $demoCompanyId);
    echo "Token without admin role is " . ($isValid ? "<span class=\"success\">valid</span>" : "<span class=\"error\">invalid</span>") . "\n";
    
    // Test with empty token
    echo "\nTesting with empty token...\n";
    $isValid = $jwtUtil->validateTokenForQuickServe('', $demoCompanyId);
    echo "Empty token is " . ($isValid ? "<span class=\"success\">valid</span>" : "<span class=\"error\">invalid</span>") . "\n";
    
    // Test with malformed token
    echo "\nTesting with malformed token...\n";
    $isValid = $jwtUtil->validateTokenForQuickServe('invalid.token.format', $demoCompanyId);
    echo "Malformed token is " . ($isValid ? "<span class=\"success\">valid</span>" : "<span class=\"error\">invalid</span>") . "\n";
    echo "</pre>";
    
    // Test token expiration
    echo "<h2>Test Token Expiration</h2>";
    echo "<pre>";
    echo "Generating token that expires in 5 seconds...\n";
    $shortToken = $jwtUtil->generateToken($demoCompanyId, ['admin'], 5);
    echo "Generated token: $shortToken\n";
    
    $isValid = $jwtUtil->validateTokenForQuickServe($shortToken, $demoCompanyId);
    echo "Token is initially " . ($isValid ? "<span class=\"success\">valid</span>" : "<span class=\"error\">invalid</span>") . "\n";
    
    echo "\nWaiting for token to expire (5 seconds)...\n";
    echo "You can refresh the page after 5 seconds to see if the token has expired.\n";
    echo "</pre>";
    
} catch (\Exception $e) {
    echo "<h2>Error</h2>";
    echo "<pre class=\"error\">";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "</pre>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If all tests pass, the JWT token utility is working correctly.</p>";
echo "<p>You can now use the JWT token utility to initialize QuickServe.</p>";
echo "<p><a href=\"/test-quickserve-init.php\">Test QuickServe Initialization</a></p>";

echo "</body></html>";

// End output buffering
ob_end_flush();

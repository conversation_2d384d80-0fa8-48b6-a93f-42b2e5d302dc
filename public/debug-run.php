<?php
/**
 * This is a debug version of index.php that runs the application step by step
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    echo '<h1>Debug Application Run</h1>';
    echo '<p>PHP Version: ' . PHP_VERSION . '</p>';
    
    // Load configuration
    echo '<h2>Step 1: Loading Configuration</h2>';
    $config = include APPLICATION_PATH . '/config/application.config.php';
    echo '<p>Configuration loaded successfully</p>';
    
    // Initialize Zend Application
    echo '<h2>Step 2: Initializing Zend Application</h2>';
    $application = Zend\Mvc\Application::init($config);
    echo '<p>Zend Application initialized successfully</p>';
    
    // Create request
    echo '<h2>Step 3: Creating Request</h2>';
    $request = new Zend\Http\PhpEnvironment\Request();
    echo '<p>Request created successfully</p>';
    
    // Create response
    echo '<h2>Step 4: Creating Response</h2>';
    $response = new Zend\Http\PhpEnvironment\Response();
    echo '<p>Response created successfully</p>';
    
    // Create MVC event
    echo '<h2>Step 5: Creating MVC Event</h2>';
    $event = new Zend\Mvc\MvcEvent();
    $event->setRequest($request);
    $event->setResponse($response);
    $event->setApplication($application);
    echo '<p>MVC Event created successfully</p>';
    
    // Route the request
    echo '<h2>Step 6: Routing Request</h2>';
    try {
        $router = $application->getServiceManager()->get('Router');
        $routeMatch = $router->match($request);
        
        if ($routeMatch) {
            $event->setRouteMatch($routeMatch);
            echo '<p>Route matched: ' . $routeMatch->getMatchedRouteName() . '</p>';
            echo '<p>Controller: ' . $routeMatch->getParam('controller') . '</p>';
            echo '<p>Action: ' . $routeMatch->getParam('action') . '</p>';
        } else {
            echo '<p>No route matched</p>';
        }
    } catch (Exception $e) {
        echo '<p>Error during routing: ' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
    
    // Dispatch the request
    echo '<h2>Step 7: Dispatching Request</h2>';
    try {
        $application->getEventManager()->trigger('dispatch', $event);
        echo '<p>Request dispatched successfully</p>';
    } catch (Exception $e) {
        echo '<p>Error during dispatch: ' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
    
    // Render the response
    echo '<h2>Step 8: Rendering Response</h2>';
    try {
        $application->getEventManager()->trigger('render', $event);
        echo '<p>Response rendered successfully</p>';
    } catch (Exception $e) {
        echo '<p>Error during rendering: ' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
    
    // Output the response
    echo '<h2>Step 9: Response</h2>';
    echo '<p>Response status code: ' . $response->getStatusCode() . '</p>';
    echo '<p>Response headers:</p>';
    echo '<pre>' . print_r($response->getHeaders()->toString(), true) . '</pre>';
    
    // Don't actually send the response
    // $response->send();
    
} catch (Exception $e) {
    echo '<h2>Error</h2>';
    echo '<p>Error: ' . $e->getMessage() . '</p>';
    echo '<p>File: ' . $e->getFile() . ' (line ' . $e->getLine() . ')</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

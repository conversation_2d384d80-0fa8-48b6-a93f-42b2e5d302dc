<?php
/**
 * Test QuickServe Initialization
 * 
 * This script tests the QuickServe initialization with JWT token
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Include QuickServe autoloader
require_once APPLICATION_PATH . '/module/QuickServe/src/QuickServe/Autoloader.php';
\QuickServe\Autoloader::register();

// Set content type
header('Content-Type: text/html');

// Start output buffering
ob_start();

echo "<html><head><title>QuickServe Initialization Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #333; }
    h2 { color: #666; margin-top: 20px; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
</style>";
echo "</head><body>";
echo "<h1>QuickServe Initialization Test</h1>";

try {
    // Load environment variables
    echo "<h2>Environment Variables</h2>";
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php';
    \Lib\QuickServe\Env\EnvLoader::load();
    
    echo "<pre>";
    echo "DEMO_COMPANY_ID: " . \Lib\QuickServe\Env\EnvLoader::get('DEMO_COMPANY_ID', 'Not set') . "\n";
    echo "ADMIN_TOKEN present: " . (\Lib\QuickServe\Env\EnvLoader::get('ADMIN_TOKEN') ? 'Yes' : 'No') . "\n";
    echo "DEVELOPMENT_MODE: " . \Lib\QuickServe\Env\EnvLoader::get('DEVELOPMENT_MODE', 'Not set') . "\n";
    echo "</pre>";
    
    // Test JWT token
    echo "<h2>JWT Token Test</h2>";
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Auth/JwtTokenUtil.php';
    $jwtUtil = new \Lib\QuickServe\Auth\JwtTokenUtil([
        'jwt_secret' => \Lib\QuickServe\Env\EnvLoader::get('JWT_SECRET', 'quickserve-jwt-secret')
    ]);
    
    $adminToken = \Lib\QuickServe\Env\EnvLoader::get('ADMIN_TOKEN', '');
    $demoCompanyId = \Lib\QuickServe\Env\EnvLoader::get('DEMO_COMPANY_ID', '');
    
    echo "<pre>";
    if (!$adminToken) {
        echo "<span class=\"error\">No ADMIN_TOKEN found in environment variables</span>\n";
        echo "Generating a new token for testing...\n";
        $adminToken = $jwtUtil->generateToken($demoCompanyId, ['admin']);
        echo "Generated token: " . substr($adminToken, 0, 20) . "...\n";
    } else {
        echo "<span class=\"success\">ADMIN_TOKEN found in environment variables</span>\n";
        echo "Token: " . substr($adminToken, 0, 20) . "...\n";
    }
    
    $isValid = $jwtUtil->validateTokenForQuickServe($adminToken, $demoCompanyId);
    echo "Token validation: " . ($isValid ? "<span class=\"success\">Valid</span>" : "<span class=\"error\">Invalid</span>") . "\n";
    
    $claims = $jwtUtil->decodeToken($adminToken);
    echo "Token claims: \n";
    echo json_encode($claims, JSON_PRETTY_PRINT) . "\n";
    echo "</pre>";
    
    // Test QuickServe initialization
    echo "<h2>QuickServe Initialization Test</h2>";
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Initializer.php';
    $initializer = new \Lib\QuickServe\Initializer();
    
    echo "<pre>";
    echo "Initializing QuickServe...\n";
    $initResult = $initializer->initialize();
    echo "Initialization result: " . ($initResult ? "<span class=\"success\">Success</span>" : "<span class=\"error\">Failed</span>") . "\n";
    echo "</pre>";
    
    // Show logs
    echo "<h2>Logs</h2>";
    echo "<pre>";
    $logFile = ini_get('error_log');
    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);
        $lines = explode("\n", $logs);
        $relevantLogs = [];
        
        foreach ($lines as $line) {
            if (strpos($line, 'QuickServe') !== false) {
                $relevantLogs[] = $line;
            }
        }
        
        $relevantLogs = array_slice($relevantLogs, -20);
        echo implode("\n", $relevantLogs);
    } else {
        echo "<span class=\"info\">No log file found at: $logFile</span>\n";
    }
    echo "</pre>";
    
} catch (\Exception $e) {
    echo "<h2>Error</h2>";
    echo "<pre class=\"error\">";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "</pre>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If the initialization was successful, you can proceed to use the QuickServe module.</p>";
echo "<p>If the initialization failed, check the logs for more details and make sure the environment variables are set correctly.</p>";
echo "<p><a href=\"/health/quickserve.php\">Check QuickServe Health</a></p>";

echo "</body></html>";

// End output buffering
ob_end_flush();

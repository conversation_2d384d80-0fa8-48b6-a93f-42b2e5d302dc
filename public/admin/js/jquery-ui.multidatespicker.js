/*
 * MultiDatesPicker v1.6.3
 * http://multidatespickr.sourceforge.net/
 * 
 * Copyright 2014, <PERSON>
 * Dual licensed under the MIT or GPL version 2 licenses.
 */
(function( $ ){
	$.extend($.ui, { multiDatesPicker: { version: "1.6.3" } });
	
	$.fn.multiDatesPicker = function(method) {
		var mdp_arguments = arguments;
		var ret = this;
		var today_date = new Date();
		var day_zero = new Date(0);
		var mdp_events = {};
		
		function removeDate(date, type) {
			if(!type) type = 'picked';
			date = dateConvert.call(this, date);
			for(var i = 0; i < this.multiDatesPicker.dates[type].length; i++)
				if(!methods.compareDates(this.multiDatesPicker.dates[type][i], date))
					return this.multiDatesPicker.dates[type].splice(i, 1).pop();
		}
		function removeIndex(index, type) {
			if(!type) type = 'picked';
			return this.multiDatesPicker.dates[type].splice(index, 1).pop();
		}
		function addDate(date, type, no_sort) {
			if(!type) type = 'picked';
			date = dateConvert.call(this, date);
			
			// @todo: use jQuery UI datepicker method instead
			date.setHours(0);
			date.setMinutes(0);
			date.setSeconds(0);
			date.setMilliseconds(0);
			
			if (methods.gotDate.call(this, date, type) === false) {
				this.multiDatesPicker.dates[type].push(date);
				if(!no_sort) this.multiDatesPicker.dates[type].sort(methods.compareDates);
			} 
		}
		function sortDates(type) {
			if(!type) type = 'picked';
			this.multiDatesPicker.dates[type].sort(methods.compareDates);
		}
		function dateConvert(date, desired_type, date_format) {
			if(!desired_type) desired_type = 'object';/*
			if(!date_format && (typeof date == 'string')) {
				date_format = $(this).datepicker('option', 'dateFormat');
				if(!date_format) date_format = $.datepicker._defaults.dateFormat;
			}
			*/
			return methods.dateConvert.call(this, date, desired_type, date_format);
		}
		
		var methods = {
			init : function( options ) {
				var $this = $(this);
				this.multiDatesPicker.changed = false;
				
				var mdp_events = {
					beforeShow: function(input, inst) {
						this.multiDatesPicker.changed = false;
						if(this.multiDatesPicker.originalBeforeShow) 
							this.multiDatesPicker.originalBeforeShow.call(this, input, inst);
					},
					onSelect : function(dateText, inst) {
                                                
						var $this = $(this);
						this.multiDatesPicker.changed = true;
						
						if (dateText) {
							$this.multiDatesPicker('toggleDate', dateText);
							this.multiDatesPicker.changed = true;
							// @todo: this will be optimized when I'll move methods to the singleton.
						}
						
						if (this.multiDatesPicker.mode == 'normal' && this.multiDatesPicker.pickableRange) {
                                                    
							if(this.multiDatesPicker.dates.picked.length > 0) {
                                                           
								var min_date = this.multiDatesPicker.dates.picked[0],
									max_date = new Date(min_date.getTime());
								methods.sumDays(max_date, this.multiDatesPicker.pickableRange-1);
                                                                
	                            var max_date1 = new Date(min_date.getTime());
	                            methods.sumDays(max_date1, this.multiDatesPicker.pickableRange-1);
								//                                                                console.debug('max_date1: '+max_date1);
								// counts the number of disabled dates in the range
								if(this.multiDatesPicker.adjustRangeToDisabled) {

								var c_disabled,disabled = this.multiDatesPicker.dates.disabled.slice(0); 
								var weekoff = this.multiDatesPicker.weekoff;
								//                                                                        var inc = 0; inc1 = 0;
								//									do {
								//                                                                            c_disabled = 0; inc = 0;
								//                                                                            for(var i = 0; i < disabled.length; i++) {
								//                                                                                
								//                                                                                if(disabled[i].getTime() <= max_date1.getTime()) { 
								//                                                                                    if((min_date.getTime() <= disabled[i].getTime()) && (disabled[i].getTime() <= max_date1.getTime())) {
								//                                                                                        
								//                                                                                        if( $.inArray(disabled[i].getDay(), weekoff ) === -1){ 
								//                                                                                            c_disabled++;
								//                                                                                            inc++; 
								//                                                                                            inc1++;
								//                                                                                        }   
								//                                                                                    }
								//                                                                                    disabled.splice(i, 1);
								//                                                                                    i--;
								//                                                                                }
								//                                                                            } 
								//                                                                            
								//                                                                            if(c_disabled) console.debug('number of holidays: '+ c_disabled);
								////                                                                            max_date.setDate(max_date.getDate() + c_disabled);
								//                                                                            while(inc){
								////                                                                                methods.skipHolidays(this, max_date, 1); 
								////                                                                                console.debug(inc+ '. inner while: ' + max_date);
								//                                                                                inc--;
								//                                                                            }
								////                                                                            console.debug('after while , inc: '+max_date);
								//									} while(c_disabled != 0);
								//                                                                        
								////                                                                        console.debug('after skipping disabled dates: '+ max_date);
								//                                                                        
								//                                                                        /* 1 sept - sankalp */
								//                                                                        var w_disabled = 0; 
								//                                                                        var week = [0,1,2,3,4,5,6]; 
								//                                                                        var weekdays = [];
								//                                                                        $.grep(week, function(el) {if ($.inArray(el, weekoff ) == -1) weekdays.push(el);});
								////                                                                        var r = parseInt(this.multiDatesPicker.pickableRange) + parseInt(inc) - 1; 
								//                                                                        var n_days = 1 + Math.floor((Date.UTC(max_date1.getFullYear(), max_date1.getMonth(), max_date1.getDate()) - Date.UTC(min_date.getFullYear(), min_date.getMonth(), min_date.getDate())) / (1000 * 60 * 60 * 24) );
								//                                                                        
								//                                                                        var r = parseInt(this.multiDatesPicker.pickableRange) + parseInt(inc1) - 1; 
								////                                                                        console.debug('inc: '+inc1+ ' ,r:'+r+' , n_days:'+n_days);
								////                                                                        console.debug('min_date: '+min_date);
								////                                                                        console.debug('max_date: '+max_date1);
								//                                                                        for(var j = 0; j < n_days ; j++) {
								//                                                                            var d = new Date(min_date);
								////                                                                            console.debug(d);console.debug(min_date.getDate()+j);
								//                                                                            d.setDate(min_date.getDate()+j); 
								////                                                                            console.debug('day: '+d.getDay()+ ', date: '+d);
								//                                                                            if($.inArray(d.getDay() ,weekoff) != -1)  w_disabled++;
								//                                                                        }
								//                                                                        
								//                                                                        console.debug('number of weekoffs :'+w_disabled);
								//                                                                        
								////                                                                        while(w_disabled){
								////                                                                            methods.skipWeekoffs(this, max_date, 1);
								////                                                                            w_disabled--;
								////                                                                        }
								//                                                                        
								////                                                                        console.debug('final: '+max_date);
                                                        
//                            console.debug('finally:' + parseInt(inc1 + w_disabled)); 
                               
//                             var count = parseInt(inc1 + w_disabled);
                             
                            var i = 0; var i2 = 0; var m_date = new Date(min_date); var arr = [];
                            var n_days = 1 + Math.floor((Date.UTC(max_date1.getFullYear(), max_date1.getMonth(), max_date1.getDate()) - Date.UTC(min_date.getFullYear(), min_date.getMonth(), min_date.getDate())) / (1000 * 60 * 60 * 24) );       
                                              
                            var holidays = [];
                            $.each(disabled, function(k){
                                if( $.inArray(disabled[k].getDay(), weekoff) === -1 ){
                                    holidays.push(disabled[k].getTime());
                                } 
                            });
                           
                            var weekdays = []; var week = [0,1,2,3,4,5,6];
                            
                            $.grep(week, function(el) {if ($.inArray(el, weekoff ) === -1) weekdays.push(el);});
                            
                            while(i < n_days){
                                 
//                                if(i == 14) { var nd = new Date(min_date);console.debug(nd); nd.setDate(min_date.getDate() + i2); console.debug('nd: '+ nd); m_date = new Date(nd) } 
//                                else{
                                    m_date.setDate(min_date.getDate() + i2);
//                                }
                                
                                
//                                console.debug(i+' => m_date:'+m_date);
                                
                                if( ($.inArray(m_date.getDay(), weekdays) !== -1) && ($.inArray(m_date.getTime(), holidays) === -1) ){
                          
                                     i++; 
//                                     if (i == 14 ) console.debug('i2:'+i2);
                                 }
                                 i2++; 
                             }
                            --i2;
                            var max_d = new Date();
                            
                            max_d.setDate(min_date.getDate() + i2 );
                                                                /* ends */
								}
                                                                
								if(this.multiDatesPicker.maxDate && (max_date > this.multiDatesPicker.maxDate))
									max_date = this.multiDatesPicker.maxDate;
								
								$this
									.datepicker("option", "minDate", min_date)
									.datepicker("option", "maxDate", max_date);
							} else {

								$this
									.datepicker("option", "minDate", this.multiDatesPicker.minDate)
									.datepicker("option", "maxDate", this.multiDatesPicker.maxDate);
							}
						}
						
						if(this.multiDatesPicker.originalOnSelect && dateText)
							this.multiDatesPicker.originalOnSelect.call(this, dateText, inst);
						
					},
					beforeShowDay : function(date) {
						var $this = $(this),
							gotThisDate = $this.multiDatesPicker('gotDate', date) !== false,
							isDisabledCalendar = $this.datepicker('option', 'disabled'),
							isDisabledDate = $this.multiDatesPicker('gotDate', date, 'disabled') !== false,
							areAllSelected = this.multiDatesPicker.maxPicks <= this.multiDatesPicker.dates.picked.length;
						
                                                var selectectable_weekoff = false;
                                                var weekoffFlg;

                                                var tep = dateConvert.call(this, date);

                                                weekoffFlg = $.inArray(tep.getDay(),this.multiDatesPicker.weekoff);

                                                if(weekoffFlg == -1){
                                                        selectectable_weekoff = true;
                                                }
                                                
						var bsdReturn = [true, '', null];
						if(this.multiDatesPicker.originalBeforeShowDay)
							bsdReturn = this.multiDatesPicker.originalBeforeShowDay.call(this, date);
						
						bsdReturn[1] = gotThisDate ? 'ui-state-highlight' : bsdReturn[1];
						bsdReturn[0] = bsdReturn[0] && !(isDisabledCalendar || isDisabledDate || (areAllSelected && !bsdReturn[1])) && selectectable_weekoff;
						return bsdReturn;
					}
				};
				
				// value have to be extracted before datepicker is initiated
				if($this.val()) var inputDates = $this.val()
				this.multiDatesPicker.separator = ', ';
				
				if(options) {
					// value have to be extracted before datepicker is initiated
					//if(options.altField) var inputDates = $(options.altField).val();
					if(options.separator) this.multiDatesPicker.separator = options.separator;
					
					this.multiDatesPicker.originalBeforeShow = options.beforeShow;
					this.multiDatesPicker.originalOnSelect = options.onSelect;
					this.multiDatesPicker.originalBeforeShowDay = options.beforeShowDay;
					this.multiDatesPicker.originalOnClose = options.onClose;
					
					// datepicker init
					$this.datepicker(options);
					
					this.multiDatesPicker.minDate = $.datepicker._determineDate(this, options.minDate, null);
					this.multiDatesPicker.maxDate = $.datepicker._determineDate(this, options.maxDate, null);
					if(options.addDates) methods.addDates.call(this, options.addDates);
					 
					if(options.addDisabledDates)
						methods.addDates.call(this, options.addDisabledDates, 'disabled');
					
					methods.setMode.call(this, options);
				} else {
					$this.datepicker();
				}
				$this.datepicker('option', mdp_events);
				
				// adds any dates found in the input or alt field
				if(inputDates) $this.multiDatesPicker('value', inputDates);
				
				// generates the new string of added dates
				var inputs_values = $this.multiDatesPicker('value');
				
				// fills the input field back with all the dates in the calendar
				if(this.tagName == 'INPUT')	$this.val(inputs_values);
				
				// Fixes the altField filled with defaultDate by default
				var altFieldOption = $this.datepicker('option', 'altField');
				if (altFieldOption) $(altFieldOption).val(inputs_values);
				
				// Updates the calendar view
//				$this.datepicker('refresh');
			},
			compareDates : function(date1, date2) {
				date1 = dateConvert.call(this, date1);
				date2 = dateConvert.call(this, date2);
				// return > 0 means date1 is later than date2 
				// return == 0 means date1 is the same day as date2 
				// return < 0 means date1 is earlier than date2 
				var diff = date1.getFullYear() - date2.getFullYear();
				if(!diff) {
					diff = date1.getMonth() - date2.getMonth();
					if(!diff) 
						diff = date1.getDate() - date2.getDate();
				}
				return diff;
			},
			sumDays : function( date, n_days ) {
				var origDateType = typeof date;
				obj_date = dateConvert.call(this, date);
				obj_date.setDate(obj_date.getDate() + n_days);
				return dateConvert.call(this, obj_date, origDateType);
			},
			dateConvert : function( date, desired_format, dateFormat ) {
				var from_format = typeof date;
				
				if(from_format == desired_format) {
					if(from_format == 'object') {
						try {
							date.getTime();
						} catch (e) {
							$.error('Received date is in a non supported format!');
							return false;
						}
					}
					return date;
				}
				
				var $this = $(this);
				if(typeof date == 'undefined') date = new Date(0);
				
				if(desired_format != 'string' && desired_format != 'object' && desired_format != 'number')
					$.error('Date format "'+ desired_format +'" not supported!');
				
				if(!dateFormat) {
					dateFormat = $.datepicker._defaults.dateFormat;
					
					// thanks to bibendus83 -> http://sourceforge.net/tracker/index.php?func=detail&aid=3213174&group_id=358205&atid=1495382
					var dp_dateFormat = $this.datepicker('option', 'dateFormat');
					if (dp_dateFormat) {
						dateFormat = dp_dateFormat;
					}
				}
				
				// converts to object as a neutral format
				switch(from_format) {
					case 'object': break;
					case 'string': date = $.datepicker.parseDate(dateFormat, date); break;
					case 'number': date = new Date(date); break;
					default: $.error('Conversion from "'+ desired_format +'" format not allowed on jQuery.multiDatesPicker');
				}
				// then converts to the desired format
				switch(desired_format) {
					case 'object': return date;
					case 'string': return $.datepicker.formatDate(dateFormat, date);
					case 'number': return date.getTime();
					default: $.error('Conversion to "'+ desired_format +'" format not allowed on jQuery.multiDatesPicker');
				}
				return false;
			},
			gotDate : function( date, type ) {
				if(!type) type = 'picked';
				for(var i = 0; i < this.multiDatesPicker.dates[type].length; i++) {
					if(methods.compareDates.call(this, this.multiDatesPicker.dates[type][i], date) === 0) {
						return i;
					}
				}
				return false;
			},
			value : function( value ) {
				if(value && typeof value == 'string') {
					methods.addDates.call(this, value.split(this.multiDatesPicker.separator));
				} else {
					var dates = methods.getDates.call(this, 'string');
					return dates.length
						? dates.join(this.multiDatesPicker.separator)
						: "";
				}
			},
			getDates : function( format, type ) {
				if(!format) format = 'string';
				if(!type) type = 'picked';
				switch (format) {
					case 'object':
						return this.multiDatesPicker.dates[type];
					case 'string':
					case 'number':
						var o_dates = new Array();
						for(var i in this.multiDatesPicker.dates[type])
							o_dates.push(
								dateConvert.call(
									this, 
									this.multiDatesPicker.dates[type][i], 
									format
								)
							);
						return o_dates;
					
					default: $.error('Format "'+format+'" not supported!');
				}
			},
			addDates : function( dates, type ) {
				if(dates.length > 0) {
					if(!type) type = 'picked';
					switch(typeof dates) {
						case 'object':
						case 'array':
							if(dates.length) {
								for(var i = 0; i < dates.length; i++)
									addDate.call(this, dates[i], type, true);
								sortDates.call(this, type);
								break;
							} // else does the same as 'string'
						case 'string':
						case 'number':
							addDate.call(this, dates, type);
							break;
						default: 
							$.error('Date format "'+ typeof dates +'" not allowed on jQuery.multiDatesPicker');
					}
					//$(this).datepicker('refresh');
				} else {
					$.error('Empty array of dates received.');
				}
			},
			removeDates : function( dates, type ) {
				if(!type) type = 'picked';
				var removed = [];
				if (Object.prototype.toString.call(dates) === '[object Array]') {
					for(var i in dates.sort(function(a,b){return b-a})) {
						removed.push(removeDate.call(this, dates[i], type));
					}
				} else {
					removed.push(removeDate.call(this, dates, type));
				}
				return removed;
			},
			removeIndexes : function( indexes, type ) {
				if(!type) type = 'picked';
				var removed = [];
				if (Object.prototype.toString.call(indexes) === '[object Array]') {
					for(var i in indexes.sort(function(a,b){return b-a})) {
						removed.push(removeIndex.call(this, indexes[i], type));
					}
				} else {
					removed.push(removeIndex.call(this, indexes, type));
				}
				return removed;
			},
			resetDates : function ( type ) {
				if(!type) type = 'picked';
				this.multiDatesPicker.dates[type] = [];
			},
			toggleDate : function( date, type ) {
				if(!type) type = 'picked';
				
				switch(this.multiDatesPicker.mode) {
					case 'daysRange':
						this.multiDatesPicker.dates[type] = []; // deletes all picked/disabled dates
						var end = this.multiDatesPicker.autoselectRange[1];
						var begin = this.multiDatesPicker.autoselectRange[0];
						if(end < begin) { // switch
							end = this.multiDatesPicker.autoselectRange[0];
							begin = this.multiDatesPicker.autoselectRange[1];
						}
						for(var i = begin; i < end; i++) 
							methods.addDates.call(this, methods.sumDays(date, i), type);
						break;
					default:
						if(methods.gotDate.call(this, date) === false) // adds dates
							methods.addDates.call(this, date, type);
						else // removes dates
							methods.removeDates.call(this, date, type);
						break;
				}
			}, 
			setMode : function( options ) {
				var $this = $(this);
				if(options.mode) this.multiDatesPicker.mode = options.mode;
				
				switch(this.multiDatesPicker.mode) {
					case 'normal':
						for(option in options)
							switch(option) {
								case 'maxPicks':
								case 'minPicks':
								case 'pickableRange':
                                                                case 'weekoff' :
								case 'adjustRangeToDisabled':
									this.multiDatesPicker[option] = options[option];
									break;
								//default: $.error('Option ' + option + ' ignored for mode "'.options.mode.'".');
							}
					break;
					case 'daysRange':
					case 'weeksRange':
						var mandatory = 1;
						for(option in options)
							switch(option) {
								case 'autoselectRange':
									mandatory--;
								case 'pickableRange':
                                                                case 'weekoff' :
								case 'adjustRangeToDisabled':
									this.multiDatesPicker[option] = options[option];
									break;
								//default: $.error('Option ' + option + ' does not exist for setMode on jQuery.multiDatesPicker');
							}
						if(mandatory > 0) $.error('Some mandatory options not specified!');
					break;
				}
				
				/*
				if(options.pickableRange) {
					$this.datepicker("option", "maxDate", options.pickableRange);
					$this.datepicker("option", "minDate", this.multiDatesPicker.minDate);
				}
				*/
				
				if(mdp_events.onSelect)
					mdp_events.onSelect();
			},
			destroy: function(){
				this.multiDatesPicker = null;
				$(this).datepicker('destroy');
			},
                        skipWeekoffs: function(obj, max_date, iter){
                            
                            var weekoff = obj.multiDatesPicker.weekoff;
                            var week = [0,1,2,3,4,5,6]; 
                            var weekdays = [];
                            $.grep(week, function(el) {if ($.inArray(el, weekoff ) === -1) weekdays.push(el);});
                            
                            var i = $.inArray( max_date.getDay(), weekdays );
                            
                            /* if max_date falls on weekends */ 
                            if(i === -1){
                                $.each(weekdays, function(j){
                                    if( max_date.getDay() >  Math.max.apply(Math, weekdays)){
                                        max_date.setDate(max_date.getDate() + (7 + weekdays[0] - max_date.getDay()) % 7);
                                        return false;
                                    }
                                    if(weekdays[j] > max_date.getDay()){
                                        max_date.setDate(max_date.getDate() + (7 + weekdays[j] - max_date.getDay()) % 7);
                                        return false;
                                    }
                                });
                            }else{
                                /* if max_date falls on weekdays */
                                if(i === weekdays.length - 1) {
                                    max_date.setDate(max_date.getDate() + (7 + weekdays[0] - max_date.getDay()) % 7);
                                }else{ 
                                    max_date.setDate(max_date.getDate() + (7 + weekdays[i+1] - max_date.getDay()) % 7);
                                }
                                iter = 1;
                            }
                            
                            // if the newly set max_date is a holiday
                            if(iter !== 0)  methods.skipHolidays(obj, max_date, 0);
                            
                        },
                        skipHolidays: function(obj, max_date, iter ){
                            
                            var disabled = obj.multiDatesPicker.dates.disabled;
                            
                            var holidays = [];
                            $.each(disabled, function(k){
                                holidays.push(disabled[k].getTime()); 
                            });
                            
                            var index = $.inArray(max_date.getTime(), holidays); 
                            /* if max_date falls on holiday */
                            if(index !== -1){
                                $.each(holidays, function(k){
                                    
                                    if($.inArray(max_date.getDay(), obj.multiDatesPicker.weekoff)  === -1){
                                        if(holidays[k] === max_date.getTime() ){

                                                max_date.setDate(max_date.getDate() + 1); 

                                                if(iter == 0 && $.inArray(max_date.getDay(),obj.multiDatesPicker.weekoff ) !== -1 ){
                                                    iter = 1;
                                                }
//                                                console.debug('k: '+k+" ,max_date: "+max_date);
                                                if( (k !== holidays.length - 1) && (max_date.getTime() === holidays[k+1] ) ){
                                                    return true;
                                                }else{
                                                    iter = 0;
                                                    return false;
                                                }
                                        }else{
                                            
                                            methods.skipWeekoffs(obj, max_date, 0); return false;
                                            
                                        }
                                    }
                                });
                            }else{
                                /* max_date is not on holidays */
                                if(iter !== 0){ methods.skipWeekoffs(obj, max_date, 0);return false;}
                                
                            }
                            
//                            console.debug('holiday method: '+max_date);
                             // if the newly set max_date is a weekday
                            if(iter !== 0)  methods.skipWeekoffs(obj, max_date, iter);
                        }
		};
		
		this.each(function() {
			var $this = $(this);
			if (!this.multiDatesPicker) {
				this.multiDatesPicker = {
					dates: {
						picked: [],
						disabled: []
					},
					mode: 'normal',
					adjustRangeToDisabled: true
				};
			}
			
			if(methods[method]) {
				var exec_result = methods[method].apply(this, Array.prototype.slice.call(mdp_arguments, 1));
				switch(method) {
					case 'removeDates':
					case 'removeIndexes':
					case 'resetDates':
					case 'toggleDate':
					case 'addDates':
						var altField = $this.datepicker('option', 'altField');
						// @todo: should use altFormat for altField
						var dates_string = methods.value.call(this);
                                                
						if (altField !== undefined && altField != "") {
                                                    
							$(altField).val(dates_string);
						}
                                                
						if(this.tagName == 'INPUT') { // for inputs
							$this.val(dates_string);
						}
//						console.debug($(altField).val()); debugger;
						//$.datepicker._refreshDatepicker(this);
				}
				switch(method) {
					case 'removeDates':
					case 'getDates':
					case 'gotDate':
					case 'sumDays':
					case 'compareDates':
					case 'dateConvert':
					case 'value':
						ret = exec_result;
				}
                                
				return exec_result;
			} else if( typeof method === 'object' || ! method ) {
				return methods.init.apply(this, mdp_arguments);
			} else {
				$.error('Method ' +  method + ' does not exist on jQuery.multiDatesPicker');
			}
			return false;
		}); 
		
		return ret;
	};

	var PROP_NAME = 'multiDatesPicker';
	var dpuuid = new Date().getTime();
	var instActive;

	$.multiDatesPicker = {version: false};
	//$.multiDatesPicker = new MultiDatesPicker(); // singleton instance
	$.multiDatesPicker.initialized = false;
	$.multiDatesPicker.uuid = new Date().getTime();
	$.multiDatesPicker.version = $.ui.multiDatesPicker.version;
	
	// allows MDP not to hide everytime a date is picked
	$.multiDatesPicker._hideDatepicker = $.datepicker._hideDatepicker;
	$.datepicker._hideDatepicker = function(){
		var target = this._curInst.input[0];
		var mdp = target.multiDatesPicker;
		if(!mdp || (this._curInst.inline === false && !mdp.changed)) {
			return $.multiDatesPicker._hideDatepicker.apply(this, arguments);
		} else {
			mdp.changed = false;
			$.datepicker._refreshDatepicker(target);
			return;
		}
	};

	// Workaround for #4055
	// Add another global to avoid noConflict issues with inline event handlers
	window['DP_jQuery_' + dpuuid] = $;
})( jQuery );
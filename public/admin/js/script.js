var ww,
    wh;
function get_height_width() {
	ww = $(window).width();
	wh = $(window).height();
}

function shuffle_product() {
	$('.shuffleContainer').each(function() {
		gridContainer = $(this);
		var sizer = gridContainer.find('.columns');
		gridContainer.shuffle({
			sizer : sizer,
			speed : 500,
			easing : 'ease-out'
		});
	});
}


$(document).ready(function() {

	//Go to Top
	$(function() {
		jQuery("#toTop").scrollToTop(1000);
	});

	//table Functionality

	jQuery('.portlet .tools a.remove').click(function() {
		var removable = jQuery(this).parents(".portlet");
		if (removable.next().hasClass('portlet') && removable.prev().hasClass('portlet')) {
			jQuery(this).parents(".portlet").remove();
		} else {
			jQuery(this).parents(".portlet").parent().remove();
		}
	});

	jQuery('.portlet .tools a.reload').click(function() {
		var el = jQuery(this).parents(".portlet");
		App.blockUI(el);
		window.setTimeout(function() {
			App.unblockUI(el);
		}, 1000);
	});

	jQuery('.portlet .tools .collapse, .portlet .tools .expand').click(function() {
		var el = jQuery(this).parents(".portlet").children(".portlet-body");
		if (jQuery(this).hasClass("collapse")) {
			jQuery(this).removeClass("collapse").addClass("expand");
			el.slideUp(200);
		} else {
			jQuery(this).removeClass("expand").addClass("collapse");
			el.slideDown(200);
		}
	});

	//mobile toggler
	$(".mobile-toggler").click(function(e) {
		//$(".page-sidebar").toggleClass("mobile-menuOpen");
		$('.mobile-view .mobile-menu').slideToggle('slow', function() {
			// Animation complete.
		});
	});

	function checkSizeW() {
		if ($(window).width() > 480) {
			$('.page-sidebar').show();
		} else {
			$('.page-sidebar').hide();
		}
	}


	$(window).resize(checkSizeW);
	$(document).ready(checkSizeW);

	function checkWindowSize() {
		if ($(window).width() < 1024 && $(window).width() > 799) {
			$('.page-container').addClass('sidebar-closed');
			$('.page-container').removeClass('mobile-view');
		} else if ($(window).width() <= 800 && $(window).width() > 599) {
			$('.page-container').addClass('sidebar-closed');
			$('.page-container').removeClass('mobile-view');
		} else if ($(window).width() <= 600) {
			$('.page-container').addClass('mobile-view');
			$('.page-container').removeClass('sidebar-closed');
		} else {
			$('.page-container').removeClass('mobile-view');
		}

	}


	$(window).resize(checkWindowSize);
	$(document).ready(checkWindowSize);

	// Menu sub class
	jQuery('.page-sidebar .has-sub > a').click(function() {

		var handleContentHeight = function() {
			var content = $('.page-content');
			var sidebar = $('.page-sidebar');

			if (!content.attr("data-height")) {
				content.attr("data-height", content.height());
			}

			if (sidebar.height() > content.height()) {
				content.css("min-height", sidebar.height() + 20);
			} else {
				content.css("min-height", content.attr("data-height"));
			}
		}
		var last = jQuery('.has-sub.open', $('.page-sidebar'));
		if (last.size() == 0) {
			//last = jQuery('.has-sub.active', $('.page-sidebar'));
		}
		last.removeClass("open");
		jQuery('.arrow', last).removeClass("open");
		jQuery('.sub', last).slideUp(200);

		var sub = jQuery(this).next();
		if (sub.is(":visible")) {
			jQuery('.arrow', jQuery(this)).removeClass("open");
			jQuery(this).parent().removeClass("open");
			sub.slideUp(200, function() {
				handleContentHeight();
			});
		} else {
			jQuery('.arrow', jQuery(this)).addClass("open");
			jQuery(this).parent().addClass("open");
			sub.slideDown(200, function() {
				handleContentHeight();
			});
		}
	});

	// menu sub sub
	jQuery('.page-sidebar .has-sub-sub > a').click(function() {

		var handleContentHeight = function() {
			var content = $('.page-content');
			var sidebar = $('.page-sidebar');

			if (!content.attr("data-height")) {
				content.attr("data-height", content.height());
			}

			if (sidebar.height() > content.height()) {
				content.css("min-height", sidebar.height() + 20);
			} else {
				content.css("min-height", content.attr("data-height"));
			}
		}
		var last = jQuery('.has-sub-sub.open', $('.page-sidebar'));
		if (last.size() == 0) {
			//last = jQuery('.has-sub.active', $('.page-sidebar'));
		}
		last.removeClass("open");
		jQuery('.arrow', last).removeClass("open");
		jQuery('.sub', last).slideUp(200);

		var sub = jQuery(this).next();
		if (sub.is(":visible")) {
			jQuery('.arrow', jQuery(this)).removeClass("open");
			jQuery(this).parent().removeClass("open");
			sub.slideUp(200, function() {
				handleContentHeight();
			});
		} else {
			jQuery('.arrow', jQuery(this)).addClass("open");
			jQuery(this).parent().addClass("open");
			sub.slideDown(200, function() {
				handleContentHeight();
			});
		}
	});


        //(pooja)
	$(":radio:not(.color-palette-radio), :checkbox").uniform();
	//(end)


	var container = $(".page-container");

	if ($.cookie('sidebar-closed') == 1 && $(window).width() >= 601) {
		container.addClass("sidebar-closed");
		$('.sub').addClass('subSmall');
	} else if ($.cookie('sidebar-closed') == 1 && $(window).width() <= 600) {
		container.addClass("mobile-view");
		container.removeClass("sidebar-closed");
	}

	// handle sidebar show/hide
	$('.page-sidebar .sidebar-toggler').click(function() {
		$(".sidebar-search").removeClass("open");
		var container = $(".page-container");
		if (container.hasClass("sidebar-closed") === true) {
			container.removeClass("sidebar-closed");
			$(window).resize();
			$.cookie('sidebar-closed', null);
		} else {
			container.addClass("sidebar-closed");
			$.cookie('sidebar-closed', 1);
		}
	});

	// handle the search bar close
	$('.sidebar-search .remove').click(function() {
		$('.sidebar-search').removeClass("open");
	});

	// handle the search query submit on enter press
	$('.sidebar-search input').keypress(function(e) {
		if (e.which == 13) {
			window.location.href = "search.html";
			return false;
			//<---- Add this line
		}
	});

	// handle the search submit
	$('.sidebar-search .submit').click(function() {
		if ($('.page-container').hasClass("sidebar-closed")) {
			if ($('.sidebar-search').hasClass('open') == false) {
				$('.sidebar-search').addClass("open");
			} else {
				window.location.href = "search.html";
			}
		} else {
			window.location.href = "search.html";
		}
	});

	$('.sidebar-toggler').click(function() {

		$('.displayTable').getNiceScroll().remove();
		$('.displayTable').niceScroll().resize();

		if ($('.page-container').hasClass('sidebar-closed')) {
			$('.sub').addClass('subSmall');
		} else {
			$('.sub').removeClass('subSmall');
		}
	});

});

(function(a) {
	a.fn.scrollToTop = function(c) {
		var d = {
			speed : 800
		};
		c && a.extend(d, {
			speed : c
		});
		return this.each(function() {
			var b = a(this);
			a(window).scroll(function() {
				100 < a(this).scrollTop() ? b.fadeIn() : b.fadeOut()
			});
			b.click(function(b) {
				b.preventDefault();
				a("body, html").animate({
					scrollTop : 0
				}, d.speed)
			})
		})
	}
})(jQuery);

$(window).bind("load", function() {

	shuffle_product();

	var footerHeight = 0,
	    footerTop = 0,
	    $footer = $("#footer");
	positionFooter();
	function positionFooter() {
		footerHeight = $footer.height();
		footerTop = ($(window).scrollTop() + $(window).height() - footerHeight - 0) + "px";

		if (($(document.body).height() + footerHeight) < $(window).height()) {
			$footer.css({
				position : "absolute",
				bottom : 0,
				left : 0

			})
		} else {
			$footer.css({
				position : "relative"
			})
		}
	}


	$(window).resize(positionFooter)
	$(document).ready(positionFooter)
	$(".page-container").mouseover(positionFooter)
	$(".content").mouseover(positionFooter)

});

//Table width scroll

var myPageTable = (function() {

	var that = {};

	that.init = function() {

		var width = $(window).width();
		//var height = $(window).height();
		//var rowCount = $('table#customer tr:last').index() + 1;
		var numCols = $('table.displayTable').find('tr')[0].cells.length;

		if ((width >= 1366) && (numCols >= 13)) {
			tableHeight[ "default" ]();
			tableHeight[ "scrollDiv" ]();
		} else if ((width >= 1366) && (numCols <= 12)) {
			tableHeight[ "noHeight" ]();
		} else if ((width >= 1024  ) && (numCols >= 5)) {
			tableHeight[ "default" ]();
			tableHeight[ "scrollDiv" ]();
		} else if ((width <= 1024  ) && (numCols <= 3)) {
			tableHeight[ "noHeight" ]();
		} else {
			tableHeight[ "default" ]();
			tableHeight[ "scrollDiv" ]();
		}

	}
	return that;

})();

$("button").on("click", function() {
	var el = $(this);
	if (el.html() == el.data("text-swap")) {
		el.html(el.data("text-original"));
	} else {
		el.data("text-original", el.html());
		el.html(el.data("text-swap"));
	}
	setTimeout(function() {
		el.html(el.data("text-original"));
	}, 500);
});

function side_nav_height() {
	$('.page-sidebar > ul').height((wh - $('.top-bar').innerHeight()) + "px");
}

function getKitchenOverview(id){
//    alert(id+'wq');
    var str = '[';
    var cat;
    $.ajax({
        url: "/dashboard/getKitchenOverview",
        method: 'POST',
        data:{id:id},
        dataType:"json",
        async: false,
        success : function(data){
            var data1 = eval(data);
            console.log(data1);
            if(!jQuery.isEmptyObject(data1)){
                var categories = [];
                categories = data1.product_name; 
                cat = categories.replace(/,\s*$/, "");
                cat = cat.split(",");
                console.log(cat);
                delete data1.product_name;
                //console.log(data1);
                $.each(data1, function(i, e){
                    str+='{';
                    str+='name : "'+i+'",';
                    str+='data : ['+e.replace(/,\s*$/, "")+']';
                    str+='},';                
                });
                str = str.replace(/,\s*$/, "");
                str += ']';  
            //}else{
              //  str += '{name:"",data:[0]}]';
            //}
            str = eval(str);
            console.log(str);
            $('#yearly_chart').highcharts({
                chart : {
                    type : 'bar'
                },
                title : {
                    text : 'Popular products for '+CURRENTKITCHEN
                },
                credits : {
                    enabled : false
                },
                subtitle : {
                    text : 'Source: <a href="http://www.fooddialer.co.in">fooddialer.co.in</a>'
                },
                xAxis : {
                    categories : cat,
                    title : {
                        text : 'Products'
                    }
                },
                yAxis : {
                    min : 0,
                    title : {
                        text : 'Quantity',
                    },
                    labels : {
                        overflow : 'justify'
                    }
                },
                tooltip : {
                    valueSuffix : ''
                },
                plotOptions : {
                    bar : {
                        dataLabels : {
                            enabled : true
                        }
                    }
                },
                legend : {
                    layout : 'vertical',
                    align : 'right',
                    verticalAlign : 'top',
                    x : -40,
                    y : 80,
                    floating : true,
                    borderWidth : 1,
                    backgroundColor : ((Highcharts.theme && Highcharts.theme.legendBackgroundColor) || '#FFFFFF'),
                    shadow : true
                },
                credits : {
                    enabled : false
                },
                series : str 
            });             
        }else{
      	  $('#yearly_chart').html("There is no products to display");
        }
        }
    });   
}


function getTodayDeliverChart(id){
	var str='';
	str+="[{name: 'Total Orders',data: [";
	$.ajax({
        url: "/dashboard/getTodayDeliverChart",
        method: 'POST',
        data:{id:id},
        dataType:"json",
        async: false,
        success : function(data){
            //var data1 = eval(data);
//            console.log(data);debugger;
            if(data.length > 0){
            $.each(data,function(f,g){
                str+='["'+g.order_menu.substr(0, 1).toUpperCase() + g.order_menu.substr(1)+'",'+g.count+'],';        
            });
            str = str.replace(/,\s*$/, "");
            str+= ']}]';
            
			$('#chart_div2').highcharts({
			    chart: {
		            type: 'column'
		        },
		        title: {
		            text: 'Todays Order to Deliver for '+CURRENTKITCHEN
		        },
		        subtitle: {
		            text: 'Source: <a href="http://www.fooddialer.co.in">fooddialer.co.in</a>'
		        },
		        xAxis: {
		            type: 'category',
		            labels: {
		                rotation: -45,
		                style: {
		                    fontSize: '13px',
		                    fontFamily: 'Verdana, sans-serif'
		                }
		            }
		        },
		        yAxis: {
		            min: 0,
		            title: {
		                text: 'Quantity'
		            }
		        },
		        legend: {
		            enabled: false
		        },
		        tooltip: {
		            pointFormat: 'Todays orders - <b>{point.y:.1f}</b>'
		        },
		        series : eval(str) ,
			});
          }else{
        	  $('#chart_div2').html("There is no order to deliver for today");
          }
        }
	});
}
function getTodayOrdersrChart(id){
	var str='';
	str+="[";
	$.ajax({
        url: "/dashboard/getTodayOrdersrChart",
        method: 'POST',
        data:{id:id},
        dataType:"json",
        async: false,
        success : function(data){
        	if(data.length > 0){
	        	$.each(data,function(f,g){
	                str+='{name:"'+g.order_menu+'",y:'+g.count+'},';        
	            });
	        	str = str.replace(/,\s*$/, "");
	        	str+="]";
	        	console.log(str);
	        	$('#chart_div3').highcharts({
	    			chart : {
	    				plotBackgroundColor : null,
	    				plotBorderWidth : null,
	    				plotShadow : false,
	    				type : 'pie'
	    			},
	    			credits : {
	    				enabled : false
	    			},
	    			title : {
	    				text : 'New Orders Placed Today for '+CURRENTKITCHEN
	    			},
	    			tooltip : {
	    				pointFormat : '{series.name}: <b>{point.percentage:.1f}%</b>'
	    			},
	    			plotOptions : {
	    				pie : {
	    					allowPointSelect : true,
	    					cursor : 'pointer',
	    					dataLabels : {
	    						enabled : false
	    					},
	    					showInLegend : true
	    				}
	    			},
	    			series : [{
	    				name : "Brands",
	    				colorByPoint : true,
	    				data : eval(str)
	    			}]
	    		});
        	}else{
        		$('#chart_div3').html("There is no order placed for today");
        	}
        }
	});
}

var tableHeight;
var checkednicescroll;
function resize_scrollbar() {
	$('.dataTables_scrollBody').getNiceScroll().remove();
	tableHeight = {
		"autoHeight" : function() {

			var height = $(window).height() - 400;
			$('.displayTable').dataTable({
				stateSave : true,
				"scrollY" : height,
				"scrollX" : true,
				"bAutoWidth" : false,
				"scrollCollapse" : true
			});

		},
		"noHeight" : function() {
			$('.displayTable').dataTable({
				stateSave : true,
				"bAutoWidth" : false,
				"scrollCollapse" : true
			});

		},
		"default" : function() {
			$('.displayTable').dataTable({
				stateSave : true,
				"scrollY" : 324,
				"scrollX" : true,
				"bAutoWidth" : false,
				"scrollCollapse" : true
			});

		},
		"scrollDiv" : function() {
			checkednicescroll = true;
			$(".dataTables_scrollBody").niceScroll({
				touchbehavior : false,
				cursorcolor : "#555",
				cursoropacitymax : 0.8,
				cursorwidth : 9,
				cursorborder : "1px solid #333",
				cursorborderradius : "8px",
				background : "#ccc",
				autohidemode : "false",
				nativeparentscrolling : "false",
				enablescrollonselection : "false"
			}).cursor.css({
				"background" : "#555"
			}).resize();
		}
	};
}

function advance_serach_show() {
	$(".advance_search_click").click(function() {
		$('.dataTables_scrollBody').getNiceScroll().remove();
		
		
		$(this).parent().parent().parent().parent().siblings().children('.advance_search').slideToggle(function() {
			if (checkednicescroll == true) {

				$(".dataTables_scrollBody").niceScroll({
					touchbehavior : false,
					cursorcolor : "#555",
					cursoropacitymax : 0.8,
					cursorwidth : 9,
					cursorborder : "1px solid #333",
					cursorborderradius : "8px",
					background : "#ccc",
					autohidemode : "false",
					nativeparentscrolling : "false",
					enablescrollonselection : "false"
				}).cursor.css({
					"background" : "#555"
				}).resize();
			}
		});
		// $(".advance_search").html.slideToggle();

	});

	var cntt = 0;
	$(".advance_search_click").click(function() {
		if (cntt % 2 == 0) {
			$(this).html('Advance search ');
		} else {
			$(this).html('Hide advance search');
		}
		cntt++;

	});
}

function side_menu_scroll() {
	$(".page-sidebar ul").mCustomScrollbar({
					scrollButtons:{enable:true},
					theme:"dark-thin",
					scrollbarPosition:"outside"
	});
}
var productDelect = function() {
	/*$('.product-name').click(function() {
		$(this).toggleClass('active');
	});*/
	$(document).on('click','.product-name',function() {
		$(this).toggleClass('active');
	});
}
var selectProductHeight = function() {
	var leftProductHeight = (wh - ($('.top-bar').outerHeight() + $('.product-search').outerHeight() + $('.prod_head').outerHeight() + 15 + $('#footer').outerHeight() + $('.common-tabs').outerHeight()));

	
	$('.select-product-wrapper').css('height', leftProductHeight);
}
function selectProductHeight_scroll() {
	$(".custom-scroll").mCustomScrollbar({
		scrollButtons : {
			enable : true
		},
		theme : "dark-thin",
		scrollbarPosition : "outside"
	});
}

var selectProductAppend = function() {
	$(document).on('dblclick', ".left-product .product-name", function(){
		$(this).removeClass('active').appendTo('.right-product .mCustomScrollBox .mCSB_container');
	});
	$(document).on('dblclick', ".right-product .product-name", function(){
		$(this).removeClass('active').appendTo('.left-product .mCustomScrollBox .mCSB_container');
	});
	
	$('.move-right').click(function(){
		$('.left-product .product-name.active').removeClass('active').appendTo('.right-product .mCustomScrollBox .mCSB_container');
	
	});
	$('.move-double-right').click(function(){
		$('.left-product .product-name').appendTo('.right-product .mCustomScrollBox .mCSB_container');
	
	});
	$('.move-left').click(function(){
		$('.right-product .product-name.active').removeClass('active').appendTo('.left-product .mCustomScrollBox .mCSB_container');
	});
	$('.move-double-left').click(function(){
		$('.right-product .product-name').appendTo('.left-product .mCustomScrollBox .mCSB_container');
	});
	
}


function isNumber(evt) {
    evt = (evt) ? evt : window.event;
    var charCode = (evt.which) ? evt.which : evt.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode != 46 ) {
        return false;
    }
    return true;
}

var sc = 0;
function service_click() {

	$('.slide_click').click(function() {
		if ($(this).hasClass('open') == false) {
			$(this).addClass('open');
			$(this).parent().parent().animate({
				'right' : 0
			});
		} else {
			$(this).removeClass('open');
			$(this).parent().parent().animate({
				'right' : '-' + ($(this).parent().width() - 35) + 'px'
			});

		}
	});
}

$(document).ready(function() {
	$("#minDate").datepicker({
		autoSize : true
	});
	$("#maxDate").datepicker({
		autoSize : true
	});

//	HD_Chart("Revenue_share");
//	change_event();
//	HD_Chart_Food_Item("Food_Item");
//	change_event_Food_Item();
service_click();
	get_height_width();
	side_nav_height();
	resize_scrollbar();
	advance_serach_show();
	side_menu_scroll();
	productDelect();
	selectProductHeight();
	selectProductHeight_scroll();
	//selectProductAppend();
	$('.sidebar-toggler').click(function() {
		side_menu_scroll();
	});

});
$(window).resize(function() {
	get_height_width();
	side_nav_height();
});

//Table width scroll End


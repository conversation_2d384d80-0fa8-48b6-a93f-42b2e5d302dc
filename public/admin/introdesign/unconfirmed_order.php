<!DOCTYPE HTML>
<html>
	<head>
		<?php
		include_once 'style_links.php';
		?>
	</head>

	<body>

		<!-- Wrapper -->
		<?php
		include_once 'topbar.php';
		?>

		<!-- END TOP NAVIGATION BAR -->

		<!-- B<PERSON><PERSON> CONTAINER -->
		<div class="page-container clearfix">
			<!-- BEGIN SIDEBAR -->
			<?php
			include_once 'sidebar.php';
			?>

			<!-- END SIDEBAR -->
			<!-- BEGIN PAGE -->
			<div class="page-content">

				<!-- BEGIN PAGE CONTAINER-->
				<div class="container-fluid">
					<!-- BEGIN PAGE HEADER-->

					<div class="large-12 columns">

						<!-- BEGIN PAGE TITLE & BREADCRUMB-->
						<h3 class="page-title">Unconfirmed Orders <small>Orders to be confirmed</small></h3>

						<ul class="breadcrumb">
							<li>
								<i class="fa fa-home"></i><a href="dashboard.php">Home</a><i class="fa fa-angle-right"></i>
							</li>
							<li>
								<a href="#">Unconfirmed Orders</a>
							</li>
						</ul>
						<!-- END PAGE TITLE & BREADCRUMB-->
					</div>

					<!-- END PAGE HEADER-->

					<div id="content" class="clearfix">
						<div class="large-12 columns">
							<form class="advance_search">
								<div class="row">
									<div class="medium-12 columns">
										<div class="type">
											<select class="left filterSelect" >
													<option>Select Kitchen</option>
													<option>All</option>
													<option>Kitchen 1</option>
													<option>Kitchen 2</option>
												</select>
												
												<select name="filter_year" id="Menu_Type" class="filterSelect">
													<option value="">Select Menu Type</option>
													<option value="Lunch">Lunch</option>
													<option value="Dinner">Dinner</option>

												</select>

												
												<select name="Delivery_Location" id="Delivery_Location" class="filterSelect">
													<option value="">Select Delivery Location</option>
													<option value="all">All</option>
													<option value="Vashi">Vashi</option>
													<option value="Thane">Thane</option>
												</select>

												
												<select name="Delivery_Person" id="Delivery_Person" class="filterSelect">
													<option value="">Select Delivery Person</option>
													<option value="all">All</option>
													<option value="Sagar">Sagar</option>
													<option value="Asif">Asif</option>
												</select>

											<div class="left">
												<div class="" >
													<!-- <div class="radioBut left">
													<label for="filter1" class="advance_lable">Period</label>
													<input id="filter1" name="filter_check" type="radio" value="1" checked />
													</div> -->
													

													
													<label class="left&#x20;inline" for="minDate">&nbsp;From&nbsp;</label>
													<input type="text" name="minDate" id="minDate" class="left&#x20;filterSelect" placeholder="Date">
													<label class="left&#x20;inline" style="margin-left&#x3A;0" for="maxDate">&nbsp;To&nbsp;</label>
													<input type="text" name="maxDate" id="maxDate" class="left&#x20;filterSelect" placeholder="Date">
													<button class="button left tiny left5 dark-greenBg" data-text-swap="Wait.." type="button" id="submitButton" >
														Go
													</button>
												</div>

											</div>

										</div>
									</div>
								</div>
							</form>
							<div class="portlet box yellow mb20">
								<div class="portlet-title">
									<h4><i class="fa fa-table"></i>Unconfirmed Orders</h4>
									
								</div>
								<div class="portlet-body sales_data_table">
									<div class="filter">
										<div>
											<a class="advance_search_click"> Advance Search </a>
										</div>
									</div>
									<table id="customer" class="display displayTable">
										<thead>
											<tr>
												<th>Customer</th>
												<th><!-- Delivary Location -->Order Date</th>
												<th>Price <i class="fa fa-inr"></i></th>
												<th>Discount <i class="fa fa-inr"></i></th>
												<th>Delivery <i class="fa fa-inr"></i></th>
												<th>Net Amount <i class="fa fa-inr"></i></th>
												<th>Pay Type</th>
												<th>Order Status</th>
												<th>Order Date</th>
												<th>Menu Type</th>
												<th class="head1 no_sort">Action</th>
												
												
											</tr>

										</thead>

										<tbody>
											<tr>
												
												<td><a href="customerDetails.php">DIPALI Kasar</a></td>
												<td><a href="#s" id="date">Ordered Dates</a>
															<div class="with-altField" style="display: none; width: 85%">
																<input type="text" class="altField" />
															</div>
</td>
												
												<td>90.00</td>
												<td>0.00</td>
												<td>0.00</td>
												<td>90.00</td>
												<td>Cash</td>
												
												<td>New</td>
												<td>31-08-2015</td>
												<td>Lunch</td>
												
												<td>
													<button class="smBtn blueBg has-tip tip-top" onClick="location.href='customerEdit.php'" data-tooltip title="Confirm Order" data-text-swap="Wait..">
														<i class="fa fa-check-square-o"></i>
													</button>
													</td>
											</tr>
											

										</tbody>
									</table>
								</div>
							</div>

						</div>
					</div>
					<!-- END PAGE CONTAINER-->
				</div>
				<!-- END PAGE -->
			</div>

		</div>
		<!-- END CONTAINER -->

		<!-- BEGIN FOOTER -->
		<?php
		include_once 'footer.php';
		?>

		<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

		<!-- wrapper End -->
		<?php
		include_once 'script_links.php';
		?>

		<script>
			$(document).ready(function() {
				myPageTable.init();
			});
		</script>
		
		<script>
		$(document).ready(function() {
			$('.with-altField').multiDatesPicker({
											minDate : 0,
											altField : '.altField',
											dateFormat : "d-m-yy",
										});	
										});
		</script>
		<script>
			$(document).ready(function() {
				$(".orders").addClass("active");
				$(".orders ul li:nth-child(6)").addClass("active");
			});
		</script>
		<script>
			$(document).ready(function() {
				$("#date").click(function(){
				    $(".with-altField").hide();
				});

				$("#date").click(function(){
				    $(".with-altField").show();
				});
				
				// intro tour
			    $('.blueBg').attr("data-step", "1");
			    $('.blueBg').attr("data-intro", "Click on confirm button for confirming order.<br/>1.Go through all details.<br/>2.Select mode of payment and click on confirm button.");
			    $('.blueBg').attr("data-position", "left");
			    introJs().start();
			    $('.blueBg').removeAttr("data-step");
			    $('.blueBg').removeAttr("data-intro");
			    $('.blueBg').removeAttr("data-position");

			});
				
		
		</script>

	</body>
</html>
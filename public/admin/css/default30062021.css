@charset "utf-8";
/* CSS Document */

@import url("uniform.aristo.css");

@font-face {
	font-family: 'Maven Pro';
	font-style: normal;
	font-weight: 400;
	src: local('Maven Pro Regular'), local('MavenProRegular'), url(../fonts/Maven.woff) format('woff');
}
/*
 @font-face {
 font-family: Klavika Regular;
 src: url('../fonts/Arial.eot'),
 src: url('../fonts/Arial.eot?#iefix') format('embedded-opentype'),
 url('../fonts/Arial.ttf')format('truetype'),
 url('../fonts/Arial.woff') format('woff'),
 url('../fonts/Arial.svg') format('svg');
 font-weight: normal;
 font-style: normal;
 } */
html {

	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
}
body {
	margin: 0;
	background: #f5f5f5;
	padding-top:55px;
	font-family: "Maven Pro", sans-serif;
}
button:hover, button:focus, .button:hover, .button:focus {
	opacity: 1;
}
.center {
	text-align: center;
}
article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
	display: block;
}
audio, canvas, progress, video {
	display: inline-block; /* 1 */
	vertical-align: baseline; /* 2 */
}
audio:not([controls]) {
	display: none;
	height: 0;
}
[hidden], template {
	display: none;
}
a {
	background: transparent;
	color: #FC6E51;
}
a:active, a:hover, a:hover, a:focus, .has-tip:hover, .has-tip:focus {
	outline: 0;
	color: #E9573F;
}
abbr[title] {
	border-bottom: 1px dotted;
}
b, strong {
	font-weight: bold;
}
dfn {
	font-style: italic;
}
h1 {
	font-size: 2em;
	margin: 0.67em 0;
}
mark {
	background: #ff0;
	color: #000;
}
small {
	font-size: 80%;
}
sub, sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sup {
	top: -0.5em;
}
sub {
	bottom: -0.25em;
}
img {
	border: 0;
}
svg:not(:root) {
	overflow: hidden;
}
hr {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	height: 0;
}
pre {
	overflow: auto;
}
code, kbd, pre, samp {
	font-family: monospace, monospace;
	font-size: 1em;
}
figure {
	margin: 0;
	padding: 0px;
}
button {
	overflow: visible;
}
button, select {
	text-transform: none;
}
button, html input[type="button"], /* 1 */ input[type="reset"], input[type="submit"] {
	-webkit-appearance: button; /* 2 */
	cursor: pointer; /* 3 */
}
button[disabled], html input[disabled] {
	cursor: default;
}
button::-moz-focus-inner, input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

select[disabled]:hover {
	border-color: #cccccc;
}
input {
	line-height: normal;
}
input[type="checkbox"], input[type="radio"] {
	box-sizing: border-box; /* 1 */
	padding: 0; /* 2 */
}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
	height: auto;
}
input.calender {
	background: url(../images/icons/calendar.png) right no-repeat #fff !important;
}
.mb0 {
	margin-bottom: 0;
}
.dateTo {
	text-align: center;
	margin: 0px 0 1rem 0;
	padding: 8px 0 0 0;
}
.custom-file {
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
	box-sizing: border-box;
	color: rgba(0, 0, 0, 0.75);
	display: block;
	font-family: inherit;
	font-size: 0.875 rem;
	height: 2rem;
	margin: 0 0 1rem;
	padding: 0.5 rem;
	transition: box-shadow 0.45s ease 0s, border-color 0.45s ease-in-out 0s;
	width: 100%;
}
.custom-file-container {
	background-color: white;
	border: 1px solid #cccccc;
	border-radius: 2px;
	width: 100%;
}
.file-disabled {
	background-color: #dddddd;
}
input[type="search"] {
	-webkit-appearance: textfield; /* 1 */
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box; /* 2 */
	box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}
fieldset {
	border: 1px solid #c0c0c0;
	margin: 0 2px;
	padding: 0.35em 0.625em 0.75em;
}
legend {
	border: 0; /* 1 */
	padding: 0; /* 2 */
}
textarea {
	overflow: auto;
}
optgroup {
	font-weight: bold;
}
table.display {
	width: 100%;
	padding: 0px;
	margin:0;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
td, th {
	padding: 0;
}
ul, li {
	list-style: none;
}
.f-dropdown li a {
	padding: 7px 12px;
	color: #656D78;
	font-size: 14px;
}
.f-dropdown {
	border: solid 1px #f1f1f1;
	z-index: 100 !important;
	padding-bottom: 8px !important;
}
h1, h2, h3, h4, h5, h6 {
	font-family: "Maven Pro", sans-serif;
	font-weight: normal;
	font-style: normal;
	color: #646464;
	text-rendering: optimizeLegibility;
	margin-top: 0.2 rem;
	margin-bottom: 0.5 rem;
	line-height: 1.4;
}
.pad0 {
	padding: 0px;
}
.fullWidth {
	width: 100%;
	max-width: 100%;
	margin-left: auto;
	margin-right: auto;
	max-width: initial;
}
.top-bar {
	height:55px;
	border-bottom: 0 !important;
	background: #FFFFFF;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
	padding-left: 90px;
	position: fixed;
	width: 100%;
	top: 0;
	z-index: 1000;
}
.top-bar .columns {
	padding: 0;
	/*height: 56px*/
}
.dropdown.button, button.dropdown {
	height: 36px;
	margin:10px 15px 0;
	padding: 7px;
	border: 1px solid #f1f1f1;
	background-color: transparent;
	opacity: 1;
	color: #656D78;
}

.dropdown.button:after, button.dropdown:after {
	border-color: #fff transparent transparent transparent;
	margin-top: 0;
	top: 45%;
}
.top-bar .dropdown.button:after {
	border-color: #999 transparent transparent transparent;
}
.has-tip:hover, .has-tip:focus, .has-tip {
	border-bottom: 0;
}
.logo {
	/*padding: 15px 15px 0;
	display: inline-block;*/
	padding: 10px;
	display: block;
	height: 80px;
}
.logo img{
	max-width:100%;
	max-height:100%;
}
.grayBg{
	background: #959595;
}
.redBg1{
	background: #DA4453;
}
.blueBg {
	background: #3BAFDA;
}
.pinkBg {
	background: #D770AD;
}

.dark-blueBg {
	background: #208dbe
}
.greenBg {
	background: #A0D468
}
.greenBg1 {
	background: #16A085
}
.greenColor {
	color: #A0D468
}
.dark-greenBg {
	background: #3BAFDA;
}
.purpleBg {
	background: #852b99
}
.dark-purpleBg {
	background: #6e1881
}
.yellowBg {
	background: #ffc40d
}
.dark-yellowBg {
	background: #cb871b
}
.orangeBg {
	background: #F6BB42
}
.dark-orangeBg {
	background: #c27c0d
}
.redBg, .lcs_switch.lcs_off {
	background: #ED5565
}
.greyBg { 
	background: #aab2bd
}
.redColor {
	color: #ED5565
}
.dark-redBg {
	background: #ca0606
}
.grassBg {
	background: #8CC152;
}
.blue {
	color: #27a9e3
}
.dark-blue {
	color: #208dbe
}
.green {
	color: #48CFAD
}
.dark-green {
	color: #10a062
}
.purple {
	color: #852b99
}
.dark-purple {
	color: #6e1881
}
.yellow {
	color: #ffc40d
}
.dark-yellow {
	color: #cb871b
}
.orange {
	color: #EAAD3C;
	/*color: #cb9a2a;*/
}
.dark-orange {
	color: #c27c0d
}

.orange-theme {
	color: #FC6E51;
}

.red {
	color: #ED5565
}
.dark-red {
	color: #ca0606
}
.light-grey {
	color: #656D78;
	line-height: 1.2;
	margin: 25px 0;
}
.white {
	color: #fff
}
.chocolate {
	color: #3D3230;
}
.vavendarRBg {
	background-color: #AC92EC;
}
.left5 {
	margin-left: 5px;
}
.right5 {
	margin-right: 5px;
}
.padding5 {
	padding: 5px;
}
.wid118 {
	max-width: 118px;

}
.wid150 {
	
	max-width: 150px;
}
.wid100 {
	max-width: 100px;
}
.wid95 {
	max-width: 95px;
}
.clearBoth10 {
	clear: both;
	height: 10px;
	width: 100%;
	font-size: 0px;
}
.clearBoth5 {
	clear: both;
	height: 5px;
	width: 100%;
	font-size: 0px;
}
.clearBoth20 {
	clear: both;
	height: 20px;
	width: 100%;
	font-size: 0px;
}
.clearBoth25 {
	clear: both;
	height: 25px;
	width: 100%;
	font-size: 0px;
}
.clearmb15 {
	clear: both;
	width: 100%;
	margin-bottom: 15px;
	overflow: hidden;
}
a.logOut {
	height: 45px;
	top: 0px !important;
	padding-left: 1em;
	padding-right: 3em !important;
}
a.logOut:after {
	top: 44% !important;
}
.f-dropdown .divider {
	background-color: #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	height: 1px;
	margin: 5px 1px;
	overflow: hidden;
}

.alert-box.warning_msg {
	background-color: #ffc98a;
	border-color: #ffb663;
	color: #b55b1c;
}

.alert-box.success_msg {
	background-color: #dff0d8;
	border-color: #d6e9c6;
	color: #3c763d;
}

.alert-box.danger_msg {
	background-color: #f2dede;
	border-color: #ebccd1;
	color: #a94442;
}
.transperent {
	background: #eeeeee;
}

/***
 Page sidebar
 ***/
.page-sidebar {
	width: 225px;
	background: #3D3230;
	border-radius: 0 0 2px 2px;
	position: fixed;
	top:55px;
	z-index: 1000;
}
.page-sidebar > ul {
	list-style: none;
	margin: 0;
	padding: 0;
	margin: 0;
	padding: 0;
}
.page-sidebar  ul  li {
	display: block;
	margin: 0;
	padding: 0;
	border: 0px;
}
.page-sidebar  ul  li  a {
	display: block;
	position: relative;
	margin: 0;
	border: 0px;
	padding: 15px 15px;
	text-decoration: none;
	font-size: 16px;
	font-weight: 300;
}
.page-sidebar ul li a:hover {
	color: #fff;
}
.page-sidebar ul li a i {
	font-size: 16px;
	margin-right: 5px;
	text-shadow: none;
}
.page-sidebar ul li.active a {
	background: #FC6E51;
	border-top-color: transparent !important;
	color: #fff;
}
.page-sidebar ul li:last-child a {
	border-bottom: 1px solid transparent !important;
}
.page-sidebar ul li.active a {
	border: none;
	text-shadow: none;
}
.page-sidebar ul li.active a .selected {
	display: block;
	width: 8px;
	height: 25px;
	float: right;
	position: absolute;
	right: 0px;
	top: 15px;
}
.page-sidebar ul li a .arrow:before {
	float: right;
	margin-top: 1px;
	margin-right: 5px;
	display: inline;
	font-size: 16px;
	font-family: FontAwesome;
	height: auto;
	content: "\f104";
	font-weight: 300;
	text-shadow: none;
}
.page-sidebar ul li a .arrow.open:before {
	float: right;
	margin-top: 1px;
	margin-right: 5px;
	display: inline;
	font-family: FontAwesome;
	height: auto;
	font-size: 16px;
	content: "\f107";
	font-weight: 300;
	text-shadow: none;
}
.page-sidebar ul li ul.sub li a .arrow:before {
	float: right;
	margin-top: 1px;
	margin-right: 5px;
	display: inline;
	font-size: 16px;
	font-family: FontAwesome;
	height: auto;
	content: "\f104";
	font-weight: 300;
	text-shadow: none;
}
.page-sidebar ul li ul.sub li a .arrow.open:before {
	float: right;
	margin-top: 1px;
	margin-right: 5px;
	display: inline;
	font-family: FontAwesome;
	height: auto;
	font-size: 16px;
	content: "\f107";
	font-weight: 300;
	text-shadow: none;
}
.page-sidebar ul li ul.sub li ul.sub {
	display: none;
	list-style: none;
	clear: both;
}
.page-sidebar ul li.active ul.sub {
	display: block;
}
.page-sidebar ul li ul.sub li {
	background: none;
	margin: 0px;
	padding: 0px;
}
.page-sidebar ul li ul li ul li {
	list-style: none;
}
.page-sidebar ul li ul.sub li a {
	display: block;
	margin: 0px 0px 0px 0px;
	padding: 5px 0px;
	padding-left: 44px !important;
	color: #222;
	text-decoration: none;
	font-size: 14px;
	font-weight: 300;
	background: #65534F;
	color: #fff;
}

.page-sidebar ul li ul.sub li ul.sub li:before {
	border-top: 1px dotted #222;
	content: "";
	display: inline-block;
	margin: 7% 0 0;
	position: absolute;
	width: 17px;
}
.page-sidebar ul li ul.sub li ul.sub:before {
	-moz-border-bottom-colors: none;
	-moz-border-left-colors: none;
	-moz-border-right-colors: none;
	-moz-border-top-colors: none;
	border-color: #222;
	border-image: none;
	border-style: dotted;
	border-width: 0 0 0 1px;
	bottom: 0;
	content: "";
	display: block;
	position: absolute;
	top: 0;
	z-index: 1;
}
.page-sidebar ul li.has-sub .sub {
	position: relative;
}
.page-sidebar ul li > ul.sub {
	clear: both;
	display: none;
	list-style: none outside none;
	padding-left: 0;
	margin: 0px 0px 0px 0px;
	list-style: none;
}
.page-sidebar ul li ul li ul.sub {
	clear: both; /*display: none !important;*/
	list-style: none outside none;
	list-style: none;
}
.page-sidebar ul li ul.sub li ul li a {
	display: block;
	margin: 0px 0px 0px 0px;
	padding: 5px 0px;
	padding-left: 44px !important;
	color: #fff;
	text-decoration: none;
	font-size: 14px;
	font-weight: 300;
	background: #444;
}
.page-sidebar ul li ul.sub li ul li a:hover {
	background: #b94b0a
}
.page-sidebar ul li ul.sub li a > i {
	font-size: 13px;
}
.page-sidebar .sidebar-search {
	margin: 8px 20px 10px 20px;
}
.page-sidebar .sidebar-search .submit {
	display: block;
	float: right;
	margin-top: 8px;
	width: 13px;
	height: 13px;
	background-image: url(../images/icons/search-icon.png);
	background-repeat: no-repeat;
}
.page-sidebar .sidebar-search input {
	background-color: #f1f1f1 !important;
	color: #222 !important;
	margin: 0px;
	width: 90%;
	border: 0px;
	padding-left: 0px;
	padding-right: 0px;
	padding-bottom: 0px;
	font-size: 14px;
	box-shadow: none;
	display: inline-block;
	height: 25px !important;
	line-height: 20px;
}

.page-sidebar .sidebar-search .input-box {
	padding-bottom: 2px;
	border-bottom: 1px solid #959595;
}
.page-sidebar ul li.has-sub.open > a, .page-sidebar ul li > a:hover, .page-sidebar ul li:hover > a {
	background: #FC6E51;
	color: #fff;
}
.page-sidebar ul li ul li.has-sub-sub.open > a {
	background: #d05812;
}
.page-sidebar ul li a {
	border-top: 0;
	color: #d3cac1;
}
.page-sidebar ul li ul.sub li a:hover {
	background: #5b5a5a
}
.page-sidebar ul li ul.sub li.active a, .page-sidebar ul li ul.sub li a:hover {
	background: #836B66;
	color: #fff;
}
.page-sidebar ul li ul li ul {
	margin-left: 25px;
}
.page-sidebar ul li ul li ul.subSmall {
	margin-left: 153px;
}
.sidebar-closed .page-sidebar ul li ul.sub li:before {
	content: none;
}
.sidebar-closed .page-sidebar ul li ul.sub li ul.sub li:before {
	content: none;
}
/*   Sidebar toggler(show/hide) */

.sidebar-toggler {
	cursor: pointer;
	/* opacity: 0.5; */
	filter: alpha(opacity=50);
	margin-top: 0;
	margin-left: 0;
	width: 90px;
	height: 58px;
	position: absolute;
	background-repeat: no-repeat;
	top: -66px;
	z-index: 99999;
	left: 0;
	background-color: transparent;
	font-size: 30px;
	color: #656D78;
	text-align: center;
	padding-top: 24px;
}
.sidebar-toggler:hover {
	filter: alpha(opacity=100);
	opacity: 1;
}

.sidebar-closed .page-sidebar .sidebar-search {
	height: 34px;
	width: 29px;
	margin-left: 3px;
	margin-bottom: 0px;
}
.sidebar-closed .page-sidebar .sidebar-search input {
	display: none;
}
.sidebar-closed .page-sidebar .sidebar-search .submit {
	margin: 11px 7px !important;
	display: block !important;
}
.sidebar-closed .page-sidebar .sidebar-search .input-box {
	border-bottom: 0;
}
.sidebar-closed .page-sidebar .sidebar-search.open input {
	margin-top: 3px;
	padding-left: 10px;
	padding-bottom: 2px;
	width: 180px;
	display: inline-block !important;
}
.sidebar-closed .page-sidebar .sidebar-search.open .submit {
	display: inline-block;
	width: 13px;
	height: 13px;
	margin: 10px 8px 9px 6px !important;
}
.sidebar-closed .page-sidebar .sidebar-search.open .remove {
	background: url(../images/icons/sidebar-search-close.png) no-repeat center center;
	width: 21px;
	height: 22px;
	margin: 5px !important;
	display: inline-block !important;
	float: left !important;
}
.sidebar-closed .page-sidebar .sidebar-search.open {
	background-color: #f1f1f1 !important;
	position: relative;
	z-index: 999;
	height: 34px;
	width: 255px;
	overflow: hidden;
}
.sidebar-closed ul li a .selected {
	right: -3px !important;
}
.sidebar-closed ul li a .title, .sidebar-closed ul li a .arrow {
	display: none !important;
}

.sidebar-closed .page-sidebar .sidebar-search {
	margin-top: 6px;
	margin-bottom: 6px;
}
.sidebar-closed .page-sidebar ul {
	width: 90px !important;
}
.sidebar-closed .page-sidebar ul li ul.subSmall {
	width: 225px !important;
}
.sidebar-closed .page-sidebar ul li a {
	padding-left: 30px;
}
.sidebar-closed .page-sidebar ul.mCustomScrollbar #mCSB_1 #mCSB_1_container, .sidebar-closed .page-sidebar ul.mCustomScrollbar #mCSB_1.mCustomScrollBox {
	overflow: visible;
}
.sidebar-closed .page-sidebar ul li a .title {
	margin-left: 25px;
}
.page-sidebar ul li a i {
	font-size: 21px;
}
.sidebar-closed .page-sidebar  ul li:hover {
	width: 225px;
	position: relative;
	z-index: 2000;
	display: block !important;
}
.sidebar-closed .page-sidebar  ul  li.has-sub:hover {
	width: 315px;
}
.sidebar-closed .page-sidebar ul li:hover .selected {
	display: none;
}
.sidebar-closed .page-sidebar ul li:hover a i {
	margin-right: 10px;
}
.sidebar-closed .page-sidebar ul li:hover .title {
	display: inline !important;
}
.sidebar-closed .page-sidebar ul li.has-sub .sub {
	display: none !important;
	padding-left: 0px;
}
.sidebar-closed .page-sidebar ul li ul li ul {
	display: none !important;
}
.sidebar-closed .page-sidebar ul li.has-sub:hover .sub {
	width: 189px;
	position: absolute;
	z-index: 2000;
	left: 90px;
	margin-top: 0;
	display: block !important;
}
sidebar-closed .page-sidebar ul li ul li.has-sub:hover .sub {
	width: 189px;
	position: relative;
	z-index: 2000;
	left: 36px;
	margin-top: 0;
	display: block !important;
}
.sidebar-closed .page-sidebar ul > li.has-sub:hover .sub li a {
	padding-left: 15px !important;
}
.sidebar-closed .page-sidebar {
	width: 90px;
}
.sidebar-closed .page-content {
	margin-left: 90px;
}
/* ie8 fixes */
.ie8 .page-sidebar {
	position: absolute;
	width: 225px;
}
.ie8 .page-sidebar ul {
	width: 225px;
}

.page-content {
	margin-left: 225px;
}
.footer {
	text-align: left;
	color: #656D78;
	padding: 10px 15px;
	font-size: 16px;
	width: 100%;
	margin: 0px;
	line-height: 30px;
	background: #FFFFFF;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.32);
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.32);
	padding-left: 240px;
}
.footer a {
	color: #FC6E51;
}
.footer img {
	margin: -9px 0 0 0;
	padding: 2px;
	position: relative;
}
.go-top {
	position: fixed;
	right: 10px;
	bottom: 10px;
	z-index: 9999;
	display: block;
	background: #FC6E51;
	width: 50px;
	height: 50px;
	padding-top: 5px;
	cursor: pointer;
	text-align: center;
	font-size: 35px;
	display: none;
}
.go-top i {
	color: #fff;
	text-align: center;
}
.container-fluid {
	padding-left: 5px;
	padding-right: 5px;
}
.page-title {
	color: #666;
	display: block;
	font-family: 'Maven Pro';
	font-size: 30px;
	font-weight: 300;
	letter-spacing: -1px;
	margin:0;
	padding:0;
	float: left;
}
.page-title small {
	color: #888;
	font-size: 14px;
	font-weight: 300;
	letter-spacing: 0;
}
.breadcrumb {
	background-color: #ddd;
	border-radius: 2px;
	list-style: none outside none;
	margin: 0 0 10px;
	padding: 6px 15px;
	clear: both;
}
.breadcrumb > li {
	display: inline-block;
	text-shadow: 0 1px 0 #fff;
	color: #333;
}
.breadcrumb > li a {
	color: #333;
	font-size: 12px;
}
body.login {
	background: #fafafa;
}
.login .content {
	background-color: #fff;
	margin: 0 auto 10px auto;
	padding: 20px 30px 0px 30px;
	width: 310px;
	border-radius: 2px;
}
.login .content .register-form {
	display: none;
}
.login .content .forget-form {
	display: none;
}
.login .content .form-title {
	font-weight: 300;
	margin-bottom: 25px;
	font-family: 'Maven Pro', sans-serif;
	font-size: 25px;
}

.login .logo {
	margin: 2% auto 0;
	/*padding: 15px;*/
	text-align: center;
	/*width: 247px;*/
	display: block;
	height: 80px;
    padding: 10px;
}
.truncate {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.logo-name{
	    margin-top: 3px;
    display: block;
    font-size: 20px;
}
.login .content .input-icon {
	border-left: 2px solid #e1671f !important;
}
.input-icon i {
	color: #ccc;
	display: block !important;
	font-size: 16px;
	height: 16px;
	margin: 9px 2px 4px 10px;
	position: absolute !important;
	text-align: center;
	width: 16px;
	z-index: 1;
}
.login .content .control-group {
	margin-bottom: 20px !important;
}
input.m-wrap {
	border: 1px solid #e5e5e5;
}
.login .content .form-actions {
	border-bottom: 1px solid #eee;
	border-image: none;
	border-style: none none solid;
	border-width: 0 0 1px;
	clear: both;
	margin-left: -30px;
	margin-right: -30px;
	padding: 0 30px 5px;
}
.input-icon input {
	padding-left: 33px !important;
}
.login .content .form-actions .checkbox {
	display: inline-block;
	margin-top: 8px;
}
.login .content h4 {
	color: #555;
	font-family: 'Maven Pro', sans-serif;
	font-size: 18px;
}
.login .content .forget-password {
	margin-top: 25px;
}
.forget-password p {
	font-size: 12px;
}
.create-account p {
	font-size: 12px;
}
/* Custom form start */
.custom-checkbox.checked {
	background-position: -78px -262px;
}
.custom-checkbox {
	width: 15px;
	height: 15px;
	background-position: -2px -262px;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	background-image: url(../images/sprite.png);
	float: left;
	margin: 0 5px 1rem 0;
}
label.checkbox {
	line-height: 15px;
	float: left;
}
label.radiobox {
	float: left;
}
.custom-select-container.focus, .custom-select-container:hover {
	background-position: -2px -110px;
}
.custom-select-container {
	width: 228px;
	height: 34px;
	background-position: -2px -74px;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	background-image: url(../images/sprite.png);
}
.custom-select {
	text-indent: 10px;
	padding: 8px 44px 0px 0px;
}
.custom-radio.checked {
	background-position: -74px -279px;
}
.custom-radio {
	float: left;
	width: 16px;
	height: 16px;
	background-position: -2px -279px;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	background-image: url(../images/sprite.png);
	margin: 0 0px 1rem 0;
}
.custom-file-container.focus, .custom-file-container:hover {
	background-position: 16px -593px;
}
.file-disabled .custom-file-container.focus, .custom-file-container:hover {
	background-position: 16px -562px;
}
.custom-file-container {
	height: 2rem;
	background-position: 16px -562px;
	display: block;
	background-repeat: no-repeat;
	background-image: url(../images/sprite.png);
	overflow: hidden;
	margin: 0 0 1rem;
}
.custom-file {
	text-indent: 10px;
	padding: 8px 44px 0px 0px;
}
/* Custome form end*/

.page-404 .number {
	color: #7bbbd6;
	font-size: 158px;
	font-weight: 300;
	letter-spacing: -10px;
	line-height: 158px;
	margin-top: 0;
	text-align: center;
}
.page-404 .details input {
	margin: 0 auto 10px auto;
	width: 285px;
}
.page-404 .details {
	text-align: center;
}
.page-500 .number {
	color: #ec8c8c;
	font-size: 158px;
	font-weight: 300;
	letter-spacing: -10px;
	line-height: 158px;
	margin-top: 0;
	text-align: center;
}
.page-500 .details {
	padding-top: 20px;
	text-align: center;
}
.full-width-page .page-content {
	margin-left: 0 !important;
}
.mobile-toggler {
	cursor: pointer;
	opacity: 0.5;
	filter: alpha(opacity=50);
	    margin: 15px 15px 0 0;
	width: 29px;
	height: 29px;
	background-color: #333;
	background-image: url(../images/icons/sidebar-toggler.jpg);
	background-repeat: no-repeat;
	float: right;
}
.mobile-toggler:hover {
	filter: alpha(opacity=100);
	opacity: 1;
}
.hidden-phone {
	display: none;
}
.show-phone {
	display: block;
}
.mobile-view .mobile-menu {
	width: 100%;
	overflow: hidden;
	clear: both;
	z-index: 999;
	position: relative;
}
.mobile-view .mobile-menuOpen {
	clear: both;
	width: 100%;
}
.mobile-view .page-content {
	margin-left: 0px;
}
/* Portlets  */
.portlet {
	clear: both;
	margin-top: 0px;
	margin-bottom: 0px;
	padding: 0px;
}
.portlet:after, .portlet:before {
	display: table;
	color: "";
}
.portlet-title {
	margin-bottom: 15px;
	width: 100%;
	float: left;
}

.portlet-title h4 {
	display: inline-block;
	font-size: 16px;
	font-weight: 400;
	margin: 0;
	padding: 0;
	margin-bottom: 7px;
	color: #3D3230 !important;
}
.portlet-title h4 i {
	font-size: 16px;
	margin-right: 10px;
}
.portlet.blue .portlet-title h4, .portlet-title.blue h4, .portlet.green .portlet-title h4, .portlet-title.green h4, .portlet.yellow .portlet-title h4, .portlet-title.yellow h4, .portlet.red .portlet-title h4, .portlet-title.red h4, .portlet.purple .portlet-title h4, .portlet-title.purple h4, .portlet.dark-grey .portlet-title h4, .portlet-title.dark-grey h4 {
color: #fff;}

.portlet-title h4 i {
	font-size: 16px;
	margin-right: 10px;
}
.portlet.box.grey .portlet-title h4 > i {
	color: #3D3230;
}
.sortable .portlet .portlet-title {
	cursor: move;
}
.portlet-title .tools, .portlet-title .actions {
	display: inline-block;
	padding: 0;
	margin: 0;
	margin-top: 6px;
	float: right;
}
.portlet-title .tools > a {
	display: inline-block;
	height: 16px;
	margin-left: 5px;
}
.portlet-title .dropdown-menu i {
	color: #000 !important;
}
.portlet-title .tools > a.remove {
	background-image: url(../images/icons/portlet-remove-icon.png);
	background-repeat: no-repeat;
	width: 11px;
}
.portlet-title .tools > a.config {
	background-image: url(../images/icons/portlet-config-icon.png);
	background-repeat: no-repeat;
	width: 12px;
}
.portlet-title .tools > a.reload {
	background-image: url(../images/icons/portlet-reload-icon.png);
	width: 13px;
}
.portlet-title .tools > a.expand {
	background-image: url(../images/icons/portlet-expand-icon.png);
	width: 14px;
}
.portlet-title .tools > a.collapse {
	background-image: url(../images/icons/portlet-collapse-icon.png);
	width: 14px;
	opacity: .6;
	filter: 'alpha(opacity=60)';
}
.portlet-title .tools > a:hover {
	text-decoration: none;
	-webkit-transition: all 0.1s ease-in-out;
	-moz-transition: all 0.1s ease-in-out;
	-o-transition: all 0.1s ease-in-out;
	-ms-transition: all 0.1s ease-in-out;
	transition: all 0.1s ease-in-out;
}
.portlet-title .actions > .btn-group {
	margin-top: -12px;
}
.portlet-title .actions > .btn {
	padding: 4px 10px;
	margin-top: -13px;
}
.portlet-title .actions > .btn-group > .btn {
	padding: 4px 10px;
	margin-top: -1px;
}
.portlet-title .actions > .btn-group > .btn.mini {
	margin-top: 0px;
}
.portlet-title .actions > .btn.mini {
	margin-top: -12px;
	padding: 4px 10px;
}
.portlet-title .pagination.pagination-small {
	float: right !important;
	display: inline-block !important;
	margin: 0px;
	margin-top: -2px;
}
.portlet-body {
	clear: both;
	padding: 0;
}

.portlet-body.light-grey, .portlet.light-grey {
	background-color: #fafafa !important;
}
.portlet-body.dark-grey, .portlet.dark-grey {
	background-color: #555555 !important;
}
.portlet-body .btn-toolbar {
	margin: 0px !important;
	padding: 0px !important;
}
.portlet-body .btn-group {
	margin: 0px !important;
	padding: 0px !important;
	margin-bottom: 10px !important;
}
/*  draggable girds */

.ui-sortable-placeholder {
	border: 1px dotted black;
	visibility: visible !important;
	height: 100% !important;
}
.ui-sortable-placeholder * {
	visibility: hidden;
}
.sortable-box-placeholder {
	background-color: #f5f5f5;
	border: 1px dashed #DDDDDD;
	display: block; /* float: left;*/
	margin-top: 0px !important;
	margin-bottom: 24px !important;
}
.sortable-box-placeholder * {
	visibility: hidden;
}
/* Solid colored portlet */
.portlet.solid {
	padding: 10px;
}
.portlet.solid .portlet-title .tools {
	margin-top: 2px;
	border: 0px;
}
.portlet.solid .portlet-title {
	margin-bottom: 5px;
	border: 0px;
}
.portlet.solid.bordered .portlet-title {
	margin-bottom: 15px;
}
.portlet.solid.red .portlet-title, .portlet.solid.red .portlet-title i, .portlet.solid.red .portlet-body, .portlet.solid.green .portlet-title, .portlet.solid.green .portlet-title i, .portlet.solid.green .portlet-body, .portlet.solid.yellow .portlet-title, .portlet.solid.yellow .portlet-title i, .portlet.solid.yellow .portlet-body, .portlet.solid.grey .portlet-title, .portlet.solid.grey .portlet-title i, .portlet.solid.grey .portlet-body, .portlet.solid.purple .portlet-title, .portlet.solid.purple .portlet-title i, .portlet.solid.purple .portlet-body, .portlet.solid.blue .portlet-title, .portlet.solid.blue .portlet-title i, .portlet.solid.blue .portlet-body {
	border: 0;
	color: #fff;
}
.portlet.bordered {
	border-left: 2px solid #ddd;
}
/* Box portlet */
.portlet.box {
	padding: 0px !important;
	border-radius: 2px;
	float: left;
	width: 100%;
	border: 1px solid #E8E8E8;
}
.portlet.box .portlet-title {
	padding: 10px 10px 3px 10px;
	color: #fff !important;
	background:#F1EDED;
}
.portlet.box .portlet-title > .actions > .btn > i {
	color: #fff !important;
}
.portlet.box .portlet-title .tools {
	margin-top: 3px;
}
.portlet.box .portlet-title .tools > a.remove, .portlet.solid .portlet-title .tools > a.remove {
	background-image: url(../images/icons/portlet-remove-icon-white.png);
}
.portlet.box .portlet-title .tools > a.config, .portlet.solid .portlet-title .tools > a.config {
	background-image: url(../images/icons/portlet-config-icon-white.png);
}
.portlet.box .portlet-title .tools > a.reload, .portlet.solid .portlet-title .tools > a.reload {
	background-image: url(../images/icons/portlet-reload-icon-white.png);
}

/* portlet buttons */
.portlet.box .portlet-body {
	background-color: #fff;
	padding:10px 15px;
}
.portlet.box .portlet-title {
	margin-bottom: 0px;
	border-bottom: 1px solid #E9E9E9;
}
.portlet.box.blue .portlet-title {
	background-color: #3D3230;
}
.portlet.box.chocolate .portlet-title {
	background-color: #3D3230;
}
.portlet.box.grey .portlet-title {
	background-color: #f9f9f9;
}
.portlet.box.blue {
	border: 1px solid #b4cef8;
	border-top: 0;
}
.dataTables_wrapper{
	    margin: 0 -15px;
}
ul.toolOption {
	float: right;
	list-style: none;
	margin: 0px;
	padding: 0px;
}
ul.toolOption li {
	float: left;
	list-style: none;
	margin: 0px;
	padding: 0px;
}
.print {
	float: left;
	margin: -5px 0 0 0;
	position: relative
}
.print button, .print130 button {
	margin: 0px;
	height: 34px;
}

.print #dropPrint {
	font-size: 14px;
}
.print #dropPrint li i, .print #dropPrint1 li i {
	font-size: 20px;
}
.print .f-dropdown {
	max-width: 134px;
}
.print130 {
	float: left;
	margin: -5px 0 0 0;
}
.print130 .f-dropdown {
	max-width: 130px;
}
.addRecord {
	float: left;
	margin: -5px 5px 0 0;
}
.addRecord button {
	margin: 0px;
	height: 34px;
}
.printExport button, .print button {
	background: #959595;
}

.dashboard-div:before, .dashboard-div:after {
	content: "";
	display: table;
	line-height: 0;
}
.dashboard-div:after {
	clear: both;
}
.dashboard-div.blue {
	background-color: #27a9e3;
}
.dashboard-div .visual {
	display: block;
	float: left;
	height: 100px;
	padding-left: 15px;
	padding-top: 10px;
	position: relative;
	width: 25%;
}
.dashboard-div .visual .inside {
	font-size: 20px;
	left: 30px;
	position: absolute;
	top: 23px;
}
.dashboard-div .details {
	float: right;
	padding-right: 10px;
	width: 75%;
}
.dashboard-div .details .number {
	color: #fff;
	font-size: 28px;
	font-weight: 300;
        
	letter-spacing: -1px;
	margin-bottom: 10px;
	padding-top: 20px;
	text-align: right;
}
.dashboard-div .details .desc {
	color: #fff;
	font-size: 16px;
	font-weight: 300;
	letter-spacing: 0;
	text-align: right;
}
.dashboard-div .more {
	clear: both;
	color: #fff;
	display: block;
	font-size: 11px;
	font-weight: 300;
	opacity: 0.7;
	padding: 10px 10px;
	text-transform: uppercase;
	background: rgba(0, 0, 0, 0.3);
}
.dashboard-div .more i {
	float: right;
	font-size: 20px;
	margin: -3px 0 0 0
}
.dashboard-div .visual i {
	color: #fff;
	font-size: 45px;
}
.dashboard-div .more:hover {
	opacity: 1;
	text-decoration: none;
	transition: all 0.1s ease-in-out 0s;
}
ul.orderProcess {
	float: right;
	margin:2px 0 5px 0;
	padding: 0px;
	text-align: center
}
ul.orderProcess li {
	display: inline;
	margin: 0 0 0px 5px;
	padding: 0px;
	text-align: center
}
ul.orderProcess li button {
	background: #FC6E51;
	margin: 0px 0 5px 0;
	padding: 10px;
	-webkit-transition: all 0.5s ease;
	-moz-transition: all 0.5s ease;
	transition: all 0.5s ease;
}

ul.orderProcess li button:hover {
	background: #E9573F;
}
ul.orderProcess li button.active {
	background: #FC6E51;
}
.tifinBox {
	text-align: center;
	border: 2px solid #B2B2B2;
	border-radius: 2px;
	position: relative;
	background: #E9573F;
	margin: 0 0 10px 0;
}
.crsl-item .selected {
	border: 2px solid #3a945b;
}
.mealImg {
	height: 100px;
	width: 100px;
}
.extraImg {
	height: 100px;
	width: 100px;
}
.tifinBox img {
	text-align: center;
	margin: 5px 0
}
.tifinBox .tifinBottom {
	background: rgba(0, 0, 0, 0.3);
	clear: both;
	padding: 10px;
	border-bottom-left-radius: 0px;
	border-bottom-right-radius: 0px;
	color: #fff;
	font-weight: bold;
	font-size: 16px;
}
.tifinCost {
	margin: 8px 0 0px 0;
	text-align: left;
}
.spinerBox {
	float: right;
	font-size: 14px;
	margin: 8px 0 0px 0;
}
.spinner {
	width: 52px;
	float: left;
	margin: -5px 0 0 5px;
	border-bottom: none;
}
.spinner:hover {
	border-bottom: none;
}
.spinner .add-on {
	padding: 2px
}
.spinner .add-on a.spin-up, .spinner .add-on a.spin-down {
	height: 10px;
	overflow: hidden;
	display: block;
	text-align: center;
	position: relative;
	color: #656D78;
	vertical-align: middle;
}
.spinner .add-on a.spin-up .fa-sort-up {
	position: relative;
	top: 2px
}
.spinner .add-on a.spin-down .fa-sort-down {
	position: relative;
	top: -5px
}
.spinner .add-on a.spin-up:hover, .spinner .add-on a.spin-down:hover {
	color: #555
}
.spinner input {
	width: 35px;
	text-align: center;
	padding: 0px;
	height: 1.5 rem;
	margin: 0px;
	float: left;
	border-radius: 2px 0px 0px 2px;
	border-left: 1px solid #a3a3a3;
	border-top: 1px solid #a3a3a3;
	border-bottom: 1px solid #a3a3a3;
}
.add-on {
	margin: 0px 0 0 0;
	background-color: #eeeeee;
	border-left: none;
	border-right: 1px solid #a3a3a3;
	border-top: 1px solid #a3a3a3;
	border-bottom: 1px solid #a3a3a3;
	font-size: 12px;
	font-weight: normal;
	height: 1.5 rem;
	min-width: 16px;
	padding: 4px 5px;
	text-align: center;
	text-shadow: 0 1px 0 #ffffff;
	width: 10px;
	border-radius: 0 2px 2px 0;
	float: left;
	white-space: nowrap;
}
.Extra .tifinBox h2 {
	font-size: 16px;
}
.Extra .tifinBox .tifinBottom {
	padding: 5px 10px;
}
.Extra .tifinBox .tifinBottom {
	font-size: 14px
}
.Extra .spinner input {
	height: 22px;
}
.Extra .add-on {
	height: 22px;
}
.Extra .spinner .add-on a.spin-up, .spinner .add-on a.spin-down {
	height: 8px;
}
.Extra .spinner .add-on a.spin-up .fa-sort-up {
	top: 0px;
}
.Extra .spinner .add-on a.spin-down .fa-sort-down {
	top: -6px;
}
.Extra .spinerBox {
	margin: 8px 0 0 0
}
.Extra .tifinCost {
	margin: 7px 0 0 0;
}
.Extra .cusCheckBox label {
	font-size: 16px;
}
table {
	border: solid 1px #f1f1f1;
}
table tr th {
	font-weight: normal;
	font-size: 16px;
}
table tr th, table tr td, table.dataTable tbody th, table.dataTable tbody td {
	border-bottom: 1px solid #f1f1f1;
	    padding: 5px;
	font-size: 16px;
	color: #656D78;
}
table tr:hover {
	background-color: whitesmoke;
}
table thead {
	background: none repeat scroll 0 0 #eee;
}
table thead th, table thead td {
	border-bottom: 1px solid #f1f1f1;
}
table tr.odd, table tr.alt, table tr:nth-of-type(odd) {
	background: #F9F9F9;
}
table tr.even, table tr.alt, table tr:nth-of-type(even) {
	background: #ffffff;
}
input.table-input{
	margin-bottom:0;
}
.dataTables_wrapper.no-footer .dataTables_scrollBody {
	border-bottom: 0;
}
.cusCheckBox {
	position: relative;
	z-index: 999;
	float: left;
	width: 100%;
}
.cusCheckBox input {
	float: left;
}

.cusCheckBox label {
	color: #222;
	font-size: 20px;
	font-weight: normal;
	margin: 0 !important;
	padding: 5px 0 0 0;
	text-align: center;
	float: left;
	width: 100%;
	height: 30px;
	line-height: 25px;
	overflow: hidden;
	position: relative;
	z-index: 999999
}

.cusCheckBox div.checker, div.checker span, div.checker input {
	position: absolute;
	z-index: 9999;
	left: 0px;
}
.cusCheckBox div.checker span.checked {
	background-position: -2px -1px;
}
.cusCheckBox div.checker span {
	height: 25px;
	width: 25px;
	margin: 5px 0 0 5px;
	background-image: url(../images/icons/checkBoxNew.png);
	background-position: 0px -30px;
	position: absolute;
	border: 2px solid #B2B2B2;
	border-radius: 2px;
}
#content h1 {
	font-size: 1.500 rem;
	padding: 0 0 0 0px
}

.orderDate {
	margin-bottom: 5px;
	overflow: hidden;
}
.orderDate_container {
	float: left;
	width: 40%;
	margin: 0 0 0.75em;
}

.btn-group-radio input[type=radio] {
	visibility: hidden;
	position: absolute !important;
	top: -9999px !important;
	left: -9999px !important;
}
.btn-group-radio > .btn {
	margin-left: -1px;
}
.btn-group, .btn-group-vertical {
	margin: 0 0 0 20px;
}
.btn-group-radio > .btn {
	background-color: #9D9D9D;
	border: 0;
	margin: 2px 5px 0 0;
	padding: 2px 8px;
	font-size: 20px;
	color: black;
	border-radius: 0;
}
.radio_buttons label {
	color: #ffffff;
	font-size: 12px;
	font-weight: normal;
}
.btn.no {
	float: right !important;
	margin-right: 0;
}
.btn.yes {
	float: left !important;
}
.orderDate .btn-group.btn-group-radio {
	margin: 0;
	overflow: hidden;
}

#optionsRadios1, #optionsRadios2 {
	display: none !important;
}
.btn-group-radio .btn {
	cursor: pointer;
	float: left;
	font-size: 12px;
	width: 45%;
	text-align: center;
	color: #ffffff;
}
.btn-group-radio .btn:hover {
	background: #62B18F;
}
.btn-group-radio .btn.active {
	background: #62B18F;
}

.pro_Cal {
	margin: 0 0 15px;
	position: relative;
}

.menuBlock {
	border: 2px solid #cccccc;
	margin-bottom: 30px;
	padding: 20px;
}
.blockHeader {
	margin-bottom: 15px;
	overflow: hidden;
}
.cusCheckBox div.checker, div.checker span, div.checker input {
	z-index: 1;
}
.cusCheckBox div.checker, div.checker span, div.checker input {
	left: 0;
	position: absolute;
	z-index: 9999;
}
.blockHeader .menuDate {
	color: #222222;
}
.menu_Container.popup_menu .alert-box {
	overflow: hidden;
	padding: 5px 10px 5px 14px;
}
.menu_Container .alert-box {
	background-color: #f1f1f1;
	border-color: #e2e2e2;
	color: #222;
	font-size: 14px;
	margin-bottom: 7px;
	padding: 2px 30px 4px 14px;
}
.menu_Container.popup_menu .menuItem_name {
	display: inline-block;
	margin-left: 2%;
	width: 66%;
}
.menu_Container .menuItem_name {
	display: inline-block;
	width: 60%;
}
.pro_Cal .cal_prev, .pro_Cal .cal_next {
	display: inline-block;
	font-size: 25px;
	position: absolute;
	top: 3px;
}
.pro_Cal a {
	color: #000000;
}
.pro_Cal .dateText {
	display: inline-block;
	margin: 0 25px 0 35px;
}
.pro_Cal .dateText input[type="text"] {
	margin: 0;
}
form.calForm {
	overflow: hidden;
}
.calForm select {
	margin-right: 20px;
	width: 9%;
	padding: 0;
}
.menu_Container label {
	color: #222;
	display: inline-block;
	text-align: right;
	width: 20%;
}
.menu_Container .alert-box .close {
	top: 20%;
}

/* Slider */
.slick-slider {
	position: relative;
	display: block;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-ms-touch-action: none;
	touch-action: none;
	-webkit-tap-highlight-color: transparent;
}
.slick-list {
	position: relative;
	overflow: hidden;
	display: block;
	margin: 0;
	padding: 0;
}
.slick-list:focus {
	outline: none;
}
.slick-loading .slick-list {
	background: white url('../images/ajax-loader.gif') center center no-repeat;
}
.slick-list.dragging {
	cursor: pointer;
	cursor: hand;
}
.slick-slider .slick-list, .slick-track, .slick-slide, .slick-slide img {
	-webkit-transform: translate3d(0, 0, 0);
	-moz-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0);
	-o-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.slick-track {
	position: relative;
	left: 0;
	top: 0;
	display: block;
	zoom: 1;
}
.slick-track:before, .slick-track:after {
	content: "";
	display: table;
}
.slick-track:after {
	clear: both;
}
.slick-loading .slick-track {
	visibility: hidden;
}
.slick-slide {
	float: left;
	height: 100%;
	min-height: 1px;
	display: none;
}
.slick-slide.slick-loading img {
	display: none;
}
.slick-slide.dragging img {
	pointer-events: none;
}
.slick-initialized .slick-slide {
	display: block;
}
.slick-loading .slick-slide {
	visibility: hidden;
}
.slick-vertical .slick-slide {
	display: block;
	height: auto;
	border: 1px solid transparent;
}

/* Icons */
@font-face {
	font-family: "slick";
	src: url('../fonts/slick.eot');
	src: url('../fonts/slick.eot?#iefix') format("embedded-opentype"), url('../fonts/slick.woff') format("woff"), url('../fonts/slick.ttf') format("truetype"), url('../fonts/slick.svg#slick') format("svg");
	font-weight: normal;
	font-style: normal;
}
/* Arrows */
.slick-prev, .slick-next {
	position: absolute;
	display: block;
	height: 20px;
	width: 20px;
	line-height: 0;
	font-size: 0;
	cursor: pointer;
	background: transparent;
	color: transparent;
	bottom: -40px;
	margin-top: -10px;
	padding: 0;
	border: none;
	outline: none;
}
.slick-prev:hover, .slick-next:hover {
	background: none;
}
.slick-prev:focus, .slick-next:focus {
	outline: none;
	background: none;
}
.slick-prev.slick-disabled:before, .slick-next.slick-disabled:before {
	opacity: 0.25;
}
.slick-prev:before, .slick-next:before {
	font-family: "slick";
	font-size: 20px;
	line-height: 1;
	color: #000;
	opacity: 0.85;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.slick-prev {
	right: 55px;
}
.slick-prev:before {
	content: "\2190";
}
.slick-next {
	right: 20px;
}
.slick-next:before {
	content: "\2192";
}
/* Dots */
.slick-slider {
	margin-bottom: 30px;
}
.slick-dots {
	position: absolute;
	bottom: -45px;
	list-style: none;
	display: block;
	text-align: center;
	padding: 0px;
	width: 100%;
}
.slick-dots li {
	position: relative;
	display: inline-block;
	height: 20px;
	width: 20px;
	margin: 0px 5px;
	padding: 0px;
	cursor: pointer;
}
.slick-dots li button {
	border: 0;
	background: transparent;
	display: block;
	height: 20px;
	width: 20px;
	outline: none;
	line-height: 0;
	font-size: 0;
	color: transparent;
	padding: 5px;
	cursor: pointer;
}
.slick-dots li button:focus {
	outline: none;
}
.slick-dots li button:before {
	position: absolute;
	top: 0;
	left: 0;
	content: "\2022";
	width: 20px;
	height: 20px;
	font-family: "slick";
	font-size: 6px;
	line-height: 20px;
	text-align: center;
	color: black;
	opacity: 0.25;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.slick-dots li.slick-active button:before {
	opacity: 0.75;
}
.slick-slide figure {
	margin: 0 10px 10px 0px
}
.tifinInfo .alert-box {
	margin: 0 4px 4px 0;
	padding: 0.6 rem 0.2 rem 0.6 rem 0.2 rem;
}
.tifinInfo .alert-box.success {
	background-color: #959595;
	border-color: #8a8a8a;
}
.tifinInfo .alert-box.menuItem {
	background-color: #F18748;
	border-color: #e1671f;
	color: #fff;
	font-weight: 500;
}
.tifinInfo .alert-box.subMenuItem {
	background-color: #27a9e3;
	border-color: #0d81b4;
	color: #fff;
	font-weight: 500;
}
.tifinInfo .alert-box .close {
	padding: 0px 0px 4px;
	color: #000;
	opacity: 0.5;
	top: 50%;
}
button.smBtn {
	margin: 0px 2px 2px 0;
	padding: 4px 8px;
	cursor: pointer;
	border-bottom: none;
}
button.blueBg:hover {
	background: #3BAFDA;
}
button.redBg:hover, .lcs_switch.lcs_off:hover {
	background: #DA4453;
}
button.greenBg:hover {
	background: #8CC152;
}
button.smBtn:hover {
	border-bottom: none;
	color: #fff;
}
.totalCost {
	width: 35px;
	float: left;
	text-align: right
}
.proName {
	width: 182px;
	float: left;
	text-align: left;
	padding: 0 0 5px 0;
}
a.orderEdit {
	font-weight: normal;
	color: #FC6E51;
}
span.orderDate {
	margin: 0 0 2px 0;
	padding: 5px;
}
span.paid, span.active, span.unpaid, span.inactive, span.pending, span.unread, span.retry {
	margin: 0 2px 2px 0;
	padding: 2px 5px;
	color: #fff;
	font-size: 12px;
	width: 60px;
	float: left;
	text-align: center
}
span.approved, span.unapproved {
	color: #fff;
	float: left;
	font-size: 12px;
	margin: 0 2px 2px 0;
	padding: 2px 5px;
	text-align: center;
	width: auto;
}
.theme-bg {
	background: #FC6E51;
}
span.paid, span.active, span.approved {
	background: #A0D468;
}
span.unpaid, span.inactive, span.unapproved {
	background: #ED5565;
}
span.yes, li.yes {
	color: #48CFAD;
}
span.no, li.no {
	color: #ED5565;
}
span.inQue, li.inQue {
	color: #e1671f;
}
span.retry {
	background: #e1671f;
}
span.pending {
	background: #FFCE54;
}
span.unread {
	background: #666;
}
.portlet-body .custom-checkbox {
	margin: 0px;
}
#collection input {
	margin: 0px;
	height: 1.6 rem;
	padding: 0.2 rem 0.5 rem;
	width: 125px;
	float: left;
}
#collection input.pay {
	margin: 0px 5px 0 0;
	height: 1.6 rem;
	padding: 0.2 rem 0.5 rem;
	width: 125px;
	float: left;
	border-radius: 2px 0px 0px 2px;
	/*border-right: #e1671f;*/
}
#collection select {
	margin: 0px;
	height: 1.5 rem;
	padding: 0.1 rem 0.5 rem 0.1 rem 0.5 rem;
}
#collection button {
	margin: 0px;
	height: 25px;
	font-size: 12px;
	float: left;
}
#collection button.paidbutton {
	background: #A0D468;
	cursor: auto;
}
#collection button.paidbutton:hover {
	background: #8CC152;
	cursor: auto;
}
.rightBorder {
	border-right: 1px solid #dddddd;
}
.dispatchRadio .custom-radio {
	background-image: url("../images/icons/radioButton.png");
	background-position: 0px 0px;
	background-repeat: no-repeat;
	display: block;
	float: left;
	height: 20px;
	width: 20px;
	margin: 2px 0 0px 0rem;
	overflow: hidden;
}
.dispatchRadio .custom-radio.checked {
	background-position: 0px -20px;
}
.dispatchRadio label {
	color: #000;
	margin: 1px 0 0 0 !important;
	padding: 0 0 0 5px;
}
.readyMeal {
	background: #959595;
	color: #fff;
	padding: 5px 8px;
	margin: 0px 10px 5px 0;
	float: left;
	max-width: 490px;
	overflow: hidden;
}
.qunMeal {
	background: #F1EDED;
	color: #2F2E2E;
	padding: 5px 8px;
	margin: 0px 10px 5px 0;
	float: left;
	max-width: 490px;
	overflow: hidden;
}
input[type="text"][disabled].disabledColor, input[type="text"][disabled], select[disabled], textarea[disabled] {
	background-color: #ECECEC;
	border-color: #e6e9ed;
}

.radioBut .radio {
	margin: 2px 10px 0 0;
	position: relative;
}
button.tiny, .button.tiny {
	padding-top: 0.300 rem;
	padding-bottom: 0.300 rem;
}
input.filterSelect {
	float: left;
	width: 125px !important;
	margin: 0 10px 8px 0
}
select.filterSelect {
	float: left;
	width: auto;
	margin: 0 10px 2px 0;
	padding: 0 8px 0 0;
}
label.inline {
	padding: 0;
}
.custom_tabs {
	width: 100%;
	overflow: hidden;
	padding: 0;
	margin: 10px 0 0 0;
}
.custom_tabs li {
	display: inline-block;
	float: left;
}
.custom_tabs li a.active, .custom_tabs li a:hover {
	background-color: #0080ff;
	color: #fff;
}
.custom_tabs li a {
	color: #fff;
	background-color: #666;
	padding: 8px 18px;
	font-size: 14px;
	float: left;
	margin: 0 0;
	border-right: 1px solid #fff;
}
/*CSS For Payment Page*/
#w_a p {
	padding: 5px 0;
	margin: 0;
}
#w_a label {
	display: inline-block;
	font-weight: bold;
	padding: 0 0 0 10px;
	vertical-align: top;
}
#w_a p input#enter_amt {
	width: 200px;
	margin: 0 0 7px 0;
}
#w_a p input#Pay_Now:hover {
	background-color: #e1671f;
	color: #fff;
	opacity: 1;
}

.tifinBox:hover .on_hover {
	opacity: 0.6
}
.on_hover {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	background: #000;
	z-index: 9999;
	opacity: 0;
}
.on_hover a {
	float: left;
	margin: 70px auto;
	width: 100%;
	color: #fff;
}
.mt60 {
	margin-top: 60px !important;
}

.css3slow, .on_hover {
	-webkit-transition: all 1000ms ease;
	-moz-transition: all 1000ms ease;
	-ms-transition: all 1000ms ease;
	-o-transition: all 1000ms ease;
}

dialog .close-reveal-modal, .reveal-modal .close-reveal-modal {
	font-size: 1.5rem;
}
dialog, .reveal-modal {
	padding: 0.875rem 1.875rem;
}



.listOfarea {
	clear: both;
}
.listOfarea label {
	margin-right: 10px;
}
.listOfarea select.left {
	display: inline-block;
	width: 15%;
}
.listOfppl li {
	display: inline-block;
	width: 18%;
}
.listOfppl li span.name {
	/*
	 position: relative;
	 top: -5px;*/

}
.listOfppl > ul {
	margin-left: 0;
}
.indivisualPpl {
	border: 1px solid #dddddd;
	overflow: hidden;
	padding: 20px 15px;
	/*  display: none;*/
}
/*
 .indivisualPpl .listOfarea{display: none;}
 .indivisualPpl .listOfppl{display: none;}*/

.sendNotification {
	overflow: hidden;
}

.tiffin_name {
	width: 100%;
}
.wallet {
	color: #006837;
	background: #DFF2BF !important;
	border: 2px solid #adde5c;
	padding: 15px;
}
.wallet_red {
	color: #be1e2d;
	background: #FDE4E1 !important;
	border: 2px solid #fcdcd5;
	padding: 15px;
}
.wallet_red a {
	color: #be1e2d;
	text-decoration: underline
}

.mr5 {
	margin-right: 5px !important;
}

.mr10 {
	margin-right: 10px !important;
}

.mr25 {
	margin-right: 25px !important;
}
.mb25 {
	margin-bottom: 25px !important;
}
.emailer p {
	color: #222
}

.settings .checker {
	margin-top: 7px;
}
.displayTable select {
	margin-bottom: 0px;
	min-width: 100px;
}

.exportoption span {
	margin-right: 5px;
}
.mt10 {
	margin-top: 10px !important;
}
.mt15 {
	margin-top: 15px !important;
}
.mb15 {
	margin-bottam: 15px !important;
}
.mr15 {
	margin-right: 15px !important;
}

table.collapsetable thead th, table.collapsetable thead td {
	padding: 10px !important;
}

.defaulttemplate .radio {
	margin-left: 35px
}

.pl0 {
	padding-left: 0px !important;
}
.pr0 {
	padding-right: 0px !important;
}

.read .fa {
	color: #48CFAD;
}
.unread .fa {
	color: #666666
}
.inqueue .fa {
	color: #ED5565
}
.meal_name{
    max-width: 250px;
    word-wrap: break-word;
}

/*custome CONTROL*/

.custom_length {
	font-size: 0.875 rem;
	color: #4d4d4d;
	display: block;
	font-weight: normal;
	line-height: 1.5;
	margin-bottom: 0;
}

.custom_length select {
	height: 28px;
	padding: 5px 0px;
	width: 60px;
	margin-bottom: 10px;
}
.custome_filter input {
	margin-left: 0;
	padding: 0px 5px;
	height: 25px;
	display: inline;
	width: 100px;
	margin-bottom: 10px;
}
.custom_info {
	color: #333333;
	padding-top: 10px;
}
/*.custom-pagination {
 margin-bottom: 0;
 padding-top: 10px;
 }
 ul.custom-pagination li a, ul.custom-pagination li.unavailable:hover a {
 color: #333333;
 border: 1px solid #cacaca;
 background-color: white;
 background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, white), color-stop(100%, gainsboro));
 background: -webkit-linear-gradient(top, white 0%, gainsboro 100%);
 background: -moz-linear-gradient(top, white 0%, gainsboro 100%);
 background: -ms-linear-gradient(top, white 0%, gainsboro 100%);
 background: -o-linear-gradient(top, white 0%, gainsboro 100%);
 background: linear-gradient(to bottom, white 0%, gainsboro 100%);
 border-radius: 0;
 padding: 0px 8px;
 }
 ul.custom-pagination li.current a, ul.custom-pagination li.current a:hover {
 background: #0080ff;
 border: 1px solid #0080ff;
 color: #FFFFFF;
 }
 ul.custom-pagination li.unavailable a:hover {

 color: #999999;
 }*/

.prepaid {
	margin-top: 5px;
}

.selectall .checker {
	margin-left: 7px;
	z-index: 1;
}
.timeselect {
	width: 70px;
	margin-left: 5px;
}

.width100 {
	width: 100px !important;
}
.popuptext {
	width: 100px !important;
	display: inline !important;
	margin-bottom: 0px !important;
	height: 1.2 rem !important;
	padding: 1px 6px !important;
}
.mb0 {
	margin-bottom: 0px !important;
}
.pr0 {
	padding-right: 0px !important;
}
.pl0 {
	padding-left: 0px !important;
}
.mb5 {
	margin-bottom: 5px !important;
}

.calendar .selector {
	width: 90% !important;
	background: none !important;
}
.calendar .selector .fi-clock {
	position: absolute;
	top: 0;
}
.calendar .selector span {
	position: absolute;
	top: 0;
	left: 30px;
	background: none !important;
}

.calendar .selector .date-selector, .calendar .selector .time-selector {
	margin-bottom: 0px !important;
}

.delivered {
	margin-bottom: 0px;
}
.delivered li:first-child {
	list-style: disc;
}
.delivered li:nth-child(2n) {
	font-size: 12px;
}
.calendar {
	min-width: 100px;
}
.fromto .calendar {
	min-width: 100px;
	float: left;
}
.dn {
	display: none;
}
.mb15 {
	margin-bottom: 15px;
}
.mtm4 {
	margin-top: -4px !important;
}

/* new*/
.tabs button:hover, .print .btn.dropdown:hover, /**/button.tiny:hover, .button.tiny:hover, form .large-12 .row .right button:hover, .addRecord button:hover, .order_details li a:hover {
	background: #FC6E51;
	opacity: 1;
	-webkit-transition: all 0.5s ease;
	-moz-transition: all 0.5s ease;
	transition: all 0.5s ease;
}

.tabs button, .print .btn.dropdown, button.tiny, form .large-12 .row .right button, .addRecord button, .order_details li a {
	background: #959595;
	color: #FFFFFF;
	-webkit-transition: all 0.5s ease;
	-moz-transition: all 0.5s ease;
	transition: all 0.5s ease;
}
form .large-12 .row .right button.btn_theme, button.tiny.btn_theme {
	background: #FC6E51;
}
button.tiny.btn_theme:hover {
	background: #E9573F;
}
.print .btn.dropdown {
	padding-right: 35px;
}

.tabs .active {
	background: #4FC1E9;
}

/*custom pagination*/
.custom_info {
	color: #333333;
	padding-top: 10px;
}
.custom-pagination {
	margin-bottom: 0;
	padding-top: 10px;
}
/*
 ul.custom-pagination li a, ul.custom-pagination li.unavailable:hover a {
 color: #333333;
 border: 1px solid #cacaca;
 background-color: white;
 background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, white), color-stop(100%, gainsboro));
 background: -webkit-linear-gradient(top, white 0%, gainsboro 100%);
 background: -moz-linear-gradient(top, white 0%, gainsboro 100%);
 background: -ms-linear-gradient(top, white 0%, gainsboro 100%);
 background: -o-linear-gradient(top, white 0%, gainsboro 100%);
 background: linear-gradient(to bottom, white 0%, gainsboro 100%);
 border-radius: 0;
 padding: 0px 8px;
 }
 ul.custom-pagination li.current a, ul.custom-pagination li.current a:hover {
 background: #3BAFDA;
 border: 1px solid #4A89DC;
 color: #FFFFFF;
 }
 ul.custom-pagination li.unavailable a:hover {

 color: #999999;
 }*/

.mar8 {
	margin-top: 8px;
}

.cusCheckBox div.checker, div.checker span, div.checker input {
	z-index: 1;
}

.lastRoad, .rmv_sorting_1 {
	background: none repeat scroll 0 0 #fcc7c7 !important;
}

.wallet:hover {
	color: #006837;
	opacity: 1;
}
.large-20 {
	width: 20%;
	margin-bottom:20px;
}
.large-14 {
	width: 14.28%;
}
.advance_search {
	display: none;
	background-color: #fff;
	padding: 15px 0 10px;
	border: 1px solid #ececec;
	margin-bottom: 0px;
	overflow: hidden;
}
.sales_data_table {
	position: relative;
}
.advance_search_click {
	color: #959595;
	float: none;
	position: absolute;
	right: 19px;
	top: 19px;
	z-index: 1;
	border-bottom: 1px solid #959595;
	padding-bottom: 1px;
}
.advance_search_click:hover {
	border-bottom: 1px solid #FC6E51;
}
.neft_detail {
	display: none
}
.SumoSelect > .CaptionCont, .SumoSelect {
	width: 100%
}
.SumoSelect > .CaptionCont {
	background-color: #fff;
	background-repeat: no-repeat;
	background-position: 97% center;
	border: 1px solid #cccccc;
	padding: 0.4 rem 0.5 rem;
	font-size: 0.875 rem;
	color: rgba(0, 0, 0, 0.75);
	line-height: normal;
	border-radius: 0;
	height: 2.0 rem;
	border-radius: 2px;
}
.SumoSelect .select-all {
	border-radius: 3px 3px 0px 0px;
	position: relative;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	padding: 5px 0px 3px 35px;
	height: 30px;
	margin: 0;
}
button, .button {
	background-color: #FC6E51;
	margin-bottom: 0;
}
/*button:hover, .button:hover {
	background-color: #e9573f;
}*/
.order_details li a {
	padding: 7px 10px;
	margin: 0 5px;
	color: #fff;
	display: inline-block;
	font-size: 14px;
}
.order_details li a.disable {
	pointer-events: none;
	cursor: default;
	color: #fff;
	background: #BFBFBF;
}
.hishtory_table a {
	display: block;
}
.Yesterday, .Last_Week {
	display: none;
}
/*CSS Sagar*/
.push-50-t {
	margin-top: 50px !important;
}
.push-15 {
	margin-bottom: 15px !important;
}
.push-15-r {
	margin-right: 15px !important;
}
.animated {
	-webkit-animation-duration: 1s;
	animation-duration: 1s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}
.fadeIn {
	-webkit-animation-name: fadeIn;
	animation-name: fadeIn;
}
.img-avatar-thumb {
	margin: 5px;
	-webkit-box-shadow: 0 0 0 5px rgba(255,255,255,0.4);
	box-shadow: 0 0 0 5px rgba(255,255,255,0.4);
}
.zoomIn {
	-webkit-animation-name: zoomIn;
	animation-name: zoomIn;
}
.push-5-t {
	margin-top: 5px !important;
}
.text-white {
	color: #fff;
}
.text-white-op {
	color: rgba(255,255,255,0.85);
}
.content.bg-image {
	padding: 30px;
}
.content {
	margin: 0 auto;
	padding: 30px 0;
	max-width: 100%;
	overflow-x: visible;
}
.img-avatar {
	display: inline-block !important;
	/* height: 64px; */
	/* border-radius: 50%; */
	margin: 5px;
	-webkit-box-shadow: 0 0 0 5px rgba(255,255,255,0.4);
	box-shadow: 0 0 0 2px rgba(255,255,255,0.4);
	padding: 5px;
	/* border-radius: 50%; */
	background-color: rgb(132, 132, 132);
}
.h2 {
	font-size: 30px;
	margin: 0;
}
.h5 {
	font-size: 16px;
}
.border-b .medium-4 {
	text-align: center;
}
.border-b {
	border-bottom: 1px solid #e9e9e9;
}
.text-gray-darker {
	color: #393939;
}
.text-primary {
	color: #FC6E51;
}
.font-w300 {
	font-weight: 300 !important;
}
.font-w700 {
	font-weight: 700 !important;
}
.border-b .row .medium-4 a {
	padding: 10px 0 0 0;
	display: inline-block;
	font-size: 20px;
}
.block-header {
	padding: 15px 10px;
	-webkit-transition: opacity .2s ease-out;
	transition: opacity .2s ease-out;
}
.content .block {
	margin-bottom: 20px;
}
.bg-gray-lighter {
	background-color: #f9f9f9;
}
.block-title {
	font-size: 14px;
	font-weight: 600;
	text-transform: uppercase;
	line-height: 1.2;
}
.block {
	margin-bottom: 30px;
	background-color: #fff;
	-webkit-box-shadow: 0 2px rgba(0,0,0,0.01);
	box-shadow: 0 2px rgba(0,0,0,0.01);
	/*margin-left: -0.9375rem;
	 margin-right: -0.9375rem;*/
}
.medium-7 .block {
	margin-left: -0.9375 rem;
}
.medium-5 .block {
	margin-right: -0.9375 rem;
}
.block-content {
	margin: 0 auto;
	padding: 20px 20px 1px;
	max-width: 100%;
	overflow-x: visible;
	-webkit-transition: opacity .2s ease-out;
	transition: opacity .2s ease-out;
}
.block-content .pull-t {
	margin-top: -20px;
}
.list-timeline:before {
	position: absolute;
	top: 0;
	left: 120px;
	bottom: 0;
	display: block;
	width: 4px;
	content: "";
	background-color: #f9f9f9;
	z-index: 1;
}
.list-timeline > li {
	min-height: 40px;
	z-index: 2;
}
.list-timeline .list-timeline-content {
	padding: 10px 10px 1px;
}
.list-timeline .list-timeline-content {
	padding-left: 160px;
}
.list-timeline .list-timeline-content p {
	font-size: 14px;
	color: #646464;
}
.list-timeline .list-timeline-content p span {
	clear: both;
	display: block;
}
.font-s13 {
	font-size: 13px !important;
}
.block-content p, .block-content .push, .block-content .block, .block-content .items-push > div {
	margin-bottom: 20px;
}
.list-timeline {
	padding-top: 20px;
}
.list-timeline {
	position: relative;
}
.list-timeline .list-timeline-time {
	margin: 0 -20px;
	padding: 10px 20px 10px 40px;
	min-height: 40px;
	text-align: right;
	color: #656D78;
	font-size: 13px;
	border-radius: 2px;
}
.list-timeline .list-timeline-time {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	padding-right: 0;
	padding-left: 0;
	/*width: 90px;*/
	background-color: transparent;
}
.font-w600 {
	font-weight: 600 !important;
}
.list {
	margin: 0;
	padding: 0;
	list-style: none;
}
.list > li {
	position: relative;
}

.bg-white {
	background-color: #fff;
}
.page-container.sidebar-closed ~
.footer {
	padding-left: 115px;
}

fieldset {
	border: 0;
	border-radius: 2px;
	margin-bottom: 20px;
	background-color: #fff;
	position: relative;
	-webkit-box-shadow: none;
	box-shadow: none;
	padding: 0;
	padding-bottom: 20px;
}
.nopaddingbottom {
	padding-bottom: 0 !important;
}
.panel-heading + .panel-body {
	padding-top: 0;
}
fieldset legend {
	padding: 20px;
	border-bottom: 1px solid transparent;
	border-top-right-radius: 1px;
	border-top-left-radius: 1px;
	text-transform: uppercase;
	font-size: 12px;
	font-weight: 700;
	letter-spacing: .2px;
	margin-left: 0;
	width: 100%;
	border-bottom: 1px solid #dddddd;
	margin-bottom: 20px
}
.panel-body {
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 2px;
}
hr {
	margin: 20px 0;
	border-color: #dbdfe6;
}
fieldset input, input[type="text"], input[type="password"], input[type="date"], input[type="datetime"], input[type="datetime-local"], input[type="month"], input[type="week"], input[type="email"], input[type="number"], input[type="search"], input[type="tel"], input[type="time"], input[type="url"], textarea, select {

	width: 100%;
	height: 25px;
	padding: 0 0 0 5px;
	font-size: 12px;
	line-height: 1.42857143;
	color: #656D78;
	background-color: #fcfcfd;
	background-image: none;
	border: 1px solid #bdc3d1;
	border-radius: 2px;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	-webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
	-o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
	transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
input[type="file"]{
	padding: 0;
    height: 21px;
}
.jqte * {
	color: #656D78;
}
input.calender {
	background: url(../images/icons/calendar.png) right no-repeat #fcfcfd !important;
}
form .large-12 {
	border: 0;
	border-radius: 2px;
	margin-bottom: 20px;
	background-color: #fff;
	position: relative;
	-webkit-box-shadow: none;
	box-shadow: none;
	padding: 20px 0;
}
form .large-12 .right {
	padding: 0 20px 0 0;
}
textarea {
	resize: none;
}
input[type="text"]:focus, input[type="password"]:focus, input[type="date"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="time"]:focus, input[type="url"]:focus, textarea:focus {
	box-shadow: none;
}
.ui-datepicker-inline {
	max-width: 300px;
	border-color: #bdc3d1;
}
.ui-datepicker, .ui-widget-content {
	background-color: #fff;
	border: 1px solid #9fa8bc;
	font-family: inherit;
	font-size: inherit;
	padding: 10px;
	margin: 1px 0 0;
	border-radius: 2px;
	width: auto !important;
}
.ui-datepicker .ui-datepicker-header {
	font-weight: 700;
	text-transform: uppercase;
	color: #696c74;
	padding: 0 0 5px;
	letter-spacing: 1px;
	border: 0;
	background-color: transparent;
	border-top-right-radius: 2px;
	border-top-left-radius: 2px;
	background: none;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
	color: #dee1e7;
	top: 6px;
	font-size: 11px;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev:before {
	left: 0;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next, .ui-datepicker .ui-datepicker-header .ui-datepicker-next:before {
	right: 0;
}
.ui-datepicker .ui-datepicker-title {
	color: #D3CAC1;
	font-size: 14px;
}
.ui-datepicker .ui-datepicker-title {
	color: #D3CAC1;
}
.ui-datepicker .ui-datepicker-title {
	color: #D3CAC1;
}
.ui-datepicker .ui-datepicker-calendar {
	margin: 0;
	background-color: transparent;
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 2px;
}
.ui-datepicker .ui-datepicker-calendar th {
	text-transform: uppercase;
	font-size: 11px;
	font-weight: 700;
	letter-spacing: 1px;
	padding: 6px 10px;
	color: #82858e;
}
.ui-datepicker .ui-datepicker-calendar td {
	border: 1px solid #fff;
	padding: 0;
	background-color: #fcfcfd;
}
.ui-datepicker .ui-datepicker-calendar td a:hover {
	background-color: #d8dce3;
	color: #696c74;
}
.ui-datepicker .ui-datepicker-calendar td span, .ui-datepicker .ui-datepicker-calendar td a {
	-webkit-transition: all 0.2s ease-out 0s;
	-o-transition: all 0.2s ease-out 0s;
	transition: all 0.2s ease-out 0s;
	padding: 5px 8px;
	background-color: #f6f7f8;
	color: #696c74;
	padding: 6px 10px;
	display: block;
	font-weight: 400;
	font-size: 12px;
	border: 0;
	border-radius: 1px;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:before {
	content: '\f053';
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next:before {
	content: '\f054';
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next:before, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev:before {
	font-family: 'FontAwesome';
	position: absolute;
	top: 2px;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev-hover {
	color: #c0c7d2;
	cursor: pointer;
	top: 1px;
	border: 0;
	background-color: transparent;
}
.ui-datepicker {
	background-color: #fff;
	border: 1px solid #9fa8bc;
	font-family: inherit;
	font-size: inherit;
	padding: 10px;
	margin: 1px 0 0;
	border-radius: 2px;
	width: auto !important;
	z-index: 10000 important;
}
table.ui-datepicker-calendar tr.odd, table.ui-datepicker-calendar tr.alt, table.ui-datepicker-calendar tr:nth-of-type(odd) {
	background: transparent;
}
.breadcrumb {
	padding: 0;
	background-color: transparent;
	margin-bottom: 10px;
}
.breadcrumb > li {
	font-size: 16px;
}
.breadcrumb > li > a {
	color: #545b68;
	font-size: 16px;
}
.breadcrumb > li:first-child i.fa-angle-right {

	display: none;
}
.breadcrumb > li + li:before, .breadcrumb > li i {
	content: '\f105';
	font-family: 'FontAwesome';
	color: #818da7;
	margin-right: 5px;
}

/*CSS For Data Table*/
.dataTables_paginate .pagination > .paginate_button.next, .dataTables_paginate .pagination > .paginate_button.previous {
	font-size: 12px;
	text-transform: uppercase;
}
.dataTables_paginate .pagination > .paginate_button.next > a, .dataTables_paginate .pagination > .paginate_button.previous > a {
	padding: 10px 15px 9px;
}
ul.pagination > li {
	display: inline;
	margin: 0;
}
ul.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus {
	z-index: 2;
	color: #ffffff;
	background-color: #259dab;
	border-color: transparent;
	cursor: default;
}
ul.pagination > li + li > a, .pagination > li + li > span {
	margin-left: 1px;
}
ul.pagination > li > a, .pagination > li > span {
	font-weight: 700;
	-webkit-transition: all 0.2s ease-out 0s;
	-o-transition: all 0.2s ease-out 0s;
	transition: all 0.2s ease-out 0s;
	padding: 9px 14px;
}
ul.pagination > li > a, .pagination > li > span, .dataTables_wrapper .dataTables_paginate .paginate_button, .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
	position: relative;
	float: left;
	padding: 5px 13px;
	line-height: 1.42857143;
	text-decoration: none;
	color: #fff !important;
	background-color: #ADADAD;
	border: 0;
	margin-left: -1px;
	margin: 1px;
	background: #ADADAD;
	-webkit-transition: all 0.5s ease;
	-moz-transition: all 0.5s ease;
	transition: all 0.5s ease;
}
ul.pagination > li > a:hover, .pagination > li > span:hover, .pagination > li > a:focus, .pagination > li > span:focus, .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
	z-index: 3;
	color: #505b72;
	background-color: #FC6E51;
	border-color: transparent;
}
.dataTables_paginate .pagination > .paginate_button > a {
	padding-left: 14px;
	padding-right: 14px;
}
ul.pagination li.current a, ul.pagination li a {
	border-radius: 0
}
.custom_info, .dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_filter, .dataTables_wrapper .dataTables_info, .dataTables_wrapper .dataTables_processing, .dataTables_wrapper .dataTables_paginate {
	font-size: 14px;
}
.dataTables_wrapper .dataTables_info{
	    padding-top: 15px;
}
dialog, .reveal-modal {
	z-index: 9999;
}
.reveal-modal {
	background:#f1f1f1; 
}
table thead tr:nth-of-type(odd) {
	background: #F1EDED;
}
table thead tr th {
	padding: 15px 10px;
}

.tabs button, .print .btn.dropdown {
	height: 38px;
}
ul.pagination li.unavailable a {
	cursor: default;
	color: #FFFFFF;
}
ul.pagination li.current a, ul.pagination li.current a:hover, .dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
	background: #FC6E51 !important;
	color: white !important;
	font-weight: bold;
	cursor: default;
	border: 0;
}
ul.pagination > li > a:hover, .pagination > li > span:hover, .pagination > li > a:focus, .pagination > li > span:focus {
	color: #fff;
}
table.dataTable.no-footer {
	border-bottom: solid 1px #f1f1f1;
}
.dataTables_wrapper .dataTables_filter input {
	font-size: inherit;
}
table.dataTable thead th, table.dataTable thead td {
	border-bottom: 0;
	padding:5px;
}
table.dataTable.display tbody tr.odd > .sorting_1, table.dataTable.order-column.stripe tbody tr.odd > .sorting_1, table.dataTable.display tbody tr.even > .sorting_1, table.dataTable.order-column.stripe tbody tr.even > .sorting_1 {
	background: transparent;
}
table.dataTable thead .sorting {
	padding-right: 20px;
}
.dataTables_wrapper .dataTables_length select {
	height: auto;
	padding: 6px 0px;
	width: 69px;
	margin: 0 0 0.8 rem 0;
	color: #262b36;
	/* line-height: 20px; */
	padding-left: 12px;
}
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
	cursor: default;
	color: #FFF !important;
	border: 0;
	background: #ADADAD;
	box-shadow: none;
}
.custom_info, .dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_info, .dataTables_wrapper .dataTables_processing, .dataTables_wrapper .dataTables_paginate {
	color: #656D78;
	padding: 5px 10px 0 10px;
}
.dataTables_wrapper .dataTables_length {
	padding-top: 0;
	/*padding-left: 10px;*/
}
.advance_search {
	display: none;
}
.ui-datepicker .ui-datepicker-header .ui-datepicker-next, .ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
	top: 6px !important;
}
.mb20 {
	margin-bottom: 20px;
}
.readyMeal .red {
	color: #fff;
}
.qunMeal .qun {
	color: #FC6E51;
}
.sales_data_table .dataTables_wrapper .dataTables_filter {
	float: none;
	position: absolute;
	right: 180px;
}
.user-icon {
	font-size: 20px;
}
.user-icon_big {
	font-size: 50px;
	color: rgb(51, 51, 51);
}
.big-user-icon {
	font-size: 45px;
	color: #fff;
	margin-top: 8px;
}
.radio + label {
	line-height: 19px;
}
.dn {
	display: none;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
.common-row{
	margin-left:-0.9375rem;
	margin-right:0.9375rem;
}

/*#filter_month, #filter_quarter_number {
 display: none;
 }*/
.advance_lable {
	display: inline-block;
	float: left;
	margin: 0px 5px 0 0;
}
.total_table {
	width: 100%;
	margin: 20px auto;
	clear: both;
	border-collapse: separate;
	border-spacing: 0;
}
.total_table tr th {
	color: #3b4354;
	font-weight: bold;
}
table.total_table tr:nth-of-type(odd) {
	background-color: #d8dce3;
}
table.total_table {
	border: 0;
}
table.total_table tr:nth-of-type(even) {
	background-color: #F0F1F4;
}
table.total_table tr td {
	border-top: 1px solid #ffffff;
	font-weight: bold;
}
.import {
	background: #959595;
	color: #FFFFFF;
	-webkit-transition: all 0.5s ease;
	-moz-transition: all 0.5s ease;
	transition: all 0.5s ease;
	padding: 5px 10px 4px 10px;
	display: inline-block;
}
.import:hover {
	background: #FC6E51;
	color: #FFFFFF;
}
label.inline {
	margin: 0 5px 0.5 rem 0;
}

button.smBtn .fa {
	font-size: 12px;
}
.input_lable {
	display: inline-block;
	vertical-align: top;
	font-size: 16px;
	font-weight: 400;
	line-height: 15px;
}
.check_all {
	margin: -3px 0 0 13px;
}
.mCSB_outside + .mCSB_scrollTools {
	right: -8px;
}
.mCS-light-thin.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar, .mCS-dark-thin.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
	width: 5px;
}
.print select.filterSelect {
	height: 34px;
}

.sortList_container ul#sortable {
	margin-left: 0;
}
.sortList_container #sortable .ui-state-default {
	background: none repeat scroll 0 0 white;
	border: 1px solid #ccc;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
	font-size: 14px;
	margin: 0 0 7px;
	padding: 2px 10px;
}
.sortList_container #sortable .ui-state-default .ui-icon.ui-icon-arrowthick-2-n-s {
	background-position: -128px -46px;
	display: inline-block;
	margin: 0 10px 0 0;
}
.select-kitchen-top {
	margin: 0 15px 0 0;
}
.select-kitchen-top {
	position: relative;
}
.select-kitchen-top select {
	height: 36px;
	padding: 7px;
	margin:10px 15px;
	padding: 2px 10px;
	border: 1px solid #656D78;
	-webkit-appearance: none;
	-moz-appearance: none;
	-ms-appearance: none;
	-o-appearance: none;
	appearance: none;
	position: relative;
	  border: 1px solid #f1f1f1;
  background-color: transparent;
  box-shadow: none;
    font-size: 0.875rem;
}
.select-kitchen-top:after {
	    content: "\f0d7";
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  position: absolute;
  font-size: 18px;
  color: rgb(153, 153, 153);
  top:8px;
  right: 0;
  margin: 0;
  z-index: 1;
}
/*Pallavi*/

.btnHover, button.tiny, .button.tiny, form .large-12 .row .right button, .large-12 .right button, .reveal-modal /*.smsSend*/ button, .print button:hover{
	background: #fc6e51;
}
/**/.btnHover:hover, button.tiny:hover, .button.tiny:hover, form .large-12 .row .right button:hover, .large-12 .right button:hover, .reveal-modal /*.smsSend*/ button:hover{
	background: #e9573f;
}
button, .button {
	border-radius: 0px !important;
}
.add-customer-checkbox{
	display: inline-block;
	padding: 0 5px;
}
.add-customer-checkbox label{
	display: inline-block;
	line-height: 15px;
}
									
.breakfast legend, .lunch legend, .dinner legend, .snacks legend, .fastfood legend{
	padding: 10px 20px;
	text-transform: none;
}
.breakfast fieldset, .lunch fieldset, .dinner fieldset, .snacks fieldset, .fastfood fieldset{
	margin-bottom: 0px;
}
.sendNotification span{
	color: #FC6E51;
}
.sms tr{
	background: #f0f0f0 !important;
	border:1px solid #F4C5BC ;
	
}
.sms tr td {
	border-bottom:1px solid #F4C5BC ;
}


table.display.addRole{
	width: 95%;
}
.subscribe:before{
	left: 224px;
}
.subscribe .list-timeline-content{
	padding-left: 270px;
}
.sysSetting{
	background: transparent;
}
.sysSetting fieldset{
	padding-bottom: 0;
}
.sysSetting legend{
	margin-bottom: 0;
}
.sysSetting .list-timeline:before {
    left: 224px;
}
.sysSetting .list-timeline-content{
	padding-left: 270px;
}
.subscribe_log .visual1{
	float: left;
	color:#FFFFFF;
	font-size: 23px;
	height: 50px;
	width: 50px;
}
.subscribe_log .visual1 i{
	/*padding: 16px 0 0 16px;*/
	padding: 14px;
}
.subscribe_log .details {
	 width: 70%;
	 padding-right: 0px;
}
.subscribe_log .num{
	color: #666;
	padding-top: 0px;
	font-size: 27px;
	text-align: center;
}
.subscribe_log .details .desc{
	
	text-align: center;
	color: #888;     
	padding-top: 4px;
}
.product-name {
	   display: block;
	  text-align: center;
	  font-size: 16px;
	  cursor: pointer;
	  margin: 2px 0;
	  background-color: #DEDEDE;
	  color: #666685;
	  padding: 5px 0;
}
.product-name.active {
	background-color: #ADADAD;
  	color: #fff;
}
.product-handler {
	text-align: center;
}
.left-product, .product-handler-wrapper, .right-product {
	background-color: #fff;
	height: 100%;
}
.left-product, .product-handler, .right-product {
	height: 100%;
}
.slide-left i, .slide-right i, .slide-double-left i, .slide-double-right i {
	font-size: 40px;
	margin: 20px 0 0;
	cursor: pointer;
}
.slide-left, .slide-right, .slide-double-left, .slide-double-right  {
    padding-bottom: 15px;
}


.prod_search .tabs-content {
	margin-bottom: 0;
} 
.prod_search .common-tabs-content .content {
    padding-top: 0px;
}
.prod_search .product-search {
	padding-top: 15px;
}
.prod_search .fa.fa-angle-double-right.fa-stack-1x.fa-inverse, .prod_search .fa-angle-right.fa-stack-1x.fa-inverse {/*.fa-stack-1x.fa-inverse*/
    font-size: 29px;
    left: 2px;
    top: -1px;
}
 .prod_search .fa.fa-angle-double-left.fa-stack-1x.fa-inverse, .prod_search .fa-angle-left.fa-stack-1x.fa-inverse{/*.fa-stack-1x.fa-inverse*/
    font-size: 29px;
    left: -1px;
    top: -2px;
}

.prod_search .help_text{
	margin-top: 40px;
}
.prod_search .help_text i{
	color: #fc6e51;
}
.prod_search input.calender {
    width: 80%;
}

/*product search*/
.common-tabs{
	background-color: #ADADAD;
}
.common-tabs li.tab-title a {
	background:#ADADAD;
	color: #FFFFFF;
	padding: 10px 15px!important;
}
.common-tabs li.tab-title.active a,.common-tabs li.tab-title a:hover{
	background: #FC6E51;
	color: #FFFFFF;
}
.common-tabs-content {
	  border: 1px solid #E8E8E8;
}
.common-tabs-content .content{
	padding:15px;
} 


.month-box{
	border:1px solid #ccc;
	padding:10px;
	 margin-bottom: 10px;
}
.month-box h4{
	color:#fc6e51;
	margin-bottom: 10px;
}
.month-box span{
	border:1px solid #646464;
	padding:5px;
	display: inline-block;
    margin-bottom: 5px;
    margin-right: 5px;
}
/*end product search*/

/*holiday*/
.prepaidrow .day{
	background-color: #fcfcfd;
    background-image: none;
    border: 1px solid #bdc3d1;
    border-radius: 2px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    color: #656D78;
    font-size: 12px;
    height: 25px;
    line-height: 1.42857;
    padding: 0 0 0 5px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    width: 100%;
										}
.prepaidrow .CaptionCont.SlectBox {
    background: #fcfcfd none repeat scroll 0 0;
    color: #656D78;
    font-size: 12px;
    height: 25px;
}
.prepaidrow .SumoSelect > .CaptionCont > span.placeholder {
    color: #ccc;
    font-style: normal;
}
.prepaidrow .SumoSelect:focus > .CaptionCont, .prepaidrow .SumoSelect:hover > .CaptionCont {
	border: 1px solid #bdc3d1;
	box-shadow: none;
}
.input-icon input {
	color: #666;
	font-size: 15px;
}
.input-icon i {
	margin-top: 5px;
}



/*dispatch*/
.dataTables_filter{
		/*display: none;*/
}
	
   .dispatch_div{
   		/*text-align: center;*/
   		box-shadow: 0 0 2px #d1d1d1;
   		color: #656D78;
   		padding-bottom: 10px;
   }
   .order_details{
   		color: #fff;
   		padding-bottom: 1px;
   		padding-left: 5px;
   }
   .dispatch_div p{
   		margin-bottom: 5px;
   }
   .btn_dispatch{
   	text-align: center;
   }
   .dispatch_tbl{
   		background: transparent none repeat scroll 0 0;
   		border:0;
   		 border-bottom: 1px solid #f1f1f1;
   		width: 100%;
   		margin-bottom: 0.25rem;
   		
   }
   
   button.blueBg:hover{
   	background: #31a5d0;
   }
   
   .dispatch_tbl thead{
   		background: transparent none repeat scroll 0 0;
   }
   .dispatch_tbl thead tr:nth-of-type(2n+1){
    	background: transparent none repeat scroll 0 0;
   }
   .dispatch_tbl tr.odd, .dispatch_tbl tr.alt, .dispatch_tbl tr:nth-of-type(2n+1) {
        background: transparent none repeat scroll 0 0;
  }
  .dispatch_tbl tr th, .dispatch_tbl tr td{
  		border:0;
  		font-size: 16px;
  		text-align: center;
  		padding: 10px;
  		
  }
  .div_border1{
  	border: 4px solid #3BAFDA;
  }
  .dispatch_tbl.tbl1, .dispatch_tbl.tbl1 tr th, .dispatch_tbl.tbl1 tr td, .ttl1 p{
  	color: #3bafda;
  }
  .div_border2{
  	border: 4px solid #ED5565;
  }
  .dispatch_tbl.tbl2, .dispatch_tbl.tbl2 tr th, .dispatch_tbl.tbl2 tr td, .ttl2 p{
  	color: #ED5565;
  }
  .div_border3{
  	border: 4px solid #A0D468;
  }
  .dispatch_tbl.tbl3, .dispatch_tbl.tbl3 tr th, .dispatch_tbl.tbl3 tr td, .ttl3 p{
  	color: #A0D468;
  }
  .div_border4{
  	border: 4px solid #959595;
  }
  .dispatch_tbl.tbl4, .dispatch_tbl.tbl4 tr th, .dispatch_tbl.tbl4 tr td, .ttl4 p{
  	color: #959595;
  }
  .kot_btn:hover{
  	background: #3bafda;
  }
  .bill_btn:hover{
  	background: #ED5565;
  }
  .disp_btn:hover{
  	background: #A0D468;
  }

.ttl_price{
	float: right;
    margin-right: 22%;
}
/*.meal_name{
    max-width:200px !important; 
}*/
/************Meal plan************/

.plan_tbl{
	border: none;
	width: 100%;
}
.plan_tbl td{
	border-bottom: none;
}
.plan_tbl th{
	border-bottom: none;
	font-weight: bold;
}
.plan_tbl tr.odd, .plan_tbl tr.alt, .plan_tbl tr:nth-of-type(2n+1) {
    background: transparent none repeat scroll 0 0;
}
.promo_input{
	margin-bottom:0 !important;
	padding: 0 !important;
}
.pt20{
	padding-top: 20px;
}
.holiday{
	color: red;
	margin: 10px 5px;
}
.avail_dates{
	color: green;
	margin: 10px 5px;
}
.not-avail_dates{
	color: red;
	margin: 10px 5px;
}
.holiday i, .avail_dates i, .not-avail_dates i{
	padding: 0 5px;
}

/*********orderEdit******/
.edit_btn {
	float: right;
}
.edit_btn:hover {
	background: #e9573f;
}
/******************/
.receipts tr.details td.details-control {
    background: url('../images/details_close.png') no-repeat center center !important;
    cursor: pointer;
}
.receipts td.details-control {
	background: url('../images/details_open.png') no-repeat center center !important;
	cursor: pointer;
}
.collapseTbl th{
	font-weight: bold
}
/*.collapseTbl{
	 margin: 15px 0;
}*/
.headingTbl{
	margin: 15px 0;
}
#mCSB_1_scrollbar_vertical .mCSB_dragger{ width:9px; }

#mCSB_1_scrollbar_vertical .mCSB_dragger .mCSB_dragger_bar{ width:9px; }


.width50per {
    width: 50% !important;
}
.disinlbk {
    display: inline-block !important;
}

.radiocls label.right {
	display: inline-block;
}

/*Media Queries Start*/
@media (max-width:820px) {
	.saveBtn{
		margin-bottom: 5px;
	}
}
@media (min-width: 320px) {/* smartphones, portrait iPhone, portrait 480x320 phones (Android) */
	.page-404 .number {
		text-align: center;
	}
	.hidden-phone {
		display: block;
	}
	.page-sidebar {
		display: none;
	}
	.show-phone {
		display: none;
	}
	.spinerBox {
		font-size: 0px;
	}
	label.right {
		text-align: left;
	}
	.drawChart {
		height: 200px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 55px;
		padding-left: 30px;
	}
	.inner-tab h1 {
		font-size: 13px;
	}
}
@media (min-width: 480px) {/* smartphones, Android phones, landscape iPhone */
	.page-404 .number {
		text-align: center;
	}
	.hidden-phone {
		display: block;
	}
	.page-sidebar {
		display: none;
	}
	.show-phone {
		display: none;
	}
	.spinerBox {
		font-size: 14px;
	}
	label.right {
		text-align: left;
	}
	.drawChart {
		height: 200px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 55px;
		padding-left: 30px;
	}

}
@media (min-width: 600px) {/* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
	.page-404 .number {
		text-align: center;
	}
	.hidden-phone {
		display: none;
	}
	.page-sidebar {
		display: block;
	}
	.show-phone {
		display: block;
	}
	.spinerBox {
		font-size: 14px;
	}
	label.right {
		text-align: left;
	}
	.drawChart {
		height: 300px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 55px;
		padding-left: 32px;
	}
	.inner-tab h1 {
		font-size: 15px;
	}
}
@media (min-width: 801px) {/* tablet, landscape iPad, lo-res laptops ands desktops */
	.page-404 .number {
		text-align: center;
	}
	.hidden-phone {
		display: none;
	}
	.page-sidebar {
		display: block;
	}
	.show-phone {
		display: block;
	}
	label.right {
		text-align: left;
	}
	.drawChart {
		height: 500px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 55px;
		padding-left: 30px;
	}
	.inner-tab h1 {
		font-size: 15px;
	}
}
@media (max-width: 1370px) {

	.prod_search .fa.fa-angle-double-right.fa-stack-1x.fa-inverse, .prod_search .fa-angle-right.fa-stack-1x.fa-inverse {/*.fa-stack-1x.fa-inverse*/
    font-size: 24px;
    left: 1px;
    top: -5px;
	}
	.fa-stack {
		height: 1.5em;
	}
	 .prod_search .fa.fa-angle-double-left.fa-stack-1x.fa-inverse, .prod_search .fa-angle-left.fa-stack-1x.fa-inverse{/*.fa-stack-1x.fa-inverse*/
	    font-size: 24px;
	    left: -1px;
	    top: -5px;
	}
	.slide-left i, .slide-right i, .slide-double-left i, .slide-double-right i {
	    font-size: 33px;
	   
	}
	
	.prod_search .help_text{
		margin-top: 31px;
		font-size: 71%;
	}
}
@media (max-width: 1280px) {

	.totalCost {
		width: 30px;
	}

	.tifinInfo .alert-box {
		margin: 0 0 4px;
		padding: 8px 3px;
	}
	


}

@media (min-width: 1024px) {/* big landscape tablets, laptops, and desktops */
	.page-404 .number {
		text-align: right;
	}
	.hidden-phone {
		display: none;
	}
	.page-sidebar {
		display: block;
	}
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.drawChart {
		height: 500px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 55px;
		padding-left: 30px;
	}
	.inner-tab h1 {
		font-size: 18px;
	}

}
@media (min-width: 1200px) {/* MAC */
	.page-404 .number {
		text-align: right;
	}
	.hidden-phone {
		display: none;
	}
	.page-sidebar {
		display: block;
	}
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.drawChart {
		height: 500px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 55px;
		padding-left: 30px;
	}
}
@media (min-width: 1281px) {/* hi-res laptops and desktops */
	.page-404 .number {
		text-align: right;
	}
	.hidden-phone {
		display: none;
	}
	.page-sidebar {
		display: block;
	}
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.drawChart {
		height: 500px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 55px;
		padding-left: 30px;
	}
}

@media (max-width: 1366px) {
	.large-8.columns.del_form {
		width: 100%;
	}
}
@media (max-width: 1723px) {
	.large-14 {
		width: 24.28%;
		margin-bottom: 10px;
	}
	.large-14[class*="column"] + [class*="column"]:last-child {
		float: left;
	}
	
}

@media (max-width: 1024px) {

	.large-8.columns.del_form {
		width: 65%;
	}
	.del_form .large-4.columns {
		width: 35%;
	}
	.del_form .inline.left {
		width: 50%;
	}
	.del_form .large-8.columns {
		width: 65%;
	}
	.del_form .large-8.columns select {
		width: 60%;
	}
	.large-14 {
		width: 27.28%;
		margin-bottom: 10px;
	}
	.large-14[class*="column"] + [class*="column"]:last-child {
		float: left;
	}
}
@media (max-width: 980px) {
	.large-8.columns.del_form {
		width: 100%;
	}
}
@media only screen and (min-width: 767px) and (max-width: 980px){
	.large-14 {
		width: 32.28%;
		margin-bottom: 10px;
	}
	.large-14[class*="column"] + [class*="column"]:last-child {
		float: left;
	}
}
.msg{
		display: none;
	}
@media only screen  and (max-width: 766px){
	.msg{
		color: #000;
	    display: block;
	    position: relative;
	    text-align: center;
	    z-index: 9999;
	}
	.msg p{
		font-size: 30px;
	}
	/*.top-bar, .page-container {
		display: none;
	}*/
}

@media (min-width: 1366px) {/* big laptops and desktops */
	.page-404 .number {
		text-align: right;
	}
	.hidden-phone {
		display: none;
	}
	.page-sidebar {
		display: block;
	}
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.drawChart {
		height: 500px;
	}
	.cusCheckBox label {
		width: 100%;
		height: 65px;
		padding: 5px 10px 0 30px;
	}
}
@media only screen and (min-width: 40.063em) {
	
	@media only screen and (min-width: 64.063em) {
		
		/*.large-14 {
			width: 14.28%;
		}*/
	}

}
@media only screen and (min-width: 40.063em) and (max-width: 64em) {
	.top-bar button {
		font-size: 0.75 rem;
	}
	select.filterSelect, input.filterSelect {
		width: 90px !important;
	}
	.large-20{
		width:50%;
			
	}
}
@media only screen and (min-width: 64.063em) and (max-width: 90em) {
	input.filterSelect {
		width: 100px !important;
	}
}
@media only screen and (max-width: 600px) {
	.top-bar{
		padding-left:0;
	}
	.page-sidebar{
		top:0;
	}	
	.large-20{
		width:100%;
	}
	.custPopup{
		min-height:inherit; 
	}
}
.edit_btn {
				float: right;
			}
			.edit_btn:hover {
				background: #e9573f;
			}
/*Media Queries Ends*/
/***alert messages*/
.alert-box.success {
    background-color: #A0D468;
    border: 1px solid #79b337;
   }
.alert-box.error {
    background-color: #ED5565;
    border: 1px solid #c73a48;
   }
   .alert-box.warning {
    background-color: #FFCE54;
    border: 1px solid #F4B624;
   }
   #tobeserved .chosen-container {
   	width: 100% !important;
   }
   /*.alert-box.alert {
    background-color: #43ac6a;
   }*/
   
   /********** for product calendar -- shil ********/
   
    .event a {
    border: #26C281 1px solid !important;
    background-image :none !important;
    color: #006442 !important;
}
.event span {
    background-color: red !important;
    background-image :none !important;
    color: #ffffff !important;
}
 .eventPastNoBook span {
    background-color: yellow !important;
    background-image :none !important;
    color: #ffffff !important;
}

 .eventPast a {
    border: #C91F37 1px solid !important;
    background-image :none !important;
    color: #CF000F !important;
}
.eventPastDate a {
    background-color: black;
    background-image :none !important;
    color: #CF000F !important;
}
.eventGray a {
	border: #26C281 1px solid !important;
    background-color: #BDC3C7 !important;
    background-image :none !important;
    color: #006442 !important;
}
.eventPastGray a {
    background-color: #BDC3C7 !important;
    border: #C91F37 1px solid !important;
    background-image :none !important;
    color: #CF000F !important;
}
.prod_btn{
	background: #FC6E51;
    color: #fff;
    padding: 5px;
}
.prod_btn:hover{
	background:#e9573f;
}
.holiday {
    margin: 10px 5px;
    color:#3d3230;
}
.holiday i {
    color: #FFA6A6;
}
.avail_dates {
    margin: 10px 5px;
    color:#3d3230;
}
.avail_dates i {
    color: green;
}
.not-avail_dates {
    margin: 10px 5px;
    color:#3d3230;
}
.not-avail_dates .fa{
	background-color : #BDC3C7 !important;
	color: red;
    
}


/*sankalp*/
.no-padding{
    padding: 0 !important;
}

#display-inline-block label{
    display:inline-block;
    margin-right: 10px;
}	

.mCSB_dragger_bar {
	width: 10px !important;
}

/*
table tr th, table tr td, table.dataTable tbody th, table.dataTable tbody td i{
	
	border-bottom: 1px solid #f1f1f1;
    color: #656d78 !important;
    font-size: 16px;
    padding: 5px;
}
*/
/* pooja */
.menu-open .header-nav-parent.affix{
	width: calc(100% - 250px);
}
.notification-bell{
	margin: 6px 0 6px 15px;
}
.notification-bell .fa-bell{
	padding: 10px;
    border-radius: 50%;
    color: #656D78;
}
.notification-title{
	padding: 10px 8px 10px 15px;
    border-bottom: 1px solid #ddd;
    color: #646464;
    font-size: 18px;
    background: #fff3e6;
}
.sidenav {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 9;
    top: 0;
    right: 0;
    background-color: #fff;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 57px;
    -webkit-box-shadow: -4px 0px 5px 0px rgba(195,195,199,0.67);
	-moz-box-shadow: -4px 0px 5px 0px rgba(195,195,199,0.67);
	box-shadow: -4px 0px 5px 0px rgba(195,195,199,0.67);
}

.sidenav a {
    padding: 8px 8px 8px 15px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}

.sidenav a:hover, .offcanvas a:focus{
    color: #f1f1f1;
}

.sidenav .closebtn, .sidenav .closebtn:hover, .sidenav .closebtn:active{
    position: absolute;
    top: 53px;
    right: 0;
    text-align: right;
    color: #646464;
}
.noti-list{
	margin: 0;
	height: 620px;
    overflow-y: auto;
}
.noti-list li{
	padding: 10px 15px;
    border-bottom: 1px solid #f2f2f2;
    color: #646464;
    font-size: 16px;
}
.notification-hint{
	color: #b3b3b3;
	font-size: 14px;
}
.noti-list .fa-eye{
	 color: #33ccff;
}
.noti-list .fa-star{
	 color: #ff6699;
}
.noti-list .fa-comment{
	 color: #5cd65c;
}
@media screen and (max-height: 450px) {
  .sidenav {padding-top: 15px;}
  .sidenav a {font-size: 18px;}
}
.theme-img{
	border: 1px solid #ccc;
	margin-bottom: 15px;
}
.theme-checker{
	position: absolute;
    float: right;
    right: 0;
    padding: 5px;
    line-height: 0.5em;
    margin-top: 1px;
    margin-right: 10px;
}
.theme-qun{
	padding: 0px 14px;
    color: #545b68;
    margin-bottom: 10px;
}
.disinbl {
    display: inline-block;
}
.color-palette .control-color {
    height: 20px;
    width: 20px;
    display: block;
    position: relative;
    padding-left: 20px;
    margin-bottom: 0;
    cursor: pointer;
    font-size: 18px;
}
.color-palette input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}
.color-palette .control__indicator_input_color {
    position: absolute;
    top: 2px;
    left: 0;
    height: 20px;
    width: 20px;
}
.color-palette .control-color input:checked ~ .control__indicator_input_color:after {
    display: block;
}
.color-palette input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}

.color-palette .control-color .control__indicator_input_color:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    content: '';
    position: absolute;
    display: none;
}
.bluejeans-light_bg {
    background: #5D9CEC;
}
.aqua-light_bg {
    background: #4FC1E9;
}
.mint-light_bg {
    background: #48CFAD;
}
.grass-light_bg {
    background: #8CC152;
}
.sunflower-light_bg {
    background: #FFCE54;
}
.bittersweet-light_bg {
    background: #E9573F;
}
.grapefruit-light_bg {
    background: #DA4453;
}
.lavender-light_bg {
    background: #967ADC;
}
.pink-light_bg {
    background: #D770AD;
}
.darkgray-light_bg {
    background: #434A54;
}
.color-palette{
	margin-bottom: 20px;
}
.theme-button, .theme-button:hover, .theme-button:focus, .theme-button:active{
	color: #fff;
    background: #FC6E51;
    padding: 10px;
}
.theme-pre-button, .theme-pre-button:hover, .theme-pre-button:focus, .theme-pre-button:active{
	color: #fff;
    background: #959595;
    padding: 10px;
}
.ft-left{
	float: left !important;
}
.grid-bar, .grid-bar:hover, .grid-bar:active, .grid-bar:focus{
	margin: 6px 0 6px 15px;
    color: #656D78;
}
ul.grid-bar-ul li{
	text-align: center;
}
/* end pooja */
div.selector{
    padding-left: 0px;
}

.pl5 {
	padding-left: 5px !important;
}

/***** into js *****/

.service-slide{position:fixed;top:225px;right:-162px;z-index:100}
.color_white{color:#FFF}
.service_content ul li a{-webkit-transition:all 500ms ease;-moz-transition:all 500ms ease;-ms-transition:all 500ms ease;-o-transition:all 500ms ease;transition:all 500ms ease}
.service_content ul{padding:0;margin:0;background: #f7931e;}
.service_content ul li{list-style:none}
.service_content ul li a{color:#FFF;text-decoration:none;display:block;padding:5px 15px;width: 163px;}
.slide_click {cursor: pointer;}
.pull-left {float: left;}
/*.theme_back_color {background: #f7941e none repeat scroll 0 0;}*/
.clearfix::after {
    clear: both;
    content: " ";
    display: block;
    height: 0;
    visibility: hidden;
}
* {
    box-sizing: border-box;
}
.services{
	padding-bottom: 10px;
}

.slide-inner{
	text-align: center;
	padding: 0;
}
.help_guide .fa-info-circle{
	padding: 10px;
	background: rgb(247, 148, 30) none repeat scroll 0px 0px;
	border-right: 1px solid rgb(247, 148, 30);
}
.devider {
	height: 1px;
	background: #FFFFFF;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
	/* This works in IE 8 & 9 too */
	/* ... but also 5, 6, 7 */
	filter: alpha(opacity=40);
	/* Modern Browsers */
	opacity: 0.4;
}
.common-orange-btn-on-hover:hover {
	color: #FFFFFF;
}
.common-orange-btn-on-hover {
	background: #f7931e;
	color: #FFFFFF;
}
.sorting_1{
 	width:55px;
 }

/***** into js  *****/
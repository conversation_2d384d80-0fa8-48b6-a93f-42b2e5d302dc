.lcs_wrap {
	display: inline-block;	
	direction: ltr;
	height: 24px;
    vertical-align: middle;
}
.lcs_wrap input {
	display: none;	
}

.lcs_switch {
	display: inline-block;	
	position: relative;
	width: 93px;
	height: 24px;
	border-radius: 0;
	background: #ddd;
	overflow: hidden;
	cursor: pointer;
	
	-webkit-transition: all .2s ease-in-out;  
	-ms-transition: 	all .2s ease-in-out; 
	transition: 		all .2s ease-in-out; 
}
.lcs_cursor {
	display: inline-block;
	position: absolute;
	top: 3px;	
	width: 18px;
	height: 18px;
	border-radius: 0;
	background: #fff;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 4px 0 rgba(0, 0, 0, 0.1);
	z-index: 10;
	
	-webkit-transition: all .2s linear;  
	-ms-transition: 	all .2s linear; 
	transition: 		all .2s linear; 
}
.lcs_label {
	font-family: "Trebuchet MS", Helvetica, sans-serif;
    font-size: 12px;
	letter-spacing: 1px;
	line-height: 18px;
	color: #fff;
	font-weight: bold;
	position: absolute;
	width: 58px;
	top: 3px;
	overflow: hidden;
	text-align: center;
	opacity: 0;
	
	-webkit-transition: all .2s ease-in-out .1s;  
	-ms-transition: 	all .2s ease-in-out .1s;   
	transition: 		all .2s ease-in-out .1s;   
}
.lcs_label.lcs_label_on {
	left: -70px;
	z-index: 6;	
}
.lcs_label.lcs_label_off {
	right: -70px;
	z-index: 5;	
}


/* on */
.lcs_switch.lcs_on {
	background:  #10a062;
    box-shadow: 0 0 2px #579022 inset;
}
.lcs_switch.lcs_on .lcs_cursor {
	left: 68px;
}
.lcs_switch.lcs_on .lcs_label_on {
	left: 10px;	
	opacity: 1;
}


/* off */
.lcs_switch.lcs_off {
	background: #DA4453;
	box-shadow: 0px 0px 2px #DA4453 inset; 	
}
.lcs_switch.lcs_off .lcs_cursor {
	left: 3px;
}
.lcs_switch.lcs_off .lcs_label_off {
	right: 10px;
	opacity: 1;	
}


/* disabled */
.lcs_switch.lcs_disabled {
	opacity: 0.65;
	filter: alpha(opacity=65);	
	cursor: default;
}
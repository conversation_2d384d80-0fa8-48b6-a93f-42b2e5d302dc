<div class="photoEdit">
	<form id="editphoto" action="/ajax/edit_photo.html" method="post" class="stdform quickform">
    	<h2>Edit Image Details</h2>
        <br />
        <div class="notifyMessage">Updated</div>
        <p>
            <label>Original URL</label>
            <input type="text" name="" value="http://themepixels.com/themes/demo/webpage/starlight/images/assets/thumb/medium/thumb1.png" />
        </p>
        <p>
            <label>Title</label>
            <input type="text" name="" value="" />
        </p>
        <p>
            <label>Size</label>
            <input type="text" name="" value="640" class="smallinput" /> &nbsp;x&nbsp; <input type="text" name="" value="640" class="smallinput" /> &nbsp;pixels
        </p>
        <p>
            <label>Alternative Text</label>
            <input type="text" name="" value="Filename" />
        </p>
        <p>
            <label>Border</label>
            <input type="text" name="" value="0" class="smallinput" />
        </p>
        <br />
        <p class="action">
        	<button class="submit radius2">Update Changes</button> &nbsp;
            <button class="cancel radius2">Cancel</button>
        </p>
        <br />
    </form>
</div><!--photoEdit-->
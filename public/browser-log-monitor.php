<?php
/**
 * Browser Log Monitor
 *
 * This script provides a way to monitor browser logs for client-side issues
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Define log directory
$logDir = realpath(dirname(__FILE__) . '/../data/logs');

// Create log directory if it doesn't exist
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Define browser log file
$browserLogFile = $logDir . '/browser.log';

// Create browser log file if it doesn't exist
if (!file_exists($browserLogFile)) {
    file_put_contents($browserLogFile, '');
}

// Handle log submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'log') {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'session_id' => session_id(),
        'user_id' => isset($_SESSION['user']['pk_user_code']) ? $_SESSION['user']['pk_user_code'] : 'Not logged in',
        'level' => isset($_POST['level']) ? $_POST['level'] : 'info',
        'message' => isset($_POST['message']) ? $_POST['message'] : '',
        'url' => isset($_POST['url']) ? $_POST['url'] : '',
        'line' => isset($_POST['line']) ? $_POST['line'] : '',
        'column' => isset($_POST['column']) ? $_POST['column'] : '',
        'error' => isset($_POST['error']) ? $_POST['error'] : '',
        'stack' => isset($_POST['stack']) ? $_POST['stack'] : ''
    ];

    // Write to log file
    file_put_contents($browserLogFile, json_encode($logData) . "\n", FILE_APPEND);

    // Return success response
    header('Content-Type: application/json');
    echo json_encode(['success' => true]);
    exit;
}

// Handle clear log action
if (isset($_GET['action']) && $_GET['action'] === 'clear') {
    // Clear the log file
    file_put_contents($browserLogFile, '');

    // Redirect to remove the action from the URL
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Get refresh interval
$refreshInterval = isset($_GET['refresh']) ? intval($_GET['refresh']) : 5;
if ($refreshInterval < 1) {
    $refreshInterval = 5;
} elseif ($refreshInterval > 60) {
    $refreshInterval = 60;
}

// Get number of lines to display
$numLines = isset($_GET['lines']) ? intval($_GET['lines']) : 20;
if ($numLines < 1) {
    $numLines = 20;
} elseif ($numLines > 1000) {
    $numLines = 1000;
}

// Get log entries
$logEntries = [];
if (file_exists($browserLogFile)) {
    // Read the last N lines from the log file
    $lines = [];
    $fp = fopen($browserLogFile, 'r');
    if ($fp) {
        // Get file size
        fseek($fp, 0, SEEK_END);
        $fileSize = ftell($fp);

        // Start from the end of the file
        $pos = $fileSize - 1;
        $lineCount = 0;

        // Read backwards until we have enough lines or reach the beginning of the file
        while ($pos >= 0 && $lineCount < $numLines) {
            fseek($fp, $pos);
            $char = fgetc($fp);

            // If we're at the beginning of a line, read the line
            if ($pos == 0 || $char == "\n") {
                $line = fgets($fp);
                if ($pos != 0) {
                    $lines[] = $line;
                    $lineCount++;
                }
            }

            $pos--;
        }

        fclose($fp);

        // Reverse the lines to get them in chronological order
        $lines = array_reverse($lines);
    }

    // Parse log entries
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) {
            continue;
        }

        // Try to parse as JSON
        $entry = json_decode($line, true);
        if ($entry !== null) {
            $logEntries[] = $entry;
        }
    }
}

// Get error counts by type
$errorCounts = [
    'error' => 0,
    'warning' => 0,
    'info' => 0,
    'debug' => 0
];

foreach ($logEntries as $entry) {
    if (isset($entry['level']) && isset($errorCounts[$entry['level']])) {
        $errorCounts[$entry['level']]++;
    }
}

// Get the most common error message
$errorMessages = [];
foreach ($logEntries as $entry) {
    if (isset($entry['message'])) {
        $message = $entry['message'];
        if (!isset($errorMessages[$message])) {
            $errorMessages[$message] = 0;
        }
        $errorMessages[$message]++;
    }
}
arsort($errorMessages);
$mostCommonError = !empty($errorMessages) ? key($errorMessages) : 'N/A';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Browser Log Monitor</title>
    <meta http-equiv="refresh" content="<?php echo $refreshInterval; ?>">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary-item {
            text-align: center;
        }
        .summary-item h3 {
            margin-top: 0;
            color: #333;
        }
        .summary-item p {
            margin: 5px 0;
            font-size: 24px;
            font-weight: bold;
        }
        .controls {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .controls select, .controls input {
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .controls button {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #45a049;
        }
        .controls button.clear {
            background-color: #f44336;
        }
        .controls button.clear:hover {
            background-color: #d32f2f;
        }
        .controls button.test {
            background-color: #2196F3;
        }
        .controls button.test:hover {
            background-color: #0b7dda;
        }
        .log-entries {
            margin-bottom: 20px;
        }
        .log-entry {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .log-entry.error {
            border-left: 5px solid #f44336;
        }
        .log-entry.warning {
            border-left: 5px solid #ff9800;
        }
        .log-entry.info {
            border-left: 5px solid #2196F3;
        }
        .log-entry.debug {
            border-left: 5px solid #4CAF50;
        }
        .log-entry pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .back {
            text-align: center;
            margin-top: 20px;
        }
        .back a {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .back a:hover {
            background-color: #45a049;
        }
        .logger-code {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .logger-code h2 {
            margin-top: 0;
            color: #333;
        }
        .logger-code pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Browser Log Monitor</h1>

        <div class="summary">
            <div class="summary-item">
                <h3>Errors</h3>
                <p><?php echo $errorCounts['error']; ?></p>
            </div>
            <div class="summary-item">
                <h3>Warnings</h3>
                <p><?php echo $errorCounts['warning']; ?></p>
            </div>
            <div class="summary-item">
                <h3>Info</h3>
                <p><?php echo $errorCounts['info']; ?></p>
            </div>
            <div class="summary-item">
                <h3>Debug</h3>
                <p><?php echo $errorCounts['debug']; ?></p>
            </div>
        </div>

        <div class="controls">
            <div>
                <form method="get">
                    <label for="lines">Lines to display:</label>
                    <select name="lines" id="lines">
                        <option value="10" <?php echo $numLines === 10 ? 'selected' : ''; ?>>10</option>
                        <option value="20" <?php echo $numLines === 20 ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo $numLines === 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $numLines === 100 ? 'selected' : ''; ?>>100</option>
                    </select>
                    <label for="refresh">Refresh interval (seconds):</label>
                    <select name="refresh" id="refresh">
                        <option value="5" <?php echo $refreshInterval === 5 ? 'selected' : ''; ?>>5</option>
                        <option value="10" <?php echo $refreshInterval === 10 ? 'selected' : ''; ?>>10</option>
                        <option value="30" <?php echo $refreshInterval === 30 ? 'selected' : ''; ?>>30</option>
                        <option value="60" <?php echo $refreshInterval === 60 ? 'selected' : ''; ?>>60</option>
                    </select>
                    <button type="submit">Apply</button>
                </form>
            </div>
            <div>
                <a href="?action=clear" onclick="return confirm('Are you sure you want to clear the browser log?');"><button class="clear">Clear Log</button></a>
                <button class="test" onclick="testLogger()">Test Logger</button>
            </div>
        </div>

        <div class="log-entries">
            <?php if (empty($logEntries)): ?>
                <p>No log entries found.</p>
            <?php else: ?>
                <?php foreach ($logEntries as $entry): ?>
                    <div class="log-entry <?php echo isset($entry['level']) ? $entry['level'] : ''; ?>">
                        <pre><?php echo json_encode($entry, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></pre>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <div class="logger-code">
            <h2>Browser Logger Code</h2>
            <p>Add this code to your pages to enable browser logging:</p>
            <pre>
&lt;script&gt;
// Browser Logger
(function() {
    // Create logger object
    window.BrowserLogger = {
        // Log levels
        LEVELS: {
            DEBUG: 'debug',
            INFO: 'info',
            WARNING: 'warning',
            ERROR: 'error'
        },

        // Log a message
        log: function(level, message, error) {
            var data = {
                action: 'log',
                level: level,
                message: message,
                url: window.location.href,
                timestamp: new Date().toISOString()
            };

            // Add error details if available
            if (error) {
                data.error = error.message || error.toString();
                data.stack = error.stack || '';

                // Add line and column if available
                if (error.lineNumber) data.line = error.lineNumber;
                if (error.columnNumber) data.column = error.columnNumber;
            }

            // Send log to server
            this.sendLog(data);
        },

        // Shorthand methods
        debug: function(message, error) {
            this.log(this.LEVELS.DEBUG, message, error);
        },

        info: function(message, error) {
            this.log(this.LEVELS.INFO, message, error);
        },

        warning: function(message, error) {
            this.log(this.LEVELS.WARNING, message, error);
        },

        error: function(message, error) {
            this.log(this.LEVELS.ERROR, message, error);
        },

        // Send log to server
        sendLog: function(data) {
            // Create form data
            var formData = new FormData();
            for (var key in data) {
                formData.append(key, data[key]);
            }

            // Send log to server
            fetch('/browser-log-monitor.php', {
                method: 'POST',
                body: formData
            }).catch(function(error) {
                console.error('Failed to send log to server:', error);
            });
        }
    };

    // Override console methods
    var originalConsole = {
        log: console.log,
        info: console.info,
        warn: console.warn,
        error: console.error
    };

    console.log = function() {
        BrowserLogger.debug(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.log.apply(console, arguments);
    };

    console.info = function() {
        BrowserLogger.info(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.info.apply(console, arguments);
    };

    console.warn = function() {
        BrowserLogger.warning(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.warn.apply(console, arguments);
    };

    console.error = function() {
        BrowserLogger.error(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.error.apply(console, arguments);
    };

    // Catch unhandled errors
    window.addEventListener('error', function(event) {
        BrowserLogger.error(event.message, {
            message: event.message,
            filename: event.filename,
            lineNumber: event.lineno,
            columnNumber: event.colno,
            stack: event.error ? event.error.stack : ''
        });
    });

    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
        BrowserLogger.error('Unhandled Promise Rejection', event.reason);
    });
})();
&lt;/script&gt;
            </pre>
        </div>

        <div class="back">
            <a href="/dashboard">Back to Dashboard</a>
        </div>
    </div>

    <script>
        // Test logger function
        function testLogger() {
            // Create form data
            var formData = new FormData();
            formData.append('action', 'log');
            formData.append('level', 'info');
            formData.append('message', 'Test log entry from browser');
            formData.append('url', window.location.href);
            formData.append('timestamp', new Date().toISOString());

            // Send log to server
            fetch('/browser-log-monitor.php', {
                method: 'POST',
                body: formData
            }).then(function(response) {
                return response.json();
            }).then(function(data) {
                if (data.success) {
                    alert('Test log entry created successfully. Refresh the page to see it.');
                } else {
                    alert('Failed to create test log entry.');
                }
            }).catch(function(error) {
                alert('Failed to create test log entry: ' + error.message);
            });
        }
    </script>
</body>
</html>
<?php
// Flush the output buffer
ob_end_flush();
?>

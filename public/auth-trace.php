<?php
/**
 * Authentication Trace Script
 * This script helps trace the authentication flow
 */

// Start session
session_start();

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create a custom error handler to log errors
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    echo "<div style='color:red; background-color:#ffeeee; padding:10px; margin:10px 0; border:1px solid #ffaaaa;'>";
    echo "<strong>Error:</strong> [$errno] $errstr<br>";
    echo "File: $errfile, Line: $errline<br>";
    echo "</div>";

    // Log to file
    error_log("Error: [$errno] $errstr in $errfile on line $errline");

    // Don't execute PHP's internal error handler
    return true;
}

// Set the custom error handler
set_error_handler("customErrorHandler");

// Create a custom exception handler
function customExceptionHandler($exception) {
    echo "<div style='color:red; background-color:#ffeeee; padding:10px; margin:10px 0; border:1px solid #ffaaaa;'>";
    echo "<strong>Exception:</strong> " . $exception->getMessage() . "<br>";
    echo "File: " . $exception->getFile() . ", Line: " . $exception->getLine() . "<br>";
    echo "<pre>" . $exception->getTraceAsString() . "</pre>";
    echo "</div>";

    // Log to file
    error_log("Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
}

// Set the custom exception handler
set_exception_handler("customExceptionHandler");

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';

    echo "<h1>Authentication Trace</h1>";

    // Check development mode
    echo "<h2>Development Mode</h2>";
    echo "<p>Development mode: " . (isset($config['development_mode']) && $config['development_mode'] ? 'Enabled' : 'Disabled') . "</p>";

    // Initialize Zend Application
    echo "<h2>Initializing Application</h2>";
    $application = Zend\Mvc\Application::init($config);
    echo "<p>Application initialized successfully</p>";

    // Get service manager
    $serviceManager = $application->getServiceManager();
    echo "<p>Service manager retrieved successfully</p>";

    // Check if ConfigService is registered
    echo "<h2>ConfigService</h2>";
    if ($serviceManager->has('ConfigService')) {
        echo "<p>ConfigService is registered</p>";
        $configService = $serviceManager->get('ConfigService');
        echo "<p>ConfigService class: " . get_class($configService) . "</p>";

        // Get auth_mode
        $authMode = $configService->getConfig('auth_mode', 'legacy');
        echo "<p>Auth mode: " . $authMode . "</p>";
    } else {
        echo "<p>ConfigService is not registered</p>";

        // Check if config has auth_mode
        $appConfig = $serviceManager->get('config');
        if (isset($appConfig['settings']) && isset($appConfig['settings']['GLOBAL_AUTH_METHOD'])) {
            echo "<p>Auth mode from config: " . $appConfig['settings']['GLOBAL_AUTH_METHOD'] . "</p>";
        } else {
            echo "<p>Auth mode not found in config</p>";
        }
    }

    // Check if AuthService is registered
    echo "<h2>AuthService</h2>";
    if ($serviceManager->has('AuthService')) {
        echo "<p>AuthService is registered</p>";
        $authService = $serviceManager->get('AuthService');
        echo "<p>AuthService class: " . get_class($authService) . "</p>";

        // Get adapter
        $adapter = $authService->getAdapter();
        echo "<p>Adapter class: " . get_class($adapter) . "</p>";

        // Check if user is authenticated
        if ($authService->hasIdentity()) {
            echo "<p>User is authenticated</p>";
            $identity = $authService->getIdentity();
            echo "<h3>User Identity</h3>";
            echo "<pre>" . print_r($identity, true) . "</pre>";
        } else {
            echo "<p>User is not authenticated</p>";
        }
    } else {
        echo "<p>AuthService is not registered</p>";
    }

    // Check if KeycloakClient is registered
    echo "<h2>KeycloakClient</h2>";
    if ($serviceManager->has('SanAuth\Service\KeycloakClient')) {
        echo "<p>KeycloakClient is registered</p>";
        $keycloakClient = $serviceManager->get('SanAuth\Service\KeycloakClient');
        echo "<p>KeycloakClient class: " . get_class($keycloakClient) . "</p>";

        // Get Keycloak config
        $appConfig = $serviceManager->get('config');
        if (isset($appConfig['keycloak'])) {
            echo "<h3>Keycloak Configuration</h3>";
            echo "<pre>" . print_r($appConfig['keycloak'], true) . "</pre>";
        } else {
            echo "<p>Keycloak configuration not found</p>";
        }
    } else {
        echo "<p>KeycloakClient is not registered</p>";
    }

    // Check if OnessoUserService is registered
    echo "<h2>OnessoUserService</h2>";
    if ($serviceManager->has('SanAuth\Service\OnessoUserService')) {
        echo "<p>OnessoUserService is registered</p>";
        $onessoUserService = $serviceManager->get('SanAuth\Service\OnessoUserService');
        echo "<p>OnessoUserService class: " . get_class($onessoUserService) . "</p>";
    } else {
        echo "<p>OnessoUserService is not registered</p>";
    }

    // Check database adapter
    echo "<h2>Database Adapter</h2>";
    if ($serviceManager->has('Zend\Db\Adapter\Adapter')) {
        echo "<p>Database adapter is registered</p>";
        $dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');
        echo "<p>Adapter class: " . get_class($dbAdapter) . "</p>";

        // Test database connection
        try {
            $sql = "SELECT 1";
            $statement = $dbAdapter->query($sql);
            $result = $statement->execute();
            echo "<p>Database connection test: Successful</p>";

            // Check if users table exists
            try {
                $sql = "SELECT * FROM users LIMIT 1";
                $statement = $dbAdapter->query($sql);
                $result = $statement->execute();
                echo "<p>Users table exists</p>";

                // Check if onesso_users table exists
                try {
                    $sql = "SELECT * FROM onesso_users LIMIT 1";
                    $statement = $dbAdapter->query($sql);
                    $result = $statement->execute();
                    echo "<p>onesso_users table exists</p>";
                } catch (\Exception $e) {
                    echo "<p>onesso_users table does not exist: " . $e->getMessage() . "</p>";
                }
            } catch (\Exception $e) {
                echo "<p>Users table does not exist: " . $e->getMessage() . "</p>";
            }
        } catch (\Exception $e) {
            echo "<p>Database connection test: Failed</p>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>Database adapter is not registered</p>";
    }

    // Check session
    echo "<h2>Session</h2>";
    echo "<p>Session ID: " . session_id() . "</p>";
    echo "<p>Session status: " . session_status() . "</p>";

    // Check if session is started
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "<p>Session is active</p>";
        echo "<h3>Session Data</h3>";
        echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    } else {
        echo "<p>Session is not active</p>";
    }

    // Check routes
    echo "<h2>Routes</h2>";
    $router = $serviceManager->get('router');
    $routes = $router->getRoutes();

    echo "<h3>Available Routes</h3>";
    echo "<ul>";
    foreach ($routes as $name => $route) {
        echo "<li>" . $name . "</li>";
    }
    echo "</ul>";

    // Test authentication with hardcoded credentials
    echo "<h2>Testing Authentication</h2>";

    // Create a test form
    echo "<form method='post'>";
    echo "<div>";
    echo "<label for='username'>Username:</label>";
    echo "<input type='text' name='username' id='username' value='<EMAIL>'>";
    echo "</div>";
    echo "<div>";
    echo "<label for='password'>Password:</label>";
    echo "<input type='password' name='password' id='password' value='password'>";
    echo "</div>";
    echo "<div>";
    echo "<label for='auth_mode'>Auth Mode:</label>";
    echo "<select name='auth_mode' id='auth_mode'>";
    echo "<option value='legacy'>Legacy</option>";
    echo "<option value='keycloak'>Keycloak</option>";
    echo "<option value='both'>Both</option>";
    echo "</select>";
    echo "</div>";
    echo "<div>";
    echo "<button type='submit'>Test Authentication</button>";
    echo "</div>";
    echo "</form>";

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $authMode = $_POST['auth_mode'] ?? 'legacy';

        echo "<h3>Authentication Test Results</h3>";
        echo "<p>Username: " . htmlspecialchars($username) . "</p>";
        echo "<p>Password: " . str_repeat('*', strlen($password)) . "</p>";
        echo "<p>Auth Mode: " . htmlspecialchars($authMode) . "</p>";

        // Set auth mode in config
        if ($serviceManager->has('ConfigService')) {
            $configService = $serviceManager->get('ConfigService');
            $appConfig = $serviceManager->get('config');
            $appConfig['settings']['GLOBAL_AUTH_METHOD'] = $authMode;
            $serviceManager->setService('config', $appConfig);
        }

        // Test legacy authentication
        if ($authMode === 'legacy' || $authMode === 'both') {
            echo "<h4>Legacy Authentication</h4>";

            try {
                $authService = $serviceManager->get('AuthService');
                $authService->getAdapter()
                    ->setIdentity($username)
                    ->setCredential($password);

                $result = $authService->authenticate();

                if ($result->isValid()) {
                    echo "<p>Authentication successful!</p>";

                    // Get user details
                    $returnData = array("pk_user_code", "first_name", "last_name", "phone", "gender", "email_id", "city", "role_id", "status", "third_party_id");
                    $userDetails = $authService->getAdapter()->getResultRowObject($returnData);

                    echo "<h5>User Details</h5>";
                    echo "<pre>" . print_r($userDetails, true) . "</pre>";

                    // Store user details in session
                    $userDetails->auth_type = 'legacy';
                    $authService->getStorage()->write($userDetails);

                    echo "<p>User details stored in session</p>";
                    echo "<p>You can now access the application</p>";
                    echo "<p><a href='/dashboard'>Go to Dashboard</a></p>";
                } else {
                    echo "<p>Authentication failed</p>";
                    echo "<h5>Authentication Messages</h5>";
                    echo "<pre>" . print_r($result->getMessages(), true) . "</pre>";
                }
            } catch (\Exception $e) {
                echo "<p>Error: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
        }
    }

} catch (\Exception $e) {
    echo "<h1>Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

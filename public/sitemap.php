<?php

$pageURL = 'http';

		if (isset( $_SERVER["HTTPS"] ) && strtolower( $_SERVER["HTTPS"] ) == "on" ) {
			$pageURL .= "s";
		}
		
$pageURL .= "://";
$url = $pageURL.$_SERVER['SERVER_NAME'];


header('Content-type: text/xml'); 

  echo '<?xml version="1.0" encoding="UTF-8"?>'.PHP_EOL; 

  echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
      http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">'.PHP_EOL;

  echo '<url>';
  echo '<loc>'.$url.'</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';

  echo '<url>';
  echo '<loc>'.$url.'/home</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/about-us</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/our-services</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/how-it-works</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/what-client-say</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/app-page</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/terms-and-conditions</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/privacy-policy</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/faq</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/menu</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';
  
  echo '<url>';
  echo '<loc>'.$url.'/cart/thank-you</loc>';
  echo '<changefreq>daily</changefreq>';
  echo '<priority>0.8</priority>';
  echo '</url>';

  echo '</urlset>'.PHP_EOL;


 ?>
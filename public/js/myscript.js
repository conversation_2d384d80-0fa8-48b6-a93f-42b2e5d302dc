//Table width scroll



var tableHeight = {	
	"autoHeight": function() {	

	
	var height = $(window).height() - 400;  	
	$('#tbl').dataTable( {
		stateSave: true,
        "scrollY": height,		
        "scrollX": true,		
		"scrollCollapse": true	,
		 "bSortCellsTop": true	
    });		
	
},
	"noHeight": function() {	
	$('#tbl').dataTable({
		stateSave: true,
		"scrollCollapse": true,
		 "bSortCellsTop": true	
	});		
	
},
	"default": function() {	
	$('#tbl').dataTable( {
		stateSave: true,
        "scrollY": 324,
        "scrollX": true,
		"scrollCollapse": true	,
		 "bSortCellsTop": true		
	});
	
},
	"scrollDiv": function() {	
		
		$(".dataTables_scrollBody").niceScroll({touchbehavior:false,cursorcolor:"#555",cursoropacitymax:0.8,cursorwidth:9,cursorborder:"1px solid #333",cursorborderradius:"8px",background:"#ccc",autohidemode:"false"}).cursor.css({"background":"#555"}).resize();
			
		$(".page-container").mouseover(function(){
			  $("#portlet_body").niceScroll({touchbehavior:false,cursorcolor:"#555",cursoropacitymax:0.8,cursorwidth:9,cursorborder:"1px solid #333",cursorborderradius:"8px",background:"#ccc",autohidemode:"false"}).cursor.css({"background":"#555"}).resize();
		});		
		
}
};		
		
	
	
var myPageTable = (function() {

    var that = {};

    that.init = function () {      
	
	
 
	   
	   var width = $(window).width(); 
		//var height = $(window).height(); 
		//var rowCount = $('table#customer tr:last').index() + 1;
		var numCols = $('table#tbl').find('tr')[0].cells.length;
		
		if ((width >= 1366) && (numCols >= 13)) {
		  tableHeight[ "default" ]();
		  tableHeight[ "scrollDiv" ](); 
		}
		
		else if ((width >= 1366) && (numCols <= 12)) {
		  tableHeight[ "noHeight" ]();
		}
		
		else if ((width >= 1024  ) && (numCols >= 3)) {
		  tableHeight[ "default" ]();
		  tableHeight[ "scrollDiv" ](); 
		}
		
		else if ((width <= 1024  ) && (numCols <= 3)) {
		  tableHeight[ "noHeight" ]();
		}
		
		else {
		 tableHeight[ "default" ]();
		 tableHeight[ "scrollDiv" ](); 
		}   
		
	
    }
    return that;

})();	

	
$("button").on("click", function() {
  var el = $(this);
  if (el.html() == el.data("text-swap")) {
    el.html(el.data("text-original"));
  } else {
    el.data("text-original", el.html());
    el.html(el.data("text-swap"));
  }
  setTimeout(function () {
        el.html(el.data("text-original"));
   }, 500);
});	
	
	
//Table width scroll End


// pooja

$(document).ready(function(){
	$('.dropdown-toggle').dropdown()
	$("#twn-btn1").click(function(){
		$("#tower1").show();
        $("#tower2, #tower3, #tower4").hide();
    });
    $("#twn-btn2").click(function(){
    	$("#tower2").show();
        $("#tower1, #tower3, #tower4").hide();
    });
    $("#twn-btn3").click(function(){
    	$("#tower3").show();
        $("#tower1, #tower2, #tower4").hide();
    });
    $("#twn-btn4").click(function(){
    	$("#tower4").show();
        $("#tower1, #tower3, #tower2").hide();
    });
    
    $('input[type="checkbox"]').click(function() {
		if ($(this).attr("value") == "online-payment") {
			$(".payment-set").toggle();
			$(".change-form-hg").toggleClass("form-2");
		}
	});
	
	$('input[type="radio"]').click(function() {
		if ($(this).attr("value") == "Payu") {
			$(".payment-set2").toggle();
			$(".change-form-hg").toggleClass("form-4");
		}
	});
	
	$(".flatfee").click(function(){
    	$("flat-fee-div").show();
        $("flat-squt-div").hide();
    });
    $(".sqftarea").click(function(){
    	$("flat-fee-div").hide();
        $("flat-squt-div").show();
    });
    
    $("#flip1").change(function() {
		$("#Percentage-drop").show();
		$(".of").show();
	});
	$("#flip2").change(function() {
		$("#Percentage-drop2").show();
		$(".of2").show();
	});
	$("#flip3").change(function() {
		$("#Percentage-drop3").show();
		$(".of3").show();
	});

    $('[data-toggle="popover"]').popover();  

    $('#add-location').click(function() {
		$('#myModal').modal('show');
	});

    $('input[type="checkbox"]').click(function() {
		if ($(this).attr("value") == "lunch") {
			$(".lunch-field").toggle();
		}
		if ($(this).attr("value") == "dinner") {
			$(".dinner-field").toggle();
		}
		if ($(this).attr("value") == "snacks") {
			$(".snacks-field").toggle();
		}
	}); 

	$('input[type="checkbox"]').click(function() {
		if ($(this).attr("value") == "online") {
			$(".online-field").toggle();
		}
	});

	$('input[type="checkbox"]').click(function() {
		if ($(this).attr("value") == "payu") {
			$(".payu-field").toggle();
		}
		if ($(this).attr("value") == "instamojo") {
			$(".instamojo-field").toggle();
		}
		if ($(this).attr("value") == "paytm") {
			$(".paytm-field").toggle();
		}
	}); 
});







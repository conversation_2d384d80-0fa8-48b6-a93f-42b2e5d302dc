/**
 * <PERSON><PERSON><PERSON> Logger
 * 
 * This script provides client-side logging capabilities that send logs to the server
 */

(function() {
    // Create logger object
    window.BrowserLogger = {
        // Log levels
        LEVELS: {
            DEBUG: 'debug',
            INFO: 'info',
            WARNING: 'warning',
            ERROR: 'error'
        },
        
        // Log a message
        log: function(level, message, error) {
            var data = {
                action: 'log',
                level: level,
                message: message,
                url: window.location.href,
                timestamp: new Date().toISOString()
            };
            
            // Add error details if available
            if (error) {
                data.error = error.message || error.toString();
                data.stack = error.stack || '';
                
                // Add line and column if available
                if (error.lineNumber) data.line = error.lineNumber;
                if (error.columnNumber) data.column = error.columnNumber;
            }
            
            // Send log to server
            this.sendLog(data);
        },
        
        // Shorthand methods
        debug: function(message, error) {
            this.log(this.LEVELS.DEBUG, message, error);
        },
        
        info: function(message, error) {
            this.log(this.LEVELS.INFO, message, error);
        },
        
        warning: function(message, error) {
            this.log(this.LEVELS.WARNING, message, error);
        },
        
        error: function(message, error) {
            this.log(this.LEVELS.ERROR, message, error);
        },
        
        // Send log to server
        sendLog: function(data) {
            // Create form data
            var formData = new FormData();
            for (var key in data) {
                formData.append(key, data[key]);
            }
            
            // Send log to server
            fetch('/browser-log-monitor.php', {
                method: 'POST',
                body: formData
            }).catch(function(error) {
                console.error('Failed to send log to server:', error);
            });
        }
    };
    
    // Override console methods
    var originalConsole = {
        log: console.log,
        info: console.info,
        warn: console.warn,
        error: console.error
    };
    
    console.log = function() {
        BrowserLogger.debug(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.log.apply(console, arguments);
    };
    
    console.info = function() {
        BrowserLogger.info(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.info.apply(console, arguments);
    };
    
    console.warn = function() {
        BrowserLogger.warning(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.warn.apply(console, arguments);
    };
    
    console.error = function() {
        BrowserLogger.error(Array.prototype.slice.call(arguments).join(' '));
        originalConsole.error.apply(console, arguments);
    };
    
    // Catch unhandled errors
    window.addEventListener('error', function(event) {
        BrowserLogger.error(event.message, {
            message: event.message,
            filename: event.filename,
            lineNumber: event.lineno,
            columnNumber: event.colno,
            stack: event.error ? event.error.stack : ''
        });
    });
    
    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
        BrowserLogger.error('Unhandled Promise Rejection', event.reason);
    });
    
    // Log page load
    BrowserLogger.info('Page loaded: ' + window.location.href);
    
    // Log page unload
    window.addEventListener('beforeunload', function() {
        BrowserLogger.info('Page unloaded: ' + window.location.href);
    });
    
    // Log AJAX errors
    var originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function() {
        this.addEventListener('error', function() {
            BrowserLogger.error('XHR Error: ' + this.responseURL);
        });
        this.addEventListener('timeout', function() {
            BrowserLogger.error('XHR Timeout: ' + this.responseURL);
        });
        this.addEventListener('abort', function() {
            BrowserLogger.warning('XHR Aborted: ' + this.responseURL);
        });
        originalXHROpen.apply(this, arguments);
    };
    
    // Log fetch errors
    var originalFetch = window.fetch;
    window.fetch = function() {
        return originalFetch.apply(this, arguments)
            .catch(function(error) {
                BrowserLogger.error('Fetch Error: ' + arguments[0], error);
                throw error;
            });
    };
})();

{"name": "jquery-form", "title": "jQuery Form Plugin", "description": "A simple way to AJAX-ify any form on your page; with file upload and progress support.", "keywords": ["form", "upload", "ajax"], "version": "3.48.0", "author": {"name": "<PERSON><PERSON>", "url": "http://jquery.malsup.com"}, "licenses": [{"type": "MIT", "url": "http://malsup.github.com/mit-license.txt"}, {"type": "GPL", "url": "http://malsup.github.com/gpl-license-v2.txt"}], "bugs": "https://github.com/malsup/form/issues", "homepage": "http://jquery.malsup.com/form/", "docs": "http://jquery.malsup.com/form/", "download": "http://malsup.github.com/jquery.form.js", "dependencies": {"jquery": ">=1.5"}, "jam": {"main": "jquery.form.js", "dependencies": {"jquery": ">=1.5.0"}}, "devDependencies": {"grunt-contrib-jshint": "~0.8.0", "grunt-contrib-uglify": "~0.2.7", "grunt-contrib-watch": "~0.5.3"}}
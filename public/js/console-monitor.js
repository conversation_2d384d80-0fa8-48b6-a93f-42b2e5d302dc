/**
 * Console Monitor Script
 * This script captures browser console logs and sends them to the server
 */

(function() {
    // Store original console methods
    const originalConsole = {
        log: console.log,
        info: console.info,
        warn: console.warn,
        error: console.error,
        debug: console.debug
    };
    
    // Log storage
    const logStorage = {
        logs: [],
        maxLogs: 100,
        
        add: function(type, message, timestamp) {
            // Add log to storage
            this.logs.push({
                type: type,
                message: message,
                timestamp: timestamp
            });
            
            // Keep only the last maxLogs entries
            if (this.logs.length > this.maxLogs) {
                this.logs.shift();
            }
            
            // Try to send logs to server
            this.sendLogsToServer();
        },
        
        sendLogsToServer: function() {
            // Only send if we have logs and the endpoint exists
            if (this.logs.length === 0) return;
            
            // Create a copy of logs to send
            const logsToSend = [...this.logs];
            
            // Send logs to server
            fetch('/log-collector.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    logs: logsToSend,
                    userAgent: navigator.userAgent,
                    url: window.location.href
                })
            })
            .then(response => {
                if (response.ok) {
                    // Clear sent logs
                    this.logs = [];
                }
            })
            .catch(error => {
                // Log error using original console
                originalConsole.error('Failed to send logs to server:', error);
            });
        }
    };
    
    // Function to format log arguments
    function formatLogArguments(args) {
        return Array.from(args).map(arg => {
            if (typeof arg === 'object') {
                try {
                    return JSON.stringify(arg);
                } catch (e) {
                    return String(arg);
                }
            }
            return String(arg);
        }).join(' ');
    }
    
    // Override console methods
    console.log = function() {
        const message = formatLogArguments(arguments);
        logStorage.add('log', message, new Date().toISOString());
        originalConsole.log.apply(console, arguments);
    };
    
    console.info = function() {
        const message = formatLogArguments(arguments);
        logStorage.add('info', message, new Date().toISOString());
        originalConsole.info.apply(console, arguments);
    };
    
    console.warn = function() {
        const message = formatLogArguments(arguments);
        logStorage.add('warn', message, new Date().toISOString());
        originalConsole.warn.apply(console, arguments);
    };
    
    console.error = function() {
        const message = formatLogArguments(arguments);
        logStorage.add('error', message, new Date().toISOString());
        originalConsole.error.apply(console, arguments);
    };
    
    console.debug = function() {
        const message = formatLogArguments(arguments);
        logStorage.add('debug', message, new Date().toISOString());
        originalConsole.debug.apply(console, arguments);
    };
    
    // Capture global errors
    window.addEventListener('error', function(event) {
        const errorMessage = `ERROR: ${event.message} at ${event.filename}:${event.lineno}:${event.colno}`;
        logStorage.add('error', errorMessage, new Date().toISOString());
    });
    
    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
        let message = 'Unhandled Promise Rejection';
        if (event.reason) {
            message += ': ' + (event.reason.message || event.reason);
        }
        logStorage.add('error', message, new Date().toISOString());
    });
    
    // Send logs periodically (every 10 seconds)
    setInterval(() => {
        logStorage.sendLogsToServer();
    }, 10000);
    
    // Send logs when page is about to unload
    window.addEventListener('beforeunload', () => {
        logStorage.sendLogsToServer();
    });
    
    // Log initialization
    console.log('Console monitor initialized');
})();

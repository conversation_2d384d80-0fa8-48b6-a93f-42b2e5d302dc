<?php
/**
 * This is a simple dashboard page that bypasses the Zend Framework routing
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';
    
    // Initialize Zend Application
    $application = Zend\Mvc\Application::init($config);
    
    // Get service manager
    $serviceManager = $application->getServiceManager();
    
    // Get authentication service
    $authService = $serviceManager->get('AuthService');
    
    // Check if user is logged in
    if (!$authService->hasIdentity()) {
        // Redirect to login page
        header('Location: /simple-login.php');
        exit;
    }
    
    // Get user identity
    $identity = $authService->getIdentity();
    
    // Display dashboard
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Dashboard</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background-color: #fff;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }
            h1 {
                text-align: center;
                color: #333;
            }
            .user-info {
                margin-bottom: 20px;
                padding: 15px;
                background-color: #f9f9f9;
                border-radius: 5px;
            }
            .user-info h2 {
                margin-top: 0;
                color: #333;
            }
            .user-info p {
                margin: 5px 0;
            }
            .logout {
                text-align: center;
                margin-top: 20px;
            }
            .logout a {
                display: inline-block;
                background-color: #f44336;
                color: white;
                padding: 10px 15px;
                text-decoration: none;
                border-radius: 3px;
            }
            .logout a:hover {
                background-color: #d32f2f;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Simple Dashboard</h1>
            <div class="user-info">
                <h2>Welcome, <?php echo htmlspecialchars($identity->first_name . ' ' . $identity->last_name); ?>!</h2>
                <p><strong>Email:</strong> <?php echo htmlspecialchars($identity->email_id); ?></p>
                <p><strong>Role:</strong> <?php echo htmlspecialchars($identity->rolename ?? 'Unknown'); ?></p>
                <p><strong>User ID:</strong> <?php echo htmlspecialchars($identity->pk_user_code); ?></p>
            </div>
            <div class="logout">
                <a href="/simple-logout.php">Logout</a>
            </div>
        </div>
    </body>
    </html>
    <?php
} catch (Exception $e) {
    echo '<h1>Error</h1>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

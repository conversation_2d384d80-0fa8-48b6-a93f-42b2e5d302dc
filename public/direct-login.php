<?php
/**
 * Direct Login Script
 * 
 * This script bypasses the normal authentication flow and directly logs in the user
 */

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Define hardcoded credentials
$validCredentials = [
    '<EMAIL>' => [
        'password' => 'password',
        'pk_user_code' => 1,
        'first_name' => 'Admin',
        'last_name' => 'User',
        'role_id' => 1,
        'rolename' => 'admin'
    ],
    '<EMAIL>' => [
        'password' => 'password',
        'pk_user_code' => 2,
        'first_name' => 'Chef',
        'last_name' => 'User',
        'role_id' => 2,
        'rolename' => 'chef'
    ],
    '<EMAIL>' => [
        'password' => 'password',
        'pk_user_code' => 3,
        'first_name' => 'Delivery',
        'last_name' => 'User',
        'role_id' => 3,
        'rolename' => 'delivery'
    ]
];

// Process login
$message = '';
$authenticated = false;
$userDetails = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (isset($validCredentials[$username]) && $validCredentials[$username]['password'] === $password) {
        // Create user details
        $userDetails = $validCredentials[$username];
        $userDetails['email_id'] = $username;
        $userDetails['auth_type'] = 'legacy';
        
        // Store user details in session
        $_SESSION['storage'] = (object) $userDetails;
        $_SESSION['user'] = $userDetails;
        
        // Set company ID in session if not set
        if (!isset($_SESSION['tenant']) || !isset($_SESSION['tenant']['company_id'])) {
            $_SESSION['tenant'] = [
                'company_id' => 1,
                'unit_id' => 1,
                'company_details' => [
                    'company_name' => 'Demo Company',
                    'company_address' => '123 Main St',
                    'company_phone' => '555-1234',
                    'company_email' => '<EMAIL>',
                    'domain' => $_SERVER['HTTP_HOST']
                ]
            ];
        }
        
        // Set global variables
        $GLOBALS['company_id'] = $_SESSION['tenant']['company_id'];
        $GLOBALS['unit_id'] = $_SESSION['tenant']['unit_id'];
        
        // Initialize settings
        if (!isset($_SESSION['setting'])) {
            $_SESSION['setting'] = [
                'setting' => [
                    'WEBSITE_MAINTENANCE_ADMIN_PORTAL' => 'no',
                    'GLOBAL_AUTH_METHOD' => 'legacy',
                    'WIZARD_SETUP' => '1,1',
                    'GLOBAL_LOCALE' => 'en_US',
                    'GLOBAL_CURRENCY' => 'USD',
                    'GLOBAL_CURRENCY_ENTITY' => '$',
                    'GLOBAL_THEME' => 'default',
                    'MERCHANT_COMPANY_NAME' => 'Demo Company'
                ]
            ];
        }
        
        // Log successful authentication
        $logDir = realpath(dirname(__FILE__) . '/../data/logs');
        $logFile = $logDir . '/auth.log';
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'session_id' => session_id(),
            'user_id' => $userDetails['pk_user_code'],
            'message' => 'login_success',
            'data' => [
                'user_id' => $userDetails['pk_user_code'],
                'username' => $username,
                'method' => 'legacy',
                'auth_type' => 'legacy',
                'role' => $userDetails['rolename']
            ]
        ];
        
        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND);
        
        $authenticated = true;
        $message = 'Authentication successful';
        
        // Redirect to dashboard if requested
        if (isset($_POST['redirect']) && $_POST['redirect'] === 'true') {
            header('Location: /dashboard');
            exit;
        }
    } else {
        $message = 'Invalid username or password';
    }
}

// Check if user is already logged in
$isLoggedIn = isset($_SESSION['user']);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Direct Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
            border: none;
            cursor: pointer;
        }
        .btn-secondary {
            background-color: #2196F3;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Direct Login</h1>
        
        <?php if ($message): ?>
            <div class="<?php echo $authenticated ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($isLoggedIn): ?>
            <div class="success">
                <p>You are currently logged in as: <?php echo $_SESSION['user']['first_name'] . ' ' . $_SESSION['user']['last_name']; ?></p>
                <p>Role: <?php echo $_SESSION['user']['rolename']; ?></p>
                <p>Authentication Type: <?php echo $_SESSION['user']['auth_type']; ?></p>
            </div>
            
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <input type="hidden" name="logout" value="true">
                <button type="submit" class="btn" style="background-color: #f44336;">Logout</button>
            </form>
            
            <a href="/dashboard" class="btn">Go to Dashboard</a>
        <?php else: ?>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" value="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" value="password" required>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="redirect" value="true"> Redirect to dashboard after login
                    </label>
                </div>
                
                <button type="submit" class="btn">Login</button>
            </form>
        <?php endif; ?>
        
        <h2>Session Information:</h2>
        <pre><?php echo json_encode($_SESSION, JSON_PRETTY_PRINT); ?></pre>
        
        <div style="margin-top: 20px;">
            <a href="/init-logs.php" class="btn btn-secondary">Initialize Logs</a>
            <a href="/auth-logs.php" class="btn" style="background-color: #FF9800;">View Auth Logs</a>
            <a href="/auth" class="btn" style="background-color: #9C27B0;">Go to Real Login</a>
        </div>
    </div>
</body>
</html>

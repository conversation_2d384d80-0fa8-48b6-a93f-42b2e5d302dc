{"info": {"_postman_id": "kitchen-service-v2-working-apis", "name": "Kitchen Service V2 - Working APIs Only", "description": "Only the actually implemented and working Kitchen Service V2 endpoints with real database integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Monitoring", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}}, {"name": "Advanced Health Check (V2)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchen/health", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchen", "health"]}}}, {"name": "Export Prometheus Metrics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/plain"}], "url": {"raw": "{{base_url}}/api/v2/kitchen/metrics", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchen", "metrics"]}}}]}, {"name": "Kitchen Operations (V2)", "item": [{"name": "Get All Kitchens", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens"]}}}, {"name": "Get All Kitchens with Filters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens?date=2025-06-02&menu=lunch&kitchen_id=1", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens"], "query": [{"key": "date", "value": "2025-06-02"}, {"key": "menu", "value": "lunch"}, {"key": "kitchen_id", "value": "1"}]}}}, {"name": "Get Specific Kitchen", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/1", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "1"]}}}, {"name": "Update Prepared Count", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": 123,\n    \"prepared_count\": 1,\n    \"date\": \"2025-06-02\",\n    \"menu\": \"lunch\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/1/prepared", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "1", "prepared"]}}}, {"name": "Update All Prepared Count", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2025-06-02\",\n    \"menu\": \"lunch\",\n    \"mark_all_prepared\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/1/prepared/all", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "1", "prepared", "all"]}}}]}, {"name": "Recipe Management (V2)", "item": [{"name": "Get Recipe by Product ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/recipes/123", "host": ["{{base_url}}"], "path": ["api", "v2", "recipes", "123"]}}}]}, {"name": "Integration Endpoints (V2)", "item": [{"name": "Get Preparation Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/preparation-status?product_ids=123,124,125&kitchen_id=1&date=2025-06-02&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "preparation-status"], "query": [{"key": "product_ids", "value": "123,124,125"}, {"key": "kitchen_id", "value": "1"}, {"key": "date", "value": "2025-06-02"}, {"key": "menu", "value": "lunch"}]}}}, {"name": "Get Order Preparation Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/orders/ORD-12345/preparation-status?date=2025-06-02&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "orders", "ORD-12345", "preparation-status"], "query": [{"key": "date", "value": "2025-06-02"}, {"key": "menu", "value": "lunch"}]}}}, {"name": "Get Preparation Summary", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/preparation-summary?kitchen_id=1&date=2025-06-02&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "preparation-summary"], "query": [{"key": "kitchen_id", "value": "1"}, {"key": "date", "value": "2025-06-02"}, {"key": "menu", "value": "lunch"}]}}}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8105", "type": "string"}]}
{"info": {"_postman_id": "kitchen-service-v2-apis", "name": "Kitchen Service V2 APIs", "description": "Complete collection of Kitchen Service V2 endpoints with real database integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Monitoring", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}}, {"name": "Advanced Health Check (V2)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchen/health", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchen", "health"]}}}, {"name": "Detailed Health Check (V2)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchen/health/detailed", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchen", "health", "detailed"]}}}, {"name": "Export Prometheus Metrics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/plain"}], "url": {"raw": "{{base_url}}/api/v2/kitchen/metrics", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchen", "metrics"]}}}]}, {"name": "Kitchen Operations", "item": [{"name": "Get All Kitchens (V2)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens"]}}}, {"name": "Get Specific Kitchen (V2)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/1", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "1"]}}}, {"name": "Update Prepared Count (V2)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": 123,\n    \"prepared_count\": 50,\n    \"date\": \"2024-01-15\",\n    \"menu\": \"lunch\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/1/prepared", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "1", "prepared"]}}}, {"name": "Update All Prepared Count (V2)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2024-01-15\",\n    \"menu\": \"lunch\",\n    \"mark_all_prepared\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/1/prepared/all", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "1", "prepared", "all"]}}}]}, {"name": "Kitchen Order Management", "item": [{"name": "Get Kitchen Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/orders", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders"]}}}, {"name": "Get Specific Order", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/orders/ORD-12345", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders", "ORD-12345"]}}}, {"name": "Start Order Preparation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"kitchen_staff_id\": 101,\n    \"estimated_completion_time\": \"2024-01-15 14:30:00\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/orders/ORD-12345/start", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders", "ORD-12345", "start"]}}}, {"name": "Mark Order Ready", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"completed_by\": 101,\n    \"completion_notes\": \"Order ready for pickup\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/orders/ORD-12345/ready", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders", "ORD-12345", "ready"]}}}, {"name": "Mark Order Complete", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"completed_by\": 101,\n    \"delivery_handed_to\": \"delivery_agent_123\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/orders/ORD-12345/complete", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders", "ORD-12345", "complete"]}}}, {"name": "Get Order Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/orders/ORD-12345/status", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders", "ORD-12345", "status"]}}}, {"name": "Add Order Note", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"note\": \"Customer requested extra spicy\",\n    \"added_by\": 101\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/orders/ORD-12345/notes", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders", "ORD-12345", "notes"]}}}, {"name": "Get Order Notes", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/orders/ORD-12345/notes", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "orders", "ORD-12345", "notes"]}}}]}, {"name": "Kitchen Analytics", "item": [{"name": "Get Performance Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/analytics/performance", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "analytics", "performance"]}}}, {"name": "Get Order Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/analytics/orders", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "analytics", "orders"]}}}, {"name": "Get Preparation Time Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/analytics/preparation-times", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "analytics", "preparation-times"]}}}]}, {"name": "Kitchen Staff Management", "item": [{"name": "Get Kitchen Staff", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/staff", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "staff"]}}}, {"name": "Get Staff Performance", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/staff/101/performance", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "staff", "101", "performance"]}}}]}, {"name": "Recipe Management", "item": [{"name": "Get All Recipes (V2)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/recipes", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "recipes"]}}}, {"name": "Get Specific Recipe (V2)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/recipes/123", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "recipes", "123"]}}}, {"name": "Create Recipe (V2)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Chicken Biryani\",\n    \"description\": \"Aromatic basmati rice with spiced chicken\",\n    \"ingredients\": [\n        {\"name\": \"Basmati Rice\", \"quantity\": \"2 cups\"},\n        {\"name\": \"Chicken\", \"quantity\": \"500g\"},\n        {\"name\": \"Onions\", \"quantity\": \"2 large\"}\n    ],\n    \"instructions\": [\n        \"Soak rice for 30 minutes\",\n        \"Marinate chicken with spices\",\n        \"Cook in layers\"\n    ],\n    \"preparation_time\": 45,\n    \"cooking_time\": 60,\n    \"serves\": 4\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/recipes", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "recipes"]}}}, {"name": "Update Recipe (V2)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Chicken Biryani Deluxe\",\n    \"description\": \"Premium aromatic basmati rice with spiced chicken\",\n    \"preparation_time\": 50,\n    \"cooking_time\": 65,\n    \"serves\": 6\n}"}, "url": {"raw": "{{base_url}}/api/v2/kitchens/recipes/123", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "recipes", "123"]}}}, {"name": "Delete Recipe (V2)", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/kitchens/recipes/123", "host": ["{{base_url}}"], "path": ["api", "v2", "kitchens", "recipes", "123"]}}}]}, {"name": "Integration Endpoints", "item": [{"name": "Get Preparation Status (Integration)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/preparation-status?product_ids=123,124,125&kitchen_id=1&date=2024-01-15&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "preparation-status"], "query": [{"key": "product_ids", "value": "123,124,125"}, {"key": "kitchen_id", "value": "1"}, {"key": "date", "value": "2024-01-15"}, {"key": "menu", "value": "lunch"}]}}}, {"name": "Get Order Preparation Status (Integration)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "orders", "ORD-12345", "preparation-status"], "query": [{"key": "date", "value": "2024-01-15"}, {"key": "menu", "value": "lunch"}]}}}, {"name": "Get Preparation Summary (Integration)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/preparation-summary?kitchen_id=1&date=2024-01-15&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "preparation-summary"], "query": [{"key": "kitchen_id", "value": "1"}, {"key": "date", "value": "2024-01-15"}, {"key": "menu", "value": "lunch"}]}}}]}, {"name": "Customer Integration", "item": [{"name": "Get Order Preparation Status for Customer", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/customer/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "customer", "orders", "ORD-12345", "preparation-status"], "query": [{"key": "date", "value": "2024-01-15"}, {"key": "menu", "value": "lunch"}]}}}, {"name": "Get Multiple Orders Preparation Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_ids\": [\"ORD-12345\", \"ORD-12346\", \"ORD-12347\"],\n    \"date\": \"2024-01-15\",\n    \"menu\": \"lunch\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/integration/customer/orders/preparation-status", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "customer", "orders", "preparation-status"]}}}, {"name": "Get Customer Preparation Summary", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/customer/CUST-123/preparation-summary?date=2024-01-15&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "customer", "CUST-123", "preparation-summary"], "query": [{"key": "date", "value": "2024-01-15"}, {"key": "menu", "value": "lunch"}]}}}]}, {"name": "Delivery Integration", "item": [{"name": "Get Order Preparation Status for Delivery", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/delivery/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "delivery", "orders", "ORD-12345", "preparation-status"], "query": [{"key": "date", "value": "2024-01-15"}, {"key": "menu", "value": "lunch"}]}}}, {"name": "Estimate Delivery Time", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/integration/delivery/orders/ORD-12345/estimate-delivery-time?date=2024-01-15&menu=lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "delivery", "orders", "ORD-12345", "estimate-delivery-time"], "query": [{"key": "date", "value": "2024-01-15"}, {"key": "menu", "value": "lunch"}]}}}, {"name": "Notify Delivery Status Update", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": \"ORD-12345\",\n    \"status\": \"picked_up\",\n    \"timestamp\": \"2024-01-15 15:30:00\",\n    \"delivery_agent\": {\n        \"id\": \"DA-001\",\n        \"name\": \"<PERSON>\",\n        \"phone\": \"+1234567890\"\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v2/integration/delivery/status-update", "host": ["{{base_url}}"], "path": ["api", "v2", "integration", "delivery", "status-update"]}}}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}]}
#!/bin/bash
# test-implementation.sh

# Start Docker environment
echo "Starting Docker environment..."
./setup-dev.sh

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Test Auth Service MFA
echo "Testing Auth Service MFA..."
curl -X POST http://localhost:8001/api/v2/auth/mfa/request \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"method": "email"}'

echo "Testing Auth Service MFA Verification..."
curl -X POST http://localhost:8001/api/v2/auth/mfa/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"otp": "123456"}'

# Test QuickServe Service Customer OTP
echo "Testing QuickServe Service Customer OTP..."
curl -X POST http://localhost:8002/api/v2/quickserve/customers/1/otp/send \
  -H "Content-Type: application/json" \
  -d '{"method": "sms"}'

echo "Testing QuickServe Service Customer OTP Verification..."
curl -X POST http://localhost:8002/api/v2/quickserve/customers/1/otp/verify \
  -H "Content-Type: application/json" \
  -d '{"otp": "123456", "method": "sms"}'

echo "Tests completed!"

#!/bin/bash

# Kong API Gateway Health Check
# This script checks the health of Kong API Gateway and its services

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway Health Check"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Get Kong status
echo "Kong Status:"
curl -s http://localhost:8001/status | jq

# Check services
echo "====================================="
echo "Services Health:"
echo "====================================="
services=$(curl -s http://localhost:8001/services | jq -r '.data[].name')

for service in $services; do
  echo "Checking service: $service"
  
  # Get service details
  service_url=$(curl -s http://localhost:8001/services/$service | jq -r '.url')
  
  # Extract host and port from URL
  host=$(echo $service_url | sed -e 's|^[^/]*//||' -e 's|/.*$||' -e 's|:.*$||')
  port=$(echo $service_url | grep -o ':[0-9]*' | sed 's/://')
  
  # Default port if not specified
  if [ -z "$port" ]; then
    if [[ $service_url == https://* ]]; then
      port=443
    else
      port=80
    fi
  fi
  
  # Check if service is reachable
  if nc -z -w 5 $host $port 2>/dev/null; then
    echo "✅ Service $service is reachable at $host:$port"
  else
    echo "❌ Service $service is not reachable at $host:$port"
  fi
done

# Check routes
echo "====================================="
echo "Routes Health:"
echo "====================================="
routes=$(curl -s http://localhost:8001/routes | jq -r '.data[].name')

for route in $routes; do
  echo "Route: $route"
  
  # Get route details
  paths=$(curl -s http://localhost:8001/routes/$route | jq -r '.paths[]')
  
  for path in $paths; do
    echo "  Path: $path"
  done
done

echo "====================================="
echo "Health check complete!"
echo "====================================="

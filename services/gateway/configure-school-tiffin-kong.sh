#!/bin/bash

# Kong API Gateway Configuration for School Tiffin Services
# This script configures Kong with all necessary services, routes, and plugins
# for the OneFoodDialer 2025 school tiffin meal subscription system

set -e

echo "====================================="
echo "Kong School Tiffin Services Configuration"
echo "====================================="

# Wait for Kong to be ready
echo "Waiting for Kong to be ready..."
until curl -f -s http://localhost:8001/status > /dev/null 2>&1; do
    echo "Kong is not ready yet. Waiting..."
    sleep 5
done
echo "Kong is ready!"

# Set environment variables for JWT secrets
export PARENT_MOBILE_JWT_SECRET=${PARENT_MOBILE_JWT_SECRET:-"parent-mobile-secret-key-2025"}
export SCHOOL_ADMIN_JWT_SECRET=${SCHOOL_ADMIN_JWT_SECRET:-"school-admin-secret-key-2025"}
export DELIVERY_MOBILE_JWT_SECRET=${DELIVERY_MOBILE_JWT_SECRET:-"delivery-mobile-secret-key-2025"}

echo "Configuring School Tiffin Services..."

# Customer Service (Enhanced for School Tiffin)
echo "Configuring Customer Service..."
curl -s -X PUT http://localhost:8001/services/customer-service-v12 \
  -d name=customer-service-v12 \
  -d url=http://customer-service-v12:8000 \
  -d connect_timeout=60000 \
  -d write_timeout=60000 \
  -d read_timeout=60000 \
  -d retries=3

# Customer service main route
curl -s -X PUT http://localhost:8001/services/customer-service-v12/routes/customer-service-route \
  -d name=customer-service-route \
  -d paths[]=/v2/customers \
  -d strip_path=false \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Parent service route
curl -s -X PUT http://localhost:8001/services/customer-service-v12/routes/parent-service-route \
  -d name=parent-service-route \
  -d paths[]=/v2/parents \
  -d strip_path=false \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Customer service health check
curl -s -X PUT http://localhost:8001/services/customer-service-v12/routes/customer-service-health \
  -d name=customer-service-health \
  -d paths[]=/v2/health/customer \
  -d strip_path=true \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Subscription Service
echo "Configuring Subscription Service..."
curl -s -X PUT http://localhost:8001/services/subscription-service-v12 \
  -d name=subscription-service-v12 \
  -d url=http://subscription-service-v12:8000 \
  -d connect_timeout=60000 \
  -d write_timeout=60000 \
  -d read_timeout=60000 \
  -d retries=3

# Subscription service main route
curl -s -X PUT http://localhost:8001/services/subscription-service-v12/routes/subscription-service-route \
  -d name=subscription-service-route \
  -d paths[]=/v2/subscriptions \
  -d strip_path=false \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Meal plans route
curl -s -X PUT http://localhost:8001/services/subscription-service-v12/routes/meal-plans-route \
  -d name=meal-plans-route \
  -d paths[]=/v2/meal-plans \
  -d strip_path=false \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# School meal subscriptions route
curl -s -X PUT http://localhost:8001/services/subscription-service-v12/routes/school-meal-subscriptions-route \
  -d name=school-meal-subscriptions-route \
  -d paths[]=/v2/school-meal-subscriptions \
  -d strip_path=false \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Subscription service health check
curl -s -X PUT http://localhost:8001/services/subscription-service-v12/routes/subscription-service-health \
  -d name=subscription-service-health \
  -d paths[]=/v2/health/subscription \
  -d strip_path=true \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Delivery Service
echo "Configuring Delivery Service..."
curl -s -X PUT http://localhost:8001/services/delivery-service-v12 \
  -d name=delivery-service-v12 \
  -d url=http://delivery-service-v12:8000 \
  -d connect_timeout=60000 \
  -d write_timeout=60000 \
  -d read_timeout=60000 \
  -d retries=3

# Delivery service main route
curl -s -X PUT http://localhost:8001/services/delivery-service-v12/routes/delivery-service-route \
  -d name=delivery-service-route \
  -d paths[]=/v2/delivery \
  -d strip_path=false \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# School delivery route
curl -s -X PUT http://localhost:8001/services/delivery-service-v12/routes/school-delivery-route \
  -d name=school-delivery-route \
  -d paths[]=/v2/school \
  -d strip_path=false \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Delivery service health check
curl -s -X PUT http://localhost:8001/services/delivery-service-v12/routes/delivery-service-health \
  -d name=delivery-service-health \
  -d paths[]=/v2/health/delivery \
  -d strip_path=true \
  -d preserve_host=true \
  -d protocols[]=http \
  -d protocols[]=https

# Configure JWT Consumers
echo "Configuring JWT Consumers..."

# Parent mobile app consumer
curl -s -X PUT http://localhost:8001/consumers/parent-mobile-app \
  -d username=parent-mobile-app \
  -d custom_id=parent-mobile-app-1 \
  -d tags[]=mobile-app \
  -d tags[]=parent

curl -s -X POST http://localhost:8001/consumers/parent-mobile-app/jwt \
  -d algorithm=HS256 \
  -d key=parent-mobile-key \
  -d secret="$PARENT_MOBILE_JWT_SECRET"

# School admin web app consumer
curl -s -X PUT http://localhost:8001/consumers/school-admin-web \
  -d username=school-admin-web \
  -d custom_id=school-admin-web-1 \
  -d tags[]=web-app \
  -d tags[]=school-admin

curl -s -X POST http://localhost:8001/consumers/school-admin-web/jwt \
  -d algorithm=HS256 \
  -d key=school-admin-key \
  -d secret="$SCHOOL_ADMIN_JWT_SECRET"

# Delivery person mobile app consumer
curl -s -X PUT http://localhost:8001/consumers/delivery-mobile-app \
  -d username=delivery-mobile-app \
  -d custom_id=delivery-mobile-app-1 \
  -d tags[]=mobile-app \
  -d tags[]=delivery

curl -s -X POST http://localhost:8001/consumers/delivery-mobile-app/jwt \
  -d algorithm=HS256 \
  -d key=delivery-mobile-key \
  -d secret="$DELIVERY_MOBILE_JWT_SECRET"

# Configure Global Plugins
echo "Configuring Global Plugins..."

# Correlation ID
curl -s -X PUT http://localhost:8001/plugins/correlation-id \
  -d name=correlation-id \
  -d config.header_name=X-Correlation-ID \
  -d config.generator=uuid \
  -d config.echo_downstream=true

# Request ID
curl -s -X PUT http://localhost:8001/plugins/request-id \
  -d name=request-id \
  -d config.header_name=X-Request-ID \
  -d config.generator=uuid \
  -d config.echo_downstream=true

# Prometheus metrics
curl -s -X PUT http://localhost:8001/plugins/prometheus \
  -d name=prometheus \
  -d config.status_code_metrics=true \
  -d config.latency_metrics=true \
  -d config.upstream_health_metrics=true \
  -d config.bandwidth_metrics=true \
  -d config.per_consumer=true

# HTTP logging
curl -s -X PUT http://localhost:8001/plugins/http-log \
  -d name=http-log \
  -d config.http_endpoint=http://logging-service:8000/api/v1/logs \
  -d config.method=POST \
  -d config.timeout=10000 \
  -d config.keepalive=60000 \
  -d config.content_type=application/json

# Response transformer for security headers
curl -s -X PUT http://localhost:8001/plugins/response-transformer \
  -d name=response-transformer \
  -d config.add.headers[]="Strict-Transport-Security: max-age=31536000; includeSubDomains; preload" \
  -d config.add.headers[]="X-Content-Type-Options: nosniff" \
  -d config.add.headers[]="X-Frame-Options: DENY" \
  -d config.add.headers[]="X-XSS-Protection: 1; mode=block" \
  -d config.add.headers[]="Referrer-Policy: strict-origin-when-cross-origin"

# Configure Service-Specific Plugins
echo "Configuring Service-Specific Plugins..."

# Customer Service Plugins
echo "Configuring Customer Service plugins..."

# JWT for customer service
curl -s -X POST http://localhost:8001/services/customer-service-v12/plugins \
  -d name=jwt \
  -d config.claims_to_verify[]=exp \
  -d config.claims_to_verify[]=nbf \
  -d config.key_claim_name=kid \
  -d config.secret_is_base64=false \
  -d config.run_on_preflight=true

# Rate limiting for customer service
curl -s -X POST http://localhost:8001/services/customer-service-v12/plugins \
  -d name=rate-limiting \
  -d config.minute=100 \
  -d config.hour=2000 \
  -d config.day=20000 \
  -d config.policy=local \
  -d config.fault_tolerant=true \
  -d config.hide_client_headers=false

# CORS for customer service
curl -s -X POST http://localhost:8001/services/customer-service-v12/plugins \
  -d name=cors \
  -d config.origins[]=* \
  -d config.methods[]=GET \
  -d config.methods[]=POST \
  -d config.methods[]=PUT \
  -d config.methods[]=DELETE \
  -d config.methods[]=OPTIONS \
  -d config.methods[]=PATCH \
  -d config.headers[]=Accept \
  -d config.headers[]=Authorization \
  -d config.headers[]=Content-Type \
  -d config.headers[]=X-Correlation-ID \
  -d config.headers[]=X-Request-ID \
  -d config.credentials=true \
  -d config.max_age=3600

# Subscription Service Plugins
echo "Configuring Subscription Service plugins..."

# JWT for subscription service
curl -s -X POST http://localhost:8001/services/subscription-service-v12/plugins \
  -d name=jwt \
  -d config.claims_to_verify[]=exp \
  -d config.claims_to_verify[]=nbf \
  -d config.key_claim_name=kid \
  -d config.secret_is_base64=false \
  -d config.run_on_preflight=true

# Rate limiting for subscription service
curl -s -X POST http://localhost:8001/services/subscription-service-v12/plugins \
  -d name=rate-limiting \
  -d config.minute=80 \
  -d config.hour=1500 \
  -d config.day=15000 \
  -d config.policy=local \
  -d config.fault_tolerant=true

# CORS for subscription service
curl -s -X POST http://localhost:8001/services/subscription-service-v12/plugins \
  -d name=cors \
  -d config.origins[]=* \
  -d config.methods[]=GET \
  -d config.methods[]=POST \
  -d config.methods[]=PUT \
  -d config.methods[]=DELETE \
  -d config.methods[]=OPTIONS \
  -d config.methods[]=PATCH \
  -d config.headers[]=Accept \
  -d config.headers[]=Authorization \
  -d config.headers[]=Content-Type \
  -d config.headers[]=X-Correlation-ID \
  -d config.headers[]=X-Request-ID \
  -d config.credentials=true \
  -d config.max_age=3600

# Delivery Service Plugins
echo "Configuring Delivery Service plugins..."

# JWT for delivery service
curl -s -X POST http://localhost:8001/services/delivery-service-v12/plugins \
  -d name=jwt \
  -d config.claims_to_verify[]=exp \
  -d config.claims_to_verify[]=nbf \
  -d config.key_claim_name=kid \
  -d config.secret_is_base64=false \
  -d config.run_on_preflight=true

# Rate limiting for delivery service
curl -s -X POST http://localhost:8001/services/delivery-service-v12/plugins \
  -d name=rate-limiting \
  -d config.minute=60 \
  -d config.hour=1000 \
  -d config.day=10000 \
  -d config.policy=local \
  -d config.fault_tolerant=true

# CORS for delivery service
curl -s -X POST http://localhost:8001/services/delivery-service-v12/plugins \
  -d name=cors \
  -d config.origins[]=* \
  -d config.methods[]=GET \
  -d config.methods[]=POST \
  -d config.methods[]=PUT \
  -d config.methods[]=DELETE \
  -d config.methods[]=OPTIONS \
  -d config.methods[]=PATCH \
  -d config.headers[]=Accept \
  -d config.headers[]=Authorization \
  -d config.headers[]=Content-Type \
  -d config.headers[]=X-Correlation-ID \
  -d config.headers[]=X-Request-ID \
  -d config.credentials=true \
  -d config.max_age=3600

echo "School Tiffin Kong configuration completed successfully!"
echo "====================================="
echo "Configured Services:"
curl -s http://localhost:8001/services | jq -r '.data[] | select(.name | contains("v12")) | .name'
echo ""
echo "Configured Routes:"
curl -s http://localhost:8001/routes | jq -r '.data[] | select(.name | contains("service") or contains("meal") or contains("school")) | .name'
echo ""
echo "Configured Consumers:"
curl -s http://localhost:8001/consumers | jq -r '.data[] | select(.username | contains("app")) | .username'
echo "====================================="
echo "School Tiffin API Gateway is ready!"
echo "Base URL: http://localhost:8000"
echo "Admin API: http://localhost:8001"
echo "====================================="

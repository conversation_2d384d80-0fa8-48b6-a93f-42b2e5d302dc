#!/bin/bash

# Script to set up monitoring and observability for Kong API Gateway
# This script sets up Prometheus and Grafana for monitoring Kong API Gateway

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway Monitoring Setup"
echo "====================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo "Error: Docker is not running. Please start Docker and try again."
  exit 1
fi

# Create directories for monitoring
mkdir -p monitoring/prometheus
mkdir -p monitoring/grafana/provisioning/datasources
mkdir -p monitoring/grafana/provisioning/dashboards

# Create Prometheus configuration
cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'kong'
    scrape_interval: 5s
    static_configs:
      - targets: ['kong:8001']
    metrics_path: /metrics

  - job_name: 'prometheus'
    scrape_interval: 5s
    static_configs:
      - targets: ['localhost:9090']
EOF

# Create Grafana datasource configuration
cat > monitoring/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

# Create Grafana dashboard configuration
cat > monitoring/grafana/provisioning/dashboards/kong.yml << EOF
apiVersion: 1

providers:
  - name: 'Kong'
    orgId: 1
    folder: 'Kong'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

# Download Kong dashboard for Grafana
curl -s -o monitoring/grafana/provisioning/dashboards/kong-dashboard.json https://raw.githubusercontent.com/Kong/kong-plugin-prometheus/master/grafana/kong-dashboard.json

# Create Docker Compose file for monitoring
cat > docker-compose.monitoring.yml << EOF
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - kong-net

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - kong-net

networks:
  kong-net:
    external: true

volumes:
  prometheus_data:
  grafana_data:
EOF

# Start monitoring services
echo "Starting monitoring services..."
docker-compose -f docker-compose.monitoring.yml up -d

# Wait for services to be ready
echo "Waiting for monitoring services to be ready..."
until curl -s http://localhost:9090/-/ready > /dev/null; do
  echo "Prometheus is not ready yet. Waiting..."
  sleep 5
done

until curl -s http://localhost:3000/api/health > /dev/null; do
  echo "Grafana is not ready yet. Waiting..."
  sleep 5
done

echo "Monitoring services are ready!"
echo "====================================="
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3000 (admin/admin)"
echo "====================================="
echo "Next steps:"
echo "1. Open Grafana and explore the Kong dashboard"
echo "2. Set up alerts for critical metrics"
echo "3. Configure additional dashboards as needed"
echo "====================================="

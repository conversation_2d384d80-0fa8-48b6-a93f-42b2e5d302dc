#!/bin/bash

# Script to generate RSA key pairs for JWT signing
# This script generates RSA key pairs for JWT token signing using RS256 algorithm

set -e

# Display banner
echo "====================================="
echo "JWT RSA Key Pair Generator"
echo "====================================="

# Create keys directory if it doesn't exist
mkdir -p keys

# Generate keys for each consumer
CONSUMERS=("auth-service" "mobile-app" "web-app")

for consumer in "${CONSUMERS[@]}"; do
  echo "Generating keys for $consumer..."

  # Generate private key
  openssl genrsa -out "keys/${consumer}_private.pem" 2048

  # Generate public key
  openssl rsa -in "keys/${consumer}_private.pem" -pubout -out "keys/${consumer}_public.pem"

  echo "Keys generated for $consumer"
  echo "Private key: keys/${consumer}_private.pem"
  echo "Public key: keys/${consumer}_public.pem"
  echo
done

echo "All keys generated successfully!"
echo "====================================="
echo "Next steps:"
echo "1. Update the jwt-auth.yaml file with the public keys"
echo "2. Securely store the private keys in your Auth Service"
echo "====================================="

# End of script

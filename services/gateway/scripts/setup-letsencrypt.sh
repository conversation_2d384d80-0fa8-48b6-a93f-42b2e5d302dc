#!/bin/bash

# Script to set up Let's Encrypt certificates for Kong API Gateway
# This script uses certbot to obtain and renew Let's Encrypt certificates

set -e

# Display banner
echo "====================================="
echo "Let's Encrypt Certificate Setup"
echo "====================================="

# Check if certbot is installed
if ! command -v certbot &> /dev/null; then
  echo "Error: certbot is not installed. Please install certbot and try again."
  echo "You can install certbot with: brew install certbot"
  exit 1
fi

# Get domain name
if [ -z "$1" ]; then
  read -p "Enter domain name (e.g., tenant.cubeonebiz.com): " DOMAIN
else
  DOMAIN=$1
fi

# Get email address
if [ -z "$2" ]; then
  read -p "Enter email address for certificate notifications: " EMAIL
else
  EMAIL=$2
fi

# Create certificates directory if it doesn't exist
mkdir -p /etc/ssl/certs
mkdir -p /etc/ssl/private

# Obtain certificate
echo "Obtaining Let's Encrypt certificate for $DOMAIN..."
certbot certonly --standalone \
  --preferred-challenges http \
  --http-01-port 80 \
  --non-interactive \
  --agree-tos \
  --email $EMAIL \
  --domains $DOMAIN

# Copy certificates to Kong directory
echo "Copying certificates to Kong directory..."
cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem /etc/ssl/certs/$DOMAIN.crt
cp /etc/letsencrypt/live/$DOMAIN/privkey.pem /etc/ssl/private/$DOMAIN.key

# Set up automatic renewal
echo "Setting up automatic certificate renewal..."
cat > /etc/cron.d/certbot-renew << EOF
0 0,12 * * * root certbot renew --quiet --post-hook "cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem /etc/ssl/certs/$DOMAIN.crt && cp /etc/letsencrypt/live/$DOMAIN/privkey.pem /etc/ssl/private/$DOMAIN.key && docker exec kong kong reload"
EOF

echo "Let's Encrypt certificate setup completed successfully!"
echo "====================================="
echo "Certificate: /etc/ssl/certs/$DOMAIN.crt"
echo "Private key: /etc/ssl/private/$DOMAIN.key"
echo "Automatic renewal: Every 12 hours (if needed)"
echo "====================================="

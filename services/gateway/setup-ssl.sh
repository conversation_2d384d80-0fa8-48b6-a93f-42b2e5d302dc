#!/bin/bash

# Script to set up SSL/TLS with Let's Encrypt for Kong API Gateway
# This script uses certbot to obtain and renew Let's Encrypt certificates

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway SSL/TLS Setup"
echo "====================================="

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root or with sudo"
  exit 1
fi

# Check if certbot is installed
if ! command -v certbot &> /dev/null; then
  echo "Certbot not found. Installing..."
  if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    brew install certbot
  elif [[ -f /etc/debian_version ]]; then
    # Debian/Ubuntu
    apt-get update
    apt-get install -y certbot
  elif [[ -f /etc/redhat-release ]]; then
    # CentOS/RHEL
    yum install -y certbot
  else
    echo "Unsupported OS. Please install certbot manually."
    exit 1
  fi
fi

# Get domain name
read -p "Enter domain name (e.g., tenant.cubeonebiz.com): " DOMAIN

# Get email address
read -p "Enter email address for certificate notifications: " EMAIL

# Create directories for certificates
mkdir -p /etc/letsencrypt/live/$DOMAIN
mkdir -p /etc/ssl/certs
mkdir -p /etc/ssl/private

# Obtain certificate
echo "Obtaining Let's Encrypt certificate for $DOMAIN..."
certbot certonly --standalone \
  --preferred-challenges http \
  --http-01-port 80 \
  --non-interactive \
  --agree-tos \
  --email $EMAIL \
  --domains $DOMAIN

# Copy certificates to Kong directory
echo "Copying certificates to Kong directory..."
cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem /etc/ssl/certs/$DOMAIN.crt
cp /etc/letsencrypt/live/$DOMAIN/privkey.pem /etc/ssl/private/$DOMAIN.key

# Set up automatic renewal
echo "Setting up automatic certificate renewal..."
cat > /etc/cron.d/certbot-renew << EOF
0 0,12 * * * root certbot renew --quiet --post-hook "cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem /etc/ssl/certs/$DOMAIN.crt && cp /etc/letsencrypt/live/$DOMAIN/privkey.pem /etc/ssl/private/$DOMAIN.key && docker exec kong kong reload"
EOF

# Configure Kong to use the certificates
echo "Configuring Kong to use the certificates..."
curl -s -X POST http://localhost:8001/certificates \
  -F "cert=@/etc/ssl/certs/$DOMAIN.crt" \
  -F "key=@/etc/ssl/private/$DOMAIN.key" \
  -F "snis[]=$DOMAIN"

# Configure HTTP to HTTPS redirection
echo "Configuring HTTP to HTTPS redirection..."
curl -s -X POST http://localhost:8001/plugins \
  -d name=redirect \
  -d config.status_code=301 \
  -d config.https_port=443 \
  -d config.from=http \
  -d config.to=https

echo "SSL/TLS setup completed successfully!"
echo "====================================="
echo "Certificate: /etc/ssl/certs/$DOMAIN.crt"
echo "Private key: /etc/ssl/private/$DOMAIN.key"
echo "Automatic renewal: Every 12 hours (if needed)"
echo "====================================="
echo "Next steps:"
echo "1. Test HTTPS access to your API Gateway"
echo "2. Configure your DNS to point to this server"
echo "3. Set up monitoring and logging"
echo "====================================="

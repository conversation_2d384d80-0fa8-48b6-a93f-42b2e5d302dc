openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - Kong API Gateway
  description: |
    Consolidated Kong API Gateway specification for OneFoodDialer 2025 microservices architecture.
    
    This specification defines the unified API gateway that routes requests to various microservices
    following the pattern '/v2/{service-name}/*' with comprehensive Kong plugin configurations.
    
    ## Architecture Overview
    - **Gateway Pattern**: Centralized API gateway with microservice routing
    - **Authentication**: JWT with RS256, 24-hour tokens, 7-day refresh tokens
    - **Rate Limiting**: Configurable per endpoint and consumer
    - **CORS**: Cross-origin resource sharing for web applications
    - **Health Checks**: Automated service health monitoring
    - **Circuit Breaker**: Resilience patterns for service failures
    
    ## Service Discovery
    All microservices are automatically discovered and routed through Kong with
    consistent plugin configurations and monitoring capabilities.
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 Development Team
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: Proprietary
    url: https://onefooddialer.com/license

servers:
  - url: https://api.onefooddialer.com
    description: Production Kong API Gateway
    variables:
      version:
        default: v2
        description: API version
  - url: https://staging-api.onefooddialer.com
    description: Staging Kong API Gateway
  - url: http://localhost:8000
    description: Local development Kong Gateway

# Kong-specific extensions
x-kong-service-defaults: &service-defaults
  connect_timeout: 60000
  write_timeout: 60000
  read_timeout: 60000
  retries: 5
  protocol: http
  host: localhost
  port: 8000

x-kong-route-defaults: &route-defaults
  preserve_host: true
  strip_path: false
  protocols:
    - http
    - https
  regex_priority: 0

x-kong-plugin-configs:
  jwt: &jwt-config
    name: jwt
    config:
      secret_is_base64: false
      claims_to_verify:
        - exp
        - nbf
        - iat
      key_claim_name: iss
      algorithm: RS256
      maximum_expiration: 86400  # 24 hours
      run_on_preflight: false
  
  rate-limiting: &rate-limiting-config
    name: rate-limiting
    config:
      minute: 60
      hour: 1000
      day: 10000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
      redis_host: redis
      redis_port: 6379
      redis_timeout: 2000
  
  cors: &cors-config
    name: cors
    config:
      origins:
        - "https://app.onefooddialer.com"
        - "https://admin.onefooddialer.com"
        - "http://localhost:3000"
        - "http://localhost:3001"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        - X-Requested-With
        - X-Correlation-ID
      exposed_headers:
        - X-Auth-Token
        - X-RateLimit-Limit
        - X-RateLimit-Remaining
        - X-RateLimit-Reset
        - X-Correlation-ID
      credentials: true
      max_age: 3600
      preflight_continue: false
  
  request-transformer: &request-transformer-config
    name: request-transformer
    config:
      add:
        headers:
          - "X-Forwarded-Proto: https"
          - "X-Gateway: kong"
          - "X-Gateway-Version: 3.4.0"
      append:
        headers:
          - "X-Correlation-ID: $(uuid)"
  
  response-transformer: &response-transformer-config
    name: response-transformer
    config:
      add:
        headers:
          - "X-Powered-By: OneFoodDialer-2025"
          - "X-API-Version: v2"
          - "Cache-Control: no-cache, no-store, must-revalidate"
      remove:
        headers:
          - "Server"
          - "X-Powered-By: PHP"
  
  prometheus: &prometheus-config
    name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true

# Service definitions following Kong patterns
x-kong-services:
  auth-service: &auth-service
    name: auth-service-v12
    url: http://auth-service-v12:8000
    <<: *service-defaults
    tags:
      - authentication
      - microservice
      - v12
    
  admin-service: &admin-service
    name: admin-service-v12
    url: http://admin-service-v12:8000
    <<: *service-defaults
    tags:
      - administration
      - microservice
      - v12
    
  customer-service: &customer-service
    name: customer-service-v12
    url: http://customer-service-v12:8000
    <<: *service-defaults
    tags:
      - customer-management
      - microservice
      - v12
    
  payment-service: &payment-service
    name: payment-service-v12
    url: http://payment-service-v12:8000
    <<: *service-defaults
    tags:
      - payment-processing
      - microservice
      - v12
    
  meal-service: &meal-service
    name: meal-service-v12
    url: http://meal-service-v12:8000
    <<: *service-defaults
    tags:
      - meal-management
      - microservice
      - v12

# Kong route patterns
x-kong-routes:
  auth-routes: &auth-routes
    - name: auth-service-route
      <<: *route-defaults
      paths:
        - /v2/auth-service-v12
      service: *auth-service
      tags:
        - auth
        - public
    
  admin-routes: &admin-routes
    - name: admin-service-route
      <<: *route-defaults
      paths:
        - /v2/admin-service-v12
      service: *admin-service
      tags:
        - admin
        - protected
    
  customer-routes: &customer-routes
    - name: customer-service-route
      <<: *route-defaults
      paths:
        - /v2/customer-service-v12
      service: *customer-service
      tags:
        - customer
        - protected
    
  payment-routes: &payment-routes
    - name: payment-service-route
      <<: *route-defaults
      paths:
        - /v2/payment-service-v12
      service: *payment-service
      tags:
        - payment
        - protected
    
  meal-routes: &meal-routes
    - name: meal-service-route
      <<: *route-defaults
      paths:
        - /v2/meal-service-v12
      service: *meal-service
      tags:
        - meal
        - protected

# Consumer definitions for JWT authentication
x-kong-consumers:
  - username: admin-dashboard
    custom_id: admin-app-001
    tags:
      - admin
      - dashboard
    jwt_secrets:
      - algorithm: RS256
        key: admin-dashboard-key
        secret: admin-dashboard-secret
    acls:
      - group: admin
      - group: dashboard
    
  - username: customer-mobile-app
    custom_id: mobile-app-001
    tags:
      - customer
      - mobile
    jwt_secrets:
      - algorithm: RS256
        key: mobile-app-key
        secret: mobile-app-secret
    acls:
      - group: customer
      - group: mobile
    
  - username: customer-web-app
    custom_id: web-app-001
    tags:
      - customer
      - web
    jwt_secrets:
      - algorithm: RS256
        key: web-app-key
        secret: web-app-secret
    acls:
      - group: customer
      - group: web

# Global plugin configurations
x-kong-global-plugins:
  - <<: *prometheus-config
    enabled: true
    protocols:
      - http
      - https
  
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
    enabled: true
  
  - name: request-size-limiting
    config:
      allowed_payload_size: 10
      size_unit: megabytes
      require_content_length: false
    enabled: true
  
  - name: response-ratelimiting
    config:
      limits:
        video: 
          minute: 10
        image:
          minute: 20
      block_on_first_violation: false
      fault_tolerant: true
    enabled: true

paths:
  # Health check endpoints
  /health:
    get:
      summary: Kong Gateway Health Check
      description: Check the health status of Kong API Gateway
      operationId: getGatewayHealth
      tags:
        - Health
      responses:
        '200':
          description: Gateway is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '503':
          description: Gateway is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      x-kong-plugin-prometheus:
        enabled: true
      x-kong-plugin-cors:
        <<: *cors-config
        enabled: true

  /v2/auth-service-v12/{proxy+}:
    x-kong-service-name: auth-service-v12
    x-kong-route-name: auth-service-route
    x-kong-plugins:
      - <<: *cors-config
      - <<: *rate-limiting-config
        config:
          minute: 100  # Higher limit for auth
          hour: 2000
      - <<: *request-transformer-config
        config:
          add:
            headers:
              - "X-Service: auth-service-v12"
      - <<: *response-transformer-config
    parameters:
      - name: proxy+
        in: path
        required: true
        schema:
          type: string
        description: Proxy path to auth service
    get: &auth-proxy-operation
      summary: Proxy to Auth Service
      description: Proxy all requests to the auth microservice
      operationId: proxyToAuthService
      tags:
        - Auth Service Proxy
      responses:
        '200':
          description: Successful response from auth service
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Internal server error
    post: *auth-proxy-operation
    put: *auth-proxy-operation
    patch: *auth-proxy-operation
    delete: *auth-proxy-operation
    options: *auth-proxy-operation

  /v2/admin-service-v12/{proxy+}:
    x-kong-service-name: admin-service-v12
    x-kong-route-name: admin-service-route
    x-kong-plugins:
      - <<: *jwt-config
      - <<: *cors-config
      - <<: *rate-limiting-config
        config:
          minute: 120  # Higher limit for admin operations
          hour: 5000
      - <<: *request-transformer-config
        config:
          add:
            headers:
              - "X-Service: admin-service-v12"
      - <<: *response-transformer-config
      - name: acl
        config:
          whitelist:
            - admin
            - dashboard
    parameters:
      - name: proxy+
        in: path
        required: true
        schema:
          type: string
        description: Proxy path to admin service
    get: &admin-proxy-operation
      summary: Proxy to Admin Service
      description: Proxy all requests to the admin microservice
      operationId: proxyToAdminService
      tags:
        - Admin Service Proxy
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response from admin service
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Internal server error
    post: *admin-proxy-operation
    put: *admin-proxy-operation
    patch: *admin-proxy-operation
    delete: *admin-proxy-operation
    options: *admin-proxy-operation

  /v2/customer-service-v12/{proxy+}:
    x-kong-service-name: customer-service-v12
    x-kong-route-name: customer-service-route
    x-kong-plugins:
      - <<: *jwt-config
      - <<: *cors-config
      - <<: *rate-limiting-config
      - <<: *request-transformer-config
        config:
          add:
            headers:
              - "X-Service: customer-service-v12"
      - <<: *response-transformer-config
      - name: acl
        config:
          whitelist:
            - customer
            - admin
            - mobile
            - web
    parameters:
      - name: proxy+
        in: path
        required: true
        schema:
          type: string
        description: Proxy path to customer service
    get: &customer-proxy-operation
      summary: Proxy to Customer Service
      description: Proxy all requests to the customer microservice
      operationId: proxyToCustomerService
      tags:
        - Customer Service Proxy
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response from customer service
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Internal server error
    post: *customer-proxy-operation
    put: *customer-proxy-operation
    patch: *customer-proxy-operation
    delete: *customer-proxy-operation
    options: *customer-proxy-operation

  /v2/payment-service-v12/{proxy+}:
    x-kong-service-name: payment-service-v12
    x-kong-route-name: payment-service-route
    x-kong-plugins:
      - <<: *jwt-config
      - <<: *cors-config
      - <<: *rate-limiting-config
        config:
          minute: 30   # Lower limit for payment operations
          hour: 500
          day: 2000
      - <<: *request-transformer-config
        config:
          add:
            headers:
              - "X-Service: payment-service-v12"
              - "X-Payment-Gateway: kong"
      - <<: *response-transformer-config
      - name: acl
        config:
          whitelist:
            - customer
            - admin
            - payment
      - name: request-size-limiting
        config:
          allowed_payload_size: 5
          size_unit: megabytes
    parameters:
      - name: proxy+
        in: path
        required: true
        schema:
          type: string
        description: Proxy path to payment service
    get: &payment-proxy-operation
      summary: Proxy to Payment Service
      description: Proxy all requests to the payment microservice
      operationId: proxyToPaymentService
      tags:
        - Payment Service Proxy
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response from payment service
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Internal server error
    post: *payment-proxy-operation
    put: *payment-proxy-operation
    patch: *payment-proxy-operation
    delete: *payment-proxy-operation
    options: *payment-proxy-operation

  /v2/meal-service-v12/{proxy+}:
    x-kong-service-name: meal-service-v12
    x-kong-route-name: meal-service-route
    x-kong-plugins:
      - <<: *jwt-config
      - <<: *cors-config
      - <<: *rate-limiting-config
      - <<: *request-transformer-config
        config:
          add:
            headers:
              - "X-Service: meal-service-v12"
      - <<: *response-transformer-config
      - name: acl
        config:
          whitelist:
            - customer
            - admin
            - kitchen
    parameters:
      - name: proxy+
        in: path
        required: true
        schema:
          type: string
        description: Proxy path to meal service
    get: &meal-proxy-operation
      summary: Proxy to Meal Service
      description: Proxy all requests to the meal microservice
      operationId: proxyToMealService
      tags:
        - Meal Service Proxy
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response from meal service
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Internal server error
    post: *meal-proxy-operation
    put: *meal-proxy-operation
    patch: *meal-proxy-operation
    delete: *meal-proxy-operation
    options: *meal-proxy-operation

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication using RS256 algorithm.

        Token format: `Bearer <jwt-token>`

        **Token Specifications:**
        - Algorithm: RS256
        - Expiration: 24 hours
        - Refresh Token: 7 days
        - Claims: iss, exp, nbf, iat, sub, aud

        **Example:**
        ```
        Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
        ```

  schemas:
    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          example: healthy
        timestamp:
          type: string
          format: date-time
          example: "2025-01-28T10:30:00Z"
        version:
          type: string
          example: "3.4.0"
        services:
          type: object
          properties:
            total:
              type: integer
              example: 12
            healthy:
              type: integer
              example: 11
            unhealthy:
              type: integer
              example: 1
        uptime:
          type: number
          format: float
          description: Uptime in seconds
          example: 86400.5
        memory:
          type: object
          properties:
            used:
              type: number
              format: float
              description: Memory usage in MB
              example: 256.7
            total:
              type: number
              format: float
              description: Total memory in MB
              example: 1024.0
        load_balancer:
          type: object
          properties:
            active_targets:
              type: integer
              example: 8
            total_targets:
              type: integer
              example: 12

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum: [error]
          example: error
        message:
          type: string
          example: An error occurred
        error_code:
          type: string
          example: GATEWAY_ERROR
        timestamp:
          type: string
          format: date-time
          example: "2025-01-28T10:30:00Z"
        correlation_id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        details:
          type: object
          additionalProperties: true

    RateLimitResponse:
      type: object
      properties:
        status:
          type: string
          enum: [error]
          example: error
        message:
          type: string
          example: Rate limit exceeded
        error_code:
          type: string
          example: RATE_LIMIT_EXCEEDED
        retry_after:
          type: integer
          description: Seconds to wait before retrying
          example: 60
        limit:
          type: integer
          description: Rate limit threshold
          example: 60
        remaining:
          type: integer
          description: Remaining requests in current window
          example: 0
        reset:
          type: integer
          description: Unix timestamp when limit resets
          example: **********

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  error_code:
                    example: UNAUTHORIZED
                  message:
                    example: Authentication required
      headers:
        WWW-Authenticate:
          schema:
            type: string
          description: Authentication scheme
          example: Bearer realm="OneFoodDialer API"

    Forbidden:
      description: Access forbidden
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  error_code:
                    example: FORBIDDEN
                  message:
                    example: Access forbidden

    RateLimitExceeded:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RateLimitResponse'
      headers:
        X-RateLimit-Limit:
          schema:
            type: integer
          description: Rate limit threshold
        X-RateLimit-Remaining:
          schema:
            type: integer
          description: Remaining requests
        X-RateLimit-Reset:
          schema:
            type: integer
          description: Reset timestamp
        Retry-After:
          schema:
            type: integer
          description: Seconds to wait

    ServiceUnavailable:
      description: Service temporarily unavailable
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  error_code:
                    example: SERVICE_UNAVAILABLE
                  message:
                    example: Service temporarily unavailable
      headers:
        Retry-After:
          schema:
            type: integer
          description: Seconds to wait before retrying

  headers:
    X-Correlation-ID:
      description: Unique request correlation ID for tracing
      schema:
        type: string
        format: uuid
      example: "123e4567-e89b-12d3-a456-************"

    X-RateLimit-Limit:
      description: Rate limit threshold for the current window
      schema:
        type: integer
      example: 60

    X-RateLimit-Remaining:
      description: Number of requests remaining in current window
      schema:
        type: integer
      example: 45

    X-RateLimit-Reset:
      description: Unix timestamp when the rate limit window resets
      schema:
        type: integer
      example: **********

    X-Gateway-Version:
      description: Kong API Gateway version
      schema:
        type: string
      example: "3.4.0"

    X-Service-Name:
      description: Name of the upstream service that handled the request
      schema:
        type: string
      example: "customer-service-v12"

tags:
  - name: Health
    description: Gateway health check endpoints
  - name: Auth Service Proxy
    description: Authentication and authorization service proxy
  - name: Admin Service Proxy
    description: Administrative operations service proxy
  - name: Customer Service Proxy
    description: Customer management service proxy
  - name: Payment Service Proxy
    description: Payment processing service proxy
  - name: Meal Service Proxy
    description: Meal management service proxy

# Kong-specific documentation extensions
x-kong-documentation:
  deployment:
    description: |
      ## Kong Deployment Configuration

      This specification can be deployed to Kong using the following methods:

      ### 1. Using Kong Deck (Recommended)
      ```bash
      deck sync --kong-addr http://localhost:8001
      ```

      ### 2. Using Kong Admin API
      ```bash
      curl -X POST http://localhost:8001/config \
        -F config=@kong-gateway-consolidated.yaml
      ```

      ### 3. Using Kong Kubernetes Ingress Controller
      ```bash
      kubectl apply -f kong-gateway-consolidated.yaml
      ```

  monitoring:
    description: |
      ## Monitoring and Observability

      Kong provides comprehensive monitoring through:

      - **Prometheus Metrics**: Available at `/metrics` endpoint
      - **Health Checks**: Service health monitoring
      - **Request Tracing**: Correlation ID tracking
      - **Rate Limit Monitoring**: Real-time rate limit status
      - **Circuit Breaker**: Automatic failure detection

  security:
    description: |
      ## Security Configuration

      - **JWT Authentication**: RS256 with 24-hour expiration
      - **ACL Authorization**: Role-based access control
      - **Rate Limiting**: Per-consumer and global limits
      - **CORS**: Configured for web application origins
      - **Request Size Limiting**: Payload size restrictions
      - **TLS Termination**: HTTPS enforcement

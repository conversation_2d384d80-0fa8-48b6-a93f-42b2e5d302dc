#!/bin/bash

# Script to test Kong API Gateway configuration
# This script tests the Kong API Gateway routes, plugins, and authentication

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway Testing"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Test Kong Admin API
echo "Testing Kong Admin API..."
curl -s http://localhost:8001/status | jq

# Test Kong routes
echo "====================================="
echo "Testing Kong Routes"
echo "====================================="

# Test Auth Service route
echo "Testing Auth Service route..."
curl -i -s http://localhost:8000/v2/auth/health

# Test Customer Service route
echo "====================================="
echo "Testing Customer Service route..."
curl -i -s http://localhost:8000/v2/customer/health

# Test QuickServe Service route
echo "====================================="
echo "Testing QuickServe Service route..."
curl -i -s http://localhost:8000/v2/quickserve/health

# Test Payment Service route
echo "====================================="
echo "Testing Payment Service route..."
curl -i -s http://localhost:8000/v2/payment/health

# Test Meal Service route
echo "====================================="
echo "Testing Meal Service route..."
curl -i -s http://localhost:8000/v2/meal/health

# Test JWT authentication
echo "====================================="
echo "Testing JWT Authentication"
echo "====================================="

# Create a test JWT token
echo "Creating a test JWT token..."
JWT_SECRET="admin-api-secret"
JWT_KEY="admin-api-key"
JWT_HEADER=$(echo -n '{"alg":"HS256","typ":"JWT"}' | base64 | tr -d '=' | tr '/+' '_-')
JWT_PAYLOAD=$(echo -n '{"sub":"**********","name":"Admin User","iat":'"$(date +%s)"',"exp":'"$(($(date +%s) + 3600))"'}' | base64 | tr -d '=' | tr '/+' '_-')
JWT_SIGNATURE=$(echo -n "${JWT_HEADER}.${JWT_PAYLOAD}" | openssl dgst -sha256 -hmac "${JWT_SECRET}" -binary | base64 | tr -d '=' | tr '/+' '_-')
JWT_TOKEN="${JWT_HEADER}.${JWT_PAYLOAD}.${JWT_SIGNATURE}"

echo "JWT Token: $JWT_TOKEN"

# Test JWT authentication with the token
echo "Testing JWT authentication..."
curl -i -s http://localhost:8000/v2/customer/profile \
  -H "Authorization: Bearer $JWT_TOKEN"

# Test rate limiting
echo "====================================="
echo "Testing Rate Limiting"
echo "====================================="

echo "Making multiple requests to test rate limiting..."
for i in {1..5}; do
  echo "Request $i:"
  curl -i -s http://localhost:8000/v2/auth/health | grep -E "X-RateLimit-Remaining|RateLimit-Remaining"
  sleep 1
done

# Test CORS
echo "====================================="
echo "Testing CORS"
echo "====================================="

echo "Testing CORS preflight request..."
curl -i -s -X OPTIONS http://localhost:8000/v2/auth/health \
  -H "Origin: http://example.com" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Authorization"

# Test HSTS header
echo "====================================="
echo "Testing HSTS Header"
echo "====================================="

echo "Checking for HSTS header..."
curl -i -s http://localhost:8000/v2/auth/health | grep -i "Strict-Transport-Security"

echo "====================================="
echo "Testing completed!"
echo "====================================="

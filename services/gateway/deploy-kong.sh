#!/bin/bash

# Deploy Kong API Gateway
# This script deploys Kong API Gateway using Docker Compose

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway Deployment"
echo "====================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo "Error: Docker is not running. Please start Docker and try again."
  exit 1
fi

# Check if Docker Compose is installed
if ! docker-compose --version > /dev/null 2>&1; then
  echo "Error: Docker Compose is not installed. Please install Docker Compose and try again."
  exit 1
fi

# Copy configuration files
echo "Copying configuration files..."
mkdir -p kong
cp kong/*.yaml kong/

# Deploy Kong
echo "Deploying Kong API Gateway..."
docker-compose up -d

# Wait for Kong to be ready
echo "Waiting for Kong to be ready..."
until curl -s http://localhost:8001/status > /dev/null; do
  echo "Kong is not ready yet. Waiting..."
  sleep 5
done

# Apply configuration
echo "Applying Kong configuration..."
curl -s -X POST http://localhost:8001/config \
  -F "config=@kong/kong.yaml" \
  -H "Content-Type: multipart/form-data"

echo "Kong API Gateway deployed successfully!"
echo "Kong Admin API: http://localhost:8001"
echo "Kong Proxy: http://localhost:8000"
echo "Konga Dashboard: http://localhost:1337"

# Display routes
echo "====================================="
echo "Kong Routes"
echo "====================================="
curl -s http://localhost:8001/routes | jq '.data[] | {name, paths}'

echo "====================================="
echo "Kong Services"
echo "====================================="
curl -s http://localhost:8001/services | jq '.data[] | {name, url}'

echo "====================================="
echo "Kong Plugins"
echo "====================================="
curl -s http://localhost:8001/plugins | jq '.data[] | {name, service: .service.name}'

echo "====================================="
echo "Deployment complete!"
echo "====================================="

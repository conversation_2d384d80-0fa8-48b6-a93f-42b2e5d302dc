# Kong API Gateway Configuration

This document describes how to set up and configure Kong API Gateway for the microservices architecture.

## Overview

Kong API Gateway is used to route requests between the microservices and the legacy Zend application components. It provides the following features:

- Path-based routing with the pattern '/v2/{service-name}/*'
- JWT authentication with RS256 algorithm
- Rate limiting and request size validation
- CORS configuration
- Health check endpoints
- Circuit breaker patterns
- Consistent error responses
- Logging and monitoring
- SSL/TLS termination

## Prerequisites

- Docker and Docker Compose
- OpenSSL (for generating JWT keys)
- jq (for parsing JSON responses)
- curl (for making HTTP requests)

## Installation

1. Clone the repository
2. Navigate to the gateway directory:
   ```bash
   cd services/gateway
   ```
3. Start Kong API Gateway:
   ```bash
   ./deploy-kong.sh
   ```

## Configuration

### JWT Authentication

Kong API Gateway is configured to use JWT authentication with RS256 algorithm. The following steps are required to set up JWT authentication:

1. Generate RSA key pairs:
   ```bash
   ./scripts/generate-jwt-keys.sh
   ```
2. Update the JWT configuration with the public keys:
   ```bash
   ./deploy-kong-config.sh
   ```

### SSL/TLS Configuration

Kong API Gateway is configured to use SSL/TLS termination. The following steps are required to set up SSL/TLS:

1. Generate Let's Encrypt certificates:
   ```bash
   ./scripts/setup-letsencrypt.sh tenant.cubeonebiz.com <EMAIL>
   ```
2. Update the SSL/TLS configuration:
   ```bash
   ./deploy-kong-config.sh
   ```

### Microservices Routing

Kong API Gateway is configured to route requests to the appropriate microservices based on the path pattern '/v2/{service-name}/*'. The following services are configured:

- Auth Service: `/v2/auth/*`
- Customer Service: `/v2/customer/*`
- QuickServe Service: `/v2/quickserve/*`
- Payment Service: `/v2/payment/*`
- Meal Service: `/v2/meal/*`

### Health Checks

Kong API Gateway is configured to check the health of all services every 30 seconds. The following health check endpoints are configured:

- Auth Service: `/v2/health/auth`
- Customer Service: `/v2/health/customer`
- QuickServe Service: `/v2/health/quickserve`
- Payment Service: `/v2/health/payment`
- Meal Service: `/v2/health/meal`

### Monitoring and Logging

Kong API Gateway is configured to send logs to a centralized logging service and metrics to Prometheus. The following plugins are configured:

- Prometheus: Collects metrics for monitoring
- HTTP Log: Sends logs to a centralized logging service
- Correlation ID: Adds a correlation ID to all requests for tracing

## Usage

### Testing Routes

To test the routes, use the following commands:

```bash
# Test Auth Service
curl -i http://localhost:8000/v2/auth/health

# Test Customer Service
curl -i http://localhost:8000/v2/customer/health

# Test QuickServe Service
curl -i http://localhost:8000/v2/quickserve/health

# Test Payment Service
curl -i http://localhost:8000/v2/payment/health

# Test Meal Service
curl -i http://localhost:8000/v2/meal/health
```

### Testing JWT Authentication

To test JWT authentication, use the following commands:

```bash
# Generate a JWT token
TOKEN=$(curl -s -X POST http://localhost:8000/v2/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' | jq -r '.token')

# Use the token to access a protected endpoint
curl -i http://localhost:8000/v2/customer/profile \
  -H "Authorization: Bearer $TOKEN"
```

## Troubleshooting

If you encounter issues with Kong API Gateway, check the following:

1. Make sure Kong is running:
   ```bash
   curl -s http://localhost:8001/status
   ```

2. Check the Kong logs:
   ```bash
   docker logs kong
   ```

3. Check the health of the services:
   ```bash
   ./health-check.sh
   ```

4. Check the routes:
   ```bash
   curl -s http://localhost:8001/routes | jq
   ```

5. Check the services:
   ```bash
   curl -s http://localhost:8001/services | jq
   ```

6. Check the plugins:
   ```bash
   curl -s http://localhost:8001/plugins | jq
   ```

## References

- [Kong Documentation](https://docs.konghq.com/)
- [JWT Authentication](https://docs.konghq.com/hub/kong-inc/jwt/)
- [Rate Limiting](https://docs.konghq.com/hub/kong-inc/rate-limiting/)
- [Circuit Breaker](https://docs.konghq.com/hub/kong-inc/circuit-breaker/)
- [Prometheus](https://docs.konghq.com/hub/kong-inc/prometheus/)
- [HTTP Log](https://docs.konghq.com/hub/kong-inc/http-log/)
- [Correlation ID](https://docs.konghq.com/hub/kong-inc/correlation-id/)

#!/bin/bash

# Master script to set up Kong API Gateway
# This script runs all the setup steps for Kong API Gateway

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway Setup"
echo "====================================="

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root or with sudo for SSL/TLS setup"
  exit 1
fi

# Step 1: Deploy Kong API Gateway
echo "Step 1: Deploying Kong API Gateway..."
./deploy-kong.sh

# Step 2: Configure Kong API Gateway
echo "Step 2: Configuring Kong API Gateway..."
./configure-kong-api.sh

# Step 3: Set up SSL/TLS
echo "Step 3: Setting up SSL/TLS..."
./setup-ssl.sh

# Step 4: Set up monitoring
echo "Step 4: Setting up monitoring..."
./setup-monitoring.sh

# Step 5: Update JWT credentials to use RS256
echo "Step 5: Updating JWT credentials to use RS256..."
./update-jwt-rs256.sh

# Step 6: Generate OpenAPI specification
echo "Step 6: Generating OpenAPI specification..."
./generate-openapi.sh

# Step 7: Test Kong API Gateway
echo "Step 7: Testing Kong API Gateway..."
./test-kong.sh

echo "Kong API Gateway setup completed successfully!"
echo "====================================="
echo "Kong Admin API: http://localhost:8001"
echo "Kong Proxy: http://localhost:8000"
echo "Konga Dashboard: http://localhost:1337"
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3000 (admin/admin)"
echo "====================================="
echo "Next steps:"
echo "1. Configure your DNS to point to this server"
echo "2. Set up client applications to use the API Gateway"
echo "3. Monitor the API Gateway for performance and errors"
echo "====================================="

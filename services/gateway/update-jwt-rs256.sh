#!/bin/bash

# Script to update JWT credentials to use RS256 algorithm
# This script updates the JWT credentials for all consumers to use RS256 algorithm

set -e

# Display banner
echo "====================================="
echo "Update JWT Credentials to RS256"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Check if keys directory exists
if [ ! -d "keys" ]; then
  echo "Error: Keys directory not found. Please run the configure-kong-api.sh script first."
  exit 1
fi

# Function to format public key for Kong
format_public_key() {
  local key_file=$1
  local formatted_key=""
  
  # Read the key file line by line
  while IFS= read -r line; do
    # Skip empty lines
    if [ -n "$line" ]; then
      formatted_key="${formatted_key}${line}\\n"
    fi
  done < "$key_file"
  
  echo "$formatted_key"
}

# Update JWT credentials for each consumer
for consumer in "admin-api" "mobile-app" "web-app"; do
  echo "Updating JWT credentials for $consumer..."
  
  # Get consumer ID
  consumer_id=$(curl -s http://localhost:8001/consumers/$consumer | jq -r '.id')
  
  # Get existing JWT credentials
  jwt_id=$(curl -s http://localhost:8001/consumers/$consumer/jwt | jq -r '.data[0].id')
  
  # Delete existing JWT credentials
  if [ "$jwt_id" != "null" ]; then
    echo "Deleting existing JWT credentials..."
    curl -s -X DELETE http://localhost:8001/consumers/$consumer/jwt/$jwt_id
  fi
  
  # Format public key
  public_key=$(format_public_key "keys/${consumer}_public.pem")
  
  # Create new JWT credentials with RS256 algorithm
  echo "Creating new JWT credentials with RS256 algorithm..."
  curl -s -X POST http://localhost:8001/consumers/$consumer/jwt \
    -d "algorithm=RS256" \
    -d "key=${consumer}-key" \
    -d "rsa_public_key=${public_key}"
  
  echo "JWT credentials updated for $consumer"
  echo
done

echo "All JWT credentials updated to use RS256 algorithm!"
echo "====================================="
echo "Next steps:"
echo "1. Test JWT authentication with RS256 tokens"
echo "2. Update client applications to use RS256 tokens"
echo "====================================="

# Create a test RS256 token
echo "Creating a test RS256 token..."
echo "Note: This is for demonstration purposes only. In a real environment, you would use the private key to sign the token."

# Create a sample RS256 token using the private key
for consumer in "admin-api"; do
  echo "Creating RS256 token for $consumer..."
  
  # Create header and payload
  header='{"alg":"RS256","typ":"JWT"}'
  payload='{"sub":"1234567890","name":"Admin User","iat":'"$(date +%s)"',"exp":'"$(($(date +%s) + 3600))"'}'
  
  # Base64 encode header and payload
  b64header=$(echo -n "$header" | openssl base64 -e | tr -d '\n=' | tr '/+' '_-')
  b64payload=$(echo -n "$payload" | openssl base64 -e | tr -d '\n=' | tr '/+' '_-')
  
  # Create signature
  signature=$(echo -n "$b64header.$b64payload" | openssl dgst -sha256 -sign "keys/${consumer}_private.pem" | openssl base64 -e | tr -d '\n=' | tr '/+' '_-')
  
  # Create token
  token="$b64header.$b64payload.$signature"
  
  echo "RS256 Token: $token"
  echo
  echo "To use this token:"
  echo "curl -i http://localhost:8000/v2/customer/profile -H \"Authorization: Bearer $token\""
done

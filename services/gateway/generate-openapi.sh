#!/bin/bash

# Script to generate OpenAPI specification for Kong API Gateway
# This script generates an OpenAPI 3.1.0 specification for the Kong API Gateway

set -e

# Display banner
echo "====================================="
echo "Generate OpenAPI Specification"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Create openapi directory
mkdir -p openapi

# Get Kong services
services=$(curl -s http://localhost:8001/services | jq -r '.data[].name')

# Create OpenAPI specification
cat > openapi/openapi.yaml << EOF
openapi: 3.1.0
info:
  title: QuickServe API
  description: API for QuickServe microservices
  version: 1.0.0
  contact:
    name: QuickServe API Team
    email: <EMAIL>
    url: https://tenant.cubeonebiz.com
  license:
    name: Proprietary
servers:
  - url: https://tenant.cubeonebiz.com
    description: Production server
  - url: http://localhost:8000
    description: Local development server
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication
  schemas:
    Error:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: An error occurred
        code:
          type: integer
          example: 400
      required:
        - status
        - message
    Health:
      type: object
      properties:
        status:
          type: string
          example: UP
        timestamp:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00Z"
      required:
        - status
        - timestamp
paths:
EOF

# Add health check endpoints
for service in $services; do
  # Skip example service
  if [ "$service" == "example-service" ]; then
    continue
  fi
  
  # Get service path
  path=$(curl -s http://localhost:8001/services/$service/routes | jq -r '.data[0].paths[0]')
  
  # Add health check endpoint
  cat >> openapi/openapi.yaml << EOF
  /v2/health/${service#*-}:
    get:
      summary: Get ${service} health
      description: Check the health of the ${service}
      operationId: get${service^}Health
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                \$ref: '#/components/schemas/Health'
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                \$ref: '#/components/schemas/Error'
EOF
done

# Add service endpoints
for service in $services; do
  # Skip example service
  if [ "$service" == "example-service" ]; then
    continue
  fi
  
  # Get service path
  path=$(curl -s http://localhost:8001/services/$service/routes | jq -r '.data[0].paths[0]')
  
  # Add service endpoints
  case "$service" in
    auth-service)
      cat >> openapi/openapi.yaml << EOF
  ${path}/login:
    post:
      summary: Login
      description: Authenticate a user and get a JWT token
      operationId: login
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: password123
              required:
                - email
                - password
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  token:
                    type: string
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        example: 123
                      email:
                        type: string
                        format: email
                        example: <EMAIL>
                      name:
                        type: string
                        example: John Doe
                required:
                  - status
                  - token
                  - user
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                \$ref: '#/components/schemas/Error'
EOF
      ;;
    customer-service)
      cat >> openapi/openapi.yaml << EOF
  ${path}/profile:
    get:
      summary: Get customer profile
      description: Get the profile of the authenticated customer
      operationId: getCustomerProfile
      tags:
        - Customer
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Customer profile
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    example: 123
                  email:
                    type: string
                    format: email
                    example: <EMAIL>
                  name:
                    type: string
                    example: John Doe
                  phone:
                    type: string
                    example: "+1234567890"
                  address:
                    type: string
                    example: "123 Main St, City, Country"
                required:
                  - id
                  - email
                  - name
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                \$ref: '#/components/schemas/Error'
EOF
      ;;
    # Add more services as needed
  esac
done

echo "OpenAPI specification generated successfully!"
echo "====================================="
echo "OpenAPI specification: openapi/openapi.yaml"
echo "====================================="
echo "Next steps:"
echo "1. Review and enhance the OpenAPI specification"
echo "2. Generate client SDKs from the OpenAPI specification"
echo "3. Set up Swagger UI to visualize the API documentation"
echo "====================================="

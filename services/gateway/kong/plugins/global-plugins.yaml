_format_version: "2.1"
_transform: true

# Global plugins
plugins:
  # Prometheus metrics
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
  
  # HTTP logging
  - name: http-log
    config:
      http_endpoint: http://logging-service:8000/logs
      method: POST
      timeout: 10000
      keepalive: 60000
      retry_count: 5
      queue_size: 10
      flush_timeout: 2
  
  # Correlation ID
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
  
  # Rate limiting
  - name: rate-limiting
    config:
      minute: 100
      hour: 1000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
  
  # CORS
  - name: cors
    config:
      origins:
        - "*"
      methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
      credentials: true
      max_age: 3600
      preflight_continue: false
  
  # Request termination for deprecated endpoints
  - name: request-termination
    config:
      status_code: 410
      message: "This API endpoint has been deprecated"
    routes:
      - name: deprecated-routes
        paths:
          - /api/v1/deprecated
  
  # Bot detection
  - name: bot-detection
    config:
      allow:
        - "curl"
        - "PostmanRuntime"
      deny:
        - "curl/malicious"
  
  # IP restriction for admin API
  - name: ip-restriction
    config:
      whitelist:
        - 127.0.0.1/32
        - 10.0.0.0/8
        - **********/12
        - ***********/16
    routes:
      - name: admin-routes
        paths:
          - /admin
  
  # Request size limiting
  - name: request-size-limiting
    config:
      allowed_payload_size: 10

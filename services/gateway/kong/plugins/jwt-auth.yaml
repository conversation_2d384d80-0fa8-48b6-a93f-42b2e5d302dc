_format_version: "2.1"
_transform: true

# Global JWT Authentication Plugin Configuration
plugins:
  - name: jwt
    config:
      key_claim_name: iss
      claims_to_verify:
        - exp
        - nbf
      secret_is_base64: false
      run_on_preflight: false
      maximum_expiration: 86400  # 24 hours in seconds

# JWT Consumers
consumers:
  - username: auth-service
    jwt_secrets:
      - algorithm: RS256
        key: auth-service-key
        rsa_public_key: |
          -----BEGIN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

  - username: mobile-app
    jwt_secrets:
      - algorithm: RS256
        key: mobile-app-key
        rsa_public_key: |
          -----BEGIN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

  - username: web-app
    jwt_secrets:
      - algorithm: RS256
        key: web-app-key
        rsa_public_key: |
          -----B<PERSON>IN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

# Rate Limiting for Authenticated Users
plugins:
  - name: rate-limiting
    consumer: auth-service
    config:
      minute: 100
      hour: 1000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
      
  - name: rate-limiting
    consumer: mobile-app
    config:
      minute: 100
      hour: 1000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
      
  - name: rate-limiting
    consumer: web-app
    config:
      minute: 100
      hour: 1000
      policy: local
      fault_tolerant: true
      hide_client_headers: false

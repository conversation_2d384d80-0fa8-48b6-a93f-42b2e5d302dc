_format_version: "2.1"
_transform: true

# SSL/TLS Configuration
certificates:
  - cert: /etc/ssl/certs/cubeonebiz.com.crt
    key: /etc/ssl/private/cubeonebiz.com.key
    snis:
      - tenant.cubeonebiz.com
      - api.cubeonebiz.com
      - *.cubeonebiz.com

# HTTP to HTTPS redirection
plugins:
  - name: request-transformer
    config:
      add:
        headers:
          - Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
    routes:
      - name: all-routes
        paths:
          - /
  
  - name: response-transformer
    config:
      add:
        headers:
          - Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
    routes:
      - name: all-routes
        paths:
          - /

# Let's Encrypt ACME Challenge Route
routes:
  - name: acme-challenge
    paths:
      - /.well-known/acme-challenge/
    strip_path: false
    preserve_host: true
    protocols:
      - http
    service: acme-service

services:
  - name: acme-service
    url: http://certbot:80
    routes:
      - name: acme-challenge
        paths:
          - /.well-known/acme-challenge/

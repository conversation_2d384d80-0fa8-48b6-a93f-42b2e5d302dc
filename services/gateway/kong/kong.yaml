_format_version: "2.1"
_transform: true

# SSL/TLS Configuration
certificates:
  - cert: /etc/ssl/certs/cubeonebiz.com.crt
    key: /etc/ssl/private/cubeonebiz.com.key
    snis:
      - tenant.cubeonebiz.com
      - api.cubeonebiz.com

services:
  # Auth Service
  - name: auth-service
    url: http://auth-service-v12:8000
    routes:
      - name: auth-service-route
        paths:
          - /v2/auth
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: auth-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: auth-service-v12

  # Customer Service
  - name: customer-service
    url: http://customer-service-v12:8000
    routes:
      - name: customer-service-route
        paths:
          - /v2/customers
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: customer-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: customer-service-v12

  # Meal Service
  - name: meal-service
    url: http://meal-service-v12:8000
    routes:
      - name: meal-service-route
        paths:
          - /v2/meals
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: meal-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: meal-service-v12

  # Payment Service
  - name: payment-service
    url: http://payment-service-v12:8000
    routes:
      - name: payment-service-route
        paths:
          - /v2/payment
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: payment-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: payment-service-v12
      - name: circuit-breaker
        config:
          timeout: 60000
          threshold: 5
          window_size: 60
          retry_after: 300

  # Legacy API Fallback
  - name: legacy-api
    url: http://zend-app:8888
    routes:
      - name: legacy-api-route
        paths:
          - /api
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: legacy-api

# Health check routes for all services
routes:
  - name: auth-service-health
    service: auth-service
    paths:
      - /v2/health/auth
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

  - name: customer-service-health
    service: customer-service
    paths:
      - /v2/health/customer
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

  - name: meal-service-health
    service: meal-service
    paths:
      - /v2/health/meal
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

  - name: payment-service-health
    service: payment-service
    paths:
      - /v2/health/payment
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

# Global plugins
plugins:
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
  - name: http-log
    config:
      http_endpoint: http://logging-service:8000/logs
      method: POST
      timeout: 10000
      keepalive: 60000
      retry_count: 5
      queue_size: 10
      flush_timeout: 2
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
  - name: response-transformer
    config:
      add:
        headers:
          - Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  - name: request-termination
    config:
      status_code: 410
      message: "This API endpoint has been deprecated"
    routes:
      - name: deprecated-routes
        paths:
          - /api/v1/deprecated

# Consumers
consumers:
  - username: admin-api
    jwt_secrets:
      - algorithm: RS256
        key: admin-api-key
        rsa_public_key: |
          -----BEGIN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

  - username: mobile-app
    jwt_secrets:
      - algorithm: RS256
        key: mobile-app-key
        rsa_public_key: |
          -----BEGIN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

  - username: web-app
    jwt_secrets:
      - algorithm: RS256
        key: web-app-key
        rsa_public_key: |
          -----BEGIN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

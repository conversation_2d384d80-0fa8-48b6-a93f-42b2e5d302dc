_format_version: "2.1"
_transform: true

services:
  # Auth Service
  - name: auth-service
    url: http://auth-service-v12:8000
    routes:
      - name: auth-routes
        paths:
          - /v2/auth
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
      - name: auth-service-health
        paths:
          - /v2/health/auth
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
        destinations:
          - port: 8000
            path: /api/health
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: auth-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: auth-service-v12

upstreams:
  - name: auth-service-upstream
    targets:
      - target: auth-service-v12:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2

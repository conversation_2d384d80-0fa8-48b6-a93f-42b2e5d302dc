# Kong API Gateway Deployment Configuration for OneFoodDialer 2025
# This file contains the complete Kong configuration for deploying the microservices architecture

_format_version: "3.0"
_transform: true

# Services Configuration
services:
  - name: auth-service-v12
    url: http://auth-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    protocol: http
    tags:
      - authentication
      - microservice
      - v12

  - name: admin-service-v12
    url: http://admin-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    protocol: http
    tags:
      - administration
      - microservice
      - v12

  - name: customer-service-v12
    url: http://customer-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    protocol: http
    tags:
      - customer-management
      - microservice
      - v12

  - name: payment-service-v12
    url: http://payment-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    protocol: http
    tags:
      - payment-processing
      - microservice
      - v12

  - name: meal-service-v12
    url: http://meal-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    protocol: http
    tags:
      - meal-management
      - microservice
      - v12

  - name: subscription-service-v12
    url: http://subscription-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    protocol: http
    tags:
      - subscription-management
      - microservice
      - v12

# Routes Configuration
routes:
  # Auth Service Routes
  - name: auth-service-route
    service: auth-service-v12
    paths:
      - /v2/auth-service-v12
    preserve_host: true
    strip_path: false
    protocols:
      - http
      - https
    tags:
      - auth
      - public

  # Admin Service Routes
  - name: admin-service-route
    service: admin-service-v12
    paths:
      - /v2/admin-service-v12
    preserve_host: true
    strip_path: false
    protocols:
      - http
      - https
    tags:
      - admin
      - protected

  # Customer Service Routes
  - name: customer-service-route
    service: customer-service-v12
    paths:
      - /v2/customer-service-v12
    preserve_host: true
    strip_path: false
    protocols:
      - http
      - https
    tags:
      - customer
      - protected

  # Payment Service Routes
  - name: payment-service-route
    service: payment-service-v12
    paths:
      - /v2/payment-service-v12
    preserve_host: true
    strip_path: false
    protocols:
      - http
      - https
    tags:
      - payment
      - protected

  # Meal Service Routes
  - name: meal-service-route
    service: meal-service-v12
    paths:
      - /v2/meal-service-v12
    preserve_host: true
    strip_path: false
    protocols:
      - http
      - https
    tags:
      - meal
      - protected

  # Subscription Service Routes
  - name: subscription-service-route
    service: subscription-service-v12
    paths:
      - /v2/subscription-service-v12
    preserve_host: true
    strip_path: false
    protocols:
      - http
      - https
    tags:
      - subscription
      - protected

# Consumers Configuration
consumers:
  - username: admin-dashboard
    custom_id: admin-app-001
    tags:
      - admin
      - dashboard

  - username: customer-mobile-app
    custom_id: mobile-app-001
    tags:
      - customer
      - mobile

  - username: customer-web-app
    custom_id: web-app-001
    tags:
      - customer
      - web

# JWT Credentials
jwt_secrets:
  - consumer: admin-dashboard
    algorithm: RS256
    key: admin-dashboard-key
    secret: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END PUBLIC KEY-----

  - consumer: customer-mobile-app
    algorithm: RS256
    key: mobile-app-key
    secret: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END PUBLIC KEY-----

  - consumer: customer-web-app
    algorithm: RS256
    key: web-app-key
    secret: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END PUBLIC KEY-----

# ACL Groups
acls:
  - consumer: admin-dashboard
    group: admin
  - consumer: admin-dashboard
    group: dashboard
  - consumer: customer-mobile-app
    group: customer
  - consumer: customer-mobile-app
    group: mobile
  - consumer: customer-web-app
    group: customer
  - consumer: customer-web-app
    group: web

# Global Plugins
plugins:
  # Prometheus Monitoring
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
    enabled: true
    protocols:
      - http
      - https

  # Correlation ID
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
    enabled: true

  # Request Size Limiting
  - name: request-size-limiting
    config:
      allowed_payload_size: 10
      size_unit: megabytes
      require_content_length: false
    enabled: true

  # Response Rate Limiting
  - name: response-ratelimiting
    config:
      limits:
        video: 
          minute: 10
        image:
          minute: 20
      block_on_first_violation: false
      fault_tolerant: true
    enabled: true

# Service-Specific Plugins

# Auth Service Plugins
  - name: cors
    service: auth-service-v12
    config:
      origins:
        - "https://app.onefooddialer.com"
        - "https://admin.onefooddialer.com"
        - "http://localhost:3000"
        - "http://localhost:3001"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        - X-Requested-With
        - X-Correlation-ID
      exposed_headers:
        - X-Auth-Token
        - X-RateLimit-Limit
        - X-RateLimit-Remaining
        - X-RateLimit-Reset
        - X-Correlation-ID
      credentials: true
      max_age: 3600
      preflight_continue: false

  - name: rate-limiting
    service: auth-service-v12
    config:
      minute: 100
      hour: 2000
      day: 20000
      policy: local
      fault_tolerant: true
      hide_client_headers: false

# Admin Service Plugins
  - name: jwt
    service: admin-service-v12
    config:
      secret_is_base64: false
      claims_to_verify:
        - exp
        - nbf
        - iat
      key_claim_name: iss
      algorithm: RS256
      maximum_expiration: 86400
      run_on_preflight: false

  - name: acl
    service: admin-service-v12
    config:
      whitelist:
        - admin
        - dashboard

  - name: rate-limiting
    service: admin-service-v12
    config:
      minute: 120
      hour: 5000
      day: 50000
      policy: local
      fault_tolerant: true
      hide_client_headers: false

  - name: cors
    service: admin-service-v12
    config:
      origins:
        - "https://admin.onefooddialer.com"
        - "https://dashboard.onefooddialer.com"
        - "http://localhost:3001"
        - "http://localhost:3002"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        - X-Requested-With
        - X-Correlation-ID
        - X-Admin-Role
      exposed_headers:
        - X-Auth-Token
        - X-RateLimit-Limit
        - X-RateLimit-Remaining
        - X-RateLimit-Reset
        - X-Correlation-ID
        - X-Admin-Role
      credentials: true
      max_age: 3600
      preflight_continue: false

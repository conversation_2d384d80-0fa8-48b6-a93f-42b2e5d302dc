_format_version: "2.1"
_transform: true

services:
  # Auth Service
  - name: auth-service
    url: http://auth-service:8000
    routes:
      - name: auth-service-route
        paths:
          - /v2/auth
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: auth-service

  # Product Service (future microservice)
  - name: product-service
    url: http://product-service:8000
    routes:
      - name: product-service-route
        paths:
          - /v2/products
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: product-service

  # Customer Service (future microservice)
  - name: customer-service
    url: http://customer-service:8000
    routes:
      - name: customer-service-route
        paths:
          - /v2/customers
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: customer-service

  # Legacy API Fallback
  - name: legacy-api
    url: http://zend-app:8888
    routes:
      - name: legacy-api-route
        paths:
          - /api
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: legacy-api

# Global plugins
plugins:
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
  - name: http-log
    config:
      http_endpoint: http://logging-service:8000/logs
      method: POST
      timeout: 10000
      keepalive: 60000
      retry_count: 5
      queue_size: 10
      flush_timeout: 2

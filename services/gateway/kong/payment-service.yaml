_format_version: "2.1"
_transform: true

services:
  - name: payment-service
    url: http://payment-service-v12:8000
    routes:
      - name: payment-routes
        paths:
          - /v2/payments
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: payment-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: payment-service-v12
      - name: http-log
        config:
          http_endpoint: http://logging-service:8000/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          retry_count: 5
          queue_size: 10
          flush_timeout: 2

routes:
  - name: payment-service-health
    service: payment-service
    paths:
      - /v2/health/payment
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

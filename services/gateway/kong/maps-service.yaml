_format_version: "2.1"
_transform: true

services:
  # Maps Service
  - name: maps-service
    url: http://maps-nginx:80
    routes:
      - name: maps-tiles-route
        paths:
          - /api/v2/maps/tiles
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
        destinations:
          - port: 80
            path: /tiles
      - name: maps-geocode-route
        paths:
          - /api/v2/maps/geocode
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
        destinations:
          - port: 80
            path: /geocode
      - name: maps-route-route
        paths:
          - /api/v2/maps/route
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
        destinations:
          - port: 80
            path: /route
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 120
          hour: 2000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: maps-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: OpenStreetMap
              - X-Service: maps-service

# Health check route for maps service
routes:
  - name: maps-service-health
    service: maps-service
    paths:
      - /api/v2/health/maps
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 80
        path: /health

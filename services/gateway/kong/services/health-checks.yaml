_format_version: "2.1"
_transform: true

# Health check routes for all services
routes:
  - name: auth-service-health
    service: auth-service
    paths:
      - /v2/health/auth
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

  - name: customer-service-health
    service: customer-service
    paths:
      - /v2/health/customer
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

  - name: quickserve-service-health
    service: quickserve-service
    paths:
      - /v2/health/quickserve
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

  - name: payment-service-health
    service: payment-service
    paths:
      - /v2/health/payment
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

  - name: meal-service-health
    service: meal-service
    paths:
      - /v2/health/meal
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

# Health check upstreams
upstreams:
  - name: auth-service-upstream
    targets:
      - target: auth-service:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/health
        healthy:
          interval: 30
          successes: 2
        unhealthy:
          interval: 30
          http_failures: 2
          timeouts: 2

  - name: customer-service-upstream
    targets:
      - target: customer-service:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/health
        healthy:
          interval: 30
          successes: 2
        unhealthy:
          interval: 30
          http_failures: 2
          timeouts: 2

  - name: quickserve-service-upstream
    targets:
      - target: quickserve-service:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/health
        healthy:
          interval: 30
          successes: 2
        unhealthy:
          interval: 30
          http_failures: 2
          timeouts: 2

  - name: payment-service-upstream
    targets:
      - target: payment-service:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/health
        healthy:
          interval: 30
          successes: 2
        unhealthy:
          interval: 30
          http_failures: 2
          timeouts: 2

  - name: meal-service-upstream
    targets:
      - target: meal-service:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/health
        healthy:
          interval: 30
          successes: 2
        unhealthy:
          interval: 30
          http_failures: 2
          timeouts: 2

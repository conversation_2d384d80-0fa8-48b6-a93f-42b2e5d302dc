_format_version: "2.1"
_transform: true

services:
  # Meal Service
  - name: meal-service
    url: http://meal-service:8000
    routes:
      - name: meal-service-route
        paths:
          - /v2/meal
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: meal-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: meal-service-v12
      - name: http-log
        config:
          http_endpoint: http://logging-service:8000/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          retry_count: 5
          queue_size: 10
          flush_timeout: 2
      - name: correlation-id
        config:
          header_name: X-Correlation-ID
          generator: uuid
          echo_downstream: true
      - name: circuit-breaker
        config:
          timeout: 60000
          threshold: 5
          window_size: 60
          retry_after: 300

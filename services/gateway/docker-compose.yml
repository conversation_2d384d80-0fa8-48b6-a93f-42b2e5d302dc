version: '3.8'

services:
  # Kong database
  kong-database:
    image: postgres:13-alpine
    container_name: kong-database
    environment:
      POSTGRES_USER: kong
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: kong
    volumes:
      - kong_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "kong"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - kong-net

  # Kong migrations
  kong-migrations:
    image: kong:3.4.0-alpine
    container_name: kong-migrations
    depends_on:
      - kong-database
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
    command: kong migrations bootstrap
    restart: on-failure
    networks:
      - kong-net

  # Kong API Gateway
  kong:
    image: kong:3.4.0-alpine
    container_name: kong
    depends_on:
      - kong-database
      - kong-migrations
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001, 0.0.0.0:8444 ssl
      KONG_DECLARATIVE_CONFIG: /etc/kong/kong.yml
      KONG_PLUGINS: bundled,prometheus
    ports:
      - "8000:8000" # Kong proxy
      - "8443:8443" # Kong proxy SSL
      - "8001:8001" # Kong admin API
      - "8444:8444" # Kong admin API SSL
    volumes:
      - ./kong/kong.yml:/etc/kong/kong.yml:ro
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - kong-net

  # Kong Dashboard (Konga)
  konga:
    image: pantsel/konga:latest
    container_name: konga
    depends_on:
      - kong
    environment:
      NODE_ENV: production
      DB_ADAPTER: postgres
      DB_HOST: kong-database
      DB_USER: kong
      DB_PASSWORD: kong
      DB_DATABASE: konga
      KONGA_HOOK_TIMEOUT: 120000
      DB_PG_SCHEMA: public
    ports:
      - "1337:1337"
    restart: unless-stopped
    networks:
      - kong-net

networks:
  kong-net:
    driver: bridge

volumes:
  kong_data:

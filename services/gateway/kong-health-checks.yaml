_format_version: "2.1"
_transform: true

upstreams:
  # Auth Service Upstream
  - name: auth-service-upstream
    healthchecks:
      active:
        http_path: /v2/auth/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2
      passive:
        healthy:
          successes: 5
        unhealthy:
          http_failures: 5
          timeouts: 5
    targets:
      - target: auth-service:8000
        weight: 100

  # Payment Service Upstream
  - name: payment-service-upstream
    healthchecks:
      active:
        http_path: /v2/payments/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2
      passive:
        healthy:
          successes: 5
        unhealthy:
          http_failures: 5
          timeouts: 5
    targets:
      - target: payment-service:8000
        weight: 100

  # Customer Service Upstream
  - name: customer-service-upstream
    healthchecks:
      active:
        http_path: /v2/customers/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2
      passive:
        healthy:
          successes: 5
        unhealthy:
          http_failures: 5
          timeouts: 5
    targets:
      - target: customer-service:8000
        weight: 100

  # Catalogue Service Upstream
  - name: catalogue-service-upstream
    healthchecks:
      active:
        http_path: /v2/catalogue/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2
      passive:
        healthy:
          successes: 5
        unhealthy:
          http_failures: 5
          timeouts: 5
    targets:
      - target: catalogue-service:8000
        weight: 100

  # Kitchen Service Upstream
  - name: kitchen-service-upstream
    healthchecks:
      active:
        http_path: /v2/kitchen/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2
      passive:
        healthy:
          successes: 5
        unhealthy:
          http_failures: 5
          timeouts: 5
    targets:
      - target: kitchen-service:8000
        weight: 100

services:
  # Auth Service
  - name: auth-service
    url: http://auth-service-upstream
    routes:
      - name: auth-service-route
        paths:
          - /v2/auth
        strip_path: false
        methods:
          - GET
          - POST
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - PATCH
            - OPTIONS
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Payment Service
  - name: payment-service
    url: http://payment-service-upstream
    routes:
      - name: payment-service-route
        paths:
          - /v2/payments
        strip_path: false
        methods:
          - GET
          - POST
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - PATCH
            - OPTIONS
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Customer Service
  - name: customer-service
    url: http://customer-service-upstream
    routes:
      - name: customer-service-route
        paths:
          - /v2/customers
        strip_path: false
        methods:
          - GET
          - POST
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - PATCH
            - OPTIONS
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Catalogue Service
  - name: catalogue-service
    url: http://catalogue-service-upstream
    routes:
      - name: catalogue-service-route
        paths:
          - /v2/catalogue
        strip_path: false
        methods:
          - GET
          - POST
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - PATCH
            - OPTIONS
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Kitchen Service
  - name: kitchen-service
    url: http://kitchen-service-upstream
    routes:
      - name: kitchen-service-route
        paths:
          - /v2/kitchen
        strip_path: false
        methods:
          - GET
          - POST
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - PATCH
            - OPTIONS
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

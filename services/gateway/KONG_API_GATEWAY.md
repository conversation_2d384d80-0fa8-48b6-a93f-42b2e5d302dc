# Kong API Gateway Configuration Guide

This document provides a comprehensive guide to the Kong API Gateway configuration for the microservices architecture.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Authentication](#authentication)
6. [SSL/TLS](#ssltls)
7. [Monitoring](#monitoring)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)
10. [References](#references)

## Overview

Kong API Gateway is used to route requests between the microservices and the legacy Zend application components. It provides the following features:

- Path-based routing with the pattern '/v2/{service-name}/*'
- JWT authentication with RS256/HS256 algorithm
- Rate limiting and request size validation
- CORS configuration
- Health check endpoints
- Circuit breaker patterns
- Consistent error responses
- Logging and monitoring
- SSL/TLS termination

## Architecture

The API Gateway architecture consists of the following components:

- **Kong API Gateway**: Routes requests to the appropriate microservices
- **Kong Database**: Stores Kong configuration
- **Microservices**: Handle specific business logic
- **Monitoring**: Prometheus and Grafana for monitoring
- **Logging**: Centralized logging with ELK stack

```
                   ┌─────────────┐
                   │    Client   │
                   └──────┬──────┘
                          │
                          ▼
                   ┌─────────────┐
                   │  Kong API   │
                   │   Gateway   │
                   └──────┬──────┘
                          │
           ┌──────────────┼──────────────┐
           │              │              │
           ▼              ▼              ▼
    ┌─────────────┐┌─────────────┐┌─────────────┐
    │ Auth Service││Customer Svc ││QuickServe Svc│
    └─────────────┘└─────────────┘└─────────────┘
           │              │              │
           └──────────────┼──────────────┘
                          │
                          ▼
                   ┌─────────────┐
                   │  Database   │
                   └─────────────┘
```

## Installation

### Prerequisites

- Docker and Docker Compose
- OpenSSL (for generating JWT keys)
- jq (for parsing JSON responses)
- curl (for making HTTP requests)

### Installation Steps

1. Clone the repository
2. Navigate to the gateway directory:
   ```bash
   cd services/gateway
   ```
3. Start Kong API Gateway:
   ```bash
   ./deploy-kong.sh
   ```

## Configuration

### Services and Routes

Kong API Gateway is configured with the following services and routes:

| Service | Route | Path |
|---------|-------|------|
| Auth Service | auth-service-route | /v2/auth/* |
| Customer Service | customer-service-route | /v2/customer/* |
| QuickServe Service | quickserve-service-route | /v2/quickserve/* |
| Payment Service | payment-service-route | /v2/payment/* |
| Meal Service | meal-service-route | /v2/meal/* |

### Health Check Endpoints

Health check endpoints are configured for each service:

| Service | Health Check Endpoint |
|---------|----------------------|
| Auth Service | /v2/health/auth |
| Customer Service | /v2/health/customer |
| QuickServe Service | /v2/health/quickserve |
| Payment Service | /v2/health/payment |
| Meal Service | /v2/health/meal |

### Plugins

The following plugins are configured:

| Plugin | Purpose |
|--------|---------|
| JWT | Authentication |
| Rate Limiting | Prevent abuse |
| CORS | Cross-origin resource sharing |
| Prometheus | Metrics collection |
| HTTP Log | Centralized logging |
| Correlation ID | Request tracking |
| Response Transformer | Add response headers |
| Redirect | HTTP to HTTPS redirection |

## Authentication

### JWT Authentication

Kong API Gateway is configured to use JWT authentication with HS256 algorithm. The following consumers are configured:

- admin-api
- mobile-app
- web-app

Each consumer has a JWT credential with a key and secret.

### Creating a JWT Token

To create a JWT token for testing:

```bash
JWT_SECRET="admin-api-secret"
JWT_KEY="admin-api-key"
JWT_HEADER=$(echo -n '{"alg":"HS256","typ":"JWT"}' | base64 | tr -d '=' | tr '/+' '_-')
JWT_PAYLOAD=$(echo -n '{"sub":"**********","name":"Admin User","iat":'"$(date +%s)"',"exp":'"$(($(date +%s) + 3600))"'}' | base64 | tr -d '=' | tr '/+' '_-')
JWT_SIGNATURE=$(echo -n "${JWT_HEADER}.${JWT_PAYLOAD}" | openssl dgst -sha256 -hmac "${JWT_SECRET}" -binary | base64 | tr -d '=' | tr '/+' '_-')
JWT_TOKEN="${JWT_HEADER}.${JWT_PAYLOAD}.${JWT_SIGNATURE}"
```

### Using a JWT Token

To use a JWT token for authentication:

```bash
curl -i http://localhost:8000/v2/customer/profile \
  -H "Authorization: Bearer $JWT_TOKEN"
```

## SSL/TLS

### Let's Encrypt Certificates

Kong API Gateway is configured to use Let's Encrypt certificates for SSL/TLS. The following steps are required to set up SSL/TLS:

1. Run the SSL/TLS setup script:
   ```bash
   sudo ./setup-ssl.sh
   ```
2. Enter the domain name and email address when prompted
3. The script will obtain a certificate from Let's Encrypt and configure Kong to use it
4. The script will also set up automatic certificate renewal

### HTTP to HTTPS Redirection

HTTP to HTTPS redirection is configured using the redirect plugin. All HTTP requests are redirected to HTTPS with a 301 status code.

## Monitoring

### Prometheus and Grafana

Kong API Gateway is configured to expose metrics to Prometheus. Grafana is used to visualize these metrics.

1. Run the monitoring setup script:
   ```bash
   ./setup-monitoring.sh
   ```
2. Access Prometheus at http://localhost:9090
3. Access Grafana at http://localhost:3000 (admin/admin)
4. Explore the Kong dashboard in Grafana

### Key Metrics

The following metrics are important to monitor:

- Request rate
- Error rate
- Latency
- Upstream health
- Rate limiting
- Authentication failures

## Testing

### Testing Kong API Gateway

To test the Kong API Gateway configuration:

1. Run the testing script:
   ```bash
   ./test-kong.sh
   ```
2. The script will test the following:
   - Kong Admin API
   - Kong routes
   - JWT authentication
   - Rate limiting
   - CORS
   - HSTS header

### Manual Testing

To manually test specific endpoints:

```bash
# Test Auth Service
curl -i http://localhost:8000/v2/auth/health

# Test with JWT authentication
curl -i http://localhost:8000/v2/customer/profile \
  -H "Authorization: Bearer $JWT_TOKEN"
```

## Troubleshooting

### Common Issues

1. **Kong is not running**
   - Check if Kong container is running: `docker ps | grep kong`
   - Check Kong logs: `docker logs kong`

2. **JWT authentication fails**
   - Check if the JWT token is valid
   - Check if the consumer exists
   - Check if the JWT credential exists

3. **SSL/TLS issues**
   - Check if the certificate is valid
   - Check if the certificate is properly configured in Kong
   - Check if the domain name matches the certificate

### Debugging

To debug Kong issues:

1. Check Kong status:
   ```bash
   curl -s http://localhost:8001/status | jq
   ```

2. Check Kong configuration:
   ```bash
   curl -s http://localhost:8001/config | jq
   ```

3. Check Kong logs:
   ```bash
   docker logs kong
   ```

## References

- [Kong Documentation](https://docs.konghq.com/)
- [JWT Authentication](https://docs.konghq.com/hub/kong-inc/jwt/)
- [Rate Limiting](https://docs.konghq.com/hub/kong-inc/rate-limiting/)
- [Prometheus](https://docs.konghq.com/hub/kong-inc/prometheus/)
- [HTTP Log](https://docs.konghq.com/hub/kong-inc/http-log/)
- [Correlation ID](https://docs.konghq.com/hub/kong-inc/correlation-id/)
- [Let's Encrypt](https://letsencrypt.org/docs/)

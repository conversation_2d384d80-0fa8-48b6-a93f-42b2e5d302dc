#!/bin/bash

# Health Check Endpoints Testing Script
# This script tests the health check endpoints for all services

set -e

# Display banner
echo "====================================="
echo "Health Check Endpoints Testing"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Define colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Define health check endpoints
declare -A health_endpoints=(
  ["Auth Service"]="http://localhost:8000/v2/health/auth"
  ["Customer Service"]="http://localhost:8000/v2/health/customer"
  ["Meal Service"]="http://localhost:8000/v2/health/meal"
)

# Run tests
echo "Testing health check endpoints..."
echo "====================================="

passed=0
failed=0
total=${#health_endpoints[@]}

for service in "${!health_endpoints[@]}"; do
  endpoint=${health_endpoints[$service]}
  
  echo -n "Testing $service health check... "
  
  # Make the request
  response=$(curl -s -w "\n%{http_code}" $endpoint)
  status_code=$(echo "$response" | tail -n1)
  body=$(echo "$response" | sed '$d')
  
  # Check if the status code is 200
  if [ "$status_code" -eq 200 ]; then
    # Check if the response contains "status": "ok"
    if echo "$body" | grep -q '"status":"ok"' || echo "$body" | grep -q '"status": "ok"'; then
      echo -e "${GREEN}PASSED${NC} (Status: OK)"
      ((passed++))
    else
      echo -e "${YELLOW}WARNING${NC} (Status code: 200, but service reported issues)"
      echo "$body"
      ((failed++))
    fi
  else
    echo -e "${RED}FAILED${NC} (Status code: $status_code)"
    echo "$body"
    ((failed++))
  fi
done

# Print summary
echo "====================================="
echo "Test Summary:"
echo "====================================="
echo -e "Total services: $total"
echo -e "Healthy: ${GREEN}$passed${NC}"
echo -e "Unhealthy: ${RED}$failed${NC}"

# Exit with appropriate status code
if [ $failed -eq 0 ]; then
  echo -e "${GREEN}All services are healthy!${NC}"
  exit 0
else
  echo -e "${RED}Some services are unhealthy!${NC}"
  exit 1
fi

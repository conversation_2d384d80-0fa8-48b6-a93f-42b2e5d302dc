#!/bin/bash

# Kong API Gateway Route Testing Script
# This script tests the Kong API Gateway routes

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway Route Testing"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Define colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Define test cases
declare -A test_cases=(
  ["Auth Service Health"]="curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/v2/health/auth"
  ["Auth Service Login"]="curl -s -o /dev/null -w '%{http_code}' -X POST -H 'Content-Type: application/json' -d '{\"username\":\"admin\",\"password\":\"admin123\"}' http://localhost:8000/v2/auth/login"
  ["Customer Service Health"]="curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/v2/health/customer"
  ["Meal Service Health"]="curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/v2/health/meal"
)

# Run tests
echo "Running tests..."
echo "====================================="

passed=0
failed=0
total=${#test_cases[@]}

for test_name in "${!test_cases[@]}"; do
  test_command=${test_cases[$test_name]}
  
  echo -n "Testing $test_name... "
  
  # Run the test command
  status_code=$(eval $test_command)
  
  # Check if the status code is 2xx
  if [[ $status_code =~ ^2[0-9][0-9]$ ]]; then
    echo -e "${GREEN}PASSED${NC} (Status code: $status_code)"
    ((passed++))
  else
    echo -e "${RED}FAILED${NC} (Status code: $status_code)"
    ((failed++))
  fi
done

# Print summary
echo "====================================="
echo "Test Summary:"
echo "====================================="
echo -e "Total tests: $total"
echo -e "Passed: ${GREEN}$passed${NC}"
echo -e "Failed: ${RED}$failed${NC}"

# Exit with appropriate status code
if [ $failed -eq 0 ]; then
  echo -e "${GREEN}All tests passed!${NC}"
  exit 0
else
  echo -e "${RED}Some tests failed!${NC}"
  exit 1
fi

#!/bin/bash

# Script to configure Kong API Gateway using the Admin API
# This script applies the Kong configuration to a running Kong instance in database mode

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway API Configuration"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Generate JWT keys
echo "Generating JWT keys..."
mkdir -p keys

# Generate keys for each consumer
CONSUMERS=("admin-api" "mobile-app" "web-app")

for consumer in "${CONSUMERS[@]}"; do
  echo "Generating keys for $consumer..."

  # Generate private key
  openssl genrsa -out "keys/${consumer}_private.pem" 2048

  # Generate public key
  openssl rsa -in "keys/${consumer}_private.pem" -pubout -out "keys/${consumer}_public.pem"

  echo "Keys generated for $consumer"
  echo "Private key: keys/${consumer}_private.pem"
  echo "Public key: keys/${consumer}_public.pem"
  echo
done

echo "All keys generated successfully!"

# Configure services and routes
echo "Configuring services and routes..."

# Auth Service
echo "Configuring Auth Service..."
curl -s -X PUT http://localhost:8001/services/auth-service \
  -d name=auth-service \
  -d url=http://auth-service-v12:8000

curl -s -X PUT http://localhost:8001/services/auth-service/routes/auth-service-route \
  -d name=auth-service-route \
  -d paths[]=/v2/auth \
  -d strip_path=false \
  -d preserve_host=true

# Customer Service
echo "Configuring Customer Service..."
curl -s -X PUT http://localhost:8001/services/customer-service \
  -d name=customer-service \
  -d url=http://customer-service-v12:8000

curl -s -X PUT http://localhost:8001/services/customer-service/routes/customer-service-route \
  -d name=customer-service-route \
  -d paths[]=/v2/customer \
  -d strip_path=false \
  -d preserve_host=true

# QuickServe Service
echo "Configuring QuickServe Service..."
curl -s -X PUT http://localhost:8001/services/quickserve-service \
  -d name=quickserve-service \
  -d url=http://quickserve-service-v12:8000

curl -s -X PUT http://localhost:8001/services/quickserve-service/routes/quickserve-service-route \
  -d name=quickserve-service-route \
  -d paths[]=/v2/quickserve \
  -d strip_path=false \
  -d preserve_host=true

# Payment Service
echo "Configuring Payment Service..."
curl -s -X PUT http://localhost:8001/services/payment-service \
  -d name=payment-service \
  -d url=http://payment-service-v12:8000

curl -s -X PUT http://localhost:8001/services/payment-service/routes/payment-service-route \
  -d name=payment-service-route \
  -d paths[]=/v2/payment \
  -d strip_path=false \
  -d preserve_host=true

# Meal Service
echo "Configuring Meal Service..."
curl -s -X PUT http://localhost:8001/services/meal-service \
  -d name=meal-service \
  -d url=http://meal-service-v12:8000

curl -s -X PUT http://localhost:8001/services/meal-service/routes/meal-service-route \
  -d name=meal-service-route \
  -d paths[]=/v2/meal \
  -d strip_path=false \
  -d preserve_host=true

# Subscription Service
echo "Configuring Subscription Service..."
curl -s -X PUT http://localhost:8001/services/subscription-service \
  -d name=subscription-service \
  -d url=http://subscription-service-v12:8000

curl -s -X PUT http://localhost:8001/services/subscription-service/routes/subscription-service-route \
  -d name=subscription-service-route \
  -d paths[]=/v2/subscription \
  -d strip_path=false \
  -d preserve_host=true

# Delivery Service
echo "Configuring Delivery Service..."
curl -s -X PUT http://localhost:8001/services/delivery-service \
  -d name=delivery-service \
  -d url=http://delivery-service-v12:8000

curl -s -X PUT http://localhost:8001/services/delivery-service/routes/delivery-service-route \
  -d name=delivery-service-route \
  -d paths[]=/v2/delivery \
  -d strip_path=false \
  -d preserve_host=true

# Configure health check routes
echo "Configuring health check routes..."

# Auth Service Health
curl -s -X PUT http://localhost:8001/services/auth-service/routes/auth-service-health \
  -d name=auth-service-health \
  -d paths[]=/v2/health/auth \
  -d strip_path=true \
  -d preserve_host=true

# Customer Service Health
curl -s -X PUT http://localhost:8001/services/customer-service/routes/customer-service-health \
  -d name=customer-service-health \
  -d paths[]=/v2/health/customer \
  -d strip_path=true \
  -d preserve_host=true

# QuickServe Service Health
curl -s -X PUT http://localhost:8001/services/quickserve-service/routes/quickserve-service-health \
  -d name=quickserve-service-health \
  -d paths[]=/v2/health/quickserve \
  -d strip_path=true \
  -d preserve_host=true

# Payment Service Health
curl -s -X PUT http://localhost:8001/services/payment-service/routes/payment-service-health \
  -d name=payment-service-health \
  -d paths[]=/v2/health/payment \
  -d strip_path=true \
  -d preserve_host=true

# Meal Service Health
curl -s -X PUT http://localhost:8001/services/meal-service/routes/meal-service-health \
  -d name=meal-service-health \
  -d paths[]=/v2/health/meal \
  -d strip_path=true \
  -d preserve_host=true

# Subscription Service Health
curl -s -X PUT http://localhost:8001/services/subscription-service/routes/subscription-service-health \
  -d name=subscription-service-health \
  -d paths[]=/v2/health/subscription \
  -d strip_path=true \
  -d preserve_host=true

# Delivery Service Health
curl -s -X PUT http://localhost:8001/services/delivery-service/routes/delivery-service-health \
  -d name=delivery-service-health \
  -d paths[]=/v2/health/delivery \
  -d strip_path=true \
  -d preserve_host=true

# Configure JWT authentication
echo "Configuring JWT authentication..."

# Create consumers
echo "Creating consumers..."
curl -s -X PUT http://localhost:8001/consumers/admin-api \
  -d username=admin-api

curl -s -X PUT http://localhost:8001/consumers/mobile-app \
  -d username=mobile-app

curl -s -X PUT http://localhost:8001/consumers/web-app \
  -d username=web-app

# Add JWT credentials
echo "Adding JWT credentials..."
for consumer in "admin-api" "mobile-app" "web-app"; do
  # Read public key
  public_key=$(cat keys/${consumer}_public.pem)

  # Add JWT credential
  curl -s -X POST http://localhost:8001/consumers/${consumer}/jwt \
    -d algorithm=RS256 \
    -d key=${consumer}-key \
    -d rsa_public_key="$public_key"
done

# Configure global plugins
echo "Configuring global plugins..."

# Prometheus
curl -s -X PUT http://localhost:8001/plugins/prometheus \
  -d name=prometheus \
  -d config.status_code_metrics=true \
  -d config.latency_metrics=true \
  -d config.bandwidth_metrics=true \
  -d config.upstream_health_metrics=true

# HTTP Log
curl -s -X PUT http://localhost:8001/plugins/http-log \
  -d name=http-log \
  -d config.http_endpoint=http://logging-service:8000/logs \
  -d config.method=POST \
  -d config.timeout=10000 \
  -d config.keepalive=60000 \
  -d config.retry_count=5 \
  -d config.queue_size=10 \
  -d config.flush_timeout=2

# Correlation ID
curl -s -X PUT http://localhost:8001/plugins/correlation-id \
  -d name=correlation-id \
  -d config.header_name=X-Correlation-ID \
  -d config.generator=uuid \
  -d config.echo_downstream=true

# Rate Limiting
curl -s -X PUT http://localhost:8001/plugins/rate-limiting \
  -d name=rate-limiting \
  -d config.minute=100 \
  -d config.hour=1000 \
  -d config.policy=local \
  -d config.fault_tolerant=true \
  -d config.hide_client_headers=false

# CORS
curl -s -X PUT http://localhost:8001/plugins/cors \
  -d name=cors \
  -d config.origins=* \
  -d config.methods=GET,POST,PUT,DELETE,OPTIONS \
  -d config.headers=Accept,Accept-Version,Content-Length,Content-MD5,Content-Type,Date,X-Auth-Token,Authorization \
  -d config.credentials=true \
  -d config.max_age=3600 \
  -d config.preflight_continue=false

# Response Transformer for HSTS
curl -s -X PUT http://localhost:8001/plugins/response-transformer \
  -d name=response-transformer \
  -d config.add.headers[]=Strict-Transport-Security:max-age=31536000;\ includeSubDomains;\ preload

echo "Kong API Gateway configuration completed successfully!"
echo "====================================="
echo "Services:"
curl -s http://localhost:8001/services | jq '.data[].name'
echo "Routes:"
curl -s http://localhost:8001/routes | jq '.data[].name'
echo "Plugins:"
curl -s http://localhost:8001/plugins | jq '.data[].name'
echo "Consumers:"
curl -s http://localhost:8001/consumers | jq '.data[].username'
echo "====================================="
echo "Next steps:"
echo "1. Set up SSL/TLS certificates with Let's Encrypt"
echo "2. Test the API Gateway"
echo "3. Set up monitoring and logging"
echo "====================================="

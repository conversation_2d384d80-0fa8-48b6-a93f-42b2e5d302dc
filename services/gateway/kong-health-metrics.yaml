_format_version: "2.1"
_transform: true

services:
  # Auth Service Health Check
  - name: auth-service-health
    url: http://auth-service:8000
    routes:
      - name: auth-service-health-route
        paths:
          - /v2/auth/health
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Auth Service Detailed Health Check
  - name: auth-service-health-detailed
    url: http://auth-service:8000
    routes:
      - name: auth-service-health-detailed-route
        paths:
          - /v2/auth/health/detailed
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

  # Auth Service Metrics
  - name: auth-service-metrics
    url: http://auth-service:8000
    routes:
      - name: auth-service-metrics-route
        paths:
          - /v2/auth/metrics
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

  # Payment Service Health Check
  - name: payment-service-health
    url: http://payment-service:8000
    routes:
      - name: payment-service-health-route
        paths:
          - /v2/payments/health
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Payment Service Detailed Health Check
  - name: payment-service-health-detailed
    url: http://payment-service:8000
    routes:
      - name: payment-service-health-detailed-route
        paths:
          - /v2/payments/health/detailed
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

  # Payment Service Metrics
  - name: payment-service-metrics
    url: http://payment-service:8000
    routes:
      - name: payment-service-metrics-route
        paths:
          - /v2/payments/metrics
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

  # Customer Service Health Check
  - name: customer-service-health
    url: http://customer-service:8000
    routes:
      - name: customer-service-health-route
        paths:
          - /v2/customers/health
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Customer Service Detailed Health Check
  - name: customer-service-health-detailed
    url: http://customer-service:8000
    routes:
      - name: customer-service-health-detailed-route
        paths:
          - /v2/customers/health/detailed
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

  # Customer Service Metrics
  - name: customer-service-metrics
    url: http://customer-service:8000
    routes:
      - name: customer-service-metrics-route
        paths:
          - /v2/customers/metrics
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

  # Kitchen Service Health Check
  - name: kitchen-service-health
    url: http://kitchen-service:8000
    routes:
      - name: kitchen-service-health-route
        paths:
          - /v2/kitchen/health
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Kitchen Service Detailed Health Check
  - name: kitchen-service-health-detailed
    url: http://kitchen-service:8000
    routes:
      - name: kitchen-service-health-detailed-route
        paths:
          - /v2/kitchen/health/detailed
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

  # Kitchen Service Metrics
  - name: kitchen-service-metrics
    url: http://kitchen-service:8000
    routes:
      - name: kitchen-service-metrics-route
        paths:
          - /v2/kitchen/metrics
        strip_path: false
        methods:
          - GET
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
          headers:
            - Content-Type
            - Authorization
          exposed_headers:
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
      - name: acl
        config:
          allow:
            - admin

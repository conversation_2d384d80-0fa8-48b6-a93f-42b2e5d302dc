#!/bin/bash

# Script to deploy Kong API Gateway configuration
# This script applies the Kong configuration to a running Kong instance

set -e

# Display banner
echo "====================================="
echo "Kong API Gateway Configuration Deployment"
echo "====================================="

# Check if Kong is running
if ! curl -s http://localhost:8001/status > /dev/null; then
  echo "Error: Kong is not running. Please start Kong and try again."
  exit 1
fi

# Generate JWT keys
echo "Generating JWT keys..."
mkdir -p keys
bash scripts/generate-jwt-keys.sh

# Update JWT configuration with public keys
echo "Updating JWT configuration with public keys..."
for consumer in "auth-service" "mobile-app" "web-app"; do
  public_key=$(cat keys/${consumer}_public.pem)
  sed -i '' "s|# Replace with your actual public key|$public_key|g" kong/plugins/jwt-auth.yaml
done

# Apply configuration
echo "Applying Kong configuration..."
curl -s -X POST http://localhost:8001/config \
  -F "config=@kong/kong.yaml" \
  -H "Content-Type: multipart/form-data"

# Verify configuration
echo "Verifying Kong configuration..."
echo "Services:"
curl -s http://localhost:8001/services | jq '.data[].name'
echo "Routes:"
curl -s http://localhost:8001/routes | jq '.data[].name'
echo "Plugins:"
curl -s http://localhost:8001/plugins | jq '.data[].name'
echo "Consumers:"
curl -s http://localhost:8001/consumers | jq '.data[].username'

echo "Kong API Gateway configuration deployed successfully!"
echo "====================================="
echo "Next steps:"
echo "1. Set up SSL/TLS certificates with Let's Encrypt"
echo "2. Configure health checks for all services"
echo "3. Set up monitoring and logging"
echo "====================================="

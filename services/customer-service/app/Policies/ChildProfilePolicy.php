<?php

namespace App\Policies;

use App\Models\ChildProfile;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;

class ChildProfilePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any child profiles.
     */
    public function viewAny(User $user): bool
    {
        // Admins can view all child profiles
        if ($user->hasRole('admin') || $user->hasRole('super_admin')) {
            return true;
        }

        // Parents can view their own children
        if ($user->hasRole('parent')) {
            return true;
        }

        // School admins can view children in their school
        if ($user->hasRole('school_admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view the child profile.
     */
    public function view(User $user, ChildProfile $childProfile): Response
    {
        // Admins can view any child profile
        if ($user->hasRole('admin') || $user->hasRole('super_admin')) {
            return Response::allow();
        }

        // Parents can only view their own children
        if ($user->hasRole('parent')) {
            if ($childProfile->parent_customer_id === $user->customer_id) {
                return Response::allow();
            }
            return Response::deny('You can only view your own children.');
        }

        // School admins can view children in their school
        if ($user->hasRole('school_admin')) {
            $userSchoolIds = $user->schools()->pluck('id')->toArray();
            if (in_array($childProfile->school_id, $userSchoolIds)) {
                return Response::allow();
            }
            return Response::deny('You can only view children in your school.');
        }

        return Response::deny('You do not have permission to view this child profile.');
    }

    /**
     * Determine whether the user can create child profiles.
     */
    public function create(User $user): bool
    {
        // Only parents can create child profiles
        if ($user->hasRole('parent')) {
            return true;
        }

        // Admins can create child profiles for testing/support
        if ($user->hasRole('admin') || $user->hasRole('super_admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can update the child profile.
     */
    public function update(User $user, ChildProfile $childProfile): Response
    {
        // Admins can update any child profile
        if ($user->hasRole('admin') || $user->hasRole('super_admin')) {
            return Response::allow();
        }

        // Parents can only update their own children
        if ($user->hasRole('parent')) {
            if ($childProfile->parent_customer_id === $user->customer_id) {
                return Response::allow();
            }
            return Response::deny('You can only update your own children.');
        }

        // School admins can update limited fields for children in their school
        if ($user->hasRole('school_admin')) {
            $userSchoolIds = $user->schools()->pluck('id')->toArray();
            if (in_array($childProfile->school_id, $userSchoolIds)) {
                return Response::allow('Limited school admin access');
            }
            return Response::deny('You can only update children in your school.');
        }

        return Response::deny('You do not have permission to update this child profile.');
    }

    /**
     * Determine whether the user can delete the child profile.
     */
    public function delete(User $user, ChildProfile $childProfile): Response
    {
        // Only super admins can delete child profiles
        if ($user->hasRole('super_admin')) {
            return Response::allow();
        }

        // Parents can delete their own children (with restrictions)
        if ($user->hasRole('parent')) {
            if ($childProfile->parent_customer_id === $user->customer_id) {
                // Check if child has active subscriptions
                if ($childProfile->active_subscriptions > 0) {
                    return Response::deny('Cannot delete child with active subscriptions.');
                }
                return Response::allow();
            }
            return Response::deny('You can only delete your own children.');
        }

        return Response::deny('You do not have permission to delete this child profile.');
    }

    /**
     * Determine whether the user can restore the child profile.
     */
    public function restore(User $user, ChildProfile $childProfile): bool
    {
        // Only admins can restore deleted child profiles
        return $user->hasRole('admin') || $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can permanently delete the child profile.
     */
    public function forceDelete(User $user, ChildProfile $childProfile): bool
    {
        // Only super admins can permanently delete child profiles
        return $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can manage subscriptions for the child.
     */
    public function manageSubscriptions(User $user, ChildProfile $childProfile): Response
    {
        // Admins can manage any subscriptions
        if ($user->hasRole('admin') || $user->hasRole('super_admin')) {
            return Response::allow();
        }

        // Parents can manage subscriptions for their own children
        if ($user->hasRole('parent')) {
            if ($childProfile->parent_customer_id === $user->customer_id) {
                return Response::allow();
            }
            return Response::deny('You can only manage subscriptions for your own children.');
        }

        // School admins can view but not modify subscriptions
        if ($user->hasRole('school_admin')) {
            $userSchoolIds = $user->schools()->pluck('id')->toArray();
            if (in_array($childProfile->school_id, $userSchoolIds)) {
                return Response::allow('View-only access');
            }
            return Response::deny('You can only view subscriptions for children in your school.');
        }

        return Response::deny('You do not have permission to manage subscriptions for this child.');
    }

    /**
     * Determine whether the user can view delivery information for the child.
     */
    public function viewDeliveries(User $user, ChildProfile $childProfile): Response
    {
        // Admins can view any delivery information
        if ($user->hasRole('admin') || $user->hasRole('super_admin')) {
            return Response::allow();
        }

        // Parents can view deliveries for their own children
        if ($user->hasRole('parent')) {
            if ($childProfile->parent_customer_id === $user->customer_id) {
                return Response::allow();
            }
            return Response::deny('You can only view deliveries for your own children.');
        }

        // School admins can view deliveries for children in their school
        if ($user->hasRole('school_admin')) {
            $userSchoolIds = $user->schools()->pluck('id')->toArray();
            if (in_array($childProfile->school_id, $userSchoolIds)) {
                return Response::allow();
            }
            return Response::deny('You can only view deliveries for children in your school.');
        }

        // Kitchen staff can view deliveries they are responsible for
        if ($user->hasRole('kitchen_staff')) {
            // Check if user's kitchen serves this child's school
            $userKitchenSchools = $user->kitchen->schools()->pluck('id')->toArray();
            if (in_array($childProfile->school_id, $userKitchenSchools)) {
                return Response::allow();
            }
            return Response::deny('You can only view deliveries for schools your kitchen serves.');
        }

        return Response::deny('You do not have permission to view delivery information for this child.');
    }

    /**
     * Determine whether the user can access child's dietary information.
     */
    public function viewDietaryInfo(User $user, ChildProfile $childProfile): Response
    {
        // This is sensitive information, so more restrictive access
        
        // Admins can view dietary information
        if ($user->hasRole('admin') || $user->hasRole('super_admin')) {
            return Response::allow();
        }

        // Parents can view their own children's dietary information
        if ($user->hasRole('parent')) {
            if ($childProfile->parent_customer_id === $user->customer_id) {
                return Response::allow();
            }
            return Response::deny('You can only view dietary information for your own children.');
        }

        // Kitchen staff can view dietary restrictions for meal preparation
        if ($user->hasRole('kitchen_staff')) {
            $userKitchenSchools = $user->kitchen->schools()->pluck('id')->toArray();
            if (in_array($childProfile->school_id, $userKitchenSchools)) {
                return Response::allow('Kitchen preparation access');
            }
            return Response::deny('You can only view dietary information for schools your kitchen serves.');
        }

        return Response::deny('You do not have permission to view dietary information for this child.');
    }
}

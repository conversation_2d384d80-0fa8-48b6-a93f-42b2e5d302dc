<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Customer Address Model
 * 
 * This model represents a customer's address in the system.
 */
class CustomerAddress extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_address';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_address_code';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_code',
        'address_type',
        'address_name',
        'address_line1',
        'address_line2',
        'landmark',
        'city',
        'state',
        'country',
        'pincode',
        'latitude',
        'longitude',
        'is_default',
        'status',
        'company_id',
        'unit_id'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_default' => 'boolean',
        'status' => 'boolean',
    ];

    /**
     * Get the customer that owns the address.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the full address as a string.
     *
     * @return string
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address_line1;
        
        if (!empty($this->address_line2)) {
            $address .= ', ' . $this->address_line2;
        }
        
        if (!empty($this->landmark)) {
            $address .= ', ' . $this->landmark;
        }
        
        $address .= ', ' . $this->city;
        
        if (!empty($this->state)) {
            $address .= ', ' . $this->state;
        }
        
        if (!empty($this->country)) {
            $address .= ', ' . $this->country;
        }
        
        if (!empty($this->pincode)) {
            $address .= ' - ' . $this->pincode;
        }
        
        return $address;
    }
}

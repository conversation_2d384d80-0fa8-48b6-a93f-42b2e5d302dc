<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Customer Model
 * 
 * This model represents a customer in the system.
 */
class Customer extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_code';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_name',
        'phone',
        'email_address',
        'customer_Address',
        'location_code',
        'location_name',
        'lunch_loc_code',
        'lunch_loc_name',
        'lunch_add',
        'dinner_loc_code',
        'dinner_loc_name',
        'dinner_add',
        'food_preference',
        'city',
        'city_name',
        'company_name',
        'group_code',
        'group_name',
        'registered_on',
        'registered_from',
        'food_referance',
        'status',
        'otp',
        'password',
        'thirdparty',
        'phone_verified',
        'subscription_notification',
        'email_verified',
        'source',
        'referer',
        'gcm_id',
        'alt_phone',
        'company_id',
        'unit_id',
        'dabbawala_code_type',
        'dabbawala_code',
        'dabbawala_image',
        'isguest',
        'delivery_note'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
        'phone_verified' => 'boolean',
        'email_verified' => 'boolean',
        'registered_on' => 'datetime',
    ];

    /**
     * Get the customer's full name.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return $this->customer_name;
    }

    /**
     * Check if the customer is active.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->status == 1;
    }

    /**
     * Get the customer's addresses.
     */
    public function addresses()
    {
        return $this->hasMany(CustomerAddress::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's wallet.
     */
    public function wallet()
    {
        return $this->hasOne(CustomerWallet::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's orders.
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Generate a random string for OTP.
     *
     * @param int $length
     * @return string
     */
    public static function generateRandomString($length = 6)
    {
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SchoolSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $schools = [
            [
                'school_name' => 'Delhi Public School, Vasant Kunj',
                'address' => 'Sector C, Pocket A&B, Vasant Kunj, New Delhi, Delhi 110070',
                'contact_person_name' => 'Dr. <PERSON>',
                'contact_person_phone' => '9876543210',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:30', 'end' => '10:45'],
                    'lunch_break' => ['start' => '12:30', 'end' => '13:15'],
                    'evening_snack' => ['start' => '15:00', 'end' => '15:15']
                ]),
                'delivery_zones' => json_encode(['Vasant Kunj', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>']),
                'partnership_status' => 'active',
                'commission_percentage' => 8.50,
                'partnership_start_date' => '2024-01-01',
                'partnership_end_date' => '2024-12-31',
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_name' => 'Modern School, Barakhamba Road',
                'address' => 'Barakhamba Road, Connaught Place, New Delhi, Delhi 110001',
                'contact_person_name' => 'Ms. Priya Sharma',
                'contact_person_phone' => '9876543211',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:15', 'end' => '10:30'],
                    'lunch_break' => ['start' => '12:00', 'end' => '12:45'],
                    'afternoon_break' => ['start' => '14:30', 'end' => '14:45']
                ]),
                'delivery_zones' => json_encode(['Connaught Place', 'Karol Bagh', 'Rajouri Garden']),
                'partnership_status' => 'active',
                'commission_percentage' => 10.00,
                'partnership_start_date' => '2024-01-15',
                'partnership_end_date' => '2024-12-31',
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_name' => 'Ryan International School, Mayur Vihar',
                'address' => 'Pocket A, Mayur Vihar Phase III, Delhi 110096',
                'contact_person_name' => 'Mr. Amit Gupta',
                'contact_person_phone' => '9876543212',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_snack' => ['start' => '10:00', 'end' => '10:15'],
                    'lunch_break' => ['start' => '12:15', 'end' => '13:00'],
                    'evening_snack' => ['start' => '15:30', 'end' => '15:45']
                ]),
                'delivery_zones' => json_encode(['Mayur Vihar', 'Laxmi Nagar', 'Preet Vihar']),
                'partnership_status' => 'active',
                'commission_percentage' => 7.50,
                'partnership_start_date' => '2024-02-01',
                'partnership_end_date' => '2024-12-31',
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_name' => 'Bal Bharati Public School, Pitampura',
                'address' => 'A-1, Sector 14, Rohini, Delhi 110085',
                'contact_person_name' => 'Dr. Sunita Verma',
                'contact_person_phone' => '9876543213',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:45', 'end' => '11:00'],
                    'lunch_break' => ['start' => '12:45', 'end' => '13:30'],
                    'afternoon_snack' => ['start' => '15:15', 'end' => '15:30']
                ]),
                'delivery_zones' => json_encode(['Pitampura', 'Rohini', 'Shalimar Bagh']),
                'partnership_status' => 'pending',
                'commission_percentage' => 9.00,
                'partnership_start_date' => null,
                'partnership_end_date' => null,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_name' => 'Springdales School, Pusa Road',
                'address' => 'Pusa Road, New Delhi, Delhi 110005',
                'contact_person_name' => 'Mrs. Kavita Singh',
                'contact_person_phone' => '9876543214',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:20', 'end' => '10:35'],
                    'lunch_break' => ['start' => '12:20', 'end' => '13:05'],
                    'evening_break' => ['start' => '15:00', 'end' => '15:15']
                ]),
                'delivery_zones' => json_encode(['Pusa Road', 'Karol Bagh', 'Dev Nagar']),
                'partnership_status' => 'inactive',
                'commission_percentage' => 0.00,
                'partnership_start_date' => null,
                'partnership_end_date' => null,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_name' => 'Sardar Patel Vidyalaya',
                'address' => 'Lodi Estate, New Delhi, Delhi 110003',
                'contact_person_name' => 'Mr. Ravi Malhotra',
                'contact_person_phone' => '9876543215',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_snack' => ['start' => '10:10', 'end' => '10:25'],
                    'lunch_break' => ['start' => '12:10', 'end' => '12:55'],
                    'afternoon_break' => ['start' => '14:45', 'end' => '15:00']
                ]),
                'delivery_zones' => json_encode(['Lodi Estate', 'India Gate', 'Khan Market']),
                'partnership_status' => 'active',
                'commission_percentage' => 12.00,
                'partnership_start_date' => '2024-01-10',
                'partnership_end_date' => '2024-12-31',
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_name' => 'The Heritage School, Vasant Kunj',
                'address' => 'Sector B, Vasant Kunj, New Delhi, Delhi 110070',
                'contact_person_name' => 'Dr. Meera Joshi',
                'contact_person_phone' => '9876543216',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:35', 'end' => '10:50'],
                    'lunch_break' => ['start' => '12:35', 'end' => '13:20'],
                    'snack_time' => ['start' => '15:10', 'end' => '15:25']
                ]),
                'delivery_zones' => json_encode(['Vasant Kunj', 'Mahipalpur', 'Munirka']),
                'partnership_status' => 'active',
                'commission_percentage' => 9.50,
                'partnership_start_date' => '2024-01-20',
                'partnership_end_date' => '2024-12-31',
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_name' => 'Amity International School, Saket',
                'address' => 'A Block, Saket, New Delhi, Delhi 110017',
                'contact_person_name' => 'Ms. Neha Agarwal',
                'contact_person_phone' => '9876543217',
                'contact_person_email' => '<EMAIL>',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:25', 'end' => '10:40'],
                    'lunch_break' => ['start' => '12:25', 'end' => '13:10'],
                    'evening_snack' => ['start' => '15:20', 'end' => '15:35']
                ]),
                'delivery_zones' => json_encode(['Saket', 'Malviya Nagar', 'Green Park']),
                'partnership_status' => 'pending',
                'commission_percentage' => 8.00,
                'partnership_start_date' => null,
                'partnership_end_date' => null,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($schools as $school) {
            DB::table('schools')->insert($school);
        }

        $this->command->info('Schools seeded successfully!');
        $this->command->info('Created ' . count($schools) . ' schools with different partnership statuses and break times.');
        $this->command->info('Partnership Status Distribution:');
        $this->command->info('- Active: 5 schools');
        $this->command->info('- Pending: 2 schools');
        $this->command->info('- Inactive: 1 school');
    }
}

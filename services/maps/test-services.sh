#!/bin/bash

# Script to test OpenStreetMap services
# This script tests the OpenStreetMap services to ensure they are working correctly

set -e

# Display banner
echo "====================================="
echo "OpenStreetMap Services Test"
echo "====================================="

# Check if the services are running
if ! curl -s http://localhost:8080/health > /dev/null; then
  echo "Error: OpenStreetMap services are not running. Please start the services and try again."
  exit 1
fi

# Test the tile server
echo "Testing tile server..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/tiles/15/29587/18573.png
if [ $? -eq 0 ]; then
  echo "✅ Tile server is working"
else
  echo "❌ Tile server is not working"
fi

# Test the geocoding service
echo "Testing geocoding service..."
GEOCODE_RESULT=$(curl -s "http://localhost:8080/geocode/search?q=Mumbai&format=json&limit=1")
if [[ $GEOCODE_RESULT == *"lat"* && $GEOCODE_RESULT == *"lon"* ]]; then
  echo "✅ Geocoding service is working"
else
  echo "❌ Geocoding service is not working"
fi

# Test the reverse geocoding service
echo "Testing reverse geocoding service..."
REVERSE_RESULT=$(curl -s "http://localhost:8080/geocode/reverse?lat=19.0760&lon=72.8777&format=json")
if [[ $REVERSE_RESULT == *"display_name"* ]]; then
  echo "✅ Reverse geocoding service is working"
else
  echo "❌ Reverse geocoding service is not working"
fi

# Test the routing service
echo "Testing routing service..."
ROUTE_RESULT=$(curl -s "http://localhost:8080/route/route/v1/driving/72.8777,19.0760;72.9000,19.1000?overview=false")
if [[ $ROUTE_RESULT == *"distance"* && $ROUTE_RESULT == *"duration"* ]]; then
  echo "✅ Routing service is working"
else
  echo "❌ Routing service is not working"
fi

# Test the trip service
echo "Testing trip service..."
TRIP_RESULT=$(curl -s "http://localhost:8080/route/trip/v1/driving/72.8777,19.0760;72.9000,19.1000;72.9500,19.1500?overview=false")
if [[ $TRIP_RESULT == *"distance"* && $TRIP_RESULT == *"duration"* ]]; then
  echo "✅ Trip service is working"
else
  echo "❌ Trip service is not working"
fi

# Test the Kong API Gateway integration
echo "Testing Kong API Gateway integration..."
if curl -s http://localhost:8000/api/v2/health/maps > /dev/null; then
  echo "✅ Kong API Gateway integration is working"
else
  echo "❌ Kong API Gateway integration is not working"
fi

echo "====================================="
echo "Test complete!"
echo "====================================="

user nginx;
worker_processes auto;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    multi_accept on;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Gzip settings
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;

    # Cache settings
    proxy_cache_path /var/cache/nginx/tiles levels=1:2 keys_zone=tiles:10m max_size=10g inactive=7d use_temp_path=off;
    proxy_cache_path /var/cache/nginx/nominatim levels=1:2 keys_zone=nominatim:10m max_size=1g inactive=1d use_temp_path=off;
    proxy_cache_path /var/cache/nginx/osrm levels=1:2 keys_zone=osrm:10m max_size=1g inactive=1d use_temp_path=off;

    # Include server configurations
    include /etc/nginx/conf.d/*.conf;
}

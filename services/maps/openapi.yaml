openapi: 3.1.0
info:
  title: Mumbai Dabbawala Delivery Service - Maps API
  description: |
    API for self-hosted OpenStreetMap services used by the Mumbai Dabbawala Delivery Service.
    
    This API provides access to map tiles, geocoding, and routing services.
  version: 1.0.0
  contact:
    name: Mumbai Dabbawala Delivery Service
    email: <EMAIL>
servers:
  - url: http://localhost:8000/api/v2/maps
    description: Local development server
  - url: https://api.fooddialer.com/api/v2/maps
    description: Production server
security:
  - bearerAuth: []
paths:
  /tiles/{z}/{x}/{y}.png:
    get:
      summary: Get map tile
      description: Returns a map tile for the specified coordinates and zoom level.
      operationId: getTile
      parameters:
        - name: z
          in: path
          description: Zoom level (0-19)
          required: true
          schema:
            type: integer
            minimum: 0
            maximum: 19
        - name: x
          in: path
          description: X coordinate
          required: true
          schema:
            type: integer
        - name: y
          in: path
          description: Y coordinate
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Map tile
          content:
            image/png:
              schema:
                type: string
                format: binary
        '404':
          description: Tile not found
  /geocode/search:
    get:
      summary: Geocode address
      description: Converts an address to coordinates.
      operationId: geocodeAddress
      parameters:
        - name: q
          in: query
          description: Query string (address to geocode)
          required: true
          schema:
            type: string
        - name: format
          in: query
          description: Response format
          required: false
          schema:
            type: string
            enum: [json, xml, jsonv2]
            default: json
        - name: limit
          in: query
          description: Maximum number of results
          required: false
          schema:
            type: integer
            default: 1
        - name: addressdetails
          in: query
          description: Include address details in the response
          required: false
          schema:
            type: integer
            enum: [0, 1]
            default: 1
      responses:
        '200':
          description: Geocoding results
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GeocodingResult'
  /geocode/reverse:
    get:
      summary: Reverse geocode coordinates
      description: Converts coordinates to an address.
      operationId: reverseGeocodeCoordinates
      parameters:
        - name: lat
          in: query
          description: Latitude
          required: true
          schema:
            type: number
            format: double
        - name: lon
          in: query
          description: Longitude
          required: true
          schema:
            type: number
            format: double
        - name: format
          in: query
          description: Response format
          required: false
          schema:
            type: string
            enum: [json, xml, jsonv2]
            default: json
        - name: addressdetails
          in: query
          description: Include address details in the response
          required: false
          schema:
            type: integer
            enum: [0, 1]
            default: 1
      responses:
        '200':
          description: Reverse geocoding results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GeocodingResult'
  /route/route/v1/{profile}/{coordinates}:
    get:
      summary: Calculate route
      description: Calculates a route between two or more points.
      operationId: calculateRoute
      parameters:
        - name: profile
          in: path
          description: Routing profile
          required: true
          schema:
            type: string
            enum: [driving, car, bike, foot]
        - name: coordinates
          in: path
          description: Comma-separated list of longitude,latitude pairs
          required: true
          schema:
            type: string
        - name: steps
          in: query
          description: Include turn-by-turn instructions
          required: false
          schema:
            type: boolean
            default: false
        - name: geometries
          in: query
          description: Geometry format
          required: false
          schema:
            type: string
            enum: [polyline, polyline6, geojson]
            default: polyline
        - name: overview
          in: query
          description: Include route geometry
          required: false
          schema:
            type: string
            enum: [simplified, full, false]
            default: simplified
      responses:
        '200':
          description: Route calculation results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RouteResult'
  /route/trip/v1/{profile}/{coordinates}:
    get:
      summary: Calculate optimized trip
      description: Calculates an optimized route visiting multiple points.
      operationId: calculateTrip
      parameters:
        - name: profile
          in: path
          description: Routing profile
          required: true
          schema:
            type: string
            enum: [driving, car, bike, foot]
        - name: coordinates
          in: path
          description: Comma-separated list of longitude,latitude pairs
          required: true
          schema:
            type: string
        - name: roundtrip
          in: query
          description: Return to the starting point
          required: false
          schema:
            type: boolean
            default: true
        - name: source
          in: query
          description: Starting point
          required: false
          schema:
            type: string
            enum: [any, first]
            default: any
        - name: destination
          in: query
          description: Ending point
          required: false
          schema:
            type: string
            enum: [any, last]
            default: any
      responses:
        '200':
          description: Trip calculation results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TripResult'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    GeocodingResult:
      type: object
      properties:
        place_id:
          type: integer
        licence:
          type: string
        osm_type:
          type: string
        osm_id:
          type: integer
        lat:
          type: string
        lon:
          type: string
        display_name:
          type: string
        address:
          type: object
          additionalProperties:
            type: string
        boundingbox:
          type: array
          items:
            type: string
    RouteResult:
      type: object
      properties:
        code:
          type: string
        routes:
          type: array
          items:
            type: object
            properties:
              distance:
                type: number
              duration:
                type: number
              geometry:
                type: string
              legs:
                type: array
                items:
                  type: object
    TripResult:
      type: object
      properties:
        code:
          type: string
        trips:
          type: array
          items:
            type: object
            properties:
              distance:
                type: number
              duration:
                type: number
              geometry:
                type: string
        waypoints:
          type: array
          items:
            type: object
            properties:
              waypoint_index:
                type: integer
              trips_index:
                type: integer

version: '3.8'

services:
  # PostgreSQL with PostGIS for spatial data
  postgis:
    image: postgis/postgis:15-3.4
    container_name: maps-postgis
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: gis
    volumes:
      - postgis-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - maps-net

  # Nominatim for geocoding
  nominatim:
    image: mediagis/nominatim:4.3
    container_name: maps-nominatim
    environment:
      PBF_URL: https://download.geofabrik.de/asia/india/maharashtra-latest.osm.pbf
      REPLICATION_URL: https://download.geofabrik.de/asia/india/maharashtra-updates/
      NOMINATIM_PASSWORD: nominatim
      POSTGRES_DB: nominatim
      POSTGRES_USER: nominatim
      POSTGRES_PASSWORD: nominatim
      POSTGRES_HOST: nominatim-db
      NOMINATIM_MAX_LOAD: 20
    volumes:
      - nominatim-data:/var/lib/nominatim
    depends_on:
      - nominatim-db
    restart: unless-stopped
    networks:
      - maps-net

  # Separate PostgreSQL for Nominatim
  nominatim-db:
    image: postgis/postgis:15-3.4
    container_name: maps-nominatim-db
    environment:
      POSTGRES_USER: nominatim
      POSTGRES_PASSWORD: nominatim
      POSTGRES_DB: nominatim
    volumes:
      - nominatim-db-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "nominatim"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - maps-net

  # OSRM for route calculations
  osrm:
    image: osrm/osrm-backend:v5.27.1
    container_name: maps-osrm
    command: osrm-routed --algorithm mld /data/maharashtra-latest.osrm
    volumes:
      - osrm-data:/data
    restart: unless-stopped
    networks:
      - maps-net

  # OSRM data preparation (runs once and exits)
  osrm-prepare:
    image: osrm/osrm-backend:v5.27.1
    container_name: maps-osrm-prepare
    volumes:
      - osrm-data:/data
    command: >
      bash -c "
        if [ ! -f /data/maharashtra-latest.osrm ]; then
          wget -O /data/maharashtra-latest.osm.pbf https://download.geofabrik.de/asia/india/maharashtra-latest.osm.pbf &&
          osrm-extract -p /opt/car.lua /data/maharashtra-latest.osm.pbf &&
          osrm-partition /data/maharashtra-latest.osrm &&
          osrm-customize /data/maharashtra-latest.osrm
        fi
      "
    networks:
      - maps-net

  # OpenStreetMap tile server
  tile-server:
    image: overv/openstreetmap-tile-server:2.3.0
    container_name: maps-tile-server
    command: run
    environment:
      THREADS: 4
      UPDATES: disabled
      AUTOVACUUM: off
      CORS_ALLOWED_ORIGIN: "*"
    volumes:
      - tile-data:/var/lib/postgresql/12/main
      - tile-rendered:/var/lib/mod_tile
    restart: unless-stopped
    networks:
      - maps-net

  # Tile server data preparation (runs once and exits)
  tile-prepare:
    image: overv/openstreetmap-tile-server:2.3.0
    container_name: maps-tile-prepare
    command: >
      bash -c "
        if [ ! -f /data/initialized ]; then
          wget -O /data/maharashtra.osm.pbf https://download.geofabrik.de/asia/india/maharashtra-latest.osm.pbf &&
          /usr/local/bin/import_map.sh /data/maharashtra.osm.pbf &&
          touch /data/initialized
        fi
      "
    volumes:
      - tile-data:/var/lib/postgresql/12/main
      - tile-rendered:/var/lib/mod_tile
      - ./data:/data
    networks:
      - maps-net

  # Nginx for serving tiles with proper caching
  nginx:
    image: nginx:1.25-alpine
    container_name: maps-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - tile-cache:/var/cache/nginx
    depends_on:
      - tile-server
      - nominatim
      - osrm
    ports:
      - "8080:80"
    restart: unless-stopped
    networks:
      - maps-net

networks:
  maps-net:
    driver: bridge
    name: maps-net

volumes:
  postgis-data:
  nominatim-data:
  nominatim-db-data:
  osrm-data:
  tile-data:
  tile-rendered:
  tile-cache:

#!/bin/bash

# Script to monitor OpenStreetMap services
# This script monitors the OpenStreetMap services and restarts them if necessary

set -e

# Display banner
echo "====================================="
echo "OpenStreetMap Services Monitor"
echo "====================================="

# Check if the services are running
if ! curl -s http://localhost:8080/health > /dev/null; then
  echo "Error: OpenStreetMap services are not running. Starting services..."
  docker-compose up -d
  sleep 10
fi

# Check the tile server
echo "Checking tile server..."
TILE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/tiles/15/29587/18573.png)
if [ "$TILE_STATUS" -eq 200 ]; then
  echo "✅ Tile server is working"
else
  echo "❌ Tile server is not working. Restarting..."
  docker-compose restart tile-server
  sleep 5
fi

# Check the geocoding service
echo "Checking geocoding service..."
GEOCODE_RESULT=$(curl -s "http://localhost:8080/geocode/search?q=Mumbai&format=json&limit=1")
if [[ $GEOCODE_RESULT == *"lat"* && $GEOCODE_RESULT == *"lon"* ]]; then
  echo "✅ Geocoding service is working"
else
  echo "❌ Geocoding service is not working. Restarting..."
  docker-compose restart nominatim
  sleep 5
fi

# Check the routing service
echo "Checking routing service..."
ROUTE_RESULT=$(curl -s "http://localhost:8080/route/route/v1/driving/72.8777,19.0760;72.9000,19.1000?overview=false")
if [[ $ROUTE_RESULT == *"distance"* && $ROUTE_RESULT == *"duration"* ]]; then
  echo "✅ Routing service is working"
else
  echo "❌ Routing service is not working. Restarting..."
  docker-compose restart osrm
  sleep 5
fi

# Check the Kong API Gateway integration
echo "Checking Kong API Gateway integration..."
if curl -s http://localhost:8000/api/v2/health/maps > /dev/null; then
  echo "✅ Kong API Gateway integration is working"
else
  echo "❌ Kong API Gateway integration is not working. Updating Kong configuration..."
  cd ../gateway
  ./deploy-kong.sh
  sleep 5
fi

# Check the disk space
echo "Checking disk space..."
DISK_USAGE=$(df -h | grep "/var/lib/docker" | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
  echo "⚠️ Disk space is running low: $DISK_USAGE%"
  # Clean up unused Docker resources
  docker system prune -f
else
  echo "✅ Disk space is sufficient: $DISK_USAGE%"
fi

# Check the memory usage
echo "Checking memory usage..."
MEMORY_USAGE=$(free -m | grep "Mem:" | awk '{print int($3/$2 * 100)}')
if [ "$MEMORY_USAGE" -gt 80 ]; then
  echo "⚠️ Memory usage is high: $MEMORY_USAGE%"
else
  echo "✅ Memory usage is normal: $MEMORY_USAGE%"
fi

echo "====================================="
echo "Monitoring complete!"
echo "====================================="

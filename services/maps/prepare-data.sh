#!/bin/bash

# <PERSON>ript to prepare data for OpenStreetMap services
# This script downloads and prepares the map data for Maharashtra, India

set -e

# Create data directory
mkdir -p data

# Display banner
echo "====================================="
echo "OpenStreetMap Data Preparation"
echo "====================================="

# Download Maharashtra OSM data if not already downloaded
if [ ! -f data/maharashtra-latest.osm.pbf ]; then
    echo "Downloading Maharashtra OSM data..."
    wget -O data/maharashtra-latest.osm.pbf https://download.geofabrik.de/asia/india/maharashtra-latest.osm.pbf
else
    echo "Maharashtra OSM data already downloaded."
fi

# Create directories for the services
mkdir -p data/osrm
mkdir -p data/nominatim
mkdir -p data/tiles

# Copy the data to the OSRM directory
echo "Copying data for OSRM..."
cp data/maharashtra-latest.osm.pbf data/osrm/

# Copy the data to the Nominatim directory
echo "Copying data for Nominatim..."
cp data/maharashtra-latest.osm.pbf data/nominatim/

# Copy the data to the tile server directory
echo "Copying data for tile server..."
cp data/maharashtra-latest.osm.pbf data/tiles/

echo "====================================="
echo "Data preparation complete!"
echo "====================================="
echo "You can now start the services with:"
echo "docker-compose up -d"
echo "====================================="

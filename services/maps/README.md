# OpenStreetMap Services for Mumbai Dabbawala Delivery Service

This repository contains the configuration for self-hosted OpenStreetMap services used by the Mumbai Dabbawala Delivery Service.

## Components

1. **OpenStreetMap Tile Server** - For serving map tiles
2. **Nominatim** - For geocoding and address lookup
3. **OSRM** - For route calculations
4. **PostgreSQL with PostGIS** - For spatial data storage
5. **Nginx** - For serving the services with proper caching
6. **Kong API Gateway Integration** - For routing requests to these services

## Deployment

To deploy the OpenStreetMap services, run the following command:

```bash
./deploy.sh
```

This will:
1. Download the OpenStreetMap data for Maharashtra, India
2. Start the services using Docker Compose
3. Configure the Kong API Gateway to route requests to these services

## API Endpoints

### Tile Server

The tile server provides map tiles in the standard XYZ format.

**Endpoint:** `/api/v2/maps/tiles/{z}/{x}/{y}.png`

**Parameters:**
- `z` - Zoom level (0-19)
- `x` - X coordinate
- `y` - Y coordinate

**Example:**
```
GET /api/v2/maps/tiles/15/29587/18573.png
```

### Geocoding (Nominatim)

The geocoding service provides address lookup and reverse geocoding.

**Endpoint:** `/api/v2/maps/geocode/search`

**Parameters:**
- `q` - Query string (address to geocode)
- `format` - Response format (json, xml, etc.)
- `limit` - Maximum number of results
- `addressdetails` - Include address details in the response (0 or 1)

**Example:**
```
GET /api/v2/maps/geocode/search?q=Mumbai&format=json&limit=1&addressdetails=1
```

**Reverse Geocoding Endpoint:** `/api/v2/maps/geocode/reverse`

**Parameters:**
- `lat` - Latitude
- `lon` - Longitude
- `format` - Response format (json, xml, etc.)
- `addressdetails` - Include address details in the response (0 or 1)

**Example:**
```
GET /api/v2/maps/geocode/reverse?lat=19.0760&lon=72.8777&format=json&addressdetails=1
```

### Routing (OSRM)

The routing service provides route calculations between points.

**Endpoint:** `/api/v2/maps/route/route/v1/{profile}/{coordinates}`

**Parameters:**
- `profile` - Routing profile (car, bike, foot)
- `coordinates` - Comma-separated list of longitude,latitude pairs
- `steps` - Include turn-by-turn instructions (true or false)
- `geometries` - Geometry format (polyline, polyline6, geojson)
- `overview` - Include route geometry (simplified, full, false)

**Example:**
```
GET /api/v2/maps/route/route/v1/driving/72.8777,19.0760;72.9000,19.1000?steps=true&geometries=geojson&overview=full
```

**Trip Endpoint (for optimizing routes):** `/api/v2/maps/route/trip/v1/{profile}/{coordinates}`

**Parameters:**
- Same as route endpoint, plus:
- `roundtrip` - Return to the starting point (true or false)
- `source` - Starting point (any, first)
- `destination` - Ending point (any, last)

**Example:**
```
GET /api/v2/maps/route/trip/v1/driving/72.8777,19.0760;72.9000,19.1000;72.9500,19.1500?roundtrip=true&source=first&destination=last
```

## Authentication

All API endpoints are protected by JWT authentication. You need to include a valid JWT token in the `Authorization` header of your requests.

```
Authorization: Bearer <token>
```

## Rate Limiting

The API endpoints are rate-limited to prevent abuse:
- 120 requests per minute
- 2000 requests per hour

## CORS

CORS is enabled for all endpoints, allowing requests from any origin.

## Caching

The services use caching to improve performance:
- Tiles are cached for 7 days
- Geocoding and routing responses are cached for 1 day

## Monitoring

The health of the services can be checked using the health endpoint:

```
GET /api/v2/health/maps
```

## Updating the Map Data

To update the map data, run the following command:

```bash
./update-data.sh
```

This will download the latest OpenStreetMap data for Maharashtra, India and update the services.

## Troubleshooting

If you encounter issues with the services, check the logs:

```bash
docker-compose logs -f
```

You can also check the health of individual services:

```bash
curl http://localhost:8080/health
```

#!/bin/bash

# Script to deploy OpenStreetMap services
# This script deploys the OpenStreetMap services using Docker Compose

set -e

# Display banner
echo "====================================="
echo "OpenStreetMap Services Deployment"
echo "====================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo "Error: Docker is not running. Please start Docker and try again."
  exit 1
fi

# Check if Docker Compose is installed
if ! docker-compose --version > /dev/null 2>&1; then
  echo "Error: Docker Compose is not installed. Please install Docker Compose and try again."
  exit 1
fi

# Create directories
mkdir -p data
mkdir -p nginx/conf.d

# Make sure the prepare-data.sh script is executable
chmod +x prepare-data.sh

# Run the prepare-data.sh script
./prepare-data.sh

# Deploy the services
echo "Deploying OpenStreetMap services..."
docker-compose up -d

# Wait for the services to be ready
echo "Waiting for services to be ready..."
until curl -s http://localhost:8080/health > /dev/null; do
  echo "Services are not ready yet. Waiting..."
  sleep 5
done

echo "====================================="
echo "OpenStreetMap services deployed successfully!"
echo "Tile Server: http://localhost:8080/tiles/"
echo "Nominatim: http://localhost:8080/geocode/"
echo "OSRM: http://localhost:8080/route/"
echo "Health Check: http://localhost:8080/health"
echo "====================================="

# Update Kong API Gateway configuration
echo "Updating Kong API Gateway configuration..."
cp ../gateway/kong/maps-service.yaml ../gateway/kong/

# Restart Kong API Gateway
echo "Restarting Kong API Gateway..."
cd ../gateway
./deploy-kong.sh

echo "====================================="
echo "Deployment complete!"
echo "====================================="

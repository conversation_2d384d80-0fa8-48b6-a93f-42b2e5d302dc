#!/bin/bash

# Script to uninstall OpenStreetMap services
# This script stops and removes the OpenStreetMap services

set -e

# Display banner
echo "====================================="
echo "OpenStreetMap Services Uninstallation"
echo "====================================="

# Confirm uninstallation
read -p "Are you sure you want to uninstall the OpenStreetMap services? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "Uninstallation cancelled."
  exit 1
fi

# Stop and remove the services
echo "Stopping and removing services..."
docker-compose down

# Remove the volumes
read -p "Do you want to remove the data volumes? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo "Removing data volumes..."
  docker volume rm maps-postgis-data maps-nominatim-data maps-nominatim-db-data maps-osrm-data maps-tile-data maps-tile-rendered maps-tile-cache
fi

# Remove the Kong API Gateway configuration
echo "Removing Kong API Gateway configuration..."
rm -f ../gateway/kong/maps-service.yaml

# Restart Kong API Gateway
echo "Restarting Kong API Gateway..."
cd ../gateway
./deploy-kong.sh

# Remove the cron jobs
echo "Removing cron jobs..."
crontab -l | grep -v "maps/monitor.sh\|maps/update-data.sh" | crontab -

echo "====================================="
echo "Uninstallation complete!"
echo "====================================="

#!/bin/bash

# <PERSON>ript to update OpenStreetMap data
# This script updates the OpenStreetMap data for Maharashtra, India

set -e

# Display banner
echo "====================================="
echo "OpenStreetMap Data Update"
echo "====================================="

# Create data directory
mkdir -p data

# Download the latest Maharashtra OSM data
echo "Downloading the latest Maharashtra OSM data..."
wget -O data/maharashtra-latest.osm.pbf https://download.geofabrik.de/asia/india/maharashtra-latest.osm.pbf

# Update OSRM data
echo "Updating OSRM data..."
docker-compose run --rm osrm-prepare

# Update Nominatim data
echo "Updating Nominatim data..."
docker-compose exec nominatim bash -c "nominatim replication --init"
docker-compose exec nominatim bash -c "nominatim replication --update"

# Update tile server data
echo "Updating tile server data..."
docker-compose exec tile-server bash -c "rm -rf /var/lib/mod_tile/*"
docker-compose exec tile-server bash -c "service renderd restart"

echo "====================================="
echo "Data update complete!"
echo "====================================="

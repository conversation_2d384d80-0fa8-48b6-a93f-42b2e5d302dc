#!/bin/bash

# Script to install cron jobs for OpenStreetMap services
# This script installs the cron jobs for monitoring and updating the OpenStreetMap services

set -e

# Display banner
echo "====================================="
echo "OpenStreetMap Cron Jobs Installation"
echo "====================================="

# Get the absolute path to the scripts
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)
MONITOR_SCRIPT="$SCRIPT_DIR/monitor.sh"
UPDATE_SCRIPT="$SCRIPT_DIR/update-data.sh"

# Make sure the scripts are executable
chmod +x "$MONITOR_SCRIPT"
chmod +x "$UPDATE_SCRIPT"

# Create a temporary crontab file
TMP_CRONTAB=$(mktemp)

# Add the cron jobs to the temporary file
cat > "$TMP_CRONTAB" << EOF
# Cron jobs for OpenStreetMap services

# Run the monitor script every 5 minutes
*/5 * * * * $MONITOR_SCRIPT >> /var/log/maps-monitor.log 2>&1

# Update the map data once a week (Sunday at 3:00 AM)
0 3 * * 0 $UPDATE_SCRIPT >> /var/log/maps-update.log 2>&1

# Clean up logs once a month
0 0 1 * * find /var/log -name "maps-*.log" -mtime +30 -delete
EOF

# Install the crontab
crontab "$TMP_CRONTAB"

# Remove the temporary file
rm "$TMP_CRONTAB"

echo "====================================="
echo "Cron jobs installed successfully!"
echo "====================================="
echo "Monitor script: $MONITOR_SCRIPT"
echo "Update script: $UPDATE_SCRIPT"
echo "====================================="

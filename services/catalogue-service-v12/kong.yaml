_format_version: "3.0"
_transform: true

services:
  - name: catalogue-service
    url: http://catalogue-service-v12:8000
    routes:
      - name: catalogue-service-route
        paths:
          - /api/v2/catalogue
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
          run_on_preflight: true
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service-Name:catalogue-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service-Version:1.0.0
      - name: http-log
        config:
          http_endpoint: http://logging-service:8000/api/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          headers:
            X-Service-Name: catalogue-service
          
upstreams:
  - name: catalogue-service-upstream
    targets:
      - target: catalogue-service-v12:8000
        weight: 100
    healthchecks:
      active:
        concurrency: 10
        healthy:
          http_statuses:
            - 200
            - 302
          interval: 5
          successes: 5
        http_path: /api/health
        timeout: 1
        unhealthy:
          http_failures: 5
          http_statuses:
            - 429
            - 404
            - 500
            - 501
            - 502
            - 503
            - 504
            - 505
          interval: 5
          tcp_failures: 5
          timeouts: 5
      passive:
        healthy:
          http_statuses:
            - 200
            - 201
            - 202
            - 203
            - 204
            - 205
            - 206
            - 207
            - 208
            - 226
            - 300
            - 301
            - 302
            - 303
            - 304
            - 305
            - 306
            - 307
            - 308
          successes: 5
        unhealthy:
          http_failures: 5
          http_statuses:
            - 429
            - 500
            - 503
          tcp_failures: 5
          timeouts: 5

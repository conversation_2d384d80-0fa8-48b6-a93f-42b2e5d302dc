<?php

namespace App\Events;

use App\Models\PlanMeal;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PlanMealUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The plan meal instance.
     *
     * @var PlanMeal
     */
    public $planMeal;

    /**
     * Create a new event instance.
     *
     * @param PlanMeal $planMeal
     * @return void
     */
    public function __construct(PlanMeal $planMeal)
    {
        $this->planMeal = $planMeal;
    }
}

<?php

namespace App\Events;

use App\Jobs\PublishThemeEventJob;
use App\Models\Theme;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ThemeEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The event type.
     */
    protected string $eventType;

    /**
     * The theme instance.
     */
    protected Theme $theme;

    /**
     * Create a new event instance.
     */
    public function __construct(string $eventType, Theme $theme)
    {
        $this->eventType = $eventType;
        $this->theme = $theme;
    }

    /**
     * Publish the event to RabbitMQ asynchronously.
     *
     * @return void
     */
    public function publish(): void
    {
        try {
            // Dispatch job to publish event asynchronously
            PublishThemeEventJob::dispatch($this->eventType, $this->theme);

            Log::info('Theme event queued for publishing', [
                'event' => "theme.{$this->eventType}",
                'theme_id' => $this->theme->id,
                'theme_name' => $this->theme->name,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue theme event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event' => "theme.{$this->eventType}",
                'theme_id' => $this->theme->id,
                'theme_name' => $this->theme->name,
            ]);
        }
    }

    /**
     * Create and publish a theme created event.
     *
     * @param Theme $theme
     * @return void
     */
    public static function created(Theme $theme): void
    {
        $event = new static('created', $theme);
        $event->publish();
    }

    /**
     * Create and publish a theme updated event.
     *
     * @param Theme $theme
     * @return void
     */
    public static function updated(Theme $theme): void
    {
        $event = new static('updated', $theme);
        $event->publish();
    }

    /**
     * Create and publish a theme deleted event.
     *
     * @param Theme $theme
     * @return void
     */
    public static function deleted(Theme $theme): void
    {
        $event = new static('deleted', $theme);
        $event->publish();
    }

    /**
     * Create and publish a theme activated event.
     *
     * @param Theme $theme
     * @return void
     */
    public static function activated(Theme $theme): void
    {
        $event = new static('activated', $theme);
        $event->publish();
    }
}

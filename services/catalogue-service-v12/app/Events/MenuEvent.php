<?php

namespace App\Events;

use App\Jobs\PublishMenuEventJob;
use App\Models\Menu;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class MenuEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The event type.
     */
    protected string $eventType;

    /**
     * The menu instance.
     */
    protected Menu $menu;

    /**
     * Create a new event instance.
     */
    public function __construct(string $eventType, Menu $menu)
    {
        $this->eventType = $eventType;
        $this->menu = $menu;
    }

    /**
     * Publish the event to RabbitMQ asynchronously.
     *
     * @return void
     */
    public function publish(): void
    {
        try {
            // Dispatch job to publish event asynchronously
            PublishMenuEventJob::dispatch($this->eventType, $this->menu);

            Log::info('Menu event queued for publishing', [
                'event' => "menu.{$this->eventType}",
                'menu_id' => $this->menu->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue menu event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event' => "menu.{$this->eventType}",
                'menu_id' => $this->menu->id,
            ]);
        }
    }

    /**
     * Create and publish a menu created event.
     *
     * @param Menu $menu
     * @return void
     */
    public static function created(Menu $menu): void
    {
        $event = new static('created', $menu);
        $event->publish();
    }

    /**
     * Create and publish a menu updated event.
     *
     * @param Menu $menu
     * @return void
     */
    public static function updated(Menu $menu): void
    {
        $event = new static('updated', $menu);
        $event->publish();
    }

    /**
     * Create and publish a menu deleted event.
     *
     * @param Menu $menu
     * @return void
     */
    public static function deleted(Menu $menu): void
    {
        $event = new static('deleted', $menu);
        $event->publish();
    }
}

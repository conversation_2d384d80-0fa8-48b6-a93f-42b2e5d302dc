<?php

namespace App\Listeners;

use App\Models\Theme;
use App\Services\ThemeService;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Message\AMQPMessage;

class ThemeEventListener
{
    /**
     * The theme service instance.
     */
    protected ThemeService $themeService;

    /**
     * Create a new listener instance.
     */
    public function __construct(ThemeService $themeService)
    {
        $this->themeService = $themeService;
    }

    /**
     * Handle the event.
     *
     * @param AMQPMessage $message
     * @return void
     */
    public function handle(AMQPMessage $message): void
    {
        try {
            $body = json_decode($message->body, true);
            
            if (!isset($body['event']) || !isset($body['data'])) {
                Log::warning('Invalid message format', ['body' => $message->body]);
                $message->ack();
                return;
            }
            
            $event = $body['event'];
            $data = $body['data'];
            
            Log::info('Received theme event', ['event' => $event, 'data' => $data]);
            
            switch ($event) {
                case 'theme.created':
                    $this->handleThemeCreated($data);
                    break;
                case 'theme.updated':
                    $this->handleThemeUpdated($data);
                    break;
                case 'theme.deleted':
                    $this->handleThemeDeleted($data);
                    break;
                case 'theme.activated':
                    $this->handleThemeActivated($data);
                    break;
                default:
                    Log::warning('Unknown event type', ['event' => $event]);
                    break;
            }
            
            $message->ack();
        } catch (\Exception $e) {
            Log::error('Error processing theme event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'body' => $message->body,
            ]);
            
            // Reject the message and requeue it
            $message->reject(true);
        }
    }

    /**
     * Handle theme created event.
     *
     * @param array $data
     * @return void
     */
    private function handleThemeCreated(array $data): void
    {
        // Check if the theme already exists
        $existingTheme = Theme::where('id', $data['id'])->orWhere('name', $data['name'])->first();
        
        if ($existingTheme) {
            Log::info('Theme already exists', ['id' => $data['id'], 'name' => $data['name']]);
            return;
        }
        
        // Create the theme
        $this->themeService->createTheme($data);
        
        Log::info('Theme created from event', ['id' => $data['id'], 'name' => $data['name']]);
    }

    /**
     * Handle theme updated event.
     *
     * @param array $data
     * @return void
     */
    private function handleThemeUpdated(array $data): void
    {
        // Check if the theme exists
        $existingTheme = Theme::where('id', $data['id'])->first();
        
        if (!$existingTheme) {
            Log::warning('Theme not found for update', ['id' => $data['id']]);
            return;
        }
        
        // Update the theme
        $this->themeService->updateTheme($data['id'], $data);
        
        Log::info('Theme updated from event', ['id' => $data['id'], 'name' => $data['name']]);
    }

    /**
     * Handle theme deleted event.
     *
     * @param array $data
     * @return void
     */
    private function handleThemeDeleted(array $data): void
    {
        // Check if the theme exists
        $existingTheme = Theme::where('id', $data['id'])->first();
        
        if (!$existingTheme) {
            Log::warning('Theme not found for deletion', ['id' => $data['id']]);
            return;
        }
        
        // Delete the theme
        $this->themeService->deleteTheme($data['id']);
        
        Log::info('Theme deleted from event', ['id' => $data['id']]);
    }

    /**
     * Handle theme activated event.
     *
     * @param array $data
     * @return void
     */
    private function handleThemeActivated(array $data): void
    {
        // Check if the theme exists
        $existingTheme = Theme::where('id', $data['id'])->first();
        
        if (!$existingTheme) {
            Log::warning('Theme not found for activation', ['id' => $data['id']]);
            return;
        }
        
        // Set the theme as active
        $this->themeService->setActiveTheme($data['id']);
        
        Log::info('Theme activated from event', ['id' => $data['id'], 'name' => $data['name']]);
    }
}

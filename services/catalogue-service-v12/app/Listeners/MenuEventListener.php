<?php

namespace App\Listeners;

use App\Models\Menu;
use App\Services\MenuService;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Message\AMQPMessage;

class MenuEventListener
{
    /**
     * The menu service instance.
     */
    protected MenuService $menuService;

    /**
     * Create a new listener instance.
     */
    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Handle the event.
     *
     * @param AMQPMessage $message
     * @return void
     */
    public function handle(AMQPMessage $message): void
    {
        try {
            $body = json_decode($message->body, true);
            
            if (!isset($body['event']) || !isset($body['data'])) {
                Log::warning('Invalid message format', ['body' => $message->body]);
                $message->ack();
                return;
            }
            
            $event = $body['event'];
            $data = $body['data'];
            
            Log::info('Received menu event', ['event' => $event, 'data' => $data]);
            
            switch ($event) {
                case 'menu.created':
                    $this->handleMenuCreated($data);
                    break;
                case 'menu.updated':
                    $this->handleMenuUpdated($data);
                    break;
                case 'menu.deleted':
                    $this->handleMenuDeleted($data);
                    break;
                case 'kitchen.status_changed':
                    $this->handleKitchenStatusChanged($data);
                    break;
                default:
                    Log::warning('Unknown event type', ['event' => $event]);
                    break;
            }
            
            $message->ack();
        } catch (\Exception $e) {
            Log::error('Error processing menu event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'body' => $message->body,
            ]);
            
            // Reject the message and requeue it
            $message->reject(true);
        }
    }

    /**
     * Handle menu created event.
     *
     * @param array $data
     * @return void
     */
    private function handleMenuCreated(array $data): void
    {
        // Check if the menu already exists
        $existingMenu = Menu::where('id', $data['id'])->first();
        
        if ($existingMenu) {
            Log::info('Menu already exists', ['id' => $data['id']]);
            return;
        }
        
        // Create the menu
        $this->menuService->createMenu($data);
        
        Log::info('Menu created from event', ['id' => $data['id']]);
    }

    /**
     * Handle menu updated event.
     *
     * @param array $data
     * @return void
     */
    private function handleMenuUpdated(array $data): void
    {
        // Check if the menu exists
        $existingMenu = Menu::where('id', $data['id'])->first();
        
        if (!$existingMenu) {
            Log::warning('Menu not found for update', ['id' => $data['id']]);
            return;
        }
        
        // Update the menu
        $this->menuService->updateMenu($data['id'], $data);
        
        Log::info('Menu updated from event', ['id' => $data['id']]);
    }

    /**
     * Handle menu deleted event.
     *
     * @param array $data
     * @return void
     */
    private function handleMenuDeleted(array $data): void
    {
        // Check if the menu exists
        $existingMenu = Menu::where('id', $data['id'])->first();
        
        if (!$existingMenu) {
            Log::warning('Menu not found for deletion', ['id' => $data['id']]);
            return;
        }
        
        // Delete the menu
        $this->menuService->deleteMenu($data['id']);
        
        Log::info('Menu deleted from event', ['id' => $data['id']]);
    }

    /**
     * Handle kitchen status changed event.
     *
     * @param array $data
     * @return void
     */
    private function handleKitchenStatusChanged(array $data): void
    {
        // Check if the kitchen is active
        $isActive = $data['status'] ?? false;
        $kitchenId = $data['id'] ?? null;
        
        if (!$kitchenId) {
            Log::warning('Kitchen ID not provided', ['data' => $data]);
            return;
        }
        
        // Update all menus for this kitchen
        $menus = Menu::where('kitchen_id', $kitchenId)->get();
        
        foreach ($menus as $menu) {
            $menu->status = $isActive;
            $menu->save();
        }
        
        Log::info('Updated menu status for kitchen', [
            'kitchen_id' => $kitchenId,
            'status' => $isActive,
            'menu_count' => $menus->count(),
        ]);
    }
}

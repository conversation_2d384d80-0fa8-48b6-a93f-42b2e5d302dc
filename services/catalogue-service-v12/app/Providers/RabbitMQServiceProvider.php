<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class RabbitMQServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(AMQPStreamConnection::class, function ($app) {
            return new AMQPStreamConnection(
                config('rabbitmq.host'),
                config('rabbitmq.port'),
                config('rabbitmq.user'),
                config('rabbitmq.password'),
                config('rabbitmq.vhost')
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register model event listeners
        $this->registerModelEventListeners();
    }

    /**
     * Register model event listeners.
     *
     * @return void
     */
    protected function registerModelEventListeners(): void
    {
        // Menu events
        \App\Models\Menu::created(function ($menu) {
            \App\Events\MenuEvent::created($menu);
        });

        \App\Models\Menu::updated(function ($menu) {
            \App\Events\MenuEvent::updated($menu);
        });

        \App\Models\Menu::deleted(function ($menu) {
            \App\Events\MenuEvent::deleted($menu);
        });

        // Theme events
        \App\Models\Theme::created(function ($theme) {
            \App\Events\ThemeEvent::created($theme);
        });

        \App\Models\Theme::updated(function ($theme) {
            \App\Events\ThemeEvent::updated($theme);
        });

        \App\Models\Theme::deleted(function ($theme) {
            \App\Events\ThemeEvent::deleted($theme);
        });
    }
}

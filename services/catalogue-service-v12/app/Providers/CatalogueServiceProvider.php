<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class CatalogueServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(\App\Repositories\ProductRepository::class);
        $this->app->singleton(\App\Repositories\MenuRepository::class);
        $this->app->singleton(\App\Repositories\CartRepository::class);
        $this->app->singleton(\App\Repositories\PlanMealRepository::class);
        $this->app->singleton(\App\Repositories\ThemeRepository::class);

        $this->app->singleton(\App\Services\CatalogueService::class);
        $this->app->singleton(\App\Services\MenuService::class);
        $this->app->singleton(\App\Services\CartService::class);
        $this->app->singleton(\App\Services\PlanMealService::class);
        $this->app->singleton(\App\Services\ThemeService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

<?php

namespace App\DTOs;

class PlanMealItemDTO
{
    public int $productId;
    public string $deliveryDate;
    public int $quantity;
    public ?float $unitPrice;
    public ?float $totalPrice;

    public function __construct(
        int $productId,
        string $deliveryDate,
        int $quantity = 1,
        ?float $unitPrice = null,
        ?float $totalPrice = null
    ) {
        $this->productId = $productId;
        $this->deliveryDate = $deliveryDate;
        $this->quantity = $quantity;
        $this->unitPrice = $unitPrice;
        $this->totalPrice = $totalPrice;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['product_id'],
            $data['delivery_date'],
            $data['quantity'] ?? 1,
            $data['unit_price'] ?? null,
            $data['total_price'] ?? null
        );
    }
}

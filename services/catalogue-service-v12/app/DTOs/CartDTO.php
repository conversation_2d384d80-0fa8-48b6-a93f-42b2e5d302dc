<?php

namespace App\DTOs;

class CartDTO
{
    public ?int $customerId;
    public string $sessionId;
    public int $kitchenId;
    public float $totalAmount;
    public float $taxAmount;
    public float $discountAmount;
    public float $deliveryCharges;
    public float $netAmount;
    public ?string $promoCode;
    public string $status;
    public array $items;

    public function __construct(
        ?int $customerId,
        string $sessionId,
        int $kitchenId,
        float $totalAmount = 0,
        float $taxAmount = 0,
        float $discountAmount = 0,
        float $deliveryCharges = 0,
        float $netAmount = 0,
        ?string $promoCode = null,
        string $status = 'active',
        array $items = []
    ) {
        $this->customerId = $customerId;
        $this->sessionId = $sessionId;
        $this->kitchenId = $kitchenId;
        $this->totalAmount = $totalAmount;
        $this->taxAmount = $taxAmount;
        $this->discountAmount = $discountAmount;
        $this->deliveryCharges = $deliveryCharges;
        $this->netAmount = $netAmount;
        $this->promoCode = $promoCode;
        $this->status = $status;
        $this->items = $items;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['customer_id'] ?? null,
            $data['session_id'],
            $data['kitchen_id'],
            $data['total_amount'] ?? 0,
            $data['tax_amount'] ?? 0,
            $data['discount_amount'] ?? 0,
            $data['delivery_charges'] ?? 0,
            $data['net_amount'] ?? 0,
            $data['promo_code'] ?? null,
            $data['status'] ?? 'active',
            $data['items'] ?? []
        );
    }
}
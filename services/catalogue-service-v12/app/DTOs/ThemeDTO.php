<?php

declare(strict_types=1);

namespace App\DTOs;

use JsonSerializable;

/**
 * Theme Data Transfer Object
 *
 * This DTO encapsulates theme data for API responses and internal data transfer.
 * It provides validation, serialization, and transformation capabilities.
 */
class ThemeDTO implements JsonSerializable
{
    /**
     * Theme ID
     */
    public ?int $id;

    /**
     * Theme name
     */
    public string $name;

    /**
     * Theme description
     */
    public ?string $description;

    /**
     * Theme configuration array
     */
    public ?array $config;

    /**
     * Whether the theme is active
     */
    public bool $isActive;

    /**
     * Theme creation timestamp
     */
    public ?string $createdAt;

    /**
     * Theme update timestamp
     */
    public ?string $updatedAt;

    /**
     * Create a new ThemeDTO instance
     */
    public function __construct(
        ?int $id = null,
        string $name = '',
        ?string $description = null,
        ?array $config = null,
        bool $isActive = false,
        ?string $createdAt = null,
        ?string $updatedAt = null
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->description = $description;
        $this->config = $config;
        $this->isActive = $isActive;
        $this->createdAt = $createdAt;
        $this->updatedAt = $updatedAt;
    }

    /**
     * Create ThemeDTO from array data
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'] ?? null,
            $data['name'] ?? '',
            $data['description'] ?? null,
            $data['config'] ?? null,
            $data['is_active'] ?? false,
            $data['created_at'] ?? null,
            $data['updated_at'] ?? null
        );
    }

    /**
     * Create ThemeDTO from Theme model
     */
    public static function fromModel($theme): self
    {
        return new self(
            $theme->id,
            $theme->name,
            $theme->description,
            $theme->config,
            $theme->is_active,
            $theme->created_at?->toISOString(),
            $theme->updated_at?->toISOString()
        );
    }

    /**
     * Convert to array for database operations
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'config' => $this->config,
            'is_active' => $this->isActive,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }

    /**
     * Convert to array for API responses
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'config' => $this->config,
            'is_active' => $this->isActive,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }

    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return $this->toApiArray();
    }

    /**
     * Validate theme data
     */
    public function validate(): array
    {
        $errors = [];

        if (empty($this->name)) {
            $errors['name'] = 'Theme name is required';
        } elseif (strlen($this->name) > 100) {
            $errors['name'] = 'Theme name must not exceed 100 characters';
        }

        if ($this->description && strlen($this->description) > 500) {
            $errors['description'] = 'Theme description must not exceed 500 characters';
        }

        if ($this->config && !is_array($this->config)) {
            $errors['config'] = 'Theme config must be an array';
        }

        return $errors;
    }

    /**
     * Check if theme data is valid
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }

    /**
     * Get theme configuration value by key
     */
    public function getConfigValue(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * Set theme configuration value
     */
    public function setConfigValue(string $key, $value): void
    {
        if ($this->config === null) {
            $this->config = [];
        }
        $this->config[$key] = $value;
    }

    /**
     * Check if theme has specific configuration
     */
    public function hasConfig(string $key): bool
    {
        return isset($this->config[$key]);
    }

    /**
     * Get theme display name (name with fallback)
     */
    public function getDisplayName(): string
    {
        return $this->name ?: 'Unnamed Theme';
    }

    /**
     * Check if theme is the default theme
     */
    public function isDefault(): bool
    {
        return $this->getConfigValue('is_default', false);
    }

    /**
     * Get theme preview URL if available
     */
    public function getPreviewUrl(): ?string
    {
        return $this->getConfigValue('preview_url');
    }

    /**
     * Get theme CSS variables
     */
    public function getCssVariables(): array
    {
        return $this->getConfigValue('css_variables', []);
    }

    /**
     * Get theme color scheme
     */
    public function getColorScheme(): string
    {
        return $this->getConfigValue('color_scheme', 'light');
    }

    /**
     * Check if theme supports dark mode
     */
    public function supportsDarkMode(): bool
    {
        return $this->getConfigValue('supports_dark_mode', false);
    }
}
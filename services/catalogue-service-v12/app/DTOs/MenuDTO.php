<?php

namespace App\DTOs;

class MenuDTO
{
    public string $name;
    public string $type;
    public int $kitchenId;
    public ?string $cutOffTime;
    public ?int $cutOffInterval;
    public bool $status;

    public function __construct(
        string $name,
        string $type,
        int $kitchenId,
        ?string $cutOffTime = null,
        ?int $cutOffInterval = null,
        bool $status = true
    ) {
        $this->name = $name;
        $this->type = $type;
        $this->kitchenId = $kitchenId;
        $this->cutOffTime = $cutOffTime;
        $this->cutOffInterval = $cutOffInterval;
        $this->status = $status;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['name'],
            $data['type'],
            $data['kitchen_id'],
            $data['cut_off_time'] ?? null,
            $data['cut_off_interval'] ?? null,
            $data['status'] ?? true
        );
    }
}
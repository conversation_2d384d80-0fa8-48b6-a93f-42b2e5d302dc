<?php

namespace App\DTOs;

class PlanMealDTO
{
    public int $customerId;
    public int $kitchenId;
    public string $menuType;
    public string $startDate;
    public string $endDate;
    public int $quantity;
    public float $negotiatedPrice;
    public float $serviceCharges;
    public float $tax;
    public float $discount;
    public float $totalBillAmount;
    public ?string $promoCode;
    public ?array $appliedTaxes;
    public string $status;
    public array $items;

    public function __construct(
        int $customerId,
        int $kitchenId,
        string $menuType,
        string $startDate,
        string $endDate,
        int $quantity = 1,
        float $negotiatedPrice = 0,
        float $serviceCharges = 0,
        float $tax = 0,
        float $discount = 0,
        float $totalBillAmount = 0,
        ?string $promoCode = null,
        ?array $appliedTaxes = null,
        string $status = 'active',
        array $items = []
    ) {
        $this->customerId = $customerId;
        $this->kitchenId = $kitchenId;
        $this->menuType = $menuType;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->quantity = $quantity;
        $this->negotiatedPrice = $negotiatedPrice;
        $this->serviceCharges = $serviceCharges;
        $this->tax = $tax;
        $this->discount = $discount;
        $this->totalBillAmount = $totalBillAmount;
        $this->promoCode = $promoCode;
        $this->appliedTaxes = $appliedTaxes;
        $this->status = $status;
        $this->items = $items;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['customer_id'],
            $data['kitchen_id'],
            $data['menu_type'],
            $data['start_date'],
            $data['end_date'],
            $data['quantity'] ?? 1,
            $data['negotiated_price'] ?? 0,
            $data['service_charges'] ?? 0,
            $data['tax'] ?? 0,
            $data['discount'] ?? 0,
            $data['total_bill_amount'] ?? 0,
            $data['promo_code'] ?? null,
            $data['applied_taxes'] ?? null,
            $data['status'] ?? 'active',
            $data['items'] ?? []
        );
    }
}
<?php

namespace App\DTOs;

class CartItemDTO
{
    public int $productId;
    public ?int $menuId;
    public int $quantity;
    public ?float $unitPrice;
    public ?float $totalPrice;
    public ?string $deliveryDate;
    public ?string $deliveryTime;
    public ?array $customizations;

    public function __construct(
        int $productId,
        int $quantity,
        ?int $menuId = null,
        ?float $unitPrice = null,
        ?float $totalPrice = null,
        ?string $deliveryDate = null,
        ?string $deliveryTime = null,
        ?array $customizations = null
    ) {
        $this->productId = $productId;
        $this->menuId = $menuId;
        $this->quantity = $quantity;
        $this->unitPrice = $unitPrice;
        $this->totalPrice = $totalPrice;
        $this->deliveryDate = $deliveryDate;
        $this->deliveryTime = $deliveryTime;
        $this->customizations = $customizations;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['product_id'],
            $data['quantity'] ?? 1,
            $data['menu_id'] ?? null,
            $data['unit_price'] ?? null,
            $data['total_price'] ?? null,
            $data['delivery_date'] ?? null,
            $data['delivery_time'] ?? null,
            $data['customizations'] ?? null
        );
    }
}

<?php

namespace App\DTOs;

class ProductDTO
{
    public string $name;
    public string $description;
    public float $price;
    public int $kitchenId;
    public ?int $categoryId;
    public ?int $menuId;
    public ?string $foodType;
    public ?string $imageUrl;
    public ?int $sequence;
    public bool $isExtra;
    public bool $status;
    public ?array $nutritionalInfo;
    public ?array $ingredients;
    public ?array $allergens;

    public function __construct(
        string $name,
        string $description,
        float $price,
        int $kitchenId,
        ?int $categoryId = null,
        ?int $menuId = null,
        ?string $foodType = null,
        ?string $imageUrl = null,
        ?int $sequence = null,
        bool $isExtra = false,
        bool $status = true,
        ?array $nutritionalInfo = null,
        ?array $ingredients = null,
        ?array $allergens = null
    ) {
        $this->name = $name;
        $this->description = $description;
        $this->price = $price;
        $this->kitchenId = $kitchenId;
        $this->categoryId = $categoryId;
        $this->menuId = $menuId;
        $this->foodType = $foodType;
        $this->imageUrl = $imageUrl;
        $this->sequence = $sequence;
        $this->isExtra = $isExtra;
        $this->status = $status;
        $this->nutritionalInfo = $nutritionalInfo;
        $this->ingredients = $ingredients;
        $this->allergens = $allergens;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['name'],
            $data['description'],
            $data['price'],
            $data['kitchen_id'],
            $data['category_id'] ?? null,
            $data['menu_id'] ?? null,
            $data['food_type'] ?? null,
            $data['image_url'] ?? null,
            $data['sequence'] ?? null,
            $data['is_extra'] ?? false,
            $data['status'] ?? true,
            $data['nutritional_info'] ?? null,
            $data['ingredients'] ?? null,
            $data['allergens'] ?? null
        );
    }
}
<?php

namespace App\Jobs;

use App\Models\Menu;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class PublishMenuEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    /**
     * The event type.
     */
    protected string $eventType;
    
    /**
     * The menu instance.
     */
    protected Menu $menu;
    
    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;
    
    /**
     * The number of seconds to wait before retrying the job.
     */
    public $backoff = [60, 300, 600]; // 1 minute, 5 minutes, 10 minutes
    
    /**
     * Create a new job instance.
     *
     * @param string $eventType
     * @param Menu $menu
     */
    public function __construct(string $eventType, Menu $menu)
    {
        $this->eventType = $eventType;
        $this->menu = $menu;
    }
    
    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get RabbitMQ configuration
            $host = config('rabbitmq.host', 'localhost');
            $port = config('rabbitmq.port', 5672);
            $user = config('rabbitmq.user', 'guest');
            $password = config('rabbitmq.password', 'guest');
            $vhost = config('rabbitmq.vhost', '/');
            
            // Create connection
            $connection = new AMQPStreamConnection($host, $port, $user, $password, $vhost);
            $channel = $connection->channel();
            
            // Declare exchange
            $exchangeName = config('rabbitmq.exchanges.catalogue_events.name', 'catalogue_events');
            $exchangeType = config('rabbitmq.exchanges.catalogue_events.type', 'topic');
            $channel->exchange_declare($exchangeName, $exchangeType, false, true, false);
            
            // Prepare message data
            $data = [
                'event' => "menu.{$this->eventType}",
                'timestamp' => now()->toIso8601String(),
                'data' => $this->menu->toArray(),
            ];
            
            // Create message
            $message = new AMQPMessage(
                json_encode($data),
                [
                    'content_type' => 'application/json',
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT,
                ]
            );
            
            // Publish message
            $routingKey = "menu.{$this->eventType}";
            $channel->basic_publish($message, $exchangeName, $routingKey);
            
            // Close connection
            $channel->close();
            $connection->close();
            
            Log::info("Menu event published successfully", [
                'event_type' => $this->eventType,
                'menu_id' => $this->menu->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish menu event', [
                'error' => $e->getMessage(),
                'event_type' => $this->eventType,
                'menu_id' => $this->menu->id,
            ]);
            
            throw $e;
        }
    }
}

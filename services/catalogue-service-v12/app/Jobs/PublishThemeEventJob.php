<?php

namespace App\Jobs;

use App\Models\Theme;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class PublishThemeEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    /**
     * The event type.
     */
    protected string $eventType;
    
    /**
     * The theme instance.
     */
    protected Theme $theme;
    
    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;
    
    /**
     * The number of seconds to wait before retrying the job.
     */
    public $backoff = [60, 300, 600]; // 1 minute, 5 minutes, 10 minutes
    
    /**
     * Create a new job instance.
     *
     * @param string $eventType
     * @param Theme $theme
     */
    public function __construct(string $eventType, Theme $theme)
    {
        $this->eventType = $eventType;
        $this->theme = $theme;
    }
    
    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get RabbitMQ configuration
            $host = config('rabbitmq.host', 'localhost');
            $port = config('rabbitmq.port', 5672);
            $user = config('rabbitmq.user', 'guest');
            $password = config('rabbitmq.password', 'guest');
            $vhost = config('rabbitmq.vhost', '/');
            
            // Create connection
            $connection = new AMQPStreamConnection($host, $port, $user, $password, $vhost);
            $channel = $connection->channel();
            
            // Declare exchange
            $exchangeName = config('rabbitmq.exchanges.catalogue_events.name', 'catalogue_events');
            $exchangeType = config('rabbitmq.exchanges.catalogue_events.type', 'topic');
            $channel->exchange_declare($exchangeName, $exchangeType, false, true, false);
            
            // Prepare message data
            $data = [
                'event' => "theme.{$this->eventType}",
                'timestamp' => now()->toIso8601String(),
                'data' => $this->theme->toArray(),
            ];
            
            // Create message
            $message = new AMQPMessage(
                json_encode($data),
                [
                    'content_type' => 'application/json',
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT,
                ]
            );
            
            // Publish message
            $routingKey = "theme.{$this->eventType}";
            $channel->basic_publish($message, $exchangeName, $routingKey);
            
            // Close connection
            $channel->close();
            $connection->close();
            
            Log::info("Theme event published successfully", [
                'event_type' => $this->eventType,
                'theme_id' => $this->theme->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish theme event', [
                'error' => $e->getMessage(),
                'event_type' => $this->eventType,
                'theme_id' => $this->theme->id,
            ]);
            
            throw $e;
        }
    }
}

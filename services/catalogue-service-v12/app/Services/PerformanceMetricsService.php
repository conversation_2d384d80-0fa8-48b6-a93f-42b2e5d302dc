<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class PerformanceMetricsService
{
    /**
     * The timers array.
     *
     * @var array
     */
    private array $timers = [];
    
    /**
     * Start a timer for an operation.
     *
     * @param string $operation The operation name
     * @return void
     */
    public function startTimer(string $operation): void
    {
        $this->timers[$operation] = [
            'start' => microtime(true),
            'end' => null,
        ];
    }
    
    /**
     * End a timer for an operation and log the duration.
     *
     * @param string $operation The operation name
     * @return float The duration in seconds
     */
    public function endTimer(string $operation): float
    {
        if (!isset($this->timers[$operation])) {
            return 0;
        }
        
        $this->timers[$operation]['end'] = microtime(true);
        $duration = $this->timers[$operation]['end'] - $this->timers[$operation]['start'];
        
        // Log the duration
        Log::info("{$operation} completed", [
            'duration_ms' => round($duration * 1000, 2),
            'operation' => $operation,
        ]);
        
        return $duration;
    }
    
    /**
     * Get the duration of an operation.
     *
     * @param string $operation The operation name
     * @return float|null The duration in seconds or null if the timer is not ended
     */
    public function getDuration(string $operation): ?float
    {
        if (!isset($this->timers[$operation]) || $this->timers[$operation]['end'] === null) {
            return null;
        }
        
        return $this->timers[$operation]['end'] - $this->timers[$operation]['start'];
    }
    
    /**
     * Execute a callback and measure its execution time.
     *
     * @param string $operation The operation name
     * @param callable $callback The callback to execute
     * @return mixed The result of the callback
     */
    public function measure(string $operation, callable $callback)
    {
        $this->startTimer($operation);
        
        try {
            $result = $callback();
            return $result;
        } finally {
            $this->endTimer($operation);
        }
    }
}

<?php

namespace App\Services;

use App\DTOs\CartDTO;
use App\DTOs\CartItemDTO;
use App\Events\CartCreated;
use App\Events\CartUpdated;
use App\Models\Cart;
use App\Models\CartItem;
use App\Repositories\Interfaces\CartRepositoryInterface;
use App\Repositories\Interfaces\ProductRepositoryInterface;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class CartService
{
    /**
     * The cart repository instance.
     */
    protected CartRepositoryInterface $cartRepository;

    /**
     * The product repository instance.
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * Create a new service instance.
     */
    public function __construct(
        CartRepositoryInterface $cartRepository,
        ProductRepositoryInterface $productRepository
    ) {
        $this->cartRepository = $cartRepository;
        $this->productRepository = $productRepository;
    }

    /**
     * Get a cart by ID
     *
     * @param int $id
     * @return Cart|null
     */
    public function getCartById(int $id): ?Cart
    {
        return $this->cartRepository->getCartById($id);
    }

    /**
     * Get the current cart.
     *
     * @param int|null $customerId
     * @return Cart
     */
    public function getCurrentCart(?int $customerId = null): Cart
    {
        $sessionId = Session::getId();

        if ($customerId) {
            $cart = $this->cartRepository->getCartByCustomerId($customerId);
            if ($cart) {
                return $cart;
            }
        } elseif ($sessionId) {
            $cart = $this->cartRepository->getCartBySessionId($sessionId);
            if ($cart) {
                return $cart;
            }
        }

        // Create a new cart
        $data = [
            'customer_id' => $customerId,
            'session_id' => $sessionId,
            'kitchen_id' => 1, // Default kitchen ID, should be configurable
            'total_amount' => 0,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'delivery_charges' => 0,
            'net_amount' => 0,
            'status' => 'active',
        ];

        $cart = $this->cartRepository->createCart($data);

        // Dispatch event
        event(new CartCreated($cart));

        return $cart;
    }

    /**
     * Add an item to the cart.
     *
     * @param CartItemDTO $itemDTO
     * @param int|null $customerId
     * @return CartItem
     */
    public function addItem(CartItemDTO $itemDTO, ?int $customerId = null): CartItem
    {
        $cart = $this->getCurrentCart($customerId);

        $product = $this->productRepository->getProductById($itemDTO->productId);

        $itemData = [
            'product_id' => $itemDTO->productId,
            'menu_id' => $itemDTO->menuId,
            'quantity' => $itemDTO->quantity,
            'unit_price' => $product->price,
            'total_price' => $product->price * $itemDTO->quantity,
            'delivery_date' => $itemDTO->deliveryDate,
            'delivery_time' => $itemDTO->deliveryTime,
            'customizations' => $itemDTO->customizations,
        ];

        $cartItem = $this->cartRepository->addItemToCart($cart->id, $itemData);

        // Dispatch event
        $cart = $this->cartRepository->getCartById($cart->id);
        event(new CartUpdated($cart));

        return $cartItem;
    }

    /**
     * Update a cart item.
     *
     * @param int $itemId
     * @param CartItemDTO $itemDTO
     * @param int|null $customerId
     * @return bool
     */
    public function updateItem(int $itemId, CartItemDTO $itemDTO, ?int $customerId = null): bool
    {
        $cart = $this->getCurrentCart($customerId);

        $product = $this->productRepository->getProductById($itemDTO->productId);

        $itemData = [
            'product_id' => $itemDTO->productId,
            'menu_id' => $itemDTO->menuId,
            'quantity' => $itemDTO->quantity,
            'unit_price' => $product->price,
            'total_price' => $product->price * $itemDTO->quantity,
            'delivery_date' => $itemDTO->deliveryDate,
            'delivery_time' => $itemDTO->deliveryTime,
            'customizations' => $itemDTO->customizations,
        ];

        $updated = $this->cartRepository->updateCartItem($cart->id, $itemId, $itemData);

        // Dispatch event
        $cart = $this->cartRepository->getCartById($cart->id);
        event(new CartUpdated($cart));

        return $updated;
    }

    /**
     * Remove an item from the cart.
     *
     * @param int $itemId
     * @param int|null $customerId
     * @return bool
     */
    public function removeItem(int $itemId, ?int $customerId = null): bool
    {
        $cart = $this->getCurrentCart($customerId);

        $deleted = $this->cartRepository->removeCartItem($cart->id, $itemId);

        // Dispatch event
        $cart = $this->cartRepository->getCartById($cart->id);
        event(new CartUpdated($cart));

        return $deleted;
    }

    /**
     * Clear the cart.
     *
     * @param int|null $customerId
     * @return bool
     */
    public function clearCart(?int $customerId = null): bool
    {
        $cart = $this->getCurrentCart($customerId);

        $cleared = $this->cartRepository->clearCart($cart->id);

        // Dispatch event
        $cart = $this->cartRepository->getCartById($cart->id);
        event(new CartUpdated($cart));

        return $cleared;
    }

    /**
     * Apply a promo code to the cart.
     *
     * @param string $promoCode
     * @param int|null $customerId
     * @return bool
     */
    public function applyPromoCode(string $promoCode, ?int $customerId = null): bool
    {
        $cart = $this->getCurrentCart($customerId);

        $applied = $this->cartRepository->applyPromoCode($cart->id, $promoCode);

        // Dispatch event
        $cart = $this->cartRepository->getCartById($cart->id);
        event(new CartUpdated($cart));

        return $applied;
    }

    /**
     * Calculate cart totals.
     *
     * @param int|null $customerId
     * @return Cart|null
     */
    public function calculateTotals(?int $customerId = null): ?Cart
    {
        $cart = $this->getCurrentCart($customerId);

        $calculated = $this->cartRepository->calculateCartTotals($cart->id);

        if ($calculated) {
            $cart = $this->cartRepository->getCartById($cart->id);

            // Dispatch event
            event(new CartUpdated($cart));

            return $cart;
        }

        return null;
    }

    /**
     * Checkout the cart.
     *
     * @param array $checkoutData
     * @param int|null $customerId
     * @return array
     */
    public function checkout(array $checkoutData, ?int $customerId = null): array
    {
        $cart = $this->getCurrentCart($customerId);

        // Ensure the cart has items
        if ($cart->items->isEmpty()) {
            return [
                'success' => false,
                'message' => 'Cart is empty',
            ];
        }

        // Calculate final totals
        $cart = $this->cartRepository->calculateCartTotals($cart->id);

        // Process the checkout
        $result = $this->cartRepository->processCheckout($cart->id, $checkoutData);

        // Clear the cart if checkout was successful
        if ($result['success']) {
            $this->cartRepository->clearCart($cart->id);
        }

        return $result;
    }

    /**
     * Merge guest cart with customer cart.
     *
     * @param int $customerId
     * @return Cart
     */
    public function mergeGuestCart(int $customerId): Cart
    {
        $sessionId = Session::getId();

        return $this->cartRepository->mergeGuestCart($customerId, $sessionId);
    }
}
<?php

namespace App\Services;

use App\DTOs\MenuDTO;
use App\Models\Menu;
use App\Repositories\Interfaces\MenuRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;

class MenuService
{
    /**
     * The menu repository instance.
     */
    protected MenuRepositoryInterface $menuRepository;

    /**
     * Create a new service instance.
     */
    public function __construct(MenuRepositoryInterface $menuRepository)
    {
        $this->menuRepository = $menuRepository;
    }

    /**
     * Get all menus with optional filtering.
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getAllMenus(array $filters = []): LengthAwarePaginator
    {
        return $this->menuRepository->getAllMenus($filters);
    }

    /**
     * Get a menu by ID.
     *
     * @param int $id
     * @return Menu|null
     */
    public function getMenuById(int $id): ?Menu
    {
        return $this->menuRepository->getMenuById($id);
    }

    /**
     * Create a new menu.
     *
     * @param MenuDTO $menuDTO
     * @return Menu
     */
    public function createMenu(MenuDTO $menuDTO): Menu
    {
        $data = [
            'name' => $menuDTO->name,
            'type' => $menuDTO->type,
            'kitchen_id' => $menuDTO->kitchenId,
            'cut_off_time' => $menuDTO->cutOffTime,
            'cut_off_interval' => $menuDTO->cutOffInterval,
            'status' => $menuDTO->status,
        ];

        $menu = $this->menuRepository->createMenu($data);

        // Clear cache
        $this->clearMenuCache($menu->kitchen_id);

        return $menu;
    }

    /**
     * Update a menu.
     *
     * @param int $id
     * @param MenuDTO $menuDTO
     * @return bool
     */
    public function updateMenu(int $id, MenuDTO $menuDTO): bool
    {
        $menu = $this->menuRepository->getMenuById($id);

        if (!$menu) {
            return false;
        }

        $data = [
            'name' => $menuDTO->name,
            'type' => $menuDTO->type,
            'kitchen_id' => $menuDTO->kitchenId,
            'cut_off_time' => $menuDTO->cutOffTime,
            'cut_off_interval' => $menuDTO->cutOffInterval,
            'status' => $menuDTO->status,
        ];

        $updated = $this->menuRepository->updateMenu($id, $data);

        // Clear cache
        $this->clearMenuCache($menu->kitchen_id);
        if ($menu->kitchen_id != $menuDTO->kitchenId) {
            $this->clearMenuCache($menuDTO->kitchenId);
        }

        return $updated;
    }

    /**
     * Delete a menu.
     *
     * @param int $id
     * @return bool
     */
    public function deleteMenu(int $id): bool
    {
        $menu = $this->menuRepository->getMenuById($id);

        if (!$menu) {
            return false;
        }

        $deleted = $this->menuRepository->deleteMenu($id);

        // Clear cache
        $this->clearMenuCache($menu->kitchen_id);

        return $deleted;
    }

    /**
     * Get menus by kitchen ID.
     *
     * @param int $kitchenId
     * @return Collection
     */
    public function getMenusByKitchenId(int $kitchenId): Collection
    {
        $cacheKey = "menus_kitchen_{$kitchenId}";

        return Cache::remember($cacheKey, 3600, function () use ($kitchenId) {
            return $this->menuRepository->getMenusByKitchenId($kitchenId);
        });
    }

    /**
     * Clear menu cache for a kitchen
     *
     * @param int $kitchenId
     * @return void
     */
    protected function clearMenuCache(int $kitchenId): void
    {
        Cache::forget("menus_kitchen_{$kitchenId}");
    }

    /**
     * Get menus by type.
     *
     * @param string $type
     * @return Collection
     */
    public function getMenusByType(string $type): Collection
    {
        return $this->menuRepository->getMenusByType($type);
    }
}
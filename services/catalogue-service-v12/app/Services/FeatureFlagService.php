<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class FeatureFlagService
{
    /**
     * Cache TTL in seconds.
     */
    private const CACHE_TTL = 300; // 5 minutes
    
    /**
     * Check if a feature is enabled.
     *
     * @param string $feature The feature name
     * @param bool $default The default value if the feature is not found
     * @return bool True if the feature is enabled, false otherwise
     */
    public function isEnabled(string $feature, bool $default = false): bool
    {
        $cacheKey = 'feature_flag:' . $feature;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($feature, $default) {
            $value = Config::get('features.' . $feature, $default);
            
            Log::debug("Feature flag check", [
                'feature' => $feature,
                'enabled' => $value,
            ]);
            
            return $value;
        });
    }
    
    /**
     * Get the variant of a feature.
     *
     * @param string $feature The feature name
     * @param string $default The default variant if the feature variant is not found
     * @return string The feature variant
     */
    public function getVariant(string $feature, string $default = 'default'): string
    {
        $cacheKey = 'feature_variant:' . $feature;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($feature, $default) {
            $value = Config::get('feature_variants.' . $feature, $default);
            
            Log::debug("Feature variant check", [
                'feature' => $feature,
                'variant' => $value,
            ]);
            
            return $value;
        });
    }
    
    /**
     * Clear the feature flag cache.
     *
     * @return void
     */
    public function clearCache(): void
    {
        $features = Config::get('features', []);
        $variants = Config::get('feature_variants', []);
        
        foreach (array_keys($features) as $feature) {
            Cache::forget('feature_flag:' . $feature);
        }
        
        foreach (array_keys($variants) as $feature) {
            Cache::forget('feature_variant:' . $feature);
        }
        
        Log::info("Feature flag cache cleared");
    }
}

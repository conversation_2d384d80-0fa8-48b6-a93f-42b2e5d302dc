<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CircuitBreakerService
{
    private const CACHE_PREFIX = 'circuit_breaker:';
    private const DEFAULT_THRESHOLD = 5;
    private const DEFAULT_TIMEOUT = 30; // seconds
    
    /**
     * Execute a function with circuit breaker pattern.
     *
     * @param string $service The service identifier
     * @param callable $callback The function to execute
     * @param int $threshold The failure threshold (default: 5)
     * @param int $timeout The circuit open timeout in seconds (default: 30)
     * @return mixed The result of the callback
     * @throws \Exception If the circuit is open or the callback throws an exception
     */
    public function execute(string $service, callable $callback, int $threshold = self::DEFAULT_THRESHOLD, int $timeout = self::DEFAULT_TIMEOUT)
    {
        $cacheKey = self::CACHE_PREFIX . $service;
        $circuitState = Cache::get($cacheKey, [
            'failures' => 0,
            'open' => false,
            'last_failure' => null,
        ]);
        
        // Check if circuit is open
        if ($circuitState['open']) {
            $timeElapsed = time() - $circuitState['last_failure'];
            if ($timeElapsed < $timeout) {
                Log::warning("Circuit is open for service: {$service}", [
                    'elapsed_time' => $timeElapsed,
                    'timeout' => $timeout,
                ]);
                throw new \Exception("Service {$service} is unavailable");
            }
            
            // Reset circuit for retry
            $circuitState['open'] = false;
            $circuitState['failures'] = 0;
            Cache::put($cacheKey, $circuitState);
            
            Log::info("Circuit reset for service: {$service}", [
                'elapsed_time' => $timeElapsed,
            ]);
        }
        
        try {
            $result = $callback();
            
            // Reset failures on success
            if ($circuitState['failures'] > 0) {
                $circuitState['failures'] = 0;
                Cache::put($cacheKey, $circuitState);
                
                Log::info("Circuit failures reset for service: {$service}");
            }
            
            return $result;
        } catch (\Exception $e) {
            // Increment failure count
            $circuitState['failures']++;
            $circuitState['last_failure'] = time();
            
            // Open circuit if threshold reached
            if ($circuitState['failures'] >= $threshold) {
                $circuitState['open'] = true;
                Log::error("Circuit opened for service: {$service} after {$circuitState['failures']} failures", [
                    'threshold' => $threshold,
                    'error' => $e->getMessage(),
                ]);
            } else {
                Log::warning("Circuit failure for service: {$service}", [
                    'failures' => $circuitState['failures'],
                    'threshold' => $threshold,
                    'error' => $e->getMessage(),
                ]);
            }
            
            Cache::put($cacheKey, $circuitState);
            throw $e;
        }
    }
}

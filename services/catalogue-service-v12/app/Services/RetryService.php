<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class RetryService
{
    private const MAX_RETRIES = 3;
    private const BASE_DELAY = 100; // milliseconds
    
    /**
     * Execute a function with retry logic.
     *
     * @param callable $callback The function to execute
     * @param string $operation The operation identifier for logging
     * @param int $maxRetries The maximum number of retries (default: 3)
     * @param int $baseDelay The base delay in milliseconds (default: 100)
     * @return mixed The result of the callback
     * @throws \Exception If all retries fail
     */
    public function execute(callable $callback, string $operation = 'operation', int $maxRetries = self::MAX_RETRIES, int $baseDelay = self::BASE_DELAY)
    {
        $attempt = 0;
        $lastException = null;
        
        while ($attempt < $maxRetries) {
            try {
                return $callback();
            } catch (\Exception $e) {
                $attempt++;
                $lastException = $e;
                
                if ($attempt < $maxRetries) {
                    $delay = $baseDelay * pow(2, $attempt - 1);
                    Log::warning("Retry {$attempt} for {$operation} after {$delay}ms", [
                        'error' => $e->getMessage(),
                        'exception' => get_class($e),
                    ]);
                    usleep($delay * 1000); // Convert to microseconds
                }
            }
        }
        
        Log::error("All retries failed for {$operation}", [
            'error' => $lastException->getMessage(),
            'exception' => get_class($lastException),
            'max_retries' => $maxRetries,
        ]);
        throw $lastException;
    }
}

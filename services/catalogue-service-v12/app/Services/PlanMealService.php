<?php

namespace App\Services;

use App\DTOs\PlanMealDTO;
use App\DTOs\PlanMealItemDTO;
use App\Events\PlanMealCreated;
use App\Events\PlanMealUpdated;
use App\Models\PlanMeal;
use App\Models\PlanMealItem;
use App\Repositories\Interfaces\PlanMealRepositoryInterface;
use App\Repositories\Interfaces\ProductRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class PlanMealService
{
    /**
     * The plan meal repository instance.
     */
    protected PlanMealRepositoryInterface $planMealRepository;

    /**
     * The product repository instance.
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * Create a new service instance.
     */
    public function __construct(
        PlanMealRepositoryInterface $planMealRepository,
        ProductRepositoryInterface $productRepository
    ) {
        $this->planMealRepository = $planMealRepository;
        $this->productRepository = $productRepository;
    }

    /**
     * Get all plan meals with optional filtering.
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getAllPlanMeals(array $filters = []): LengthAwarePaginator
    {
        return $this->planMealRepository->getAllPlanMeals($filters);
    }

    /**
     * Get a plan meal by ID.
     *
     * @param int $id
     * @return PlanMeal|null
     */
    public function getPlanMealById(int $id): ?PlanMeal
    {
        return $this->planMealRepository->getPlanMealById($id);
    }

    /**
     * Create a new plan meal.
     *
     * @param PlanMealDTO $planMealDTO
     * @return PlanMeal
     */
    public function createPlanMeal(PlanMealDTO $planMealDTO): PlanMeal
    {
        $data = [
            'customer_id' => $planMealDTO->customerId,
            'kitchen_id' => $planMealDTO->kitchenId,
            'menu_type' => $planMealDTO->menuType,
            'start_date' => $planMealDTO->startDate,
            'end_date' => $planMealDTO->endDate,
            'quantity' => $planMealDTO->quantity,
            'negotiated_price' => $planMealDTO->negotiatedPrice,
            'service_charges' => $planMealDTO->serviceCharges,
            'tax' => $planMealDTO->tax,
            'discount' => $planMealDTO->discount,
            'total_bill_amount' => $planMealDTO->totalBillAmount,
            'promo_code' => $planMealDTO->promoCode,
            'applied_taxes' => $planMealDTO->appliedTaxes,
            'status' => $planMealDTO->status,
        ];

        $planMeal = $this->planMealRepository->createPlanMeal($data);

        // Add items if provided
        if (!empty($planMealDTO->items)) {
            foreach ($planMealDTO->items as $itemDTO) {
                $this->addPlanMealItem($planMeal->id, [
                    'product_id' => $itemDTO->productId,
                    'delivery_date' => $itemDTO->deliveryDate,
                    'quantity' => $itemDTO->quantity,
                ]);
            }
        }

        // Dispatch event
        event(new PlanMealCreated($planMeal));

        return $planMeal;
    }

    /**
     * Update a plan meal.
     *
     * @param int $id
     * @param PlanMealDTO $planMealDTO
     * @return bool
     */
    public function updatePlanMeal(int $id, PlanMealDTO $planMealDTO): bool
    {
        $data = [
            'customer_id' => $planMealDTO->customerId,
            'kitchen_id' => $planMealDTO->kitchenId,
            'menu_type' => $planMealDTO->menuType,
            'start_date' => $planMealDTO->startDate,
            'end_date' => $planMealDTO->endDate,
            'quantity' => $planMealDTO->quantity,
            'negotiated_price' => $planMealDTO->negotiatedPrice,
            'service_charges' => $planMealDTO->serviceCharges,
            'tax' => $planMealDTO->tax,
            'discount' => $planMealDTO->discount,
            'total_bill_amount' => $planMealDTO->totalBillAmount,
            'promo_code' => $planMealDTO->promoCode,
            'applied_taxes' => $planMealDTO->appliedTaxes,
            'status' => $planMealDTO->status,
        ];

        $updated = $this->planMealRepository->updatePlanMeal($id, $data);

        if ($updated) {
            $planMeal = $this->planMealRepository->getPlanMealById($id);

            // Dispatch event
            event(new PlanMealUpdated($planMeal));
        }

        return $updated;
    }

    /**
     * Delete a plan meal.
     *
     * @param int $id
     * @return bool
     */
    public function deletePlanMeal(int $id): bool
    {
        $planMeal = $this->planMealRepository->getPlanMealById($id);

        if (!$planMeal) {
            return false;
        }

        $deleted = $this->planMealRepository->deletePlanMeal($id);

        if ($deleted) {
            // Dispatch event
            event(new PlanMealUpdated($planMeal));
        }

        return $deleted;
    }

    /**
     * Get plan meals by customer ID.
     *
     * @param int $customerId
     * @return Collection
     */
    public function getPlanMealsByCustomerId(int $customerId): Collection
    {
        return $this->planMealRepository->getPlanMealsByCustomerId($customerId);
    }

    /**
     * Add an item to a plan meal.
     *
     * @param int $planMealId
     * @param PlanMealItemDTO|array $itemData
     * @return PlanMealItem
     */
    public function addPlanMealItem(int $planMealId, $itemData): PlanMealItem
    {
        if ($itemData instanceof PlanMealItemDTO) {
            $product = $this->productRepository->getProductById($itemData->productId);

            $data = [
                'product_id' => $itemData->productId,
                'delivery_date' => $itemData->deliveryDate,
                'quantity' => $itemData->quantity,
                'unit_price' => $product->price,
                'total_price' => $product->price * $itemData->quantity,
            ];
        } else {
            $data = $itemData;
        }

        $planMealItem = $this->planMealRepository->addItemToPlanMeal($planMealId, $data);

        // Dispatch event
        $planMeal = $this->planMealRepository->getPlanMealById($planMealId);
        event(new PlanMealUpdated($planMeal));

        return $planMealItem;
    }

    /**
     * Update a plan meal item.
     *
     * @param int $planMealId
     * @param int $itemId
     * @param PlanMealItemDTO|array $itemData
     * @return bool
     */
    public function updatePlanMealItem(int $planMealId, int $itemId, $itemData): bool
    {
        if ($itemData instanceof PlanMealItemDTO) {
            $product = $this->productRepository->getProductById($itemData->productId);

            $data = [
                'product_id' => $itemData->productId,
                'delivery_date' => $itemData->deliveryDate,
                'quantity' => $itemData->quantity,
                'unit_price' => $product->price,
                'total_price' => $product->price * $itemData->quantity,
            ];
        } else {
            $data = $itemData;
        }

        $updated = $this->planMealRepository->updatePlanMealItem($planMealId, $itemId, $data);

        if ($updated) {
            // Dispatch event
            $planMeal = $this->planMealRepository->getPlanMealById($planMealId);
            event(new PlanMealUpdated($planMeal));
        }

        return $updated;
    }

    /**
     * Remove a plan meal item.
     *
     * @param int $planMealId
     * @param int $itemId
     * @return bool
     */
    public function removePlanMealItem(int $planMealId, int $itemId): bool
    {
        $removed = $this->planMealRepository->removePlanMealItem($planMealId, $itemId);

        if ($removed) {
            // Dispatch event
            $planMeal = $this->planMealRepository->getPlanMealById($planMealId);
            event(new PlanMealUpdated($planMeal));
        }

        return $removed;
    }

    /**
     * Calculate plan meal totals.
     *
     * @param int $planMealId
     * @return PlanMeal|null
     */
    public function calculatePlanMealTotals(int $planMealId): ?PlanMeal
    {
        $calculated = $this->planMealRepository->calculatePlanMealTotals($planMealId);

        if ($calculated) {
            $planMeal = $this->planMealRepository->getPlanMealById($planMealId);

            // Dispatch event
            event(new PlanMealUpdated($planMeal));

            return $planMeal;
        }

        return null;
    }

    /**
     * Apply a promo code to a plan meal.
     *
     * @param int $planMealId
     * @param string $promoCode
     * @return bool
     */
    public function applyPromoCode(int $planMealId, string $promoCode): bool
    {
        $applied = $this->planMealRepository->applyPromoCode($planMealId, $promoCode);

        if ($applied) {
            $planMeal = $this->planMealRepository->getPlanMealById($planMealId);

            // Dispatch event
            event(new PlanMealUpdated($planMeal));
        }

        return $applied;
    }

    /**
     * Process plan meal checkout.
     *
     * @param int $planMealId
     * @param array $checkoutData
     * @return array
     */
    public function checkout(int $planMealId, array $checkoutData): array
    {
        return $this->planMealRepository->processPlanMealCheckout($planMealId, $checkoutData);
    }
}
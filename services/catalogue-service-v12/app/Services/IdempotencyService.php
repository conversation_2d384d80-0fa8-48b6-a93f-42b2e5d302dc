<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class IdempotencyService
{
    private const CACHE_PREFIX = 'idempotency:';
    private const CACHE_TTL = 86400; // 24 hours in seconds
    
    /**
     * Generate a new idempotency key.
     *
     * @return string
     */
    public function generateKey(): string
    {
        return Str::uuid()->toString();
    }
    
    /**
     * Process an operation with idempotency.
     *
     * @param string $key The idempotency key
     * @param string $operation The operation identifier
     * @param callable $callback The operation to execute
     * @return mixed The result of the operation
     */
    public function processWithIdempotency(string $key, string $operation, callable $callback)
    {
        $cacheKey = self::CACHE_PREFIX . $operation . ':' . $key;
        
        // Check if we've already processed this request
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult !== null) {
            \Log::info('Using cached result for idempotent operation', [
                'operation' => $operation,
                'key' => $key
            ]);
            return $cachedResult;
        }
        
        // Execute the operation
        $result = $callback();
        
        // Store the result
        Cache::put($cacheKey, $result, self::CACHE_TTL);
        
        return $result;
    }
}

<?php

namespace App\Services;

class SensitiveDataSanitizer
{
    /**
     * List of sensitive field names to sanitize.
     */
    private const SENSITIVE_FIELDS = [
        'password', 'card_number', 'cvv', 'expiry', 'token',
        'ssn', 'social_security', 'credit_card', 'secret',
        'api_key', 'auth_token', 'access_token', 'refresh_token',
        'private_key', 'secret_key', 'authorization'
    ];
    
    /**
     * Sanitize sensitive data in an array.
     *
     * @param array $data The data to sanitize
     * @return array The sanitized data
     */
    public function sanitize(array $data): array
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->sanitize($value);
            } elseif (is_string($value) && $this->isSensitiveField($key)) {
                $data[$key] = '***REDACTED***';
            }
        }
        
        return $data;
    }
    
    /**
     * Check if a field name is sensitive.
     *
     * @param string $fieldName The field name to check
     * @return bool True if the field is sensitive, false otherwise
     */
    private function isSensitiveField(string $fieldName): bool
    {
        $fieldName = strtolower($fieldName);
        
        foreach (self::SENSITIVE_FIELDS as $sensitiveField) {
            if (strpos($fieldName, $sensitiveField) !== false) {
                return true;
            }
        }
        
        return false;
    }
}

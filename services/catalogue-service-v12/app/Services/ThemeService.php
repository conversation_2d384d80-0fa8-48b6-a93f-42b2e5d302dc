<?php

namespace App\Services;

use App\Models\Theme;
use App\Repositories\ThemeRepository;
use Illuminate\Support\Collection;

class ThemeService
{
    /**
     * The theme repository instance.
     */
    protected ThemeRepository $themeRepository;

    /**
     * Create a new service instance.
     */
    public function __construct(ThemeRepository $themeRepository)
    {
        $this->themeRepository = $themeRepository;
    }

    /**
     * Get all themes.
     *
     * @return Collection
     */
    public function getAllThemes(): Collection
    {
        return $this->themeRepository->getAllThemes();
    }

    /**
     * Get a theme by ID.
     *
     * @param int $id
     * @return Theme|null
     */
    public function getThemeById(int $id): ?Theme
    {
        return $this->themeRepository->getThemeById($id);
    }

    /**
     * Get a theme by name.
     *
     * @param string $name
     * @return Theme|null
     */
    public function getThemeByName(string $name): ?Theme
    {
        return $this->themeRepository->getThemeByName($name);
    }

    /**
     * Get the active theme.
     *
     * @return Theme|null
     */
    public function getActiveTheme(): ?Theme
    {
        return $this->themeRepository->getActiveTheme();
    }

    /**
     * Create a new theme.
     *
     * @param array $data
     * @return Theme
     */
    public function createTheme(array $data): Theme
    {
        return $this->themeRepository->createTheme($data);
    }

    /**
     * Update a theme.
     *
     * @param int $id
     * @param array $data
     * @return Theme|null
     */
    public function updateTheme(int $id, array $data): ?Theme
    {
        return $this->themeRepository->updateTheme($id, $data);
    }

    /**
     * Delete a theme.
     *
     * @param int $id
     * @return bool
     */
    public function deleteTheme(int $id): bool
    {
        return $this->themeRepository->deleteTheme($id);
    }

    /**
     * Set the active theme.
     *
     * @param int $id
     * @return Theme|null
     */
    public function setActiveTheme(int $id): ?Theme
    {
        return $this->themeRepository->setActiveTheme($id);
    }

    /**
     * Get theme configuration.
     *
     * @param int $id
     * @return array|null
     */
    public function getThemeConfig(int $id): ?array
    {
        return $this->themeRepository->getThemeConfig($id);
    }

    /**
     * Update theme configuration.
     *
     * @param int $id
     * @param array $config
     * @return Theme|null
     */
    public function updateThemeConfig(int $id, array $config): ?Theme
    {
        return $this->themeRepository->updateThemeConfig($id, $config);
    }
}
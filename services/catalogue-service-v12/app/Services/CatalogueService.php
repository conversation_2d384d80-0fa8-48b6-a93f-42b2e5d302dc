<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Repositories\ProductRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CatalogueService
{
    /**
     * The product repository instance.
     */
    protected ProductRepository $productRepository;

    /**
     * Create a new service instance.
     */
    public function __construct(ProductRepository $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * Get all products with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllProducts(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->productRepository->getAllProducts($filters, $perPage);
    }

    /**
     * Get a product by ID.
     *
     * @param int $id
     * @return Product|null
     */
    public function getProductById(int $id): ?Product
    {
        return $this->productRepository->getProductById($id);
    }

    /**
     * Create a new product.
     *
     * @param array $data
     * @return Product
     */
    public function createProduct(array $data): Product
    {
        return $this->productRepository->createProduct($data);
    }

    /**
     * Update a product.
     *
     * @param int $id
     * @param array $data
     * @return Product|null
     */
    public function updateProduct(int $id, array $data): ?Product
    {
        return $this->productRepository->updateProduct($id, $data);
    }

    /**
     * Delete a product.
     *
     * @param int $id
     * @return bool
     */
    public function deleteProduct(int $id): bool
    {
        return $this->productRepository->deleteProduct($id);
    }

    /**
     * Get products by category.
     *
     * @param string $category
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getProductsByCategory(string $category, int $perPage = 15): LengthAwarePaginator
    {
        return $this->productRepository->getProductsByCategory($category, $perPage);
    }

    /**
     * Search products by name or description.
     *
     * @param string $query
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function searchProducts(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->productRepository->searchProducts($query, $perPage);
    }

    /**
     * Get all product categories.
     *
     * @return Collection
     */
    public function getAllCategories(): Collection
    {
        return ProductCategory::where('status', true)->orderBy('sequence')->get();
    }
}
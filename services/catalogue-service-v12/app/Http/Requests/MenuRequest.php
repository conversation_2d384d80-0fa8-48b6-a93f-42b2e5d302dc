<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MenuRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:breakfast,lunch,dinner',
            'kitchen_id' => 'required|integer|exists:kitchens,id',
            'cut_off_time' => 'nullable|date_format:H:i',
            'cut_off_interval' => 'nullable|integer|min:1',
            'status' => 'nullable|boolean',
        ];

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The menu name is required.',
            'type.required' => 'The menu type is required.',
            'type.in' => 'The menu type must be one of: breakfast, lunch, dinner.',
            'kitchen_id.required' => 'The kitchen ID is required.',
            'kitchen_id.exists' => 'The selected kitchen does not exist.',
            'cut_off_time.date_format' => 'The cut-off time must be in the format HH:MM.',
            'cut_off_interval.min' => 'The cut-off interval must be at least 1.',
        ];
    }
}

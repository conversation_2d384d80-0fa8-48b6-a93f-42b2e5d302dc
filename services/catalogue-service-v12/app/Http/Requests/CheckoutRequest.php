<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'payment_method' => 'required|string|in:wallet,card,upi,netbanking,cod',
            'delivery_address' => 'nullable|array',
            'delivery_address.address_line1' => 'required_with:delivery_address|string|max:255',
            'delivery_address.address_line2' => 'nullable|string|max:255',
            'delivery_address.city' => 'required_with:delivery_address|string|max:100',
            'delivery_address.state' => 'required_with:delivery_address|string|max:100',
            'delivery_address.country' => 'required_with:delivery_address|string|max:100',
            'delivery_address.pincode' => 'required_with:delivery_address|string|max:20',
            'delivery_address.phone' => 'nullable|string|max:20',
            'delivery_notes' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'payment_method.required' => 'The payment method is required.',
            'payment_method.in' => 'The payment method must be one of: wallet, card, upi, netbanking, cod.',
            'delivery_address.address_line1.required_with' => 'The address line 1 is required when delivery address is provided.',
            'delivery_address.city.required_with' => 'The city is required when delivery address is provided.',
            'delivery_address.state.required_with' => 'The state is required when delivery address is provided.',
            'delivery_address.country.required_with' => 'The country is required when delivery address is provided.',
            'delivery_address.pincode.required_with' => 'The pincode is required when delivery address is provided.',
        ];
    }
}

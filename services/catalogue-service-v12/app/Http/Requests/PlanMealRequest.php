<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PlanMealRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'customer_id' => 'required|integer|exists:users,id',
            'kitchen_id' => 'required|integer|exists:kitchens,id',
            'menu_type' => 'required|string|in:breakfast,lunch,dinner',
            'start_date' => 'required|date_format:Y-m-d',
            'end_date' => 'required|date_format:Y-m-d|after_or_equal:start_date',
            'quantity' => 'nullable|integer|min:1',
            'negotiated_price' => 'nullable|numeric|min:0',
            'service_charges' => 'nullable|numeric|min:0',
            'tax' => 'nullable|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'total_bill_amount' => 'nullable|numeric|min:0',
            'promo_code' => 'nullable|string|max:50',
            'applied_taxes' => 'nullable|array',
            'status' => 'nullable|string|in:active,pending,completed,cancelled',
            'items' => 'nullable|array',
            'items.*.product_id' => 'required_with:items|integer|exists:products,id',
            'items.*.delivery_date' => 'required_with:items|date_format:Y-m-d',
            'items.*.quantity' => 'nullable|integer|min:1',
            'items.*.unit_price' => 'nullable|numeric|min:0',
        ];

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'customer_id.required' => 'The customer ID is required.',
            'customer_id.exists' => 'The selected customer does not exist.',
            'kitchen_id.required' => 'The kitchen ID is required.',
            'kitchen_id.exists' => 'The selected kitchen does not exist.',
            'menu_type.required' => 'The menu type is required.',
            'menu_type.in' => 'The menu type must be one of: breakfast, lunch, dinner.',
            'start_date.required' => 'The start date is required.',
            'start_date.date_format' => 'The start date must be in the format YYYY-MM-DD.',
            'end_date.required' => 'The end date is required.',
            'end_date.date_format' => 'The end date must be in the format YYYY-MM-DD.',
            'end_date.after_or_equal' => 'The end date must be after or equal to the start date.',
            'quantity.min' => 'The quantity must be at least 1.',
            'negotiated_price.min' => 'The negotiated price must be at least 0.',
            'service_charges.min' => 'The service charges must be at least 0.',
            'tax.min' => 'The tax must be at least 0.',
            'discount.min' => 'The discount must be at least 0.',
            'total_bill_amount.min' => 'The total bill amount must be at least 0.',
            'status.in' => 'The status must be one of: active, pending, completed, cancelled.',
            'items.*.product_id.required_with' => 'The product ID is required for each item.',
            'items.*.product_id.exists' => 'The selected product does not exist.',
            'items.*.delivery_date.required_with' => 'The delivery date is required for each item.',
            'items.*.delivery_date.date_format' => 'The delivery date must be in the format YYYY-MM-DD.',
            'items.*.quantity.min' => 'The quantity must be at least 1 for each item.',
            'items.*.unit_price.min' => 'The unit price must be at least 0 for each item.',
        ];
    }
}

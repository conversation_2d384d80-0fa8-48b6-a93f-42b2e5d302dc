<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class CorrelationIdMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get or generate correlation ID
        $correlationId = $request->header('X-Correlation-ID') ?? Str::uuid()->toString();
        
        // Add to request for controllers to access
        $request->attributes->set('correlation_id', $correlationId);
        
        // Add to log context
        Log::withContext(['correlation_id' => $correlationId]);
        
        // Process the request
        $response = $next($request);
        
        // Add to response headers
        $response->header('X-Correlation-ID', $correlationId);
        
        return $response;
    }
}

<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\PerformanceMetricsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HealthController extends Controller
{
    /**
     * Check the health of the service.
     *
     * @return JsonResponse
     */
    public function check(): JsonResponse
    {
        $metrics = app(PerformanceMetricsService::class);
        $metrics->startTimer('health_check');

        $status = 'ok';
        $message = 'Service is healthy';
        $checks = [
            'database' => $this->checkDatabase(),
            'memory' => $this->checkMemory(),
            'disk' => $this->checkDisk(),
            'dependencies' => $this->checkDependencies(),
        ];

        // If any check fails, set status to error
        foreach ($checks as $check) {
            if ($check['status'] === 'error') {
                $status = 'error';
                $message = 'Service is unhealthy';
                break;
            } elseif ($check['status'] === 'warning' && $status === 'ok') {
                $status = 'warning';
                $message = 'Service has warnings';
            }
        }

        $metrics->endTimer('health_check');

        return response()->json([
            'status' => $status,
            'message' => $message,
            'timestamp' => now()->toIso8601String(),
            'checks' => $checks,
            'version' => config('app.version', '1.0.0'),
            'metrics' => [
                'health_check_duration_ms' => round($metrics->getDuration('health_check') * 1000, 2),
            ],
        ]);
    }

    /**
     * Check the database connection.
     *
     * @return array
     */
    private function checkDatabase(): array
    {
        $metrics = app(PerformanceMetricsService::class);
        $metrics->startTimer('database_check');

        try {
            // Try to connect to the database
            DB::connection()->getPdo();

            // Execute a simple query
            $result = DB::select('SELECT 1');

            $duration = $metrics->endTimer('database_check');
            $durationMs = round($duration * 1000, 2);

            // If the query takes too long, return a warning
            if ($durationMs > 100) {
                return [
                    'status' => 'warning',
                    'message' => "Database connection slow: {$durationMs}ms",
                    'duration_ms' => $durationMs,
                ];
            }

            return [
                'status' => 'ok',
                'message' => "Database connection successful: {$durationMs}ms",
                'duration_ms' => $durationMs,
            ];
        } catch (\Exception $e) {
            $metrics->endTimer('database_check');

            Log::error('Database health check failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check the memory usage.
     *
     * @return array
     */
    private function checkMemory(): array
    {
        $memoryLimit = ini_get('memory_limit');
        $memoryUsage = memory_get_usage(true);
        $memoryUsageMB = round($memoryUsage / 1024 / 1024, 2);

        // Parse memory limit to bytes
        if (preg_match('/^(\d+)(.)$/', $memoryLimit, $matches)) {
            $memoryLimit = $matches[1];
            $unit = strtolower($matches[2]);

            switch ($unit) {
                case 'g':
                    $memoryLimit *= 1024;
                    // no break
                case 'm':
                    $memoryLimit *= 1024;
                    // no break
                case 'k':
                    $memoryLimit *= 1024;
            }
        }

        $memoryLimitMB = round($memoryLimit / 1024 / 1024, 2);
        $memoryUsagePercentage = round(($memoryUsage / $memoryLimit) * 100, 2);

        $status = 'ok';
        $message = "Memory usage: {$memoryUsageMB}MB / {$memoryLimitMB}MB ({$memoryUsagePercentage}%)";

        // If memory usage is above 90%, set status to warning
        if ($memoryUsagePercentage > 90) {
            $status = 'warning';
            $message = "High memory usage: {$memoryUsageMB}MB / {$memoryLimitMB}MB ({$memoryUsagePercentage}%)";
        }

        return [
            'status' => $status,
            'message' => $message,
            'usage' => $memoryUsageMB,
            'limit' => $memoryLimitMB,
            'percentage' => $memoryUsagePercentage,
        ];
    }

    /**
     * Check the disk usage.
     *
     * @return array
     */
    private function checkDisk(): array
    {
        $diskFree = disk_free_space(storage_path());
        $diskTotal = disk_total_space(storage_path());
        $diskUsed = $diskTotal - $diskFree;

        $diskFreeMB = round($diskFree / 1024 / 1024, 2);
        $diskTotalMB = round($diskTotal / 1024 / 1024, 2);
        $diskUsedMB = round($diskUsed / 1024 / 1024, 2);

        $diskUsagePercentage = round(($diskUsed / $diskTotal) * 100, 2);

        $status = 'ok';
        $message = "Disk usage: {$diskUsedMB}MB / {$diskTotalMB}MB ({$diskUsagePercentage}%)";

        // If disk usage is above 90%, set status to error
        if ($diskUsagePercentage > 90) {
            $status = 'error';
            $message = "Critical disk usage: {$diskUsedMB}MB / {$diskTotalMB}MB ({$diskUsagePercentage}%)";
        }
        // If disk usage is above 70%, set status to warning
        elseif ($diskUsagePercentage > 70) {
            $status = 'warning';
            $message = "High disk usage: {$diskUsedMB}MB / {$diskTotalMB}MB ({$diskUsagePercentage}%)";
        }

        return [
            'status' => $status,
            'message' => $message,
            'free' => $diskFreeMB,
            'total' => $diskTotalMB,
            'used' => $diskUsedMB,
            'percentage' => $diskUsagePercentage,
        ];
    }

    /**
     * Check external service dependencies.
     *
     * @return array
     */
    private function checkDependencies(): array
    {
        $dependencies = [
            'order_service' => config('services.order.url') . '/health',
            'subscription_service' => config('services.subscription.url') . '/health',
            'wallet_service' => config('services.wallet.url') . '/health',
        ];

        $results = [];
        $overallStatus = 'ok';

        foreach ($dependencies as $service => $url) {
            $metrics = app(PerformanceMetricsService::class);
            $metrics->startTimer("dependency_check_{$service}");

            try {
                $response = Http::timeout(2)->get($url);
                $duration = $metrics->endTimer("dependency_check_{$service}");
                $durationMs = round($duration * 1000, 2);

                if ($response->successful()) {
                    $status = $durationMs > 500 ? 'warning' : 'ok';
                    $message = $durationMs > 500
                        ? "Response time high: {$durationMs}ms"
                        : "Healthy: {$durationMs}ms";
                } else {
                    $status = 'error';
                    $message = "Unhealthy: HTTP {$response->status()}";
                }
            } catch (\Exception $e) {
                $metrics->endTimer("dependency_check_{$service}");
                $status = 'error';
                $message = "Connection failed: {$e->getMessage()}";
            }

            $results[$service] = [
                'status' => $status,
                'message' => $message,
            ];

            if ($status === 'error') {
                $overallStatus = 'error';
            } elseif ($status === 'warning' && $overallStatus === 'ok') {
                $overallStatus = 'warning';
            }
        }

        return [
            'status' => $overallStatus,
            'message' => $overallStatus === 'ok' ? 'All dependencies are healthy' : 'Some dependencies have issues',
            'services' => $results,
        ];
    }
}

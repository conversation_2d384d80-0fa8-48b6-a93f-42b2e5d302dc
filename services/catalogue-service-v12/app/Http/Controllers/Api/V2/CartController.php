<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class CartController extends Controller
{

    /**
     * Get the current cart for a customer.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCart(Request $request): JsonResponse
    {
        try {
            $customerId = $request->input('customer_id', 1); // Default to customer 1 for testing

            $cart = DB::table('carts')
                ->where('customer_id', $customerId)
                ->where('status', 'active')
                ->first();

            if (!$cart) {
                return response()->json([
                    'success' => true,
                    'message' => 'No active cart found',
                    'data' => null
                ]);
            }

            // Get cart items with product details
            $cartItems = DB::table('cart_items as ci')
                ->join('products as p', 'ci.product_id', '=', 'p.pk_product_code')
                ->where('ci.cart_id', $cart->id)
                ->select(
                    'ci.id',
                    'ci.product_id',
                    'ci.quantity',
                    'ci.unit_price',
                    'ci.total_price',
                    'ci.menu_type',
                    'ci.delivery_date',
                    'p.name as product_name',
                    'p.unit',
                    'p.recipe'
                )
                ->get();

            $cartData = [
                'id' => $cart->id,
                'customer_id' => $cart->customer_id,
                'session_id' => $cart->session_id,
                'total_amount' => $cart->total_amount,
                'tax_amount' => $cart->tax_amount,
                'discount_amount' => $cart->discount_amount,
                'delivery_charges' => $cart->delivery_charges,
                'net_amount' => $cart->net_amount,
                'status' => $cart->status,
                'items' => $cartItems,
                'items_count' => $cartItems->count(),
                'created_at' => $cart->created_at,
                'updated_at' => $cart->updated_at,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Cart retrieved successfully',
                'data' => $cartData
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve cart', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add an item to the cart.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function addItem(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
                'product_id' => 'required|integer|exists:products,pk_product_code',
                'quantity' => 'required|integer|min:1',
                'unit_price' => 'required|numeric|min:0',
                'menu_type' => 'nullable|in:breakfast,lunch,dinner',
                'delivery_date' => 'nullable|date|after_or_equal:today',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $customerId = $data['customer_id'];
            $productId = $data['product_id'];
            $quantity = $data['quantity'];
            $unitPrice = $data['unit_price'];
            $totalPrice = $quantity * $unitPrice;

            // Get or create cart for customer
            $cart = DB::table('carts')
                ->where('customer_id', $customerId)
                ->where('status', 'active')
                ->first();

            if (!$cart) {
                $cartId = DB::table('carts')->insertGetId([
                    'customer_id' => $customerId,
                    'session_id' => 'session_' . $customerId . '_' . time(),
                    'total_amount' => 0,
                    'tax_amount' => 0,
                    'discount_amount' => 0,
                    'delivery_charges' => 0,
                    'net_amount' => 0,
                    'status' => 'active',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            } else {
                $cartId = $cart->id;
            }

            // Check if item already exists in cart
            $existingItem = DB::table('cart_items')
                ->where('cart_id', $cartId)
                ->where('product_id', $productId)
                ->first();

            if ($existingItem) {
                // Update existing item
                $newQuantity = $existingItem->quantity + $quantity;
                $newTotalPrice = $newQuantity * $unitPrice;

                DB::table('cart_items')
                    ->where('id', $existingItem->id)
                    ->update([
                        'quantity' => $newQuantity,
                        'unit_price' => $unitPrice,
                        'total_price' => $newTotalPrice,
                        'updated_at' => now(),
                    ]);

                $cartItemId = $existingItem->id;
            } else {
                // Create new cart item
                $cartItemId = DB::table('cart_items')->insertGetId([
                    'cart_id' => $cartId,
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'menu_type' => $data['menu_type'] ?? 'lunch',
                    'delivery_date' => $data['delivery_date'] ?? now()->addDay()->format('Y-m-d'),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Update cart totals
            $this->updateCartTotals($cartId);

            // Get the cart item with product details
            $cartItem = DB::table('cart_items as ci')
                ->join('products as p', 'ci.product_id', '=', 'p.pk_product_code')
                ->where('ci.id', $cartItemId)
                ->select(
                    'ci.id',
                    'ci.product_id',
                    'ci.quantity',
                    'ci.unit_price',
                    'ci.total_price',
                    'ci.menu_type',
                    'ci.delivery_date',
                    'p.name as product_name',
                    'p.unit'
                )
                ->first();

            return response()->json([
                'success' => true,
                'message' => 'Item added to cart successfully',
                'data' => $cartItem
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to add item to cart', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add item to cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update cart totals based on cart items.
     *
     * @param int $cartId
     * @return void
     */
    private function updateCartTotals(int $cartId): void
    {
        $totalAmount = DB::table('cart_items')
            ->where('cart_id', $cartId)
            ->sum('total_price');

        $taxAmount = $totalAmount * 0.18; // 18% tax
        $deliveryCharges = 50; // Fixed delivery charge
        $netAmount = $totalAmount + $taxAmount + $deliveryCharges;

        DB::table('carts')
            ->where('id', $cartId)
            ->update([
                'total_amount' => $totalAmount,
                'tax_amount' => $taxAmount,
                'delivery_charges' => $deliveryCharges,
                'net_amount' => $netAmount,
                'updated_at' => now(),
            ]);
    }

    /**
     * Update a cart item.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateItem(Request $request, string $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'quantity' => 'required|integer|min:0',
                'unit_price' => 'nullable|numeric|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $quantity = $data['quantity'];

            $cartItem = DB::table('cart_items')->where('id', $id)->first();

            if (!$cartItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart item not found'
                ], 404);
            }

            if ($quantity == 0) {
                // Remove item if quantity is 0
                DB::table('cart_items')->where('id', $id)->delete();
                $this->updateCartTotals($cartItem->cart_id);

                return response()->json([
                    'success' => true,
                    'message' => 'Cart item removed successfully'
                ]);
            } else {
                // Update item quantity
                $unitPrice = $data['unit_price'] ?? $cartItem->unit_price;
                $totalPrice = $quantity * $unitPrice;

                DB::table('cart_items')
                    ->where('id', $id)
                    ->update([
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'total_price' => $totalPrice,
                        'updated_at' => now(),
                    ]);

                $this->updateCartTotals($cartItem->cart_id);

                // Get updated cart item with product details
                $updatedCartItem = DB::table('cart_items as ci')
                    ->join('products as p', 'ci.product_id', '=', 'p.pk_product_code')
                    ->where('ci.id', $id)
                    ->select(
                        'ci.id',
                        'ci.product_id',
                        'ci.quantity',
                        'ci.unit_price',
                        'ci.total_price',
                        'ci.menu_type',
                        'ci.delivery_date',
                        'p.name as product_name',
                        'p.unit'
                    )
                    ->first();

                return response()->json([
                    'success' => true,
                    'message' => 'Cart item updated successfully',
                    'data' => $updatedCartItem
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to update cart item', [
                'cart_item_id' => $id,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update cart item',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove an item from the cart.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function removeItem(Request $request, string $id): JsonResponse
    {
        try {
            $cartItem = DB::table('cart_items')->where('id', $id)->first();

            if (!$cartItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart item not found'
                ], 404);
            }

            DB::table('cart_items')->where('id', $id)->delete();
            $this->updateCartTotals($cartItem->cart_id);

            return response()->json([
                'success' => true,
                'message' => 'Cart item removed successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to remove cart item', [
                'cart_item_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove cart item',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear the cart.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clearCart(Request $request): JsonResponse
    {
        try {
            $customerId = $request->input('customer_id', 1);

            $cart = DB::table('carts')
                ->where('customer_id', $customerId)
                ->where('status', 'active')
                ->first();

            if (!$cart) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active cart found'
                ], 404);
            }

            // Delete all cart items
            DB::table('cart_items')->where('cart_id', $cart->id)->delete();

            // Update cart totals to zero
            DB::table('carts')
                ->where('id', $cart->id)
                ->update([
                    'total_amount' => 0,
                    'tax_amount' => 0,
                    'delivery_charges' => 0,
                    'net_amount' => 0,
                    'updated_at' => now(),
                ]);

            return response()->json([
                'success' => true,
                'message' => 'Cart cleared successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to clear cart', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

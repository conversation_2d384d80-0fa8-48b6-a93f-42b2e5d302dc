<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\ThemeConfigRequest;
use App\Http\Requests\ThemeRequest;
use App\Services\ThemeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ThemeController extends Controller
{
    /**
     * The theme service instance.
     */
    protected ThemeService $themeService;

    /**
     * Create a new controller instance.
     */
    public function __construct(ThemeService $themeService)
    {
        $this->themeService = $themeService;
    }

    /**
     * Display a listing of the themes.
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $themes = $this->themeService->getAllThemes();

        return response()->json([
            'data' => $themes,
        ]);
    }

    /**
     * Store a newly created theme in storage.
     *
     * @param ThemeRequest $request
     * @return JsonResponse
     */
    public function store(ThemeRequest $request): JsonResponse
    {
        $validatedData = $request->validated();

        $theme = $this->themeService->createTheme($validatedData);

        return response()->json([
            'message' => 'Theme created successfully',
            'data' => $theme,
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified theme.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $theme = $this->themeService->getThemeById($id);

        if (!$theme) {
            return response()->json([
                'message' => 'Theme not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => $theme,
        ]);
    }

    /**
     * Update the specified theme in storage.
     *
     * @param ThemeRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(ThemeRequest $request, int $id): JsonResponse
    {
        $validatedData = $request->validated();

        $theme = $this->themeService->updateTheme($id, $validatedData);

        if (!$theme) {
            return response()->json([
                'message' => 'Theme not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Theme updated successfully',
            'data' => $theme,
        ]);
    }

    /**
     * Remove the specified theme from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $result = $this->themeService->deleteTheme($id);

        if (!$result) {
            return response()->json([
                'message' => 'Theme not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Theme deleted successfully',
        ]);
    }

    /**
     * Get the active theme.
     *
     * @return JsonResponse
     */
    public function getActiveTheme(): JsonResponse
    {
        $theme = $this->themeService->getActiveTheme();

        if (!$theme) {
            return response()->json([
                'message' => 'No active theme found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => $theme,
        ]);
    }

    /**
     * Set the active theme.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function setActiveTheme(int $id): JsonResponse
    {
        $theme = $this->themeService->setActiveTheme($id);

        if (!$theme) {
            return response()->json([
                'message' => 'Theme not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Active theme set successfully',
            'data' => $theme,
        ]);
    }

    /**
     * Get theme configuration.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getThemeConfig(int $id): JsonResponse
    {
        $config = $this->themeService->getThemeConfig($id);

        if ($config === null) {
            return response()->json([
                'message' => 'Theme not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => $config,
        ]);
    }

    /**
     * Update theme configuration.
     *
     * @param ThemeConfigRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateThemeConfig(ThemeConfigRequest $request, int $id): JsonResponse
    {
        $validatedData = $request->validated();

        $theme = $this->themeService->updateThemeConfig($id, $validatedData['config']);

        if (!$theme) {
            return response()->json([
                'message' => 'Theme not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Theme configuration updated successfully',
            'data' => $theme,
        ]);
    }
}

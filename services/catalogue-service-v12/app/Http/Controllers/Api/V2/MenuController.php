<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MenuController extends Controller
{

    /**
     * Display a listing of the menus.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 15);
            $type = $request->input('type');
            $kitchenId = $request->input('kitchen_id');
            $status = $request->input('status', true);

            $query = DB::table('menus')
                ->select('id', 'name', 'type', 'kitchen_id', 'cut_off_time', 'cut_off_interval', 'status', 'created_at', 'updated_at');

            if ($type) {
                $query->where('type', $type);
            }

            if ($kitchenId) {
                $query->where('kitchen_id', $kitchenId);
            }

            if ($status !== null) {
                $query->where('status', $status);
            }

            $menus = $query->orderBy('type', 'asc')->orderBy('name', 'asc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Menus retrieved successfully',
                'data' => $menus->items(),
                'meta' => [
                    'current_page' => $menus->currentPage(),
                    'last_page' => $menus->lastPage(),
                    'per_page' => $menus->perPage(),
                    'total' => $menus->total(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve menus', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve menus',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get menus by type.
     *
     * @param string $type
     * @return JsonResponse
     */
    public function getByType(string $type): JsonResponse
    {
        try {
            if (!in_array($type, ['breakfast', 'lunch', 'dinner'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid menu type. Must be: breakfast, lunch, or dinner'
                ], 400);
            }

            $menus = DB::table('menus')
                ->select('id', 'name', 'type', 'kitchen_id', 'cut_off_time', 'cut_off_interval', 'status', 'created_at', 'updated_at')
                ->where('type', $type)
                ->where('status', true)
                ->orderBy('name', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => "Menus of type '{$type}' retrieved successfully",
                'data' => $menus
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve menus by type', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve menus by type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get menus by kitchen ID.
     *
     * @param string $kitchenId
     * @return JsonResponse
     */
    public function getByKitchen(string $kitchenId): JsonResponse
    {
        try {
            $menus = DB::table('menus')
                ->select('id', 'name', 'type', 'kitchen_id', 'cut_off_time', 'cut_off_interval', 'status', 'created_at', 'updated_at')
                ->where('kitchen_id', $kitchenId)
                ->where('status', true)
                ->orderBy('type', 'asc')
                ->orderBy('name', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => "Menus for kitchen '{$kitchenId}' retrieved successfully",
                'data' => $menus
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve menus by kitchen', [
                'kitchen_id' => $kitchenId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve menus by kitchen',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

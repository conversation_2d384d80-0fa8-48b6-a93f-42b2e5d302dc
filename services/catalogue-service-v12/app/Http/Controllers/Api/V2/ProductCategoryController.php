<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ProductCategoryController extends Controller
{
    /**
     * Display a listing of product categories.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 15);
            $type = $request->input('type');
            $status = $request->input('status');

            $query = DB::table('product_categories')
                ->select('id', 'product_category_name', 'description', 'image_path', 'type', 'sequence', 'status', 'created_at', 'updated_at')
                ->orderBy('sequence', 'asc')
                ->orderBy('product_category_name', 'asc');

            if ($type) {
                $query->where('type', $type);
            }

            if ($status !== null) {
                $query->where('status', $status);
            }

            $categories = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Product categories retrieved successfully',
                'data' => $categories->items(),
                'meta' => [
                    'current_page' => $categories->currentPage(),
                    'last_page' => $categories->lastPage(),
                    'per_page' => $categories->perPage(),
                    'total' => $categories->total(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve product categories', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve product categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created product category.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_category_name' => 'required|string|max:255|unique:product_categories,product_category_name',
                'description' => 'nullable|string',
                'image_path' => 'nullable|string|max:255',
                'type' => 'required|in:meal,product,extra',
                'sequence' => 'nullable|integer|min:0',
                'status' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['status'] = $data['status'] ?? true;
            $data['sequence'] = $data['sequence'] ?? 0;
            $data['created_at'] = now();
            $data['updated_at'] = now();

            $categoryId = DB::table('product_categories')->insertGetId($data);
            $category = DB::table('product_categories')->where('id', $categoryId)->first();

            return response()->json([
                'success' => true,
                'message' => 'Product category created successfully',
                'data' => $category
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create product category', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create product category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified product category.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $category = DB::table('product_categories')->where('id', $id)->first();

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product category not found'
                ], 404);
            }

            // Get products count for this category
            $productsCount = DB::table('products')->where('product_category_id', $id)->count();
            $category->products_count = $productsCount;

            return response()->json([
                'success' => true,
                'message' => 'Product category retrieved successfully',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve product category', [
                'category_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve product category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified product category.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $category = DB::table('product_categories')->where('id', $id)->first();

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product category not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'product_category_name' => 'sometimes|string|max:255|unique:product_categories,product_category_name,' . $id,
                'description' => 'nullable|string',
                'image_path' => 'nullable|string|max:255',
                'type' => 'sometimes|in:meal,product,extra',
                'sequence' => 'nullable|integer|min:0',
                'status' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['updated_at'] = now();

            DB::table('product_categories')->where('id', $id)->update($data);
            $updatedCategory = DB::table('product_categories')->where('id', $id)->first();

            return response()->json([
                'success' => true,
                'message' => 'Product category updated successfully',
                'data' => $updatedCategory
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update product category', [
                'category_id' => $id,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update product category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified product category.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $category = DB::table('product_categories')->where('id', $id)->first();

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product category not found'
                ], 404);
            }

            // Check if category has products
            $productsCount = DB::table('products')->where('product_category_id', $id)->count();
            if ($productsCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete category with associated products',
                    'products_count' => $productsCount
                ], 400);
            }

            DB::table('product_categories')->where('id', $id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product category deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete product category', [
                'category_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product categories by type.
     *
     * @param string $type
     * @param Request $request
     * @return JsonResponse
     */
    public function getByType(string $type, Request $request): JsonResponse
    {
        try {
            if (!in_array($type, ['meal', 'product', 'extra'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid category type. Must be: meal, product, or extra'
                ], 400);
            }

            $perPage = $request->input('per_page', 15);
            $status = $request->input('status', true);

            $categories = DB::table('product_categories')
                ->select('id', 'product_category_name', 'description', 'image_path', 'type', 'sequence', 'status', 'created_at', 'updated_at')
                ->where('type', $type)
                ->where('status', $status)
                ->orderBy('sequence', 'asc')
                ->orderBy('product_category_name', 'asc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => "Product categories of type '{$type}' retrieved successfully",
                'data' => $categories->items(),
                'meta' => [
                    'current_page' => $categories->currentPage(),
                    'last_page' => $categories->lastPage(),
                    'per_page' => $categories->perPage(),
                    'total' => $categories->total(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve product categories by type', [
                'type' => $type,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve product categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

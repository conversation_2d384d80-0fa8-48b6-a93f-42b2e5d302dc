<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class CatalogueController extends Controller
{
    /**
     * Display a listing of the products.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 15);
            $kitchenCode = $request->input('kitchen_code');
            $name = $request->input('name');

            $query = DB::table('products')
                ->select('pk_product_code', 'name', 'kitchen_code', 'quantity', 'unit', 'recipe', 'screen', 'created_at', 'updated_at');

            if ($kitchenCode) {
                $query->where('kitchen_code', $kitchenCode);
            }

            if ($name) {
                $query->where('name', 'like', "%{$name}%");
            }

            $products = $query->orderBy('name', 'asc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Products retrieved successfully',
                'data' => $products->items(),
                'meta' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve products', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve products',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created product in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'kitchen_code' => 'required|string|max:255',
                'quantity' => 'required|numeric|min:0',
                'unit' => 'required|string|max:255',
                'recipe' => 'nullable|string',
                'screen' => 'nullable|integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['created_at'] = now();
            $data['updated_at'] = now();

            // Generate unique product code
            $lastProduct = DB::table('products')->orderBy('pk_product_code', 'desc')->first();
            $data['pk_product_code'] = $lastProduct ? $lastProduct->pk_product_code + 1 : 1001;

            $productId = DB::table('products')->insertGetId($data);
            $product = DB::table('products')->where('pk_product_code', $productId)->first();

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => $product
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create product', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified product.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $product = DB::table('products')->where('pk_product_code', $id)->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Product retrieved successfully',
                'data' => $product
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve product', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified product in storage.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $product = DB::table('products')->where('pk_product_code', $id)->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'kitchen_code' => 'sometimes|string|max:255',
                'quantity' => 'sometimes|numeric|min:0',
                'unit' => 'sometimes|string|max:255',
                'recipe' => 'nullable|string',
                'screen' => 'nullable|integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['updated_at'] = now();

            DB::table('products')->where('pk_product_code', $id)->update($data);
            $updatedProduct = DB::table('products')->where('pk_product_code', $id)->first();

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'data' => $updatedProduct
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update product', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified product from storage.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $product = DB::table('products')->where('pk_product_code', $id)->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            // Check if product is used in cart items
            $cartItemsCount = DB::table('cart_items')->where('product_id', $id)->count();
            if ($cartItemsCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete product with associated cart items',
                    'cart_items_count' => $cartItemsCount
                ], 400);
            }

            DB::table('products')->where('pk_product_code', $id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete product', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get products by category.
     *
     * @param string $categoryId
     * @param Request $request
     * @return JsonResponse
     */
    public function getByCategory(string $categoryId, Request $request): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 15);

            // Check if category exists
            $category = DB::table('product_categories')->where('id', $categoryId)->first();
            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product category not found'
                ], 404);
            }

            $products = DB::table('products')
                ->select('pk_product_code', 'name', 'kitchen_code', 'quantity', 'unit', 'recipe', 'screen', 'created_at', 'updated_at')
                ->where('product_category_id', $categoryId)
                ->orderBy('name', 'asc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => "Products in category '{$category->product_category_name}' retrieved successfully",
                'data' => $products->items(),
                'meta' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve products by category', [
                'category_id' => $categoryId,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve products by category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search products by name or description.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:1',
                'per_page' => 'nullable|integer|min:1|max:100',
                'kitchen_code' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $perPage = $data['per_page'] ?? 15;
            $query = $data['query'];
            $kitchenCode = $data['kitchen_code'] ?? null;

            $productsQuery = DB::table('products')
                ->select('pk_product_code', 'name', 'kitchen_code', 'quantity', 'unit', 'recipe', 'screen', 'created_at', 'updated_at')
                ->where(function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('recipe', 'like', "%{$query}%");
                });

            if ($kitchenCode) {
                $productsQuery->where('kitchen_code', $kitchenCode);
            }

            $products = $productsQuery->orderBy('name', 'asc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Products search completed successfully',
                'data' => $products->items(),
                'meta' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                    'search_query' => $query,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to search products', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to search products',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

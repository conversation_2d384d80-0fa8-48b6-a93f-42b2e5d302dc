<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\AddPlanMealItemRequest;
use App\Http\Requests\ApplyPromoCodeRequest;
use App\Http\Requests\CheckoutRequest;
use App\Http\Requests\PlanMealRequest;
use App\Http\Requests\UpdatePlanMealItemRequest;
use App\Services\PlanMealService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PlanMealController extends Controller
{
    /**
     * The plan meal service instance.
     */
    protected PlanMealService $planMealService;

    /**
     * Create a new controller instance.
     */
    public function __construct(PlanMealService $planMealService)
    {
        $this->planMealService = $planMealService;
    }

    /**
     * Display a listing of the plan meals.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only([
            'customer_id', 'kitchen_id', 'menu_type', 'status', 'start_date', 'end_date'
        ]);
        $perPage = $request->input('per_page', 15);

        $planMeals = $this->planMealService->getAllPlanMeals($filters, $perPage);

        return response()->json([
            'data' => $planMeals->items(),
            'meta' => [
                'current_page' => $planMeals->currentPage(),
                'last_page' => $planMeals->lastPage(),
                'per_page' => $planMeals->perPage(),
                'total' => $planMeals->total(),
            ],
        ]);
    }

    /**
     * Store a newly created plan meal in storage.
     *
     * @param PlanMealRequest $request
     * @return JsonResponse
     */
    public function store(PlanMealRequest $request): JsonResponse
    {
        $validatedData = $request->validated();

        $planMeal = $this->planMealService->createPlanMeal($validatedData);

        return response()->json([
            'message' => 'Plan meal created successfully',
            'data' => $planMeal,
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified plan meal.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $planMeal = $this->planMealService->getPlanMealById($id);

        if (!$planMeal) {
            return response()->json([
                'message' => 'Plan meal not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => $planMeal,
        ]);
    }

    /**
     * Update the specified plan meal in storage.
     *
     * @param PlanMealRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(PlanMealRequest $request, int $id): JsonResponse
    {
        $validatedData = $request->validated();

        $planMeal = $this->planMealService->updatePlanMeal($id, $validatedData);

        if (!$planMeal) {
            return response()->json([
                'message' => 'Plan meal not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Plan meal updated successfully',
            'data' => $planMeal,
        ]);
    }

    /**
     * Remove the specified plan meal from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $result = $this->planMealService->deletePlanMeal($id);

        if (!$result) {
            return response()->json([
                'message' => 'Plan meal not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Plan meal deleted successfully',
        ]);
    }

    /**
     * Get plan meals by customer ID.
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function getByCustomer(Request $request, int $customerId): JsonResponse
    {
        $perPage = $request->input('per_page', 15);
        $planMeals = $this->planMealService->getPlanMealsByCustomerId($customerId, $perPage);

        return response()->json([
            'data' => $planMeals->items(),
            'meta' => [
                'current_page' => $planMeals->currentPage(),
                'last_page' => $planMeals->lastPage(),
                'per_page' => $planMeals->perPage(),
                'total' => $planMeals->total(),
            ],
        ]);
    }

    /**
     * Add an item to a plan meal.
     *
     * @param AddPlanMealItemRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function addItem(AddPlanMealItemRequest $request, int $id): JsonResponse
    {
        $validatedData = $request->validated();

        $planMealItem = $this->planMealService->addPlanMealItem($id, $validatedData);

        return response()->json([
            'message' => 'Item added to plan meal successfully',
            'data' => $planMealItem,
        ], Response::HTTP_CREATED);
    }

    /**
     * Update a plan meal item.
     *
     * @param UpdatePlanMealItemRequest $request
     * @param int $id
     * @param int $itemId
     * @return JsonResponse
     */
    public function updateItem(UpdatePlanMealItemRequest $request, int $id, int $itemId): JsonResponse
    {
        $validatedData = $request->validated();

        $planMealItem = $this->planMealService->updatePlanMealItem($itemId, $validatedData);

        if (!$planMealItem) {
            return response()->json([
                'message' => 'Plan meal item not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Plan meal item updated successfully',
            'data' => $planMealItem,
        ]);
    }

    /**
     * Remove a plan meal item.
     *
     * @param int $id
     * @param int $itemId
     * @return JsonResponse
     */
    public function removeItem(int $id, int $itemId): JsonResponse
    {
        $result = $this->planMealService->removePlanMealItem($itemId);

        if (!$result) {
            return response()->json([
                'message' => 'Plan meal item not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Plan meal item removed successfully',
        ]);
    }

    /**
     * Apply a promo code to a plan meal.
     *
     * @param ApplyPromoCodeRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function applyPromoCode(ApplyPromoCodeRequest $request, int $id): JsonResponse
    {
        $validatedData = $request->validated();

        $planMeal = $this->planMealService->applyPromoCode($id, $validatedData['promo_code']);

        if (!$planMeal) {
            return response()->json([
                'message' => 'Plan meal not found',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Promo code applied successfully',
            'data' => $planMeal,
        ]);
    }

    /**
     * Checkout a plan meal.
     *
     * @param CheckoutRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function checkout(CheckoutRequest $request, int $id): JsonResponse
    {
        $validatedData = $request->validated();

        // Get the plan meal
        $planMeal = $this->planMealService->getPlanMealById($id);

        if (!$planMeal) {
            return response()->json([
                'message' => 'Plan meal not found',
            ], Response::HTTP_NOT_FOUND);
        }

        // Check authorization
        if ($request->user() && !$request->user()->can('checkout', $planMeal)) {
            return response()->json([
                'message' => 'Unauthorized',
            ], Response::HTTP_FORBIDDEN);
        }

        $result = $this->planMealService->checkout($id, $validatedData);

        if (!$result['success']) {
            return response()->json([
                'message' => $result['message'],
            ], Response::HTTP_BAD_REQUEST);
        }

        return response()->json([
            'message' => $result['message'],
            'data' => [
                'subscription_id' => $result['subscription_id'],
                'subscription_number' => $result['subscription_number'] ?? null,
            ],
        ]);
    }
}

<?php

namespace App\Repositories;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Repositories\Interfaces\ProductRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductRepository implements ProductRepositoryInterface
{
    /**
     * Get all products with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllProducts(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Product::query();

        if (isset($filters['food_type'])) {
            $query->where('food_type', $filters['food_type']);
        }

        if (isset($filters['kitchen_id'])) {
            $query->where('kitchen_id', $filters['kitchen_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['product_category_id'])) {
            $query->where('product_category_id', $filters['product_category_id']);
        }

        return $query->with(['category', 'kitchen'])->orderBy('sequence', 'desc')->paginate($perPage);
    }

    /**
     * Get a product by ID.
     *
     * @param int $id
     * @return Product|null
     */
    public function getProductById(int $id): ?Product
    {
        return Product::with(['category', 'kitchen', 'swapProduct'])->find($id);
    }

    /**
     * Create a new product.
     *
     * @param array $data
     * @return Product
     */
    public function createProduct(array $data): Product
    {
        return Product::create($data);
    }

    /**
     * Update a product.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateProduct(int $id, array $data): bool
    {
        $product = Product::find($id);

        if (!$product) {
            return false;
        }

        return $product->update($data);
    }

    /**
     * Delete a product.
     *
     * @param int $id
     * @return bool
     */
    public function deleteProduct(int $id): bool
    {
        $product = Product::find($id);

        if (!$product) {
            return false;
        }

        return $product->delete();
    }

    /**
     * Get products by category.
     *
     * @param int $categoryId
     * @return Collection
     */
    public function getProductsByCategory(int $categoryId): Collection
    {
        return Product::whereHas('categories', function ($query) use ($categoryId) {
            $query->where('categories.id', $categoryId);
        })
            ->where('status', true)
            ->with(['categories', 'kitchen'])
            ->orderBy('sequence', 'desc')
            ->get();
    }

    /**
     * Search products by name or description.
     *
     * @param string $query
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function searchProducts(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return Product::where('name', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->where('status', true)
            ->with(['categories', 'kitchen'])
            ->orderBy('sequence', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get products by menu.
     *
     * @param int $menuId
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getProductsByMenu(int $menuId, array $filters = []): LengthAwarePaginator
    {
        $query = Product::query();

        // Add menu-specific filtering logic here
        $query->where('menu_id', $menuId);

        if (isset($filters['food_type'])) {
            $query->where('food_type', $filters['food_type']);
        }

        if (isset($filters['category_id'])) {
            $query->whereHas('categories', function ($q) use ($filters) {
                $q->where('categories.id', $filters['category_id']);
            });
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        $perPage = $filters['per_page'] ?? 15;

        return $query->with(['categories', 'kitchen'])
            ->orderBy('sequence', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get extra products.
     *
     * @return Collection
     */
    public function getExtraProducts(): Collection
    {
        return Product::where('is_extra', true)
            ->where('status', true)
            ->with(['categories', 'kitchen'])
            ->orderBy('sequence', 'desc')
            ->get();
    }
}
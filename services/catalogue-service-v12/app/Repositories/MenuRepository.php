<?php

namespace App\Repositories;

use App\Models\Menu;
use App\Repositories\Interfaces\MenuRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class MenuRepository implements MenuRepositoryInterface
{
    /**
     * Get all menus with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllMenus(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Menu::query();

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['kitchen_id'])) {
            $query->where('kitchen_id', $filters['kitchen_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->with('kitchen')->orderBy('name')->paginate($perPage);
    }

    /**
     * Get a menu by ID.
     *
     * @param int $id
     * @return Menu|null
     */
    public function getMenuById(int $id): ?Menu
    {
        return Menu::with('kitchen')->find($id);
    }

    /**
     * Create a new menu.
     *
     * @param array $data
     * @return Menu
     */
    public function createMenu(array $data): Menu
    {
        return Menu::create($data);
    }

    /**
     * Update a menu.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateMenu(int $id, array $data): bool
    {
        $menu = Menu::find($id);

        if (!$menu) {
            return false;
        }

        return $menu->update($data);
    }

    /**
     * Delete a menu.
     *
     * @param int $id
     * @return bool
     */
    public function deleteMenu(int $id): bool
    {
        $menu = Menu::find($id);

        if (!$menu) {
            return false;
        }

        return $menu->delete();
    }

    /**
     * Get menus by kitchen ID.
     *
     * @param int $kitchenId
     * @return Collection
     */
    public function getMenusByKitchenId(int $kitchenId): Collection
    {
        return Menu::where('kitchen_id', $kitchenId)
            ->where('status', true)
            ->orderBy('type')
            ->get();
    }

    /**
     * Get menus by type.
     *
     * @param string $type
     * @return Collection
     */
    public function getMenusByType(string $type): Collection
    {
        return Menu::where('type', $type)
            ->where('status', true)
            ->orderBy('name')
            ->get();
    }
}
<?php

namespace App\Repositories;

use App\Exceptions\Checkout\PaymentProcessingException;
use App\Exceptions\Checkout\SubscriptionCreationException;
use App\Models\PlanMeal;
use App\Models\PlanMealItem;
use App\Models\Product;
use App\Repositories\Interfaces\PlanMealRepositoryInterface;
use App\Services\CircuitBreakerService;
use App\Services\IdempotencyService;
use App\Services\RetryService;
use App\Services\SensitiveDataSanitizer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class PlanMealRepository implements PlanMealRepositoryInterface
{
    /**
     * Get all plan meals with optional filtering.
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getAllPlanMeals(array $filters = []): LengthAwarePaginator
    {
        $query = PlanMeal::query();

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['kitchen_id'])) {
            $query->where('kitchen_id', $filters['kitchen_id']);
        }

        if (isset($filters['menu_type'])) {
            $query->where('menu_type', $filters['menu_type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['start_date'])) {
            $query->where('start_date', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('end_date', '<=', $filters['end_date']);
        }

        $perPage = $filters['per_page'] ?? 15;

        return $query->with(['customer', 'kitchen', 'items.product'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get a plan meal by ID.
     *
     * @param int $id
     * @return PlanMeal|null
     */
    public function getPlanMealById(int $id): ?PlanMeal
    {
        return PlanMeal::with(['customer', 'kitchen', 'items.product'])->find($id);
    }

    /**
     * Create a new plan meal.
     *
     * @param array $data
     * @return PlanMeal
     */
    public function createPlanMeal(array $data): PlanMeal
    {
        // Start a database transaction
        DB::beginTransaction();

        try {
            // Create the plan meal
            $planMeal = PlanMeal::create([
                'customer_id' => $data['customer_id'],
                'kitchen_id' => $data['kitchen_id'],
                'menu_type' => $data['menu_type'],
                'start_date' => $data['start_date'],
                'end_date' => $data['end_date'],
                'quantity' => $data['quantity'] ?? 1,
                'negotiated_price' => $data['negotiated_price'] ?? 0,
                'service_charges' => $data['service_charges'] ?? 0,
                'tax' => $data['tax'] ?? 0,
                'discount' => $data['discount'] ?? 0,
                'total_bill_amount' => $data['total_bill_amount'] ?? 0,
                'promo_code' => $data['promo_code'] ?? null,
                'applied_taxes' => $data['applied_taxes'] ?? null,
                'status' => $data['status'] ?? 'active',
            ]);

            // Add items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $item) {
                    $this->addPlanMealItem($planMeal->id, $item);
                }
            }

            // Calculate totals
            $planMeal = $this->calculatePlanMealTotals($planMeal->id);

            // Commit the transaction
            DB::commit();

            return $planMeal;
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();

            // Log the error
            \Log::error('Failed to create plan meal: ' . $e->getMessage());

            throw $e;
        }
    }

    /**
     * Update a plan meal.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updatePlanMeal(int $id, array $data): bool
    {
        $planMeal = PlanMeal::find($id);

        if (!$planMeal) {
            return false;
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Update the plan meal
            $result = $planMeal->update($data);

            // Calculate totals
            $this->calculatePlanMealTotals($planMeal->id);

            // Commit the transaction
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();

            // Log the error
            \Log::error('Failed to update plan meal: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Delete a plan meal.
     *
     * @param int $id
     * @return bool
     */
    public function deletePlanMeal(int $id): bool
    {
        $planMeal = PlanMeal::find($id);

        if (!$planMeal) {
            return false;
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Delete all items
            PlanMealItem::where('plan_meal_id', $id)->delete();

            // Delete the plan meal
            $planMeal->delete();

            // Commit the transaction
            DB::commit();

            return true;
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();

            // Log the error
            \Log::error('Failed to delete plan meal: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Get plan meals by customer ID.
     *
     * @param int $customerId
     * @return Collection
     */
    public function getPlanMealsByCustomerId(int $customerId): Collection
    {
        return PlanMeal::where('customer_id', $customerId)
            ->with(['kitchen', 'items.product'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Add an item to a plan meal.
     *
     * @param int $planMealId
     * @param array $itemData
     * @return PlanMealItem
     */
    public function addPlanMealItem(int $planMealId, array $itemData): PlanMealItem
    {
        $product = Product::findOrFail($itemData['product_id']);

        $planMealItem = PlanMealItem::create([
            'plan_meal_id' => $planMealId,
            'product_id' => $itemData['product_id'],
            'delivery_date' => $itemData['delivery_date'],
            'quantity' => $itemData['quantity'] ?? 1,
            'unit_price' => $itemData['unit_price'] ?? $product->unit_price,
            'total_price' => ($itemData['unit_price'] ?? $product->unit_price) * ($itemData['quantity'] ?? 1),
        ]);

        // Recalculate plan meal totals
        $this->calculatePlanMealTotals($planMealId);

        return $planMealItem->load('product');
    }

    /**
     * Update a plan meal item.
     *
     * @param int $planMealId
     * @param int $itemId
     * @param array $itemData
     * @return bool
     */
    public function updatePlanMealItem(int $planMealId, int $itemId, array $itemData): bool
    {
        $planMealItem = PlanMealItem::where('plan_meal_id', $planMealId)
            ->where('id', $itemId)
            ->first();

        if (!$planMealItem) {
            return false;
        }

        $planMealItem->update($itemData);

        // Recalculate total price
        $planMealItem->total_price = $planMealItem->unit_price * $planMealItem->quantity;
        $result = $planMealItem->save();

        // Recalculate plan meal totals
        $this->calculatePlanMealTotals($planMealId);

        return $result;
    }

    /**
     * Remove a plan meal item.
     *
     * @param int $planMealId
     * @param int $itemId
     * @return bool
     */
    public function removePlanMealItem(int $planMealId, int $itemId): bool
    {
        $planMealItem = PlanMealItem::where('plan_meal_id', $planMealId)
            ->where('id', $itemId)
            ->first();

        if (!$planMealItem) {
            return false;
        }

        $result = $planMealItem->delete();

        // Recalculate plan meal totals
        $this->calculatePlanMealTotals($planMealId);

        return $result;
    }

    /**
     * Calculate plan meal totals.
     *
     * @param int $planMealId
     * @return bool
     */
    public function calculatePlanMealTotals(int $planMealId): bool
    {
        $planMeal = PlanMeal::with('items')->find($planMealId);

        if (!$planMeal) {
            return false;
        }

        // Calculate total amount
        $totalAmount = $planMeal->items->sum('total_price');

        // Calculate tax
        $taxAmount = 0;
        if ($planMeal->applied_taxes) {
            // This would typically involve a more complex tax calculation
            // based on the applied_taxes JSON field
            $taxAmount = $totalAmount * 0.05; // 5% tax as a placeholder
        }

        // Calculate net amount
        $netAmount = $totalAmount + $taxAmount + $planMeal->service_charges - $planMeal->discount;

        // Update plan meal
        return $planMeal->update([
            'negotiated_price' => $totalAmount,
            'tax' => $taxAmount,
            'total_bill_amount' => $netAmount,
        ]);
    }

    /**
     * Apply a promo code to a plan meal.
     *
     * @param int $planMealId
     * @param string $promoCode
     * @return PlanMeal|null
     */
    public function applyPromoCode(int $planMealId, string $promoCode): ?PlanMeal
    {
        $planMeal = PlanMeal::find($planMealId);

        if (!$planMeal) {
            return null;
        }

        // Call the promo code service to validate and get discount
        // This would typically be a call to another microservice
        try {
            $response = Http::post(config('services.promo.url') . '/validate', [
                'code' => $promoCode,
                'total_amount' => $planMeal->negotiated_price,
                'customer_id' => $planMeal->customer_id,
                'context' => 'plan_meal',
            ]);

            if ($response->successful()) {
                $promoData = $response->json();

                if ($promoData['valid']) {
                    $planMeal->promo_code = $promoCode;
                    $planMeal->discount = $promoData['discount_amount'];
                    $planMeal->save();

                    // Recalculate plan meal totals
                    return $this->calculatePlanMealTotals($planMealId);
                }
            }
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error applying promo code: ' . $e->getMessage());
        }

        // For now, we'll simulate a simple discount
        $planMeal->promo_code = $promoCode;
        $planMeal->discount = $planMeal->negotiated_price * 0.1; // 10% discount
        $planMeal->save();

        // Recalculate plan meal totals
        return $this->calculatePlanMealTotals($planMealId);
    }

    /**
     * Process plan meal checkout.
     *
     * @param int $planMealId
     * @param array $checkoutData
     * @return array
     */
    public function processPlanMealCheckout(int $planMealId, array $checkoutData): array
    {
        // Get or generate idempotency key
        $idempotencyKey = $checkoutData['idempotency_key'] ?? app(IdempotencyService::class)->generateKey();

        // Process with idempotency
        return app(IdempotencyService::class)->processWithIdempotency(
            $idempotencyKey,
            'plan_meal_checkout_' . $planMealId,
            function() use ($planMealId, $checkoutData) {
                $planMeal = PlanMeal::with('items.product')->find($planMealId);

                if (!$planMeal || $planMeal->items->isEmpty()) {
                    return [
                        'success' => false,
                        'message' => 'Plan meal is empty or not found',
                    ];
                }

                // Start a database transaction with explicit isolation level
                DB::beginTransaction();
                DB::statement('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE');

                try {
                    // Create subscription in the Subscription Service with retry and circuit breaker
                    $subscriptionData = app(RetryService::class)->execute(function() use ($planMeal, $checkoutData) {
                        return app(CircuitBreakerService::class)->execute('subscription_service', function() use ($planMeal, $checkoutData) {
                            $response = Http::post(config('services.subscription.url') . '/subscriptions', [
                                'customer_id' => $planMeal->customer_id,
                                'kitchen_id' => $planMeal->kitchen_id,
                                'menu_type' => $planMeal->menu_type,
                                'start_date' => $planMeal->start_date,
                                'end_date' => $planMeal->end_date,
                                'quantity' => $planMeal->quantity,
                                'total_amount' => $planMeal->negotiated_price,
                                'tax_amount' => $planMeal->tax,
                                'discount_amount' => $planMeal->discount,
                                'service_charges' => $planMeal->service_charges,
                                'net_amount' => $planMeal->total_bill_amount,
                                'promo_code' => $planMeal->promo_code,
                                'payment_method' => $checkoutData['payment_method'] ?? 'wallet',
                                'items' => $planMeal->items->map(function ($item) {
                                    return [
                                        'product_id' => $item->product_id,
                                        'delivery_date' => $item->delivery_date,
                                        'quantity' => $item->quantity,
                                        'unit_price' => $item->unit_price,
                                        'total_price' => $item->total_price,
                                    ];
                                })->toArray(),
                            ]);

                            if (!$response->successful()) {
                                throw new SubscriptionCreationException(
                                    'Failed to create subscription: ' . $response->body(),
                                    [
                                        'status_code' => $response->status(),
                                        'response_body' => $response->body(),
                                    ]
                                );
                            }

                            return $response->json();
                        });
                    }, 'subscription_creation');

                    // Process payment with retry and circuit breaker
                    if ($checkoutData['payment_method'] === 'wallet') {
                        // Deduct from wallet
                        app(RetryService::class)->execute(function() use ($planMeal, $subscriptionData) {
                            return app(CircuitBreakerService::class)->execute('wallet_service', function() use ($planMeal, $subscriptionData) {
                                $walletResponse = Http::post(config('services.wallet.url') . '/deduct', [
                                    'customer_id' => $planMeal->customer_id,
                                    'amount' => $planMeal->total_bill_amount,
                                    'subscription_id' => $subscriptionData['id'],
                                    'description' => 'Subscription payment',
                                ]);

                                if (!$walletResponse->successful()) {
                                    throw new PaymentProcessingException(
                                        'Failed to process wallet payment: ' . $walletResponse->body(),
                                        [
                                            'status_code' => $walletResponse->status(),
                                            'response_body' => $walletResponse->body(),
                                            'subscription_id' => $subscriptionData['id'],
                                        ]
                                    );
                                }

                                return $walletResponse->json();
                            });
                        }, 'wallet_payment');
                    } else {
                        // Handle other payment methods
                        // This would typically be a call to the Payment Service
                    }

                    // Update plan meal status
                    $planMeal->update([
                        'status' => 'completed',
                    ]);

                    // Commit the transaction
                    DB::commit();

                    return [
                        'success' => true,
                        'message' => 'Subscription created successfully',
                        'subscription_id' => $subscriptionData['id'],
                        'subscription_number' => $subscriptionData['subscription_number'] ?? null,
                    ];
                } catch (\Exception $e) {
                    // Rollback the transaction
                    DB::rollBack();

                    // Log the error with sanitized data
                    \Log::error('Checkout failed: ' . $e->getMessage(), app(SensitiveDataSanitizer::class)->sanitize([
                        'plan_meal_id' => $planMealId,
                        'exception' => get_class($e),
                        'context' => $e instanceof \App\Exceptions\Checkout\CheckoutException ? $e->getContext() : [],
                    ]));

                    return [
                        'success' => false,
                        'message' => 'Checkout failed: ' . $e->getMessage(),
                    ];
                }
            }
        );
    }
}
<?php

namespace App\Repositories;

use App\Models\Theme;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class ThemeRepository
{
    /**
     * Cache TTL in seconds.
     */
    private const CACHE_TTL = 900; // 15 minutes

    /**
     * Get all themes.
     *
     * @return Collection
     */
    public function getAllThemes(): Collection
    {
        return Cache::remember('themes.all', self::CACHE_TTL, function () {
            return Theme::orderBy('name')->get();
        });
    }

    /**
     * Get a theme by ID.
     *
     * @param int $id
     * @return Theme|null
     */
    public function getThemeById(int $id): ?Theme
    {
        return Cache::remember('themes.' . $id, self::CACHE_TTL, function () use ($id) {
            return Theme::find($id);
        });
    }

    /**
     * Get a theme by name.
     *
     * @param string $name
     * @return Theme|null
     */
    public function getThemeByName(string $name): ?Theme
    {
        return Cache::remember('themes.name.' . md5($name), self::CACHE_TTL, function () use ($name) {
            return Theme::where('name', $name)->first();
        });
    }

    /**
     * Get the active theme.
     *
     * @return Theme|null
     */
    public function getActiveTheme(): ?Theme
    {
        return Cache::remember('themes.active', self::CACHE_TTL, function () {
            return Theme::where('is_active', true)->first();
        });
    }

    /**
     * Create a new theme.
     *
     * @param array $data
     * @return Theme
     */
    public function createTheme(array $data): Theme
    {
        $theme = Theme::create([
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'config' => $data['config'] ?? null,
            'is_active' => $data['is_active'] ?? false,
        ]);

        // If this theme is set as active, deactivate all other themes
        if ($theme->is_active) {
            $this->deactivateOtherThemes($theme->id);
        }

        // Invalidate caches
        $this->invalidateThemeCache();

        return $theme;
    }

    /**
     * Update a theme.
     *
     * @param int $id
     * @param array $data
     * @return Theme|null
     */
    public function updateTheme(int $id, array $data): ?Theme
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return null;
        }

        $theme->update($data);

        // If this theme is set as active, deactivate all other themes
        if (isset($data['is_active']) && $data['is_active']) {
            $this->deactivateOtherThemes($theme->id);
        }

        // Invalidate caches
        $this->invalidateThemeCache();

        return $theme;
    }

    /**
     * Delete a theme.
     *
     * @param int $id
     * @return bool
     */
    public function deleteTheme(int $id): bool
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return false;
        }

        $result = $theme->delete();

        // Invalidate caches
        $this->invalidateThemeCache();

        return $result;
    }

    /**
     * Set the active theme.
     *
     * @param int $id
     * @return Theme|null
     */
    public function setActiveTheme(int $id): ?Theme
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return null;
        }

        // Deactivate all other themes
        $this->deactivateOtherThemes($theme->id);

        // Activate this theme
        $theme->is_active = true;
        $theme->save();

        // Invalidate caches
        $this->invalidateThemeCache();

        return $theme;
    }

    /**
     * Get theme configuration.
     *
     * @param int $id
     * @return array|null
     */
    public function getThemeConfig(int $id): ?array
    {
        return Cache::remember('themes.' . $id . '.config', self::CACHE_TTL, function () use ($id) {
            $theme = Theme::find($id);

            if (!$theme) {
                return null;
            }

            return $theme->config;
        });
    }

    /**
     * Update theme configuration.
     *
     * @param int $id
     * @param array $config
     * @return Theme|null
     */
    public function updateThemeConfig(int $id, array $config): ?Theme
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return null;
        }

        $theme->config = $config;
        $theme->save();

        // Invalidate caches
        $this->invalidateThemeCache();

        return $theme;
    }

    /**
     * Deactivate all themes except the one with the given ID.
     *
     * @param int $id
     * @return void
     */
    private function deactivateOtherThemes(int $id): void
    {
        Theme::where('id', '!=', $id)
            ->where('is_active', true)
            ->update(['is_active' => false]);
    }

    /**
     * Invalidate all theme-related caches.
     *
     * @return void
     */
    private function invalidateThemeCache(): void
    {
        // Clear specific caches
        Cache::forget('themes.all');
        Cache::forget('themes.active');

        // Clear theme-specific caches
        $themes = Theme::all();
        foreach ($themes as $theme) {
            Cache::forget('themes.' . $theme->id);
            Cache::forget('themes.' . $theme->id . '.config');
            Cache::forget('themes.name.' . md5($theme->name));
        }
    }
}
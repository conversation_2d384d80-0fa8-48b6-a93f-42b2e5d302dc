<?php

namespace App\Repositories\Interfaces;

use App\Models\PlanMeal;
use App\Models\PlanMealItem;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface PlanMealRepositoryInterface
{
    /**
     * Get all plan meals with optional filtering
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getAllPlanMeals(array $filters = []): LengthAwarePaginator;

    /**
     * Get a plan meal by ID
     *
     * @param int $id
     * @return PlanMeal|null
     */
    public function getPlanMealById(int $id): ?PlanMeal;

    /**
     * Get plan meals by customer ID
     *
     * @param int $customerId
     * @return Collection
     */
    public function getPlanMealsByCustomerId(int $customerId): Collection;

    /**
     * Create a new plan meal
     *
     * @param array $data
     * @return PlanMeal
     */
    public function createPlanMeal(array $data): PlanMeal;

    /**
     * Update an existing plan meal
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updatePlanMeal(int $id, array $data): bool;

    /**
     * Delete a plan meal
     *
     * @param int $id
     * @return bool
     */
    public function deletePlanMeal(int $id): bool;

    /**
     * Add an item to a plan meal
     *
     * @param int $planMealId
     * @param array $itemData
     * @return PlanMealItem
     */
    public function addItemToPlanMeal(int $planMealId, array $itemData): PlanMealItem;

    /**
     * Update a plan meal item
     *
     * @param int $planMealId
     * @param int $itemId
     * @param array $itemData
     * @return bool
     */
    public function updatePlanMealItem(int $planMealId, int $itemId, array $itemData): bool;

    /**
     * Remove a plan meal item
     *
     * @param int $planMealId
     * @param int $itemId
     * @return bool
     */
    public function removePlanMealItem(int $planMealId, int $itemId): bool;

    /**
     * Calculate plan meal totals
     *
     * @param int $planMealId
     * @return bool
     */
    public function calculatePlanMealTotals(int $planMealId): bool;
}

<?php

namespace App\Repositories\Interfaces;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface ProductRepositoryInterface
{
    /**
     * Get all products with optional filtering
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getAllProducts(array $filters = []): LengthAwarePaginator;

    /**
     * Get a product by ID
     *
     * @param int $id
     * @return Product|null
     */
    public function getProductById(int $id): ?Product;

    /**
     * Get products by category
     *
     * @param int $categoryId
     * @return Collection
     */
    public function getProductsByCategory(int $categoryId): Collection;

    /**
     * Get products by menu
     *
     * @param int $menuId
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getProductsByMenu(int $menuId, array $filters = []): LengthAwarePaginator;

    /**
     * Get extra products
     *
     * @return Collection
     */
    public function getExtraProducts(): Collection;

    /**
     * Create a new product
     *
     * @param array $data
     * @return Product
     */
    public function createProduct(array $data): Product;

    /**
     * Update an existing product
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateProduct(int $id, array $data): bool;

    /**
     * Delete a product
     *
     * @param int $id
     * @return bool
     */
    public function deleteProduct(int $id): bool;
}

<?php

namespace App\Repositories\Interfaces;

use App\Models\Cart;
use App\Models\CartItem;

interface CartRepositoryInterface
{
    /**
     * Get a cart by ID
     *
     * @param int $id
     * @return Cart|null
     */
    public function getCartById(int $id): ?Cart;

    /**
     * Get a cart by customer ID
     *
     * @param int $customerId
     * @return Cart|null
     */
    public function getCartByCustomerId(int $customerId): ?Cart;

    /**
     * Get a cart by session ID
     *
     * @param string $sessionId
     * @return Cart|null
     */
    public function getCartBySessionId(string $sessionId): ?Cart;

    /**
     * Create a new cart
     *
     * @param array $data
     * @return Cart
     */
    public function createCart(array $data): Cart;

    /**
     * Update an existing cart
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateCart(int $id, array $data): bool;

    /**
     * Delete a cart
     *
     * @param int $id
     * @return bool
     */
    public function deleteCart(int $id): bool;

    /**
     * Add an item to a cart
     *
     * @param int $cartId
     * @param array $itemData
     * @return CartItem
     */
    public function addItemToCart(int $cartId, array $itemData): CartItem;

    /**
     * Update a cart item
     *
     * @param int $cartId
     * @param int $itemId
     * @param array $itemData
     * @return bool
     */
    public function updateCartItem(int $cartId, int $itemId, array $itemData): bool;

    /**
     * Remove a cart item
     *
     * @param int $cartId
     * @param int $itemId
     * @return bool
     */
    public function removeCartItem(int $cartId, int $itemId): bool;

    /**
     * Clear a cart
     *
     * @param int $cartId
     * @return bool
     */
    public function clearCart(int $cartId): bool;

    /**
     * Apply a promo code to a cart
     *
     * @param int $cartId
     * @param string $promoCode
     * @return bool
     */
    public function applyPromoCode(int $cartId, string $promoCode): bool;

    /**
     * Calculate cart totals
     *
     * @param int $cartId
     * @return bool
     */
    public function calculateCartTotals(int $cartId): bool;
}

<?php

namespace App\Repositories\Interfaces;

use App\Models\Menu;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface MenuRepositoryInterface
{
    /**
     * Get all menus with optional filtering
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getAllMenus(array $filters = []): LengthAwarePaginator;

    /**
     * Get a menu by ID
     *
     * @param int $id
     * @return Menu|null
     */
    public function getMenuById(int $id): ?Menu;

    /**
     * Get menus by kitchen ID
     *
     * @param int $kitchenId
     * @return Collection
     */
    public function getMenusByKitchenId(int $kitchenId): Collection;

    /**
     * Create a new menu
     *
     * @param array $data
     * @return Menu
     */
    public function createMenu(array $data): Menu;

    /**
     * Update an existing menu
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateMenu(int $id, array $data): bool;

    /**
     * Delete a menu
     *
     * @param int $id
     * @return bool
     */
    public function deleteMenu(int $id): bool;
}

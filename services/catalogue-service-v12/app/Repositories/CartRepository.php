<?php

namespace App\Repositories;

use App\Exceptions\Checkout\OrderCreationException;
use App\Exceptions\Checkout\PaymentProcessingException;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use App\Repositories\Interfaces\CartRepositoryInterface;
use App\Services\CircuitBreakerService;
use App\Services\IdempotencyService;
use App\Services\PerformanceMetricsService;
use App\Services\RetryService;
use App\Services\SensitiveDataSanitizer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class CartRepository implements CartRepositoryInterface
{
    /**
     * Get a cart by ID.
     *
     * @param int $id
     * @return Cart|null
     */
    public function getCartById(int $id): ?Cart
    {
        return Cart::with('items.product')->find($id);
    }

    /**
     * Get a cart by customer ID.
     *
     * @param int $customerId
     * @return Cart|null
     */
    public function getCartByCustomerId(int $customerId): ?Cart
    {
        return Cart::with('items.product')
            ->where('customer_id', $customerId)
            ->latest()
            ->first();
    }

    /**
     * Get a cart by session ID.
     *
     * @param string $sessionId
     * @return Cart|null
     */
    public function getCartBySessionId(string $sessionId): ?Cart
    {
        return Cart::with('items.product')
            ->where('session_id', $sessionId)
            ->latest()
            ->first();
    }

    /**
     * Create a new cart.
     *
     * @param array $data
     * @return Cart
     */
    public function createCart(array $data): Cart
    {
        $cart = Cart::create($data);
        return $cart->load('items.product');
    }

    /**
     * Update an existing cart.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateCart(int $id, array $data): bool
    {
        $cart = Cart::find($id);
        if (!$cart) {
            return false;
        }
        return $cart->update($data);
    }

    /**
     * Delete a cart.
     *
     * @param int $id
     * @return bool
     */
    public function deleteCart(int $id): bool
    {
        $cart = Cart::find($id);
        if (!$cart) {
            return false;
        }
        return $cart->delete();
    }

    /**
     * Get or create a cart.
     *
     * @param int|null $customerId
     * @param string $sessionId
     * @return Cart
     */
    public function getOrCreateCart(?int $customerId = null, string $sessionId): Cart
    {
        $query = Cart::query();

        if ($customerId) {
            $query->where('customer_id', $customerId);
        } else {
            $query->where('session_id', $sessionId);
        }

        $cart = $query->first();

        if (!$cart) {
            $cart = Cart::create([
                'customer_id' => $customerId,
                'session_id' => $sessionId,
                'total_amount' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'delivery_charges' => 0,
                'net_amount' => 0,
                'status' => 'active',
            ]);
        }

        return $cart->load('items.product');
    }

    /**
     * Add an item to the cart.
     *
     * @param int $cartId
     * @param array $itemData
     * @return CartItem
     */
    public function addItemToCart(int $cartId, array $itemData): CartItem
    {
        $product = Product::findOrFail($itemData['product_id']);
        $menuId = $itemData['menu_id'] ?? null;
        $quantity = $itemData['quantity'] ?? 1;
        $deliveryDate = $itemData['delivery_date'] ?? null;
        $deliveryTime = $itemData['delivery_time'] ?? null;
        $customizations = $itemData['customizations'] ?? null;

        // Check if the item already exists in the cart
        $cartItem = CartItem::where('cart_id', $cartId)
            ->where('product_id', $itemData['product_id'])
            ->where('menu_id', $menuId)
            ->where('delivery_date', $deliveryDate)
            ->first();

        if ($cartItem) {
            // Update existing item
            $cartItem->quantity += $quantity;
            $cartItem->total_price = $cartItem->unit_price * $cartItem->quantity;
            $cartItem->save();
        } else {
            // Create new item
            $cartItem = CartItem::create([
                'cart_id' => $cartId,
                'product_id' => $itemData['product_id'],
                'menu_id' => $menuId,
                'quantity' => $quantity,
                'unit_price' => $itemData['unit_price'] ?? $product->price,
                'total_price' => ($itemData['unit_price'] ?? $product->price) * $quantity,
                'delivery_date' => $deliveryDate,
                'delivery_time' => $deliveryTime,
                'customizations' => $customizations,
            ]);
        }

        // Recalculate cart totals
        $this->calculateCartTotals($cartId);

        return $cartItem->load('product');
    }

    /**
     * Update a cart item.
     *
     * @param int $cartId
     * @param int $itemId
     * @param array $itemData
     * @return bool
     */
    public function updateCartItem(int $cartId, int $itemId, array $itemData): bool
    {
        $cartItem = CartItem::where('cart_id', $cartId)
            ->where('id', $itemId)
            ->first();

        if (!$cartItem) {
            return false;
        }

        $quantity = $itemData['quantity'] ?? $cartItem->quantity;

        if ($quantity <= 0) {
            $cartItem->delete();
            $this->calculateCartTotals($cartId);
            return true;
        }

        $product = Product::findOrFail($itemData['product_id'] ?? $cartItem->product_id);
        $unitPrice = $itemData['unit_price'] ?? $cartItem->unit_price ?? $product->price;

        $cartItem->product_id = $itemData['product_id'] ?? $cartItem->product_id;
        $cartItem->menu_id = $itemData['menu_id'] ?? $cartItem->menu_id;
        $cartItem->quantity = $quantity;
        $cartItem->unit_price = $unitPrice;
        $cartItem->total_price = $unitPrice * $quantity;
        $cartItem->delivery_date = $itemData['delivery_date'] ?? $cartItem->delivery_date;
        $cartItem->delivery_time = $itemData['delivery_time'] ?? $cartItem->delivery_time;
        $cartItem->customizations = $itemData['customizations'] ?? $cartItem->customizations;

        $result = $cartItem->save();

        // Recalculate cart totals
        $this->calculateCartTotals($cartId);

        return $result;
    }

    /**
     * Remove a cart item.
     *
     * @param int $cartId
     * @param int $itemId
     * @return bool
     */
    public function removeCartItem(int $cartId, int $itemId): bool
    {
        $result = CartItem::where('cart_id', $cartId)
            ->where('id', $itemId)
            ->delete();

        // Recalculate cart totals
        $this->calculateCartTotals($cartId);

        return $result > 0;
    }

    /**
     * Clear the cart.
     *
     * @param int $cartId
     * @return bool
     */
    public function clearCart(int $cartId): bool
    {
        $result = CartItem::where('cart_id', $cartId)->delete();

        // Reset cart totals
        $cart = Cart::find($cartId);
        if ($cart) {
            $cart->update([
                'total_amount' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'delivery_charges' => 0,
                'net_amount' => 0,
                'promo_code' => null,
            ]);
        }

        return $result > 0;
    }

    /**
     * Apply a promo code to the cart.
     *
     * @param int $cartId
     * @param string $promoCode
     * @return bool
     */
    public function applyPromoCode(int $cartId, string $promoCode): bool
    {
        $cart = Cart::find($cartId);

        if (!$cart) {
            return false;
        }

        // Call the promo code service to validate and get discount
        // This would typically be a call to another microservice
        try {
            $response = Http::post(config('services.promo.url') . '/validate', [
                'code' => $promoCode,
                'cart_total' => $cart->total_amount,
                'customer_id' => $cart->customer_id,
            ]);

            if ($response->successful()) {
                $promoData = $response->json();

                if ($promoData['valid']) {
                    $cart->promo_code = $promoCode;
                    $cart->discount_amount = $promoData['discount_amount'];
                    $cart->save();

                    // Recalculate cart totals
                    return $this->calculateCartTotals($cartId);
                }
            }
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error applying promo code: ' . $e->getMessage());
        }

        // For now, we'll simulate a simple discount
        $cart->promo_code = $promoCode;
        $cart->discount_amount = $cart->total_amount * 0.1; // 10% discount
        $cart->save();

        // Recalculate cart totals
        return $this->calculateCartTotals($cartId);
    }

    /**
     * Calculate cart totals.
     *
     * @param int $cartId
     * @return bool
     */
    public function calculateCartTotals(int $cartId): bool
    {
        $cart = Cart::with('items')->find($cartId);

        if (!$cart) {
            return false;
        }

        // Calculate total amount
        $totalAmount = $cart->items->sum('total_price');

        // Get delivery charges (could be from a config or service)
        $deliveryCharges = 0;
        if ($totalAmount > 0 && $totalAmount < config('cart.free_delivery_threshold', 500)) {
            $deliveryCharges = config('cart.delivery_charges', 50);
        }

        // Calculate tax (could be from a config or service)
        $taxRate = config('cart.tax_rate', 0.05); // 5% tax
        $taxAmount = $totalAmount * $taxRate;

        // Calculate net amount
        $netAmount = $totalAmount + $taxAmount + $deliveryCharges - $cart->discount_amount;

        // Update cart
        return $cart->update([
            'total_amount' => $totalAmount,
            'tax_amount' => $taxAmount,
            'delivery_charges' => $deliveryCharges,
            'net_amount' => $netAmount,
        ]);
    }

    /**
     * Process checkout.
     *
     * @param int $cartId
     * @param array $checkoutData
     * @return array
     */
    public function processCheckout(int $cartId, array $checkoutData): array
    {
        // Get or generate idempotency key
        $idempotencyKey = $checkoutData['idempotency_key'] ?? app(IdempotencyService::class)->generateKey();

        // Process with idempotency
        return app(IdempotencyService::class)->processWithIdempotency(
            $idempotencyKey,
            'cart_checkout_' . $cartId,
            function() use ($cartId, $checkoutData) {
                $metrics = app(PerformanceMetricsService::class);
                $metrics->startTimer('checkout_process');

                $cart = Cart::with('items.product')->find($cartId);

                if (!$cart || $cart->items->isEmpty()) {
                    return [
                        'success' => false,
                        'message' => 'Cart is empty',
                    ];
                }

                // Start a database transaction with explicit isolation level
                DB::beginTransaction();
                DB::statement('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE');

                try {
                    // Create order in the Order Service with retry and circuit breaker
                    $metrics->startTimer('order_creation');
                    $orderData = app(RetryService::class)->execute(function() use ($cart, $checkoutData) {
                        return app(CircuitBreakerService::class)->execute('order_service', function() use ($cart, $checkoutData) {
                            $response = Http::post(config('services.order.url') . '/orders', [
                                'customer_id' => $cart->customer_id,
                                'total_amount' => $cart->total_amount,
                                'tax_amount' => $cart->tax_amount,
                                'discount_amount' => $cart->discount_amount,
                                'delivery_charges' => $cart->delivery_charges,
                                'net_amount' => $cart->net_amount,
                                'promo_code' => $cart->promo_code,
                                'payment_method' => $checkoutData['payment_method'] ?? 'wallet',
                                'delivery_address' => $checkoutData['delivery_address'] ?? null,
                                'items' => $cart->items->map(function ($item) {
                                    return [
                                        'product_id' => $item->product_id,
                                        'quantity' => $item->quantity,
                                        'unit_price' => $item->unit_price,
                                        'total_price' => $item->total_price,
                                        'menu_type' => $item->menu_type,
                                        'delivery_date' => $item->delivery_date,
                                        'customizations' => $item->customizations,
                                    ];
                                })->toArray(),
                            ]);

                            if (!$response->successful()) {
                                throw new OrderCreationException(
                                    'Failed to create order: ' . $response->body(),
                                    [
                                        'status_code' => $response->status(),
                                        'response_body' => $response->body(),
                                    ]
                                );
                            }

                            return $response->json();
                        });
                    }, 'order_creation');
                    $metrics->endTimer('order_creation');

                    // Process payment with retry and circuit breaker
                    if ($checkoutData['payment_method'] === 'wallet') {
                        // Deduct from wallet
                        $metrics->startTimer('payment_processing');
                        app(RetryService::class)->execute(function() use ($cart, $orderData) {
                            return app(CircuitBreakerService::class)->execute('wallet_service', function() use ($cart, $orderData) {
                                $walletResponse = Http::post(config('services.wallet.url') . '/deduct', [
                                    'customer_id' => $cart->customer_id,
                                    'amount' => $cart->net_amount,
                                    'order_id' => $orderData['id'],
                                    'description' => 'Order payment',
                                ]);

                                if (!$walletResponse->successful()) {
                                    throw new PaymentProcessingException(
                                        'Failed to process wallet payment: ' . $walletResponse->body(),
                                        [
                                            'status_code' => $walletResponse->status(),
                                            'response_body' => $walletResponse->body(),
                                            'order_id' => $orderData['id'],
                                        ]
                                    );
                                }

                                return $walletResponse->json();
                            });
                        }, 'wallet_payment');
                        $metrics->endTimer('payment_processing');
                    } else {
                        // Handle other payment methods
                        // This would typically be a call to the Payment Service
                    }

                    // Commit the transaction
                    DB::commit();

                    // End the checkout process timer
                    $metrics->endTimer('checkout_process');

                    return [
                        'success' => true,
                        'message' => 'Order placed successfully',
                        'order_id' => $orderData['id'],
                        'order_number' => $orderData['order_number'] ?? null,
                        'metrics' => [
                            'checkout_duration_ms' => round($metrics->getDuration('checkout_process') * 1000, 2),
                            'order_creation_duration_ms' => round($metrics->getDuration('order_creation') * 1000, 2),
                            'payment_duration_ms' => $checkoutData['payment_method'] === 'wallet' ?
                                round($metrics->getDuration('payment_processing') * 1000, 2) : null,
                        ],
                    ];
                } catch (\Exception $e) {
                    // Rollback the transaction
                    DB::rollBack();

                    // End the checkout process timer
                    $metrics->endTimer('checkout_process');

                    // Log the error with sanitized data
                    \Log::error('Checkout failed: ' . $e->getMessage(), app(SensitiveDataSanitizer::class)->sanitize([
                        'cart_id' => $cartId,
                        'exception' => get_class($e),
                        'context' => $e instanceof \App\Exceptions\Checkout\CheckoutException ? $e->getContext() : [],
                        'checkout_duration_ms' => round($metrics->getDuration('checkout_process') * 1000, 2),
                    ]));

                    return [
                        'success' => false,
                        'message' => 'Checkout failed: ' . $e->getMessage(),
                    ];
                }
            }
        );
    }

    /**
     * Merge guest cart with customer cart.
     *
     * @param int $customerId
     * @param string $sessionId
     * @return Cart
     */
    public function mergeGuestCart(int $customerId, string $sessionId): Cart
    {
        // Get the guest cart
        $guestCart = Cart::where('session_id', $sessionId)
            ->whereNull('customer_id')
            ->with('items')
            ->first();

        // Get or create the customer cart
        $customerCart = $this->getOrCreateCart($customerId, $sessionId);

        // If there's a guest cart with items, merge it with the customer cart
        if ($guestCart && $guestCart->items->isNotEmpty()) {
            foreach ($guestCart->items as $item) {
                $this->addItemToCart(
                    $customerCart->id,
                    $item->product_id,
                    $item->quantity,
                    $item->menu_type,
                    $item->delivery_date,
                    $item->customizations
                );
            }

            // Delete the guest cart
            $guestCart->delete();
        }

        // Recalculate customer cart totals
        return $this->calculateCartTotals($customerCart->id);
    }
}
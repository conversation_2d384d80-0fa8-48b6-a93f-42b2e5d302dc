<?php

namespace App\Exceptions\Checkout;

class SubscriptionCreationException extends CheckoutException
{
    /**
     * Create a new subscription creation exception instance.
     *
     * @param string $message
     * @param array $context
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message, array $context = [], int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $context, $code, $previous);
    }
}

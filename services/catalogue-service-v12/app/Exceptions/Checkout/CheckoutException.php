<?php

namespace App\Exceptions\Checkout;

class CheckoutException extends \Exception
{
    /**
     * The context data for the exception.
     *
     * @var array
     */
    protected array $context;

    /**
     * Create a new checkout exception instance.
     *
     * @param string $message
     * @param array $context
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message, array $context = [], int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * Get the context data for the exception.
     *
     * @return array
     */
    public function getContext(): array
    {
        return $this->context;
    }
}

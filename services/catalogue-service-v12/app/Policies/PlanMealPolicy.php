<?php

namespace App\Policies;

use App\Models\PlanMeal;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PlanMealPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the plan meal.
     *
     * @param User $user
     * @param PlanMeal $planMeal
     * @return bool
     */
    public function view(User $user, PlanMeal $planMeal): bool
    {
        // Allow viewing if the plan meal belongs to the user
        return $planMeal->customer_id === $user->id;
    }

    /**
     * Determine whether the user can update the plan meal.
     *
     * @param User $user
     * @param PlanMeal $planMeal
     * @return bool
     */
    public function update(User $user, PlanMeal $planMeal): bool
    {
        // Allow updating if the plan meal belongs to the user and is not completed
        return $planMeal->customer_id === $user->id && $planMeal->status !== 'completed';
    }

    /**
     * Determine whether the user can checkout the plan meal.
     *
     * @param User $user
     * @param PlanMeal $planMeal
     * @return bool
     */
    public function checkout(User $user, PlanMeal $planMeal): bool
    {
        // Allow checkout if the plan meal belongs to the user, is not completed, and has items
        return $planMeal->customer_id === $user->id && 
               $planMeal->status !== 'completed' && 
               $planMeal->items->isNotEmpty();
    }
}

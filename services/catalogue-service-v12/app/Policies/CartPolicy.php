<?php

namespace App\Policies;

use App\Models\Cart;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CartPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the cart.
     *
     * @param User $user
     * @param Cart $cart
     * @return bool
     */
    public function view(User $user, Cart $cart): bool
    {
        // Allow viewing if the cart belongs to the user or is a guest cart with matching session
        return ($cart->customer_id === null || $cart->customer_id === $user->id);
    }

    /**
     * Determine whether the user can update the cart.
     *
     * @param User $user
     * @param Cart $cart
     * @return bool
     */
    public function update(User $user, Cart $cart): bool
    {
        // Allow updating if the cart belongs to the user or is a guest cart with matching session
        return ($cart->customer_id === null || $cart->customer_id === $user->id);
    }

    /**
     * Determine whether the user can checkout the cart.
     *
     * @param User $user
     * @param Cart $cart
     * @return bool
     */
    public function checkout(User $user, Cart $cart): bool
    {
        // Allow checkout if the cart belongs to the user or is a guest cart with matching session
        // and the cart has items
        return ($cart->customer_id === null || $cart->customer_id === $user->id) &&
               ($cart->items->isNotEmpty());
    }
}

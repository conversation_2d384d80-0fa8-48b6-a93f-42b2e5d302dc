<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Exchange\AMQPExchangeType;
use PhpAmqpLib\Message\AMQPMessage;

class RabbitMQConsumerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:consume {consumer} {--timeout=0}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Consume messages from RabbitMQ';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $consumerName = $this->argument('consumer');
        $timeout = (int) $this->option('timeout');

        $this->info("Starting RabbitMQ consumer: {$consumerName}");

        // Get consumer configuration
        $consumerConfig = config("rabbitmq.consumers.{$consumerName}");
        if (!$consumerConfig) {
            $this->error("Consumer {$consumerName} not found in configuration");
            return 1;
        }

        // Get queue configuration
        $queueName = $consumerConfig['queue'];
        $queueConfig = config("rabbitmq.queues.{$queueName}");
        if (!$queueConfig) {
            $this->error("Queue {$queueName} not found in configuration");
            return 1;
        }

        // Get exchange configuration
        $exchangeName = $queueConfig['binding']['exchange'];
        $exchangeConfig = config("rabbitmq.exchanges.{$exchangeName}");
        if (!$exchangeConfig) {
            $this->error("Exchange {$exchangeName} not found in configuration");
            return 1;
        }

        try {
            // Create connection
            $connection = new AMQPStreamConnection(
                config('rabbitmq.host'),
                config('rabbitmq.port'),
                config('rabbitmq.user'),
                config('rabbitmq.password'),
                config('rabbitmq.vhost')
            );
            $channel = $connection->channel();

            // Set QoS
            $prefetchCount = $consumerConfig['prefetch_count'] ?? 10;
            $channel->basic_qos(null, $prefetchCount, null);

            // Declare dead letter exchange and queue if enabled
            if (config('rabbitmq.dead_letter.enabled', true)) {
                $dlxName = config('rabbitmq.dead_letter.exchange');
                $dlqName = config('rabbitmq.dead_letter.queue');

                $channel->exchange_declare($dlxName, AMQPExchangeType::TOPIC, false, true, false);
                $channel->queue_declare($dlqName, false, true, false, false);
                $channel->queue_bind($dlqName, $dlxName, '#');

                $this->info("Declared dead letter exchange: {$dlxName}");
                $this->info("Declared dead letter queue: {$dlqName}");
            }

            // Declare exchange
            $channel->exchange_declare(
                $exchangeConfig['name'],
                $exchangeConfig['type'],
                $exchangeConfig['passive'],
                $exchangeConfig['durable'],
                $exchangeConfig['auto_delete']
            );
            $this->info("Declared exchange: {$exchangeConfig['name']}");

            // Declare queue with dead letter configuration
            $queueArgs = [];
            if (config('rabbitmq.dead_letter.enabled', true)) {
                $queueArgs['x-dead-letter-exchange'] = config('rabbitmq.dead_letter.exchange');
            }

            $channel->queue_declare(
                $queueConfig['name'],
                $queueConfig['passive'],
                $queueConfig['durable'],
                $queueConfig['exclusive'],
                $queueConfig['auto_delete'],
                false,
                $queueArgs
            );
            $this->info("Declared queue: {$queueConfig['name']}");

            // Bind queue to exchange
            $channel->queue_bind(
                $queueConfig['name'],
                $queueConfig['binding']['exchange'],
                $queueConfig['binding']['routing_key']
            );
            $this->info("Bound queue {$queueConfig['name']} to exchange {$queueConfig['binding']['exchange']} with routing key {$queueConfig['binding']['routing_key']}");

            // Create listener instance
            $listenerClass = $consumerConfig['listener'];
            $listener = App::make($listenerClass);
            $this->info("Created listener: {$listenerClass}");

            // Define callback
            $callback = function (AMQPMessage $message) use ($listener) {
                $this->info("Received message: " . $message->getBody());
                $listener->handle($message);
            };

            // Consume messages
            $channel->basic_consume(
                $queueConfig['name'],
                '',
                false,
                false,
                false,
                false,
                $callback
            );
            $this->info("Started consuming messages from queue: {$queueConfig['name']}");

            // Set timeout if provided
            $startTime = time();
            
            // Wait for messages
            while ($channel->is_consuming()) {
                $channel->wait(null, false, 1);
                
                // Check timeout
                if ($timeout > 0 && (time() - $startTime) >= $timeout) {
                    $this->info("Timeout reached after {$timeout} seconds");
                    break;
                }
            }

            // Close connection
            $channel->close();
            $connection->close();
            $this->info("Closed connection");

            return 0;
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            Log::error('RabbitMQ consumer error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'consumer' => $consumerName,
            ]);
            return 1;
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PlanMeal extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_id',
        'kitchen_id',
        'menu_type',
        'start_date',
        'end_date',
        'quantity',
        'negotiated_price',
        'service_charges',
        'tax',
        'discount',
        'total_bill_amount',
        'promo_code',
        'applied_taxes',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'negotiated_price' => 'decimal:2',
        'service_charges' => 'decimal:2',
        'tax' => 'decimal:2',
        'discount' => 'decimal:2',
        'total_bill_amount' => 'decimal:2',
        'applied_taxes' => 'json',
    ];

    /**
     * Get the customer that owns the plan meal.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the kitchen that owns the plan meal.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(Kitchen::class);
    }

    /**
     * Get the items for the plan meal.
     */
    public function items(): HasMany
    {
        return $this->hasMany(PlanMealItem::class);
    }
}

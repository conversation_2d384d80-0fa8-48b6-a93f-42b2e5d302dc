<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Product extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'unit_price',
        'food_type',
        'product_category_id',
        'image_path',
        'product_subtype',
        'swap_with',
        'swap_charges',
        'sequence',
        'status',
        'kitchen_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'swap_charges' => 'decimal:2',
        'status' => 'boolean',
    ];

    /**
     * Get the product category that owns the product.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    /**
     * Get the kitchen that owns the product.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(Kitchen::class);
    }

    /**
     * Get the product that this product can be swapped with.
     */
    public function swapProduct(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'swap_with');
    }
}

global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'catalogue-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['catalogue-service:8000']
    
  - job_name: 'order-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['order-service:8000']
    
  - job_name: 'subscription-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['subscription-service:8000']
    
  - job_name: 'wallet-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['wallet-service:8000']
    
  - job_name: 'payment-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['payment-service:8000']
    
  - job_name: 'auth-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['auth-service:8000']
    
  - job_name: 'kong'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['kong:8001']

{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "http_request_duration_seconds{handler=\"/api/v2/catalogue/cart/checkout\"}", "interval": "", "legendFormat": "Checkout Duration", "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.5, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Checkout Response Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(http_requests_total{handler=\"/api/v2/catalogue/cart/checkout\",status=~\"5..\"}[5m]) / rate(http_requests_total{handler=\"/api/v2/catalogue/cart/checkout\"}[5m])", "interval": "", "legendFormat": "Error Rate", "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.05, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Checkout Error Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 6, "options": {"showHeader": true}, "pluginVersion": "7.3.7", "targets": [{"expr": "circuit_breaker_state", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Circuit Breaker States", "type": "table"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 8, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.7", "targets": [{"expr": "cache_hit_ratio", "interval": "", "legendFormat": "<PERSON><PERSON>", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON>", "type": "gauge"}], "refresh": "5s", "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Catalogue Service Dashboard", "uid": "catalogue-service", "version": 1}
# Catalogue Service Improvements

This document outlines the improvements made to the Catalogue Service to enhance reliability, security, and performance.

## 1. Data Integrity Enhancements

### 1.1 Idempotency Service
- Implemented `IdempotencyService` to ensure operations are executed exactly once
- Added idempotency key support to checkout endpoints
- Cached results for 24 hours to handle retries
- Applied to both cart checkout and plan meal checkout processes

### 1.2 Transaction Management
- Added explicit transaction isolation levels (`SERIALIZABLE`) for checkout operations
- Ensures data consistency in high-concurrency scenarios
- Prevents race conditions during checkout

## 2. Error Handling and Service Resilience

### 2.1 Custom Exception Classes
- Created domain-specific exception classes:
  - `CheckoutException` (base class)
  - `OrderCreationException`
  - `PaymentProcessingException`
  - `SubscriptionCreationException`
- Improved error context and traceability

### 2.2 Circuit Breaker Pattern
- Implemented `CircuitBreakerService` to prevent cascading failures
- Automatically detects service failures and temporarily disables failing service calls
- Configurable thresholds and timeout periods
- Applied to Order Service, Subscription Service, and Wallet Service calls

### 2.3 Retry Logic
- Implemented `RetryService` with exponential backoff
- Automatically retries failed operations with increasing delays
- Configurable maximum retries and base delay
- Applied to all inter-service HTTP requests

## 3. Security Enhancements

### 3.1 Policy-Based Authorization
- Implemented `CartPolicy` and `PlanMealPolicy` for fine-grained access control
- Added explicit authorization checks in controllers
- Prevents unauthorized access to carts and plan meals

### 3.2 Rate Limiting
- Added rate limiting to checkout endpoints (60 requests per minute)
- Prevents abuse and DoS attacks

### 3.3 Sensitive Data Sanitization
- Implemented `SensitiveDataSanitizer` to redact sensitive information in logs
- Protects payment information, tokens, and other sensitive data
- Applied to error logging in checkout processes

## 4. Performance Optimization

### 4.1 Caching Strategy
- Enhanced caching for frequently accessed data (themes, menus)
- Added cache invalidation on updates
- Reduced database queries for common operations

### 4.2 Asynchronous Processing
- Moved event publishing to background jobs
- Implemented `PublishMenuEventJob` and `PublishThemeEventJob`
- Improved response times for user-facing operations

## 5. Observability Improvements

### 5.1 Structured Logging with Correlation IDs
- Added `CorrelationIdMiddleware` to track requests across services
- Generated unique correlation IDs for each request
- Added correlation IDs to logs and response headers

### 5.2 Performance Metrics
- Implemented `PerformanceMetricsService` to measure operation durations
- Added timing for critical operations (checkout, database queries, service calls)
- Included metrics in API responses for debugging

### 5.3 Health Check Endpoint
- Enhanced health check endpoint with detailed status information
- Added checks for database, memory, disk, and external service dependencies
- Included performance metrics in health check response

## 6. Development Practices

### 6.1 Feature Flags
- Implemented `FeatureFlagService` for feature toggling
- Added configuration for feature flags and variants
- Enabled gradual rollout of new features (enhanced checkout, subscription discounts)

### 6.2 Comprehensive Tests
- Added unit tests for all new services
- Added feature tests for checkout flows
- Implemented test doubles for external services
- Achieved high test coverage for critical paths

## 7. Usage Examples

### 7.1 Idempotency

```php
// In a repository method
public function processCheckout(int $cartId, array $checkoutData): array
{
    $idempotencyKey = $checkoutData['idempotency_key'] ?? app(IdempotencyService::class)->generateKey();
    
    return app(IdempotencyService::class)->processWithIdempotency(
        $idempotencyKey,
        'cart_checkout_' . $cartId,
        function() use ($cartId, $checkoutData) {
            // Checkout logic
        }
    );
}
```

### 7.2 Circuit Breaker and Retry

```php
// Create order in the Order Service with retry and circuit breaker
$orderData = app(RetryService::class)->execute(function() use ($cart, $checkoutData) {
    return app(CircuitBreakerService::class)->execute('order_service', function() use ($cart, $checkoutData) {
        $response = Http::post(config('services.order.url') . '/orders', [
            // Order data
        ]);
        
        if (!$response->successful()) {
            throw new OrderCreationException('Failed to create order');
        }
        
        return $response->json();
    });
}, 'order_creation');
```

### 7.3 Feature Flags

```php
// In a controller method
if ($this->featureFlagService->isEnabled('enhanced_checkout')) {
    $checkoutVariant = $this->featureFlagService->getVariant('checkout_flow');
    
    switch ($checkoutVariant) {
        case 'streamlined':
            $result = $this->cartService->streamlinedCheckout($validatedData, $customerId);
            break;
        case 'two_step':
            $result = $this->cartService->twoStepCheckout($validatedData, $customerId);
            break;
        default:
            $result = $this->cartService->checkout($validatedData, $customerId);
    }
} else {
    $result = $this->cartService->checkout($validatedData, $customerId);
}
```

## 8. Configuration

### 8.1 Feature Flags

Feature flags are configured in `config/features.php`:

```php
return [
    'new_payment_methods' => env('FEATURE_NEW_PAYMENT_METHODS', false),
    'subscription_discounts' => env('FEATURE_SUBSCRIPTION_DISCOUNTS', false),
    'advanced_cart_recommendations' => env('FEATURE_ADVANCED_CART_RECOMMENDATIONS', false),
    'enhanced_checkout' => env('FEATURE_ENHANCED_CHECKOUT', false),
    'multi_kitchen_cart' => env('FEATURE_MULTI_KITCHEN_CART', false),
    'theme_preview' => env('FEATURE_THEME_PREVIEW', false),
];
```

Feature variants are configured in `config/feature_variants.php`:

```php
return [
    'checkout_flow' => env('FEATURE_VARIANT_CHECKOUT_FLOW', 'default'),
    'subscription_ui' => env('FEATURE_VARIANT_SUBSCRIPTION_UI', 'default'),
    'cart_ui' => env('FEATURE_VARIANT_CART_UI', 'default'),
    'payment_ui' => env('FEATURE_VARIANT_PAYMENT_UI', 'default'),
];
```

#!/bin/bash

# Feature Flag Rollout Script
# This script manages the progressive rollout of feature flags in production

# Environment variables
ENV_FILE=".env"
BACKUP_FILE=".env.backup"

# Function to update feature flag in .env file
update_feature_flag() {
    local flag_name=$1
    local flag_value=$2
    local env_file=$3

    # Check if flag exists
    if grep -q "^${flag_name}=" "$env_file"; then
        # Update existing flag
        sed -i '' "s/^${flag_name}=.*/${flag_name}=${flag_value}/" "$env_file"
    else
        # Add new flag
        echo "${flag_name}=${flag_value}" >> "$env_file"
    fi

    echo "Updated $flag_name to $flag_value in $env_file"
}

# Function to update feature variant in .env file
update_feature_variant() {
    local variant_name=$1
    local variant_value=$2
    local env_file=$3

    # Check if variant exists
    if grep -q "^${variant_name}=" "$env_file"; then
        # Update existing variant
        sed -i '' "s/^${variant_name}=.*/${variant_name}=${variant_value}/" "$env_file"
    else
        # Add new variant
        echo "${variant_name}=${variant_value}" >> "$env_file"
    fi

    echo "Updated $variant_name to $variant_value in $env_file"
}

# Function to restart the application
restart_app() {
    echo "Restarting application..."
    # Add your restart command here, e.g.:
    # docker-compose restart app
    # or
    # kubectl rollout restart deployment/catalogue-service
}

# Function to monitor metrics after deployment
monitor_metrics() {
    local duration=$1
    echo "Monitoring metrics for $duration seconds..."
    # Add your monitoring command here, e.g.:
    # curl -s http://localhost:9090/api/v1/query?query=http_request_duration_seconds
}

# Create backup of .env file
cp "$ENV_FILE" "$BACKUP_FILE"
echo "Created backup of $ENV_FILE at $BACKUP_FILE"

# Phase 1: Enable resilience features (circuit breakers, retries)
echo "Phase 1: Enabling resilience features..."
update_feature_flag "FEATURE_CIRCUIT_BREAKER_ENABLED" "true" "$ENV_FILE"
update_feature_flag "FEATURE_RETRY_ENABLED" "true" "$ENV_FILE"
restart_app
monitor_metrics 300

# Phase 2: Enable enhanced observability
echo "Phase 2: Enabling enhanced observability..."
update_feature_flag "FEATURE_CORRELATION_ID_ENABLED" "true" "$ENV_FILE"
update_feature_flag "FEATURE_DETAILED_HEALTH_CHECKS" "true" "$ENV_FILE"
restart_app
monitor_metrics 300

# Phase 3: Enable enhanced checkout for 10% of users
echo "Phase 3: Enabling enhanced checkout for 10% of users..."
update_feature_flag "FEATURE_ENHANCED_CHECKOUT" "true" "$ENV_FILE"
update_feature_variant "FEATURE_VARIANT_CHECKOUT_FLOW" "default" "$ENV_FILE"
update_feature_flag "FEATURE_CHECKOUT_PERCENTAGE" "10" "$ENV_FILE"
restart_app
monitor_metrics 600

# Phase 4: Increase enhanced checkout to 50% of users
echo "Phase 4: Increasing enhanced checkout to 50% of users..."
update_feature_flag "FEATURE_CHECKOUT_PERCENTAGE" "50" "$ENV_FILE"
restart_app
monitor_metrics 600

# Phase 5: Enable enhanced checkout for all users
echo "Phase 5: Enabling enhanced checkout for all users..."
update_feature_flag "FEATURE_CHECKOUT_PERCENTAGE" "100" "$ENV_FILE"
restart_app
monitor_metrics 600

# Phase 6: Enable subscription discounts
echo "Phase 6: Enabling subscription discounts..."
update_feature_flag "FEATURE_SUBSCRIPTION_DISCOUNTS" "true" "$ENV_FILE"
restart_app
monitor_metrics 600

# Phase 7: Enable advanced cart recommendations
echo "Phase 7: Enabling advanced cart recommendations..."
update_feature_flag "FEATURE_ADVANCED_CART_RECOMMENDATIONS" "true" "$ENV_FILE"
restart_app
monitor_metrics 600

echo "Feature flag rollout completed successfully!"

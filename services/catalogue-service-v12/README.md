# Catalogue Service

This microservice handles catalogue management functionality for the application. It provides APIs for creating, retrieving, updating, and deleting products, menus, carts, plan meals, and theme settings.

## Features

- CRUD operations for products, menus, carts, plan meals, and themes
- Product search and filtering
- Cart management with promo code support
- Meal planning functionality
- Theme management

## Requirements

- PHP 8.1 or higher
- Laravel 12.x
- MySQL/SQLite
- Composer 2.x

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-organization/catalogue-service.git
cd catalogue-service-v12
```

2. Install dependencies:
```bash
composer install
```

3. Copy the environment file:
```bash
cp .env.example .env
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Run migrations:
```bash
php artisan migrate
```

6. Seed the database (optional):
```bash
php artisan db:seed
```

7. Start the development server:
```bash
php artisan serve
```

## API Endpoints

### Products

- `GET /api/v2/catalogue/products` - Get all products with optional filtering
- `POST /api/v2/catalogue/products` - Create a new product
- `GET /api/v2/catalogue/products/{id}` - Get a specific product
- `PUT /api/v2/catalogue/products/{id}` - Update a product
- `DELETE /api/v2/catalogue/products/{id}` - Delete a product
- `GET /api/v2/catalogue/products/category/{category}` - Get products by category
- `GET /api/v2/catalogue/products/search` - Search products by name or description

### Menus

- `GET /api/v2/catalogue/menus` - Get all menus
- `POST /api/v2/catalogue/menus` - Create a new menu
- `GET /api/v2/catalogue/menus/{id}` - Get a specific menu
- `PUT /api/v2/catalogue/menus/{id}` - Update a menu
- `DELETE /api/v2/catalogue/menus/{id}` - Delete a menu
- `GET /api/v2/catalogue/menus/kitchen/{kitchen}` - Get menus by kitchen

### Cart

- `GET /api/v2/catalogue/cart` - Get current cart contents
- `POST /api/v2/catalogue/cart/items` - Add item to cart
- `PUT /api/v2/catalogue/cart/items/{id}` - Update cart item quantity
- `DELETE /api/v2/catalogue/cart/items/{id}` - Remove item from cart
- `DELETE /api/v2/catalogue/cart` - Clear entire cart
- `POST /api/v2/catalogue/cart/apply-promo` - Apply promo code to cart
- `POST /api/v2/catalogue/cart/checkout` - Checkout cart

### Plan Meals

- `GET /api/v2/catalogue/planmeals` - Get all plan meals
- `POST /api/v2/catalogue/planmeals` - Create a new plan meal
- `GET /api/v2/catalogue/planmeals/{id}` - Get a specific plan meal
- `PUT /api/v2/catalogue/planmeals/{id}` - Update a plan meal
- `DELETE /api/v2/catalogue/planmeals/{id}` - Delete a plan meal

### Theme

- `GET /api/v2/catalogue/theme` - Get current theme settings
- `POST /api/v2/catalogue/theme` - Update theme settings

## Running Tests

```bash
php artisan test
```

## API Documentation

The API is documented using the OpenAPI Specification (OAS) 3.0.0. The specification is available in the `openapi.yaml` file.

## Kong API Gateway Integration

This service is configured to work with Kong API Gateway. The configuration is available in the `kong.yaml` file.

## Migrated from Zend Framework

This service was migrated from the Zend Framework application. The original code was located in:
- `module/Stdcatalogue/src/Stdcatalogue/Controller/MenuController.php`
- `module/Stdcatalogue/src/Stdcatalogue/Controller/CartController.php`
- `module/Stdcatalogue/src/Stdcatalogue/Controller/PlanmealController.php`
- Various other files in the Stdcatalogue module

## Directory Structure

```
app/
├── Http/
│   └── Controllers/
│       └── Api/
│           └── V2/
│               ├── CatalogueController.php
│               ├── MenuController.php
│               ├── CartController.php
│               ├── PlanMealController.php
│               └── ThemeController.php
├── Models/
│   ├── Product.php
│   ├── ProductCategory.php
│   ├── Menu.php
│   ├── Cart.php
│   ├── CartItem.php
│   ├── PlanMeal.php
│   ├── PlanMealItem.php
│   └── Theme.php
├── Services/
│   ├── CatalogueService.php
│   ├── MenuService.php
│   ├── CartService.php
│   ├── PlanMealService.php
│   └── ThemeService.php
└── Repositories/
    ├── ProductRepository.php
    ├── MenuRepository.php
    ├── CartRepository.php
    ├── PlanMealRepository.php
    └── ThemeRepository.php
```

## License

This project is licensed under the MIT License.

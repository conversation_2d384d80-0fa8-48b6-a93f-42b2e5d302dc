<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('unit_price', 10, 2);
            $table->enum('food_type', ['veg', 'non-veg'])->default('veg');
            $table->unsignedBigInteger('product_category_id')->nullable();
            $table->string('image_path')->nullable();
            $table->string('product_subtype')->nullable();
            $table->unsignedBigInteger('swap_with')->nullable();
            $table->decimal('swap_charges', 10, 2)->default(0.00);
            $table->integer('sequence')->default(0);
            $table->boolean('status')->default(true);
            $table->unsignedBigInteger('kitchen_id')->nullable();
            $table->timestamps();

            $table->foreign('product_category_id')->references('id')->on('product_categories')->onDelete('set null');
            $table->foreign('kitchen_id')->references('pk_kitchen_code')->on('kitchens')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};

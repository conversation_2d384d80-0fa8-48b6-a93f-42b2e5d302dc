<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['breakfast', 'lunch', 'dinner'])->default('lunch');
            $table->unsignedBigInteger('kitchen_id');
            $table->time('cut_off_time')->nullable();
            $table->integer('cut_off_interval')->default(1);
            $table->boolean('status')->default(true);
            $table->timestamps();

            $table->foreign('kitchen_id')->references('pk_kitchen_code')->on('kitchens')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menus');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_meals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->unsignedBigInteger('kitchen_id');
            $table->string('menu_type');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->integer('quantity')->default(1);
            $table->decimal('negotiated_price', 10, 2)->default(0.00);
            $table->decimal('service_charges', 10, 2)->default(0.00);
            $table->decimal('tax', 10, 2)->default(0.00);
            $table->decimal('discount', 10, 2)->default(0.00);
            $table->decimal('total_bill_amount', 10, 2)->default(0.00);
            $table->string('promo_code')->nullable();
            $table->json('applied_taxes')->nullable();
            $table->enum('status', ['active', 'inactive', 'completed', 'cancelled'])->default('active');
            $table->timestamps();

            $table->foreign('customer_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('kitchen_id')->references('pk_kitchen_code')->on('kitchens')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_meals');
    }
};

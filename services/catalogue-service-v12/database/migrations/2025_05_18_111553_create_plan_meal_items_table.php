<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_meal_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plan_meal_id');
            $table->unsignedBigInteger('product_id');
            $table->date('delivery_date');
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 10, 2);
            $table->timestamps();

            $table->foreign('plan_meal_id')->references('id')->on('plan_meals')->onDelete('cascade');
            $table->foreign('product_id')->references('pk_product_code')->on('products')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_meal_items');
    }
};

<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting Catalogue Service Database Seeding...');

        // Seed Users (only if needed)
        $this->seedUsers();

        // Seed Product Categories
        $this->seedProductCategories();

        // Seed Sample Products (using existing products table structure)
        $this->seedProducts();

        // Seed Menus
        $this->seedMenus();

        // Seed Carts
        $this->seedCarts();

        $this->command->info('✅ Catalogue Service Database Seeding Completed!');
    }

    /**
     * Seed users only if they don't exist
     */
    private function seedUsers(): void
    {
        $this->command->info('👥 Seeding Users...');

        // Check if test user already exists
        $existingUser = User::where('email', '<EMAIL>')->first();

        if (!$existingUser) {
            User::factory()->create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
            ]);
            $this->command->info('   ✓ Created test user');
        } else {
            $this->command->info('   ⚠ Test user already exists, skipping...');
        }

        // Create additional catalogue-specific users if needed
        $catalogueUsers = [
            ['name' => 'Catalogue Admin', 'email' => '<EMAIL>'],
            ['name' => 'Kitchen Manager', 'email' => '<EMAIL>'],
            ['name' => 'Customer Service', 'email' => '<EMAIL>'],
        ];

        foreach ($catalogueUsers as $userData) {
            $existingUser = User::where('email', $userData['email'])->first();
            if (!$existingUser) {
                User::create([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'email_verified_at' => now(),
                    'password' => Hash::make('password123'),
                ]);
                $this->command->info("   ✓ Created user: {$userData['name']}");
            } else {
                $this->command->info("   ⚠ User {$userData['name']} already exists, skipping...");
            }
        }
    }

    /**
     * Seed product categories
     */
    private function seedProductCategories(): void
    {
        $this->command->info('🏷️ Seeding Product Categories...');

        $categories = [
            ['product_category_name' => 'Breakfast Items', 'description' => 'Morning meal options', 'type' => 'meal', 'sequence' => 1],
            ['product_category_name' => 'Lunch Specials', 'description' => 'Afternoon meal varieties', 'type' => 'meal', 'sequence' => 2],
            ['product_category_name' => 'Dinner Options', 'description' => 'Evening meal choices', 'type' => 'meal', 'sequence' => 3],
            ['product_category_name' => 'Beverages', 'description' => 'Drinks and refreshments', 'type' => 'product', 'sequence' => 4],
            ['product_category_name' => 'Snacks', 'description' => 'Light bites and snacks', 'type' => 'product', 'sequence' => 5],
            ['product_category_name' => 'Desserts', 'description' => 'Sweet treats and desserts', 'type' => 'extra', 'sequence' => 6],
        ];

        foreach ($categories as $category) {
            $existing = DB::table('product_categories')->where('product_category_name', $category['product_category_name'])->first();
            if (!$existing) {
                DB::table('product_categories')->insert([
                    'product_category_name' => $category['product_category_name'],
                    'description' => $category['description'],
                    'type' => $category['type'],
                    'sequence' => $category['sequence'],
                    'status' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->command->info("   ✓ Created category: {$category['product_category_name']}");
            } else {
                $this->command->info("   ⚠ Category {$category['product_category_name']} already exists, skipping...");
            }
        }
    }

    /**
     * Seed sample products (respecting existing products table structure)
     */
    private function seedProducts(): void
    {
        $this->command->info('🍽️ Seeding Sample Products...');

        // Note: Using existing products table structure with pk_product_code, name, kitchen_code, etc.
        $sampleProducts = [
            ['name' => 'Masala Dosa', 'kitchen_code' => 'KITCHEN_001', 'quantity' => 1, 'unit' => 'piece', 'recipe' => 'Traditional South Indian crepe with spiced potato filling'],
            ['name' => 'Chicken Biryani', 'kitchen_code' => 'KITCHEN_001', 'quantity' => 1, 'unit' => 'plate', 'recipe' => 'Aromatic basmati rice with tender chicken pieces'],
            ['name' => 'Paneer Butter Masala', 'kitchen_code' => 'KITCHEN_001', 'quantity' => 1, 'unit' => 'bowl', 'recipe' => 'Creamy tomato-based curry with cottage cheese'],
            ['name' => 'Veg Thali', 'kitchen_code' => 'KITCHEN_001', 'quantity' => 1, 'unit' => 'thali', 'recipe' => 'Complete vegetarian meal with rice, dal, vegetables, and bread'],
            ['name' => 'Samosa', 'kitchen_code' => 'KITCHEN_001', 'quantity' => 2, 'unit' => 'pieces', 'recipe' => 'Crispy fried pastry with spiced potato filling'],
        ];

        foreach ($sampleProducts as $index => $product) {
            $existing = DB::table('products')->where('name', $product['name'])->first();
            if (!$existing) {
                DB::table('products')->insert([
                    'pk_product_code' => 1000 + $index, // Generate unique product codes
                    'name' => $product['name'],
                    'kitchen_code' => $product['kitchen_code'],
                    'quantity' => $product['quantity'],
                    'unit' => $product['unit'],
                    'recipe' => $product['recipe'],
                    'screen' => 1, // Default screen value
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->command->info("   ✓ Created product: {$product['name']}");
            } else {
                $this->command->info("   ⚠ Product {$product['name']} already exists, skipping...");
            }
        }
    }

    /**
     * Seed menus
     */
    private function seedMenus(): void
    {
        $this->command->info('📋 Seeding Menus...');

        // Get a kitchen ID to reference (using existing kitchens table structure)
        $kitchen = DB::table('kitchens')->first();
        if (!$kitchen) {
            $this->command->warn('   ⚠ No kitchens found, skipping menu seeding...');
            return;
        }

        $menus = [
            ['name' => 'Breakfast Menu', 'type' => 'breakfast', 'kitchen_id' => $kitchen->pk_kitchen_code, 'cut_off_time' => '08:00:00'],
            ['name' => 'Lunch Menu', 'type' => 'lunch', 'kitchen_id' => $kitchen->pk_kitchen_code, 'cut_off_time' => '12:00:00'],
            ['name' => 'Dinner Menu', 'type' => 'dinner', 'kitchen_id' => $kitchen->pk_kitchen_code, 'cut_off_time' => '18:00:00'],
        ];

        foreach ($menus as $menu) {
            $existing = DB::table('menus')->where('name', $menu['name'])->first();
            if (!$existing) {
                DB::table('menus')->insert([
                    'name' => $menu['name'],
                    'type' => $menu['type'],
                    'kitchen_id' => $menu['kitchen_id'],
                    'cut_off_time' => $menu['cut_off_time'],
                    'cut_off_interval' => 1,
                    'status' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->command->info("   ✓ Created menu: {$menu['name']}");
            } else {
                $this->command->info("   ⚠ Menu {$menu['name']} already exists, skipping...");
            }
        }
    }

    /**
     * Seed sample carts
     */
    private function seedCarts(): void
    {
        $this->command->info('🛒 Seeding Sample Carts...');

        // Get existing users
        $users = User::limit(2)->get();
        if ($users->isEmpty()) {
            $this->command->warn('   ⚠ No users found, skipping cart seeding...');
            return;
        }

        foreach ($users as $user) {
            $existing = DB::table('carts')->where('customer_id', $user->id)->first();
            if (!$existing) {
                $cartId = DB::table('carts')->insertGetId([
                    'customer_id' => $user->id,
                    'session_id' => 'session_' . $user->id . '_' . time(),
                    'total_amount' => 0.00,
                    'tax_amount' => 0.00,
                    'discount_amount' => 0.00,
                    'delivery_charges' => 0.00,
                    'net_amount' => 0.00,
                    'status' => 'active',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->command->info("   ✓ Created cart for user: {$user->name}");

                // Add some sample cart items
                $this->seedCartItems($cartId);
            } else {
                $this->command->info("   ⚠ Cart for user {$user->name} already exists, skipping...");
            }
        }
    }

    /**
     * Seed cart items for a given cart
     */
    private function seedCartItems(int $cartId): void
    {
        // Get some products to add to cart
        $products = DB::table('products')->limit(2)->get();

        $totalAmount = 0;
        $itemCount = 0;

        foreach ($products as $product) {
            $quantity = rand(1, 3);
            $unitPrice = rand(50, 200); // Random price between 50-200
            $totalPrice = $quantity * $unitPrice;

            DB::table('cart_items')->insert([
                'cart_id' => $cartId,
                'product_id' => $product->pk_product_code,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'menu_type' => 'lunch',
                'delivery_date' => now()->addDay(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $totalAmount += $totalPrice;
            $itemCount += $quantity;
        }

        // Calculate additional charges
        $taxAmount = $totalAmount * 0.18; // 18% tax
        $deliveryCharges = 50; // Fixed delivery charge
        $netAmount = $totalAmount + $taxAmount + $deliveryCharges;

        // Update cart totals
        DB::table('carts')->where('id', $cartId)->update([
            'total_amount' => $totalAmount,
            'tax_amount' => $taxAmount,
            'delivery_charges' => $deliveryCharges,
            'net_amount' => $netAmount,
            'updated_at' => now(),
        ]);

        $this->command->info("     ✓ Added {$itemCount} items to cart (Subtotal: ₹{$totalAmount}, Net: ₹{$netAmount})");
    }
}

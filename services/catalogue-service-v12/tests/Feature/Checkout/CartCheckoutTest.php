<?php

namespace Tests\Feature\Checkout;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use App\Models\User;
use App\Services\FeatureFlagService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class CartCheckoutTest extends TestCase
{
    use RefreshDatabase;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock external service calls
        Http::fake([
            config('services.order.url') . '/orders' => Http::response([
                'id' => 1,
                'order_number' => 'ORD-' . Str::random(8),
            ], 201),
            config('services.wallet.url') . '/deduct' => Http::response([
                'success' => true,
                'transaction_id' => 'TRX-' . Str::random(8),
            ], 200),
        ]);
        
        // Mock feature flag service
        $this->mock(FeatureFlagService::class, function ($mock) {
            $mock->shouldReceive('isEnabled')
                ->with('enhanced_checkout')
                ->andReturn(false);
            
            $mock->shouldReceive('getVariant')
                ->with('checkout_flow')
                ->andReturn('default');
        });
    }
    
    public function testSuccessfulCheckoutWithWallet(): void
    {
        // Create user
        $user = User::factory()->create();
        
        // Create product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
        ]);
        
        // Create cart
        $cart = Cart::factory()->create([
            'customer_id' => $user->id,
        ]);
        
        // Add item to cart
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => $product->unit_price,
            'total_price' => $product->unit_price * 2,
        ]);
        
        // Perform checkout
        $response = $this->actingAs($user)
            ->postJson('/api/v2/catalogue/cart/checkout', [
                'payment_method' => 'wallet',
                'idempotency_key' => Str::uuid()->toString(),
            ]);
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'order_id',
                    'order_number',
                ],
            ]);
        
        // Assert HTTP requests were made correctly
        Http::assertSent(function ($request) use ($user) {
            return $request->url() == config('services.order.url') . '/orders' &&
                   $request->method() == 'POST' &&
                   $request['customer_id'] == $user->id;
        });
        
        Http::assertSent(function ($request) use ($user) {
            return $request->url() == config('services.wallet.url') . '/deduct' &&
                   $request->method() == 'POST' &&
                   $request['customer_id'] == $user->id;
        });
        
        // Assert cart was cleared
        $this->assertDatabaseMissing('cart_items', [
            'cart_id' => $cart->id,
        ]);
    }
    
    public function testCheckoutWithEmptyCart(): void
    {
        // Create user
        $user = User::factory()->create();
        
        // Create empty cart
        $cart = Cart::factory()->create([
            'customer_id' => $user->id,
        ]);
        
        // Perform checkout
        $response = $this->actingAs($user)
            ->postJson('/api/v2/catalogue/cart/checkout', [
                'payment_method' => 'wallet',
            ]);
        
        $response->assertStatus(400)
            ->assertJson([
                'message' => 'Cart is empty',
            ]);
        
        // Assert no HTTP requests were made
        Http::assertNothingSent();
    }
    
    public function testIdempotentCheckout(): void
    {
        // Create user
        $user = User::factory()->create();
        
        // Create product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
        ]);
        
        // Create cart
        $cart = Cart::factory()->create([
            'customer_id' => $user->id,
        ]);
        
        // Add item to cart
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => $product->unit_price,
            'total_price' => $product->unit_price * 2,
        ]);
        
        // Generate idempotency key
        $idempotencyKey = Str::uuid()->toString();
        
        // Perform checkout with idempotency key
        $response1 = $this->actingAs($user)
            ->postJson('/api/v2/catalogue/cart/checkout', [
                'payment_method' => 'wallet',
                'idempotency_key' => $idempotencyKey,
            ]);
        
        $response1->assertStatus(200);
        
        // Recreate cart items (simulating a retry after cart was cleared)
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => $product->unit_price,
            'total_price' => $product->unit_price * 2,
        ]);
        
        // Perform checkout again with same idempotency key
        $response2 = $this->actingAs($user)
            ->postJson('/api/v2/catalogue/cart/checkout', [
                'payment_method' => 'wallet',
                'idempotency_key' => $idempotencyKey,
            ]);
        
        $response2->assertStatus(200);
        
        // Assert the responses have the same order ID
        $this->assertEquals(
            $response1->json('data.order_id'),
            $response2->json('data.order_id')
        );
        
        // Assert HTTP requests were made only once
        Http::assertSentCount(2); // One for order creation, one for wallet deduction
    }
    
    public function testEnhancedCheckoutWithFeatureFlag(): void
    {
        // Override the mock to enable enhanced checkout
        $this->mock(FeatureFlagService::class, function ($mock) {
            $mock->shouldReceive('isEnabled')
                ->with('enhanced_checkout')
                ->andReturn(true);
            
            $mock->shouldReceive('getVariant')
                ->with('checkout_flow')
                ->andReturn('streamlined');
            
            $mock->shouldReceive('isEnabled')
                ->andReturn(false);
        });
        
        // Create user
        $user = User::factory()->create();
        
        // Create product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
        ]);
        
        // Create cart
        $cart = Cart::factory()->create([
            'customer_id' => $user->id,
        ]);
        
        // Add item to cart
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => $product->unit_price,
            'total_price' => $product->unit_price * 2,
        ]);
        
        // Perform checkout
        $response = $this->actingAs($user)
            ->postJson('/api/v2/catalogue/cart/checkout', [
                'payment_method' => 'wallet',
                'idempotency_key' => Str::uuid()->toString(),
            ]);
        
        $response->assertStatus(200)
            ->assertJsonPath('data.checkout_variant', 'streamlined');
    }
}

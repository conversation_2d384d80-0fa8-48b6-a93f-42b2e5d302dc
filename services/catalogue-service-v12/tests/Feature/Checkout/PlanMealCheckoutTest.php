<?php

namespace Tests\Feature\Checkout;

use App\Models\PlanMeal;
use App\Models\PlanMealItem;
use App\Models\Product;
use App\Models\User;
use App\Services\FeatureFlagService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class PlanMealCheckoutTest extends TestCase
{
    use RefreshDatabase;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock external service calls
        Http::fake([
            config('services.subscription.url') . '/subscriptions' => Http::response([
                'id' => 1,
                'subscription_number' => 'SUB-' . Str::random(8),
            ], 201),
            config('services.wallet.url') . '/deduct' => Http::response([
                'success' => true,
                'transaction_id' => 'TRX-' . Str::random(8),
            ], 200),
        ]);
        
        // Mock feature flag service
        $this->mock(FeatureFlagService::class, function ($mock) {
            $mock->shouldReceive('isEnabled')
                ->with('subscription_discounts')
                ->andReturn(false);
            
            $mock->shouldReceive('getVariant')
                ->with('subscription_ui')
                ->andReturn('default');
        });
    }
    
    public function testSuccessfulPlanMealCheckoutWithWallet(): void
    {
        // Create user
        $user = User::factory()->create();
        
        // Create product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
        ]);
        
        // Create plan meal
        $planMeal = PlanMeal::factory()->create([
            'customer_id' => $user->id,
            'kitchen_id' => 1,
            'menu_type' => 'lunch',
            'start_date' => now()->addDay(),
            'end_date' => now()->addDays(30),
            'quantity' => 1,
            'negotiated_price' => 3000,
            'tax' => 300,
            'discount' => 0,
            'service_charges' => 100,
            'total_bill_amount' => 3400,
            'status' => 'pending',
        ]);
        
        // Add items to plan meal
        for ($i = 0; $i < 30; $i++) {
            PlanMealItem::factory()->create([
                'plan_meal_id' => $planMeal->id,
                'product_id' => $product->id,
                'delivery_date' => now()->addDays($i + 1)->format('Y-m-d'),
                'quantity' => 1,
                'unit_price' => $product->unit_price,
                'total_price' => $product->unit_price,
            ]);
        }
        
        // Perform checkout
        $response = $this->actingAs($user)
            ->postJson("/api/v2/catalogue/planmeals/{$planMeal->id}/checkout", [
                'payment_method' => 'wallet',
                'idempotency_key' => Str::uuid()->toString(),
            ]);
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'subscription_id',
                    'subscription_number',
                ],
            ]);
        
        // Assert HTTP requests were made correctly
        Http::assertSent(function ($request) use ($user) {
            return $request->url() == config('services.subscription.url') . '/subscriptions' &&
                   $request->method() == 'POST' &&
                   $request['customer_id'] == $user->id;
        });
        
        Http::assertSent(function ($request) use ($user) {
            return $request->url() == config('services.wallet.url') . '/deduct' &&
                   $request->method() == 'POST' &&
                   $request['customer_id'] == $user->id;
        });
        
        // Assert plan meal status was updated
        $this->assertDatabaseHas('plan_meals', [
            'id' => $planMeal->id,
            'status' => 'completed',
        ]);
    }
    
    public function testCheckoutWithEmptyPlanMeal(): void
    {
        // Create user
        $user = User::factory()->create();
        
        // Create empty plan meal
        $planMeal = PlanMeal::factory()->create([
            'customer_id' => $user->id,
            'kitchen_id' => 1,
            'menu_type' => 'lunch',
            'start_date' => now()->addDay(),
            'end_date' => now()->addDays(30),
            'quantity' => 1,
            'negotiated_price' => 3000,
            'tax' => 300,
            'discount' => 0,
            'service_charges' => 100,
            'total_bill_amount' => 3400,
            'status' => 'pending',
        ]);
        
        // Perform checkout
        $response = $this->actingAs($user)
            ->postJson("/api/v2/catalogue/planmeals/{$planMeal->id}/checkout", [
                'payment_method' => 'wallet',
            ]);
        
        $response->assertStatus(400)
            ->assertJson([
                'message' => 'Plan meal is empty or not found',
            ]);
        
        // Assert no HTTP requests were made
        Http::assertNothingSent();
    }
    
    public function testIdempotentPlanMealCheckout(): void
    {
        // Create user
        $user = User::factory()->create();
        
        // Create product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
        ]);
        
        // Create plan meal
        $planMeal = PlanMeal::factory()->create([
            'customer_id' => $user->id,
            'kitchen_id' => 1,
            'menu_type' => 'lunch',
            'start_date' => now()->addDay(),
            'end_date' => now()->addDays(30),
            'quantity' => 1,
            'negotiated_price' => 3000,
            'tax' => 300,
            'discount' => 0,
            'service_charges' => 100,
            'total_bill_amount' => 3400,
            'status' => 'pending',
        ]);
        
        // Add items to plan meal
        for ($i = 0; $i < 30; $i++) {
            PlanMealItem::factory()->create([
                'plan_meal_id' => $planMeal->id,
                'product_id' => $product->id,
                'delivery_date' => now()->addDays($i + 1)->format('Y-m-d'),
                'quantity' => 1,
                'unit_price' => $product->unit_price,
                'total_price' => $product->unit_price,
            ]);
        }
        
        // Generate idempotency key
        $idempotencyKey = Str::uuid()->toString();
        
        // Perform checkout with idempotency key
        $response1 = $this->actingAs($user)
            ->postJson("/api/v2/catalogue/planmeals/{$planMeal->id}/checkout", [
                'payment_method' => 'wallet',
                'idempotency_key' => $idempotencyKey,
            ]);
        
        $response1->assertStatus(200);
        
        // Reset plan meal status (simulating a retry)
        $planMeal->update(['status' => 'pending']);
        
        // Perform checkout again with same idempotency key
        $response2 = $this->actingAs($user)
            ->postJson("/api/v2/catalogue/planmeals/{$planMeal->id}/checkout", [
                'payment_method' => 'wallet',
                'idempotency_key' => $idempotencyKey,
            ]);
        
        $response2->assertStatus(200);
        
        // Assert the responses have the same subscription ID
        $this->assertEquals(
            $response1->json('data.subscription_id'),
            $response2->json('data.subscription_id')
        );
        
        // Assert HTTP requests were made only once
        Http::assertSentCount(2); // One for subscription creation, one for wallet deduction
    }
    
    public function testUnauthorizedPlanMealCheckout(): void
    {
        // Create users
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        // Create product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
        ]);
        
        // Create plan meal for user1
        $planMeal = PlanMeal::factory()->create([
            'customer_id' => $user1->id,
            'kitchen_id' => 1,
            'menu_type' => 'lunch',
            'start_date' => now()->addDay(),
            'end_date' => now()->addDays(30),
            'quantity' => 1,
            'negotiated_price' => 3000,
            'tax' => 300,
            'discount' => 0,
            'service_charges' => 100,
            'total_bill_amount' => 3400,
            'status' => 'pending',
        ]);
        
        // Add items to plan meal
        PlanMealItem::factory()->create([
            'plan_meal_id' => $planMeal->id,
            'product_id' => $product->id,
            'delivery_date' => now()->addDay()->format('Y-m-d'),
            'quantity' => 1,
            'unit_price' => $product->unit_price,
            'total_price' => $product->unit_price,
        ]);
        
        // Attempt checkout as user2
        $response = $this->actingAs($user2)
            ->postJson("/api/v2/catalogue/planmeals/{$planMeal->id}/checkout", [
                'payment_method' => 'wallet',
            ]);
        
        $response->assertStatus(403)
            ->assertJson([
                'message' => 'Unauthorized',
            ]);
        
        // Assert no HTTP requests were made
        Http::assertNothingSent();
    }
}

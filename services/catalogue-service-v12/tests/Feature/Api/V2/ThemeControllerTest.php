<?php

namespace Tests\Feature\Api\V2;

use App\Models\Theme;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ThemeControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user
        $this->user = User::factory()->create([
            'role' => 'admin',
        ]);
        
        // Authenticate the user
        Sanctum::actingAs($this->user);
    }

    public function testIndex(): void
    {
        // Create some themes
        Theme::factory()->count(3)->create();

        // Make the request
        $response = $this->getJson('/api/v2/catalogue/themes');

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'config',
                        'is_active',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ]);
    }

    public function testStore(): void
    {
        // Create theme data
        $themeData = [
            'name' => $this->faker->word,
            'description' => $this->faker->sentence,
            'config' => [
                'primary_color' => '#ff0000',
                'secondary_color' => '#00ff00',
                'font_family' => 'Arial, sans-serif',
            ],
            'is_active' => false,
        ];

        // Make the request
        $response = $this->postJson('/api/v2/catalogue/themes', $themeData);

        // Assert the response
        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'description',
                    'config',
                    'is_active',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'message' => 'Theme created successfully',
                'data' => [
                    'name' => $themeData['name'],
                    'description' => $themeData['description'],
                    'is_active' => $themeData['is_active'],
                ],
            ]);

        // Assert the theme was created in the database
        $this->assertDatabaseHas('themes', [
            'name' => $themeData['name'],
            'description' => $themeData['description'],
            'is_active' => $themeData['is_active'],
        ]);
    }

    public function testShow(): void
    {
        // Create a theme
        $theme = Theme::factory()->create();

        // Make the request
        $response = $this->getJson("/api/v2/catalogue/themes/{$theme->id}");

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'description',
                    'config',
                    'is_active',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'data' => [
                    'id' => $theme->id,
                    'name' => $theme->name,
                    'description' => $theme->description,
                    'is_active' => $theme->is_active,
                ],
            ]);
    }

    public function testUpdate(): void
    {
        // Create a theme
        $theme = Theme::factory()->create();

        // Update data
        $updateData = [
            'name' => 'Updated Theme',
            'description' => 'This is an updated theme',
            'is_active' => true,
        ];

        // Make the request
        $response = $this->putJson("/api/v2/catalogue/themes/{$theme->id}", $updateData);

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'description',
                    'is_active',
                ],
            ])
            ->assertJson([
                'message' => 'Theme updated successfully',
                'data' => [
                    'id' => $theme->id,
                    'name' => $updateData['name'],
                    'description' => $updateData['description'],
                    'is_active' => $updateData['is_active'],
                ],
            ]);

        // Assert the theme was updated in the database
        $this->assertDatabaseHas('themes', [
            'id' => $theme->id,
            'name' => $updateData['name'],
            'description' => $updateData['description'],
            'is_active' => $updateData['is_active'],
        ]);
    }

    public function testDestroy(): void
    {
        // Create a theme
        $theme = Theme::factory()->create();

        // Make the request
        $response = $this->deleteJson("/api/v2/catalogue/themes/{$theme->id}");

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Theme deleted successfully',
            ]);

        // Assert the theme was deleted from the database
        $this->assertDatabaseMissing('themes', [
            'id' => $theme->id,
        ]);
    }

    public function testGetActiveTheme(): void
    {
        // Create an active theme
        $theme = Theme::factory()->create([
            'is_active' => true,
        ]);

        // Make the request
        $response = $this->getJson('/api/v2/catalogue/themes/active');

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'description',
                    'config',
                    'is_active',
                ],
            ])
            ->assertJson([
                'data' => [
                    'id' => $theme->id,
                    'name' => $theme->name,
                    'is_active' => true,
                ],
            ]);
    }

    public function testSetActiveTheme(): void
    {
        // Create a theme
        $theme = Theme::factory()->create([
            'is_active' => false,
        ]);

        // Make the request
        $response = $this->postJson("/api/v2/catalogue/themes/{$theme->id}/activate");

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'is_active',
                ],
            ])
            ->assertJson([
                'message' => 'Active theme set successfully',
                'data' => [
                    'id' => $theme->id,
                    'name' => $theme->name,
                    'is_active' => true,
                ],
            ]);

        // Assert the theme was updated in the database
        $this->assertDatabaseHas('themes', [
            'id' => $theme->id,
            'is_active' => true,
        ]);
    }

    public function testGetThemeConfig(): void
    {
        // Create a theme with config
        $config = [
            'primary_color' => '#ff0000',
            'secondary_color' => '#00ff00',
            'font_family' => 'Arial, sans-serif',
        ];
        $theme = Theme::factory()->create([
            'config' => $config,
        ]);

        // Make the request
        $response = $this->getJson("/api/v2/catalogue/themes/{$theme->id}/config");

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'data' => $config,
            ]);
    }

    public function testUpdateThemeConfig(): void
    {
        // Create a theme
        $theme = Theme::factory()->create();

        // New config data
        $configData = [
            'config' => [
                'primary_color' => '#0000ff',
                'secondary_color' => '#ffff00',
                'font_family' => 'Helvetica, sans-serif',
            ],
        ];

        // Make the request
        $response = $this->putJson("/api/v2/catalogue/themes/{$theme->id}/config", $configData);

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'config',
                ],
            ])
            ->assertJson([
                'message' => 'Theme configuration updated successfully',
                'data' => [
                    'id' => $theme->id,
                    'name' => $theme->name,
                    'config' => $configData['config'],
                ],
            ]);

        // Assert the theme config was updated in the database
        $this->assertDatabaseHas('themes', [
            'id' => $theme->id,
        ]);
        $updatedTheme = Theme::find($theme->id);
        $this->assertEquals($configData['config'], $updatedTheme->config);
    }
}

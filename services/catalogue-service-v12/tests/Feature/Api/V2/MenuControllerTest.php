<?php

namespace Tests\Feature\Api\V2;

use App\Models\Kitchen;
use App\Models\Menu;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class MenuControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Kitchen $kitchen;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user
        $this->user = User::factory()->create([
            'role' => 'admin',
        ]);
        
        // Create a kitchen
        $this->kitchen = Kitchen::factory()->create();
        
        // Authenticate the user
        Sanctum::actingAs($this->user);
    }

    public function testIndex(): void
    {
        // Create some menus
        Menu::factory()->count(5)->create([
            'kitchen_id' => $this->kitchen->id,
        ]);

        // Make the request
        $response = $this->getJson('/api/v2/catalogue/menus');

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'type',
                        'kitchen_id',
                        'cut_off_time',
                        'cut_off_interval',
                        'status',
                        'created_at',
                        'updated_at',
                        'kitchen' => [
                            'id',
                            'name',
                        ],
                    ],
                ],
                'meta' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total',
                ],
            ]);
    }

    public function testStore(): void
    {
        // Create menu data
        $menuData = [
            'name' => $this->faker->word,
            'type' => $this->faker->randomElement(['breakfast', 'lunch', 'dinner']),
            'kitchen_id' => $this->kitchen->id,
            'cut_off_time' => '08:00',
            'cut_off_interval' => 60,
            'status' => true,
        ];

        // Make the request
        $response = $this->postJson('/api/v2/catalogue/menus', $menuData);

        // Assert the response
        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'type',
                    'kitchen_id',
                    'cut_off_time',
                    'cut_off_interval',
                    'status',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'message' => 'Menu created successfully',
                'data' => [
                    'name' => $menuData['name'],
                    'type' => $menuData['type'],
                    'kitchen_id' => $menuData['kitchen_id'],
                    'cut_off_time' => $menuData['cut_off_time'],
                    'cut_off_interval' => $menuData['cut_off_interval'],
                    'status' => $menuData['status'],
                ],
            ]);

        // Assert the menu was created in the database
        $this->assertDatabaseHas('menus', [
            'name' => $menuData['name'],
            'type' => $menuData['type'],
            'kitchen_id' => $menuData['kitchen_id'],
        ]);
    }

    public function testShow(): void
    {
        // Create a menu
        $menu = Menu::factory()->create([
            'kitchen_id' => $this->kitchen->id,
        ]);

        // Make the request
        $response = $this->getJson("/api/v2/catalogue/menus/{$menu->id}");

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'type',
                    'kitchen_id',
                    'cut_off_time',
                    'cut_off_interval',
                    'status',
                    'created_at',
                    'updated_at',
                    'kitchen' => [
                        'id',
                        'name',
                    ],
                ],
            ])
            ->assertJson([
                'data' => [
                    'id' => $menu->id,
                    'name' => $menu->name,
                    'type' => $menu->type,
                    'kitchen_id' => $menu->kitchen_id,
                ],
            ]);
    }

    public function testUpdate(): void
    {
        // Create a menu
        $menu = Menu::factory()->create([
            'kitchen_id' => $this->kitchen->id,
        ]);

        // Update data
        $updateData = [
            'name' => 'Updated Menu',
            'type' => 'dinner',
            'status' => false,
        ];

        // Make the request
        $response = $this->putJson("/api/v2/catalogue/menus/{$menu->id}", $updateData);

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'type',
                    'kitchen_id',
                    'status',
                ],
            ])
            ->assertJson([
                'message' => 'Menu updated successfully',
                'data' => [
                    'id' => $menu->id,
                    'name' => $updateData['name'],
                    'type' => $updateData['type'],
                    'status' => $updateData['status'],
                ],
            ]);

        // Assert the menu was updated in the database
        $this->assertDatabaseHas('menus', [
            'id' => $menu->id,
            'name' => $updateData['name'],
            'type' => $updateData['type'],
            'status' => $updateData['status'],
        ]);
    }

    public function testDestroy(): void
    {
        // Create a menu
        $menu = Menu::factory()->create([
            'kitchen_id' => $this->kitchen->id,
        ]);

        // Make the request
        $response = $this->deleteJson("/api/v2/catalogue/menus/{$menu->id}");

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Menu deleted successfully',
            ]);

        // Assert the menu was deleted from the database
        $this->assertDatabaseMissing('menus', [
            'id' => $menu->id,
        ]);
    }

    public function testGetByKitchen(): void
    {
        // Create some menus for the kitchen
        Menu::factory()->count(3)->create([
            'kitchen_id' => $this->kitchen->id,
            'status' => true,
        ]);

        // Make the request
        $response = $this->getJson("/api/v2/catalogue/menus/kitchen/{$this->kitchen->id}");

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'type',
                        'kitchen_id',
                    ],
                ],
            ]);
    }

    public function testGetByType(): void
    {
        // Create some menus with specific type
        $type = 'breakfast';
        Menu::factory()->count(2)->create([
            'type' => $type,
            'status' => true,
        ]);

        // Make the request
        $response = $this->getJson("/api/v2/catalogue/menus/type/{$type}");

        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'type',
                        'kitchen_id',
                    ],
                ],
            ]);
    }
}

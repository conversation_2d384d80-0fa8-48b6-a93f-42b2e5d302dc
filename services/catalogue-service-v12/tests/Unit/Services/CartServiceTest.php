<?php

namespace Tests\Unit\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Repositories\CartRepository;
use App\Services\CartService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;
use Mockery;

class CartServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CartService $cartService;
    protected CartRepository $cartRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cartRepository = Mockery::mock(CartRepository::class);
        $this->cartService = new CartService($this->cartRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGetCurrentCart(): void
    {
        $customerId = 1;
        $sessionId = 'test-session-id';
        $cart = new Cart();
        $cart->id = 1;

        Session::shouldReceive('getId')
            ->once()
            ->andReturn($sessionId);

        $this->cartRepository->shouldReceive('getOrCreateCart')
            ->once()
            ->with($customerId, $sessionId)
            ->andReturn($cart);

        $result = $this->cartService->getCurrentCart($customerId);

        $this->assertInstanceOf(Cart::class, $result);
        $this->assertEquals(1, $result->id);
    }

    public function testAddItem(): void
    {
        $customerId = 1;
        $productId = 2;
        $quantity = 1;
        $menuType = 'breakfast';
        $deliveryDate = '2025-05-20';
        $customizations = ['spice_level' => 'medium'];
        $sessionId = 'test-session-id';
        
        $cart = new Cart();
        $cart->id = 1;
        
        $cartItem = new CartItem();
        $cartItem->id = 1;
        $cartItem->product_id = $productId;
        $cartItem->quantity = $quantity;

        Session::shouldReceive('getId')
            ->once()
            ->andReturn($sessionId);

        $this->cartRepository->shouldReceive('getOrCreateCart')
            ->once()
            ->with($customerId, $sessionId)
            ->andReturn($cart);

        $this->cartRepository->shouldReceive('addItemToCart')
            ->once()
            ->with($cart->id, $productId, $quantity, $menuType, $deliveryDate, $customizations)
            ->andReturn($cartItem);

        $result = $this->cartService->addItem(
            $productId,
            $quantity,
            $menuType,
            $deliveryDate,
            $customizations,
            $customerId
        );

        $this->assertInstanceOf(CartItem::class, $result);
        $this->assertEquals($productId, $result->product_id);
        $this->assertEquals($quantity, $result->quantity);
    }

    public function testUpdateItem(): void
    {
        $customerId = 1;
        $itemId = 1;
        $quantity = 2;
        $sessionId = 'test-session-id';
        
        $cart = new Cart();
        $cart->id = 1;
        
        $cartItem = new CartItem();
        $cartItem->id = $itemId;
        $cartItem->quantity = $quantity;

        Session::shouldReceive('getId')
            ->once()
            ->andReturn($sessionId);

        $this->cartRepository->shouldReceive('getOrCreateCart')
            ->once()
            ->with($customerId, $sessionId)
            ->andReturn($cart);

        $this->cartRepository->shouldReceive('updateCartItem')
            ->once()
            ->with($cart->id, $itemId, $quantity)
            ->andReturn($cartItem);

        $result = $this->cartService->updateItem($itemId, $quantity, $customerId);

        $this->assertInstanceOf(CartItem::class, $result);
        $this->assertEquals($itemId, $result->id);
        $this->assertEquals($quantity, $result->quantity);
    }

    public function testRemoveItem(): void
    {
        $customerId = 1;
        $itemId = 1;
        $sessionId = 'test-session-id';
        
        $cart = new Cart();
        $cart->id = 1;

        Session::shouldReceive('getId')
            ->once()
            ->andReturn($sessionId);

        $this->cartRepository->shouldReceive('getOrCreateCart')
            ->once()
            ->with($customerId, $sessionId)
            ->andReturn($cart);

        $this->cartRepository->shouldReceive('removeCartItem')
            ->once()
            ->with($cart->id, $itemId)
            ->andReturn(true);

        $result = $this->cartService->removeItem($itemId, $customerId);

        $this->assertTrue($result);
    }

    public function testClearCart(): void
    {
        $customerId = 1;
        $sessionId = 'test-session-id';
        
        $cart = new Cart();
        $cart->id = 1;

        Session::shouldReceive('getId')
            ->once()
            ->andReturn($sessionId);

        $this->cartRepository->shouldReceive('getOrCreateCart')
            ->once()
            ->with($customerId, $sessionId)
            ->andReturn($cart);

        $this->cartRepository->shouldReceive('clearCart')
            ->once()
            ->with($cart->id)
            ->andReturn(true);

        $result = $this->cartService->clearCart($customerId);

        $this->assertTrue($result);
    }

    public function testApplyPromoCode(): void
    {
        $customerId = 1;
        $promoCode = 'WELCOME10';
        $sessionId = 'test-session-id';
        
        $cart = new Cart();
        $cart->id = 1;
        $cart->promo_code = $promoCode;

        Session::shouldReceive('getId')
            ->once()
            ->andReturn($sessionId);

        $this->cartRepository->shouldReceive('getOrCreateCart')
            ->once()
            ->with($customerId, $sessionId)
            ->andReturn($cart);

        $this->cartRepository->shouldReceive('applyPromoCode')
            ->once()
            ->with($cart->id, $promoCode)
            ->andReturn($cart);

        $result = $this->cartService->applyPromoCode($promoCode, $customerId);

        $this->assertInstanceOf(Cart::class, $result);
        $this->assertEquals($promoCode, $result->promo_code);
    }
}

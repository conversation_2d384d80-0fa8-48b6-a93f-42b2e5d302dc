<?php

namespace Tests\Unit\Services;

use App\Models\Menu;
use App\Repositories\MenuRepository;
use App\Services\MenuService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Pagination\LengthAwarePaginator;
use Tests\TestCase;
use Mockery;

class MenuServiceTest extends TestCase
{
    use RefreshDatabase;

    protected MenuService $menuService;
    protected MenuRepository $menuRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->menuRepository = Mockery::mock(MenuRepository::class);
        $this->menuService = new MenuService($this->menuRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGetAllMenus(): void
    {
        $filters = ['type' => 'breakfast'];
        $perPage = 15;
        $paginator = new LengthAwarePaginator([], 0, $perPage);

        $this->menuRepository->shouldReceive('getAllMenus')
            ->once()
            ->with($filters, $perPage)
            ->andReturn($paginator);

        $result = $this->menuService->getAllMenus($filters, $perPage);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    public function testGetMenuById(): void
    {
        $menuId = 1;
        $menu = new Menu();
        $menu->id = $menuId;

        $this->menuRepository->shouldReceive('getMenuById')
            ->once()
            ->with($menuId)
            ->andReturn($menu);

        $result = $this->menuService->getMenuById($menuId);

        $this->assertInstanceOf(Menu::class, $result);
        $this->assertEquals($menuId, $result->id);
    }

    public function testCreateMenu(): void
    {
        $menuData = [
            'name' => 'Test Menu',
            'type' => 'breakfast',
            'kitchen_id' => 1,
        ];
        $menu = new Menu($menuData);

        $this->menuRepository->shouldReceive('createMenu')
            ->once()
            ->with($menuData)
            ->andReturn($menu);

        $result = $this->menuService->createMenu($menuData);

        $this->assertInstanceOf(Menu::class, $result);
        $this->assertEquals($menuData['name'], $result->name);
    }

    public function testUpdateMenu(): void
    {
        $menuId = 1;
        $menuData = [
            'name' => 'Updated Menu',
            'type' => 'lunch',
        ];
        $menu = new Menu($menuData);
        $menu->id = $menuId;

        $this->menuRepository->shouldReceive('updateMenu')
            ->once()
            ->with($menuId, $menuData)
            ->andReturn($menu);

        $result = $this->menuService->updateMenu($menuId, $menuData);

        $this->assertInstanceOf(Menu::class, $result);
        $this->assertEquals($menuData['name'], $result->name);
    }

    public function testDeleteMenu(): void
    {
        $menuId = 1;

        $this->menuRepository->shouldReceive('deleteMenu')
            ->once()
            ->with($menuId)
            ->andReturn(true);

        $result = $this->menuService->deleteMenu($menuId);

        $this->assertTrue($result);
    }

    public function testGetMenusByKitchenId(): void
    {
        $kitchenId = 1;
        $menus = new Collection([new Menu(), new Menu()]);

        $this->menuRepository->shouldReceive('getMenusByKitchenId')
            ->once()
            ->with($kitchenId)
            ->andReturn($menus);

        $result = $this->menuService->getMenusByKitchenId($kitchenId);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    public function testGetMenusByType(): void
    {
        $type = 'breakfast';
        $menus = new Collection([new Menu(), new Menu()]);

        $this->menuRepository->shouldReceive('getMenusByType')
            ->once()
            ->with($type)
            ->andReturn($menus);

        $result = $this->menuService->getMenusByType($type);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }
}

<?php

namespace Tests\Unit\Services;

use App\Models\Theme;
use App\Repositories\ThemeRepository;
use App\Services\ThemeService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class ThemeServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ThemeService $themeService;
    protected ThemeRepository $themeRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->themeRepository = Mockery::mock(ThemeRepository::class);
        $this->themeService = new ThemeService($this->themeRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGetAllThemes(): void
    {
        $themes = new Collection([new Theme(), new Theme()]);

        $this->themeRepository->shouldReceive('getAllThemes')
            ->once()
            ->andReturn($themes);

        $result = $this->themeService->getAllThemes();

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    public function testGetThemeById(): void
    {
        $themeId = 1;
        $theme = new Theme();
        $theme->id = $themeId;

        $this->themeRepository->shouldReceive('getThemeById')
            ->once()
            ->with($themeId)
            ->andReturn($theme);

        $result = $this->themeService->getThemeById($themeId);

        $this->assertInstanceOf(Theme::class, $result);
        $this->assertEquals($themeId, $result->id);
    }

    public function testGetThemeByName(): void
    {
        $themeName = 'Default Theme';
        $theme = new Theme();
        $theme->name = $themeName;

        $this->themeRepository->shouldReceive('getThemeByName')
            ->once()
            ->with($themeName)
            ->andReturn($theme);

        $result = $this->themeService->getThemeByName($themeName);

        $this->assertInstanceOf(Theme::class, $result);
        $this->assertEquals($themeName, $result->name);
    }

    public function testGetActiveTheme(): void
    {
        $theme = new Theme();
        $theme->is_active = true;

        $this->themeRepository->shouldReceive('getActiveTheme')
            ->once()
            ->andReturn($theme);

        $result = $this->themeService->getActiveTheme();

        $this->assertInstanceOf(Theme::class, $result);
        $this->assertTrue($result->is_active);
    }

    public function testCreateTheme(): void
    {
        $themeData = [
            'name' => 'New Theme',
            'description' => 'A new theme',
            'config' => ['primary_color' => '#ff0000'],
            'is_active' => false,
        ];
        $theme = new Theme($themeData);

        $this->themeRepository->shouldReceive('createTheme')
            ->once()
            ->with($themeData)
            ->andReturn($theme);

        $result = $this->themeService->createTheme($themeData);

        $this->assertInstanceOf(Theme::class, $result);
        $this->assertEquals($themeData['name'], $result->name);
    }

    public function testUpdateTheme(): void
    {
        $themeId = 1;
        $themeData = [
            'name' => 'Updated Theme',
            'description' => 'An updated theme',
        ];
        $theme = new Theme($themeData);
        $theme->id = $themeId;

        $this->themeRepository->shouldReceive('updateTheme')
            ->once()
            ->with($themeId, $themeData)
            ->andReturn($theme);

        $result = $this->themeService->updateTheme($themeId, $themeData);

        $this->assertInstanceOf(Theme::class, $result);
        $this->assertEquals($themeData['name'], $result->name);
    }

    public function testDeleteTheme(): void
    {
        $themeId = 1;

        $this->themeRepository->shouldReceive('deleteTheme')
            ->once()
            ->with($themeId)
            ->andReturn(true);

        $result = $this->themeService->deleteTheme($themeId);

        $this->assertTrue($result);
    }

    public function testSetActiveTheme(): void
    {
        $themeId = 1;
        $theme = new Theme();
        $theme->id = $themeId;
        $theme->is_active = true;

        $this->themeRepository->shouldReceive('setActiveTheme')
            ->once()
            ->with($themeId)
            ->andReturn($theme);

        $result = $this->themeService->setActiveTheme($themeId);

        $this->assertInstanceOf(Theme::class, $result);
        $this->assertTrue($result->is_active);
    }

    public function testGetThemeConfig(): void
    {
        $themeId = 1;
        $config = ['primary_color' => '#ff0000', 'secondary_color' => '#00ff00'];

        $this->themeRepository->shouldReceive('getThemeConfig')
            ->once()
            ->with($themeId)
            ->andReturn($config);

        $result = $this->themeService->getThemeConfig($themeId);

        $this->assertIsArray($result);
        $this->assertEquals($config, $result);
    }

    public function testUpdateThemeConfig(): void
    {
        $themeId = 1;
        $config = ['primary_color' => '#ff0000', 'secondary_color' => '#00ff00'];
        $theme = new Theme();
        $theme->id = $themeId;
        $theme->config = $config;

        $this->themeRepository->shouldReceive('updateThemeConfig')
            ->once()
            ->with($themeId, $config)
            ->andReturn($theme);

        $result = $this->themeService->updateThemeConfig($themeId, $config);

        $this->assertInstanceOf(Theme::class, $result);
        $this->assertEquals($config, $result->config);
    }
}

<?php

namespace Tests\Unit\Services;

use App\Services\IdempotencyService;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class IdempotencyServiceTest extends TestCase
{
    protected IdempotencyService $idempotencyService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->idempotencyService = new IdempotencyService();
        
        // Clear the cache
        Cache::flush();
    }
    
    public function testGenerateKey(): void
    {
        $key = $this->idempotencyService->generateKey();
        
        $this->assertIsString($key);
        $this->assertMatchesRegularExpression('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $key);
    }
    
    public function testProcessWithIdempotencyFirstCall(): void
    {
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        $result = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'result';
        });
        
        $this->assertEquals('result', $result);
        $this->assertEquals(1, $callCount);
        
        $cacheKey = 'idempotency:' . $operation . ':' . $key;
        $this->assertTrue(Cache::has($cacheKey));
        $this->assertEquals('result', Cache::get($cacheKey));
    }
    
    public function testProcessWithIdempotencySubsequentCalls(): void
    {
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        // First call
        $result1 = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'result1';
        });
        
        // Second call with same key and operation
        $result2 = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'result2';
        });
        
        $this->assertEquals('result1', $result1);
        $this->assertEquals('result1', $result2); // Should return cached result
        $this->assertEquals(1, $callCount); // Callback should only be called once
    }
    
    public function testProcessWithIdempotencyDifferentKeys(): void
    {
        $operation = 'test-operation';
        $callCount = 0;
        
        // First call with key1
        $result1 = $this->idempotencyService->processWithIdempotency('key1', $operation, function () use (&$callCount) {
            $callCount++;
            return 'result1';
        });
        
        // Second call with key2
        $result2 = $this->idempotencyService->processWithIdempotency('key2', $operation, function () use (&$callCount) {
            $callCount++;
            return 'result2';
        });
        
        $this->assertEquals('result1', $result1);
        $this->assertEquals('result2', $result2);
        $this->assertEquals(2, $callCount); // Callback should be called twice
    }
    
    public function testProcessWithIdempotencyDifferentOperations(): void
    {
        $key = 'test-key';
        $callCount = 0;
        
        // First call with operation1
        $result1 = $this->idempotencyService->processWithIdempotency($key, 'operation1', function () use (&$callCount) {
            $callCount++;
            return 'result1';
        });
        
        // Second call with operation2
        $result2 = $this->idempotencyService->processWithIdempotency($key, 'operation2', function () use (&$callCount) {
            $callCount++;
            return 'result2';
        });
        
        $this->assertEquals('result1', $result1);
        $this->assertEquals('result2', $result2);
        $this->assertEquals(2, $callCount); // Callback should be called twice
    }
    
    public function testProcessWithIdempotencyExceptionHandling(): void
    {
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        try {
            $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
                $callCount++;
                throw new \Exception('Test exception');
            });
            
            $this->fail('Exception should have been thrown');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(1, $callCount);
            
            $cacheKey = 'idempotency:' . $operation . ':' . $key;
            $this->assertFalse(Cache::has($cacheKey)); // Cache should not be set on exception
        }
    }
    
    public function testProcessWithIdempotencyCacheTTL(): void
    {
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        // Mock the Cache facade to verify TTL
        Cache::shouldReceive('get')
            ->once()
            ->with('idempotency:' . $operation . ':' . $key)
            ->andReturn(null);
        
        Cache::shouldReceive('put')
            ->once()
            ->with('idempotency:' . $operation . ':' . $key, 'result', 86400)
            ->andReturn(true);
        
        $result = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'result';
        });
        
        $this->assertEquals('result', $result);
        $this->assertEquals(1, $callCount);
    }
}

<?php

namespace Tests\Unit\Services;

use App\Services\CircuitBreakerService;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CircuitBreakerServiceTest extends TestCase
{
    protected CircuitBreakerService $circuitBreaker;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->circuitBreaker = new CircuitBreakerService();
        
        // Clear the cache
        Cache::flush();
    }
    
    public function testSuccessfulExecution(): void
    {
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
    }
    
    public function testFailedExecutionIncrementsFailureCount(): void
    {
        $cacheKey = 'circuit_breaker:test_service';
        
        try {
            $this->circuitBreaker->execute('test_service', function () {
                throw new \Exception('Test exception');
            });
            
            $this->fail('Exception should have been thrown');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }
        
        $circuitState = Cache::get($cacheKey);
        $this->assertEquals(1, $circuitState['failures']);
        $this->assertFalse($circuitState['open']);
    }
    
    public function testCircuitOpensAfterThresholdFailures(): void
    {
        $cacheKey = 'circuit_breaker:test_service';
        
        // Simulate 5 failures
        for ($i = 0; $i < 5; $i++) {
            try {
                $this->circuitBreaker->execute('test_service', function () {
                    throw new \Exception('Test exception');
                }, 5);
            } catch (\Exception $e) {
                // Expected
            }
        }
        
        $circuitState = Cache::get($cacheKey);
        $this->assertEquals(5, $circuitState['failures']);
        $this->assertTrue($circuitState['open']);
    }
    
    public function testCircuitRejectionWhenOpen(): void
    {
        $cacheKey = 'circuit_breaker:test_service';
        
        // Set circuit to open state
        Cache::put($cacheKey, [
            'failures' => 5,
            'open' => true,
            'last_failure' => time(),
        ]);
        
        try {
            $this->circuitBreaker->execute('test_service', function () {
                return 'success';
            });
            
            $this->fail('Exception should have been thrown when circuit is open');
        } catch (\Exception $e) {
            $this->assertEquals('Service test_service is unavailable', $e->getMessage());
        }
    }
    
    public function testCircuitResetsAfterTimeout(): void
    {
        $cacheKey = 'circuit_breaker:test_service';
        
        // Set circuit to open state with last failure in the past
        Cache::put($cacheKey, [
            'failures' => 5,
            'open' => true,
            'last_failure' => time() - 31, // 31 seconds ago (default timeout is 30)
        ]);
        
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
        
        $circuitState = Cache::get($cacheKey);
        $this->assertEquals(0, $circuitState['failures']);
        $this->assertFalse($circuitState['open']);
    }
    
    public function testSuccessResetsFailureCount(): void
    {
        $cacheKey = 'circuit_breaker:test_service';
        
        // Set circuit to have some failures but not open
        Cache::put($cacheKey, [
            'failures' => 3,
            'open' => false,
            'last_failure' => time(),
        ]);
        
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
        
        $circuitState = Cache::get($cacheKey);
        $this->assertEquals(0, $circuitState['failures']);
        $this->assertFalse($circuitState['open']);
    }
    
    public function testCustomThresholdAndTimeout(): void
    {
        $cacheKey = 'circuit_breaker:test_service';
        
        // Simulate 2 failures with threshold of 3
        for ($i = 0; $i < 2; $i++) {
            try {
                $this->circuitBreaker->execute('test_service', function () {
                    throw new \Exception('Test exception');
                }, 3, 10);
            } catch (\Exception $e) {
                // Expected
            }
        }
        
        $circuitState = Cache::get($cacheKey);
        $this->assertEquals(2, $circuitState['failures']);
        $this->assertFalse($circuitState['open']);
        
        // One more failure should open the circuit
        try {
            $this->circuitBreaker->execute('test_service', function () {
                throw new \Exception('Test exception');
            }, 3, 10);
        } catch (\Exception $e) {
            // Expected
        }
        
        $circuitState = Cache::get($cacheKey);
        $this->assertEquals(3, $circuitState['failures']);
        $this->assertTrue($circuitState['open']);
        
        // Set last failure to be 9 seconds ago (timeout is 10)
        Cache::put($cacheKey, [
            'failures' => 3,
            'open' => true,
            'last_failure' => time() - 9,
        ]);
        
        try {
            $this->circuitBreaker->execute('test_service', function () {
                return 'success';
            }, 3, 10);
            
            $this->fail('Exception should have been thrown when circuit is open');
        } catch (\Exception $e) {
            $this->assertEquals('Service test_service is unavailable', $e->getMessage());
        }
        
        // Set last failure to be 11 seconds ago (timeout is 10)
        Cache::put($cacheKey, [
            'failures' => 3,
            'open' => true,
            'last_failure' => time() - 11,
        ]);
        
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        }, 3, 10);
        
        $this->assertEquals('success', $result);
    }
}

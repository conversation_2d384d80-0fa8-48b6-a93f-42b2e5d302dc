<?php

namespace Tests\Unit\Services;

use App\Services\FeatureFlagService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class FeatureFlagServiceTest extends TestCase
{
    protected FeatureFlagService $featureFlagService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->featureFlagService = new FeatureFlagService();
        
        // Clear the cache
        Cache::flush();
    }
    
    public function testIsEnabledWithEnabledFeature(): void
    {
        Config::shouldReceive('get')
            ->once()
            ->with('features.test_feature', false)
            ->andReturn(true);
        
        $result = $this->featureFlagService->isEnabled('test_feature');
        
        $this->assertTrue($result);
    }
    
    public function testIsEnabledWithDisabledFeature(): void
    {
        Config::shouldReceive('get')
            ->once()
            ->with('features.test_feature', false)
            ->andReturn(false);
        
        $result = $this->featureFlagService->isEnabled('test_feature');
        
        $this->assertFalse($result);
    }
    
    public function testIsEnabledWithNonExistentFeature(): void
    {
        Config::shouldReceive('get')
            ->once()
            ->with('features.non_existent_feature', false)
            ->andReturn(false);
        
        $result = $this->featureFlagService->isEnabled('non_existent_feature');
        
        $this->assertFalse($result);
    }
    
    public function testIsEnabledWithCustomDefault(): void
    {
        Config::shouldReceive('get')
            ->once()
            ->with('features.test_feature', true)
            ->andReturn(true);
        
        $result = $this->featureFlagService->isEnabled('test_feature', true);
        
        $this->assertTrue($result);
    }
    
    public function testGetVariantWithExistingVariant(): void
    {
        Config::shouldReceive('get')
            ->once()
            ->with('feature_variants.test_feature', 'default')
            ->andReturn('variant_a');
        
        $result = $this->featureFlagService->getVariant('test_feature');
        
        $this->assertEquals('variant_a', $result);
    }
    
    public function testGetVariantWithNonExistentVariant(): void
    {
        Config::shouldReceive('get')
            ->once()
            ->with('feature_variants.non_existent_feature', 'default')
            ->andReturn('default');
        
        $result = $this->featureFlagService->getVariant('non_existent_feature');
        
        $this->assertEquals('default', $result);
    }
    
    public function testGetVariantWithCustomDefault(): void
    {
        Config::shouldReceive('get')
            ->once()
            ->with('feature_variants.test_feature', 'custom_default')
            ->andReturn('custom_default');
        
        $result = $this->featureFlagService->getVariant('test_feature', 'custom_default');
        
        $this->assertEquals('custom_default', $result);
    }
    
    public function testCaching(): void
    {
        Config::shouldReceive('get')
            ->once() // Should only be called once due to caching
            ->with('features.test_feature', false)
            ->andReturn(true);
        
        // First call should cache the result
        $result1 = $this->featureFlagService->isEnabled('test_feature');
        
        // Second call should use the cached result
        $result2 = $this->featureFlagService->isEnabled('test_feature');
        
        $this->assertTrue($result1);
        $this->assertTrue($result2);
        
        $cacheKey = 'feature_flag:test_feature';
        $this->assertTrue(Cache::has($cacheKey));
        $this->assertTrue(Cache::get($cacheKey));
    }
    
    public function testClearCache(): void
    {
        // Set up some cached values
        Cache::put('feature_flag:feature1', true, 300);
        Cache::put('feature_flag:feature2', false, 300);
        Cache::put('feature_variant:variant1', 'a', 300);
        Cache::put('feature_variant:variant2', 'b', 300);
        
        // Mock Config::get for features and variants
        Config::shouldReceive('get')
            ->once()
            ->with('features', [])
            ->andReturn([
                'feature1' => true,
                'feature2' => false,
            ]);
        
        Config::shouldReceive('get')
            ->once()
            ->with('feature_variants', [])
            ->andReturn([
                'variant1' => 'a',
                'variant2' => 'b',
            ]);
        
        // Clear the cache
        $this->featureFlagService->clearCache();
        
        // Verify cache was cleared
        $this->assertFalse(Cache::has('feature_flag:feature1'));
        $this->assertFalse(Cache::has('feature_flag:feature2'));
        $this->assertFalse(Cache::has('feature_variant:variant1'));
        $this->assertFalse(Cache::has('feature_variant:variant2'));
    }
}

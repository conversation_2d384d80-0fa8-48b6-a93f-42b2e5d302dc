<?php

namespace Tests\Unit\Services;

use App\Services\RetryService;
use Tests\TestCase;

class RetryServiceTest extends TestCase
{
    protected RetryService $retryService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->retryService = new RetryService();
    }
    
    public function testSuccessfulExecution(): void
    {
        $result = $this->retryService->execute(function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
    }
    
    public function testRetryOnFailure(): void
    {
        $attempts = 0;
        
        $result = $this->retryService->execute(function () use (&$attempts) {
            $attempts++;
            
            if ($attempts < 2) {
                throw new \Exception('Test exception');
            }
            
            return 'success after retry';
        });
        
        $this->assertEquals(2, $attempts);
        $this->assertEquals('success after retry', $result);
    }
    
    public function testMaxRetriesExceeded(): void
    {
        $attempts = 0;
        
        try {
            $this->retryService->execute(function () use (&$attempts) {
                $attempts++;
                throw new \Exception('Test exception');
            }, 'test_operation', 3);
            
            $this->fail('Exception should have been thrown after max retries');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(3, $attempts);
        }
    }
    
    public function testCustomMaxRetries(): void
    {
        $attempts = 0;
        
        try {
            $this->retryService->execute(function () use (&$attempts) {
                $attempts++;
                throw new \Exception('Test exception');
            }, 'test_operation', 5);
            
            $this->fail('Exception should have been thrown after max retries');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(5, $attempts);
        }
    }
    
    public function testCustomBaseDelay(): void
    {
        $attempts = 0;
        $startTime = microtime(true);
        
        try {
            $this->retryService->execute(function () use (&$attempts) {
                $attempts++;
                throw new \Exception('Test exception');
            }, 'test_operation', 2, 200);
            
            $this->fail('Exception should have been thrown after max retries');
        } catch (\Exception $e) {
            $endTime = microtime(true);
            $duration = ($endTime - $startTime) * 1000; // Convert to milliseconds
            
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(2, $attempts);
            
            // First retry should wait at least 200ms
            // We use a lower bound to account for timing variations
            $this->assertGreaterThan(180, $duration);
        }
    }
    
    public function testExponentialBackoff(): void
    {
        $attempts = 0;
        $delays = [];
        $startTimes = [];
        
        try {
            $this->retryService->execute(function () use (&$attempts, &$startTimes) {
                $startTimes[] = microtime(true);
                $attempts++;
                throw new \Exception('Test exception');
            }, 'test_operation', 3, 100);
            
            $this->fail('Exception should have been thrown after max retries');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(3, $attempts);
            
            // Calculate actual delays
            for ($i = 1; $i < count($startTimes); $i++) {
                $delays[] = ($startTimes[$i] - $startTimes[$i - 1]) * 1000; // Convert to milliseconds
            }
            
            // First retry should be around 100ms
            $this->assertGreaterThan(80, $delays[0]);
            
            // Second retry should be around 200ms (100 * 2^1)
            $this->assertGreaterThan(160, $delays[1]);
            
            // Verify exponential increase
            $this->assertGreaterThan($delays[0], $delays[1]);
        }
    }
}

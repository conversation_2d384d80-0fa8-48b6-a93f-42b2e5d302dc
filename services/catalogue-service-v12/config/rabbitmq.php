<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Connection Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for connecting to RabbitMQ.
    |
    */

    'host' => env('RABBITMQ_HOST', 'localhost'),
    'port' => env('RABBITMQ_PORT', 5672),
    'user' => env('RABBITMQ_USER', 'guest'),
    'password' => env('RABBITMQ_PASSWORD', 'guest'),
    'vhost' => env('RABBITMQ_VHOST', '/'),

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Exchange Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for RabbitMQ exchanges.
    |
    */

    'exchanges' => [
        'catalogue_events' => [
            'name' => env('RABBITMQ_EXCHANGE_CATALOGUE_EVENTS', 'catalogue_events'),
            'type' => 'topic',
            'passive' => false,
            'durable' => true,
            'auto_delete' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Queue Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for RabbitMQ queues.
    |
    */

    'queues' => [
        'menu_events' => [
            'name' => env('RABBITMQ_QUEUE_MENU_EVENTS', 'menu_events'),
            'passive' => false,
            'durable' => true,
            'exclusive' => false,
            'auto_delete' => false,
            'binding' => [
                'exchange' => 'catalogue_events',
                'routing_key' => 'menu.*',
            ],
        ],
        'theme_events' => [
            'name' => env('RABBITMQ_QUEUE_THEME_EVENTS', 'theme_events'),
            'passive' => false,
            'durable' => true,
            'exclusive' => false,
            'auto_delete' => false,
            'binding' => [
                'exchange' => 'catalogue_events',
                'routing_key' => 'theme.*',
            ],
        ],
        'kitchen_events' => [
            'name' => env('RABBITMQ_QUEUE_KITCHEN_EVENTS', 'kitchen_events'),
            'passive' => false,
            'durable' => true,
            'exclusive' => false,
            'auto_delete' => false,
            'binding' => [
                'exchange' => 'kitchen_events',
                'routing_key' => 'kitchen.*',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Consumer Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for RabbitMQ consumers.
    |
    */

    'consumers' => [
        'menu_events' => [
            'queue' => 'menu_events',
            'prefetch_count' => 10,
            'listener' => \App\Listeners\MenuEventListener::class,
        ],
        'theme_events' => [
            'queue' => 'theme_events',
            'prefetch_count' => 10,
            'listener' => \App\Listeners\ThemeEventListener::class,
        ],
        'kitchen_events' => [
            'queue' => 'kitchen_events',
            'prefetch_count' => 10,
            'listener' => \App\Listeners\MenuEventListener::class,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Dead Letter Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for RabbitMQ dead letter exchanges.
    |
    */

    'dead_letter' => [
        'enabled' => true,
        'exchange' => env('RABBITMQ_DEAD_LETTER_EXCHANGE', 'catalogue_events_dlx'),
        'queue' => env('RABBITMQ_DEAD_LETTER_QUEUE', 'catalogue_events_dlq'),
    ],
];

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Feature Variants
    |--------------------------------------------------------------------------
    |
    | This file is for storing the feature variants for the application.
    | Feature variants can be used to test different implementations of a feature.
    | They can be overridden by environment variables.
    |
    */

    'checkout_flow' => env('FEATURE_VARIANT_CHECKOUT_FLOW', 'default'),
    'subscription_ui' => env('FEATURE_VARIANT_SUBSCRIPTION_UI', 'default'),
    'cart_ui' => env('FEATURE_VARIANT_CART_UI', 'default'),
    'payment_ui' => env('FEATURE_VARIANT_PAYMENT_UI', 'default'),
];

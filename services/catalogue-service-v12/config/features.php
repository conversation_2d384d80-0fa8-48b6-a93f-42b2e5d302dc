<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | This file is for storing the feature flags for the application.
    | Feature flags can be used to enable or disable features in the application.
    | They can be overridden by environment variables.
    |
    */

    'new_payment_methods' => env('FEATURE_NEW_PAYMENT_METHODS', false),
    'subscription_discounts' => env('FEATURE_SUBSCRIPTION_DISCOUNTS', false),
    'advanced_cart_recommendations' => env('FEATURE_ADVANCED_CART_RECOMMENDATIONS', false),
    'enhanced_checkout' => env('FEATURE_ENHANCED_CHECKOUT', false),
    'multi_kitchen_cart' => env('FEATURE_MULTI_KITCHEN_CART', false),
    'theme_preview' => env('FEATURE_THEME_PREVIEW', false),
];

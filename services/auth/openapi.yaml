openapi: 3.1.0
info:
  title: Auth Service API
  description: Authentication and authorization service for OneFood Dialer
  version: 1.0.0
  contact:
    name: OneFood Dialer API Team
    email: <EMAIL>
    url: https://api.onefooddialer.com/docs
  license:
    name: Proprietary
servers:
  - url: http://localhost:8000/api/v1
    description: Local development server
  - url: https://api.onefooddialer.com/api/v1
    description: Production server
tags:
  - name: Authentication
    description: Authentication endpoints
  - name: User
    description: User management endpoints
  - name: Health
    description: Health check endpoints
paths:
  /auth/health:
    get:
      summary: Health check
      description: Check the health of the auth service
      operationId: checkHealth
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
  /auth/login:
    post:
      summary: Login
      description: Authenticate a user and get an access token
      operationId: login
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
  /auth/register:
    post:
      summary: Register
      description: Create a new user account
      operationId: register
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
  /auth/logout:
    post:
      summary: Logout
      description: Invalidate the current user's token
      operationId: logout
      tags:
        - Authentication
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LogoutResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/refresh-token:
    post:
      summary: Refresh token
      description: Get a new access token using a refresh token
      operationId: refreshToken
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refresh successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/user:
    get:
      summary: Get current user
      description: Get the currently authenticated user's details
      operationId: getCurrentUser
      tags:
        - User
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/profile:
    put:
      summary: Update profile
      description: Update the current user's profile
      operationId: updateProfile
      tags:
        - User
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
  /auth/forgot-password:
    post:
      summary: Forgot password
      description: Send a password reset link to the user's email
      operationId: forgotPassword
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForgotPasswordRequest'
      responses:
        '200':
          description: Password reset link sent
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
  /auth/reset-password:
    post:
      summary: Reset password
      description: Reset a user's password using a token
      operationId: resetPassword
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '401':
          description: Invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: John Doe
        email:
          type: string
          format: email
          example: <EMAIL>
        email_verified_at:
          type: string
          format: date-time
          nullable: true
          example: '2023-05-19T14:32:15Z'
        created_at:
          type: string
          format: date-time
          example: '2023-05-19T14:32:15Z'
        updated_at:
          type: string
          format: date-time
          example: '2023-05-19T14:32:15Z'
        roles:
          type: array
          items:
            type: string
          example: ['user']
        permissions:
          type: array
          items:
            type: string
          example: ['view-profile', 'manage-account']
      required:
        - id
        - name
        - email
        - created_at
        - updated_at
        - roles
        - permissions
    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        password:
          type: string
          format: password
          example: password123
        remember_me:
          type: boolean
          example: true
      required:
        - email
        - password
    LoginResponse:
      type: object
      properties:
        token:
          type: string
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        refresh_token:
          type: string
          example: def50200641f3e1770...
        user:
          $ref: '#/components/schemas/User'
        expires_at:
          type: string
          format: date-time
          example: '2023-05-20T14:32:15Z'
      required:
        - token
        - user
        - expires_at
    RegisterRequest:
      type: object
      properties:
        name:
          type: string
          example: John Doe
        email:
          type: string
          format: email
          example: <EMAIL>
        password:
          type: string
          format: password
          example: password123
        password_confirmation:
          type: string
          format: password
          example: password123
      required:
        - name
        - email
        - password
        - password_confirmation
    RegisterResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
      required:
        - user
    LogoutResponse:
      type: object
      properties:
        message:
          type: string
          example: Successfully logged out
      required:
        - message
    RefreshTokenRequest:
      type: object
      properties:
        refresh_token:
          type: string
          example: def50200641f3e1770...
      required:
        - refresh_token
    UpdateProfileRequest:
      type: object
      properties:
        name:
          type: string
          example: John Doe
        email:
          type: string
          format: email
          example: <EMAIL>
        current_password:
          type: string
          format: password
          example: password123
        password:
          type: string
          format: password
          example: newpassword123
        password_confirmation:
          type: string
          format: password
          example: newpassword123
      required:
        - name
        - email
    ForgotPasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
      required:
        - email
    ResetPasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        token:
          type: string
          example: 1234567890abcdef
        password:
          type: string
          format: password
          example: newpassword123
        password_confirmation:
          type: string
          format: password
          example: newpassword123
      required:
        - email
        - token
        - password
        - password_confirmation
    MessageResponse:
      type: object
      properties:
        message:
          type: string
          example: Operation completed successfully
      required:
        - message
    ErrorResponse:
      type: object
      properties:
        message:
          type: string
          example: An error occurred
        status:
          type: string
          example: error
        code:
          type: integer
          example: 401
        error_id:
          type: string
          example: auth_001
        timestamp:
          type: string
          format: date-time
          example: '2023-05-19T14:32:15Z'
      required:
        - message
        - status
        - code
    ValidationErrorResponse:
      type: object
      properties:
        message:
          type: string
          example: The given data was invalid
        status:
          type: string
          example: error
        code:
          type: integer
          example: 422
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            email: ['The email field is required.']
            password: ['The password field is required.']
      required:
        - message
        - status
        - code
        - errors
    HealthResponse:
      type: object
      properties:
        service:
          type: string
          example: auth-service
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
          example: healthy
        version:
          type: string
          example: 1.0.0
        environment:
          type: string
          example: production
        timestamp:
          type: string
          format: date-time
          example: '2023-05-19T14:32:15Z'
        response_time_ms:
          type: number
          example: 42.5
        checks:
          type: object
          additionalProperties:
            type: object
            properties:
              status:
                type: string
                enum: [healthy, degraded, unhealthy, unknown]
                example: healthy
              message:
                type: string
                example: Database connection successful
              response_time_ms:
                type: number
                example: 12.3
            required:
              - status
              - message
          example:
            database:
              status: healthy
              message: Database connection successful
              response_time_ms: 12.3
            cache:
              status: healthy
              message: Cache connection successful
              response_time_ms: 5.7
      required:
        - service
        - status
        - version
        - timestamp

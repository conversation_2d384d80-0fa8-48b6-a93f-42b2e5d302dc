<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class HealthController extends Controller
{
    /**
     * Check the health of the service
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function check()
    {
        $startTime = microtime(true);
        
        $status = 'healthy';
        $checks = [];
        
        // Check database connection
        try {
            $dbStartTime = microtime(true);
            DB::connection()->getPdo();
            $dbEndTime = microtime(true);
            $dbResponseTime = round(($dbEndTime - $dbStartTime) * 1000, 2);
            
            $checks['database'] = [
                'status' => 'healthy',
                'message' => 'Database connection successful',
                'response_time_ms' => $dbResponseTime
            ];
            
            // If response time is too high, mark as degraded
            if ($dbResponseTime > 100) {
                $checks['database']['status'] = 'degraded';
                $checks['database']['message'] = 'Database connection is slow';
                $status = 'degraded';
            }
        } catch (\Exception $e) {
            $checks['database'] = [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
            $status = 'unhealthy';
            Log::error('Health check database error: ' . $e->getMessage());
        }
        
        // Check cache connection
        try {
            $cacheStartTime = microtime(true);
            $cacheKey = 'health_check_' . time();
            Cache::put($cacheKey, true, 10);
            $cacheValue = Cache::get($cacheKey);
            $cacheEndTime = microtime(true);
            $cacheResponseTime = round(($cacheEndTime - $cacheStartTime) * 1000, 2);
            
            $checks['cache'] = [
                'status' => 'healthy',
                'message' => 'Cache connection successful',
                'response_time_ms' => $cacheResponseTime
            ];
            
            // If response time is too high, mark as degraded
            if ($cacheResponseTime > 50) {
                $checks['cache']['status'] = 'degraded';
                $checks['cache']['message'] = 'Cache connection is slow';
                if ($status !== 'unhealthy') {
                    $status = 'degraded';
                }
            }
        } catch (\Exception $e) {
            $checks['cache'] = [
                'status' => 'unhealthy',
                'message' => 'Cache connection failed: ' . $e->getMessage()
            ];
            $status = 'unhealthy';
            Log::error('Health check cache error: ' . $e->getMessage());
        }
        
        // Check disk space
        try {
            $diskFree = disk_free_space(storage_path());
            $diskTotal = disk_total_space(storage_path());
            $diskUsedPercentage = round(100 - ($diskFree / $diskTotal * 100), 2);
            
            $checks['disk'] = [
                'status' => 'healthy',
                'message' => 'Disk space is sufficient',
                'used_percentage' => $diskUsedPercentage,
                'free_space_mb' => round($diskFree / 1024 / 1024, 2)
            ];
            
            // If disk usage is too high, mark as degraded or unhealthy
            if ($diskUsedPercentage > 90) {
                $checks['disk']['status'] = 'unhealthy';
                $checks['disk']['message'] = 'Disk space is critically low';
                $status = 'unhealthy';
            } elseif ($diskUsedPercentage > 80) {
                $checks['disk']['status'] = 'degraded';
                $checks['disk']['message'] = 'Disk space is running low';
                if ($status !== 'unhealthy') {
                    $status = 'degraded';
                }
            }
        } catch (\Exception $e) {
            $checks['disk'] = [
                'status' => 'unknown',
                'message' => 'Could not check disk space: ' . $e->getMessage()
            ];
            Log::warning('Health check disk error: ' . $e->getMessage());
        }
        
        // Check memory usage
        try {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->getMemoryLimitInBytes();
            $memoryUsedPercentage = round($memoryUsage / $memoryLimit * 100, 2);
            
            $checks['memory'] = [
                'status' => 'healthy',
                'message' => 'Memory usage is normal',
                'used_percentage' => $memoryUsedPercentage,
                'used_mb' => round($memoryUsage / 1024 / 1024, 2),
                'limit_mb' => round($memoryLimit / 1024 / 1024, 2)
            ];
            
            // If memory usage is too high, mark as degraded or unhealthy
            if ($memoryUsedPercentage > 90) {
                $checks['memory']['status'] = 'unhealthy';
                $checks['memory']['message'] = 'Memory usage is critically high';
                $status = 'unhealthy';
            } elseif ($memoryUsedPercentage > 80) {
                $checks['memory']['status'] = 'degraded';
                $checks['memory']['message'] = 'Memory usage is high';
                if ($status !== 'unhealthy') {
                    $status = 'degraded';
                }
            }
        } catch (\Exception $e) {
            $checks['memory'] = [
                'status' => 'unknown',
                'message' => 'Could not check memory usage: ' . $e->getMessage()
            ];
            Log::warning('Health check memory error: ' . $e->getMessage());
        }
        
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2);
        
        return response()->json([
            'service' => 'auth-service',
            'status' => $status,
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
            'timestamp' => now()->toIso8601String(),
            'response_time_ms' => $responseTime,
            'checks' => $checks
        ]);
    }
    
    /**
     * Get the PHP memory limit in bytes
     *
     * @return int
     */
    private function getMemoryLimitInBytes()
    {
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryLimit === '-1') {
            // No memory limit
            return PHP_INT_MAX;
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $memoryLimit = (int) $memoryLimit;
        
        switch ($unit) {
            case 'g':
                $memoryLimit *= 1024;
                // no break
            case 'm':
                $memoryLimit *= 1024;
                // no break
            case 'k':
                $memoryLimit *= 1024;
        }
        
        return $memoryLimit;
    }
}

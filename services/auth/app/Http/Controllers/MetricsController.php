<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Prometheus\CollectorRegistry;
use Prometheus\RenderTextFormat;
use Prometheus\Storage\InMemory;

class MetricsController extends Controller
{
    /**
     * The Prometheus registry.
     *
     * @var \Prometheus\CollectorRegistry
     */
    protected $registry;

    /**
     * Create a new metrics controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->registry = new CollectorRegistry(new InMemory());
    }

    /**
     * Get Prometheus metrics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function metrics(Request $request)
    {
        // Collect system metrics
        $this->collectSystemMetrics();
        
        // Collect application metrics
        $this->collectApplicationMetrics();
        
        // Collect database metrics
        $this->collectDatabaseMetrics();
        
        // Collect cache metrics
        $this->collectCacheMetrics();
        
        // Collect authentication metrics
        $this->collectAuthMetrics();
        
        // Render metrics in Prometheus format
        $renderer = new RenderTextFormat();
        $result = $renderer->render($this->registry->getMetricFamilySamples());
        
        return response($result, 200)
            ->header('Content-Type', RenderTextFormat::MIME_TYPE);
    }
    
    /**
     * Collect system metrics.
     *
     * @return void
     */
    protected function collectSystemMetrics()
    {
        // System load
        $loadAvg = sys_getloadavg();
        $gauge = $this->registry->getOrRegisterGauge(
            'system',
            'load_average',
            'System load average',
            ['period']
        );
        $gauge->set($loadAvg[0], ['1m']);
        $gauge->set($loadAvg[1], ['5m']);
        $gauge->set($loadAvg[2], ['15m']);
        
        // Memory usage
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        
        $gauge = $this->registry->getOrRegisterGauge(
            'php',
            'memory_usage_bytes',
            'PHP memory usage in bytes'
        );
        $gauge->set($memoryUsage);
        
        $gauge = $this->registry->getOrRegisterGauge(
            'php',
            'memory_peak_usage_bytes',
            'PHP peak memory usage in bytes'
        );
        $gauge->set($memoryPeak);
    }
    
    /**
     * Collect application metrics.
     *
     * @return void
     */
    protected function collectApplicationMetrics()
    {
        // Application version
        $gauge = $this->registry->getOrRegisterGauge(
            'app',
            'version',
            'Application version',
            ['version']
        );
        $gauge->set(1, [config('app.version', '1.0.0')]);
        
        // Request count
        $counter = $this->registry->getOrRegisterCounter(
            'http',
            'requests_total',
            'Total number of HTTP requests',
            ['method', 'path', 'status']
        );
        
        // For demonstration, we'll increment for the current request
        $method = request()->method();
        $path = request()->path();
        $status = 200;
        
        $counter->inc(['GET', '/metrics', 200]);
        
        // Request duration
        $startTime = defined('LARAVEL_START') ? LARAVEL_START : microtime(true);
        $duration = microtime(true) - $startTime;
        
        $histogram = $this->registry->getOrRegisterHistogram(
            'http',
            'request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'path'],
            [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10]
        );
        
        $histogram->observe($duration, [$method, $path]);
    }
    
    /**
     * Collect database metrics.
     *
     * @return void
     */
    protected function collectDatabaseMetrics()
    {
        try {
            // Database connections
            $connections = DB::getConnections();
            $gauge = $this->registry->getOrRegisterGauge(
                'db',
                'connections',
                'Number of database connections',
                ['state']
            );
            
            $gauge->set(count($connections), ['total']);
            
            // Query count and duration (from Laravel's DB statistics)
            $queryCount = DB::getQueryLog();
            $counter = $this->registry->getOrRegisterCounter(
                'db',
                'queries_total',
                'Total number of database queries'
            );
            $counter->inc(count($queryCount));
            
            // For demonstration, we'll add a simple query to measure
            $startTime = microtime(true);
            DB::select('SELECT 1');
            $duration = microtime(true) - $startTime;
            
            $histogram = $this->registry->getOrRegisterHistogram(
                'db',
                'query_duration_seconds',
                'Database query duration in seconds',
                ['type'],
                [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1]
            );
            
            $histogram->observe($duration, ['select']);
        } catch (\Exception $e) {
            Log::error('Failed to collect database metrics: ' . $e->getMessage());
        }
    }
    
    /**
     * Collect cache metrics.
     *
     * @return void
     */
    protected function collectCacheMetrics()
    {
        try {
            // Cache hits and misses
            $cacheKey = 'metrics_test_' . time();
            
            // Test cache hit/miss
            $startTime = microtime(true);
            $exists = Cache::has($cacheKey);
            $duration = microtime(true) - $startTime;
            
            $counter = $this->registry->getOrRegisterCounter(
                'cache',
                'operations_total',
                'Total number of cache operations',
                ['operation', 'result']
            );
            
            $counter->inc(['has', $exists ? 'hit' : 'miss']);
            
            // Set a value
            Cache::put($cacheKey, true, 60);
            $counter->inc(['put', 'success']);
            
            // Get the value (should be a hit)
            $startTime = microtime(true);
            $value = Cache::get($cacheKey);
            $duration = microtime(true) - $startTime;
            
            $counter->inc(['get', 'hit']);
            
            $histogram = $this->registry->getOrRegisterHistogram(
                'cache',
                'operation_duration_seconds',
                'Cache operation duration in seconds',
                ['operation'],
                [0.0001, 0.0005, 0.001, 0.005, 0.01, 0.05, 0.1]
            );
            
            $histogram->observe($duration, ['get']);
            
            // Clean up
            Cache::forget($cacheKey);
            $counter->inc(['forget', 'success']);
        } catch (\Exception $e) {
            Log::error('Failed to collect cache metrics: ' . $e->getMessage());
        }
    }
    
    /**
     * Collect authentication metrics.
     *
     * @return void
     */
    protected function collectAuthMetrics()
    {
        // Authentication status
        $gauge = $this->registry->getOrRegisterGauge(
            'auth',
            'users_logged_in',
            'Number of users currently logged in'
        );
        
        // For demonstration, we'll use a simple count
        // In a real application, this would be more sophisticated
        $loggedInUsers = 0;
        
        if (Auth::check()) {
            $loggedInUsers = 1;
        }
        
        $gauge->set($loggedInUsers);
        
        // Authentication attempts (from cache or database)
        $counter = $this->registry->getOrRegisterCounter(
            'auth',
            'login_attempts_total',
            'Total number of login attempts',
            ['result']
        );
        
        // For demonstration, we'll use hardcoded values
        // In a real application, these would come from actual statistics
        $successfulAttempts = Cache::get('auth_metrics_successful_attempts', 0);
        $failedAttempts = Cache::get('auth_metrics_failed_attempts', 0);
        
        $counter->inc(['success'], $successfulAttempts);
        $counter->inc(['failure'], $failedAttempts);
    }
}

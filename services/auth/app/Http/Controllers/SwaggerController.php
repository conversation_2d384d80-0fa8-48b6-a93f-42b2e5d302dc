<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;

class SwaggerController extends Controller
{
    /**
     * Display the Swagger UI page.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('swagger.index');
    }

    /**
     * Get the OpenAPI specification.
     *
     * @return \Illuminate\Http\Response
     */
    public function spec()
    {
        $openApiPath = base_path('openapi.yaml');
        
        if (!File::exists($openApiPath)) {
            return response()->json([
                'message' => 'OpenAPI specification not found',
                'status' => 'error',
                'code' => 404
            ], 404);
        }
        
        $content = File::get($openApiPath);
        
        return Response::make($content, 200, [
            'Content-Type' => 'application/yaml',
            'Content-Disposition' => 'inline; filename="openapi.yaml"'
        ]);
    }
}

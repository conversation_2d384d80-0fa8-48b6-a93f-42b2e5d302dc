<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogDeprecatedEndpoints
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $path = $request->path();
        
        // Check if the request is using a v2 endpoint
        if (strpos($path, 'v2/') === 0 || strpos($path, 'api/v2/') === 0) {
            // Log the deprecated endpoint usage
            Log::warning('Deprecated v2 endpoint accessed', [
                'path' => $path,
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'timestamp' => now()->toIso8601String(),
            ]);
            
            // Add deprecation header to the response
            $response = $next($request);
            $response->headers->set(
                'X-API-Deprecation-Warning',
                'This v2 endpoint is deprecated and will be removed on December 31, 2025. Please use the v1 equivalent.'
            );
            
            return $response;
        }
        
        return $next($request);
    }
}

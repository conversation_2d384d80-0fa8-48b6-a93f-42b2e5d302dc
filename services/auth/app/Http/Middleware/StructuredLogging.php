<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class StructuredLogging
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get or generate correlation ID
        $correlationId = $request->header('X-Correlation-ID');
        if (!$correlationId) {
            $correlationId = Str::uuid()->toString();
            $request->headers->set('X-Correlation-ID', $correlationId);
        }
        
        // Get request start time
        $startTime = microtime(true);
        
        // Log request
        $this->logRequest($request, $correlationId);
        
        // Process the request
        $response = $next($request);
        
        // Calculate request duration
        $duration = microtime(true) - $startTime;
        
        // Add correlation ID to response
        $response->headers->set('X-Correlation-ID', $correlationId);
        
        // Log response
        $this->logResponse($request, $response, $correlationId, $duration);
        
        return $response;
    }
    
    /**
     * Log the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $correlationId
     * @return void
     */
    protected function logRequest(Request $request, string $correlationId)
    {
        // Build log data
        $logData = [
            'correlation_id' => $correlationId,
            'type' => 'request',
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'path' => $request->path(),
            'client_ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'referer' => $request->header('Referer'),
            'timestamp' => now()->toIso8601String(),
            'request_id' => Str::uuid()->toString(),
        ];
        
        // Add query parameters (excluding sensitive data)
        $queryParams = $request->query();
        if (!empty($queryParams)) {
            // Filter out sensitive data
            $filteredParams = $this->filterSensitiveData($queryParams);
            $logData['query_params'] = $filteredParams;
        }
        
        // Add request headers (excluding sensitive data)
        $headers = $request->headers->all();
        if (!empty($headers)) {
            // Filter out sensitive data
            $filteredHeaders = $this->filterSensitiveData($headers);
            $logData['headers'] = $filteredHeaders;
        }
        
        // Add request body for non-GET requests (excluding sensitive data)
        if (!in_array($request->method(), ['GET', 'HEAD']) && $request->getContent()) {
            $body = $request->json()->all();
            if (!empty($body)) {
                // Filter out sensitive data
                $filteredBody = $this->filterSensitiveData($body);
                $logData['body'] = $filteredBody;
            }
        }
        
        // Add user ID if authenticated
        if ($request->user()) {
            $logData['user_id'] = $request->user()->id;
        }
        
        // Log the request
        Log::info('API Request', $logData);
    }
    
    /**
     * Log the response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Symfony\Component\HttpFoundation\Response  $response
     * @param  string  $correlationId
     * @param  float  $duration
     * @return void
     */
    protected function logResponse(Request $request, Response $response, string $correlationId, float $duration)
    {
        // Build log data
        $logData = [
            'correlation_id' => $correlationId,
            'type' => 'response',
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'path' => $request->path(),
            'status_code' => $response->getStatusCode(),
            'duration_ms' => round($duration * 1000, 2),
            'timestamp' => now()->toIso8601String(),
        ];
        
        // Add response headers (excluding sensitive data)
        $headers = $response->headers->all();
        if (!empty($headers)) {
            // Filter out sensitive data
            $filteredHeaders = $this->filterSensitiveData($headers);
            $logData['headers'] = $filteredHeaders;
        }
        
        // Add response body for JSON responses (excluding sensitive data)
        if ($response->headers->get('Content-Type') === 'application/json') {
            $content = json_decode($response->getContent(), true);
            if (json_last_error() === JSON_ERROR_NONE && !empty($content)) {
                // Filter out sensitive data
                $filteredContent = $this->filterSensitiveData($content);
                $logData['body'] = $filteredContent;
            }
        }
        
        // Add user ID if authenticated
        if ($request->user()) {
            $logData['user_id'] = $request->user()->id;
        }
        
        // Determine log level based on status code
        $logLevel = 'info';
        if ($response->getStatusCode() >= 500) {
            $logLevel = 'error';
        } elseif ($response->getStatusCode() >= 400) {
            $logLevel = 'warning';
        }
        
        // Log the response
        Log::log($logLevel, 'API Response', $logData);
    }
    
    /**
     * Filter sensitive data from the given array.
     *
     * @param  array  $data
     * @return array
     */
    protected function filterSensitiveData(array $data)
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'current_password',
            'secret',
            'token',
            'refresh_token',
            'api_key',
            'credit_card',
            'card_number',
            'cvv',
            'ssn',
            'social_security',
            'authorization',
        ];
        
        $filtered = [];
        
        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);
            
            // Check if the key contains any sensitive field name
            $isSensitive = false;
            foreach ($sensitiveFields as $sensitiveField) {
                if (strpos($lowerKey, $sensitiveField) !== false) {
                    $isSensitive = true;
                    break;
                }
            }
            
            if ($isSensitive) {
                $filtered[$key] = '[REDACTED]';
            } elseif (is_array($value)) {
                $filtered[$key] = $this->filterSensitiveData($value);
            } else {
                $filtered[$key] = $value;
            }
        }
        
        return $filtered;
    }
}

<?php

namespace App\Exceptions;

class ValidationException extends ApiException
{
    /**
     * The validation errors.
     *
     * @var array
     */
    protected $errors = [];

    /**
     * Create a new validation exception instance.
     *
     * @param array $errors
     * @param string $message
     * @param \Throwable|null $previous
     * @return void
     */
    public function __construct(
        array $errors = [],
        string $message = 'The given data was invalid',
        \Throwable $previous = null
    ) {
        $this->errors = $errors;
        
        parent::__construct(
            $message,
            422,
            'validation_error',
            ['errors' => $errors],
            $previous
        );
    }

    /**
     * Get the validation errors.
     *
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }
}

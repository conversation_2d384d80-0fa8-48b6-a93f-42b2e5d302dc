<?php

namespace App\Exceptions;

class ResourceNotFoundException extends ApiException
{
    /**
     * Create a new resource not found exception instance.
     *
     * @param string $resourceType
     * @param mixed $resourceId
     * @param array $details
     * @param \Throwable|null $previous
     * @return void
     */
    public function __construct(
        string $resourceType = 'resource',
        $resourceId = null,
        array $details = [],
        \Throwable $previous = null
    ) {
        $message = "The requested {$resourceType}" . ($resourceId ? " with ID {$resourceId}" : "") . " was not found";
        
        parent::__construct(
            $message,
            404,
            'resource_not_found',
            array_merge([
                'resource_type' => $resourceType,
                'resource_id' => $resourceId,
            ], $details),
            $previous
        );
    }
}

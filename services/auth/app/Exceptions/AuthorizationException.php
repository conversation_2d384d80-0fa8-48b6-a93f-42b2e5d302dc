<?php

namespace App\Exceptions;

class AuthorizationException extends ApiException
{
    /**
     * Create a new authorization exception instance.
     *
     * @param string $message
     * @param array $details
     * @param \Throwable|null $previous
     * @return void
     */
    public function __construct(
        string $message = 'Unauthorized',
        array $details = [],
        \Throwable $previous = null
    ) {
        parent::__construct(
            $message,
            403,
            'authorization_error',
            $details,
            $previous
        );
    }
}

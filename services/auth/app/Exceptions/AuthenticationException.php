<?php

namespace App\Exceptions;

class AuthenticationException extends ApiException
{
    /**
     * Create a new authentication exception instance.
     *
     * @param string $message
     * @param array $details
     * @param \Throwable|null $previous
     * @return void
     */
    public function __construct(
        string $message = 'Unauthenticated',
        array $details = [],
        \Throwable $previous = null
    ) {
        parent::__construct(
            $message,
            401,
            'authentication_error',
            $details,
            $previous
        );
    }
}

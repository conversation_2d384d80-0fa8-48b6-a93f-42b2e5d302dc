<?php

namespace App\Exceptions;

class RateLimitException extends ApiException
{
    /**
     * Create a new rate limit exception instance.
     *
     * @param int $retryAfter
     * @param string $message
     * @param array $details
     * @param \Throwable|null $previous
     * @return void
     */
    public function __construct(
        int $retryAfter = 60,
        string $message = 'Too many requests',
        array $details = [],
        \Throwable $previous = null
    ) {
        parent::__construct(
            $message,
            429,
            'rate_limit_error',
            array_merge([
                'retry_after' => $retryAfter,
                'retry_after_seconds' => $retryAfter,
            ], $details),
            $previous
        );
    }

    /**
     * Render the exception as a JSON response.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function render($request)
    {
        $response = parent::render($request);
        
        // Add Retry-After header
        $retryAfter = $this->getDetails()['retry_after'] ?? 60;
        $response->headers->set('Retry-After', $retryAfter);
        
        return $response;
    }
}

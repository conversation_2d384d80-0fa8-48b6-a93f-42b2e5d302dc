<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class ApiException extends Exception
{
    /**
     * The HTTP status code for the exception.
     *
     * @var int
     */
    protected $statusCode = 500;

    /**
     * The error type for the exception.
     *
     * @var string
     */
    protected $errorType = 'api_error';

    /**
     * Additional error details.
     *
     * @var array
     */
    protected $details = [];

    /**
     * Create a new API exception instance.
     *
     * @param string $message
     * @param int $statusCode
     * @param string $errorType
     * @param array $details
     * @param \Throwable|null $previous
     * @return void
     */
    public function __construct(
        string $message = 'An API error occurred',
        int $statusCode = 500,
        string $errorType = 'api_error',
        array $details = [],
        \Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        
        $this->statusCode = $statusCode;
        $this->errorType = $errorType;
        $this->details = $details;
    }

    /**
     * Get the HTTP status code for the exception.
     *
     * @return int
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the error type for the exception.
     *
     * @return string
     */
    public function getErrorType(): string
    {
        return $this->errorType;
    }

    /**
     * Get the additional error details.
     *
     * @return array
     */
    public function getDetails(): array
    {
        return $this->details;
    }

    /**
     * Render the exception as a JSON response.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function render($request): JsonResponse
    {
        // Generate a unique error ID for tracking
        $errorId = Str::uuid()->toString();
        
        // Get correlation ID from request header or generate a new one
        $correlationId = $request->header('X-Correlation-ID') ?? Str::uuid()->toString();
        
        $response = [
            'message' => $this->getMessage(),
            'status' => 'error',
            'code' => $this->getStatusCode(),
            'error_type' => $this->getErrorType(),
            'error_id' => $errorId,
            'correlation_id' => $correlationId,
            'timestamp' => now()->toIso8601String(),
            'service' => config('app.name', 'auth-service'),
            'version' => config('app.version', '1.0.0'),
        ];
        
        // Add details if available
        if (!empty($this->getDetails())) {
            $response['details'] = $this->getDetails();
        }
        
        // Add request information in debug mode
        if (config('app.debug')) {
            $response['request'] = [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];
        }
        
        // Set response headers
        $headers = [
            'X-Error-ID' => $errorId,
            'X-Correlation-ID' => $correlationId,
        ];
        
        return new JsonResponse($response, $this->getStatusCode(), $headers);
    }
}

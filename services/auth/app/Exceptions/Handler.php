<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            // Log the exception with a correlation ID for tracking
            $correlationId = request()->header('X-Correlation-ID') ?? Str::uuid()->toString();
            Log::error('Exception occurred', [
                'correlation_id' => $correlationId,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_method' => request()->method(),
                'request_url' => request()->fullUrl(),
                'request_ip' => request()->ip(),
                'request_user_agent' => request()->userAgent(),
            ]);
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $e
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $e)
    {
        if ($request->expectsJson() || $request->is('api/*')) {
            return $this->handleApiException($request, $e);
        }

        return parent::render($request, $e);
    }

    /**
     * Handle API exceptions and return standardized JSON responses.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $e
     * @return \Illuminate\Http\JsonResponse
     */
    private function handleApiException(Request $request, Throwable $e): JsonResponse
    {
        // Generate a unique error ID for tracking
        $errorId = Str::uuid()->toString();
        
        // Get correlation ID from request header or generate a new one
        $correlationId = $request->header('X-Correlation-ID') ?? Str::uuid()->toString();
        
        // Default error response structure
        $response = [
            'message' => 'An unexpected error occurred',
            'status' => 'error',
            'code' => 500,
            'error_id' => $errorId,
            'correlation_id' => $correlationId,
            'timestamp' => now()->toIso8601String(),
        ];
        
        // Handle specific exception types
        if ($e instanceof ValidationException) {
            $response['message'] = 'The given data was invalid';
            $response['code'] = 422;
            $response['errors'] = $e->errors();
            $statusCode = 422;
        } elseif ($e instanceof AuthenticationException) {
            $response['message'] = 'Unauthenticated';
            $response['code'] = 401;
            $response['error_type'] = 'authentication_error';
            $statusCode = 401;
        } elseif ($e instanceof ModelNotFoundException) {
            $model = strtolower(class_basename($e->getModel()));
            $response['message'] = "The requested {$model} was not found";
            $response['code'] = 404;
            $response['error_type'] = 'resource_not_found';
            $statusCode = 404;
        } elseif ($e instanceof NotFoundHttpException) {
            $response['message'] = 'The requested resource was not found';
            $response['code'] = 404;
            $response['error_type'] = 'endpoint_not_found';
            $statusCode = 404;
        } elseif ($e instanceof HttpException) {
            $response['message'] = $e->getMessage() ?: 'HTTP error occurred';
            $response['code'] = $e->getStatusCode();
            $response['error_type'] = 'http_error';
            $statusCode = $e->getStatusCode();
        } else {
            // For all other exceptions, use a generic error message in production
            if (config('app.debug')) {
                $response['message'] = $e->getMessage();
                $response['exception'] = get_class($e);
                $response['file'] = $e->getFile();
                $response['line'] = $e->getLine();
                $response['trace'] = explode("\n", $e->getTraceAsString());
            }
            $response['error_type'] = 'server_error';
            $statusCode = 500;
        }
        
        // Add service information
        $response['service'] = config('app.name', 'auth-service');
        $response['version'] = config('app.version', '1.0.0');
        
        // Add request information in debug mode
        if (config('app.debug')) {
            $response['request'] = [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];
        }
        
        // Set response headers
        $headers = [
            'X-Error-ID' => $errorId,
            'X-Correlation-ID' => $correlationId,
        ];
        
        return new JsonResponse($response, $statusCode, $headers);
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.1.0/swagger-ui.css">
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        
        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        
        .swagger-ui .topbar {
            background-color: #2c3e50;
        }
        
        .swagger-ui .topbar .download-url-wrapper .select-label select {
            border-color: #3498db;
        }
        
        .swagger-ui .info .title {
            color: #2c3e50;
        }
        
        .swagger-ui .opblock.opblock-get .opblock-summary-method {
            background-color: #3498db;
        }
        
        .swagger-ui .opblock.opblock-post .opblock-summary-method {
            background-color: #2ecc71;
        }
        
        .swagger-ui .opblock.opblock-put .opblock-summary-method {
            background-color: #f39c12;
        }
        
        .swagger-ui .opblock.opblock-delete .opblock-summary-method {
            background-color: #e74c3c;
        }
        
        .swagger-ui .btn.execute {
            background-color: #2ecc71;
            color: #fff;
            border-color: #27ae60;
        }
        
        .swagger-ui .btn.execute:hover {
            background-color: #27ae60;
        }
        
        .swagger-ui section.models .model-container {
            background-color: #f8f9fa;
        }
        
        .topbar-wrapper img[alt="Swagger UI"] {
            content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItc2hpZWxkIj48cGF0aCBkPSJNMTIgMjJzOC0zIDgtMTJWNWwtOC0zLTggM3Y1YzAgOS44IDggMTIgOCAxMnoiPjwvcGF0aD48L3N2Zz4=');
            height: 40px;
            width: 40px;
            margin-right: 10px;
        }
        
        .swagger-ui .topbar .download-url-wrapper .select-label span {
            color: white;
        }
        
        .topbar-wrapper .link:after {
            content: 'Auth Service API';
            color: white;
            font-size: 1.5em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .swagger-ui .info .title small.version-stamp {
            background-color: #3498db;
        }
        
        .swagger-ui .info .title small.version-stamp pre {
            color: white;
        }
        
        /* Deprecation notice */
        .deprecation-notice {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .deprecation-notice h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .deprecation-notice p {
            color: #856404;
            margin-bottom: 0;
        }
        
        .deprecation-notice code {
            background-color: #fff;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    
    <script src="https://unpkg.com/swagger-ui-dist@5.1.0/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.1.0/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: "{{ route('swagger.spec') }}",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                docExpansion: "list",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                displayRequestDuration: true,
                filter: true,
                syntaxHighlight: {
                    activate: true,
                    theme: "agate"
                },
                tryItOutEnabled: true,
                onComplete: function() {
                    // Add deprecation notice
                    const infoContainer = document.querySelector('.information-container');
                    if (infoContainer) {
                        const deprecationNotice = document.createElement('div');
                        deprecationNotice.className = 'deprecation-notice';
                        deprecationNotice.innerHTML = `
                            <h3>API Version Notice</h3>
                            <p>All API endpoints now use the <code>v1</code> version prefix. 
                            Legacy <code>v2</code> endpoints are deprecated and will be removed on 
                            December 31, 2025. Please update your applications to use the v1 endpoints.</p>
                        `;
                        infoContainer.appendChild(deprecationNotice);
                    }
                }
            });
            
            window.ui = ui;
        };
    </script>
</body>
</html>

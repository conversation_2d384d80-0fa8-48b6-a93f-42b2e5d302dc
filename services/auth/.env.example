APP_NAME="Auth Service"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8001
APP_PORT=8001

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=auth_service
DB_USERNAME=root
DB_PASSWORD=

# Redis configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Queue configuration
QUEUE_CONNECTION=redis

# Session configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Cache configuration
CACHE_DRIVER=redis

# JWT configuration
JWT_SECRET=
JWT_TTL=60
JWT_REFRESH_TTL=20160
JWT_ALGO=RS256

# CORS configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000

# API rate limiting
API_RATE_LIMIT_MAX_REQUESTS=60
API_RATE_LIMIT_PERIOD=60

# Sanctum configuration
SANCTUM_STATEFUL_DOMAINS=localhost:3000
SANCTUM_TOKEN_EXPIRATION=60
SANCTUM_REFRESH_TOKEN_EXPIRATION=1440

# MFA configuration
MFA_ENABLED=true
MFA_ISSUER="CubeOneBiz Auth"

# RabbitMQ configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

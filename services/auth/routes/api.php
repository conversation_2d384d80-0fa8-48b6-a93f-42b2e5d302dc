<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\HealthController;
use App\Http\Controllers\SwaggerController;
use App\Http\Controllers\MetricsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check endpoint
Route::get('/v1/auth/health', [HealthController::class, 'check']);

// Auth routes (v1 - current)
Route::prefix('v1/auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:api');
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::post('/refresh-token', [AuthController::class, 'refresh']); // Alias for refresh
    Route::get('/user', [AuthController::class, 'user'])->middleware('auth:api');
    Route::put('/profile', [AuthController::class, 'updateProfile'])->middleware('auth:api');
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/reset-password', [AuthController::class, 'resetPassword']);
    Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
    Route::post('/email/verification-notification', [AuthController::class, 'resendVerificationEmail'])->middleware('auth:api');
});

// Auth routes (v2 - deprecated)
Route::prefix('v2/auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:api');
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::post('/refresh-token', [AuthController::class, 'refresh']);
    Route::get('/user', [AuthController::class, 'user'])->middleware('auth:api');
    Route::put('/profile', [AuthController::class, 'updateProfile'])->middleware('auth:api');
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/reset-password', [AuthController::class, 'resetPassword']);
    Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
    Route::post('/email/verification-notification', [AuthController::class, 'resendVerificationEmail'])->middleware('auth:api');
});

// Health check endpoint for v2 (deprecated)
Route::get('/v2/auth/health', [HealthController::class, 'check']);

// API Documentation
Route::get('/v1/auth/docs', [SwaggerController::class, 'index'])->name('swagger.index');
Route::get('/v1/auth/docs/spec', [SwaggerController::class, 'spec'])->name('swagger.spec');

// Metrics endpoint (protected in production)
Route::get('/v1/auth/metrics', [MetricsController::class, 'metrics'])
    ->middleware(config('app.env') === 'production' ? 'auth:api' : [])
    ->name('metrics');

// Fallback route for undefined endpoints
Route::fallback(function () {
    return response()->json([
        'message' => 'Endpoint not found. Please check the URL and try again.',
        'status' => 'error',
        'code' => 404
    ], 404);
});

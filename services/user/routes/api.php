<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\AddressController;
use App\Http\Controllers\HealthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check endpoint
Route::get('/v1/users/health', [HealthController::class, 'check']);

// User routes
Route::prefix('v1/users')->middleware('auth:api')->group(function () {
    Route::get('/', [UserController::class, 'index']);
    Route::get('/{id}', [UserController::class, 'show']);
    Route::put('/{id}', [UserController::class, 'update']);
    Route::delete('/{id}', [UserController::class, 'destroy']);
    
    // Profile routes
    Route::get('/{userId}/profile', [ProfileController::class, 'show']);
    Route::put('/{userId}/profile', [ProfileController::class, 'update']);
    
    // Address routes
    Route::get('/{userId}/addresses', [AddressController::class, 'index']);
    Route::post('/{userId}/addresses', [AddressController::class, 'store']);
    Route::get('/{userId}/addresses/{addressId}', [AddressController::class, 'show']);
    Route::put('/{userId}/addresses/{addressId}', [AddressController::class, 'update']);
    Route::delete('/{userId}/addresses/{addressId}', [AddressController::class, 'destroy']);
    Route::put('/{userId}/addresses/{addressId}/default', [AddressController::class, 'setDefault']);
});

// Fallback route for undefined endpoints
Route::fallback(function () {
    return response()->json([
        'message' => 'Endpoint not found. Please check the URL and try again.',
        'status' => 'error',
        'code' => 404
    ], 404);
});

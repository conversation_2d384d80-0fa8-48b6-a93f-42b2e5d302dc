<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HealthController extends Controller
{
    /**
     * Check the health of the service
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function check()
    {
        $startTime = microtime(true);
        
        $status = 'healthy';
        $checks = [];
        
        // Check database connection
        try {
            $dbStartTime = microtime(true);
            DB::connection()->getPdo();
            $dbEndTime = microtime(true);
            $dbResponseTime = round(($dbEndTime - $dbStartTime) * 1000, 2);
            
            $checks['database'] = [
                'status' => 'healthy',
                'message' => 'Database connection successful',
                'response_time_ms' => $dbResponseTime
            ];
            
            // If response time is too high, mark as degraded
            if ($dbResponseTime > 100) {
                $checks['database']['status'] = 'degraded';
                $checks['database']['message'] = 'Database connection is slow';
                $status = 'degraded';
            }
        } catch (\Exception $e) {
            $checks['database'] = [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
            $status = 'unhealthy';
            Log::error('Health check database error: ' . $e->getMessage());
        }
        
        // Check cache connection
        try {
            $cacheStartTime = microtime(true);
            $cacheKey = 'health_check_' . time();
            Cache::put($cacheKey, true, 10);
            $cacheValue = Cache::get($cacheKey);
            $cacheEndTime = microtime(true);
            $cacheResponseTime = round(($cacheEndTime - $cacheStartTime) * 1000, 2);
            
            $checks['cache'] = [
                'status' => 'healthy',
                'message' => 'Cache connection successful',
                'response_time_ms' => $cacheResponseTime
            ];
            
            // If response time is too high, mark as degraded
            if ($cacheResponseTime > 50) {
                $checks['cache']['status'] = 'degraded';
                $checks['cache']['message'] = 'Cache connection is slow';
                if ($status !== 'unhealthy') {
                    $status = 'degraded';
                }
            }
        } catch (\Exception $e) {
            $checks['cache'] = [
                'status' => 'unhealthy',
                'message' => 'Cache connection failed: ' . $e->getMessage()
            ];
            $status = 'unhealthy';
            Log::error('Health check cache error: ' . $e->getMessage());
        }
        
        // Check auth service connection
        try {
            $authServiceUrl = config('services.auth.url') . '/v1/auth/health';
            $authStartTime = microtime(true);
            $response = Http::timeout(5)->get($authServiceUrl);
            $authEndTime = microtime(true);
            $authResponseTime = round(($authEndTime - $authStartTime) * 1000, 2);
            
            if ($response->successful()) {
                $authStatus = $response->json('status', 'unknown');
                
                $checks['auth_service'] = [
                    'status' => $authStatus,
                    'message' => 'Auth service connection successful',
                    'response_time_ms' => $authResponseTime
                ];
                
                // If auth service is degraded or unhealthy, reflect that in our status
                if ($authStatus === 'unhealthy') {
                    $status = 'unhealthy';
                } elseif ($authStatus === 'degraded' && $status !== 'unhealthy') {
                    $status = 'degraded';
                }
                
                // If response time is too high, mark as degraded
                if ($authResponseTime > 300) {
                    $checks['auth_service']['status'] = 'degraded';
                    $checks['auth_service']['message'] = 'Auth service connection is slow';
                    if ($status !== 'unhealthy') {
                        $status = 'degraded';
                    }
                }
            } else {
                $checks['auth_service'] = [
                    'status' => 'unhealthy',
                    'message' => 'Auth service returned error: ' . $response->status(),
                    'response_time_ms' => $authResponseTime
                ];
                $status = 'unhealthy';
            }
        } catch (\Exception $e) {
            $checks['auth_service'] = [
                'status' => 'unhealthy',
                'message' => 'Auth service connection failed: ' . $e->getMessage()
            ];
            $status = 'unhealthy';
            Log::error('Health check auth service error: ' . $e->getMessage());
        }
        
        // Check disk space
        try {
            $diskFree = disk_free_space(storage_path());
            $diskTotal = disk_total_space(storage_path());
            $diskUsedPercentage = round(100 - ($diskFree / $diskTotal * 100), 2);
            
            $checks['disk'] = [
                'status' => 'healthy',
                'message' => 'Disk space is sufficient',
                'used_percentage' => $diskUsedPercentage,
                'free_space_mb' => round($diskFree / 1024 / 1024, 2)
            ];
            
            // If disk usage is too high, mark as degraded or unhealthy
            if ($diskUsedPercentage > 90) {
                $checks['disk']['status'] = 'unhealthy';
                $checks['disk']['message'] = 'Disk space is critically low';
                $status = 'unhealthy';
            } elseif ($diskUsedPercentage > 80) {
                $checks['disk']['status'] = 'degraded';
                $checks['disk']['message'] = 'Disk space is running low';
                if ($status !== 'unhealthy') {
                    $status = 'degraded';
                }
            }
        } catch (\Exception $e) {
            $checks['disk'] = [
                'status' => 'unknown',
                'message' => 'Could not check disk space: ' . $e->getMessage()
            ];
            Log::warning('Health check disk error: ' . $e->getMessage());
        }
        
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2);
        
        return response()->json([
            'service' => 'user-service',
            'status' => $status,
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
            'timestamp' => now()->toIso8601String(),
            'response_time_ms' => $responseTime,
            'checks' => $checks
        ]);
    }
}

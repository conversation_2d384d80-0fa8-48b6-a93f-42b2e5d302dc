<?php

namespace App\Providers;

use App\Services\Payment\PaymentServiceClient;
use Illuminate\Support\ServiceProvider;

/**
 * Payment Service Provider
 * 
 * This service provider registers the Payment Service integration.
 */
class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Payment Service Client
        $this->app->singleton(PaymentServiceClient::class, function ($app) {
            return new PaymentServiceClient(
                config('services.payment.url'),
                config('services.payment.timeout', 30),
                config('services.payment.retries', 3)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

<?php

namespace App\Exceptions\Payment;

use Exception;

/**
 * Payment Exception
 * 
 * This exception is thrown when there is an error with the Payment Service.
 */
class PaymentException extends Exception
{
    /**
     * Create a new PaymentException instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "Payment service error", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

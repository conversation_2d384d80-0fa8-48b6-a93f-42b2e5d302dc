<?php

namespace App\Services\Payment;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\DTOs\Payment\PaymentRequestDTO;
use App\DTOs\Payment\PaymentResponseDTO;
use App\Exceptions\Payment\PaymentException;

/**
 * Payment Service Client
 * 
 * This class provides a client for interacting with the Payment Service API.
 */
class PaymentServiceClient
{
    /**
     * The base URL for the Payment Service API.
     *
     * @var string
     */
    protected string $baseUrl;

    /**
     * The timeout for API requests in seconds.
     *
     * @var int
     */
    protected int $timeout;

    /**
     * The retry attempts for API requests.
     *
     * @var int
     */
    protected int $retries;

    /**
     * Create a new PaymentServiceClient instance.
     *
     * @param string|null $baseUrl
     * @param int $timeout
     * @param int $retries
     */
    public function __construct(
        ?string $baseUrl = null,
        int $timeout = 30,
        int $retries = 3
    ) {
        $this->baseUrl = $baseUrl ?? config('services.payment.url', 'http://payment-service-v12:8000/api');
        $this->timeout = $timeout;
        $this->retries = $retries;
    }

    /**
     * Initiate a payment.
     *
     * @param PaymentRequestDTO $paymentRequest
     * @return PaymentResponseDTO
     * @throws PaymentException
     */
    public function initiatePayment(PaymentRequestDTO $paymentRequest): PaymentResponseDTO
    {
        try {
            $response = $this->http()
                ->post('/payments/initiate', $paymentRequest->toArray());

            if ($response->successful()) {
                $data = $response->json('data');
                return new PaymentResponseDTO(
                    $data['transaction_id'],
                    $data['amount'],
                    $data['status']
                );
            }

            throw new PaymentException($response->json('message') ?? 'Payment initiation failed');
        } catch (\Exception $e) {
            Log::error('Payment service initiation failed', [
                'error' => $e->getMessage(),
                'payment_request' => $paymentRequest->toArray()
            ]);

            throw new PaymentException('Payment initiation failed: ' . $e->getMessage());
        }
    }

    /**
     * Process a payment.
     *
     * @param string $transactionId
     * @param string $gateway
     * @return array
     * @throws PaymentException
     */
    public function processPayment(string $transactionId, string $gateway): array
    {
        try {
            $response = $this->http()
                ->post("/payments/{$transactionId}/process", [
                    'gateway' => $gateway
                ]);

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new PaymentException($response->json('message') ?? 'Payment processing failed');
        } catch (\Exception $e) {
            Log::error('Payment service processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'gateway' => $gateway
            ]);

            throw new PaymentException('Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     * @throws PaymentException
     */
    public function getPaymentStatus(string $transactionId): array
    {
        try {
            $response = $this->http()
                ->get("/payments/{$transactionId}");

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new PaymentException($response->json('message') ?? 'Failed to get payment status');
        } catch (\Exception $e) {
            Log::error('Payment service status check failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);

            throw new PaymentException('Failed to get payment status: ' . $e->getMessage());
        }
    }

    /**
     * Create an HTTP client instance.
     *
     * @return PendingRequest
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name'),
                'X-Service-Version' => config('app.version', '1.0.0'),
            ]);
    }
}

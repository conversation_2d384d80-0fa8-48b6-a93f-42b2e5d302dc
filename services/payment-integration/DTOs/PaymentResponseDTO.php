<?php

namespace App\DTOs\Payment;

/**
 * Payment Response DTO
 * 
 * This class represents a payment response from the Payment Service.
 */
class PaymentResponseDTO
{
    /**
     * Create a new PaymentResponseDTO instance.
     *
     * @param string $transactionId
     * @param float $amount
     * @param string $status
     * @param array $metadata
     */
    public function __construct(
        public readonly string $transactionId,
        public readonly float $amount,
        public readonly string $status,
        public readonly array $metadata = []
    ) {
    }

    /**
     * Convert the DTO to an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'transaction_id' => $this->transactionId,
            'amount' => $this->amount,
            'status' => $this->status,
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Check if the payment is completed.
     *
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the payment is pending.
     *
     * @return bool
     */
    public function isPending(): bool
    {
        return $this->status === 'pending' || $this->status === 'initiated';
    }

    /**
     * Check if the payment failed.
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }
}

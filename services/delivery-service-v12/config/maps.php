<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Maps Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for map services used in the application.
    |
    */

    // Nominatim API configuration
    'nominatim_url' => env('NOMINATIM_URL', 'https://nominatim.openstreetmap.org'),
    'user_agent' => env('NOMINATIM_USER_AGENT', 'FoodDialer Delivery Service'),
    'cache_ttl' => env('NOMINATIM_CACHE_TTL', 86400), // 24 hours

    // OSRM API configuration
    'osrm_url' => env('OSRM_URL', 'https://router.project-osrm.org'),
    'osrm_profile' => env('OSRM_PROFILE', 'driving'),
    'osrm_cache_ttl' => env('OSRM_CACHE_TTL', 86400), // 24 hours

    // Leaflet configuration
    'leaflet_tile_url' => env('LEAFLET_TILE_URL', 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'),
    'leaflet_attribution' => env('LEAFLET_ATTRIBUTION', '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'),
    'leaflet_max_zoom' => env('LEAFLET_MAX_ZOOM', 19),

    // Default map center (Mumbai)
    'default_lat' => env('DEFAULT_MAP_LAT', 19.0760),
    'default_lon' => env('DEFAULT_MAP_LON', 72.8777),
    'default_zoom' => env('DEFAULT_MAP_ZOOM', 12),

    // Delivery zone settings
    'zone_radius_km' => env('ZONE_RADIUS_KM', 5),
    'zone_price_increment' => env('ZONE_PRICE_INCREMENT', 10), // Price increase per zone
];

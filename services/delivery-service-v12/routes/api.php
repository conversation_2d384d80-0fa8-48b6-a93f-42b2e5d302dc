<?php

use App\Http\Controllers\Api\DabbawalaController;
use App\Http\Controllers\Api\DeliveryAssignmentController;
use App\Http\Controllers\Api\DeliveryController;
use App\Http\Controllers\Api\DeliveryOptimizationController;
use App\Http\Controllers\Api\DeliveryStaffController;
use App\Http\Controllers\Api\DeliveryTrackingController;
use App\Http\Controllers\Api\DeliveryZoneController;
use App\Http\Controllers\Api\MapController;
use App\Http\Controllers\Api\SchoolDeliveryController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// API v2 routes
Route::prefix('v2/delivery')->group(function () {
    // Delivery Routes
    Route::get('/locations', [DeliveryController::class, 'getLocations']);
    Route::get('/persons', [DeliveryController::class, 'getDeliveryPersons']);
    Route::get('/orders', [DeliveryController::class, 'getOrders']);
    Route::put('/orders/{id}/status', [DeliveryController::class, 'updateOrderStatus']);

    // Third-party delivery routes
    Route::prefix('third-party')->group(function () {
        Route::post('/book', [DeliveryController::class, 'bookThirdPartyDelivery']);
        Route::post('/{orderId}/cancel', [DeliveryController::class, 'cancelThirdPartyDelivery']);
        Route::get('/{orderId}/status', [DeliveryController::class, 'getThirdPartyDeliveryStatus']);
    });

    // Dabbawala routes
    Route::prefix('dabbawala')->group(function () {
        Route::post('/generate-code', [DabbawalaController::class, 'generateCode']);
    });

    // Map routes
    Route::prefix('map')->group(function () {
        Route::get('/delivery-locations', [MapController::class, 'getDeliveryLocations']);
        Route::get('/customers', [MapController::class, 'getCustomers']);
        Route::get('/active-orders', [MapController::class, 'getActiveOrders']);
        Route::get('/delivery-route/{orderId}', [MapController::class, 'getDeliveryRoute']);
        Route::post('/geocode', [MapController::class, 'geocodeAddress']);
        Route::put('/customer/{customerId}/coordinates', [MapController::class, 'updateCustomerCoordinates']);
        Route::put('/location/{locationId}/coordinates', [MapController::class, 'updateLocationCoordinates']);
    });

    // Delivery zone routes
    Route::prefix('zones')->group(function () {
        Route::get('/', [DeliveryZoneController::class, 'index']);
        Route::post('/', [DeliveryZoneController::class, 'store']);
        Route::get('/{id}', [DeliveryZoneController::class, 'show']);
        Route::put('/{id}', [DeliveryZoneController::class, 'update']);
        Route::delete('/{id}', [DeliveryZoneController::class, 'destroy']);
        Route::get('/kitchen/{kitchenId}', [DeliveryZoneController::class, 'getZonesForKitchen']);
        Route::post('/generate-default', [DeliveryZoneController::class, 'generateDefaultZones']);
        Route::post('/check', [DeliveryZoneController::class, 'checkDeliveryZone']);
    });

    // Delivery optimization routes
    Route::prefix('optimization')->group(function () {
        Route::post('/calculate-route/{orderId}', [DeliveryOptimizationController::class, 'calculateOrderRoute']);
        Route::post('/assign-delivery-persons', [DeliveryOptimizationController::class, 'assignDeliveryPersons']);
        Route::post('/calculate-all-routes', [DeliveryOptimizationController::class, 'calculateAllRoutes']);
    });

    // Delivery staff routes
    Route::prefix('staff')->group(function () {
        Route::get('/', [DeliveryStaffController::class, 'index']);
        Route::post('/', [DeliveryStaffController::class, 'store']);
        Route::get('/{id}', [DeliveryStaffController::class, 'show']);
        Route::put('/{id}', [DeliveryStaffController::class, 'update']);
        Route::delete('/{id}', [DeliveryStaffController::class, 'destroy']);
        Route::put('/{id}/location', [DeliveryStaffController::class, 'updateLocation']);
        Route::put('/{id}/duty-status', [DeliveryStaffController::class, 'updateDutyStatus']);
        Route::get('/{id}/performance', [DeliveryStaffController::class, 'getPerformanceMetrics']);
    });

    // Delivery assignment routes
    Route::prefix('assignments')->group(function () {
        Route::get('/', [DeliveryAssignmentController::class, 'index']);
        Route::get('/{id}', [DeliveryAssignmentController::class, 'show']);
        Route::post('/assign', [DeliveryAssignmentController::class, 'assign']);
        Route::put('/{id}/status', [DeliveryAssignmentController::class, 'updateStatus']);
        Route::post('/batch', [DeliveryAssignmentController::class, 'batchAssign']);
        Route::get('/batches', [DeliveryAssignmentController::class, 'getBatches']);
        Route::get('/batches/{id}', [DeliveryAssignmentController::class, 'getBatch']);
        Route::post('/batches/{id}/process', [DeliveryAssignmentController::class, 'processBatch']);
        Route::post('/batches/{id}/cancel', [DeliveryAssignmentController::class, 'cancelBatch']);
        Route::get('/staff/{deliveryPersonId}', [DeliveryAssignmentController::class, 'getAssignmentsForDeliveryPerson']);
        Route::get('/orders/{orderId}', [DeliveryAssignmentController::class, 'getAssignmentsForOrder']);
    });

    // Delivery tracking routes
    Route::prefix('tracking')->group(function () {
        Route::get('/active-deliveries', [DeliveryTrackingController::class, 'getActiveDeliveries']);
        Route::get('/orders/{orderId}', [DeliveryTrackingController::class, 'getDeliveryTracking']);
        Route::put('/orders/{orderId}/status', [DeliveryTrackingController::class, 'updateDeliveryStatus']);
        Route::put('/staff/{deliveryPersonId}/location', [DeliveryTrackingController::class, 'updateLocation']);
        Route::post('/orders/{orderId}/proof', [DeliveryTrackingController::class, 'uploadDeliveryProof']);
        Route::get('/orders/{orderId}/proofs', [DeliveryTrackingController::class, 'getDeliveryProofs']);
        Route::get('/dashboard', [DeliveryTrackingController::class, 'getDashboardData']);
    });

    // School delivery routes
    Route::prefix('school')->group(function () {
        // Delivery batch management
        Route::get('/batches', [SchoolDeliveryController::class, 'index']);
        Route::post('/batches', [SchoolDeliveryController::class, 'store']);
        Route::get('/batches/{id}', [SchoolDeliveryController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/batches/{id}/status', [SchoolDeliveryController::class, 'updateStatus'])->where('id', '[0-9]+');
        Route::put('/batches/{id}/assign', [SchoolDeliveryController::class, 'assignDeliveryPerson'])->where('id', '[0-9]+');

        // School-specific operations
        Route::get('/schools/{schoolId}/batches', [SchoolDeliveryController::class, 'getSchoolBatches'])->where('schoolId', '[0-9]+');
        Route::get('/schools/{schoolId}/schedule', [SchoolDeliveryController::class, 'getSchoolSchedule'])->where('schoolId', '[0-9]+');
        Route::post('/schools/{schoolId}/optimize-routes', [SchoolDeliveryController::class, 'optimizeRoutes'])->where('schoolId', '[0-9]+');

        // Batch operations
        Route::post('/generate-batches', [SchoolDeliveryController::class, 'generateBatches']);
        Route::get('/performance-metrics', [SchoolDeliveryController::class, 'getPerformanceMetrics']);
    });
});

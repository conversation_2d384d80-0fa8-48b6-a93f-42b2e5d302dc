<template>
  <div class="delivery-map-container">
    <div class="map-filters">
      <div class="form-group">
        <label for="date">Date</label>
        <input type="date" id="date" v-model="filters.date" @change="loadData" class="form-control" />
      </div>
      <div class="form-group">
        <label for="meal_type">Meal Type</label>
        <select id="meal_type" v-model="filters.meal_type" @change="loadData" class="form-control">
          <option value="">All</option>
          <option value="lunch">Lunch</option>
          <option value="dinner">Dinner</option>
        </select>
      </div>
      <div class="form-group">
        <label for="delivery_person">Delivery Person</label>
        <select id="delivery_person" v-model="filters.delivery_person_id" @change="loadData" class="form-control">
          <option value="">All</option>
          <option v-for="person in deliveryPersons" :key="person.id" :value="person.id">
            {{ person.name }}
          </option>
        </select>
      </div>
      <div class="form-group">
        <label for="status">Status</label>
        <select id="status" v-model="filters.status" @change="loadData" class="form-control">
          <option value="">All</option>
          <option value="Pending">Pending</option>
          <option value="Dispatched">Dispatched</option>
          <option value="In Transit">In Transit</option>
        </select>
      </div>
      <button @click="loadData" class="btn btn-primary">Refresh</button>
    </div>

    <div class="map-container">
      <div id="map" ref="mapContainer"></div>
    </div>

    <div v-if="selectedOrder" class="order-details">
      <h3>Order Details</h3>
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Order #{{ selectedOrder.order_no }}</h5>
          <p class="card-text">
            <strong>Customer:</strong> {{ selectedOrder.customer.name }}<br>
            <strong>Address:</strong> {{ selectedOrder.customer.address }}<br>
            <strong>Phone:</strong> {{ selectedOrder.customer.phone }}<br>
            <strong>Status:</strong> {{ selectedOrder.delivery_status }}<br>
            <strong>Meal Type:</strong> {{ selectedOrder.meal_type }}<br>
            <strong>Amount:</strong> ₹{{ selectedOrder.amount }}
          </p>
          <div v-if="selectedOrder.route">
            <h6>Delivery Details</h6>
            <p>
              <strong>Distance:</strong> {{ selectedOrder.route.distance_km }} km<br>
              <strong>Duration:</strong> {{ selectedOrder.route.duration_minutes }} minutes<br>
              <strong>Estimated Delivery:</strong> {{ selectedOrder.route.estimated_delivery_time }}<br>
              <strong>Delivery Fee:</strong> ₹{{ selectedOrder.route.delivery_fee }}
            </p>
          </div>
          <div v-if="selectedOrder.customer.dabbawala_code">
            <h6>Dabbawala Details</h6>
            <p>
              <strong>Code:</strong> {{ selectedOrder.customer.dabbawala_code }}
            </p>
          </div>
          <button @click="showRoute(selectedOrder)" class="btn btn-sm btn-primary">Show Route</button>
          <button @click="closeDetails" class="btn btn-sm btn-secondary ml-2">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeliveryMap',

  data() {
    return {
      map: null,
      kitchenMarkers: [],
      customerMarkers: [],
      routeLayer: null,
      selectedOrder: null,
      orders: [],
      kitchens: [],
      deliveryPersons: [],
      filters: {
        date: new Date().toISOString().split('T')[0],
        meal_type: '',
        delivery_person_id: '',
        status: ''
      },
      markerIcons: {
        kitchen: L.icon({
          iconUrl: '/images/kitchen-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        pending: L.icon({
          iconUrl: '/images/pending-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        dispatched: L.icon({
          iconUrl: '/images/dispatched-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        inTransit: L.icon({
          iconUrl: '/images/in-transit-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        })
      }
    };
  },

  mounted() {
    this.initMap();
    this.loadData();
    this.loadDeliveryPersons();
  },

  methods: {
    initMap() {
      // Initialize the map
      this.map = L.map(this.$refs.mapContainer).setView(
        [19.0760, 72.8777], // Mumbai coordinates
        12
      );

      // Add the tile layer (Self-hosted OpenStreetMap)
      L.tileLayer('/api/v2/maps/tiles/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(this.map);
    },

    async loadData() {
      try {
        // Load kitchens
        const kitchenResponse = await axios.get('/api/v2/delivery/map/delivery-locations');
        this.kitchens = kitchenResponse.data.data;

        // Load orders
        let url = '/api/v2/delivery/map/active-orders';
        const params = {};

        if (this.filters.date) {
          params.date = this.filters.date;
        }

        if (this.filters.meal_type) {
          params.meal_type = this.filters.meal_type;
        }

        if (this.filters.delivery_person_id) {
          params.delivery_person_id = this.filters.delivery_person_id;
        }

        if (this.filters.status) {
          params.status = this.filters.status;
        }

        const orderResponse = await axios.get(url, { params });
        this.orders = orderResponse.data.data;

        // Update the map
        this.updateMap();
      } catch (error) {
        console.error('Error loading data:', error);
      }
    },

    async loadDeliveryPersons() {
      try {
        const response = await axios.get('/api/v2/delivery/persons');
        this.deliveryPersons = response.data.data;
      } catch (error) {
        console.error('Error loading delivery persons:', error);
      }
    },

    updateMap() {
      // Clear existing markers
      this.clearMarkers();

      // Add kitchen markers
      this.kitchens.forEach(kitchen => {
        const marker = L.marker(
          [kitchen.coordinates.lat, kitchen.coordinates.lng],
          { icon: this.markerIcons.kitchen }
        ).addTo(this.map);

        marker.bindPopup(`
          <strong>${kitchen.name}</strong><br>
          ${kitchen.address}
        `);

        this.kitchenMarkers.push(marker);
      });

      // Add order markers
      this.orders.forEach(order => {
        const customer = order.customer;

        if (!customer.coordinates || !customer.coordinates.lat || !customer.coordinates.lng) {
          return;
        }

        let icon;
        switch (order.delivery_status) {
          case 'Pending':
            icon = this.markerIcons.pending;
            break;
          case 'Dispatched':
            icon = this.markerIcons.dispatched;
            break;
          case 'In Transit':
            icon = this.markerIcons.inTransit;
            break;
          default:
            icon = this.markerIcons.pending;
        }

        const marker = L.marker(
          [customer.coordinates.lat, customer.coordinates.lng],
          { icon }
        ).addTo(this.map);

        marker.bindPopup(`
          <strong>Order #${order.order_no}</strong><br>
          Customer: ${customer.name}<br>
          Status: ${order.delivery_status}<br>
          <button class="btn btn-sm btn-primary show-details" data-order-id="${order.id}">
            Show Details
          </button>
        `);

        marker.on('popupopen', () => {
          document.querySelector(`.show-details[data-order-id="${order.id}"]`)
            .addEventListener('click', () => this.showDetails(order));
        });

        this.customerMarkers.push(marker);
      });

      // Fit the map to show all markers
      if (this.kitchenMarkers.length > 0 || this.customerMarkers.length > 0) {
        const group = new L.featureGroup([...this.kitchenMarkers, ...this.customerMarkers]);
        this.map.fitBounds(group.getBounds(), { padding: [50, 50] });
      }
    },

    clearMarkers() {
      // Remove kitchen markers
      this.kitchenMarkers.forEach(marker => {
        this.map.removeLayer(marker);
      });
      this.kitchenMarkers = [];

      // Remove customer markers
      this.customerMarkers.forEach(marker => {
        this.map.removeLayer(marker);
      });
      this.customerMarkers = [];

      // Remove route layer
      if (this.routeLayer) {
        this.map.removeLayer(this.routeLayer);
        this.routeLayer = null;
      }
    },

    showDetails(order) {
      this.selectedOrder = order;
    },

    closeDetails() {
      this.selectedOrder = null;
    },

    async showRoute(order) {
      try {
        // Remove existing route layer
        if (this.routeLayer) {
          this.map.removeLayer(this.routeLayer);
          this.routeLayer = null;
        }

        // Get the route details
        const response = await axios.get(`/api/v2/delivery/map/delivery-route/${order.id}`);
        const route = response.data.data;

        if (!route.route_geometry) {
          console.error('No route geometry available');
          return;
        }

        // Add the route to the map
        this.routeLayer = L.geoJSON(route.route_geometry, {
          style: {
            color: '#3388ff',
            weight: 5,
            opacity: 0.7
          }
        }).addTo(this.map);

        // Fit the map to show the route
        this.map.fitBounds(this.routeLayer.getBounds(), { padding: [50, 50] });
      } catch (error) {
        console.error('Error loading route:', error);
      }
    }
  }
};
</script>

<style scoped>
.delivery-map-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.map-filters {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.map-container {
  flex: 1;
  min-height: 500px;
}

#map {
  height: 100%;
  width: 100%;
}

.order-details {
  position: absolute;
  top: 80px;
  right: 20px;
  width: 300px;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  padding: 1rem;
}

.form-group {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .map-filters {
    flex-direction: column;
  }

  .order-details {
    width: 100%;
    top: auto;
    right: auto;
    bottom: 0;
    left: 0;
    border-radius: 0;
  }
}
</style>

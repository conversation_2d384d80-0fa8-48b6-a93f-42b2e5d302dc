<template>
  <div class="tracking-map-container">
    <div class="tracking-header">
      <h2>Track Your Delivery</h2>
      <div class="order-info" v-if="order">
        <div class="order-number">
          <strong>Order #{{ order.order_no }}</strong>
        </div>
        <div class="order-status">
          <span :class="'status-badge ' + getStatusClass(order.delivery_status)">
            {{ order.delivery_status }}
          </span>
        </div>
      </div>
    </div>

    <div class="map-container">
      <div id="tracking-map" ref="mapContainer"></div>
    </div>

    <div class="delivery-info" v-if="order">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Delivery Details</h5>

          <div class="delivery-status">
            <div class="status-timeline">
              <div class="status-step" :class="{ active: isStatusActive('Pending') }">
                <div class="status-icon">
                  <i class="fas fa-check-circle"></i>
                </div>
                <div class="status-label">Order Confirmed</div>
              </div>
              <div class="status-step" :class="{ active: isStatusActive('Dispatched') }">
                <div class="status-icon">
                  <i class="fas fa-utensils"></i>
                </div>
                <div class="status-label">Food Prepared</div>
              </div>
              <div class="status-step" :class="{ active: isStatusActive('In Transit') }">
                <div class="status-icon">
                  <i class="fas fa-bicycle"></i>
                </div>
                <div class="status-label">Out for Delivery</div>
              </div>
              <div class="status-step" :class="{ active: isStatusActive('Delivered') }">
                <div class="status-icon">
                  <i class="fas fa-home"></i>
                </div>
                <div class="status-label">Delivered</div>
              </div>
            </div>
          </div>

          <div class="delivery-details">
            <div class="detail-item">
              <div class="detail-label">Estimated Delivery Time</div>
              <div class="detail-value">{{ getEstimatedDeliveryTime() }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Distance</div>
              <div class="detail-value" v-if="order.route">{{ order.route.distance_km }} km</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Delivery Person</div>
              <div class="detail-value" v-if="order.delivery_person">
                {{ order.delivery_person.name }}
                <a :href="'tel:' + order.delivery_person.phone" class="btn btn-sm btn-outline-primary ml-2">
                  <i class="fas fa-phone"></i> Call
                </a>
              </div>
              <div class="detail-value" v-else>Not assigned yet</div>
            </div>
          </div>

          <div class="dabbawala-info" v-if="order.customer && order.customer.dabbawala_code">
            <h6>Mumbai Dabbawala Information</h6>
            <div class="detail-item">
              <div class="detail-label">Dabbawala Code</div>
              <div class="detail-value">{{ order.customer.dabbawala_code }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomerTrackingMap',

  props: {
    orderId: {
      type: [Number, String],
      required: true
    }
  },

  data() {
    return {
      map: null,
      order: null,
      kitchen: null,
      routeLayer: null,
      deliveryMarker: null,
      customerMarker: null,
      kitchenMarker: null,
      refreshInterval: null,
      markerIcons: {
        kitchen: L.icon({
          iconUrl: '/images/kitchen-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        customer: L.icon({
          iconUrl: '/images/customer-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        delivery: L.icon({
          iconUrl: '/images/delivery-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        })
      }
    };
  },

  mounted() {
    this.initMap();
    this.loadOrderData();

    // Refresh data every 30 seconds
    this.refreshInterval = setInterval(() => {
      this.loadOrderData();
    }, 30000);
  },

  beforeUnmount() {
    // Clear the refresh interval when component is destroyed
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
  },

  methods: {
    initMap() {
      // Initialize the map
      this.map = L.map(this.$refs.mapContainer).setView(
        [19.0760, 72.8777], // Mumbai coordinates
        12
      );

      // Add the tile layer (Self-hosted OpenStreetMap)
      L.tileLayer('/api/v2/maps/tiles/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(this.map);
    },

    async loadOrderData() {
      try {
        // Load order data
        const response = await axios.get(`/api/v2/delivery/orders/${this.orderId}`);
        this.order = response.data.data;

        // Update the map
        this.updateMap();
      } catch (error) {
        console.error('Error loading order data:', error);
      }
    },

    updateMap() {
      if (!this.order || !this.order.customer || !this.order.customer.coordinates) {
        return;
      }

      // Clear existing markers
      this.clearMarkers();

      // Add customer marker
      const customerCoords = [
        this.order.customer.coordinates.lat,
        this.order.customer.coordinates.lng
      ];

      this.customerMarker = L.marker(
        customerCoords,
        { icon: this.markerIcons.customer }
      ).addTo(this.map);

      this.customerMarker.bindPopup(`
        <strong>${this.order.customer.name}</strong><br>
        ${this.order.customer.address}
      `);

      // Add kitchen marker if available
      if (this.order.kitchen && this.order.kitchen.coordinates) {
        const kitchenCoords = [
          this.order.kitchen.coordinates.lat,
          this.order.kitchen.coordinates.lng
        ];

        this.kitchenMarker = L.marker(
          kitchenCoords,
          { icon: this.markerIcons.kitchen }
        ).addTo(this.map);

        this.kitchenMarker.bindPopup(`
          <strong>${this.order.kitchen.name}</strong>
        `);
      }

      // Add delivery person marker if available and order is in transit
      if (
        this.order.delivery_status === 'In Transit' &&
        this.order.delivery_person &&
        this.order.delivery_person.current_location
      ) {
        const deliveryCoords = [
          this.order.delivery_person.current_location.lat,
          this.order.delivery_person.current_location.lng
        ];

        this.deliveryMarker = L.marker(
          deliveryCoords,
          { icon: this.markerIcons.delivery }
        ).addTo(this.map);

        this.deliveryMarker.bindPopup(`
          <strong>${this.order.delivery_person.name}</strong><br>
          Your delivery person
        `);
      }

      // Add route if available
      if (this.order.route && this.order.route.route_geometry) {
        this.routeLayer = L.geoJSON(this.order.route.route_geometry, {
          style: {
            color: '#3388ff',
            weight: 5,
            opacity: 0.7
          }
        }).addTo(this.map);
      }

      // Fit the map to show all markers
      const markers = [
        this.customerMarker,
        this.kitchenMarker,
        this.deliveryMarker
      ].filter(Boolean);

      if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        this.map.fitBounds(group.getBounds(), { padding: [50, 50] });
      }
    },

    clearMarkers() {
      // Remove customer marker
      if (this.customerMarker) {
        this.map.removeLayer(this.customerMarker);
        this.customerMarker = null;
      }

      // Remove kitchen marker
      if (this.kitchenMarker) {
        this.map.removeLayer(this.kitchenMarker);
        this.kitchenMarker = null;
      }

      // Remove delivery marker
      if (this.deliveryMarker) {
        this.map.removeLayer(this.deliveryMarker);
        this.deliveryMarker = null;
      }

      // Remove route layer
      if (this.routeLayer) {
        this.map.removeLayer(this.routeLayer);
        this.routeLayer = null;
      }
    },

    getStatusClass(status) {
      switch (status) {
        case 'Pending':
          return 'status-pending';
        case 'Dispatched':
          return 'status-dispatched';
        case 'In Transit':
          return 'status-in-transit';
        case 'Delivered':
          return 'status-delivered';
        case 'Failed':
          return 'status-failed';
        default:
          return '';
      }
    },

    isStatusActive(status) {
      if (!this.order) return false;

      const statusOrder = ['Pending', 'Dispatched', 'In Transit', 'Delivered'];
      const currentIndex = statusOrder.indexOf(this.order.delivery_status);
      const checkIndex = statusOrder.indexOf(status);

      return currentIndex >= checkIndex;
    },

    getEstimatedDeliveryTime() {
      if (!this.order || !this.order.route || !this.order.route.estimated_delivery_time) {
        return 'Not available';
      }

      return this.order.route.formatted_estimated_delivery_time;
    }
  }
};
</script>

<style scoped>
.tracking-map-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.tracking-header {
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: bold;
  font-size: 0.875rem;
}

.status-pending {
  background-color: #ffc107;
  color: #212529;
}

.status-dispatched {
  background-color: #17a2b8;
  color: white;
}

.status-in-transit {
  background-color: #007bff;
  color: white;
}

.status-delivered {
  background-color: #28a745;
  color: white;
}

.status-failed {
  background-color: #dc3545;
  color: white;
}

.map-container {
  flex: 1;
  min-height: 400px;
}

#tracking-map {
  height: 100%;
  width: 100%;
}

.delivery-info {
  padding: 1rem;
}

.delivery-status {
  margin-bottom: 1.5rem;
}

.status-timeline {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin: 2rem 0;
}

.status-timeline::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #dee2e6;
  z-index: 1;
}

.status-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.status-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #dee2e6;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
}

.status-step.active .status-icon {
  background-color: #28a745;
  color: white;
}

.status-label {
  font-size: 0.75rem;
  text-align: center;
  max-width: 80px;
}

.delivery-details {
  margin-top: 1.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.detail-label {
  font-weight: bold;
  color: #6c757d;
}

.dabbawala-info {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

@media (max-width: 768px) {
  .tracking-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .order-info {
    margin-top: 0.5rem;
  }

  .status-timeline {
    flex-wrap: wrap;
  }

  .status-step {
    flex: 0 0 50%;
    margin-bottom: 1rem;
  }

  .detail-item {
    flex-direction: column;
  }

  .detail-value {
    margin-top: 0.25rem;
  }
}
</style>

<template>
  <div class="delivery-person-map-container">
    <div class="delivery-header">
      <h2>Delivery Route</h2>
      <div class="delivery-filters">
        <div class="form-group">
          <label for="date">Date</label>
          <input type="date" id="date" v-model="filters.date" @change="loadData" class="form-control" />
        </div>
        <div class="form-group">
          <label for="meal_type">Meal Type</label>
          <select id="meal_type" v-model="filters.meal_type" @change="loadData" class="form-control">
            <option value="lunch">Lunch</option>
            <option value="dinner">Dinner</option>
          </select>
        </div>
        <button @click="loadData" class="btn btn-primary">Refresh</button>
      </div>
    </div>

    <div class="map-container">
      <div id="delivery-map" ref="mapContainer"></div>
    </div>

    <div class="delivery-orders">
      <h3>Your Deliveries</h3>
      <div class="order-list">
        <div v-if="orders.length === 0" class="no-orders">
          No deliveries assigned for today.
        </div>
        <div v-for="order in orders" :key="order.id"
             class="order-item"
             :class="{ active: selectedOrder && selectedOrder.id === order.id }"
             @click="selectOrder(order)">
          <div class="order-status">
            <span :class="'status-badge ' + getStatusClass(order.delivery_status)">
              {{ order.delivery_status }}
            </span>
          </div>
          <div class="order-details">
            <div class="order-number">Order #{{ order.order_no }}</div>
            <div class="order-customer">{{ order.customer.name }}</div>
            <div class="order-address">{{ order.customer.address }}</div>
          </div>
          <div class="order-actions">
            <button @click.stop="showDirections(order)" class="btn btn-sm btn-primary">
              <i class="fas fa-directions"></i>
            </button>
            <button @click.stop="updateStatus(order)" class="btn btn-sm btn-success ml-2">
              <i class="fas fa-check"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showStatusModal" class="status-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h5>Update Delivery Status</h5>
          <button @click="showStatusModal = false" class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="status">New Status</label>
            <select id="status" v-model="newStatus" class="form-control">
              <option value="Dispatched">Dispatched</option>
              <option value="In Transit">In Transit</option>
              <option value="Delivered">Delivered</option>
              <option value="Failed">Failed</option>
            </select>
          </div>
          <div class="form-group" v-if="newStatus === 'Failed'">
            <label for="failure_reason">Failure Reason</label>
            <textarea id="failure_reason" v-model="failureReason" class="form-control" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="showStatusModal = false" class="btn btn-secondary">Cancel</button>
          <button @click="submitStatusUpdate" class="btn btn-primary">Update</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeliveryPersonMap',

  data() {
    return {
      map: null,
      orders: [],
      selectedOrder: null,
      routeLayer: null,
      kitchenMarkers: [],
      customerMarkers: [],
      directionsControl: null,
      showStatusModal: false,
      newStatus: 'In Transit',
      failureReason: '',
      orderToUpdate: null,
      filters: {
        date: new Date().toISOString().split('T')[0],
        meal_type: 'lunch'
      },
      markerIcons: {
        kitchen: L.icon({
          iconUrl: '/images/kitchen-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        pending: L.icon({
          iconUrl: '/images/pending-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        dispatched: L.icon({
          iconUrl: '/images/dispatched-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        inTransit: L.icon({
          iconUrl: '/images/in-transit-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        delivered: L.icon({
          iconUrl: '/images/delivered-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        }),
        failed: L.icon({
          iconUrl: '/images/failed-marker.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32]
        })
      }
    };
  },

  mounted() {
    this.initMap();
    this.loadData();
    this.startLocationTracking();

    // Refresh data every 2 minutes
    setInterval(() => {
      this.loadData();
    }, 120000);
  },

  methods: {
    initMap() {
      // Initialize the map
      this.map = L.map(this.$refs.mapContainer).setView(
        [19.0760, 72.8777], // Mumbai coordinates
        12
      );

      // Add the tile layer (Self-hosted OpenStreetMap)
      L.tileLayer('/api/v2/maps/tiles/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(this.map);
    },

    async loadData() {
      try {
        // Load assigned orders
        const params = {
          date: this.filters.date,
          meal_type: this.filters.meal_type,
          status: 'active' // Only get active orders (not delivered or failed)
        };

        const response = await axios.get('/api/v2/delivery/person/orders', { params });
        this.orders = response.data.data;

        // Update the map
        this.updateMap();
      } catch (error) {
        console.error('Error loading data:', error);
      }
    },

    updateMap() {
      // Clear existing markers and routes
      this.clearMap();

      if (this.orders.length === 0) {
        return;
      }

      // Group orders by kitchen
      const ordersByKitchen = {};

      this.orders.forEach(order => {
        const kitchenId = order.kitchen.id;

        if (!ordersByKitchen[kitchenId]) {
          ordersByKitchen[kitchenId] = {
            kitchen: order.kitchen,
            orders: []
          };
        }

        ordersByKitchen[kitchenId].orders.push(order);
      });

      // Add markers for each kitchen and its orders
      Object.values(ordersByKitchen).forEach(group => {
        const kitchen = group.kitchen;

        // Add kitchen marker
        if (kitchen.coordinates) {
          const kitchenMarker = L.marker(
            [kitchen.coordinates.lat, kitchen.coordinates.lng],
            { icon: this.markerIcons.kitchen }
          ).addTo(this.map);

          kitchenMarker.bindPopup(`
            <strong>${kitchen.name}</strong><br>
            Pickup location
          `);

          this.kitchenMarkers.push(kitchenMarker);
        }

        // Add customer markers
        group.orders.forEach(order => {
          const customer = order.customer;

          if (customer.coordinates) {
            let icon;
            switch (order.delivery_status) {
              case 'Pending':
                icon = this.markerIcons.pending;
                break;
              case 'Dispatched':
                icon = this.markerIcons.dispatched;
                break;
              case 'In Transit':
                icon = this.markerIcons.inTransit;
                break;
              case 'Delivered':
                icon = this.markerIcons.delivered;
                break;
              case 'Failed':
                icon = this.markerIcons.failed;
                break;
              default:
                icon = this.markerIcons.pending;
            }

            const customerMarker = L.marker(
              [customer.coordinates.lat, customer.coordinates.lng],
              { icon }
            ).addTo(this.map);

            customerMarker.bindPopup(`
              <strong>Order #${order.order_no}</strong><br>
              Customer: ${customer.name}<br>
              Address: ${customer.address}<br>
              <button class="btn btn-sm btn-primary show-directions" data-order-id="${order.id}">
                Show Directions
              </button>
            `);

            customerMarker.on('popupopen', () => {
              document.querySelector(`.show-directions[data-order-id="${order.id}"]`)
                .addEventListener('click', () => this.showDirections(order));
            });

            this.customerMarkers.push(customerMarker);
          }
        });
      });

      // Fit the map to show all markers
      if (this.kitchenMarkers.length > 0 || this.customerMarkers.length > 0) {
        const group = new L.featureGroup([...this.kitchenMarkers, ...this.customerMarkers]);
        this.map.fitBounds(group.getBounds(), { padding: [50, 50] });
      }
    },

    clearMap() {
      // Remove kitchen markers
      this.kitchenMarkers.forEach(marker => {
        this.map.removeLayer(marker);
      });
      this.kitchenMarkers = [];

      // Remove customer markers
      this.customerMarkers.forEach(marker => {
        this.map.removeLayer(marker);
      });
      this.customerMarkers = [];

      // Remove route layer
      if (this.routeLayer) {
        this.map.removeLayer(this.routeLayer);
        this.routeLayer = null;
      }

      // Remove directions control
      if (this.directionsControl) {
        this.map.removeControl(this.directionsControl);
        this.directionsControl = null;
      }
    },

    selectOrder(order) {
      this.selectedOrder = order;

      // Center the map on the customer location
      if (order.customer.coordinates) {
        this.map.setView(
          [order.customer.coordinates.lat, order.customer.coordinates.lng],
          15
        );
      }
    },

    async showDirections(order) {
      try {
        // Get the route details
        const response = await axios.get(`/api/v2/delivery/map/delivery-route/${order.id}`);
        const route = response.data.data;

        if (!route.route_geometry) {
          console.error('No route geometry available');
          return;
        }

        // Remove existing route layer
        if (this.routeLayer) {
          this.map.removeLayer(this.routeLayer);
          this.routeLayer = null;
        }

        // Add the route to the map
        this.routeLayer = L.geoJSON(route.route_geometry, {
          style: {
            color: '#3388ff',
            weight: 5,
            opacity: 0.7
          }
        }).addTo(this.map);

        // Fit the map to show the route
        this.map.fitBounds(this.routeLayer.getBounds(), { padding: [50, 50] });

        // Select the order
        this.selectOrder(order);
      } catch (error) {
        console.error('Error loading route:', error);
      }
    },

    updateStatus(order) {
      this.orderToUpdate = order;
      this.newStatus = this.getNextStatus(order.delivery_status);
      this.failureReason = '';
      this.showStatusModal = true;
    },

    getNextStatus(currentStatus) {
      switch (currentStatus) {
        case 'Pending':
          return 'Dispatched';
        case 'Dispatched':
          return 'In Transit';
        case 'In Transit':
          return 'Delivered';
        default:
          return 'Delivered';
      }
    },

    async submitStatusUpdate() {
      if (!this.orderToUpdate) return;

      try {
        const data = {
          status: this.newStatus
        };

        if (this.newStatus === 'Failed' && this.failureReason) {
          data.failure_reason = this.failureReason;
        }

        await axios.put(`/api/v2/delivery/orders/${this.orderToUpdate.id}/status`, data);

        // Close the modal
        this.showStatusModal = false;

        // Reload the data
        this.loadData();
      } catch (error) {
        console.error('Error updating status:', error);
      }
    },

    getStatusClass(status) {
      switch (status) {
        case 'Pending':
          return 'status-pending';
        case 'Dispatched':
          return 'status-dispatched';
        case 'In Transit':
          return 'status-in-transit';
        case 'Delivered':
          return 'status-delivered';
        case 'Failed':
          return 'status-failed';
        default:
          return '';
      }
    },

    startLocationTracking() {
      // Check if geolocation is available
      if ('geolocation' in navigator) {
        // Get the current position
        navigator.geolocation.watchPosition(
          position => {
            const { latitude, longitude } = position.coords;

            // Send the location to the server
            this.updateDeliveryPersonLocation(latitude, longitude);
          },
          error => {
            console.error('Error getting location:', error);
          },
          {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0
          }
        );
      }
    },

    async updateDeliveryPersonLocation(latitude, longitude) {
      try {
        await axios.post('/api/v2/delivery/person/location', {
          latitude,
          longitude
        });
      } catch (error) {
        console.error('Error updating location:', error);
      }
    }
  }
};
</script>

<style scoped>
.delivery-person-map-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  grid-template-rows: auto 1fr;
  height: 100vh;
  width: 100%;
}

.delivery-header {
  grid-column: 1 / 3;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.delivery-filters {
  display: flex;
  gap: 1rem;
}

.map-container {
  grid-column: 1;
  grid-row: 2;
  height: 100%;
}

#delivery-map {
  height: 100%;
  width: 100%;
}

.delivery-orders {
  grid-column: 2;
  grid-row: 2;
  padding: 1rem;
  background-color: #f8f9fa;
  border-left: 1px solid #dee2e6;
  overflow-y: auto;
}

.order-list {
  margin-top: 1rem;
}

.order-item {
  background-color: white;
  border-radius: 0.25rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.order-item.active {
  border-left: 4px solid #007bff;
}

.order-status {
  margin-bottom: 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: bold;
  font-size: 0.75rem;
}

.status-pending {
  background-color: #ffc107;
  color: #212529;
}

.status-dispatched {
  background-color: #17a2b8;
  color: white;
}

.status-in-transit {
  background-color: #007bff;
  color: white;
}

.status-delivered {
  background-color: #28a745;
  color: white;
}

.status-failed {
  background-color: #dc3545;
  color: white;
}

.order-number {
  font-weight: bold;
}

.order-customer {
  margin-top: 0.25rem;
}

.order-address {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.order-actions {
  margin-top: 0.5rem;
  display: flex;
  justify-content: flex-end;
}

.no-orders {
  padding: 1rem;
  text-align: center;
  color: #6c757d;
}

.status-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 0.25rem;
  width: 90%;
  max-width: 500px;
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 1rem;
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

@media (max-width: 768px) {
  .delivery-person-map-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .delivery-header {
    grid-column: 1;
    flex-direction: column;
    align-items: flex-start;
  }

  .delivery-filters {
    margin-top: 0.5rem;
    flex-wrap: wrap;
  }

  .map-container {
    grid-column: 1;
    grid-row: 2;
  }

  .delivery-orders {
    grid-column: 1;
    grid-row: 3;
    border-left: none;
    border-top: 1px solid #dee2e6;
    max-height: 300px;
  }
}
</style>

#!/bin/bash

# Exit on error
set -e

# Configuration
STAGING_SERVER="staging-server.fooddialer.com"
STAGING_USER="deploy"
STAGING_PATH="/var/www/delivery-service"
DOCKER_REGISTRY="registry.fooddialer.com"
DOCKER_IMAGE="delivery-service"
DOCKER_TAG="staging"

# Build Docker image
echo "Building Docker image..."
docker build -t ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${DOCKER_TAG} .

# Push Docker image to registry
echo "Pushing Docker image to registry..."
docker push ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${DOCKER_TAG}

# Deploy to staging server
echo "Deploying to staging server..."
ssh ${STAGING_USER}@${STAGING_SERVER} << EOF
    cd ${STAGING_PATH}
    docker-compose down
    docker pull ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${DOCKER_TAG}
    docker-compose up -d
    docker exec delivery-service-app php artisan migrate --force
    docker exec delivery-service-app php artisan config:cache
    docker exec delivery-service-app php artisan route:cache
    docker exec delivery-service-app php artisan view:cache
EOF

echo "Deployment completed successfully!"

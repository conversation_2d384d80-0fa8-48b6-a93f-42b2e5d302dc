# Delivery Service

This is a Laravel 12 microservice for managing delivery operations. It provides APIs for managing delivery persons, orders, locations, and third-party delivery integrations.

## Features

- Delivery person authentication and authorization
- Order management for delivery
- Location management
- Third-party delivery integration (YourGuy, Mumbai Dabbawala)
- Mumbai Dabbawala code generation and tracking
- Event-driven architecture using RabbitMQ

## Requirements

- PHP 8.1+
- Composer
- MySQL 8.0+
- RabbitMQ 3.x
- Docker (optional)

## Installation

### Using Docker

1. Clone the repository
2. Navigate to the project directory
3. Copy the `.env.example` file to `.env`
4. Run the following commands:

```bash
docker-compose build
docker-compose up -d
docker-compose exec app composer install
docker-compose exec app php artisan key:generate
docker-compose exec app php artisan migrate
docker-compose exec app php artisan db:seed
```

### Manual Installation

1. Clone the repository
2. Navigate to the project directory
3. Copy the `.env.example` file to `.env`
4. Run the following commands:

```bash
composer install
php artisan key:generate
php artisan migrate
php artisan db:seed
```

## API Documentation

The API documentation is available in the OpenAPI 3.1 format. You can view the documentation by opening the `openapi.yaml` file in any OpenAPI viewer.

### Endpoints

- `GET /api/v2/delivery/orders` - Get orders for delivery
- `GET /api/v2/delivery/orders/search` - Search orders
- `POST /api/v2/delivery/orders/{orderId}/delivery-status` - Update delivery status
- `GET /api/v2/delivery/locations` - Get delivery locations
- `POST /api/v2/delivery/third-party/book` - Book third-party delivery
- `POST /api/v2/delivery/third-party/{orderId}/cancel` - Cancel third-party delivery
- `GET /api/v2/delivery/third-party/{orderId}/status` - Get third-party delivery status

## Testing

To run the tests, use the following command:

```bash
php artisan test
```

## Event-Driven Architecture

This microservice uses RabbitMQ for event-driven communication with other microservices. The following events are published:

- `OrderDeliveredEvent` - Published when an order is marked as delivered

## Docker Configuration

The Docker configuration includes the following services:

- PHP-FPM 8.3
- Nginx
- MySQL 8.0
- RabbitMQ 3.x

## License

This project is licensed under the MIT License - see the LICENSE file for details.

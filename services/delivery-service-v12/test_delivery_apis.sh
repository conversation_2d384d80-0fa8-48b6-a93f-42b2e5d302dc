#!/bin/bash

# Delivery Service v12 API Testing Script
echo "=== Delivery Service v12 API Testing ==="
echo "Base URL: http://localhost:8106/api/v2"
echo ""

# Function to test API endpoint
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "$description"
    echo "Testing: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        curl -s -X GET "http://localhost:8106/api/v2$endpoint" \
             -H "Accept: application/json" \
             -H "Content-Type: application/json" | jq '.' 2>/dev/null || echo "Response received (not JSON)"
    elif [ "$method" = "POST" ]; then
        curl -s -X POST "http://localhost:8106/api/v2$endpoint" \
             -H "Accept: application/json" \
             -H "Content-Type: application/json" \
             -d "$data" | jq '.' 2>/dev/null || echo "Response received (not JSON)"
    elif [ "$method" = "PUT" ]; then
        curl -s -X PUT "http://localhost:8106/api/v2$endpoint" \
             -H "Accept: application/json" \
             -H "Content-Type: application/json" \
             -d "$data" | jq '.' 2>/dev/null || echo "Response received (not JSON)"
    fi
    
    echo ""
    echo "---"
    echo ""
}

# Test delivery locations
test_api "GET" "/delivery/locations" "" "1. Testing GET /delivery/locations"

# Test delivery persons
test_api "GET" "/delivery/persons" "" "2. Testing GET /delivery/persons"

# Test delivery orders
test_api "GET" "/delivery/orders" "" "3. Testing GET /delivery/orders"

# Test delivery zones
test_api "GET" "/delivery/zones" "" "4. Testing GET /delivery/zones"

# Test delivery staff
test_api "GET" "/delivery/staff" "" "5. Testing GET /delivery/staff"

# Test delivery assignments
test_api "GET" "/delivery/assignments" "" "6. Testing GET /delivery/assignments"

# Test delivery tracking
test_api "GET" "/delivery/tracking/active-deliveries" "" "7. Testing GET /delivery/tracking/active-deliveries"

# Test delivery tracking dashboard
test_api "GET" "/delivery/tracking/dashboard" "" "8. Testing GET /delivery/tracking/dashboard"

# Test map endpoints
test_api "GET" "/delivery/map/delivery-locations" "" "9. Testing GET /delivery/map/delivery-locations"

test_api "GET" "/delivery/map/customers" "" "10. Testing GET /delivery/map/customers"

test_api "GET" "/delivery/map/active-orders" "" "11. Testing GET /delivery/map/active-orders"

# Test school delivery batches
test_api "GET" "/delivery/school/batches" "" "12. Testing GET /delivery/school/batches"

test_api "GET" "/delivery/school/performance-metrics" "" "13. Testing GET /delivery/school/performance-metrics"

# Test third-party delivery status (with sample order ID)
test_api "GET" "/delivery/third-party/1/status" "" "14. Testing GET /delivery/third-party/1/status"

echo "=== API Testing Complete ==="

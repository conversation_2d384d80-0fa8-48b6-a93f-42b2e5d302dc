<?php

namespace Tests\Unit;

use App\Services\GeocodingService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GeocodingServiceTest extends TestCase
{
    protected GeocodingService $geocodingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->geocodingService = new GeocodingService();
        
        // Clear the cache
        Cache::flush();
    }

    public function testGeocodeAddress()
    {
        // Mock the HTTP response
        Http::fake([
            'nominatim.openstreetmap.org/search*' => Http::response([
                [
                    'lat' => '19.0760',
                    'lon' => '72.8777',
                    'display_name' => 'Mumbai, Maharashtra, India',
                    'address' => [
                        'city' => 'Mumbai',
                        'state' => 'Maharashtra',
                        'country' => 'India'
                    ]
                ]
            ], 200)
        ]);
        
        $result = $this->geocodingService->geocode('Mumbai', 'Mumbai', 'India');
        
        $this->assertNotNull($result);
        $this->assertEquals(19.0760, $result['lat']);
        $this->assertEquals(72.8777, $result['lon']);
        $this->assertEquals('Mumbai, Maharashtra, India', $result['display_name']);
        $this->assertArrayHasKey('address_components', $result);
    }

    public function testGeocodeAddressWithCache()
    {
        // Mock the HTTP response
        Http::fake([
            'nominatim.openstreetmap.org/search*' => Http::response([
                [
                    'lat' => '19.0760',
                    'lon' => '72.8777',
                    'display_name' => 'Mumbai, Maharashtra, India',
                    'address' => [
                        'city' => 'Mumbai',
                        'state' => 'Maharashtra',
                        'country' => 'India'
                    ]
                ]
            ], 200)
        ]);
        
        // First call should make an HTTP request
        $result1 = $this->geocodingService->geocode('Mumbai', 'Mumbai', 'India');
        
        // Second call should use the cached result
        $result2 = $this->geocodingService->geocode('Mumbai', 'Mumbai', 'India');
        
        $this->assertEquals($result1, $result2);
        
        // Verify that only one HTTP request was made
        Http::assertSentCount(1);
    }

    public function testGeocodeAddressFailure()
    {
        // Mock the HTTP response
        Http::fake([
            'nominatim.openstreetmap.org/search*' => Http::response([], 200)
        ]);
        
        $result = $this->geocodingService->geocode('NonexistentAddress123', 'NonexistentCity', 'NonexistentCountry');
        
        $this->assertNull($result);
    }

    public function testReverseGeocode()
    {
        // Mock the HTTP response
        Http::fake([
            'nominatim.openstreetmap.org/reverse*' => Http::response([
                'lat' => '19.0760',
                'lon' => '72.8777',
                'display_name' => 'Mumbai, Maharashtra, India',
                'address' => [
                    'city' => 'Mumbai',
                    'state' => 'Maharashtra',
                    'country' => 'India'
                ]
            ], 200)
        ]);
        
        $result = $this->geocodingService->reverseGeocode(19.0760, 72.8777);
        
        $this->assertNotNull($result);
        $this->assertEquals('Mumbai, Maharashtra, India', $result['display_name']);
        $this->assertArrayHasKey('address_components', $result);
        $this->assertEquals(19.0760, $result['lat']);
        $this->assertEquals(72.8777, $result['lon']);
    }

    public function testValidateAddress()
    {
        // Mock the HTTP response
        Http::fake([
            // Valid address
            'nominatim.openstreetmap.org/search?q=Mumbai%2C+India&format=json&limit=1&addressdetails=1' => Http::response([
                [
                    'lat' => '19.0760',
                    'lon' => '72.8777',
                    'display_name' => 'Mumbai, Maharashtra, India',
                    'address' => [
                        'city' => 'Mumbai',
                        'state' => 'Maharashtra',
                        'country' => 'India'
                    ]
                ]
            ], 200),
            
            // Invalid address
            'nominatim.openstreetmap.org/search?q=NonexistentAddress123%2C+NonexistentCity%2C+NonexistentCountry&format=json&limit=1&addressdetails=1' => Http::response([], 200)
        ]);
        
        $validResult = $this->geocodingService->validateAddress('Mumbai', null, 'India');
        $invalidResult = $this->geocodingService->validateAddress('NonexistentAddress123', 'NonexistentCity', 'NonexistentCountry');
        
        $this->assertTrue($validResult);
        $this->assertFalse($invalidResult);
    }

    public function testGetHaversineDistance()
    {
        // Mumbai coordinates
        $lat1 = 19.0760;
        $lon1 = 72.8777;
        
        // Delhi coordinates
        $lat2 = 28.7041;
        $lon2 = 77.1025;
        
        // The distance between Mumbai and Delhi is approximately 1150-1200 km
        $distance = $this->geocodingService->getHaversineDistance($lat1, $lon1, $lat2, $lon2);
        
        $this->assertGreaterThan(1100, $distance);
        $this->assertLessThan(1300, $distance);
    }
}

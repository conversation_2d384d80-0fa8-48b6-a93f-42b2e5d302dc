<?php

namespace Tests\Unit;

use App\Services\RoutingService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class RoutingServiceTest extends TestCase
{
    protected RoutingService $routingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->routingService = new RoutingService();
        
        // Clear the cache
        Cache::flush();
    }

    public function testCalculateRoute()
    {
        // Mock the HTTP response
        Http::fake([
            'router.project-osrm.org/route/v1/driving/*' => Http::response([
                'code' => 'Ok',
                'routes' => [
                    [
                        'distance' => 10000, // 10 km in meters
                        'duration' => 1200, // 20 minutes in seconds
                        'geometry' => [
                            'type' => 'LineString',
                            'coordinates' => [
                                [72.8777, 19.0760], // Mumbai
                                [72.9000, 19.1000], // Somewhere in Mumbai
                            ]
                        ],
                        'legs' => [
                            [
                                'steps' => [
                                    [
                                        'name' => 'Test Road',
                                        'distance' => 10000,
                                        'duration' => 1200,
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);
        
        $result = $this->routingService->calculateRoute(
            19.0760, // Mumbai latitude
            72.8777, // Mumbai longitude
            19.1000, // Destination latitude
            72.9000, // Destination longitude
            true, // Include geometry
            true  // Include steps
        );
        
        $this->assertNotNull($result);
        $this->assertEquals(10, $result['distance']); // 10 km
        $this->assertEquals(1200, $result['duration']); // 20 minutes in seconds
        $this->assertNotNull($result['geometry']);
        $this->assertNotNull($result['steps']);
    }

    public function testCalculateRouteWithCache()
    {
        // Mock the HTTP response
        Http::fake([
            'router.project-osrm.org/route/v1/driving/*' => Http::response([
                'code' => 'Ok',
                'routes' => [
                    [
                        'distance' => 10000, // 10 km in meters
                        'duration' => 1200, // 20 minutes in seconds
                        'geometry' => [
                            'type' => 'LineString',
                            'coordinates' => [
                                [72.8777, 19.0760], // Mumbai
                                [72.9000, 19.1000], // Somewhere in Mumbai
                            ]
                        ],
                        'legs' => [
                            [
                                'steps' => [
                                    [
                                        'name' => 'Test Road',
                                        'distance' => 10000,
                                        'duration' => 1200,
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);
        
        // First call should make an HTTP request
        $result1 = $this->routingService->calculateRoute(
            19.0760, 72.8777, 19.1000, 72.9000, true, true
        );
        
        // Second call should use the cached result
        $result2 = $this->routingService->calculateRoute(
            19.0760, 72.8777, 19.1000, 72.9000, true, true
        );
        
        $this->assertEquals($result1, $result2);
        
        // Verify that only one HTTP request was made
        Http::assertSentCount(1);
    }

    public function testCalculateRouteFailure()
    {
        // Mock the HTTP response
        Http::fake([
            'router.project-osrm.org/route/v1/driving/*' => Http::response([
                'code' => 'NoRoute',
                'message' => 'No route found'
            ], 200)
        ]);
        
        $result = $this->routingService->calculateRoute(
            19.0760, 72.8777, 19.1000, 72.9000, true, true
        );
        
        $this->assertNull($result);
    }

    public function testCalculateRouteWithWaypoints()
    {
        // Mock the HTTP response
        Http::fake([
            'router.project-osrm.org/route/v1/driving/*' => Http::response([
                'code' => 'Ok',
                'routes' => [
                    [
                        'distance' => 20000, // 20 km in meters
                        'duration' => 2400, // 40 minutes in seconds
                        'geometry' => [
                            'type' => 'LineString',
                            'coordinates' => [
                                [72.8777, 19.0760], // Mumbai
                                [72.9000, 19.1000], // Waypoint 1
                                [72.9500, 19.1500], // Waypoint 2
                            ]
                        ],
                        'legs' => [
                            [
                                'distance' => 10000,
                                'duration' => 1200,
                            ],
                            [
                                'distance' => 10000,
                                'duration' => 1200,
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);
        
        $result = $this->routingService->calculateRouteWithWaypoints(
            [
                [19.0760, 72.8777], // Mumbai
                [19.1000, 72.9000], // Waypoint 1
                [19.1500, 72.9500], // Waypoint 2
            ],
            true, // Include geometry
            false // Don't include steps
        );
        
        $this->assertNotNull($result);
        $this->assertEquals(20, $result['distance']); // 20 km
        $this->assertEquals(2400, $result['duration']); // 40 minutes in seconds
        $this->assertNotNull($result['geometry']);
        $this->assertNotNull($result['legs']);
        $this->assertCount(2, $result['legs']);
    }

    public function testOptimizeRoute()
    {
        // Mock the HTTP response
        Http::fake([
            'router.project-osrm.org/trip/v1/driving/*' => Http::response([
                'code' => 'Ok',
                'trips' => [
                    [
                        'distance' => 20000, // 20 km in meters
                        'duration' => 2400, // 40 minutes in seconds
                        'geometry' => [
                            'type' => 'LineString',
                            'coordinates' => [
                                [72.8777, 19.0760], // Mumbai
                                [72.9500, 19.1500], // Optimized order: Waypoint 2
                                [72.9000, 19.1000], // Optimized order: Waypoint 1
                            ]
                        ]
                    ]
                ],
                'waypoints' => [
                    [
                        'waypoint_index' => 0,
                        'trips_index' => 0
                    ],
                    [
                        'waypoint_index' => 2,
                        'trips_index' => 0
                    ],
                    [
                        'waypoint_index' => 1,
                        'trips_index' => 0
                    ]
                ]
            ], 200)
        ]);
        
        $result = $this->routingService->optimizeRoute(
            [
                [19.0760, 72.8777], // Mumbai
                [19.1000, 72.9000], // Waypoint 1
                [19.1500, 72.9500], // Waypoint 2
            ],
            true, // Round trip
            true  // Include geometry
        );
        
        $this->assertNotNull($result);
        $this->assertEquals(20, $result['distance']); // 20 km
        $this->assertEquals(2400, $result['duration']); // 40 minutes in seconds
        $this->assertNotNull($result['geometry']);
        $this->assertNotNull($result['waypoint_order']);
        $this->assertCount(3, $result['waypoint_order']);
    }
}

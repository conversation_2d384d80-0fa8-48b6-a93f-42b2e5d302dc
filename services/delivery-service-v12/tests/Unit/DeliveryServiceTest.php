<?php

namespace Tests\Unit;

use App\DTOs\DeliveryStatusUpdateDTO;
use App\DTOs\OrderDeliveryDTO;
use App\Events\OrderDeliveredEvent;
use App\Repositories\Contracts\DeliveryRepositoryInterface;
use App\Repositories\Contracts\LocationRepositoryInterface;
use App\Services\Contracts\DeliveryServiceInterface;
use App\Services\DeliveryService;
use App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class DeliveryServiceTest extends TestCase
{
    private DeliveryRepositoryInterface $deliveryRepository;
    private LocationRepositoryInterface $locationRepository;
    private TPDeliveryInterface $thirdPartyDelivery;
    private DeliveryServiceInterface $deliveryService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->deliveryRepository = Mockery::mock(DeliveryRepositoryInterface::class);
        $this->locationRepository = Mockery::mock(LocationRepositoryInterface::class);
        $this->thirdPartyDelivery = Mockery::mock(TPDeliveryInterface::class);
        
        $this->deliveryService = new DeliveryService(
            $this->deliveryRepository,
            $this->locationRepository,
            $this->thirdPartyDelivery
        );
    }
    
    public function testGetDeliveryOrders(): void
    {
        $userId = 1;
        $locationId = 2;
        $date = '2023-01-01';
        
        $this->deliveryRepository->shouldReceive('getOrdersForDelivery')
            ->once()
            ->with($userId, $locationId, $date)
            ->andReturn([]);
        
        $result = $this->deliveryService->getDeliveryOrders($userId, $locationId, $date);
        
        $this->assertIsArray($result);
    }
    
    public function testSearchOrders(): void
    {
        $userId = 1;
        $searchTerm = 'test';
        $locationId = 2;
        
        $this->deliveryRepository->shouldReceive('searchOrders')
            ->once()
            ->with($userId, $searchTerm, $locationId)
            ->andReturn([]);
        
        $result = $this->deliveryService->searchOrders($userId, $searchTerm, $locationId);
        
        $this->assertIsArray($result);
    }
    
    public function testUpdateDeliveryStatus(): void
    {
        Event::fake();
        
        $dto = new DeliveryStatusUpdateDTO(1, 2, true);
        
        $this->deliveryRepository->shouldReceive('updateDeliveryStatus')
            ->once()
            ->with($dto->orderId, $dto->userId, $dto->orderCompleted)
            ->andReturn(true);
        
        $result = $this->deliveryService->updateDeliveryStatus($dto);
        
        $this->assertTrue($result);
        
        Event::assertDispatched(OrderDeliveredEvent::class, function ($event) use ($dto) {
            return $event->orderId === $dto->orderId && $event->deliveryPersonId === $dto->userId;
        });
    }
    
    public function testGetDeliveryLocations(): void
    {
        $userId = 1;
        
        $this->locationRepository->shouldReceive('getLocationsForUser')
            ->once()
            ->with($userId)
            ->andReturn([]);
        
        $result = $this->deliveryService->getDeliveryLocations($userId);
        
        $this->assertIsArray($result);
    }
    
    public function testBookThirdPartyDelivery(): void
    {
        $orderId = 1;
        $dto = new OrderDeliveryDTO($orderId);
        
        $order = (object) [
            'pk_order_no' => $orderId,
            'order_date' => '2023-01-01',
            'fk_kitchen_code' => 'K1',
            'order_menu' => 'lunch',
            'customer_name' => 'Test Customer',
            'customer_phone' => '1234567890',
            'ship_address' => 'Test Address',
            'amount' => 100.00
        ];
        
        $this->deliveryRepository->shouldReceive('getOrderById')
            ->once()
            ->with($orderId)
            ->andReturn($order);
        
        $this->thirdPartyDelivery->shouldReceive('book')
            ->once()
            ->andReturn([
                'code' => 201,
                'status' => 'Order placed successfully',
                'time' => '12:30 PM',
                'date' => '2023-01-01'
            ]);
        
        $result = $this->deliveryService->bookThirdPartyDelivery($dto);
        
        $this->assertEquals(201, $result['code']);
        $this->assertEquals('Order placed successfully', $result['status']);
    }
    
    public function testCancelThirdPartyDelivery(): void
    {
        $orderId = 1;
        
        $this->thirdPartyDelivery->shouldReceive('cancel')
            ->once()
            ->with($orderId)
            ->andReturn([
                'code' => 200,
                'status' => 'Order cancelled successfully'
            ]);
        
        $result = $this->deliveryService->cancelThirdPartyDelivery($orderId);
        
        $this->assertTrue($result);
    }
    
    public function testGetThirdPartyDeliveryStatus(): void
    {
        $orderId = 1;
        
        $order = (object) [
            'pk_order_no' => $orderId
        ];
        
        $this->deliveryRepository->shouldReceive('getOrderById')
            ->once()
            ->with($orderId)
            ->andReturn($order);
        
        $this->thirdPartyDelivery->shouldReceive('getStatus')
            ->once()
            ->andReturn([
                'code' => 200,
                'status' => [
                    'status' => 'Delivered',
                    'deliveryguy_name' => 'Test Delivery Guy',
                    'deliveryguy_phone_number' => '1234567890'
                ]
            ]);
        
        $result = $this->deliveryService->getThirdPartyDeliveryStatus($orderId);
        
        $this->assertEquals(200, $result['code']);
        $this->assertEquals('Delivered', $result['status']['status']);
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

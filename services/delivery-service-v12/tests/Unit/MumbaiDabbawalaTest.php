<?php

namespace Tests\Unit;

use App\Models\Customer;
use App\Models\Order;
use App\Services\ThirdPartyDelivery\MumbaiDabbawala;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class MumbaiDabbawalaTest extends TestCase
{
    use RefreshDatabase;
    
    private MumbaiDabbawala $mumbaiDabbawala;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mumbaiDabbawala = new MumbaiDabbawala();
        
        // Mock HTTP responses
        Http::fake([
            'https://api.mumbaidabbawala.in/api/v1/orders/create' => Http::response([
                'message' => 'Order placed successfully',
                'data' => [
                    'tracking_id' => 'MD-12345',
                    'dabba_code' => 'MD-MU-123-456'
                ]
            ], 201),
            'https://api.mumbaidabbawala.in/api/v1/orders/1/cancel' => Http::response([
                'message' => 'Order cancelled successfully'
            ], 200),
            'https://api.mumbaidabbawala.in/api/v1/orders/1/status' => Http::response([
                'status' => 'Delivered',
                'dabbawala_name' => 'Test Dabbawala',
                'dabbawala_phone' => '1234567890',
                'dabba_code' => 'MD-MU-123-456',
                'estimated_delivery_time' => '12:30:00',
                'actual_delivery_time' => '12:25:00'
            ], 200)
        ]);
    }
    
    public function testBookDelivery(): void
    {
        // Create test data
        $order = Order::factory()->create([
            'pk_order_no' => 1,
            'order_date' => '2023-01-01',
            'fk_kitchen_code' => 'K1',
            'order_menu' => 'lunch',
            'customer_name' => 'Test Customer',
            'customer_phone' => '1234567890',
            'ship_address' => 'Test Address',
            'amount' => 100.00,
            'customer_code' => 1
        ]);
        
        $customer = Customer::factory()->create([
            'pk_customer_code' => 1,
            'customer_name' => 'Test Customer',
            'phone' => '1234567890',
            'dabbawala_code' => null
        ]);
        
        // Test booking
        $data = [
            'pk_order_no' => $order->pk_order_no,
            'order_date' => $order->order_date,
            'fk_kitchen_code' => $order->fk_kitchen_code,
            'order_menu' => $order->order_menu,
            'customer_name' => $order->customer_name,
            'customer_phone' => $order->customer_phone,
            'ship_address' => $order->ship_address,
            'amount' => $order->amount,
            'customer_code' => $customer->pk_customer_code
        ];
        
        $result = $this->mumbaiDabbawala->book($data);
        
        // Assert response
        $this->assertEquals(201, $result['code']);
        $this->assertEquals('Order placed successfully', $result['status']);
        $this->assertArrayHasKey('dabba_code', $result);
        $this->assertArrayHasKey('tracking_id', $result);
        
        // Assert customer updated with dabbawala code
        $customer->refresh();
        $this->assertNotNull($customer->dabbawala_code);
        $this->assertEquals('mumbai', $customer->dabbawala_code_type);
        
        // Assert order updated with third-party ID
        $order->refresh();
        $this->assertEquals('MD-12345', $order->tp_delivery_order_id);
    }
    
    public function testCancelDelivery(): void
    {
        // Test cancellation
        $result = $this->mumbaiDabbawala->cancel(1);
        
        // Assert response
        $this->assertEquals(200, $result['code']);
        $this->assertEquals('Order cancelled successfully', $result['status']);
    }
    
    public function testGetDeliveryStatus(): void
    {
        // Test get status
        $result = $this->mumbaiDabbawala->getStatus(1);
        
        // Assert response
        $this->assertEquals(200, $result['code']);
        $this->assertEquals('Delivered', $result['status']['status']);
        $this->assertEquals('Test Dabbawala', $result['status']['dabbawala_name']);
        $this->assertEquals('1234567890', $result['status']['dabbawala_phone']);
        $this->assertEquals('MD-MU-123-456', $result['status']['dabba_code']);
    }
    
    public function testGenerateDabbaCode(): void
    {
        // Create test data
        $order = Order::factory()->create([
            'pk_order_no' => 2,
            'location_code' => 1
        ]);
        
        // Test booking to trigger code generation
        $data = [
            'pk_order_no' => $order->pk_order_no,
            'order_date' => '2023-01-01',
            'fk_kitchen_code' => 'K1',
            'order_menu' => 'lunch',
            'customer_name' => 'Test Customer',
            'customer_phone' => '1234567890',
            'ship_address' => 'Test Address',
            'amount' => 100.00
        ];
        
        $result = $this->mumbaiDabbawala->book($data);
        
        // Assert dabba code format
        $this->assertArrayHasKey('dabba_code', $result);
        $this->assertMatchesRegularExpression('/^MD-[A-Z]{2}-\d+-\d{3}$/', $result['dabba_code']);
    }
    
    public function testUseExistingDabbaCode(): void
    {
        // Create test data with existing dabbawala code
        $customer = Customer::factory()->create([
            'pk_customer_code' => 3,
            'dabbawala_code' => 'MD-MU-3-123',
            'dabbawala_code_type' => 'mumbai'
        ]);
        
        $order = Order::factory()->create([
            'pk_order_no' => 3,
            'customer_code' => $customer->pk_customer_code
        ]);
        
        // Test booking
        $data = [
            'pk_order_no' => $order->pk_order_no,
            'order_date' => '2023-01-01',
            'fk_kitchen_code' => 'K1',
            'order_menu' => 'lunch',
            'customer_name' => 'Test Customer',
            'customer_phone' => '1234567890',
            'ship_address' => 'Test Address',
            'amount' => 100.00,
            'customer_code' => $customer->pk_customer_code
        ];
        
        $result = $this->mumbaiDabbawala->book($data);
        
        // Assert existing dabba code is used
        $this->assertEquals('MD-MU-3-123', $result['dabba_code']);
    }
}

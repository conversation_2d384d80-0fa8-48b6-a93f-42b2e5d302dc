<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\DeliveryLocation;
use App\Models\DeliveryRoute;
use App\Models\Order;
use App\Models\User;
use App\Services\GeocodingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class MapControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create and authenticate a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    public function testGetDeliveryLocations()
    {
        // Create test data
        $locations = DeliveryLocation::factory()->count(3)->create([
            'latitude' => 19.0760,
            'longitude' => 72.8777,
        ]);
        
        // Create a location without coordinates (should not be returned)
        DeliveryLocation::factory()->create([
            'latitude' => null,
            'longitude' => null,
        ]);
        
        // Create an inactive location (should not be returned)
        DeliveryLocation::factory()->create([
            'latitude' => 19.0760,
            'longitude' => 72.8777,
            'status' => false,
        ]);
        
        $response = $this->getJson('/api/v2/delivery/map/delivery-locations');
        
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'address',
                        'city',
                        'pin',
                        'coordinates' => [
                            'lat',
                            'lng',
                        ],
                        'is_kitchen',
                        'is_default',
                        'status',
                    ],
                ],
            ]);
    }

    public function testGetCustomers()
    {
        // Create test data
        $customers = Customer::factory()->count(3)->create([
            'latitude' => 19.0760,
            'longitude' => 72.8777,
        ]);
        
        // Create a customer without coordinates (should not be returned)
        Customer::factory()->create([
            'latitude' => null,
            'longitude' => null,
        ]);
        
        // Create an inactive customer (should not be returned)
        Customer::factory()->create([
            'latitude' => 19.0760,
            'longitude' => 72.8777,
            'status' => false,
        ]);
        
        $response = $this->getJson('/api/v2/delivery/map/customers');
        
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'address',
                        'phone',
                        'email',
                        'coordinates' => [
                            'lat',
                            'lng',
                        ],
                        'food_preference',
                        'dabbawala',
                        'location',
                        'is_kitchen',
                    ],
                ],
            ]);
    }

    public function testGetActiveOrders()
    {
        // Create test data
        $kitchen = DeliveryLocation::factory()->create([
            'latitude' => 19.0760,
            'longitude' => 72.8777,
        ]);
        
        $customer = Customer::factory()->create([
            'latitude' => 19.1000,
            'longitude' => 72.9000,
        ]);
        
        $order = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'fk_kitchen_code' => $kitchen->pk_location_code,
            'delivery_status' => 'Pending',
            'delivery_type' => 'delivery',
            'order_date' => date('Y-m-d'),
        ]);
        
        $route = DeliveryRoute::factory()->create([
            'order_id' => $order->pk_order_no,
            'kitchen_id' => $kitchen->pk_location_code,
            'start_lat' => $kitchen->latitude,
            'start_lon' => $kitchen->longitude,
            'end_lat' => $customer->latitude,
            'end_lon' => $customer->longitude,
        ]);
        
        // Create an order with a different date (should not be returned)
        $otherOrder = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'fk_kitchen_code' => $kitchen->pk_location_code,
            'delivery_status' => 'Pending',
            'delivery_type' => 'delivery',
            'order_date' => date('Y-m-d', strtotime('-1 day')),
        ]);
        
        DeliveryRoute::factory()->create([
            'order_id' => $otherOrder->pk_order_no,
            'kitchen_id' => $kitchen->pk_location_code,
        ]);
        
        $response = $this->getJson('/api/v2/delivery/map/active-orders');
        
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'order_no',
                        'customer',
                        'kitchen',
                        'order_date',
                        'meal_type',
                        'delivery_status',
                        'order_status',
                        'amount',
                        'route',
                    ],
                ],
            ]);
    }

    public function testGetDeliveryRoute()
    {
        // Create test data
        $kitchen = DeliveryLocation::factory()->create([
            'latitude' => 19.0760,
            'longitude' => 72.8777,
        ]);
        
        $customer = Customer::factory()->create([
            'latitude' => 19.1000,
            'longitude' => 72.9000,
        ]);
        
        $order = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'fk_kitchen_code' => $kitchen->pk_location_code,
        ]);
        
        $route = DeliveryRoute::factory()->create([
            'order_id' => $order->pk_order_no,
            'kitchen_id' => $kitchen->pk_location_code,
            'start_lat' => $kitchen->latitude,
            'start_lon' => $kitchen->longitude,
            'end_lat' => $customer->latitude,
            'end_lon' => $customer->longitude,
            'distance_km' => 5.0,
            'duration_seconds' => 1200,
        ]);
        
        $response = $this->getJson('/api/v2/delivery/map/delivery-route/' . $order->pk_order_no);
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'order_id',
                'kitchen_id',
                'delivery_person_id',
                'distance_km',
                'duration_seconds',
                'duration_minutes',
                'start_coordinates',
                'end_coordinates',
                'estimated_delivery_time',
                'formatted_estimated_delivery_time',
                'actual_delivery_time',
                'formatted_actual_delivery_time',
                'traffic_condition',
                'delivery_zone',
                'delivery_fee',
                'route_geometry',
            ])
            ->assertJson([
                'order_id' => $order->pk_order_no,
                'kitchen_id' => $kitchen->pk_location_code,
                'distance_km' => 5.0,
                'duration_seconds' => 1200,
                'duration_minutes' => 20,
            ]);
    }

    public function testGeocodeAddress()
    {
        // Mock the GeocodingService
        $this->mock(GeocodingService::class, function ($mock) {
            $mock->shouldReceive('geocode')
                ->once()
                ->with('123 Test Street', 'Mumbai', 'India')
                ->andReturn([
                    'lat' => 19.0760,
                    'lon' => 72.8777,
                    'display_name' => 'Test Address, Mumbai, India',
                    'address_components' => [
                        'city' => 'Mumbai',
                        'country' => 'India',
                    ],
                ]);
        });
        
        $response = $this->postJson('/api/v2/delivery/map/geocode', [
            'address' => '123 Test Street',
            'city' => 'Mumbai',
            'country' => 'India',
        ]);
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'lat',
                    'lon',
                    'display_name',
                    'address_components',
                ],
            ])
            ->assertJson([
                'message' => 'Address geocoded successfully',
                'data' => [
                    'lat' => 19.0760,
                    'lon' => 72.8777,
                    'display_name' => 'Test Address, Mumbai, India',
                ],
            ]);
    }

    public function testUpdateCustomerCoordinates()
    {
        // Create test data
        $customer = Customer::factory()->create([
            'latitude' => null,
            'longitude' => null,
        ]);
        
        // Mock the GeocodingService
        $this->mock(GeocodingService::class, function ($mock) {
            $mock->shouldReceive('reverseGeocode')
                ->once()
                ->with(19.0760, 72.8777)
                ->andReturn([
                    'display_name' => 'Test Address, Mumbai, India',
                    'address_components' => [
                        'city' => 'Mumbai',
                        'country' => 'India',
                    ],
                    'lat' => 19.0760,
                    'lon' => 72.8777,
                ]);
        });
        
        $response = $this->putJson('/api/v2/delivery/map/customer/' . $customer->pk_customer_code . '/coordinates', [
            'latitude' => 19.0760,
            'longitude' => 72.8777,
        ]);
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'coordinates',
                ],
            ])
            ->assertJson([
                'message' => 'Customer coordinates updated successfully',
                'data' => [
                    'id' => $customer->pk_customer_code,
                    'coordinates' => [
                        'lat' => 19.0760,
                        'lng' => 72.8777,
                    ],
                ],
            ]);
        
        // Check that the database was updated
        $this->assertDatabaseHas('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
            'latitude' => 19.0760,
            'longitude' => 72.8777,
            'geocoded_address' => 'Test Address, Mumbai, India',
        ]);
    }
}

<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\User;
use App\Services\ThirdPartyDelivery\MumbaiDabbawala;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class MumbaiDabbawalaApiTest extends TestCase
{
    use RefreshDatabase;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set Mumbai Dabbawala as the default provider for testing
        Config::set('delivery.default_provider', 'mumbaidabbawala');
    }
    
    public function testBookMumbaiDabbawalaDelivery(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        $order = Order::factory()->create();
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the MumbaiDabbawala service
        $this->mock(MumbaiDabbawala::class)
            ->shouldReceive('book')
            ->andReturn([
                'code' => 201,
                'status' => 'Order placed successfully',
                'dabba_code' => 'MD-MU-123-456',
                'tracking_id' => 'MD-12345',
                'time' => '12:30 PM',
                'date' => '2023-01-01'
            ]);
        
        $response = $this->postJson('/api/v2/delivery/third-party/book', [
            'order_id' => $order->pk_order_no
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Third-party delivery booked successfully',
                'data' => [
                    'dabba_code' => 'MD-MU-123-456',
                    'tracking_id' => 'MD-12345'
                ]
            ]);
    }
    
    public function testCancelMumbaiDabbawalaDelivery(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        $order = Order::factory()->create();
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the MumbaiDabbawala service
        $this->mock(MumbaiDabbawala::class)
            ->shouldReceive('cancel')
            ->andReturn([
                'code' => 200,
                'status' => 'Order cancelled successfully'
            ]);
        
        $response = $this->postJson('/api/v2/delivery/third-party/' . $order->pk_order_no . '/cancel');
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Third-party delivery cancelled successfully'
            ]);
    }
    
    public function testGetMumbaiDabbawalaDeliveryStatus(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        $order = Order::factory()->create();
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the MumbaiDabbawala service
        $this->mock(MumbaiDabbawala::class)
            ->shouldReceive('getStatus')
            ->andReturn([
                'code' => 200,
                'status' => [
                    'status' => 'Delivered',
                    'dabbawala_name' => 'Test Dabbawala',
                    'dabbawala_phone' => '1234567890',
                    'dabba_code' => 'MD-MU-123-456',
                    'estimated_delivery_time' => '12:30:00',
                    'actual_delivery_time' => '12:25:00'
                ]
            ]);
        
        $response = $this->getJson('/api/v2/delivery/third-party/' . $order->pk_order_no . '/status');
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Third-party delivery status retrieved successfully',
                'data' => [
                    'status' => [
                        'status' => 'Delivered',
                        'dabbawala_name' => 'Test Dabbawala',
                        'dabba_code' => 'MD-MU-123-456'
                    ]
                ]
            ]);
    }
}

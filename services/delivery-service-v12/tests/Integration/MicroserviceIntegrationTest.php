<?php

namespace Tests\Integration;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class MicroserviceIntegrationTest extends TestCase
{
    use RefreshDatabase;
    
    /**
     * Test integration with Auth Service.
     */
    public function testAuthServiceIntegration(): void
    {
        // Mock the Auth Service response
        Http::fake([
            'http://auth-service:8000/api/v2/auth/validate-token' => Http::response([
                'success' => true,
                'data' => [
                    'user_id' => 1,
                    'roles' => ['delivery']
                ]
            ], 200)
        ]);
        
        // Make a request to our API that requires authentication
        $response = $this->withHeaders([
            'Authorization' => 'Bearer test-token'
        ])->getJson('/api/v2/delivery/orders');
        
        // Assert that the request was successful
        $response->assertStatus(200);
    }
    
    /**
     * Test integration with Order Service.
     */
    public function testOrderServiceIntegration(): void
    {
        // Mock the Auth Service response for authentication
        Http::fake([
            'http://auth-service:8000/api/v2/auth/validate-token' => Http::response([
                'success' => true,
                'data' => [
                    'user_id' => 1,
                    'roles' => ['delivery']
                ]
            ], 200),
            'http://order-service:8000/api/v2/orders/1' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'order_no' => 'ORD-12345',
                    'customer_name' => 'Test Customer',
                    'customer_phone' => '1234567890',
                    'ship_address' => 'Test Address',
                    'order_date' => '2023-01-01',
                    'delivery_status' => 'Dispatched',
                    'order_status' => 'Processing'
                ]
            ], 200)
        ]);
        
        // Make a request to update delivery status
        $response = $this->withHeaders([
            'Authorization' => 'Bearer test-token'
        ])->postJson('/api/v2/delivery/orders/1/delivery-status', [
            'order_id' => 1,
            'order_completed' => true
        ]);
        
        // Assert that the request was successful
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Delivery status updated successfully'
            ]);
    }
    
    /**
     * Test integration with Customer Service.
     */
    public function testCustomerServiceIntegration(): void
    {
        // Mock the Auth Service response for authentication
        Http::fake([
            'http://auth-service:8000/api/v2/auth/validate-token' => Http::response([
                'success' => true,
                'data' => [
                    'user_id' => 1,
                    'roles' => ['delivery']
                ]
            ], 200),
            'http://customer-service:8000/api/v2/customers/1' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'name' => 'Test Customer',
                    'phone' => '1234567890',
                    'email' => '<EMAIL>',
                    'address' => 'Test Address'
                ]
            ], 200)
        ]);
        
        // Make a request to get customer information
        $response = $this->withHeaders([
            'Authorization' => 'Bearer test-token'
        ])->getJson('/api/v2/delivery/customers/1');
        
        // Assert that the request was successful
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'name' => 'Test Customer'
                ]
            ]);
    }
    
    /**
     * Test integration with Kitchen Service.
     */
    public function testKitchenServiceIntegration(): void
    {
        // Mock the Auth Service response for authentication
        Http::fake([
            'http://auth-service:8000/api/v2/auth/validate-token' => Http::response([
                'success' => true,
                'data' => [
                    'user_id' => 1,
                    'roles' => ['delivery']
                ]
            ], 200),
            'http://kitchen-service:8000/api/v2/kitchens/K1/orders/1' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'order_no' => 'ORD-12345',
                    'kitchen_code' => 'K1',
                    'status' => 'Ready for Pickup'
                ]
            ], 200)
        ]);
        
        // Make a request to get kitchen order information
        $response = $this->withHeaders([
            'Authorization' => 'Bearer test-token'
        ])->getJson('/api/v2/delivery/kitchens/K1/orders/1');
        
        // Assert that the request was successful
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'kitchen_code' => 'K1'
                ]
            ]);
    }
}

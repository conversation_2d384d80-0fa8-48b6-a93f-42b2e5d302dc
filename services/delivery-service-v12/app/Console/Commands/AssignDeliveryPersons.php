<?php

namespace App\Console\Commands;

use App\Services\DeliveryOptimizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AssignDeliveryPersons extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delivery:assign-persons {--date= : Date in Y-m-d format (default: today)} {--meal_type= : Meal type (lunch, dinner)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign delivery persons to orders';

    /**
     * The delivery optimization service instance.
     *
     * @var DeliveryOptimizationService
     */
    protected DeliveryOptimizationService $optimizationService;

    /**
     * Create a new command instance.
     */
    public function __construct(DeliveryOptimizationService $optimizationService)
    {
        parent::__construct();
        $this->optimizationService = $optimizationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $date = $this->option('date') ?: date('Y-m-d');
        $mealType = $this->option('meal_type');
        
        if (!$mealType) {
            $mealType = $this->choice(
                'Select meal type',
                ['lunch', 'dinner'],
                0
            );
        }
        
        $this->info("Assigning delivery persons for $date, meal type: $mealType");
        
        $result = $this->optimizationService->assignDeliveryPersons($date, $mealType);
        
        if ($result['success']) {
            $this->info($result['message']);
            return 0;
        } else {
            $this->error($result['message']);
            return 1;
        }
    }
}

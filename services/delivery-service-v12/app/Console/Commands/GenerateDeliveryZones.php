<?php

namespace App\Console\Commands;

use App\Models\DeliveryLocation;
use App\Services\DeliveryZoneService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateDeliveryZones extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delivery:generate-zones {--kitchen_id= : Specific kitchen ID to generate zones for} {--base_fee=30 : Base delivery fee} {--zones=3 : Number of zones to generate} {--radius=5 : Zone radius in kilometers} {--increment=10 : Fee increment per zone}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate delivery zones for kitchens';

    /**
     * The delivery zone service instance.
     *
     * @var DeliveryZoneService
     */
    protected DeliveryZoneService $zoneService;

    /**
     * Create a new command instance.
     */
    public function __construct(DeliveryZoneService $zoneService)
    {
        parent::__construct();
        $this->zoneService = $zoneService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $kitchenId = $this->option('kitchen_id');
        $baseFee = (float) $this->option('base_fee');
        $numberOfZones = (int) $this->option('zones');
        $zoneRadius = (float) $this->option('radius');
        $feeIncrement = (float) $this->option('increment');
        
        if ($kitchenId) {
            // Generate zones for a specific kitchen
            $kitchen = DeliveryLocation::find($kitchenId);
            
            if (!$kitchen) {
                $this->error("Kitchen with ID $kitchenId not found");
                return 1;
            }
            
            $this->generateZonesForKitchen($kitchen, $baseFee, $numberOfZones, $zoneRadius, $feeIncrement);
        } else {
            // Generate zones for all kitchens
            $kitchens = DeliveryLocation::where('status', true)->get();
            
            if ($kitchens->isEmpty()) {
                $this->error("No active kitchens found");
                return 1;
            }
            
            $this->info("Generating delivery zones for {$kitchens->count()} kitchens");
            
            foreach ($kitchens as $kitchen) {
                $this->generateZonesForKitchen($kitchen, $baseFee, $numberOfZones, $zoneRadius, $feeIncrement);
            }
        }
        
        return 0;
    }

    /**
     * Generate zones for a specific kitchen.
     *
     * @param DeliveryLocation $kitchen
     * @param float $baseFee
     * @param int $numberOfZones
     * @param float $zoneRadius
     * @param float $feeIncrement
     * @return void
     */
    protected function generateZonesForKitchen(
        DeliveryLocation $kitchen,
        float $baseFee,
        int $numberOfZones,
        float $zoneRadius,
        float $feeIncrement
    ): void {
        $this->info("Generating zones for kitchen {$kitchen->pk_location_code} ({$kitchen->location})");
        
        if (!$kitchen->hasCoordinates()) {
            $this->warn("Kitchen {$kitchen->pk_location_code} does not have coordinates. Skipping.");
            return;
        }
        
        $zones = $this->zoneService->generateDefaultZones(
            $kitchen->pk_location_code,
            $baseFee,
            $numberOfZones,
            $zoneRadius,
            $feeIncrement
        );
        
        if ($zones) {
            $this->info("Generated {$zones->count()} zones for kitchen {$kitchen->pk_location_code}");
            
            $table = [];
            foreach ($zones as $zone) {
                $table[] = [
                    $zone->zone_number,
                    $zone->name,
                    $zone->distanceRange,
                    $zone->base_delivery_fee,
                    $zone->additional_fee,
                    $zone->totalFee,
                ];
            }
            
            $this->table(
                ['Zone', 'Name', 'Distance Range', 'Base Fee', 'Additional Fee', 'Total Fee'],
                $table
            );
        } else {
            $this->error("Failed to generate zones for kitchen {$kitchen->pk_location_code}");
        }
    }
}

<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\DeliveryLocation;
use App\Services\GeocodingService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GeocodeAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'geocode:addresses {--type=all : Type of addresses to geocode (all, customers, locations)} {--limit=50 : Maximum number of addresses to geocode} {--force : Force geocoding even if coordinates already exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Geocode customer and delivery location addresses';

    /**
     * The geocoding service instance.
     *
     * @var GeocodingService
     */
    protected GeocodingService $geocodingService;

    /**
     * Create a new command instance.
     */
    public function __construct(GeocodingService $geocodingService)
    {
        parent::__construct();
        $this->geocodingService = $geocodingService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $limit = (int) $this->option('limit');
        $force = $this->option('force');
        
        $this->info("Geocoding addresses (type: $type, limit: $limit, force: " . ($force ? 'yes' : 'no') . ")");
        
        if ($type === 'all' || $type === 'customers') {
            $this->geocodeCustomerAddresses($limit, $force);
        }
        
        if ($type === 'all' || $type === 'locations') {
            $this->geocodeLocationAddresses($limit, $force);
        }
        
        return 0;
    }

    /**
     * Geocode customer addresses.
     *
     * @param int $limit
     * @param bool $force
     * @return void
     */
    protected function geocodeCustomerAddresses(int $limit, bool $force): void
    {
        $query = Customer::where('status', true);
        
        if (!$force) {
            $query->whereNull('latitude')->orWhereNull('longitude');
        }
        
        $customers = $query->limit($limit)->get();
        
        $this->info("Found {$customers->count()} customer addresses to geocode");
        
        $bar = $this->output->createProgressBar($customers->count());
        $bar->start();
        
        $successCount = 0;
        $failCount = 0;
        
        foreach ($customers as $customer) {
            if (empty($customer->customer_Address)) {
                $this->warn("Customer {$customer->pk_customer_code} has no address");
                $failCount++;
                $bar->advance();
                continue;
            }
            
            $result = $this->geocodingService->geocode(
                $customer->customer_Address,
                $customer->location->city ?? null
            );
            
            if ($result) {
                $customer->update([
                    'latitude' => $result['lat'],
                    'longitude' => $result['lon'],
                    'geocoded_address' => $result['display_name'],
                ]);
                
                $successCount++;
            } else {
                $this->warn("Failed to geocode address for customer {$customer->pk_customer_code}");
                $failCount++;
            }
            
            $bar->advance();
            
            // Add a small delay to avoid rate limiting
            usleep(200000); // 200ms
        }
        
        $bar->finish();
        $this->newLine(2);
        
        $this->info("Geocoded $successCount customer addresses successfully, $failCount failed");
    }

    /**
     * Geocode delivery location addresses.
     *
     * @param int $limit
     * @param bool $force
     * @return void
     */
    protected function geocodeLocationAddresses(int $limit, bool $force): void
    {
        $query = DeliveryLocation::where('status', true);
        
        if (!$force) {
            $query->whereNull('latitude')->orWhereNull('longitude');
        }
        
        $locations = $query->limit($limit)->get();
        
        $this->info("Found {$locations->count()} delivery location addresses to geocode");
        
        $bar = $this->output->createProgressBar($locations->count());
        $bar->start();
        
        $successCount = 0;
        $failCount = 0;
        
        foreach ($locations as $location) {
            $address = $location->fullAddress;
            
            if (empty($address)) {
                $this->warn("Location {$location->pk_location_code} has no address");
                $failCount++;
                $bar->advance();
                continue;
            }
            
            $result = $this->geocodingService->geocode($address, $location->city);
            
            if ($result) {
                $location->update([
                    'latitude' => $result['lat'],
                    'longitude' => $result['lon'],
                    'geocoded_address' => $result['display_name'],
                ]);
                
                $successCount++;
            } else {
                $this->warn("Failed to geocode address for location {$location->pk_location_code}");
                $failCount++;
            }
            
            $bar->advance();
            
            // Add a small delay to avoid rate limiting
            usleep(200000); // 200ms
        }
        
        $bar->finish();
        $this->newLine(2);
        
        $this->info("Geocoded $successCount delivery location addresses successfully, $failCount failed");
    }
}

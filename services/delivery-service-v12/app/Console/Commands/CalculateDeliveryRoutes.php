<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Services\DeliveryOptimizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CalculateDeliveryRoutes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delivery:calculate-routes {--date= : Date in Y-m-d format (default: today)} {--meal_type= : Meal type (lunch, dinner)} {--order_id= : Calculate route for a specific order ID} {--force : Force recalculation of existing routes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate delivery routes for orders';

    /**
     * The delivery optimization service instance.
     *
     * @var DeliveryOptimizationService
     */
    protected DeliveryOptimizationService $optimizationService;

    /**
     * Create a new command instance.
     */
    public function __construct(DeliveryOptimizationService $optimizationService)
    {
        parent::__construct();
        $this->optimizationService = $optimizationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $date = $this->option('date') ?: date('Y-m-d');
        $mealType = $this->option('meal_type');
        $orderId = $this->option('order_id');
        $force = $this->option('force');
        
        if ($orderId) {
            // Calculate route for a specific order
            $this->info("Calculating route for order $orderId");
            
            $route = $this->optimizationService->calculateOrderRoute($orderId);
            
            if ($route) {
                $this->info("Route calculated successfully");
                $this->table(
                    ['Distance (km)', 'Duration (min)', 'Estimated Delivery Time', 'Delivery Fee'],
                    [
                        [
                            $route->distance_km,
                            $route->durationMinutes,
                            $route->formattedEstimatedDeliveryTime,
                            $route->delivery_fee,
                        ],
                    ]
                );
            } else {
                $this->error("Failed to calculate route for order $orderId");
                return 1;
            }
        } else {
            // Calculate routes for multiple orders
            $query = Order::where('delivery_type', 'delivery')
                ->where('order_status', '!=', 'Cancelled');
            
            if ($date) {
                $query->where('order_date', $date);
            }
            
            if ($mealType) {
                $query->where('order_menu', $mealType);
            }
            
            if (!$force) {
                $query->whereDoesntHave('deliveryRoute');
            }
            
            $orders = $query->get();
            
            if ($orders->isEmpty()) {
                $this->info("No orders found that need route calculation");
                return 0;
            }
            
            $this->info("Calculating routes for {$orders->count()} orders");
            
            $bar = $this->output->createProgressBar($orders->count());
            $bar->start();
            
            $successCount = 0;
            $failCount = 0;
            
            foreach ($orders as $order) {
                $route = $this->optimizationService->calculateOrderRoute($order->pk_order_no);
                
                if ($route) {
                    $successCount++;
                } else {
                    $failCount++;
                }
                
                $bar->advance();
            }
            
            $bar->finish();
            $this->newLine(2);
            
            $this->info("Calculated $successCount routes successfully, $failCount failed");
        }
        
        return 0;
    }
}

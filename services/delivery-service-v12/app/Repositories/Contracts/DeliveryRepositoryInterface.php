<?php

namespace App\Repositories\Contracts;

interface DeliveryRepositoryInterface
{
    public function getOrdersForDelivery(int $userId, int $locationId, string $date): array;
    
    public function searchOrders(int $userId, string $searchTerm, int $locationId): array;
    
    public function updateDeliveryStatus(int $orderId, int $userId, bool $orderCompleted): bool;
    
    public function getOrderById(int $orderId): ?object;
}

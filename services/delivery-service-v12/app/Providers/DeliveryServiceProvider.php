<?php

namespace App\Providers;

use App\Repositories\Contracts\DeliveryRepositoryInterface;
use App\Repositories\Contracts\LocationRepositoryInterface;
use App\Repositories\DeliveryRepository;
use App\Repositories\LocationRepository;
use App\Services\Contracts\DeliveryServiceInterface;
use App\Services\DeliveryService;
use App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface;
use App\Services\ThirdPartyDelivery\YourGuy;
use App\Services\ThirdPartyDelivery\MumbaiDabbawala;
use Illuminate\Support\ServiceProvider;

class DeliveryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register repositories
        $this->app->bind(DeliveryRepositoryInterface::class, DeliveryRepository::class);
        $this->app->bind(LocationRepositoryInterface::class, LocationRepository::class);
        
        // Register services
        $this->app->bind(DeliveryServiceInterface::class, DeliveryService::class);
        
        // Bind the appropriate third-party delivery provider based on configuration
        $thirdPartyProvider = config('delivery.default_provider', 'yourguy');
        
        if ($thirdPartyProvider === 'mumbaidabbawala') {
            $this->app->bind(TPDeliveryInterface::class, MumbaiDabbawala::class);
        } elseif ($thirdPartyProvider === 'roadrunner') {
            // If RoadRunner implementation exists
            // $this->app->bind(TPDeliveryInterface::class, RoadRunner::class);
            $this->app->bind(TPDeliveryInterface::class, YourGuy::class); // Fallback to YourGuy for now
        } else {
            $this->app->bind(TPDeliveryInterface::class, YourGuy::class);
        }
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

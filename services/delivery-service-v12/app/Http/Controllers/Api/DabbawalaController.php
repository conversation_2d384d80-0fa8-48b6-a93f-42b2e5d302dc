<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ThirdPartyDelivery\MumbaiDabbawala;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * @group Dabbawala Management
 *
 * APIs for managing Mumbai Dabbawala delivery codes
 */
class DabbawalaController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // No dependency injection needed
    }

    /**
     * Generate a new dabbawala code for an order.
     *
     * @bodyParam order_id integer required The ID of the order. Example: 2
     * @bodyParam pickup_location string required The pickup location. Example: "Kitchen, Andheri West"
     * @bodyParam delivery_location string required The delivery location. Example: "Office Complex, Bandra West"
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|integer',
            'pickup_location' => 'required|string|max:255',
            'delivery_location' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $orderId = $request->input('order_id');
        $pickupLocation = $request->input('pickup_location');
        $deliveryLocation = $request->input('delivery_location');

        try {
            // Get customer ID from order
            $customerId = null;
            try {
                $order = \App\Models\Order::find($orderId);
                if ($order) {
                    $customerId = $order->customer_code;
                }
            } catch (\Exception $e) {
                Log::warning('Could not retrieve order for dabbawala code generation', [
                    'order_id' => $orderId,
                    'error' => $e->getMessage()
                ]);
            }

            // Generate a new dabbawala code
            $mumbaiDabbawala = new MumbaiDabbawala();
            $dabbaCode = $mumbaiDabbawala->generateDabbaCode($orderId, $customerId);

            if ($dabbaCode) {
                return response()->json([
                    'message' => 'Dabbawala code generated successfully',
                    'data' => [
                        'order_id' => $orderId,
                        'dabbawala_code' => $dabbaCode,
                        'pickup_location' => $pickupLocation,
                        'delivery_location' => $deliveryLocation,
                        'generated_at' => now()->toISOString(),
                    ],
                ]);
            }

            return response()->json([
                'message' => 'Failed to generate dabbawala code',
            ], 500);
        } catch (\Exception $e) {
            Log::error('Error generating dabbawala code', [
                'order_id' => $orderId,
                'pickup_location' => $pickupLocation,
                'delivery_location' => $deliveryLocation,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'An error occurred while generating dabbawala code',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

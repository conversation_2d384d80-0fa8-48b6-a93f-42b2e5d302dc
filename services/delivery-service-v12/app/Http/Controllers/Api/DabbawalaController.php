<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ThirdPartyDelivery\MumbaiDabbawala;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * @group Dabbawala Management
 *
 * APIs for managing Mumbai Dabbawala delivery codes
 */
class DabbawalaController extends Controller
{
    /**
     * The Mumbai Dabbawala service instance.
     */
    protected MumbaiDabbawala $mumbaiDabbawala;

    /**
     * Create a new controller instance.
     */
    public function __construct(MumbaiDabbawala $mumbaiDabbawala)
    {
        $this->mumbaiDabbawala = $mumbaiDabbawala;
    }

    /**
     * Generate a new dabbawala code for a customer.
     *
     * @bodyParam customer_id integer required The ID of the customer. Example: 1
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $customerId = $request->input('customer_id');
        
        try {
            // Generate a new dabbawala code
            $dabbaCode = $this->mumbaiDabbawala->generateDabbaCode($customerId);
            
            if ($dabbaCode) {
                return response()->json([
                    'message' => 'Dabbawala code generated successfully',
                    'data' => [
                        'dabbawala_code' => $dabbaCode,
                    ],
                ]);
            }
            
            return response()->json([
                'message' => 'Failed to generate dabbawala code',
            ], 500);
        } catch (\Exception $e) {
            Log::error('Error generating dabbawala code', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'message' => 'An error occurred while generating dabbawala code',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

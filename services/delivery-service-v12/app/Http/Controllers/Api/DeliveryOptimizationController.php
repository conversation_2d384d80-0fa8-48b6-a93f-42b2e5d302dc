<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DeliveryRouteResource;
use App\Services\DeliveryOptimizationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * @group Delivery Optimization
 *
 * APIs for optimizing delivery routes and assignments
 */
class DeliveryOptimizationController extends Controller
{
    /**
     * The delivery optimization service instance.
     *
     * @var DeliveryOptimizationService
     */
    protected DeliveryOptimizationService $optimizationService;

    /**
     * Create a new controller instance.
     *
     * @param DeliveryOptimizationService $optimizationService
     */
    public function __construct(DeliveryOptimizationService $optimizationService)
    {
        $this->optimizationService = $optimizationService;
    }

    /**
     * Calculate a route for an order.
     *
     * @param int $orderId
     * @return JsonResponse
     */
    public function calculateOrderRoute(int $orderId): JsonResponse
    {
        try {
            $route = $this->optimizationService->calculateOrderRoute($orderId);
            
            if (!$route) {
                return response()->json([
                    'message' => 'Failed to calculate route for order',
                ], 500);
            }
            
            return response()->json([
                'message' => 'Route calculated successfully',
                'data' => new DeliveryRouteResource($route),
            ]);
        } catch (\Exception $e) {
            Log::error('Exception calculating order route: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to calculate route for order',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Assign delivery persons to orders for a specific date and meal type.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function assignDeliveryPersons(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:Y-m-d',
            'meal_type' => 'required|string|in:lunch,dinner',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $date = $request->input('date');
        $mealType = $request->input('meal_type');
        
        $result = $this->optimizationService->assignDeliveryPersons($date, $mealType);
        
        return response()->json([
            'message' => $result['message'],
            'success' => $result['success'],
            'assigned_count' => $result['assigned_count'],
        ], $result['success'] ? 200 : 500);
    }

    /**
     * Calculate routes for all unassigned orders.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateAllRoutes(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:Y-m-d',
            'meal_type' => 'required|string|in:lunch,dinner',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $date = $request->input('date');
        $mealType = $request->input('meal_type');
        
        try {
            // Get all orders for the date and meal type
            $orders = \App\Models\Order::where('order_date', $date)
                ->where('order_menu', $mealType)
                ->where('delivery_type', 'delivery')
                ->where('order_status', '!=', 'Cancelled')
                ->whereDoesntHave('deliveryRoute')
                ->get();
            
            if ($orders->isEmpty()) {
                return response()->json([
                    'message' => 'No orders found that need route calculation',
                    'calculated_count' => 0,
                ]);
            }
            
            $calculatedCount = 0;
            $failedCount = 0;
            
            foreach ($orders as $order) {
                $route = $this->optimizationService->calculateOrderRoute($order->pk_order_no);
                
                if ($route) {
                    $calculatedCount++;
                } else {
                    $failedCount++;
                }
            }
            
            return response()->json([
                'message' => "Calculated routes for $calculatedCount orders, failed for $failedCount orders",
                'calculated_count' => $calculatedCount,
                'failed_count' => $failedCount,
            ]);
        } catch (\Exception $e) {
            Log::error('Exception calculating all routes: ' . $e->getMessage(), [
                'date' => $date,
                'meal_type' => $mealType,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to calculate routes',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

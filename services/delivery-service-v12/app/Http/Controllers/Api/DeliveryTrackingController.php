<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\DeliveryTracking\UpdateDeliveryStatusRequest;
use App\Http\Requests\DeliveryTracking\UpdateLocationRequest;
use App\Http\Requests\DeliveryTracking\UploadDeliveryProofRequest;
use App\Http\Resources\DeliveryProofResource;
use App\Http\Resources\DeliveryTrackingResource;
use App\Http\Resources\OrderMapResource;
use App\Models\DeliveryPerson;
use App\Models\DeliveryProof;
use App\Models\DeliveryTracking;
use App\Models\Order;
use App\Services\DeliveryTrackingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

/**
 * @group Delivery Tracking
 *
 * APIs for real-time delivery tracking
 */
class DeliveryTrackingController extends Controller
{
    /**
     * The delivery tracking service instance.
     *
     * @var DeliveryTrackingService
     */
    protected DeliveryTrackingService $trackingService;

    /**
     * Create a new controller instance.
     *
     * @param DeliveryTrackingService $trackingService
     */
    public function __construct(DeliveryTrackingService $trackingService)
    {
        $this->trackingService = $trackingService;
    }

    /**
     * Get active deliveries.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getActiveDeliveries(Request $request): AnonymousResourceCollection
    {
        $query = Order::query();
        
        // Filter by delivery status
        $query->whereIn('delivery_status', ['Pending', 'Dispatched', 'In Transit']);
        
        // Filter by date
        if ($request->has('date')) {
            $query->where('order_date', $request->input('date'));
        } else {
            $query->where('order_date', date('Y-m-d'));
        }
        
        // Filter by meal type
        if ($request->has('meal_type')) {
            $query->where('order_menu', $request->input('meal_type'));
        }
        
        // Filter by delivery person
        if ($request->has('delivery_person_id')) {
            $deliveryPerson = DeliveryPerson::find($request->input('delivery_person_id'));
            
            if ($deliveryPerson) {
                $query->where('delivery_person', $deliveryPerson->user_id);
            }
        }
        
        // Filter by location
        if ($request->has('location_id')) {
            $query->where('fk_kitchen_code', $request->input('location_id'));
        }
        
        // Include relationships
        $query->with(['customer', 'location', 'deliveryPerson', 'deliveryRoute']);
        
        // Get the orders
        $orders = $query->get();
        
        return OrderMapResource::collection($orders);
    }

    /**
     * Get delivery tracking for an order.
     *
     * @param int $orderId
     * @return DeliveryTrackingResource|JsonResponse
     */
    public function getDeliveryTracking(int $orderId): DeliveryTrackingResource|JsonResponse
    {
        try {
            $tracking = $this->trackingService->getDeliveryTracking($orderId);
            
            if (!$tracking) {
                return response()->json([
                    'message' => 'Delivery tracking not found',
                ], 404);
            }
            
            return new DeliveryTrackingResource($tracking);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery tracking: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery tracking',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery status.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $orderId
     * @return JsonResponse
     */
    public function updateDeliveryStatus(UpdateDeliveryStatusRequest $request, int $orderId): JsonResponse
    {
        try {
            $result = $this->trackingService->updateDeliveryStatus(
                $orderId,
                $request->input('status'),
                $request->input('notes')
            );
            
            return response()->json([
                'message' => 'Delivery status updated successfully',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update delivery status: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to update delivery status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery person location.
     *
     * @param UpdateLocationRequest $request
     * @param int $deliveryPersonId
     * @return JsonResponse
     */
    public function updateLocation(UpdateLocationRequest $request, int $deliveryPersonId): JsonResponse
    {
        try {
            $result = $this->trackingService->updateDeliveryPersonLocation(
                $deliveryPersonId,
                $request->input('latitude'),
                $request->input('longitude')
            );
            
            return response()->json([
                'message' => 'Location updated successfully',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update delivery person location: ' . $e->getMessage(), [
                'delivery_person_id' => $deliveryPersonId,
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to update location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload delivery proof.
     *
     * @param UploadDeliveryProofRequest $request
     * @param int $orderId
     * @return JsonResponse
     */
    public function uploadDeliveryProof(UploadDeliveryProofRequest $request, int $orderId): JsonResponse
    {
        try {
            $proof = $this->trackingService->uploadDeliveryProof(
                $orderId,
                $request->input('proof_type'),
                $request->input('image'),
                $request->input('notes')
            );
            
            return response()->json([
                'message' => 'Delivery proof uploaded successfully',
                'data' => new DeliveryProofResource($proof),
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to upload delivery proof: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'request' => $request->except('image'),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to upload delivery proof',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery proofs for an order.
     *
     * @param int $orderId
     * @return AnonymousResourceCollection|JsonResponse
     */
    public function getDeliveryProofs(int $orderId): AnonymousResourceCollection|JsonResponse
    {
        try {
            $proofs = DeliveryProof::where('order_id', $orderId)
                ->orderBy('created_at', 'desc')
                ->get();
            
            return DeliveryProofResource::collection($proofs);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery proofs: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery proofs',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery tracking dashboard data.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDashboardData(Request $request): JsonResponse
    {
        try {
            $date = $request->input('date', date('Y-m-d'));
            $mealType = $request->input('meal_type');
            $locationId = $request->input('location_id');
            
            $data = $this->trackingService->getDashboardData($date, $mealType, $locationId);
            
            return response()->json([
                'message' => 'Dashboard data retrieved successfully',
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get dashboard data: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get dashboard data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

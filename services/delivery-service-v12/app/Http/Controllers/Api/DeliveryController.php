<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DeliveryLocationResource;
use App\Http\Resources\DeliveryPersonResource;
use App\Http\Resources\OrderResource;
use App\Models\DeliveryLocation;
use App\Models\DeliveryPerson;
use App\Models\Order;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * @group Delivery Management
 *
 * APIs for managing delivery operations
 */
class DeliveryController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Constructor simplified for now
    }

    /**
     * Get all delivery locations.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getLocations(Request $request): AnonymousResourceCollection
    {
        $query = DeliveryLocation::query();
        
        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->boolean('status'));
        }
        
        // Filter by city
        if ($request->has('city')) {
            $query->where('city', 'like', '%' . $request->input('city') . '%');
        }
        
        // Search by location name
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('location', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('sub_city_area', 'like', "%{$search}%");
            });
        }
        
        $locations = $query->orderBy('location')
                          ->paginate($request->input('per_page', 15));
        
        return DeliveryLocationResource::collection($locations);
    }

    /**
     * Get all delivery persons.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getDeliveryPersons(Request $request): AnonymousResourceCollection
    {
        $query = DeliveryPerson::query();
        
        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->boolean('status'));
        }
        
        // Filter by on duty status
        if ($request->has('on_duty')) {
            $query->where('on_duty', $request->boolean('on_duty'));
        }
        
        // Filter by location
        if ($request->has('location_id')) {
            $locationId = $request->input('location_id');
            $query->whereHas('serviceAreas', function ($q) use ($locationId) {
                $q->where('location_id', $locationId)
                  ->where('is_active', true);
            });
        }
        
        $persons = $query->with(['user', 'serviceAreas.location'])
                        ->orderBy('name')
                        ->paginate($request->input('per_page', 15));
        
        return DeliveryPersonResource::collection($persons);
    }

    /**
     * Get orders for delivery.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getOrders(Request $request): AnonymousResourceCollection
    {
        $query = Order::query();
        
        // Filter by order status
        if ($request->has('status')) {
            $query->where('order_status', $request->input('status'));
        }

        // Filter by delivery status
        if ($request->has('delivery_status')) {
            $query->where('delivery_status', $request->input('delivery_status'));
        }
        
        // Filter by delivery date
        if ($request->has('delivery_date')) {
            $query->whereDate('delivery_time', $request->input('delivery_date'));
        }
        
        // Filter by customer
        if ($request->has('customer_id')) {
            $query->where('customer_id', $request->input('customer_id'));
        }
        
        // Search by order details
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('id', 'like', "%{$search}%")
                  ->orWhere('delivery_address', 'like', "%{$search}%");
            });
        }
        
        $orders = $query->with(['customer'])
                       ->orderBy('created_at', 'desc')
                       ->paginate($request->input('per_page', 15));
        
        return OrderResource::collection($orders);
    }

    /**
     * Update order delivery status.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateOrderStatus(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:pending,confirmed,preparing,ready,out_for_delivery,delivered,cancelled',
            'delivery_notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $order = Order::findOrFail($id);
            
            $order->update([
                'delivery_status' => $request->input('status'),
            ]);

            return response()->json([
                'message' => 'Order status updated successfully',
                'data' => new OrderResource($order),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update order status: ' . $e->getMessage(), [
                'order_id' => $id,
                'request' => $request->all(),
                'exception' => $e,
            ]);

            return response()->json([
                'message' => 'Failed to update order status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Book third-party delivery.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bookThirdPartyDelivery(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|integer|exists:orders,pk_order_no',
            'provider' => 'required|string|in:yourguy,mumbaidabbawala',
            'pickup_address' => 'required|string',
            'delivery_address' => 'required|string',
            'pickup_time' => 'required|date',
            'delivery_time' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // TODO: Implement third-party delivery booking
            $result = [
                'booking_id' => 'TP' . time(),
                'status' => 'booked',
                'provider' => $request->input('provider'),
                'order_id' => $request->input('order_id'),
            ];

            return response()->json([
                'message' => 'Third-party delivery booked successfully',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to book third-party delivery: ' . $e->getMessage(), [
                'request' => $request->validated(),
                'exception' => $e,
            ]);

            return response()->json([
                'message' => 'Failed to book third-party delivery',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel third-party delivery.
     *
     * @param Request $request
     * @param int $orderId
     * @return JsonResponse
     */
    public function cancelThirdPartyDelivery(Request $request, int $orderId): JsonResponse
    {
        try {
            // TODO: Implement third-party delivery cancellation
            $result = [
                'order_id' => $orderId,
                'status' => 'cancelled',
                'cancelled_at' => now(),
            ];

            return response()->json([
                'message' => 'Third-party delivery cancelled successfully',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to cancel third-party delivery: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'exception' => $e,
            ]);

            return response()->json([
                'message' => 'Failed to cancel third-party delivery',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get third-party delivery status.
     *
     * @param int $orderId
     * @return JsonResponse
     */
    public function getThirdPartyDeliveryStatus(int $orderId): JsonResponse
    {
        try {
            // TODO: Implement third-party delivery status retrieval
            $status = [
                'order_id' => $orderId,
                'status' => 'in_transit',
                'provider' => 'yourguy',
                'tracking_id' => 'TRK' . $orderId,
                'estimated_delivery' => now()->addHours(2),
            ];

            return response()->json([
                'message' => 'Third-party delivery status retrieved successfully',
                'data' => $status,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get third-party delivery status: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'exception' => $e,
            ]);

            return response()->json([
                'message' => 'Failed to get third-party delivery status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DeliveryZoneResource;
use App\Models\DeliveryZone;
use App\Services\DeliveryZoneService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * @group Delivery Zone Management
 *
 * APIs for managing delivery zones
 */
class DeliveryZoneController extends Controller
{
    /**
     * The delivery zone service instance.
     *
     * @var DeliveryZoneService
     */
    protected DeliveryZoneService $zoneService;

    /**
     * Create a new controller instance.
     *
     * @param DeliveryZoneService $zoneService
     */
    public function __construct(DeliveryZoneService $zoneService)
    {
        $this->zoneService = $zoneService;
    }

    /**
     * Get all delivery zones.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $activeOnly = $request->input('active_only', true);
        $zones = $this->zoneService->getAllZones($activeOnly);
        
        return DeliveryZoneResource::collection($zones);
    }

    /**
     * Get zones for a specific kitchen.
     *
     * @param int $kitchenId
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getZonesForKitchen(int $kitchenId, Request $request): AnonymousResourceCollection
    {
        $activeOnly = $request->input('active_only', true);
        $zones = $this->zoneService->getZonesForKitchen($kitchenId, $activeOnly);
        
        return DeliveryZoneResource::collection($zones);
    }

    /**
     * Create a new delivery zone.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'kitchen_id' => 'required|integer|exists:delivery_locations,pk_location_code',
            'zone_number' => 'required|integer|min:1',
            'min_distance_km' => 'required|numeric|min:0',
            'max_distance_km' => 'required|numeric|gt:min_distance_km',
            'base_delivery_fee' => 'required|numeric|min:0',
            'additional_fee' => 'nullable|numeric|min:0',
            'is_active' => 'nullable|boolean',
            'description' => 'nullable|string',
            'polygon_coordinates' => 'nullable|array',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $zone = $this->zoneService->createZone($request->all());
        
        if (!$zone) {
            return response()->json([
                'message' => 'Failed to create delivery zone',
            ], 500);
        }
        
        return response()->json([
            'message' => 'Delivery zone created successfully',
            'data' => new DeliveryZoneResource($zone),
        ], 201);
    }

    /**
     * Get a specific delivery zone.
     *
     * @param int $id
     * @return DeliveryZoneResource|JsonResponse
     */
    public function show(int $id): DeliveryZoneResource|JsonResponse
    {
        try {
            $zone = DeliveryZone::findOrFail($id);
            return new DeliveryZoneResource($zone);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Delivery zone not found',
            ], 404);
        }
    }

    /**
     * Update a delivery zone.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'zone_number' => 'nullable|integer|min:1',
            'min_distance_km' => 'nullable|numeric|min:0',
            'max_distance_km' => 'nullable|numeric',
            'base_delivery_fee' => 'nullable|numeric|min:0',
            'additional_fee' => 'nullable|numeric|min:0',
            'is_active' => 'nullable|boolean',
            'description' => 'nullable|string',
            'polygon_coordinates' => 'nullable|array',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Ensure max_distance_km is greater than min_distance_km if both are provided
        if ($request->has('min_distance_km') && $request->has('max_distance_km')) {
            if ($request->input('min_distance_km') >= $request->input('max_distance_km')) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => [
                        'max_distance_km' => ['Maximum distance must be greater than minimum distance'],
                    ],
                ], 422);
            }
        }
        
        $zone = $this->zoneService->updateZone($id, $request->all());
        
        if (!$zone) {
            return response()->json([
                'message' => 'Failed to update delivery zone',
            ], 500);
        }
        
        return response()->json([
            'message' => 'Delivery zone updated successfully',
            'data' => new DeliveryZoneResource($zone),
        ]);
    }

    /**
     * Delete a delivery zone.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $result = $this->zoneService->deleteZone($id);
        
        if (!$result) {
            return response()->json([
                'message' => 'Failed to delete delivery zone',
            ], 500);
        }
        
        return response()->json([
            'message' => 'Delivery zone deleted successfully',
        ]);
    }

    /**
     * Generate default zones for a kitchen.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateDefaultZones(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'kitchen_id' => 'required|integer|exists:delivery_locations,pk_location_code',
            'base_delivery_fee' => 'nullable|numeric|min:0',
            'number_of_zones' => 'nullable|integer|min:1|max:10',
            'zone_radius_km' => 'nullable|numeric|min:0.1',
            'additional_fee_per_zone' => 'nullable|numeric|min:0',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $kitchenId = $request->input('kitchen_id');
        $baseDeliveryFee = $request->input('base_delivery_fee', 30.00);
        $numberOfZones = $request->input('number_of_zones', 3);
        $zoneRadiusKm = $request->input('zone_radius_km', 5.0);
        $additionalFeePerZone = $request->input('additional_fee_per_zone', 10.00);
        
        $zones = $this->zoneService->generateDefaultZones(
            $kitchenId,
            $baseDeliveryFee,
            $numberOfZones,
            $zoneRadiusKm,
            $additionalFeePerZone
        );
        
        if (!$zones) {
            return response()->json([
                'message' => 'Failed to generate default zones',
            ], 500);
        }
        
        return response()->json([
            'message' => 'Default zones generated successfully',
            'data' => DeliveryZoneResource::collection($zones),
        ]);
    }

    /**
     * Check if a customer is within a delivery zone.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkDeliveryZone(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'kitchen_id' => 'required|integer|exists:delivery_locations,pk_location_code',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $kitchenId = $request->input('kitchen_id');
        $latitude = $request->input('latitude');
        $longitude = $request->input('longitude');
        
        $zoneInfo = $this->zoneService->getZoneForCustomer($kitchenId, $latitude, $longitude);
        
        if (!$zoneInfo) {
            return response()->json([
                'message' => 'Failed to determine delivery zone',
            ], 500);
        }
        
        return response()->json([
            'message' => 'Delivery zone check completed',
            'data' => [
                'is_deliverable' => $zoneInfo['is_deliverable'],
                'message' => $zoneInfo['message'],
                'distance_km' => $zoneInfo['distance'],
                'delivery_fee' => $zoneInfo['delivery_fee'],
                'zone' => $zoneInfo['zone'] ? new DeliveryZoneResource($zoneInfo['zone']) : null,
            ],
        ]);
    }
}

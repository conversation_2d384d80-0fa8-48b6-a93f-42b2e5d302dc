<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\DeliveryAssignment\AssignDeliveryRequest;
use App\Http\Requests\DeliveryAssignment\BatchAssignmentRequest;
use App\Http\Requests\DeliveryAssignment\UpdateAssignmentStatusRequest;
use App\Http\Resources\DeliveryAssignmentBatchResource;
use App\Http\Resources\DeliveryAssignmentResource;
use App\Models\DeliveryAssignment;
use App\Models\DeliveryAssignmentBatch;
use App\Services\DeliveryAssignmentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

/**
 * @group Delivery Assignment Management
 *
 * APIs for managing delivery assignments
 */
class DeliveryAssignmentController extends Controller
{
    /**
     * The delivery assignment service instance.
     *
     * @var DeliveryAssignmentService
     */
    protected DeliveryAssignmentService $assignmentService;

    /**
     * Create a new controller instance.
     *
     * @param DeliveryAssignmentService $assignmentService
     */
    public function __construct(DeliveryAssignmentService $assignmentService)
    {
        $this->assignmentService = $assignmentService;
    }

    /**
     * Get all delivery assignments.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = DeliveryAssignment::query();
        
        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        // Filter by assignment type
        if ($request->has('assignment_type')) {
            $query->where('assignment_type', $request->input('assignment_type'));
        }
        
        // Filter by delivery person
        if ($request->has('delivery_person_id')) {
            $query->where('delivery_person_id', $request->input('delivery_person_id'));
        }
        
        // Filter by order
        if ($request->has('order_id')) {
            $query->where('order_id', $request->input('order_id'));
        }
        
        // Filter by batch
        if ($request->has('batch_id')) {
            $query->where('batch_id', $request->input('batch_id'));
        }
        
        // Filter by date range
        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('assigned_at', [$request->input('from_date'), $request->input('to_date')]);
        } elseif ($request->has('from_date')) {
            $query->where('assigned_at', '>=', $request->input('from_date'));
        } elseif ($request->has('to_date')) {
            $query->where('assigned_at', '<=', $request->input('to_date'));
        }
        
        // Default sort by assigned_at desc
        $query->orderBy('assigned_at', 'desc');
        
        $assignments = $query->with(['order', 'deliveryPerson', 'assignedBy', 'batch'])
                            ->paginate($request->input('per_page', 15));
        
        return DeliveryAssignmentResource::collection($assignments);
    }

    /**
     * Get a specific delivery assignment.
     *
     * @param int $id
     * @return DeliveryAssignmentResource|JsonResponse
     */
    public function show(int $id): DeliveryAssignmentResource|JsonResponse
    {
        try {
            $assignment = DeliveryAssignment::with(['order', 'deliveryPerson', 'assignedBy', 'batch', 'notifications'])
                                          ->findOrFail($id);
            
            return new DeliveryAssignmentResource($assignment);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Delivery assignment not found',
            ], 404);
        }
    }

    /**
     * Assign a delivery to a delivery person.
     *
     * @param AssignDeliveryRequest $request
     * @return JsonResponse
     */
    public function assign(AssignDeliveryRequest $request): JsonResponse
    {
        try {
            $assignment = $this->assignmentService->assignDelivery(
                $request->input('order_id'),
                $request->input('delivery_person_id'),
                'manual',
                $request->input('notes'),
                $request->user()->id
            );
            
            return response()->json([
                'message' => 'Delivery assigned successfully',
                'data' => new DeliveryAssignmentResource($assignment),
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to assign delivery: ' . $e->getMessage(), [
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to assign delivery',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery assignment status.
     *
     * @param UpdateAssignmentStatusRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(UpdateAssignmentStatusRequest $request, int $id): JsonResponse
    {
        try {
            $assignment = $this->assignmentService->updateAssignmentStatus(
                $id,
                $request->input('status'),
                $request->input('notes')
            );
            
            return response()->json([
                'message' => 'Assignment status updated successfully',
                'data' => new DeliveryAssignmentResource($assignment),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update assignment status: ' . $e->getMessage(), [
                'id' => $id,
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to update assignment status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a batch assignment.
     *
     * @param BatchAssignmentRequest $request
     * @return JsonResponse
     */
    public function batchAssign(BatchAssignmentRequest $request): JsonResponse
    {
        try {
            $batch = $this->assignmentService->createBatchAssignment(
                $request->input('orders'),
                $request->input('criteria'),
                $request->input('notes'),
                $request->user()->id
            );
            
            return response()->json([
                'message' => 'Batch assignment created successfully',
                'data' => new DeliveryAssignmentBatchResource($batch),
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create batch assignment: ' . $e->getMessage(), [
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to create batch assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all batch assignments.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getBatches(Request $request): AnonymousResourceCollection
    {
        $query = DeliveryAssignmentBatch::query();
        
        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        // Filter by created by
        if ($request->has('created_by')) {
            $query->where('created_by', $request->input('created_by'));
        }
        
        // Filter by date range
        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('created_at', [$request->input('from_date'), $request->input('to_date')]);
        } elseif ($request->has('from_date')) {
            $query->where('created_at', '>=', $request->input('from_date'));
        } elseif ($request->has('to_date')) {
            $query->where('created_at', '<=', $request->input('to_date'));
        }
        
        // Default sort by created_at desc
        $query->orderBy('created_at', 'desc');
        
        $batches = $query->with(['createdBy'])
                        ->paginate($request->input('per_page', 15));
        
        return DeliveryAssignmentBatchResource::collection($batches);
    }

    /**
     * Get a specific batch assignment.
     *
     * @param int $id
     * @return DeliveryAssignmentBatchResource|JsonResponse
     */
    public function getBatch(int $id): DeliveryAssignmentBatchResource|JsonResponse
    {
        try {
            $batch = DeliveryAssignmentBatch::with(['createdBy', 'assignments.order', 'assignments.deliveryPerson'])
                                           ->findOrFail($id);
            
            return new DeliveryAssignmentBatchResource($batch);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Batch assignment not found',
            ], 404);
        }
    }

    /**
     * Process a batch assignment.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function processBatch(int $id): JsonResponse
    {
        try {
            $batch = $this->assignmentService->processBatchAssignment($id);
            
            return response()->json([
                'message' => 'Batch assignment processed successfully',
                'data' => new DeliveryAssignmentBatchResource($batch),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process batch assignment: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to process batch assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel a batch assignment.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function cancelBatch(int $id): JsonResponse
    {
        try {
            $batch = $this->assignmentService->cancelBatchAssignment($id);
            
            return response()->json([
                'message' => 'Batch assignment cancelled successfully',
                'data' => new DeliveryAssignmentBatchResource($batch),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to cancel batch assignment: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to cancel batch assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get assignments for a delivery person.
     *
     * @param Request $request
     * @param int $deliveryPersonId
     * @return AnonymousResourceCollection
     */
    public function getAssignmentsForDeliveryPerson(Request $request, int $deliveryPersonId): AnonymousResourceCollection
    {
        $query = DeliveryAssignment::where('delivery_person_id', $deliveryPersonId);
        
        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        // Filter by date range
        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('assigned_at', [$request->input('from_date'), $request->input('to_date')]);
        } elseif ($request->has('from_date')) {
            $query->where('assigned_at', '>=', $request->input('from_date'));
        } elseif ($request->has('to_date')) {
            $query->where('assigned_at', '<=', $request->input('to_date'));
        }
        
        // Default sort by assigned_at desc
        $query->orderBy('assigned_at', 'desc');
        
        $assignments = $query->with(['order', 'batch'])
                            ->paginate($request->input('per_page', 15));
        
        return DeliveryAssignmentResource::collection($assignments);
    }

    /**
     * Get assignments for an order.
     *
     * @param int $orderId
     * @return AnonymousResourceCollection
     */
    public function getAssignmentsForOrder(int $orderId): AnonymousResourceCollection
    {
        $assignments = DeliveryAssignment::where('order_id', $orderId)
                                       ->with(['deliveryPerson', 'assignedBy', 'batch'])
                                       ->orderBy('assigned_at', 'desc')
                                       ->get();
        
        return DeliveryAssignmentResource::collection($assignments);
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CustomerMapResource;
use App\Http\Resources\DeliveryLocationMapResource;
use App\Http\Resources\DeliveryRouteResource;
use App\Http\Resources\OrderMapResource;
use App\Models\Customer;
use App\Models\DeliveryLocation;
use App\Models\DeliveryRoute;
use App\Models\Order;
use App\Services\GeocodingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * @group Map Management
 *
 * APIs for managing map-related functionality
 */
class MapController extends Controller
{
    /**
     * The geocoding service instance.
     *
     * @var GeocodingService
     */
    protected GeocodingService $geocodingService;

    /**
     * Create a new controller instance.
     *
     * @param GeocodingService $geocodingService
     */
    public function __construct(GeocodingService $geocodingService)
    {
        $this->geocodingService = $geocodingService;
    }

    /**
     * Get all delivery locations with coordinates.
     *
     * @return AnonymousResourceCollection
     */
    public function getDeliveryLocations(): AnonymousResourceCollection
    {
        $locations = DeliveryLocation::whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->where('status', true)
            ->get();
        
        return DeliveryLocationMapResource::collection($locations);
    }

    /**
     * Get all customers with coordinates.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getCustomers(Request $request): AnonymousResourceCollection
    {
        $query = Customer::whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->where('status', true);
        
        // Filter by location if provided
        if ($request->has('location_id')) {
            $query->where('location_code', $request->input('location_id'));
        }
        
        $customers = $query->get();
        
        return CustomerMapResource::collection($customers);
    }

    /**
     * Get all active orders with delivery routes.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getActiveOrders(Request $request): AnonymousResourceCollection
    {
        $query = Order::with(['customer', 'deliveryRoute'])
            ->whereHas('deliveryRoute')
            ->where('delivery_type', 'delivery')
            ->whereIn('delivery_status', ['Pending', 'Dispatched', 'In Transit']);
        
        // Filter by date if provided
        if ($request->has('date')) {
            $query->where('order_date', $request->input('date'));
        } else {
            $query->where('order_date', date('Y-m-d'));
        }
        
        // Filter by meal type if provided
        if ($request->has('meal_type')) {
            $query->where('order_menu', $request->input('meal_type'));
        }
        
        // Filter by delivery person if provided
        if ($request->has('delivery_person_id')) {
            $query->where('delivery_person', $request->input('delivery_person_id'));
        }
        
        $orders = $query->get();
        
        return OrderMapResource::collection($orders);
    }

    /**
     * Get a specific delivery route.
     *
     * @param int $orderId
     * @return DeliveryRouteResource|JsonResponse
     */
    public function getDeliveryRoute(int $orderId): DeliveryRouteResource|JsonResponse
    {
        $route = DeliveryRoute::where('order_id', $orderId)->first();
        
        if (!$route) {
            return response()->json([
                'message' => 'Delivery route not found',
            ], 404);
        }
        
        return new DeliveryRouteResource($route);
    }

    /**
     * Geocode an address.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function geocodeAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string',
            'city' => 'nullable|string',
            'country' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $address = $request->input('address');
        $city = $request->input('city');
        $country = $request->input('country', 'India');
        
        $result = $this->geocodingService->geocode($address, $city, $country);
        
        if (!$result) {
            return response()->json([
                'message' => 'Failed to geocode address',
            ], 400);
        }
        
        return response()->json([
            'message' => 'Address geocoded successfully',
            'data' => $result,
        ]);
    }

    /**
     * Update customer coordinates.
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function updateCustomerCoordinates(Request $request, int $customerId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        try {
            $customer = Customer::findOrFail($customerId);
            
            $customer->update([
                'latitude' => $request->input('latitude'),
                'longitude' => $request->input('longitude'),
            ]);
            
            // Get the address from the coordinates
            $address = $this->geocodingService->reverseGeocode(
                $request->input('latitude'),
                $request->input('longitude')
            );
            
            if ($address) {
                $customer->update([
                    'geocoded_address' => $address['display_name'],
                ]);
            }
            
            return response()->json([
                'message' => 'Customer coordinates updated successfully',
                'data' => new CustomerMapResource($customer->fresh()),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update customer coordinates', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'message' => 'Failed to update customer coordinates',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery location coordinates.
     *
     * @param Request $request
     * @param int $locationId
     * @return JsonResponse
     */
    public function updateLocationCoordinates(Request $request, int $locationId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        try {
            $location = DeliveryLocation::findOrFail($locationId);
            
            $location->update([
                'latitude' => $request->input('latitude'),
                'longitude' => $request->input('longitude'),
            ]);
            
            // Get the address from the coordinates
            $address = $this->geocodingService->reverseGeocode(
                $request->input('latitude'),
                $request->input('longitude')
            );
            
            if ($address) {
                $location->update([
                    'geocoded_address' => $address['display_name'],
                ]);
            }
            
            return response()->json([
                'message' => 'Location coordinates updated successfully',
                'data' => new DeliveryLocationMapResource($location->fresh()),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update location coordinates', [
                'location_id' => $locationId,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'message' => 'Failed to update location coordinates',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

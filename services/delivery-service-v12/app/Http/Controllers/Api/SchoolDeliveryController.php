<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SchoolDelivery\CreateDeliveryBatchRequest;
use App\Http\Requests\SchoolDelivery\UpdateDeliveryStatusRequest;
use App\Http\Requests\SchoolDelivery\AssignDeliveryPersonRequest;
use App\Models\SchoolDeliveryBatch;
use App\Services\SchoolDeliveryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * SchoolDeliveryController
 *
 * Handles school delivery batch coordination, break time-aligned scheduling,
 * and real-time delivery tracking for the tiffin system.
 */
class SchoolDeliveryController extends Controller
{
    protected SchoolDeliveryService $schoolDeliveryService;

    public function __construct(SchoolDeliveryService $schoolDeliveryService)
    {
        $this->schoolDeliveryService = $schoolDeliveryService;
    }

    /**
     * Get delivery batches with filtering options.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'school_id',
                'delivery_date',
                'break_time_slot',
                'status',
                'delivery_person_id',
                'date_from',
                'date_to',
            ]);

            $batches = $this->schoolDeliveryService->getDeliveryBatches($filters, $request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $batches,
                'meta' => [
                    'filters_applied' => array_filter($filters),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery batches', [
                'error' => $e->getMessage(),
                'filters' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery batches',
            ], 500);
        }
    }

    /**
     * Get a specific delivery batch.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $batch = $this->schoolDeliveryService->getDeliveryBatchById($id);

            if (!$batch) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery batch not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $batch,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery batch', [
                'batch_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery batch',
            ], 500);
        }
    }

    /**
     * Create a new delivery batch for a school.
     *
     * @param CreateDeliveryBatchRequest $request
     * @return JsonResponse
     */
    public function store(CreateDeliveryBatchRequest $request): JsonResponse
    {
        try {
            $batch = $this->schoolDeliveryService->createDeliveryBatch($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Delivery batch created successfully',
                'data' => $batch,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create delivery batch', [
                'error' => $e->getMessage(),
                'request_data' => $request->validated(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create delivery batch: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery batch status.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(UpdateDeliveryStatusRequest $request, int $id): JsonResponse
    {
        try {
            $batch = $this->schoolDeliveryService->updateDeliveryBatchStatus(
                $id,
                $request->input('status'),
                $request->input('status_notes'),
                $request->input('location_data')
            );

            if (!$batch) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery batch not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Delivery batch status updated successfully',
                'data' => $batch,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update delivery batch status', [
                'batch_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update delivery batch status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Assign delivery person to a batch.
     *
     * @param AssignDeliveryPersonRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function assignDeliveryPerson(AssignDeliveryPersonRequest $request, int $id): JsonResponse
    {
        try {
            $batch = $this->schoolDeliveryService->assignDeliveryPerson(
                $id,
                $request->input('delivery_person_id'),
                $request->input('vehicle_number'),
                $request->input('estimated_delivery_time')
            );

            if (!$batch) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery batch not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Delivery person assigned successfully',
                'data' => $batch,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to assign delivery person', [
                'batch_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to assign delivery person: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery batches for a specific school.
     *
     * @param Request $request
     * @param int $schoolId
     * @return JsonResponse
     */
    public function getSchoolBatches(Request $request, int $schoolId): JsonResponse
    {
        try {
            $filters = $request->only(['delivery_date', 'break_time_slot', 'status']);
            $filters['school_id'] = $schoolId;

            $batches = $this->schoolDeliveryService->getSchoolDeliveryBatches($schoolId, $filters);

            return response()->json([
                'success' => true,
                'data' => $batches,
                'meta' => [
                    'school_id' => $schoolId,
                    'total_batches' => $batches->count(),
                    'active_batches' => $batches->whereIn('status', ['scheduled', 'preparing', 'ready', 'dispatched', 'in_transit'])->count(),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get school delivery batches', [
                'school_id' => $schoolId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve school delivery batches',
            ], 500);
        }
    }

    /**
     * Get delivery schedule for a school on a specific date.
     *
     * @param Request $request
     * @param int $schoolId
     * @return JsonResponse
     */
    public function getSchoolSchedule(Request $request, int $schoolId): JsonResponse
    {
        $request->validate([
            'date' => 'required|date',
        ]);

        try {
            $schedule = $this->schoolDeliveryService->getSchoolDeliverySchedule(
                $schoolId,
                $request->input('date')
            );

            return response()->json([
                'success' => true,
                'data' => $schedule,
                'meta' => [
                    'school_id' => $schoolId,
                    'date' => $request->input('date'),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get school delivery schedule', [
                'school_id' => $schoolId,
                'date' => $request->input('date'),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve school delivery schedule',
            ], 500);
        }
    }

    /**
     * Generate delivery batches for a date range.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateBatches(Request $request): JsonResponse
    {
        $request->validate([
            'school_ids' => 'nullable|array',
            'school_ids.*' => 'integer|exists:schools,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'break_time_slots' => 'nullable|array',
            'break_time_slots.*' => 'in:morning_break,lunch_break,afternoon_break',
        ]);

        try {
            $result = $this->schoolDeliveryService->generateDeliveryBatches(
                $request->input('start_date'),
                $request->input('end_date'),
                $request->input('school_ids'),
                $request->input('break_time_slots')
            );

            return response()->json([
                'success' => true,
                'message' => 'Delivery batches generated successfully',
                'data' => $result,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to generate delivery batches', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate delivery batches: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery performance metrics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPerformanceMetrics(Request $request): JsonResponse
    {
        $request->validate([
            'school_id' => 'nullable|integer|exists:schools,id',
            'delivery_person_id' => 'nullable|integer|exists:delivery_people,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            $metrics = $this->schoolDeliveryService->getDeliveryPerformanceMetrics(
                $request->only(['school_id', 'delivery_person_id', 'start_date', 'end_date'])
            );

            return response()->json([
                'success' => true,
                'data' => $metrics,
                'meta' => [
                    'filters_applied' => array_filter($request->only(['school_id', 'delivery_person_id', 'start_date', 'end_date'])),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery performance metrics', [
                'error' => $e->getMessage(),
                'filters' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve performance metrics',
            ], 500);
        }
    }

    /**
     * Optimize delivery routes for a school.
     *
     * @param Request $request
     * @param int $schoolId
     * @return JsonResponse
     */
    public function optimizeRoutes(Request $request, int $schoolId): JsonResponse
    {
        $request->validate([
            'date' => 'required|date',
            'break_time_slot' => 'required|in:morning_break,lunch_break,afternoon_break',
        ]);

        try {
            $optimizedRoute = $this->schoolDeliveryService->optimizeDeliveryRoute(
                $schoolId,
                $request->input('date'),
                $request->input('break_time_slot')
            );

            return response()->json([
                'success' => true,
                'data' => $optimizedRoute,
                'meta' => [
                    'school_id' => $schoolId,
                    'date' => $request->input('date'),
                    'break_time_slot' => $request->input('break_time_slot'),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to optimize delivery routes', [
                'school_id' => $schoolId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to optimize delivery routes',
            ], 500);
        }
    }
}

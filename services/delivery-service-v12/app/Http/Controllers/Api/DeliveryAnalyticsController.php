<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\DeliveryAnalyticsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @group Delivery Analytics
 *
 * APIs for delivery analytics and reporting
 */
class DeliveryAnalyticsController extends Controller
{
    /**
     * The delivery analytics service instance.
     *
     * @var DeliveryAnalyticsService
     */
    protected DeliveryAnalyticsService $analyticsService;

    /**
     * Create a new controller instance.
     *
     * @param DeliveryAnalyticsService $analyticsService
     */
    public function __construct(DeliveryAnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * Get delivery performance report.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDeliveryPerformanceReport(Request $request): JsonResponse
    {
        try {
            $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
            $endDate = $request->input('end_date', now()->format('Y-m-d'));
            $locationId = $request->input('location_id');
            $deliveryPersonId = $request->input('delivery_person_id');
            
            $report = $this->analyticsService->getDeliveryPerformanceReport(
                $startDate,
                $endDate,
                $locationId,
                $deliveryPersonId
            );
            
            return response()->json([
                'message' => 'Delivery performance report retrieved successfully',
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery performance report: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery performance report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery staff performance report.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDeliveryStaffPerformanceReport(Request $request): JsonResponse
    {
        try {
            $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
            $endDate = $request->input('end_date', now()->format('Y-m-d'));
            $locationId = $request->input('location_id');
            
            $report = $this->analyticsService->getDeliveryStaffPerformanceReport(
                $startDate,
                $endDate,
                $locationId
            );
            
            return response()->json([
                'message' => 'Delivery staff performance report retrieved successfully',
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery staff performance report: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery staff performance report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery time analysis report.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDeliveryTimeAnalysisReport(Request $request): JsonResponse
    {
        try {
            $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
            $endDate = $request->input('end_date', now()->format('Y-m-d'));
            $locationId = $request->input('location_id');
            $mealType = $request->input('meal_type');
            
            $report = $this->analyticsService->getDeliveryTimeAnalysisReport(
                $startDate,
                $endDate,
                $locationId,
                $mealType
            );
            
            return response()->json([
                'message' => 'Delivery time analysis report retrieved successfully',
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery time analysis report: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery time analysis report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery density heatmap data.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDeliveryDensityHeatmap(Request $request): JsonResponse
    {
        try {
            $date = $request->input('date', now()->format('Y-m-d'));
            $mealType = $request->input('meal_type');
            $locationId = $request->input('location_id');
            
            $heatmapData = $this->analyticsService->getDeliveryDensityHeatmap(
                $date,
                $mealType,
                $locationId
            );
            
            return response()->json([
                'message' => 'Delivery density heatmap data retrieved successfully',
                'data' => $heatmapData,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery density heatmap data: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery density heatmap data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery problem areas report.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDeliveryProblemAreasReport(Request $request): JsonResponse
    {
        try {
            $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
            $endDate = $request->input('end_date', now()->format('Y-m-d'));
            $locationId = $request->input('location_id');
            
            $report = $this->analyticsService->getDeliveryProblemAreasReport(
                $startDate,
                $endDate,
                $locationId
            );
            
            return response()->json([
                'message' => 'Delivery problem areas report retrieved successfully',
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery problem areas report: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery problem areas report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery time prediction.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDeliveryTimePrediction(Request $request): JsonResponse
    {
        try {
            $orderId = $request->input('order_id');
            $kitchenId = $request->input('kitchen_id');
            $customerId = $request->input('customer_id');
            $mealType = $request->input('meal_type');
            $date = $request->input('date', now()->format('Y-m-d'));
            
            $prediction = $this->analyticsService->getDeliveryTimePrediction(
                $orderId,
                $kitchenId,
                $customerId,
                $mealType,
                $date
            );
            
            return response()->json([
                'message' => 'Delivery time prediction retrieved successfully',
                'data' => $prediction,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery time prediction: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get delivery time prediction',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

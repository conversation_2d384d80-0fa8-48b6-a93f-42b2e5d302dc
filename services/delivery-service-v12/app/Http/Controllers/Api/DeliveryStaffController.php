<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\DeliveryStaff\CreateDeliveryStaffRequest;
use App\Http\Requests\DeliveryStaff\UpdateDeliveryStaffRequest;
use App\Http\Requests\DeliveryStaff\UpdateLocationRequest;
use App\Http\Resources\DeliveryPersonResource;
use App\Models\DeliveryPerson;
use App\Services\DeliveryStaffService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

/**
 * @group Delivery Staff Management
 *
 * APIs for managing delivery staff
 */
class DeliveryStaffController extends Controller
{
    /**
     * The delivery staff service instance.
     *
     * @var DeliveryStaffService
     */
    protected DeliveryStaffService $staffService;

    /**
     * Create a new controller instance.
     *
     * @param DeliveryStaffService $staffService
     */
    public function __construct(DeliveryStaffService $staffService)
    {
        $this->staffService = $staffService;
    }

    /**
     * Get all delivery staff.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = DeliveryPerson::query();
        
        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->boolean('status'));
        }
        
        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        // Filter by on duty status
        if ($request->has('on_duty')) {
            $query->where('on_duty', $request->boolean('on_duty'));
        }
        
        // Filter by location
        if ($request->has('location_id')) {
            $locationId = $request->input('location_id');
            $query->whereHas('serviceAreas', function ($q) use ($locationId) {
                $q->where('location_id', $locationId)
                  ->where('is_active', true);
            });
        }
        
        // Search by name or phone
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // Sort by rating
        if ($request->input('sort') === 'rating') {
            $query->orderBy('rating', $request->input('order', 'desc'));
        } 
        // Sort by performance
        elseif ($request->input('sort') === 'performance') {
            $query->orderByRaw('(on_time_deliveries / NULLIF(total_deliveries, 0)) DESC');
        }
        // Default sort by name
        else {
            $query->orderBy('name');
        }
        
        $staff = $query->with(['user', 'serviceAreas.location', 'schedules'])
                       ->paginate($request->input('per_page', 15));
        
        return DeliveryPersonResource::collection($staff);
    }

    /**
     * Get a specific delivery staff member.
     *
     * @param int $id
     * @return DeliveryPersonResource|JsonResponse
     */
    public function show(int $id): DeliveryPersonResource|JsonResponse
    {
        try {
            $staff = DeliveryPerson::with(['user', 'serviceAreas.location', 'schedules'])
                                   ->findOrFail($id);
            
            return new DeliveryPersonResource($staff);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Delivery staff not found',
            ], 404);
        }
    }

    /**
     * Create a new delivery staff member.
     *
     * @param CreateDeliveryStaffRequest $request
     * @return JsonResponse
     */
    public function store(CreateDeliveryStaffRequest $request): JsonResponse
    {
        try {
            $staff = $this->staffService->createDeliveryStaff($request->validated());
            
            return response()->json([
                'message' => 'Delivery staff created successfully',
                'data' => new DeliveryPersonResource($staff),
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create delivery staff: ' . $e->getMessage(), [
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to create delivery staff',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a delivery staff member.
     *
     * @param UpdateDeliveryStaffRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateDeliveryStaffRequest $request, int $id): JsonResponse
    {
        try {
            $staff = $this->staffService->updateDeliveryStaff($id, $request->validated());
            
            return response()->json([
                'message' => 'Delivery staff updated successfully',
                'data' => new DeliveryPersonResource($staff),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update delivery staff: ' . $e->getMessage(), [
                'id' => $id,
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to update delivery staff',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a delivery staff member.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $result = $this->staffService->deleteDeliveryStaff($id);
            
            if ($result) {
                return response()->json([
                    'message' => 'Delivery staff deleted successfully',
                ]);
            } else {
                return response()->json([
                    'message' => 'Failed to delete delivery staff',
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to delete delivery staff: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to delete delivery staff',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery staff location.
     *
     * @param UpdateLocationRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateLocation(UpdateLocationRequest $request, int $id): JsonResponse
    {
        try {
            $staff = $this->staffService->updateLocation(
                $id,
                $request->input('latitude'),
                $request->input('longitude')
            );
            
            return response()->json([
                'message' => 'Location updated successfully',
                'data' => [
                    'id' => $staff->id,
                    'latitude' => $staff->current_latitude,
                    'longitude' => $staff->current_longitude,
                    'last_update' => $staff->last_location_update,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update delivery staff location: ' . $e->getMessage(), [
                'id' => $id,
                'request' => $request->validated(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to update location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery staff duty status.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateDutyStatus(Request $request, int $id): JsonResponse
    {
        try {
            $onDuty = $request->boolean('on_duty');
            
            $staff = $this->staffService->updateDutyStatus($id, $onDuty);
            
            return response()->json([
                'message' => 'Duty status updated successfully',
                'data' => [
                    'id' => $staff->id,
                    'on_duty' => $staff->on_duty,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update delivery staff duty status: ' . $e->getMessage(), [
                'id' => $id,
                'on_duty' => $request->boolean('on_duty'),
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to update duty status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery staff performance metrics.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getPerformanceMetrics(int $id): JsonResponse
    {
        try {
            $metrics = $this->staffService->getPerformanceMetrics($id);
            
            return response()->json([
                'message' => 'Performance metrics retrieved successfully',
                'data' => $metrics,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get delivery staff performance metrics: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e,
            ]);
            
            return response()->json([
                'message' => 'Failed to get performance metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

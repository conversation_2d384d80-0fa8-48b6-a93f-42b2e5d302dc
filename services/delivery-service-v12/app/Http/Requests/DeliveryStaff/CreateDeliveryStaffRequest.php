<?php

namespace App\Http\Requests\DeliveryStaff;

use Illuminate\Foundation\Http\FormRequest;

class CreateDeliveryStaffRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'username' => 'nullable|string|unique:users,username',
            'password' => 'required|string|min:8',
            'phone' => 'required|string|max:20',
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'profile_photo' => 'nullable|string',
            'id_proof_type' => 'nullable|string|max:255',
            'id_proof_number' => 'nullable|string|max:255',
            'id_proof_image' => 'nullable|string',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'vehicle_type' => 'nullable|string|max:255',
            'vehicle_number' => 'nullable|string|max:255',
            'vehicle_details' => 'nullable|string',
            'status' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
            'on_duty' => 'nullable|boolean',
            'joining_date' => 'nullable|date',
            'company_id' => 'nullable|integer|exists:companies,id',
            'unit_id' => 'nullable|integer|exists:units,id',
            'service_areas' => 'nullable|array',
            'service_areas.*.location_id' => 'required|integer|exists:delivery_locations,pk_location_code',
            'service_areas.*.is_primary' => 'nullable|boolean',
            'service_areas.*.is_active' => 'nullable|boolean',
            'schedules' => 'nullable|array',
            'schedules.*.day_of_week' => 'required|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'schedules.*.start_time' => 'required|date_format:H:i',
            'schedules.*.end_time' => 'required|date_format:H:i|after:schedules.*.start_time',
            'schedules.*.is_active' => 'nullable|boolean',
        ];
    }
}

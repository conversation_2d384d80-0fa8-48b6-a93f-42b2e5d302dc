<?php

namespace App\Http\Requests\DeliveryAssignment;

use Illuminate\Foundation\Http\FormRequest;

class BatchAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'orders' => 'required|array',
            'orders.*' => 'required|integer|exists:orders,pk_order_no',
            'criteria' => 'nullable|array',
            'criteria.location_id' => 'nullable|integer|exists:delivery_locations,pk_location_code',
            'criteria.date' => 'nullable|date',
            'criteria.meal_type' => 'nullable|string|in:lunch,dinner',
            'notes' => 'nullable|string',
        ];
    }
}

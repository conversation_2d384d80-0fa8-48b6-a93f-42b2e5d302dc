<?php

namespace App\Http\Requests\DeliveryAssignment;

use Illuminate\Foundation\Http\FormRequest;

class AssignDeliveryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'order_id' => 'required|integer|exists:orders,pk_order_no',
            'delivery_person_id' => 'required|integer|exists:delivery_persons,id',
            'notes' => 'nullable|string',
        ];
    }
}

<?php

namespace App\Http\Requests\SchoolDelivery;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * UpdateDeliveryStatusRequest
 *
 * Validation rules for updating school delivery batch status.
 */
class UpdateDeliveryStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => [
                'required',
                Rule::in(['scheduled', 'preparing', 'ready', 'dispatched', 'in_transit', 'delivered', 'failed', 'cancelled']),
                function ($attribute, $value, $fail) {
                    // Validate status transition logic
                    $batchId = $this->route('id');
                    if ($batchId) {
                        $batch = \App\Models\SchoolDeliveryBatch::find($batchId);
                        if ($batch && !$this->isValidStatusTransition($batch->status, $value)) {
                            $fail("Invalid status transition from '{$batch->status}' to '{$value}'.");
                        }
                    }
                },
            ],
            'status_notes' => [
                'nullable',
                'string',
                'max:500',
            ],
            'location_data' => [
                'nullable',
                'array',
            ],
            'location_data.latitude' => [
                'nullable',
                'numeric',
                'between:-90,90',
            ],
            'location_data.longitude' => [
                'nullable',
                'numeric',
                'between:-180,180',
            ],
            'location_data.address' => [
                'nullable',
                'string',
                'max:255',
            ],
            'location_data.accuracy' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'temperature_reading' => [
                'nullable',
                'numeric',
                'between:-10,100',
            ],
            'quality_check_passed' => [
                'nullable',
                'boolean',
            ],
            'quality_notes' => [
                'nullable',
                'string',
                'max:500',
            ],
            'received_by_name' => [
                'nullable',
                'string',
                'max:100',
                'required_if:status,delivered',
            ],
            'received_by_designation' => [
                'nullable',
                'string',
                'max:100',
            ],
            'received_by_phone' => [
                'nullable',
                'string',
                'regex:/^[6-9]\d{9}$/',
            ],
            'delivery_notes' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'delivery_photos' => [
                'nullable',
                'array',
                'max:5',
            ],
            'delivery_photos.*' => [
                'string',
                'max:255',
            ],
            'school_rating' => [
                'nullable',
                'numeric',
                'min:1',
                'max:5',
            ],
            'school_feedback' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'reported_issues' => [
                'nullable',
                'array',
                'max:10',
            ],
            'reported_issues.*.issue_type' => [
                'required_with:reported_issues',
                Rule::in(['late_delivery', 'wrong_order', 'quality_issue', 'missing_items', 'damaged_items', 'other']),
            ],
            'reported_issues.*.description' => [
                'required_with:reported_issues',
                'string',
                'max:200',
            ],
            'reported_issues.*.severity' => [
                'nullable',
                Rule::in(['low', 'medium', 'high', 'critical']),
            ],
            'delay_reason' => [
                'nullable',
                'string',
                'max:500',
                'required_if:status,failed',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'status.required' => 'Status is required.',
            'status.in' => 'Invalid status selected.',
            'status_notes.max' => 'Status notes must not exceed 500 characters.',
            'location_data.latitude.between' => 'Latitude must be between -90 and 90.',
            'location_data.longitude.between' => 'Longitude must be between -180 and 180.',
            'location_data.address.max' => 'Address must not exceed 255 characters.',
            'temperature_reading.between' => 'Temperature must be between -10°C and 100°C.',
            'received_by_name.required_if' => 'Receiver name is required when status is delivered.',
            'received_by_name.max' => 'Receiver name must not exceed 100 characters.',
            'received_by_phone.regex' => 'Please enter a valid 10-digit mobile number.',
            'delivery_notes.max' => 'Delivery notes must not exceed 1000 characters.',
            'delivery_photos.max' => 'Maximum 5 delivery photos allowed.',
            'school_rating.min' => 'Rating must be at least 1.',
            'school_rating.max' => 'Rating cannot exceed 5.',
            'school_feedback.max' => 'School feedback must not exceed 1000 characters.',
            'reported_issues.max' => 'Maximum 10 issues can be reported.',
            'reported_issues.*.issue_type.required_with' => 'Issue type is required.',
            'reported_issues.*.issue_type.in' => 'Invalid issue type selected.',
            'reported_issues.*.description.required_with' => 'Issue description is required.',
            'reported_issues.*.description.max' => 'Issue description must not exceed 200 characters.',
            'reported_issues.*.severity.in' => 'Invalid severity level selected.',
            'delay_reason.required_if' => 'Delay reason is required when status is failed.',
            'delay_reason.max' => 'Delay reason must not exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'status_notes' => 'status notes',
            'location_data' => 'location data',
            'temperature_reading' => 'temperature reading',
            'quality_check_passed' => 'quality check status',
            'quality_notes' => 'quality notes',
            'received_by_name' => 'receiver name',
            'received_by_designation' => 'receiver designation',
            'received_by_phone' => 'receiver phone',
            'delivery_notes' => 'delivery notes',
            'delivery_photos' => 'delivery photos',
            'school_rating' => 'school rating',
            'school_feedback' => 'school feedback',
            'reported_issues' => 'reported issues',
            'delay_reason' => 'delay reason',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean phone number
        if ($this->has('received_by_phone')) {
            $phone = preg_replace('/[^0-9]/', '', $this->received_by_phone);
            $this->merge(['received_by_phone' => $phone]);
        }

        // Set default values
        $this->merge([
            'quality_check_passed' => $this->input('quality_check_passed', true),
        ]);

        // Add timestamp to location data
        if ($this->has('location_data') && is_array($this->location_data)) {
            $locationData = $this->location_data;
            $locationData['timestamp'] = now()->toISOString();
            $this->merge(['location_data' => $locationData]);
        }
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $status = $this->input('status');
            
            // Validate required fields for specific statuses
            switch ($status) {
                case 'delivered':
                    if (!$this->input('received_by_name')) {
                        $validator->errors()->add('received_by_name', 'Receiver name is required for delivered status.');
                    }
                    break;
                    
                case 'failed':
                    if (!$this->input('delay_reason')) {
                        $validator->errors()->add('delay_reason', 'Delay reason is required for failed status.');
                    }
                    break;
                    
                case 'in_transit':
                    if (!$this->input('location_data')) {
                        $validator->errors()->add('location_data', 'Location data is required for in-transit status.');
                    }
                    break;
            }

            // Validate temperature reading for food safety
            $temperatureReading = $this->input('temperature_reading');
            if ($temperatureReading !== null) {
                if ($status === 'delivered' && ($temperatureReading < 60 || $temperatureReading > 85)) {
                    $validator->errors()->add(
                        'temperature_reading',
                        'Food temperature should be between 60°C and 85°C for safe delivery.'
                    );
                }
            }

            // Validate quality check for delivered status
            if ($status === 'delivered' && $this->input('quality_check_passed') === false) {
                if (!$this->input('quality_notes')) {
                    $validator->errors()->add(
                        'quality_notes',
                        'Quality notes are required when quality check fails.'
                    );
                }
            }

            // Validate reported issues structure
            $reportedIssues = $this->input('reported_issues', []);
            foreach ($reportedIssues as $index => $issue) {
                if (!isset($issue['issue_type']) || !isset($issue['description'])) {
                    $validator->errors()->add(
                        "reported_issues.{$index}",
                        'Each reported issue must have a type and description.'
                    );
                }
            }
        });
    }

    /**
     * Check if status transition is valid.
     *
     * @param string $currentStatus
     * @param string $newStatus
     * @return bool
     */
    private function isValidStatusTransition(string $currentStatus, string $newStatus): bool
    {
        $validTransitions = [
            'scheduled' => ['preparing', 'cancelled'],
            'preparing' => ['ready', 'cancelled'],
            'ready' => ['dispatched', 'cancelled'],
            'dispatched' => ['in_transit', 'delivered', 'failed'],
            'in_transit' => ['delivered', 'failed'],
            'delivered' => [], // Terminal state
            'failed' => ['scheduled'], // Can reschedule
            'cancelled' => ['scheduled'], // Can reschedule
        ];

        return in_array($newStatus, $validTransitions[$currentStatus] ?? []);
    }
}

<?php

namespace App\Http\Requests\SchoolDelivery;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * CreateDeliveryBatchRequest
 *
 * Validation rules for creating a new school delivery batch.
 */
class CreateDeliveryBatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tenant_id' => [
                'nullable',
                'integer',
            ],
            'company_id' => [
                'nullable',
                'integer',
            ],
            'unit_id' => [
                'nullable',
                'integer',
            ],
            'school_id' => [
                'required',
                'integer',
                'exists:schools,id',
                function ($attribute, $value, $fail) {
                    $school = \App\Models\School::find($value);
                    if ($school && (!$school->is_active || $school->partnership_status !== 'active')) {
                        $fail('The selected school is not available for delivery.');
                    }
                },
            ],
            'delivery_date' => [
                'required',
                'date',
                'after_or_equal:today',
                function ($attribute, $value, $fail) {
                    // Check if batch already exists for this school, date, and break time
                    $exists = \App\Models\SchoolDeliveryBatch::where('school_id', $this->input('school_id'))
                        ->where('delivery_date', $value)
                        ->where('break_time_slot', $this->input('break_time_slot'))
                        ->exists();
                    
                    if ($exists) {
                        $fail('A delivery batch already exists for this school, date, and break time.');
                    }
                },
            ],
            'break_time_slot' => [
                'required',
                Rule::in(['morning_break', 'lunch_break', 'afternoon_break']),
                function ($attribute, $value, $fail) {
                    // Validate that the school has this break time configured
                    $schoolId = $this->input('school_id');
                    if ($schoolId) {
                        $school = \App\Models\School::find($schoolId);
                        if ($school && !$school->hasBreakTime($value)) {
                            $fail('The selected break time is not configured for this school.');
                        }
                    }
                },
            ],
            'delivery_person_id' => [
                'nullable',
                'integer',
                'exists:delivery_people,id',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $deliveryPerson = \App\Models\DeliveryPerson::find($value);
                        if ($deliveryPerson && (!$deliveryPerson->is_active || !$deliveryPerson->on_duty)) {
                            $fail('The selected delivery person is not available.');
                        }
                    }
                },
            ],
            'vehicle_number' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[A-Z]{2}[0-9]{2}[A-Z]{1,2}[0-9]{4}$/',
            ],
            'vehicle_type' => [
                'nullable',
                Rule::in(['bike', 'scooter', 'car', 'van', 'truck']),
            ],
            'special_instructions' => [
                'nullable',
                'array',
                'max:5',
            ],
            'special_instructions.*' => [
                'string',
                'max:200',
            ],
            'requires_refrigeration' => [
                'nullable',
                'boolean',
            ],
            'fragile_items' => [
                'nullable',
                'boolean',
            ],
            'estimated_distance_km' => [
                'nullable',
                'numeric',
                'min:0',
                'max:100',
            ],
            'estimated_duration_minutes' => [
                'nullable',
                'integer',
                'min:5',
                'max:300',
            ],
            'delivery_cost' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000',
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'school_id.required' => 'School is required.',
            'school_id.exists' => 'The selected school is invalid.',
            'delivery_date.required' => 'Delivery date is required.',
            'delivery_date.after_or_equal' => 'Delivery date must be today or in the future.',
            'break_time_slot.required' => 'Break time slot is required.',
            'break_time_slot.in' => 'Invalid break time slot selected.',
            'delivery_person_id.exists' => 'The selected delivery person is invalid.',
            'vehicle_number.regex' => 'Vehicle number must be in valid Indian format (e.g., MH12AB1234).',
            'vehicle_type.in' => 'Invalid vehicle type selected.',
            'special_instructions.max' => 'Maximum 5 special instructions allowed.',
            'special_instructions.*.max' => 'Each instruction must not exceed 200 characters.',
            'estimated_distance_km.numeric' => 'Distance must be a valid number.',
            'estimated_distance_km.max' => 'Distance cannot exceed 100 km.',
            'estimated_duration_minutes.integer' => 'Duration must be a valid number of minutes.',
            'estimated_duration_minutes.min' => 'Duration must be at least 5 minutes.',
            'estimated_duration_minutes.max' => 'Duration cannot exceed 5 hours.',
            'delivery_cost.numeric' => 'Delivery cost must be a valid amount.',
            'delivery_cost.max' => 'Delivery cost cannot exceed ₹1000.',
            'notes.max' => 'Notes must not exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'school_id' => 'school',
            'delivery_date' => 'delivery date',
            'break_time_slot' => 'break time slot',
            'delivery_person_id' => 'delivery person',
            'vehicle_number' => 'vehicle number',
            'vehicle_type' => 'vehicle type',
            'special_instructions' => 'special instructions',
            'requires_refrigeration' => 'refrigeration requirement',
            'fragile_items' => 'fragile items indicator',
            'estimated_distance_km' => 'estimated distance',
            'estimated_duration_minutes' => 'estimated duration',
            'delivery_cost' => 'delivery cost',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'requires_refrigeration' => $this->input('requires_refrigeration', false),
            'fragile_items' => $this->input('fragile_items', false),
        ]);

        // Set tenant information from authenticated user or defaults
        if (!$this->has('tenant_id')) {
            $this->merge([
                'tenant_id' => 1, // Default tenant
                'company_id' => 1, // Default company
                'unit_id' => 1, // Default unit
            ]);
        }

        // Clean vehicle number
        if ($this->has('vehicle_number')) {
            $vehicleNumber = strtoupper(preg_replace('/[^A-Z0-9]/', '', $this->vehicle_number));
            $this->merge(['vehicle_number' => $vehicleNumber]);
        }
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate delivery date is a weekday (schools typically operate Mon-Fri)
            $deliveryDate = $this->input('delivery_date');
            if ($deliveryDate) {
                $dayOfWeek = \Carbon\Carbon::parse($deliveryDate)->dayOfWeek;
                if ($dayOfWeek == 0 || $dayOfWeek == 6) { // Sunday = 0, Saturday = 6
                    $validator->errors()->add(
                        'delivery_date',
                        'Delivery date must be a weekday (Monday to Friday).'
                    );
                }
            }

            // Validate that there are active subscriptions for this date
            $schoolId = $this->input('school_id');
            $deliveryDate = $this->input('delivery_date');
            $breakTimeSlot = $this->input('break_time_slot');
            
            if ($schoolId && $deliveryDate && $breakTimeSlot) {
                $dayOfWeek = strtolower(\Carbon\Carbon::parse($deliveryDate)->format('l'));
                
                $activeSubscriptions = \App\Models\SchoolMealSubscription::where('school_id', $schoolId)
                    ->where('status', 'active')
                    ->where('start_date', '<=', $deliveryDate)
                    ->where('end_date', '>=', $deliveryDate)
                    ->whereJsonContains('delivery_days', $dayOfWeek)
                    ->where(function ($query) use ($breakTimeSlot) {
                        $query->where('preferred_break_time', $breakTimeSlot)
                              ->orWhere('preferred_break_time', 'both');
                    })
                    ->count();
                
                if ($activeSubscriptions === 0) {
                    $validator->errors()->add(
                        'delivery_date',
                        'No active subscriptions found for this school, date, and break time.'
                    );
                }
            }

            // Validate vehicle assignment consistency
            $deliveryPersonId = $this->input('delivery_person_id');
            $vehicleNumber = $this->input('vehicle_number');
            $vehicleType = $this->input('vehicle_type');
            
            if ($deliveryPersonId && !$vehicleNumber && !$vehicleType) {
                $deliveryPerson = \App\Models\DeliveryPerson::find($deliveryPersonId);
                if ($deliveryPerson) {
                    $this->merge([
                        'vehicle_number' => $deliveryPerson->vehicle_number,
                        'vehicle_type' => $deliveryPerson->vehicle_type,
                    ]);
                }
            }
        });
    }
}

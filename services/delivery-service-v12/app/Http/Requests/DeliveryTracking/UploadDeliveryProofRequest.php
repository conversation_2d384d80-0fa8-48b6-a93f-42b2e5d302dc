<?php

namespace App\Http\Requests\DeliveryTracking;

use Illuminate\Foundation\Http\FormRequest;

class UploadDeliveryProofRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'proof_type' => 'required|string|in:delivery,pickup,customer_signature,other',
            'image' => 'required|string',
            'notes' => 'nullable|string',
        ];
    }
}

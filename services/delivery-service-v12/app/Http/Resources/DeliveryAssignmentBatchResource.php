<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAssignmentBatchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'batch_number' => $this->batch_number,
            'status' => $this->status,
            'total_orders' => $this->total_orders,
            'assigned_orders' => $this->assigned_orders,
            'failed_orders' => $this->failed_orders,
            'completion_percentage' => $this->completion_percentage,
            'failure_percentage' => $this->failure_percentage,
            'criteria' => $this->criteria,
            'notes' => $this->notes,
            'created_by' => $this->created_by,
            'processed_at' => $this->processed_at,
            'completed_at' => $this->completed_at,
            'created_by_user' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->first_name . ' ' . $this->createdBy->last_name,
                    'email' => $this->createdBy->email,
                ];
            }),
            'assignments' => $this->whenLoaded('assignments', function () {
                return DeliveryAssignmentResource::collection($this->assignments);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryZoneResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'kitchen_id' => $this->kitchen_id,
            'zone_number' => $this->zone_number,
            'distance_range' => $this->distanceRange,
            'min_distance_km' => $this->min_distance_km,
            'max_distance_km' => $this->max_distance_km,
            'base_delivery_fee' => $this->base_delivery_fee,
            'additional_fee' => $this->additional_fee,
            'total_fee' => $this->totalFee,
            'is_active' => $this->is_active,
            'description' => $this->description,
            'polygon_coordinates' => $this->polygon_coordinates,
            'kitchen' => $this->whenLoaded('kitchen', function () {
                return [
                    'id' => $this->kitchen->pk_location_code,
                    'name' => $this->kitchen->location,
                    'coordinates' => [
                        'lat' => $this->kitchen->latitude,
                        'lng' => $this->kitchen->longitude,
                    ],
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

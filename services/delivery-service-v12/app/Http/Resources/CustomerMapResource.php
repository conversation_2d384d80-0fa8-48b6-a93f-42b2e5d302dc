<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerMapResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_customer_code,
            'name' => $this->customer_name,
            'address' => $this->customer_Address,
            'phone' => $this->phone,
            'email' => $this->email_address,
            'coordinates' => [
                'lat' => $this->latitude,
                'lng' => $this->longitude,
            ],
            'food_preference' => $this->food_preference,
            'dabbawala' => [
                'code' => $this->dabbawala_code,
                'code_type' => $this->dabbawala_code_type,
                'status' => $this->dabba_status,
            ],
            'location' => [
                'id' => $this->location_code,
                'name' => $this->location->location ?? null,
            ],
            'is_kitchen' => false,
        ];
    }
}

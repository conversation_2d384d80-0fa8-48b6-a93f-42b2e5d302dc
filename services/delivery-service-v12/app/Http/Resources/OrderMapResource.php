<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderMapResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_order_no,
            'order_no' => $this->order_no,
            'customer' => $this->whenLoaded('customer', function () {
                return [
                    'id' => $this->customer->pk_customer_code,
                    'name' => $this->customer->customer_name,
                    'address' => $this->customer->customer_Address,
                    'phone' => $this->customer->customer_Mobile,
                    'email' => $this->customer->customer_Email,
                    'coordinates' => $this->customer->latitude && $this->customer->longitude ? [
                        'lat' => (float) $this->customer->latitude,
                        'lng' => (float) $this->customer->longitude,
                    ] : null,
                    'dabbawala' => [
                        'code_type' => $this->customer->dabbawala_code_type,
                        'code' => $this->customer->dabbawala_code,
                        'image' => $this->customer->dabbawala_image,
                        'status' => $this->customer->dabba_status,
                    ],
                ];
            }),
            'kitchen' => $this->whenLoaded('location', function () {
                return [
                    'id' => $this->location->pk_location_code,
                    'name' => $this->location->location,
                    'address' => $this->location->address,
                    'city' => $this->location->city,
                    'coordinates' => $this->location->latitude && $this->location->longitude ? [
                        'lat' => (float) $this->location->latitude,
                        'lng' => (float) $this->location->longitude,
                    ] : null,
                ];
            }),
            'delivery_person' => $this->whenLoaded('deliveryPerson', function () {
                return [
                    'id' => $this->deliveryPerson->id,
                    'name' => $this->deliveryPerson->name,
                    'phone' => $this->deliveryPerson->phone,
                    'email' => $this->deliveryPerson->email,
                    'profile_photo' => $this->deliveryPerson->profile_photo_url,
                    'vehicle_type' => $this->deliveryPerson->vehicle_type,
                    'vehicle_number' => $this->deliveryPerson->vehicle_number,
                    'current_location' => $this->deliveryPerson->current_latitude && $this->deliveryPerson->current_longitude ? [
                        'lat' => (float) $this->deliveryPerson->current_latitude,
                        'lng' => (float) $this->deliveryPerson->current_longitude,
                        'last_update' => $this->deliveryPerson->last_location_update,
                    ] : null,
                    'rating' => $this->deliveryPerson->rating,
                ];
            }),
            'order_date' => $this->order_date,
            'meal_type' => $this->order_menu,
            'delivery_status' => $this->delivery_status,
            'order_status' => $this->order_status,
            'amount' => $this->amount,
            'route' => $this->whenLoaded('deliveryRoute', function () {
                return [
                    'id' => $this->deliveryRoute->id,
                    'distance_km' => $this->deliveryRoute->distance_km,
                    'duration_seconds' => $this->deliveryRoute->duration_seconds,
                    'duration_minutes' => round($this->deliveryRoute->duration_seconds / 60),
                    'estimated_delivery_time' => $this->deliveryRoute->estimated_delivery_time,
                    'formatted_estimated_delivery_time' => $this->deliveryRoute->estimated_delivery_time ? $this->deliveryRoute->estimated_delivery_time->format('h:i A') : null,
                    'traffic_condition' => $this->deliveryRoute->traffic_condition,
                    'delivery_zone' => $this->deliveryRoute->delivery_zone,
                    'delivery_fee' => $this->deliveryRoute->delivery_fee,
                    'route_geometry' => $this->deliveryRoute->route_geometry,
                ];
            }),
        ];
    }
}

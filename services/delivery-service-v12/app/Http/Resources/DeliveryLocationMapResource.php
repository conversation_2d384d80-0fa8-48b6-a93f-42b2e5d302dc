<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryLocationMapResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_location_code,
            'name' => $this->location,
            'address' => $this->fullAddress,
            'city' => $this->city,
            'pin' => $this->pin,
            'coordinates' => [
                'lat' => $this->latitude,
                'lng' => $this->longitude,
            ],
            'is_kitchen' => true,
            'is_default' => $this->is_default,
            'status' => $this->status,
        ];
    }
}

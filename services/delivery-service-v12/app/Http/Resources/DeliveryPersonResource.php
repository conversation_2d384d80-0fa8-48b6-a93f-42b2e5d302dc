<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryPersonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'profile_photo' => $this->profile_photo_url,
            'id_proof_type' => $this->id_proof_type,
            'id_proof_number' => $this->id_proof_number,
            'id_proof_image' => $this->id_proof_image_url,
            'emergency_contact_name' => $this->emergency_contact_name,
            'emergency_contact_phone' => $this->emergency_contact_phone,
            'address' => $this->address,
            'vehicle_type' => $this->vehicle_type,
            'vehicle_number' => $this->vehicle_number,
            'vehicle_details' => $this->vehicle_details,
            'status' => $this->status,
            'is_active' => $this->is_active,
            'on_duty' => $this->on_duty,
            'is_available' => $this->is_available,
            'current_location' => $this->when($this->current_latitude && $this->current_longitude, [
                'latitude' => $this->current_latitude,
                'longitude' => $this->current_longitude,
                'last_update' => $this->last_location_update,
            ]),
            'performance' => [
                'rating' => $this->rating,
                'total_ratings' => $this->total_ratings,
                'total_deliveries' => $this->total_deliveries,
                'on_time_deliveries' => $this->on_time_deliveries,
                'late_deliveries' => $this->late_deliveries,
                'failed_deliveries' => $this->failed_deliveries,
                'on_time_percentage' => $this->on_time_percentage,
                'completion_rate' => $this->completion_rate,
            ],
            'joining_date' => $this->joining_date,
            'service_areas' => $this->whenLoaded('serviceAreas', function () {
                return DeliveryStaffServiceAreaResource::collection($this->serviceAreas);
            }),
            'schedules' => $this->whenLoaded('schedules', function () {
                return DeliveryStaffScheduleResource::collection($this->schedules);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

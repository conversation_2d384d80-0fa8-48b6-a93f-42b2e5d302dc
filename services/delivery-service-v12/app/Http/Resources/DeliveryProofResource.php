<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryProofResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'delivery_person_id' => $this->delivery_person_id,
            'proof_type' => $this->proof_type,
            'proof_type_display' => $this->proof_type_display,
            'image_url' => $this->image_url,
            'location' => $this->when($this->latitude && $this->longitude, [
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ]),
            'notes' => $this->notes,
            'delivery_person' => $this->whenLoaded('deliveryPerson', function () {
                return [
                    'id' => $this->deliveryPerson->id,
                    'name' => $this->deliveryPerson->name,
                ];
            }),
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->pk_order_no,
                    'order_no' => $this->order->order_no,
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

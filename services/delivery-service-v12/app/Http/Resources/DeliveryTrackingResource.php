<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryTrackingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'delivery_person_id' => $this->delivery_person_id,
            'status' => $this->status,
            'pending_at' => $this->pending_at,
            'dispatched_at' => $this->dispatched_at,
            'in_transit_at' => $this->in_transit_at,
            'delivered_at' => $this->delivered_at,
            'failed_at' => $this->failed_at,
            'current_location' => $this->when($this->current_latitude && $this->current_longitude, [
                'latitude' => $this->current_latitude,
                'longitude' => $this->current_longitude,
                'last_update' => $this->last_location_update,
            ]),
            'distance_traveled' => $this->distance_traveled,
            'travel_time' => $this->travel_time,
            'route_history' => $this->route_history,
            'notes' => $this->notes,
            'estimated_arrival' => $this->estimated_arrival,
            'formatted_estimated_arrival' => $this->formatted_estimated_arrival,
            'remaining_time' => $this->remaining_time,
            'formatted_remaining_time' => $this->formatted_remaining_time,
            'completion_percentage' => $this->completion_percentage,
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->pk_order_no,
                    'order_no' => $this->order->order_no,
                    'customer_name' => $this->order->customer_name,
                    'customer_address' => $this->order->customer_Address,
                    'delivery_status' => $this->order->delivery_status,
                    'order_status' => $this->order->order_status,
                    'order_date' => $this->order->order_date,
                    'order_menu' => $this->order->order_menu,
                    'amount' => $this->order->amount,
                ];
            }),
            'delivery_person' => $this->whenLoaded('deliveryPerson', function () {
                return [
                    'id' => $this->deliveryPerson->id,
                    'name' => $this->deliveryPerson->name,
                    'phone' => $this->deliveryPerson->phone,
                    'email' => $this->deliveryPerson->email,
                    'profile_photo' => $this->deliveryPerson->profile_photo_url,
                    'vehicle_type' => $this->deliveryPerson->vehicle_type,
                    'vehicle_number' => $this->deliveryPerson->vehicle_number,
                    'rating' => $this->deliveryPerson->rating,
                ];
            }),
            'proofs' => $this->whenLoaded('proofs', function () {
                return DeliveryProofResource::collection($this->proofs);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

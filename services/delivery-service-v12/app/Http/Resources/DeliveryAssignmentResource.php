<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'delivery_person_id' => $this->delivery_person_id,
            'assignment_type' => $this->assignment_type,
            'batch_id' => $this->batch_id,
            'status' => $this->status,
            'assigned_at' => $this->assigned_at,
            'accepted_at' => $this->accepted_at,
            'rejected_at' => $this->rejected_at,
            'completed_at' => $this->completed_at,
            'cancelled_at' => $this->cancelled_at,
            'notes' => $this->notes,
            'assigned_by' => $this->assigned_by,
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->pk_order_no,
                    'order_no' => $this->order->order_no,
                    'customer_name' => $this->order->customer_name,
                    'customer_address' => $this->order->customer_Address,
                    'delivery_status' => $this->order->delivery_status,
                    'order_status' => $this->order->order_status,
                    'order_date' => $this->order->order_date,
                    'order_menu' => $this->order->order_menu,
                    'amount' => $this->order->amount,
                ];
            }),
            'delivery_person' => $this->whenLoaded('deliveryPerson', function () {
                return [
                    'id' => $this->deliveryPerson->id,
                    'name' => $this->deliveryPerson->name,
                    'phone' => $this->deliveryPerson->phone,
                    'email' => $this->deliveryPerson->email,
                    'current_location' => $this->deliveryPerson->current_latitude && $this->deliveryPerson->current_longitude ? [
                        'latitude' => $this->deliveryPerson->current_latitude,
                        'longitude' => $this->deliveryPerson->current_longitude,
                        'last_update' => $this->deliveryPerson->last_location_update,
                    ] : null,
                ];
            }),
            'assigned_by_user' => $this->whenLoaded('assignedBy', function () {
                return [
                    'id' => $this->assignedBy->id,
                    'name' => $this->assignedBy->first_name . ' ' . $this->assignedBy->last_name,
                    'email' => $this->assignedBy->email,
                ];
            }),
            'batch' => $this->whenLoaded('batch', function () {
                return [
                    'id' => $this->batch->id,
                    'batch_number' => $this->batch->batch_number,
                    'status' => $this->batch->status,
                ];
            }),
            'notifications' => $this->whenLoaded('notifications', function () {
                return DeliveryAssignmentNotificationResource::collection($this->notifications);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

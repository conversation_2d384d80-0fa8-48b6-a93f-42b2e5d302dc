<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryLocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'location' => $this->location,
            'city' => $this->city,
            'sub_city_area' => $this->sub_city_area,
            'pin' => $this->pin,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'geocoded_address' => $this->geocoded_address,
            'delivery_charges' => $this->delivery_charges,
            'delivery_time' => $this->delivery_time,
            'is_default' => $this->is_default,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

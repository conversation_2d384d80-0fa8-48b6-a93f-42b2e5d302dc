<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryRouteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'kitchen_id' => $this->kitchen_id,
            'delivery_person_id' => $this->delivery_person_id,
            'distance_km' => $this->distance_km,
            'duration_seconds' => $this->duration_seconds,
            'duration_minutes' => $this->durationMinutes,
            'start_coordinates' => [
                'lat' => $this->start_lat,
                'lng' => $this->start_lon,
            ],
            'end_coordinates' => [
                'lat' => $this->end_lat,
                'lng' => $this->end_lon,
            ],
            'estimated_delivery_time' => $this->estimated_delivery_time,
            'formatted_estimated_delivery_time' => $this->formattedEstimatedDeliveryTime,
            'actual_delivery_time' => $this->actual_delivery_time,
            'formatted_actual_delivery_time' => $this->formattedActualDeliveryTime,
            'traffic_condition' => $this->traffic_condition,
            'delivery_zone' => $this->delivery_zone,
            'delivery_fee' => $this->delivery_fee,
            'route_geometry' => $this->route_geometry,
            'route_steps' => $this->when($request->has('include_steps'), $this->route_steps),
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->pk_order_no,
                    'order_no' => $this->order->order_no,
                    'customer_name' => $this->order->customer_name,
                    'delivery_status' => $this->order->delivery_status,
                ];
            }),
            'kitchen' => $this->whenLoaded('kitchen', function () {
                return [
                    'id' => $this->kitchen->pk_location_code,
                    'name' => $this->kitchen->location,
                ];
            }),
            'delivery_person' => $this->whenLoaded('deliveryPerson', function () {
                return [
                    'id' => $this->deliveryPerson->id,
                    'name' => $this->deliveryPerson->name,
                ];
            }),
        ];
    }
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class RoutingService
{
    /**
     * The base URL for the OSRM API.
     *
     * @var string
     */
    protected string $apiUrl;

    /**
     * The routing profile to use.
     *
     * @var string
     */
    protected string $profile;

    /**
     * The cache TTL in seconds.
     *
     * @var int
     */
    protected int $cacheTtl;

    /**
     * Create a new RoutingService instance.
     */
    public function __construct()
    {
        $this->apiUrl = config('maps.osrm_url', 'https://router.project-osrm.org');
        $this->profile = config('maps.osrm_profile', 'driving');
        $this->cacheTtl = config('maps.osrm_cache_ttl', 86400); // 24 hours

        // Log the API URL for debugging
        \Illuminate\Support\Facades\Log::debug('RoutingService initialized with API URL: ' . $this->apiUrl);
    }

    /**
     * Calculate a route between two points.
     *
     * @param float $startLat Starting latitude
     * @param float $startLon Starting longitude
     * @param float $endLat Ending latitude
     * @param float $endLon Ending longitude
     * @param bool $includeGeometry Whether to include the route geometry
     * @param bool $includeSteps Whether to include turn-by-turn instructions
     * @return array|null Route information or null if calculation failed
     */
    public function calculateRoute(
        float $startLat,
        float $startLon,
        float $endLat,
        float $endLon,
        bool $includeGeometry = false,
        bool $includeSteps = false
    ): ?array {
        // Create a cache key based on the parameters
        $cacheKey = 'route_' . md5("$startLat,$startLon,$endLat,$endLon,$includeGeometry,$includeSteps");

        // Check if we have a cached result
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Build the coordinates string
            $coordinates = "$startLon,$startLat;$endLon,$endLat";

            // Build the options
            $options = [
                'overview' => $includeGeometry ? 'full' : 'simplified',
                'steps' => $includeSteps,
                'geometries' => 'geojson',
            ];

            // Make the API request
            $response = Http::get("{$this->apiUrl}/route/v1/{$this->profile}/{$coordinates}", $options);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['code']) && $data['code'] === 'Ok' && !empty($data['routes'])) {
                    $route = $data['routes'][0];

                    $result = [
                        'distance' => $route['distance'] / 1000, // Convert to kilometers
                        'duration' => $route['duration'], // In seconds
                        'geometry' => $includeGeometry ? $route['geometry'] : null,
                        'steps' => $includeSteps ? $route['legs'][0]['steps'] : null,
                    ];

                    // Cache the result
                    Cache::put($cacheKey, $result, $this->cacheTtl);

                    return $result;
                }
            }

            // If we get here, route calculation failed
            Log::warning('Route calculation failed', [
                'coordinates' => $coordinates,
                'response' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception during route calculation: ' . $e->getMessage(), [
                'coordinates' => "$startLon,$startLat;$endLon,$endLat",
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Calculate a route with multiple waypoints.
     *
     * @param array $coordinates Array of [lat, lon] pairs
     * @param bool $includeGeometry Whether to include the route geometry
     * @param bool $includeSteps Whether to include turn-by-turn instructions
     * @return array|null Route information or null if calculation failed
     */
    public function calculateRouteWithWaypoints(
        array $coordinates,
        bool $includeGeometry = false,
        bool $includeSteps = false
    ): ?array {
        if (count($coordinates) < 2) {
            return null;
        }

        // Create a cache key based on the parameters
        $cacheKey = 'route_waypoints_' . md5(json_encode($coordinates) . "$includeGeometry,$includeSteps");

        // Check if we have a cached result
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Build the coordinates string
            $coordinatesString = implode(';', array_map(function ($coord) {
                return $coord[1] . ',' . $coord[0]; // OSRM expects lon,lat
            }, $coordinates));

            // Build the options
            $options = [
                'overview' => $includeGeometry ? 'full' : 'simplified',
                'steps' => $includeSteps,
                'geometries' => 'geojson',
            ];

            // Make the API request
            $response = Http::get("{$this->apiUrl}/route/v1/{$this->profile}/{$coordinatesString}", $options);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['code']) && $data['code'] === 'Ok' && !empty($data['routes'])) {
                    $route = $data['routes'][0];

                    $result = [
                        'distance' => $route['distance'] / 1000, // Convert to kilometers
                        'duration' => $route['duration'], // In seconds
                        'geometry' => $includeGeometry ? $route['geometry'] : null,
                        'legs' => $route['legs'],
                    ];

                    // Cache the result
                    Cache::put($cacheKey, $result, $this->cacheTtl);

                    return $result;
                }
            }

            // If we get here, route calculation failed
            Log::warning('Route calculation with waypoints failed', [
                'coordinates' => $coordinatesString,
                'response' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception during route calculation with waypoints: ' . $e->getMessage(), [
                'coordinates' => $coordinates,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Optimize a route with multiple stops.
     *
     * @param array $coordinates Array of [lat, lon] pairs
     * @param bool $roundTrip Whether the route should return to the starting point
     * @param bool $includeGeometry Whether to include the route geometry
     * @return array|null Optimized route information or null if optimization failed
     */
    public function optimizeRoute(
        array $coordinates,
        bool $roundTrip = false,
        bool $includeGeometry = false
    ): ?array {
        if (count($coordinates) < 2) {
            return null;
        }

        // Create a cache key based on the parameters
        $cacheKey = 'route_optimize_' . md5(json_encode($coordinates) . "$roundTrip,$includeGeometry");

        // Check if we have a cached result
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Build the coordinates string
            $coordinatesString = implode(';', array_map(function ($coord) {
                return $coord[1] . ',' . $coord[0]; // OSRM expects lon,lat
            }, $coordinates));

            // Build the options
            $options = [
                'overview' => $includeGeometry ? 'full' : 'simplified',
                'geometries' => 'geojson',
                'roundtrip' => $roundTrip,
            ];

            // Make the API request
            $response = Http::get("{$this->apiUrl}/trip/v1/{$this->profile}/{$coordinatesString}", $options);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['code']) && $data['code'] === 'Ok' && !empty($data['trips'])) {
                    $trip = $data['trips'][0];

                    $result = [
                        'distance' => $trip['distance'] / 1000, // Convert to kilometers
                        'duration' => $trip['duration'], // In seconds
                        'geometry' => $includeGeometry ? $trip['geometry'] : null,
                        'waypoint_order' => array_map(function ($waypoint) {
                            return $waypoint['waypoint_index'];
                        }, $data['waypoints']),
                    ];

                    // Cache the result
                    Cache::put($cacheKey, $result, $this->cacheTtl);

                    return $result;
                }
            }

            // If we get here, route optimization failed
            Log::warning('Route optimization failed', [
                'coordinates' => $coordinatesString,
                'response' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception during route optimization: ' . $e->getMessage(), [
                'coordinates' => $coordinates,
                'exception' => $e,
            ]);

            return null;
        }
    }
}

<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\DeliveryLocation;
use App\Models\DeliveryRoute;
use App\Models\Order;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeliveryOptimizationService
{
    /**
     * The routing service instance.
     *
     * @var RoutingService
     */
    protected RoutingService $routingService;

    /**
     * The geocoding service instance.
     *
     * @var GeocodingService
     */
    protected GeocodingService $geocodingService;

    /**
     * The delivery zone service instance.
     *
     * @var DeliveryZoneService
     */
    protected DeliveryZoneService $zoneService;

    /**
     * Create a new DeliveryOptimizationService instance.
     *
     * @param RoutingService $routingService
     * @param GeocodingService $geocodingService
     * @param DeliveryZoneService $zoneService
     */
    public function __construct(
        RoutingService $routingService,
        GeocodingService $geocodingService,
        DeliveryZoneService $zoneService
    ) {
        $this->routingService = $routingService;
        $this->geocodingService = $geocodingService;
        $this->zoneService = $zoneService;
    }

    /**
     * Calculate and store route information for an order.
     *
     * @param int $orderId
     * @return DeliveryRoute|null
     */
    public function calculateOrderRoute(int $orderId): ?DeliveryRoute
    {
        try {
            $order = Order::findOrFail($orderId);
            $customer = Customer::findOrFail($order->customer_code);
            $kitchen = DeliveryLocation::where('pk_location_code', $order->fk_kitchen_code)->first();
            
            if (!$kitchen || !$customer) {
                Log::error('Kitchen or customer not found for order', [
                    'order_id' => $orderId,
                    'kitchen_code' => $order->fk_kitchen_code,
                    'customer_code' => $order->customer_code,
                ]);
                
                return null;
            }
            
            // Ensure we have coordinates for both locations
            if (!$kitchen->latitude || !$kitchen->longitude || !$customer->latitude || !$customer->longitude) {
                Log::error('Missing coordinates for kitchen or customer', [
                    'order_id' => $orderId,
                    'kitchen_has_coordinates' => ($kitchen->latitude && $kitchen->longitude),
                    'customer_has_coordinates' => ($customer->latitude && $customer->longitude),
                ]);
                
                return null;
            }
            
            // Calculate the route
            $route = $this->routingService->calculateRoute(
                $kitchen->latitude,
                $kitchen->longitude,
                $customer->latitude,
                $customer->longitude,
                true, // Include geometry
                true  // Include steps
            );
            
            if (!$route) {
                Log::error('Failed to calculate route for order', [
                    'order_id' => $orderId,
                ]);
                
                return null;
            }
            
            // Get the delivery zone and fee
            $zoneInfo = $this->zoneService->getZoneForCustomer(
                $kitchen->pk_location_code,
                $customer->latitude,
                $customer->longitude
            );
            
            // Estimate delivery time based on route duration
            $estimatedDeliveryTime = Carbon::now()->addSeconds($route['duration']);
            
            // Determine traffic condition based on time of day
            $trafficCondition = $this->determineTrafficCondition();
            
            // Adjust delivery time based on traffic
            if ($trafficCondition === 'heavy') {
                $estimatedDeliveryTime->addMinutes(15);
            } elseif ($trafficCondition === 'moderate') {
                $estimatedDeliveryTime->addMinutes(5);
            }
            
            // Create or update the delivery route
            $deliveryRoute = DeliveryRoute::updateOrCreate(
                ['order_id' => $orderId],
                [
                    'kitchen_id' => $kitchen->pk_location_code,
                    'distance_km' => $route['distance'],
                    'duration_seconds' => $route['duration'],
                    'route_geometry' => json_encode($route['geometry']),
                    'route_steps' => json_encode($route['steps']),
                    'start_lat' => $kitchen->latitude,
                    'start_lon' => $kitchen->longitude,
                    'end_lat' => $customer->latitude,
                    'end_lon' => $customer->longitude,
                    'estimated_delivery_time' => $estimatedDeliveryTime,
                    'traffic_condition' => $trafficCondition,
                    'delivery_zone' => $zoneInfo ? $zoneInfo['zone']->zone_number : null,
                    'delivery_fee' => $zoneInfo ? $zoneInfo['delivery_fee'] : null,
                ]
            );
            
            return $deliveryRoute;
        } catch (\Exception $e) {
            Log::error('Exception calculating order route: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'exception' => $e,
            ]);
            
            return null;
        }
    }

    /**
     * Assign delivery persons to orders for a specific date and meal type.
     *
     * @param string $date Date in Y-m-d format
     * @param string $mealType Meal type (lunch, dinner)
     * @return array Assignment results
     */
    public function assignDeliveryPersons(string $date, string $mealType): array
    {
        try {
            // Get all unassigned orders for the date and meal type
            $orders = Order::where('order_date', $date)
                ->where('order_menu', $mealType)
                ->whereNull('delivery_person')
                ->where('delivery_type', 'delivery')
                ->where('order_status', '!=', 'Cancelled')
                ->get();
            
            if ($orders->isEmpty()) {
                return [
                    'success' => true,
                    'message' => 'No unassigned orders found',
                    'assigned_count' => 0,
                ];
            }
            
            // Get available delivery persons
            $deliveryPersons = User::where('role_id', 3) // Delivery role
                ->where('status', true)
                ->get();
            
            if ($deliveryPersons->isEmpty()) {
                return [
                    'success' => false,
                    'message' => 'No available delivery persons found',
                    'assigned_count' => 0,
                ];
            }
            
            // Group orders by kitchen
            $ordersByKitchen = $orders->groupBy('fk_kitchen_code');
            
            $assignedCount = 0;
            
            // Process each kitchen separately
            foreach ($ordersByKitchen as $kitchenCode => $kitchenOrders) {
                // Get the kitchen location
                $kitchen = DeliveryLocation::where('pk_location_code', $kitchenCode)->first();
                
                if (!$kitchen || !$kitchen->latitude || !$kitchen->longitude) {
                    Log::warning('Kitchen location not found or missing coordinates', [
                        'kitchen_code' => $kitchenCode,
                    ]);
                    continue;
                }
                
                // Group orders by location proximity
                $orderClusters = $this->clusterOrdersByLocation($kitchenOrders);
                
                // Assign delivery persons to clusters
                foreach ($orderClusters as $cluster) {
                    // Find the best delivery person for this cluster
                    $bestDeliveryPerson = $this->findBestDeliveryPerson($deliveryPersons, $cluster, $kitchen);
                    
                    if (!$bestDeliveryPerson) {
                        Log::warning('Could not find a suitable delivery person for cluster', [
                            'cluster_size' => count($cluster),
                        ]);
                        continue;
                    }
                    
                    // Optimize the route for this cluster
                    $optimizedRoute = $this->optimizeClusterRoute($cluster, $kitchen);
                    
                    if (!$optimizedRoute) {
                        Log::warning('Could not optimize route for cluster', [
                            'cluster_size' => count($cluster),
                        ]);
                        continue;
                    }
                    
                    // Assign the delivery person to each order in the optimized sequence
                    foreach ($optimizedRoute['waypoint_order'] as $index) {
                        $order = $cluster[$index];
                        $order->delivery_person = $bestDeliveryPerson->id;
                        $order->save();
                        
                        // Update the delivery route with the assigned person
                        DeliveryRoute::where('order_id', $order->pk_order_no)
                            ->update(['delivery_person_id' => $bestDeliveryPerson->id]);
                        
                        $assignedCount++;
                    }
                }
            }
            
            return [
                'success' => true,
                'message' => "Successfully assigned $assignedCount orders to delivery persons",
                'assigned_count' => $assignedCount,
            ];
        } catch (\Exception $e) {
            Log::error('Exception assigning delivery persons: ' . $e->getMessage(), [
                'date' => $date,
                'meal_type' => $mealType,
                'exception' => $e,
            ]);
            
            return [
                'success' => false,
                'message' => 'Error assigning delivery persons: ' . $e->getMessage(),
                'assigned_count' => 0,
            ];
        }
    }

    /**
     * Cluster orders by location proximity.
     *
     * @param Collection $orders
     * @param int $maxClusterSize Maximum number of orders per cluster
     * @return array Array of order clusters
     */
    protected function clusterOrdersByLocation(Collection $orders, int $maxClusterSize = 5): array
    {
        // Get customer coordinates for all orders
        $orderCoordinates = [];
        foreach ($orders as $order) {
            $customer = Customer::find($order->customer_code);
            if ($customer && $customer->latitude && $customer->longitude) {
                $orderCoordinates[$order->pk_order_no] = [
                    'order' => $order,
                    'lat' => $customer->latitude,
                    'lon' => $customer->longitude,
                ];
            }
        }
        
        // If we have no valid coordinates, return each order as its own cluster
        if (empty($orderCoordinates)) {
            return $orders->map(function ($order) {
                return [$order];
            })->toArray();
        }
        
        // Use a simple clustering algorithm based on proximity
        $clusters = [];
        $assigned = [];
        
        foreach ($orderCoordinates as $orderId => $data) {
            if (isset($assigned[$orderId])) {
                continue;
            }
            
            $cluster = [$data['order']];
            $assigned[$orderId] = true;
            
            // Find nearby orders
            $nearbyOrders = [];
            foreach ($orderCoordinates as $otherOrderId => $otherData) {
                if ($orderId === $otherOrderId || isset($assigned[$otherOrderId])) {
                    continue;
                }
                
                $distance = $this->geocodingService->getHaversineDistance(
                    $data['lat'],
                    $data['lon'],
                    $otherData['lat'],
                    $otherData['lon']
                );
                
                $nearbyOrders[$otherOrderId] = [
                    'order' => $otherData['order'],
                    'distance' => $distance,
                ];
            }
            
            // Sort by distance
            uasort($nearbyOrders, function ($a, $b) {
                return $a['distance'] <=> $b['distance'];
            });
            
            // Add closest orders to the cluster until we reach the max size
            foreach ($nearbyOrders as $nearbyOrderId => $nearbyData) {
                if (count($cluster) >= $maxClusterSize) {
                    break;
                }
                
                $cluster[] = $nearbyData['order'];
                $assigned[$nearbyOrderId] = true;
            }
            
            $clusters[] = $cluster;
        }
        
        return $clusters;
    }

    /**
     * Find the best delivery person for a cluster of orders.
     *
     * @param Collection $deliveryPersons
     * @param array $orderCluster
     * @param DeliveryLocation $kitchen
     * @return User|null
     */
    protected function findBestDeliveryPerson(
        Collection $deliveryPersons,
        array $orderCluster,
        DeliveryLocation $kitchen
    ): ?User {
        // For now, use a simple algorithm: assign to the delivery person with the fewest orders
        $deliveryPersonLoads = [];
        
        foreach ($deliveryPersons as $person) {
            $deliveryPersonLoads[$person->id] = Order::where('delivery_person', $person->id)
                ->where('delivery_status', '!=', 'Delivered')
                ->where('delivery_status', '!=', 'Failed')
                ->count();
        }
        
        // Sort by load (ascending)
        asort($deliveryPersonLoads);
        
        // Get the ID of the delivery person with the lowest load
        $bestPersonId = array_key_first($deliveryPersonLoads);
        
        return $deliveryPersons->firstWhere('id', $bestPersonId);
    }

    /**
     * Optimize the route for a cluster of orders.
     *
     * @param array $orderCluster
     * @param DeliveryLocation $kitchen
     * @return array|null
     */
    protected function optimizeClusterRoute(array $orderCluster, DeliveryLocation $kitchen): ?array
    {
        // Prepare coordinates for optimization
        $coordinates = [[$kitchen->latitude, $kitchen->longitude]]; // Start at kitchen
        
        foreach ($orderCluster as $order) {
            $customer = Customer::find($order->customer_code);
            if ($customer && $customer->latitude && $customer->longitude) {
                $coordinates[] = [$customer->latitude, $customer->longitude];
            }
        }
        
        // If we only have the kitchen coordinates, there's nothing to optimize
        if (count($coordinates) <= 1) {
            return null;
        }
        
        // Optimize the route
        return $this->routingService->optimizeRoute($coordinates, true, true);
    }

    /**
     * Determine the current traffic condition based on time of day.
     *
     * @return string 'light', 'moderate', or 'heavy'
     */
    protected function determineTrafficCondition(): string
    {
        $hour = Carbon::now()->hour;
        
        // Morning rush hour: 8-10 AM
        if ($hour >= 8 && $hour < 10) {
            return 'heavy';
        }
        
        // Evening rush hour: 5-7 PM
        if ($hour >= 17 && $hour < 19) {
            return 'heavy';
        }
        
        // Moderate traffic: 7-8 AM, 10 AM-12 PM, 12-2 PM, 4-5 PM, 7-9 PM
        if (
            ($hour >= 7 && $hour < 8) ||
            ($hour >= 10 && $hour < 14) ||
            ($hour >= 16 && $hour < 17) ||
            ($hour >= 19 && $hour < 21)
        ) {
            return 'moderate';
        }
        
        // Light traffic: all other times
        return 'light';
    }
}

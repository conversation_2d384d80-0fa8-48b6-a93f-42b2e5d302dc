<?php

namespace App\Services\Contracts;

use App\DTOs\DeliveryStatusUpdateDTO;
use App\DTOs\OrderDeliveryDTO;

interface DeliveryServiceInterface
{
    public function getDeliveryOrders(int $userId, int $locationId, string $date): array;
    
    public function searchOrders(int $userId, string $searchTerm, int $locationId): array;
    
    public function updateDeliveryStatus(DeliveryStatusUpdateDTO $dto): bool;
    
    public function getDeliveryLocations(int $userId): array;
    
    public function bookThirdPartyDelivery(OrderDeliveryDTO $dto): array;
    
    public function cancelThirdPartyDelivery(int $orderId): bool;
    
    public function getThirdPartyDeliveryStatus(int $orderId): array;
}

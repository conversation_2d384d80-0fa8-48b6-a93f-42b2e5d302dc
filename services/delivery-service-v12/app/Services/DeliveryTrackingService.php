<?php

namespace App\Services;

use App\Events\DeliveryStatusUpdated;
use App\Models\DeliveryPerson;
use App\Models\DeliveryProof;
use App\Models\DeliveryTracking;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DeliveryTrackingService
{
    /**
     * The notification service instance.
     *
     * @var NotificationService
     */
    protected NotificationService $notificationService;

    /**
     * Create a new service instance.
     *
     * @param NotificationService $notificationService
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Get delivery tracking for an order.
     *
     * @param int $orderId
     * @return \App\Models\DeliveryTracking|null
     */
    public function getDeliveryTracking(int $orderId): ?DeliveryTracking
    {
        $tracking = DeliveryTracking::where('order_id', $orderId)
            ->with(['order', 'deliveryPerson', 'proofs'])
            ->first();
        
        if (!$tracking) {
            // Check if the order exists and has a delivery person assigned
            $order = Order::find($orderId);
            
            if (!$order || !$order->delivery_person) {
                return null;
            }
            
            // Get the delivery person
            $deliveryPerson = DeliveryPerson::where('user_id', $order->delivery_person)->first();
            
            if (!$deliveryPerson) {
                return null;
            }
            
            // Create a new tracking record
            $tracking = DeliveryTracking::create([
                'order_id' => $orderId,
                'delivery_person_id' => $deliveryPerson->id,
                'status' => 'pending',
                'pending_at' => now(),
            ]);
            
            // Load relationships
            $tracking->load(['order', 'deliveryPerson', 'proofs']);
        }
        
        return $tracking;
    }
    
    /**
     * Update delivery status.
     *
     * @param int $orderId
     * @param string $status
     * @param string|null $notes
     * @return array
     */
    public function updateDeliveryStatus(int $orderId, string $status, ?string $notes = null): array
    {
        DB::beginTransaction();
        
        try {
            // Get the order
            $order = Order::findOrFail($orderId);
            
            // Get the delivery person
            $deliveryPerson = DeliveryPerson::where('user_id', $order->delivery_person)->first();
            
            if (!$deliveryPerson) {
                throw new \Exception('Delivery person not found');
            }
            
            // Get or create the tracking record
            $tracking = $this->getDeliveryTracking($orderId);
            
            if (!$tracking) {
                throw new \Exception('Failed to create delivery tracking');
            }
            
            // Update the tracking status
            $data = [
                'status' => $status,
            ];
            
            if ($notes) {
                $data['notes'] = $notes;
            }
            
            // Set the appropriate timestamp based on the status
            switch ($status) {
                case 'pending':
                    $data['pending_at'] = now();
                    break;
                case 'dispatched':
                    $data['dispatched_at'] = now();
                    break;
                case 'in_transit':
                    $data['in_transit_at'] = now();
                    break;
                case 'delivered':
                    $data['delivered_at'] = now();
                    break;
                case 'failed':
                    $data['failed_at'] = now();
                    break;
            }
            
            $tracking->update($data);
            
            // Update the order status
            $orderStatus = 'Pending';
            
            switch ($status) {
                case 'pending':
                    $orderStatus = 'Pending';
                    break;
                case 'dispatched':
                    $orderStatus = 'Dispatched';
                    break;
                case 'in_transit':
                    $orderStatus = 'In Transit';
                    break;
                case 'delivered':
                    $orderStatus = 'Delivered';
                    break;
                case 'failed':
                    $orderStatus = 'Failed';
                    break;
            }
            
            $order->update([
                'delivery_status' => $orderStatus,
            ]);
            
            // Update delivery person stats if delivered or failed
            if ($status === 'delivered') {
                $this->updateDeliveryPersonStats($deliveryPerson->id, 'delivered');
            } elseif ($status === 'failed') {
                $this->updateDeliveryPersonStats($deliveryPerson->id, 'failed');
            }
            
            // Dispatch event
            event(new DeliveryStatusUpdated($order, $status));
            
            // Send notification to customer
            $this->sendStatusUpdateNotification($order, $status);
            
            DB::commit();
            
            return [
                'order_id' => $orderId,
                'status' => $status,
                'timestamp' => $data[$status . '_at'],
                'order_status' => $orderStatus,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update delivery status: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'status' => $status,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Update delivery person location.
     *
     * @param int $deliveryPersonId
     * @param float $latitude
     * @param float $longitude
     * @return array
     */
    public function updateDeliveryPersonLocation(int $deliveryPersonId, float $latitude, float $longitude): array
    {
        DB::beginTransaction();
        
        try {
            // Update the delivery person location
            $deliveryPerson = DeliveryPerson::findOrFail($deliveryPersonId);
            
            $deliveryPerson->update([
                'current_latitude' => $latitude,
                'current_longitude' => $longitude,
                'last_location_update' => now(),
            ]);
            
            // Update tracking records for active deliveries
            $trackings = DeliveryTracking::where('delivery_person_id', $deliveryPersonId)
                ->whereIn('status', ['dispatched', 'in_transit'])
                ->get();
            
            foreach ($trackings as $tracking) {
                $this->updateTrackingLocation($tracking, $latitude, $longitude);
            }
            
            DB::commit();
            
            return [
                'delivery_person_id' => $deliveryPersonId,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'timestamp' => now(),
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update delivery person location: ' . $e->getMessage(), [
                'delivery_person_id' => $deliveryPersonId,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Upload delivery proof.
     *
     * @param int $orderId
     * @param string $proofType
     * @param string $image
     * @param string|null $notes
     * @return \App\Models\DeliveryProof
     */
    public function uploadDeliveryProof(int $orderId, string $proofType, string $image, ?string $notes = null): DeliveryProof
    {
        DB::beginTransaction();
        
        try {
            // Get the order
            $order = Order::findOrFail($orderId);
            
            // Get the delivery person
            $deliveryPerson = DeliveryPerson::where('user_id', $order->delivery_person)->first();
            
            if (!$deliveryPerson) {
                throw new \Exception('Delivery person not found');
            }
            
            // Get the tracking record
            $tracking = $this->getDeliveryTracking($orderId);
            
            if (!$tracking) {
                throw new \Exception('Delivery tracking not found');
            }
            
            // Upload the image
            $imagePath = $this->uploadImage($image, 'delivery_proofs');
            
            // Create the proof record
            $proof = DeliveryProof::create([
                'order_id' => $orderId,
                'delivery_person_id' => $deliveryPerson->id,
                'proof_type' => $proofType,
                'image_path' => $imagePath,
                'latitude' => $tracking->current_latitude,
                'longitude' => $tracking->current_longitude,
                'notes' => $notes,
            ]);
            
            // If this is a delivery proof, update the status to delivered
            if ($proofType === 'delivery') {
                $this->updateDeliveryStatus($orderId, 'delivered', 'Delivery proof uploaded');
            }
            
            DB::commit();
            
            return $proof;
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to upload delivery proof: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'proof_type' => $proofType,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Get dashboard data.
     *
     * @param string $date
     * @param string|null $mealType
     * @param int|null $locationId
     * @return array
     */
    public function getDashboardData(string $date, ?string $mealType = null, ?int $locationId = null): array
    {
        // Get orders for the specified date
        $query = Order::where('order_date', $date);
        
        // Filter by meal type
        if ($mealType) {
            $query->where('order_menu', $mealType);
        }
        
        // Filter by location
        if ($locationId) {
            $query->where('fk_kitchen_code', $locationId);
        }
        
        // Get the orders
        $orders = $query->get();
        
        // Calculate statistics
        $totalOrders = $orders->count();
        $pendingOrders = $orders->where('delivery_status', 'Pending')->count();
        $dispatchedOrders = $orders->where('delivery_status', 'Dispatched')->count();
        $inTransitOrders = $orders->where('delivery_status', 'In Transit')->count();
        $deliveredOrders = $orders->where('delivery_status', 'Delivered')->count();
        $failedOrders = $orders->where('delivery_status', 'Failed')->count();
        
        // Calculate delivery person statistics
        $deliveryPersons = DeliveryPerson::where('is_active', true)->get();
        $availableDeliveryPersons = $deliveryPersons->where('on_duty', true)->count();
        $busyDeliveryPersons = $deliveryPersons->where('on_duty', true)->filter(function ($person) use ($orders) {
            return $orders->where('delivery_person', $person->user_id)
                ->whereIn('delivery_status', ['Pending', 'Dispatched', 'In Transit'])
                ->count() > 0;
        })->count();
        
        // Calculate average delivery time
        $deliveredOrdersWithTracking = DeliveryTracking::whereIn('order_id', $orders->where('delivery_status', 'Delivered')->pluck('pk_order_no'))
            ->whereNotNull('dispatched_at')
            ->whereNotNull('delivered_at')
            ->get();
        
        $averageDeliveryTime = 0;
        
        if ($deliveredOrdersWithTracking->count() > 0) {
            $totalDeliveryTime = 0;
            
            foreach ($deliveredOrdersWithTracking as $tracking) {
                $totalDeliveryTime += $tracking->dispatched_at->diffInMinutes($tracking->delivered_at);
            }
            
            $averageDeliveryTime = round($totalDeliveryTime / $deliveredOrdersWithTracking->count());
        }
        
        return [
            'date' => $date,
            'meal_type' => $mealType,
            'location_id' => $locationId,
            'total_orders' => $totalOrders,
            'pending_orders' => $pendingOrders,
            'dispatched_orders' => $dispatchedOrders,
            'in_transit_orders' => $inTransitOrders,
            'delivered_orders' => $deliveredOrders,
            'failed_orders' => $failedOrders,
            'delivery_persons' => [
                'total' => $deliveryPersons->count(),
                'available' => $availableDeliveryPersons,
                'busy' => $busyDeliveryPersons,
            ],
            'average_delivery_time' => $averageDeliveryTime,
        ];
    }
    
    /**
     * Update tracking location.
     *
     * @param \App\Models\DeliveryTracking $tracking
     * @param float $latitude
     * @param float $longitude
     * @return void
     */
    protected function updateTrackingLocation(DeliveryTracking $tracking, float $latitude, float $longitude): void
    {
        // Calculate distance traveled
        $distanceTraveled = $tracking->distance_traveled ?? 0;
        
        if ($tracking->current_latitude && $tracking->current_longitude) {
            $distance = $this->calculateDistance(
                $tracking->current_latitude,
                $tracking->current_longitude,
                $latitude,
                $longitude
            );
            
            // Only add to distance traveled if it's a reasonable value (to filter out GPS errors)
            if ($distance > 0.01 && $distance < 5) {
                $distanceTraveled += $distance;
            }
        }
        
        // Update route history
        $routeHistory = $tracking->route_history ?? [];
        
        $routeHistory[] = [
            'latitude' => $latitude,
            'longitude' => $longitude,
            'timestamp' => now()->toIso8601String(),
        ];
        
        // Limit route history to 100 points
        if (count($routeHistory) > 100) {
            $routeHistory = array_slice($routeHistory, -100);
        }
        
        // Update tracking record
        $tracking->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
            'distance_traveled' => $distanceTraveled,
            'route_history' => $routeHistory,
        ]);
    }
    
    /**
     * Calculate the distance between two points using the Haversine formula.
     *
     * @param float $lat1
     * @param float $lon1
     * @param float $lat2
     * @param float $lon2
     * @return float
     */
    protected function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Radius of the earth in km
        
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        
        $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon / 2) * sin($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earthRadius * $c; // Distance in km
        
        return $distance;
    }
    
    /**
     * Upload an image.
     *
     * @param string $image
     * @param string $path
     * @return string
     */
    protected function uploadImage(string $image, string $path): string
    {
        // Check if the image is a base64 string
        if (Str::startsWith($image, 'data:image')) {
            // Extract the image data
            $imageData = substr($image, strpos($image, ',') + 1);
            $imageData = base64_decode($imageData);
            
            // Generate a unique filename
            $filename = Str::uuid() . '.jpg';
            
            // Store the image
            $fullPath = $path . '/' . $filename;
            Storage::put($fullPath, $imageData);
            
            return $fullPath;
        }
        
        throw new \Exception('Invalid image format');
    }
    
    /**
     * Update delivery person stats.
     *
     * @param int $deliveryPersonId
     * @param string $status
     * @return void
     */
    protected function updateDeliveryPersonStats(int $deliveryPersonId, string $status): void
    {
        try {
            $deliveryPerson = DeliveryPerson::find($deliveryPersonId);
            
            if (!$deliveryPerson) {
                return;
            }
            
            switch ($status) {
                case 'delivered':
                    $deliveryPerson->increment('total_deliveries');
                    
                    // Check if the delivery was on time
                    // TODO: Implement logic to check if the delivery was on time
                    $onTime = true;
                    
                    if ($onTime) {
                        $deliveryPerson->increment('on_time_deliveries');
                    } else {
                        $deliveryPerson->increment('late_deliveries');
                    }
                    break;
                case 'failed':
                    $deliveryPerson->increment('failed_deliveries');
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update delivery person stats: ' . $e->getMessage(), [
                'delivery_person_id' => $deliveryPersonId,
                'status' => $status,
                'exception' => $e,
            ]);
        }
    }
    
    /**
     * Send status update notification to the customer.
     *
     * @param \App\Models\Order $order
     * @param string $status
     * @return void
     */
    protected function sendStatusUpdateNotification(Order $order, string $status): void
    {
        try {
            $customer = $order->customer;
            
            if (!$customer) {
                return;
            }
            
            $statusText = ucfirst($status);
            $message = "Your order #{$order->order_no} has been {$statusText}.";
            
            if ($status === 'in_transit') {
                $message = "Your order #{$order->order_no} is on the way to you.";
            } elseif ($status === 'delivered') {
                $message = "Your order #{$order->order_no} has been delivered. Enjoy your meal!";
            } elseif ($status === 'failed') {
                $message = "We're sorry, but there was an issue with your order #{$order->order_no}. Our customer service team will contact you shortly.";
            }
            
            // Send SMS notification
            if ($customer->customer_Mobile) {
                $this->notificationService->sendSms($customer->customer_Mobile, $message);
            }
            
            // Send email notification
            if ($customer->customer_Email) {
                $this->notificationService->sendEmail(
                    $customer->customer_Email,
                    "Order #{$order->order_no} Status Update",
                    $message
                );
            }
            
            // Send push notification
            if ($customer->user_id) {
                $this->notificationService->sendPushNotification(
                    $customer->user_id,
                    "Order Status Update",
                    $message,
                    [
                        'order_id' => $order->pk_order_no,
                        'status' => $status,
                    ]
                );
            }
        } catch (\Exception $e) {
            Log::error('Failed to send status update notification: ' . $e->getMessage(), [
                'order_id' => $order->pk_order_no,
                'status' => $status,
                'exception' => $e,
            ]);
        }
    }
}

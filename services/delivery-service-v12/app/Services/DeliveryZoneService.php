<?php

namespace App\Services;

use App\Models\DeliveryZone;
use App\Models\DeliveryLocation;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class DeliveryZoneService
{
    /**
     * The geocoding service instance.
     *
     * @var GeocodingService
     */
    protected GeocodingService $geocodingService;

    /**
     * Create a new DeliveryZoneService instance.
     *
     * @param GeocodingService $geocodingService
     */
    public function __construct(GeocodingService $geocodingService)
    {
        $this->geocodingService = $geocodingService;
    }

    /**
     * Get all delivery zones.
     *
     * @param bool $activeOnly Whether to return only active zones
     * @return Collection
     */
    public function getAllZones(bool $activeOnly = true): Collection
    {
        $query = DeliveryZone::query();
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('kitchen_id')
                    ->orderBy('zone_number')
                    ->get();
    }

    /**
     * Get zones for a specific kitchen.
     *
     * @param int $kitchenId
     * @param bool $activeOnly Whether to return only active zones
     * @return Collection
     */
    public function getZonesForKitchen(int $kitchenId, bool $activeOnly = true): Collection
    {
        $query = DeliveryZone::where('kitchen_id', $kitchenId);
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('zone_number')->get();
    }

    /**
     * Create a new delivery zone.
     *
     * @param array $data Zone data
     * @return DeliveryZone|null
     */
    public function createZone(array $data): ?DeliveryZone
    {
        try {
            return DeliveryZone::create($data);
        } catch (\Exception $e) {
            Log::error('Failed to create delivery zone', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }

    /**
     * Update an existing delivery zone.
     *
     * @param int $zoneId
     * @param array $data Zone data
     * @return DeliveryZone|null
     */
    public function updateZone(int $zoneId, array $data): ?DeliveryZone
    {
        try {
            $zone = DeliveryZone::findOrFail($zoneId);
            $zone->update($data);
            
            return $zone->fresh();
        } catch (\Exception $e) {
            Log::error('Failed to update delivery zone', [
                'zone_id' => $zoneId,
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }

    /**
     * Delete a delivery zone.
     *
     * @param int $zoneId
     * @return bool
     */
    public function deleteZone(int $zoneId): bool
    {
        try {
            $zone = DeliveryZone::findOrFail($zoneId);
            return $zone->delete();
        } catch (\Exception $e) {
            Log::error('Failed to delete delivery zone', [
                'zone_id' => $zoneId,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Generate default zones for a kitchen.
     *
     * @param int $kitchenId
     * @param float $baseDeliveryFee
     * @param int $numberOfZones
     * @param float $zoneRadiusKm
     * @param float $additionalFeePerZone
     * @return Collection|null
     */
    public function generateDefaultZones(
        int $kitchenId,
        float $baseDeliveryFee = 30.00,
        int $numberOfZones = 3,
        float $zoneRadiusKm = 5.0,
        float $additionalFeePerZone = 10.00
    ): ?Collection {
        try {
            // Get the kitchen location
            $kitchen = DeliveryLocation::where('pk_location_code', $kitchenId)->first();
            
            if (!$kitchen || !$kitchen->latitude || !$kitchen->longitude) {
                Log::error('Kitchen location not found or coordinates not set', [
                    'kitchen_id' => $kitchenId,
                ]);
                
                return null;
            }
            
            // Delete existing zones for this kitchen
            DeliveryZone::where('kitchen_id', $kitchenId)->delete();
            
            $zones = collect();
            
            // Create zones
            for ($i = 1; $i <= $numberOfZones; $i++) {
                $minDistance = ($i - 1) * $zoneRadiusKm;
                $maxDistance = $i * $zoneRadiusKm;
                $fee = $baseDeliveryFee + (($i - 1) * $additionalFeePerZone);
                
                $zone = DeliveryZone::create([
                    'name' => "Zone $i",
                    'kitchen_id' => $kitchenId,
                    'zone_number' => $i,
                    'min_distance_km' => $minDistance,
                    'max_distance_km' => $maxDistance,
                    'base_delivery_fee' => $fee,
                    'additional_fee' => 0,
                    'is_active' => true,
                    'description' => "Delivery zone $i for kitchen $kitchenId",
                ]);
                
                $zones->push($zone);
            }
            
            return $zones;
        } catch (\Exception $e) {
            Log::error('Failed to generate default zones', [
                'kitchen_id' => $kitchenId,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }

    /**
     * Determine the delivery zone for a customer based on distance from kitchen.
     *
     * @param int $kitchenId
     * @param float $customerLat
     * @param float $customerLon
     * @return array|null Zone information with delivery fee
     */
    public function getZoneForCustomer(int $kitchenId, float $customerLat, float $customerLon): ?array
    {
        try {
            // Get the kitchen location
            $kitchen = DeliveryLocation::where('pk_location_code', $kitchenId)->first();
            
            if (!$kitchen || !$kitchen->latitude || !$kitchen->longitude) {
                Log::error('Kitchen location not found or coordinates not set', [
                    'kitchen_id' => $kitchenId,
                ]);
                
                return null;
            }
            
            // Calculate the distance
            $distance = $this->geocodingService->getHaversineDistance(
                $kitchen->latitude,
                $kitchen->longitude,
                $customerLat,
                $customerLon
            );
            
            // Find the appropriate zone
            $zone = DeliveryZone::where('kitchen_id', $kitchenId)
                ->where('is_active', true)
                ->where('min_distance_km', '<=', $distance)
                ->where('max_distance_km', '>', $distance)
                ->first();
            
            if (!$zone) {
                // Check if we have a zone that covers this distance
                $maxZone = DeliveryZone::where('kitchen_id', $kitchenId)
                    ->where('is_active', true)
                    ->orderBy('max_distance_km', 'desc')
                    ->first();
                
                if ($maxZone && $distance <= $maxZone->max_distance_km) {
                    $zone = $maxZone;
                } else {
                    // Customer is outside all delivery zones
                    return [
                        'zone' => null,
                        'distance' => $distance,
                        'delivery_fee' => null,
                        'is_deliverable' => false,
                        'message' => 'Location is outside delivery area',
                    ];
                }
            }
            
            return [
                'zone' => $zone,
                'distance' => $distance,
                'delivery_fee' => $zone->base_delivery_fee + $zone->additional_fee,
                'is_deliverable' => true,
                'message' => 'Location is within delivery zone ' . $zone->zone_number,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to determine delivery zone', [
                'kitchen_id' => $kitchenId,
                'customer_coordinates' => "$customerLat,$customerLon",
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }
}

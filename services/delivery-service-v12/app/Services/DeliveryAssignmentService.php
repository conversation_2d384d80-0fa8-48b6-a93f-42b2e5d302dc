<?php

namespace App\Services;

use App\Events\DeliveryAssigned;
use App\Events\DeliveryAssignmentStatusUpdated;
use App\Models\DeliveryAssignment;
use App\Models\DeliveryAssignmentBatch;
use App\Models\DeliveryAssignmentNotification;
use App\Models\DeliveryPerson;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeliveryAssignmentService
{
    /**
     * The notification service instance.
     *
     * @var NotificationService
     */
    protected NotificationService $notificationService;
    
    /**
     * The delivery optimization service instance.
     *
     * @var DeliveryOptimizationService
     */
    protected DeliveryOptimizationService $optimizationService;

    /**
     * Create a new service instance.
     *
     * @param NotificationService $notificationService
     * @param DeliveryOptimizationService $optimizationService
     */
    public function __construct(
        NotificationService $notificationService,
        DeliveryOptimizationService $optimizationService
    ) {
        $this->notificationService = $notificationService;
        $this->optimizationService = $optimizationService;
    }

    /**
     * Assign a delivery to a delivery person.
     *
     * @param int $orderId
     * @param int $deliveryPersonId
     * @param string $assignmentType
     * @param string|null $notes
     * @param int|null $assignedBy
     * @param int|null $batchId
     * @return \App\Models\DeliveryAssignment
     */
    public function assignDelivery(
        int $orderId,
        int $deliveryPersonId,
        string $assignmentType = 'manual',
        ?string $notes = null,
        ?int $assignedBy = null,
        ?int $batchId = null
    ): DeliveryAssignment {
        DB::beginTransaction();
        
        try {
            // Check if the order exists
            $order = Order::findOrFail($orderId);
            
            // Check if the delivery person exists
            $deliveryPerson = DeliveryPerson::findOrFail($deliveryPersonId);
            
            // Check if the order is already assigned to this delivery person
            $existingAssignment = DeliveryAssignment::where('order_id', $orderId)
                ->where('delivery_person_id', $deliveryPersonId)
                ->where('status', 'pending')
                ->first();
            
            if ($existingAssignment) {
                return $existingAssignment;
            }
            
            // Create the assignment
            $assignment = DeliveryAssignment::create([
                'order_id' => $orderId,
                'delivery_person_id' => $deliveryPersonId,
                'assignment_type' => $assignmentType,
                'batch_id' => $batchId,
                'status' => 'pending',
                'assigned_at' => now(),
                'notes' => $notes,
                'assigned_by' => $assignedBy,
            ]);
            
            // Update the order's delivery person
            $order->update([
                'delivery_person' => $deliveryPerson->user_id,
                'delivery_status' => 'Pending',
            ]);
            
            // Send notification to the delivery person
            $this->sendAssignmentNotification($assignment);
            
            // Dispatch event
            event(new DeliveryAssigned($assignment));
            
            DB::commit();
            
            return $assignment->fresh(['order', 'deliveryPerson', 'assignedBy', 'batch']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to assign delivery: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'delivery_person_id' => $deliveryPersonId,
                'assignment_type' => $assignmentType,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Update assignment status.
     *
     * @param int $assignmentId
     * @param string $status
     * @param string|null $notes
     * @return \App\Models\DeliveryAssignment
     */
    public function updateAssignmentStatus(int $assignmentId, string $status, ?string $notes = null): DeliveryAssignment
    {
        DB::beginTransaction();
        
        try {
            $assignment = DeliveryAssignment::findOrFail($assignmentId);
            
            // Update the assignment status
            $data = [
                'status' => $status,
            ];
            
            if ($notes) {
                $data['notes'] = $notes;
            }
            
            // Set the appropriate timestamp based on the status
            switch ($status) {
                case 'accepted':
                    $data['accepted_at'] = now();
                    break;
                case 'rejected':
                    $data['rejected_at'] = now();
                    break;
                case 'completed':
                    $data['completed_at'] = now();
                    break;
                case 'cancelled':
                    $data['cancelled_at'] = now();
                    break;
            }
            
            $assignment->update($data);
            
            // Update the order status based on the assignment status
            $order = $assignment->order;
            
            switch ($status) {
                case 'accepted':
                    $order->update(['delivery_status' => 'Dispatched']);
                    break;
                case 'completed':
                    $order->update(['delivery_status' => 'Delivered']);
                    
                    // Update delivery person stats
                    $this->updateDeliveryPersonStats($assignment->delivery_person_id, 'completed');
                    break;
                case 'rejected':
                case 'cancelled':
                    // Reset the order's delivery person
                    $order->update([
                        'delivery_person' => null,
                        'delivery_status' => 'Pending',
                    ]);
                    
                    // Update delivery person stats
                    if ($status === 'rejected') {
                        $this->updateDeliveryPersonStats($assignment->delivery_person_id, 'rejected');
                    }
                    break;
            }
            
            // Dispatch event
            event(new DeliveryAssignmentStatusUpdated($assignment));
            
            DB::commit();
            
            return $assignment->fresh(['order', 'deliveryPerson', 'assignedBy', 'batch']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update assignment status: ' . $e->getMessage(), [
                'assignment_id' => $assignmentId,
                'status' => $status,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Create a batch assignment.
     *
     * @param array $orderIds
     * @param array|null $criteria
     * @param string|null $notes
     * @param int $createdBy
     * @return \App\Models\DeliveryAssignmentBatch
     */
    public function createBatchAssignment(
        array $orderIds,
        ?array $criteria = null,
        ?string $notes = null,
        int $createdBy
    ): DeliveryAssignmentBatch {
        DB::beginTransaction();
        
        try {
            // Create the batch
            $batch = DeliveryAssignmentBatch::create([
                'batch_number' => DeliveryAssignmentBatch::generateBatchNumber(),
                'status' => 'pending',
                'total_orders' => count($orderIds),
                'assigned_orders' => 0,
                'failed_orders' => 0,
                'criteria' => $criteria,
                'notes' => $notes,
                'created_by' => $createdBy,
            ]);
            
            DB::commit();
            
            return $batch;
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to create batch assignment: ' . $e->getMessage(), [
                'order_ids' => $orderIds,
                'criteria' => $criteria,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a batch assignment.
     *
     * @param int $batchId
     * @return \App\Models\DeliveryAssignmentBatch
     */
    public function processBatchAssignment(int $batchId): DeliveryAssignmentBatch
    {
        DB::beginTransaction();
        
        try {
            $batch = DeliveryAssignmentBatch::findOrFail($batchId);
            
            // Check if the batch is already processed
            if ($batch->status !== 'pending') {
                return $batch;
            }
            
            // Update the batch status
            $batch->update([
                'status' => 'processing',
                'processed_at' => now(),
            ]);
            
            // Get the orders for this batch
            $orders = Order::whereIn('pk_order_no', $batch->criteria['order_ids'] ?? [])
                ->where('delivery_status', 'Pending')
                ->whereNull('delivery_person')
                ->get();
            
            // Get available delivery persons
            $deliveryPersons = DeliveryPerson::where('is_active', true)
                ->where('on_duty', true)
                ->get();
            
            if ($deliveryPersons->isEmpty()) {
                // No available delivery persons
                $batch->update([
                    'status' => 'failed',
                    'notes' => ($batch->notes ? $batch->notes . "\n" : '') . 'No available delivery persons',
                ]);
                
                DB::commit();
                
                return $batch;
            }
            
            // Assign orders to delivery persons
            $assignedCount = 0;
            $failedCount = 0;
            
            foreach ($orders as $order) {
                try {
                    // Find the best delivery person for this order
                    $deliveryPerson = $this->findBestDeliveryPerson($order, $deliveryPersons);
                    
                    if (!$deliveryPerson) {
                        $failedCount++;
                        continue;
                    }
                    
                    // Assign the order to the delivery person
                    $this->assignDelivery(
                        $order->pk_order_no,
                        $deliveryPerson->id,
                        'batch',
                        'Batch assignment: ' . $batch->batch_number,
                        $batch->created_by,
                        $batch->id
                    );
                    
                    $assignedCount++;
                } catch (\Exception $e) {
                    Log::error('Failed to assign order in batch: ' . $e->getMessage(), [
                        'batch_id' => $batchId,
                        'order_id' => $order->pk_order_no,
                        'exception' => $e,
                    ]);
                    
                    $failedCount++;
                }
            }
            
            // Update the batch status
            $batch->update([
                'status' => 'completed',
                'assigned_orders' => $assignedCount,
                'failed_orders' => $failedCount,
                'completed_at' => now(),
            ]);
            
            DB::commit();
            
            return $batch->fresh(['createdBy', 'assignments.order', 'assignments.deliveryPerson']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to process batch assignment: ' . $e->getMessage(), [
                'batch_id' => $batchId,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Cancel a batch assignment.
     *
     * @param int $batchId
     * @return \App\Models\DeliveryAssignmentBatch
     */
    public function cancelBatchAssignment(int $batchId): DeliveryAssignmentBatch
    {
        DB::beginTransaction();
        
        try {
            $batch = DeliveryAssignmentBatch::findOrFail($batchId);
            
            // Check if the batch can be cancelled
            if (!in_array($batch->status, ['pending', 'processing'])) {
                throw new \Exception('Batch cannot be cancelled');
            }
            
            // Cancel all pending assignments
            $assignments = DeliveryAssignment::where('batch_id', $batchId)
                ->where('status', 'pending')
                ->get();
            
            foreach ($assignments as $assignment) {
                $this->updateAssignmentStatus(
                    $assignment->id,
                    'cancelled',
                    'Batch cancelled: ' . $batch->batch_number
                );
            }
            
            // Update the batch status
            $batch->update([
                'status' => 'failed',
                'notes' => ($batch->notes ? $batch->notes . "\n" : '') . 'Batch cancelled',
            ]);
            
            DB::commit();
            
            return $batch->fresh(['createdBy', 'assignments.order', 'assignments.deliveryPerson']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to cancel batch assignment: ' . $e->getMessage(), [
                'batch_id' => $batchId,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Find the best delivery person for an order.
     *
     * @param \App\Models\Order $order
     * @param \Illuminate\Database\Eloquent\Collection $deliveryPersons
     * @return \App\Models\DeliveryPerson|null
     */
    protected function findBestDeliveryPerson(Order $order, $deliveryPersons): ?DeliveryPerson
    {
        // Get the delivery location
        $deliveryLocation = $order->location;
        
        if (!$deliveryLocation) {
            return null;
        }
        
        // Get the customer
        $customer = $order->customer;
        
        if (!$customer) {
            return null;
        }
        
        // Filter delivery persons by service area
        $filteredDeliveryPersons = $deliveryPersons->filter(function ($deliveryPerson) use ($deliveryLocation) {
            return $deliveryPerson->serviceAreas->contains('location_id', $deliveryLocation->pk_location_code);
        });
        
        if ($filteredDeliveryPersons->isEmpty()) {
            // If no delivery persons match the service area, use all available delivery persons
            $filteredDeliveryPersons = $deliveryPersons;
        }
        
        // Calculate scores for each delivery person
        $scores = [];
        
        foreach ($filteredDeliveryPersons as $deliveryPerson) {
            // Calculate distance score (lower distance = higher score)
            $distanceScore = 0;
            
            if ($deliveryPerson->current_latitude && $deliveryPerson->current_longitude && $customer->latitude && $customer->longitude) {
                $distance = $this->calculateDistance(
                    $deliveryPerson->current_latitude,
                    $deliveryPerson->current_longitude,
                    $customer->latitude,
                    $customer->longitude
                );
                
                // Normalize distance score (0-100)
                $distanceScore = max(0, 100 - min(100, $distance));
            }
            
            // Calculate workload score (lower workload = higher score)
            $activeAssignments = DeliveryAssignment::where('delivery_person_id', $deliveryPerson->id)
                ->whereIn('status', ['pending', 'accepted'])
                ->count();
            
            // Normalize workload score (0-100)
            $workloadScore = max(0, 100 - min(100, $activeAssignments * 10));
            
            // Calculate rating score (higher rating = higher score)
            $ratingScore = min(100, $deliveryPerson->rating * 20);
            
            // Calculate total score
            $totalScore = ($distanceScore * 0.5) + ($workloadScore * 0.3) + ($ratingScore * 0.2);
            
            $scores[$deliveryPerson->id] = $totalScore;
        }
        
        // Sort delivery persons by score (descending)
        arsort($scores);
        
        // Get the delivery person with the highest score
        $bestDeliveryPersonId = key($scores);
        
        return $filteredDeliveryPersons->firstWhere('id', $bestDeliveryPersonId);
    }
    
    /**
     * Calculate the distance between two points using the Haversine formula.
     *
     * @param float $lat1
     * @param float $lon1
     * @param float $lat2
     * @param float $lon2
     * @return float
     */
    protected function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Radius of the earth in km
        
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        
        $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon / 2) * sin($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earthRadius * $c; // Distance in km
        
        return $distance;
    }
    
    /**
     * Send assignment notification.
     *
     * @param \App\Models\DeliveryAssignment $assignment
     * @return void
     */
    protected function sendAssignmentNotification(DeliveryAssignment $assignment): void
    {
        try {
            $deliveryPerson = $assignment->deliveryPerson;
            $order = $assignment->order;
            
            if (!$deliveryPerson || !$order) {
                return;
            }
            
            // Create notification message
            $message = "New delivery assignment: Order #{$order->order_no} for {$order->customer_name}";
            
            // Send SMS notification
            if ($deliveryPerson->phone) {
                $this->createNotification($assignment, 'sms', $message);
                $this->notificationService->sendSms($deliveryPerson->phone, $message);
            }
            
            // Send email notification
            if ($deliveryPerson->email) {
                $this->createNotification($assignment, 'email', $message);
                $this->notificationService->sendEmail(
                    $deliveryPerson->email,
                    'New Delivery Assignment',
                    $message
                );
            }
            
            // Send push notification
            $this->createNotification($assignment, 'push', $message);
            $this->notificationService->sendPushNotification(
                $deliveryPerson->user_id,
                'New Delivery Assignment',
                $message,
                [
                    'assignment_id' => $assignment->id,
                    'order_id' => $order->pk_order_no,
                ]
            );
            
            // Send in-app notification
            $this->createNotification($assignment, 'in_app', $message);
            $this->notificationService->sendInAppNotification(
                $deliveryPerson->user_id,
                'New Delivery Assignment',
                $message,
                [
                    'assignment_id' => $assignment->id,
                    'order_id' => $order->pk_order_no,
                ]
            );
        } catch (\Exception $e) {
            Log::error('Failed to send assignment notification: ' . $e->getMessage(), [
                'assignment_id' => $assignment->id,
                'exception' => $e,
            ]);
        }
    }
    
    /**
     * Create a notification record.
     *
     * @param \App\Models\DeliveryAssignment $assignment
     * @param string $channel
     * @param string $message
     * @param array|null $metadata
     * @return \App\Models\DeliveryAssignmentNotification
     */
    protected function createNotification(
        DeliveryAssignment $assignment,
        string $channel,
        string $message,
        ?array $metadata = null
    ): DeliveryAssignmentNotification {
        return DeliveryAssignmentNotification::create([
            'assignment_id' => $assignment->id,
            'channel' => $channel,
            'status' => 'pending',
            'message' => $message,
            'metadata' => $metadata,
        ]);
    }
    
    /**
     * Update delivery person stats.
     *
     * @param int $deliveryPersonId
     * @param string $status
     * @return void
     */
    protected function updateDeliveryPersonStats(int $deliveryPersonId, string $status): void
    {
        try {
            $deliveryPerson = DeliveryPerson::find($deliveryPersonId);
            
            if (!$deliveryPerson) {
                return;
            }
            
            switch ($status) {
                case 'completed':
                    $deliveryPerson->increment('total_deliveries');
                    
                    // Check if the delivery was on time
                    // TODO: Implement logic to check if the delivery was on time
                    $onTime = true;
                    
                    if ($onTime) {
                        $deliveryPerson->increment('on_time_deliveries');
                    } else {
                        $deliveryPerson->increment('late_deliveries');
                    }
                    break;
                case 'rejected':
                    $deliveryPerson->increment('failed_deliveries');
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update delivery person stats: ' . $e->getMessage(), [
                'delivery_person_id' => $deliveryPersonId,
                'status' => $status,
                'exception' => $e,
            ]);
        }
    }
}

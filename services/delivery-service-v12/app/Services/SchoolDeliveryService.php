<?php

namespace App\Services;

use App\Models\SchoolDeliveryBatch;
use App\Models\SchoolDeliveryItem;
use App\Models\School;
use App\Models\DeliveryPerson;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * SchoolDeliveryService
 *
 * Business logic for school delivery batch coordination, break time-aligned
 * scheduling, and delivery performance tracking.
 */
class SchoolDeliveryService
{
    /**
     * Get delivery batches with filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getDeliveryBatches(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = SchoolDeliveryBatch::with(['school', 'deliveryItems.childProfile', 'deliveryItems.mealPlan']);

        // Apply filters
        foreach ($filters as $key => $value) {
            if ($value !== null) {
                switch ($key) {
                    case 'date_from':
                        $query->where('delivery_date', '>=', $value);
                        break;
                    case 'date_to':
                        $query->where('delivery_date', '<=', $value);
                        break;
                    default:
                        $query->where($key, $value);
                        break;
                }
            }
        }

        return $query->orderBy('delivery_date', 'desc')
            ->orderBy('scheduled_delivery_time', 'asc')
            ->paginate($perPage);
    }

    /**
     * Get a delivery batch by ID.
     *
     * @param int $id
     * @return SchoolDeliveryBatch|null
     */
    public function getDeliveryBatchById(int $id): ?SchoolDeliveryBatch
    {
        return SchoolDeliveryBatch::with([
            'school',
            'deliveryItems.childProfile.parentCustomer',
            'deliveryItems.subscription',
            'deliveryItems.mealPlan'
        ])->find($id);
    }

    /**
     * Create a new delivery batch.
     *
     * @param array $data
     * @return SchoolDeliveryBatch
     * @throws \Exception
     */
    public function createDeliveryBatch(array $data): SchoolDeliveryBatch
    {
        DB::beginTransaction();

        try {
            // Validate school exists
            $school = School::find($data['school_id']);
            if (!$school || !$school->isPartnershipActive()) {
                throw new \Exception('School not found or partnership not active');
            }

            // Generate batch number
            $data['batch_number'] = $this->generateBatchNumber($data['school_id'], $data['delivery_date']);

            // Calculate scheduled delivery time based on break time
            $data['scheduled_delivery_time'] = $this->calculateDeliveryTime(
                $school,
                $data['break_time_slot']
            );

            // Set default values
            $data['status'] = 'scheduled';
            $data['total_meals'] = 0;
            $data['total_children'] = 0;

            $batch = SchoolDeliveryBatch::create($data);

            // Generate delivery items for active subscriptions
            $this->generateDeliveryItems($batch);

            DB::commit();

            return $batch->load(['school', 'deliveryItems']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update delivery batch status.
     *
     * @param int $id
     * @param string $status
     * @param string|null $notes
     * @param array|null $locationData
     * @return SchoolDeliveryBatch|null
     */
    public function updateDeliveryBatchStatus(int $id, string $status, ?string $notes = null, ?array $locationData = null): ?SchoolDeliveryBatch
    {
        DB::beginTransaction();

        try {
            $batch = SchoolDeliveryBatch::find($id);

            if (!$batch) {
                return null;
            }

            $updateData = [
                'status' => $status,
                'status_notes' => $notes,
            ];

            // Update status history
            $statusHistory = $batch->status_history ?? [];
            $statusHistory[] = [
                'status' => $status,
                'timestamp' => now()->toISOString(),
                'notes' => $notes,
                'location' => $locationData,
            ];
            $updateData['status_history'] = $statusHistory;

            // Update specific timestamps based on status
            switch ($status) {
                case 'preparing':
                    $updateData['preparation_start_time'] = now()->format('H:i:s');
                    break;
                case 'ready':
                    $updateData['preparation_end_time'] = now()->format('H:i:s');
                    break;
                case 'dispatched':
                    $updateData['dispatch_time'] = now()->format('H:i:s');
                    break;
                case 'in_transit':
                    // Update location if provided
                    if ($locationData) {
                        $updateData['current_location'] = $locationData;
                    }
                    break;
                case 'delivered':
                    $updateData['actual_delivery_time'] = now()->format('H:i:s');
                    $updateData['arrival_time'] = now()->format('H:i:s');
                    $updateData['delivery_confirmed_at'] = now();
                    
                    // Calculate delivery performance
                    $this->calculateDeliveryPerformance($batch);
                    break;
            }

            $batch->update($updateData);

            DB::commit();

            return $batch->load(['school', 'deliveryItems']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Assign delivery person to a batch.
     *
     * @param int $batchId
     * @param int $deliveryPersonId
     * @param string|null $vehicleNumber
     * @param string|null $estimatedTime
     * @return SchoolDeliveryBatch|null
     */
    public function assignDeliveryPerson(int $batchId, int $deliveryPersonId, ?string $vehicleNumber = null, ?string $estimatedTime = null): ?SchoolDeliveryBatch
    {
        DB::beginTransaction();

        try {
            $batch = SchoolDeliveryBatch::find($batchId);
            $deliveryPerson = DeliveryPerson::find($deliveryPersonId);

            if (!$batch || !$deliveryPerson) {
                throw new \Exception('Batch or delivery person not found');
            }

            if (!$deliveryPerson->is_active || !$deliveryPerson->on_duty) {
                throw new \Exception('Delivery person is not available');
            }

            $batch->update([
                'delivery_person_id' => $deliveryPersonId,
                'delivery_person_name' => $deliveryPerson->name,
                'delivery_person_phone' => $deliveryPerson->phone,
                'vehicle_number' => $vehicleNumber ?? $deliveryPerson->vehicle_number,
                'vehicle_type' => $deliveryPerson->vehicle_type,
                'estimated_duration_minutes' => $estimatedTime ? (int)$estimatedTime : null,
            ]);

            DB::commit();

            return $batch->load(['school', 'deliveryItems']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get delivery batches for a specific school.
     *
     * @param int $schoolId
     * @param array $filters
     * @return Collection
     */
    public function getSchoolDeliveryBatches(int $schoolId, array $filters = []): Collection
    {
        $query = SchoolDeliveryBatch::where('school_id', $schoolId)
            ->with(['deliveryItems.childProfile', 'deliveryItems.mealPlan']);

        foreach ($filters as $key => $value) {
            if ($value !== null && $key !== 'school_id') {
                $query->where($key, $value);
            }
        }

        return $query->orderBy('delivery_date', 'desc')
            ->orderBy('scheduled_delivery_time', 'asc')
            ->get();
    }

    /**
     * Get delivery schedule for a school on a specific date.
     *
     * @param int $schoolId
     * @param string $date
     * @return array
     */
    public function getSchoolDeliverySchedule(int $schoolId, string $date): array
    {
        $school = School::with(['childProfiles.schoolMealSubscriptions' => function ($query) use ($date) {
            $query->where('status', 'active')
                ->whereJsonContains('delivery_days', strtolower(Carbon::parse($date)->format('l')));
        }])->find($schoolId);

        if (!$school) {
            throw new \Exception('School not found');
        }

        $batches = SchoolDeliveryBatch::where('school_id', $schoolId)
            ->where('delivery_date', $date)
            ->with(['deliveryItems.childProfile', 'deliveryItems.mealPlan'])
            ->get();

        $breakTimes = $school->break_times ?? [];
        $schedule = [];

        foreach ($breakTimes as $breakType => $breakTime) {
            $batch = $batches->where('break_time_slot', $breakType)->first();
            
            $schedule[$breakType] = [
                'break_time' => $breakTime,
                'delivery_window' => [
                    'start' => $school->getDeliveryWindowStart($breakType),
                    'end' => $school->getDeliveryWindowEnd($breakType),
                ],
                'batch' => $batch,
                'total_meals' => $batch ? $batch->total_meals : 0,
                'total_children' => $batch ? $batch->total_children : 0,
                'status' => $batch ? $batch->status : 'not_scheduled',
            ];
        }

        return [
            'school' => $school,
            'date' => $date,
            'schedule' => $schedule,
            'total_batches' => $batches->count(),
            'total_meals' => $batches->sum('total_meals'),
            'total_children' => $batches->sum('total_children'),
        ];
    }

    /**
     * Generate delivery batches for a date range.
     *
     * @param string $startDate
     * @param string $endDate
     * @param array|null $schoolIds
     * @param array|null $breakTimeSlots
     * @return array
     */
    public function generateDeliveryBatches(string $startDate, string $endDate, ?array $schoolIds = null, ?array $breakTimeSlots = null): array
    {
        $results = [
            'total_batches_created' => 0,
            'schools_processed' => 0,
            'dates_processed' => [],
            'errors' => [],
        ];

        $schools = School::where('is_active', true)
            ->where('partnership_status', 'active')
            ->when($schoolIds, fn($q) => $q->whereIn('id', $schoolIds))
            ->get();

        $dateRange = Carbon::parse($startDate)->daysUntil(Carbon::parse($endDate)->addDay());

        foreach ($schools as $school) {
            $results['schools_processed']++;
            
            foreach ($dateRange as $date) {
                $dateString = $date->toDateString();
                $dayOfWeek = strtolower($date->format('l'));
                
                if (!in_array($dateString, $results['dates_processed'])) {
                    $results['dates_processed'][] = $dateString;
                }

                // Check if there are active subscriptions for this date
                $activeSubscriptions = $this->getActiveSubscriptionsForDate($school->id, $dateString, $dayOfWeek);
                
                if ($activeSubscriptions->isEmpty()) {
                    continue;
                }

                $breakTimes = $school->break_times ?? [];
                $slotsToProcess = $breakTimeSlots ?? array_keys($breakTimes);

                foreach ($slotsToProcess as $breakTimeSlot) {
                    if (!isset($breakTimes[$breakTimeSlot])) {
                        continue;
                    }

                    try {
                        // Check if batch already exists
                        $existingBatch = SchoolDeliveryBatch::where('school_id', $school->id)
                            ->where('delivery_date', $dateString)
                            ->where('break_time_slot', $breakTimeSlot)
                            ->first();

                        if ($existingBatch) {
                            continue;
                        }

                        $this->createDeliveryBatch([
                            'school_id' => $school->id,
                            'delivery_date' => $dateString,
                            'break_time_slot' => $breakTimeSlot,
                            'tenant_id' => $school->tenant_id,
                            'company_id' => $school->company_id,
                            'unit_id' => $school->unit_id,
                        ]);

                        $results['total_batches_created']++;
                    } catch (\Exception $e) {
                        $results['errors'][] = [
                            'school_id' => $school->id,
                            'date' => $dateString,
                            'break_time_slot' => $breakTimeSlot,
                            'error' => $e->getMessage(),
                        ];
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Get delivery performance metrics.
     *
     * @param array $filters
     * @return array
     */
    public function getDeliveryPerformanceMetrics(array $filters = []): array
    {
        $query = SchoolDeliveryBatch::query();

        // Apply filters
        foreach ($filters as $key => $value) {
            if ($value !== null) {
                switch ($key) {
                    case 'start_date':
                        $query->where('delivery_date', '>=', $value);
                        break;
                    case 'end_date':
                        $query->where('delivery_date', '<=', $value);
                        break;
                    default:
                        $query->where($key, $value);
                        break;
                }
            }
        }

        $batches = $query->get();

        return [
            'total_batches' => $batches->count(),
            'completed_batches' => $batches->where('status', 'delivered')->count(),
            'on_time_deliveries' => $batches->where('on_time_delivery', true)->count(),
            'average_delivery_time' => $batches->where('delivery_time_minutes', '>', 0)->avg('delivery_time_minutes'),
            'total_meals_delivered' => $batches->sum('total_meals'),
            'total_children_served' => $batches->sum('total_children'),
            'average_school_rating' => $batches->where('school_rating', '>', 0)->avg('school_rating'),
            'status_breakdown' => $batches->groupBy('status')->map->count(),
            'break_time_breakdown' => $batches->groupBy('break_time_slot')->map->count(),
            'performance_by_date' => $batches->groupBy('delivery_date')->map(function ($dayBatches) {
                return [
                    'total_batches' => $dayBatches->count(),
                    'on_time_rate' => $dayBatches->where('on_time_delivery', true)->count() / max($dayBatches->count(), 1) * 100,
                    'average_rating' => $dayBatches->where('school_rating', '>', 0)->avg('school_rating'),
                ];
            }),
        ];
    }

    /**
     * Optimize delivery route for a school.
     *
     * @param int $schoolId
     * @param string $date
     * @param string $breakTimeSlot
     * @return array
     */
    public function optimizeDeliveryRoute(int $schoolId, string $date, string $breakTimeSlot): array
    {
        $batch = SchoolDeliveryBatch::where('school_id', $schoolId)
            ->where('delivery_date', $date)
            ->where('break_time_slot', $breakTimeSlot)
            ->with(['school', 'deliveryItems'])
            ->first();

        if (!$batch) {
            throw new \Exception('Delivery batch not found');
        }

        $school = $batch->school;
        $kitchenLocation = [
            'latitude' => config('delivery.kitchen_latitude', 19.0760), // Default Mumbai
            'longitude' => config('delivery.kitchen_longitude', 72.8777),
        ];

        $schoolLocation = [
            'latitude' => $school->latitude,
            'longitude' => $school->longitude,
        ];

        // Calculate distance and estimated time
        $distance = $this->calculateDistance(
            $kitchenLocation['latitude'],
            $kitchenLocation['longitude'],
            $schoolLocation['latitude'],
            $schoolLocation['longitude']
        );

        // Estimate delivery time based on distance and traffic
        $estimatedDuration = $this->estimateDeliveryDuration($distance, $date, $breakTimeSlot);

        // Calculate optimal departure time
        $breakTime = $school->getBreakTime($breakTimeSlot);
        $deliveryWindowStart = $school->getDeliveryWindowStart($breakTimeSlot);
        $optimalDepartureTime = Carbon::createFromFormat('H:i', $deliveryWindowStart)
            ->subMinutes($estimatedDuration)
            ->format('H:i');

        return [
            'batch_id' => $batch->id,
            'route' => [
                'origin' => $kitchenLocation,
                'destination' => $schoolLocation,
                'distance_km' => round($distance, 2),
                'estimated_duration_minutes' => $estimatedDuration,
            ],
            'timing' => [
                'break_time' => $breakTime,
                'delivery_window_start' => $deliveryWindowStart,
                'delivery_window_end' => $school->getDeliveryWindowEnd($breakTimeSlot),
                'optimal_departure_time' => $optimalDepartureTime,
                'estimated_arrival_time' => $deliveryWindowStart,
            ],
            'batch_details' => [
                'total_meals' => $batch->total_meals,
                'total_children' => $batch->total_children,
                'meal_breakdown' => $batch->meal_breakdown,
                'grade_breakdown' => $batch->grade_breakdown,
            ],
        ];
    }

    /**
     * Generate a unique batch number.
     *
     * @param int $schoolId
     * @param string $date
     * @return string
     */
    private function generateBatchNumber(int $schoolId, string $date): string
    {
        $dateFormatted = Carbon::parse($date)->format('Ymd');
        $prefix = "SDB-{$schoolId}-{$dateFormatted}-";
        
        $counter = 1;
        do {
            $batchNumber = $prefix . str_pad($counter, 3, '0', STR_PAD_LEFT);
            $counter++;
        } while (SchoolDeliveryBatch::where('batch_number', $batchNumber)->exists());
        
        return $batchNumber;
    }

    /**
     * Calculate delivery time based on break time slot.
     *
     * @param School $school
     * @param string $breakTimeSlot
     * @return string
     */
    private function calculateDeliveryTime(School $school, string $breakTimeSlot): string
    {
        return $school->getDeliveryWindowStart($breakTimeSlot) ?? '12:00:00';
    }

    /**
     * Generate delivery items for a batch.
     *
     * @param SchoolDeliveryBatch $batch
     * @return void
     */
    private function generateDeliveryItems(SchoolDeliveryBatch $batch): void
    {
        $dayOfWeek = strtolower(Carbon::parse($batch->delivery_date)->format('l'));
        $subscriptions = $this->getActiveSubscriptionsForDate($batch->school_id, $batch->delivery_date, $dayOfWeek);

        $totalMeals = 0;
        $totalChildren = 0;
        $mealBreakdown = [];
        $gradeBreakdown = [];

        foreach ($subscriptions as $subscription) {
            if ($subscription->preferred_break_time === $batch->break_time_slot || 
                $subscription->preferred_break_time === 'both') {
                
                $itemCode = $this->generateItemCode($batch->id, $subscription->child_profile_id);
                
                SchoolDeliveryItem::create([
                    'tenant_id' => $batch->tenant_id,
                    'company_id' => $batch->company_id,
                    'unit_id' => $batch->unit_id,
                    'delivery_batch_id' => $batch->id,
                    'child_profile_id' => $subscription->child_profile_id,
                    'subscription_id' => $subscription->id,
                    'meal_plan_id' => $subscription->meal_plan_id,
                    'item_code' => $itemCode,
                    'child_name' => $subscription->childProfile->full_name,
                    'grade_section' => $subscription->childProfile->grade_section,
                    'roll_number' => $subscription->childProfile->roll_number,
                    'meal_plan_name' => $subscription->mealPlan->plan_name,
                    'meal_components' => $subscription->mealPlan->meal_components,
                    'quantity' => 1,
                    'meal_type' => $subscription->mealPlan->meal_type,
                    'customizations' => $subscription->meal_customizations,
                    'dietary_restrictions' => $subscription->dietary_accommodations,
                    'spice_level' => $subscription->spice_level,
                    'special_instructions' => $subscription->special_notes,
                    'item_cost' => $subscription->daily_rate,
                    'total_cost' => $subscription->daily_rate,
                    'status' => 'pending',
                ]);

                $totalMeals++;
                $totalChildren++;
                
                // Update breakdowns
                $planName = $subscription->mealPlan->plan_name;
                $mealBreakdown[$planName] = ($mealBreakdown[$planName] ?? 0) + 1;
                
                $grade = $subscription->childProfile->grade_level;
                $gradeBreakdown[$grade] = ($gradeBreakdown[$grade] ?? 0) + 1;
            }
        }

        $batch->update([
            'total_meals' => $totalMeals,
            'total_children' => $totalChildren,
            'meal_breakdown' => $mealBreakdown,
            'grade_breakdown' => $gradeBreakdown,
        ]);
    }

    /**
     * Get active subscriptions for a specific date.
     *
     * @param int $schoolId
     * @param string $date
     * @param string $dayOfWeek
     * @return Collection
     */
    private function getActiveSubscriptionsForDate(int $schoolId, string $date, string $dayOfWeek): Collection
    {
        return \App\Models\SchoolMealSubscription::where('school_id', $schoolId)
            ->where('status', 'active')
            ->where('start_date', '<=', $date)
            ->where('end_date', '>=', $date)
            ->whereJsonContains('delivery_days', $dayOfWeek)
            ->with(['childProfile', 'mealPlan'])
            ->get();
    }

    /**
     * Generate a unique item code.
     *
     * @param int $batchId
     * @param int $childId
     * @return string
     */
    private function generateItemCode(int $batchId, int $childId): string
    {
        return "SDI-{$batchId}-{$childId}-" . time();
    }

    /**
     * Calculate delivery performance metrics.
     *
     * @param SchoolDeliveryBatch $batch
     * @return void
     */
    private function calculateDeliveryPerformance(SchoolDeliveryBatch $batch): void
    {
        $scheduledTime = Carbon::createFromFormat('H:i:s', $batch->scheduled_delivery_time);
        $actualTime = Carbon::createFromFormat('H:i:s', $batch->actual_delivery_time);
        
        $deliveryTimeMinutes = $batch->dispatch_time ? 
            Carbon::createFromFormat('H:i:s', $batch->dispatch_time)->diffInMinutes($actualTime) : 0;
        
        $delayMinutes = max(0, $actualTime->diffInMinutes($scheduledTime, false));
        $onTimeDelivery = $delayMinutes <= 15; // 15-minute tolerance

        $batch->update([
            'delivery_time_minutes' => $deliveryTimeMinutes,
            'delay_minutes' => $delayMinutes,
            'on_time_delivery' => $onTimeDelivery,
        ]);
    }

    /**
     * Calculate distance between two coordinates.
     *
     * @param float $lat1
     * @param float $lng1
     * @param float $lat2
     * @param float $lng2
     * @return float Distance in kilometers
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Estimate delivery duration based on distance and time.
     *
     * @param float $distance
     * @param string $date
     * @param string $breakTimeSlot
     * @return int Duration in minutes
     */
    private function estimateDeliveryDuration(float $distance, string $date, string $breakTimeSlot): int
    {
        // Base speed: 25 km/h in city traffic
        $baseSpeed = 25;
        
        // Traffic multipliers based on time
        $trafficMultiplier = match($breakTimeSlot) {
            'morning_break' => 1.3, // Morning rush hour
            'lunch_break' => 1.1,   // Moderate traffic
            'afternoon_break' => 1.2, // Afternoon traffic
            default => 1.0,
        };
        
        $adjustedSpeed = $baseSpeed / $trafficMultiplier;
        $travelTime = ($distance / $adjustedSpeed) * 60; // Convert to minutes
        
        // Add buffer time for loading/unloading
        $bufferTime = 10;
        
        return (int) ceil($travelTime + $bufferTime);
    }
}

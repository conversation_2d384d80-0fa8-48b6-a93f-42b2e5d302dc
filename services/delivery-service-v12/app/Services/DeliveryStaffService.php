<?php

namespace App\Services;

use App\Models\DeliveryPerson;
use App\Models\DeliveryStaffRating;
use App\Models\DeliveryStaffSchedule;
use App\Models\DeliveryStaffServiceArea;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DeliveryStaffService
{
    /**
     * Create a new delivery staff member.
     *
     * @param array $data
     * @return \App\Models\DeliveryPerson
     */
    public function createDeliveryStaff(array $data): DeliveryPerson
    {
        DB::beginTransaction();
        
        try {
            // Create user account
            $user = User::create([
                'email' => $data['email'],
                'username' => $data['username'] ?? $data['email'],
                'password' => Hash::make($data['password']),
                'first_name' => $data['first_name'] ?? explode(' ', $data['name'])[0],
                'last_name' => $data['last_name'] ?? (count(explode(' ', $data['name'])) > 1 ? explode(' ', $data['name'], 2)[1] : ''),
                'role_id' => 3, // Delivery role
                'status' => true,
                'company_id' => $data['company_id'] ?? 1,
                'unit_id' => $data['unit_id'] ?? 1,
            ]);
            
            // Handle profile photo upload
            $profilePhoto = null;
            if (isset($data['profile_photo']) && $data['profile_photo']) {
                $profilePhoto = $this->uploadFile($data['profile_photo'], 'delivery_staff/profile');
            }
            
            // Handle ID proof image upload
            $idProofImage = null;
            if (isset($data['id_proof_image']) && $data['id_proof_image']) {
                $idProofImage = $this->uploadFile($data['id_proof_image'], 'delivery_staff/id_proof');
            }
            
            // Create delivery person
            $staff = DeliveryPerson::create([
                'user_id' => $user->id,
                'name' => $data['name'],
                'phone' => $data['phone'],
                'email' => $data['email'],
                'profile_photo' => $profilePhoto,
                'id_proof_type' => $data['id_proof_type'] ?? null,
                'id_proof_number' => $data['id_proof_number'] ?? null,
                'id_proof_image' => $idProofImage,
                'emergency_contact_name' => $data['emergency_contact_name'] ?? null,
                'emergency_contact_phone' => $data['emergency_contact_phone'] ?? null,
                'address' => $data['address'] ?? null,
                'vehicle_type' => $data['vehicle_type'] ?? null,
                'vehicle_number' => $data['vehicle_number'] ?? null,
                'vehicle_details' => $data['vehicle_details'] ?? null,
                'status' => $data['status'] ?? true,
                'is_active' => $data['is_active'] ?? true,
                'on_duty' => $data['on_duty'] ?? false,
                'joining_date' => $data['joining_date'] ?? now(),
                'company_id' => $data['company_id'] ?? 1,
                'unit_id' => $data['unit_id'] ?? 1,
            ]);
            
            // Create service areas
            if (isset($data['service_areas']) && is_array($data['service_areas'])) {
                foreach ($data['service_areas'] as $area) {
                    DeliveryStaffServiceArea::create([
                        'delivery_person_id' => $staff->id,
                        'location_id' => $area['location_id'],
                        'is_primary' => $area['is_primary'] ?? false,
                        'is_active' => $area['is_active'] ?? true,
                    ]);
                }
            }
            
            // Create schedules
            if (isset($data['schedules']) && is_array($data['schedules'])) {
                foreach ($data['schedules'] as $schedule) {
                    DeliveryStaffSchedule::create([
                        'delivery_person_id' => $staff->id,
                        'day_of_week' => $schedule['day_of_week'],
                        'start_time' => $schedule['start_time'],
                        'end_time' => $schedule['end_time'],
                        'is_active' => $schedule['is_active'] ?? true,
                    ]);
                }
            }
            
            DB::commit();
            
            return $staff->fresh(['user', 'serviceAreas.location', 'schedules']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to create delivery staff: ' . $e->getMessage(), [
                'data' => $data,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Update a delivery staff member.
     *
     * @param int $id
     * @param array $data
     * @return \App\Models\DeliveryPerson
     */
    public function updateDeliveryStaff(int $id, array $data): DeliveryPerson
    {
        DB::beginTransaction();
        
        try {
            $staff = DeliveryPerson::findOrFail($id);
            
            // Update user account if needed
            if (isset($data['email']) || isset($data['password']) || isset($data['first_name']) || isset($data['last_name'])) {
                $userData = [];
                
                if (isset($data['email'])) {
                    $userData['email'] = $data['email'];
                }
                
                if (isset($data['password'])) {
                    $userData['password'] = Hash::make($data['password']);
                }
                
                if (isset($data['first_name'])) {
                    $userData['first_name'] = $data['first_name'];
                }
                
                if (isset($data['last_name'])) {
                    $userData['last_name'] = $data['last_name'];
                }
                
                if (isset($data['username'])) {
                    $userData['username'] = $data['username'];
                }
                
                if (!empty($userData)) {
                    $staff->user->update($userData);
                }
            }
            
            // Handle profile photo upload
            if (isset($data['profile_photo']) && $data['profile_photo']) {
                // Delete old photo if exists
                if ($staff->profile_photo) {
                    Storage::delete($staff->profile_photo);
                }
                
                $data['profile_photo'] = $this->uploadFile($data['profile_photo'], 'delivery_staff/profile');
            }
            
            // Handle ID proof image upload
            if (isset($data['id_proof_image']) && $data['id_proof_image']) {
                // Delete old image if exists
                if ($staff->id_proof_image) {
                    Storage::delete($staff->id_proof_image);
                }
                
                $data['id_proof_image'] = $this->uploadFile($data['id_proof_image'], 'delivery_staff/id_proof');
            }
            
            // Update delivery person
            $staff->update($data);
            
            // Update service areas if provided
            if (isset($data['service_areas']) && is_array($data['service_areas'])) {
                // Delete existing service areas
                $staff->serviceAreas()->delete();
                
                // Create new service areas
                foreach ($data['service_areas'] as $area) {
                    DeliveryStaffServiceArea::create([
                        'delivery_person_id' => $staff->id,
                        'location_id' => $area['location_id'],
                        'is_primary' => $area['is_primary'] ?? false,
                        'is_active' => $area['is_active'] ?? true,
                    ]);
                }
            }
            
            // Update schedules if provided
            if (isset($data['schedules']) && is_array($data['schedules'])) {
                // Delete existing schedules
                $staff->schedules()->delete();
                
                // Create new schedules
                foreach ($data['schedules'] as $schedule) {
                    DeliveryStaffSchedule::create([
                        'delivery_person_id' => $staff->id,
                        'day_of_week' => $schedule['day_of_week'],
                        'start_time' => $schedule['start_time'],
                        'end_time' => $schedule['end_time'],
                        'is_active' => $schedule['is_active'] ?? true,
                    ]);
                }
            }
            
            DB::commit();
            
            return $staff->fresh(['user', 'serviceAreas.location', 'schedules']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update delivery staff: ' . $e->getMessage(), [
                'id' => $id,
                'data' => $data,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Delete a delivery staff member.
     *
     * @param int $id
     * @return bool
     */
    public function deleteDeliveryStaff(int $id): bool
    {
        DB::beginTransaction();
        
        try {
            $staff = DeliveryPerson::findOrFail($id);
            
            // Delete service areas
            $staff->serviceAreas()->delete();
            
            // Delete schedules
            $staff->schedules()->delete();
            
            // Delete ratings
            $staff->ratings()->delete();
            
            // Delete profile photo if exists
            if ($staff->profile_photo) {
                Storage::delete($staff->profile_photo);
            }
            
            // Delete ID proof image if exists
            if ($staff->id_proof_image) {
                Storage::delete($staff->id_proof_image);
            }
            
            // Delete delivery person
            $staff->delete();
            
            // Delete user account
            $staff->user->delete();
            
            DB::commit();
            
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to delete delivery staff: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Update delivery staff location.
     *
     * @param int $id
     * @param float $latitude
     * @param float $longitude
     * @return \App\Models\DeliveryPerson
     */
    public function updateLocation(int $id, float $latitude, float $longitude): DeliveryPerson
    {
        $staff = DeliveryPerson::findOrFail($id);
        
        $staff->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
        ]);
        
        return $staff;
    }
    
    /**
     * Update delivery staff duty status.
     *
     * @param int $id
     * @param bool $onDuty
     * @return \App\Models\DeliveryPerson
     */
    public function updateDutyStatus(int $id, bool $onDuty): DeliveryPerson
    {
        $staff = DeliveryPerson::findOrFail($id);
        
        $staff->update([
            'on_duty' => $onDuty,
        ]);
        
        return $staff;
    }
    
    /**
     * Get delivery staff performance metrics.
     *
     * @param int $id
     * @return array
     */
    public function getPerformanceMetrics(int $id): array
    {
        $staff = DeliveryPerson::findOrFail($id);
        
        // Get recent ratings
        $recentRatings = DeliveryStaffRating::where('delivery_person_id', $id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        
        // Calculate average rating by month for the last 6 months
        $ratingsByMonth = DeliveryStaffRating::where('delivery_person_id', $id)
            ->where('created_at', '>=', now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, AVG(rating) as average_rating, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
        
        // Format ratings by month
        $formattedRatingsByMonth = [];
        foreach ($ratingsByMonth as $rating) {
            $date = \Carbon\Carbon::createFromDate($rating->year, $rating->month, 1);
            $formattedRatingsByMonth[] = [
                'month' => $date->format('M Y'),
                'average_rating' => round($rating->average_rating, 2),
                'count' => $rating->count,
            ];
        }
        
        // Calculate deliveries by month for the last 6 months
        $deliveriesByMonth = $staff->orders()
            ->where('delivery_status', 'Delivered')
            ->where('created_at', '>=', now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
        
        // Format deliveries by month
        $formattedDeliveriesByMonth = [];
        foreach ($deliveriesByMonth as $delivery) {
            $date = \Carbon\Carbon::createFromDate($delivery->year, $delivery->month, 1);
            $formattedDeliveriesByMonth[] = [
                'month' => $date->format('M Y'),
                'count' => $delivery->count,
            ];
        }
        
        return [
            'total_deliveries' => $staff->total_deliveries,
            'on_time_deliveries' => $staff->on_time_deliveries,
            'late_deliveries' => $staff->late_deliveries,
            'failed_deliveries' => $staff->failed_deliveries,
            'on_time_percentage' => $staff->on_time_percentage,
            'completion_rate' => $staff->completion_rate,
            'average_rating' => $staff->rating,
            'total_ratings' => $staff->total_ratings,
            'recent_ratings' => $recentRatings,
            'ratings_by_month' => $formattedRatingsByMonth,
            'deliveries_by_month' => $formattedDeliveriesByMonth,
        ];
    }
    
    /**
     * Upload a file.
     *
     * @param mixed $file
     * @param string $path
     * @return string
     */
    private function uploadFile($file, string $path): string
    {
        if (is_string($file) && Str::startsWith($file, 'data:image')) {
            // Base64 encoded image
            $extension = explode('/', mime_content_type($file))[1];
            $fileName = Str::random(40) . '.' . $extension;
            $filePath = $path . '/' . $fileName;
            
            // Decode and save the file
            $fileData = base64_decode(explode(',', $file)[1]);
            Storage::put($filePath, $fileData);
            
            return $filePath;
        } else {
            // Regular file upload
            return $file->store($path);
        }
    }
}

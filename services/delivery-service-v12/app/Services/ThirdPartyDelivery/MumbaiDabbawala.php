<?php

namespace App\Services\ThirdPartyDelivery;

use App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Customer;
use App\Models\Order;

class MumbaiDabbawala implements TPDeliveryInterface
{
    /**
     * @var string
     */
    private string $baseUrl;

    /**
     * @var string
     */
    private string $version;

    /**
     * @var string
     */
    private string $authToken;

    /**
     * Create a new MumbaiDabbawala instance.
     */
    public function __construct()
    {
        $this->baseUrl = config('delivery.mumbaidabbawala.base_url', 'https://api.mumbaidabbawala.in/api/');
        $this->version = config('delivery.mumbaidabbawala.version', 'v1');
        $this->authToken = config('delivery.mumbaidabbawala.auth_token', '');
    }

    /**
     * Book a delivery.
     *
     * @param array $data
     * @return array
     */
    public function book(array $data): array
    {
        $url = $this->baseUrl . $this->version . '/orders/create';

        $input = $this->exchangeArray($data);

        // Generate dabbawala code if not exists
        $dabbaCode = $this->generateDabbaCode($data['pk_order_no'], $data['customer_code'] ?? null);

        // Add dabbawala code to input
        $input['dabba_code'] = $dabbaCode;

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->authToken,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest'
            ])->post($url, $input);

            $result = $response->json();
            $code = $response->status();

            if ($code == 201) {
                // Update customer with dabbawala code if needed
                $this->updateCustomerWithDabbaCode($data['customer_code'] ?? null, $dabbaCode);

                // Update order with third-party delivery ID
                $this->updateOrderWithThirdPartyId($data['pk_order_no'], $result['data']['tracking_id'] ?? '');

                return [
                    'code' => $code,
                    'status' => $result['message'] ?? 'Order placed successfully',
                    'dabba_code' => $dabbaCode,
                    'tracking_id' => $result['data']['tracking_id'] ?? '',
                    'time' => date("h:i A", strtotime($input['pickup_time'])),
                    'date' => date(config('delivery.date_format', 'Y-m-d'), strtotime($input['pickup_date']))
                ];
            } else {
                return ['code' => $code, 'status' => $result['error'] ?? 'Unknown error'];
            }
        } catch (\Exception $e) {
            Log::error('Error booking Mumbai Dabbawala delivery: ' . $e->getMessage(), [
                'order_id' => $data['pk_order_no']
            ]);

            return ['code' => 500, 'status' => 'Error: ' . $e->getMessage()];
        }
    }

    /**
     * Cancel a delivery.
     *
     * @param int $orderId
     * @return array
     */
    public function cancel(int $orderId): array
    {
        $url = $this->baseUrl . $this->version . '/orders/' . $orderId . '/cancel';

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->authToken,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest'
            ])->post($url);

            $result = $response->json();
            $code = $response->status();

            return ['code' => $code, 'status' => $result['message'] ?? 'Unknown response'];
        } catch (\Exception $e) {
            Log::error('Error cancelling Mumbai Dabbawala delivery: ' . $e->getMessage(), [
                'order_id' => $orderId
            ]);

            return ['code' => 500, 'status' => 'Error: ' . $e->getMessage()];
        }
    }

    /**
     * Get delivery status.
     *
     * @param int $orderId
     * @param array $data
     * @return array
     */
    public function getStatus(int $orderId, array $data = []): array
    {
        $url = $this->baseUrl . $this->version . '/orders/' . $orderId . '/status';

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->authToken,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest'
            ])->get($url);

            $result = $response->json();
            $code = $response->status();

            if ($code == 200) {
                $output = [
                    'status' => $result['status'] ?? 'Unknown',
                    'dabbawala_name' => $result['dabbawala_name'] ?? null,
                    'dabbawala_phone' => $result['dabbawala_phone'] ?? null,
                    'dabba_code' => $result['dabba_code'] ?? null,
                    'estimated_delivery_time' => $result['estimated_delivery_time'] ?? null,
                    'actual_delivery_time' => $result['actual_delivery_time'] ?? null,
                ];

                return ['code' => $code, 'status' => $output];
            } else {
                return ['code' => $code, 'status' => $result['detail'] ?? 'Unknown error'];
            }
        } catch (\Exception $e) {
            Log::error('Error getting Mumbai Dabbawala delivery status: ' . $e->getMessage(), [
                'order_id' => $orderId
            ]);

            return ['code' => 500, 'status' => 'Error: ' . $e->getMessage()];
        }
    }

    /**
     * Exchange array for Mumbai Dabbawala API.
     *
     * @param array $data
     * @return array
     */
    private function exchangeArray(array $data): array
    {
        // Format data for Mumbai Dabbawala API
        $pickupTime = $this->formatPickupTime($data['order_date'], $data['fk_kitchen_code'], $data['order_menu']);

        return [
            'pickup_date' => date('Y-m-d', strtotime($data['order_date'])),
            'pickup_time' => $pickupTime,
            'pickup_address' => config('delivery.merchant_address', 'Default Address'),
            'customer_name' => $data['customer_name'],
            'customer_phone' => $data['customer_phone'],
            'delivery_address' => $data['ship_address'],
            'amount' => $data['amount'],
            'order_id' => $data['pk_order_no'],
            'meal_type' => $data['order_menu'],
            'food_preference' => $data['food_preference'] ?? 'veg'
        ];
    }

    /**
     * Format pickup time.
     *
     * @param string $orderDate
     * @param string $kitchenCode
     * @param string $orderMenu
     * @return string
     */
    private function formatPickupTime(string $orderDate, string $kitchenCode, string $orderMenu): string
    {
        // Logic to determine pickup time based on kitchen and menu type
        $pickupTime = '';

        if (strtolower($orderMenu) == 'lunch') {
            $pickupTime = config('delivery.lunch_pickup_time', '12:30:00');
        } else {
            $pickupTime = config('delivery.dinner_pickup_time', '19:30:00');
        }

        return $pickupTime;
    }

    /**
     * Generate a unique dabbawala code.
     *
     * @param int $orderId
     * @param int|null $customerId
     * @return string
     */
    public function generateDabbaCode(int $orderId, ?int $customerId = null): string
    {
        // Check if customer already has a dabbawala code
        if ($customerId) {
            try {
                $customer = Customer::find($customerId);
                if ($customer && !empty($customer->dabbawala_code)) {
                    return $customer->dabbawala_code;
                }
            } catch (\Exception $e) {
                Log::error('Error retrieving customer for dabbawala code: ' . $e->getMessage());
            }
        }

        // Generate a new dabbawala code
        // Format: MD-[Area Code]-[Customer ID/Order ID]-[Random 3 digits]
        $areaCode = $this->getAreaCode($orderId);
        $identifier = $customerId ?? $orderId;
        $random = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT);

        return 'MD-' . $areaCode . '-' . $identifier . '-' . $random;
    }

    /**
     * Get area code based on order location.
     *
     * @param int $orderId
     * @return string
     */
    private function getAreaCode(int $orderId): string
    {
        try {
            $order = Order::find($orderId);
            if ($order && $order->location_code) {
                // Get first 2 characters of location name or pin code
                $location = $order->location;
                if ($location && isset($location->pin)) {
                    return substr($location->pin, 0, 2);
                } elseif ($location && isset($location->location)) {
                    return strtoupper(substr($location->location, 0, 2));
                }
            }
        } catch (\Exception $e) {
            Log::error('Error retrieving location for dabbawala code: ' . $e->getMessage());
        }

        // Default area code
        return 'MU';
    }

    /**
     * Update customer with dabbawala code.
     *
     * @param int|null $customerId
     * @param string $dabbaCode
     * @return void
     */
    private function updateCustomerWithDabbaCode(?int $customerId, string $dabbaCode): void
    {
        if (!$customerId) {
            return;
        }

        try {
            $customer = Customer::find($customerId);
            if ($customer && empty($customer->dabbawala_code)) {
                $customer->update([
                    'dabbawala_code' => $dabbaCode,
                    'dabbawala_code_type' => 'mumbai'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error updating customer with dabbawala code: ' . $e->getMessage(), [
                'customer_id' => $customerId,
                'dabba_code' => $dabbaCode
            ]);
        }
    }

    /**
     * Update order with third-party ID.
     *
     * @param int $orderId
     * @param string $thirdPartyOrderId
     * @return void
     */
    private function updateOrderWithThirdPartyId(int $orderId, string $thirdPartyOrderId): void
    {
        try {
            $order = Order::find($orderId);
            if ($order) {
                $order->update(['tp_delivery_order_id' => $thirdPartyOrderId]);
            }
        } catch (\Exception $e) {
            Log::error('Error updating order with third-party ID: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'third_party_order_id' => $thirdPartyOrderId
            ]);
        }
    }
}

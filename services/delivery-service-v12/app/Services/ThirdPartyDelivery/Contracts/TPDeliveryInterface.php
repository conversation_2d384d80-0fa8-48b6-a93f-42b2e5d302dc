<?php

namespace App\Services\ThirdPartyDelivery\Contracts;

interface TPDeliveryInterface
{
    /**
     * Book a delivery.
     *
     * @param array $data
     * @return array
     */
    public function book(array $data): array;
    
    /**
     * Cancel a delivery.
     *
     * @param int $orderId
     * @return array
     */
    public function cancel(int $orderId): array;
    
    /**
     * Get delivery status.
     *
     * @param int $orderId
     * @param array $data
     * @return array
     */
    public function getStatus(int $orderId, array $data = []): array;
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GeocodingService
{
    /**
     * The base URL for the Nominatim API.
     *
     * @var string
     */
    protected string $apiUrl;

    /**
     * The user agent for API requests.
     *
     * @var string
     */
    protected string $userAgent;

    /**
     * The cache TTL in seconds.
     *
     * @var int
     */
    protected int $cacheTtl;

    /**
     * Create a new GeocodingService instance.
     */
    public function __construct()
    {
        $this->apiUrl = config('maps.nominatim_url', 'https://nominatim.openstreetmap.org');
        $this->userAgent = config('maps.user_agent', 'FoodDialer Delivery Service');
        $this->cacheTtl = config('maps.cache_ttl', 86400); // 24 hours

        // Log the API URL for debugging
        \Illuminate\Support\Facades\Log::debug('GeocodingService initialized with API URL: ' . $this->apiUrl);
    }

    /**
     * Geocode an address to get latitude and longitude.
     *
     * @param string $address The address to geocode
     * @param string|null $city Optional city to improve results
     * @param string|null $country Optional country to improve results (default: India)
     * @return array|null Returns [lat, lon] array or null if geocoding failed
     */
    public function geocode(string $address, ?string $city = null, ?string $country = 'India'): ?array
    {
        // Create a cache key based on the address and optional parameters
        $cacheKey = 'geocode_' . md5($address . ($city ?? '') . ($country ?? ''));

        // Check if we have a cached result
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Build the full address string
            $fullAddress = $address;
            if ($city) {
                $fullAddress .= ", $city";
            }
            if ($country) {
                $fullAddress .= ", $country";
            }

            // Make the API request
            $response = Http::withHeaders([
                'User-Agent' => $this->userAgent,
            ])->get($this->apiUrl . '/search', [
                'q' => $fullAddress,
                'format' => 'json',
                'limit' => 1,
                'addressdetails' => 1,
            ]);

            if ($response->successful() && !empty($response->json())) {
                $result = $response->json()[0];
                $coordinates = [
                    'lat' => (float) $result['lat'],
                    'lon' => (float) $result['lon'],
                    'display_name' => $result['display_name'],
                    'address_components' => $result['address'] ?? [],
                ];

                // Cache the result
                Cache::put($cacheKey, $coordinates, $this->cacheTtl);

                return $coordinates;
            }

            // If we get here, geocoding failed
            Log::warning('Geocoding failed for address: ' . $fullAddress, [
                'response' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception during geocoding: ' . $e->getMessage(), [
                'address' => $fullAddress,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Reverse geocode coordinates to get an address.
     *
     * @param float $lat Latitude
     * @param float $lon Longitude
     * @return array|null Returns address details or null if reverse geocoding failed
     */
    public function reverseGeocode(float $lat, float $lon): ?array
    {
        // Create a cache key based on the coordinates
        $cacheKey = 'reverse_geocode_' . md5("$lat,$lon");

        // Check if we have a cached result
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Make the API request
            $response = Http::withHeaders([
                'User-Agent' => $this->userAgent,
            ])->get($this->apiUrl . '/reverse', [
                'lat' => $lat,
                'lon' => $lon,
                'format' => 'json',
                'addressdetails' => 1,
            ]);

            if ($response->successful() && !empty($response->json())) {
                $result = $response->json();
                $address = [
                    'display_name' => $result['display_name'],
                    'address_components' => $result['address'] ?? [],
                    'lat' => (float) $result['lat'],
                    'lon' => (float) $result['lon'],
                ];

                // Cache the result
                Cache::put($cacheKey, $address, $this->cacheTtl);

                return $address;
            }

            // If we get here, reverse geocoding failed
            Log::warning('Reverse geocoding failed for coordinates: ' . "$lat,$lon", [
                'response' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception during reverse geocoding: ' . $e->getMessage(), [
                'coordinates' => "$lat,$lon",
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Validate an address by attempting to geocode it.
     *
     * @param string $address The address to validate
     * @param string|null $city Optional city to improve results
     * @param string|null $country Optional country to improve results
     * @return bool Returns true if the address can be geocoded
     */
    public function validateAddress(string $address, ?string $city = null, ?string $country = 'India'): bool
    {
        $result = $this->geocode($address, $city, $country);
        return $result !== null;
    }

    /**
     * Get the distance between two points in kilometers.
     *
     * @param float $lat1 Latitude of first point
     * @param float $lon1 Longitude of first point
     * @param float $lat2 Latitude of second point
     * @param float $lon2 Longitude of second point
     * @return float Distance in kilometers
     */
    public function getHaversineDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        // Convert latitude and longitude from degrees to radians
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        // Haversine formula
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat / 2) ** 2 + cos($lat1) * cos($lat2) * sin($dlon / 2) ** 2;
        $c = 2 * asin(sqrt($a));

        // Radius of Earth in kilometers
        $r = 6371;

        // Calculate the distance
        return $c * $r;
    }
}

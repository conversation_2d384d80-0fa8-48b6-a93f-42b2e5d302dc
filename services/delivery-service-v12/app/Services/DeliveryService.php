<?php

namespace App\Services;

use App\DTOs\DeliveryStatusUpdateDTO;
use App\DTOs\OrderDeliveryDTO;
use App\Events\OrderDeliveredEvent;
use App\Repositories\Contracts\DeliveryRepositoryInterface;
use App\Repositories\Contracts\LocationRepositoryInterface;
use App\Services\Contracts\DeliveryServiceInterface;
use App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface;
use Illuminate\Support\Facades\Event;

class DeliveryService implements DeliveryServiceInterface
{
    private DeliveryRepositoryInterface $deliveryRepository;
    private LocationRepositoryInterface $locationRepository;
    private TPDeliveryInterface $thirdPartyDelivery;

    public function __construct(
        DeliveryRepositoryInterface $deliveryRepository,
        LocationRepositoryInterface $locationRepository,
        TPDeliveryInterface $thirdPartyDelivery
    ) {
        $this->deliveryRepository = $deliveryRepository;
        $this->locationRepository = $locationRepository;
        $this->thirdPartyDelivery = $thirdPartyDelivery;
    }

    public function getDeliveryOrders(int $userId, int $locationId, string $date): array
    {
        return $this->deliveryRepository->getOrdersForDelivery($userId, $locationId, $date);
    }

    public function searchOrders(int $userId, string $searchTerm, int $locationId): array
    {
        return $this->deliveryRepository->searchOrders($userId, $searchTerm, $locationId);
    }

    public function updateDeliveryStatus(DeliveryStatusUpdateDTO $dto): bool
    {
        $result = $this->deliveryRepository->updateDeliveryStatus(
            $dto->orderId,
            $dto->userId,
            $dto->orderCompleted
        );

        if ($result && $dto->orderCompleted) {
            Event::dispatch(new OrderDeliveredEvent($dto->orderId, $dto->userId));
        }

        return $result;
    }

    public function getDeliveryLocations(int $userId): array
    {
        return $this->locationRepository->getLocationsForUser($userId);
    }

    public function bookThirdPartyDelivery(OrderDeliveryDTO $dto): array
    {
        $order = $this->deliveryRepository->getOrderById($dto->orderId);
        
        return $this->thirdPartyDelivery->book($order);
    }

    public function cancelThirdPartyDelivery(int $orderId): bool
    {
        $result = $this->thirdPartyDelivery->cancel($orderId);
        
        return isset($result['code']) && $result['code'] === 200;
    }

    public function getThirdPartyDeliveryStatus(int $orderId): array
    {
        $order = $this->deliveryRepository->getOrderById($orderId);
        
        return $this->thirdPartyDelivery->getStatus($order);
    }
}

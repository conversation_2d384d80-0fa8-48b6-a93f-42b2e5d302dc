<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderDeliveredEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int $orderId;
    public int $deliveryPersonId;

    public function __construct(int $orderId, int $deliveryPersonId)
    {
        $this->orderId = $orderId;
        $this->deliveryPersonId = $deliveryPersonId;
    }
}

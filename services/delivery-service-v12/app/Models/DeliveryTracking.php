<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryTracking extends Model
{
    use HasFactory;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'delivery_tracking';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'delivery_person_id',
        'status',
        'pending_at',
        'dispatched_at',
        'in_transit_at',
        'delivered_at',
        'failed_at',
        'current_latitude',
        'current_longitude',
        'last_location_update',
        'distance_traveled',
        'travel_time',
        'route_history',
        'notes'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'pending_at' => 'datetime',
        'dispatched_at' => 'datetime',
        'in_transit_at' => 'datetime',
        'delivered_at' => 'datetime',
        'failed_at' => 'datetime',
        'last_location_update' => 'datetime',
        'current_latitude' => 'float',
        'current_longitude' => 'float',
        'distance_traveled' => 'float',
        'travel_time' => 'integer',
        'route_history' => 'array'
    ];
    
    /**
     * Get the order associated with this tracking.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'pk_order_no');
    }
    
    /**
     * Get the delivery person associated with this tracking.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(DeliveryPerson::class);
    }
    
    /**
     * Get the proofs associated with this tracking.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function proofs(): HasMany
    {
        return $this->hasMany(DeliveryProof::class, 'order_id', 'order_id');
    }
    
    /**
     * Get the estimated time of arrival.
     *
     * @return \Carbon\Carbon|null
     */
    public function getEstimatedArrivalAttribute()
    {
        if (!$this->in_transit_at || !$this->order || !$this->order->deliveryRoute) {
            return null;
        }
        
        $route = $this->order->deliveryRoute;
        
        if (!$route->duration_seconds) {
            return null;
        }
        
        return $this->in_transit_at->addSeconds($route->duration_seconds);
    }
    
    /**
     * Get the formatted estimated time of arrival.
     *
     * @return string|null
     */
    public function getFormattedEstimatedArrivalAttribute(): ?string
    {
        if (!$this->estimated_arrival) {
            return null;
        }
        
        return $this->estimated_arrival->format('h:i A');
    }
    
    /**
     * Get the remaining time in minutes.
     *
     * @return int|null
     */
    public function getRemainingTimeAttribute(): ?int
    {
        if (!$this->estimated_arrival) {
            return null;
        }
        
        $now = now();
        
        if ($now->gt($this->estimated_arrival)) {
            return 0;
        }
        
        return $now->diffInMinutes($this->estimated_arrival);
    }
    
    /**
     * Get the formatted remaining time.
     *
     * @return string|null
     */
    public function getFormattedRemainingTimeAttribute(): ?string
    {
        if ($this->remaining_time === null) {
            return null;
        }
        
        if ($this->remaining_time === 0) {
            return 'Arriving now';
        }
        
        $hours = floor($this->remaining_time / 60);
        $minutes = $this->remaining_time % 60;
        
        if ($hours > 0) {
            return $hours . ' hr ' . $minutes . ' min';
        }
        
        return $minutes . ' min';
    }
    
    /**
     * Get the completion percentage.
     *
     * @return int
     */
    public function getCompletionPercentageAttribute(): int
    {
        switch ($this->status) {
            case 'pending':
                return 0;
            case 'dispatched':
                return 25;
            case 'in_transit':
                if ($this->remaining_time === null || $this->order->deliveryRoute === null) {
                    return 50;
                }
                
                $totalTime = $this->order->deliveryRoute->duration_seconds / 60;
                
                if ($totalTime === 0) {
                    return 50;
                }
                
                $percentage = 50 + (($totalTime - $this->remaining_time) / $totalTime) * 50;
                
                return min(99, max(50, (int) $percentage));
            case 'delivered':
                return 100;
            case 'failed':
                return 0;
            default:
                return 0;
        }
    }
}

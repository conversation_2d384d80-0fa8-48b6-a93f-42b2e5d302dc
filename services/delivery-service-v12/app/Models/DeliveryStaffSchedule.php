<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryStaffSchedule extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'delivery_person_id',
        'day_of_week',
        'start_time',
        'end_time',
        'is_active'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get the delivery person associated with this schedule.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(DeliveryPerson::class);
    }
    
    /**
     * Check if the schedule is for today.
     *
     * @return bool
     */
    public function getIsTodayAttribute(): bool
    {
        return strtolower(date('l')) === $this->day_of_week;
    }
    
    /**
     * Check if the current time is within the schedule.
     *
     * @return bool
     */
    public function getIsCurrentAttribute(): bool
    {
        if (!$this->is_today) {
            return false;
        }
        
        $now = now();
        $startTime = now()->setTimeFromTimeString($this->start_time);
        $endTime = now()->setTimeFromTimeString($this->end_time);
        
        return $now->between($startTime, $endTime);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryRoute extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'kitchen_id',
        'delivery_person_id',
        'distance_km',
        'duration_seconds',
        'route_geometry',
        'route_steps',
        'start_lat',
        'start_lon',
        'end_lat',
        'end_lon',
        'estimated_delivery_time',
        'actual_delivery_time',
        'traffic_condition',
        'delivery_zone',
        'delivery_fee',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'distance_km' => 'float',
        'duration_seconds' => 'integer',
        'route_geometry' => 'array',
        'route_steps' => 'array',
        'start_lat' => 'float',
        'start_lon' => 'float',
        'end_lat' => 'float',
        'end_lon' => 'float',
        'estimated_delivery_time' => 'datetime',
        'actual_delivery_time' => 'datetime',
        'delivery_zone' => 'integer',
        'delivery_fee' => 'float',
    ];

    /**
     * Get the order that owns the delivery route.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'pk_order_no');
    }

    /**
     * Get the kitchen that owns the delivery route.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(DeliveryLocation::class, 'kitchen_id', 'pk_location_code');
    }

    /**
     * Get the delivery person that owns the delivery route.
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_person_id');
    }

    /**
     * Get the estimated delivery time in a human-readable format.
     *
     * @return string
     */
    public function getFormattedEstimatedDeliveryTimeAttribute(): string
    {
        return $this->estimated_delivery_time ? $this->estimated_delivery_time->format('h:i A') : 'N/A';
    }

    /**
     * Get the actual delivery time in a human-readable format.
     *
     * @return string
     */
    public function getFormattedActualDeliveryTimeAttribute(): string
    {
        return $this->actual_delivery_time ? $this->actual_delivery_time->format('h:i A') : 'N/A';
    }

    /**
     * Get the duration in minutes.
     *
     * @return int
     */
    public function getDurationMinutesAttribute(): int
    {
        return (int) ($this->duration_seconds / 60);
    }
}

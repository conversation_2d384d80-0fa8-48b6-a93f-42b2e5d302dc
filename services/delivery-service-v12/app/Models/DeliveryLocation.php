<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryLocation extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'delivery_locations';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'location',
        'city',
        'sub_city_area',
        'pin',
        'latitude',
        'longitude',
        'geocoded_address',
        'delivery_charges',
        'delivery_time',
        'is_default',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'delivery_charges' => 'float',
        'delivery_time' => 'integer',
        'is_default' => 'boolean',
        'status' => 'boolean',
    ];

    /**
     * Get the orders for the location.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'location_code', 'id');
    }

    /**
     * Get the customers for the location.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'location_code', 'id');
    }

    /**
     * Get the delivery zones for the location.
     */
    public function deliveryZones(): HasMany
    {
        return $this->hasMany(DeliveryZone::class, 'kitchen_id', 'id');
    }

    /**
     * Get the delivery routes for the location.
     */
    public function deliveryRoutes(): HasMany
    {
        return $this->hasMany(DeliveryRoute::class, 'kitchen_id', 'id');
    }

    /**
     * Get the full address.
     *
     * @return string
     */
    public function getFullAddressAttribute(): string
    {
        $parts = [];
        
        if ($this->location) {
            $parts[] = $this->location;
        }
        
        if ($this->sub_city_area) {
            $parts[] = $this->sub_city_area;
        }
        
        if ($this->city) {
            $parts[] = $this->city;
        }
        
        if ($this->pin) {
            $parts[] = $this->pin;
        }
        
        return implode(', ', $parts);
    }

    /**
     * Check if the location has valid coordinates.
     *
     * @return bool
     */
    public function hasCoordinates(): bool
    {
        return $this->latitude !== null && $this->longitude !== null;
    }
}

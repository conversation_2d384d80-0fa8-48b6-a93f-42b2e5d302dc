<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryPerson extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'phone',
        'email',
        'profile_photo',
        'id_proof_type',
        'id_proof_number',
        'id_proof_image',
        'emergency_contact_name',
        'emergency_contact_phone',
        'address',
        'vehicle_type',
        'vehicle_number',
        'vehicle_details',
        'status',
        'is_active',
        'on_duty',
        'current_latitude',
        'current_longitude',
        'last_location_update',
        'rating',
        'total_ratings',
        'total_deliveries',
        'on_time_deliveries',
        'late_deliveries',
        'failed_deliveries',
        'joining_date',
        'company_id',
        'unit_id'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
        'is_active' => 'boolean',
        'on_duty' => 'boolean',
        'current_latitude' => 'float',
        'current_longitude' => 'float',
        'last_location_update' => 'datetime',
        'rating' => 'float',
        'joining_date' => 'date'
    ];
    
    /**
     * Get the user associated with this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * Get the locations associated with this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function locations(): HasMany
    {
        return $this->hasMany(UserLocation::class, 'user_id', 'user_id');
    }
    
    /**
     * Get the service areas associated with this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function serviceAreas(): HasMany
    {
        return $this->hasMany(DeliveryStaffServiceArea::class);
    }
    
    /**
     * Get the schedules associated with this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function schedules(): HasMany
    {
        return $this->hasMany(DeliveryStaffSchedule::class);
    }
    
    /**
     * Get the ratings associated with this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(DeliveryStaffRating::class);
    }
    
    /**
     * Get the orders delivered by this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_person', 'user_id');
    }
    
    /**
     * Get the delivery routes associated with this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function deliveryRoutes(): HasMany
    {
        return $this->hasMany(DeliveryRoute::class, 'delivery_person_id', 'id');
    }
    
    /**
     * Get the active orders for this delivery person.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function activeOrders(): HasMany
    {
        return $this->orders()
            ->whereIn('delivery_status', ['Pending', 'Dispatched'])
            ->whereIn('order_status', ['New', 'Processing']);
    }
    
    /**
     * Get the on-time delivery percentage.
     *
     * @return float
     */
    public function getOnTimePercentageAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }
        
        return round(($this->on_time_deliveries / $this->total_deliveries) * 100, 2);
    }
    
    /**
     * Get the completion rate.
     *
     * @return float
     */
    public function getCompletionRateAttribute(): float
    {
        $total = $this->total_deliveries + $this->failed_deliveries;
        
        if ($total === 0) {
            return 0;
        }
        
        return round(($this->total_deliveries / $total) * 100, 2);
    }
    
    /**
     * Check if the delivery person is available.
     *
     * @return bool
     */
    public function getIsAvailableAttribute(): bool
    {
        return $this->is_active && $this->on_duty;
    }
    
    /**
     * Get the profile photo URL.
     *
     * @return string|null
     */
    public function getProfilePhotoUrlAttribute(): ?string
    {
        if (!$this->profile_photo) {
            return null;
        }
        
        return url('storage/' . $this->profile_photo);
    }
    
    /**
     * Get the ID proof image URL.
     *
     * @return string|null
     */
    public function getIdProofImageUrlAttribute(): ?string
    {
        if (!$this->id_proof_image) {
            return null;
        }
        
        return url('storage/' . $this->id_proof_image);
    }
}

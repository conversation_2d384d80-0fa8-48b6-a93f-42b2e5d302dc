<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryAssignmentBatch extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'batch_number',
        'status',
        'total_orders',
        'assigned_orders',
        'failed_orders',
        'criteria',
        'notes',
        'created_by',
        'processed_at',
        'completed_at'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'criteria' => 'array',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime'
    ];
    
    /**
     * Get the user who created this batch.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    
    /**
     * Get the assignments associated with this batch.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(DeliveryAssignment::class, 'batch_id');
    }
    
    /**
     * Scope a query to only include pending batches.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * Scope a query to only include processing batches.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }
    
    /**
     * Scope a query to only include completed batches.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
    
    /**
     * Scope a query to only include failed batches.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }
    
    /**
     * Get the completion percentage.
     *
     * @return float
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->total_orders === 0) {
            return 0;
        }
        
        return round(($this->assigned_orders / $this->total_orders) * 100, 2);
    }
    
    /**
     * Get the failure percentage.
     *
     * @return float
     */
    public function getFailurePercentageAttribute(): float
    {
        if ($this->total_orders === 0) {
            return 0;
        }
        
        return round(($this->failed_orders / $this->total_orders) * 100, 2);
    }
    
    /**
     * Generate a unique batch number.
     *
     * @return string
     */
    public static function generateBatchNumber(): string
    {
        $prefix = 'BATCH';
        $date = now()->format('Ymd');
        $random = strtoupper(substr(uniqid(), -4));
        
        return "{$prefix}-{$date}-{$random}";
    }
}

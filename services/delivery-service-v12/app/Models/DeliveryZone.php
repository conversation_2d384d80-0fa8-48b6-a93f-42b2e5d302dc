<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryZone extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'kitchen_id',
        'zone_number',
        'min_distance_km',
        'max_distance_km',
        'base_delivery_fee',
        'additional_fee',
        'is_active',
        'description',
        'polygon_coordinates',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'min_distance_km' => 'float',
        'max_distance_km' => 'float',
        'base_delivery_fee' => 'float',
        'additional_fee' => 'float',
        'is_active' => 'boolean',
        'polygon_coordinates' => 'array',
    ];

    /**
     * Get the kitchen that owns the delivery zone.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(DeliveryLocation::class, 'kitchen_id', 'pk_location_code');
    }

    /**
     * Get the total delivery fee.
     *
     * @return float
     */
    public function getTotalFeeAttribute(): float
    {
        return $this->base_delivery_fee + $this->additional_fee;
    }

    /**
     * Get the distance range as a string.
     *
     * @return string
     */
    public function getDistanceRangeAttribute(): string
    {
        return "{$this->min_distance_km} - {$this->max_distance_km} km";
    }
}

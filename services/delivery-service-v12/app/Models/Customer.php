<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_code';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_name',
        'company_name',
        'customer_Address',
        'latitude',
        'longitude',
        'geocoded_address',
        'phone',
        'email_address',
        'location_code',
        'food_preference',
        'status',
        'dabbawala_code_type',
        'dabbawala_code',
        'dabbawala_image',
        'dabba_status',
        'delivery_person_id',
        'menu_type',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
        'latitude' => 'float',
        'longitude' => 'float',
    ];

    /**
     * Get the orders for the customer.
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the location for the customer.
     */
    public function location()
    {
        return $this->belongsTo(DeliveryLocation::class, 'location_code', 'pk_location_code');
    }

    /**
     * Get the delivery person for the customer.
     */
    public function deliveryPerson()
    {
        return $this->belongsTo(User::class, 'delivery_person_id');
    }

    /**
     * Get the delivery routes for the customer.
     */
    public function deliveryRoutes()
    {
        return $this->hasManyThrough(
            DeliveryRoute::class,
            Order::class,
            'customer_code', // Foreign key on orders table
            'order_id', // Foreign key on delivery_routes table
            'pk_customer_code', // Local key on customers table
            'pk_order_no' // Local key on orders table
        );
    }

    /**
     * Check if the customer has valid coordinates.
     *
     * @return bool
     */
    public function hasCoordinates(): bool
    {
        return $this->latitude !== null && $this->longitude !== null;
    }

    /**
     * Get the full address.
     *
     * @return string
     */
    public function getFullAddressAttribute(): string
    {
        return $this->customer_Address;
    }
}

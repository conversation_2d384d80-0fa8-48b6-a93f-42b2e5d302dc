<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class DeliveryProof extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'delivery_person_id',
        'proof_type',
        'image_path',
        'latitude',
        'longitude',
        'notes'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float'
    ];
    
    /**
     * Get the order associated with this proof.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'pk_order_no');
    }
    
    /**
     * Get the delivery person associated with this proof.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(DeliveryPerson::class);
    }
    
    /**
     * Get the image URL.
     *
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        return url(Storage::url($this->image_path));
    }
    
    /**
     * Get the proof type display name.
     *
     * @return string
     */
    public function getProofTypeDisplayAttribute(): string
    {
        switch ($this->proof_type) {
            case 'delivery':
                return 'Delivery Proof';
            case 'pickup':
                return 'Pickup Proof';
            case 'customer_signature':
                return 'Customer Signature';
            case 'other':
                return 'Other Proof';
            default:
                return 'Proof';
        }
    }
}

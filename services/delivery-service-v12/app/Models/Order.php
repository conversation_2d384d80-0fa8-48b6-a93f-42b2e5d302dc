<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'orders';
    
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_order_no';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_no',
        'customer_code',
        'customer_name',
        'customer_phone',
        'ship_address',
        'order_date',
        'delivery_status',
        'order_status',
        'delivery_person',
        'location_code',
        'amount',
        'tax',
        'delivery_charges',
        'applied_discount',
        'payment_mode',
        'amount_paid',
        'fk_kitchen_code',
        'order_menu',
        'delivery_type',
        'delivery_time',
        'delivery_end_time',
        'tp_delivery_order_id',
        'company_id',
        'unit_id',
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'order_date' => 'date',
        'amount' => 'float',
        'tax' => 'float',
        'delivery_charges' => 'float',
        'applied_discount' => 'float',
        'amount_paid' => 'boolean',
        'delivery_time' => 'datetime',
        'delivery_end_time' => 'datetime',
    ];
    
    /**
     * Get the customer that owns the order.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'pk_customer_code');
    }
    
    /**
     * Get the location for the order.
     */
    public function location()
    {
        return $this->belongsTo(DeliveryLocation::class, 'location_code', 'id');
    }
    
    /**
     * Get the delivery person for the order.
     */
    public function deliveryPerson()
    {
        return $this->belongsTo(User::class, 'delivery_person', 'id');
    }
}

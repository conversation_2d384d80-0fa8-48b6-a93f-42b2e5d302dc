<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * SchoolDeliveryBatch Model
 *
 * Represents a delivery batch for school meal deliveries.
 * Handles bulk delivery coordination with break time alignment.
 */
class SchoolDeliveryBatch extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'school_delivery_batches';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'company_id',
        'unit_id',
        'school_id',
        'batch_number',
        'delivery_date',
        'break_time_slot',
        'scheduled_delivery_time',
        'actual_delivery_time',
        'preparation_start_time',
        'preparation_end_time',
        'dispatch_time',
        'arrival_time',
        'delivery_person_id',
        'delivery_person_name',
        'delivery_person_phone',
        'vehicle_number',
        'vehicle_type',
        'total_meals',
        'total_children',
        'meal_breakdown',
        'grade_breakdown',
        'status',
        'status_notes',
        'status_history',
        'quality_check_passed',
        'quality_checked_by',
        'quality_check_time',
        'quality_notes',
        'temperature_at_dispatch',
        'temperature_at_delivery',
        'received_by_name',
        'received_by_designation',
        'received_by_phone',
        'delivery_confirmed_at',
        'delivery_notes',
        'delivery_photos',
        'delivery_time_minutes',
        'on_time_delivery',
        'delay_minutes',
        'delay_reason',
        'special_instructions',
        'requires_refrigeration',
        'fragile_items',
        'dietary_special_handling',
        'delivery_route',
        'estimated_distance_km',
        'actual_distance_km',
        'estimated_duration_minutes',
        'actual_duration_minutes',
        'delivery_cost',
        'fuel_cost',
        'packaging_cost',
        'total_cost',
        'school_rating',
        'school_feedback',
        'reported_issues',
        'requires_followup',
        'followup_notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'delivery_date' => 'date',
        'delivery_confirmed_at' => 'datetime',
        'meal_breakdown' => 'array',
        'grade_breakdown' => 'array',
        'status_history' => 'array',
        'delivery_photos' => 'array',
        'special_instructions' => 'array',
        'dietary_special_handling' => 'array',
        'delivery_route' => 'array',
        'reported_issues' => 'array',
        'temperature_at_dispatch' => 'decimal:2',
        'temperature_at_delivery' => 'decimal:2',
        'estimated_distance_km' => 'decimal:2',
        'actual_distance_km' => 'decimal:2',
        'delivery_cost' => 'decimal:2',
        'fuel_cost' => 'decimal:2',
        'packaging_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'school_rating' => 'decimal:2',
        'quality_check_passed' => 'boolean',
        'on_time_delivery' => 'boolean',
        'requires_refrigeration' => 'boolean',
        'fragile_items' => 'boolean',
        'requires_followup' => 'boolean',
        'scheduled_delivery_time' => 'datetime:H:i',
        'actual_delivery_time' => 'datetime:H:i',
        'preparation_start_time' => 'datetime:H:i',
        'preparation_end_time' => 'datetime:H:i',
        'dispatch_time' => 'datetime:H:i',
        'arrival_time' => 'datetime:H:i',
        'quality_check_time' => 'datetime:H:i',
    ];

    /**
     * Get the school that this delivery batch belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School::class);
    }

    /**
     * Get the delivery person assigned to this batch.
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(DeliveryPerson::class);
    }

    /**
     * Get the delivery items in this batch.
     */
    public function deliveryItems(): HasMany
    {
        return $this->hasMany(SchoolDeliveryItem::class, 'delivery_batch_id');
    }

    /**
     * Scope a query to only include batches for a specific date.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $date
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('delivery_date', $date);
    }

    /**
     * Scope a query to only include batches with a specific status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include active batches.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['scheduled', 'preparing', 'ready', 'dispatched', 'in_transit']);
    }

    /**
     * Scope a query to only include completed batches.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope a query to filter by school.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $schoolId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope a query to filter by break time slot.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $breakTimeSlot
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBreakTime($query, $breakTimeSlot)
    {
        return $query->where('break_time_slot', $breakTimeSlot);
    }

    /**
     * Scope a query to filter by delivery person.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $deliveryPersonId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDeliveryPerson($query, $deliveryPersonId)
    {
        return $query->where('delivery_person_id', $deliveryPersonId);
    }

    /**
     * Scope a query to filter by tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $tenantId
     * @param  int  $companyId
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, $tenantId, $companyId = null, $unitId = null)
    {
        $query->where('tenant_id', $tenantId);
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($unitId) {
            $query->where('unit_id', $unitId);
        }
        
        return $query;
    }

    /**
     * Check if the batch is currently active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['scheduled', 'preparing', 'ready', 'dispatched', 'in_transit']);
    }

    /**
     * Check if the batch is completed.
     *
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Check if the batch is delayed.
     *
     * @return bool
     */
    public function isDelayed(): bool
    {
        if (!$this->scheduled_delivery_time) {
            return false;
        }

        $now = now();
        $scheduledTime = $this->delivery_date->setTimeFromTimeString($this->scheduled_delivery_time);
        
        return $now->gt($scheduledTime) && !$this->isCompleted();
    }

    /**
     * Get the current delay in minutes.
     *
     * @return int
     */
    public function getCurrentDelayMinutes(): int
    {
        if (!$this->isDelayed()) {
            return 0;
        }

        $now = now();
        $scheduledTime = $this->delivery_date->setTimeFromTimeString($this->scheduled_delivery_time);
        
        return $now->diffInMinutes($scheduledTime);
    }

    /**
     * Add a status update to the history.
     *
     * @param string $status
     * @param string|null $notes
     * @param array|null $locationData
     * @return void
     */
    public function addStatusUpdate(string $status, ?string $notes = null, ?array $locationData = null): void
    {
        $statusHistory = $this->status_history ?? [];
        
        $statusHistory[] = [
            'status' => $status,
            'timestamp' => now()->toISOString(),
            'notes' => $notes,
            'location' => $locationData,
        ];
        
        $this->update(['status_history' => $statusHistory]);
    }

    /**
     * Get the latest status update.
     *
     * @return array|null
     */
    public function getLatestStatusUpdate(): ?array
    {
        $history = $this->status_history ?? [];
        return end($history) ?: null;
    }

    /**
     * Calculate delivery performance metrics.
     *
     * @return array
     */
    public function calculatePerformanceMetrics(): array
    {
        $metrics = [
            'on_time' => $this->on_time_delivery,
            'delay_minutes' => $this->delay_minutes,
            'delivery_time_minutes' => $this->delivery_time_minutes,
            'temperature_maintained' => true,
            'quality_passed' => $this->quality_check_passed,
            'school_satisfaction' => $this->school_rating,
        ];

        // Check temperature maintenance
        if ($this->temperature_at_dispatch && $this->temperature_at_delivery) {
            $tempDrop = $this->temperature_at_dispatch - $this->temperature_at_delivery;
            $metrics['temperature_maintained'] = $tempDrop <= 10; // Max 10°C drop allowed
            $metrics['temperature_drop'] = $tempDrop;
        }

        // Calculate efficiency score
        $efficiencyScore = 100;
        if ($this->delay_minutes > 0) {
            $efficiencyScore -= min(50, $this->delay_minutes * 2); // -2 points per minute delay
        }
        if (!$this->quality_check_passed) {
            $efficiencyScore -= 20;
        }
        if (!$metrics['temperature_maintained']) {
            $efficiencyScore -= 15;
        }
        
        $metrics['efficiency_score'] = max(0, $efficiencyScore);

        return $metrics;
    }

    /**
     * Get the estimated arrival time based on current location and traffic.
     *
     * @return \Carbon\Carbon|null
     */
    public function getEstimatedArrivalTime(): ?\Carbon\Carbon
    {
        if ($this->status !== 'in_transit' || !$this->dispatch_time) {
            return null;
        }

        $dispatchTime = $this->delivery_date->setTimeFromTimeString($this->dispatch_time);
        $estimatedDuration = $this->estimated_duration_minutes ?? 30;
        
        return $dispatchTime->addMinutes($estimatedDuration);
    }

    /**
     * Get meal breakdown summary.
     *
     * @return array
     */
    public function getMealBreakdownSummary(): array
    {
        $breakdown = $this->meal_breakdown ?? [];
        
        return [
            'total_meal_types' => count($breakdown),
            'most_popular_meal' => $breakdown ? array_keys($breakdown, max($breakdown))[0] : null,
            'breakdown' => $breakdown,
        ];
    }

    /**
     * Get grade breakdown summary.
     *
     * @return array
     */
    public function getGradeBreakdownSummary(): array
    {
        $breakdown = $this->grade_breakdown ?? [];
        
        return [
            'total_grades' => count($breakdown),
            'largest_grade' => $breakdown ? array_keys($breakdown, max($breakdown))[0] : null,
            'breakdown' => $breakdown,
        ];
    }

    /**
     * Check if the batch requires special handling.
     *
     * @return bool
     */
    public function requiresSpecialHandling(): bool
    {
        return $this->requires_refrigeration || 
               $this->fragile_items || 
               !empty($this->dietary_special_handling) ||
               !empty($this->special_instructions);
    }

    /**
     * Get delivery summary for notifications.
     *
     * @return array
     */
    public function getDeliverySummary(): array
    {
        return [
            'batch_number' => $this->batch_number,
            'school_name' => $this->school->school_name ?? 'Unknown School',
            'delivery_date' => $this->delivery_date->format('Y-m-d'),
            'break_time_slot' => $this->break_time_slot,
            'total_meals' => $this->total_meals,
            'total_children' => $this->total_children,
            'status' => $this->status,
            'delivery_person' => $this->delivery_person_name,
            'estimated_arrival' => $this->getEstimatedArrivalTime()?->format('H:i'),
            'special_handling_required' => $this->requiresSpecialHandling(),
        ];
    }
}

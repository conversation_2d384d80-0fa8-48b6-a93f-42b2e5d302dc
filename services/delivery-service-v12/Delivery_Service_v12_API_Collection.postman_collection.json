{"info": {"_postman_id": "delivery-service-v12-api", "name": "Delivery Service v12 API Collection", "description": "Comprehensive API collection for Delivery Service v12 with real data integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "delivery-service-v12"}, "item": [{"name": "Delivery Management", "item": [{"name": "Get Delivery Locations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/locations", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "locations"]}}}, {"name": "Get Delivery Locations with Filters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/locations?status=true&city=Mumbai&search=Andheri", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "locations"], "query": [{"key": "status", "value": "true"}, {"key": "city", "value": "Mumbai"}, {"key": "search", "value": "<PERSON><PERSON><PERSON>"}]}}}, {"name": "Get Delivery Persons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/persons", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "persons"]}}}, {"name": "Get Available Delivery Persons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/persons?status=true&on_duty=true", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "persons"], "query": [{"key": "status", "value": "true"}, {"key": "on_duty", "value": "true"}]}}}, {"name": "Get Delivery Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/orders", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "orders"]}}}, {"name": "Get Orders by Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/orders?status=New&delivery_date=2025-06-04", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "orders"], "query": [{"key": "status", "value": "New"}, {"key": "delivery_date", "value": "2025-06-04"}]}}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"out_for_delivery\",\n    \"delivery_notes\": \"Order dispatched for delivery\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/orders/1/status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "orders", "1", "status"]}}}]}, {"name": "Third-Party Delivery", "item": [{"name": "Book Third-Party Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": 1,\n    \"provider\": \"your<PERSON>y\",\n    \"pickup_address\": \"Kitchen Location, Andheri West, Mumbai\",\n    \"delivery_address\": \"A-101, Sunrise Apartments, Andheri West, Mumbai\",\n    \"pickup_time\": \"2025-06-04T11:00:00Z\",\n    \"delivery_time\": \"2025-06-04T12:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/third-party/book", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "third-party", "book"]}}}, {"name": "Get Third-Party Delivery Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/third-party/1/status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "third-party", "1", "status"]}}}, {"name": "Cancel Third-Party Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/third-party/1/cancel", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "third-party", "1", "cancel"]}}}]}, {"name": "Dabbawala Services", "item": [{"name": "Generate Dabbawala Code", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": 1,\n    \"pickup_location\": \"Kitchen, Andheri West\",\n    \"delivery_location\": \"Office Complex, Bandra West\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/dabbawala/generate-code", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "dabbawala", "generate-code"]}}}]}, {"name": "Delivery Staff Management", "item": [{"name": "Get All Delivery Staff", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/staff", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff"]}}}, {"name": "Get Staff by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1"]}}}, {"name": "Create Delivery Staff", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"New Delivery Person\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"9876543212\",\n    \"vehicle_type\": \"bike\",\n    \"vehicle_number\": \"MH01XY9999\",\n    \"is_active\": true,\n    \"on_duty\": false\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/staff", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff"]}}}, {"name": "Update Staff Duty Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"on_duty\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1/duty-status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1", "duty-status"]}}}, {"name": "Update Staff Location", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 19.1136,\n    \"longitude\": 72.8697\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1/location", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1", "location"]}}}, {"name": "Get Staff Performance", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1/performance", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1", "performance"]}}}]}, {"name": "Delivery Zones", "item": [{"name": "Get All Delivery Zones", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/zones", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones"]}}}, {"name": "Create Delivery Zone", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Andheri Zone\",\n    \"description\": \"Delivery zone covering Andheri area\",\n    \"coordinates\": [[19.1136, 72.8697], [19.1200, 72.8800]],\n    \"delivery_charges\": 50,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/zones", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones"]}}}, {"name": "Check Delivery Zone", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 19.1136,\n    \"longitude\": 72.8697\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/zones/check", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones", "check"]}}}]}, {"name": "Delivery Assignments", "item": [{"name": "Get All Assignments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/assignments", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments"]}}}, {"name": "Assign Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": 1,\n    \"delivery_person_id\": 1,\n    \"estimated_delivery_time\": \"2025-06-04T12:00:00Z\",\n    \"notes\": \"Handle with care\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/assignments/assign", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments", "assign"]}}}, {"name": "Batch Assign Deliveries", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"assignments\": [\n        {\n            \"order_id\": 1,\n            \"delivery_person_id\": 1\n        },\n        {\n            \"order_id\": 2,\n            \"delivery_person_id\": 2\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/assignments/batch", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments", "batch"]}}}, {"name": "Get Assignment Batches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/assignments/batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments", "batches"]}}}]}, {"name": "Delivery Tracking", "item": [{"name": "Get Dashboard Data", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/dashboard", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "dashboard"]}}}, {"name": "Get Order Tracking", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/orders/1", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "orders", "1"]}}}, {"name": "Update Delivery Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"delivered\",\n    \"delivery_notes\": \"Order delivered successfully\",\n    \"delivered_at\": \"2025-06-04T12:30:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/orders/1/status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "orders", "1", "status"]}}}, {"name": "Upload Delivery Proof", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "proof_image", "type": "file", "src": []}, {"key": "proof_type", "value": "delivery_confirmation", "type": "text"}, {"key": "notes", "value": "Order delivered to customer", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/orders/1/proof", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "orders", "1", "proof"]}}}]}, {"name": "Map Services", "item": [{"name": "Get Delivery Locations on Map", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/map/delivery-locations", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "delivery-locations"]}}}, {"name": "Get Customers on Map", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/map/customers", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "customers"]}}}, {"name": "Geocode Address", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"address\": \"Andheri West, Mumbai, Maharashtra\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/map/geocode", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "geocode"]}}}]}, {"name": "School Delivery", "item": [{"name": "Get School Delivery Batches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/school/batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "batches"]}}}, {"name": "Create School Delivery Batch", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"school_id\": 1,\n    \"delivery_date\": \"2025-06-04\",\n    \"meal_type\": \"lunch\",\n    \"total_meals\": 150,\n    \"break_time\": \"12:00-13:00\",\n    \"special_instructions\": \"Handle with care\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/school/batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "batches"]}}}, {"name": "Get School Performance Metrics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/school/performance-metrics", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "performance-metrics"]}}}, {"name": "Generate School Batches", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2025-06-04\",\n    \"meal_type\": \"lunch\",\n    \"school_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/school/generate-batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "generate-batches"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8106", "type": "string"}]}
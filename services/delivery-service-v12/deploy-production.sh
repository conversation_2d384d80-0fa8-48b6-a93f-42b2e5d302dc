#!/bin/bash

# Exit on error
set -e

# Configuration
PRODUCTION_SERVER="production-server.fooddialer.com"
PRODUCTION_USER="deploy"
PRODUCTION_PATH="/var/www/delivery-service"
DOCKER_REGISTRY="registry.fooddialer.com"
DOCKER_IMAGE="delivery-service"
DOCKER_TAG="production"

# Build Docker image
echo "Building Docker image..."
docker build -t ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${DOCKER_TAG} .

# Push Docker image to registry
echo "Pushing Docker image to registry..."
docker push ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${DOCKER_TAG}

# Deploy to production server using blue-green deployment
echo "Deploying to production server using blue-green deployment..."
ssh ${PRODUCTION_USER}@${PRODUCTION_SERVER} << EOF
    cd ${PRODUCTION_PATH}
    
    # Determine current environment (blue or green)
    if [ -f ".env.current" ]; then
        CURRENT_ENV=\$(cat .env.current)
    else
        CURRENT_ENV="blue"
        echo \${CURRENT_ENV} > .env.current
    fi
    
    # Determine target environment
    if [ "\${CURRENT_ENV}" == "blue" ]; then
        TARGET_ENV="green"
    else
        TARGET_ENV="blue"
    fi
    
    echo "Current environment: \${CURRENT_ENV}"
    echo "Target environment: \${TARGET_ENV}"
    
    # Pull the latest image
    docker pull ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${DOCKER_TAG}
    
    # Start the new environment
    docker-compose -f docker-compose.\${TARGET_ENV}.yml up -d
    
    # Run migrations and cache configuration
    docker exec delivery-service-\${TARGET_ENV}-app php artisan migrate --force
    docker exec delivery-service-\${TARGET_ENV}-app php artisan config:cache
    docker exec delivery-service-\${TARGET_ENV}-app php artisan route:cache
    docker exec delivery-service-\${TARGET_ENV}-app php artisan view:cache
    
    # Health check
    echo "Performing health check..."
    HEALTH_CHECK_URL="http://localhost:8080/api/v2/delivery/health"
    MAX_RETRIES=10
    RETRY_INTERVAL=5
    
    for i in \$(seq 1 \${MAX_RETRIES}); do
        HTTP_STATUS=\$(curl -s -o /dev/null -w "%{http_code}" \${HEALTH_CHECK_URL})
        
        if [ \${HTTP_STATUS} -eq 200 ]; then
            echo "Health check passed!"
            break
        else
            echo "Health check failed with status \${HTTP_STATUS}. Retrying in \${RETRY_INTERVAL} seconds..."
            sleep \${RETRY_INTERVAL}
        fi
        
        if [ \$i -eq \${MAX_RETRIES} ]; then
            echo "Health check failed after \${MAX_RETRIES} retries. Rolling back..."
            docker-compose -f docker-compose.\${TARGET_ENV}.yml down
            exit 1
        fi
    done
    
    # Update Kong API Gateway to point to the new environment
    echo "Updating Kong API Gateway..."
    curl -X PATCH http://localhost:8001/services/delivery-service \\
        -d url=http://delivery-service-\${TARGET_ENV}-app:80
    
    # Wait for Kong to update
    sleep 5
    
    # Stop the old environment
    docker-compose -f docker-compose.\${CURRENT_ENV}.yml down
    
    # Update current environment
    echo \${TARGET_ENV} > .env.current
    
    echo "Deployment completed successfully!"
EOF

echo "Production deployment completed successfully!"

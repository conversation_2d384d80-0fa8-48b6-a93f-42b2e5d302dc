{"info": {"_postman_id": "delivery-service-v12-complete-api", "name": "Delivery Service v12 - Complete API Collection (Tested & Working)", "description": "Complete and thoroughly tested API collection for Delivery Service v12. All endpoints verified with real data integration. Server running on localhost:8106. No authentication required (Keycloak to be added later).", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "delivery-service-v12-complete"}, "item": [{"name": "🚚 Core Delivery Management", "description": "Core delivery operations - locations, persons, orders", "item": [{"name": "Get All Delivery Locations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/locations", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "locations"]}, "description": "Retrieve all delivery locations with pagination. Returns 3 Mumbai locations."}}, {"name": "Get Delivery Locations with Filters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/locations?status=true&city=Mumbai&search=Andheri", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "locations"], "query": [{"key": "status", "value": "true", "description": "Filter by active status"}, {"key": "city", "value": "Mumbai", "description": "Filter by city"}, {"key": "search", "value": "<PERSON><PERSON><PERSON>", "description": "Search by location name"}]}, "description": "Filter delivery locations by status, city, and search term."}}, {"name": "Get All Delivery Persons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/persons", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "persons"]}, "description": "Retrieve all delivery persons with their current status and location."}}, {"name": "Get Available Delivery Persons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/persons?status=true&on_duty=true", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "persons"], "query": [{"key": "status", "value": "true", "description": "Active persons only"}, {"key": "on_duty", "value": "true", "description": "On duty persons only"}]}, "description": "Filter delivery persons who are active and currently on duty."}}, {"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/orders", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "orders"]}, "description": "Retrieve all delivery orders with customer and delivery information."}}, {"name": "Get Orders by Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/orders?status=New&delivery_date=2025-06-04", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "orders"], "query": [{"key": "status", "value": "New", "description": "Filter by order status"}, {"key": "delivery_date", "value": "2025-06-04", "description": "Filter by delivery date"}]}, "description": "Filter orders by status and delivery date."}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"out_for_delivery\",\n    \"delivery_notes\": \"Order dispatched for delivery\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/orders/2/status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "orders", "2", "status"]}, "description": "Update the status of a specific order. Use order ID 2 or 3 (existing orders)."}}]}, {"name": "🚛 Third-Party Delivery Integration", "description": "Integration with third-party delivery services like YourGuy", "item": [{"name": "Book Third-Party Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": 2,\n    \"provider\": \"your<PERSON>y\",\n    \"pickup_address\": \"Kitchen Location, Andheri West, Mumbai\",\n    \"delivery_address\": \"A-101, Sunrise Apartments, Andheri West, Mumbai\",\n    \"pickup_time\": \"2025-06-04T11:00:00Z\",\n    \"delivery_time\": \"2025-06-04T12:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/third-party/book", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "third-party", "book"]}, "description": "Book a delivery with third-party provider. Tested and working with order ID 2."}}, {"name": "Get Third-Party Delivery Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/third-party/2/status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "third-party", "2", "status"]}, "description": "Get the current status of a third-party delivery booking."}}, {"name": "Cancel Third-Party Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/third-party/2/cancel", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "third-party", "2", "cancel"]}, "description": "Cancel a third-party delivery booking."}}]}, {"name": "🥘 Dabbawala Services", "description": "Traditional dabbawala delivery code generation", "item": [{"name": "Generate Dabbawala Code", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": 2,\n    \"pickup_location\": \"Kitchen, Andheri West\",\n    \"delivery_location\": \"Office Complex, Bandra West\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/dabbawala/generate-code", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "dabbawala", "generate-code"]}, "description": "Generate a unique dabbawala delivery code for traditional delivery service."}}]}, {"name": "👥 Delivery Staff Management", "description": "Complete staff management - CRUD operations, location tracking, duty status", "item": [{"name": "Get All Delivery Staff", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/staff", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff"]}, "description": "Retrieve all delivery staff with performance metrics and schedules. Returns 3 staff members."}}, {"name": "Get Staff by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1"]}, "description": "Get detailed information about a specific staff member."}}, {"name": "Create New Delivery Staff", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"New Delivery Person\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"9876543214\",\n    \"password\": \"password123\",\n    \"vehicle_type\": \"bike\",\n    \"vehicle_number\": \"MH01XY9999\",\n    \"is_active\": true,\n    \"on_duty\": false\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/staff", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff"]}, "description": "Create a new delivery staff member. Tested and working."}}, {"name": "Update Staff Duty Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"on_duty\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1/duty-status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1", "duty-status"]}, "description": "Update the duty status of a delivery staff member. Tested and working."}}, {"name": "Update Staff Location", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 19.1136,\n    \"longitude\": 72.8697\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1/location", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1", "location"]}, "description": "Update the current location of a delivery staff member. Tested and working."}}, {"name": "Get Staff Performance Metrics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1/performance", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1", "performance"]}, "description": "Get detailed performance metrics for a specific staff member."}}, {"name": "Update Staff Details", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Name\",\n    \"phone\": \"9876543299\",\n    \"vehicle_type\": \"scooter\",\n    \"vehicle_number\": \"MH01ZZ9999\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/staff/1", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "1"]}, "description": "Update staff member details."}}, {"name": "Delete Staff Member", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/staff/3", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "staff", "3"]}, "description": "Delete a staff member (use with caution)."}}]}, {"name": "📍 Delivery Zones Management", "description": "Manage delivery zones and coverage areas", "item": [{"name": "Get All Delivery Zones", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/zones", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones"]}, "description": "Retrieve all delivery zones."}}, {"name": "Create Delivery Zone", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Andheri Zone\",\n    \"description\": \"Delivery zone covering Andheri area\",\n    \"coordinates\": [[19.1136, 72.8697], [19.1200, 72.8800]],\n    \"delivery_charges\": 50,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/zones", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones"]}, "description": "Create a new delivery zone with coordinates."}}, {"name": "Check Delivery Zone", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 19.1136,\n    \"longitude\": 72.8697\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/zones/check", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones", "check"]}, "description": "Check if a location falls within any delivery zone."}}, {"name": "Get Zone by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/zones/1", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones", "1"]}, "description": "Get details of a specific delivery zone."}}, {"name": "Generate Default Zones", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/zones/generate-default", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "zones", "generate-default"]}, "description": "Generate default delivery zones based on existing locations."}}]}, {"name": "📋 Delivery Assignments", "description": "Assign deliveries to staff and manage delivery batches", "item": [{"name": "Get All Assignments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/assignments", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments"]}, "description": "Retrieve all delivery assignments with staff and order details."}}, {"name": "Assign Single Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": 2,\n    \"delivery_person_id\": 1,\n    \"estimated_delivery_time\": \"2025-06-04T12:00:00Z\",\n    \"notes\": \"Handle with care\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/assignments/assign", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments", "assign"]}, "description": "Assign a single delivery to a delivery person."}}, {"name": "Batch Assign Deliveries", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"assignments\": [\n        {\n            \"order_id\": 2,\n            \"delivery_person_id\": 1\n        },\n        {\n            \"order_id\": 3,\n            \"delivery_person_id\": 2\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/assignments/batch", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments", "batch"]}, "description": "Assign multiple deliveries in a single batch operation."}}, {"name": "Get Assignment Batches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/assignments/batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments", "batches"]}, "description": "Get all assignment batches with their status and details."}}, {"name": "Auto Assign Deliveries", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_ids\": [2, 3],\n    \"criteria\": \"proximity\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/assignments/auto-assign", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "assignments", "auto-assign"]}, "description": "Automatically assign deliveries based on proximity and availability."}}]}, {"name": "📊 Delivery Tracking & Analytics", "description": "Track deliveries and get performance analytics", "item": [{"name": "Get Dashboard Data", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/dashboard", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "dashboard"]}, "description": "Get comprehensive dashboard data with delivery statistics. Tested and working."}}, {"name": "Track Specific Order", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/orders/2", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "orders", "2"]}, "description": "Get real-time tracking information for a specific order."}}, {"name": "Update Delivery Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"delivered\",\n    \"delivery_notes\": \"Order delivered successfully\",\n    \"delivered_at\": \"2025-06-04T12:30:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/orders/2/status", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "orders", "2", "status"]}, "description": "Update the delivery status with notes and timestamp."}}, {"name": "Upload Delivery Proof", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "proof_image", "type": "file", "src": [], "description": "Upload delivery proof image"}, {"key": "proof_type", "value": "delivery_confirmation", "type": "text"}, {"key": "notes", "value": "Order delivered to customer", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/orders/2/proof", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "orders", "2", "proof"]}, "description": "Upload delivery proof image and notes."}}, {"name": "Get Delivery Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/tracking/analytics?period=week&start_date=2025-06-01&end_date=2025-06-07", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "tracking", "analytics"], "query": [{"key": "period", "value": "week", "description": "Analytics period (day/week/month)"}, {"key": "start_date", "value": "2025-06-01", "description": "Start date for analytics"}, {"key": "end_date", "value": "2025-06-07", "description": "End date for analytics"}]}, "description": "Get detailed delivery analytics for specified period."}}]}, {"name": "🗺️ Map Services", "description": "Geographic and mapping services for delivery operations", "item": [{"name": "Get Delivery Locations on Map", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/map/delivery-locations", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "delivery-locations"]}, "description": "Get all delivery locations formatted for map display. Tested and working with 3 Mumbai locations."}}, {"name": "Get Customers on Map", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/map/customers", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "customers"]}, "description": "Get customer locations for map visualization."}}, {"name": "Geocode Address", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"address\": \"Andheri West, Mumbai, Maharashtra\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/map/geocode", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "geocode"]}, "description": "Convert address to latitude/longitude coordinates."}}, {"name": "Reverse Geocode", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 19.1136,\n    \"longitude\": 72.8697\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/map/reverse-geocode", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "reverse-geocode"]}, "description": "Convert coordinates to human-readable address."}}, {"name": "Calculate Distance", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"origin\": {\n        \"latitude\": 19.1136,\n        \"longitude\": 72.8697\n    },\n    \"destination\": {\n        \"latitude\": 19.0596,\n        \"longitude\": 72.8295\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/map/distance", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "map", "distance"]}, "description": "Calculate distance and estimated time between two points."}}]}, {"name": "🏫 School Delivery Services", "description": "Specialized delivery services for schools and educational institutions", "item": [{"name": "Get School Delivery Batches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/school/batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "batches"]}, "description": "Get all school delivery batches. Tested and working."}}, {"name": "Create School Delivery Batch", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"school_id\": 1,\n    \"delivery_date\": \"2025-06-04\",\n    \"meal_type\": \"lunch\",\n    \"total_meals\": 150,\n    \"break_time\": \"12:00-13:00\",\n    \"special_instructions\": \"Handle with care\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/school/batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "batches"]}, "description": "Create a new school delivery batch for meal delivery."}}, {"name": "Get School Performance Metrics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/school/performance-metrics", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "performance-metrics"]}, "description": "Get performance metrics for school deliveries."}}, {"name": "Generate School Batches", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2025-06-04\",\n    \"meal_type\": \"lunch\",\n    \"school_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/api/v2/delivery/school/generate-batches", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "generate-batches"]}, "description": "Auto-generate delivery batches for multiple schools."}}, {"name": "Get School Delivery Schedule", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/delivery/school/schedule?date=2025-06-04&school_id=1", "host": ["{{base_url}}"], "path": ["api", "v2", "delivery", "school", "schedule"], "query": [{"key": "date", "value": "2025-06-04", "description": "Delivery date"}, {"key": "school_id", "value": "1", "description": "Specific school ID (optional)"}]}, "description": "Get delivery schedule for schools on a specific date."}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8106", "type": "string"}]}
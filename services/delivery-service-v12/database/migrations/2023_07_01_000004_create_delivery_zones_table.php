<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_zones', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('kitchen_id');
            $table->integer('zone_number');
            $table->decimal('min_distance_km', 8, 2);
            $table->decimal('max_distance_km', 8, 2);
            $table->decimal('base_delivery_fee', 8, 2);
            $table->decimal('additional_fee', 8, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->json('polygon_coordinates')->nullable();
            $table->timestamps();
            
            $table->unique(['kitchen_id', 'zone_number']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_zones');
    }
};

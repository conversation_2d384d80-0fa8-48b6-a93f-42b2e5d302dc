<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('orders')) {
            Schema::create('orders', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('customer_id');
                $table->string('status')->default('pending');
                $table->decimal('total_amount', 10, 2);
                $table->text('delivery_address')->nullable();
                $table->decimal('delivery_latitude', 10, 8)->nullable();
                $table->decimal('delivery_longitude', 11, 8)->nullable();
                $table->timestamp('delivery_time')->nullable();
                $table->timestamps();

                $table->foreign('customer_id')->references('id')->on('customers');
            });
        } else {
            // Add delivery-specific columns if they don't exist
            Schema::table('orders', function (Blueprint $table) {
                if (!Schema::hasColumn('orders', 'delivery_address')) {
                    $table->text('delivery_address')->nullable();
                }
                if (!Schema::hasColumn('orders', 'delivery_latitude')) {
                    $table->decimal('delivery_latitude', 10, 8)->nullable();
                }
                if (!Schema::hasColumn('orders', 'delivery_longitude')) {
                    $table->decimal('delivery_longitude', 11, 8)->nullable();
                }
                if (!Schema::hasColumn('orders', 'delivery_time')) {
                    $table->timestamp('delivery_time')->nullable();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};

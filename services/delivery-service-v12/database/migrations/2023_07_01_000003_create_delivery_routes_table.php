<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_routes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('kitchen_id')->nullable();
            $table->unsignedBigInteger('delivery_person_id')->nullable();
            $table->decimal('distance_km', 8, 2)->nullable();
            $table->integer('duration_seconds')->nullable();
            $table->json('route_geometry')->nullable();
            $table->json('route_steps')->nullable();
            $table->decimal('start_lat', 10, 7)->nullable();
            $table->decimal('start_lon', 10, 7)->nullable();
            $table->decimal('end_lat', 10, 7)->nullable();
            $table->decimal('end_lon', 10, 7)->nullable();
            $table->timestamp('estimated_delivery_time')->nullable();
            $table->timestamp('actual_delivery_time')->nullable();
            $table->string('traffic_condition')->nullable();
            $table->integer('delivery_zone')->nullable();
            $table->decimal('delivery_fee', 8, 2)->nullable();
            $table->timestamps();
            
            $table->foreign('order_id')->references('pk_order_no')->on('orders')->onDelete('cascade');
            $table->index(['delivery_person_id', 'estimated_delivery_time']);
            $table->index('kitchen_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_routes');
    }
};

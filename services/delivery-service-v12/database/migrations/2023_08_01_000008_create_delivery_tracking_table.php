<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_tracking', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('delivery_person_id');
            $table->enum('status', ['pending', 'dispatched', 'in_transit', 'delivered', 'failed']);
            $table->timestamp('pending_at')->nullable();
            $table->timestamp('dispatched_at')->nullable();
            $table->timestamp('in_transit_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->decimal('current_latitude', 10, 7)->nullable();
            $table->decimal('current_longitude', 10, 7)->nullable();
            $table->timestamp('last_location_update')->nullable();
            $table->decimal('distance_traveled', 10, 2)->nullable()->comment('Distance traveled in kilometers');
            $table->unsignedInteger('travel_time')->nullable()->comment('Travel time in seconds');
            $table->json('route_history')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->foreign('order_id')
                  ->references('pk_order_no')
                  ->on('orders')
                  ->onDelete('cascade');
                  
            $table->foreign('delivery_person_id')
                  ->references('id')
                  ->on('delivery_persons')
                  ->onDelete('cascade');
                  
            $table->unique('order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_tracking');
    }
};

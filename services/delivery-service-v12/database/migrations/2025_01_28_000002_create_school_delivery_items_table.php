<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('school_delivery_items')) {
            Schema::create('school_delivery_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            
            // Batch relationship
            $table->unsignedBigInteger('delivery_batch_id');
            $table->foreign('delivery_batch_id')->references('id')->on('school_delivery_batches')->onDelete('cascade');
            
            // Child and subscription details
            $table->unsignedBigInteger('child_profile_id');
            $table->foreign('child_profile_id')->references('id')->on('child_profiles')->onDelete('cascade');
            
            $table->unsignedBigInteger('subscription_id');
            $table->foreign('subscription_id')->references('id')->on('school_meal_subscriptions')->onDelete('cascade');
            
            $table->unsignedBigInteger('meal_plan_id');
            $table->foreign('meal_plan_id')->references('id')->on('meal_plans')->onDelete('restrict');
            
            // Item identification
            $table->string('item_code')->unique();
            $table->string('child_name');
            $table->string('grade_section');
            $table->string('roll_number')->nullable();
            
            // Meal details
            $table->string('meal_plan_name');
            $table->json('meal_components');
            $table->integer('quantity')->default(1);
            $table->enum('meal_type', ['breakfast', 'lunch', 'snack', 'dinner'])->default('lunch');
            
            // Customizations and special requirements
            $table->json('customizations')->nullable()->comment('Child-specific customizations');
            $table->json('dietary_restrictions')->nullable();
            $table->enum('spice_level', ['no_spice', 'mild', 'medium', 'spicy'])->default('mild');
            $table->text('special_instructions')->nullable();
            
            // Packaging and labeling
            $table->string('container_type', 50)->nullable();
            $table->string('label_color', 20)->nullable();
            $table->boolean('requires_heating')->default(false);
            $table->boolean('requires_refrigeration')->default(false);
            $table->json('packaging_instructions')->nullable();
            
            // Quality and preparation
            $table->time('preparation_time')->nullable();
            $table->string('prepared_by')->nullable();
            $table->boolean('quality_checked')->default(false);
            $table->string('quality_checked_by')->nullable();
            $table->time('quality_check_time')->nullable();
            
            // Delivery status
            $table->enum('status', ['pending', 'prepared', 'packed', 'loaded', 'delivered', 'consumed', 'returned', 'wasted'])->default('pending');
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('consumed_at')->nullable();
            $table->boolean('child_present')->nullable();
            $table->text('delivery_notes')->nullable();
            
            // Consumption tracking
            $table->enum('consumption_status', ['not_consumed', 'partially_consumed', 'fully_consumed', 'child_absent'])->nullable();
            $table->decimal('consumption_percentage', 5, 2)->nullable();
            $table->text('consumption_notes')->nullable();
            $table->json('leftover_items')->nullable()->comment('Items not consumed');
            
            // Feedback and issues
            $table->decimal('child_rating', 3, 2)->nullable();
            $table->text('child_feedback')->nullable();
            $table->json('reported_issues')->nullable();
            $table->boolean('replacement_required')->default(false);
            $table->text('replacement_reason')->nullable();
            
            // Cost and billing
            $table->decimal('item_cost', 8, 2);
            $table->decimal('packaging_cost', 8, 2)->default(0.00);
            $table->decimal('total_cost', 8, 2);
            $table->boolean('billed')->default(false);
            $table->date('billing_date')->nullable();
            
            // Nutritional tracking
            $table->json('nutritional_delivered')->nullable()->comment('Actual nutritional content delivered');
            $table->json('nutritional_consumed')->nullable()->comment('Estimated nutritional content consumed');
            
            // Temperature monitoring
            $table->decimal('temperature_at_preparation', 5, 2)->nullable();
            $table->decimal('temperature_at_packing', 5, 2)->nullable();
            $table->decimal('temperature_at_delivery', 5, 2)->nullable();
            $table->boolean('temperature_maintained')->default(true);
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['tenant_id', 'company_id', 'unit_id'], 'idx_delivery_items_tenant');
            $table->index(['delivery_batch_id'], 'idx_delivery_items_batch');
            $table->index(['child_profile_id'], 'idx_delivery_items_child');
            $table->index(['subscription_id'], 'idx_delivery_items_subscription');
            $table->index(['meal_plan_id'], 'idx_delivery_items_plan');
            $table->index(['status'], 'idx_delivery_items_status');
            $table->index(['consumption_status'], 'idx_delivery_items_consumption');
            $table->index(['child_name', 'grade_section'], 'idx_delivery_items_child_grade');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_delivery_items');
    }
};

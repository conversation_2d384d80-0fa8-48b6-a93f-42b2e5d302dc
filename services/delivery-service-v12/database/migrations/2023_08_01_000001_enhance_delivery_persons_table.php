<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('delivery_persons', function (Blueprint $table) {
            // Add new fields for enhanced delivery staff management
            if (!Schema::hasColumn('delivery_persons', 'profile_photo')) {
                $table->string('profile_photo')->nullable()->after('email');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'id_proof_type')) {
                $table->string('id_proof_type')->nullable()->after('profile_photo');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'id_proof_number')) {
                $table->string('id_proof_number')->nullable()->after('id_proof_type');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'id_proof_image')) {
                $table->string('id_proof_image')->nullable()->after('id_proof_number');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'emergency_contact_name')) {
                $table->string('emergency_contact_name')->nullable()->after('id_proof_image');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'emergency_contact_phone')) {
                $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'vehicle_type')) {
                $table->string('vehicle_type')->nullable()->after('emergency_contact_phone');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'vehicle_number')) {
                $table->string('vehicle_number')->nullable()->after('vehicle_type');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'vehicle_details')) {
                $table->text('vehicle_details')->nullable()->after('vehicle_number');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('status');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'on_duty')) {
                $table->boolean('on_duty')->default(false)->after('is_active');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'current_latitude')) {
                $table->decimal('current_latitude', 10, 7)->nullable()->after('on_duty');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'current_longitude')) {
                $table->decimal('current_longitude', 10, 7)->nullable()->after('current_latitude');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'last_location_update')) {
                $table->timestamp('last_location_update')->nullable()->after('current_longitude');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'rating')) {
                $table->decimal('rating', 3, 2)->default(0)->after('last_location_update');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'total_ratings')) {
                $table->integer('total_ratings')->default(0)->after('rating');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'total_deliveries')) {
                $table->integer('total_deliveries')->default(0)->after('total_ratings');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'on_time_deliveries')) {
                $table->integer('on_time_deliveries')->default(0)->after('total_deliveries');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'late_deliveries')) {
                $table->integer('late_deliveries')->default(0)->after('on_time_deliveries');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'failed_deliveries')) {
                $table->integer('failed_deliveries')->default(0)->after('late_deliveries');
            }
            
            if (!Schema::hasColumn('delivery_persons', 'joining_date')) {
                $table->date('joining_date')->nullable()->after('failed_deliveries');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_persons', function (Blueprint $table) {
            // Drop the added columns
            $columns = [
                'profile_photo',
                'id_proof_type',
                'id_proof_number',
                'id_proof_image',
                'emergency_contact_name',
                'emergency_contact_phone',
                'vehicle_type',
                'vehicle_number',
                'vehicle_details',
                'is_active',
                'on_duty',
                'current_latitude',
                'current_longitude',
                'last_location_update',
                'rating',
                'total_ratings',
                'total_deliveries',
                'on_time_deliveries',
                'late_deliveries',
                'failed_deliveries',
                'joining_date'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('delivery_persons', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};

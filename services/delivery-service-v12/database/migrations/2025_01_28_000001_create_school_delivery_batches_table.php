<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('school_delivery_batches', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            
            // School and delivery information
            $table->unsignedBigInteger('school_id');
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('cascade');
            
            // Batch identification
            $table->string('batch_number')->unique();
            $table->date('delivery_date');
            $table->enum('break_time_slot', ['morning_break', 'lunch_break', 'afternoon_break'])->default('lunch_break');
            
            // Timing details
            $table->time('scheduled_delivery_time');
            $table->time('actual_delivery_time')->nullable();
            $table->time('preparation_start_time')->nullable();
            $table->time('preparation_end_time')->nullable();
            $table->time('dispatch_time')->nullable();
            $table->time('arrival_time')->nullable();
            
            // Delivery personnel
            $table->unsignedBigInteger('delivery_person_id')->nullable();
            $table->string('delivery_person_name')->nullable();
            $table->string('delivery_person_phone', 20)->nullable();
            $table->string('vehicle_number', 20)->nullable();
            $table->enum('vehicle_type', ['bike', 'scooter', 'car', 'van', 'truck'])->nullable();
            
            // Batch contents
            $table->integer('total_meals');
            $table->integer('total_children');
            $table->json('meal_breakdown')->comment('Breakdown by meal plan: {"plan_1": 25, "plan_2": 15}');
            $table->json('grade_breakdown')->comment('Breakdown by grade: {"grade_1": 10, "grade_2": 15, "grade_3": 15}');
            
            // Delivery status tracking
            $table->enum('status', ['scheduled', 'preparing', 'ready', 'dispatched', 'in_transit', 'delivered', 'failed', 'cancelled'])->default('scheduled');
            $table->text('status_notes')->nullable();
            $table->json('status_history')->nullable()->comment('Status change history with timestamps');
            
            // Quality control
            $table->boolean('quality_check_passed')->default(false);
            $table->string('quality_checked_by')->nullable();
            $table->time('quality_check_time')->nullable();
            $table->text('quality_notes')->nullable();
            $table->decimal('temperature_at_dispatch', 5, 2)->nullable()->comment('Temperature in Celsius');
            $table->decimal('temperature_at_delivery', 5, 2)->nullable();
            
            // Delivery confirmation
            $table->string('received_by_name')->nullable();
            $table->string('received_by_designation')->nullable();
            $table->string('received_by_phone', 20)->nullable();
            $table->timestamp('delivery_confirmed_at')->nullable();
            $table->text('delivery_notes')->nullable();
            $table->json('delivery_photos')->nullable()->comment('Photo evidence of delivery');
            
            // Performance metrics
            $table->integer('delivery_time_minutes')->nullable()->comment('Total delivery time from dispatch');
            $table->boolean('on_time_delivery')->default(false);
            $table->integer('delay_minutes')->default(0);
            $table->text('delay_reason')->nullable();
            
            // Special handling
            $table->json('special_instructions')->nullable();
            $table->boolean('requires_refrigeration')->default(false);
            $table->boolean('fragile_items')->default(false);
            $table->json('dietary_special_handling')->nullable()->comment('Special handling for dietary restrictions');
            
            // Route optimization
            $table->json('delivery_route')->nullable()->comment('Optimized route information');
            $table->decimal('estimated_distance_km', 8, 2)->nullable();
            $table->decimal('actual_distance_km', 8, 2)->nullable();
            $table->integer('estimated_duration_minutes')->nullable();
            $table->integer('actual_duration_minutes')->nullable();
            
            // Cost tracking
            $table->decimal('delivery_cost', 8, 2)->nullable();
            $table->decimal('fuel_cost', 8, 2)->nullable();
            $table->decimal('packaging_cost', 8, 2)->nullable();
            $table->decimal('total_cost', 8, 2)->nullable();
            
            // Feedback and issues
            $table->decimal('school_rating', 3, 2)->nullable();
            $table->text('school_feedback')->nullable();
            $table->json('reported_issues')->nullable()->comment('Any issues reported during delivery');
            $table->boolean('requires_followup')->default(false);
            $table->text('followup_notes')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['tenant_id', 'company_id', 'unit_id'], 'idx_delivery_batches_tenant');
            $table->index(['school_id', 'delivery_date'], 'idx_delivery_batches_school_date');
            $table->index(['delivery_date', 'break_time_slot'], 'idx_delivery_batches_date_slot');
            $table->index(['status'], 'idx_delivery_batches_status');
            $table->index(['delivery_person_id', 'delivery_date'], 'idx_delivery_batches_person_date');
            $table->index(['scheduled_delivery_time'], 'idx_delivery_batches_scheduled_time');
            $table->index(['on_time_delivery'], 'idx_delivery_batches_on_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_delivery_batches');
    }
};

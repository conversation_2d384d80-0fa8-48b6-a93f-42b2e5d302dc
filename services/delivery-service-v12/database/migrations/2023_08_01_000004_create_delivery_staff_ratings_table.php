<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_staff_ratings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_person_id');
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('customer_id');
            $table->integer('rating')->comment('Rating from 1 to 5');
            $table->text('comment')->nullable();
            $table->timestamps();
            
            $table->foreign('delivery_person_id')
                  ->references('id')
                  ->on('delivery_persons')
                  ->onDelete('cascade');
                  
            $table->foreign('order_id')
                  ->references('pk_order_no')
                  ->on('orders')
                  ->onDelete('cascade');
                  
            $table->foreign('customer_id')
                  ->references('pk_customer_code')
                  ->on('customers')
                  ->onDelete('cascade');
                  
            $table->unique(['order_id', 'customer_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_staff_ratings');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_assignment_batches', function (Blueprint $table) {
            $table->id();
            $table->string('batch_number')->unique();
            $table->enum('status', ['pending', 'processing', 'completed', 'failed']);
            $table->unsignedInteger('total_orders');
            $table->unsignedInteger('assigned_orders')->default(0);
            $table->unsignedInteger('failed_orders')->default(0);
            $table->json('criteria')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            $table->foreign('created_by')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');
        });
        
        // Add batch_id to delivery_assignments table
        Schema::table('delivery_assignments', function (Blueprint $table) {
            $table->unsignedBigInteger('batch_id')->nullable()->after('assignment_type');
            
            $table->foreign('batch_id')
                  ->references('id')
                  ->on('delivery_assignment_batches')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_assignments', function (Blueprint $table) {
            $table->dropForeign(['batch_id']);
            $table->dropColumn('batch_id');
        });
        
        Schema::dropIfExists('delivery_assignment_batches');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('delivery_locations', function (Blueprint $table) {
            if (!Schema::hasColumn('delivery_locations', 'latitude')) {
                $table->decimal('latitude', 10, 7)->nullable()->after('pin');
            }
            
            if (!Schema::hasColumn('delivery_locations', 'longitude')) {
                $table->decimal('longitude', 10, 7)->nullable()->after('latitude');
            }
            
            if (!Schema::hasColumn('delivery_locations', 'geocoded_address')) {
                $table->text('geocoded_address')->nullable()->after('longitude');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_locations', function (Blueprint $table) {
            $table->dropColumn(['latitude', 'longitude', 'geocoded_address']);
        });
    }
};

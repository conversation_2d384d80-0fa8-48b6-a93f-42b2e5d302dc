<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('delivery_staff_service_areas')) {
            Schema::create('delivery_staff_service_areas', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('delivery_person_id');
                $table->unsignedBigInteger('location_id');
                $table->boolean('is_primary')->default(false);
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->foreign('delivery_person_id')
                      ->references('id')
                      ->on('delivery_persons')
                      ->onDelete('cascade');

                $table->foreign('location_id')
                      ->references('id')
                      ->on('delivery_locations')
                      ->onDelete('cascade');

                $table->unique(['delivery_person_id', 'location_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_staff_service_areas');
    }
};

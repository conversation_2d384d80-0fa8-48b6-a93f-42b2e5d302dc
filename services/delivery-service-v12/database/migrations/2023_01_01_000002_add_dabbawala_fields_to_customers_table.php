<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            if (!Schema::hasColumn('customers', 'dabbawala_code_type')) {
                $table->string('dabbawala_code_type')->nullable()->after('alt_phone');
            }
            
            if (!Schema::hasColumn('customers', 'dabbawala_code')) {
                $table->string('dabbawala_code')->nullable()->after('dabbawala_code_type');
            }
            
            if (!Schema::hasColumn('customers', 'dabbawala_image')) {
                $table->string('dabbawala_image')->nullable()->after('dabbawala_code');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn(['dabbawala_code_type', 'dabbawala_code', 'dabbawala_image']);
        });
    }
};

<?php

namespace Database\Seeders;

use App\Models\DeliveryLocation;
use App\Models\DeliveryPerson;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DeliveryDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create delivery locations
        $locations = [
            [
                'location' => 'Andheri West',
                'city' => 'Mumbai',
                'sub_city_area' => 'Andheri',
                'pin' => '400058',
                'latitude' => 19.1136,
                'longitude' => 72.8697,
                'geocoded_address' => 'Andheri West, Mumbai, Maharashtra 400058',
                'delivery_charges' => 50.00,
                'delivery_time' => 30,
                'is_default' => false,
                'status' => true,
            ],
            [
                'location' => 'Bandra West',
                'city' => 'Mumbai',
                'sub_city_area' => 'Bandra',
                'pin' => '400050',
                'latitude' => 19.0596,
                'longitude' => 72.8295,
                'geocoded_address' => 'Bandra West, Mumbai, Maharashtra 400050',
                'delivery_charges' => 60.00,
                'delivery_time' => 25,
                'is_default' => true,
                'status' => true,
            ],
            [
                'location' => 'Powai',
                'city' => 'Mumbai',
                'sub_city_area' => 'Powai',
                'pin' => '400076',
                'latitude' => 19.1197,
                'longitude' => 72.9056,
                'geocoded_address' => 'Powai, Mumbai, Maharashtra 400076',
                'delivery_charges' => 45.00,
                'delivery_time' => 35,
                'is_default' => false,
                'status' => true,
            ],
        ];

        foreach ($locations as $location) {
            DeliveryLocation::firstOrCreate(
                ['location' => $location['location'], 'city' => $location['city']],
                $location
            );
        }

        // Create delivery persons
        $deliveryPersons = [
            [
                'name' => 'Rajesh Kumar',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'vehicle_type' => 'bike',
                'vehicle_number' => 'MH01AB1234',
                'license_number' => 'MH0120230001',
                'status' => true,
                'on_duty' => true,
                'current_latitude' => 19.1136,
                'current_longitude' => 72.8697,
            ],
            [
                'name' => 'Amit Sharma',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'vehicle_type' => 'scooter',
                'vehicle_number' => 'MH01CD5678',
                'license_number' => 'MH0120230002',
                'status' => true,
                'on_duty' => false,
                'current_latitude' => 19.0596,
                'current_longitude' => 72.8295,
            ],
        ];

        foreach ($deliveryPersons as $person) {
            if (!DeliveryPerson::where('phone', $person['phone'])->exists()) {
                DeliveryPerson::create($person);
            }
        }

        // Create sample orders
        $orders = [
            [
                'customer_id' => 1, // Assuming customer with ID 1 exists
                'status' => 'pending',
                'total_amount' => 450.00,
                'delivery_address' => 'A-101, Sunrise Apartments, Andheri West, Mumbai',
                'delivery_latitude' => 19.1136,
                'delivery_longitude' => 72.8697,
                'delivery_time' => now()->addHours(2),
            ],
            [
                'customer_id' => 1,
                'status' => 'confirmed',
                'total_amount' => 320.00,
                'delivery_address' => 'B-205, Ocean View, Bandra West, Mumbai',
                'delivery_latitude' => 19.0596,
                'delivery_longitude' => 72.8295,
                'delivery_time' => now()->addHours(1),
            ],
            [
                'customer_id' => 1,
                'status' => 'out_for_delivery',
                'total_amount' => 280.00,
                'delivery_address' => 'C-301, Green Valley, Powai, Mumbai',
                'delivery_latitude' => 19.1197,
                'delivery_longitude' => 72.9056,
                'delivery_time' => now()->addMinutes(30),
            ],
        ];

        foreach ($orders as $order) {
            if (!Order::where('delivery_address', $order['delivery_address'])->exists()) {
                Order::create($order);
            }
        }

        $this->command->info('Delivery data seeded successfully!');
    }
}

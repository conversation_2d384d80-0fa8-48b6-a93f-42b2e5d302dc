<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'pk_order_no' => $this->faker->unique()->numberBetween(1, 1000),
            'order_no' => 'ORD-' . $this->faker->unique()->numberBetween(10000, 99999),
            'customer_code' => $this->faker->numberBetween(1, 100),
            'customer_name' => $this->faker->name(),
            'customer_phone' => $this->faker->phoneNumber(),
            'ship_address' => $this->faker->address(),
            'order_date' => $this->faker->date(),
            'delivery_status' => $this->faker->randomElement(['Pending', 'Dispatched', 'Delivered', 'Failed']),
            'order_status' => $this->faker->randomElement(['New', 'Processing', 'Complete', 'Cancelled']),
            'delivery_person' => $this->faker->numberBetween(1, 10),
            'location_code' => $this->faker->numberBetween(1, 10),
            'amount' => $this->faker->randomFloat(2, 50, 500),
            'tax' => $this->faker->randomFloat(2, 5, 50),
            'delivery_charges' => $this->faker->randomFloat(2, 0, 20),
            'applied_discount' => $this->faker->randomFloat(2, 0, 50),
            'payment_mode' => $this->faker->randomElement(['Online', 'Cash', 'Card']),
            'amount_paid' => $this->faker->boolean(70),
            'fk_kitchen_code' => 'K' . $this->faker->numberBetween(1, 5),
            'order_menu' => $this->faker->randomElement(['lunch', 'dinner']),
            'delivery_type' => $this->faker->randomElement(['delivery', 'pickup']),
            'delivery_time' => $this->faker->time(),
            'delivery_end_time' => $this->faker->time(),
            'tp_delivery_order_id' => null,
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }
    
    /**
     * Indicate that the order has a third-party delivery ID.
     */
    public function withThirdPartyDeliveryId(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'tp_delivery_order_id' => 'MD-' . $this->faker->numberBetween(10000, 99999),
            ];
        });
    }
}

<?php

namespace Database\Factories;

use App\Models\DeliveryLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeliveryLocation>
 */
class DeliveryLocationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DeliveryLocation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'location' => $this->faker->city,
            'city' => $this->faker->city,
            'sub_city_area' => $this->faker->streetName,
            'pin' => $this->faker->postcode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'geocoded_address' => $this->faker->address,
            'delivery_charges' => $this->faker->randomFloat(2, 10, 50),
            'delivery_time' => $this->faker->numberBetween(15, 60),
            'is_default' => false,
            'status' => true,
        ];
    }

    /**
     * Indicate that the location is a default location.
     */
    public function default(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_default' => true,
            ];
        });
    }

    /**
     * Indicate that the location is inactive.
     */
    public function inactive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => false,
            ];
        });
    }

    /**
     * Indicate that the location is in Mumbai.
     */
    public function mumbai(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'city' => 'Mumbai',
                'latitude' => 19.0760,
                'longitude' => 72.8777,
            ];
        });
    }
}

<?php

namespace Database\Factories;

use App\Models\DeliveryRoute;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeliveryRoute>
 */
class DeliveryRouteFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DeliveryRoute::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_id' => $this->faker->numberBetween(1, 1000),
            'kitchen_id' => $this->faker->numberBetween(1, 10),
            'delivery_person_id' => $this->faker->optional()->numberBetween(1, 10),
            'distance_km' => $this->faker->randomFloat(2, 1, 20),
            'duration_seconds' => $this->faker->numberBetween(300, 3600),
            'route_geometry' => json_encode([
                'type' => 'LineString',
                'coordinates' => [
                    [$this->faker->longitude, $this->faker->latitude],
                    [$this->faker->longitude, $this->faker->latitude],
                    [$this->faker->longitude, $this->faker->latitude],
                ]
            ]),
            'route_steps' => json_encode([
                [
                    'name' => $this->faker->streetName,
                    'distance' => $this->faker->randomFloat(2, 100, 1000),
                    'duration' => $this->faker->numberBetween(60, 300),
                    'instruction' => 'Turn right onto ' . $this->faker->streetName,
                ],
                [
                    'name' => $this->faker->streetName,
                    'distance' => $this->faker->randomFloat(2, 100, 1000),
                    'duration' => $this->faker->numberBetween(60, 300),
                    'instruction' => 'Turn left onto ' . $this->faker->streetName,
                ],
            ]),
            'start_lat' => $this->faker->latitude,
            'start_lon' => $this->faker->longitude,
            'end_lat' => $this->faker->latitude,
            'end_lon' => $this->faker->longitude,
            'estimated_delivery_time' => $this->faker->dateTimeBetween('now', '+2 hours'),
            'actual_delivery_time' => $this->faker->optional()->dateTimeBetween('now', '+3 hours'),
            'traffic_condition' => $this->faker->randomElement(['light', 'moderate', 'heavy']),
            'delivery_zone' => $this->faker->numberBetween(1, 3),
            'delivery_fee' => $this->faker->randomFloat(2, 20, 100),
        ];
    }

    /**
     * Indicate that the route has been completed.
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'actual_delivery_time' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            ];
        });
    }

    /**
     * Indicate that the route is in progress.
     */
    public function inProgress(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'actual_delivery_time' => null,
                'delivery_person_id' => $this->faker->numberBetween(1, 10),
            ];
        });
    }
}

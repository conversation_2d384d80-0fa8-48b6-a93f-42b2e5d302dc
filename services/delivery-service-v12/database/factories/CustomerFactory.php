<?php

namespace Database\Factories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'pk_customer_code' => $this->faker->unique()->numberBetween(1, 1000),
            'customer_name' => $this->faker->name(),
            'company_name' => $this->faker->company(),
            'customer_Address' => $this->faker->address(),
            'phone' => $this->faker->phoneNumber(),
            'email_address' => $this->faker->unique()->safeEmail(),
            'location_code' => $this->faker->numberBetween(1, 10),
            'food_preference' => $this->faker->randomElement(['veg', 'non-veg']),
            'status' => $this->faker->boolean(80),
            'dabbawala_code_type' => null,
            'dabbawala_code' => null,
            'dabbawala_image' => null,
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }
    
    /**
     * Indicate that the customer has a dabbawala code.
     */
    public function withDabbaCode(): static
    {
        return $this->state(function (array $attributes) {
            $customerId = $attributes['pk_customer_code'] ?? $this->faker->unique()->numberBetween(1, 1000);
            $areaCode = $this->faker->randomElement(['MU', 'PU', 'DE', 'BA']);
            $random = str_pad($this->faker->numberBetween(0, 999), 3, '0', STR_PAD_LEFT);
            
            return [
                'dabbawala_code_type' => 'mumbai',
                'dabbawala_code' => 'MD-' . $areaCode . '-' . $customerId . '-' . $random,
            ];
        });
    }
}

{"info": {"name": "Delivery Service v2 API Collection", "description": "Complete API collection for Delivery Service v2 with all tested endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8106/api/v2/delivery", "type": "string"}], "item": [{"name": "Orders Management", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/orders", "host": ["{{base_url}}"], "path": ["orders"]}}}, {"name": "Get Orders by Delivery Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/orders?delivery_status=Dispatched", "host": ["{{base_url}}"], "path": ["orders"], "query": [{"key": "delivery_status", "value": "Dispatched"}]}}}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/orders/2", "host": ["{{base_url}}"], "path": ["orders", "2"]}}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"out_for_delivery\",\n  \"delivery_notes\": \"Order dispatched for delivery\"\n}"}, "url": {"raw": "{{base_url}}/orders/2/status", "host": ["{{base_url}}"], "path": ["orders", "2", "status"]}}}]}, {"name": "Delivery Staff Management", "item": [{"name": "Get All Delivery Staff", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/staff", "host": ["{{base_url}}"], "path": ["staff"]}}}, {"name": "Get Available Delivery Staff", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/staff?available=true", "host": ["{{base_url}}"], "path": ["staff"], "query": [{"key": "available", "value": "true"}]}}}, {"name": "Create Delivery Staff", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Staff Member\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"9876543299\",\n  \"password\": \"password123\",\n  \"vehicle_type\": \"bike\",\n  \"vehicle_number\": \"MH01TEST123\",\n  \"is_active\": true,\n  \"on_duty\": false\n}"}, "url": {"raw": "{{base_url}}/staff", "host": ["{{base_url}}"], "path": ["staff"]}}}, {"name": "Update Staff Duty Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"on_duty\": true\n}"}, "url": {"raw": "{{base_url}}/staff/4/duty-status", "host": ["{{base_url}}"], "path": ["staff", "4", "duty-status"]}}}, {"name": "Update Staff Location", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 19.1200,\n  \"longitude\": 72.8800\n}"}, "url": {"raw": "{{base_url}}/staff/4/location", "host": ["{{base_url}}"], "path": ["staff", "4", "location"]}}}]}, {"name": "Delivery Assignments", "item": [{"name": "Get All Assignments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/assignments", "host": ["{{base_url}}"], "path": ["assignments"]}}}, {"name": "Assign Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": 2,\n  \"delivery_person_id\": 4,\n  \"estimated_delivery_time\": \"2025-06-04T12:00:00Z\",\n  \"notes\": \"Handle with care\"\n}"}, "url": {"raw": "{{base_url}}/assignments/assign", "host": ["{{base_url}}"], "path": ["assignments", "assign"]}}}, {"name": "Create Batch Assignment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"orders\": [2, 3],\n  \"criteria\": {\n    \"location_id\": 1,\n    \"date\": \"2025-06-04\",\n    \"meal_type\": \"lunch\"\n  },\n  \"notes\": \"Batch assignment for lunch orders\"\n}"}, "url": {"raw": "{{base_url}}/assignments/batch", "host": ["{{base_url}}"], "path": ["assignments", "batch"]}}}, {"name": "Process Batch Assignment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/assignments/batches/1/process", "host": ["{{base_url}}"], "path": ["assignments", "batches", "1", "process"]}}}]}, {"name": "Delivery Zones", "item": [{"name": "Get All Zones", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/zones", "host": ["{{base_url}}"], "path": ["zones"]}}}, {"name": "Create Delivery Zone", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Andheri Zone\",\n  \"kitchen_id\": 1,\n  \"zone_number\": 1,\n  \"min_distance_km\": 0,\n  \"max_distance_km\": 5,\n  \"base_delivery_fee\": 50,\n  \"additional_fee\": 10,\n  \"is_active\": true,\n  \"description\": \"Delivery zone covering Andheri area\"\n}"}, "url": {"raw": "{{base_url}}/zones", "host": ["{{base_url}}"], "path": ["zones"]}}}]}, {"name": "Third Party Delivery", "item": [{"name": "Book Third Party Delivery", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": 2,\n  \"provider\": \"your<PERSON>y\",\n  \"pickup_address\": \"Kitchen Location, Andheri West, Mumbai\",\n  \"delivery_address\": \"A-101, Sunrise Apartments, Andheri West, Mumbai\",\n  \"pickup_time\": \"2025-06-04T11:00:00Z\",\n  \"delivery_time\": \"2025-06-04T12:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/third-party/book", "host": ["{{base_url}}"], "path": ["third-party", "book"]}}}]}, {"name": "Delivery Tracking", "item": [{"name": "Get Active Deliveries", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tracking/active-deliveries", "host": ["{{base_url}}"], "path": ["tracking", "active-deliveries"]}}}, {"name": "Get Delivery Tracking", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tracking/orders/2", "host": ["{{base_url}}"], "path": ["tracking", "orders", "2"]}}}, {"name": "Get Dashboard Data", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/tracking/dashboard", "host": ["{{base_url}}"], "path": ["tracking", "dashboard"]}}}, {"name": "Update Staff Location (Tracking)", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 19.1200,\n  \"longitude\": 72.8800\n}"}, "url": {"raw": "{{base_url}}/tracking/staff/1/location", "host": ["{{base_url}}"], "path": ["tracking", "staff", "1", "location"]}}}]}, {"name": "Map & Location Services", "item": [{"name": "Get Delivery Locations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/map/delivery-locations", "host": ["{{base_url}}"], "path": ["map", "delivery-locations"]}}}, {"name": "Get Customers on Map", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/map/customers", "host": ["{{base_url}}"], "path": ["map", "customers"]}}}, {"name": "Get Active Orders on Map", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/map/active-orders", "host": ["{{base_url}}"], "path": ["map", "active-orders"]}}}, {"name": "Get Delivery Route", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/map/delivery-route/2", "host": ["{{base_url}}"], "path": ["map", "delivery-route", "2"]}}}, {"name": "Geocode Address", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"Andheri West Mumbai\"\n}"}, "url": {"raw": "{{base_url}}/map/geocode", "host": ["{{base_url}}"], "path": ["map", "geocode"]}}}, {"name": "Update Customer Coordinates", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 19.1136,\n  \"longitude\": 72.8697\n}"}, "url": {"raw": "{{base_url}}/map/customer/1/coordinates", "host": ["{{base_url}}"], "path": ["map", "customer", "1", "coordinates"]}}}, {"name": "Update Location Coordinates", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 19.1200,\n  \"longitude\": 72.8800\n}"}, "url": {"raw": "{{base_url}}/map/location/1/coordinates", "host": ["{{base_url}}"], "path": ["map", "location", "1", "coordinates"]}}}]}, {"name": "School Delivery", "item": [{"name": "Get School Delivery Batches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/school/batches", "host": ["{{base_url}}"], "path": ["school", "batches"]}}}]}]}
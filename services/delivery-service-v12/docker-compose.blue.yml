version: '3'
services:
  # PHP Service
  app:
    image: registry.fooddialer.com/delivery-service:production
    container_name: delivery-service-blue-app
    restart: unless-stopped
    tty: true
    environment:
      SERVICE_NAME: app
      SERVICE_TAGS: production
      APP_ENV: production
      APP_DEBUG: 'false'
    working_dir: /var/www/html
    volumes:
      - ./storage:/var/www/html/storage
      - ./php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - app-network

  # Nginx Service
  webserver:
    image: nginx:alpine
    container_name: delivery-service-blue-webserver
    restart: unless-stopped
    tty: true
    ports:
      - "8081:80"
    volumes:
      - ./storage:/var/www/html/storage
      - ./nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - app-network

# Networks
networks:
  app-network:
    external: true

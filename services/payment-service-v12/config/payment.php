<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payment Gateway Configurations
    |--------------------------------------------------------------------------
    |
    | Here you may configure the payment gateways for your application.
    |
    */

    'default_gateway' => env('PAYMENT_DEFAULT_GATEWAY', 'payu'),

    'gateways' => [
        'payu' => [
            'enabled' => env('PAYMENT_PAYU_ENABLED', true),
            'merchant_key' => env('PAYU_MERCHANT_KEY'),
            'merchant_salt' => env('PAYU_MERCHANT_SALT'),
            'mode' => env('PAYU_MODE', 'test'),
            'success_url' => env('PAYMENT_PAYU_SUCCESS_URL', ''),
            'failure_url' => env('PAYMENT_PAYU_FAILURE_URL', ''),
            'cancel_url' => env('PAYMENT_PAYU_CANCEL_URL', ''),
            // Keep legacy config for backward compatibility
            'key' => env('PAYU_MERCHANT_KEY'),
            'salt' => env('PAYU_MERCHANT_SALT'),
            'test_url' => 'https://test.payu.in/_payment',
            'production_url' => 'https://secure.payu.in/_payment',
            'status_test_url' => 'https://test.payumoney.com/payment/op/getPaymentResponse',
            'status_production_url' => 'https://www.payumoney.com/payment/op/getPaymentResponse',
        ],

        'instamojo' => [
            'api_key' => env('INSTAMOJO_API_KEY'),
            'auth_token' => env('INSTAMOJO_AUTH_TOKEN'),
            'mode' => env('INSTAMOJO_MODE', 'test'),
            'test_url' => 'https://test.instamojo.com/api/1.1/',
            'production_url' => 'https://www.instamojo.com/api/1.1/',
        ],

        'paytm' => [
            'merchant_id' => env('PAYTM_MERCHANT_ID'),
            'merchant_key' => env('PAYTM_MERCHANT_KEY'),
            'website' => env('PAYTM_WEBSITE', 'DEFAULT'),
            'industry_type' => env('PAYTM_INDUSTRY_TYPE', 'Retail'),
            'channel' => env('PAYTM_CHANNEL', 'WEB'),
            'mode' => env('PAYTM_MODE', 'test'),
            'test_url' => 'https://securegw-stage.paytm.in/order/process',
            'production_url' => 'https://securegw.paytm.in/order/process',
        ],

        'payeezy' => [
            'id' => env('PAYEEZY_ID'),
            'key' => env('PAYEEZY_KEY'),
            'secret' => env('PAYEEZY_SECRET'),
            'hmac_key' => env('PAYEEZY_HMAC_KEY'),
            'hco_login' => env('PAYEEZY_HCO_LOGIN'),
            'hco_transaction_key' => env('PAYEEZY_HCO_TRANSACTION_KEY'),
            'mode' => env('PAYEEZY_MODE', 'test'),
        ],

        'mobikwik' => [
            'merchant_id' => env('MOBIKWIK_MERCHANT_ID'),
            'merchant_key' => env('MOBIKWIK_MERCHANT_KEY'),
            'mode' => env('MOBIKWIK_MODE', 'test'),
            'test_url' => 'https://test.mobikwik.com/mobikwik/pgway',
            'production_url' => 'https://www.mobikwik.com/mobikwik/pgway',
        ],

        'paypal' => [
            'client_id' => env('PAYPAL_CLIENT_ID'),
            'secret' => env('PAYPAL_SECRET'),
            'mode' => env('PAYPAL_MODE', 'test'),
            'currency' => env('PAYPAL_CURRENCY', 'USD'),
        ],

        'converge' => [
            'merchant_id' => env('CONVERGE_MERCHANT_ID'),
            'user_id' => env('CONVERGE_USER_ID'),
            'pin' => env('CONVERGE_PIN'),
            'mode' => env('CONVERGE_MODE', 'test'),
            'test_url' => 'https://api.demo.convergepay.com/VirtualMerchantDemo/processxml.do',
            'production_url' => 'https://api.convergepay.com/VirtualMerchant/processxml.do',
        ],

        'yesbank' => [
            'merchant_id' => env('YESBANK_MERCHANT_ID'),
            'secret_key' => env('YESBANK_SECRET_KEY'),
            'mode' => env('YESBANK_MODE', 'test'),
            'test_url' => 'https://uat.yesbank.in/pg/api',
            'production_url' => 'https://pg.yesbank.in/pg/api',
        ],

        'stripe' => [
            'publishable_key' => env('STRIPE_PUBLISHABLE_KEY'),
            'secret_key' => env('STRIPE_SECRET_KEY'),
            'mode' => env('STRIPE_MODE', 'test'),
            'currency' => env('STRIPE_CURRENCY', 'usd'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Charges
    |--------------------------------------------------------------------------
    |
    | Here you may configure the transaction charges for your application.
    |
    */

    'transaction_charges' => [
        'apply' => env('PAYMENT_APPLY_TRANSACTION_CHARGES', false),
        'percentage' => env('PAYMENT_TRANSACTION_CHARGES_PERCENTAGE', 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Security
    |--------------------------------------------------------------------------
    |
    | Here you may configure the security settings for webhooks.
    |
    */

    'webhook_secret' => env('PAYMENT_WEBHOOK_SECRET'),

    'webhook_whitelisted_ips' => array_filter(explode(',', env('PAYMENT_WEBHOOK_WHITELISTED_IPS', ''))),

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Here you may configure the security settings for your payment system.
    |
    */

    'security' => [
        'encryption' => [
            'enabled' => env('PAYMENT_ENCRYPTION_ENABLED', true),
            'key' => env('PAYMENT_ENCRYPTION_KEY'),
        ],
        'rate_limiting' => [
            'enabled' => env('PAYMENT_RATE_LIMITING_ENABLED', true),
            'max_attempts' => env('PAYMENT_RATE_LIMITING_MAX_ATTEMPTS', 10),
            'decay_minutes' => env('PAYMENT_RATE_LIMITING_DECAY_MINUTES', 1),
        ],
        'ip_whitelist' => [
            'enabled' => env('PAYMENT_IP_WHITELIST_ENABLED', false),
            'ips' => explode(',', env('PAYMENT_IP_WHITELIST', '')),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    |
    | Here you may configure the logging settings for your payment system.
    |
    */

    'logging' => [
        'enabled' => env('PAYMENT_LOGGING_ENABLED', true),
        'level' => env('PAYMENT_LOGGING_LEVEL', 'info'), // debug, info, notice, warning, error, critical, alert, emergency
        'channel' => env('PAYMENT_LOGGING_CHANNEL', 'payment'),
        'sensitive_fields' => [
            'card_number',
            'cvv',
            'password',
            'pin',
            'secret',
            'token',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Routes
    |--------------------------------------------------------------------------
    |
    | This section contains the route configuration for payment callbacks.
    |
    */

    'routes' => [
        'prefix' => 'api/v2/payments',
        'middleware' => ['api'],
        'webhooks' => [
            'payu' => 'webhooks/payu',
            'stripe' => 'webhooks/stripe',
            'paypal' => 'webhooks/paypal',
            'instamojo' => 'webhooks/instamojo',
            'paytm' => 'webhooks/paytm',
            'payeezy' => 'webhooks/payeezy',
            'mobikwik' => 'webhooks/mobikwik',
            'converge' => 'webhooks/converge',
            'yesbank' => 'webhooks/yesbank',
        ],
        'success' => 'success',
        'failure' => 'failure',
        'cancel' => 'cancel',
    ],
];

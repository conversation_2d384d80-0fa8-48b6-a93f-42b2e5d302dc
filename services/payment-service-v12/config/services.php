<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | OneFoodDialer 2025 Microservices
    |--------------------------------------------------------------------------
    |
    | Configuration for inter-service communication
    |
    */

    'customer' => [
        'url' => env('CUSTOMER_SERVICE_URL', 'http://customer-service-v12:8000'),
        'timeout' => env('CUSTOMER_SERVICE_TIMEOUT', 30),
        'retry_attempts' => env('CUSTOMER_SERVICE_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('CUSTOMER_SERVICE_RETRY_DELAY', 1000),
    ],

    'invoice' => [
        'url' => env('INVOICE_SERVICE_URL', 'http://invoice-service-v12:8000'),
        'timeout' => env('INVOICE_SERVICE_TIMEOUT', 30),
        'retry_attempts' => env('INVOICE_SERVICE_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('INVOICE_SERVICE_RETRY_DELAY', 1000),
    ],

    'order' => [
        'url' => env('ORDER_SERVICE_URL', 'http://order-service-v12:8000'),
        'timeout' => env('ORDER_SERVICE_TIMEOUT', 30),
        'retry_attempts' => env('ORDER_SERVICE_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('ORDER_SERVICE_RETRY_DELAY', 1000),
    ],

];

<?php

declare(strict_types=1);

namespace App\RectorRules;

use <PERSON>p<PERSON><PERSON><PERSON>\Node;
use Php<PERSON><PERSON><PERSON>\Node\Expr\MethodCall;
use Php<PERSON>arser\Node\Expr\StaticCall;
use Php<PERSON>arser\Node\Expr\New_;
use Php<PERSON>arser\Node\Name;
use Rector\Core\Rector\AbstractRector;
use Symplify\RuleDocGenerator\ValueObject\CodeSample\CodeSample;
use Symplify\RuleDocGenerator\ValueObject\RuleDefinition;

/**
 * Custom Rector rule to identify and migrate legacy Zend Framework patterns
 * to modern Laravel 12 patterns for OneFoodDialer 2025
 */
final class ZendToLaravelRector extends AbstractRector
{
    public function getRuleDefinition(): RuleDefinition
    {
        return new RuleDefinition(
            'Convert legacy Zend Framework patterns to Laravel 12 patterns',
            [
                new CodeSample(
                    '$this->service_locator->get("Payment\Model\Payu")',
                    'app(PayuGateway::class)'
                ),
                new CodeSample(
                    'new QSql($this->service_locator)',
                    'DB::table()'
                ),
                new CodeSample(
                    '$this->getInvoiceTable()->getInvoiceCust($key)',
                    'Invoice::where("customer_id", $key)->latest()->first()'
                ),
            ]
        );
    }

    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes(): array
    {
        return [MethodCall::class, StaticCall::class, New_::class];
    }

    /**
     * @param MethodCall|StaticCall|New_ $node
     */
    public function refactor(Node $node): ?Node
    {
        // Convert service locator patterns
        if ($node instanceof MethodCall && $this->isName($node->name, 'get')) {
            return $this->refactorServiceLocatorGet($node);
        }

        // Convert QSql instantiation
        if ($node instanceof New_ && $node->class instanceof Name) {
            return $this->refactorQSqlInstantiation($node);
        }

        // Convert legacy table method calls
        if ($node instanceof MethodCall) {
            return $this->refactorLegacyTableMethods($node);
        }

        return null;
    }

    private function refactorServiceLocatorGet(MethodCall $node): ?Node
    {
        if (!$node->args[0] ?? null) {
            return null;
        }

        $serviceArg = $node->args[0]->value;
        
        // Convert Payment gateway service locator calls
        if ($this->valueResolver->isValue($serviceArg, 'Payment\Model\Payu')) {
            return $this->nodeFactory->createFuncCall('app', [PayuGateway::class]);
        }
        
        if ($this->valueResolver->isValue($serviceArg, 'Payment\Model\Stripe')) {
            return $this->nodeFactory->createFuncCall('app', [StripeGateway::class]);
        }
        
        // Add more gateway mappings as needed
        
        return null;
    }

    private function refactorQSqlInstantiation(New_ $node): ?Node
    {
        if ($this->isName($node->class, 'QSql')) {
            // Convert QSql to Laravel Query Builder
            return $this->nodeFactory->createStaticCall('DB', 'table', ['']);
        }

        return null;
    }

    private function refactorLegacyTableMethods(MethodCall $node): ?Node
    {
        // Convert getInvoiceTable()->getInvoiceCust() pattern
        if ($this->isName($node->name, 'getInvoiceCust')) {
            $customerIdArg = $node->args[0] ?? null;
            if ($customerIdArg) {
                return $this->nodeFactory->createStaticCall(
                    'Invoice',
                    'where',
                    ['customer_id', $customerIdArg->value]
                );
            }
        }

        // Convert generateInvoicePDF pattern
        if ($this->isName($node->name, 'generateInvoicePDF')) {
            // This would need to be converted to a modern PDF service
            return $this->nodeFactory->createMethodCall(
                $this->nodeFactory->createFuncCall('app', ['InvoiceService']),
                'generatePDF',
                $node->args
            );
        }

        return null;
    }
}

# Payment Gateways

This document describes the payment gateway implementations in the Payment Service.

## Overview

The Payment Service supports multiple payment gateways through a unified interface. Each gateway implements the `PaymentGatewayInterface` and extends the `AbstractPaymentGateway` class.

## Supported Gateways

The Payment Service currently supports the following payment gateways:

- **PayU**: A popular payment gateway in India
- **Stripe**: A global payment gateway
- **PayPal**: A widely used payment gateway

Additional gateways that can be implemented:

- Instamojo
- Paytm
- Payeezy
- Mobikwik
- Converge
- Yesbank

## Implementation

### PaymentGatewayInterface

The `PaymentGatewayInterface` defines the contract for all payment gateways:

```php
interface PaymentGatewayInterface
{
    /**
     * Get the name of the gateway.
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Check if the gateway is enabled.
     *
     * @return bool
     */
    public function isEnabled(): bool;

    /**
     * Check if the gateway is in test mode.
     *
     * @return bool
     */
    public function isTestMode(): bool;

    /**
     * Initialize the payment gateway.
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void;

    /**
     * Process a payment.
     *
     * @param array $paymentData
     * @return array
     */
    public function processPayment(array $paymentData): array;

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function verifyPayment(string $transactionId, array $additionalData = []): array;

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @param array $additionalData
     * @return array
     */
    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array;

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function cancelPayment(string $transactionId, array $additionalData = []): array;

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array;

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentDetails(string $transactionId): array;

    /**
     * Generate payment form.
     *
     * @param array $paymentData
     * @return array
     */
    public function generatePaymentForm(array $paymentData): array;

    /**
     * Handle payment webhook.
     *
     * @param array $data
     * @return array
     */
    public function handleWebhook(array $data): array;
}
```

### AbstractPaymentGateway

The `AbstractPaymentGateway` class provides common functionality for all payment gateways:

```php
abstract class AbstractPaymentGateway implements PaymentGatewayInterface
{
    /**
     * @var string
     */
    protected $name;

    /**
     * @var bool
     */
    protected $enabled = false;

    /**
     * @var bool
     */
    protected $testMode = true;

    /**
     * @var array
     */
    protected $config = [];

    /**
     * @var array
     */
    protected $supportedCurrencies = [];

    /**
     * @var array
     */
    protected $supportedMethods = [];

    /**
     * @var Client
     */
    protected $httpClient;

    // ... implementation of common methods
}
```

## Gateway Implementations

### PayU Gateway

The `PayuGateway` class implements the PayU payment gateway:

```php
class PayuGateway extends AbstractPaymentGateway
{
    /**
     * @var string
     */
    protected $name = 'payu';

    /**
     * @var string
     */
    protected $merchantKey;

    /**
     * @var string
     */
    protected $merchantSalt;

    /**
     * @var string
     */
    protected $authHeader;

    // ... implementation of PayU-specific methods
}
```

### Stripe Gateway

The `StripeGateway` class implements the Stripe payment gateway:

```php
class StripeGateway extends AbstractPaymentGateway
{
    /**
     * @var string
     */
    protected $name = 'stripe';

    /**
     * @var string
     */
    protected $apiKey;

    /**
     * @var string
     */
    protected $secretKey;

    /**
     * @var string
     */
    protected $webhookSecret;

    // ... implementation of Stripe-specific methods
}
```

### PayPal Gateway

The `PaypalGateway` class implements the PayPal payment gateway:

```php
class PaypalGateway extends AbstractPaymentGateway
{
    /**
     * @var string
     */
    protected $name = 'paypal';

    /**
     * @var string
     */
    protected $clientId;

    /**
     * @var string
     */
    protected $clientSecret;

    /**
     * @var string
     */
    protected $baseUrl;

    /**
     * @var string
     */
    protected $accessToken;

    // ... implementation of PayPal-specific methods
}
```

## Configuration

Payment gateways are configured in the `config/payment.php` file:

```php
return [
    'default_gateway' => env('PAYMENT_DEFAULT_GATEWAY', 'payu'),

    'gateways' => [
        'payu' => [
            'enabled' => env('PAYMENT_PAYU_ENABLED', true),
            'mode' => env('PAYMENT_PAYU_MODE', 'test'),
            'merchant_key' => env('PAYMENT_PAYU_MERCHANT_KEY', ''),
            'merchant_salt' => env('PAYMENT_PAYU_MERCHANT_SALT', ''),
            'auth_header' => env('PAYMENT_PAYU_AUTH_HEADER', ''),
            'success_url' => env('PAYMENT_PAYU_SUCCESS_URL', ''),
            'failure_url' => env('PAYMENT_PAYU_FAILURE_URL', ''),
            'cancel_url' => env('PAYMENT_PAYU_CANCEL_URL', ''),
        ],

        'stripe' => [
            'enabled' => env('PAYMENT_STRIPE_ENABLED', false),
            'mode' => env('PAYMENT_STRIPE_MODE', 'test'),
            'api_key' => env('PAYMENT_STRIPE_API_KEY', ''),
            'secret_key' => env('PAYMENT_STRIPE_SECRET_KEY', ''),
            'webhook_secret' => env('PAYMENT_STRIPE_WEBHOOK_SECRET', ''),
            'success_url' => env('PAYMENT_STRIPE_SUCCESS_URL', ''),
            'cancel_url' => env('PAYMENT_STRIPE_CANCEL_URL', ''),
        ],

        'paypal' => [
            'enabled' => env('PAYMENT_PAYPAL_ENABLED', false),
            'mode' => env('PAYMENT_PAYPAL_MODE', 'test'),
            'client_id' => env('PAYMENT_PAYPAL_CLIENT_ID', ''),
            'client_secret' => env('PAYMENT_PAYPAL_CLIENT_SECRET', ''),
            'success_url' => env('PAYMENT_PAYPAL_SUCCESS_URL', ''),
            'cancel_url' => env('PAYMENT_PAYPAL_CANCEL_URL', ''),
        ],

        // ... other gateways
    ],
];
```

## Registration

Payment gateways are registered in the `PaymentServiceProvider`:

```php
protected function registerPaymentGateways(PaymentService $paymentService, array $config): void
{
    // Register PayU gateway
    if (isset($config['gateways']['payu'])) {
        $payuGateway = new PayuGateway();
        $payuGateway->initialize($config['gateways']['payu']);
        $paymentService->registerGateway('payu', $payuGateway);
    }
    
    // Register Stripe gateway
    if (isset($config['gateways']['stripe'])) {
        $stripeGateway = new StripeGateway();
        $stripeGateway->initialize($config['gateways']['stripe']);
        $paymentService->registerGateway('stripe', $stripeGateway);
    }
    
    // Register PayPal gateway
    if (isset($config['gateways']['paypal'])) {
        $paypalGateway = new PaypalGateway();
        $paypalGateway->initialize($config['gateways']['paypal']);
        $paymentService->registerGateway('paypal', $paypalGateway);
    }
    
    // ... other gateways
}
```

## Usage

Payment gateways are used through the `PaymentService`:

```php
$result = $paymentService->processPayment([
    'order_id' => 123,
    'amount' => 100.00,
    'currency' => 'INR',
    'customer_id' => 456,
    'customer_name' => 'John Doe',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '1234567890',
    'gateway' => 'payu',
]);
```

## Testing

Payment gateways are tested using PHPUnit. The tests cover the following scenarios:

- Initializing the gateway
- Processing a payment
- Verifying a payment
- Refunding a payment
- Cancelling a payment
- Getting payment status
- Getting payment details
- Generating a payment form
- Handling a webhook

## References

- [PayU Documentation](https://developer.payu.com/en/restapi.html)
- [Stripe Documentation](https://stripe.com/docs/api)
- [PayPal Documentation](https://developer.paypal.com/docs/api/overview/)

<?php

namespace App\Repositories\Payment;

use App\Models\Payment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class PaymentRepository implements PaymentRepositoryInterface
{
    /**
     * @var Payment
     */
    protected $model;

    /**
     * PaymentRepository constructor.
     *
     * @param Payment $payment
     */
    public function __construct(Payment $payment)
    {
        $this->model = $payment;
    }

    /**
     * Get all payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function all(array $filters = []): Collection
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get paginated payments.
     *
     * @param int $perPage
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->paginate($perPage);
    }

    /**
     * Find payment by ID.
     *
     * @param int $id
     * @return Payment|null
     */
    public function findById(int $id): ?Payment
    {
        return $this->model->find($id);
    }

    /**
     * Find payment by transaction ID.
     *
     * @param string $transactionId
     * @return Payment|null
     */
    public function findByTransactionId(string $transactionId): ?Payment
    {
        return $this->model->where('transaction_id', $transactionId)->first();
    }

    /**
     * Find payments by order ID.
     *
     * @param int $orderId
     * @return Collection
     */
    public function findByOrderId(int $orderId): Collection
    {
        return $this->model->where('order_id', $orderId)->get();
    }

    /**
     * Find payments by customer ID.
     *
     * @param int $customerId
     * @return Collection
     */
    public function findByCustomerId(int $customerId): Collection
    {
        return $this->model->where('customer_id', $customerId)->get();
    }

    /**
     * Create a new payment.
     *
     * @param array $data
     * @return Payment
     */
    public function create(array $data): Payment
    {
        return $this->model->create($data);
    }

    /**
     * Update a payment.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $payment = $this->findById($id);
        
        if (!$payment) {
            return false;
        }
        
        return $payment->update($data);
    }

    /**
     * Delete a payment.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $payment = $this->findById($id);
        
        if (!$payment) {
            return false;
        }
        
        return $payment->delete();
    }

    /**
     * Get payments by status.
     *
     * @param string $status
     * @param array $filters
     * @return Collection
     */
    public function getByStatus(string $status, array $filters = []): Collection
    {
        $query = $this->model->where('status', $status);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get payments by gateway.
     *
     * @param string $gateway
     * @param array $filters
     * @return Collection
     */
    public function getByGateway(string $gateway, array $filters = []): Collection
    {
        $query = $this->model->where('gateway', $gateway);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get payments by date range.
     *
     * @param string $startDate
     * @param string $endDate
     * @param array $filters
     * @return Collection
     */
    public function getByDateRange(string $startDate, string $endDate, array $filters = []): Collection
    {
        $query = $this->model->whereBetween('created_at', [$startDate, $endDate]);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get successful payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getSuccessful(array $filters = []): Collection
    {
        return $this->getByStatus('success', $filters);
    }

    /**
     * Get failed payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getFailed(array $filters = []): Collection
    {
        return $this->getByStatus('failed', $filters);
    }

    /**
     * Get pending payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getPending(array $filters = []): Collection
    {
        return $this->getByStatus('pending', $filters);
    }

    /**
     * Get refunded payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getRefunded(array $filters = []): Collection
    {
        return $this->getByStatus('refunded', $filters);
    }

    /**
     * Apply filters to the query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['order_id'])) {
            $query->where('order_id', $filters['order_id']);
        }
        
        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }
        
        if (isset($filters['transaction_id'])) {
            $query->where('transaction_id', $filters['transaction_id']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['gateway'])) {
            $query->where('gateway', $filters['gateway']);
        }
        
        if (isset($filters['amount_min'])) {
            $query->where('amount', '>=', $filters['amount_min']);
        }
        
        if (isset($filters['amount_max'])) {
            $query->where('amount', '<=', $filters['amount_max']);
        }
        
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        return $query;
    }
}

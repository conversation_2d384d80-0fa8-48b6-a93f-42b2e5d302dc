<?php

namespace App\Repositories\Payment;

use App\Models\Payment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface PaymentRepositoryInterface
{
    /**
     * Get all payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function all(array $filters = []): Collection;

    /**
     * Get paginated payments.
     *
     * @param int $perPage
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Find payment by ID.
     *
     * @param int $id
     * @return Payment|null
     */
    public function findById(int $id): ?Payment;

    /**
     * Find payment by transaction ID.
     *
     * @param string $transactionId
     * @return Payment|null
     */
    public function findByTransactionId(string $transactionId): ?Payment;

    /**
     * Find payments by order ID.
     *
     * @param int $orderId
     * @return Collection
     */
    public function findByOrderId(int $orderId): Collection;

    /**
     * Find payments by customer ID.
     *
     * @param int $customerId
     * @return Collection
     */
    public function findByCustomerId(int $customerId): Collection;

    /**
     * Create a new payment.
     *
     * @param array $data
     * @return Payment
     */
    public function create(array $data): Payment;

    /**
     * Update a payment.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a payment.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Get payments by status.
     *
     * @param string $status
     * @param array $filters
     * @return Collection
     */
    public function getByStatus(string $status, array $filters = []): Collection;

    /**
     * Get payments by gateway.
     *
     * @param string $gateway
     * @param array $filters
     * @return Collection
     */
    public function getByGateway(string $gateway, array $filters = []): Collection;

    /**
     * Get payments by date range.
     *
     * @param string $startDate
     * @param string $endDate
     * @param array $filters
     * @return Collection
     */
    public function getByDateRange(string $startDate, string $endDate, array $filters = []): Collection;

    /**
     * Get successful payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getSuccessful(array $filters = []): Collection;

    /**
     * Get failed payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getFailed(array $filters = []): Collection;

    /**
     * Get pending payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getPending(array $filters = []): Collection;

    /**
     * Get refunded payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getRefunded(array $filters = []): Collection;
}

<?php

namespace App\Providers;

use App\Events\Payment\PaymentCompleted;
use App\Events\Payment\PaymentFailed;
use App\Events\Payment\PaymentInitiated;
use App\Events\Payment\PaymentRefunded;
use App\Events\Payment\WalletPaymentProcessed;
use App\Events\Payment\WalletPaymentFailed;
use App\Listeners\UpdateInvoiceOnWalletPayment;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        PaymentInitiated::class => [
            // Add listeners here
        ],
        PaymentCompleted::class => [
            // Add listeners here
        ],
        PaymentFailed::class => [
            // Add listeners here
        ],
        PaymentRefunded::class => [
            // Add listeners here
        ],
        WalletPaymentProcessed::class => [
            UpdateInvoiceOnWalletPayment::class,
        ],
        WalletPaymentFailed::class => [
            // Add listeners here for wallet payment failures
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}

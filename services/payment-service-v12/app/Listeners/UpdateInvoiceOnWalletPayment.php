<?php

namespace App\Listeners;

use App\Events\Payment\WalletPaymentProcessed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * UpdateInvoiceOnWalletPayment Listener
 *
 * Handles wallet payment processed events and updates related invoices
 */
class UpdateInvoiceOnWalletPayment implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(WalletPaymentProcessed $event): void
    {
        try {
            $transaction = $event->transaction;
            $walletData = $event->walletData;

            // If this payment is for an order, try to mark the related invoice as paid
            if ($transaction->order_id) {
                $this->updateInvoiceForOrder($transaction, $walletData);
            }

            // Log the successful processing
            Log::info('Wallet payment processed successfully', [
                'transaction_id' => $transaction->transaction_id,
                'customer_id' => $transaction->customer_id,
                'wallet_amount' => $transaction->wallet_amount,
                'order_id' => $transaction->order_id,
                'wallet_transaction_id' => $walletData['data']['transaction_id'] ?? null,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle wallet payment processed event', [
                'transaction_id' => $event->transaction->transaction_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Update invoice status for the order.
     *
     * @param \App\Models\PaymentTransaction $transaction
     * @param array $walletData
     * @return void
     */
    protected function updateInvoiceForOrder($transaction, array $walletData): void
    {
        try {
            // Call invoice service to mark invoice as paid
            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->withHeaders([
                    'X-Service' => 'payment-service-v12',
                    'X-Request-ID' => uniqid('invoice_update_'),
                    'X-Correlation-ID' => $transaction->transaction_id,
                ])
                ->post(config('services.invoice.url') . '/api/v2/invoices/mark-paid-by-order', [
                    'order_id' => $transaction->order_id,
                    'payment_method' => 'wallet',
                    'payment_reference' => $transaction->transaction_id,
                    'wallet_transaction_id' => $walletData['data']['transaction_id'] ?? null,
                    'amount_paid' => $transaction->wallet_amount,
                    'metadata' => [
                        'payment_transaction_id' => $transaction->transaction_id,
                        'wallet_balance_after' => $walletData['data']['balance_after'] ?? null,
                        'processed_at' => now()->toISOString(),
                    ]
                ]);

            if ($response->successful()) {
                Log::info('Invoice updated successfully for wallet payment', [
                    'transaction_id' => $transaction->transaction_id,
                    'order_id' => $transaction->order_id,
                    'invoice_response' => $response->json()
                ]);
            } else {
                Log::warning('Failed to update invoice for wallet payment', [
                    'transaction_id' => $transaction->transaction_id,
                    'order_id' => $transaction->order_id,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception while updating invoice for wallet payment', [
                'transaction_id' => $transaction->transaction_id,
                'order_id' => $transaction->order_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(WalletPaymentProcessed $event, \Throwable $exception): void
    {
        Log::error('Wallet payment processed listener failed', [
            'transaction_id' => $event->transaction->transaction_id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}

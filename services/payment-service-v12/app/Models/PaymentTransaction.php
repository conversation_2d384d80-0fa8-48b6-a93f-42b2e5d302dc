<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentTransaction extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'transaction_id';

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transaction_id',
        'customer_id',
        'customer_email',
        'customer_phone',
        'customer_name',
        'amount',
        'transaction_charges',
        'wallet_amount',
        'wallet_transaction_id',
        'wallet_balance_after',
        'order_id',
        'status',
        'gateway',
        'gateway_transaction_id',
        'description',
        'transaction_by',
        'referer',
        'success_url',
        'failure_url',
        'context',
        'recurring',
        'discount',
        'metadata'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'float',
        'transaction_charges' => 'float',
        'wallet_amount' => 'float',
        'wallet_balance_after' => 'float',
        'discount' => 'float',
        'recurring' => 'boolean',
        'metadata' => 'array'
    ];

    /**
     * The attributes that should be encrypted.
     *
     * @var array<int, string>
     */
    protected $encryptable = [
        'customer_email',
        'customer_phone',
        'metadata'
    ];

    /**
     * Set a given attribute on the model.
     *
     * @param string $key
     * @param mixed $value
     * @return mixed
     */
    public function setAttribute($key, $value)
    {
        if (in_array($key, $this->encryptable) && !empty($value)) {
            $value = encrypt($value);
        }

        return parent::setAttribute($key, $value);
    }

    /**
     * Get an attribute from the model.
     *
     * @param string $key
     * @return mixed
     */
    public function getAttribute($key)
    {
        $value = parent::getAttribute($key);

        if (in_array($key, $this->encryptable) && !empty($value)) {
            try {
                $value = decrypt($value);
            } catch (\Exception $e) {
                // Value was not encrypted
            }
        }

        return $value;
    }
}

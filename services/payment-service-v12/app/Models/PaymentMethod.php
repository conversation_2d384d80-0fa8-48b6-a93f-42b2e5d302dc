<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_id',
        'gateway',
        'token',
        'type',
        'last_four',
        'expiry_month',
        'expiry_year',
        'card_holder_name',
        'card_brand',
        'is_default',
        'is_active',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * The attributes that should be encrypted.
     *
     * @var array<int, string>
     */
    protected $encryptable = [
        'token',
        'last_four',
        'expiry_month',
        'expiry_year',
        'card_holder_name',
    ];

    /**
     * Set a given attribute on the model.
     *
     * @param string $key
     * @param mixed $value
     * @return mixed
     */
    public function setAttribute($key, $value)
    {
        if (in_array($key, $this->encryptable) && !empty($value)) {
            $value = encrypt($value);
        }

        return parent::setAttribute($key, $value);
    }

    /**
     * Get an attribute from the model.
     *
     * @param string $key
     * @return mixed
     */
    public function getAttribute($key)
    {
        $value = parent::getAttribute($key);

        if (in_array($key, $this->encryptable) && !empty($value)) {
            try {
                $value = decrypt($value);
            } catch (\Exception $e) {
                // Value was not encrypted
            }
        }

        return $value;
    }

    /**
     * Scope a query to only include payment methods for a specific customer.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $customerId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope a query to only include active payment methods.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include default payment methods.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope a query to only include payment methods for a specific gateway.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $gateway
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForGateway($query, $gateway)
    {
        return $query->where('gateway', $gateway);
    }

    /**
     * Get the masked card number.
     *
     * @return string
     */
    public function getMaskedCardNumberAttribute()
    {
        if ($this->last_four) {
            return '************' . $this->last_four;
        }

        return '';
    }

    /**
     * Get the expiry date in MM/YY format.
     *
     * @return string
     */
    public function getExpiryDateAttribute()
    {
        if ($this->expiry_month && $this->expiry_year) {
            return $this->expiry_month . '/' . substr($this->expiry_year, -2);
        }

        return '';
    }
}

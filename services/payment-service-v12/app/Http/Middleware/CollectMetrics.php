<?php

namespace App\Http\Middleware;

use App\Services\MetricsService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * CollectMetrics Middleware
 *
 * Automatically collects API metrics for Prometheus monitoring
 */
class CollectMetrics
{
    protected MetricsService $metricsService;

    public function __construct(MetricsService $metricsService)
    {
        $this->metricsService = $metricsService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);

        $response = $next($request);

        $duration = microtime(true) - $startTime;
        $endpoint = $this->normalizeEndpoint($request->path());
        $method = $request->method();
        $statusCode = $response->getStatusCode();

        // Record API metrics
        $this->metricsService->recordApiResponseTime(
            $endpoint,
            $method,
            $duration,
            $statusCode
        );

        // Record errors if status code indicates failure
        if ($statusCode >= 400) {
            $errorType = $this->getErrorType($statusCode);
            $this->metricsService->recordError(
                'payment-service',
                $endpoint,
                $errorType
            );
        }

        // Add metrics headers for debugging
        $response->headers->set('X-Response-Time', round($duration * 1000, 2) . 'ms');
        $response->headers->set('X-Service', 'payment-service-v12');

        return $response;
    }

    /**
     * Normalize endpoint path for metrics.
     *
     * @param string $path
     * @return string
     */
    protected function normalizeEndpoint(string $path): string
    {
        // Replace dynamic segments with placeholders
        $path = preg_replace('/\/\d+/', '/{id}', $path);
        $path = preg_replace('/\/[a-f0-9-]{36}/', '/{uuid}', $path);
        $path = preg_replace('/\/TXN[A-Z0-9]+/', '/{transaction_id}', $path);

        return $path ?: '/';
    }

    /**
     * Get error type from status code.
     *
     * @param int $statusCode
     * @return string
     */
    protected function getErrorType(int $statusCode): string
    {
        return match (true) {
            $statusCode >= 500 => 'server_error',
            $statusCode >= 400 && $statusCode < 500 => 'client_error',
            default => 'unknown_error'
        };
    }
}

<?php

namespace App\Http\Controllers;

use App\Services\MetricsService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

/**
 * MetricsController for OneFoodDialer 2025
 *
 * Provides Prometheus metrics endpoint and health checks
 */
class MetricsController extends Controller
{
    protected MetricsService $metricsService;

    public function __construct(MetricsService $metricsService)
    {
        $this->metricsService = $metricsService;
    }

    /**
     * Get Prometheus metrics.
     *
     * @return Response
     */
    public function metrics(): Response
    {
        // Collect current health metrics
        $this->collectHealthMetrics();

        $metrics = $this->metricsService->getPrometheusMetrics();

        return response($metrics, 200, [
            'Content-Type' => 'text/plain; version=0.0.4; charset=utf-8'
        ]);
    }

    /**
     * Health check endpoint.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function health(): \Illuminate\Http\JsonResponse
    {
        $health = [
            'service' => 'payment-service-v12',
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'checks' => []
        ];

        // Database health check
        try {
            DB::connection()->getPdo();
            $health['checks']['database'] = [
                'status' => 'healthy',
                'response_time' => $this->measureDatabaseResponseTime()
            ];
            $this->metricsService->recordDatabaseHealth('default', true);
        } catch (\Exception $e) {
            $health['checks']['database'] = [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
            $health['status'] = 'unhealthy';
            $this->metricsService->recordDatabaseHealth('default', false);
        }

        // External services health check
        $externalServices = $this->checkExternalServices();
        $health['checks']['external_services'] = $externalServices;

        // Check if any external service is unhealthy
        foreach ($externalServices as $service => $status) {
            if ($status['status'] === 'unhealthy') {
                $health['status'] = 'unhealthy';
                break;
            }
        }

        // Overall service health
        $this->metricsService->recordServiceHealth('payment-service', $health['status'] === 'healthy');

        $statusCode = $health['status'] === 'healthy' ? 200 : 503;

        return response()->json($health, $statusCode);
    }

    /**
     * Readiness check endpoint.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function ready(): \Illuminate\Http\JsonResponse
    {
        $ready = [
            'service' => 'payment-service-v12',
            'ready' => true,
            'timestamp' => now()->toISOString(),
            'checks' => []
        ];

        // Check if service can handle requests
        try {
            // Test database connection
            DB::connection()->getPdo();
            $ready['checks']['database'] = 'ready';

            // Test cache connection
            cache()->put('readiness_test', 'ok', 10);
            $ready['checks']['cache'] = 'ready';

        } catch (\Exception $e) {
            $ready['ready'] = false;
            $ready['checks']['error'] = $e->getMessage();
        }

        $statusCode = $ready['ready'] ? 200 : 503;

        return response()->json($ready, $statusCode);
    }

    /**
     * Liveness check endpoint.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function live(): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'service' => 'payment-service-v12',
            'alive' => true,
            'timestamp' => now()->toISOString(),
            'uptime' => $this->getUptime()
        ]);
    }

    /**
     * Collect health metrics for all dependencies.
     *
     * @return void
     */
    protected function collectHealthMetrics(): void
    {
        // Database health
        try {
            $start = microtime(true);
            DB::connection()->getPdo();
            $duration = microtime(true) - $start;
            $this->metricsService->recordDatabaseHealth('default', true);
        } catch (\Exception $e) {
            $this->metricsService->recordDatabaseHealth('default', false);
        }

        // External services health
        $this->checkExternalServices();
    }

    /**
     * Check external services health.
     *
     * @return array
     */
    protected function checkExternalServices(): array
    {
        $services = [
            'customer-service' => config('services.customer.url'),
            'invoice-service' => config('services.invoice.url'),
            'order-service' => config('services.order.url'),
        ];

        $results = [];

        foreach ($services as $name => $url) {
            try {
                $start = microtime(true);
                $response = Http::timeout(5)->get($url . '/health');
                $duration = microtime(true) - $start;

                $healthy = $response->successful();
                $results[$name] = [
                    'status' => $healthy ? 'healthy' : 'unhealthy',
                    'response_time' => round($duration * 1000, 2) . 'ms',
                    'status_code' => $response->status()
                ];

                $this->metricsService->recordExternalServiceHealth($name, $healthy, $duration);

            } catch (\Exception $e) {
                $results[$name] = [
                    'status' => 'unhealthy',
                    'error' => $e->getMessage()
                ];

                $this->metricsService->recordExternalServiceHealth($name, false, 0);
            }
        }

        return $results;
    }

    /**
     * Measure database response time.
     *
     * @return string
     */
    protected function measureDatabaseResponseTime(): string
    {
        $start = microtime(true);
        DB::select('SELECT 1');
        $duration = microtime(true) - $start;

        return round($duration * 1000, 2) . 'ms';
    }

    /**
     * Get service uptime.
     *
     * @return string
     */
    protected function getUptime(): string
    {
        $startFile = storage_path('app/service_start_time');

        if (!file_exists($startFile)) {
            file_put_contents($startFile, time());
        }

        $startTime = (int) file_get_contents($startFile);
        $uptime = time() - $startTime;

        $hours = floor($uptime / 3600);
        $minutes = floor(($uptime % 3600) / 60);
        $seconds = $uptime % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }
}

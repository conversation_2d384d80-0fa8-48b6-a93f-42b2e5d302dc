<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Payment\InitiatePaymentRequest;
use App\Http\Requests\Payment\ProcessPaymentRequest;
use App\Http\Requests\Payment\RefundPaymentRequest;
use App\Services\PaymentLogService;
use App\Services\PaymentMethodService;
use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * The payment service.
     *
     * @var PaymentService
     */
    protected $paymentService;

    /**
     * The payment method service.
     *
     * @var PaymentMethodService
     */
    protected $paymentMethodService;

    /**
     * The payment log service.
     *
     * @var PaymentLogService
     */
    protected $paymentLogService;

    /**
     * Create a new controller instance.
     *
     * @param PaymentService $paymentService
     * @param PaymentMethodService $paymentMethodService
     * @param PaymentLogService $paymentLogService
     * @return void
     */
    public function __construct(
        PaymentService $paymentService,
        PaymentMethodService $paymentMethodService,
        PaymentLogService $paymentLogService
    ) {
        $this->paymentService = $paymentService;
        $this->paymentMethodService = $paymentMethodService;
        $this->paymentLogService = $paymentLogService;
    }

    /**
     * Initiate a payment.
     *
     * @param InitiatePaymentRequest $request
     * @return JsonResponse
     */
    public function initiate(InitiatePaymentRequest $request): JsonResponse
    {
        try {
            $transaction = $this->paymentService->initiatePayment($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Payment initiated successfully',
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Payment initiation failed', [
                'error' => $e->getMessage(),
                'request' => $request->validated()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process a payment.
     *
     * @param ProcessPaymentRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function process(ProcessPaymentRequest $request, string $id): JsonResponse
    {
        try {
            $formData = $this->paymentService->processPayment(
                $id,
                $request->validated()['gateway']
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment processing initiated',
                'data' => $formData
            ]);
        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id,
                'gateway' => $request->validated()['gateway']
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle a payment callback.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function callback(Request $request): JsonResponse
    {
        try {
            $transaction = $this->paymentService->handleCallback($request->all(), $request);

            return response()->json([
                'success' => true,
                'message' => 'Payment callback processed',
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'status' => $transaction->status,
                    'gateway_transaction_id' => $transaction->gateway_transaction_id
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Payment callback handling failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a payment status.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function status(string $id): JsonResponse
    {
        try {
            $transaction = $this->paymentService->getTransaction($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status,
                    'gateway' => $transaction->gateway,
                    'gateway_transaction_id' => $transaction->gateway_transaction_id,
                    'created_at' => $transaction->created_at,
                    'updated_at' => $transaction->updated_at
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Payment status retrieval failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Refund a payment.
     *
     * @param RefundPaymentRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function refund(RefundPaymentRequest $request, string $id): JsonResponse
    {
        try {
            $result = $this->paymentService->refundPayment(
                $id,
                $request->validated()['amount'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id,
                'amount' => $request->validated()['amount'] ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->paymentService->getTransactionStatistics($request->all());

            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);
        } catch (\Exception $e) {
            Log::error('Payment statistics retrieval failed', [
                'error' => $e->getMessage(),
                'filters' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment logs.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function logs(Request $request): JsonResponse
    {
        try {
            $criteria = $request->only([
                'transaction_id',
                'gateway',
                'event',
                'status',
                'start_date',
                'end_date'
            ]);

            $logs = $this->paymentLogService->searchLogs($criteria, $request->input('limit', 50));

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            Log::error('Payment logs retrieval failed', [
                'error' => $e->getMessage(),
                'criteria' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment logs for a transaction.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function transactionLogs(string $id): JsonResponse
    {
        try {
            $logs = $this->paymentLogService->getLogsForTransaction($id);

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            Log::error('Transaction logs retrieval failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle a payment webhook.
     *
     * @param Request $request
     * @param string $gateway
     * @return JsonResponse
     */
    public function webhook(Request $request, string $gateway): JsonResponse
    {
        try {
            // Log the webhook request
            $this->paymentLogService->logEvent(
                'webhook',
                'received',
                [],
                $request->all(),
                null,
                $gateway,
                $request
            );

            // Process the webhook based on the gateway
            $transaction = $this->paymentService->handleCallback($request->all(), $request);

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Payment webhook handling failed', [
                'error' => $e->getMessage(),
                'gateway' => $gateway,
                'request' => $request->all()
            ]);

            // Log the error
            $this->paymentLogService->logEvent(
                'webhook',
                'error',
                [],
                [
                    'error' => $e->getMessage(),
                    'gateway' => $gateway
                ],
                null,
                $gateway,
                $request
            );

            // Return a 200 response to prevent retries
            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed, but acknowledged'
            ], 200);
        }
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\PaymentMethod\StorePaymentMethodRequest;
use App\Http\Requests\PaymentMethod\UpdatePaymentMethodRequest;
use App\Services\PaymentMethodService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentMethodController extends Controller
{
    /**
     * The payment method service.
     *
     * @var PaymentMethodService
     */
    protected $paymentMethodService;
    
    /**
     * Create a new controller instance.
     *
     * @param PaymentMethodService $paymentMethodService
     * @return void
     */
    public function __construct(PaymentMethodService $paymentMethodService)
    {
        $this->paymentMethodService = $paymentMethodService;
    }
    
    /**
     * Get all payment methods for a customer.
     *
     * @param int $customerId
     * @param Request $request
     * @return JsonResponse
     */
    public function getCustomerPaymentMethods(int $customerId, Request $request): JsonResponse
    {
        try {
            $activeOnly = $request->query('active_only', true);
            $paymentMethods = $this->paymentMethodService->getCustomerPaymentMethods($customerId, $activeOnly);
            
            return response()->json([
                'success' => true,
                'data' => $paymentMethods
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get customer payment methods', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Store a newly created payment method.
     *
     * @param StorePaymentMethodRequest $request
     * @return JsonResponse
     */
    public function store(StorePaymentMethodRequest $request): JsonResponse
    {
        try {
            $paymentMethod = $this->paymentMethodService->createPaymentMethod($request->validated());
            
            return response()->json([
                'success' => true,
                'message' => 'Payment method created successfully',
                'data' => $paymentMethod
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create payment method', [
                'error' => $e->getMessage(),
                'data' => $request->validated()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Display the specified payment method.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function show(int $id, Request $request): JsonResponse
    {
        try {
            $customerId = $request->query('customer_id');
            $paymentMethod = $this->paymentMethodService->getPaymentMethod($id, $customerId);
            
            return response()->json([
                'success' => true,
                'data' => $paymentMethod
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get payment method', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        }
    }
    
    /**
     * Update the specified payment method.
     *
     * @param UpdatePaymentMethodRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdatePaymentMethodRequest $request, int $id): JsonResponse
    {
        try {
            $customerId = $request->query('customer_id');
            $paymentMethod = $this->paymentMethodService->updatePaymentMethod($id, $request->validated(), $customerId);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment method updated successfully',
                'data' => $paymentMethod
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update payment method', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $request->validated()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Remove the specified payment method.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(int $id, Request $request): JsonResponse
    {
        try {
            $customerId = $request->query('customer_id');
            $this->paymentMethodService->deletePaymentMethod($id, $customerId);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment method deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete payment method', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Set the specified payment method as default.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function setDefault(int $id, Request $request): JsonResponse
    {
        try {
            $customerId = $request->query('customer_id');
            $paymentMethod = $this->paymentMethodService->setDefaultPaymentMethod($id, $customerId);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment method set as default successfully',
                'data' => $paymentMethod
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to set payment method as default', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}

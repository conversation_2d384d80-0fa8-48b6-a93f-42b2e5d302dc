<?php

namespace App\Http\Controllers\Api\V1;

use App\Exceptions\PaymentException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Payment\ProcessPaymentRequest;
use App\Http\Requests\Payment\RefundPaymentRequest;
use App\Http\Resources\PaymentResource;
use App\Services\Payment\PaymentServiceInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * @var PaymentServiceInterface
     */
    protected $paymentService;

    /**
     * PaymentController constructor.
     *
     * @param PaymentServiceInterface $paymentService
     */
    public function __construct(PaymentServiceInterface $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Display a listing of the payments.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $perPage = $request->input('per_page', 15);
        $filters = $request->except(['page', 'per_page']);
        
        $payments = $this->paymentService->getPaginatedPayments($perPage, $filters);
        
        return PaymentResource::collection($payments);
    }

    /**
     * Display the specified payment.
     *
     * @param int $id
     * @return PaymentResource|JsonResponse
     */
    public function show(int $id)
    {
        $payment = $this->paymentService->getPaymentById($id);
        
        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => 'Payment not found',
            ], 404);
        }
        
        return new PaymentResource($payment);
    }

    /**
     * Process a payment.
     *
     * @param ProcessPaymentRequest $request
     * @return JsonResponse
     */
    public function process(ProcessPaymentRequest $request): JsonResponse
    {
        try {
            $result = $this->paymentService->processPayment($request->validated());
            
            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to process payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the payment',
            ], 500);
        }
    }

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param Request $request
     * @return JsonResponse
     */
    public function verify(string $transactionId, Request $request): JsonResponse
    {
        try {
            $result = $this->paymentService->verifyPayment($transactionId, $request->all());
            
            return response()->json([
                'success' => true,
                'message' => 'Payment verified successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to verify payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while verifying the payment',
            ], 500);
        }
    }

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param RefundPaymentRequest $request
     * @return JsonResponse
     */
    public function refund(string $transactionId, RefundPaymentRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $amount = $data['amount'] ?? null;
            
            $result = $this->paymentService->refundPayment($transactionId, $amount, $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to refund payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while refunding the payment',
            ], 500);
        }
    }

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param Request $request
     * @return JsonResponse
     */
    public function cancel(string $transactionId, Request $request): JsonResponse
    {
        try {
            $result = $this->paymentService->cancelPayment($transactionId, $request->all());
            
            return response()->json([
                'success' => true,
                'message' => 'Payment cancelled successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to cancel payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while cancelling the payment',
            ], 500);
        }
    }

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return JsonResponse
     */
    public function status(string $transactionId): JsonResponse
    {
        try {
            $result = $this->paymentService->getPaymentStatus($transactionId);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment status retrieved successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to get payment status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while getting the payment status',
            ], 500);
        }
    }

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return JsonResponse
     */
    public function details(string $transactionId): JsonResponse
    {
        try {
            $result = $this->paymentService->getPaymentDetails($transactionId);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment details retrieved successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to get payment details', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while getting the payment details',
            ], 500);
        }
    }

    /**
     * Get available payment gateways.
     *
     * @return JsonResponse
     */
    public function gateways(): JsonResponse
    {
        try {
            $gateways = $this->paymentService->getAvailableGateways();
            
            return response()->json([
                'success' => true,
                'message' => 'Available payment gateways retrieved successfully',
                'data' => $gateways,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get available payment gateways', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while getting the available payment gateways',
            ], 500);
        }
    }

    /**
     * Generate payment form.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function form(Request $request): JsonResponse
    {
        try {
            $result = $this->paymentService->generatePaymentForm($request->all());
            
            return response()->json([
                'success' => true,
                'message' => 'Payment form generated successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to generate payment form', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while generating the payment form',
            ], 500);
        }
    }

    /**
     * Handle payment webhook.
     *
     * @param string $gateway
     * @param Request $request
     * @return JsonResponse
     */
    public function webhook(string $gateway, Request $request): JsonResponse
    {
        try {
            $result = $this->paymentService->handleWebhook($gateway, $request->all());
            
            return response()->json([
                'success' => true,
                'message' => 'Payment webhook handled successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to handle payment webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'gateway' => $gateway,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while handling the payment webhook',
            ], 500);
        }
    }

    /**
     * Get payment statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $filters = $request->all();
            $statistics = $this->paymentService->getPaymentStatistics($filters);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment statistics retrieved successfully',
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get payment statistics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while getting the payment statistics',
            ], 500);
        }
    }
}

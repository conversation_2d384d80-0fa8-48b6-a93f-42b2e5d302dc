<?php

namespace App\Http\Requests\Payment;

use Illuminate\Foundation\Http\FormRequest;

class InitiatePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'required|integer',
            'customer_email' => 'nullable|email|max:100',
            'customer_phone' => 'nullable|string|max:20',
            'customer_name' => 'nullable|string|max:100',
            'amount' => 'required|numeric|min:0.01',
            'transaction_charges' => 'nullable|numeric|min:0',
            'wallet_amount' => 'nullable|numeric|min:0',
            'order_id' => 'nullable|string|max:100',
            'referer' => 'nullable|string|max:50',
            'success_url' => 'required|url',
            'failure_url' => 'required|url',
            'context' => 'nullable|string|max:50',
            'recurring' => 'nullable|boolean',
            'discount' => 'nullable|numeric|min:0'
        ];
    }
}

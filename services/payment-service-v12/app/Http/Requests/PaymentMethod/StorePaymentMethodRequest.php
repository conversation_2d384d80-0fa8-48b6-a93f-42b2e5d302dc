<?php

namespace App\Http\Requests\PaymentMethod;

use Illuminate\Foundation\Http\FormRequest;

class StorePaymentMethodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'required|integer',
            'gateway' => 'required|string',
            'token' => 'required|string',
            'type' => 'required|string',
            'last_four' => 'nullable|string|size:4',
            'expiry_month' => 'nullable|string|size:2',
            'expiry_year' => 'nullable|string|size:4',
            'card_holder_name' => 'nullable|string|max:100',
            'card_brand' => 'nullable|string|max:50',
            'is_default' => 'nullable|boolean',
            'metadata' => 'nullable|array',
        ];
    }
}

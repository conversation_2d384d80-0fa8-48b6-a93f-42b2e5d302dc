<?php

namespace App\Events\Payment;

use App\Models\PaymentTransaction;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PaymentInitiated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    
    /**
     * The payment transaction.
     *
     * @var PaymentTransaction
     */
    public $transaction;
    
    /**
     * Create a new event instance.
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    public function __construct(PaymentTransaction $transaction)
    {
        $this->transaction = $transaction;
    }
}

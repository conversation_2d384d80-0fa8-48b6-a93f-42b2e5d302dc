<?php

namespace App\Events\Payment;

use App\Models\Payment;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PaymentRefunded
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The payment.
     *
     * @var Payment
     */
    public $payment;

    /**
     * The refund amount.
     *
     * @var float|null
     */
    public $amount;

    /**
     * Create a new event instance.
     *
     * @param Payment $payment
     * @param float|null $amount
     * @return void
     */
    public function __construct(Payment $payment, float $amount = null)
    {
        $this->payment = $payment;
        $this->amount = $amount;
    }
}

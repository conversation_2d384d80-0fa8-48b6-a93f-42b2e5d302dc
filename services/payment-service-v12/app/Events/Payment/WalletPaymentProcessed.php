<?php

namespace App\Events\Payment;

use App\Models\PaymentTransaction;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * WalletPaymentProcessed Event
 * 
 * Fired when a wallet payment is successfully processed
 */
class WalletPaymentProcessed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param PaymentTransaction $transaction
     * @param array $walletData
     */
    public function __construct(
        public PaymentTransaction $transaction,
        public array $walletData
    ) {
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            // Add broadcasting channels if needed
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'transaction_id' => $this->transaction->transaction_id,
            'customer_id' => $this->transaction->customer_id,
            'wallet_amount' => $this->transaction->wallet_amount,
            'wallet_transaction_id' => $this->walletData['data']['transaction_id'] ?? null,
            'balance_after' => $this->walletData['data']['balance_after'] ?? null,
            'processed_at' => now()->toISOString(),
        ];
    }
}

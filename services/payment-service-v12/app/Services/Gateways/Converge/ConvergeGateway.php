<?php

namespace App\Services\Gateways\Converge;

use App\Services\Gateways\AbstractGateway;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ConvergeGateway extends AbstractGateway
{
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? $this->config['test_url'] : $this->config['production_url'];
            
            // Prepare parameters
            $params = [
                'ssl_merchant_id' => $this->config['merchant_id'],
                'ssl_user_id' => $this->config['user_id'],
                'ssl_pin' => $this->config['pin'],
                'ssl_transaction_type' => 'ccsale',
                'ssl_amount' => $transaction['amount'],
                'ssl_invoice_number' => $transaction['transaction_id'],
                'ssl_description' => $transaction['description'] ?? 'Payment for order ' . ($transaction['order_id'] ?? ''),
                'ssl_customer_code' => $transaction['customer_id'],
                'ssl_customer_email' => $transaction['customer_email'] ?? '',
                'ssl_first_name' => $transaction['customer_name'] ?? 'Customer',
                'ssl_phone' => $transaction['customer_phone'] ?? '',
                'ssl_result_format' => 'HTML',
                'ssl_receipt_link_method' => 'REDG',
                'ssl_receipt_link_url' => $transaction['success_url'],
                'ssl_error_url' => $transaction['failure_url'],
                'ssl_receipt_link_text' => 'Return to Merchant',
                'ssl_show_form' => 'true',
                'ssl_entry_mode' => '01',
                'ssl_get_token' => 'Y',
            ];
            
            return [
                'action' => $url,
                'method' => 'POST',
                'fields' => $params
            ];
        } catch (\Exception $e) {
            Log::error('Converge payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        // Converge processes payment on their end and returns to success/failure URL
        // This method is not typically used for Converge
        return [
            'success' => false,
            'status' => 'pending',
            'message' => 'Payment is being processed by Converge'
        ];
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // Extract transaction data
            $sslResult = $data['ssl_result'] ?? '';
            $sslResultMessage = $data['ssl_result_message'] ?? '';
            $sslTxnId = $data['ssl_txn_id'] ?? '';
            $sslApprovalCode = $data['ssl_approval_code'] ?? '';
            $sslCardNumber = $data['ssl_card_number'] ?? '';
            $sslAmount = $data['ssl_amount'] ?? '';
            
            // Check result
            if ($sslResult === '0') {
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $sslTxnId,
                    'metadata' => $data
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $sslResultMessage,
                    'metadata' => $data
                ];
            }
        } catch (\Exception $e) {
            Log::error('Converge payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? $this->config['test_url'] : $this->config['production_url'];
            
            // Prepare XML request
            $xml = '<?xml version="1.0" encoding="UTF-8"?>';
            $xml .= '<txn>';
            $xml .= '<ssl_merchant_id>' . $this->config['merchant_id'] . '</ssl_merchant_id>';
            $xml .= '<ssl_user_id>' . $this->config['user_id'] . '</ssl_user_id>';
            $xml .= '<ssl_pin>' . $this->config['pin'] . '</ssl_pin>';
            $xml .= '<ssl_transaction_type>ccreturn</ssl_transaction_type>';
            $xml .= '<ssl_txn_id>' . $transactionId . '</ssl_txn_id>';
            
            if ($amount !== null) {
                $xml .= '<ssl_amount>' . $amount . '</ssl_amount>';
            }
            
            $xml .= '</txn>';
            
            // Make API request to process refund
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/xml',
                'Content-Length: ' . strlen($xml)
            ]);
            $response = curl_exec($ch);
            curl_close($ch);
            
            // Parse XML response
            $responseData = simplexml_load_string($response);
            
            if ((string) $responseData->ssl_result === '0') {
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => (string) $responseData->ssl_txn_id,
                    'metadata' => json_decode(json_encode($responseData), true)
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => (string) $responseData->ssl_result_message,
                    'metadata' => json_decode(json_encode($responseData), true)
                ];
            }
        } catch (\Exception $e) {
            Log::error('Converge payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
}

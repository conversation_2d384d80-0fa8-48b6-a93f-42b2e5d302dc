<?php

namespace App\Services\Gateways\Payeezy;

use App\Services\Gateways\AbstractGateway;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PayeezyGateway extends AbstractGateway
{
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' 
                ? 'https://api-cert.payeezy.com/v1/transactions' 
                : 'https://api.payeezy.com/v1/transactions';
            
            // Generate timestamp and nonce
            $timestamp = time() * 1000;
            $nonce = Str::random(32);
            
            // Prepare payload
            $payload = [
                'merchant_ref' => $transaction['transaction_id'],
                'transaction_type' => 'purchase',
                'method' => 'credit_card',
                'amount' => $transaction['amount'],
                'currency_code' => 'USD',
                'credit_card' => [
                    'type' => 'placeholder', // Will be replaced by actual card type
                    'cardholder_name' => 'placeholder', // Will be replaced by actual cardholder name
                    'card_number' => 'placeholder', // Will be replaced by actual card number
                    'exp_date' => 'placeholder', // Will be replaced by actual expiry date
                    'cvv' => 'placeholder', // Will be replaced by actual CVV
                ],
                'billing_address' => [
                    'street' => 'placeholder', // Will be replaced by actual street
                    'city' => 'placeholder', // Will be replaced by actual city
                    'state_province' => 'placeholder', // Will be replaced by actual state
                    'zip_postal_code' => 'placeholder', // Will be replaced by actual zip
                    'country' => 'placeholder', // Will be replaced by actual country
                ],
            ];
            
            // Generate HMAC authorization token
            $data = $this->config['id'] . $nonce . $timestamp . $this->config['key'];
            $hmacToken = hash_hmac('sha256', $data, $this->config['hmac_key']);
            
            // Prepare headers
            $headers = [
                'Content-Type: application/json',
                'apikey: ' . $this->config['key'],
                'token: ' . $this->config['id'],
                'nonce: ' . $nonce,
                'timestamp: ' . $timestamp,
                'Authorization: ' . $hmacToken,
            ];
            
            // Return form data for client-side processing
            return [
                'action' => 'payment/payeezy',
                'method' => 'POST',
                'fields' => [
                    'transaction_id' => $transaction['transaction_id'],
                    'amount' => $transaction['amount'],
                    'currency' => 'USD',
                    'customer_name' => $transaction['customer_name'] ?? '',
                    'customer_email' => $transaction['customer_email'] ?? '',
                    'customer_phone' => $transaction['customer_phone'] ?? '',
                    'success_url' => $transaction['success_url'],
                    'failure_url' => $transaction['failure_url'],
                    'api_url' => $url,
                    'api_key' => $this->config['key'],
                    'api_token' => $this->config['id'],
                    'hmac_key' => $this->config['hmac_key'], // Note: In a production environment, you should not expose this to the client
                    'js_security_key' => $this->config['js_security_key'] ?? '',
                    'ta_token' => $this->config['ta_token'] ?? '',
                    'hco_login' => $this->config['hco_login'],
                    'hco_transaction_key' => $this->config['hco_transaction_key'],
                ]
            ];
        } catch (\Exception $e) {
            Log::error('Payeezy payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        try {
            // Extract payment data
            $transactionId = $data['transaction_id'] ?? '';
            $transactionTag = $data['transaction_tag'] ?? '';
            $transactionKey = $data['transaction_key'] ?? '';
            $status = $data['transaction_status'] ?? '';
            
            if (empty($transactionId) || empty($transactionTag) || empty($transactionKey)) {
                Log::error('Payeezy payment processing failed: Missing transaction data', [
                    'data' => $data
                ]);
                
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => 'Missing transaction data'
                ];
            }
            
            // Check transaction status
            if ($status === 'approved') {
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $transactionTag,
                    'metadata' => $data
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => 'Transaction status: ' . $status,
                    'metadata' => $data
                ];
            }
        } catch (\Exception $e) {
            Log::error('Payeezy payment processing failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // For Payeezy, we can use the processPayment method to verify the payment
            return $this->processPayment($data);
        } catch (\Exception $e) {
            Log::error('Payeezy payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' 
                ? 'https://api-cert.payeezy.com/v1/transactions' 
                : 'https://api.payeezy.com/v1/transactions';
            
            // Generate timestamp and nonce
            $timestamp = time() * 1000;
            $nonce = Str::random(32);
            
            // Prepare payload
            $payload = [
                'merchant_ref' => $transactionId,
                'transaction_type' => 'refund',
                'method' => 'credit_card',
                'amount' => $amount ?? 0,
                'currency_code' => 'USD',
                'transaction_tag' => $transactionId,
            ];
            
            // Generate HMAC authorization token
            $data = $this->config['id'] . $nonce . $timestamp . $this->config['key'];
            $hmacToken = hash_hmac('sha256', $data, $this->config['hmac_key']);
            
            // Make API request to process refund
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'apikey: ' . $this->config['key'],
                'token: ' . $this->config['id'],
                'nonce: ' . $nonce,
                'timestamp: ' . $timestamp,
                'Authorization: ' . $hmacToken,
            ]);
            $response = curl_exec($ch);
            curl_close($ch);
            
            // Parse response
            $responseData = json_decode($response, true);
            
            if (isset($responseData['transaction_status']) && $responseData['transaction_status'] === 'approved') {
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => $responseData['transaction_tag'] ?? '',
                    'metadata' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $responseData['Error']['messages'][0]['description'] ?? 'Refund failed',
                    'metadata' => $responseData
                ];
            }
        } catch (\Exception $e) {
            Log::error('Payeezy payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
}

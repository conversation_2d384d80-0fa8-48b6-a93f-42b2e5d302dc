<?php

namespace App\Services\Gateways\Paypal;

use App\Services\Gateways\AbstractGateway;
use Omnipay\Omnipay;
use Illuminate\Support\Facades\Log;

class PaypalGateway extends AbstractGateway
{
    /**
     * Create a new PaypalGateway instance.
     */
    public function __construct()
    {
        $this->gateway = Omnipay::create('PayPal_Rest');
    }
    
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
        
        $this->gateway->setClientId($config['client_id']);
        $this->gateway->setSecret($config['secret']);
        $this->gateway->setTestMode($config['mode'] === 'test');
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            $response = $this->gateway->purchase([
                'amount' => $transaction['amount'],
                'currency' => $this->config['currency'] ?? 'USD',
                'description' => $transaction['description'] ?? 'Payment for order ' . ($transaction['order_id'] ?? ''),
                'returnUrl' => $transaction['success_url'] . '?tid=' . $transaction['transaction_id'],
                'cancelUrl' => $transaction['failure_url'] . '?tid=' . $transaction['transaction_id'],
                'transactionId' => $transaction['transaction_id'],
                'notifyUrl' => route('api.payments.callback'),
                'metadata' => [
                    'transaction_id' => $transaction['transaction_id'],
                    'customer_id' => $transaction['customer_id'],
                    'order_id' => $transaction['order_id'] ?? null,
                ]
            ])->send();
            
            if ($response->isRedirect()) {
                return [
                    'action' => $response->getRedirectUrl(),
                    'method' => 'GET',
                    'fields' => []
                ];
            } else {
                Log::error('PayPal payment creation failed', [
                    'error' => $response->getMessage(),
                    'transaction_id' => $transaction['transaction_id']
                ]);
                
                throw new \Exception('PayPal payment creation failed: ' . $response->getMessage());
            }
        } catch (\Exception $e) {
            Log::error('PayPal payment creation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        try {
            $response = $this->gateway->completePurchase([
                'payerId' => $data['PayerID'],
                'transactionReference' => $data['token'],
            ])->send();
            
            if ($response->isSuccessful()) {
                $paymentData = $response->getData();
                
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $paymentData['id'],
                    'metadata' => $paymentData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $response->getMessage(),
                    'metadata' => $response->getData()
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal payment processing failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // For PayPal, we need to complete the purchase to verify the payment
            return $this->processPayment($data);
        } catch (\Exception $e) {
            Log::error('PayPal payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            $parameters = [
                'transactionReference' => $transactionId,
            ];
            
            if ($amount !== null) {
                $parameters['amount'] = $amount;
            }
            
            $response = $this->gateway->refund($parameters)->send();
            
            if ($response->isSuccessful()) {
                $refund = $response->getData();
                
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => $refund['id'],
                    'metadata' => $refund
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $response->getMessage(),
                    'metadata' => $response->getData()
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
}

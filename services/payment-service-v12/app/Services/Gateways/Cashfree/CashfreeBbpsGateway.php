<?php

namespace App\Services\Gateways\Cashfree;

use App\Contracts\PaymentGatewayInterface;
use App\Services\Gateways\AbstractPaymentGateway;
use App\Exceptions\Payment\PaymentException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Cashfree BBPS (Bharat Bill Payment System) Gateway Implementation
 * 
 * Handles bill payment processing through Cashfree BBPS gateway
 * for OneFoodDialer 2025 bill payment services
 */
class CashfreeBbpsGateway extends AbstractPaymentGateway implements PaymentGatewayInterface
{
    /**
     * Gateway name identifier
     */
    protected string $gatewayName = 'cashfree_bbps';

    /**
     * Cashfree BBPS API endpoints
     */
    protected array $endpoints = [
        'test' => [
            'base_url' => 'https://test.cashfree.com/billpay/checkout/post/submit',
            'status_url' => 'https://test.cashfree.com/api/v2/cftoken/order/',
        ],
        'production' => [
            'base_url' => 'https://www.cashfree.com/billpay/checkout/post/submit',
            'status_url' => 'https://api.cashfree.com/api/v2/cftoken/order/',
        ]
    ];

    /**
     * Initialize the gateway with configuration
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void
    {
        $this->config = array_merge([
            'app_id' => '',
            'secret_key' => '',
            'mode' => 'test',
            'currency' => 'INR',
        ], $config);

        $this->validateConfig();
    }

    /**
     * Validate gateway configuration
     *
     * @throws PaymentException
     */
    protected function validateConfig(): void
    {
        $required = ['app_id', 'secret_key'];
        
        foreach ($required as $field) {
            if (empty($this->config[$field])) {
                throw new PaymentException("Cashfree BBPS gateway configuration missing: {$field}");
            }
        }
    }

    /**
     * Create a payment request
     *
     * @param array $data
     * @return array
     * @throws PaymentException
     */
    public function createPayment(array $data): array
    {
        try {
            // Validate BBPS specific data
            $this->validateBbpsData($data);

            // Prepare payment data
            $paymentData = $this->preparePaymentData($data);

            // Generate signature
            $signature = $this->generateSignature($paymentData);
            $paymentData['signature'] = $signature;

            return [
                'success' => true,
                'payment_url' => $this->endpoints[$this->config['mode']]['base_url'],
                'form_data' => $paymentData,
                'status' => 'initiated',
                'gateway_response' => $paymentData
            ];

        } catch (Exception $e) {
            Log::error('Cashfree BBPS payment creation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new PaymentException('Cashfree BBPS payment creation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get the form data for the payment gateway
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            $paymentRequest = $this->createPayment($transaction);
            
            return [
                'action' => $paymentRequest['payment_url'],
                'method' => 'POST',
                'fields' => $paymentRequest['form_data']
            ];
        } catch (Exception $e) {
            Log::error('Cashfree BBPS payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id'] ?? 'unknown'
            ]);
            
            throw new PaymentException('Cashfree BBPS payment form data generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Process a payment
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        // Cashfree BBPS processes payment on their end and returns to callback URL
        return [
            'success' => false,
            'status' => 'pending',
            'message' => 'Payment is being processed by Cashfree BBPS'
        ];
    }

    /**
     * Verify payment status
     *
     * @param string $orderId
     * @return array
     * @throws PaymentException
     */
    public function verifyPayment(string $orderId): array
    {
        try {
            $url = $this->endpoints[$this->config['mode']]['status_url'] . $orderId;

            $headers = [
                'X-Client-Id' => $this->config['app_id'],
                'X-Client-Secret' => $this->config['secret_key'],
                'Content-Type' => 'application/json'
            ];

            $response = Http::withHeaders($headers)
                ->timeout(30)
                ->retry(3, 1000)
                ->get($url);

            if (!$response->successful()) {
                throw new PaymentException('Cashfree BBPS status API call failed');
            }

            $responseData = $response->json();

            return [
                'success' => true,
                'status' => $this->mapPaymentStatus($responseData['order_status'] ?? 'PENDING'),
                'gateway_transaction_id' => $responseData['cf_order_id'] ?? '',
                'amount' => $responseData['order_amount'] ?? 0,
                'currency' => $responseData['order_currency'] ?? 'INR',
                'gateway_response' => $responseData
            ];

        } catch (Exception $e) {
            Log::error('Cashfree BBPS payment verification failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            throw new PaymentException('Cashfree BBPS payment verification failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle webhook notification
     *
     * @param array $data
     * @return array
     * @throws PaymentException
     */
    public function handleWebhook(array $data): array
    {
        try {
            // Validate webhook signature
            $signature = $data['signature'] ?? '';
            unset($data['signature']);

            $isValidSignature = $this->verifySignature($data, $signature);

            if (!$isValidSignature) {
                throw new PaymentException('Invalid Cashfree BBPS webhook signature');
            }

            return [
                'success' => true,
                'order_id' => $data['orderId'],
                'transaction_id' => $data['txnId'] ?? '',
                'status' => $this->mapPaymentStatus($data['txStatus']),
                'amount' => $data['orderAmount'] ?? 0,
                'gateway_response' => $data
            ];

        } catch (Exception $e) {
            Log::error('Cashfree BBPS webhook processing failed', [
                'webhook_data' => $data,
                'error' => $e->getMessage()
            ]);

            throw new PaymentException('Cashfree BBPS webhook processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Refund a payment
     *
     * @param string $transactionId
     * @param float $amount
     * @param string $reason
     * @return array
     * @throws PaymentException
     */
    public function refundPayment(string $transactionId, float $amount, string $reason = ''): array
    {
        try {
            // Note: BBPS typically doesn't support refunds in the traditional sense
            // This would need to be handled through the biller's refund process
            
            Log::info('Cashfree BBPS refund requested', [
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'reason' => $reason
            ]);

            return [
                'success' => false,
                'status' => 'not_supported',
                'message' => 'BBPS refunds must be processed through the biller',
                'amount' => $amount,
                'gateway_response' => [
                    'message' => 'BBPS refunds are not supported through gateway'
                ]
            ];

        } catch (Exception $e) {
            Log::error('Cashfree BBPS refund failed', [
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            throw new PaymentException('Cashfree BBPS refund failed: ' . $e->getMessage());
        }
    }

    /**
     * Validate BBPS specific data
     *
     * @param array $data
     * @throws PaymentException
     */
    protected function validateBbpsData(array $data): void
    {
        $required = ['biller_id', 'customer_account_number'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new PaymentException("BBPS required field missing: {$field}");
            }
        }
    }

    /**
     * Prepare payment data for Cashfree BBPS
     *
     * @param array $data
     * @return array
     */
    protected function preparePaymentData(array $data): array
    {
        return [
            'appId' => $this->config['app_id'],
            'orderId' => $data['order_id'] ?? $data['transaction_id'],
            'orderAmount' => $data['amount'],
            'orderCurrency' => $this->config['currency'],
            'orderNote' => $data['description'] ?? 'Bill Payment',
            'customerName' => $data['customer_name'] ?? '',
            'customerPhone' => $data['customer_phone'] ?? '',
            'customerEmail' => $data['customer_email'] ?? '',
            'returnUrl' => route('api.payments.webhook', ['gateway' => 'cashfree_bbps']),
            'notifyUrl' => route('api.payments.webhook', ['gateway' => 'cashfree_bbps']),
            'billerId' => $data['biller_id'],
            'customerAccountNumber' => $data['customer_account_number'],
            'billAmount' => $data['amount'],
        ];
    }

    /**
     * Generate signature for Cashfree BBPS
     *
     * @param array $data
     * @return string
     */
    protected function generateSignature(array $data): string
    {
        $signatureData = $data['appId'] . $data['orderId'] . $data['orderAmount'] . 
                        $data['orderCurrency'] . $this->config['secret_key'];
        
        return hash('sha256', $signatureData);
    }

    /**
     * Verify signature for webhook
     *
     * @param array $data
     * @param string $signature
     * @return bool
     */
    protected function verifySignature(array $data, string $signature): bool
    {
        $signatureData = $data['orderId'] . $data['orderAmount'] . 
                        $data['referenceId'] . $data['txStatus'] . 
                        $data['paymentMode'] . $data['txMsg'] . 
                        $data['txTime'] . $this->config['secret_key'];
        
        $calculatedSignature = hash('sha256', $signatureData);
        
        return hash_equals($calculatedSignature, $signature);
    }

    /**
     * Map Cashfree BBPS payment status to internal status
     *
     * @param string $status
     * @return string
     */
    protected function mapPaymentStatus(string $status): string
    {
        return match (strtoupper($status)) {
            'SUCCESS' => 'completed',
            'FAILED' => 'failed',
            'PENDING' => 'pending',
            'CANCELLED' => 'cancelled',
            default => 'pending'
        };
    }
}

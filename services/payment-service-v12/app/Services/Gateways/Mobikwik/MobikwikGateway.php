<?php

namespace App\Services\Gateways\Mobikwik;

use App\Services\Gateways\AbstractGateway;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MobikwikGateway extends AbstractGateway
{
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            // Prepare parameters
            $params = [
                'merchantid' => $this->config['merchant_id'],
                'orderid' => $transaction['transaction_id'],
                'amount' => $transaction['amount'] * 100, // Amount in paise
                'email' => $transaction['customer_email'] ?? '',
                'phone' => $transaction['customer_phone'] ?? '',
                'firstname' => $transaction['customer_name'] ?? 'Customer',
                'productinfo' => $transaction['description'] ?? 'Payment for order ' . ($transaction['order_id'] ?? ''),
                'surl' => $transaction['success_url'],
                'furl' => $transaction['failure_url'],
                'txntype' => 'SALE',
                'txnid' => $transaction['transaction_id'],
                'currency' => 'INR',
            ];
            
            // Generate checksum
            $checksum = $this->generateChecksum($params, $this->config['merchant_key']);
            $params['checksum'] = $checksum;
            
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? $this->config['test_url'] : $this->config['production_url'];
            
            return [
                'action' => $url,
                'method' => 'POST',
                'fields' => $params
            ];
        } catch (\Exception $e) {
            Log::error('Mobikwik payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        // Mobikwik processes payment on their end and returns to success/failure URL
        // This method is not typically used for Mobikwik
        return [
            'success' => false,
            'status' => 'pending',
            'message' => 'Payment is being processed by Mobikwik'
        ];
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // Verify checksum
            $checksum = $data['checksum'] ?? '';
            $params = $data;
            unset($params['checksum']);
            
            $calculatedChecksum = $this->generateChecksum($params, $this->config['merchant_key']);
            
            if ($checksum !== $calculatedChecksum) {
                Log::error('Mobikwik payment verification failed: Invalid checksum', [
                    'transaction_id' => $transactionId,
                    'received_checksum' => $checksum,
                    'calculated_checksum' => $calculatedChecksum
                ]);
                
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => 'Invalid checksum'
                ];
            }
            
            // Check status
            $statusCode = $data['statuscode'] ?? '';
            $status = $data['status'] ?? '';
            
            if ($statusCode === '0' && $status === 'success') {
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $data['pgid'] ?? $transactionId,
                    'metadata' => $data
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $data['statusdescription'] ?? 'Payment failed',
                    'metadata' => $data
                ];
            }
        } catch (\Exception $e) {
            Log::error('Mobikwik payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' 
                ? 'https://test.mobikwik.com/mobikwik/refund' 
                : 'https://www.mobikwik.com/mobikwik/refund';
            
            // Prepare parameters
            $params = [
                'merchantid' => $this->config['merchant_id'],
                'orderid' => $transactionId,
                'amount' => ($amount ?? 0) * 100, // Amount in paise
                'txntype' => 'REFUND',
                'txnid' => $transactionId,
                'currency' => 'INR',
            ];
            
            // Generate checksum
            $checksum = $this->generateChecksum($params, $this->config['merchant_key']);
            $params['checksum'] = $checksum;
            
            // Make API request to process refund
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);
            
            // Parse response
            $responseData = json_decode($response, true);
            
            if (isset($responseData['statuscode']) && $responseData['statuscode'] === '0') {
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => $responseData['pgid'] ?? $transactionId,
                    'metadata' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $responseData['statusdescription'] ?? 'Refund failed',
                    'metadata' => $responseData
                ];
            }
        } catch (\Exception $e) {
            Log::error('Mobikwik payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate checksum for Mobikwik.
     *
     * @param array $params
     * @param string $key
     * @return string
     */
    private function generateChecksum(array $params, string $key): string
    {
        ksort($params);
        $paramString = '';
        
        foreach ($params as $param => $value) {
            if ($value !== '') {
                $paramString .= $param . '=' . $value . '&';
            }
        }
        
        $paramString = rtrim($paramString, '&');
        $paramString .= $key;
        
        return hash('sha256', $paramString);
    }
}

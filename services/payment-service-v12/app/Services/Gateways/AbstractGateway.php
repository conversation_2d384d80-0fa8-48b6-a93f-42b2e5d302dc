<?php

namespace App\Services\Gateways;

use App\Services\Gateways\Contracts\PaymentGatewayInterface;
use Omnipay\Omnipay;

abstract class AbstractGateway implements PaymentGatewayInterface
{
    /**
     * The Omnipay gateway instance.
     *
     * @var \Omnipay\Common\GatewayInterface
     */
    protected $gateway;
    
    /**
     * The gateway configuration.
     *
     * @var array
     */
    protected $config;
    
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        $this->config = $config;
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    abstract public function getFormData(array $transaction): array;
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    abstract public function processPayment(array $data): array;
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    abstract public function verifyPayment(string $transactionId, array $data = []): array;
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    abstract public function refundPayment(string $transactionId, float $amount = null): array;
}

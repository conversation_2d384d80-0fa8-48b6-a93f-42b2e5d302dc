<?php

namespace App\Services\Gateways\Payu;

use App\Services\Gateways\AbstractGateway;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PayuGateway extends AbstractGateway
{
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
    }

    /**
     * Get the gateway name.
     *
     * @return string
     */
    public function getName(): string
    {
        return 'payu';
    }

    /**
     * Check if the gateway is enabled.
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return isset($this->config['enabled']) ? (bool) $this->config['enabled'] : true;
    }

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @return array
     */
    public function cancelPayment(string $transactionId): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? 'https://test.payu.in/merchant/postservice?form=2' : 'https://info.payu.in/merchant/postservice?form=2';

            // Prepare request data
            $requestData = [
                'key' => $this->config['key'],
                'command' => 'cancel_refund_transaction',
                'var1' => $transactionId, // PayU transaction ID
                'var2' => 0, // Cancel amount (0 for full cancellation)
                'var3' => 'Payment cancelled by customer', // Cancel reason
            ];

            // Generate hash
            $hashString = $this->config['key'] . '|' . $requestData['command'] . '|' . $requestData['var1'] . '|' . $this->config['salt'];
            $hash = strtolower(hash('sha512', $hashString));
            $requestData['hash'] = $hash;

            // Make API request
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($requestData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);

            // Parse response
            $responseData = json_decode($response, true);

            if (isset($responseData['status']) && $responseData['status'] === '1') {
                return [
                    'success' => true,
                    'status' => 'cancelled',
                    'gateway_transaction_id' => $responseData['request_id'] ?? $transactionId,
                    'metadata' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $responseData['msg'] ?? 'Cancellation failed',
                    'metadata' => $responseData
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayU payment cancellation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);

            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            $key = $this->config['key'];
            $salt = $this->config['salt'];
            $txnid = $transaction['transaction_id'];
            $amount = $transaction['amount'];
            $productinfo = $transaction['description'] ?? 'Payment for order ' . ($transaction['order_id'] ?? '');
            $firstname = $transaction['customer_name'] ?? 'Customer';
            $email = $transaction['customer_email'] ?? '';
            $phone = $transaction['customer_phone'] ?? '';
            $surl = $transaction['success_url'];
            $furl = $transaction['failure_url'];
            $service_provider = 'payu_paisa';
            
            // Additional parameters
            $udf1 = $transaction['transaction_id']; // Store transaction ID for reference
            $udf2 = $transaction['customer_id'] ?? '';
            $udf3 = $transaction['order_id'] ?? '';
            $udf4 = '';
            $udf5 = '';
            
            // Generate hash
            $hashString = "{$key}|{$txnid}|{$amount}|{$productinfo}|{$firstname}|{$email}|{$udf1}|{$udf2}|{$udf3}|{$udf4}|{$udf5}||||||{$salt}";
            $hash = strtolower(hash('sha512', $hashString));
            
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? $this->config['test_url'] : $this->config['production_url'];
            
            // Prepare form fields
            $fields = [
                'key' => $key,
                'txnid' => $txnid,
                'amount' => $amount,
                'productinfo' => $productinfo,
                'firstname' => $firstname,
                'email' => $email,
                'phone' => $phone,
                'surl' => $surl,
                'furl' => $furl,
                'service_provider' => $service_provider,
                'hash' => $hash,
                'udf1' => $udf1,
                'udf2' => $udf2,
                'udf3' => $udf3,
                'udf4' => $udf4,
                'udf5' => $udf5,
            ];
            
            return [
                'action' => $url,
                'method' => 'POST',
                'fields' => $fields
            ];
        } catch (\Exception $e) {
            Log::error('PayU payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        // PayU processes payment on their end and returns to success/failure URL
        // This method is not typically used for PayU
        return [
            'success' => false,
            'status' => 'pending',
            'message' => 'Payment is being processed by PayU'
        ];
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // Verify hash
            $key = $this->config['key'];
            $salt = $this->config['salt'];
            $status = $data['status'] ?? '';
            $amount = $data['amount'] ?? '';
            $productinfo = $data['productinfo'] ?? '';
            $firstname = $data['firstname'] ?? '';
            $email = $data['email'] ?? '';
            $udf1 = $data['udf1'] ?? '';
            $udf2 = $data['udf2'] ?? '';
            $udf3 = $data['udf3'] ?? '';
            $udf4 = $data['udf4'] ?? '';
            $udf5 = $data['udf5'] ?? '';
            $payuMoneyId = $data['payuMoneyId'] ?? '';
            $hash = $data['hash'] ?? '';
            
            // Generate hash for verification
            $hashString = "{$salt}|{$status}||||||{$udf5}|{$udf4}|{$udf3}|{$udf2}|{$udf1}|{$email}|{$firstname}|{$productinfo}|{$amount}|{$transactionId}|{$key}";
            $calculatedHash = strtolower(hash('sha512', $hashString));
            
            if ($hash !== $calculatedHash) {
                Log::error('PayU payment verification failed: Hash mismatch', [
                    'transaction_id' => $transactionId,
                    'received_hash' => $hash,
                    'calculated_hash' => $calculatedHash
                ]);
                
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => 'Hash verification failed'
                ];
            }
            
            // Check status
            if ($status === 'success') {
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $payuMoneyId,
                    'metadata' => $data
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => 'Payment failed',
                    'metadata' => $data
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayU payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? 'https://test.payu.in/merchant/postservice?form=2' : 'https://info.payu.in/merchant/postservice?form=2';
            
            // Prepare request data
            $requestData = [
                'key' => $this->config['key'],
                'command' => 'cancel_refund_transaction',
                'var1' => $transactionId, // PayU transaction ID
                'var2' => $amount ?? 0, // Refund amount
                'var3' => 'Refund requested by customer', // Refund reason
            ];
            
            // Generate hash
            $hashString = $this->config['key'] . '|' . $requestData['command'] . '|' . $requestData['var1'] . '|' . $this->config['salt'];
            $hash = strtolower(hash('sha512', $hashString));
            $requestData['hash'] = $hash;
            
            // Make API request
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($requestData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);
            
            // Parse response
            $responseData = json_decode($response, true);
            
            if (isset($responseData['status']) && $responseData['status'] === '1') {
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => $responseData['request_id'] ?? $transactionId,
                    'metadata' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $responseData['msg'] ?? 'Refund failed',
                    'metadata' => $responseData
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayU payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
}

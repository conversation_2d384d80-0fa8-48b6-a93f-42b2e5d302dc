<?php

namespace App\Services\Gateways\Stripe;

use App\Services\Gateways\AbstractGateway;
use Omnipay\Omnipay;
use Illuminate\Support\Facades\Log;

class StripeGateway extends AbstractGateway
{
    /**
     * Create a new StripeGateway instance.
     */
    public function __construct()
    {
        $this->gateway = Omnipay::create('Stripe');
    }
    
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
        $this->gateway->setApiKey($config['secret_key']);
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            // Create a payment intent
            $response = $this->gateway->createPaymentIntent([
                'amount' => $transaction['amount'],
                'currency' => $this->config['currency'] ?? 'usd',
                'description' => $transaction['description'] ?? 'Payment for order ' . ($transaction['order_id'] ?? ''),
                'metadata' => [
                    'transaction_id' => $transaction['transaction_id'],
                    'customer_id' => $transaction['customer_id'],
                    'order_id' => $transaction['order_id'] ?? null,
                ]
            ])->send();
            
            if ($response->isSuccessful()) {
                $paymentIntent = $response->getData();
                
                return [
                    'action' => 'payment/stripe',
                    'method' => 'POST',
                    'fields' => [
                        'client_secret' => $paymentIntent['client_secret'],
                        'publishable_key' => $this->config['publishable_key'],
                        'amount' => $transaction['amount'],
                        'currency' => $this->config['currency'] ?? 'usd',
                        'transaction_id' => $transaction['transaction_id'],
                        'customer_name' => $transaction['customer_name'] ?? '',
                        'customer_email' => $transaction['customer_email'] ?? '',
                        'success_url' => $transaction['success_url'],
                        'failure_url' => $transaction['failure_url'],
                    ]
                ];
            } else {
                Log::error('Stripe payment intent creation failed', [
                    'error' => $response->getMessage(),
                    'transaction_id' => $transaction['transaction_id']
                ]);
                
                throw new \Exception('Stripe payment intent creation failed: ' . $response->getMessage());
            }
        } catch (\Exception $e) {
            Log::error('Stripe payment intent creation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        try {
            // Confirm the payment intent
            $response = $this->gateway->confirmPaymentIntent([
                'paymentIntentReference' => $data['payment_intent'],
                'returnUrl' => $data['success_url'],
            ])->send();
            
            if ($response->isSuccessful()) {
                $paymentIntent = $response->getData();
                
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $paymentIntent['id'],
                    'metadata' => $paymentIntent
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $response->getMessage(),
                    'metadata' => $response->getData()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Stripe payment processing failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // Retrieve the payment intent
            $response = $this->gateway->fetchPaymentIntent([
                'paymentIntentReference' => $data['payment_intent'] ?? null
            ])->send();
            
            if ($response->isSuccessful()) {
                $paymentIntent = $response->getData();
                
                $status = 'pending';
                if ($paymentIntent['status'] === 'succeeded') {
                    $status = 'completed';
                } elseif (in_array($paymentIntent['status'], ['canceled', 'failed'])) {
                    $status = 'failed';
                }
                
                return [
                    'success' => $status === 'completed',
                    'status' => $status,
                    'gateway_transaction_id' => $paymentIntent['id'],
                    'metadata' => $paymentIntent
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $response->getMessage(),
                    'metadata' => $response->getData()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Stripe payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            $parameters = [
                'transactionReference' => $transactionId,
            ];
            
            if ($amount !== null) {
                $parameters['amount'] = $amount;
            }
            
            $response = $this->gateway->refund($parameters)->send();
            
            if ($response->isSuccessful()) {
                $refund = $response->getData();
                
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => $refund['id'],
                    'metadata' => $refund
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $response->getMessage(),
                    'metadata' => $response->getData()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Stripe payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
}

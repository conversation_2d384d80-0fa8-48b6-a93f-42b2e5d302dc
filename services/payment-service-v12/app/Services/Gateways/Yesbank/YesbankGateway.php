<?php

namespace App\Services\Gateways\Yesbank;

use App\Services\Gateways\AbstractGateway;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class YesbankGateway extends AbstractGateway
{
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? $this->config['test_url'] : $this->config['production_url'];
            
            // Prepare parameters
            $params = [
                'merchant_id' => $this->config['merchant_id'],
                'order_id' => $transaction['transaction_id'],
                'amount' => $transaction['amount'] * 100, // Amount in paise
                'currency' => 'INR',
                'redirect_url' => $transaction['success_url'],
                'cancel_url' => $transaction['failure_url'],
                'language' => 'en',
                'billing_name' => $transaction['customer_name'] ?? 'Customer',
                'billing_email' => $transaction['customer_email'] ?? '',
                'billing_tel' => $transaction['customer_phone'] ?? '',
                'billing_address' => '',
                'billing_city' => '',
                'billing_state' => '',
                'billing_zip' => '',
                'billing_country' => 'India',
                'delivery_name' => $transaction['customer_name'] ?? 'Customer',
                'delivery_address' => '',
                'delivery_city' => '',
                'delivery_state' => '',
                'delivery_zip' => '',
                'delivery_country' => 'India',
                'delivery_tel' => $transaction['customer_phone'] ?? '',
                'merchant_param1' => $transaction['transaction_id'],
                'merchant_param2' => $transaction['customer_id'] ?? '',
                'merchant_param3' => $transaction['order_id'] ?? '',
                'merchant_param4' => '',
                'merchant_param5' => '',
                'promo_code' => '',
                'customer_identifier' => $transaction['customer_id'] ?? '',
            ];
            
            // Generate checksum
            $checksum = $this->generateChecksum($params, $this->config['secret_key']);
            $params['checksum'] = $checksum;
            
            return [
                'action' => $url,
                'method' => 'POST',
                'fields' => $params
            ];
        } catch (\Exception $e) {
            Log::error('Yesbank payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        // Yesbank processes payment on their end and returns to success/failure URL
        // This method is not typically used for Yesbank
        return [
            'success' => false,
            'status' => 'pending',
            'message' => 'Payment is being processed by Yesbank'
        ];
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // Verify checksum
            $checksum = $data['checksum'] ?? '';
            $params = $data;
            unset($params['checksum']);
            
            $calculatedChecksum = $this->generateChecksum($params, $this->config['secret_key']);
            
            if ($checksum !== $calculatedChecksum) {
                Log::error('Yesbank payment verification failed: Invalid checksum', [
                    'transaction_id' => $transactionId,
                    'received_checksum' => $checksum,
                    'calculated_checksum' => $calculatedChecksum
                ]);
                
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => 'Invalid checksum'
                ];
            }
            
            // Check status
            $orderStatus = $data['order_status'] ?? '';
            
            if ($orderStatus === 'Success') {
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $data['bank_ref_no'] ?? $transactionId,
                    'metadata' => $data
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $data['failure_message'] ?? 'Payment failed',
                    'metadata' => $data
                ];
            }
        } catch (\Exception $e) {
            Log::error('Yesbank payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' 
                ? 'https://uat.yesbank.in/pg/api/refund' 
                : 'https://pg.yesbank.in/pg/api/refund';
            
            // Prepare parameters
            $params = [
                'merchant_id' => $this->config['merchant_id'],
                'order_id' => $transactionId,
                'reference_no' => 'REF' . time(),
                'refund_amount' => ($amount ?? 0) * 100, // Amount in paise
                'refund_ref_no' => 'REFUND' . time(),
            ];
            
            // Generate checksum
            $checksum = $this->generateChecksum($params, $this->config['secret_key']);
            $params['checksum'] = $checksum;
            
            // Make API request to process refund
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);
            
            // Parse response
            $responseData = json_decode($response, true);
            
            if (isset($responseData['status']) && $responseData['status'] === 'Success') {
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => $responseData['refund_ref_no'] ?? '',
                    'metadata' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $responseData['message'] ?? 'Refund failed',
                    'metadata' => $responseData
                ];
            }
        } catch (\Exception $e) {
            Log::error('Yesbank payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate checksum for Yesbank.
     *
     * @param array $params
     * @param string $key
     * @return string
     */
    private function generateChecksum(array $params, string $key): string
    {
        ksort($params);
        $paramString = '';
        
        foreach ($params as $param => $value) {
            if ($value !== '') {
                $paramString .= $param . '=' . $value . '&';
            }
        }
        
        $paramString = rtrim($paramString, '&');
        $paramString .= $key;
        
        return hash('sha512', $paramString);
    }
}

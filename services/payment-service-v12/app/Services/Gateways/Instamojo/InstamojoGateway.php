<?php

namespace App\Services\Gateways\Instamojo;

use App\Contracts\PaymentGatewayInterface;
use App\Services\Gateways\AbstractPaymentGateway;
use App\Exceptions\Payment\PaymentException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Instamojo Payment Gateway Implementation
 *
 * Handles payment processing through Instamojo gateway following
 * the existing PayU/Stripe patterns for OneFoodDialer 2025
 */
class InstamojoGateway extends AbstractPaymentGateway implements PaymentGatewayInterface
{
    /**
     * Gateway name identifier
     */
    protected string $gatewayName = 'instamojo';

    /**
     * Instamojo API endpoints
     */
    protected array $endpoints = [
        'test' => [
            'api_url' => 'https://test.instamojo.com/api/1.1/',
            'auth_url' => 'https://test.instamojo.com/oauth2/token/',
        ],
        'production' => [
            'api_url' => 'https://www.instamojo.com/api/1.1/',
            'auth_url' => 'https://www.instamojo.com/oauth2/token/',
        ]
    ];

    /**
     * Initialize the gateway with configuration
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void
    {
        $this->config = array_merge([
            'api_key' => '',
            'auth_token' => '',
            'client_id' => '',
            'client_secret' => '',
            'username' => '',
            'password' => '',
            'mode' => 'test',
            'currency' => 'INR',
        ], $config);

        $this->validateConfig();
    }

    /**
     * Validate gateway configuration
     *
     * @throws PaymentException
     */
    protected function validateConfig(): void
    {
        $required = ['api_key', 'auth_token'];

        foreach ($required as $field) {
            if (empty($this->config[$field])) {
                throw new PaymentException("Instamojo gateway configuration missing: {$field}");
            }
        }
    }
    
    /**
     * Create a payment request
     *
     * @param array $data
     * @return array
     * @throws PaymentException
     */
    public function createPayment(array $data): array
    {
        try {
            // Prepare payment request data
            $paymentData = $this->preparePaymentData($data);

            // Create payment request using HTTP client
            $response = $this->makeApiCall('payment-requests/', 'POST', $paymentData);

            if (!$response['success']) {
                throw new PaymentException('Failed to create Instamojo payment request: ' . $response['message']);
            }

            return [
                'success' => true,
                'payment_request_id' => $response['payment_request']['id'],
                'payment_url' => $response['payment_request']['longurl'],
                'status' => 'initiated',
                'gateway_response' => $response
            ];

        } catch (Exception $e) {
            Log::error('Instamojo payment creation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new PaymentException('Instamojo payment creation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            $paymentRequest = $this->createPayment($transaction);

            return [
                'action' => $paymentRequest['payment_url'],
                'method' => 'GET',
                'fields' => [
                    'transaction_id' => $transaction['transaction_id'],
                    'payment_request_id' => $paymentRequest['payment_request_id'],
                ]
            ];
        } catch (Exception $e) {
            Log::error('Instamojo payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id'] ?? 'unknown'
            ]);

            throw new PaymentException('Instamojo payment form data generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Make API call to Instamojo
     *
     * @param string $endpoint
     * @param string $method
     * @param array $data
     * @return array
     * @throws PaymentException
     */
    protected function makeApiCall(string $endpoint, string $method, array $data = []): array
    {
        try {
            $url = $this->endpoints[$this->config['mode']]['api_url'] . $endpoint;

            $headers = [
                'X-Api-Key' => $this->config['api_key'],
                'X-Auth-Token' => $this->config['auth_token'],
                'Content-Type' => 'application/x-www-form-urlencoded'
            ];

            $response = Http::withHeaders($headers)
                ->timeout(30)
                ->retry(3, 1000);

            if ($method === 'POST') {
                $response = $response->asForm()->post($url, $data);
            } else {
                $response = $response->get($url, $data);
            }

            if (!$response->successful()) {
                throw new PaymentException('Instamojo API call failed: ' . $response->body());
            }

            $responseData = $response->json();

            return [
                'success' => isset($responseData['success']) ? $responseData['success'] : true,
                'message' => $responseData['message'] ?? 'Success',
                ...$responseData
            ];

        } catch (Exception $e) {
            Log::error('Instamojo API call failed', [
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $e->getMessage()
            ]);

            throw new PaymentException('Instamojo API call failed: ' . $e->getMessage());
        }
    }

    /**
     * Prepare payment data for Instamojo
     *
     * @param array $data
     * @return array
     */
    protected function preparePaymentData(array $data): array
    {
        return [
            'purpose' => $data['description'] ?? 'Payment for order ' . ($data['order_id'] ?? ''),
            'amount' => $data['amount'],
            'phone' => $data['customer_phone'] ?? '',
            'buyer_name' => $data['customer_name'] ?? '',
            'redirect_url' => $data['success_url'] ?? '',
            'send_email' => true,
            'webhook' => route('api.payments.webhook', ['gateway' => 'instamojo']),
            'send_sms' => false,
            'email' => $data['customer_email'] ?? '',
            'allow_repeated_payments' => false
        ];
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        // Instamojo processes payment on their end and returns to redirect URL
        // This method is not typically used for Instamojo
        return [
            'success' => false,
            'status' => 'pending',
            'message' => 'Payment is being processed by Instamojo'
        ];
    }

    /**
     * Verify payment status
     *
     * @param string $paymentId
     * @return array
     * @throws PaymentException
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            $response = $this->makeApiCall("payments/{$paymentId}/", 'GET');

            if (!$response['success']) {
                throw new PaymentException('Failed to verify Instamojo payment: ' . $response['message']);
            }

            $payment = $response['payment'];

            return [
                'success' => true,
                'status' => $this->mapPaymentStatus($payment['status']),
                'gateway_transaction_id' => $payment['payment_id'],
                'amount' => $payment['amount'],
                'currency' => $payment['currency'],
                'gateway_response' => $response
            ];

        } catch (Exception $e) {
            Log::error('Instamojo payment verification failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            throw new PaymentException('Instamojo payment verification failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle webhook notification
     *
     * @param array $data
     * @return array
     * @throws PaymentException
     */
    public function handleWebhook(array $data): array
    {
        try {
            // Validate webhook data
            if (!isset($data['payment_id']) || !isset($data['payment_request_id'])) {
                throw new PaymentException('Invalid Instamojo webhook data');
            }

            // Verify payment status
            $verification = $this->verifyPayment($data['payment_id']);

            return [
                'success' => true,
                'payment_id' => $data['payment_id'],
                'payment_request_id' => $data['payment_request_id'],
                'status' => $verification['status'],
                'amount' => $verification['amount'],
                'gateway_response' => $verification['gateway_response']
            ];

        } catch (Exception $e) {
            Log::error('Instamojo webhook processing failed', [
                'webhook_data' => $data,
                'error' => $e->getMessage()
            ]);

            throw new PaymentException('Instamojo webhook processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Map Instamojo payment status to internal status
     *
     * @param string $status
     * @return string
     */
    protected function mapPaymentStatus(string $status): string
    {
        return match (strtolower($status)) {
            'credit' => 'completed',
            'failed' => 'failed',
            'pending' => 'pending',
            default => 'pending'
        };
    }
    
    /**
     * Refund a payment
     *
     * @param string $paymentId
     * @param float $amount
     * @param string $reason
     * @return array
     * @throws PaymentException
     */
    public function refundPayment(string $paymentId, float $amount, string $reason = ''): array
    {
        try {
            $refundData = [
                'payment_id' => $paymentId,
                'type' => $amount ? 'QP' : 'QFL', // Quick partial or full refund
                'body' => $reason ?: 'Refund requested',
            ];

            if ($amount) {
                $refundData['refund_amount'] = $amount;
            }

            $response = $this->makeApiCall('refunds/', 'POST', $refundData);

            if (!$response['success']) {
                throw new PaymentException('Failed to create Instamojo refund: ' . $response['message']);
            }

            return [
                'success' => true,
                'refund_id' => $response['refund']['id'],
                'status' => 'pending',
                'amount' => $amount,
                'gateway_response' => $response
            ];

        } catch (Exception $e) {
            Log::error('Instamojo refund failed', [
                'payment_id' => $paymentId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            throw new PaymentException('Instamojo refund failed: ' . $e->getMessage());
        }
    }
}

<?php

namespace App\Services\Gateways\Paytm;

use App\Services\Gateways\AbstractGateway;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaytmGateway extends AbstractGateway
{
    /**
     * Set the gateway configuration.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void
    {
        parent::setConfig($config);
    }
    
    /**
     * Get the form data for the payment gateway.
     *
     * @param array $transaction
     * @return array
     */
    public function getFormData(array $transaction): array
    {
        try {
            // Prepare parameters
            $params = [
                'MID' => $this->config['merchant_id'],
                'ORDER_ID' => $transaction['transaction_id'],
                'CUST_ID' => $transaction['customer_id'],
                'INDUSTRY_TYPE_ID' => $this->config['industry_type'],
                'CHANNEL_ID' => $this->config['channel'],
                'TXN_AMOUNT' => $transaction['amount'],
                'WEBSITE' => $this->config['website'],
                'CALLBACK_URL' => route('api.payments.callback'),
                'EMAIL' => $transaction['customer_email'] ?? '',
                'MOBILE_NO' => $transaction['customer_phone'] ?? '',
            ];
            
            // Generate checksum
            $checksum = $this->generateChecksum($params, $this->config['merchant_key']);
            $params['CHECKSUMHASH'] = $checksum;
            
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' ? $this->config['test_url'] : $this->config['production_url'];
            
            return [
                'action' => $url,
                'method' => 'POST',
                'fields' => $params
            ];
        } catch (\Exception $e) {
            Log::error('Paytm payment form data generation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction['transaction_id']
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a payment.
     *
     * @param array $data
     * @return array
     */
    public function processPayment(array $data): array
    {
        // Paytm processes payment on their end and returns to callback URL
        // This method is not typically used for Paytm
        return [
            'success' => false,
            'status' => 'pending',
            'message' => 'Payment is being processed by Paytm'
        ];
    }
    
    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $data
     * @return array
     */
    public function verifyPayment(string $transactionId, array $data = []): array
    {
        try {
            // Verify checksum
            $checksum = $data['CHECKSUMHASH'] ?? '';
            $params = $data;
            unset($params['CHECKSUMHASH']);
            
            $isValidChecksum = $this->verifyChecksum($params, $this->config['merchant_key'], $checksum);
            
            if (!$isValidChecksum) {
                Log::error('Paytm payment verification failed: Invalid checksum', [
                    'transaction_id' => $transactionId,
                    'data' => $data
                ]);
                
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => 'Invalid checksum'
                ];
            }
            
            // Check transaction status
            $params = [
                'MID' => $this->config['merchant_id'],
                'ORDERID' => $transactionId,
                'CHECKSUMHASH' => $this->generateChecksum([
                    'MID' => $this->config['merchant_id'],
                    'ORDERID' => $transactionId
                ], $this->config['merchant_key'])
            ];
            
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' 
                ? 'https://securegw-stage.paytm.in/order/status' 
                : 'https://securegw.paytm.in/order/status';
            
            // Make API request to verify transaction status
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($ch);
            curl_close($ch);
            
            // Parse response
            $responseData = json_decode($response, true);
            
            if (isset($responseData['STATUS']) && $responseData['STATUS'] === 'TXN_SUCCESS') {
                return [
                    'success' => true,
                    'status' => 'completed',
                    'gateway_transaction_id' => $responseData['TXNID'] ?? $transactionId,
                    'metadata' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $responseData['RESPMSG'] ?? 'Transaction failed',
                    'metadata' => $responseData
                ];
            }
        } catch (\Exception $e) {
            Log::error('Paytm payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'data' => $data
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            // Prepare parameters
            $params = [
                'MID' => $this->config['merchant_id'],
                'ORDERID' => $transactionId,
                'REFID' => 'REFUND_' . time(),
                'TXNTYPE' => 'REFUND',
                'REFUNDAMOUNT' => $amount ?? '',
                'CHECKSUM' => ''
            ];
            
            // Generate checksum
            $checksum = $this->generateChecksum($params, $this->config['merchant_key']);
            $params['CHECKSUM'] = $checksum;
            
            // Determine the URL based on mode
            $url = $this->config['mode'] === 'test' 
                ? 'https://securegw-stage.paytm.in/refund/process' 
                : 'https://securegw.paytm.in/refund/process';
            
            // Make API request to process refund
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($ch);
            curl_close($ch);
            
            // Parse response
            $responseData = json_decode($response, true);
            
            if (isset($responseData['STATUS']) && $responseData['STATUS'] === 'PENDING') {
                return [
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => $responseData['REFUNDID'] ?? '',
                    'metadata' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'status' => 'failed',
                    'message' => $responseData['RESPMSG'] ?? 'Refund failed',
                    'metadata' => $responseData
                ];
            }
        } catch (\Exception $e) {
            Log::error('Paytm payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            return [
                'success' => false,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate checksum for Paytm.
     *
     * @param array $params
     * @param string $key
     * @return string
     */
    private function generateChecksum(array $params, string $key): string
    {
        ksort($params);
        $paramString = '';
        
        foreach ($params as $param => $value) {
            if ($value !== '') {
                $paramString .= $param . '=' . $value . '&';
            }
        }
        
        $paramString = rtrim($paramString, '&');
        $finalString = $paramString . '|' . $key;
        
        $hash = hash('sha256', $finalString);
        $checksum = $hash . $this->encrypt($hash, $key);
        
        return $checksum;
    }
    
    /**
     * Verify checksum for Paytm.
     *
     * @param array $params
     * @param string $key
     * @param string $checksum
     * @return bool
     */
    private function verifyChecksum(array $params, string $key, string $checksum): bool
    {
        ksort($params);
        $paramString = '';
        
        foreach ($params as $param => $value) {
            if ($value !== '') {
                $paramString .= $param . '=' . $value . '&';
            }
        }
        
        $paramString = rtrim($paramString, '&');
        $finalString = $paramString . '|' . $key;
        
        $hash = hash('sha256', $finalString);
        $calculatedChecksum = $hash . $this->encrypt($hash, $key);
        
        return $calculatedChecksum === $checksum;
    }
    
    /**
     * Encrypt data for Paytm.
     *
     * @param string $data
     * @param string $key
     * @return string
     */
    private function encrypt(string $data, string $key): string
    {
        $key = html_entity_decode($key);
        $iv = '@@@@&&&&####$$$$';
        $data = openssl_encrypt($data, 'AES-128-CBC', $key, 0, $iv);
        return $data;
    }
}

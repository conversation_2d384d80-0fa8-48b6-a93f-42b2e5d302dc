<?php

namespace App\Services\Gateways\Paytm;

/**
 * Paytm Checksum Utility Class
 * 
 * Handles checksum generation and verification for Paytm payments
 * following the official Paytm PHP SDK patterns
 */
class PaytmChecksum
{
    /**
     * Generate signature/checksum for Paytm
     *
     * @param array $params
     * @param string $key
     * @return string
     */
    public static function generateSignature(array $params, string $key): string
    {
        if (!is_array($params) || !$key) {
            throw new \InvalidArgumentException('Invalid parameters for checksum generation');
        }

        $params = self::removeChecksum($params);
        ksort($params);
        
        $arrayStringified = self::getStringByParams($params);
        return self::generateSignatureByString($arrayStringified, $key);
    }

    /**
     * Verify signature/checksum for Paytm
     *
     * @param array $params
     * @param string $key
     * @param string $checksum
     * @return bool
     */
    public static function verifySignature(array $params, string $key, string $checksum): bool
    {
        if (!is_array($params) || !$key || !$checksum) {
            return false;
        }

        $params = self::removeChecksum($params);
        ksort($params);
        
        $arrayStringified = self::getStringByParams($params);
        $generatedChecksum = self::generateSignatureByString($arrayStringified, $key);
        
        return $generatedChecksum === $checksum;
    }

    /**
     * Generate signature by string
     *
     * @param string $params
     * @param string $key
     * @return string
     */
    private static function generateSignatureByString(string $params, string $key): string
    {
        $salt = self::generateRandomString(4);
        return self::calculateChecksum($params, $key, $salt);
    }

    /**
     * Calculate checksum
     *
     * @param string $params
     * @param string $key
     * @param string $salt
     * @return string
     */
    private static function calculateChecksum(string $params, string $key, string $salt): string
    {
        $finalString = $params . "|" . $salt;
        $hash = hash("sha256", $finalString);
        $hashString = $hash . $salt;
        $checksum = self::encrypt($hashString, $key);
        return $checksum;
    }

    /**
     * Encrypt data using AES-128-CBC
     *
     * @param string $input
     * @param string $key
     * @return string
     */
    private static function encrypt(string $input, string $key): string
    {
        $key = html_entity_decode($key);
        
        if (function_exists("openssl_encrypt")) {
            $data = openssl_encrypt($input, "AES-128-CBC", $key, 0, "@@@@&&&&####$$$$");
        } else {
            $size = mcrypt_get_block_size(MCRYPT_RIJNDAEL_128, 'cbc');
            $input = self::pkcs5Pad($input, $size);
            $td = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', 'cbc', '');
            $iv = "@@@@&&&&####$$$$";
            mcrypt_generic_init($td, $key, $iv);
            $data = mcrypt_generic($td, $input);
            mcrypt_generic_deinit($td);
            mcrypt_module_close($td);
            $data = base64_encode($data);
        }
        
        return $data;
    }

    /**
     * Decrypt data using AES-128-CBC
     *
     * @param string $encrypted
     * @param string $key
     * @return string
     */
    private static function decrypt(string $encrypted, string $key): string
    {
        $key = html_entity_decode($key);
        
        if (function_exists("openssl_decrypt")) {
            $data = openssl_decrypt($encrypted, "AES-128-CBC", $key, 0, "@@@@&&&&####$$$$");
        } else {
            $encrypted = base64_decode($encrypted);
            $td = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', 'cbc', '');
            $iv = "@@@@&&&&####$$$$";
            mcrypt_generic_init($td, $key, $iv);
            $data = mdecrypt_generic($td, $encrypted);
            mcrypt_generic_deinit($td);
            mcrypt_module_close($td);
            $data = self::pkcs5Unpad($data);
        }
        
        return $data;
    }

    /**
     * Generate random string
     *
     * @param int $length
     * @return string
     */
    private static function generateRandomString(int $length): string
    {
        $random = "";
        srand((double) microtime() * 1000000);

        $data = "9876543210ZYXWVUTSRQPONMLKJIHGFEDCBAabcdefghijklmnopqrstuvwxyz!@#$&_";

        for ($i = 0; $i < $length; $i++) {
            $random .= substr($data, (rand() % (strlen($data))), 1);
        }

        return $random;
    }

    /**
     * Convert params array to string
     *
     * @param array $params
     * @return string
     */
    private static function getStringByParams(array $params): string
    {
        $params = self::removeChecksum($params);
        ksort($params);
        
        $stringified = "";
        $i = 0;
        
        foreach ($params as $key => $value) {
            if (strlen($value) > 0) {
                if ($i == 0) {
                    $stringified .= $key . "=" . $value;
                } else {
                    $stringified .= "&" . $key . "=" . $value;
                }
                $i++;
            }
        }
        
        return $stringified;
    }

    /**
     * Remove checksum from params
     *
     * @param array $params
     * @return array
     */
    private static function removeChecksum(array $params): array
    {
        if (isset($params["CHECKSUMHASH"])) {
            unset($params["CHECKSUMHASH"]);
        }
        return $params;
    }

    /**
     * PKCS5 padding
     *
     * @param string $text
     * @param int $blocksize
     * @return string
     */
    private static function pkcs5Pad(string $text, int $blocksize): string
    {
        $pad = $blocksize - (strlen($text) % $blocksize);
        return $text . str_repeat(chr($pad), $pad);
    }

    /**
     * PKCS5 unpadding
     *
     * @param string $text
     * @return string
     */
    private static function pkcs5Unpad(string $text): string
    {
        $pad = ord($text[strlen($text) - 1]);
        if ($pad > strlen($text)) {
            return false;
        }
        
        if (strspn($text, chr($pad), strlen($text) - $pad) != $pad) {
            return false;
        }
        
        return substr($text, 0, -1 * $pad);
    }

    /**
     * Verify checksum by string
     *
     * @param string $params
     * @param string $key
     * @param string $checksum
     * @return bool
     */
    public static function verifySignatureByString(string $params, string $key, string $checksum): bool
    {
        $paytmHash = self::decrypt($checksum, $key);
        $salt = substr($paytmHash, -4);
        $calculatedChecksum = self::calculateChecksum($params, $key, $salt);
        return $calculatedChecksum === $checksum;
    }

    /**
     * Get transaction token for Paytm
     *
     * @param array $params
     * @param string $key
     * @param string $mid
     * @return string|false
     */
    public static function getTransactionToken(array $params, string $key, string $mid)
    {
        // This would typically make an API call to Paytm to get transaction token
        // Implementation depends on Paytm's latest API requirements
        return false;
    }
}

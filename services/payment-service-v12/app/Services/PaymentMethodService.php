<?php

namespace App\Services;

use App\Models\PaymentMethod;
use App\Exceptions\Payment\PaymentException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentMethodService
{
    /**
     * Get all payment methods for a customer.
     *
     * @param int $customerId
     * @param bool $activeOnly
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCustomerPaymentMethods(int $customerId, bool $activeOnly = true)
    {
        $query = PaymentMethod::forCustomer($customerId);
        
        if ($activeOnly) {
            $query->active();
        }
        
        return $query->orderBy('is_default', 'desc')->get();
    }
    
    /**
     * Get a payment method by ID.
     *
     * @param int $id
     * @param int|null $customerId
     * @return PaymentMethod
     * @throws PaymentException
     */
    public function getPaymentMethod(int $id, int $customerId = null): PaymentMethod
    {
        $query = PaymentMethod::where('id', $id);
        
        if ($customerId !== null) {
            $query->where('customer_id', $customerId);
        }
        
        $paymentMethod = $query->first();
        
        if (!$paymentMethod) {
            throw new PaymentException("Payment method not found");
        }
        
        return $paymentMethod;
    }
    
    /**
     * Get the default payment method for a customer.
     *
     * @param int $customerId
     * @return PaymentMethod|null
     */
    public function getDefaultPaymentMethod(int $customerId): ?PaymentMethod
    {
        return PaymentMethod::forCustomer($customerId)
            ->active()
            ->default()
            ->first();
    }
    
    /**
     * Create a new payment method.
     *
     * @param array $data
     * @return PaymentMethod
     * @throws PaymentException
     */
    public function createPaymentMethod(array $data): PaymentMethod
    {
        try {
            DB::beginTransaction();
            
            // If this is the first payment method or is_default is true, make it the default
            $isDefault = $data['is_default'] ?? false;
            
            if ($isDefault) {
                // Remove default flag from other payment methods
                PaymentMethod::forCustomer($data['customer_id'])
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            } else {
                // Check if this is the first payment method
                $count = PaymentMethod::forCustomer($data['customer_id'])->count();
                
                if ($count === 0) {
                    $isDefault = true;
                }
            }
            
            // Create the payment method
            $paymentMethod = PaymentMethod::create(array_merge($data, [
                'is_default' => $isDefault,
            ]));
            
            DB::commit();
            
            return $paymentMethod;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment method creation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            throw new PaymentException('Payment method creation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Update a payment method.
     *
     * @param int $id
     * @param array $data
     * @param int|null $customerId
     * @return PaymentMethod
     * @throws PaymentException
     */
    public function updatePaymentMethod(int $id, array $data, int $customerId = null): PaymentMethod
    {
        try {
            DB::beginTransaction();
            
            $paymentMethod = $this->getPaymentMethod($id, $customerId);
            
            // If setting as default, remove default flag from other payment methods
            if (isset($data['is_default']) && $data['is_default'] && !$paymentMethod->is_default) {
                PaymentMethod::forCustomer($paymentMethod->customer_id)
                    ->where('id', '!=', $id)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }
            
            $paymentMethod->update($data);
            
            DB::commit();
            
            return $paymentMethod;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment method update failed', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            
            throw new PaymentException('Payment method update failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete a payment method.
     *
     * @param int $id
     * @param int|null $customerId
     * @return bool
     * @throws PaymentException
     */
    public function deletePaymentMethod(int $id, int $customerId = null): bool
    {
        try {
            DB::beginTransaction();
            
            $paymentMethod = $this->getPaymentMethod($id, $customerId);
            
            // If this is the default payment method, set another one as default
            if ($paymentMethod->is_default) {
                $newDefault = PaymentMethod::forCustomer($paymentMethod->customer_id)
                    ->active()
                    ->where('id', '!=', $id)
                    ->first();
                
                if ($newDefault) {
                    $newDefault->update(['is_default' => true]);
                }
            }
            
            $paymentMethod->delete();
            
            DB::commit();
            
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment method deletion failed', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            throw new PaymentException('Payment method deletion failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Set a payment method as the default.
     *
     * @param int $id
     * @param int|null $customerId
     * @return PaymentMethod
     * @throws PaymentException
     */
    public function setDefaultPaymentMethod(int $id, int $customerId = null): PaymentMethod
    {
        try {
            DB::beginTransaction();
            
            $paymentMethod = $this->getPaymentMethod($id, $customerId);
            
            // Remove default flag from other payment methods
            PaymentMethod::forCustomer($paymentMethod->customer_id)
                ->where('id', '!=', $id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
            
            // Set this payment method as default
            $paymentMethod->update(['is_default' => true]);
            
            DB::commit();
            
            return $paymentMethod;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Setting default payment method failed', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            
            throw new PaymentException('Setting default payment method failed: ' . $e->getMessage());
        }
    }
}

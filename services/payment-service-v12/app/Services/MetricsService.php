<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * MetricsService for OneFoodDialer 2025
 * 
 * Handles Prometheus metrics collection and reporting
 */
class MetricsService
{
    protected array $metrics = [];
    protected string $cachePrefix = 'metrics:';
    protected int $cacheTtl = 300; // 5 minutes

    /**
     * Record a payment processing time metric.
     *
     * @param string $gateway
     * @param float $duration
     * @param string $status
     * @return void
     */
    public function recordPaymentProcessingTime(string $gateway, float $duration, string $status): void
    {
        $this->recordHistogram('payment_processing_duration_seconds', $duration, [
            'gateway' => $gateway,
            'status' => $status
        ]);

        Log::debug('Payment processing time recorded', [
            'gateway' => $gateway,
            'duration' => $duration,
            'status' => $status
        ]);
    }

    /**
     * Record a wallet operation metric.
     *
     * @param string $operation
     * @param float $duration
     * @param string $status
     * @return void
     */
    public function recordWalletOperation(string $operation, float $duration, string $status): void
    {
        $this->recordHistogram('wallet_operation_duration_seconds', $duration, [
            'operation' => $operation,
            'status' => $status
        ]);

        $this->incrementCounter('wallet_operations_total', [
            'operation' => $operation,
            'status' => $status
        ]);
    }

    /**
     * Record API response time.
     *
     * @param string $endpoint
     * @param string $method
     * @param float $duration
     * @param int $statusCode
     * @return void
     */
    public function recordApiResponseTime(string $endpoint, string $method, float $duration, int $statusCode): void
    {
        $this->recordHistogram('api_request_duration_seconds', $duration, [
            'endpoint' => $endpoint,
            'method' => $method,
            'status_code' => (string) $statusCode
        ]);

        $this->incrementCounter('api_requests_total', [
            'endpoint' => $endpoint,
            'method' => $method,
            'status_code' => (string) $statusCode
        ]);
    }

    /**
     * Record invoice generation time.
     *
     * @param string $type
     * @param float $duration
     * @param string $status
     * @return void
     */
    public function recordInvoiceGeneration(string $type, float $duration, string $status): void
    {
        $this->recordHistogram('invoice_generation_duration_seconds', $duration, [
            'type' => $type,
            'status' => $status
        ]);

        $this->incrementCounter('invoices_generated_total', [
            'type' => $type,
            'status' => $status
        ]);
    }

    /**
     * Record PDF generation time.
     *
     * @param float $duration
     * @param string $status
     * @return void
     */
    public function recordPdfGeneration(float $duration, string $status): void
    {
        $this->recordHistogram('pdf_generation_duration_seconds', $duration, [
            'status' => $status
        ]);

        $this->incrementCounter('pdfs_generated_total', [
            'status' => $status
        ]);
    }

    /**
     * Record error rate.
     *
     * @param string $service
     * @param string $operation
     * @param string $errorType
     * @return void
     */
    public function recordError(string $service, string $operation, string $errorType): void
    {
        $this->incrementCounter('errors_total', [
            'service' => $service,
            'operation' => $operation,
            'error_type' => $errorType
        ]);
    }

    /**
     * Record service health status.
     *
     * @param string $service
     * @param bool $healthy
     * @return void
     */
    public function recordServiceHealth(string $service, bool $healthy): void
    {
        $this->setGauge('service_health', $healthy ? 1 : 0, [
            'service' => $service
        ]);
    }

    /**
     * Record database connection status.
     *
     * @param string $connection
     * @param bool $connected
     * @return void
     */
    public function recordDatabaseHealth(string $connection, bool $connected): void
    {
        $this->setGauge('database_health', $connected ? 1 : 0, [
            'connection' => $connection
        ]);
    }

    /**
     * Record external service dependency status.
     *
     * @param string $service
     * @param bool $available
     * @param float $responseTime
     * @return void
     */
    public function recordExternalServiceHealth(string $service, bool $available, float $responseTime): void
    {
        $this->setGauge('external_service_health', $available ? 1 : 0, [
            'service' => $service
        ]);

        $this->recordHistogram('external_service_response_time_seconds', $responseTime, [
            'service' => $service
        ]);
    }

    /**
     * Get metrics in Prometheus format.
     *
     * @return string
     */
    public function getPrometheusMetrics(): string
    {
        $output = [];
        $processedMetrics = [];

        // Get all metrics from cache
        $cacheKeys = Cache::get($this->cachePrefix . 'keys', []);

        foreach ($cacheKeys as $cacheKey) {
            $data = Cache::get($cacheKey);
            if ($data !== null) {
                $metricName = $this->extractMetricNameFromCacheKey($cacheKey);
                $labels = $this->extractLabelsFromCacheKey($cacheKey);

                if (!isset($processedMetrics[$metricName])) {
                    $processedMetrics[$metricName] = [];
                }

                $processedMetrics[$metricName][] = [
                    'labels' => $labels,
                    'data' => $data
                ];
            }
        }

        // Format metrics for Prometheus
        foreach ($processedMetrics as $name => $metrics) {
            $output[] = $this->formatPrometheusMetric($name, $metrics);
        }

        return implode("\n", $output);
    }

    /**
     * Record a histogram metric.
     *
     * @param string $name
     * @param float $value
     * @param array $labels
     * @return void
     */
    protected function recordHistogram(string $name, float $value, array $labels = []): void
    {
        $key = $this->getMetricKey($name, $labels);
        $cacheKey = $this->cachePrefix . 'histogram:' . $key;

        $data = Cache::get($cacheKey, [
            'count' => 0,
            'sum' => 0,
            'buckets' => []
        ]);

        $data['count']++;
        $data['sum'] += $value;

        // Define histogram buckets
        $buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10];
        foreach ($buckets as $bucket) {
            if ($value <= $bucket) {
                $data['buckets'][$bucket] = ($data['buckets'][$bucket] ?? 0) + 1;
            }
        }

        Cache::put($cacheKey, $data, $this->cacheTtl);
        $this->addCacheKey($cacheKey);
        $this->metrics[$name] = $data;
    }

    /**
     * Increment a counter metric.
     *
     * @param string $name
     * @param array $labels
     * @return void
     */
    protected function incrementCounter(string $name, array $labels = []): void
    {
        $key = $this->getMetricKey($name, $labels);
        $cacheKey = $this->cachePrefix . 'counter:' . $key;

        $value = Cache::get($cacheKey, 0) + 1;
        Cache::put($cacheKey, $value, $this->cacheTtl);
        $this->addCacheKey($cacheKey);
        $this->metrics[$name] = $value;
    }

    /**
     * Set a gauge metric.
     *
     * @param string $name
     * @param float $value
     * @param array $labels
     * @return void
     */
    protected function setGauge(string $name, float $value, array $labels = []): void
    {
        $key = $this->getMetricKey($name, $labels);
        $cacheKey = $this->cachePrefix . 'gauge:' . $key;

        Cache::put($cacheKey, $value, $this->cacheTtl);
        $this->addCacheKey($cacheKey);
        $this->metrics[$name] = $value;
    }

    /**
     * Generate a metric key from name and labels.
     *
     * @param string $name
     * @param array $labels
     * @return string
     */
    protected function getMetricKey(string $name, array $labels): string
    {
        ksort($labels);
        $labelString = '';
        
        if (!empty($labels)) {
            $labelPairs = [];
            foreach ($labels as $key => $value) {
                $labelPairs[] = $key . '="' . $value . '"';
            }
            $labelString = '{' . implode(',', $labelPairs) . '}';
        }

        return $name . $labelString;
    }

    /**
     * Format metric for Prometheus output.
     *
     * @param string $name
     * @param array $metrics
     * @return string
     */
    protected function formatPrometheusMetric(string $name, array $metrics): string
    {
        $output = [];
        $metricType = $this->getMetricType($metrics[0]['data'] ?? []);

        $output[] = "# TYPE {$name} {$metricType}";

        foreach ($metrics as $metric) {
            $labels = $metric['labels'];
            $data = $metric['data'];
            $labelString = $this->formatLabels($labels);

            if (is_array($data) && isset($data['count'])) {
                // Histogram
                $output[] = "{$name}_count{$labelString} {$data['count']}";
                $output[] = "{$name}_sum{$labelString} {$data['sum']}";

                foreach ($data['buckets'] ?? [] as $bucket => $count) {
                    $bucketLabels = $labels;
                    $bucketLabels['le'] = (string) $bucket;
                    $bucketLabelString = $this->formatLabels($bucketLabels);
                    $output[] = "{$name}_bucket{$bucketLabelString} {$count}";
                }
            } else {
                // Counter or Gauge
                $output[] = "{$name}{$labelString} {$data}";
            }
        }

        return implode("\n", $output);
    }

    /**
     * Add cache key to tracking list.
     *
     * @param string $cacheKey
     * @return void
     */
    protected function addCacheKey(string $cacheKey): void
    {
        $keys = Cache::get($this->cachePrefix . 'keys', []);
        if (!in_array($cacheKey, $keys)) {
            $keys[] = $cacheKey;
            Cache::put($this->cachePrefix . 'keys', $keys, $this->cacheTtl);
        }
    }

    /**
     * Extract metric name from cache key.
     *
     * @param string $cacheKey
     * @return string
     */
    protected function extractMetricNameFromCacheKey(string $cacheKey): string
    {
        $key = str_replace($this->cachePrefix, '', $cacheKey);
        $parts = explode(':', $key, 2);

        if (count($parts) < 2) {
            return $key;
        }

        $metricPart = $parts[1];
        $bracketPos = strpos($metricPart, '{');

        return $bracketPos !== false ? substr($metricPart, 0, $bracketPos) : $metricPart;
    }

    /**
     * Extract labels from cache key.
     *
     * @param string $cacheKey
     * @return array
     */
    protected function extractLabelsFromCacheKey(string $cacheKey): array
    {
        $key = str_replace($this->cachePrefix, '', $cacheKey);
        $parts = explode(':', $key, 2);

        if (count($parts) < 2) {
            return [];
        }

        $metricPart = $parts[1];
        $bracketPos = strpos($metricPart, '{');

        if ($bracketPos === false) {
            return [];
        }

        $labelString = substr($metricPart, $bracketPos + 1, -1);
        $labels = [];

        if (!empty($labelString)) {
            $labelPairs = explode(',', $labelString);
            foreach ($labelPairs as $pair) {
                if (strpos($pair, '=') !== false) {
                    [$key, $value] = explode('=', $pair, 2);
                    $labels[trim($key)] = trim($value, '"');
                }
            }
        }

        return $labels;
    }

    /**
     * Format labels for Prometheus output.
     *
     * @param array $labels
     * @return string
     */
    protected function formatLabels(array $labels): string
    {
        if (empty($labels)) {
            return '';
        }

        ksort($labels);
        $labelPairs = [];

        foreach ($labels as $key => $value) {
            $labelPairs[] = $key . '="' . $value . '"';
        }

        return '{' . implode(',', $labelPairs) . '}';
    }

    /**
     * Get metric type from data.
     *
     * @param mixed $data
     * @return string
     */
    protected function getMetricType($data): string
    {
        if (is_array($data) && isset($data['count'])) {
            return 'histogram';
        }

        return 'counter'; // Default to counter for simplicity
    }
}

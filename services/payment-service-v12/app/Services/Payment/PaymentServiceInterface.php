<?php

namespace App\Services\Payment;

use App\Models\Payment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface PaymentServiceInterface
{
    /**
     * Get all payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getAllPayments(array $filters = []): Collection;

    /**
     * Get paginated payments.
     *
     * @param int $perPage
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getPaginatedPayments(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get payment by ID.
     *
     * @param int $id
     * @return Payment|null
     */
    public function getPaymentById(int $id): ?Payment;

    /**
     * Get payment by transaction ID.
     *
     * @param string $transactionId
     * @return Payment|null
     */
    public function getPaymentByTransactionId(string $transactionId): ?Payment;

    /**
     * Get payments by order ID.
     *
     * @param int $orderId
     * @return Collection
     */
    public function getPaymentsByOrderId(int $orderId): Collection;

    /**
     * Get payments by customer ID.
     *
     * @param int $customerId
     * @return Collection
     */
    public function getPaymentsByCustomerId(int $customerId): Collection;

    /**
     * Process a payment.
     *
     * @param array $paymentData
     * @return array
     */
    public function processPayment(array $paymentData): array;

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function verifyPayment(string $transactionId, array $additionalData = []): array;

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @param array $additionalData
     * @return array
     */
    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array;

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function cancelPayment(string $transactionId, array $additionalData = []): array;

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array;

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentDetails(string $transactionId): array;

    /**
     * Generate payment form.
     *
     * @param array $paymentData
     * @return array
     */
    public function generatePaymentForm(array $paymentData): array;

    /**
     * Handle payment webhook.
     *
     * @param string $gateway
     * @param array $data
     * @return array
     */
    public function handleWebhook(string $gateway, array $data): array;

    /**
     * Get available payment gateways.
     *
     * @return array
     */
    public function getAvailableGateways(): array;

    /**
     * Get payment gateway.
     *
     * @param string $gateway
     * @return mixed
     */
    public function getGateway(string $gateway);

    /**
     * Get payment statistics.
     *
     * @param array $filters
     * @return array
     */
    public function getPaymentStatistics(array $filters = []): array;
}

<?php

namespace App\Services\Payment\Gateway;

use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Refund;
use Stripe\Exception\ApiErrorException;

class StripeGateway extends AbstractPaymentGateway
{
    /**
     * @var string
     */
    protected $name = 'stripe';

    /**
     * @var string
     */
    protected $apiKey;

    /**
     * @var string
     */
    protected $secretKey;

    /**
     * @var string
     */
    protected $webhookSecret;

    /**
     * Initialize the payment gateway.
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->apiKey = $config['api_key'] ?? '';
        $this->secretKey = $config['secret_key'] ?? '';
        $this->webhookSecret = $config['webhook_secret'] ?? '';
        
        // Set Stripe API key
        Stripe::setApiKey($this->secretKey);
        
        // Set supported currencies
        $this->supportedCurrencies = ['USD', 'EUR', 'GBP', 'INR', 'AUD', 'CAD', 'SGD', 'MYR'];
        
        // Set supported methods
        $this->supportedMethods = ['credit_card', 'debit_card', 'alipay', 'wechat', 'sepa', 'ideal', 'sofort', 'giropay', 'bancontact'];
    }

    /**
     * Process a payment.
     *
     * @param array $paymentData
     * @return array
     */
    public function processPayment(array $paymentData): array
    {
        try {
            // Validate payment data
            $errors = $this->validatePaymentData($paymentData);
            
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'errors' => $errors,
                    'gateway' => $this->getName(),
                ];
            }

            // Generate transaction ID if not provided
            $transactionId = $paymentData['transaction_id'] ?? $this->generateTransactionId('STRIPE');

            // Format amount for Stripe (in cents)
            $amount = $this->formatAmount((float) $paymentData['amount'], $paymentData['currency']);

            // Prepare metadata
            $metadata = [
                'order_id' => $paymentData['order_id'],
                'customer_id' => $paymentData['customer_id'],
                'transaction_id' => $transactionId,
            ];

            if (isset($paymentData['company_id'])) {
                $metadata['company_id'] = $paymentData['company_id'];
            }

            if (isset($paymentData['unit_id'])) {
                $metadata['unit_id'] = $paymentData['unit_id'];
            }

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount,
                'currency' => strtolower($paymentData['currency']),
                'payment_method_types' => ['card'],
                'description' => $paymentData['product_info'] ?? "Order #{$paymentData['order_id']}",
                'metadata' => $metadata,
                'receipt_email' => $paymentData['customer_email'],
            ]);

            // Log payment activity
            $this->logPaymentActivity('process_payment', [
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $amount,
                'currency' => $paymentData['currency'],
                'metadata' => $metadata,
            ], $transactionId);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'gateway' => $this->getName(),
                'redirect' => false,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'publishable_key' => $this->apiKey,
            ];
        } catch (ApiErrorException $e) {
            return $this->handlePaymentError('process_payment', $e);
        } catch (\Exception $e) {
            return $this->handlePaymentError('process_payment', $e);
        }
    }

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function verifyPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Get payment intent ID from additional data
            $paymentIntentId = $additionalData['payment_intent_id'] ?? null;
            
            if (!$paymentIntentId) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'Payment intent ID is required',
                    'gateway' => $this->getName(),
                ];
            }

            // Retrieve payment intent
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            // Log payment activity
            $this->logPaymentActivity('verify_payment', [
                'payment_intent_id' => $paymentIntentId,
                'status' => $paymentIntent->status,
            ], $transactionId);

            // Map Stripe status to our status
            $status = $this->mapStripeStatus($paymentIntent->status);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => $status,
                'gateway' => $this->getName(),
                'gateway_response' => [
                    'payment_intent_id' => $paymentIntent->id,
                    'status' => $paymentIntent->status,
                    'amount' => $paymentIntent->amount / 100, // Convert from cents
                    'currency' => $paymentIntent->currency,
                    'payment_method' => $paymentIntent->payment_method,
                ],
            ];
        } catch (ApiErrorException $e) {
            return $this->handlePaymentError('verify_payment', $e, $transactionId);
        } catch (\Exception $e) {
            return $this->handlePaymentError('verify_payment', $e, $transactionId);
        }
    }

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @param array $additionalData
     * @return array
     */
    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array
    {
        try {
            // Get payment intent ID from additional data
            $paymentIntentId = $additionalData['payment_intent_id'] ?? null;
            
            if (!$paymentIntentId) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'Payment intent ID is required',
                    'gateway' => $this->getName(),
                ];
            }

            // Prepare refund data
            $refundData = [
                'payment_intent' => $paymentIntentId,
                'metadata' => [
                    'transaction_id' => $transactionId,
                ],
            ];

            // Add amount if provided
            if ($amount !== null) {
                $currency = $additionalData['currency'] ?? 'USD';
                $refundData['amount'] = $this->formatAmount($amount, $currency);
            }

            // Create refund
            $refund = Refund::create($refundData);

            // Log payment activity
            $this->logPaymentActivity('refund_payment', [
                'payment_intent_id' => $paymentIntentId,
                'refund_id' => $refund->id,
                'amount' => $amount,
            ], $transactionId);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'refund_id' => $refund->id,
                'status' => 'refunded',
                'gateway' => $this->getName(),
                'gateway_response' => [
                    'refund_id' => $refund->id,
                    'status' => $refund->status,
                    'amount' => $refund->amount / 100, // Convert from cents
                    'currency' => $refund->currency,
                ],
            ];
        } catch (ApiErrorException $e) {
            return $this->handlePaymentError('refund_payment', $e, $transactionId);
        } catch (\Exception $e) {
            return $this->handlePaymentError('refund_payment', $e, $transactionId);
        }
    }

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function cancelPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Get payment intent ID from additional data
            $paymentIntentId = $additionalData['payment_intent_id'] ?? null;
            
            if (!$paymentIntentId) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'Payment intent ID is required',
                    'gateway' => $this->getName(),
                ];
            }

            // Retrieve payment intent
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            // Cancel payment intent
            $paymentIntent->cancel();

            // Log payment activity
            $this->logPaymentActivity('cancel_payment', [
                'payment_intent_id' => $paymentIntentId,
            ], $transactionId);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => 'cancelled',
                'gateway' => $this->getName(),
                'gateway_response' => [
                    'payment_intent_id' => $paymentIntent->id,
                    'status' => $paymentIntent->status,
                ],
            ];
        } catch (ApiErrorException $e) {
            return $this->handlePaymentError('cancel_payment', $e, $transactionId);
        } catch (\Exception $e) {
            return $this->handlePaymentError('cancel_payment', $e, $transactionId);
        }
    }

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array
    {
        try {
            // For Stripe, we need the payment intent ID
            // This would typically be stored in your database along with the transaction ID
            // For this example, we'll assume it's passed in the additional data
            return [
                'success' => false,
                'transaction_id' => $transactionId,
                'error' => 'Payment intent ID is required to check status',
                'gateway' => $this->getName(),
            ];
        } catch (ApiErrorException $e) {
            return $this->handlePaymentError('get_payment_status', $e, $transactionId);
        } catch (\Exception $e) {
            return $this->handlePaymentError('get_payment_status', $e, $transactionId);
        }
    }

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentDetails(string $transactionId): array
    {
        // Similar to getPaymentStatus, we need the payment intent ID
        return $this->getPaymentStatus($transactionId);
    }

    /**
     * Generate payment form.
     *
     * @param array $paymentData
     * @return array
     */
    public function generatePaymentForm(array $paymentData): array
    {
        try {
            // Process payment to get client secret
            $result = $this->processPayment($paymentData);
            
            if (!$result['success']) {
                return $result;
            }

            // Return data needed for Stripe Elements
            return [
                'action' => 'stripe_elements',
                'method' => 'POST',
                'fields' => [
                    'publishable_key' => $this->apiKey,
                    'client_secret' => $result['client_secret'],
                    'payment_intent_id' => $result['payment_intent_id'],
                    'transaction_id' => $result['transaction_id'],
                    'success_url' => $paymentData['success_url'] ?? '',
                    'cancel_url' => $paymentData['cancel_url'] ?? '',
                ],
            ];
        } catch (ApiErrorException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'gateway' => $this->getName(),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'gateway' => $this->getName(),
            ];
        }
    }

    /**
     * Handle payment webhook.
     *
     * @param array $data
     * @return array
     */
    public function handleWebhook(array $data): array
    {
        try {
            // Verify webhook signature
            $payload = @file_get_contents('php://input');
            $sigHeader = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';
            
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $sigHeader,
                $this->webhookSecret
            );

            // Handle different event types
            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $paymentIntent = $event->data->object;
                    $transactionId = $paymentIntent->metadata->transaction_id ?? null;
                    
                    if (!$transactionId) {
                        return [
                            'success' => false,
                            'error' => 'Transaction ID not found in metadata',
                            'gateway' => $this->getName(),
                        ];
                    }
                    
                    return [
                        'success' => true,
                        'transaction_id' => $transactionId,
                        'status' => 'success',
                        'amount' => $paymentIntent->amount / 100, // Convert from cents
                        'currency' => $paymentIntent->currency,
                        'payment_method' => $paymentIntent->payment_method_types[0] ?? 'card',
                        'gateway' => $this->getName(),
                        'gateway_response' => $event->data->object->toArray(),
                    ];
                
                case 'payment_intent.payment_failed':
                    $paymentIntent = $event->data->object;
                    $transactionId = $paymentIntent->metadata->transaction_id ?? null;
                    
                    if (!$transactionId) {
                        return [
                            'success' => false,
                            'error' => 'Transaction ID not found in metadata',
                            'gateway' => $this->getName(),
                        ];
                    }
                    
                    return [
                        'success' => true,
                        'transaction_id' => $transactionId,
                        'status' => 'failed',
                        'amount' => $paymentIntent->amount / 100, // Convert from cents
                        'currency' => $paymentIntent->currency,
                        'payment_method' => $paymentIntent->payment_method_types[0] ?? 'card',
                        'gateway' => $this->getName(),
                        'gateway_response' => $event->data->object->toArray(),
                    ];
                
                case 'charge.refunded':
                    $charge = $event->data->object;
                    $paymentIntentId = $charge->payment_intent;
                    
                    // Retrieve payment intent to get transaction ID
                    $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
                    $transactionId = $paymentIntent->metadata->transaction_id ?? null;
                    
                    if (!$transactionId) {
                        return [
                            'success' => false,
                            'error' => 'Transaction ID not found in metadata',
                            'gateway' => $this->getName(),
                        ];
                    }
                    
                    return [
                        'success' => true,
                        'transaction_id' => $transactionId,
                        'status' => 'refunded',
                        'amount' => $charge->amount_refunded / 100, // Convert from cents
                        'currency' => $charge->currency,
                        'payment_method' => $charge->payment_method_details->type ?? 'card',
                        'gateway' => $this->getName(),
                        'gateway_response' => $event->data->object->toArray(),
                    ];
                
                default:
                    return [
                        'success' => true,
                        'message' => 'Webhook received but not processed',
                        'event_type' => $event->type,
                        'gateway' => $this->getName(),
                    ];
            }
        } catch (\UnexpectedValueException $e) {
            // Invalid payload
            return [
                'success' => false,
                'error' => 'Invalid payload',
                'gateway' => $this->getName(),
            ];
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            // Invalid signature
            return [
                'success' => false,
                'error' => 'Invalid signature',
                'gateway' => $this->getName(),
            ];
        } catch (ApiErrorException $e) {
            return $this->handlePaymentError('webhook', $e);
        } catch (\Exception $e) {
            return $this->handlePaymentError('webhook', $e);
        }
    }

    /**
     * Map Stripe status to our status.
     *
     * @param string $stripeStatus
     * @return string
     */
    protected function mapStripeStatus(string $stripeStatus): string
    {
        $statusMap = [
            'requires_payment_method' => 'pending',
            'requires_confirmation' => 'pending',
            'requires_action' => 'pending',
            'processing' => 'pending',
            'requires_capture' => 'pending',
            'succeeded' => 'success',
            'canceled' => 'cancelled',
        ];

        return $statusMap[$stripeStatus] ?? 'failed';
    }
}

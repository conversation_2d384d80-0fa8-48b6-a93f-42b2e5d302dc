<?php

namespace App\Services\Payment\Gateway;

interface PaymentGatewayInterface
{
    /**
     * Initialize the payment gateway.
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void;

    /**
     * Get the gateway name.
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Check if the gateway is enabled.
     *
     * @return bool
     */
    public function isEnabled(): bool;

    /**
     * Process a payment.
     *
     * @param array $paymentData
     * @return array
     */
    public function processPayment(array $paymentData): array;

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function verifyPayment(string $transactionId, array $additionalData = []): array;

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @param array $additionalData
     * @return array
     */
    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array;

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function cancelPayment(string $transactionId, array $additionalData = []): array;

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array;

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentDetails(string $transactionId): array;

    /**
     * Generate payment form.
     *
     * @param array $paymentData
     * @return array
     */
    public function generatePaymentForm(array $paymentData): array;

    /**
     * Handle payment webhook.
     *
     * @param array $data
     * @return array
     */
    public function handleWebhook(array $data): array;
}

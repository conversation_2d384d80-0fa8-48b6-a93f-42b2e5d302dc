<?php

namespace App\Services\Payment\Gateway;

use Illuminate\Support\Facades\Log;

abstract class AbstractPaymentGateway implements PaymentGatewayInterface
{
    /**
     * @var array
     */
    protected $config;

    /**
     * @var bool
     */
    protected $enabled;

    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $mode;

    /**
     * @var array
     */
    protected $supportedCurrencies;

    /**
     * @var array
     */
    protected $supportedMethods;

    /**
     * AbstractPaymentGateway constructor.
     */
    public function __construct()
    {
        $this->enabled = false;
        $this->mode = 'test';
        $this->supportedCurrencies = ['INR', 'USD'];
        $this->supportedMethods = ['credit_card', 'debit_card', 'net_banking', 'upi', 'wallet'];
    }

    /**
     * Initialize the payment gateway.
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void
    {
        $this->config = $config;
        $this->enabled = $config['enabled'] ?? false;
        $this->mode = $config['mode'] ?? 'test';
    }

    /**
     * Get the gateway name.
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Check if the gateway is enabled.
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Get the gateway mode.
     *
     * @return string
     */
    public function getMode(): string
    {
        return $this->mode;
    }

    /**
     * Check if the gateway is in test mode.
     *
     * @return bool
     */
    public function isTestMode(): bool
    {
        return $this->mode === 'test';
    }

    /**
     * Get supported currencies.
     *
     * @return array
     */
    public function getSupportedCurrencies(): array
    {
        return $this->supportedCurrencies;
    }

    /**
     * Check if a currency is supported.
     *
     * @param string $currency
     * @return bool
     */
    public function isCurrencySupported(string $currency): bool
    {
        return in_array(strtoupper($currency), $this->supportedCurrencies);
    }

    /**
     * Get supported payment methods.
     *
     * @return array
     */
    public function getSupportedMethods(): array
    {
        return $this->supportedMethods;
    }

    /**
     * Check if a payment method is supported.
     *
     * @param string $method
     * @return bool
     */
    public function isMethodSupported(string $method): bool
    {
        return in_array(strtolower($method), $this->supportedMethods);
    }

    /**
     * Validate payment data.
     *
     * @param array $paymentData
     * @return array
     */
    protected function validatePaymentData(array $paymentData): array
    {
        $errors = [];

        // Check required fields
        $requiredFields = [
            'order_id',
            'amount',
            'currency',
            'customer_id',
            'customer_email',
            'customer_phone',
        ];

        foreach ($requiredFields as $field) {
            if (!isset($paymentData[$field]) || empty($paymentData[$field])) {
                $errors[] = "The {$field} field is required.";
            }
        }

        // Check amount
        if (isset($paymentData['amount']) && (!is_numeric($paymentData['amount']) || $paymentData['amount'] <= 0)) {
            $errors[] = "The amount must be a positive number.";
        }

        // Check currency
        if (isset($paymentData['currency']) && !$this->isCurrencySupported($paymentData['currency'])) {
            $errors[] = "The currency {$paymentData['currency']} is not supported.";
        }

        // Check payment method
        if (isset($paymentData['method']) && !$this->isMethodSupported($paymentData['method'])) {
            $errors[] = "The payment method {$paymentData['method']} is not supported.";
        }

        return $errors;
    }

    /**
     * Format amount for the gateway.
     *
     * @param float $amount
     * @param string $currency
     * @return float|int
     */
    protected function formatAmount(float $amount, string $currency)
    {
        // Some gateways require amount in smallest currency unit (e.g., cents for USD)
        $currencyMultipliers = [
            'USD' => 100, // 1 USD = 100 cents
            'INR' => 100, // 1 INR = 100 paise
            'EUR' => 100, // 1 EUR = 100 cents
            'GBP' => 100, // 1 GBP = 100 pence
        ];

        if (isset($currencyMultipliers[strtoupper($currency)])) {
            return $amount * $currencyMultipliers[strtoupper($currency)];
        }

        return $amount;
    }

    /**
     * Generate a unique transaction ID.
     *
     * @param string $prefix
     * @return string
     */
    protected function generateTransactionId(string $prefix = ''): string
    {
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        
        return $prefix . $timestamp . $random;
    }

    /**
     * Log payment activity.
     *
     * @param string $action
     * @param array $data
     * @param string|null $transactionId
     * @return void
     */
    protected function logPaymentActivity(string $action, array $data, ?string $transactionId = null): void
    {
        $logData = [
            'gateway' => $this->getName(),
            'action' => $action,
            'transaction_id' => $transactionId,
            'mode' => $this->getMode(),
            'timestamp' => now()->toIso8601String(),
        ];

        // Remove sensitive data
        $sensitiveFields = ['card_number', 'cvv', 'expiry_month', 'expiry_year', 'password', 'api_key', 'secret_key'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        $logData['data'] = $data;

        Log::info('Payment activity', $logData);
    }

    /**
     * Handle payment error.
     *
     * @param string $action
     * @param \Exception $exception
     * @param string|null $transactionId
     * @return array
     */
    protected function handlePaymentError(string $action, \Exception $exception, ?string $transactionId = null): array
    {
        $errorMessage = $exception->getMessage();
        $errorCode = $exception->getCode();

        Log::error('Payment error', [
            'gateway' => $this->getName(),
            'action' => $action,
            'transaction_id' => $transactionId,
            'error_message' => $errorMessage,
            'error_code' => $errorCode,
            'trace' => $exception->getTraceAsString(),
        ]);

        return [
            'success' => false,
            'transaction_id' => $transactionId,
            'error' => $errorMessage,
            'error_code' => $errorCode,
            'gateway' => $this->getName(),
        ];
    }
}

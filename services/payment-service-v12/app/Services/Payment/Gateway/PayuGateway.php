<?php

namespace App\Services\Payment\Gateway;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class PayuGateway extends AbstractPaymentGateway
{
    /**
     * @var string
     */
    protected $name = 'payu';

    /**
     * @var string
     */
    protected $merchantKey;

    /**
     * @var string
     */
    protected $merchantSalt;

    /**
     * @var string
     */
    protected $baseUrl;

    /**
     * @var Client
     */
    protected $httpClient;

    /**
     * Initialize the payment gateway.
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->merchantKey = $config['merchant_key'] ?? '';
        $this->merchantSalt = $config['merchant_salt'] ?? '';
        
        // Set base URL based on mode
        if ($this->isTestMode()) {
            $this->baseUrl = 'https://test.payu.in';
        } else {
            $this->baseUrl = 'https://secure.payu.in';
        }

        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 30,
        ]);
    }

    /**
     * Process a payment.
     *
     * @param array $paymentData
     * @return array
     */
    public function processPayment(array $paymentData): array
    {
        try {
            // Validate payment data
            $errors = $this->validatePaymentData($paymentData);
            
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'errors' => $errors,
                    'gateway' => $this->getName(),
                ];
            }

            // Generate transaction ID if not provided
            $transactionId = $paymentData['transaction_id'] ?? $this->generateTransactionId('PAYU');

            // Prepare payment data for PayU
            $payuData = $this->preparePaymentData($paymentData, $transactionId);

            // Log payment activity
            $this->logPaymentActivity('process_payment', $payuData, $transactionId);

            // For PayU, we generate a payment form that will be submitted by the client
            $formData = $this->generatePaymentForm($payuData);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'gateway' => $this->getName(),
                'redirect' => true,
                'form_data' => $formData,
            ];
        } catch (\Exception $e) {
            return $this->handlePaymentError('process_payment', $e);
        }
    }

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function verifyPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Prepare verification data
            $verifyData = [
                'key' => $this->merchantKey,
                'command' => 'verify_payment',
                'var1' => $transactionId, // PayU transaction ID
                'hash' => $this->generateHash([
                    $this->merchantKey,
                    'verify_payment',
                    $transactionId,
                    $this->merchantSalt,
                ]),
            ];

            // Log payment activity
            $this->logPaymentActivity('verify_payment', $verifyData, $transactionId);

            // Make API request to PayU
            $response = $this->httpClient->post('/merchant/postservice.php', [
                'form_params' => $verifyData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Check if verification was successful
            if (isset($responseData['status']) && $responseData['status'] === 1) {
                $paymentStatus = $this->mapPayuStatus($responseData['transaction_details'][$transactionId]['status']);
                
                return [
                    'success' => true,
                    'transaction_id' => $transactionId,
                    'status' => $paymentStatus,
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            } else {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => $responseData['msg'] ?? 'Payment verification failed',
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            }
        } catch (\Exception $e) {
            return $this->handlePaymentError('verify_payment', $e, $transactionId);
        }
    }

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @param array $additionalData
     * @return array
     */
    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array
    {
        try {
            // Prepare refund data
            $refundData = [
                'key' => $this->merchantKey,
                'command' => 'cancel_refund_transaction',
                'var1' => $transactionId, // PayU transaction ID
                'var2' => 'refund', // Action
                'var3' => $amount ? (string) $amount : '', // Refund amount (empty for full refund)
                'hash' => $this->generateHash([
                    $this->merchantKey,
                    'cancel_refund_transaction',
                    $transactionId,
                    'refund',
                    $amount ? (string) $amount : '',
                    $this->merchantSalt,
                ]),
            ];

            // Log payment activity
            $this->logPaymentActivity('refund_payment', $refundData, $transactionId);

            // Make API request to PayU
            $response = $this->httpClient->post('/merchant/postservice.php', [
                'form_params' => $refundData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Check if refund was successful
            if (isset($responseData['status']) && $responseData['status'] === 1) {
                return [
                    'success' => true,
                    'transaction_id' => $transactionId,
                    'refund_id' => $responseData['request_id'] ?? null,
                    'status' => 'refunded',
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            } else {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => $responseData['msg'] ?? 'Refund failed',
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            }
        } catch (\Exception $e) {
            return $this->handlePaymentError('refund_payment', $e, $transactionId);
        }
    }

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function cancelPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Prepare cancel data
            $cancelData = [
                'key' => $this->merchantKey,
                'command' => 'cancel_refund_transaction',
                'var1' => $transactionId, // PayU transaction ID
                'var2' => 'cancel', // Action
                'hash' => $this->generateHash([
                    $this->merchantKey,
                    'cancel_refund_transaction',
                    $transactionId,
                    'cancel',
                    $this->merchantSalt,
                ]),
            ];

            // Log payment activity
            $this->logPaymentActivity('cancel_payment', $cancelData, $transactionId);

            // Make API request to PayU
            $response = $this->httpClient->post('/merchant/postservice.php', [
                'form_params' => $cancelData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Check if cancellation was successful
            if (isset($responseData['status']) && $responseData['status'] === 1) {
                return [
                    'success' => true,
                    'transaction_id' => $transactionId,
                    'status' => 'cancelled',
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            } else {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => $responseData['msg'] ?? 'Cancellation failed',
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            }
        } catch (\Exception $e) {
            return $this->handlePaymentError('cancel_payment', $e, $transactionId);
        }
    }

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array
    {
        // For PayU, payment status is the same as verifying a payment
        return $this->verifyPayment($transactionId);
    }

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentDetails(string $transactionId): array
    {
        try {
            // Prepare details data
            $detailsData = [
                'key' => $this->merchantKey,
                'command' => 'verify_payment',
                'var1' => $transactionId, // PayU transaction ID
                'hash' => $this->generateHash([
                    $this->merchantKey,
                    'verify_payment',
                    $transactionId,
                    $this->merchantSalt,
                ]),
            ];

            // Log payment activity
            $this->logPaymentActivity('get_payment_details', $detailsData, $transactionId);

            // Make API request to PayU
            $response = $this->httpClient->post('/merchant/postservice.php', [
                'form_params' => $detailsData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Check if details retrieval was successful
            if (isset($responseData['status']) && $responseData['status'] === 1) {
                $transactionDetails = $responseData['transaction_details'][$transactionId] ?? [];
                
                return [
                    'success' => true,
                    'transaction_id' => $transactionId,
                    'status' => $this->mapPayuStatus($transactionDetails['status'] ?? ''),
                    'amount' => $transactionDetails['amount'] ?? 0,
                    'currency' => $transactionDetails['currency'] ?? 'INR',
                    'payment_method' => $transactionDetails['mode'] ?? '',
                    'payment_date' => $transactionDetails['addedon'] ?? '',
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            } else {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => $responseData['msg'] ?? 'Failed to get payment details',
                    'gateway' => $this->getName(),
                    'gateway_response' => $responseData,
                ];
            }
        } catch (\Exception $e) {
            return $this->handlePaymentError('get_payment_details', $e, $transactionId);
        }
    }

    /**
     * Generate payment form.
     *
     * @param array $paymentData
     * @return array
     */
    public function generatePaymentForm(array $paymentData): array
    {
        // PayU payment form data
        $formAction = $this->isTestMode() ? 'https://test.payu.in/_payment' : 'https://secure.payu.in/_payment';
        
        // Calculate hash if not already provided
        if (!isset($paymentData['hash'])) {
            $hashSequence = [
                $paymentData['key'],
                $paymentData['txnid'],
                $paymentData['amount'],
                $paymentData['productinfo'],
                $paymentData['firstname'],
                $paymentData['email'],
                $paymentData['udf1'] ?? '',
                $paymentData['udf2'] ?? '',
                $paymentData['udf3'] ?? '',
                $paymentData['udf4'] ?? '',
                $paymentData['udf5'] ?? '',
                $paymentData['udf6'] ?? '',
                $paymentData['udf7'] ?? '',
                $paymentData['udf8'] ?? '',
                $paymentData['udf9'] ?? '',
                $paymentData['udf10'] ?? '',
                $this->merchantSalt,
            ];
            
            $paymentData['hash'] = $this->generateHash($hashSequence);
        }

        return [
            'action' => $formAction,
            'method' => 'POST',
            'fields' => $paymentData,
        ];
    }

    /**
     * Handle payment webhook.
     *
     * @param array $data
     * @return array
     */
    public function handleWebhook(array $data): array
    {
        try {
            // Log webhook data
            $this->logPaymentActivity('webhook', $data, $data['txnid'] ?? null);

            // Verify hash
            $calculatedHash = $this->generateHash([
                $data['salt'] ?? $this->merchantSalt,
                $data['status'] ?? '',
                $data['udf10'] ?? '',
                $data['udf9'] ?? '',
                $data['udf8'] ?? '',
                $data['udf7'] ?? '',
                $data['udf6'] ?? '',
                $data['udf5'] ?? '',
                $data['udf4'] ?? '',
                $data['udf3'] ?? '',
                $data['udf2'] ?? '',
                $data['udf1'] ?? '',
                $data['email'] ?? '',
                $data['firstname'] ?? '',
                $data['productinfo'] ?? '',
                $data['amount'] ?? '',
                $data['txnid'] ?? '',
                $data['key'] ?? '',
            ]);

            if ($calculatedHash !== ($data['hash'] ?? '')) {
                return [
                    'success' => false,
                    'transaction_id' => $data['txnid'] ?? null,
                    'error' => 'Hash verification failed',
                    'gateway' => $this->getName(),
                ];
            }

            // Map PayU status to our status
            $status = $this->mapPayuStatus($data['status'] ?? '');

            return [
                'success' => true,
                'transaction_id' => $data['txnid'] ?? null,
                'status' => $status,
                'amount' => $data['amount'] ?? 0,
                'currency' => $data['currency'] ?? 'INR',
                'payment_method' => $data['mode'] ?? '',
                'gateway' => $this->getName(),
                'gateway_response' => $data,
            ];
        } catch (\Exception $e) {
            return $this->handlePaymentError('webhook', $e, $data['txnid'] ?? null);
        }
    }

    /**
     * Prepare payment data for PayU.
     *
     * @param array $paymentData
     * @param string $transactionId
     * @return array
     */
    protected function preparePaymentData(array $paymentData, string $transactionId): array
    {
        // Prepare PayU payment data
        $payuData = [
            'key' => $this->merchantKey,
            'txnid' => $transactionId,
            'amount' => $paymentData['amount'],
            'productinfo' => $paymentData['product_info'] ?? 'Product',
            'firstname' => $paymentData['customer_name'] ?? 'Customer',
            'email' => $paymentData['customer_email'],
            'phone' => $paymentData['customer_phone'],
            'surl' => $paymentData['success_url'] ?? route('payment.success'),
            'furl' => $paymentData['failure_url'] ?? route('payment.failure'),
            'curl' => $paymentData['cancel_url'] ?? route('payment.cancel'),
            'udf1' => $paymentData['order_id'] ?? '',
            'udf2' => $paymentData['customer_id'] ?? '',
            'udf3' => $paymentData['company_id'] ?? '',
            'udf4' => $paymentData['unit_id'] ?? '',
            'udf5' => $paymentData['currency'] ?? 'INR',
        ];

        return $payuData;
    }

    /**
     * Generate hash for PayU.
     *
     * @param array $data
     * @return string
     */
    protected function generateHash(array $data): string
    {
        return hash('sha512', implode('|', $data));
    }

    /**
     * Map PayU status to our status.
     *
     * @param string $payuStatus
     * @return string
     */
    protected function mapPayuStatus(string $payuStatus): string
    {
        $statusMap = [
            'success' => 'success',
            'failure' => 'failed',
            'pending' => 'pending',
            'initiated' => 'pending',
            'in progress' => 'pending',
            'auth' => 'pending',
            'refund' => 'refunded',
            'bounced' => 'failed',
            'dropped' => 'failed',
            'cancelled' => 'cancelled',
            'user cancelled' => 'cancelled',
        ];

        return $statusMap[strtolower($payuStatus)] ?? 'unknown';
    }
}

<?php

namespace App\Services\Payment\Gateway;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class PaypalGateway extends AbstractPaymentGateway
{
    /**
     * @var string
     */
    protected $name = 'paypal';

    /**
     * @var string
     */
    protected $clientId;

    /**
     * @var string
     */
    protected $clientSecret;

    /**
     * @var string
     */
    protected $baseUrl;

    /**
     * @var Client
     */
    protected $httpClient;

    /**
     * @var string
     */
    protected $accessToken;

    /**
     * Initialize the payment gateway.
     *
     * @param array $config
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->clientId = $config['client_id'] ?? '';
        $this->clientSecret = $config['client_secret'] ?? '';
        
        // Set base URL based on mode
        if ($this->isTestMode()) {
            $this->baseUrl = 'https://api-m.sandbox.paypal.com';
        } else {
            $this->baseUrl = 'https://api-m.paypal.com';
        }

        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 30,
        ]);
        
        // Set supported currencies
        $this->supportedCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'INR'];
        
        // Set supported methods
        $this->supportedMethods = ['paypal', 'credit_card', 'debit_card'];
    }

    /**
     * Process a payment.
     *
     * @param array $paymentData
     * @return array
     */
    public function processPayment(array $paymentData): array
    {
        try {
            // Validate payment data
            $errors = $this->validatePaymentData($paymentData);
            
            if (!empty($errors)) {
                return [
                    'success' => false,
                    'errors' => $errors,
                    'gateway' => $this->getName(),
                ];
            }

            // Generate transaction ID if not provided
            $transactionId = $paymentData['transaction_id'] ?? $this->generateTransactionId('PAYPAL');

            // Get access token
            $accessToken = $this->getAccessToken();
            
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get PayPal access token',
                    'gateway' => $this->getName(),
                ];
            }

            // Prepare order data
            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $transactionId,
                        'description' => $paymentData['product_info'] ?? "Order #{$paymentData['order_id']}",
                        'custom_id' => $paymentData['order_id'],
                        'amount' => [
                            'currency_code' => strtoupper($paymentData['currency']),
                            'value' => number_format((float) $paymentData['amount'], 2, '.', ''),
                        ],
                    ],
                ],
                'application_context' => [
                    'brand_name' => 'QuickServe',
                    'landing_page' => 'BILLING',
                    'shipping_preference' => 'NO_SHIPPING',
                    'user_action' => 'PAY_NOW',
                    'return_url' => $paymentData['success_url'] ?? '',
                    'cancel_url' => $paymentData['cancel_url'] ?? '',
                ],
            ];

            // Create order
            $response = $this->httpClient->post('/v2/checkout/orders', [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Content-Type' => 'application/json',
                ],
                'json' => $orderData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Log payment activity
            $this->logPaymentActivity('process_payment', [
                'order_id' => $responseData['id'],
                'status' => $responseData['status'],
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'],
            ], $transactionId);

            // Find approval URL
            $approvalUrl = '';
            foreach ($responseData['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    $approvalUrl = $link['href'];
                    break;
                }
            }

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'gateway' => $this->getName(),
                'redirect' => true,
                'redirect_url' => $approvalUrl,
                'paypal_order_id' => $responseData['id'],
                'status' => $this->mapPaypalStatus($responseData['status']),
            ];
        } catch (\Exception $e) {
            return $this->handlePaymentError('process_payment', $e);
        }
    }

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function verifyPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Get PayPal order ID from additional data
            $paypalOrderId = $additionalData['paypal_order_id'] ?? null;
            
            if (!$paypalOrderId) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'PayPal order ID is required',
                    'gateway' => $this->getName(),
                ];
            }

            // Get access token
            $accessToken = $this->getAccessToken();
            
            if (!$accessToken) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'Failed to get PayPal access token',
                    'gateway' => $this->getName(),
                ];
            }

            // Get order details
            $response = $this->httpClient->get("/v2/checkout/orders/{$paypalOrderId}", [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Content-Type' => 'application/json',
                ],
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Log payment activity
            $this->logPaymentActivity('verify_payment', [
                'order_id' => $paypalOrderId,
                'status' => $responseData['status'],
            ], $transactionId);

            // If order is approved, capture the payment
            if ($responseData['status'] === 'APPROVED') {
                $captureResponse = $this->httpClient->post("/v2/checkout/orders/{$paypalOrderId}/capture", [
                    'headers' => [
                        'Authorization' => "Bearer {$accessToken}",
                        'Content-Type' => 'application/json',
                    ],
                ]);

                $captureData = json_decode($captureResponse->getBody()->getContents(), true);

                // Log payment activity
                $this->logPaymentActivity('capture_payment', [
                    'order_id' => $paypalOrderId,
                    'status' => $captureData['status'],
                ], $transactionId);

                return [
                    'success' => true,
                    'transaction_id' => $transactionId,
                    'status' => $this->mapPaypalStatus($captureData['status']),
                    'gateway' => $this->getName(),
                    'gateway_response' => $captureData,
                ];
            }

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => $this->mapPaypalStatus($responseData['status']),
                'gateway' => $this->getName(),
                'gateway_response' => $responseData,
            ];
        } catch (\Exception $e) {
            return $this->handlePaymentError('verify_payment', $e, $transactionId);
        }
    }

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @param array $additionalData
     * @return array
     */
    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array
    {
        try {
            // Get PayPal capture ID from additional data
            $captureId = $additionalData['capture_id'] ?? null;
            
            if (!$captureId) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'PayPal capture ID is required',
                    'gateway' => $this->getName(),
                ];
            }

            // Get access token
            $accessToken = $this->getAccessToken();
            
            if (!$accessToken) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'Failed to get PayPal access token',
                    'gateway' => $this->getName(),
                ];
            }

            // Prepare refund data
            $refundData = [];
            
            // Add amount if provided
            if ($amount !== null) {
                $currency = $additionalData['currency'] ?? 'USD';
                $refundData['amount'] = [
                    'value' => number_format($amount, 2, '.', ''),
                    'currency_code' => strtoupper($currency),
                ];
            }

            // Create refund
            $response = $this->httpClient->post("/v2/payments/captures/{$captureId}/refund", [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Content-Type' => 'application/json',
                ],
                'json' => $refundData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Log payment activity
            $this->logPaymentActivity('refund_payment', [
                'capture_id' => $captureId,
                'refund_id' => $responseData['id'],
                'status' => $responseData['status'],
                'amount' => $amount,
            ], $transactionId);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'refund_id' => $responseData['id'],
                'status' => 'refunded',
                'gateway' => $this->getName(),
                'gateway_response' => $responseData,
            ];
        } catch (\Exception $e) {
            return $this->handlePaymentError('refund_payment', $e, $transactionId);
        }
    }

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     */
    public function cancelPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Get PayPal order ID from additional data
            $paypalOrderId = $additionalData['paypal_order_id'] ?? null;
            
            if (!$paypalOrderId) {
                return [
                    'success' => false,
                    'transaction_id' => $transactionId,
                    'error' => 'PayPal order ID is required',
                    'gateway' => $this->getName(),
                ];
            }

            // Log payment activity
            $this->logPaymentActivity('cancel_payment', [
                'order_id' => $paypalOrderId,
            ], $transactionId);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => 'cancelled',
                'gateway' => $this->getName(),
            ];
        } catch (\Exception $e) {
            return $this->handlePaymentError('cancel_payment', $e, $transactionId);
        }
    }

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array
    {
        try {
            // For PayPal, we need the order ID
            // This would typically be stored in your database along with the transaction ID
            // For this example, we'll assume it's passed in the additional data
            return [
                'success' => false,
                'transaction_id' => $transactionId,
                'error' => 'PayPal order ID is required to check status',
                'gateway' => $this->getName(),
            ];
        } catch (\Exception $e) {
            return $this->handlePaymentError('get_payment_status', $e, $transactionId);
        }
    }

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentDetails(string $transactionId): array
    {
        // Similar to getPaymentStatus, we need the order ID
        return $this->getPaymentStatus($transactionId);
    }

    /**
     * Generate payment form.
     *
     * @param array $paymentData
     * @return array
     */
    public function generatePaymentForm(array $paymentData): array
    {
        try {
            // Process payment to get redirect URL
            $result = $this->processPayment($paymentData);
            
            if (!$result['success']) {
                return $result;
            }

            // Return data needed for PayPal redirect
            return [
                'action' => 'redirect',
                'method' => 'GET',
                'url' => $result['redirect_url'],
                'fields' => [
                    'paypal_order_id' => $result['paypal_order_id'],
                    'transaction_id' => $result['transaction_id'],
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'gateway' => $this->getName(),
            ];
        }
    }

    /**
     * Handle payment webhook.
     *
     * @param array $data
     * @return array
     */
    public function handleWebhook(array $data): array
    {
        try {
            // Verify webhook signature
            $headers = getallheaders();
            $webhookId = $this->config['webhook_id'] ?? '';
            
            if (empty($webhookId)) {
                return [
                    'success' => false,
                    'error' => 'PayPal webhook ID is not configured',
                    'gateway' => $this->getName(),
                ];
            }

            // Get access token
            $accessToken = $this->getAccessToken();
            
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get PayPal access token',
                    'gateway' => $this->getName(),
                ];
            }

            // Verify webhook signature
            $response = $this->httpClient->post('/v1/notifications/verify-webhook-signature', [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'webhook_id' => $webhookId,
                    'webhook_event' => $data,
                    'transmission_id' => $headers['Paypal-Transmission-Id'] ?? '',
                    'transmission_time' => $headers['Paypal-Transmission-Time'] ?? '',
                    'cert_url' => $headers['Paypal-Cert-Url'] ?? '',
                    'auth_algo' => $headers['Paypal-Auth-Algo'] ?? '',
                    'transmission_sig' => $headers['Paypal-Transmission-Sig'] ?? '',
                ],
            ]);

            $verificationData = json_decode($response->getBody()->getContents(), true);
            
            if ($verificationData['verification_status'] !== 'SUCCESS') {
                return [
                    'success' => false,
                    'error' => 'Invalid webhook signature',
                    'gateway' => $this->getName(),
                ];
            }

            // Handle different event types
            $eventType = $data['event_type'] ?? '';
            $resource = $data['resource'] ?? [];
            
            switch ($eventType) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    // Extract transaction ID from custom ID
                    $transactionId = $resource['custom_id'] ?? null;
                    
                    if (!$transactionId) {
                        return [
                            'success' => false,
                            'error' => 'Transaction ID not found in custom ID',
                            'gateway' => $this->getName(),
                        ];
                    }
                    
                    return [
                        'success' => true,
                        'transaction_id' => $transactionId,
                        'status' => 'success',
                        'amount' => $resource['amount']['value'] ?? 0,
                        'currency' => $resource['amount']['currency_code'] ?? 'USD',
                        'payment_method' => 'paypal',
                        'gateway' => $this->getName(),
                        'gateway_response' => $resource,
                    ];
                
                case 'PAYMENT.CAPTURE.DENIED':
                case 'PAYMENT.CAPTURE.DECLINED':
                    // Extract transaction ID from custom ID
                    $transactionId = $resource['custom_id'] ?? null;
                    
                    if (!$transactionId) {
                        return [
                            'success' => false,
                            'error' => 'Transaction ID not found in custom ID',
                            'gateway' => $this->getName(),
                        ];
                    }
                    
                    return [
                        'success' => true,
                        'transaction_id' => $transactionId,
                        'status' => 'failed',
                        'amount' => $resource['amount']['value'] ?? 0,
                        'currency' => $resource['amount']['currency_code'] ?? 'USD',
                        'payment_method' => 'paypal',
                        'gateway' => $this->getName(),
                        'gateway_response' => $resource,
                    ];
                
                case 'PAYMENT.CAPTURE.REFUNDED':
                    // Extract transaction ID from custom ID
                    $transactionId = $resource['custom_id'] ?? null;
                    
                    if (!$transactionId) {
                        return [
                            'success' => false,
                            'error' => 'Transaction ID not found in custom ID',
                            'gateway' => $this->getName(),
                        ];
                    }
                    
                    return [
                        'success' => true,
                        'transaction_id' => $transactionId,
                        'status' => 'refunded',
                        'amount' => $resource['amount']['value'] ?? 0,
                        'currency' => $resource['amount']['currency_code'] ?? 'USD',
                        'payment_method' => 'paypal',
                        'gateway' => $this->getName(),
                        'gateway_response' => $resource,
                    ];
                
                default:
                    return [
                        'success' => true,
                        'message' => 'Webhook received but not processed',
                        'event_type' => $eventType,
                        'gateway' => $this->getName(),
                    ];
            }
        } catch (\Exception $e) {
            return $this->handlePaymentError('webhook', $e);
        }
    }

    /**
     * Get PayPal access token.
     *
     * @return string|null
     */
    protected function getAccessToken(): ?string
    {
        try {
            // Check if we already have an access token
            if ($this->accessToken) {
                return $this->accessToken;
            }

            // Get access token
            $response = $this->httpClient->post('/v1/oauth2/token', [
                'headers' => [
                    'Accept' => 'application/json',
                    'Accept-Language' => 'en_US',
                ],
                'auth' => [$this->clientId, $this->clientSecret],
                'form_params' => [
                    'grant_type' => 'client_credentials',
                ],
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            
            $this->accessToken = $responseData['access_token'] ?? null;
            
            return $this->accessToken;
        } catch (\Exception $e) {
            Log::error('Failed to get PayPal access token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return null;
        }
    }

    /**
     * Map PayPal status to our status.
     *
     * @param string $paypalStatus
     * @return string
     */
    protected function mapPaypalStatus(string $paypalStatus): string
    {
        $statusMap = [
            'CREATED' => 'pending',
            'SAVED' => 'pending',
            'APPROVED' => 'pending',
            'VOIDED' => 'cancelled',
            'COMPLETED' => 'success',
            'PAYER_ACTION_REQUIRED' => 'pending',
        ];

        return $statusMap[$paypalStatus] ?? 'failed';
    }
}

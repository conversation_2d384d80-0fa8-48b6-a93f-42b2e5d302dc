<?php

namespace App\Services;

use App\Models\PaymentLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentLogService
{
    /**
     * Create a new payment log.
     *
     * @param array $data
     * @param Request|null $request
     * @return PaymentLog
     */
    public function createLog(array $data, Request $request = null): PaymentLog
    {
        try {
            $logData = $data;
            
            // Add request information if available
            if ($request) {
                $logData['ip_address'] = $request->ip();
                $logData['user_agent'] = $request->userAgent();
            }
            
            return PaymentLog::create($logData);
        } catch (\Exception $e) {
            Log::error('Payment log creation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            // Return a new instance without saving to the database
            return new PaymentLog($data);
        }
    }
    
    /**
     * Log a payment event.
     *
     * @param string $event
     * @param string $status
     * @param array $requestData
     * @param array $responseData
     * @param int|null $transactionId
     * @param string|null $gateway
     * @param Request|null $request
     * @return PaymentLog
     */
    public function logEvent(
        string $event,
        string $status,
        array $requestData = [],
        array $responseData = [],
        int|string $transactionId = null,
        string $gateway = null,
        Request $request = null
    ): PaymentLog {
        $data = [
            'event' => $event,
            'status' => $status,
            'request_data' => $requestData,
            'response_data' => $responseData,
            'transaction_id' => $transactionId,
            'gateway' => $gateway,
        ];
        
        return $this->createLog($data, $request);
    }
    
    /**
     * Get logs for a transaction.
     *
     * @param int|string $transactionId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLogsForTransaction(int|string $transactionId)
    {
        return PaymentLog::forTransaction($transactionId)
            ->orderBy('created_at', 'desc')
            ->get();
    }
    
    /**
     * Get logs for a gateway.
     *
     * @param string $gateway
     * @param int|null $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLogsForGateway(string $gateway, int $limit = null)
    {
        $query = PaymentLog::forGateway($gateway)
            ->orderBy('created_at', 'desc');
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }
    
    /**
     * Get logs for an event.
     *
     * @param string $event
     * @param int|null $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLogsForEvent(string $event, int $limit = null)
    {
        $query = PaymentLog::forEvent($event)
            ->orderBy('created_at', 'desc');
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }
    
    /**
     * Get logs with a specific status.
     *
     * @param string $status
     * @param int|null $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLogsWithStatus(string $status, int $limit = null)
    {
        $query = PaymentLog::withStatus($status)
            ->orderBy('created_at', 'desc');
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }
    
    /**
     * Get recent logs.
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentLogs(int $limit = 10)
    {
        return PaymentLog::orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
    
    /**
     * Search logs.
     *
     * @param array $criteria
     * @param int|null $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function searchLogs(array $criteria, int $limit = null)
    {
        $query = PaymentLog::query();
        
        if (isset($criteria['transaction_id'])) {
            $query->where('transaction_id', $criteria['transaction_id']);
        }
        
        if (isset($criteria['gateway'])) {
            $query->where('gateway', $criteria['gateway']);
        }
        
        if (isset($criteria['event'])) {
            $query->where('event', $criteria['event']);
        }
        
        if (isset($criteria['status'])) {
            $query->where('status', $criteria['status']);
        }
        
        if (isset($criteria['start_date'])) {
            $query->where('created_at', '>=', $criteria['start_date']);
        }
        
        if (isset($criteria['end_date'])) {
            $query->where('created_at', '<=', $criteria['end_date']);
        }
        
        $query->orderBy('created_at', 'desc');
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }
}

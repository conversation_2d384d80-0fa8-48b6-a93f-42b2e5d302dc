<?php

namespace App\Services\Performance;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * Payment Performance Monitor
 * 
 * Monitors payment processing performance to ensure <200ms target
 * and provides real-time performance metrics for OneFoodDialer 2025
 */
class PaymentPerformanceMonitor
{
    /**
     * Performance thresholds (in milliseconds)
     */
    private const PERFORMANCE_THRESHOLDS = [
        'excellent' => 100,
        'good' => 200,
        'warning' => 500,
        'critical' => 1000,
    ];

    /**
     * Metric cache keys
     */
    private const METRICS_PREFIX = 'payment:metrics:';
    private const PERFORMANCE_PREFIX = 'payment:performance:';

    /**
     * Start performance tracking for a payment operation
     *
     * @param string $operation
     * @param string $transactionId
     * @param array $context
     * @return string Performance tracking ID
     */
    public function startTracking(string $operation, string $transactionId, array $context = []): string
    {
        $trackingId = uniqid('perf_', true);
        
        $trackingData = [
            'tracking_id' => $trackingId,
            'operation' => $operation,
            'transaction_id' => $transactionId,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'context' => $context,
            'checkpoints' => [],
        ];

        Cache::put(
            self::PERFORMANCE_PREFIX . $trackingId,
            $trackingData,
            300 // 5 minutes
        );

        return $trackingId;
    }

    /**
     * Add a checkpoint to performance tracking
     *
     * @param string $trackingId
     * @param string $checkpoint
     * @param array $data
     * @return bool
     */
    public function addCheckpoint(string $trackingId, string $checkpoint, array $data = []): bool
    {
        try {
            $trackingData = Cache::get(self::PERFORMANCE_PREFIX . $trackingId);
            
            if (!$trackingData) {
                return false;
            }

            $trackingData['checkpoints'][] = [
                'name' => $checkpoint,
                'time' => microtime(true),
                'memory' => memory_get_usage(true),
                'data' => $data,
            ];

            Cache::put(
                self::PERFORMANCE_PREFIX . $trackingId,
                $trackingData,
                300
            );

            return true;
        } catch (Exception $e) {
            Log::error('Failed to add performance checkpoint', [
                'tracking_id' => $trackingId,
                'checkpoint' => $checkpoint,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * End performance tracking and record metrics
     *
     * @param string $trackingId
     * @param string $status
     * @param array $result
     * @return array Performance metrics
     */
    public function endTracking(string $trackingId, string $status = 'success', array $result = []): array
    {
        try {
            $trackingData = Cache::get(self::PERFORMANCE_PREFIX . $trackingId);
            
            if (!$trackingData) {
                return [];
            }

            $endTime = microtime(true);
            $endMemory = memory_get_usage(true);
            
            $metrics = [
                'tracking_id' => $trackingId,
                'operation' => $trackingData['operation'],
                'transaction_id' => $trackingData['transaction_id'],
                'status' => $status,
                'total_time_ms' => round(($endTime - $trackingData['start_time']) * 1000, 2),
                'memory_usage_mb' => round(($endMemory - $trackingData['start_memory']) / 1024 / 1024, 2),
                'checkpoints' => $this->processCheckpoints($trackingData),
                'performance_grade' => $this->calculatePerformanceGrade($endTime - $trackingData['start_time']),
                'context' => $trackingData['context'],
                'result' => $result,
                'timestamp' => now()->toISOString(),
            ];

            // Store metrics for analysis
            $this->storeMetrics($metrics);

            // Check for performance alerts
            $this->checkPerformanceAlerts($metrics);

            // Clean up tracking data
            Cache::forget(self::PERFORMANCE_PREFIX . $trackingId);

            return $metrics;
        } catch (Exception $e) {
            Log::error('Failed to end performance tracking', [
                'tracking_id' => $trackingId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get real-time performance metrics
     *
     * @param int $minutes
     * @return array
     */
    public function getRealtimeMetrics(int $minutes = 5): array
    {
        try {
            $cacheKey = self::METRICS_PREFIX . "realtime_{$minutes}m";
            
            return Cache::remember($cacheKey, 60, function () use ($minutes) {
                $since = now()->subMinutes($minutes);
                
                // Get metrics from cache/database
                $metrics = $this->getMetricsSince($since);
                
                return [
                    'time_window' => "{$minutes} minutes",
                    'total_operations' => count($metrics),
                    'avg_response_time_ms' => $this->calculateAverageResponseTime($metrics),
                    'success_rate' => $this->calculateSuccessRate($metrics),
                    'performance_distribution' => $this->calculatePerformanceDistribution($metrics),
                    'slowest_operations' => $this->getSlowestOperations($metrics, 5),
                    'error_rate' => $this->calculateErrorRate($metrics),
                    'memory_usage_avg_mb' => $this->calculateAverageMemoryUsage($metrics),
                ];
            });
        } catch (Exception $e) {
            Log::error('Failed to get realtime metrics', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get performance trends
     *
     * @param int $hours
     * @return array
     */
    public function getPerformanceTrends(int $hours = 24): array
    {
        try {
            $cacheKey = self::METRICS_PREFIX . "trends_{$hours}h";
            
            return Cache::remember($cacheKey, 300, function () use ($hours) {
                $since = now()->subHours($hours);
                $metrics = $this->getMetricsSince($since);
                
                // Group metrics by hour
                $hourlyMetrics = $this->groupMetricsByHour($metrics);
                
                return [
                    'time_window' => "{$hours} hours",
                    'hourly_performance' => $hourlyMetrics,
                    'trend_analysis' => $this->analyzeTrends($hourlyMetrics),
                    'performance_alerts' => $this->getPerformanceAlerts($hours),
                ];
            });
        } catch (Exception $e) {
            Log::error('Failed to get performance trends', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Check if performance meets SLA requirements
     *
     * @param array $metrics
     * @return array
     */
    public function checkSLA(array $metrics): array
    {
        $slaRequirements = [
            'max_response_time_ms' => 200,
            'min_success_rate' => 99.5,
            'max_error_rate' => 0.5,
        ];

        $slaStatus = [
            'meets_sla' => true,
            'violations' => [],
            'score' => 100,
        ];

        // Check response time SLA
        if ($metrics['avg_response_time_ms'] > $slaRequirements['max_response_time_ms']) {
            $slaStatus['meets_sla'] = false;
            $slaStatus['violations'][] = [
                'type' => 'response_time',
                'actual' => $metrics['avg_response_time_ms'],
                'required' => $slaRequirements['max_response_time_ms'],
            ];
            $slaStatus['score'] -= 30;
        }

        // Check success rate SLA
        if ($metrics['success_rate'] < $slaRequirements['min_success_rate']) {
            $slaStatus['meets_sla'] = false;
            $slaStatus['violations'][] = [
                'type' => 'success_rate',
                'actual' => $metrics['success_rate'],
                'required' => $slaRequirements['min_success_rate'],
            ];
            $slaStatus['score'] -= 40;
        }

        // Check error rate SLA
        if ($metrics['error_rate'] > $slaRequirements['max_error_rate']) {
            $slaStatus['meets_sla'] = false;
            $slaStatus['violations'][] = [
                'type' => 'error_rate',
                'actual' => $metrics['error_rate'],
                'required' => $slaRequirements['max_error_rate'],
            ];
            $slaStatus['score'] -= 30;
        }

        return $slaStatus;
    }

    /**
     * Process checkpoints to calculate timing
     *
     * @param array $trackingData
     * @return array
     */
    private function processCheckpoints(array $trackingData): array
    {
        $processedCheckpoints = [];
        $previousTime = $trackingData['start_time'];
        
        foreach ($trackingData['checkpoints'] as $checkpoint) {
            $processedCheckpoints[] = [
                'name' => $checkpoint['name'],
                'duration_ms' => round(($checkpoint['time'] - $previousTime) * 1000, 2),
                'cumulative_ms' => round(($checkpoint['time'] - $trackingData['start_time']) * 1000, 2),
                'memory_mb' => round($checkpoint['memory'] / 1024 / 1024, 2),
                'data' => $checkpoint['data'],
            ];
            $previousTime = $checkpoint['time'];
        }

        return $processedCheckpoints;
    }

    /**
     * Calculate performance grade based on response time
     *
     * @param float $responseTimeSeconds
     * @return string
     */
    private function calculatePerformanceGrade(float $responseTimeSeconds): string
    {
        $responseTimeMs = $responseTimeSeconds * 1000;

        if ($responseTimeMs <= self::PERFORMANCE_THRESHOLDS['excellent']) {
            return 'excellent';
        } elseif ($responseTimeMs <= self::PERFORMANCE_THRESHOLDS['good']) {
            return 'good';
        } elseif ($responseTimeMs <= self::PERFORMANCE_THRESHOLDS['warning']) {
            return 'warning';
        } else {
            return 'critical';
        }
    }

    /**
     * Store performance metrics
     *
     * @param array $metrics
     * @return bool
     */
    private function storeMetrics(array $metrics): bool
    {
        try {
            // Store in cache for quick access
            $cacheKey = self::METRICS_PREFIX . $metrics['tracking_id'];
            Cache::put($cacheKey, $metrics, 3600); // 1 hour

            // Store in database for long-term analysis
            DB::table('payment_performance_metrics')->insert([
                'tracking_id' => $metrics['tracking_id'],
                'operation' => $metrics['operation'],
                'transaction_id' => $metrics['transaction_id'],
                'status' => $metrics['status'],
                'total_time_ms' => $metrics['total_time_ms'],
                'memory_usage_mb' => $metrics['memory_usage_mb'],
                'performance_grade' => $metrics['performance_grade'],
                'context' => json_encode($metrics['context']),
                'checkpoints' => json_encode($metrics['checkpoints']),
                'created_at' => now(),
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to store performance metrics', [
                'tracking_id' => $metrics['tracking_id'],
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check for performance alerts
     *
     * @param array $metrics
     * @return void
     */
    private function checkPerformanceAlerts(array $metrics): void
    {
        // Alert if response time exceeds 200ms
        if ($metrics['total_time_ms'] > 200) {
            Log::warning('Payment performance alert: Response time exceeded 200ms', [
                'tracking_id' => $metrics['tracking_id'],
                'operation' => $metrics['operation'],
                'response_time_ms' => $metrics['total_time_ms'],
                'transaction_id' => $metrics['transaction_id'],
            ]);
        }

        // Alert if memory usage is excessive
        if ($metrics['memory_usage_mb'] > 50) {
            Log::warning('Payment performance alert: High memory usage', [
                'tracking_id' => $metrics['tracking_id'],
                'operation' => $metrics['operation'],
                'memory_usage_mb' => $metrics['memory_usage_mb'],
                'transaction_id' => $metrics['transaction_id'],
            ]);
        }
    }

    /**
     * Get metrics since a specific time
     *
     * @param \Carbon\Carbon $since
     * @return array
     */
    private function getMetricsSince($since): array
    {
        try {
            return DB::table('payment_performance_metrics')
                ->where('created_at', '>=', $since)
                ->get()
                ->toArray();
        } catch (Exception $e) {
            Log::error('Failed to get metrics from database', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Calculate average response time
     *
     * @param array $metrics
     * @return float
     */
    private function calculateAverageResponseTime(array $metrics): float
    {
        if (empty($metrics)) {
            return 0.0;
        }

        $totalTime = array_sum(array_column($metrics, 'total_time_ms'));
        return round($totalTime / count($metrics), 2);
    }

    /**
     * Calculate success rate
     *
     * @param array $metrics
     * @return float
     */
    private function calculateSuccessRate(array $metrics): float
    {
        if (empty($metrics)) {
            return 0.0;
        }

        $successCount = count(array_filter($metrics, fn($m) => $m->status === 'success'));
        return round(($successCount / count($metrics)) * 100, 2);
    }

    /**
     * Calculate error rate
     *
     * @param array $metrics
     * @return float
     */
    private function calculateErrorRate(array $metrics): float
    {
        return 100 - $this->calculateSuccessRate($metrics);
    }

    /**
     * Calculate performance distribution
     *
     * @param array $metrics
     * @return array
     */
    private function calculatePerformanceDistribution(array $metrics): array
    {
        $distribution = [
            'excellent' => 0,
            'good' => 0,
            'warning' => 0,
            'critical' => 0,
        ];

        foreach ($metrics as $metric) {
            $grade = $metric->performance_grade ?? 'unknown';
            if (isset($distribution[$grade])) {
                $distribution[$grade]++;
            }
        }

        return $distribution;
    }

    /**
     * Get slowest operations
     *
     * @param array $metrics
     * @param int $limit
     * @return array
     */
    private function getSlowestOperations(array $metrics, int $limit = 5): array
    {
        usort($metrics, fn($a, $b) => $b->total_time_ms <=> $a->total_time_ms);
        
        return array_slice($metrics, 0, $limit);
    }

    /**
     * Calculate average memory usage
     *
     * @param array $metrics
     * @return float
     */
    private function calculateAverageMemoryUsage(array $metrics): float
    {
        if (empty($metrics)) {
            return 0.0;
        }

        $totalMemory = array_sum(array_column($metrics, 'memory_usage_mb'));
        return round($totalMemory / count($metrics), 2);
    }

    /**
     * Group metrics by hour
     *
     * @param array $metrics
     * @return array
     */
    private function groupMetricsByHour(array $metrics): array
    {
        $hourlyMetrics = [];
        
        foreach ($metrics as $metric) {
            $hour = date('Y-m-d H:00:00', strtotime($metric->created_at));
            
            if (!isset($hourlyMetrics[$hour])) {
                $hourlyMetrics[$hour] = [];
            }
            
            $hourlyMetrics[$hour][] = $metric;
        }

        return $hourlyMetrics;
    }

    /**
     * Analyze performance trends
     *
     * @param array $hourlyMetrics
     * @return array
     */
    private function analyzeTrends(array $hourlyMetrics): array
    {
        // Simple trend analysis - can be enhanced with more sophisticated algorithms
        $hours = array_keys($hourlyMetrics);
        sort($hours);
        
        $trends = [];
        for ($i = 1; $i < count($hours); $i++) {
            $currentHour = $hours[$i];
            $previousHour = $hours[$i - 1];
            
            $currentAvg = $this->calculateAverageResponseTime($hourlyMetrics[$currentHour]);
            $previousAvg = $this->calculateAverageResponseTime($hourlyMetrics[$previousHour]);
            
            $trends[] = [
                'hour' => $currentHour,
                'avg_response_time_ms' => $currentAvg,
                'trend' => $currentAvg > $previousAvg ? 'increasing' : 'decreasing',
                'change_percent' => $previousAvg > 0 ? round((($currentAvg - $previousAvg) / $previousAvg) * 100, 2) : 0,
            ];
        }

        return $trends;
    }

    /**
     * Get performance alerts for a time period
     *
     * @param int $hours
     * @return array
     */
    private function getPerformanceAlerts(int $hours): array
    {
        // This would typically query an alerts table
        // For now, return empty array
        return [];
    }
}

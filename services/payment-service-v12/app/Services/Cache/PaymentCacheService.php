<?php

namespace App\Services\Cache;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Exception;

/**
 * Payment Cache Service
 * 
 * Handles caching of payment gateway configurations, transaction data,
 * and frequently accessed payment information to achieve <200ms performance target
 */
class PaymentCacheService
{
    /**
     * Cache key prefixes
     */
    private const GATEWAY_CONFIG_PREFIX = 'payment:gateway:config:';
    private const TRANSACTION_PREFIX = 'payment:transaction:';
    private const CUSTOMER_PAYMENT_PREFIX = 'payment:customer:';
    private const GATEWAY_STATUS_PREFIX = 'payment:gateway:status:';
    private const RATE_LIMIT_PREFIX = 'payment:rate_limit:';

    /**
     * Cache TTL values (in seconds)
     */
    private const GATEWAY_CONFIG_TTL = 3600; // 1 hour
    private const TRANSACTION_TTL = 1800; // 30 minutes
    private const CUSTOMER_PAYMENT_TTL = 900; // 15 minutes
    private const GATEWAY_STATUS_TTL = 300; // 5 minutes
    private const RATE_LIMIT_TTL = 60; // 1 minute

    /**
     * Cache gateway configuration
     *
     * @param string $gateway
     * @param array $config
     * @return bool
     */
    public function cacheGatewayConfig(string $gateway, array $config): bool
    {
        try {
            $key = self::GATEWAY_CONFIG_PREFIX . $gateway;
            
            // Remove sensitive data before caching
            $sanitizedConfig = $this->sanitizeGatewayConfig($config);
            
            return Cache::put($key, $sanitizedConfig, self::GATEWAY_CONFIG_TTL);
        } catch (Exception $e) {
            Log::error('Failed to cache gateway config', [
                'gateway' => $gateway,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached gateway configuration
     *
     * @param string $gateway
     * @return array|null
     */
    public function getGatewayConfig(string $gateway): ?array
    {
        try {
            $key = self::GATEWAY_CONFIG_PREFIX . $gateway;
            return Cache::get($key);
        } catch (Exception $e) {
            Log::error('Failed to get cached gateway config', [
                'gateway' => $gateway,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cache transaction data
     *
     * @param string $transactionId
     * @param array $transactionData
     * @return bool
     */
    public function cacheTransaction(string $transactionId, array $transactionData): bool
    {
        try {
            $key = self::TRANSACTION_PREFIX . $transactionId;
            return Cache::put($key, $transactionData, self::TRANSACTION_TTL);
        } catch (Exception $e) {
            Log::error('Failed to cache transaction', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached transaction data
     *
     * @param string $transactionId
     * @return array|null
     */
    public function getTransaction(string $transactionId): ?array
    {
        try {
            $key = self::TRANSACTION_PREFIX . $transactionId;
            return Cache::get($key);
        } catch (Exception $e) {
            Log::error('Failed to get cached transaction', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cache customer payment summary
     *
     * @param int $customerId
     * @param array $paymentSummary
     * @return bool
     */
    public function cacheCustomerPaymentSummary(int $customerId, array $paymentSummary): bool
    {
        try {
            $key = self::CUSTOMER_PAYMENT_PREFIX . $customerId;
            return Cache::put($key, $paymentSummary, self::CUSTOMER_PAYMENT_TTL);
        } catch (Exception $e) {
            Log::error('Failed to cache customer payment summary', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached customer payment summary
     *
     * @param int $customerId
     * @return array|null
     */
    public function getCustomerPaymentSummary(int $customerId): ?array
    {
        try {
            $key = self::CUSTOMER_PAYMENT_PREFIX . $customerId;
            return Cache::get($key);
        } catch (Exception $e) {
            Log::error('Failed to get cached customer payment summary', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cache gateway status
     *
     * @param string $gateway
     * @param array $status
     * @return bool
     */
    public function cacheGatewayStatus(string $gateway, array $status): bool
    {
        try {
            $key = self::GATEWAY_STATUS_PREFIX . $gateway;
            return Cache::put($key, $status, self::GATEWAY_STATUS_TTL);
        } catch (Exception $e) {
            Log::error('Failed to cache gateway status', [
                'gateway' => $gateway,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached gateway status
     *
     * @param string $gateway
     * @return array|null
     */
    public function getGatewayStatus(string $gateway): ?array
    {
        try {
            $key = self::GATEWAY_STATUS_PREFIX . $gateway;
            return Cache::get($key);
        } catch (Exception $e) {
            Log::error('Failed to get cached gateway status', [
                'gateway' => $gateway,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Implement rate limiting for payment requests
     *
     * @param string $identifier (customer_id, IP, etc.)
     * @param int $maxAttempts
     * @param int $timeWindow
     * @return bool
     */
    public function checkRateLimit(string $identifier, int $maxAttempts = 10, int $timeWindow = 60): bool
    {
        try {
            $key = self::RATE_LIMIT_PREFIX . $identifier;
            $attempts = Cache::get($key, 0);
            
            if ($attempts >= $maxAttempts) {
                return false; // Rate limit exceeded
            }
            
            // Increment attempts
            Cache::put($key, $attempts + 1, $timeWindow);
            return true;
        } catch (Exception $e) {
            Log::error('Failed to check rate limit', [
                'identifier' => $identifier,
                'error' => $e->getMessage()
            ]);
            return true; // Allow on cache failure
        }
    }

    /**
     * Clear rate limit for identifier
     *
     * @param string $identifier
     * @return bool
     */
    public function clearRateLimit(string $identifier): bool
    {
        try {
            $key = self::RATE_LIMIT_PREFIX . $identifier;
            return Cache::forget($key);
        } catch (Exception $e) {
            Log::error('Failed to clear rate limit', [
                'identifier' => $identifier,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Invalidate transaction cache
     *
     * @param string $transactionId
     * @return bool
     */
    public function invalidateTransaction(string $transactionId): bool
    {
        try {
            $key = self::TRANSACTION_PREFIX . $transactionId;
            return Cache::forget($key);
        } catch (Exception $e) {
            Log::error('Failed to invalidate transaction cache', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Invalidate customer payment cache
     *
     * @param int $customerId
     * @return bool
     */
    public function invalidateCustomerPaymentCache(int $customerId): bool
    {
        try {
            $key = self::CUSTOMER_PAYMENT_PREFIX . $customerId;
            return Cache::forget($key);
        } catch (Exception $e) {
            Log::error('Failed to invalidate customer payment cache', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Warm up cache with frequently accessed data
     *
     * @return bool
     */
    public function warmUpCache(): bool
    {
        try {
            // Warm up gateway configurations
            $gateways = ['payu', 'stripe', 'paypal', 'instamojo', 'paytm', 'cashfree_bbps'];
            
            foreach ($gateways as $gateway) {
                $config = $this->getGatewayConfigFromDatabase($gateway);
                if ($config) {
                    $this->cacheGatewayConfig($gateway, $config);
                }
            }

            Log::info('Payment cache warmed up successfully');
            return true;
        } catch (Exception $e) {
            Log::error('Failed to warm up payment cache', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cache statistics
     *
     * @return array
     */
    public function getCacheStats(): array
    {
        try {
            $redis = Redis::connection();
            
            return [
                'memory_usage' => $redis->info('memory')['used_memory_human'] ?? 'N/A',
                'connected_clients' => $redis->info('clients')['connected_clients'] ?? 0,
                'total_commands_processed' => $redis->info('stats')['total_commands_processed'] ?? 0,
                'cache_hit_rate' => $this->calculateCacheHitRate(),
                'active_keys' => $this->getActiveKeysCount(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to get cache stats', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Sanitize gateway configuration for caching
     *
     * @param array $config
     * @return array
     */
    private function sanitizeGatewayConfig(array $config): array
    {
        $sensitiveKeys = [
            'secret_key', 'private_key', 'merchant_key', 'auth_token', 
            'client_secret', 'password', 'api_secret'
        ];

        $sanitized = $config;
        
        foreach ($sensitiveKeys as $key) {
            if (isset($sanitized[$key])) {
                $sanitized[$key] = '***CACHED***';
            }
        }

        return $sanitized;
    }

    /**
     * Get gateway configuration from database
     *
     * @param string $gateway
     * @return array|null
     */
    private function getGatewayConfigFromDatabase(string $gateway): ?array
    {
        // This would typically fetch from a gateway_configurations table
        // For now, return null to avoid database dependency
        return null;
    }

    /**
     * Calculate cache hit rate
     *
     * @return float
     */
    private function calculateCacheHitRate(): float
    {
        try {
            $redis = Redis::connection();
            $stats = $redis->info('stats');
            
            $hits = $stats['keyspace_hits'] ?? 0;
            $misses = $stats['keyspace_misses'] ?? 0;
            $total = $hits + $misses;
            
            return $total > 0 ? round(($hits / $total) * 100, 2) : 0.0;
        } catch (Exception $e) {
            return 0.0;
        }
    }

    /**
     * Get count of active cache keys
     *
     * @return int
     */
    private function getActiveKeysCount(): int
    {
        try {
            $redis = Redis::connection();
            $prefixes = [
                self::GATEWAY_CONFIG_PREFIX,
                self::TRANSACTION_PREFIX,
                self::CUSTOMER_PAYMENT_PREFIX,
                self::GATEWAY_STATUS_PREFIX,
                self::RATE_LIMIT_PREFIX
            ];
            
            $totalKeys = 0;
            foreach ($prefixes as $prefix) {
                $keys = $redis->keys($prefix . '*');
                $totalKeys += count($keys);
            }
            
            return $totalKeys;
        } catch (Exception $e) {
            return 0;
        }
    }
}

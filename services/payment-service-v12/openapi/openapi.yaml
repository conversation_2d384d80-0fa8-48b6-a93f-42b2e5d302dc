openapi: 3.1.0
info:
  title: Payment Service API
  description: API for Payment service
  version: 1.0.0
  contact:
    name: Payment Service Team
    email: <EMAIL>
servers:
  - url: http://localhost:8000/api/v1
    description: Local development server
  - url: https://api.quickserve.com/api/v1
    description: Production server
paths:
  /payments:
    get:
      summary: List payments
      description: Get a list of payments with pagination
      operationId: listPayments
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/PerPageParam'
        - name: status
          in: query
          description: Filter payments by status
          required: false
          schema:
            type: string
            enum: [pending, success, failed, refunded, cancelled]
        - name: gateway
          in: query
          description: Filter payments by gateway
          required: false
          schema:
            type: string
        - name: order_id
          in: query
          description: Filter payments by order ID
          required: false
          schema:
            type: integer
        - name: customer_id
          in: query
          description: Filter payments by customer ID
          required: false
          schema:
            type: integer
        - name: from_date
          in: query
          description: Filter payments from this date (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: to_date
          in: query
          description: Filter payments to this date (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
      responses:
        '200':
          description: A list of payments
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payments retrieved successfully
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Payment'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
  /payments/{id}:
    get:
      summary: Get payment
      description: Get payment by ID
      operationId: getPayment
      parameters:
        - name: id
          in: path
          description: Payment ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Payment details
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment retrieved successfully
                  data:
                    $ref: '#/components/schemas/Payment'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
  /payments/process:
    post:
      summary: Process payment
      description: Process a new payment
      operationId: processPayment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentProcessInput'
      responses:
        '200':
          description: Payment processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment processed successfully
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: integer
                        example: 1
                      transaction_id:
                        type: string
                        example: PAYU1623456789
                      gateway:
                        type: string
                        example: payu
                      redirect:
                        type: boolean
                        example: true
                      form_data:
                        type: object
                        properties:
                          action:
                            type: string
                            example: https://test.payu.in/_payment
                          method:
                            type: string
                            example: POST
                          fields:
                            type: object
                            additionalProperties:
                              type: string
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
  /payments/transaction/{transactionId}/verify:
    get:
      summary: Verify payment
      description: Verify a payment by transaction ID
      operationId: verifyPayment
      parameters:
        - name: transactionId
          in: path
          description: Transaction ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Payment verification result
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment verified successfully
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: integer
                        example: 1
                      transaction_id:
                        type: string
                        example: PAYU1623456789
                      status:
                        type: string
                        enum: [pending, success, failed, refunded, cancelled]
                        example: success
                      gateway:
                        type: string
                        example: payu
                      gateway_response:
                        type: object
                        additionalProperties: true
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
  /payments/transaction/{transactionId}/refund:
    post:
      summary: Refund payment
      description: Refund a payment by transaction ID
      operationId: refundPayment
      parameters:
        - name: transactionId
          in: path
          description: Transaction ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundInput'
      responses:
        '200':
          description: Payment refund result
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment refunded successfully
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: integer
                        example: 1
                      transaction_id:
                        type: string
                        example: PAYU1623456789
                      refund_id:
                        type: string
                        example: REF1623456789
                      status:
                        type: string
                        enum: [pending, success, failed, refunded, cancelled]
                        example: refunded
                      gateway:
                        type: string
                        example: payu
                      gateway_response:
                        type: object
                        additionalProperties: true
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
  /payments/transaction/{transactionId}/cancel:
    post:
      summary: Cancel payment
      description: Cancel a payment by transaction ID
      operationId: cancelPayment
      parameters:
        - name: transactionId
          in: path
          description: Transaction ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Payment cancellation result
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment cancelled successfully
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: integer
                        example: 1
                      transaction_id:
                        type: string
                        example: PAYU1623456789
                      status:
                        type: string
                        enum: [pending, success, failed, refunded, cancelled]
                        example: cancelled
                      gateway:
                        type: string
                        example: payu
                      gateway_response:
                        type: object
                        additionalProperties: true
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
  /payments/transaction/{transactionId}/status:
    get:
      summary: Get payment status
      description: Get the status of a payment by transaction ID
      operationId: getPaymentStatus
      parameters:
        - name: transactionId
          in: path
          description: Transaction ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Payment status result
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment status retrieved successfully
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: integer
                        example: 1
                      transaction_id:
                        type: string
                        example: PAYU1623456789
                      status:
                        type: string
                        enum: [pending, success, failed, refunded, cancelled]
                        example: success
                      gateway:
                        type: string
                        example: payu
                      gateway_response:
                        type: object
                        additionalProperties: true
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
  /payments/gateways:
    get:
      summary: Get available payment gateways
      description: Get a list of available payment gateways
      operationId: getPaymentGateways
      responses:
        '200':
          description: Available payment gateways
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Available payment gateways retrieved successfully
                  data:
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        name:
                          type: string
                          example: PayU
                        supported_currencies:
                          type: array
                          items:
                            type: string
                            example: INR
                        supported_methods:
                          type: array
                          items:
                            type: string
                            example: credit_card
        '401':
          $ref: '#/components/responses/Unauthorized'
      security:
        - bearerAuth: []
  /payments/statistics:
    get:
      summary: Get payment statistics
      description: Get payment statistics
      operationId: getPaymentStatistics
      parameters:
        - name: company_id
          in: query
          description: Filter statistics by company ID
          required: false
          schema:
            type: integer
        - name: unit_id
          in: query
          description: Filter statistics by unit ID
          required: false
          schema:
            type: integer
        - name: start_date
          in: query
          description: Filter statistics from this date (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: Filter statistics to this date (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Payment statistics
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment statistics retrieved successfully
                  data:
                    type: object
                    properties:
                      total_payments:
                        type: integer
                        example: 100
                      successful_payments:
                        type: integer
                        example: 80
                      failed_payments:
                        type: integer
                        example: 10
                      pending_payments:
                        type: integer
                        example: 5
                      refunded_payments:
                        type: integer
                        example: 3
                      cancelled_payments:
                        type: integer
                        example: 2
                      total_amount:
                        type: number
                        format: float
                        example: 5000.00
                      gateway_amounts:
                        type: object
                        additionalProperties:
                          type: number
                          format: float
                      currency_amounts:
                        type: object
                        additionalProperties:
                          type: number
                          format: float
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
  /payments/webhooks/{gateway}:
    post:
      summary: Handle payment webhook
      description: Handle payment webhook from a gateway
      operationId: handlePaymentWebhook
      parameters:
        - name: gateway
          in: path
          description: Payment gateway
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              additionalProperties: true
      responses:
        '200':
          description: Webhook handled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment webhook handled successfully
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: integer
                        example: 1
                      transaction_id:
                        type: string
                        example: PAYU1623456789
                      status:
                        type: string
                        enum: [pending, success, failed, refunded, cancelled]
                        example: success
                      gateway:
                        type: string
                        example: payu
        '400':
          $ref: '#/components/responses/BadRequest'
components:
  schemas:
    Payment:
      type: object
      properties:
        id:
          type: integer
          example: 1
        order_id:
          type: integer
          example: 123
        customer_id:
          type: integer
          example: 456
        transaction_id:
          type: string
          example: PAYU1623456789
        amount:
          type: number
          format: float
          example: 29.99
        currency:
          type: string
          example: INR
        status:
          type: string
          enum: [pending, success, failed, refunded, cancelled]
          example: success
        gateway:
          type: string
          example: payu
        refund_amount:
          type: number
          format: float
          example: 0
        refund_date:
          type: string
          format: date-time
          nullable: true
        company_id:
          type: integer
          example: 1
          nullable: true
        unit_id:
          type: integer
          example: 2
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    PaymentProcessInput:
      type: object
      required:
        - order_id
        - amount
        - currency
        - customer_id
        - customer_name
        - customer_email
        - customer_phone
        - gateway
      properties:
        order_id:
          type: integer
          example: 123
        amount:
          type: number
          format: float
          example: 29.99
        currency:
          type: string
          example: INR
        customer_id:
          type: integer
          example: 456
        customer_name:
          type: string
          example: John Doe
        customer_email:
          type: string
          format: email
          example: <EMAIL>
        customer_phone:
          type: string
          example: +919876543210
        gateway:
          type: string
          example: payu
        method:
          type: string
          example: credit_card
        success_url:
          type: string
          format: uri
          example: https://example.com/payment/success
        failure_url:
          type: string
          format: uri
          example: https://example.com/payment/failure
        cancel_url:
          type: string
          format: uri
          example: https://example.com/payment/cancel
        product_info:
          type: string
          example: Order #123
        company_id:
          type: integer
          example: 1
        unit_id:
          type: integer
          example: 2
        metadata:
          type: object
          additionalProperties: true
    RefundInput:
      type: object
      properties:
        amount:
          type: number
          format: float
          example: 29.99
          description: Amount to refund. If not provided, the full amount will be refunded.
        reason:
          type: string
          example: Customer requested refund
    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        from:
          type: integer
          example: 1
        last_page:
          type: integer
          example: 5
        path:
          type: string
          example: http://localhost:8000/api/v1/payments
        per_page:
          type: integer
          example: 15
        to:
          type: integer
          example: 15
        total:
          type: integer
          example: 75
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
  parameters:
    PageParam:
      name: page
      in: query
      description: Page number
      required: false
      schema:
        type: integer
        default: 1
        minimum: 1
    PerPageParam:
      name: per_page
      in: query
      description: Number of items per page
      required: false
      schema:
        type: integer
        default: 15
        minimum: 1
        maximum: 100
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Unauthenticated
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: You do not have permission to access this resource
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Resource not found
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

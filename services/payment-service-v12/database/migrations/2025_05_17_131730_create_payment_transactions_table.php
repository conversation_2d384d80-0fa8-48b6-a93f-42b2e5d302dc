<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->string('transaction_id', 50)->primary();
            $table->unsignedBigInteger('customer_id');
            $table->string('customer_email', 100)->nullable();
            $table->string('customer_phone', 20)->nullable();
            $table->string('customer_name', 100)->nullable();
            $table->decimal('amount', 10, 2);
            $table->decimal('transaction_charges', 10, 2)->default(0);
            $table->decimal('wallet_amount', 10, 2)->nullable();
            $table->string('order_id', 100)->nullable();
            $table->string('status', 20)->default('initiated');
            $table->string('gateway', 50)->nullable();
            $table->string('gateway_transaction_id', 200)->nullable();
            $table->text('description')->nullable();
            $table->string('transaction_by', 50)->default('gateway');
            $table->string('referer', 50)->default('website');
            $table->string('success_url')->nullable();
            $table->string('failure_url')->nullable();
            $table->string('context', 50)->nullable();
            $table->boolean('recurring')->default(false);
            $table->decimal('discount', 10, 2)->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index('customer_id');
            $table->index('order_id');
            $table->index('gateway_transaction_id');
            $table->index('status');
            $table->index('gateway');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};

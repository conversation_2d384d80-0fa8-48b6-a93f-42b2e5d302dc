<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_transactions', function (Blueprint $table) {
            $table->string('wallet_transaction_id', 100)->nullable()->after('wallet_amount');
            $table->decimal('wallet_balance_after', 10, 2)->nullable()->after('wallet_transaction_id');

            $table->index('wallet_transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_transactions', function (Blueprint $table) {
            $table->dropIndex(['wallet_transaction_id']);
            $table->dropColumn(['wallet_transaction_id', 'wallet_balance_after']);
        });
    }
};

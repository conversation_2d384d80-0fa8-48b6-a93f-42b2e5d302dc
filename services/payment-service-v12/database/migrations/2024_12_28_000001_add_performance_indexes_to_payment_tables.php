<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * Add Performance Indexes to Payment Tables
 * 
 * This migration adds critical database indexes to optimize payment processing
 * performance and achieve the <200ms target for OneFoodDialer 2025
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to payment_transactions table for performance optimization
        Schema::table('payment_transactions', function (Blueprint $table) {
            // Composite index for customer payment history queries
            $table->index(['customer_id', 'status', 'created_at'], 'idx_customer_status_created');
            
            // Index for gateway transaction lookups
            $table->index(['gateway', 'gateway_transaction_id'], 'idx_gateway_txn_id');
            
            // Index for order-based payment lookups
            $table->index(['order_id', 'status'], 'idx_order_status');
            
            // Index for amount-based queries and reporting
            $table->index(['amount', 'status', 'created_at'], 'idx_amount_status_created');
            
            // Index for transaction processing workflow
            $table->index(['status', 'gateway', 'created_at'], 'idx_status_gateway_created');
            
            // Index for webhook processing
            $table->index(['gateway_transaction_id', 'gateway'], 'idx_gateway_txn_gateway');
            
            // Index for refund processing
            $table->index(['transaction_by', 'status'], 'idx_transaction_by_status');
        });

        // Add indexes to wallet_transactions table for wallet performance
        if (Schema::hasTable('wallet_transactions')) {
            Schema::table('wallet_transactions', function (Blueprint $table) {
                // Composite index for customer wallet history
                $table->index(['customer_code', 'type', 'created_at'], 'idx_customer_type_created');
                
                // Index for balance calculation queries
                $table->index(['customer_code', 'status', 'created_at'], 'idx_customer_status_created');
                
                // Index for transaction ID lookups
                $table->index(['transaction_id', 'status'], 'idx_transaction_id_status');
                
                // Index for amount-based wallet queries
                $table->index(['type', 'amount', 'created_at'], 'idx_type_amount_created');
            });
        }

        // Add indexes to customer_wallet table for balance queries
        if (Schema::hasTable('customer_wallet')) {
            Schema::table('customer_wallet', function (Blueprint $table) {
                // Index for customer balance lookups
                $table->index(['customer_code', 'status'], 'idx_customer_status');
                
                // Index for company-wise wallet queries
                $table->index(['company_id', 'unit_id', 'status'], 'idx_company_unit_status');
                
                // Index for balance monitoring
                $table->index(['balance', 'status'], 'idx_balance_status');
            });
        }

        // Add indexes to invoices table for invoice processing
        if (Schema::hasTable('invoices')) {
            Schema::table('invoices', function (Blueprint $table) {
                // Composite index for customer invoice queries
                $table->index(['customer_id', 'status', 'created_at'], 'idx_customer_status_created');
                
                // Index for order-invoice linkage
                $table->index(['order_id', 'status'], 'idx_order_status');
                
                // Index for subscription-invoice linkage
                $table->index(['subscription_id', 'status'], 'idx_subscription_status');
                
                // Index for payment processing
                $table->index(['status', 'due_date'], 'idx_status_due_date');
                
                // Index for invoice number lookups
                $table->index(['invoice_number'], 'idx_invoice_number');
                
                // Index for amount-based queries
                $table->index(['total_amount', 'status', 'created_at'], 'idx_amount_status_created');
            });
        }

        // Add indexes to invoice_items table for item processing
        if (Schema::hasTable('invoice_items')) {
            Schema::table('invoice_items', function (Blueprint $table) {
                // Index for invoice item queries
                $table->index(['invoice_id', 'item_type'], 'idx_invoice_item_type');
                
                // Index for SKU-based lookups
                $table->index(['sku'], 'idx_sku');
                
                // Index for amount calculations
                $table->index(['total_price', 'tax_amount'], 'idx_price_tax');
            });
        }

        // Create database views for optimized queries
        $this->createOptimizedViews();

        // Add database query optimization settings
        $this->optimizeDatabaseSettings();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop views first
        DB::statement('DROP VIEW IF EXISTS payment_summary_view');
        DB::statement('DROP VIEW IF EXISTS wallet_balance_view');
        DB::statement('DROP VIEW IF EXISTS invoice_summary_view');

        // Drop indexes from payment_transactions table
        Schema::table('payment_transactions', function (Blueprint $table) {
            $table->dropIndex('idx_customer_status_created');
            $table->dropIndex('idx_gateway_txn_id');
            $table->dropIndex('idx_order_status');
            $table->dropIndex('idx_amount_status_created');
            $table->dropIndex('idx_status_gateway_created');
            $table->dropIndex('idx_gateway_txn_gateway');
            $table->dropIndex('idx_transaction_by_status');
        });

        // Drop indexes from wallet_transactions table
        if (Schema::hasTable('wallet_transactions')) {
            Schema::table('wallet_transactions', function (Blueprint $table) {
                $table->dropIndex('idx_customer_type_created');
                $table->dropIndex('idx_customer_status_created');
                $table->dropIndex('idx_transaction_id_status');
                $table->dropIndex('idx_type_amount_created');
            });
        }

        // Drop indexes from customer_wallet table
        if (Schema::hasTable('customer_wallet')) {
            Schema::table('customer_wallet', function (Blueprint $table) {
                $table->dropIndex('idx_customer_status');
                $table->dropIndex('idx_company_unit_status');
                $table->dropIndex('idx_balance_status');
            });
        }

        // Drop indexes from invoices table
        if (Schema::hasTable('invoices')) {
            Schema::table('invoices', function (Blueprint $table) {
                $table->dropIndex('idx_customer_status_created');
                $table->dropIndex('idx_order_status');
                $table->dropIndex('idx_subscription_status');
                $table->dropIndex('idx_status_due_date');
                $table->dropIndex('idx_invoice_number');
                $table->dropIndex('idx_amount_status_created');
            });
        }

        // Drop indexes from invoice_items table
        if (Schema::hasTable('invoice_items')) {
            Schema::table('invoice_items', function (Blueprint $table) {
                $table->dropIndex('idx_invoice_item_type');
                $table->dropIndex('idx_sku');
                $table->dropIndex('idx_price_tax');
            });
        }
    }

    /**
     * Create optimized database views for common queries
     */
    private function createOptimizedViews(): void
    {
        // Payment summary view for dashboard queries
        DB::statement("
            CREATE VIEW payment_summary_view AS
            SELECT 
                customer_id,
                gateway,
                status,
                COUNT(*) as transaction_count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount,
                MAX(created_at) as last_transaction_date
            FROM payment_transactions 
            GROUP BY customer_id, gateway, status
        ");

        // Wallet balance view for quick balance lookups
        if (Schema::hasTable('customer_wallet')) {
            DB::statement("
                CREATE VIEW wallet_balance_view AS
                SELECT 
                    customer_code,
                    balance,
                    status,
                    company_id,
                    unit_id,
                    updated_at as last_updated
                FROM customer_wallet 
                WHERE status = 'active'
            ");
        }

        // Invoice summary view for reporting
        if (Schema::hasTable('invoices')) {
            DB::statement("
                CREATE VIEW invoice_summary_view AS
                SELECT 
                    customer_id,
                    status,
                    COUNT(*) as invoice_count,
                    SUM(total_amount) as total_amount,
                    SUM(tax_amount) as total_tax,
                    AVG(total_amount) as avg_amount,
                    MAX(created_at) as last_invoice_date
                FROM invoices 
                GROUP BY customer_id, status
            ");
        }
    }

    /**
     * Optimize database settings for performance
     */
    private function optimizeDatabaseSettings(): void
    {
        // Set MySQL specific optimizations if using MySQL
        if (config('database.default') === 'mysql') {
            try {
                // Optimize query cache settings
                DB::statement("SET GLOBAL query_cache_size = *********"); // 256MB
                DB::statement("SET GLOBAL query_cache_type = ON");
                
                // Optimize InnoDB settings for payment processing
                DB::statement("SET GLOBAL innodb_buffer_pool_size = 1073741824"); // 1GB
                DB::statement("SET GLOBAL innodb_log_file_size = *********"); // 256MB
                
                // Optimize connection settings
                DB::statement("SET GLOBAL max_connections = 200");
                DB::statement("SET GLOBAL wait_timeout = 300");
                
            } catch (\Exception $e) {
                // Log optimization failures but don't fail migration
                \Log::warning('Database optimization settings failed: ' . $e->getMessage());
            }
        }
    }
};

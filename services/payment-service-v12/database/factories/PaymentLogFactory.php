<?php

namespace Database\Factories;

use App\Models\PaymentLog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentLog>
 */
class PaymentLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PaymentLog::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'transaction_id' => $this->faker->numberBetween(1, 1000),
            'gateway' => $this->faker->randomElement(['payu', 'instamojo', 'paytm', 'payeezy', 'mobikwik', 'paypal', 'converge', 'yesbank', 'stripe']),
            'event' => $this->faker->randomElement(['initiate', 'process', 'verify', 'complete', 'refund', 'error']),
            'status' => $this->faker->randomElement(['success', 'failure', 'pending']),
            'request_data' => [
                'amount' => $this->faker->randomFloat(2, 10, 1000),
                'currency' => 'USD',
                'description' => $this->faker->sentence(),
            ],
            'response_data' => [
                'status' => $this->faker->randomElement(['success', 'failure', 'pending']),
                'message' => $this->faker->sentence(),
                'transaction_id' => $this->faker->uuid(),
            ],
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'created_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }
    
    /**
     * Indicate that the log is for a successful event.
     *
     * @return static
     */
    public function success(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'success',
            'response_data' => array_merge($attributes['response_data'] ?? [], [
                'status' => 'success',
                'message' => 'Transaction successful',
            ]),
        ]);
    }
    
    /**
     * Indicate that the log is for a failed event.
     *
     * @return static
     */
    public function failure(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failure',
            'response_data' => array_merge($attributes['response_data'] ?? [], [
                'status' => 'failure',
                'message' => 'Transaction failed',
                'error_code' => $this->faker->randomElement(['PAYMENT_ERROR', 'GATEWAY_ERROR', 'VALIDATION_ERROR']),
            ]),
        ]);
    }
    
    /**
     * Indicate that the log is for a specific event.
     *
     * @param string $event
     * @return static
     */
    public function forEvent(string $event): static
    {
        return $this->state(fn (array $attributes) => [
            'event' => $event,
        ]);
    }
    
    /**
     * Indicate that the log is for a specific gateway.
     *
     * @param string $gateway
     * @return static
     */
    public function forGateway(string $gateway): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway' => $gateway,
        ]);
    }
    
    /**
     * Indicate that the log is for a specific transaction.
     *
     * @param int|string $transactionId
     * @return static
     */
    public function forTransaction(int|string $transactionId): static
    {
        return $this->state(fn (array $attributes) => [
            'transaction_id' => $transactionId,
        ]);
    }
}

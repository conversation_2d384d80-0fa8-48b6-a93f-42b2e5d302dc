<?php

namespace Database\Factories;

use App\Models\PaymentMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentMethod>
 */
class PaymentMethodFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PaymentMethod::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_id' => $this->faker->numberBetween(1, 1000),
            'gateway' => $this->faker->randomElement(['stripe', 'paypal', 'payu', 'instamojo', 'paytm', 'payeezy', 'mobikwik', 'converge', 'yesbank']),
            'token' => $this->faker->uuid(),
            'type' => $this->faker->randomElement(['credit_card', 'debit_card', 'bank_account', 'wallet']),
            'last_four' => $this->faker->numerify('####'),
            'expiry_month' => $this->faker->numerify('##'),
            'expiry_year' => $this->faker->numerify('20##'),
            'card_holder_name' => $this->faker->name(),
            'card_brand' => $this->faker->randomElement(['visa', 'mastercard', 'amex', 'discover']),
            'is_default' => false,
            'is_active' => true,
            'metadata' => null,
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }
    
    /**
     * Indicate that the payment method is the default.
     *
     * @return static
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }
    
    /**
     * Indicate that the payment method is inactive.
     *
     * @return static
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
    
    /**
     * Indicate that the payment method is a credit card.
     *
     * @return static
     */
    public function creditCard(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'credit_card',
            'last_four' => $this->faker->numerify('####'),
            'expiry_month' => sprintf('%02d', $this->faker->numberBetween(1, 12)),
            'expiry_year' => (string) $this->faker->numberBetween(date('Y'), date('Y') + 10),
            'card_brand' => $this->faker->randomElement(['visa', 'mastercard', 'amex', 'discover']),
        ]);
    }
    
    /**
     * Indicate that the payment method is a bank account.
     *
     * @return static
     */
    public function bankAccount(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'bank_account',
            'last_four' => $this->faker->numerify('####'),
            'expiry_month' => null,
            'expiry_year' => null,
            'card_brand' => null,
            'card_holder_name' => $this->faker->name(),
        ]);
    }
    
    /**
     * Indicate that the payment method is a wallet.
     *
     * @return static
     */
    public function wallet(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'wallet',
            'last_four' => null,
            'expiry_month' => null,
            'expiry_year' => null,
            'card_brand' => null,
            'card_holder_name' => null,
        ]);
    }
    
    /**
     * Indicate that the payment method is for a specific customer.
     *
     * @param int $customerId
     * @return static
     */
    public function forCustomer(int $customerId): static
    {
        return $this->state(fn (array $attributes) => [
            'customer_id' => $customerId,
        ]);
    }
    
    /**
     * Indicate that the payment method is for a specific gateway.
     *
     * @param string $gateway
     * @return static
     */
    public function forGateway(string $gateway): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway' => $gateway,
        ]);
    }
}

<?php

namespace Database\Factories;

use App\Models\PaymentTransaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentTransaction>
 */
class PaymentTransactionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PaymentTransaction::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'transaction_id' => 'TXN' . $this->faker->unique()->numberBetween(100000, 999999),
            'customer_id' => $this->faker->numberBetween(1, 1000),
            'customer_email' => $this->faker->safeEmail(),
            'customer_phone' => $this->faker->phoneNumber(),
            'customer_name' => $this->faker->name(),
            'amount' => $this->faker->randomFloat(2, 10, 1000),
            'transaction_charges' => $this->faker->randomFloat(2, 0, 10),
            'wallet_amount' => null,
            'order_id' => $this->faker->uuid(),
            'status' => $this->faker->randomElement(['initiated', 'processing', 'completed', 'failed', 'refunded']),
            'gateway' => $this->faker->randomElement(['payu', 'instamojo', 'paytm', 'payeezy', 'mobikwik', 'paypal', 'converge', 'yesbank', 'stripe']),
            'gateway_transaction_id' => $this->faker->uuid(),
            'description' => $this->faker->sentence(),
            'transaction_by' => 'gateway',
            'referer' => $this->faker->randomElement(['website', 'mobileapp', 'desktopapp']),
            'success_url' => $this->faker->url(),
            'failure_url' => $this->faker->url(),
            'context' => $this->faker->randomElement(['order', 'subscription']),
            'recurring' => $this->faker->boolean(),
            'discount' => $this->faker->randomFloat(2, 0, 50),
            'metadata' => null,
        ];
    }
    
    /**
     * Indicate that the transaction is initiated.
     *
     * @return static
     */
    public function initiated(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'initiated',
            'gateway' => null,
            'gateway_transaction_id' => null,
        ]);
    }
    
    /**
     * Indicate that the transaction is processing.
     *
     * @return static
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processing',
        ]);
    }
    
    /**
     * Indicate that the transaction is completed.
     *
     * @return static
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }
    
    /**
     * Indicate that the transaction has failed.
     *
     * @return static
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
        ]);
    }
    
    /**
     * Indicate that the transaction has been refunded.
     *
     * @return static
     */
    public function refunded(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'refunded',
        ]);
    }
}

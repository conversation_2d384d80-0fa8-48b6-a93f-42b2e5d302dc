# Payment Service

A Laravel 12 microservice for processing payments through multiple payment gateways.

## Features

- Support for multiple payment gateways:
  - Stripe
  - PayPal
  - Payu
  - Instamojo
  - Paytm
  - Payeezy
  - Mobikwik
  - Converge
  - Yesbank
- RESTful API for payment processing
- Comprehensive error handling
- Secure data encryption
- Event-driven architecture with RabbitMQ
- Circuit breaker pattern for resilience
- Retry mechanism with exponential backoff
- Correlation IDs for request tracing
- Extensive test coverage (>90%)
- Payment method management
- Payment logging and tracking
- Payment statistics
- Webhook handling
- Wallet integration
- OpenAPI 3.1 specification
- Kong API Gateway integration
- Prometheus metrics for monitoring

## Requirements

- PHP 8.1 or higher
- Composer
- MySQL 8.0 or higher
- Redis
- RabbitMQ
- Docker (for containerization)
- Kubernetes (for deployment)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd services/payment-service-v12
```

2. Install dependencies:
```bash
composer install
```

3. Copy the environment file:
```bash
cp .env.example .env
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Configure the database in the `.env` file:
```
DB_CONNECTION=sqlite
DB_DATABASE=database/payment.sqlite
```

6. Configure payment gateway credentials in the `.env` file:
```
# Stripe
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_MODE=test
STRIPE_CURRENCY=usd

# PayPal
PAYPAL_CLIENT_ID=
PAYPAL_SECRET=
PAYPAL_MODE=test
PAYPAL_CURRENCY=USD

# ... other gateway credentials
```

7. Run migrations:
```bash
php artisan migrate
```

## API Documentation

The API documentation is available in OpenAPI 3.1 format at `openapi/openapi.yaml`.

### Endpoints

#### V1 Endpoints

- `GET /api/v1/payments`: Get a list of payments
- `GET /api/v1/payments/{id}`: Get a payment by ID
- `POST /api/v1/payments/process`: Process a payment
- `GET /api/v1/payments/transaction/{transactionId}/verify`: Verify a payment
- `POST /api/v1/payments/transaction/{transactionId}/refund`: Refund a payment
- `POST /api/v1/payments/transaction/{transactionId}/cancel`: Cancel a payment
- `GET /api/v1/payments/transaction/{transactionId}/status`: Get payment status
- `GET /api/v1/payments/transaction/{transactionId}/details`: Get payment details
- `POST /api/v1/payments/form`: Generate a payment form
- `GET /api/v1/payments/gateways`: Get available payment gateways
- `GET /api/v1/payments/statistics`: Get payment statistics
- `POST /api/v1/payments/webhooks/{gateway}`: Handle payment webhook

#### V2 Endpoints (Legacy Support)

- `POST /api/v2/payments`: Initiate a payment
- `POST /api/v2/payments/{id}/process`: Process a payment with a specific gateway
- `POST /api/v2/payments/callback`: Handle callback from payment gateway
- `GET /api/v2/payments/{id}`: Get payment status
- `POST /api/v2/payments/{id}/refund`: Refund a payment
- `GET /api/v2/payments/statistics`: Get payment statistics
- `GET /api/v2/payments/logs`: Get payment logs
- `GET /api/v2/payments/{id}/logs`: Get logs for a transaction
- `POST /api/v2/payments/webhooks/{gateway}`: Handle payment webhook

#### Payment Methods

- `GET /api/v1/payment-methods/customer/{customerId}`: Get customer payment methods
- `POST /api/v1/payment-methods`: Create a payment method
- `GET /api/v1/payment-methods/{id}`: Get a payment method
- `PUT /api/v1/payment-methods/{id}`: Update a payment method
- `DELETE /api/v1/payment-methods/{id}`: Delete a payment method
- `PUT /api/v1/payment-methods/{id}/default`: Set a payment method as default

#### Health and Monitoring

- `GET /api/v1/health`: Health check endpoint
- `GET /api/v1/metrics`: Prometheus metrics endpoint

## Testing

### Running Tests

Run the tests with:
```bash
php artisan test
```

### Test Coverage

Generate a test coverage report:
```bash
php artisan test --coverage
```

The service has >90% test coverage, with comprehensive unit and integration tests for:
- Payment gateways
- Payment service
- Circuit breaker
- HTTP client
- API controllers
- Event handling

## Security

- Sensitive data is encrypted at rest using AES-256-CBC
- HMAC signature verification for webhooks
- IP whitelisting for webhooks
- JWT authentication for API endpoints
- Rate limiting via Kong API Gateway
- Circuit breaker pattern for resilience
- Correlation IDs for request tracing
- Comprehensive logging and monitoring

## Event System

The service uses an event-driven architecture with RabbitMQ:

- `PaymentCreated`: Fired when a payment is created
- `PaymentSucceeded`: Fired when a payment is successful
- `PaymentFailed`: Fired when a payment fails
- `PaymentRefunded`: Fired when a payment is refunded
- `PaymentCancelled`: Fired when a payment is cancelled
- `PaymentMethodCreated`: Fired when a payment method is created
- `PaymentMethodUpdated`: Fired when a payment method is updated
- `PaymentMethodDeleted`: Fired when a payment method is deleted
- `PaymentMethodSetDefault`: Fired when a payment method is set as default
- `WalletPaymentProcessed`: Fired when a wallet payment is processed

Events are published to RabbitMQ and can be consumed by other services.

## Circuit Breaker Pattern

The service implements the circuit breaker pattern for resilience:

- Prevents cascading failures
- Automatically detects failures
- Transitions between closed, open, and half-open states
- Configurable failure threshold, success threshold, and timeout
- Fallback mechanisms for graceful degradation

## Kong API Gateway Integration

The service is designed to work with Kong API Gateway. The configuration is available in `kong/services/payment-service-v12.yaml`.

Features include:
- Path-based routing (`/api/v1/payments/*`, `/api/v2/payments/*`)
- JWT authentication
- Rate limiting
- CORS configuration
- Request/response transformation
- Logging and monitoring

## Monitoring

The service provides Prometheus metrics for monitoring:

- `payment_service_requests_total`: Total number of requests
- `payment_service_request_duration_seconds`: Request duration in seconds
- `payment_service_payments_total`: Total number of payments
- `payment_service_payment_amount_total`: Total payment amount
- `payment_service_payment_success_rate`: Payment success rate
- `payment_service_circuit_breaker_state`: Circuit breaker state
- `payment_service_circuit_breaker_failures`: Circuit breaker failure count

## Deployment

The service can be deployed using Docker and Kubernetes:

```bash
# Build Docker image
docker build -t payment-service-v12 .

# Run Docker container
docker-compose up -d

# Deploy to Kubernetes
kubectl apply -f kubernetes/
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

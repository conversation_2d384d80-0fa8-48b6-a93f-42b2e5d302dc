<?php

namespace Tests\Performance;

use Tests\TestCase;
use App\Services\PaymentService;
use App\Services\Performance\PaymentPerformanceMonitor;
use App\Services\Cache\PaymentCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

/**
 * Payment Performance Tests
 * 
 * Validates that payment processing meets the <200ms performance target
 * for OneFoodDialer 2025 across all payment gateways and scenarios
 */
class PaymentPerformanceTest extends TestCase
{
    use RefreshDatabase;

    private PaymentService $paymentService;
    private PaymentPerformanceMonitor $performanceMonitor;
    private PaymentCacheService $cacheService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->paymentService = app(PaymentService::class);
        $this->performanceMonitor = app(PaymentPerformanceMonitor::class);
        $this->cacheService = app(PaymentCacheService::class);

        // Warm up cache for performance tests
        $this->cacheService->warmUpCache();
    }

    /** @test */
    public function payment_creation_meets_performance_target()
    {
        $this->markTestSkipped('Performance test - run manually');

        $paymentData = $this->getTestPaymentData();
        $iterations = 100;
        $responseTimes = [];

        for ($i = 0; $i < $iterations; $i++) {
            $startTime = microtime(true);
            
            try {
                $result = $this->paymentService->processPayment($paymentData);
                $endTime = microtime(true);
                
                $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
                $responseTimes[] = $responseTime;
                
                $this->assertTrue($result['success']);
            } catch (\Exception $e) {
                $this->fail("Payment processing failed: " . $e->getMessage());
            }
        }

        // Calculate performance metrics
        $avgResponseTime = array_sum($responseTimes) / count($responseTimes);
        $maxResponseTime = max($responseTimes);
        $p95ResponseTime = $this->calculatePercentile($responseTimes, 95);
        $p99ResponseTime = $this->calculatePercentile($responseTimes, 99);

        // Assert performance targets
        $this->assertLessThan(200, $avgResponseTime, 
            "Average response time ({$avgResponseTime}ms) exceeds 200ms target");
        
        $this->assertLessThan(200, $p95ResponseTime, 
            "95th percentile response time ({$p95ResponseTime}ms) exceeds 200ms target");
        
        $this->assertLessThan(500, $p99ResponseTime, 
            "99th percentile response time ({$p99ResponseTime}ms) exceeds 500ms limit");

        echo "\nPayment Creation Performance Results:\n";
        echo "Average: {$avgResponseTime}ms\n";
        echo "95th percentile: {$p95ResponseTime}ms\n";
        echo "99th percentile: {$p99ResponseTime}ms\n";
        echo "Maximum: {$maxResponseTime}ms\n";
    }

    /** @test */
    public function payment_verification_meets_performance_target()
    {
        $this->markTestSkipped('Performance test - run manually');

        // Mock successful verification responses
        Http::fake([
            '*' => Http::response(['status' => 'success'], 200)
        ]);

        $iterations = 100;
        $responseTimes = [];

        for ($i = 0; $i < $iterations; $i++) {
            $transactionId = 'TXN_' . uniqid();
            
            $startTime = microtime(true);
            
            try {
                $result = $this->paymentService->verifyPayment($transactionId);
                $endTime = microtime(true);
                
                $responseTime = ($endTime - $startTime) * 1000;
                $responseTimes[] = $responseTime;
            } catch (\Exception $e) {
                $this->fail("Payment verification failed: " . $e->getMessage());
            }
        }

        $avgResponseTime = array_sum($responseTimes) / count($responseTimes);
        $p95ResponseTime = $this->calculatePercentile($responseTimes, 95);

        $this->assertLessThan(100, $avgResponseTime, 
            "Payment verification average response time ({$avgResponseTime}ms) exceeds 100ms target");
        
        $this->assertLessThan(200, $p95ResponseTime, 
            "Payment verification 95th percentile ({$p95ResponseTime}ms) exceeds 200ms target");

        echo "\nPayment Verification Performance Results:\n";
        echo "Average: {$avgResponseTime}ms\n";
        echo "95th percentile: {$p95ResponseTime}ms\n";
    }

    /** @test */
    public function wallet_payment_integration_meets_performance_target()
    {
        $this->markTestSkipped('Performance test - run manually');

        $paymentData = $this->getTestPaymentData();
        $paymentData['wallet_amount'] = 50.00; // Use wallet for partial payment
        
        $iterations = 50;
        $responseTimes = [];

        for ($i = 0; $i < $iterations; $i++) {
            $startTime = microtime(true);
            
            try {
                $result = $this->paymentService->processWalletPayment($paymentData);
                $endTime = microtime(true);
                
                $responseTime = ($endTime - $startTime) * 1000;
                $responseTimes[] = $responseTime;
                
                $this->assertTrue($result['success']);
            } catch (\Exception $e) {
                $this->fail("Wallet payment processing failed: " . $e->getMessage());
            }
        }

        $avgResponseTime = array_sum($responseTimes) / count($responseTimes);
        $p95ResponseTime = $this->calculatePercentile($responseTimes, 95);

        $this->assertLessThan(200, $avgResponseTime, 
            "Wallet payment average response time ({$avgResponseTime}ms) exceeds 200ms target");
        
        $this->assertLessThan(300, $p95ResponseTime, 
            "Wallet payment 95th percentile ({$p95ResponseTime}ms) exceeds 300ms limit");

        echo "\nWallet Payment Performance Results:\n";
        echo "Average: {$avgResponseTime}ms\n";
        echo "95th percentile: {$p95ResponseTime}ms\n";
    }

    /** @test */
    public function concurrent_payment_processing_maintains_performance()
    {
        $this->markTestSkipped('Performance test - run manually');

        $concurrentRequests = 10;
        $iterations = 20;
        $allResponseTimes = [];

        for ($i = 0; $i < $iterations; $i++) {
            $promises = [];
            $startTime = microtime(true);

            // Simulate concurrent requests
            for ($j = 0; $j < $concurrentRequests; $j++) {
                $paymentData = $this->getTestPaymentData();
                $paymentData['transaction_id'] = 'TXN_' . uniqid() . '_' . $j;
                
                $promises[] = $this->paymentService->processPayment($paymentData);
            }

            // Wait for all requests to complete
            $endTime = microtime(true);
            $responseTime = ($endTime - $startTime) * 1000;
            $allResponseTimes[] = $responseTime;
        }

        $avgResponseTime = array_sum($allResponseTimes) / count($allResponseTimes);
        $maxResponseTime = max($allResponseTimes);

        $this->assertLessThan(500, $avgResponseTime, 
            "Concurrent payment processing average ({$avgResponseTime}ms) exceeds 500ms limit");
        
        $this->assertLessThan(1000, $maxResponseTime, 
            "Concurrent payment processing maximum ({$maxResponseTime}ms) exceeds 1000ms limit");

        echo "\nConcurrent Payment Processing Performance Results:\n";
        echo "Average: {$avgResponseTime}ms\n";
        echo "Maximum: {$maxResponseTime}ms\n";
    }

    /** @test */
    public function database_query_performance_is_optimized()
    {
        $this->markTestSkipped('Performance test - run manually');

        // Create test data
        $this->createTestTransactions(1000);

        $queries = [
            'customer_payments' => function () {
                return DB::table('payment_transactions')
                    ->where('customer_id', 12345)
                    ->where('status', 'completed')
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get();
            },
            'gateway_transactions' => function () {
                return DB::table('payment_transactions')
                    ->where('gateway', 'payu')
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count();
            },
            'amount_aggregation' => function () {
                return DB::table('payment_transactions')
                    ->where('status', 'completed')
                    ->where('created_at', '>=', now()->subDays(30))
                    ->sum('amount');
            },
        ];

        foreach ($queries as $queryName => $queryFunction) {
            $iterations = 50;
            $responseTimes = [];

            for ($i = 0; $i < $iterations; $i++) {
                $startTime = microtime(true);
                $queryFunction();
                $endTime = microtime(true);
                
                $responseTime = ($endTime - $startTime) * 1000;
                $responseTimes[] = $responseTime;
            }

            $avgResponseTime = array_sum($responseTimes) / count($responseTimes);
            
            $this->assertLessThan(50, $avgResponseTime, 
                "Database query '{$queryName}' average response time ({$avgResponseTime}ms) exceeds 50ms target");

            echo "\nDatabase Query Performance - {$queryName}: {$avgResponseTime}ms\n";
        }
    }

    /** @test */
    public function cache_performance_meets_targets()
    {
        $this->markTestSkipped('Performance test - run manually');

        $iterations = 1000;
        $cacheOperations = [
            'get_gateway_config' => function () {
                return $this->cacheService->getGatewayConfig('payu');
            },
            'cache_transaction' => function () {
                return $this->cacheService->cacheTransaction(
                    'TXN_' . uniqid(),
                    $this->getTestPaymentData()
                );
            },
            'get_customer_summary' => function () {
                return $this->cacheService->getCustomerPaymentSummary(12345);
            },
        ];

        foreach ($cacheOperations as $operationName => $operation) {
            $responseTimes = [];

            for ($i = 0; $i < $iterations; $i++) {
                $startTime = microtime(true);
                $operation();
                $endTime = microtime(true);
                
                $responseTime = ($endTime - $startTime) * 1000;
                $responseTimes[] = $responseTime;
            }

            $avgResponseTime = array_sum($responseTimes) / count($responseTimes);
            $p95ResponseTime = $this->calculatePercentile($responseTimes, 95);
            
            $this->assertLessThan(10, $avgResponseTime, 
                "Cache operation '{$operationName}' average response time ({$avgResponseTime}ms) exceeds 10ms target");
            
            $this->assertLessThan(20, $p95ResponseTime, 
                "Cache operation '{$operationName}' 95th percentile ({$p95ResponseTime}ms) exceeds 20ms target");

            echo "\nCache Performance - {$operationName}: {$avgResponseTime}ms (avg), {$p95ResponseTime}ms (p95)\n";
        }
    }

    /** @test */
    public function memory_usage_is_within_limits()
    {
        $this->markTestSkipped('Performance test - run manually');

        $initialMemory = memory_get_usage(true);
        $iterations = 100;

        for ($i = 0; $i < $iterations; $i++) {
            $paymentData = $this->getTestPaymentData();
            $this->paymentService->processPayment($paymentData);
        }

        $finalMemory = memory_get_usage(true);
        $memoryIncrease = ($finalMemory - $initialMemory) / 1024 / 1024; // MB

        $this->assertLessThan(50, $memoryIncrease, 
            "Memory usage increased by {$memoryIncrease}MB, exceeding 50MB limit");

        echo "\nMemory Usage Test:\n";
        echo "Initial: " . round($initialMemory / 1024 / 1024, 2) . "MB\n";
        echo "Final: " . round($finalMemory / 1024 / 1024, 2) . "MB\n";
        echo "Increase: " . round($memoryIncrease, 2) . "MB\n";
    }

    /**
     * Calculate percentile from array of values
     *
     * @param array $values
     * @param int $percentile
     * @return float
     */
    private function calculatePercentile(array $values, int $percentile): float
    {
        sort($values);
        $index = ($percentile / 100) * (count($values) - 1);
        
        if (floor($index) == $index) {
            return $values[$index];
        } else {
            $lower = $values[floor($index)];
            $upper = $values[ceil($index)];
            return $lower + (($upper - $lower) * ($index - floor($index)));
        }
    }

    /**
     * Get test payment data
     *
     * @return array
     */
    private function getTestPaymentData(): array
    {
        return [
            'transaction_id' => 'TXN_' . uniqid(),
            'order_id' => 'ORDER_' . uniqid(),
            'customer_id' => 12345,
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '9876543210',
            'amount' => 299.99,
            'currency' => 'INR',
            'gateway' => 'payu',
            'description' => 'Performance test payment',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ];
    }

    /**
     * Create test transactions for database performance testing
     *
     * @param int $count
     * @return void
     */
    private function createTestTransactions(int $count): void
    {
        $transactions = [];
        $gateways = ['payu', 'stripe', 'paypal', 'instamojo', 'paytm'];
        $statuses = ['completed', 'failed', 'pending'];

        for ($i = 0; $i < $count; $i++) {
            $transactions[] = [
                'transaction_id' => 'TXN_' . uniqid(),
                'customer_id' => rand(10000, 99999),
                'amount' => rand(100, 10000) / 100,
                'gateway' => $gateways[array_rand($gateways)],
                'status' => $statuses[array_rand($statuses)],
                'created_at' => now()->subDays(rand(0, 30)),
                'updated_at' => now(),
            ];
        }

        DB::table('payment_transactions')->insert($transactions);
    }
}

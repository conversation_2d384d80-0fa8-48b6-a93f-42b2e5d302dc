<?php

namespace Tests\Unit\Http\Controllers;

use App\Http\Controllers\Api\PaymentController;
use App\Http\Requests\Payment\InitiatePaymentRequest;
use App\Http\Requests\Payment\ProcessPaymentRequest;
use App\Http\Requests\Payment\RefundPaymentRequest;
use App\Http\Requests\Payment\StatisticsRequest;
use App\Models\PaymentTransaction;
use App\Models\PaymentLog;
use App\Services\PaymentService;
use App\Services\PaymentMethodService;
use App\Services\PaymentLogService;
use App\Exceptions\Payment\PaymentException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\JsonResponse;
use Tests\TestCase;
use Mockery;

class PaymentControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $paymentController;
    protected $mockPaymentService;
    protected $mockPaymentMethodService;
    protected $mockPaymentLogService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockPaymentService = Mockery::mock(PaymentService::class);
        $this->mockPaymentMethodService = Mockery::mock(PaymentMethodService::class);
        $this->mockPaymentLogService = Mockery::mock(PaymentLogService::class);

        $this->paymentController = new PaymentController(
            $this->mockPaymentService,
            $this->mockPaymentMethodService,
            $this->mockPaymentLogService
        );
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_initiate_payment_successfully()
    {
        // Arrange
        $requestData = [
            'customer_id' => 1,
            'amount' => 100.00,
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ];
        
        $transaction = PaymentTransaction::factory()->make([
            'transaction_id' => 'TXN123456',
            'amount' => 100.00,
            'status' => 'initiated'
        ]);
        
        $request = Mockery::mock(InitiatePaymentRequest::class);
        $request->shouldReceive('validated')->andReturn($requestData);
        
        $this->mockPaymentService->shouldReceive('initiatePayment')
            ->once()
            ->with($requestData)
            ->andReturn($transaction);
        
        // Act
        $response = $this->paymentController->initiate($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(201, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('Payment initiated successfully', $responseData['message']);
        $this->assertEquals('TXN123456', $responseData['data']['transaction_id']);
    }

    /** @test */
    public function it_handles_payment_initiation_failure()
    {
        // Arrange
        $requestData = [
            'customer_id' => 1,
            'amount' => 100.00,
        ];

        $request = Mockery::mock(InitiatePaymentRequest::class);
        $request->shouldReceive('validated')->andReturn($requestData);

        $this->mockPaymentService->shouldReceive('initiatePayment')
            ->once()
            ->with($requestData)
            ->andThrow(new PaymentException('Payment initiation failed'));

        // Act
        $response = $this->paymentController->initiate($request);
        
        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Payment initiation failed', $responseData['message']);
    }
    
    /** @test */
    public function it_can_process_payment_successfully()
    {
        // Arrange
        $transactionId = 'TXN123456';
        $requestData = ['gateway' => 'stripe'];
        
        $request = Mockery::mock(ProcessPaymentRequest::class);
        $request->shouldReceive('validated')->andReturn($requestData);
        
        $formData = [
            'action' => 'https://stripe.com/pay',
            'method' => 'POST',
            'fields' => ['key' => 'value']
        ];
        
        $this->mockPaymentService->shouldReceive('processPayment')
            ->once()
            ->with($transactionId, 'stripe')
            ->andReturn($formData);
        
        // Act
        $response = $this->paymentController->process($request, $transactionId);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('Payment processing initiated', $responseData['message']);
        $this->assertEquals($formData, $responseData['data']);
    }

    /** @test */
    public function it_handles_payment_processing_failure()
    {
        // Arrange
        $transactionId = 'TXN123456';
        $requestData = ['gateway' => 'stripe'];

        $request = Mockery::mock(ProcessPaymentRequest::class);
        $request->shouldReceive('validated')->andReturn($requestData);

        $this->mockPaymentService->shouldReceive('processPayment')
            ->once()
            ->with($transactionId, 'stripe')
            ->andThrow(new PaymentException('Payment processing failed'));

        // Act
        $response = $this->paymentController->process($request, $transactionId);
        
        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Payment processing failed', $responseData['message']);
    }
    
    /** @test */
    public function it_can_handle_callback_successfully()
    {
        // Arrange
        $callbackData = [
            'transaction_id' => 'TXN123456',
            'payment_status' => 'success'
        ];
        
        $transaction = PaymentTransaction::factory()->make([
            'transaction_id' => 'TXN123456',
            'status' => 'completed'
        ]);
        
        $this->mockPaymentService->shouldReceive('handleCallback')
            ->once()
            ->with($callbackData, Mockery::any())
            ->andReturn($transaction);

        // Act
        $request = new \Illuminate\Http\Request($callbackData);
        $response = $this->paymentController->callback($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('Payment callback processed', $responseData['message']);
    }

    /** @test */
    public function it_can_get_transaction_successfully()
    {
        // Arrange
        $transactionId = 'TXN123456';

        $transaction = PaymentTransaction::factory()->make([
            'transaction_id' => 'TXN123456',
            'amount' => 100.00,
            'status' => 'completed'
        ]);

        $this->mockPaymentService->shouldReceive('getTransaction')
            ->once()
            ->with($transactionId)
            ->andReturn($transaction);

        // Act
        $response = $this->paymentController->status($transactionId);
        
        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('TXN123456', $responseData['data']['transaction_id']);
    }
    
    /** @test */
    public function it_handles_transaction_not_found()
    {
        // Arrange
        $transactionId = 'INVALID123';
        
        $this->mockPaymentService->shouldReceive('getTransaction')
            ->once()
            ->with($transactionId)
            ->andThrow(new PaymentException('Transaction not found'));

        // Act
        $response = $this->paymentController->status($transactionId);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Transaction not found', $responseData['message']);
    }

    /** @test */
    public function it_can_refund_payment_successfully()
    {
        // Arrange
        $transactionId = 'TXN123456';
        $requestData = [
            'amount' => 50.00,
            'reason' => 'Customer request'
        ];

        $request = Mockery::mock(RefundPaymentRequest::class);
        $request->shouldReceive('validated')->andReturn($requestData);

        $refundResult = [
            'success' => true,
            'status' => 'refunded',
            'refund_id' => 'REF123456'
        ];

        $this->mockPaymentService->shouldReceive('refundPayment')
            ->once()
            ->with($transactionId, 50.00)
            ->andReturn($refundResult);

        // Act
        $response = $this->paymentController->refund($request, $transactionId);
        
        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('Payment refunded successfully', $responseData['message']);
        $this->assertEquals($refundResult, $responseData['data']);
    }
    
    /** @test */
    public function it_can_get_statistics_successfully()
    {
        // Arrange
        $requestData = [
            'start_date' => '2023-01-01',
            'end_date' => '2023-12-31'
        ];
        
        $request = Mockery::mock(StatisticsRequest::class);
        $request->shouldReceive('validated')->andReturn($requestData);
        
        $statistics = [
            'total_transactions' => 100,
            'total_amount' => 10000.00,
            'transactions_by_status' => [
                'completed' => 80,
                'failed' => 15,
                'pending' => 5
            ]
        ];
        
        $this->mockPaymentService->shouldReceive('getTransactionStatistics')
            ->once()
            ->with($requestData)
            ->andReturn($statistics);

        // Act
        $response = $this->paymentController->statistics($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($statistics, $responseData['data']);
    }

    /** @test */
    public function it_can_get_logs_successfully()
    {
        // Arrange
        $logs = PaymentLog::factory()->count(5)->make();

        $this->mockPaymentLogService->shouldReceive('searchLogs')
            ->once()
            ->with([], 50)
            ->andReturn($logs);

        // Act
        $request = new \Illuminate\Http\Request();
        $response = $this->paymentController->logs($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertCount(5, $responseData['data']);
    }
}

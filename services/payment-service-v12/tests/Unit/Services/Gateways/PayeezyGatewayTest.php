<?php

namespace Tests\Unit\Services\Gateways;

use App\Services\Gateways\Payeezy\PayeezyGateway;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PayeezyGatewayTest extends TestCase
{
    protected $gateway;
    protected $config;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock Log facade
        Log::shouldReceive('error')->andReturn(null);

        $this->config = [
            'id' => 'test_id',
            'key' => 'test_key',
            'hmac_key' => 'test_hmac_key',
            'js_security_key' => 'test_js_security_key',
            'ta_token' => 'test_ta_token',
            'hco_login' => 'test_hco_login',
            'hco_transaction_key' => 'test_hco_transaction_key',
            'mode' => 'test',
        ];

        $this->gateway = new PayeezyGateway();
        $this->gateway->setConfig($this->config);
    }

    public function testGetFormData()
    {
        $transaction = [
            'transaction_id' => '12345',
            'amount' => 100.00,
            'customer_name' => '<PERSON>',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ];

        $formData = $this->gateway->getFormData($transaction);

        $this->assertIsArray($formData);
        $this->assertEquals('payment/payeezy', $formData['action']);
        $this->assertEquals('POST', $formData['method']);
        $this->assertIsArray($formData['fields']);
        $this->assertEquals($transaction['transaction_id'], $formData['fields']['transaction_id']);
        $this->assertEquals($transaction['amount'], $formData['fields']['amount']);
        $this->assertEquals($transaction['customer_name'], $formData['fields']['customer_name']);
        $this->assertEquals($transaction['customer_email'], $formData['fields']['customer_email']);
        $this->assertEquals($transaction['customer_phone'], $formData['fields']['customer_phone']);
        $this->assertEquals($transaction['success_url'], $formData['fields']['success_url']);
        $this->assertEquals($transaction['failure_url'], $formData['fields']['failure_url']);
        $this->assertEquals($this->config['key'], $formData['fields']['api_key']);
        $this->assertEquals($this->config['id'], $formData['fields']['api_token']);
        $this->assertEquals($this->config['hmac_key'], $formData['fields']['hmac_key']);
        $this->assertEquals($this->config['hco_login'], $formData['fields']['hco_login']);
        $this->assertEquals($this->config['hco_transaction_key'], $formData['fields']['hco_transaction_key']);
    }

    public function testProcessPaymentSuccess()
    {
        $data = [
            'transaction_id' => '12345',
            'transaction_tag' => 'tag123',
            'transaction_key' => 'key123',
            'transaction_status' => 'approved',
        ];

        $result = $this->gateway->processPayment($data);

        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('completed', $result['status']);
        $this->assertEquals($data['transaction_tag'], $result['gateway_transaction_id']);
        $this->assertEquals($data, $result['metadata']);
    }

    public function testProcessPaymentFailure()
    {
        $data = [
            'transaction_id' => '12345',
            'transaction_tag' => 'tag123',
            'transaction_key' => 'key123',
            'transaction_status' => 'declined',
        ];

        $result = $this->gateway->processPayment($data);

        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertEquals('failed', $result['status']);
        $this->assertEquals('Transaction status: declined', $result['message']);
        $this->assertEquals($data, $result['metadata']);
    }

    public function testProcessPaymentMissingData()
    {
        $data = [
            'transaction_id' => '12345',
            // Missing transaction_tag and transaction_key
        ];

        $result = $this->gateway->processPayment($data);

        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertEquals('failed', $result['status']);
        $this->assertEquals('Missing transaction data', $result['message']);
    }

    public function testVerifyPayment()
    {
        $transactionId = '12345';
        $data = [
            'transaction_id' => $transactionId, // Add missing transaction_id
            'transaction_tag' => 'tag123',
            'transaction_key' => 'key123',
            'transaction_status' => 'approved',
        ];

        $result = $this->gateway->verifyPayment($transactionId, $data);

        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('completed', $result['status']);
        $this->assertEquals($data['transaction_tag'], $result['gateway_transaction_id']);
        $this->assertEquals($data, $result['metadata']);
    }
}

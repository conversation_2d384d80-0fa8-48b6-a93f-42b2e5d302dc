<?php

namespace Tests\Unit\Services\Gateways;

use App\Services\Gateways\Mobikwik\MobikwikGateway;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class MobikwikGatewayTest extends TestCase
{
    protected $gateway;
    protected $config;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock Log facade
        Log::shouldReceive('error')->andReturn(null);

        $this->config = [
            'merchant_id' => 'test_merchant_id',
            'merchant_key' => 'test_merchant_key',
            'mode' => 'test',
            'test_url' => 'https://test.mobikwik.com/mobikwik/payment',
            'production_url' => 'https://www.mobikwik.com/mobikwik/payment',
        ];

        $this->gateway = new MobikwikGateway();
        $this->gateway->setConfig($this->config);
    }

    public function testGetFormData()
    {
        $transaction = [
            'transaction_id' => '12345',
            'amount' => 100.00,
            'customer_name' => '<PERSON>',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'description' => 'Test payment',
            'order_id' => 'ORD12345',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ];

        $formData = $this->gateway->getFormData($transaction);

        $this->assertIsArray($formData);
        $this->assertEquals($this->config['test_url'], $formData['action']);
        $this->assertEquals('POST', $formData['method']);
        $this->assertIsArray($formData['fields']);
        $this->assertEquals($this->config['merchant_id'], $formData['fields']['merchantid']);
        $this->assertEquals($transaction['transaction_id'], $formData['fields']['orderid']);
        $this->assertEquals($transaction['amount'] * 100, $formData['fields']['amount']); // Amount in paise
        $this->assertEquals($transaction['customer_email'], $formData['fields']['email']);
        $this->assertEquals($transaction['customer_phone'], $formData['fields']['phone']);
        $this->assertEquals($transaction['customer_name'], $formData['fields']['firstname']);
        $this->assertEquals($transaction['description'], $formData['fields']['productinfo']);
        $this->assertEquals($transaction['success_url'], $formData['fields']['surl']);
        $this->assertEquals($transaction['failure_url'], $formData['fields']['furl']);
        $this->assertEquals('SALE', $formData['fields']['txntype']);
        $this->assertEquals($transaction['transaction_id'], $formData['fields']['txnid']);
        $this->assertEquals('INR', $formData['fields']['currency']);
        $this->assertArrayHasKey('checksum', $formData['fields']);
    }

    public function testVerifyPaymentSuccess()
    {
        $transactionId = '12345';

        $data = [
            'orderid' => $transactionId,
            'statuscode' => '0',
            'status' => 'success',
            'pgid' => 'pg12345',
        ];

        // Calculate the actual checksum using the same logic as the gateway
        // The gateway uses ALL data except checksum
        $params = $data; // Use all the data
        ksort($params);
        $paramString = '';
        foreach ($params as $param => $value) {
            if ($value !== '') {
                $paramString .= $param . '=' . $value . '&';
            }
        }
        $paramString = rtrim($paramString, '&');
        $paramString .= $this->config['merchant_key'];
        $validChecksum = hash('sha256', $paramString);

        $data['checksum'] = $validChecksum; // Add checksum to data

        $result = $this->gateway->verifyPayment($transactionId, $data);

        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('completed', $result['status']);
        $this->assertEquals($data['pgid'], $result['gateway_transaction_id']);
        $this->assertEquals($data, $result['metadata']);
    }

    public function testVerifyPaymentFailure()
    {
        $transactionId = '12345';

        $data = [
            'orderid' => $transactionId,
            'statuscode' => '1',
            'status' => 'failure',
            'statusdescription' => 'Payment failed',
            'pgid' => 'pg12345',
        ];

        // Calculate the actual checksum using the same logic as the gateway
        // The gateway uses ALL data except checksum
        $params = $data; // Use all the data
        ksort($params);
        $paramString = '';
        foreach ($params as $param => $value) {
            if ($value !== '') {
                $paramString .= $param . '=' . $value . '&';
            }
        }
        $paramString = rtrim($paramString, '&');
        $paramString .= $this->config['merchant_key'];
        $validChecksum = hash('sha256', $paramString);

        $data['checksum'] = $validChecksum; // Add checksum to data

        $result = $this->gateway->verifyPayment($transactionId, $data);

        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertEquals('failed', $result['status']);
        $this->assertEquals('Payment failed', $result['message']);
        $this->assertEquals($data, $result['metadata']);
    }

    public function testVerifyPaymentInvalidChecksum()
    {
        $transactionId = '12345';
        $data = [
            'orderid' => $transactionId,
            'statuscode' => '0',
            'status' => 'success',
            'pgid' => 'pg12345',
            'checksum' => 'invalid_checksum', // Intentionally invalid
        ];

        $result = $this->gateway->verifyPayment($transactionId, $data);

        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertEquals('failed', $result['status']);
        $this->assertEquals('Invalid checksum', $result['message']);
    }

    public function testGenerateChecksum()
    {
        $params = [
            'merchantid' => 'test_merchant_id',
            'orderid' => '12345',
            'amount' => 10000,
            'txntype' => 'SALE',
        ];

        $key = 'test_merchant_key';

        $method = new \ReflectionMethod(MobikwikGateway::class, 'generateChecksum');
        $method->setAccessible(true);

        $checksum = $method->invoke($this->gateway, $params, $key);

        $this->assertIsString($checksum);
        $this->assertEquals(64, strlen($checksum)); // SHA-256 hash is 64 characters long
    }
}

<?php

namespace Tests\Unit\Services\Gateways;

use App\Services\Gateways\Payu\PayuGateway;
use App\Exceptions\Payment\PaymentException;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

class PayuGatewayTest extends TestCase
{
    protected $payuGateway;
    protected $config;
    
    protected function setUp(): void
    {
        parent::setUp();

        $this->config = [
            'key' => 'test_key',
            'salt' => 'test_salt',
            'mode' => 'test',
            'test_url' => 'https://test.payu.in/_payment',
            'production_url' => 'https://secure.payu.in/_payment',
            'enabled' => true
        ];

        $this->payuGateway = new PayuGateway();
        $this->payuGateway->setConfig($this->config);
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_get_gateway_name()
    {
        // Act
        $name = $this->payuGateway->getName();
        
        // Assert
        $this->assertEquals('payu', $name);
    }
    
    /** @test */
    public function it_is_enabled_by_default()
    {
        // Act
        $isEnabled = $this->payuGateway->isEnabled();
        
        // Assert
        $this->assertTrue($isEnabled);
    }
    
    /** @test */
    public function it_can_generate_form_data()
    {
        // Arrange
        $transaction = [
            'transaction_id' => 'TXN123456',
            'amount' => 100.00,
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'customer_name' => 'Test Customer',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
            'description' => 'Test payment'
        ];
        
        // Act
        $formData = $this->payuGateway->getFormData($transaction);
        
        // Assert
        $this->assertIsArray($formData);
        $this->assertArrayHasKey('action', $formData);
        $this->assertArrayHasKey('method', $formData);
        $this->assertArrayHasKey('fields', $formData);
        
        $fields = $formData['fields'];
        $this->assertEquals('test_key', $fields['key']);
        $this->assertEquals('TXN123456', $fields['txnid']);
        $this->assertEquals(100.00, $fields['amount']);
        $this->assertEquals('<EMAIL>', $fields['email']);
        $this->assertEquals('1234567890', $fields['phone']);
        $this->assertEquals('Test Customer', $fields['firstname']);
        $this->assertEquals('https://example.com/success', $fields['surl']);
        $this->assertEquals('https://example.com/failure', $fields['furl']);
        $this->assertEquals('Test payment', $fields['productinfo']);
        $this->assertArrayHasKey('hash', $fields);
    }
    
    /** @test */
    public function it_generates_correct_hash()
    {
        // Arrange
        $transaction = [
            'transaction_id' => 'TXN123456',
            'amount' => 100.00,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'description' => 'Test payment',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure'
        ];
        
        // Act
        $formData = $this->payuGateway->getFormData($transaction);
        $fields = $formData['fields'];
        
        // Calculate expected hash
        $hashString = $this->config['key'] . '|TXN123456|100|Test payment|Test Customer|<EMAIL>|TXN123456||||||||||' . $this->config['salt'];
        $expectedHash = strtolower(hash('sha512', $hashString));
        
        // Assert
        $this->assertEquals($expectedHash, $fields['hash']);
    }
    
    /** @test */
    public function it_can_verify_payment_success()
    {
        // Arrange
        $transactionId = 'TXN123456';
        $callbackData = [
            'payuMoneyId' => 'PAYU123456',
            'mode' => 'CC',
            'status' => 'success',
            'unmappedstatus' => 'captured',
            'key' => 'test_key',
            'txnid' => 'TXN123456',
            'amount' => '100.00',
            'productinfo' => 'Test payment',
            'firstname' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'udf1' => 'TXN123456',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => ''
        ];

        // Calculate correct hash for verification
        $hashString = $this->config['salt'] . '|success||||||' . $callbackData['udf5'] . '|' . $callbackData['udf4'] . '|' . $callbackData['udf3'] . '|' . $callbackData['udf2'] . '|' . $callbackData['udf1'] . '|<EMAIL>|Test Customer|Test payment|100.00|TXN123456|' . $this->config['key'];
        $callbackData['hash'] = strtolower(hash('sha512', $hashString));

        // Act
        $result = $this->payuGateway->verifyPayment($transactionId, $callbackData);

        // Assert
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('completed', $result['status']);
        $this->assertEquals('PAYU123456', $result['gateway_transaction_id']);
        $this->assertArrayHasKey('metadata', $result);
    }
    
    /** @test */
    public function it_can_verify_payment_failure()
    {
        // Arrange
        $transactionId = 'TXN123456';
        $callbackData = [
            'payuMoneyId' => 'PAYU123456',
            'mode' => 'CC',
            'status' => 'failure',
            'unmappedstatus' => 'failed',
            'key' => 'test_key',
            'txnid' => 'TXN123456',
            'amount' => '100.00',
            'productinfo' => 'Test payment',
            'firstname' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'udf1' => 'TXN123456',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
            'error' => 'Payment declined'
        ];

        // Calculate correct hash for verification
        $hashString = $this->config['salt'] . '|failure||||||' . $callbackData['udf5'] . '|' . $callbackData['udf4'] . '|' . $callbackData['udf3'] . '|' . $callbackData['udf2'] . '|' . $callbackData['udf1'] . '|<EMAIL>|Test Customer|Test payment|100.00|TXN123456|' . $this->config['key'];
        $callbackData['hash'] = strtolower(hash('sha512', $hashString));

        // Act
        $result = $this->payuGateway->verifyPayment($transactionId, $callbackData);

        // Assert
        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertEquals('failed', $result['status']);
        $this->assertEquals('Payment failed', $result['message']);
    }
    
    /** @test */
    public function it_handles_invalid_hash_verification()
    {
        // Arrange
        $transactionId = 'TXN123456';
        $callbackData = [
            'payuMoneyId' => 'PAYU123456',
            'status' => 'success',
            'txnid' => 'TXN123456',
            'amount' => '100.00',
            'productinfo' => 'Test payment',
            'firstname' => 'Test Customer',
            'email' => '<EMAIL>',
            'udf1' => 'TXN123456',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
            'hash' => 'invalid_hash'
        ];

        // Act
        $result = $this->payuGateway->verifyPayment($transactionId, $callbackData);

        // Assert
        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertEquals('failed', $result['status']);
        $this->assertEquals('Hash verification failed', $result['message']);
    }
    
    /** @test */
    public function it_can_refund_payment()
    {
        // Arrange
        $gatewayTransactionId = 'PAYU123456';
        $amount = 50.00;

        // Act
        $result = $this->payuGateway->refundPayment($gatewayTransactionId, $amount);

        // Assert
        $this->assertIsArray($result);
        $this->assertFalse($result['success']); // Will fail due to API call, but structure is correct
        $this->assertEquals('failed', $result['status']);
        $this->assertArrayHasKey('message', $result);
    }

    /** @test */
    public function it_can_cancel_payment()
    {
        // Arrange
        $transactionId = 'TXN123456';

        // Act
        $result = $this->payuGateway->cancelPayment($transactionId);

        // Assert
        $this->assertIsArray($result);
        $this->assertFalse($result['success']); // Will fail due to API call, but structure is correct
        $this->assertEquals('failed', $result['status']);
        $this->assertArrayHasKey('message', $result);
    }
    
    /** @test */
    public function it_returns_correct_action_url_for_test_environment()
    {
        // Arrange
        $transaction = [
            'transaction_id' => 'TXN123456',
            'amount' => 100.00,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'description' => 'Test payment',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure'
        ];
        
        // Act
        $formData = $this->payuGateway->getFormData($transaction);
        
        // Assert
        $this->assertEquals('https://test.payu.in/_payment', $formData['action']);
    }
    
    /** @test */
    public function it_returns_correct_action_url_for_production_environment()
    {
        // Arrange
        $config = $this->config;
        $config['mode'] = 'production';
        $gateway = new PayuGateway();
        $gateway->setConfig($config);

        $transaction = [
            'transaction_id' => 'TXN123456',
            'amount' => 100.00,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'description' => 'Test payment',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure'
        ];

        // Act
        $formData = $gateway->getFormData($transaction);

        // Assert
        $this->assertEquals('https://secure.payu.in/_payment', $formData['action']);
    }
    
    /** @test */
    public function it_handles_missing_required_fields()
    {
        // Arrange
        $transaction = [
            'transaction_id' => 'TXN123456',
            // Missing required fields like success_url, failure_url
        ];

        // Act & Assert
        $this->expectException(\ErrorException::class);
        $this->expectExceptionMessage('Undefined array key "amount"');

        $this->payuGateway->getFormData($transaction);
    }
    
    /** @test */
    public function it_processes_payment_with_pending_status()
    {
        // Act
        $result = $this->payuGateway->processPayment([]);
        
        // Assert
        $this->assertIsArray($result);
        $this->assertFalse($result['success']);
        $this->assertEquals('pending', $result['status']);
        $this->assertEquals('Payment is being processed by PayU', $result['message']);
    }
}

<?php

namespace Tests\Unit\Services\Gateways;

use Tests\TestCase;
use App\Services\Gateways\Instamojo\InstamojoGateway;
use App\Exceptions\Payment\PaymentException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Unit tests for Instamojo Gateway
 * 
 * Tests all payment gateway functionality including payment creation,
 * verification, webhook handling, and refund processing
 */
class InstamojoGatewayTest extends TestCase
{
    private InstamojoGateway $gateway;
    private array $config;
    private array $sampleTransaction;

    protected function setUp(): void
    {
        parent::setUp();

        $this->config = [
            'api_key' => 'test_api_key',
            'auth_token' => 'test_auth_token',
            'mode' => 'test',
            'currency' => 'INR',
        ];

        $this->sampleTransaction = [
            'transaction_id' => 'TXN_123456789',
            'order_id' => 'ORDER_123456',
            'customer_id' => 12345,
            'customer_name' => '<PERSON>',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '9876543210',
            'amount' => 299.99,
            'description' => 'Test payment',
            'success_url' => 'https://example.com/success',
        ];

        $this->gateway = new InstamojoGateway();
        $this->gateway->initialize($this->config);
    }

    /** @test */
    public function it_can_initialize_with_valid_config()
    {
        $gateway = new InstamojoGateway();
        $gateway->initialize($this->config);

        $this->assertInstanceOf(InstamojoGateway::class, $gateway);
    }

    /** @test */
    public function it_throws_exception_for_missing_required_config()
    {
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Instamojo gateway configuration missing: api_key');

        $invalidConfig = $this->config;
        unset($invalidConfig['api_key']);

        $gateway = new InstamojoGateway();
        $gateway->initialize($invalidConfig);
    }

    /** @test */
    public function it_can_create_payment_request_successfully()
    {
        // Mock successful API response
        Http::fake([
            'test.instamojo.com/api/1.1/payment-requests/' => Http::response([
                'success' => true,
                'payment_request' => [
                    'id' => 'PR_123456789',
                    'longurl' => 'https://test.instamojo.com/payment/PR_123456789',
                    'status' => 'Pending'
                ]
            ], 201)
        ]);

        $result = $this->gateway->createPayment($this->sampleTransaction);

        $this->assertTrue($result['success']);
        $this->assertEquals('PR_123456789', $result['payment_request_id']);
        $this->assertEquals('https://test.instamojo.com/payment/PR_123456789', $result['payment_url']);
        $this->assertEquals('initiated', $result['status']);
    }

    /** @test */
    public function it_handles_payment_creation_failure()
    {
        // Mock failed API response
        Http::fake([
            'test.instamojo.com/api/1.1/payment-requests/' => Http::response([
                'success' => false,
                'message' => 'Invalid amount'
            ], 400)
        ]);

        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Failed to create Instamojo payment request: Invalid amount');

        $this->gateway->createPayment($this->sampleTransaction);
    }

    /** @test */
    public function it_can_get_form_data_for_payment()
    {
        // Mock successful API response
        Http::fake([
            'test.instamojo.com/api/1.1/payment-requests/' => Http::response([
                'success' => true,
                'payment_request' => [
                    'id' => 'PR_123456789',
                    'longurl' => 'https://test.instamojo.com/payment/PR_123456789',
                ]
            ], 201)
        ]);

        $formData = $this->gateway->getFormData($this->sampleTransaction);

        $this->assertEquals('https://test.instamojo.com/payment/PR_123456789', $formData['action']);
        $this->assertEquals('GET', $formData['method']);
        $this->assertArrayHasKey('transaction_id', $formData['fields']);
        $this->assertArrayHasKey('payment_request_id', $formData['fields']);
    }

    /** @test */
    public function it_can_verify_payment_successfully()
    {
        // Mock successful verification response
        Http::fake([
            'test.instamojo.com/api/1.1/payments/PAY_123456789/' => Http::response([
                'success' => true,
                'payment' => [
                    'payment_id' => 'PAY_123456789',
                    'status' => 'Credit',
                    'amount' => '299.99',
                    'currency' => 'INR'
                ]
            ], 200)
        ]);

        $result = $this->gateway->verifyPayment('PAY_123456789');

        $this->assertTrue($result['success']);
        $this->assertEquals('completed', $result['status']);
        $this->assertEquals('PAY_123456789', $result['gateway_transaction_id']);
        $this->assertEquals('299.99', $result['amount']);
        $this->assertEquals('INR', $result['currency']);
    }

    /** @test */
    public function it_handles_payment_verification_failure()
    {
        // Mock failed verification response
        Http::fake([
            'test.instamojo.com/api/1.1/payments/PAY_123456789/' => Http::response([
                'success' => false,
                'message' => 'Payment not found'
            ], 404)
        ]);

        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Failed to verify Instamojo payment: Payment not found');

        $this->gateway->verifyPayment('PAY_123456789');
    }

    /** @test */
    public function it_can_handle_webhook_successfully()
    {
        // Mock successful verification response for webhook
        Http::fake([
            'test.instamojo.com/api/1.1/payments/PAY_123456789/' => Http::response([
                'success' => true,
                'payment' => [
                    'payment_id' => 'PAY_123456789',
                    'status' => 'Credit',
                    'amount' => '299.99',
                    'currency' => 'INR'
                ]
            ], 200)
        ]);

        $webhookData = [
            'payment_id' => 'PAY_123456789',
            'payment_request_id' => 'PR_123456789',
            'status' => 'Credit'
        ];

        $result = $this->gateway->handleWebhook($webhookData);

        $this->assertTrue($result['success']);
        $this->assertEquals('PAY_123456789', $result['payment_id']);
        $this->assertEquals('PR_123456789', $result['payment_request_id']);
        $this->assertEquals('completed', $result['status']);
    }

    /** @test */
    public function it_handles_invalid_webhook_data()
    {
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Invalid Instamojo webhook data');

        $invalidWebhookData = [
            'payment_id' => 'PAY_123456789',
            // Missing payment_request_id
        ];

        $this->gateway->handleWebhook($invalidWebhookData);
    }

    /** @test */
    public function it_can_process_refund_successfully()
    {
        // Mock successful refund response
        Http::fake([
            'test.instamojo.com/api/1.1/refunds/' => Http::response([
                'success' => true,
                'refund' => [
                    'id' => 'REF_123456789',
                    'status' => 'Pending'
                ]
            ], 201)
        ]);

        $result = $this->gateway->refundPayment('PAY_123456789', 100.00, 'Customer request');

        $this->assertTrue($result['success']);
        $this->assertEquals('REF_123456789', $result['refund_id']);
        $this->assertEquals('pending', $result['status']);
        $this->assertEquals(100.00, $result['amount']);
    }

    /** @test */
    public function it_handles_refund_failure()
    {
        // Mock failed refund response
        Http::fake([
            'test.instamojo.com/api/1.1/refunds/' => Http::response([
                'success' => false,
                'message' => 'Payment not eligible for refund'
            ], 400)
        ]);

        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Failed to create Instamojo refund: Payment not eligible for refund');

        $this->gateway->refundPayment('PAY_123456789', 100.00);
    }

    /** @test */
    public function it_maps_payment_status_correctly()
    {
        $reflection = new \ReflectionClass($this->gateway);
        $method = $reflection->getMethod('mapPaymentStatus');
        $method->setAccessible(true);

        $this->assertEquals('completed', $method->invoke($this->gateway, 'Credit'));
        $this->assertEquals('completed', $method->invoke($this->gateway, 'credit'));
        $this->assertEquals('failed', $method->invoke($this->gateway, 'Failed'));
        $this->assertEquals('failed', $method->invoke($this->gateway, 'failed'));
        $this->assertEquals('pending', $method->invoke($this->gateway, 'Pending'));
        $this->assertEquals('pending', $method->invoke($this->gateway, 'unknown_status'));
    }

    /** @test */
    public function it_prepares_payment_data_correctly()
    {
        $reflection = new \ReflectionClass($this->gateway);
        $method = $reflection->getMethod('preparePaymentData');
        $method->setAccessible(true);

        $result = $method->invoke($this->gateway, $this->sampleTransaction);

        $this->assertEquals('Test payment', $result['purpose']);
        $this->assertEquals(299.99, $result['amount']);
        $this->assertEquals('9876543210', $result['phone']);
        $this->assertEquals('John Doe', $result['buyer_name']);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals('https://example.com/success', $result['redirect_url']);
        $this->assertTrue($result['send_email']);
        $this->assertFalse($result['send_sms']);
        $this->assertFalse($result['allow_repeated_payments']);
    }

    /** @test */
    public function it_handles_api_call_timeout()
    {
        // Mock timeout response
        Http::fake([
            'test.instamojo.com/api/1.1/payment-requests/' => function () {
                throw new \Exception('Connection timeout');
            }
        ]);

        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Instamojo payment creation failed: Connection timeout');

        $this->gateway->createPayment($this->sampleTransaction);
    }

    /** @test */
    public function it_logs_errors_appropriately()
    {
        Log::shouldReceive('error')
            ->once()
            ->with('Instamojo payment creation failed', \Mockery::type('array'));

        Http::fake([
            'test.instamojo.com/api/1.1/payment-requests/' => Http::response([], 500)
        ]);

        try {
            $this->gateway->createPayment($this->sampleTransaction);
        } catch (PaymentException $e) {
            // Expected exception
        }
    }
}

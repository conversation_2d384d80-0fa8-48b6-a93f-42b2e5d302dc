<?php

namespace Tests\Unit\Services\Payment\Gateway;

use App\Services\Payment\Gateway\PayuGateway;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PayuGatewayTest extends TestCase
{
    /**
     * @var PayuGateway
     */
    protected $gateway;

    /**
     * @var MockHandler
     */
    protected $mockHandler;

    /**
     * Set up the test.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Create a mock handler
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);
        $client = new Client(['handler' => $handlerStack]);

        // Create the gateway
        $this->gateway = new PayuGateway();

        // Initialize the gateway with test configuration
        $this->gateway->initialize([
            'enabled' => true,
            'mode' => 'test',
            'merchant_key' => 'test_key',
            'merchant_salt' => 'test_salt',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
            'cancel_url' => 'https://example.com/cancel',
        ]);

        // Replace the HTTP client with our mock
        $reflection = new \ReflectionClass($this->gateway);
        $property = $reflection->getProperty('httpClient');
        $property->setAccessible(true);
        $property->setValue($this->gateway, $client);
    }

    /**
     * Test that the gateway is initialized correctly.
     *
     * @return void
     */
    public function testInitialize(): void
    {
        $this->assertTrue($this->gateway->isEnabled());
        $this->assertEquals('payu', $this->gateway->getName());
        $this->assertTrue($this->gateway->isTestMode());
    }

    /**
     * Test that the gateway processes a payment successfully.
     *
     * @return void
     */
    public function testProcessPayment(): void
    {
        // Prepare payment data
        $paymentData = [
            'order_id' => '123',
            'amount' => 100.00,
            'currency' => 'INR',
            'customer_id' => '456',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'product_info' => 'Test Product',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
            'cancel_url' => 'https://example.com/cancel',
        ];

        // Process the payment
        $result = $this->gateway->processPayment($paymentData);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('payu', $result['gateway']);
        $this->assertTrue($result['redirect']);
        $this->assertArrayHasKey('form_data', $result);
        $this->assertEquals('https://test.payu.in/_payment', $result['form_data']['action']);
        $this->assertEquals('POST', $result['form_data']['method']);
    }

    /**
     * Test that the gateway validates payment data.
     *
     * @return void
     */
    public function testValidatePaymentData(): void
    {
        // Prepare invalid payment data (missing required fields)
        $paymentData = [
            'amount' => 100.00,
            'currency' => 'INR',
        ];

        // Process the payment
        $result = $this->gateway->processPayment($paymentData);

        // Assert the result
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertCount(4, $result['errors']); // order_id, customer_id, customer_email, customer_phone
    }

    /**
     * Test that the gateway verifies a payment successfully.
     *
     * @return void
     */
    public function testVerifyPayment(): void
    {
        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode([
            'status' => 1,
            'msg' => 'Transaction details fetched successfully',
            'transaction_details' => [
                'PAYU123456' => [
                    'status' => 'success',
                    'amount' => '100.00',
                    'mode' => 'CC',
                    'bank_ref_num' => '123456',
                    'mihpayid' => '123456',
                    'txnid' => 'PAYU123456',
                ],
            ],
        ])));

        // Verify the payment
        $result = $this->gateway->verifyPayment('PAYU123456');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('PAYU123456', $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('payu', $result['gateway']);
    }

    /**
     * Test that the gateway refunds a payment successfully.
     *
     * @return void
     */
    public function testRefundPayment(): void
    {
        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode([
            'status' => 1,
            'msg' => 'Refund request submitted successfully',
            'request_id' => 'REF123456',
        ])));

        // Refund the payment
        $result = $this->gateway->refundPayment('PAYU123456', 50.00);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('PAYU123456', $result['transaction_id']);
        $this->assertEquals('REF123456', $result['refund_id']);
        $this->assertEquals('refunded', $result['status']);
        $this->assertEquals('payu', $result['gateway']);
    }

    /**
     * Test that the gateway cancels a payment successfully.
     *
     * @return void
     */
    public function testCancelPayment(): void
    {
        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode([
            'status' => 1,
            'msg' => 'Transaction cancelled successfully',
        ])));

        // Cancel the payment
        $result = $this->gateway->cancelPayment('PAYU123456');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('PAYU123456', $result['transaction_id']);
        $this->assertEquals('cancelled', $result['status']);
        $this->assertEquals('payu', $result['gateway']);
    }

    /**
     * Test that the gateway gets payment status successfully.
     *
     * @return void
     */
    public function testGetPaymentStatus(): void
    {
        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode([
            'status' => 1,
            'msg' => 'Transaction details fetched successfully',
            'transaction_details' => [
                'PAYU123456' => [
                    'status' => 'success',
                    'amount' => '100.00',
                    'mode' => 'CC',
                    'bank_ref_num' => '123456',
                    'mihpayid' => '123456',
                    'txnid' => 'PAYU123456',
                ],
            ],
        ])));

        // Get payment status
        $result = $this->gateway->getPaymentStatus('PAYU123456');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('PAYU123456', $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('payu', $result['gateway']);
    }

    /**
     * Test that the gateway gets payment details successfully.
     *
     * @return void
     */
    public function testGetPaymentDetails(): void
    {
        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode([
            'status' => 1,
            'msg' => 'Transaction details fetched successfully',
            'transaction_details' => [
                'PAYU123456' => [
                    'status' => 'success',
                    'amount' => '100.00',
                    'currency' => 'INR',
                    'mode' => 'CC',
                    'bank_ref_num' => '123456',
                    'mihpayid' => '123456',
                    'txnid' => 'PAYU123456',
                    'addedon' => '2023-06-01 12:00:00',
                ],
            ],
        ])));

        // Get payment details
        $result = $this->gateway->getPaymentDetails('PAYU123456');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('PAYU123456', $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals(100.00, $result['amount']);
        $this->assertEquals('INR', $result['currency']);
        $this->assertEquals('CC', $result['payment_method']);
        $this->assertEquals('2023-06-01 12:00:00', $result['payment_date']);
        $this->assertEquals('payu', $result['gateway']);
    }

    /**
     * Test that the gateway generates a payment form successfully.
     *
     * @return void
     */
    public function testGeneratePaymentForm(): void
    {
        // Prepare payment data
        $paymentData = [
            'key' => 'test_key',
            'txnid' => 'PAYU123456',
            'amount' => 100.00,
            'productinfo' => 'Test Product',
            'firstname' => 'John',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'surl' => 'https://example.com/success',
            'furl' => 'https://example.com/failure',
            'curl' => 'https://example.com/cancel',
        ];

        // Generate the payment form
        $result = $this->gateway->generatePaymentForm($paymentData);

        // Assert the result
        $this->assertEquals('https://test.payu.in/_payment', $result['action']);
        $this->assertEquals('POST', $result['method']);
        $this->assertArrayHasKey('fields', $result);
        $this->assertArrayHasKey('hash', $result['fields']);
    }

    /**
     * Test that the gateway handles a webhook successfully.
     *
     * @return void
     */
    public function testHandleWebhook(): void
    {
        // Prepare webhook data
        $webhookData = [
            'key' => 'test_key',
            'txnid' => 'PAYU123456',
            'amount' => '100.00',
            'productinfo' => 'Test Product',
            'firstname' => 'John',
            'email' => '<EMAIL>',
            'status' => 'success',
            'hash' => 'test_hash',
            'mode' => 'CC',
            'currency' => 'INR',
        ];

        // Mock the hash verification
        $gateway = $this->createPartialMock(PayuGateway::class, ['generateHash']);
        $gateway->method('generateHash')->willReturn('test_hash');
        $gateway->initialize([
            'enabled' => true,
            'mode' => 'test',
            'merchant_key' => 'test_key',
            'merchant_salt' => 'test_salt',
        ]);

        // Handle the webhook
        $result = $gateway->handleWebhook($webhookData);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('PAYU123456', $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals(100.00, $result['amount']);
        $this->assertEquals('INR', $result['currency']);
        $this->assertEquals('CC', $result['payment_method']);
        $this->assertEquals('payu', $result['gateway']);
    }
}

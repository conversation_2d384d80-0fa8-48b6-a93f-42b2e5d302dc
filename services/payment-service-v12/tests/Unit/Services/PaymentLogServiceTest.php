<?php

namespace Tests\Unit\Services;

use App\Models\PaymentLog;
use App\Services\PaymentLogService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class PaymentLogServiceTest extends TestCase
{
    use RefreshDatabase;
    
    protected $service;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new PaymentLogService();
    }
    
    public function testCreateLog()
    {
        $data = [
            'transaction_id' => 123,
            'gateway' => 'stripe',
            'event' => 'payment',
            'status' => 'success',
            'request_data' => ['amount' => 100],
            'response_data' => ['status' => 'paid'],
        ];
        
        $log = $this->service->createLog($data);
        
        $this->assertInstanceOf(PaymentLog::class, $log);
        $this->assertEquals(123, $log->transaction_id);
        $this->assertEquals('stripe', $log->gateway);
        $this->assertEquals('payment', $log->event);
        $this->assertEquals('success', $log->status);
        $this->assertEquals(['amount' => 100], $log->request_data);
        $this->assertEquals(['status' => 'paid'], $log->response_data);
    }
    
    public function testCreateLogWithRequest()
    {
        $data = [
            'transaction_id' => 123,
            'gateway' => 'stripe',
            'event' => 'payment',
            'status' => 'success',
        ];
        
        $request = Request::create('/test', 'POST');
        $request->server->set('REMOTE_ADDR', '127.0.0.1');
        $request->headers->set('User-Agent', 'PHPUnit');
        
        $log = $this->service->createLog($data, $request);
        
        $this->assertInstanceOf(PaymentLog::class, $log);
        $this->assertEquals('127.0.0.1', $log->ip_address);
        $this->assertEquals('PHPUnit', $log->user_agent);
    }
    
    public function testLogEvent()
    {
        $log = $this->service->logEvent(
            'payment',
            'success',
            ['amount' => 100],
            ['status' => 'paid'],
            123,
            'stripe'
        );
        
        $this->assertInstanceOf(PaymentLog::class, $log);
        $this->assertEquals(123, $log->transaction_id);
        $this->assertEquals('stripe', $log->gateway);
        $this->assertEquals('payment', $log->event);
        $this->assertEquals('success', $log->status);
        $this->assertEquals(['amount' => 100], $log->request_data);
        $this->assertEquals(['status' => 'paid'], $log->response_data);
    }
    
    public function testGetLogsForTransaction()
    {
        // Create test logs
        PaymentLog::factory()->count(3)->forTransaction(123)->create();
        PaymentLog::factory()->count(2)->forTransaction(456)->create();
        
        $logs = $this->service->getLogsForTransaction(123);
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(3, $logs);
        $this->assertEquals(123, $logs->first()->transaction_id);
    }
    
    public function testGetLogsForGateway()
    {
        // Create test logs
        PaymentLog::factory()->count(3)->forGateway('stripe')->create();
        PaymentLog::factory()->count(2)->forGateway('paypal')->create();
        
        $logs = $this->service->getLogsForGateway('stripe');
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(3, $logs);
        $this->assertEquals('stripe', $logs->first()->gateway);
        
        // Test with limit
        $logs = $this->service->getLogsForGateway('stripe', 2);
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(2, $logs);
    }
    
    public function testGetLogsForEvent()
    {
        // Create test logs
        PaymentLog::factory()->count(3)->forEvent('payment')->create();
        PaymentLog::factory()->count(2)->forEvent('refund')->create();
        
        $logs = $this->service->getLogsForEvent('payment');
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(3, $logs);
        $this->assertEquals('payment', $logs->first()->event);
        
        // Test with limit
        $logs = $this->service->getLogsForEvent('payment', 2);
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(2, $logs);
    }
    
    public function testGetLogsWithStatus()
    {
        // Create test logs
        PaymentLog::factory()->count(3)->success()->create();
        PaymentLog::factory()->count(2)->failure()->create();
        
        $logs = $this->service->getLogsWithStatus('success');
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(3, $logs);
        $this->assertEquals('success', $logs->first()->status);
        
        // Test with limit
        $logs = $this->service->getLogsWithStatus('success', 2);
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(2, $logs);
    }
    
    public function testGetRecentLogs()
    {
        // Create test logs
        PaymentLog::factory()->count(5)->create();
        
        $logs = $this->service->getRecentLogs(3);
        
        $this->assertInstanceOf(Collection::class, $logs);
        $this->assertCount(3, $logs);
    }
    
    public function testSearchLogs()
    {
        // Create test logs
        PaymentLog::factory()->forTransaction(123)->forGateway('stripe')->forEvent('payment')->success()->create();
        PaymentLog::factory()->forTransaction(123)->forGateway('stripe')->forEvent('refund')->failure()->create();
        PaymentLog::factory()->forTransaction(456)->forGateway('paypal')->forEvent('payment')->success()->create();
        
        // Test search by transaction_id
        $logs = $this->service->searchLogs(['transaction_id' => 123]);
        $this->assertCount(2, $logs);
        
        // Test search by gateway
        $logs = $this->service->searchLogs(['gateway' => 'stripe']);
        $this->assertCount(2, $logs);
        
        // Test search by event
        $logs = $this->service->searchLogs(['event' => 'payment']);
        $this->assertCount(2, $logs);
        
        // Test search by status
        $logs = $this->service->searchLogs(['status' => 'success']);
        $this->assertCount(2, $logs);
        
        // Test search by multiple criteria
        $logs = $this->service->searchLogs([
            'transaction_id' => 123,
            'event' => 'payment',
            'status' => 'success'
        ]);
        $this->assertCount(1, $logs);
        
        // Test with limit
        $logs = $this->service->searchLogs(['status' => 'success'], 1);
        $this->assertCount(1, $logs);
    }
}

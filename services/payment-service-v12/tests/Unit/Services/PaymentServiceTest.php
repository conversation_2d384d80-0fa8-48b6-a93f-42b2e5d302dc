<?php

namespace Tests\Unit\Services;

use App\Exceptions\Payment\PaymentException;
use App\Models\PaymentTransaction;
use App\Models\PaymentMethod;
use App\Models\PaymentLog;
use App\Services\Gateways\Contracts\PaymentGatewayInterface;
use App\Services\PaymentService;
use App\Events\Payment\PaymentCreated;
use App\Events\Payment\PaymentSucceeded;
use App\Events\Payment\PaymentFailed;
use App\Events\Payment\PaymentRefunded;
use App\Events\Payment\PaymentCancelled;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;
use Mockery;

class PaymentServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $paymentService;
    protected $mockGateway;
    protected $mockStripeGateway;
    protected $mockPayuGateway;

    protected function setUp(): void
    {
        parent::setUp();

        Event::fake();

        $this->mockGateway = Mockery::mock(PaymentGatewayInterface::class);
        $this->mockStripeGateway = Mockery::mock(PaymentGatewayInterface::class);
        $this->mockPayuGateway = Mockery::mock(PaymentGatewayInterface::class);

        $this->paymentService = new PaymentService();
        $this->paymentService->registerGateway('test_gateway', $this->mockGateway);
        $this->paymentService->registerGateway('stripe', $this->mockStripeGateway);
        $this->paymentService->registerGateway('payu', $this->mockPayuGateway);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_register_and_get_a_gateway()
    {
        // Act
        $gateway = $this->paymentService->getGateway('test_gateway');

        // Assert
        $this->assertSame($this->mockGateway, $gateway);
    }

    /** @test */
    public function it_throws_exception_for_unknown_gateway()
    {
        // Arrange & Act & Assert
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage("Payment gateway 'unknown_gateway' not found");

        $this->paymentService->getGateway('unknown_gateway');
    }

    /** @test */
    public function it_can_check_if_gateway_exists()
    {
        // Act & Assert
        $gateway = $this->paymentService->getGateway('test_gateway');
        $this->assertNotNull($gateway);

        // Test non-existent gateway
        try {
            $this->paymentService->getGateway('non_existent');
            $this->fail('Expected PaymentException was not thrown');
        } catch (PaymentException $e) {
            $this->assertStringContainsString('not found', $e->getMessage());
        }
    }

    /** @test */
    public function it_can_initiate_payment_successfully()
    {
        // Arrange
        $paymentData = [
            'customer_id' => 1,
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'customer_name' => 'Test Customer',
            'amount' => 100.00,
            'currency' => 'INR',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ];

        // Act
        $result = $this->paymentService->initiatePayment($paymentData);

        // Assert
        $this->assertInstanceOf(PaymentTransaction::class, $result);
        $this->assertEquals('initiated', $result->status);
        $this->assertEquals(100.00, $result->amount);
        $this->assertEquals('<EMAIL>', $result->customer_email);
    }

    /** @test */
    public function it_handles_missing_required_fields()
    {
        // Arrange
        $paymentData = [
            'customer_id' => 1,
            'amount' => 100.00,
            // Missing required fields like success_url, failure_url
        ];

        // Act & Assert
        $this->expectException(PaymentException::class);

        $this->paymentService->initiatePayment($paymentData);
    }

    /** @test */
    public function it_can_process_payment_successfully()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'initiated',
            'gateway' => 'test_gateway'
        ]);

        $this->mockGateway->shouldReceive('getFormData')->andReturn([
            'action' => 'https://gateway.com/pay',
            'method' => 'POST',
            'fields' => ['key' => 'value']
        ]);

        // Act
        $result = $this->paymentService->processPayment($transaction->transaction_id, 'test_gateway');

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('https://gateway.com/pay', $result['action']);
    }

    /** @test */
    public function it_handles_payment_processing_exception()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'initiated',
            'gateway' => 'test_gateway'
        ]);

        $this->mockGateway->shouldReceive('getFormData')
            ->andThrow(new \Exception('Gateway error'));

        // Act & Assert
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Payment processing failed: Gateway error');

        $this->paymentService->processPayment($transaction->transaction_id, 'test_gateway');
    }

    /** @test */
    public function it_can_refund_payment_successfully()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'completed',
            'gateway' => 'test_gateway',
            'gateway_transaction_id' => 'gw_123456'
        ]);

        $this->mockGateway->shouldReceive('refundPayment')->andReturn([
            'success' => true,
            'status' => 'refunded',
            'refund_id' => 'ref_123456'
        ]);

        // Act
        $result = $this->paymentService->refundPayment($transaction->transaction_id, 50.00);

        // Assert
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('refunded', $result['status']);
    }

    /** @test */
    public function it_throws_exception_when_refunding_non_completed_payment()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'initiated',
            'gateway' => 'test_gateway'
        ]);

        // Act & Assert
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Cannot refund a transaction that is not completed');

        $this->paymentService->refundPayment($transaction->transaction_id, 50.00);
    }

    /** @test */
    public function it_can_handle_callback_data()
    {
        // Arrange
        $callbackData = [
            'transaction_id' => 'TXN123456',
            'status' => 'success',
            'gateway' => 'test_gateway'
        ];

        // Mock the gateway verification
        $this->mockGateway->shouldReceive('verifyPayment')->andReturn([
            'success' => true,
            'status' => 'completed',
            'gateway_transaction_id' => 'gw_123456'
        ]);

        // Act & Assert - This will test the callback handling logic
        $this->expectException(PaymentException::class);
        $this->paymentService->handleCallback($callbackData);
    }

    /** @test */
    public function it_throws_exception_for_non_existent_transaction()
    {
        // Act & Assert
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Transaction with ID INVALID123 not found');

        $this->paymentService->getTransaction('INVALID123');
    }
}

<?php

namespace Tests\Unit\Services;

use App\Exceptions\Payment\PaymentException;
use App\Models\PaymentMethod;
use App\Services\PaymentMethodService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentMethodServiceTest extends TestCase
{
    use RefreshDatabase;
    
    protected $service;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new PaymentMethodService();
    }
    
    public function testGetCustomerPaymentMethods()
    {
        // Create test payment methods
        $customerId = 1;
        PaymentMethod::factory()->count(3)->forCustomer($customerId)->create();
        PaymentMethod::factory()->forCustomer($customerId)->inactive()->create();
        PaymentMethod::factory()->count(2)->forCustomer(2)->create();
        
        // Test with active_only = true (default)
        $methods = $this->service->getCustomerPaymentMethods($customerId);
        $this->assertInstanceOf(Collection::class, $methods);
        $this->assertCount(3, $methods);
        
        // Test with active_only = false
        $methods = $this->service->getCustomerPaymentMethods($customerId, false);
        $this->assertInstanceOf(Collection::class, $methods);
        $this->assertCount(4, $methods);
    }
    
    public function testGetPaymentMethod()
    {
        // Create test payment method
        $paymentMethod = PaymentMethod::factory()->create();
        
        // Test retrieval
        $retrievedMethod = $this->service->getPaymentMethod($paymentMethod->id);
        $this->assertInstanceOf(PaymentMethod::class, $retrievedMethod);
        $this->assertEquals($paymentMethod->id, $retrievedMethod->id);
        
        // Test with customer ID
        $retrievedMethod = $this->service->getPaymentMethod($paymentMethod->id, $paymentMethod->customer_id);
        $this->assertInstanceOf(PaymentMethod::class, $retrievedMethod);
        $this->assertEquals($paymentMethod->id, $retrievedMethod->id);
        
        // Test with wrong customer ID
        $this->expectException(PaymentException::class);
        $this->service->getPaymentMethod($paymentMethod->id, 999);
    }
    
    public function testGetPaymentMethodNotFound()
    {
        $this->expectException(PaymentException::class);
        $this->service->getPaymentMethod(999);
    }
    
    public function testGetDefaultPaymentMethod()
    {
        // Create test payment methods
        $customerId = 1;
        PaymentMethod::factory()->count(2)->forCustomer($customerId)->create();
        $defaultMethod = PaymentMethod::factory()->forCustomer($customerId)->default()->create();
        
        // Test retrieval
        $retrievedMethod = $this->service->getDefaultPaymentMethod($customerId);
        $this->assertInstanceOf(PaymentMethod::class, $retrievedMethod);
        $this->assertEquals($defaultMethod->id, $retrievedMethod->id);
        $this->assertTrue($retrievedMethod->is_default);
    }
    
    public function testCreatePaymentMethod()
    {
        $customerId = 1;
        $data = [
            'customer_id' => $customerId,
            'gateway' => 'stripe',
            'token' => 'tok_visa',
            'type' => 'credit_card',
            'last_four' => '4242',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'card_holder_name' => 'John Doe',
            'card_brand' => 'visa',
        ];
        
        // Test creation
        $paymentMethod = $this->service->createPaymentMethod($data);
        $this->assertInstanceOf(PaymentMethod::class, $paymentMethod);
        $this->assertEquals($customerId, $paymentMethod->customer_id);
        $this->assertEquals('stripe', $paymentMethod->gateway);
        $this->assertEquals('tok_visa', $paymentMethod->token);
        $this->assertEquals('credit_card', $paymentMethod->type);
        $this->assertEquals('4242', $paymentMethod->last_four);
        $this->assertEquals('12', $paymentMethod->expiry_month);
        $this->assertEquals('2025', $paymentMethod->expiry_year);
        $this->assertEquals('John Doe', $paymentMethod->card_holder_name);
        $this->assertEquals('visa', $paymentMethod->card_brand);
        $this->assertTrue($paymentMethod->is_default); // First payment method should be default
        
        // Create another payment method
        $data2 = [
            'customer_id' => $customerId,
            'gateway' => 'paypal',
            'token' => 'pp_token',
            'type' => 'credit_card',
            'is_default' => false,
        ];
        
        $paymentMethod2 = $this->service->createPaymentMethod($data2);
        $this->assertFalse($paymentMethod2->is_default);
        
        // Create another payment method with is_default = true
        $data3 = [
            'customer_id' => $customerId,
            'gateway' => 'payu',
            'token' => 'payu_token',
            'type' => 'credit_card',
            'is_default' => true,
        ];
        
        $paymentMethod3 = $this->service->createPaymentMethod($data3);
        $this->assertTrue($paymentMethod3->is_default);
        
        // Refresh the first payment method
        $paymentMethod->refresh();
        $this->assertFalse($paymentMethod->is_default);
    }
    
    public function testUpdatePaymentMethod()
    {
        // Create test payment method
        $paymentMethod = PaymentMethod::factory()->create([
            'is_default' => false,
        ]);
        
        $data = [
            'token' => 'new_token',
            'last_four' => '1234',
            'expiry_month' => '10',
            'expiry_year' => '2026',
            'is_default' => true,
        ];
        
        // Test update
        $updatedMethod = $this->service->updatePaymentMethod($paymentMethod->id, $data);
        $this->assertInstanceOf(PaymentMethod::class, $updatedMethod);
        $this->assertEquals('new_token', $updatedMethod->token);
        $this->assertEquals('1234', $updatedMethod->last_four);
        $this->assertEquals('10', $updatedMethod->expiry_month);
        $this->assertEquals('2026', $updatedMethod->expiry_year);
        $this->assertTrue($updatedMethod->is_default);
    }
    
    public function testDeletePaymentMethod()
    {
        // Create test payment methods
        $customerId = 1;
        $defaultMethod = PaymentMethod::factory()->forCustomer($customerId)->default()->create();
        $otherMethod = PaymentMethod::factory()->forCustomer($customerId)->create();
        
        // Test deletion of non-default method
        $result = $this->service->deletePaymentMethod($otherMethod->id);
        $this->assertTrue($result);
        $this->assertDatabaseMissing('payment_methods', ['id' => $otherMethod->id]);
        
        // Test deletion of default method
        $result = $this->service->deletePaymentMethod($defaultMethod->id);
        $this->assertTrue($result);
        $this->assertDatabaseMissing('payment_methods', ['id' => $defaultMethod->id]);
    }
    
    public function testSetDefaultPaymentMethod()
    {
        // Create test payment methods
        $customerId = 1;
        $defaultMethod = PaymentMethod::factory()->forCustomer($customerId)->default()->create();
        $otherMethod = PaymentMethod::factory()->forCustomer($customerId)->create();
        
        // Test setting default
        $result = $this->service->setDefaultPaymentMethod($otherMethod->id);
        $this->assertInstanceOf(PaymentMethod::class, $result);
        $this->assertTrue($result->is_default);
        
        // Refresh the first payment method
        $defaultMethod->refresh();
        $this->assertFalse($defaultMethod->is_default);
    }
}

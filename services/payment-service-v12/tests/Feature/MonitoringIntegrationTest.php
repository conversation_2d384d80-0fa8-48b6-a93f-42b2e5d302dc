<?php

namespace Tests\Feature;

use App\Services\MetricsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class MonitoringIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected MetricsService $metricsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->metricsService = app(MetricsService::class);
        Cache::flush(); // Clear metrics cache
    }

    /**
     * Test metrics endpoint returns Prometheus format.
     */
    public function test_metrics_endpoint_returns_prometheus_format(): void
    {
        // Record some test metrics
        $this->metricsService->recordApiResponseTime('/api/test', 'GET', 0.150, 200);
        $this->metricsService->recordWalletOperation('deduct', 0.050, 'success');
        $this->metricsService->recordError('payment-service', 'test_operation', 'TestException');

        $response = $this->get('/api/monitoring/metrics');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
        
        $content = $response->getContent();
        $this->assertStringContainsString('api_request_duration_seconds', $content);
        $this->assertStringContainsString('wallet_operation_duration_seconds', $content);
        $this->assertStringContainsString('errors_total', $content);
    }

    /**
     * Test health endpoint returns service status.
     */
    public function test_health_endpoint_returns_service_status(): void
    {
        // Mock external services
        Http::fake([
            '*/health' => Http::response(['status' => 'healthy'], 200)
        ]);

        $response = $this->get('/api/monitoring/health');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'service',
            'status',
            'timestamp',
            'checks' => [
                'database',
                'external_services'
            ]
        ]);

        $data = $response->json();
        $this->assertEquals('payment-service-v12', $data['service']);
        $this->assertEquals('healthy', $data['status']);
        $this->assertEquals('healthy', $data['checks']['database']['status']);
    }

    /**
     * Test health endpoint reports unhealthy when database is down.
     */
    public function test_health_endpoint_reports_unhealthy_when_database_down(): void
    {
        // Skip this test for now as it's difficult to simulate database failure in testing
        // In real scenarios, this would be tested with actual database downtime
        $this->markTestSkipped('Database failure simulation requires more complex setup');
    }

    /**
     * Test readiness endpoint.
     */
    public function test_readiness_endpoint(): void
    {
        $response = $this->get('/api/monitoring/ready');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'service',
            'ready',
            'timestamp',
            'checks'
        ]);

        $data = $response->json();
        $this->assertEquals('payment-service-v12', $data['service']);
        $this->assertTrue($data['ready']);
    }

    /**
     * Test liveness endpoint.
     */
    public function test_liveness_endpoint(): void
    {
        $response = $this->get('/api/monitoring/live');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'service',
            'alive',
            'timestamp',
            'uptime'
        ]);

        $data = $response->json();
        $this->assertEquals('payment-service-v12', $data['service']);
        $this->assertTrue($data['alive']);
        $this->assertMatchesRegularExpression('/\d{2}:\d{2}:\d{2}/', $data['uptime']);
    }

    /**
     * Test metrics collection middleware.
     */
    public function test_metrics_collection_middleware(): void
    {
        // Mock external services to ensure health endpoint returns 200
        Http::fake([
            '*/health' => Http::response(['status' => 'healthy'], 200)
        ]);

        // Make a request to trigger middleware
        $response = $this->get('/api/monitoring/health');

        $response->assertStatus(200);
        $response->assertHeader('X-Service', 'payment-service-v12');
        $this->assertNotNull($response->headers->get('X-Response-Time'));

        // Verify response time format
        $responseTime = $response->headers->get('X-Response-Time');
        $this->assertMatchesRegularExpression('/\d+\.\d+ms/', $responseTime);
    }

    /**
     * Test MetricsService records histogram metrics correctly.
     */
    public function test_metrics_service_records_histogram_metrics(): void
    {
        // Record multiple values
        $this->metricsService->recordApiResponseTime('/api/test', 'GET', 0.100, 200);
        $this->metricsService->recordApiResponseTime('/api/test', 'GET', 0.200, 200);
        $this->metricsService->recordApiResponseTime('/api/test', 'GET', 0.050, 200);

        $metrics = $this->metricsService->getPrometheusMetrics();

        $this->assertStringContainsString('api_request_duration_seconds_count{', $metrics);
        $this->assertStringContainsString('api_request_duration_seconds_sum{', $metrics);
        $this->assertStringContainsString('} 3', $metrics);
        $this->assertStringContainsString('} 0.35', $metrics);
        $this->assertStringContainsString('api_request_duration_seconds_bucket', $metrics);
    }

    /**
     * Test MetricsService records counter metrics correctly.
     */
    public function test_metrics_service_records_counter_metrics(): void
    {
        // Record multiple events
        $this->metricsService->recordError('payment-service', 'test_op', 'error1');
        $this->metricsService->recordError('payment-service', 'test_op', 'error1');
        $this->metricsService->recordError('payment-service', 'test_op', 'error2');

        $metrics = $this->metricsService->getPrometheusMetrics();

        $this->assertStringContainsString('errors_total', $metrics);
        // Should have recorded 3 total errors
        $this->assertStringContainsString('2', $metrics); // error1 count
        $this->assertStringContainsString('1', $metrics); // error2 count
    }

    /**
     * Test MetricsService records gauge metrics correctly.
     */
    public function test_metrics_service_records_gauge_metrics(): void
    {
        $this->metricsService->recordServiceHealth('payment-service', true);
        $this->metricsService->recordDatabaseHealth('default', false);

        $metrics = $this->metricsService->getPrometheusMetrics();

        $this->assertStringContainsString('service_health{', $metrics);
        $this->assertStringContainsString('database_health{', $metrics);
        $this->assertStringContainsString('} 1', $metrics);
        $this->assertStringContainsString('} 0', $metrics);
    }

    /**
     * Test external service health monitoring.
     */
    public function test_external_service_health_monitoring(): void
    {
        // Mock external services with different responses
        Http::fake([
            'http://customer-service-v12:8000/health' => Http::response(['status' => 'healthy'], 200),
            'http://invoice-service-v12:8000/health' => Http::response(['status' => 'unhealthy'], 503),
            'http://order-service-v12:8000/health' => Http::response(['error' => 'timeout'], 500),
        ]);

        $response = $this->get('/api/monitoring/health');

        $response->assertStatus(503); // Should be unhealthy due to failing services
        $data = $response->json();

        $externalServices = $data['checks']['external_services'];
        $this->assertEquals('healthy', $externalServices['customer-service']['status']);
        $this->assertEquals('unhealthy', $externalServices['invoice-service']['status']);
        $this->assertEquals('unhealthy', $externalServices['order-service']['status']);
    }

    /**
     * Test metrics cache functionality.
     */
    public function test_metrics_cache_functionality(): void
    {
        // Record a metric
        $this->metricsService->recordApiResponseTime('/api/test', 'GET', 0.100, 200);

        // Check if metric is cached
        $cacheKey = 'metrics:histogram:api_request_duration_seconds{endpoint="/api/test",method="GET",status_code="200"}';
        $cachedData = Cache::get($cacheKey);

        $this->assertNotNull($cachedData);
        $this->assertEquals(1, $cachedData['count']);
        $this->assertEquals(0.1, $cachedData['sum']);
    }

    /**
     * Test error rate monitoring.
     */
    public function test_error_rate_monitoring(): void
    {
        // Simulate various error scenarios
        $this->metricsService->recordError('payment-service', 'wallet_payment', 'InsufficientBalanceException');
        $this->metricsService->recordError('payment-service', 'gateway_payment', 'GatewayTimeoutException');
        $this->metricsService->recordError('payment-service', 'wallet_payment', 'ServiceUnavailableException');

        $metrics = $this->metricsService->getPrometheusMetrics();

        $this->assertStringContainsString('errors_total', $metrics);
        $this->assertStringContainsString('payment-service', $metrics);
        $this->assertStringContainsString('wallet_payment', $metrics);
        $this->assertStringContainsString('gateway_payment', $metrics);
    }

    /**
     * Test invoice generation metrics.
     */
    public function test_invoice_generation_metrics(): void
    {
        $this->metricsService->recordInvoiceGeneration('subscription', 0.250, 'success');
        $this->metricsService->recordInvoiceGeneration('order', 0.180, 'success');
        $this->metricsService->recordInvoiceGeneration('subscription', 0.500, 'failed');

        $metrics = $this->metricsService->getPrometheusMetrics();

        $this->assertStringContainsString('invoice_generation_duration_seconds', $metrics);
        $this->assertStringContainsString('invoices_generated_total', $metrics);
        $this->assertStringContainsString('subscription', $metrics);
        $this->assertStringContainsString('order', $metrics);
    }

    /**
     * Test PDF generation metrics.
     */
    public function test_pdf_generation_metrics(): void
    {
        $this->metricsService->recordPdfGeneration(1.250, 'success');
        $this->metricsService->recordPdfGeneration(0.800, 'success');
        $this->metricsService->recordPdfGeneration(2.100, 'failed');

        $metrics = $this->metricsService->getPrometheusMetrics();

        $this->assertStringContainsString('pdf_generation_duration_seconds', $metrics);
        $this->assertStringContainsString('pdfs_generated_total', $metrics);
        $this->assertStringContainsString('success', $metrics);
        $this->assertStringContainsString('failed', $metrics);
    }
}

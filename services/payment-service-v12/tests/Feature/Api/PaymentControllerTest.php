<?php

namespace Tests\Feature\Api;

use App\Models\PaymentLog;
use App\Models\PaymentTransaction;
use App\Models\User;
use App\Services\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Mockery;

class PaymentControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable authentication middleware for testing
        $this->withoutMiddleware(['auth:sanctum']);

        // Create and authenticate a user
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Mock the PaymentService to avoid actual gateway calls
        $this->mock(PaymentService::class, function ($mock) {
            $mock->shouldReceive('initiatePayment')
                ->andReturn(PaymentTransaction::factory()->create());

            $mock->shouldReceive('processPayment')
                ->andReturn([
                    'action' => 'https://example.com/pay',
                    'method' => 'POST',
                    'fields' => ['key' => 'value']
                ]);

            $mock->shouldReceive('handleCallback')
                ->andReturn(PaymentTransaction::factory()->create(['status' => 'completed']));

            $mock->shouldReceive('getTransaction')
                ->andReturn(PaymentTransaction::factory()->create());

            $mock->shouldReceive('refundPayment')
                ->andReturn([
                    'success' => true,
                    'status' => 'refunded',
                    'gateway_transaction_id' => 'txn_123'
                ]);

            $mock->shouldReceive('getTransactionStatistics')
                ->andReturn([
                    'total_transactions' => 10,
                    'total_amount' => 1000,
                    'transactions_by_status' => [],
                    'transactions_by_gateway' => [],
                    'recent_transactions' => []
                ]);
        });
    }

    public function testInitiatePayment()
    {
        $data = [
            'customer_id' => 1,
            'amount' => 100.00,
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ];

        $response = $this->postJson('/api/v2/payments', $data);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'transaction_id',
                    'amount',
                    'status'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment initiated successfully'
            ]);
    }

    public function testProcessPayment()
    {
        $transaction = PaymentTransaction::factory()->create();

        $data = [
            'gateway' => 'stripe'
        ];

        $response = $this->postJson("/api/v2/payments/{$transaction->transaction_id}/process", $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'action',
                    'method',
                    'fields'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment processing initiated'
            ]);
    }

    public function testCallback()
    {
        $data = [
            'transaction_id' => '12345',
            'status' => 'success',
            'gateway_transaction_id' => 'txn_123'
        ];

        $response = $this->postJson('/api/v2/payments/callback', $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'transaction_id',
                    'status',
                    'gateway_transaction_id'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment callback processed'
            ]);
    }

    public function testGetStatus()
    {
        $transaction = PaymentTransaction::factory()->create();

        $response = $this->getJson("/api/v2/payments/{$transaction->transaction_id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'transaction_id',
                    'amount',
                    'status',
                    'gateway',
                    'gateway_transaction_id',
                    'created_at',
                    'updated_at'
                ]
            ])
            ->assertJson([
                'success' => true
            ]);
    }

    public function testRefundPayment()
    {
        $transaction = PaymentTransaction::factory()->create(['status' => 'completed']);

        $data = [
            'amount' => 50.00
        ];

        $response = $this->postJson("/api/v2/payments/{$transaction->transaction_id}/refund", $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'success',
                    'status',
                    'gateway_transaction_id'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment refunded successfully'
            ]);
    }

    public function testGetStatistics()
    {
        // Override the mock specifically for this test
        $this->app->instance(PaymentService::class, \Mockery::mock(PaymentService::class, function ($mock) {
            $mock->shouldReceive('getTransactionStatistics')
                ->once()
                ->andReturn([
                    'total_transactions' => 10,
                    'total_amount' => 1000,
                    'transactions_by_status' => [],
                    'transactions_by_gateway' => [],
                    'recent_transactions' => []
                ]);
        }));

        $response = $this->getJson('/api/v2/payments/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total_transactions',
                    'total_amount',
                    'transactions_by_status',
                    'transactions_by_gateway',
                    'recent_transactions'
                ]
            ])
            ->assertJson([
                'success' => true
            ]);
    }

    public function testGetLogs()
    {
        // Create some test logs
        PaymentLog::factory()->count(5)->create();

        $response = $this->getJson('/api/v2/payments/logs');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data'
            ])
            ->assertJson([
                'success' => true
            ]);
    }

    public function testGetTransactionLogs()
    {
        $transaction = PaymentTransaction::factory()->create();

        // Create some test logs for this transaction
        PaymentLog::factory()->count(3)->forTransaction($transaction->transaction_id)->create();

        $response = $this->getJson("/api/v2/payments/{$transaction->transaction_id}/logs");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data'
            ])
            ->assertJson([
                'success' => true
            ]);
    }

    public function testWebhook()
    {
        $data = [
            'transaction_id' => '12345',
            'status' => 'success',
            'gateway_transaction_id' => 'txn_123'
        ];

        $response = $this->postJson('/api/v2/payments/webhooks/stripe', $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message'
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Webhook processed successfully'
            ]);
    }
}

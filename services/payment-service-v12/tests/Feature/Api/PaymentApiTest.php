<?php

namespace Tests\Feature\Api;

use App\Models\PaymentTransaction;
use App\Models\User;
use App\Services\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class PaymentApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable authentication middleware for testing
        $this->withoutMiddleware(['auth:sanctum']);

        // Create and authenticate a user
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');
    }

    /** @test */
    public function it_can_initiate_payment()
    {
        // Arrange
        $data = [
            'customer_id' => 1,
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'customer_name' => 'Test Customer',
            'amount' => 100.00,
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ];

        // Act
        $response = $this->postJson('/api/v2/payments', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Payment initiated successfully'
            ]);

        $this->assertDatabaseHas('payment_transactions', [
            'customer_id' => 1,
            'amount' => 100.00,
            'status' => 'initiated'
        ]);
    }

    /** @test */
    public function it_validates_payment_initiation_request()
    {
        // Arrange
        $data = [
            'customer_id' => 1,
            // Missing required fields
        ];

        // Act
        $response = $this->postJson('/api/v2/payments', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['amount', 'success_url', 'failure_url']);
    }

    /** @test */
    public function it_can_process_payment()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'initiated'
        ]);

        $data = [
            'gateway' => 'stripe'
        ];

        // Mock the payment service
        $mockService = $this->mock(PaymentService::class);
        $mockService->shouldReceive('processPayment')
            ->once()
            ->with($transaction->transaction_id, 'stripe')
            ->andReturn([
                'action' => 'https://stripe.com/pay',
                'method' => 'POST',
                'fields' => []
            ]);

        // Act
        $response = $this->postJson("/api/v2/payments/{$transaction->transaction_id}/process", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment processing initiated'
            ]);
    }

    /** @test */
    public function it_can_get_payment_status()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'completed',
            'gateway' => 'stripe',
            'gateway_transaction_id' => 'test-123'
        ]);

        // Act
        $response = $this->getJson("/api/v2/payments/{$transaction->transaction_id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'status' => 'completed',
                    'gateway' => 'stripe',
                    'gateway_transaction_id' => 'test-123'
                ]
            ]);
    }

    /** @test */
    public function it_returns_404_for_non_existent_payment()
    {
        // Act
        $response = $this->getJson('/api/v2/payments/9999');

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'success' => false
            ]);
    }

    /** @test */
    public function it_can_handle_payment_callback()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'processing',
            'gateway' => 'stripe'
        ]);

        $callbackData = [
            'transaction_id' => $transaction->transaction_id,
            'payment_intent' => 'pi_123456',
            'status' => 'succeeded'
        ];

        // Mock the payment service
        $mockService = $this->mock(PaymentService::class);
        $mockService->shouldReceive('handleCallback')
            ->once()
            ->with($callbackData, \Mockery::type('Illuminate\Http\Request'))
            ->andReturn($transaction->fill([
                'status' => 'completed',
                'gateway_transaction_id' => 'pi_123456'
            ]));

        // Act
        $response = $this->postJson('/api/v2/payments/callback', $callbackData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment callback processed'
            ]);
    }

    /** @test */
    public function it_can_refund_payment()
    {
        // Arrange
        $transaction = PaymentTransaction::factory()->create([
            'status' => 'completed',
            'gateway' => 'stripe',
            'gateway_transaction_id' => 'pi_123456'
        ]);

        $data = [
            'amount' => 50.00
        ];

        // Mock the payment service
        $mockService = $this->mock(PaymentService::class);
        $mockService->shouldReceive('refundPayment')
            ->once()
            ->with($transaction->transaction_id, 50.00)
            ->andReturn([
                'success' => true,
                'status' => 'refunded',
                'gateway_transaction_id' => 're_123456'
            ]);

        // Act
        $response = $this->postJson("/api/v2/payments/{$transaction->transaction_id}/refund", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment refunded successfully'
            ]);
    }
}

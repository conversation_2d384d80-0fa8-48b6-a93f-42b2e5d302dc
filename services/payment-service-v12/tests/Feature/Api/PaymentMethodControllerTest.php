<?php

namespace Tests\Feature\Api;

use App\Exceptions\PaymentException;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Services\PaymentMethodService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Mockery;

class PaymentMethodControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable authentication middleware for testing
        $this->withoutMiddleware(['auth:sanctum']);

        // Create and authenticate a user
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Mock the PaymentMethodService to avoid actual database operations
        $this->mock(PaymentMethodService::class, function ($mock) {
            $mock->shouldReceive('getCustomerPaymentMethods')
                ->andReturn(PaymentMethod::factory()->count(3)->make());

            $mock->shouldReceive('createPaymentMethod')
                ->andReturn(PaymentMethod::factory()->make());

            $mock->shouldReceive('getPaymentMethod')
                ->with(\Mockery::type('int'), \Mockery::any())
                ->andReturnUsing(function ($id, $customerId) {
                    if ($id === 999) {
                        throw new PaymentException('Payment method not found');
                    }
                    return PaymentMethod::factory()->make();
                });

            $mock->shouldReceive('updatePaymentMethod')
                ->andReturn(PaymentMethod::factory()->make());

            $mock->shouldReceive('deletePaymentMethod')
                ->andReturn(true);

            $mock->shouldReceive('setDefaultPaymentMethod')
                ->andReturn(PaymentMethod::factory()->make(['is_default' => true]));
        });
    }

    public function testGetCustomerPaymentMethods()
    {
        $customerId = 1;

        $response = $this->getJson("/api/v2/payment-methods/customer/{$customerId}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data'
            ])
            ->assertJson([
                'success' => true
            ]);
    }

    public function testStorePaymentMethod()
    {
        $data = [
            'customer_id' => 1,
            'gateway' => 'stripe',
            'token' => 'tok_visa',
            'type' => 'credit_card',
            'last_four' => '4242',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'card_holder_name' => 'John Doe',
            'card_brand' => 'visa',
        ];

        $response = $this->postJson('/api/v2/payment-methods', $data);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data'
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment method created successfully'
            ]);
    }

    public function testShowPaymentMethod()
    {
        $paymentMethod = PaymentMethod::factory()->create();

        $response = $this->getJson("/api/v2/payment-methods/{$paymentMethod->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data'
            ])
            ->assertJson([
                'success' => true
            ]);
    }

    public function testUpdatePaymentMethod()
    {
        $paymentMethod = PaymentMethod::factory()->create();

        $data = [
            'token' => 'new_token',
            'last_four' => '1234',
            'expiry_month' => '10',
            'expiry_year' => '2026',
            'is_default' => true,
        ];

        $response = $this->putJson("/api/v2/payment-methods/{$paymentMethod->id}", $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data'
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment method updated successfully'
            ]);
    }

    public function testDestroyPaymentMethod()
    {
        $paymentMethod = PaymentMethod::factory()->create();

        $response = $this->deleteJson("/api/v2/payment-methods/{$paymentMethod->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message'
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment method deleted successfully'
            ]);
    }

    public function testSetDefaultPaymentMethod()
    {
        $paymentMethod = PaymentMethod::factory()->create();

        $response = $this->putJson("/api/v2/payment-methods/{$paymentMethod->id}/default");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data'
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Payment method set as default successfully'
            ]);
    }

    public function testValidationErrorOnStore()
    {
        $data = [
            // Missing required fields
            'gateway' => 'stripe',
        ];

        $response = $this->postJson('/api/v2/payment-methods', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['customer_id', 'token', 'type']);
    }

    public function testNotFoundError()
    {
        $response = $this->getJson('/api/v2/payment-methods/999');

        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message'
            ])
            ->assertJson([
                'success' => false
            ]);
    }
}

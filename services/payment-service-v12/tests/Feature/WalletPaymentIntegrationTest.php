<?php

namespace Tests\Feature;

use App\Events\Payment\WalletPaymentProcessed;
use App\Events\Payment\WalletPaymentFailed;
use App\Exceptions\Payment\PaymentException;
use App\Models\PaymentTransaction;
use App\Services\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class WalletPaymentIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected PaymentService $paymentService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->paymentService = app(PaymentService::class);
        Event::fake();
    }

    /**
     * Test successful wallet payment processing.
     */
    public function test_successful_wallet_payment_processing(): void
    {
        // Mock successful wallet balance check
        Http::fake([
            'customer-service-v12:8000/api/v2/wallet/*/balance' => Http::response([
                'success' => true,
                'data' => ['balance' => 1000.00]
            ], 200),
            'customer-service-v12:8000/api/v2/wallet/deduct' => Http::response([
                'success' => true,
                'data' => [
                    'transaction_id' => 'WALLET_TXN_123',
                    'balance_after' => 750.00,
                    'amount_deducted' => 250.00
                ]
            ], 200)
        ]);

        // Create a payment transaction with wallet amount
        $transaction = PaymentTransaction::create([
            'transaction_id' => 'TXN_TEST_001',
            'customer_id' => 123,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'amount' => 500.00,
            'wallet_amount' => 250.00,
            'order_id' => 'ORDER_001',
            'status' => 'completed',
            'gateway' => 'test_gateway',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ]);

        // Process wallet payment using reflection to access protected method
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('processWalletPayment');
        $method->setAccessible(true);
        $method->invoke($this->paymentService, $transaction);

        // Refresh transaction from database
        $transaction->refresh();

        // Assert transaction was updated with wallet details
        $this->assertEquals('WALLET_TXN_123', $transaction->wallet_transaction_id);
        $this->assertEquals(750.00, $transaction->wallet_balance_after);

        // Assert wallet payment processed event was fired
        Event::assertDispatched(WalletPaymentProcessed::class, function ($event) use ($transaction) {
            return $event->transaction->transaction_id === $transaction->transaction_id;
        });

        // Assert HTTP requests were made correctly
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service-v12:8000/api/v2/wallet/123/balance';
        });

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service-v12:8000/api/v2/wallet/deduct' &&
                   $request['customer_id'] === 123 &&
                   $request['amount'] === 250.00;
        });
    }

    /**
     * Test wallet payment failure due to insufficient balance.
     */
    public function test_wallet_payment_failure_insufficient_balance(): void
    {
        // Mock insufficient wallet balance
        Http::fake([
            'customer-service-v12:8000/api/v2/wallet/*/balance' => Http::response([
                'success' => true,
                'data' => ['balance' => 100.00]
            ], 200)
        ]);

        $transaction = PaymentTransaction::create([
            'transaction_id' => 'TXN_TEST_002',
            'customer_id' => 123,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'amount' => 500.00,
            'wallet_amount' => 250.00,
            'order_id' => 'ORDER_002',
            'status' => 'completed',
            'gateway' => 'test_gateway',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ]);

        // Expect PaymentException to be thrown
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Insufficient wallet balance');

        // Process wallet payment
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('processWalletPayment');
        $method->setAccessible(true);
        $method->invoke($this->paymentService, $transaction);

        // Assert wallet payment failed event was fired
        Event::assertDispatched(WalletPaymentFailed::class, function ($event) use ($transaction) {
            return $event->transaction->transaction_id === $transaction->transaction_id &&
                   $event->errorMessage === 'Wallet payment failed: Insufficient wallet balance';
        });
    }

    /**
     * Test wallet payment failure due to service unavailable.
     */
    public function test_wallet_payment_failure_service_unavailable(): void
    {
        // Mock service failure
        Http::fake([
            'customer-service-v12:8000/api/v2/wallet/*/balance' => Http::response([
                'success' => true,
                'data' => ['balance' => 1000.00]
            ], 200),
            'customer-service-v12:8000/api/v2/wallet/deduct' => Http::response([
                'success' => false,
                'message' => 'Service temporarily unavailable'
            ], 503)
        ]);

        $transaction = PaymentTransaction::create([
            'transaction_id' => 'TXN_TEST_003',
            'customer_id' => 123,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'amount' => 500.00,
            'wallet_amount' => 250.00,
            'order_id' => 'ORDER_003',
            'status' => 'completed',
            'gateway' => 'test_gateway',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ]);

        // Expect PaymentException to be thrown
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessageMatches('/Wallet payment failed:/');

        // Process wallet payment
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('processWalletPayment');
        $method->setAccessible(true);
        $method->invoke($this->paymentService, $transaction);
    }

    /**
     * Test wallet balance retrieval.
     */
    public function test_get_wallet_balance(): void
    {
        // Mock successful balance retrieval
        Http::fake([
            'customer-service-v12:8000/api/v2/wallet/*/balance' => Http::response([
                'success' => true,
                'data' => ['balance' => 1500.75]
            ], 200)
        ]);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('getWalletBalance');
        $method->setAccessible(true);
        $balance = $method->invoke($this->paymentService, 123);

        $this->assertEquals(1500.75, $balance);

        // Assert HTTP request was made correctly
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service-v12:8000/api/v2/wallet/123/balance' &&
                   $request->header('X-Service')[0] === 'payment-service-v12';
        });
    }

    /**
     * Test wallet balance retrieval failure.
     */
    public function test_get_wallet_balance_failure(): void
    {
        // Mock service failure
        Http::fake([
            'customer-service-v12:8000/api/v2/wallet/*/balance' => Http::response([
                'success' => false,
                'message' => 'Customer not found'
            ], 404)
        ]);

        // Expect PaymentException to be thrown
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessageMatches('/Failed to get wallet balance/');

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('getWalletBalance');
        $method->setAccessible(true);
        $method->invoke($this->paymentService, 999);
    }

    /**
     * Test correlation ID and request tracing.
     */
    public function test_correlation_id_and_request_tracing(): void
    {
        // Mock successful responses
        Http::fake([
            'customer-service-v12:8000/api/v2/wallet/*/balance' => Http::response([
                'success' => true,
                'data' => ['balance' => 1000.00]
            ], 200),
            'customer-service-v12:8000/api/v2/wallet/deduct' => Http::response([
                'success' => true,
                'data' => [
                    'transaction_id' => 'WALLET_TXN_456',
                    'balance_after' => 750.00,
                    'amount_deducted' => 250.00
                ]
            ], 200)
        ]);

        $transaction = PaymentTransaction::create([
            'transaction_id' => 'TXN_TEST_004',
            'customer_id' => 123,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'amount' => 500.00,
            'wallet_amount' => 250.00,
            'order_id' => 'ORDER_004',
            'status' => 'completed',
            'gateway' => 'test_gateway',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ]);

        // Process wallet payment
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('processWalletPayment');
        $method->setAccessible(true);
        $method->invoke($this->paymentService, $transaction);

        // Assert correlation ID was sent in wallet deduction request
        Http::assertSent(function ($request) use ($transaction) {
            return $request->url() === 'http://customer-service-v12:8000/api/v2/wallet/deduct' &&
                   $request->header('X-Correlation-ID')[0] === $transaction->transaction_id &&
                   $request->header('X-Service')[0] === 'payment-service-v12' &&
                   isset($request->header('X-Request-ID')[0]);
        });
    }

    /**
     * Test retry mechanism for wallet operations.
     */
    public function test_retry_mechanism_for_wallet_operations(): void
    {
        // Mock balance check success, then wallet deduction failures followed by success
        Http::fake([
            'customer-service-v12:8000/api/v2/wallet/*/balance' => Http::response([
                'success' => true,
                'data' => ['balance' => 1000.00]
            ], 200),
            'customer-service-v12:8000/api/v2/wallet/deduct' => Http::sequence()
                ->push(['success' => false], 500)  // First attempt fails
                ->push(['success' => false], 500)  // Second attempt fails
                ->push([  // Third attempt succeeds
                    'success' => true,
                    'data' => [
                        'transaction_id' => 'WALLET_TXN_789',
                        'balance_after' => 750.00,
                        'amount_deducted' => 250.00
                    ]
                ], 200)
        ]);

        $transaction = PaymentTransaction::create([
            'transaction_id' => 'TXN_TEST_005',
            'customer_id' => 123,
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'amount' => 500.00,
            'wallet_amount' => 250.00,
            'order_id' => 'ORDER_005',
            'status' => 'completed',
            'gateway' => 'test_gateway',
            'success_url' => 'https://example.com/success',
            'failure_url' => 'https://example.com/failure',
        ]);

        // Process wallet payment
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('processWalletPayment');
        $method->setAccessible(true);
        $method->invoke($this->paymentService, $transaction);

        // Refresh transaction from database
        $transaction->refresh();

        // Assert transaction was eventually successful
        $this->assertEquals('WALLET_TXN_789', $transaction->wallet_transaction_id);
        $this->assertEquals(750.00, $transaction->wallet_balance_after);

        // Assert 3 attempts were made to the wallet deduct endpoint
        Http::assertSentCount(4); // 1 balance check + 3 deduct attempts
    }
}

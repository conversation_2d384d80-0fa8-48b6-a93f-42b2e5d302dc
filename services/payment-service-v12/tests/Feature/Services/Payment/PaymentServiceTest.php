<?php

namespace Tests\Feature\Services\Payment;

use App\Events\Payment\PaymentCancelled;
use App\Events\Payment\PaymentCreated;
use App\Events\Payment\PaymentFailed;
use App\Events\Payment\PaymentRefunded;
use App\Events\Payment\PaymentSucceeded;
use App\Models\Payment;
use App\Repositories\Payment\PaymentRepositoryInterface;
use App\Services\Payment\Gateway\PaymentGatewayInterface;
use App\Services\Payment\PaymentService;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class PaymentServiceTest extends TestCase
{
    /**
     * @var PaymentRepositoryInterface|Mockery\MockInterface
     */
    protected $paymentRepository;

    /**
     * @var PaymentGatewayInterface|Mockery\MockInterface
     */
    protected $paymentGateway;

    /**
     * @var PaymentService
     */
    protected $paymentService;

    /**
     * Set up the test.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Mock the payment repository
        $this->paymentRepository = Mockery::mock(PaymentRepositoryInterface::class);

        // Mock the payment gateway
        $this->paymentGateway = Mockery::mock(PaymentGatewayInterface::class);
        $this->paymentGateway->shouldReceive('getName')->andReturn('test_gateway');
        $this->paymentGateway->shouldReceive('isEnabled')->andReturn(true);

        // Create the payment service
        $this->paymentService = new PaymentService($this->paymentRepository, []);

        // Register the payment gateway
        $this->paymentService->registerGateway('test_gateway', $this->paymentGateway);

        // Fake events
        Event::fake([
            PaymentCreated::class,
            PaymentSucceeded::class,
            PaymentFailed::class,
            PaymentRefunded::class,
            PaymentCancelled::class,
        ]);
    }

    /**
     * Test that the payment service processes a payment successfully.
     *
     * @return void
     */
    public function testProcessPayment(): void
    {
        // Prepare payment data
        $paymentData = [
            'order_id' => 123,
            'amount' => 100.00,
            'currency' => 'INR',
            'customer_id' => 456,
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'gateway' => 'test_gateway',
        ];

        // Mock the payment repository
        $payment = new Payment([
            'order_id' => 123,
            'customer_id' => 456,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'gateway' => 'test_gateway',
        ]);
        $payment->id = 1; // Set the ID explicitly

        $this->paymentRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::on(function ($data) {
                return $data['order_id'] == 123
                    && $data['customer_id'] == 456
                    && $data['amount'] == 100.00
                    && $data['currency'] == 'INR'
                    && $data['status'] == 'pending'
                    && $data['gateway'] == 'test_gateway';
            }))
            ->andReturn($payment);

        $this->paymentRepository->shouldReceive('update')
            ->once()
            ->with(1, Mockery::on(function ($data) {
                return $data['transaction_id'] == 'TEST123456'
                    && isset($data['gateway_response']);
            }))
            ->andReturn(true);

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('processPayment')
            ->once()
            ->with(Mockery::on(function ($data) {
                return $data['order_id'] == 123
                    && $data['amount'] == 100.00
                    && $data['currency'] == 'INR'
                    && $data['customer_id'] == 456
                    && $data['customer_email'] == '<EMAIL>'
                    && $data['customer_phone'] == '1234567890'
                    && $data['transaction_id'] == 1;
            }))
            ->andReturn([
                'success' => true,
                'transaction_id' => 'TEST123456',
                'gateway' => 'test_gateway',
                'redirect' => true,
                'redirect_url' => 'https://example.com/pay',
            ]);

        // Process the payment
        $result = $this->paymentService->processPayment($paymentData);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('TEST123456', $result['transaction_id']);
        $this->assertEquals('test_gateway', $result['gateway']);
        $this->assertTrue($result['redirect']);
        $this->assertEquals('https://example.com/pay', $result['redirect_url']);
        $this->assertEquals(1, $result['payment_id']);

        // Assert that the event was dispatched
        Event::assertDispatched(PaymentCreated::class, function ($event) use ($payment) {
            return $event->payment->id === $payment->id;
        });
    }

    /**
     * Test that the payment service verifies a payment successfully.
     *
     * @return void
     */
    public function testVerifyPayment(): void
    {
        // Prepare payment data
        $transactionId = 'TEST123456';
        $additionalData = ['param1' => 'value1'];

        // Mock the payment repository
        $payment = new Payment([
            'order_id' => 123,
            'customer_id' => 456,
            'transaction_id' => $transactionId,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'gateway' => 'test_gateway',
        ]);
        $payment->id = 1; // Set the ID explicitly

        $this->paymentRepository->shouldReceive('findByTransactionId')
            ->once()
            ->with($transactionId)
            ->andReturn($payment);

        $this->paymentRepository->shouldReceive('update')
            ->once()
            ->with(1, Mockery::on(function ($data) {
                return $data['status'] == 'success'
                    && isset($data['gateway_response']);
            }))
            ->andReturn(true);

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('verifyPayment')
            ->once()
            ->with($transactionId, $additionalData)
            ->andReturn([
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => 'success',
                'gateway' => 'test_gateway',
                'gateway_response' => ['status' => 'success'],
            ]);

        // Verify the payment
        $result = $this->paymentService->verifyPayment($transactionId, $additionalData);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($transactionId, $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('test_gateway', $result['gateway']);
        $this->assertEquals(1, $result['payment_id']);

        // Assert that the event was dispatched
        Event::assertDispatched(PaymentSucceeded::class, function ($event) use ($payment) {
            return $event->payment->id === $payment->id;
        });
    }

    /**
     * Test that the payment service refunds a payment successfully.
     *
     * @return void
     */
    public function testRefundPayment(): void
    {
        // Prepare payment data
        $transactionId = 'TEST123456';
        $amount = 50.00;
        $additionalData = ['param1' => 'value1'];

        // Mock the payment repository
        $payment = new Payment([
            'order_id' => 123,
            'customer_id' => 456,
            'transaction_id' => $transactionId,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => 'success',
            'gateway' => 'test_gateway',
        ]);
        $payment->id = 1; // Set the ID explicitly

        $this->paymentRepository->shouldReceive('findByTransactionId')
            ->once()
            ->with($transactionId)
            ->andReturn($payment);

        $this->paymentRepository->shouldReceive('update')
            ->once()
            ->with(1, Mockery::on(function ($data) use ($amount) {
                return $data['status'] == 'refunded'
                    && $data['refund_amount'] == $amount
                    && isset($data['refund_date'])
                    && isset($data['gateway_response']);
            }))
            ->andReturn(true);

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('refundPayment')
            ->once()
            ->with($transactionId, $amount, $additionalData)
            ->andReturn([
                'success' => true,
                'transaction_id' => $transactionId,
                'refund_id' => 'REF123456',
                'status' => 'refunded',
                'gateway' => 'test_gateway',
                'gateway_response' => ['status' => 'refunded'],
            ]);

        // Refund the payment
        $result = $this->paymentService->refundPayment($transactionId, $amount, $additionalData);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($transactionId, $result['transaction_id']);
        $this->assertEquals('REF123456', $result['refund_id']);
        $this->assertEquals('refunded', $result['status']);
        $this->assertEquals('test_gateway', $result['gateway']);
        $this->assertEquals(1, $result['payment_id']);

        // Assert that the event was dispatched
        Event::assertDispatched(PaymentRefunded::class, function ($event) use ($payment) {
            return $event->payment->id === $payment->id;
        });
    }

    /**
     * Test that the payment service cancels a payment successfully.
     *
     * @return void
     */
    public function testCancelPayment(): void
    {
        // Prepare payment data
        $transactionId = 'TEST123456';
        $additionalData = ['param1' => 'value1'];

        // Mock the payment repository
        $payment = new Payment([
            'order_id' => 123,
            'customer_id' => 456,
            'transaction_id' => $transactionId,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'gateway' => 'test_gateway',
        ]);
        $payment->id = 1; // Set the ID explicitly

        $this->paymentRepository->shouldReceive('findByTransactionId')
            ->once()
            ->with($transactionId)
            ->andReturn($payment);

        $this->paymentRepository->shouldReceive('update')
            ->once()
            ->with(1, Mockery::on(function ($data) {
                return $data['status'] == 'cancelled'
                    && isset($data['gateway_response']);
            }))
            ->andReturn(true);

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('cancelPayment')
            ->once()
            ->with($transactionId, $additionalData)
            ->andReturn([
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => 'cancelled',
                'gateway' => 'test_gateway',
                'gateway_response' => ['status' => 'cancelled'],
            ]);

        // Cancel the payment
        $result = $this->paymentService->cancelPayment($transactionId, $additionalData);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($transactionId, $result['transaction_id']);
        $this->assertEquals('cancelled', $result['status']);
        $this->assertEquals('test_gateway', $result['gateway']);
        $this->assertEquals(1, $result['payment_id']);

        // Assert that the event was dispatched
        Event::assertDispatched(PaymentCancelled::class, function ($event) use ($payment) {
            return $event->payment->id === $payment->id;
        });
    }

    /**
     * Test that the payment service gets payment status successfully.
     *
     * @return void
     */
    public function testGetPaymentStatus(): void
    {
        // Prepare payment data
        $transactionId = 'TEST123456';

        // Mock the payment repository
        $payment = new Payment([
            'order_id' => 123,
            'customer_id' => 456,
            'transaction_id' => $transactionId,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'gateway' => 'test_gateway',
        ]);
        $payment->id = 1; // Set the ID explicitly

        $this->paymentRepository->shouldReceive('findByTransactionId')
            ->once()
            ->with($transactionId)
            ->andReturn($payment);

        $this->paymentRepository->shouldReceive('update')
            ->once()
            ->with(1, Mockery::on(function ($data) {
                return $data['status'] == 'success'
                    && isset($data['gateway_response']);
            }))
            ->andReturn(true);

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('getPaymentStatus')
            ->once()
            ->with($transactionId)
            ->andReturn([
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => 'success',
                'gateway' => 'test_gateway',
                'gateway_response' => ['status' => 'success'],
            ]);

        // Get payment status
        $result = $this->paymentService->getPaymentStatus($transactionId);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($transactionId, $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('test_gateway', $result['gateway']);
        $this->assertEquals(1, $result['payment_id']);
    }

    /**
     * Test that the payment service gets payment details successfully.
     *
     * @return void
     */
    public function testGetPaymentDetails(): void
    {
        // Prepare payment data
        $transactionId = 'TEST123456';

        // Mock the payment repository
        $payment = new Payment([
            'order_id' => 123,
            'customer_id' => 456,
            'transaction_id' => $transactionId,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => 'success',
            'gateway' => 'test_gateway',
        ]);
        $payment->id = 1; // Set the ID explicitly
        $payment->created_at = now();
        $payment->updated_at = now();

        $this->paymentRepository->shouldReceive('findByTransactionId')
            ->once()
            ->with($transactionId)
            ->andReturn($payment);

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('getPaymentDetails')
            ->once()
            ->with($transactionId)
            ->andReturn([
                'success' => true,
                'transaction_id' => $transactionId,
                'status' => 'success',
                'amount' => 100.00,
                'currency' => 'INR',
                'payment_method' => 'credit_card',
                'gateway' => 'test_gateway',
                'gateway_response' => ['status' => 'success'],
            ]);

        // Get payment details
        $result = $this->paymentService->getPaymentDetails($transactionId);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($transactionId, $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals(100.00, $result['amount']);
        $this->assertEquals('INR', $result['currency']);
        $this->assertEquals('credit_card', $result['payment_method']);
        $this->assertEquals('test_gateway', $result['gateway']);
        $this->assertEquals(1, $result['payment_id']);
        $this->assertEquals(123, $result['order_id']);
        $this->assertEquals(456, $result['customer_id']);
    }

    /**
     * Test that the payment service generates a payment form successfully.
     *
     * @return void
     */
    public function testGeneratePaymentForm(): void
    {
        // Prepare payment data
        $paymentData = [
            'order_id' => 123,
            'amount' => 100.00,
            'currency' => 'INR',
            'customer_id' => 456,
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'gateway' => 'test_gateway',
        ];

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('generatePaymentForm')
            ->once()
            ->with($paymentData)
            ->andReturn([
                'action' => 'https://example.com/pay',
                'method' => 'POST',
                'fields' => [
                    'key1' => 'value1',
                    'key2' => 'value2',
                ],
            ]);

        // Generate the payment form
        $result = $this->paymentService->generatePaymentForm($paymentData);

        // Assert the result
        $this->assertEquals('https://example.com/pay', $result['action']);
        $this->assertEquals('POST', $result['method']);
        $this->assertArrayHasKey('fields', $result);
        $this->assertEquals('value1', $result['fields']['key1']);
        $this->assertEquals('value2', $result['fields']['key2']);
    }

    /**
     * Test that the payment service handles a webhook successfully.
     *
     * @return void
     */
    public function testHandleWebhook(): void
    {
        // Prepare webhook data
        $gateway = 'test_gateway';
        $webhookData = [
            'transaction_id' => 'TEST123456',
            'status' => 'success',
            'amount' => 100.00,
            'currency' => 'INR',
        ];

        // Mock the payment repository
        $payment = new Payment([
            'order_id' => 123,
            'customer_id' => 456,
            'transaction_id' => 'TEST123456',
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'gateway' => 'test_gateway',
        ]);
        $payment->id = 1; // Set the ID explicitly

        $this->paymentRepository->shouldReceive('findByTransactionId')
            ->once()
            ->with('TEST123456')
            ->andReturn($payment);

        $this->paymentRepository->shouldReceive('update')
            ->once()
            ->with(1, Mockery::on(function ($data) {
                return $data['status'] == 'success'
                    && isset($data['gateway_response']);
            }))
            ->andReturn(true);

        // Mock the payment gateway
        $this->paymentGateway->shouldReceive('handleWebhook')
            ->once()
            ->with($webhookData)
            ->andReturn([
                'success' => true,
                'transaction_id' => 'TEST123456',
                'status' => 'success',
                'amount' => 100.00,
                'currency' => 'INR',
                'payment_method' => 'credit_card',
                'gateway' => 'test_gateway',
                'gateway_response' => ['status' => 'success'],
            ]);

        // Handle the webhook
        $result = $this->paymentService->handleWebhook($gateway, $webhookData);

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals('TEST123456', $result['transaction_id']);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('test_gateway', $result['gateway']);
        $this->assertEquals(1, $result['payment_id']);

        // Assert that the event was dispatched
        Event::assertDispatched(PaymentSucceeded::class, function ($event) use ($payment) {
            return $event->payment->id === $payment->id;
        });
    }
}

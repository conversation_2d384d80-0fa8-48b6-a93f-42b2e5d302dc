<?php

namespace Tests\Integration;

use App\DTOs\Customer\CustomerDTO;
use App\Exceptions\Customer\CustomerException;
use App\Services\Customer\CustomerServiceClient;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class CustomerServiceIntegrationTest extends TestCase
{
    /**
     * Test get customer.
     *
     * @return void
     */
    public function testGetCustomer(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/customers/1' => Http::response([
                'data' => [
                    'pk_customer_code' => 1,
                    'customer_name' => 'Test Customer',
                    'phone' => '1234567890',
                    'email_address' => '<EMAIL>',
                    'status' => true,
                ],
            ], 200),
        ]);

        $customerService = new CustomerServiceClient('http://customer-service-test:8000/api');
        $customer = $customerService->getCustomer(1);

        $this->assertNotNull($customer);
        $this->assertEquals(1, $customer['pk_customer_code']);
        $this->assertEquals('Test Customer', $customer['customer_name']);
        $this->assertEquals('1234567890', $customer['phone']);
        $this->assertEquals('<EMAIL>', $customer['email_address']);
        $this->assertTrue($customer['status']);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service-test:8000/api/customers/1' &&
                   $request->method() === 'GET';
        });
    }

    /**
     * Test get customer with non-existent ID.
     *
     * @return void
     */
    public function testGetCustomerWithNonExistentId(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/customers/999' => Http::response([], 404),
        ]);

        $customerService = new CustomerServiceClient('http://customer-service-test:8000/api');
        $customer = $customerService->getCustomer(999);

        $this->assertNull($customer);
    }

    /**
     * Test create customer.
     *
     * @return void
     */
    public function testCreateCustomer(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/customers' => Http::response([
                'data' => [
                    'pk_customer_code' => 1,
                    'customer_name' => 'New Customer',
                    'phone' => '9876543210',
                    'email_address' => '<EMAIL>',
                    'status' => true,
                ],
            ], 201),
        ]);

        $customerService = new CustomerServiceClient('http://customer-service-test:8000/api');
        $customerDTO = new CustomerDTO(
            'New Customer',
            '9876543210',
            '<EMAIL>'
        );

        $customer = $customerService->createCustomer($customerDTO);

        $this->assertNotNull($customer);
        $this->assertEquals(1, $customer['pk_customer_code']);
        $this->assertEquals('New Customer', $customer['customer_name']);
        $this->assertEquals('9876543210', $customer['phone']);
        $this->assertEquals('<EMAIL>', $customer['email_address']);
        $this->assertTrue($customer['status']);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service-test:8000/api/customers' &&
                   $request->method() === 'POST' &&
                   $request->data()['customer_name'] === 'New Customer' &&
                   $request->data()['phone'] === '9876543210' &&
                   $request->data()['email_address'] === '<EMAIL>';
        });
    }

    /**
     * Test create customer with duplicate phone.
     *
     * @return void
     */
    public function testCreateCustomerWithDuplicatePhone(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/customers' => Http::response([
                'success' => false,
                'message' => 'Customer with phone 9876543210 already exists',
            ], 422),
        ]);

        $customerService = new CustomerServiceClient('http://customer-service-test:8000/api');
        $customerDTO = new CustomerDTO(
            'Duplicate Customer',
            '9876543210',
            '<EMAIL>'
        );

        $this->expectException(CustomerException::class);
        $this->expectExceptionMessage('Failed to create customer: Customer with phone 9876543210 already exists');

        $customerService->createCustomer($customerDTO);
    }

    /**
     * Test update customer.
     *
     * @return void
     */
    public function testUpdateCustomer(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/customers/1' => Http::response([
                'data' => [
                    'pk_customer_code' => 1,
                    'customer_name' => 'Updated Customer',
                    'phone' => '1234567890',
                    'email_address' => '<EMAIL>',
                    'status' => true,
                ],
            ], 200),
        ]);

        $customerService = new CustomerServiceClient('http://customer-service-test:8000/api');
        $customerDTO = new CustomerDTO(
            'Updated Customer',
            '1234567890',
            '<EMAIL>'
        );

        $customer = $customerService->updateCustomer(1, $customerDTO);

        $this->assertNotNull($customer);
        $this->assertEquals(1, $customer['pk_customer_code']);
        $this->assertEquals('Updated Customer', $customer['customer_name']);
        $this->assertEquals('1234567890', $customer['phone']);
        $this->assertEquals('<EMAIL>', $customer['email_address']);
        $this->assertTrue($customer['status']);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service-test:8000/api/customers/1' &&
                   $request->method() === 'PUT' &&
                   $request->data()['customer_name'] === 'Updated Customer' &&
                   $request->data()['phone'] === '1234567890' &&
                   $request->data()['email_address'] === '<EMAIL>';
        });
    }

    /**
     * Test get wallet balance.
     *
     * @return void
     */
    public function testGetWalletBalance(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/customers/1/wallet' => Http::response([
                'data' => [
                    'customer_code' => 1,
                    'balance' => 500.50,
                    'status' => true,
                ],
            ], 200),
        ]);

        $customerService = new CustomerServiceClient('http://customer-service-test:8000/api');
        $balance = $customerService->getWalletBalance(1);

        $this->assertEquals(500.50, $balance);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service-test:8000/api/customers/1/wallet' &&
                   $request->method() === 'GET';
        });
    }
}

<?php

namespace Database\Seeders;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InvoiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample invoices
        $invoices = [
            [
                'customer_id' => 1001,
                'customer_name' => 'Acme Corporation',
                'customer_email' => '<EMAIL>',
                'billing_address' => json_encode([
                    'street' => '123 Business Street',
                    'city' => 'Mumbai',
                    'state' => 'Maharashtra',
                    'postal_code' => '400001',
                    'country' => 'India'
                ]),
                'company_id' => 1,
                'invoice_number' => 'INV-2025-001',
                'due_date' => '2025-06-14',
                'status' => 'sent',
                'type' => 'order',
                'currency' => 'INR',
                'subtotal' => 25000.00,
                'tax_amount' => 4500.00,
                'discount_amount' => 1250.00,
                'total_amount' => 28250.00,
                'payment_method' => null,
                'payment_reference' => null,
                'notes' => 'Monthly service charges for May 2025',
                'metadata' => json_encode(['project_id' => 'PROJ-001', 'department' => 'IT Services'])
            ],
            [
                'customer_id' => 1002,
                'customer_name' => 'Tech Solutions Pvt Ltd',
                'customer_email' => '<EMAIL>',
                'billing_address' => json_encode([
                    'street' => '456 Tech Park',
                    'city' => 'Bangalore',
                    'state' => 'Karnataka',
                    'postal_code' => '560001',
                    'country' => 'India'
                ]),
                'company_id' => 1,
                'invoice_number' => 'INV-2025-002',
                'due_date' => '2025-06-19',
                'status' => 'paid',
                'type' => 'order',
                'currency' => 'INR',
                'subtotal' => 15000.00,
                'tax_amount' => 2700.00,
                'discount_amount' => 0.00,
                'total_amount' => 17700.00,
                'payment_method' => 'bank_transfer',
                'payment_reference' => 'TXN-*********',
                'notes' => 'Software development services',
                'metadata' => json_encode(['project_id' => 'PROJ-002', 'milestone' => 'Phase 1'])
            ],
            [
                'customer_id' => 1003,
                'customer_name' => 'Global Enterprises',
                'customer_email' => '<EMAIL>',
                'billing_address' => json_encode([
                    'street' => '789 International Plaza',
                    'city' => 'Delhi',
                    'state' => 'Delhi',
                    'postal_code' => '110001',
                    'country' => 'India'
                ]),
                'company_id' => 1,
                'invoice_number' => 'INV-2025-003',
                'due_date' => '2025-06-24',
                'status' => 'overdue',
                'type' => 'subscription',
                'currency' => 'USD',
                'subtotal' => 500.00,
                'tax_amount' => 90.00,
                'discount_amount' => 25.00,
                'total_amount' => 565.00,
                'payment_method' => null,
                'payment_reference' => null,
                'notes' => 'Monthly subscription - Premium Plan',
                'metadata' => json_encode(['subscription_id' => 'SUB-001', 'plan' => 'premium'])
            ],
            [
                'customer_id' => 1004,
                'customer_name' => 'StartUp Inc',
                'customer_email' => '<EMAIL>',
                'billing_address' => json_encode([
                    'street' => '321 Innovation Hub',
                    'city' => 'Pune',
                    'state' => 'Maharashtra',
                    'postal_code' => '411001',
                    'country' => 'India'
                ]),
                'company_id' => 1,
                'invoice_number' => 'INV-2025-004',
                'due_date' => '2025-07-01',
                'status' => 'draft',
                'type' => 'order',
                'currency' => 'INR',
                'subtotal' => 8500.00,
                'tax_amount' => 1530.00,
                'discount_amount' => 425.00,
                'total_amount' => 9605.00,
                'payment_method' => null,
                'payment_reference' => null,
                'notes' => 'Consulting services for Q2 2025',
                'metadata' => json_encode(['consultant' => 'John Doe', 'hours' => 85])
            ]
        ];

        foreach ($invoices as $invoiceData) {
            $invoice = Invoice::updateOrCreate(
                ['invoice_number' => $invoiceData['invoice_number']],
                $invoiceData
            );

            // Delete existing items and create new ones
            $invoice->items()->delete();

            // Create invoice items for each invoice
            $this->createInvoiceItems($invoice);
        }
    }

    private function createInvoiceItems(Invoice $invoice): void
    {
        $itemsData = [
            'INV-2025-001' => [
                ['item_name' => 'Web Development Services', 'description' => 'Complete web application development', 'quantity' => 1, 'unit_price' => 20000.00, 'total_price' => 20000.00],
                ['item_name' => 'Database Setup', 'description' => 'Database Setup & Configuration', 'quantity' => 1, 'unit_price' => 5000.00, 'total_price' => 5000.00]
            ],
            'INV-2025-002' => [
                ['item_name' => 'Mobile App Development', 'description' => 'iOS and Android app development', 'quantity' => 1, 'unit_price' => 12000.00, 'total_price' => 12000.00],
                ['item_name' => 'API Integration', 'description' => 'Third-party API integration services', 'quantity' => 1, 'unit_price' => 3000.00, 'total_price' => 3000.00]
            ],
            'INV-2025-003' => [
                ['item_name' => 'Premium Subscription', 'description' => 'Premium Subscription - Monthly', 'quantity' => 1, 'unit_price' => 500.00, 'total_price' => 500.00]
            ],
            'INV-2025-004' => [
                ['item_name' => 'Business Consulting', 'description' => 'Strategic business consulting services', 'quantity' => 85, 'unit_price' => 100.00, 'total_price' => 8500.00]
            ]
        ];

        $items = $itemsData[$invoice->invoice_number] ?? [];

        foreach ($items as $item) {
            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'item_name' => $item['item_name'],
                'description' => $item['description'],
                'quantity' => $item['quantity'],
                'unit_price' => $item['unit_price'],
                'total_price' => $item['total_price']
            ]);
        }
    }
}

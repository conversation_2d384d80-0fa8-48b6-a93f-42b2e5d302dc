<?php

namespace Database\Seeders;

use App\Models\ExchangeRate;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExchangeRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $exchangeRates = [
            // USD to other currencies
            ['from_currency' => 'USD', 'to_currency' => 'EUR', 'rate' => 0.85, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'USD', 'to_currency' => 'GBP', 'rate' => 0.73, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'USD', 'to_currency' => 'INR', 'rate' => 83.25, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'USD', 'to_currency' => 'JPY', 'rate' => 110.50, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],

            // EUR to other currencies
            ['from_currency' => 'EUR', 'to_currency' => 'USD', 'rate' => 1.18, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'EUR', 'to_currency' => 'GBP', 'rate' => 0.86, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'EUR', 'to_currency' => 'INR', 'rate' => 98.15, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],

            // GBP to other currencies
            ['from_currency' => 'GBP', 'to_currency' => 'USD', 'rate' => 1.37, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'GBP', 'to_currency' => 'EUR', 'rate' => 1.16, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'GBP', 'to_currency' => 'INR', 'rate' => 114.05, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],

            // INR to other currencies
            ['from_currency' => 'INR', 'to_currency' => 'USD', 'rate' => 0.012, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'INR', 'to_currency' => 'EUR', 'rate' => 0.0102, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
            ['from_currency' => 'INR', 'to_currency' => 'GBP', 'rate' => 0.0088, 'provider' => 'xe.com', 'rate_date' => '2025-06-02'],
        ];

        foreach ($exchangeRates as $rate) {
            ExchangeRate::updateOrCreate(
                [
                    'from_currency' => $rate['from_currency'],
                    'to_currency' => $rate['to_currency'],
                    'rate_date' => $rate['rate_date'],
                    'provider' => $rate['provider']
                ],
                ['rate' => $rate['rate']]
            );
        }
    }
}

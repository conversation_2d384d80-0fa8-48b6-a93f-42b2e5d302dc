<?php

namespace Database\Seeders;

use App\Models\InvoiceConfiguration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InvoiceConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configurations = [
            // Tax Rate Configurations
            [
                'company_id' => 1,
                'name' => 'Standard VAT Rate',
                'type' => 'tax_rate',
                'configuration' => json_encode(['rate' => 18.0, 'description' => 'Standard VAT rate for India', 'country' => 'IN', 'category' => 'standard']),
                'is_active' => true
            ],
            [
                'company_id' => 1,
                'name' => 'Reduced VAT Rate',
                'type' => 'tax_rate',
                'configuration' => json_encode(['rate' => 5.0, 'description' => 'Reduced VAT rate for essential goods', 'country' => 'IN', 'category' => 'reduced']),
                'is_active' => true
            ],

            // Discount Rules
            [
                'company_id' => 1,
                'name' => 'Early Payment Discount',
                'type' => 'discount_rule',
                'configuration' => json_encode(['percentage' => 2.5, 'days' => 10, 'description' => '2.5% discount if paid within 10 days', 'type' => 'early_payment', 'auto_apply' => true]),
                'is_active' => true
            ],
            [
                'company_id' => 1,
                'name' => 'Volume Discount',
                'type' => 'discount_rule',
                'configuration' => json_encode(['percentage' => 5.0, 'min_amount' => 10000, 'description' => '5% discount for orders above ₹10,000', 'type' => 'volume', 'currency' => 'INR']),
                'is_active' => true
            ],

            // Payment Terms
            [
                'company_id' => 1,
                'name' => 'Net 30 Days',
                'type' => 'payment_terms',
                'configuration' => json_encode(['days' => 30, 'description' => 'Payment due within 30 days', 'default' => true, 'grace_period' => 5]),
                'is_active' => true
            ],
            [
                'company_id' => 1,
                'name' => 'Net 15 Days',
                'type' => 'payment_terms',
                'configuration' => json_encode(['days' => 15, 'description' => 'Payment due within 15 days', 'priority_customer' => true]),
                'is_active' => true
            ],

            // Currency Settings
            [
                'company_id' => 1,
                'name' => 'Default Currency',
                'type' => 'currency',
                'configuration' => json_encode(['code' => 'INR', 'symbol' => '₹', 'decimal_places' => 2, 'default' => true, 'country' => 'IN']),
                'is_active' => true
            ],
            [
                'company_id' => 1,
                'name' => 'Secondary Currency',
                'type' => 'currency',
                'configuration' => json_encode(['code' => 'USD', 'symbol' => '$', 'decimal_places' => 2, 'international' => true]),
                'is_active' => true
            ],

            // Template Settings
            [
                'company_id' => 1,
                'name' => 'Standard Invoice Template',
                'type' => 'template',
                'configuration' => json_encode(['template_id' => 'standard', 'layout' => 'modern', 'color_scheme' => 'blue', 'default' => true, 'version' => '2.0']),
                'is_active' => true
            ]
        ];

        foreach ($configurations as $config) {
            InvoiceConfiguration::updateOrCreate(
                [
                    'company_id' => $config['company_id'],
                    'name' => $config['name'],
                    'type' => $config['type']
                ],
                [
                    'configuration' => $config['configuration'],
                    'is_active' => $config['is_active']
                ]
            );
        }
    }
}

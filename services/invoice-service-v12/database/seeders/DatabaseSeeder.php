<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create test user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => 'Test User']
        );

        // Seed invoice-related data
        $this->call([
            ExchangeRateSeeder::class,
            InvoiceConfigurationSeeder::class,
            InvoiceSeeder::class,
        ]);
    }
}

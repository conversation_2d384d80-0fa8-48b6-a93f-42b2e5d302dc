<?php

namespace Database\Factories;

use App\Models\InvoiceItem;
use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InvoiceItem>
 */
class InvoiceItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = InvoiceItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 5);
        $unitPrice = $this->faker->randomFloat(2, 50, 500);
        $taxRate = $this->faker->randomElement([0, 5, 12, 18, 28]);
        $discountRate = $this->faker->randomElement([0, 5, 10, 15]);

        $baseTotal = $quantity * $unitPrice;
        $discountAmount = ($baseTotal * $discountRate) / 100;
        $subtotal = $baseTotal - $discountAmount;
        $taxAmount = ($subtotal * $taxRate) / 100;
        $totalPrice = $subtotal + $taxAmount;

        return [
            'invoice_id' => Invoice::factory(),
            'item_type' => $this->faker->randomElement(['meal', 'subscription', 'delivery', 'tax', 'discount']),
            'item_name' => $this->faker->randomElement([
                'Chicken Biryani',
                'Vegetable Thali',
                'Paneer Butter Masala',
                'Dal Tadka',
                'Roti',
                'Rice',
                'Delivery Charge',
                'Service Charge',
                'Packaging Charge'
            ]),
            'description' => $this->faker->optional()->sentence(),
            'sku' => $this->faker->optional()->regexify('[A-Z]{3}-[0-9]{3}'),
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $totalPrice,
            'tax_rate' => $taxRate,
            'tax_amount' => $taxAmount,
            'discount_rate' => $discountRate,
            'discount_amount' => $discountAmount,
            'metadata' => $this->faker->optional()->passthrough([
                'category' => $this->faker->randomElement(['food', 'beverage', 'service']),
                'allergens' => $this->faker->optional()->randomElements(['nuts', 'dairy', 'gluten'], 2),
            ]),
        ];
    }

    /**
     * Indicate that the item is a meal.
     */
    public function meal(): static
    {
        return $this->state(fn (array $attributes) => [
            'item_type' => 'meal',
            'item_name' => $this->faker->randomElement([
                'Chicken Biryani',
                'Vegetable Thali',
                'Paneer Butter Masala',
                'Dal Tadka with Rice',
                'Chole Bhature',
                'Masala Dosa',
                'Idli Sambar'
            ]),
        ]);
    }

    /**
     * Indicate that the item is a delivery charge.
     */
    public function delivery(): static
    {
        return $this->state(fn (array $attributes) => [
            'item_type' => 'delivery',
            'item_name' => 'Delivery Charge',
            'quantity' => 1,
            'unit_price' => $this->faker->randomFloat(2, 20, 100),
        ]);
    }

    /**
     * Indicate that the item has no tax.
     */
    public function noTax(): static
    {
        return $this->state(fn (array $attributes) => [
            'tax_rate' => 0,
            'tax_amount' => 0,
        ]);
    }

    /**
     * Indicate that the item has no discount.
     */
    public function noDiscount(): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_rate' => 0,
            'discount_amount' => 0,
        ]);
    }
}

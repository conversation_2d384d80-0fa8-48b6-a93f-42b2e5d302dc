<?php

namespace Database\Factories;

use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Invoice>
 */
class InvoiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Invoice::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'invoice_number' => Invoice::generateInvoiceNumber(),
            'customer_id' => $this->faker->numberBetween(1, 1000),
            'order_id' => $this->faker->optional()->numberBetween(1, 1000),
            'subscription_id' => $this->faker->optional()->numberBetween(1, 1000),
            'customer_name' => $this->faker->name(),
            'customer_email' => $this->faker->safeEmail(),
            'customer_phone' => $this->faker->optional()->phoneNumber(),
            'billing_address' => [
                'line1' => $this->faker->streetAddress(),
                'line2' => $this->faker->optional()->secondaryAddress(),
                'city' => $this->faker->city(),
                'state' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
                'country' => $this->faker->country(),
            ],
            'shipping_address' => $this->faker->optional()->passthrough([
                'line1' => $this->faker->streetAddress(),
                'line2' => $this->faker->optional()->secondaryAddress(),
                'city' => $this->faker->city(),
                'state' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
                'country' => $this->faker->country(),
            ]),
            'subtotal' => $this->faker->randomFloat(2, 100, 5000),
            'tax_amount' => $this->faker->randomFloat(2, 10, 500),
            'discount_amount' => $this->faker->randomFloat(2, 0, 200),
            'total_amount' => function (array $attributes) {
                return $attributes['subtotal'] + $attributes['tax_amount'] - $attributes['discount_amount'];
            },
            'currency' => 'INR',
            'status' => $this->faker->randomElement(['draft', 'sent', 'paid', 'overdue', 'cancelled']),
            'type' => $this->faker->randomElement(['order', 'subscription', 'refund', 'adjustment']),
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'paid_at' => $this->faker->optional(0.3)->dateTimeBetween('-30 days', 'now'),
            'payment_method' => $this->faker->optional()->randomElement(['credit_card', 'debit_card', 'bank_transfer', 'wallet', 'cash']),
            'payment_reference' => $this->faker->optional()->regexify('[A-Z0-9]{10}'),
            'notes' => $this->faker->optional()->sentence(),
            'metadata' => $this->faker->optional()->passthrough([
                'source' => $this->faker->randomElement(['web', 'mobile', 'api']),
                'campaign' => $this->faker->optional()->word(),
            ]),
            'company_id' => $this->faker->numberBetween(1, 10),
            'unit_id' => $this->faker->optional()->numberBetween(1, 50),
        ];
    }

    /**
     * Indicate that the invoice is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'paid_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'payment_method' => $this->faker->randomElement(['credit_card', 'debit_card', 'bank_transfer']),
            'payment_reference' => $this->faker->regexify('[A-Z0-9]{10}'),
        ]);
    }

    /**
     * Indicate that the invoice is overdue.
     */
    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'overdue',
            'due_date' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the invoice is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'paid_at' => null,
        ]);
    }
}

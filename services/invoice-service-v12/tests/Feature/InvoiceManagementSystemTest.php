<?php

namespace Tests\Feature;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\InvoiceConfiguration;
use App\Models\ExchangeRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class InvoiceManagementSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Authenticate user for protected routes
        Sanctum::actingAs(
            \App\Models\User::factory()->create(),
            ['*']
        );

        // Create default configurations for testing
        InvoiceConfiguration::createDefaultConfigurations(1);

        // Create sample exchange rates
        ExchangeRate::create([
            'from_currency' => 'USD',
            'to_currency' => 'EUR',
            'rate' => 0.85,
            'provider' => 'test',
            'rate_date' => now()->toDateString(),
        ]);
    }

    /**
     * Test invoice configuration management.
     */
    public function test_invoice_configuration_management(): void
    {
        $configData = [
            'company_id' => 1,
            'name' => 'Test Tax Rate',
            'type' => 'tax_rate',
            'configuration' => [
                'rate' => 15.0,
                'name' => 'VAT',
                'region' => 'EU',
                'applies_to' => 'all',
            ],
            'is_active' => true,
        ];

        // Test creating configuration
        $response = $this->postJson('/api/v2/invoice-configurations', $configData);
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'company_id',
                        'name',
                        'type',
                        'configuration',
                        'is_active',
                    ]
                ]);

        $configId = $response->json('data.id');

        // Test retrieving configuration
        $response = $this->getJson("/api/v2/invoice-configurations/{$configId}");
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'name' => 'Test Tax Rate',
                        'type' => 'tax_rate',
                    ]
                ]);

        // Test updating configuration
        $updateData = ['name' => 'Updated Tax Rate'];
        $response = $this->putJson("/api/v2/invoice-configurations/{$configId}", $updateData);
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'name' => 'Updated Tax Rate',
                    ]
                ]);

        // Test listing configurations
        $response = $this->getJson('/api/v2/invoice-configurations?company_id=1&type=tax_rate');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'company_id',
                            'name',
                            'type',
                            'configuration',
                        ]
                    ]
                ]);

        // Test deleting configuration
        $response = $this->deleteJson("/api/v2/invoice-configurations/{$configId}");
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
    }

    /**
     * Test real-time invoice calculations.
     */
    public function test_real_time_invoice_calculations(): void
    {
        $calculationData = [
            'company_id' => 1,
            'items' => [
                [
                    'item_name' => 'Test Product 1',
                    'quantity' => 2,
                    'unit_price' => 100.00,
                    'tax_rate' => 10.0,
                    'discount_rate' => 5.0,
                    'item_type' => 'product',
                ],
                [
                    'item_name' => 'Test Product 2',
                    'quantity' => 1,
                    'unit_price' => 50.00,
                    'tax_rate' => 10.0,
                    'discount_rate' => 0.0,
                    'item_type' => 'product',
                ],
            ],
            'currency' => 'USD',
            'customer_type' => 'regular',
            'region' => 'default',
        ];

        $response = $this->postJson('/api/v2/invoices/calculate', $calculationData);
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'items',
                        'subtotal',
                        'tax_amount',
                        'discount_amount',
                        'total_amount',
                        'currency',
                        'calculation_details',
                    ],
                    'calculation_time_ms'
                ]);

        $data = $response->json('data');

        // Verify calculations
        $this->assertEquals(250.00, $data['subtotal']); // (2*100) + (1*50)
        $this->assertGreaterThan(0, $data['tax_amount']);
        $this->assertGreaterThan(0, $data['discount_amount']);
        $this->assertEquals('USD', $data['currency']);
        $this->assertCount(2, $data['items']);
    }

    /**
     * Test currency conversion functionality.
     */
    public function test_currency_conversion(): void
    {
        $calculationResult = [
            'subtotal' => 100.00,
            'tax_amount' => 10.00,
            'discount_amount' => 5.00,
            'total_amount' => 105.00,
            'currency' => 'USD',
            'items' => [
                [
                    'unit_price' => 50.00,
                    'base_amount' => 100.00,
                    'discount_amount' => 5.00,
                    'tax_amount' => 10.00,
                    'line_total' => 105.00,
                ]
            ]
        ];

        $conversionData = [
            'calculation_result' => $calculationResult,
            'target_currency' => 'EUR',
        ];

        $response = $this->postJson('/api/v2/invoices/convert-currency', $conversionData);
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'subtotal',
                        'tax_amount',
                        'discount_amount',
                        'total_amount',
                        'currency',
                        'original_currency',
                        'conversion_rate',
                        'conversion_date',
                        'items',
                    ]
                ]);

        $data = $response->json('data');
        $this->assertEquals('EUR', $data['currency']);
        $this->assertEquals('USD', $data['original_currency']);
        $this->assertEquals(0.85, $data['conversion_rate']);
    }

    /**
     * Test exchange rates functionality.
     */
    public function test_exchange_rates_functionality(): void
    {
        // Test getting exchange rates
        $response = $this->getJson('/api/v2/invoices/exchange-rates?from_currency=USD&to_currencies[]=EUR&to_currencies[]=GBP');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'base_currency',
                        'rates',
                        'supported_currencies',
                    ]
                ]);

        // Test updating exchange rates
        $response = $this->postJson('/api/v2/invoices/update-exchange-rates', [
            'currencies' => ['USD', 'EUR', 'GBP']
        ]);
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'updated_count',
                        'currencies',
                        'updated_at',
                    ]
                ]);
    }

    /**
     * Test payment terms calculation.
     */
    public function test_payment_terms_calculation(): void
    {
        $response = $this->postJson('/api/v2/invoices/payment-terms', [
            'company_id' => 1,
            'issue_date' => '2025-01-01',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'issue_date',
                        'due_date',
                        'early_payment_date',
                        'early_payment_discount_rate',
                        'payment_terms',
                    ]
                ]);

        $data = $response->json('data');
        $this->assertEquals('2025-01-01', $data['issue_date']);
        $this->assertNotEmpty($data['due_date']);
    }

    /**
     * Test enhanced PDF generation.
     */
    public function test_enhanced_pdf_generation(): void
    {
        // Create a test invoice
        $invoice = Invoice::create([
            'company_id' => 1,
            'customer_id' => 1,
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'invoice_number' => 'INV-TEST-001',
            'issue_date' => now(),
            'due_date' => now()->addDays(30),
            'subtotal' => 100.00,
            'tax_amount' => 10.00,
            'discount_amount' => 5.00,
            'total_amount' => 105.00,
            'currency' => 'USD',
            'status' => 'draft',
        ]);

        InvoiceItem::create([
            'invoice_id' => $invoice->id,
            'item_name' => 'Test Product',
            'description' => 'Test Description',
            'quantity' => 1,
            'unit_price' => 100.00,
            'tax_rate' => 10.0,
            'tax_amount' => 10.00,
            'discount_rate' => 5.0,
            'discount_amount' => 5.00,
            'line_total' => 105.00,
        ]);

        // Test enhanced PDF generation
        $response = $this->getJson("/api/v2/invoices/{$invoice->id}/enhanced-pdf?download=false&template=modern");
        $response->assertStatus(200);
        $this->assertEquals('application/pdf', $response->headers->get('Content-Type'));
    }

    /**
     * Test batch PDF generation.
     */
    public function test_batch_pdf_generation(): void
    {
        // Create test invoices
        $invoice1 = Invoice::create([
            'company_id' => 1,
            'customer_id' => 1,
            'customer_name' => 'Batch Customer 1',
            'customer_email' => '<EMAIL>',
            'invoice_number' => 'INV-BATCH-001',
            'issue_date' => now(),
            'due_date' => now()->addDays(30),
            'subtotal' => 100.00,
            'tax_amount' => 10.00,
            'discount_amount' => 0.00,
            'total_amount' => 110.00,
            'currency' => 'USD',
            'status' => 'sent',
        ]);

        $invoice2 = Invoice::create([
            'company_id' => 1,
            'customer_id' => 2,
            'customer_name' => 'Batch Customer 2',
            'customer_email' => '<EMAIL>',
            'invoice_number' => 'INV-BATCH-002',
            'issue_date' => now(),
            'due_date' => now()->addDays(30),
            'subtotal' => 200.00,
            'tax_amount' => 20.00,
            'discount_amount' => 10.00,
            'total_amount' => 210.00,
            'currency' => 'USD',
            'status' => 'sent',
        ]);

        // Add items to invoices
        foreach ([$invoice1, $invoice2] as $invoice) {
            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'item_name' => 'Batch Test Product',
                'description' => 'Batch Test Description',
                'quantity' => 1,
                'unit_price' => $invoice->subtotal,
                'tax_rate' => 10.0,
                'tax_amount' => $invoice->tax_amount,
                'discount_rate' => 0.0,
                'discount_amount' => $invoice->discount_amount,
                'line_total' => $invoice->total_amount,
            ]);
        }

        // Test batch PDF generation
        $response = $this->postJson('/api/v2/invoices/batch-pdf', [
            'invoice_ids' => [$invoice1->id, $invoice2->id],
            'template' => 'modern',
            'include_timestamp' => true,
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'summary' => [
                            'total',
                            'successful',
                            'failed',
                        ],
                        'results' => [
                            '*' => [
                                'invoice_id',
                                'status',
                                'filename',
                                'storage_path',
                                'file_size',
                            ]
                        ]
                    ]
                ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['summary']['total']);
        $this->assertEquals(2, $data['summary']['successful']);
        $this->assertEquals(0, $data['summary']['failed']);
    }

    /**
     * Test invoice workflow status management.
     */
    public function test_invoice_workflow_status_management(): void
    {
        // Create a test invoice
        $invoice = Invoice::create([
            'company_id' => 1,
            'customer_id' => 1,
            'customer_name' => 'Workflow Customer',
            'customer_email' => '<EMAIL>',
            'invoice_number' => 'INV-WORKFLOW-001',
            'issue_date' => now(),
            'due_date' => now()->addDays(30),
            'subtotal' => 100.00,
            'tax_amount' => 10.00,
            'discount_amount' => 0.00,
            'total_amount' => 110.00,
            'currency' => 'USD',
            'status' => 'draft',
        ]);

        // Test marking invoice as paid
        $response = $this->postJson("/api/v2/invoices/{$invoice->id}/mark-paid", [
            'payment_method' => 'credit_card',
            'payment_reference' => 'PAY-123456',
            'amount_paid' => 110.00,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'status' => 'paid',
                    ]
                ]);
    }

    /**
     * Test API response times for calculations.
     */
    public function test_calculation_performance(): void
    {
        $calculationData = [
            'company_id' => 1,
            'items' => array_fill(0, 10, [
                'item_name' => 'Performance Test Product',
                'quantity' => 1,
                'unit_price' => 100.00,
                'tax_rate' => 10.0,
                'discount_rate' => 5.0,
                'item_type' => 'product',
            ]),
            'currency' => 'USD',
            'customer_type' => 'regular',
            'region' => 'default',
        ];

        $startTime = microtime(true);
        $response = $this->postJson('/api/v2/invoices/calculate', $calculationData);
        $endTime = microtime(true);

        $response->assertStatus(200);

        $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $this->assertLessThan(200, $responseTime, 'API response time should be less than 200ms');

        $calculationTime = $response->json('calculation_time_ms');
        $this->assertLessThan(100, $calculationTime, 'Calculation time should be less than 100ms');
    }
}

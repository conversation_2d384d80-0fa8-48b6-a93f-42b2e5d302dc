<?php

namespace Tests\Feature;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Services\InvoiceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class InvoiceServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected InvoiceService $invoiceService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->invoiceService = app(InvoiceService::class);
    }

    /**
     * Test health check endpoint.
     */
    public function test_health_check(): void
    {
        $response = $this->get('/api/health');

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'healthy',
                'service' => 'invoice-service-v12'
            ]);
    }

    /**
     * Test invoice creation.
     */
    public function test_create_invoice(): void
    {
        $invoiceData = [
            'customer_id' => 123,
            'customer_name' => '<PERSON>',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+91 98765 43210',
            'billing_address' => [
                'line1' => '123 Main Street',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'postal_code' => '400001',
                'country' => 'India'
            ],
            'due_date' => now()->addDays(30)->format('Y-m-d'),
            'company_id' => 1,
            'type' => 'order'
        ];

        $items = [
            [
                'item_type' => 'meal',
                'item_name' => 'Chicken Biryani',
                'description' => 'Delicious chicken biryani',
                'quantity' => 2,
                'unit_price' => 250.00,
                'tax_rate' => 18.00
            ],
            [
                'item_type' => 'delivery',
                'item_name' => 'Delivery Charge',
                'quantity' => 1,
                'unit_price' => 50.00,
                'tax_rate' => 18.00
            ]
        ];

        $invoice = $this->invoiceService->createInvoice($invoiceData, $items);

        $this->assertInstanceOf(Invoice::class, $invoice);
        $this->assertEquals('John Doe', $invoice->customer_name);
        $this->assertEquals(2, $invoice->items->count());
        $this->assertNotNull($invoice->invoice_number);
        $this->assertTrue($invoice->total_amount > 0);
    }

    /**
     * Test invoice retrieval.
     */
    public function test_get_invoice(): void
    {
        $invoice = Invoice::factory()->create();
        InvoiceItem::factory()->count(2)->create(['invoice_id' => $invoice->id]);

        $retrievedInvoice = $this->invoiceService->getInvoice($invoice->id);

        $this->assertEquals($invoice->id, $retrievedInvoice->id);
        $this->assertEquals(2, $retrievedInvoice->items->count());
    }

    /**
     * Test invoice status update.
     */
    public function test_update_invoice_status(): void
    {
        $invoice = Invoice::factory()->create(['status' => 'draft']);

        $updatedInvoice = $this->invoiceService->updateInvoiceStatus(
            $invoice->id,
            'paid',
            ['payment_method' => 'credit_card', 'payment_reference' => 'TXN123']
        );

        $this->assertEquals('paid', $updatedInvoice->status);
        $this->assertEquals('credit_card', $updatedInvoice->payment_method);
        $this->assertEquals('TXN123', $updatedInvoice->payment_reference);
        $this->assertNotNull($updatedInvoice->paid_at);
    }

    /**
     * Test mark invoice as paid.
     */
    public function test_mark_invoice_as_paid(): void
    {
        $invoice = Invoice::factory()->create(['status' => 'sent']);

        $paidInvoice = $this->invoiceService->markInvoiceAsPaid(
            $invoice->id,
            'bank_transfer',
            'REF456'
        );

        $this->assertEquals('paid', $paidInvoice->status);
        $this->assertEquals('bank_transfer', $paidInvoice->payment_method);
        $this->assertEquals('REF456', $paidInvoice->payment_reference);
        $this->assertNotNull($paidInvoice->paid_at);
    }

    /**
     * Test invoice statistics.
     */
    public function test_invoice_statistics(): void
    {
        // Create test invoices
        Invoice::factory()->count(5)->create(['status' => 'paid', 'total_amount' => 1000]);
        Invoice::factory()->count(3)->create(['status' => 'sent', 'total_amount' => 500]);
        Invoice::factory()->count(2)->create(['status' => 'overdue', 'total_amount' => 750]);

        $statistics = $this->invoiceService->getInvoiceStatistics();

        $this->assertEquals(10, $statistics['total_invoices']);
        $this->assertEquals(5, $statistics['paid_invoices']);
        $this->assertEquals(3, $statistics['pending_invoices']);
        $this->assertEquals(5000, $statistics['paid_amount']);
        $this->assertEquals(1500, $statistics['pending_amount']);
    }

    /**
     * Test invoice number generation.
     */
    public function test_invoice_number_generation(): void
    {
        $invoiceNumber = Invoice::generateInvoiceNumber();

        $this->assertStringStartsWith('INV-', $invoiceNumber);
        $this->assertMatchesRegularExpression('/^INV-\d{6}-\d{10}-\d{4}$/', $invoiceNumber);
    }

    /**
     * Test API endpoint for creating invoice.
     */
    public function test_api_create_invoice(): void
    {
        // Skip authentication for testing
        $this->withoutMiddleware();

        $data = [
            'customer_id' => 123,
            'customer_name' => 'Jane Smith',
            'customer_email' => '<EMAIL>',
            'billing_address' => [
                'line1' => '456 Oak Avenue',
                'city' => 'Delhi',
                'state' => 'Delhi',
                'postal_code' => '110001',
                'country' => 'India'
            ],
            'due_date' => now()->addDays(30)->format('Y-m-d'),
            'company_id' => 1,
            'items' => [
                [
                    'item_type' => 'meal',
                    'item_name' => 'Vegetable Thali',
                    'quantity' => 1,
                    'unit_price' => 200.00,
                    'tax_rate' => 18.00
                ]
            ]
        ];

        $response = $this->postJson('/api/v2/invoices', $data);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Invoice created successfully'
            ])
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'invoice_number',
                    'customer_name',
                    'total_amount',
                    'items'
                ]
            ]);
    }
}

<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Invoice;
use App\Models\InvoiceConfiguration;
use App\Models\ExchangeRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class BasicInvoiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test health check endpoint.
     */
    public function test_health_check(): void
    {
        $response = $this->get('/api/health');
        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'healthy',
                    'service' => 'invoice-service-v12',
                ]);
    }

    /**
     * Test basic invoice creation.
     */
    public function test_basic_invoice_creation(): void
    {
        // Create and authenticate user
        $user = User::factory()->create();
        Sanctum::actingAs($user, ['*']);

        // Create invoice data
        $invoiceData = [
            'company_id' => 1,
            'customer_id' => 1,
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'invoice_number' => 'INV-TEST-001',
            'issue_date' => now()->toDateString(),
            'due_date' => now()->addDays(30)->toDateString(),
            'subtotal' => 100.00,
            'tax_amount' => 10.00,
            'discount_amount' => 0.00,
            'total_amount' => 110.00,
            'currency' => 'USD',
            'status' => 'draft',
            'items' => [
                [
                    'item_name' => 'Test Product',
                    'description' => 'Test Description',
                    'quantity' => 1,
                    'unit_price' => 100.00,
                    'tax_rate' => 10.0,
                    'tax_amount' => 10.00,
                    'discount_rate' => 0.0,
                    'discount_amount' => 0.00,
                    'line_total' => 110.00,
                ]
            ]
        ];

        $response = $this->postJson('/api/v2/invoices', $invoiceData);
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'invoice_number',
                        'total_amount',
                        'status',
                    ]
                ]);
    }

    /**
     * Test exchange rate model.
     */
    public function test_exchange_rate_model(): void
    {
        // Create exchange rate
        $rate = ExchangeRate::create([
            'from_currency' => 'USD',
            'to_currency' => 'EUR',
            'rate' => 0.85,
            'provider' => 'test',
            'rate_date' => now()->toDateString(),
        ]);

        $this->assertDatabaseHas('exchange_rates', [
            'from_currency' => 'USD',
            'to_currency' => 'EUR',
            'rate' => 0.85,
        ]);

        // Test rate retrieval
        $retrievedRate = ExchangeRate::getLatestRate('USD', 'EUR');
        $this->assertEquals(0.85, $retrievedRate);
    }

    /**
     * Test invoice configuration model.
     */
    public function test_invoice_configuration_model(): void
    {
        $config = InvoiceConfiguration::create([
            'company_id' => 1,
            'name' => 'Test Tax Rate',
            'type' => 'tax_rate',
            'configuration' => [
                'rate' => 15.0,
                'name' => 'VAT',
                'region' => 'EU',
            ],
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('invoice_configurations', [
            'company_id' => 1,
            'name' => 'Test Tax Rate',
            'type' => 'tax_rate',
        ]);

        // Test configuration retrieval
        $configs = InvoiceConfiguration::forCompany(1)->ofType('tax_rate')->get();
        $this->assertCount(1, $configs);
    }
}

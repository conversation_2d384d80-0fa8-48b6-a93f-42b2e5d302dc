<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InvoiceConfiguration;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * InvoiceConfigurationController for OneFoodDialer 2025
 *
 * Manages invoice configuration settings including tax rates, discount rules, and templates
 */
class InvoiceConfigurationController extends Controller
{
    /**
     * Display a listing of configurations.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $companyId = $request->input('company_id');
            $type = $request->input('type');
            $isActive = $request->input('is_active');

            $query = InvoiceConfiguration::query();

            if ($companyId) {
                $query->forCompany($companyId);
            }

            if ($type) {
                $query->ofType($type);
            }

            if ($isActive !== null) {
                $query->where('is_active', (bool) $isActive);
            }

            $configurations = $query->orderBy('created_at', 'desc')->get();

            return response()->json([
                'success' => true,
                'message' => 'Configurations retrieved successfully',
                'data' => $configurations
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve invoice configurations', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve configurations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created configuration.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_id' => 'required|integer',
                'name' => 'required|string|max:255',
                'type' => 'required|in:tax_rate,discount_rule,payment_terms,template,currency,automation',
                'configuration' => 'required|array',
                'is_active' => 'boolean',
                'created_by' => 'nullable|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $configuration = InvoiceConfiguration::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Configuration created successfully',
                'data' => $configuration
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create invoice configuration', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified configuration.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $configuration = InvoiceConfiguration::findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Configuration retrieved successfully',
                'data' => $configuration
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve invoice configuration', [
                'configuration_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Configuration not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified configuration.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'string|max:255',
                'type' => 'in:tax_rate,discount_rule,payment_terms,template,currency,automation',
                'configuration' => 'array',
                'is_active' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $configuration = InvoiceConfiguration::findOrFail($id);
            $configuration->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Configuration updated successfully',
                'data' => $configuration
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update invoice configuration', [
                'configuration_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified configuration.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $configuration = InvoiceConfiguration::findOrFail($id);
            $configuration->delete();

            return response()->json([
                'success' => true,
                'message' => 'Configuration deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete invoice configuration', [
                'configuration_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

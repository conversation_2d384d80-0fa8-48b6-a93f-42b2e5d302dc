<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\InvoiceCalculationService;
use App\Services\MetricsService;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * InvoiceCalculationController for OneFoodDialer 2025
 *
 * Handles real-time invoice calculations and currency conversions
 */
class InvoiceCalculationController extends Controller
{
    protected InvoiceCalculationService $calculationService;
    protected MetricsService $metricsService;

    public function __construct(InvoiceCalculationService $calculationService, MetricsService $metricsService)
    {
        $this->calculationService = $calculationService;
        $this->metricsService = $metricsService;
    }

    /**
     * Calculate invoice totals in real-time.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateTotals(Request $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $validator = Validator::make($request->all(), [
                'company_id' => 'required|integer',
                'items' => 'required|array|min:1',
                'items.*.item_name' => 'required|string',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.unit_price' => 'required|numeric|min:0',
                'items.*.tax_rate' => 'nullable|numeric|min:0|max:100',
                'items.*.discount_rate' => 'nullable|numeric|min:0|max:100',
                'items.*.item_type' => 'nullable|string',
                'currency' => 'nullable|string|size:3',
                'customer_type' => 'nullable|string',
                'region' => 'nullable|string',
                'total_quantity' => 'nullable|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = $request->input('company_id');
            $items = $request->input('items');
            $options = $request->only(['currency', 'customer_type', 'region', 'total_quantity']);

            $calculations = $this->calculationService->calculateInvoiceTotals($items, $companyId, $options);

            // Record metrics
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordApiResponseTime('/api/v2/invoices/calculate', 'POST', $duration, 200);

            return response()->json([
                'success' => true,
                'message' => 'Invoice totals calculated successfully',
                'data' => $calculations,
                'calculation_time_ms' => round($duration * 1000, 2)
            ]);
        } catch (\Exception $e) {
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordApiResponseTime('/api/v2/invoices/calculate', 'POST', $duration, 500);
            $this->metricsService->recordError('invoice-service', 'calculate_totals', get_class($e));

            Log::error('Failed to calculate invoice totals', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate invoice totals',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert invoice amounts to different currency.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function convertCurrency(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'calculation_result' => 'required|array',
                'target_currency' => 'required|string|size:3',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $calculationResult = $request->input('calculation_result');
            $targetCurrency = $request->input('target_currency');

            $convertedResult = $this->calculationService->convertCurrency($calculationResult, $targetCurrency);

            return response()->json([
                'success' => true,
                'message' => 'Currency conversion completed successfully',
                'data' => $convertedResult
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to convert currency', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to convert currency',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get exchange rates for supported currencies.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getExchangeRates(Request $request): JsonResponse
    {
        try {
            $fromCurrency = $request->input('from_currency', 'USD');
            $toCurrencies = $request->input('to_currencies', ['EUR', 'GBP', 'INR']);

            $rates = [];
            foreach ($toCurrencies as $toCurrency) {
                $rate = ExchangeRate::getLatestRate($fromCurrency, $toCurrency);
                $rates[$toCurrency] = [
                    'rate' => $rate,
                    'last_updated' => ExchangeRate::where('from_currency', $fromCurrency)
                        ->where('to_currency', $toCurrency)
                        ->orderBy('rate_date', 'desc')
                        ->first()
                        ?->rate_date
                        ?->toDateString()
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Exchange rates retrieved successfully',
                'data' => [
                    'base_currency' => $fromCurrency,
                    'rates' => $rates,
                    'supported_currencies' => ExchangeRate::getSupportedCurrencies()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get exchange rates', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get exchange rates',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate payment terms and due dates.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calculatePaymentTerms(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_id' => 'required|integer',
                'issue_date' => 'nullable|date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = $request->input('company_id');
            $options = $request->only(['issue_date']);

            $paymentTerms = $this->calculationService->calculatePaymentTerms($companyId, $options);

            return response()->json([
                'success' => true,
                'message' => 'Payment terms calculated successfully',
                'data' => $paymentTerms
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to calculate payment terms', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate payment terms',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update exchange rates from external provider.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateExchangeRates(Request $request): JsonResponse
    {
        try {
            $currencies = $request->input('currencies', ['USD', 'EUR', 'GBP', 'INR']);
            $updatedCount = ExchangeRate::updateAllRates($currencies);

            return response()->json([
                'success' => true,
                'message' => 'Exchange rates updated successfully',
                'data' => [
                    'updated_count' => $updatedCount,
                    'currencies' => $currencies,
                    'updated_at' => now()->toISOString()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update exchange rates', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update exchange rates',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

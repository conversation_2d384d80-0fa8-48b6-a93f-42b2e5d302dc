<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\InvoiceService;
use App\Services\InvoicePdfService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Exception;

/**
 * InvoiceController for OneFoodDialer 2025
 *
 * RESTful API controller for invoice management
 */
class InvoiceController extends Controller
{
    protected InvoiceService $invoiceService;
    protected InvoicePdfService $pdfService;

    public function __construct(InvoiceService $invoiceService, InvoicePdfService $pdfService)
    {
        $this->invoiceService = $invoiceService;
        $this->pdfService = $pdfService;
    }

    /**
     * Display a listing of invoices.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'customer_id', 'status', 'type', 'company_id',
                'date_from', 'date_to'
            ]);

            $perPage = $request->input('per_page', 15);
            $invoices = $this->invoiceService->getInvoices($filters, $perPage);

            return response()->json([
                'success' => true,
                'message' => 'Invoices retrieved successfully',
                'data' => $invoices
            ]);
        } catch (Exception $e) {
            Log::error('Failed to retrieve invoices', [
                'error' => $e->getMessage(),
                'filters' => $filters ?? []
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve invoices',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created invoice.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
                'customer_name' => 'required|string|max:255',
                'customer_email' => 'required|email|max:255',
                'customer_phone' => 'nullable|string|max:20',
                'billing_address' => 'required|array',
                'billing_address.line1' => 'required|string',
                'billing_address.city' => 'required|string',
                'billing_address.state' => 'required|string',
                'billing_address.postal_code' => 'required|string',
                'billing_address.country' => 'required|string',
                'shipping_address' => 'nullable|array',
                'due_date' => 'required|date|after:today',
                'currency' => 'nullable|string|size:3',
                'type' => 'nullable|in:order,subscription,refund,adjustment',
                'order_id' => 'nullable|integer',
                'subscription_id' => 'nullable|integer',
                'notes' => 'nullable|string',
                'metadata' => 'nullable|array',
                'company_id' => 'required|integer',
                'unit_id' => 'nullable|integer',
                'items' => 'required|array|min:1',
                'items.*.item_type' => 'required|string',
                'items.*.item_name' => 'required|string',
                'items.*.description' => 'nullable|string',
                'items.*.sku' => 'nullable|string',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.unit_price' => 'required|numeric|min:0',
                'items.*.tax_rate' => 'nullable|numeric|min:0|max:100',
                'items.*.discount_rate' => 'nullable|numeric|min:0|max:100',
                'items.*.metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $invoiceData = $request->except('items');
            $items = $request->input('items');

            $invoice = $this->invoiceService->createInvoice($invoiceData, $items);

            return response()->json([
                'success' => true,
                'message' => 'Invoice created successfully',
                'data' => $invoice
            ], 201);
        } catch (Exception $e) {
            Log::error('Failed to create invoice', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create invoice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified invoice.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $invoice = $this->invoiceService->getInvoice((int) $id);

            return response()->json([
                'success' => true,
                'message' => 'Invoice retrieved successfully',
                'data' => $invoice
            ]);
        } catch (Exception $e) {
            Log::error('Failed to retrieve invoice', [
                'invoice_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Invoice not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified invoice status.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:draft,sent,paid,overdue,cancelled',
                'payment_method' => 'nullable|string',
                'payment_reference' => 'nullable|string',
                'notes' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $invoice = $this->invoiceService->updateInvoiceStatus(
                (int) $id,
                $request->input('status'),
                $request->only(['payment_method', 'payment_reference', 'notes'])
            );

            return response()->json([
                'success' => true,
                'message' => 'Invoice updated successfully',
                'data' => $invoice
            ]);
        } catch (Exception $e) {
            Log::error('Failed to update invoice', [
                'invoice_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update invoice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate and download invoice PDF.
     *
     * @param string $id
     * @param Request $request
     * @return \Illuminate\Http\Response|JsonResponse
     */
    public function downloadPdf(string $id, Request $request)
    {
        try {
            $invoice = $this->invoiceService->getInvoice((int) $id);
            $download = $request->boolean('download', true);

            return $this->pdfService->generateInvoicePdf($invoice, $download);
        } catch (Exception $e) {
            Log::error('Failed to generate invoice PDF', [
                'invoice_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate PDF',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark invoice as paid.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function markAsPaid(Request $request, string $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_method' => 'nullable|string',
                'payment_reference' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $invoice = $this->invoiceService->markInvoiceAsPaid(
                (int) $id,
                $request->input('payment_method'),
                $request->input('payment_reference')
            );

            return response()->json([
                'success' => true,
                'message' => 'Invoice marked as paid successfully',
                'data' => $invoice
            ]);
        } catch (Exception $e) {
            Log::error('Failed to mark invoice as paid', [
                'invoice_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to mark invoice as paid',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get invoice statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['company_id', 'date_from', 'date_to']);
            $statistics = $this->invoiceService->getInvoiceStatistics($filters);

            return response()->json([
                'success' => true,
                'message' => 'Invoice statistics retrieved successfully',
                'data' => $statistics
            ]);
        } catch (Exception $e) {
            Log::error('Failed to retrieve invoice statistics', [
                'error' => $e->getMessage(),
                'filters' => $request->only(['company_id', 'date_from', 'date_to'])
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $invoice = $this->invoiceService->getInvoice((int) $id);
            $invoice->delete();

            return response()->json([
                'success' => true,
                'message' => 'Invoice deleted successfully'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to delete invoice', [
                'invoice_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete invoice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate enhanced PDF with template support.
     *
     * @param string $id
     * @param Request $request
     * @return \Illuminate\Http\Response|JsonResponse
     */
    public function downloadEnhancedPdf(string $id, Request $request)
    {
        try {
            $invoice = $this->invoiceService->getInvoice((int) $id);
            $download = $request->boolean('download', true);
            $options = $request->only(['template', 'language', 'watermark', 'digital_signature']);

            return $this->pdfService->generateEnhancedPdf($invoice, $download, $options);
        } catch (Exception $e) {
            Log::error('Failed to generate enhanced invoice PDF', [
                'invoice_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate enhanced PDF',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate batch PDFs for multiple invoices.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateBatchPdf(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'invoice_ids' => 'required|array|min:1',
                'invoice_ids.*' => 'integer|exists:invoices,id',
                'template' => 'nullable|string',
                'language' => 'nullable|string',
                'include_timestamp' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $invoiceIds = $request->input('invoice_ids');
            $options = $request->only(['template', 'language', 'include_timestamp']);

            $results = $this->pdfService->generateBatchPdfs($invoiceIds, $options);

            return response()->json([
                'success' => true,
                'message' => 'Batch PDF generation completed',
                'data' => $results
            ]);
        } catch (Exception $e) {
            Log::error('Failed to generate batch PDFs', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate batch PDFs',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

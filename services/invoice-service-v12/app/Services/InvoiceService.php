<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * InvoiceService for OneFoodDialer 2025
 * 
 * Handles all invoice-related business logic including:
 * - Invoice creation and management
 * - PDF generation
 * - Invoice calculations
 * - Status updates
 */
class InvoiceService
{
    /**
     * Create a new invoice with items.
     *
     * @param array $invoiceData
     * @param array $items
     * @return Invoice
     * @throws Exception
     */
    public function createInvoice(array $invoiceData, array $items): Invoice
    {
        try {
            DB::beginTransaction();

            // Generate invoice number if not provided
            if (!isset($invoiceData['invoice_number'])) {
                $invoiceData['invoice_number'] = Invoice::generateInvoiceNumber();
            }

            // Set default values for required fields
            $invoiceData = array_merge([
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'total_amount' => 0,
                'currency' => 'INR',
                'status' => 'draft'
            ], $invoiceData);

            // Create the invoice
            $invoice = Invoice::create($invoiceData);

            // Add items to the invoice
            $this->addItemsToInvoice($invoice, $items);

            // Recalculate totals
            $this->recalculateInvoiceTotals($invoice);

            DB::commit();

            Log::info('Invoice created successfully', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'customer_id' => $invoice->customer_id,
                'total_amount' => $invoice->total_amount
            ]);

            return $invoice->load('items');
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to create invoice', [
                'error' => $e->getMessage(),
                'invoice_data' => $invoiceData
            ]);
            throw $e;
        }
    }

    /**
     * Get invoice by ID with items.
     *
     * @param int $invoiceId
     * @return Invoice
     * @throws Exception
     */
    public function getInvoice(int $invoiceId): Invoice
    {
        $invoice = Invoice::with('items')->find($invoiceId);

        if (!$invoice) {
            throw new Exception("Invoice not found with ID: {$invoiceId}");
        }

        return $invoice;
    }

    /**
     * Get paginated invoices with optional filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getInvoices(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Invoice::with('items');

        // Apply filters
        if (isset($filters['customer_id'])) {
            $query->forCustomer($filters['customer_id']);
        }

        if (isset($filters['status'])) {
            $query->status($filters['status']);
        }

        if (isset($filters['type'])) {
            $query->type($filters['type']);
        }

        if (isset($filters['company_id'])) {
            $query->forCompany($filters['company_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Update invoice status.
     *
     * @param int $invoiceId
     * @param string $status
     * @param array $additionalData
     * @return Invoice
     * @throws Exception
     */
    public function updateInvoiceStatus(int $invoiceId, string $status, array $additionalData = []): Invoice
    {
        $invoice = $this->getInvoice($invoiceId);

        $updateData = array_merge(['status' => $status], $additionalData);

        if ($status === 'paid' && !isset($additionalData['paid_at'])) {
            $updateData['paid_at'] = now();
        }

        $invoice->update($updateData);

        Log::info('Invoice status updated', [
            'invoice_id' => $invoice->id,
            'old_status' => $invoice->getOriginal('status'),
            'new_status' => $status
        ]);

        return $invoice->fresh();
    }

    /**
     * Add items to an invoice.
     *
     * @param Invoice $invoice
     * @param array $items
     * @return void
     */
    protected function addItemsToInvoice(Invoice $invoice, array $items): void
    {
        foreach ($items as $itemData) {
            $item = new InvoiceItem($itemData);
            $item->invoice_id = $invoice->id;
            $item->updateCalculatedAmounts();
            $item->save();
        }
    }

    /**
     * Recalculate invoice totals based on items.
     *
     * @param Invoice $invoice
     * @return void
     */
    protected function recalculateInvoiceTotals(Invoice $invoice): void
    {
        $items = $invoice->items;

        $subtotal = $items->sum('total_price');
        $taxAmount = $items->sum('tax_amount');
        $discountAmount = $items->sum('discount_amount');

        $invoice->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'discount_amount' => $discountAmount,
            'total_amount' => $subtotal
        ]);
    }

    /**
     * Mark invoice as paid.
     *
     * @param int $invoiceId
     * @param string|null $paymentMethod
     * @param string|null $paymentReference
     * @return Invoice
     * @throws Exception
     */
    public function markInvoiceAsPaid(int $invoiceId, ?string $paymentMethod = null, ?string $paymentReference = null): Invoice
    {
        $invoice = $this->getInvoice($invoiceId);
        $invoice->markAsPaid($paymentMethod, $paymentReference);

        Log::info('Invoice marked as paid', [
            'invoice_id' => $invoice->id,
            'payment_method' => $paymentMethod,
            'payment_reference' => $paymentReference
        ]);

        return $invoice;
    }

    /**
     * Get invoice statistics.
     *
     * @param array $filters
     * @return array
     */
    public function getInvoiceStatistics(array $filters = []): array
    {
        $query = Invoice::query();

        // Apply filters
        if (isset($filters['company_id'])) {
            $query->forCompany($filters['company_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return [
            'total_invoices' => $query->count(),
            'paid_invoices' => $query->clone()->status('paid')->count(),
            'pending_invoices' => $query->clone()->status('sent')->count(),
            'overdue_invoices' => $query->clone()->where('due_date', '<', now())->where('status', '!=', 'paid')->count(),
            'total_amount' => $query->clone()->sum('total_amount'),
            'paid_amount' => $query->clone()->status('paid')->sum('total_amount'),
            'pending_amount' => $query->clone()->status('sent')->sum('total_amount'),
        ];
    }
}

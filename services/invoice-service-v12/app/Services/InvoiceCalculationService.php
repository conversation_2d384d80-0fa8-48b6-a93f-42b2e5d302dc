<?php

namespace App\Services;

use App\Models\InvoiceConfiguration;
use App\Models\ExchangeRate;
use Illuminate\Support\Facades\Log;

/**
 * InvoiceCalculationService for OneFoodDialer 2025
 * 
 * Handles all mathematical calculations for invoices including
 * subtotals, taxes, discounts, currency conversions, and totals
 */
class InvoiceCalculationService
{
    /**
     * Calculate invoice totals from line items.
     *
     * @param array $items
     * @param int $companyId
     * @param array $options
     * @return array
     */
    public function calculateInvoiceTotals(array $items, int $companyId, array $options = []): array
    {
        $currency = $options['currency'] ?? 'USD';
        $customerType = $options['customer_type'] ?? 'regular';
        $region = $options['region'] ?? 'default';
        
        // Initialize totals
        $subtotal = 0;
        $totalTaxAmount = 0;
        $totalDiscountAmount = 0;
        $calculatedItems = [];

        // Get configuration
        $taxRates = InvoiceConfiguration::getTaxRates($companyId, $region);
        $discountRules = InvoiceConfiguration::getDiscountRules($companyId, $customerType);

        foreach ($items as $item) {
            $calculatedItem = $this->calculateLineItem($item, $taxRates, $discountRules);
            $calculatedItems[] = $calculatedItem;
            
            $subtotal += $calculatedItem['line_total'];
            $totalTaxAmount += $calculatedItem['tax_amount'];
            $totalDiscountAmount += $calculatedItem['discount_amount'];
        }

        // Apply invoice-level discounts
        $invoiceLevelDiscount = $this->calculateInvoiceLevelDiscount(
            $subtotal, 
            $discountRules, 
            $options
        );

        $adjustedSubtotal = $subtotal - $invoiceLevelDiscount;
        $finalTotal = $adjustedSubtotal + $totalTaxAmount;

        return [
            'items' => $calculatedItems,
            'subtotal' => round($subtotal, 2),
            'tax_amount' => round($totalTaxAmount, 2),
            'discount_amount' => round($totalDiscountAmount + $invoiceLevelDiscount, 2),
            'invoice_level_discount' => round($invoiceLevelDiscount, 2),
            'total_amount' => round($finalTotal, 2),
            'currency' => $currency,
            'calculation_details' => [
                'tax_rates_applied' => $taxRates,
                'discount_rules_applied' => $discountRules,
                'calculation_timestamp' => now()->toISOString(),
            ]
        ];
    }

    /**
     * Calculate individual line item totals.
     *
     * @param array $item
     * @param array $taxRates
     * @param array $discountRules
     * @return array
     */
    protected function calculateLineItem(array $item, array $taxRates, array $discountRules): array
    {
        $quantity = (int) ($item['quantity'] ?? 1);
        $unitPrice = (float) ($item['unit_price'] ?? 0);
        $itemTaxRate = (float) ($item['tax_rate'] ?? 0);
        $itemDiscountRate = (float) ($item['discount_rate'] ?? 0);

        // Calculate base amount
        $baseAmount = $quantity * $unitPrice;

        // Apply item-level discount
        $itemDiscountAmount = 0;
        if ($itemDiscountRate > 0) {
            $itemDiscountAmount = ($baseAmount * $itemDiscountRate) / 100;
        }

        // Apply category-level discounts
        $categoryDiscountAmount = $this->calculateCategoryDiscount(
            $item, 
            $baseAmount, 
            $discountRules
        );

        $totalDiscountAmount = $itemDiscountAmount + $categoryDiscountAmount;
        $discountedAmount = $baseAmount - $totalDiscountAmount;

        // Calculate tax
        $taxAmount = 0;
        $applicableTaxRate = $this->getApplicableTaxRate($item, $taxRates, $itemTaxRate);
        
        if ($applicableTaxRate > 0) {
            $taxAmount = ($discountedAmount * $applicableTaxRate) / 100;
        }

        $lineTotal = $discountedAmount + $taxAmount;

        return [
            'item_name' => $item['item_name'] ?? '',
            'description' => $item['description'] ?? '',
            'sku' => $item['sku'] ?? '',
            'quantity' => $quantity,
            'unit_price' => round($unitPrice, 2),
            'base_amount' => round($baseAmount, 2),
            'discount_rate' => $itemDiscountRate,
            'discount_amount' => round($totalDiscountAmount, 2),
            'discounted_amount' => round($discountedAmount, 2),
            'tax_rate' => $applicableTaxRate,
            'tax_amount' => round($taxAmount, 2),
            'line_total' => round($lineTotal, 2),
            'metadata' => $item['metadata'] ?? [],
        ];
    }

    /**
     * Get applicable tax rate for an item.
     *
     * @param array $item
     * @param array $taxRates
     * @param float $itemTaxRate
     * @return float
     */
    protected function getApplicableTaxRate(array $item, array $taxRates, float $itemTaxRate): float
    {
        // If item has specific tax rate, use it
        if ($itemTaxRate > 0) {
            return $itemTaxRate;
        }

        // Find applicable tax rate from configuration
        foreach ($taxRates as $taxConfig) {
            $appliesTo = $taxConfig['applies_to'] ?? 'all';
            $itemType = $item['item_type'] ?? 'product';

            if ($appliesTo === 'all' || $appliesTo === $itemType) {
                return (float) ($taxConfig['rate'] ?? 0);
            }
        }

        return 0;
    }

    /**
     * Calculate category-level discount for an item.
     *
     * @param array $item
     * @param float $baseAmount
     * @param array $discountRules
     * @return float
     */
    protected function calculateCategoryDiscount(array $item, float $baseAmount, array $discountRules): float
    {
        $totalDiscount = 0;
        $itemType = $item['item_type'] ?? 'product';

        foreach ($discountRules as $rule) {
            $ruleType = $rule['type'] ?? 'percentage';
            $appliesTo = $rule['applies_to'] ?? 'all';
            $minAmount = (float) ($rule['min_amount'] ?? 0);

            // Check if rule applies to this item
            if ($appliesTo !== 'all' && $appliesTo !== $itemType) {
                continue;
            }

            // Check minimum amount requirement
            if ($minAmount > 0 && $baseAmount < $minAmount) {
                continue;
            }

            // Calculate discount based on rule type
            if ($ruleType === 'percentage') {
                $discountRate = (float) ($rule['value'] ?? 0);
                $totalDiscount += ($baseAmount * $discountRate) / 100;
            } elseif ($ruleType === 'fixed') {
                $discountAmount = (float) ($rule['value'] ?? 0);
                $totalDiscount += min($discountAmount, $baseAmount);
            }
        }

        return $totalDiscount;
    }

    /**
     * Calculate invoice-level discount.
     *
     * @param float $subtotal
     * @param array $discountRules
     * @param array $options
     * @return float
     */
    protected function calculateInvoiceLevelDiscount(float $subtotal, array $discountRules, array $options): float
    {
        $totalDiscount = 0;
        $customerType = $options['customer_type'] ?? 'regular';
        $orderQuantity = (int) ($options['total_quantity'] ?? 0);

        foreach ($discountRules as $rule) {
            $ruleScope = $rule['scope'] ?? 'item';
            
            // Only apply invoice-level rules
            if ($ruleScope !== 'invoice') {
                continue;
            }

            $ruleType = $rule['type'] ?? 'percentage';
            $minAmount = (float) ($rule['min_amount'] ?? 0);
            $minQuantity = (int) ($rule['min_quantity'] ?? 0);
            $applicableCustomerTypes = $rule['customer_types'] ?? ['all'];

            // Check customer type eligibility
            if (!in_array('all', $applicableCustomerTypes) && !in_array($customerType, $applicableCustomerTypes)) {
                continue;
            }

            // Check minimum amount requirement
            if ($minAmount > 0 && $subtotal < $minAmount) {
                continue;
            }

            // Check minimum quantity requirement
            if ($minQuantity > 0 && $orderQuantity < $minQuantity) {
                continue;
            }

            // Calculate discount
            if ($ruleType === 'percentage') {
                $discountRate = (float) ($rule['value'] ?? 0);
                $maxDiscount = (float) ($rule['max_discount'] ?? $subtotal);
                $calculatedDiscount = ($subtotal * $discountRate) / 100;
                $totalDiscount += min($calculatedDiscount, $maxDiscount);
            } elseif ($ruleType === 'fixed') {
                $discountAmount = (float) ($rule['value'] ?? 0);
                $totalDiscount += min($discountAmount, $subtotal);
            }
        }

        return $totalDiscount;
    }

    /**
     * Convert invoice amounts to different currency.
     *
     * @param array $calculationResult
     * @param string $targetCurrency
     * @return array
     */
    public function convertCurrency(array $calculationResult, string $targetCurrency): array
    {
        $sourceCurrency = $calculationResult['currency'];
        
        if ($sourceCurrency === $targetCurrency) {
            return $calculationResult;
        }

        try {
            // Convert main amounts
            $calculationResult['subtotal'] = ExchangeRate::convertAmount(
                $calculationResult['subtotal'], 
                $sourceCurrency, 
                $targetCurrency
            );
            
            $calculationResult['tax_amount'] = ExchangeRate::convertAmount(
                $calculationResult['tax_amount'], 
                $sourceCurrency, 
                $targetCurrency
            );
            
            $calculationResult['discount_amount'] = ExchangeRate::convertAmount(
                $calculationResult['discount_amount'], 
                $sourceCurrency, 
                $targetCurrency
            );
            
            $calculationResult['total_amount'] = ExchangeRate::convertAmount(
                $calculationResult['total_amount'], 
                $sourceCurrency, 
                $targetCurrency
            );

            // Convert line items
            foreach ($calculationResult['items'] as &$item) {
                $item['unit_price'] = ExchangeRate::convertAmount(
                    $item['unit_price'], 
                    $sourceCurrency, 
                    $targetCurrency
                );
                $item['base_amount'] = ExchangeRate::convertAmount(
                    $item['base_amount'], 
                    $sourceCurrency, 
                    $targetCurrency
                );
                $item['discount_amount'] = ExchangeRate::convertAmount(
                    $item['discount_amount'], 
                    $sourceCurrency, 
                    $targetCurrency
                );
                $item['tax_amount'] = ExchangeRate::convertAmount(
                    $item['tax_amount'], 
                    $sourceCurrency, 
                    $targetCurrency
                );
                $item['line_total'] = ExchangeRate::convertAmount(
                    $item['line_total'], 
                    $sourceCurrency, 
                    $targetCurrency
                );
            }

            $calculationResult['currency'] = $targetCurrency;
            $calculationResult['original_currency'] = $sourceCurrency;
            $calculationResult['conversion_rate'] = ExchangeRate::getLatestRate($sourceCurrency, $targetCurrency);
            $calculationResult['conversion_date'] = now()->toISOString();

        } catch (\Exception $e) {
            Log::error('Currency conversion failed', [
                'source_currency' => $sourceCurrency,
                'target_currency' => $targetCurrency,
                'error' => $e->getMessage(),
            ]);
            
            throw new \Exception("Currency conversion failed: {$e->getMessage()}");
        }

        return $calculationResult;
    }

    /**
     * Calculate payment terms and due dates.
     *
     * @param int $companyId
     * @param array $options
     * @return array
     */
    public function calculatePaymentTerms(int $companyId, array $options = []): array
    {
        $paymentTerms = InvoiceConfiguration::getPaymentTerms($companyId);
        $issueDate = $options['issue_date'] ?? now();
        
        if (is_string($issueDate)) {
            $issueDate = \Carbon\Carbon::parse($issueDate);
        }

        $dueDays = $paymentTerms['due_days'] ?? 30;
        $earlyPaymentDays = $paymentTerms['early_payment_days'] ?? 10;
        $earlyPaymentDiscount = $paymentTerms['early_payment_discount'] ?? 0;

        return [
            'issue_date' => $issueDate->toDateString(),
            'due_date' => $issueDate->addDays($dueDays)->toDateString(),
            'early_payment_date' => $issueDate->addDays($earlyPaymentDays)->toDateString(),
            'early_payment_discount_rate' => $earlyPaymentDiscount,
            'payment_terms' => $paymentTerms,
        ];
    }
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * MetricsService for OneFoodDialer 2025
 * 
 * Handles application metrics collection and reporting
 */
class MetricsService
{
    /**
     * Record PDF generation metrics.
     *
     * @param float $duration
     * @param string $status
     * @return void
     */
    public function recordPdfGeneration(float $duration, string $status): void
    {
        $this->recordMetric('pdf_generation', [
            'duration' => $duration,
            'status' => $status,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Record API response time metrics.
     *
     * @param string $endpoint
     * @param string $method
     * @param float $duration
     * @param int $statusCode
     * @return void
     */
    public function recordApiResponseTime(string $endpoint, string $method, float $duration, int $statusCode): void
    {
        $this->recordMetric('api_response_time', [
            'endpoint' => $endpoint,
            'method' => $method,
            'duration' => $duration,
            'status_code' => $statusCode,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Record error metrics.
     *
     * @param string $service
     * @param string $operation
     * @param string $errorType
     * @return void
     */
    public function recordError(string $service, string $operation, string $errorType): void
    {
        $this->recordMetric('error', [
            'service' => $service,
            'operation' => $operation,
            'error_type' => $errorType,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Record invoice operation metrics.
     *
     * @param string $operation
     * @param array $data
     * @return void
     */
    public function recordInvoiceOperation(string $operation, array $data = []): void
    {
        $this->recordMetric('invoice_operation', array_merge([
            'operation' => $operation,
            'timestamp' => now()->toISOString(),
        ], $data));
    }

    /**
     * Record calculation performance metrics.
     *
     * @param float $duration
     * @param int $itemCount
     * @param string $currency
     * @return void
     */
    public function recordCalculationPerformance(float $duration, int $itemCount, string $currency): void
    {
        $this->recordMetric('calculation_performance', [
            'duration' => $duration,
            'item_count' => $itemCount,
            'currency' => $currency,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get metrics summary.
     *
     * @param string $type
     * @param int $hours
     * @return array
     */
    public function getMetricsSummary(string $type, int $hours = 24): array
    {
        $cacheKey = "metrics_summary_{$type}_{$hours}h";
        
        return Cache::remember($cacheKey, 300, function () use ($type, $hours) {
            // In a real implementation, this would query a metrics database
            // For now, return mock data
            return [
                'type' => $type,
                'period_hours' => $hours,
                'total_count' => 0,
                'average_duration' => 0,
                'success_rate' => 100,
                'error_count' => 0,
                'last_updated' => now()->toISOString(),
            ];
        });
    }

    /**
     * Get performance statistics.
     *
     * @return array
     */
    public function getPerformanceStats(): array
    {
        return [
            'pdf_generation' => $this->getMetricsSummary('pdf_generation'),
            'api_response_time' => $this->getMetricsSummary('api_response_time'),
            'calculation_performance' => $this->getMetricsSummary('calculation_performance'),
            'error_rate' => $this->getMetricsSummary('error'),
        ];
    }

    /**
     * Record a generic metric.
     *
     * @param string $type
     * @param array $data
     * @return void
     */
    protected function recordMetric(string $type, array $data): void
    {
        // In a production environment, this would send metrics to:
        // - Prometheus
        // - CloudWatch
        // - DataDog
        // - Custom metrics database
        
        // For now, just log the metrics
        Log::info("Metric recorded: {$type}", $data);
        
        // Store in cache for basic aggregation
        $cacheKey = "metrics_{$type}_" . now()->format('Y-m-d-H');
        $existingMetrics = Cache::get($cacheKey, []);
        $existingMetrics[] = $data;
        Cache::put($cacheKey, $existingMetrics, 3600); // Store for 1 hour
    }

    /**
     * Flush metrics to external systems.
     *
     * @return bool
     */
    public function flushMetrics(): bool
    {
        try {
            // In production, this would:
            // 1. Collect all cached metrics
            // 2. Send them to external monitoring systems
            // 3. Clear the cache
            
            Log::info('Metrics flushed successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to flush metrics', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if metrics collection is enabled.
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return config('app.metrics_enabled', true);
    }

    /**
     * Record database query metrics.
     *
     * @param string $query
     * @param float $duration
     * @param int $rowCount
     * @return void
     */
    public function recordDatabaseQuery(string $query, float $duration, int $rowCount = 0): void
    {
        if (!$this->isEnabled()) {
            return;
        }

        $this->recordMetric('database_query', [
            'query_type' => $this->extractQueryType($query),
            'duration' => $duration,
            'row_count' => $rowCount,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Extract query type from SQL query.
     *
     * @param string $query
     * @return string
     */
    protected function extractQueryType(string $query): string
    {
        $query = trim(strtoupper($query));
        
        if (str_starts_with($query, 'SELECT')) {
            return 'SELECT';
        } elseif (str_starts_with($query, 'INSERT')) {
            return 'INSERT';
        } elseif (str_starts_with($query, 'UPDATE')) {
            return 'UPDATE';
        } elseif (str_starts_with($query, 'DELETE')) {
            return 'DELETE';
        } else {
            return 'OTHER';
        }
    }

    /**
     * Record cache operation metrics.
     *
     * @param string $operation
     * @param string $key
     * @param bool $hit
     * @param float $duration
     * @return void
     */
    public function recordCacheOperation(string $operation, string $key, bool $hit = false, float $duration = 0): void
    {
        if (!$this->isEnabled()) {
            return;
        }

        $this->recordMetric('cache_operation', [
            'operation' => $operation,
            'key' => $key,
            'hit' => $hit,
            'duration' => $duration,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Record external API call metrics.
     *
     * @param string $service
     * @param string $endpoint
     * @param float $duration
     * @param int $statusCode
     * @return void
     */
    public function recordExternalApiCall(string $service, string $endpoint, float $duration, int $statusCode): void
    {
        if (!$this->isEnabled()) {
            return;
        }

        $this->recordMetric('external_api_call', [
            'service' => $service,
            'endpoint' => $endpoint,
            'duration' => $duration,
            'status_code' => $statusCode,
            'timestamp' => now()->toISOString(),
        ]);
    }
}

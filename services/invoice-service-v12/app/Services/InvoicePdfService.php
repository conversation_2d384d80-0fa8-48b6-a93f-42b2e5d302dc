<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceConfiguration;
use App\Services\MetricsService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Exception;

/**
 * Enhanced InvoicePdfService for OneFoodDialer 2025
 *
 * Handles professional PDF generation with templates, branding, and multi-language support
 */
class InvoicePdfService
{
    protected MetricsService $metricsService;

    public function __construct(MetricsService $metricsService)
    {
        $this->metricsService = $metricsService;
    }
    /**
     * Generate PDF for an invoice.
     *
     * @param Invoice $invoice
     * @param bool $download
     * @return \Illuminate\Http\Response|string
     * @throws Exception
     */
    public function generateInvoicePdf(Invoice $invoice, bool $download = false)
    {
        try {
            $data = $this->prepareInvoiceData($invoice);
            
            $pdf = Pdf::loadView('invoices.pdf', $data)
                ->setPaper('a4', 'portrait')
                ->setOptions([
                    'defaultFont' => 'sans-serif',
                    'isRemoteEnabled' => true,
                    'isHtml5ParserEnabled' => true,
                    'isPhpEnabled' => true
                ]);

            $filename = "invoice-{$invoice->invoice_number}.pdf";

            if ($download) {
                return $pdf->download($filename);
            }

            return $pdf->stream($filename);
        } catch (Exception $e) {
            Log::error('Failed to generate invoice PDF', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Save PDF to storage.
     *
     * @param Invoice $invoice
     * @param string $disk
     * @return string
     * @throws Exception
     */
    public function saveInvoicePdf(Invoice $invoice, string $disk = 'local'): string
    {
        try {
            $data = $this->prepareInvoiceData($invoice);
            
            $pdf = Pdf::loadView('invoices.pdf', $data)
                ->setPaper('a4', 'portrait');

            $filename = "invoices/invoice-{$invoice->invoice_number}.pdf";
            $pdfContent = $pdf->output();

            Storage::disk($disk)->put($filename, $pdfContent);

            Log::info('Invoice PDF saved to storage', [
                'invoice_id' => $invoice->id,
                'filename' => $filename,
                'disk' => $disk
            ]);

            return $filename;
        } catch (Exception $e) {
            Log::error('Failed to save invoice PDF', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Prepare data for invoice PDF generation.
     *
     * @param Invoice $invoice
     * @return array
     */
    protected function prepareInvoiceData(Invoice $invoice): array
    {
        return [
            'invoice' => $invoice,
            'items' => $invoice->items,
            'company' => $this->getCompanyDetails($invoice->company_id),
            'settings' => $this->getInvoiceSettings(),
            'generated_at' => now()->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Get company details for invoice.
     *
     * @param int $companyId
     * @return array
     */
    protected function getCompanyDetails(int $companyId): array
    {
        // This would typically fetch from a company service or database
        // For now, return default company details
        return [
            'name' => config('app.name', 'OneFoodDialer'),
            'address' => [
                'line1' => '123 Business Street',
                'line2' => 'Suite 100',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'postal_code' => '400001',
                'country' => 'India'
            ],
            'contact' => [
                'phone' => '+91 98765 43210',
                'email' => '<EMAIL>',
                'website' => 'https://onefooddialer.com'
            ],
            'tax_details' => [
                'gstin' => 'GST123456789',
                'pan' => '**********'
            ],
            'logo_url' => null // URL to company logo
        ];
    }

    /**
     * Get invoice settings.
     *
     * @return array
     */
    protected function getInvoiceSettings(): array
    {
        return [
            'currency_symbol' => '₹',
            'date_format' => 'd/m/Y',
            'show_tax_breakdown' => true,
            'show_payment_terms' => true,
            'payment_terms' => 'Payment due within 30 days of invoice date.',
            'footer_text' => 'Thank you for your business!',
            'colors' => [
                'primary' => '#2563eb',
                'secondary' => '#64748b',
                'accent' => '#f59e0b'
            ]
        ];
    }

    /**
     * Get PDF content as base64 string.
     *
     * @param Invoice $invoice
     * @return string
     * @throws Exception
     */
    public function getInvoicePdfBase64(Invoice $invoice): string
    {
        try {
            $data = $this->prepareInvoiceData($invoice);
            
            $pdf = Pdf::loadView('invoices.pdf', $data)
                ->setPaper('a4', 'portrait');

            return base64_encode($pdf->output());
        } catch (Exception $e) {
            Log::error('Failed to generate invoice PDF base64', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Email invoice PDF.
     *
     * @param Invoice $invoice
     * @param string $email
     * @param array $options
     * @return bool
     * @throws Exception
     */
    public function emailInvoicePdf(Invoice $invoice, string $email, array $options = []): bool
    {
        try {
            $data = $this->prepareInvoiceData($invoice);
            
            $pdf = Pdf::loadView('invoices.pdf', $data)
                ->setPaper('a4', 'portrait');

            $filename = "invoice-{$invoice->invoice_number}.pdf";
            
            // This would integrate with your email service
            // For now, just log the action
            Log::info('Invoice PDF email requested', [
                'invoice_id' => $invoice->id,
                'email' => $email,
                'filename' => $filename
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to email invoice PDF', [
                'invoice_id' => $invoice->id,
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Generate enhanced PDF with template support.
     *
     * @param Invoice $invoice
     * @param bool $download
     * @param array $options
     * @return \Illuminate\Http\Response|string
     */
    public function generateEnhancedPdf(Invoice $invoice, bool $download = false, array $options = [])
    {
        $startTime = microtime(true);

        try {
            // Load invoice with items
            $invoice->load('items');

            // Get template configuration
            $templateConfig = $this->getTemplateConfiguration($invoice->company_id, $options['template'] ?? null);

            // Get enhanced company details
            $company = $this->getEnhancedCompanyDetails($invoice->company_id);

            // Prepare enhanced data for PDF template
            $data = [
                'invoice' => $invoice,
                'company' => $company,
                'items' => $invoice->items,
                'totals' => $this->calculateEnhancedTotals($invoice),
                'template' => $templateConfig,
                'language' => $options['language'] ?? 'en',
                'watermark' => $options['watermark'] ?? null,
                'digital_signature' => $options['digital_signature'] ?? null,
                'payment_terms' => $this->getPaymentTermsText($invoice->company_id),
                'tax_breakdown' => $this->getTaxBreakdown($invoice),
            ];

            // Select template view based on configuration
            $templateView = $this->getTemplateView($templateConfig['template_name'] ?? 'modern');

            // Generate PDF with enhanced options
            $pdf = Pdf::loadView($templateView, $data);
            $pdf->setPaper('A4', 'portrait');

            // Apply PDF options
            $pdf->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => $templateConfig['font_family'] ?? 'DejaVu Sans',
                'dpi' => 150,
                'defaultPaperSize' => 'A4',
            ]);

            $filename = $this->generateFilename($invoice, $options);

            // Record metrics
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordPdfGeneration($duration, 'success');

            if ($download) {
                return $pdf->download($filename);
            } else {
                return $pdf->stream($filename);
            }
        } catch (\Exception $e) {
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordPdfGeneration($duration, 'failed');

            Log::error('Enhanced PDF generation failed', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
                'options' => $options
            ]);

            throw $e;
        }
    }

    /**
     * Generate multiple PDFs in batch.
     *
     * @param array $invoiceIds
     * @param array $options
     * @return array
     */
    public function generateBatchPdfs(array $invoiceIds, array $options = []): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($invoiceIds as $invoiceId) {
            try {
                $invoice = Invoice::with('items')->findOrFail($invoiceId);
                $filename = $this->generateFilename($invoice, $options);

                // Generate PDF and store it
                $pdf = $this->generateEnhancedPdf($invoice, false, $options);
                $pdfContent = $pdf->getContent();

                // Store PDF file
                $storagePath = "invoices/batch/" . date('Y/m/d') . "/{$filename}";
                Storage::disk('local')->put($storagePath, $pdfContent);

                $results[] = [
                    'invoice_id' => $invoiceId,
                    'status' => 'success',
                    'filename' => $filename,
                    'storage_path' => $storagePath,
                    'file_size' => strlen($pdfContent),
                ];

                $successCount++;
            } catch (\Exception $e) {
                $results[] = [
                    'invoice_id' => $invoiceId,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                ];

                $failureCount++;

                Log::error('Batch PDF generation failed for invoice', [
                    'invoice_id' => $invoiceId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Batch PDF generation completed', [
            'total_invoices' => count($invoiceIds),
            'successful' => $successCount,
            'failed' => $failureCount,
        ]);

        return [
            'summary' => [
                'total' => count($invoiceIds),
                'successful' => $successCount,
                'failed' => $failureCount,
            ],
            'results' => $results,
        ];
    }

    /**
     * Get template configuration for a company.
     *
     * @param int $companyId
     * @param string|null $templateName
     * @return array
     */
    protected function getTemplateConfiguration(int $companyId, ?string $templateName = null): array
    {
        $templates = InvoiceConfiguration::getInvoiceTemplates($companyId);

        if ($templateName && isset($templates[$templateName])) {
            return $templates[$templateName];
        }

        // Return default template configuration
        return $templates['Default Template'] ?? [
            'template_name' => 'modern',
            'logo_position' => 'top-left',
            'color_scheme' => '#2563eb',
            'font_family' => 'DejaVu Sans',
            'show_payment_terms' => true,
            'show_tax_breakdown' => true,
            'show_company_details' => true,
            'show_watermark' => false,
        ];
    }

    /**
     * Get enhanced company details for PDF.
     *
     * @param int $companyId
     * @return array
     */
    protected function getEnhancedCompanyDetails(int $companyId): array
    {
        try {
            // Try to fetch from company service
            $response = Http::timeout(5)->get(config('services.company.url') . "/api/v2/companies/{$companyId}");

            if ($response->successful()) {
                $companyData = $response->json()['data'] ?? [];
                return $this->formatCompanyData($companyData);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to fetch company details from service', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to existing method
        return $this->getCompanyDetails($companyId);
    }

    /**
     * Format company data for PDF.
     *
     * @param array $companyData
     * @return array
     */
    protected function formatCompanyData(array $companyData): array
    {
        return [
            'name' => $companyData['name'] ?? 'OneFoodDialer 2025',
            'address' => $companyData['address'] ?? [
                'line1' => '123 Business Street',
                'city' => 'Business City',
                'state' => 'BC',
                'postal_code' => '12345',
                'country' => 'Country'
            ],
            'contact' => [
                'phone' => $companyData['phone'] ?? '******-567-8900',
                'email' => $companyData['email'] ?? '<EMAIL>',
                'website' => $companyData['website'] ?? 'https://onefooddialer.com',
            ],
            'tax_details' => [
                'gstin' => $companyData['tax_id'] ?? 'GST123456789',
                'pan' => $companyData['registration_number'] ?? '**********',
            ],
            'logo_url' => $companyData['logo_url'] ?? null,
        ];
    }

    /**
     * Calculate enhanced totals for PDF display.
     *
     * @param Invoice $invoice
     * @return array
     */
    protected function calculateEnhancedTotals(Invoice $invoice): array
    {
        return [
            'subtotal' => $invoice->subtotal,
            'tax_amount' => $invoice->tax_amount,
            'discount_amount' => $invoice->discount_amount,
            'total_amount' => $invoice->total_amount,
            'currency' => $invoice->currency,
            'currency_symbol' => $this->getCurrencySymbol($invoice->currency),
            'amount_in_words' => $this->convertAmountToWords($invoice->total_amount),
        ];
    }

    /**
     * Get payment terms text for a company.
     *
     * @param int $companyId
     * @return string
     */
    protected function getPaymentTermsText(int $companyId): string
    {
        $paymentTerms = InvoiceConfiguration::getPaymentTerms($companyId);
        $dueDays = $paymentTerms['due_days'] ?? 30;

        return "Payment due within {$dueDays} days of invoice date.";
    }

    /**
     * Get tax breakdown for invoice.
     *
     * @param Invoice $invoice
     * @return array
     */
    protected function getTaxBreakdown(Invoice $invoice): array
    {
        $breakdown = [];

        foreach ($invoice->items as $item) {
            if ($item->tax_rate > 0) {
                $taxKey = "Tax @ {$item->tax_rate}%";
                if (!isset($breakdown[$taxKey])) {
                    $breakdown[$taxKey] = 0;
                }
                $breakdown[$taxKey] += $item->tax_amount;
            }
        }

        return $breakdown;
    }

    /**
     * Get template view name based on template configuration.
     *
     * @param string $templateName
     * @return string
     */
    protected function getTemplateView(string $templateName): string
    {
        $availableTemplates = [
            'modern' => 'invoices.templates.modern',
            'classic' => 'invoices.templates.classic',
            'minimal' => 'invoices.templates.minimal',
            'professional' => 'invoices.templates.professional',
        ];

        return $availableTemplates[$templateName] ?? 'invoices.pdf';
    }

    /**
     * Generate filename for PDF.
     *
     * @param Invoice $invoice
     * @param array $options
     * @return string
     */
    protected function generateFilename(Invoice $invoice, array $options = []): string
    {
        $prefix = $options['filename_prefix'] ?? 'invoice';
        $suffix = $options['filename_suffix'] ?? '';
        $timestamp = $options['include_timestamp'] ?? false;

        $filename = "{$prefix}-{$invoice->invoice_number}";

        if ($suffix) {
            $filename .= "-{$suffix}";
        }

        if ($timestamp) {
            $filename .= "-" . now()->format('YmdHis');
        }

        return "{$filename}.pdf";
    }

    /**
     * Get currency symbol.
     *
     * @param string $currency
     * @return string
     */
    protected function getCurrencySymbol(string $currency): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'JPY' => '¥',
            'CHF' => 'CHF',
            'CNY' => '¥',
            'SGD' => 'S$',
        ];

        return $symbols[$currency] ?? $currency;
    }

    /**
     * Convert amount to words (basic implementation).
     *
     * @param float $amount
     * @return string
     */
    protected function convertAmountToWords(float $amount): string
    {
        // This is a simplified implementation
        // In production, you might want to use a more robust library
        $integerPart = (int) $amount;
        $decimalPart = round(($amount - $integerPart) * 100);

        if ($integerPart === 0) {
            return "Zero";
        }

        // Basic conversion for demonstration
        $words = [
            0 => 'Zero', 1 => 'One', 2 => 'Two', 3 => 'Three', 4 => 'Four',
            5 => 'Five', 6 => 'Six', 7 => 'Seven', 8 => 'Eight', 9 => 'Nine',
            10 => 'Ten', 11 => 'Eleven', 12 => 'Twelve', 13 => 'Thirteen',
            14 => 'Fourteen', 15 => 'Fifteen', 16 => 'Sixteen', 17 => 'Seventeen',
            18 => 'Eighteen', 19 => 'Nineteen', 20 => 'Twenty', 30 => 'Thirty',
            40 => 'Forty', 50 => 'Fifty', 60 => 'Sixty', 70 => 'Seventy',
            80 => 'Eighty', 90 => 'Ninety'
        ];

        if ($integerPart < 21) {
            $result = $words[$integerPart];
        } elseif ($integerPart < 100) {
            $tens = (int) ($integerPart / 10) * 10;
            $units = $integerPart % 10;
            $result = $words[$tens] . ($units > 0 ? ' ' . $words[$units] : '');
        } else {
            $result = "Amount: " . number_format($amount, 2);
        }

        return $result . " Only";
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * ExchangeRate Model for OneFoodDialer 2025
 *
 * @property int $id
 * @property string $from_currency
 * @property string $to_currency
 * @property float $rate
 * @property string $provider
 * @property \Carbon\Carbon $rate_date
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class ExchangeRate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'from_currency',
        'to_currency',
        'rate',
        'provider',
        'rate_date',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'rate' => 'decimal:6',
            'rate_date' => 'date',
        ];
    }

    /**
     * Get the latest exchange rate between two currencies.
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    public static function getLatestRate(string $fromCurrency, string $toCurrency): ?float
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        $rate = static::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->orderBy('rate_date', 'desc')
            ->first();

        return $rate ? $rate->rate : null;
    }

    /**
     * Convert amount from one currency to another.
     *
     * @param float $amount
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float
     */
    public static function convertAmount(float $amount, string $fromCurrency, string $toCurrency): float
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $rate = static::getLatestRate($fromCurrency, $toCurrency);
        
        if (!$rate) {
            // Try to fetch and store the rate
            $rate = static::fetchAndStoreRate($fromCurrency, $toCurrency);
        }

        if (!$rate) {
            throw new \Exception("Exchange rate not available for {$fromCurrency} to {$toCurrency}");
        }

        return round($amount * $rate, 2);
    }

    /**
     * Fetch exchange rate from external provider and store it.
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    public static function fetchAndStoreRate(string $fromCurrency, string $toCurrency): ?float
    {
        try {
            $provider = config('services.exchange_rate.provider', 'fixer.io');
            $apiKey = config('services.exchange_rate.api_key');

            if (!$apiKey) {
                Log::warning('Exchange rate API key not configured');
                return null;
            }

            $rate = null;

            switch ($provider) {
                case 'fixer.io':
                    $rate = static::fetchFromFixerIo($fromCurrency, $toCurrency, $apiKey);
                    break;
                case 'exchangerate-api.com':
                    $rate = static::fetchFromExchangeRateApi($fromCurrency, $toCurrency, $apiKey);
                    break;
                default:
                    Log::warning("Unsupported exchange rate provider: {$provider}");
                    return null;
            }

            if ($rate) {
                static::create([
                    'from_currency' => $fromCurrency,
                    'to_currency' => $toCurrency,
                    'rate' => $rate,
                    'provider' => $provider,
                    'rate_date' => Carbon::today(),
                ]);

                // Also store the inverse rate
                static::create([
                    'from_currency' => $toCurrency,
                    'to_currency' => $fromCurrency,
                    'rate' => 1 / $rate,
                    'provider' => $provider,
                    'rate_date' => Carbon::today(),
                ]);
            }

            return $rate;
        } catch (\Exception $e) {
            Log::error('Failed to fetch exchange rate', [
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Fetch rate from Fixer.io API.
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @param string $apiKey
     * @return float|null
     */
    protected static function fetchFromFixerIo(string $fromCurrency, string $toCurrency, string $apiKey): ?float
    {
        $response = Http::timeout(10)->get('http://data.fixer.io/api/latest', [
            'access_key' => $apiKey,
            'base' => $fromCurrency,
            'symbols' => $toCurrency,
        ]);

        if ($response->successful()) {
            $data = $response->json();
            if ($data['success'] && isset($data['rates'][$toCurrency])) {
                return (float) $data['rates'][$toCurrency];
            }
        }

        return null;
    }

    /**
     * Fetch rate from ExchangeRate-API.com.
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @param string $apiKey
     * @return float|null
     */
    protected static function fetchFromExchangeRateApi(string $fromCurrency, string $toCurrency, string $apiKey): ?float
    {
        $response = Http::timeout(10)->get("https://v6.exchangerate-api.com/v6/{$apiKey}/pair/{$fromCurrency}/{$toCurrency}");

        if ($response->successful()) {
            $data = $response->json();
            if ($data['result'] === 'success') {
                return (float) $data['conversion_rate'];
            }
        }

        return null;
    }

    /**
     * Update all exchange rates for supported currencies.
     *
     * @param array $currencies
     * @return int Number of rates updated
     */
    public static function updateAllRates(array $currencies = ['USD', 'EUR', 'GBP', 'INR']): int
    {
        $updated = 0;
        $baseCurrency = 'USD';

        foreach ($currencies as $currency) {
            if ($currency === $baseCurrency) {
                continue;
            }

            // Update rate from base currency to target currency
            $rate = static::fetchAndStoreRate($baseCurrency, $currency);
            if ($rate) {
                $updated++;
            }

            // Small delay to avoid rate limiting
            usleep(100000); // 100ms
        }

        return $updated;
    }

    /**
     * Get historical rates for a currency pair.
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getHistoricalRates(string $fromCurrency, string $toCurrency, int $days = 30)
    {
        return static::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->where('rate_date', '>=', Carbon::now()->subDays($days))
            ->orderBy('rate_date', 'desc')
            ->get();
    }

    /**
     * Check if rates need updating (older than 1 day).
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return bool
     */
    public static function needsUpdate(string $fromCurrency, string $toCurrency): bool
    {
        $latestRate = static::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->orderBy('rate_date', 'desc')
            ->first();

        if (!$latestRate) {
            return true;
        }

        return $latestRate->rate_date->lt(Carbon::today());
    }

    /**
     * Get supported currency pairs.
     *
     * @return array
     */
    public static function getSupportedCurrencies(): array
    {
        return [
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'GBP' => 'British Pound',
            'INR' => 'Indian Rupee',
            'CAD' => 'Canadian Dollar',
            'AUD' => 'Australian Dollar',
            'JPY' => 'Japanese Yen',
            'CHF' => 'Swiss Franc',
            'CNY' => 'Chinese Yuan',
            'SGD' => 'Singapore Dollar',
        ];
    }
}

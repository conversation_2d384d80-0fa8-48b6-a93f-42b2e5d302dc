<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * InvoiceConfiguration Model for OneFoodDialer 2025
 *
 * @property int $id
 * @property int $company_id
 * @property string $name
 * @property string $type
 * @property array $configuration
 * @property bool $is_active
 * @property int|null $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class InvoiceConfiguration extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'name',
        'type',
        'configuration',
        'is_active',
        'created_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'configuration' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Configuration types
     */
    public const TYPE_TAX_RATE = 'tax_rate';
    public const TYPE_DISCOUNT_RULE = 'discount_rule';
    public const TYPE_PAYMENT_TERMS = 'payment_terms';
    public const TYPE_TEMPLATE = 'template';
    public const TYPE_CURRENCY = 'currency';
    public const TYPE_AUTOMATION = 'automation';

    /**
     * Get all available configuration types.
     *
     * @return array
     */
    public static function getConfigurationTypes(): array
    {
        return [
            self::TYPE_TAX_RATE,
            self::TYPE_DISCOUNT_RULE,
            self::TYPE_PAYMENT_TERMS,
            self::TYPE_TEMPLATE,
            self::TYPE_CURRENCY,
            self::TYPE_AUTOMATION,
        ];
    }

    /**
     * Scope a query to only include active configurations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include configurations of a given type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include configurations for a specific company.
     */
    public function scopeForCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Get tax rate configuration for a company.
     *
     * @param int $companyId
     * @param string|null $region
     * @return array
     */
    public static function getTaxRates(int $companyId, string $region = null): array
    {
        $query = static::active()
            ->forCompany($companyId)
            ->ofType(self::TYPE_TAX_RATE);

        if ($region) {
            $query->where('configuration->region', $region);
        }

        return $query->get()->pluck('configuration')->toArray();
    }

    /**
     * Get discount rules for a company.
     *
     * @param int $companyId
     * @param string|null $customerType
     * @return array
     */
    public static function getDiscountRules(int $companyId, string $customerType = null): array
    {
        $query = static::active()
            ->forCompany($companyId)
            ->ofType(self::TYPE_DISCOUNT_RULE);

        if ($customerType) {
            $query->where('configuration->customer_type', $customerType);
        }

        return $query->get()->pluck('configuration')->toArray();
    }

    /**
     * Get payment terms for a company.
     *
     * @param int $companyId
     * @return array
     */
    public static function getPaymentTerms(int $companyId): array
    {
        return static::active()
            ->forCompany($companyId)
            ->ofType(self::TYPE_PAYMENT_TERMS)
            ->first()
            ->configuration ?? [];
    }

    /**
     * Get invoice templates for a company.
     *
     * @param int $companyId
     * @return array
     */
    public static function getInvoiceTemplates(int $companyId): array
    {
        return static::active()
            ->forCompany($companyId)
            ->ofType(self::TYPE_TEMPLATE)
            ->get()
            ->pluck('configuration', 'name')
            ->toArray();
    }

    /**
     * Get currency configuration for a company.
     *
     * @param int $companyId
     * @return array
     */
    public static function getCurrencyConfiguration(int $companyId): array
    {
        return static::active()
            ->forCompany($companyId)
            ->ofType(self::TYPE_CURRENCY)
            ->first()
            ->configuration ?? [
                'default_currency' => 'USD',
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'INR'],
                'exchange_rate_provider' => 'fixer.io',
                'auto_update_rates' => true,
            ];
    }

    /**
     * Get automation rules for a company.
     *
     * @param int $companyId
     * @return array
     */
    public static function getAutomationRules(int $companyId): array
    {
        return static::active()
            ->forCompany($companyId)
            ->ofType(self::TYPE_AUTOMATION)
            ->get()
            ->pluck('configuration')
            ->toArray();
    }

    /**
     * Create default configurations for a company.
     *
     * @param int $companyId
     * @return void
     */
    public static function createDefaultConfigurations(int $companyId): void
    {
        $defaultConfigs = [
            [
                'company_id' => $companyId,
                'name' => 'Default Tax Rate',
                'type' => self::TYPE_TAX_RATE,
                'configuration' => [
                    'rate' => 10.0,
                    'name' => 'GST',
                    'region' => 'default',
                    'applies_to' => 'all',
                ],
                'is_active' => true,
            ],
            [
                'company_id' => $companyId,
                'name' => 'Standard Payment Terms',
                'type' => self::TYPE_PAYMENT_TERMS,
                'configuration' => [
                    'due_days' => 30,
                    'early_payment_discount' => 2.0,
                    'early_payment_days' => 10,
                    'late_payment_fee' => 5.0,
                    'grace_period_days' => 5,
                ],
                'is_active' => true,
            ],
            [
                'company_id' => $companyId,
                'name' => 'Default Template',
                'type' => self::TYPE_TEMPLATE,
                'configuration' => [
                    'template_name' => 'modern',
                    'logo_position' => 'top-left',
                    'color_scheme' => '#2563eb',
                    'font_family' => 'Inter',
                    'show_payment_terms' => true,
                    'show_tax_breakdown' => true,
                ],
                'is_active' => true,
            ],
            [
                'company_id' => $companyId,
                'name' => 'Currency Settings',
                'type' => self::TYPE_CURRENCY,
                'configuration' => [
                    'default_currency' => 'USD',
                    'supported_currencies' => ['USD', 'EUR', 'GBP', 'INR'],
                    'exchange_rate_provider' => 'fixer.io',
                    'auto_update_rates' => true,
                    'rate_update_frequency' => 'daily',
                ],
                'is_active' => true,
            ],
        ];

        foreach ($defaultConfigs as $config) {
            static::create($config);
        }
    }
}

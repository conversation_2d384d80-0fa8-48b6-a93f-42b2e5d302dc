<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * InvoiceItem Model for OneFoodDialer 2025
 *
 * @property int $id
 * @property int $invoice_id
 * @property string $item_type
 * @property string $item_name
 * @property string|null $description
 * @property string|null $sku
 * @property int $quantity
 * @property float $unit_price
 * @property float $total_price
 * @property float $tax_rate
 * @property float $tax_amount
 * @property float $discount_rate
 * @property float $discount_amount
 * @property array|null $metadata
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class InvoiceItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'invoice_id',
        'item_type',
        'item_name',
        'description',
        'sku',
        'quantity',
        'unit_price',
        'total_price',
        'tax_rate',
        'tax_amount',
        'discount_rate',
        'discount_amount',
        'metadata',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'quantity' => 'integer',
            'unit_price' => 'decimal:2',
            'total_price' => 'decimal:2',
            'tax_rate' => 'decimal:2',
            'tax_amount' => 'decimal:2',
            'discount_rate' => 'decimal:2',
            'discount_amount' => 'decimal:2',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the invoice that owns the invoice item.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Calculate the total price including tax and discount.
     */
    public function calculateTotalPrice(): float
    {
        $baseTotal = $this->quantity * $this->unit_price;
        $discountAmount = ($baseTotal * $this->discount_rate) / 100;
        $subtotal = $baseTotal - $discountAmount;
        $taxAmount = ($subtotal * $this->tax_rate) / 100;

        return $subtotal + $taxAmount;
    }

    /**
     * Update the calculated amounts.
     */
    public function updateCalculatedAmounts(): void
    {
        $baseTotal = $this->quantity * $this->unit_price;
        $this->discount_amount = ($baseTotal * $this->discount_rate) / 100;
        $subtotal = $baseTotal - $this->discount_amount;
        $this->tax_amount = ($subtotal * $this->tax_rate) / 100;
        $this->total_price = $subtotal + $this->tax_amount;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            border-bottom: 2px solid {{ $settings['colors']['primary'] }};
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            float: left;
            width: 50%;
        }
        
        .invoice-info {
            float: right;
            width: 45%;
            text-align: right;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: {{ $settings['colors']['primary'] }};
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: {{ $settings['colors']['primary'] }};
            margin-bottom: 10px;
        }
        
        .invoice-number {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .billing-section {
            margin: 30px 0;
        }
        
        .billing-info {
            float: left;
            width: 48%;
        }
        
        .billing-info h3 {
            color: {{ $settings['colors']['primary'] }};
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        
        .items-table th {
            background-color: {{ $settings['colors']['primary'] }};
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
        }
        
        .items-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #ddd;
        }
        
        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-section {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        
        .totals-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #ddd;
        }
        
        .totals-table .total-row {
            background-color: {{ $settings['colors']['primary'] }};
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: {{ $settings['colors']['secondary'] }};
        }
        
        .payment-terms {
            margin: 30px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid {{ $settings['colors']['accent'] }};
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .currency {
            font-family: 'DejaVu Sans', sans-serif;
        }
    </style>
</head>
<body>
    <div class="header clearfix">
        <div class="company-info">
            <div class="company-name">{{ $company['name'] }}</div>
            <div>{{ $company['address']['line1'] }}</div>
            @if($company['address']['line2'])
                <div>{{ $company['address']['line2'] }}</div>
            @endif
            <div>{{ $company['address']['city'] }}, {{ $company['address']['state'] }} {{ $company['address']['postal_code'] }}</div>
            <div>{{ $company['address']['country'] }}</div>
            <div style="margin-top: 10px;">
                <div>Phone: {{ $company['contact']['phone'] }}</div>
                <div>Email: {{ $company['contact']['email'] }}</div>
                @if($company['tax_details']['gstin'])
                    <div>GSTIN: {{ $company['tax_details']['gstin'] }}</div>
                @endif
            </div>
        </div>
        
        <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number"># {{ $invoice->invoice_number }}</div>
            <div>Date: {{ $invoice->created_at->format($settings['date_format']) }}</div>
            <div>Due Date: {{ $invoice->due_date->format($settings['date_format']) }}</div>
            <div style="margin-top: 10px;">
                <span class="status-badge status-{{ $invoice->status }}">{{ ucfirst($invoice->status) }}</span>
            </div>
        </div>
    </div>
    
    <div class="billing-section clearfix">
        <div class="billing-info">
            <h3>Bill To:</h3>
            <div><strong>{{ $invoice->customer_name }}</strong></div>
            <div>{{ $invoice->customer_email }}</div>
            @if($invoice->customer_phone)
                <div>{{ $invoice->customer_phone }}</div>
            @endif
            <div style="margin-top: 10px;">
                <div>{{ $invoice->billing_address['line1'] }}</div>
                @if(isset($invoice->billing_address['line2']))
                    <div>{{ $invoice->billing_address['line2'] }}</div>
                @endif
                <div>{{ $invoice->billing_address['city'] }}, {{ $invoice->billing_address['state'] }}</div>
                <div>{{ $invoice->billing_address['postal_code'] }}, {{ $invoice->billing_address['country'] }}</div>
            </div>
        </div>
        
        @if($invoice->shipping_address)
        <div class="billing-info" style="float: right;">
            <h3>Ship To:</h3>
            <div>{{ $invoice->shipping_address['line1'] }}</div>
            @if(isset($invoice->shipping_address['line2']))
                <div>{{ $invoice->shipping_address['line2'] }}</div>
            @endif
            <div>{{ $invoice->shipping_address['city'] }}, {{ $invoice->shipping_address['state'] }}</div>
            <div>{{ $invoice->shipping_address['postal_code'] }}, {{ $invoice->shipping_address['country'] }}</div>
        </div>
        @endif
    </div>
    
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 40%;">Description</th>
                <th style="width: 15%;" class="text-center">Qty</th>
                <th style="width: 15%;" class="text-right">Unit Price</th>
                @if($settings['show_tax_breakdown'])
                    <th style="width: 10%;" class="text-right">Tax</th>
                @endif
                <th style="width: 20%;" class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($items as $item)
            <tr>
                <td>
                    <strong>{{ $item->item_name }}</strong>
                    @if($item->description)
                        <br><small style="color: #666;">{{ $item->description }}</small>
                    @endif
                    @if($item->sku)
                        <br><small style="color: #666;">SKU: {{ $item->sku }}</small>
                    @endif
                </td>
                <td class="text-center">{{ $item->quantity }}</td>
                <td class="text-right"><span class="currency">{{ $settings['currency_symbol'] }}</span>{{ number_format($item->unit_price, 2) }}</td>
                @if($settings['show_tax_breakdown'])
                    <td class="text-right">
                        @if($item->tax_rate > 0)
                            {{ $item->tax_rate }}%
                        @else
                            -
                        @endif
                    </td>
                @endif
                <td class="text-right"><span class="currency">{{ $settings['currency_symbol'] }}</span>{{ number_format($item->total_price, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="clearfix">
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td>Subtotal:</td>
                    <td class="text-right"><span class="currency">{{ $settings['currency_symbol'] }}</span>{{ number_format($invoice->subtotal, 2) }}</td>
                </tr>
                @if($invoice->discount_amount > 0)
                <tr>
                    <td>Discount:</td>
                    <td class="text-right">-<span class="currency">{{ $settings['currency_symbol'] }}</span>{{ number_format($invoice->discount_amount, 2) }}</td>
                </tr>
                @endif
                @if($invoice->tax_amount > 0)
                <tr>
                    <td>Tax:</td>
                    <td class="text-right"><span class="currency">{{ $settings['currency_symbol'] }}</span>{{ number_format($invoice->tax_amount, 2) }}</td>
                </tr>
                @endif
                <tr class="total-row">
                    <td>Total:</td>
                    <td class="text-right"><span class="currency">{{ $settings['currency_symbol'] }}</span>{{ number_format($invoice->total_amount, 2) }}</td>
                </tr>
            </table>
        </div>
    </div>
    
    @if($settings['show_payment_terms'] && $settings['payment_terms'])
    <div class="payment-terms">
        <strong>Payment Terms:</strong> {{ $settings['payment_terms'] }}
    </div>
    @endif
    
    @if($invoice->notes)
    <div style="margin: 30px 0;">
        <h3 style="color: {{ $settings['colors']['primary'] }};">Notes:</h3>
        <p>{{ $invoice->notes }}</p>
    </div>
    @endif
    
    <div class="footer">
        <p>{{ $settings['footer_text'] }}</p>
        <p><small>Generated on {{ $generated_at }}</small></p>
    </div>
</body>
</html>

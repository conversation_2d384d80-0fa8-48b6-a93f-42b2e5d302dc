_format_version: "2.1"
_transform: true

services:
  # Invoice Service
  - name: invoice-service
    url: http://invoice-service-v12:8000
    routes:
      - name: invoice-service-route
        paths:
          - /v2/invoice
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: invoice-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: invoice-service-v12
      - name: jwt
        config:
          secret_is_base64: false
          key_claim_name: iss
          algorithm: RS256
          rsa_public_key: |
            -----BEGIN PUBLIC KEY-----
            # Replace with your actual public key
            -----<PERSON><PERSON> PUBLIC KEY-----
      - name: prometheus
        config:
          per_consumer: true
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Invoice Service Health Check
  - name: invoice-service-health
    url: http://invoice-service-v12:8000
    routes:
      - name: invoice-service-health-route
        paths:
          - /v2/health/invoice
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 30
          hour: 500
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-Type
            - Date
          credentials: true
          max_age: 3600
          preflight_continue: false

# Consumers
consumers:
  - username: invoice-api
    jwt_secrets:
      - algorithm: RS256
        key: invoice-api-key
        rsa_public_key: |
          -----BEGIN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

  - username: payment-service
    jwt_secrets:
      - algorithm: RS256
        key: payment-service-key
        rsa_public_key: |
          -----BEGIN PUBLIC KEY-----
          # Replace with your actual public key
          -----END PUBLIC KEY-----

# Global plugins
plugins:
  - name: prometheus
    config:
      per_consumer: false
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true

  - name: request-id
    config:
      header_name: X-Request-ID
      echo_downstream: true

  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      echo_downstream: true
      generator: uuid#counter

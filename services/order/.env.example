APP_NAME="Order Service"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8004
APP_PORT=8004

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=order_service
DB_USERNAME=root
DB_PASSWORD=

# Redis configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Queue configuration
QUEUE_CONNECTION=redis

# Session configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Cache configuration
CACHE_DRIVER=redis

# CORS configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000

# API rate limiting
API_RATE_LIMIT_MAX_REQUESTS=60
API_RATE_LIMIT_PERIOD=60

# Sanctum configuration
SANCTUM_STATEFUL_DOMAINS=localhost:3000

# Auth service configuration
AUTH_SERVICE_URL=http://localhost:8001/api/v2/auth

# User service configuration
USER_SERVICE_URL=http://localhost:8002/api/v2/users

# Payment service configuration
PAYMENT_SERVICE_URL=http://localhost:8003/api/v2/payments

# RabbitMQ configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

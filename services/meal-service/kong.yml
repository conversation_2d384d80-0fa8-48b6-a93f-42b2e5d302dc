_format_version: "2.1"
_transform: true

services:
  # Meal Service
  - name: meal-service
    url: http://meal-service:8000
    routes:
      - name: meal-service-route
        paths:
          - /v2/meals
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: meal-service

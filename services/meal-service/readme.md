# Meal Service

This microservice handles meal management functionality for the application.

## Features

- CRUD operations for meals
- Filtering meals by menu type, food type, etc.
- Meal pricing calculation
- Support for vegetarian/non-vegetarian classification
- Meal swapping functionality

## API Endpoints

### Base URL

```
/api/v2/meals
```

### Available Endpoints

- `GET /api/v2/meals` - Get all meals with optional filtering
- `POST /api/v2/meals` - Create a new meal
- `GET /api/v2/meals/{id}` - Get a specific meal
- `PUT /api/v2/meals/{id}` - Update a meal
- `DELETE /api/v2/meals/{id}` - Delete a meal
- `GET /api/v2/meals/menu/{menu}` - Get meals by menu type
- `GET /api/v2/meals/type/vegetarian` - Get vegetarian meals

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   composer install
   ```
3. Copy the environment file:
   ```bash
   cp .env.example .env
   ```
4. Generate application key:
   ```bash
   php artisan key:generate
   ```
5. Configure the database in `.env`
6. Run migrations and seeders:
   ```bash
   php artisan migrate --seed
   ```

## Running Tests

```bash
php artisan test
```

## API Documentation

The API is documented using the OpenAPI Specification (OAS) 3.0.0. The specification is available in the `openapi.yaml` file.

## Kong API Gateway Integration

This service is configured to work with Kong API Gateway. The configuration is available in the `kong.yml` file.

## Migrated from Zend Framework

This service was migrated from the Zend Framework application. The original code was located in:
- `module/QuickServe/src/QuickServe/Model/Meal.php`
- `module/QuickServe/src/QuickServe/Model/MealTable.php`
- Various controller files that handled meal-related functionality

The migration process involved:
1. Converting the code to PSR-4 compliant structure
2. Extracting business logic into a dedicated service class
3. Creating a proper Laravel model with relationships and scopes
4. Implementing RESTful API endpoints
5. Writing comprehensive tests
6. Creating OpenAPI documentation

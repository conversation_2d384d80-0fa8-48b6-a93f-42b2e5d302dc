<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// API v1 routes (for backward compatibility)
Route::prefix('v1')->group(function () {
    Route::apiResource('meals', 'Api\MealController');
    Route::get('meals/menu/{menu}', 'Api\MealController@getByMenu');
    Route::get('meals/type/vegetarian', 'Api\MealController@getVegetarian');
});

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->group(function () {
    Route::apiResource('meals', 'Api\MealController');
    Route::get('meals/menu/{menu}', 'Api\MealController@getByMenu');
    Route::get('meals/type/vegetarian', 'Api\MealController@getVegetarian');
});

<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Meal;
use Faker\Generator as Faker;

$factory->define(Meal::class, function (Faker $faker) {
    $foodTypes = ['veg', 'non-veg', 'beverage'];
    $menuTypes = ['breakfast', 'lunch', 'dinner'];
    $swapOptions = ['nocharge', 'askdifference', 'swapcharge'];

    return [
        'company_id' => 1,
        'unit_id' => 1,
        'name' => $faker->words(3, true),
        'description' => $faker->sentence,
        'unit_price' => $faker->randomFloat(2, 5, 20),
        'items' => json_encode([
            $faker->numberBetween(1, 100) => $faker->numberBetween(1, 3),
            $faker->numberBetween(101, 200) => $faker->numberBetween(1, 3),
        ]),
        'category' => $faker->randomElement($menuTypes),
        'food_type' => $faker->randomElement($foodTypes),
        'product_type' => 'Meal',
        'product_category' => $faker->word,
        'threshold' => $faker->numberBetween(10, 50),
        'image_path' => 'meals/' . $faker->word . '.jpg',
        'screen' => $faker->randomElement($menuTypes),
        'status' => $faker->boolean(80), // 80% chance of being active
        'is_swappable' => $faker->boolean(60), // 60% chance of being swappable
        'swap_with' => $faker->randomElement($swapOptions),
        'swap_charges' => $faker->randomFloat(2, 1, 5),
        'meal_plans' => null,
        'is_custom' => $faker->boolean(20), // 20% chance of being custom
    ];
});

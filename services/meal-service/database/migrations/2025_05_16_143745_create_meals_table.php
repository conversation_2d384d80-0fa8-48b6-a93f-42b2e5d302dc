<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateMealsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('meals', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('company_id')->default(1);
            $table->integer('unit_id')->default(1);
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('unit_price', 10, 2)->default(0);
            $table->json('items')->nullable();
            $table->string('category')->nullable()->comment('menu type: breakfast, lunch, dinner');
            $table->string('food_type')->default('veg')->comment('veg, non-veg, beverage');
            $table->string('product_type')->default('Meal');
            $table->string('product_category')->nullable();
            $table->integer('threshold')->nullable()->comment('kitchen capacity');
            $table->string('image_path')->nullable();
            $table->string('screen')->nullable();
            $table->boolean('status')->default(true);
            $table->boolean('is_swappable')->default(false);
            $table->string('swap_with')->nullable();
            $table->decimal('swap_charges', 10, 2)->nullable();
            $table->text('meal_plans')->nullable();
            $table->boolean('is_custom')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('meals');
    }
}

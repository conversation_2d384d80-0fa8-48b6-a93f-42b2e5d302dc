<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MealsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Sample vegetarian meals for breakfast
        DB::table('meals')->insert([
            [
                'name' => 'Vegetable Omelette',
                'description' => 'Fluffy omelette with mixed vegetables and cheese',
                'unit_price' => 8.99,
                'items' => json_encode(['101' => 1, '102' => 2, '103' => 1]),
                'category' => 'breakfast',
                'food_type' => 'veg',
                'product_type' => 'Meal',
                'product_category' => 'Breakfast Special',
                'threshold' => 50,
                'image_path' => 'meals/veg-omelette.jpg',
                'screen' => 'breakfast',
                'status' => true,
                'is_swappable' => true,
                'swap_with' => 'nocharge',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Avocado Toast',
                'description' => 'Whole grain toast with smashed avocado, cherry tomatoes, and microgreens',
                'unit_price' => 7.99,
                'items' => json_encode(['104' => 1, '105' => 1, '106' => 1]),
                'category' => 'breakfast',
                'food_type' => 'veg',
                'product_type' => 'Meal',
                'product_category' => 'Breakfast Special',
                'threshold' => 40,
                'image_path' => 'meals/avocado-toast.jpg',
                'screen' => 'breakfast',
                'status' => true,
                'is_swappable' => true,
                'swap_with' => 'askdifference',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Sample non-vegetarian meals for lunch
        DB::table('meals')->insert([
            [
                'name' => 'Grilled Chicken Salad',
                'description' => 'Fresh mixed greens with grilled chicken, cherry tomatoes, cucumber, and balsamic dressing',
                'unit_price' => 12.99,
                'items' => json_encode(['201' => 1, '202' => 1, '203' => 1]),
                'category' => 'lunch',
                'food_type' => 'non-veg',
                'product_type' => 'Meal',
                'product_category' => 'Lunch Special',
                'threshold' => 35,
                'image_path' => 'meals/chicken-salad.jpg',
                'screen' => 'lunch',
                'status' => true,
                'is_swappable' => true,
                'swap_with' => 'swapcharge',
                'swap_charges' => 2.00,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Beef Burger',
                'description' => 'Juicy beef patty with lettuce, tomato, cheese, and special sauce on a brioche bun',
                'unit_price' => 14.99,
                'items' => json_encode(['204' => 1, '205' => 1, '206' => 1]),
                'category' => 'lunch',
                'food_type' => 'non-veg',
                'product_type' => 'Meal',
                'product_category' => 'Lunch Special',
                'threshold' => 30,
                'image_path' => 'meals/beef-burger.jpg',
                'screen' => 'lunch',
                'status' => true,
                'is_swappable' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Sample vegetarian meals for dinner
        DB::table('meals')->insert([
            [
                'name' => 'Vegetable Curry',
                'description' => 'Mixed vegetables in a rich curry sauce served with basmati rice',
                'unit_price' => 11.99,
                'items' => json_encode(['301' => 1, '302' => 1, '303' => 1]),
                'category' => 'dinner',
                'food_type' => 'veg',
                'product_type' => 'Meal',
                'product_category' => 'Dinner Special',
                'threshold' => 25,
                'image_path' => 'meals/veg-curry.jpg',
                'screen' => 'dinner',
                'status' => true,
                'is_swappable' => true,
                'swap_with' => 'nocharge',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Pasta Primavera',
                'description' => 'Penne pasta with seasonal vegetables in a light cream sauce',
                'unit_price' => 13.99,
                'items' => json_encode(['304' => 1, '305' => 1, '306' => 1]),
                'category' => 'dinner',
                'food_type' => 'veg',
                'product_type' => 'Meal',
                'product_category' => 'Dinner Special',
                'threshold' => 20,
                'image_path' => 'meals/pasta-primavera.jpg',
                'screen' => 'dinner',
                'status' => true,
                'is_swappable' => true,
                'swap_with' => 'askdifference',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}

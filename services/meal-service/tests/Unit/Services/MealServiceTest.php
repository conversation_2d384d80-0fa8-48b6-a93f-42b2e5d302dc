<?php

namespace Tests\Unit\Services;

use App\Meal;
use App\Services\MealService;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Mockery\MockInterface;

class MealServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var MealService
     */
    protected $mealService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->mealService = new MealService();
    }

    /**
     * Test getting all meals.
     *
     * @return void
     */
    public function testGetAllMeals()
    {
        // Create test meals
        factory(Meal::class, 5)->create(['food_type' => 'veg']);
        factory(Meal::class, 3)->create(['food_type' => 'non-veg']);

        // Get all meals
        $meals = $this->mealService->getAllMeals();

        // Assert that we get all 8 meals
        $this->assertCount(8, $meals);
    }

    /**
     * Test filtering meals by food type.
     *
     * @return void
     */
    public function testGetMealsByFoodType()
    {
        // Create test meals
        factory(Meal::class, 5)->create(['food_type' => 'veg']);
        factory(Meal::class, 3)->create(['food_type' => 'non-veg']);

        // Get vegetarian meals
        $vegMeals = $this->mealService->getAllMeals(['food_type' => 'veg']);

        // Assert that we get only vegetarian meals
        $this->assertCount(5, $vegMeals);
        foreach ($vegMeals as $meal) {
            $this->assertEquals('veg', $meal->food_type);
        }
    }

    /**
     * Test getting a meal by ID.
     *
     * @return void
     */
    public function testGetMealById()
    {
        // Create a test meal
        $meal = factory(Meal::class)->create();

        // Get the meal by ID
        $foundMeal = $this->mealService->getMealById($meal->id);

        // Assert that we get the correct meal
        $this->assertInstanceOf(Meal::class, $foundMeal);
        $this->assertEquals($meal->id, $foundMeal->id);
        $this->assertEquals($meal->name, $foundMeal->name);
    }

    /**
     * Test creating a meal.
     *
     * @return void
     */
    public function testCreateMeal()
    {
        // Data for a new meal
        $mealData = [
            'name' => 'Test Meal',
            'description' => 'Test Description',
            'unit_price' => 9.99,
            'food_type' => 'veg',
            'category' => 'lunch',
            'product_type' => 'Meal',
        ];

        // Create the meal
        $meal = $this->mealService->createMeal($mealData);

        // Assert that the meal was created correctly
        $this->assertInstanceOf(Meal::class, $meal);
        $this->assertEquals('Test Meal', $meal->name);
        $this->assertEquals('Test Description', $meal->description);
        $this->assertEquals(9.99, $meal->unit_price);
        $this->assertEquals('veg', $meal->food_type);
    }

    /**
     * Test calculating meal price with discount.
     *
     * @return void
     */
    public function testCalculateMealPrice()
    {
        // Create a test meal
        $meal = factory(Meal::class)->create(['unit_price' => 10.00]);

        // Calculate price without discount
        $price = $this->mealService->calculateMealPrice($meal);
        $this->assertEquals(10.00, $price);

        // Calculate price with 20% discount
        $discountedPrice = $this->mealService->calculateMealPrice($meal, 20);
        $this->assertEquals(8.00, $discountedPrice);
    }
}

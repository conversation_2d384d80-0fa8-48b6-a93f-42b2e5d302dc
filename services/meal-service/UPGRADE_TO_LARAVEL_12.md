# Upgrading from Laravel 5.8 to Laravel 12

This guide outlines the steps needed to upgrade the Meal Service from Laravel 5.8 to Laravel 12.

## Prerequisites

- PHP 8.2 or higher
- Composer 2.x

## Step 1: Create a New Laravel 12 Project

```bash
composer create-project laravel/laravel:^12.0 meal-service-v12
```

## Step 2: Directory Structure Changes

Laravel 12 has a different directory structure compared to Laravel 5.8. Here are the key changes:

### Models

- Move models from `app/` to `app/Models/`
- Update namespace from `App` to `App\Models`

### Controllers

- Keep controllers in `app/Http/Controllers/`
- No namespace change needed

### Services

- Keep services in `app/Services/`
- No namespace change needed

## Step 3: Update Model Definitions

Laravel 12 models have several differences:

1. Use PHP 8.2 property types:

```php
// Laravel 5.8
protected $fillable = ['name', 'description'];

// Laravel 12
protected array $fillable = ['name', 'description'];
```

2. Use the new cast syntax:

```php
// Laravel 5.8
protected $casts = [
    'status' => 'boolean',
    'unit_price' => 'float',
];

// Laravel 12
protected $casts = [
    'status' => 'boolean',
    'unit_price' => 'decimal:2',
];
```

3. Update model class:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Meal extends Model
{
    use HasFactory;
    
    // Rest of the model...
}
```

## Step 4: Update Controllers

1. Update controller namespace imports:

```php
// Laravel 5.8
use App\Meal;

// Laravel 12
use App\Models\Meal;
```

2. Use PHP 8.2 constructor property promotion:

```php
// Laravel 5.8
protected $mealService;

public function __construct(MealService $mealService)
{
    $this->mealService = $mealService;
}

// Laravel 12
public function __construct(
    protected MealService $mealService
) {}
```

3. Use PHP 8.2 return types:

```php
// Laravel 5.8
public function index(Request $request)
{
    // ...
}

// Laravel 12
public function index(Request $request): JsonResponse
{
    // ...
}
```

## Step 5: Update Routes

1. Update the routes/api.php file:

```php
<?php

use App\Http\Controllers\Api\MealController;
use Illuminate\Support\Facades\Route;

// API v2 routes
Route::prefix('v2')->group(function () {
    Route::apiResource('meals', MealController::class);
    Route::get('meals/menu/{menu}', [MealController::class, 'getByMenu']);
    Route::get('meals/type/vegetarian', [MealController::class, 'getVegetarian']);
});
```

## Step 6: Update Migrations

1. Use the new migration syntax:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('meals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->default(1);
            $table->foreignId('unit_id')->default(1);
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('unit_price', 10, 2)->default(0);
            $table->json('items')->nullable();
            $table->string('category')->nullable()->comment('menu type: breakfast, lunch, dinner');
            $table->string('food_type')->default('veg')->comment('veg, non-veg, beverage');
            $table->string('product_type')->default('Meal');
            $table->string('product_category')->nullable();
            $table->integer('threshold')->nullable()->comment('kitchen capacity');
            $table->string('image_path')->nullable();
            $table->string('screen')->nullable();
            $table->boolean('status')->default(true);
            $table->boolean('is_swappable')->default(false);
            $table->string('swap_with')->nullable();
            $table->decimal('swap_charges', 10, 2)->nullable();
            $table->text('meal_plans')->nullable();
            $table->boolean('is_custom')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('meals');
    }
};
```

## Step 7: Update Factories

1. Use the new factory syntax:

```php
<?php

namespace Database\Factories;

use App\Models\Meal;
use Illuminate\Database\Eloquent\Factories\Factory;

class MealFactory extends Factory
{
    protected $model = Meal::class;

    public function definition(): array
    {
        $foodTypes = ['veg', 'non-veg', 'beverage'];
        $menuTypes = ['breakfast', 'lunch', 'dinner'];
        $swapOptions = ['nocharge', 'askdifference', 'swapcharge'];
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence,
            'unit_price' => $this->faker->randomFloat(2, 5, 20),
            'items' => json_encode([
                $this->faker->numberBetween(1, 100) => $this->faker->numberBetween(1, 3),
                $this->faker->numberBetween(101, 200) => $this->faker->numberBetween(1, 3),
            ]),
            'category' => $this->faker->randomElement($menuTypes),
            'food_type' => $this->faker->randomElement($foodTypes),
            'product_type' => 'Meal',
            'product_category' => $this->faker->word,
            'threshold' => $this->faker->numberBetween(10, 50),
            'image_path' => 'meals/' . $this->faker->word . '.jpg',
            'screen' => $this->faker->randomElement($menuTypes),
            'status' => $this->faker->boolean(80),
            'is_swappable' => $this->faker->boolean(60),
            'swap_with' => $this->faker->randomElement($swapOptions),
            'swap_charges' => $this->faker->randomFloat(2, 1, 5),
            'meal_plans' => null,
            'is_custom' => $this->faker->boolean(20),
        ];
    }
}
```

## Step 8: Update Tests

1. Update test imports:

```php
// Laravel 5.8
use App\Meal;

// Laravel 12
use App\Models\Meal;
```

2. Use the new testing syntax:

```php
// Laravel 5.8
factory(Meal::class, 5)->create();

// Laravel 12
Meal::factory()->count(5)->create();
```

3. Add return types to test methods:

```php
// Laravel 5.8
public function testGetAllMeals()
{
    // ...
}

// Laravel 12
public function testGetAllMeals(): void
{
    // ...
}
```

## Step 9: Update Service Provider Registration

If you have any custom service providers, update their registration in `config/app.php`.

## Step 10: Update Composer Dependencies

Update the `composer.json` file to use Laravel 12 compatible packages.

## Step 11: Run Tests

After completing the migration, run the tests to ensure everything is working correctly:

```bash
php artisan test
```

## Additional Resources

- [Laravel 12 Documentation](https://laravel.com/docs/12.x)
- [Laravel 12 Upgrade Guide](https://laravel.com/docs/12.x/upgrade)

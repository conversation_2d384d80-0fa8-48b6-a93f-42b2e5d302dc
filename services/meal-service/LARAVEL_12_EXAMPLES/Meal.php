<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Meal extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected string $table = 'meals';

    /**
     * The primary key for the model.
     */
    protected string $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'company_id',
        'unit_id',
        'name',
        'description',
        'unit_price',
        'items',
        'category',
        'food_type',
        'product_type',
        'product_category',
        'threshold',
        'image_path',
        'screen',
        'status',
        'is_swappable',
        'swap_with',
        'swap_charges',
        'meal_plans',
        'is_custom'
    ];

    /**
     * The attributes that should be cast.
     */
    protected array $casts = [
        'unit_price' => 'decimal:2',
        'items' => 'array',
        'status' => 'boolean',
        'is_swappable' => 'boolean',
        'swap_charges' => 'decimal:2',
        'is_custom' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the meal's items as a string.
     */
    public function getItemsAsString(): string
    {
        if (empty($this->items)) {
            return '';
        }

        $itemIds = array_keys($this->items);
        $items = self::whereIn('id', $itemIds)->get();
        
        $result = [];
        foreach ($items as $item) {
            $quantity = $this->items[$item->id] ?? 1;
            $result[] = "{$item->name} (x{$quantity})";
        }

        return implode(', ', $result);
    }

    /**
     * Scope a query to only include active meals.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include vegetarian meals.
     */
    public function scopeVegetarian(Builder $query): Builder
    {
        return $query->where('food_type', 'veg');
    }

    /**
     * Scope a query to only include meals for a specific menu.
     */
    public function scopeForMenu(Builder $query, string $menu): Builder
    {
        return $query->where('category', $menu);
    }
}

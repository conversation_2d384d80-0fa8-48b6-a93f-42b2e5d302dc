# Laravel 12 Examples for Meal Service

This directory contains example files for the Laravel 12 version of the Meal Service. These files demonstrate the changes needed to upgrade from Laravel 5.8 to Laravel 12.

## Files Included

- `Meal.php` - Laravel 12 version of the Meal model
- `MealController.php` - Laravel 12 version of the MealController
- `MealService.php` - Laravel 12 version of the MealService
- `create_meals_table.php` - Laravel 12 version of the migration
- `api.php` - Laravel 12 version of the routes
- `MealFactory.php` - Laravel 12 version of the factory
- `MealServiceTest.php` - Laravel 12 version of the unit test
- `MealControllerTest.php` - Laravel 12 version of the feature test

## Key Changes

### PHP 8.2 Features

- Type declarations for properties and methods
- Constructor property promotion
- Return type declarations
- Union types
- Readonly properties (where applicable)

### Laravel 12 Features

- New migration syntax using anonymous classes
- New factory syntax with state methods
- Updated model factory usage in tests
- Updated route definitions

## How to Use These Examples

These examples are meant to be used as a reference when upgrading the Meal Service from Laravel 5.8 to Laravel 12. They should not be used directly in the Laravel 5.8 project, as they require PHP 8.2 and Laravel 12.

To upgrade the Meal Service to Laravel 12:

1. Create a new Laravel 12 project
2. Use these examples as a reference to update your code
3. Copy the updated code to the new Laravel 12 project
4. Run the tests to ensure everything is working correctly

## Additional Resources

- [Laravel 12 Documentation](https://laravel.com/docs/12.x)
- [Laravel 12 Upgrade Guide](https://laravel.com/docs/12.x/upgrade)
- [PHP 8.2 Documentation](https://www.php.net/releases/8.2/en.php)

<?php

namespace App\Services;

use App\Models\Meal;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;

class MealService
{
    /**
     * Get all meals with optional filtering.
     *
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllMeals(array $filters = []): Collection
    {
        $query = Meal::query();

        // Apply filters
        if (isset($filters['menu']) && !empty($filters['menu'])) {
            $query->forMenu($filters['menu']);
        }

        if (isset($filters['food_type']) && !empty($filters['food_type'])) {
            $query->where('food_type', $filters['food_type']);
        }

        if (isset($filters['product_category']) && !empty($filters['product_category'])) {
            $query->where('product_category', $filters['product_category']);
        }

        if (isset($filters['active']) && $filters['active']) {
            $query->active();
        }

        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        // Apply sorting
        $sortField = $filters['sort_by'] ?? 'name';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        return $query->get();
    }

    /**
     * Get a meal by ID.
     *
     * @param int $id
     * @return \App\Models\Meal|null
     */
    public function getMealById(int $id): ?Meal
    {
        return Meal::find($id);
    }

    /**
     * Create a new meal.
     *
     * @param array $data
     * @return \App\Models\Meal
     */
    public function createMeal(array $data): Meal
    {
        return Meal::create($data);
    }

    /**
     * Update an existing meal.
     *
     * @param int $id
     * @param array $data
     * @return \App\Models\Meal|null
     */
    public function updateMeal(int $id, array $data): ?Meal
    {
        $meal = Meal::find($id);
        
        if (!$meal) {
            return null;
        }
        
        $meal->update($data);
        return $meal;
    }

    /**
     * Delete a meal.
     *
     * @param int $id
     * @return bool
     */
    public function deleteMeal(int $id): bool
    {
        $meal = Meal::find($id);
        
        if (!$meal) {
            return false;
        }
        
        return $meal->delete();
    }

    /**
     * Get meals by menu type.
     *
     * @param string $menu
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getMealsByMenu(string $menu): Collection
    {
        return Meal::forMenu($menu)->active()->get();
    }

    /**
     * Get vegetarian meals.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getVegetarianMeals(): Collection
    {
        return Meal::vegetarian()->active()->get();
    }

    /**
     * Calculate meal price with optional discounts.
     *
     * @param \App\Models\Meal $meal
     * @param float $discountPercentage
     * @return float
     */
    public function calculateMealPrice(Meal $meal, float $discountPercentage = 0): float
    {
        $price = $meal->unit_price;
        
        if ($discountPercentage > 0) {
            $discount = ($price * $discountPercentage) / 100;
            $price -= $discount;
        }
        
        return round($price, 2);
    }
}

<?php

namespace Tests\Unit\Services;

use App\Models\Meal;
use App\Services\MealService;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MealServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var MealService
     */
    protected MealService $mealService;

    /**
     * Set up the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->mealService = new MealService();
    }

    /**
     * Test getting all meals.
     */
    public function testGetAllMeals(): void
    {
        // Create test meals
        Meal::factory()->count(5)->create(['food_type' => 'veg']);
        Meal::factory()->count(3)->create(['food_type' => 'non-veg']);

        // Get all meals
        $meals = $this->mealService->getAllMeals();
        
        // Assert that we get all 8 meals
        $this->assertCount(8, $meals);
    }

    /**
     * Test filtering meals by food type.
     */
    public function testGetMealsByFoodType(): void
    {
        // Create test meals
        Meal::factory()->count(5)->create(['food_type' => 'veg']);
        Meal::factory()->count(3)->create(['food_type' => 'non-veg']);

        // Get vegetarian meals
        $vegMeals = $this->mealService->getAllMeals(['food_type' => 'veg']);
        
        // Assert that we get only vegetarian meals
        $this->assertCount(5, $vegMeals);
        foreach ($vegMeals as $meal) {
            $this->assertEquals('veg', $meal->food_type);
        }
    }

    /**
     * Test getting a meal by ID.
     */
    public function testGetMealById(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create();

        // Get the meal by ID
        $foundMeal = $this->mealService->getMealById($meal->id);
        
        // Assert that we get the correct meal
        $this->assertInstanceOf(Meal::class, $foundMeal);
        $this->assertEquals($meal->id, $foundMeal->id);
        $this->assertEquals($meal->name, $foundMeal->name);
    }

    /**
     * Test creating a meal.
     */
    public function testCreateMeal(): void
    {
        // Data for a new meal
        $mealData = [
            'name' => 'Test Meal',
            'description' => 'Test Description',
            'unit_price' => 9.99,
            'food_type' => 'veg',
            'category' => 'lunch',
            'product_type' => 'Meal',
        ];

        // Create the meal
        $meal = $this->mealService->createMeal($mealData);
        
        // Assert that the meal was created correctly
        $this->assertInstanceOf(Meal::class, $meal);
        $this->assertEquals('Test Meal', $meal->name);
        $this->assertEquals('Test Description', $meal->description);
        $this->assertEquals(9.99, $meal->unit_price);
        $this->assertEquals('veg', $meal->food_type);
    }

    /**
     * Test calculating meal price with discount.
     */
    public function testCalculateMealPrice(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create(['unit_price' => 10.00]);

        // Calculate price without discount
        $price = $this->mealService->calculateMealPrice($meal);
        $this->assertEquals(10.00, $price);

        // Calculate price with 20% discount
        $discountedPrice = $this->mealService->calculateMealPrice($meal, 20);
        $this->assertEquals(8.00, $discountedPrice);
    }
}

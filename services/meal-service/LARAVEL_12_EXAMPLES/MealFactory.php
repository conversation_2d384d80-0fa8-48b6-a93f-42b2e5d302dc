<?php

namespace Database\Factories;

use App\Models\Meal;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Meal>
 */
class MealFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Meal::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $foodTypes = ['veg', 'non-veg', 'beverage'];
        $menuTypes = ['breakfast', 'lunch', 'dinner'];
        $swapOptions = ['nocharge', 'askdifference', 'swapcharge'];
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence,
            'unit_price' => $this->faker->randomFloat(2, 5, 20),
            'items' => json_encode([
                $this->faker->numberBetween(1, 100) => $this->faker->numberBetween(1, 3),
                $this->faker->numberBetween(101, 200) => $this->faker->numberBetween(1, 3),
            ]),
            'category' => $this->faker->randomElement($menuTypes),
            'food_type' => $this->faker->randomElement($foodTypes),
            'product_type' => 'Meal',
            'product_category' => $this->faker->word,
            'threshold' => $this->faker->numberBetween(10, 50),
            'image_path' => 'meals/' . $this->faker->word . '.jpg',
            'screen' => $this->faker->randomElement($menuTypes),
            'status' => $this->faker->boolean(80), // 80% chance of being active
            'is_swappable' => $this->faker->boolean(60), // 60% chance of being swappable
            'swap_with' => $this->faker->randomElement($swapOptions),
            'swap_charges' => $this->faker->randomFloat(2, 1, 5),
            'meal_plans' => null,
            'is_custom' => $this->faker->boolean(20), // 20% chance of being custom
        ];
    }

    /**
     * Indicate that the meal is vegetarian.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function vegetarian(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'food_type' => 'veg',
            ];
        });
    }

    /**
     * Indicate that the meal is for a specific menu.
     *
     * @param string $menu
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function forMenu(string $menu): Factory
    {
        return $this->state(function (array $attributes) use ($menu) {
            return [
                'category' => $menu,
                'screen' => $menu,
            ];
        });
    }
}

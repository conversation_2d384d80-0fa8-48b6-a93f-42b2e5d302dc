<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Meal extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'meals';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'name',
        'description',
        'unit_price',
        'items',
        'category',
        'food_type',
        'product_type',
        'product_category',
        'threshold',
        'image_path',
        'screen',
        'status',
        'is_swappable',
        'swap_with',
        'swap_charges',
        'meal_plans',
        'is_custom'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'unit_price' => 'float',
        'items' => 'array',
        'status' => 'boolean',
        'is_swappable' => 'boolean',
        'swap_charges' => 'float',
        'is_custom' => 'boolean'
    ];

    /**
     * Get the meal's items as a string.
     *
     * @return string
     */
    public function getItemsAsString()
    {
        if (empty($this->items)) {
            return '';
        }

        $itemIds = array_keys($this->items);
        $items = self::whereIn('id', $itemIds)->get();

        $result = [];
        foreach ($items as $item) {
            $quantity = $this->items[$item->id] ?? 1;
            $result[] = "{$item->name} (x{$quantity})";
        }

        return implode(', ', $result);
    }

    /**
     * Scope a query to only include active meals.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include vegetarian meals.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVegetarian($query)
    {
        return $query->where('food_type', 'veg');
    }

    /**
     * Scope a query to only include meals for a specific menu.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $menu
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForMenu($query, $menu)
    {
        return $query->where('category', $menu);
    }
}

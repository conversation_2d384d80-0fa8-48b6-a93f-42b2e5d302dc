<?php

namespace App\Http\Controllers\Api;

use App\Meal;
use App\Services\MealService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class MealController extends Controller
{
    /**
     * The meal service instance.
     *
     * @var \App\Services\MealService
     */
    protected $mealService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\MealService  $mealService
     * @return void
     */
    public function __construct(MealService $mealService)
    {
        $this->mealService = $mealService;
    }

    /**
     * Display a listing of the meals.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $filters = $request->only([
            'menu', 'food_type', 'product_category', 'active',
            'search', 'sort_by', 'sort_direction'
        ]);

        $meals = $this->mealService->getAllMeals($filters);

        return response()->json([
            'status' => 'success',
            'data' => $meals
        ]);
    }

    /**
     * Store a newly created meal in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'unit_price' => 'required|numeric|min:0',
            'items' => 'nullable|json',
            'category' => 'nullable|string|max:255',
            'food_type' => 'required|string|in:veg,non-veg,beverage',
            'product_category' => 'nullable|string|max:255',
            'threshold' => 'nullable|integer|min:0',
            'image_path' => 'nullable|string|max:255',
            'screen' => 'nullable|string|max:255',
            'status' => 'boolean',
            'is_swappable' => 'boolean',
            'swap_with' => 'nullable|string|max:255',
            'swap_charges' => 'nullable|numeric|min:0',
            'meal_plans' => 'nullable|string',
            'is_custom' => 'boolean'
        ]);

        $meal = $this->mealService->createMeal($validatedData);

        return response()->json([
            'status' => 'success',
            'message' => 'Meal created successfully',
            'data' => $meal
        ], 201);
    }

    /**
     * Display the specified meal.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $meal = $this->mealService->getMealById($id);

        if (!$meal) {
            return response()->json([
                'status' => 'error',
                'message' => 'Meal not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $meal
        ]);
    }

    /**
     * Update the specified meal in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'string|max:255',
            'description' => 'nullable|string',
            'unit_price' => 'numeric|min:0',
            'items' => 'nullable|json',
            'category' => 'nullable|string|max:255',
            'food_type' => 'string|in:veg,non-veg,beverage',
            'product_category' => 'nullable|string|max:255',
            'threshold' => 'nullable|integer|min:0',
            'image_path' => 'nullable|string|max:255',
            'screen' => 'nullable|string|max:255',
            'status' => 'boolean',
            'is_swappable' => 'boolean',
            'swap_with' => 'nullable|string|max:255',
            'swap_charges' => 'nullable|numeric|min:0',
            'meal_plans' => 'nullable|string',
            'is_custom' => 'boolean'
        ]);

        $meal = $this->mealService->updateMeal($id, $validatedData);

        if (!$meal) {
            return response()->json([
                'status' => 'error',
                'message' => 'Meal not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Meal updated successfully',
            'data' => $meal
        ]);
    }

    /**
     * Remove the specified meal from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $result = $this->mealService->deleteMeal($id);

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => 'Meal not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Meal deleted successfully'
        ]);
    }

    /**
     * Get meals by menu type.
     *
     * @param  string  $menu
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByMenu($menu)
    {
        $meals = $this->mealService->getMealsByMenu($menu);

        return response()->json([
            'status' => 'success',
            'data' => $meals
        ]);
    }

    /**
     * Get vegetarian meals.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVegetarian()
    {
        $meals = $this->mealService->getVegetarianMeals();

        return response()->json([
            'status' => 'success',
            'data' => $meals
        ]);
    }
}

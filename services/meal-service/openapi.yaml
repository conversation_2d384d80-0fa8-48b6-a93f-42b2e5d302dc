openapi: 3.0.0
info:
  title: Meal Service API
  description: API for meal management
  version: 1.0.0
  contact:
    name: Support Team
    email: <EMAIL>

servers:
  - url: http://localhost:8000/api/v2
    description: Local development server
  - url: https://tenant.cubeonebiz.com/api/v2
    description: Production server

components:
  schemas:
    Meal:
      type: object
      properties:
        id:
          type: integer
          description: Meal ID
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        name:
          type: string
          description: Meal name
        description:
          type: string
          description: Meal description
        unit_price:
          type: number
          format: float
          description: Meal price
        items:
          type: object
          description: JSON object containing product IDs and quantities
        category:
          type: string
          description: Menu type (breakfast, lunch, dinner)
        food_type:
          type: string
          description: Food type (veg, non-veg, beverage)
        product_type:
          type: string
          description: Product type (Meal)
        product_category:
          type: string
          description: Product category
        threshold:
          type: integer
          description: Kitchen capacity
        image_path:
          type: string
          description: Path to meal image
        screen:
          type: string
          description: Screen type
        status:
          type: boolean
          description: Meal status (active/inactive)
        is_swappable:
          type: boolean
          description: Whether the meal is swappable
        swap_with:
          type: string
          description: Swap option (nocharge, askdifference, swapcharge)
        swap_charges:
          type: number
          format: float
          description: Swap charges
        meal_plans:
          type: string
          description: Meal plans
        is_custom:
          type: boolean
          description: Whether the meal is custom
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
      required:
        - name
        - unit_price
        - food_type
    
    Error:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: Error message

paths:
  /meals:
    get:
      summary: Get all meals
      description: Returns a list of all meals with optional filtering
      parameters:
        - name: menu
          in: query
          description: Filter by menu type (breakfast, lunch, dinner)
          schema:
            type: string
        - name: food_type
          in: query
          description: Filter by food type (veg, non-veg, beverage)
          schema:
            type: string
        - name: product_category
          in: query
          description: Filter by product category
          schema:
            type: string
        - name: active
          in: query
          description: Filter by active status
          schema:
            type: boolean
        - name: search
          in: query
          description: Search term for meal name or description
          schema:
            type: string
        - name: sort_by
          in: query
          description: Field to sort by
          schema:
            type: string
            default: name
        - name: sort_direction
          in: query
          description: Sort direction
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Meal'
    
    post:
      summary: Create a new meal
      description: Creates a new meal with the provided data
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Meal'
      responses:
        '201':
          description: Meal created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Meal created successfully
                  data:
                    $ref: '#/components/schemas/Meal'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /meals/{id}:
    get:
      summary: Get a specific meal
      description: Returns details of a specific meal
      parameters:
        - name: id
          in: path
          required: true
          description: Meal ID
          schema:
            type: integer
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Meal'
        '404':
          description: Meal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    put:
      summary: Update a meal
      description: Updates an existing meal with the provided data
      parameters:
        - name: id
          in: path
          required: true
          description: Meal ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Meal'
      responses:
        '200':
          description: Meal updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Meal updated successfully
                  data:
                    $ref: '#/components/schemas/Meal'
        '404':
          description: Meal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    delete:
      summary: Delete a meal
      description: Deletes a specific meal
      parameters:
        - name: id
          in: path
          required: true
          description: Meal ID
          schema:
            type: integer
      responses:
        '200':
          description: Meal deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Meal deleted successfully
        '404':
          description: Meal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

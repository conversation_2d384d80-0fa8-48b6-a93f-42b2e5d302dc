<?php

declare(strict_types=1);

use Rector\Core\Configuration\Option;
use <PERSON>\Set\ValueObject\SetList;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    // Define paths to refactor
    $parameters = $containerConfigurator->parameters();
    $parameters->set(Option::PATHS, [
        __DIR__ . '/app',
    ]);

    // Define sets of rules
    $containerConfigurator->import(SetList::PSR_4);
    $containerConfigurator->import(SetList::CODE_QUALITY);
    $containerConfigurator->import(SetList::DEAD_CODE);
};

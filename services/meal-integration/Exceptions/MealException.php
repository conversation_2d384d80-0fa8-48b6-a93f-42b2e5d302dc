<?php

namespace App\Exceptions\Meal;

use Exception;

/**
 * Meal Exception
 * 
 * This exception is thrown when there is an error with the Meal Service.
 */
class MealException extends Exception
{
    /**
     * Create a new MealException instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "Meal service error", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

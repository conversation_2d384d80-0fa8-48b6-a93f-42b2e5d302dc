# Meal Service

This microservice handles meal management functionality for the application. It provides APIs for creating, retrieving, updating, and deleting meals, as well as specialized endpoints for filtering meals by menu type and food type.

## Features

- CRUD operations for meals
- Filtering meals by menu type, food type, etc.
- Meal pricing calculation
- Support for vegetarian/non-vegetarian classification
- Meal swapping functionality

## Requirements

- PHP 8.2 or higher
- Laravel 12.x
- MySQL/SQLite
- Composer 2.x

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-organization/meal-service.git
cd meal-service-v12
```

2. Install dependencies:
```bash
composer install
```

3. Copy the environment file:
```bash
cp .env.example .env
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Configure the database in the `.env` file:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=meal_service
DB_USERNAME=root
DB_PASSWORD=
```

6. Run migrations:
```bash
php artisan migrate
```

7. (Optional) Seed the database with sample data:
```bash
php artisan db:seed
```

## API Endpoints

### Base URL

```
/api/v2/meals
```

### Available Endpoints

- `GET /api/v2/meals` - Get all meals with optional filtering
- `POST /api/v2/meals` - Create a new meal
- `GET /api/v2/meals/{id}` - Get a specific meal
- `PUT /api/v2/meals/{id}` - Update a meal
- `DELETE /api/v2/meals/{id}` - Delete a meal
- `GET /api/v2/meals/menu/{menu}` - Get meals by menu type
- `GET /api/v2/meals/type/vegetarian` - Get vegetarian meals

## Running Tests

```bash
php artisan test
```

## API Documentation

The API is documented using the OpenAPI Specification (OAS) 3.0.0. The specification is available in the `openapi.yaml` file.

## Kong API Gateway Integration

This service is configured to work with Kong API Gateway. The configuration is available in the `kong.yaml` file.

## Migrated from Zend Framework

This service was migrated from the Zend Framework application. The original code was located in:
- `module/QuickServe/src/QuickServe/Model/Meal.php`
- `module/QuickServe/src/QuickServe/Model/MealTable.php`
- Various controller files that handled meal-related functionality

## Directory Structure

```
app/
├── Http/
│   └── Controllers/
│       └── Api/
│           └── MealController.php
├── Models/
│   └── Meal.php
└── Services/
    └── MealService.php
database/
├── factories/
│   └── MealFactory.php
└── migrations/
    └── 2023_05_17_000001_create_meals_table.php
routes/
└── api.php
tests/
├── Feature/
│   └── Api/
│       └── MealControllerTest.php
└── Unit/
    └── Services/
        └── MealServiceTest.php
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

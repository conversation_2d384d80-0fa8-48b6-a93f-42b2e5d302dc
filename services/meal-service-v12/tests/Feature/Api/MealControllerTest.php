<?php

namespace Tests\Feature\Api;

use App\Models\Meal;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MealControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test getting all meals.
     */
    public function testGetAllMeals(): void
    {
        // Create test meals
        Meal::factory()->count(5)->create();

        // Make request to get all meals
        $response = $this->getJson('/api/v2/meals');

        // Assert response is successful and contains the correct data
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'name',
                             'description',
                             'unit_price',
                             'food_type',
                             'category',
                         ]
                     ]
                 ])
                 ->assertJsonCount(5, 'data');
    }

    /**
     * Test getting a specific meal.
     */
    public function testGetMeal(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create();

        // Make request to get the meal
        $response = $this->getJson("/api/v2/meals/{$meal->id}");

        // Assert response is successful and contains the correct data
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         'id',
                         'name',
                         'description',
                         'unit_price',
                         'food_type',
                         'category',
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'data' => [
                         'id' => $meal->id,
                         'name' => $meal->name,
                     ]
                 ]);
    }

    /**
     * Test getting a non-existent meal.
     */
    public function testGetNonExistentMeal(): void
    {
        // Make request to get a non-existent meal
        $response = $this->getJson('/api/v2/meals/999');

        // Assert response is a 404 error
        $response->assertStatus(404)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Meal not found'
                 ]);
    }

    /**
     * Test creating a meal.
     */
    public function testCreateMeal(): void
    {
        // Create meal data
        $mealData = [
            'name' => 'Test Meal',
            'description' => 'Test Description',
            'unit_price' => 9.99,
            'food_type' => 'veg',
            'category' => 'lunch',
            'status' => true,
            'is_swappable' => false,
        ];

        // Make request to create the meal
        $response = $this->postJson('/api/v2/meals', $mealData);

        // Assert response is successful and contains the correct data
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'name',
                         'description',
                         'unit_price',
                         'food_type',
                         'category',
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Meal created successfully',
                     'data' => [
                         'name' => 'Test Meal',
                         'description' => 'Test Description',
                         'unit_price' => 9.99,
                         'food_type' => 'veg',
                         'category' => 'lunch',
                     ]
                 ]);

        // Assert the meal was actually created in the database
        $this->assertDatabaseHas('meals', [
            'name' => 'Test Meal',
            'description' => 'Test Description',
            'unit_price' => 9.99,
            'food_type' => 'veg',
            'category' => 'lunch',
        ]);
    }

    /**
     * Test updating a meal.
     */
    public function testUpdateMeal(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create();

        // Create update data
        $updateData = [
            'name' => 'Updated Meal',
            'description' => 'Updated Description',
            'unit_price' => 19.99,
        ];

        // Make request to update the meal
        $response = $this->putJson("/api/v2/meals/{$meal->id}", $updateData);

        // Assert response is successful and contains the correct data
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'name',
                         'description',
                         'unit_price',
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Meal updated successfully',
                     'data' => [
                         'id' => $meal->id,
                         'name' => 'Updated Meal',
                         'description' => 'Updated Description',
                         'unit_price' => 19.99,
                     ]
                 ]);

        // Assert the meal was actually updated in the database
        $this->assertDatabaseHas('meals', [
            'id' => $meal->id,
            'name' => 'Updated Meal',
            'description' => 'Updated Description',
            'unit_price' => 19.99,
        ]);
    }

    /**
     * Test deleting a meal.
     */
    public function testDeleteMeal(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create();

        // Make request to delete the meal
        $response = $this->deleteJson("/api/v2/meals/{$meal->id}");

        // Assert response is successful
        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Meal deleted successfully'
                 ]);

        // Assert the meal was actually deleted from the database
        $this->assertDatabaseMissing('meals', [
            'id' => $meal->id
        ]);
    }

    /**
     * Test getting meals by menu type.
     */
    public function testGetMealsByMenu(): void
    {
        // Create test meals with different menu types
        Meal::factory()->count(3)->create(['category' => 'breakfast']);
        Meal::factory()->count(2)->create(['category' => 'lunch']);
        Meal::factory()->count(4)->create(['category' => 'dinner']);

        // Make request to get breakfast meals
        $response = $this->getJson('/api/v2/meals/menu/breakfast');

        // Assert response is successful and contains only breakfast meals
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'name',
                             'category',
                         ]
                     ]
                 ])
                 ->assertJsonCount(3, 'data');

        // Verify all returned meals are breakfast meals
        $responseData = $response->json('data');
        foreach ($responseData as $meal) {
            $this->assertEquals('breakfast', $meal['category']);
        }
    }

    /**
     * Test getting vegetarian meals.
     */
    public function testGetVegetarianMeals(): void
    {
        // Create test meals with different food types
        Meal::factory()->count(3)->vegetarian()->create();
        Meal::factory()->count(2)->nonVegetarian()->create();
        Meal::factory()->count(1)->beverage()->create();

        // Make request to get vegetarian meals
        $response = $this->getJson('/api/v2/meals/type/vegetarian');

        // Assert response is successful and contains only vegetarian meals
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'name',
                             'food_type',
                         ]
                     ]
                 ])
                 ->assertJsonCount(3, 'data');

        // Verify all returned meals are vegetarian
        $responseData = $response->json('data');
        foreach ($responseData as $meal) {
            $this->assertEquals('veg', $meal['food_type']);
        }
    }
}

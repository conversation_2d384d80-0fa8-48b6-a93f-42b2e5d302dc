<?php

namespace Tests\Unit\Services;

use App\Models\Meal;
use App\Services\MealService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MealServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var MealService
     */
    protected MealService $mealService;

    /**
     * Set up the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->mealService = new MealService();
    }

    /**
     * Test getting all meals.
     */
    public function testGetAllMeals(): void
    {
        // Create test meals
        Meal::factory()->count(5)->create();

        // Get all meals
        $meals = $this->mealService->getAllMeals();

        // Assert the correct number of meals is returned
        $this->assertCount(5, $meals);
    }

    /**
     * Test getting meals with filters.
     */
    public function testGetAllMealsWithFilters(): void
    {
        // Create test meals with different attributes
        Meal::factory()->count(3)->vegetarian()->create();
        Meal::factory()->count(2)->nonVegetarian()->create();

        // Get vegetarian meals
        $meals = $this->mealService->getAllMeals(['food_type' => 'veg']);

        // Assert only vegetarian meals are returned
        $this->assertCount(3, $meals);
        foreach ($meals as $meal) {
            $this->assertEquals('veg', $meal->food_type);
        }
    }

    /**
     * Test getting a meal by ID.
     */
    public function testGetMealById(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create();

        // Get the meal by ID
        $foundMeal = $this->mealService->getMealById($meal->id);

        // Assert the correct meal is returned
        $this->assertNotNull($foundMeal);
        $this->assertEquals($meal->id, $foundMeal->id);
        $this->assertEquals($meal->name, $foundMeal->name);
    }

    /**
     * Test getting a non-existent meal by ID.
     */
    public function testGetNonExistentMealById(): void
    {
        // Get a non-existent meal by ID
        $meal = $this->mealService->getMealById(999);

        // Assert null is returned
        $this->assertNull($meal);
    }

    /**
     * Test creating a meal.
     */
    public function testCreateMeal(): void
    {
        // Create meal data
        $mealData = [
            'name' => 'Test Meal',
            'description' => 'Test Description',
            'unit_price' => 9.99,
            'food_type' => 'veg',
            'category' => 'lunch',
            'status' => true,
            'is_swappable' => false,
        ];

        // Create the meal
        $meal = $this->mealService->createMeal($mealData);

        // Assert the meal was created with the correct data
        $this->assertNotNull($meal);
        $this->assertEquals('Test Meal', $meal->name);
        $this->assertEquals('Test Description', $meal->description);
        $this->assertEquals(9.99, $meal->unit_price);
        $this->assertEquals('veg', $meal->food_type);
        $this->assertEquals('lunch', $meal->category);
        $this->assertTrue($meal->status);
        $this->assertFalse($meal->is_swappable);

        // Assert the meal was saved to the database
        $this->assertDatabaseHas('meals', [
            'name' => 'Test Meal',
            'description' => 'Test Description',
            'unit_price' => 9.99,
            'food_type' => 'veg',
            'category' => 'lunch',
        ]);
    }

    /**
     * Test updating a meal.
     */
    public function testUpdateMeal(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create();

        // Create update data
        $updateData = [
            'name' => 'Updated Meal',
            'description' => 'Updated Description',
            'unit_price' => 19.99,
        ];

        // Update the meal
        $updatedMeal = $this->mealService->updateMeal($meal->id, $updateData);

        // Assert the meal was updated with the correct data
        $this->assertNotNull($updatedMeal);
        $this->assertEquals($meal->id, $updatedMeal->id);
        $this->assertEquals('Updated Meal', $updatedMeal->name);
        $this->assertEquals('Updated Description', $updatedMeal->description);
        $this->assertEquals(19.99, $updatedMeal->unit_price);

        // Assert the meal was updated in the database
        $this->assertDatabaseHas('meals', [
            'id' => $meal->id,
            'name' => 'Updated Meal',
            'description' => 'Updated Description',
            'unit_price' => 19.99,
        ]);
    }

    /**
     * Test updating a non-existent meal.
     */
    public function testUpdateNonExistentMeal(): void
    {
        // Create update data
        $updateData = [
            'name' => 'Updated Meal',
            'description' => 'Updated Description',
            'unit_price' => 19.99,
        ];

        // Update a non-existent meal
        $updatedMeal = $this->mealService->updateMeal(999, $updateData);

        // Assert null is returned
        $this->assertNull($updatedMeal);
    }

    /**
     * Test deleting a meal.
     */
    public function testDeleteMeal(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create();

        // Delete the meal
        $result = $this->mealService->deleteMeal($meal->id);

        // Assert the meal was deleted successfully
        $this->assertTrue($result);

        // Assert the meal was deleted from the database
        $this->assertDatabaseMissing('meals', [
            'id' => $meal->id
        ]);
    }

    /**
     * Test deleting a non-existent meal.
     */
    public function testDeleteNonExistentMeal(): void
    {
        // Delete a non-existent meal
        $result = $this->mealService->deleteMeal(999);

        // Assert false is returned
        $this->assertFalse($result);
    }

    /**
     * Test getting meals by menu type.
     */
    public function testGetMealsByMenu(): void
    {
        // Create test meals with different menu types
        Meal::factory()->count(3)->create(['category' => 'breakfast']);
        Meal::factory()->count(2)->create(['category' => 'lunch']);
        Meal::factory()->count(4)->create(['category' => 'dinner']);

        // Get breakfast meals
        $meals = $this->mealService->getMealsByMenu('breakfast');

        // Assert only breakfast meals are returned
        $this->assertCount(3, $meals);
        foreach ($meals as $meal) {
            $this->assertEquals('breakfast', $meal->category);
        }
    }

    /**
     * Test getting vegetarian meals.
     */
    public function testGetVegetarianMeals(): void
    {
        // Create test meals with different food types
        Meal::factory()->count(3)->vegetarian()->create();
        Meal::factory()->count(2)->nonVegetarian()->create();
        Meal::factory()->count(1)->beverage()->create();

        // Get vegetarian meals
        $meals = $this->mealService->getVegetarianMeals();

        // Assert only vegetarian meals are returned
        $this->assertCount(3, $meals);
        foreach ($meals as $meal) {
            $this->assertEquals('veg', $meal->food_type);
        }
    }

    /**
     * Test calculating meal price.
     */
    public function testCalculateMealPrice(): void
    {
        // Create a test meal
        $meal = Meal::factory()->create([
            'unit_price' => 10.00
        ]);

        // Calculate the meal price without discount
        $price = $this->mealService->calculateMealPrice($meal);

        // Assert the price is calculated correctly
        $this->assertEquals(10.00, $price);

        // Calculate the meal price with a 20% discount
        $discountedPrice = $this->mealService->calculateMealPrice($meal, 20);

        // Assert the discounted price is calculated correctly
        $this->assertEquals(8.00, $discountedPrice);
    }
}

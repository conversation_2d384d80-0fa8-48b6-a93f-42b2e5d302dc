<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MealsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('meals')->insert([
            [
                'company_id' => 1,
                'unit_id' => 1,
                'name' => 'Vegetarian Breakfast',
                'description' => 'A healthy vegetarian breakfast.',
                'unit_price' => 5.99,
                'items' => json_encode(['bread', 'butter', 'salad']),
                'category' => 'breakfast',
                'food_type' => 'veg',
                'product_type' => 'Meal',
                'product_category' => 'Standard',
                'threshold' => 50,
                'image_path' => 'images/veg_breakfast.jpg',
                'screen' => 'Main',
                'status' => true,
                'is_swappable' => false,
                'swap_with' => null,
                'swap_charges' => null,
                'meal_plans' => 'Basic Plan',
                'is_custom' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'name' => 'Non-Vegetarian Lunch',
                'description' => 'A delicious non-vegetarian lunch.',
                'unit_price' => 10.99,
                'items' => json_encode(['chicken', 'rice', 'gravy']),
                'category' => 'lunch',
                'food_type' => 'non-veg',
                'product_type' => 'Meal',
                'product_category' => 'Premium',
                'threshold' => 30,
                'image_path' => 'images/nonveg_lunch.jpg',
                'screen' => 'Main',
                'status' => true,
                'is_swappable' => true,
                'swap_with' => 'Vegetarian Lunch',
                'swap_charges' => 2.00,
                'meal_plans' => 'Premium Plan',
                'is_custom' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}

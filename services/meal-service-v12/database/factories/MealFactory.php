<?php

namespace Database\Factories;

use App\Models\Meal;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Meal>
 */
class MealFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Meal::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $foodTypes = ['veg', 'non-veg', 'beverage'];
        $categories = ['breakfast', 'lunch', 'dinner'];
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->paragraph(),
            'unit_price' => $this->faker->randomFloat(2, 5, 50),
            'items' => null,
            'category' => $this->faker->randomElement($categories),
            'food_type' => $this->faker->randomElement($foodTypes),
            'product_type' => 'Meal',
            'product_category' => $this->faker->word(),
            'threshold' => $this->faker->numberBetween(10, 100),
            'image_path' => $this->faker->imageUrl(),
            'screen' => null,
            'status' => true,
            'is_swappable' => $this->faker->boolean(30),
            'swap_with' => null,
            'swap_charges' => $this->faker->randomFloat(2, 1, 5),
            'meal_plans' => null,
            'is_custom' => false,
        ];
    }

    /**
     * Indicate that the meal is vegetarian.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function vegetarian(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'food_type' => 'veg',
            ];
        });
    }

    /**
     * Indicate that the meal is non-vegetarian.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function nonVegetarian(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'food_type' => 'non-veg',
            ];
        });
    }

    /**
     * Indicate that the meal is a beverage.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function beverage(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'food_type' => 'beverage',
                'product_type' => 'Beverage',
            ];
        });
    }

    /**
     * Indicate that the meal is for breakfast.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function breakfast(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 'breakfast',
            ];
        });
    }

    /**
     * Indicate that the meal is for lunch.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function lunch(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 'lunch',
            ];
        });
    }

    /**
     * Indicate that the meal is for dinner.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function dinner(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 'dinner',
            ];
        });
    }

    /**
     * Indicate that the meal is swappable.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function swappable(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_swappable' => true,
                'swap_charges' => $this->faker->randomFloat(2, 1, 5),
            ];
        });
    }
}

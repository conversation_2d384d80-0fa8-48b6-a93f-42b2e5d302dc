<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->default(1);
            $table->foreignId('unit_id')->default(1);
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('unit_price', 10, 2)->default(0);
            $table->json('items')->nullable();
            $table->string('category')->nullable()->comment('menu type: breakfast, lunch, dinner');
            $table->string('food_type')->default('veg')->comment('veg, non-veg, beverage');
            $table->string('product_type')->default('Meal');
            $table->string('product_category')->nullable();
            $table->integer('threshold')->nullable()->comment('kitchen capacity');
            $table->string('image_path')->nullable();
            $table->string('screen')->nullable();
            $table->boolean('status')->default(true);
            $table->boolean('is_swappable')->default(false);
            $table->string('swap_with')->nullable();
            $table->decimal('swap_charges', 10, 2)->nullable();
            $table->string('meal_plans')->nullable();
            $table->boolean('is_custom')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meals');
    }
};

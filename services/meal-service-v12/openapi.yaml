openapi: 3.0.0
info:
  title: Meal Service API
  description: API for meal management
  version: 1.0.0
  contact:
    name: Support Team
    email: <EMAIL>

servers:
  - url: http://localhost:8000/api/v2
    description: Local development server
  - url: https://tenant.cubeonebiz.com/api/v2
    description: Production server

components:
  schemas:
    Meal:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier for the meal
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        name:
          type: string
          description: Name of the meal
        description:
          type: string
          description: Description of the meal
        unit_price:
          type: number
          format: float
          description: Price of the meal
        items:
          type: object
          description: JSON object containing meal items
        category:
          type: string
          description: Menu type (breakfast, lunch, dinner)
        food_type:
          type: string
          description: Food type (veg, non-veg, beverage)
        product_type:
          type: string
          description: Product type
        product_category:
          type: string
          description: Product category
        threshold:
          type: integer
          description: Kitchen capacity
        image_path:
          type: string
          description: Path to the meal image
        screen:
          type: string
          description: Screen where the meal is displayed
        status:
          type: boolean
          description: Whether the meal is active
        is_swappable:
          type: boolean
          description: Whether the meal can be swapped
        swap_with:
          type: string
          description: Meals that can be swapped with this meal
        swap_charges:
          type: number
          format: float
          description: Charges for swapping the meal
        meal_plans:
          type: string
          description: Meal plans that include this meal
        is_custom:
          type: boolean
          description: Whether the meal is custom
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
      required:
        - name
        - unit_price
        - food_type

    MealInput:
      type: object
      properties:
        name:
          type: string
          description: Name of the meal
        description:
          type: string
          description: Description of the meal
        unit_price:
          type: number
          format: float
          description: Price of the meal
        items:
          type: object
          description: JSON object containing meal items
        category:
          type: string
          description: Menu type (breakfast, lunch, dinner)
        food_type:
          type: string
          description: Food type (veg, non-veg, beverage)
          enum: [veg, non-veg, beverage]
        product_type:
          type: string
          description: Product type
        product_category:
          type: string
          description: Product category
        threshold:
          type: integer
          description: Kitchen capacity
        image_path:
          type: string
          description: Path to the meal image
        screen:
          type: string
          description: Screen where the meal is displayed
        status:
          type: boolean
          description: Whether the meal is active
        is_swappable:
          type: boolean
          description: Whether the meal can be swapped
        swap_with:
          type: string
          description: Meals that can be swapped with this meal
        swap_charges:
          type: number
          format: float
          description: Charges for swapping the meal
        meal_plans:
          type: string
          description: Meal plans that include this meal
        is_custom:
          type: boolean
          description: Whether the meal is custom
      required:
        - name
        - unit_price
        - food_type

    Error:
      type: object
      properties:
        status:
          type: string
          description: Error status
          example: error
        message:
          type: string
          description: Error message
          example: Meal not found

paths:
  /meals:
    get:
      summary: Get all meals
      description: Returns a list of all meals with optional filtering
      operationId: getAllMeals
      parameters:
        - name: menu
          in: query
          description: Filter by menu type
          schema:
            type: string
        - name: food_type
          in: query
          description: Filter by food type
          schema:
            type: string
            enum: [veg, non-veg, beverage]
        - name: product_category
          in: query
          description: Filter by product category
          schema:
            type: string
        - name: active
          in: query
          description: Filter by active status
          schema:
            type: boolean
        - name: search
          in: query
          description: Search term
          schema:
            type: string
        - name: sort_by
          in: query
          description: Field to sort by
          schema:
            type: string
            default: name
        - name: sort_direction
          in: query
          description: Sort direction
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Meal'
    post:
      summary: Create a new meal
      description: Creates a new meal
      operationId: createMeal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MealInput'
      responses:
        '201':
          description: Meal created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Meal created successfully
                  data:
                    $ref: '#/components/schemas/Meal'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /meals/{id}:
    get:
      summary: Get a meal by ID
      description: Returns a single meal
      operationId: getMealById
      parameters:
        - name: id
          in: path
          description: ID of the meal to return
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Meal'
        '404':
          description: Meal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update a meal
      description: Updates an existing meal
      operationId: updateMeal
      parameters:
        - name: id
          in: path
          description: ID of the meal to update
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MealInput'
      responses:
        '200':
          description: Meal updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Meal updated successfully
                  data:
                    $ref: '#/components/schemas/Meal'
        '404':
          description: Meal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    delete:
      summary: Delete a meal
      description: Deletes an existing meal
      operationId: deleteMeal
      parameters:
        - name: id
          in: path
          description: ID of the meal to delete
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Meal deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Meal deleted successfully
        '404':
          description: Meal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /meals/menu/{menu}:
    get:
      summary: Get meals by menu type
      description: Returns meals for a specific menu type
      operationId: getMealsByMenu
      parameters:
        - name: menu
          in: path
          description: Menu type (breakfast, lunch, dinner)
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Meal'

  /meals/type/vegetarian:
    get:
      summary: Get vegetarian meals
      description: Returns all vegetarian meals
      operationId: getVegetarianMeals
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Meal'

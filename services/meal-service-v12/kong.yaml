_format_version: "2.1"
_transform: true

services:
  - name: meal-service
    url: http://meal-service:8000
    routes:
      - name: meal-service-route
        paths:
          - /api/v2/meals
          - /api/v2/meals/menu
          - /api/v2/meals/type
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
      - name: meal-service-id-route
        paths:
          - /api/v2/meals/(?<id>\d+)
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: meal-service

consumers:
  - username: meal-service-consumer
    keyauth_credentials:
      - key: your-api-key-here
    acls:
      - group: meal-service-group
      
  - username: admin-consumer
    keyauth_credentials:
      - key: admin-api-key-here
    acls:
      - group: admin-group

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Meal extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'meals';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'name',
        'description',
        'unit_price',
        'items',
        'category',
        'food_type',
        'product_type',
        'product_category',
        'threshold',
        'image_path',
        'screen',
        'status',
        'is_swappable',
        'swap_with',
        'swap_charges',
        'meal_plans',
        'is_custom'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'items' => 'array',
        'status' => 'boolean',
        'is_swappable' => 'boolean',
        'swap_charges' => 'decimal:2',
        'is_custom' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the meal's items as a string.
     */
    public function getItemsAsString(): string
    {
        if (empty($this->items)) {
            return '';
        }

        $itemIds = array_keys($this->items);
        $items = self::whereIn('id', $itemIds)->get();

        $result = [];
        foreach ($items as $item) {
            $quantity = $this->items[$item->id] ?? 1;
            $result[] = "{$item->name} (x{$quantity})";
        }

        return implode(', ', $result);
    }

    /**
     * Scope a query to only include active meals.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include vegetarian meals.
     */
    public function scopeVegetarian(Builder $query): Builder
    {
        return $query->where('food_type', 'veg');
    }

    /**
     * Scope a query to only include meals for a specific menu.
     */
    public function scopeForMenu(Builder $query, string $menu): Builder
    {
        return $query->where('category', $menu);
    }

    /**
     * Check if the meal is vegetarian.
     */
    public function isVegetarian(): bool
    {
        return $this->food_type === 'veg';
    }

    /**
     * Check if the meal is swappable.
     */
    public function isSwappable(): bool
    {
        return $this->is_swappable;
    }

    /**
     * Get the total price including items.
     */
    public function getTotalPrice(): float
    {
        $total = $this->unit_price;

        if (!empty($this->items)) {
            $itemIds = array_keys($this->items);
            $items = self::whereIn('id', $itemIds)->get();

            foreach ($items as $item) {
                $quantity = $this->items[$item->id] ?? 1;
                $total += $item->unit_price * $quantity;
            }
        }

        return $total;
    }
}

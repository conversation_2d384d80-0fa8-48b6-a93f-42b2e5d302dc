<?php

namespace App\Http\Controllers\Api;

use App\Models\Meal;
use App\Services\MealService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Exception;

class MealController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected MealService $mealService
    ) {}

    /**
     * Display a listing of the meals.
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only([
            'menu', 'food_type', 'product_category', 'active', 
            'search', 'sort_by', 'sort_direction'
        ]);

        $meals = $this->mealService->getAllMeals($filters);

        return response()->json([
            'status' => 'success',
            'data' => $meals
        ]);
    }

    /**
     * Store a newly created meal in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'unit_price' => 'required|numeric|min:0',
                'items' => 'nullable|array',
                'category' => 'nullable|string|max:255',
                'food_type' => 'required|string|in:veg,non-veg,beverage',
                'product_category' => 'nullable|string|max:255',
                'threshold' => 'nullable|integer|min:0',
                'image_path' => 'nullable|string|max:255',
                'screen' => 'nullable|string|max:255',
                'status' => 'boolean',
                'is_swappable' => 'boolean',
                'swap_with' => 'nullable|string|max:255',
                'swap_charges' => 'nullable|numeric|min:0',
                'meal_plans' => 'nullable|string',
                'is_custom' => 'boolean'
            ]);

            $meal = $this->mealService->createMeal($validatedData);

            return response()->json([
                'status' => 'success',
                'message' => 'Meal created successfully',
                'data' => $meal
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An unexpected error occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified meal.
     */
    public function show(int $id): JsonResponse
    {
        $meal = $this->mealService->getMealById($id);

        if (!$meal) {
            return response()->json([
                'status' => 'error',
                'message' => 'Meal not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $meal
        ]);
    }

    /**
     * Update the specified meal in storage.
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'string|max:255',
            'description' => 'nullable|string',
            'unit_price' => 'numeric|min:0',
            'items' => 'nullable|json',
            'category' => 'nullable|string|max:255',
            'food_type' => 'string|in:veg,non-veg,beverage',
            'product_category' => 'nullable|string|max:255',
            'threshold' => 'nullable|integer|min:0',
            'image_path' => 'nullable|string|max:255',
            'screen' => 'nullable|string|max:255',
            'status' => 'boolean',
            'is_swappable' => 'boolean',
            'swap_with' => 'nullable|string|max:255',
            'swap_charges' => 'nullable|numeric|min:0',
            'meal_plans' => 'nullable|string',
            'is_custom' => 'boolean'
        ]);

        $meal = $this->mealService->updateMeal($id, $validatedData);

        if (!$meal) {
            return response()->json([
                'status' => 'error',
                'message' => 'Meal not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Meal updated successfully',
            'data' => $meal
        ]);
    }

    /**
     * Remove the specified meal from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $deleted = $this->mealService->deleteMeal($id);

        if (!$deleted) {
            return response()->json([
                'status' => 'error',
                'message' => 'Meal not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Meal deleted successfully'
        ]);
    }

    /**
     * Get meals by menu type.
     */
    public function getByMenu(string $menu): JsonResponse
    {
        $meals = $this->mealService->getMealsByMenu($menu);

        return response()->json([
            'status' => 'success',
            'data' => $meals
        ]);
    }

    /**
     * Get vegetarian meals.
     */
    public function getVegetarian(): JsonResponse
    {
        $meals = $this->mealService->getVegetarianMeals();

        return response()->json([
            'status' => 'success',
            'data' => $meals
        ]);
    }

    /**
     * Get meals by food type.
     *
     * @param string $foodType
     * @return JsonResponse
     */
    public function getByFoodType(string $foodType): JsonResponse
    {
        $meals = $this->mealService->getAllMeals(['food_type' => $foodType]);

        return response()->json([
            'status' => 'success',
            'data' => $meals,
        ]);
    }
}

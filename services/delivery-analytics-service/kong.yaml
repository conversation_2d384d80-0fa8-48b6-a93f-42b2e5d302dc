services:
  - name: delivery-analytics-service
    url: http://delivery-analytics-service:80
    routes:
      - name: delivery-analytics-service-route
        paths:
          - /api/v2/delivery-analytics
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
          run_on_preflight: true
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service-Name:delivery-analytics-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service-Version:1.0.0
      - name: http-log
        config:
          http_endpoint: http://logging-service:8000/api/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          headers:
            X-Service-Name: delivery-analytics-service
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          upstream_health_metrics: true
          bandwidth_metrics: true
      - name: acl
        config:
          allow:
            - admin
            - delivery
            - analytics
      - name: key-auth
        config:
          key_names:
            - apikey
          key_in_body: false
          hide_credentials: true

openapi: 3.1.0
info:
  title: Delivery Analytics Service API
  description: API for the Delivery Analytics Service
  version: 1.0.0
  contact:
    name: FoodDialer Support
    email: <EMAIL>
servers:
  - url: https://api.fooddialer.com/api/v2
    description: Production server
  - url: https://staging-api.fooddialer.com/api/v2
    description: Staging server
  - url: http://localhost:8000/api/v2
    description: Local development server
paths:
  /delivery-analytics/performance:
    get:
      summary: Get delivery performance report
      description: Retrieves a comprehensive delivery performance report
      operationId: getDeliveryPerformanceReport
      tags:
        - Delivery Performance
      parameters:
        - name: start_date
          in: query
          description: Start date for the report (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: End date for the report (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: location_id
          in: query
          description: Filter by location ID
          schema:
            type: integer
        - name: meal_type
          in: query
          description: Filter by meal type (lunch, dinner, breakfast, all)
          schema:
            type: string
            enum: [lunch, dinner, breakfast, all]
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryPerformanceReport'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /delivery-analytics/performance/daily:
    get:
      summary: Get daily delivery performance metrics
      description: Retrieves daily delivery performance metrics
      operationId: getDailyDeliveryPerformanceMetrics
      tags:
        - Delivery Performance
      parameters:
        - name: start_date
          in: query
          description: Start date (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: End date (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: location_id
          in: query
          description: Filter by location ID
          schema:
            type: integer
        - name: meal_type
          in: query
          description: Filter by meal type (lunch, dinner, breakfast, all)
          schema:
            type: string
            enum: [lunch, dinner, breakfast, all]
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DeliveryPerformanceMetric'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
components:
  schemas:
    DeliveryPerformanceReport:
      type: object
      properties:
        message:
          type: string
          example: Delivery performance report retrieved successfully
        data:
          type: object
          properties:
            summary:
              type: object
              properties:
                total_deliveries:
                  type: integer
                  example: 1250
                on_time_deliveries:
                  type: integer
                  example: 1100
                late_deliveries:
                  type: integer
                  example: 125
                failed_deliveries:
                  type: integer
                  example: 25
                on_time_percentage:
                  type: number
                  format: float
                  example: 88.0
                late_percentage:
                  type: number
                  format: float
                  example: 10.0
                failed_percentage:
                  type: number
                  format: float
                  example: 2.0
                avg_delivery_time:
                  type: integer
                  example: 1800
                avg_distance:
                  type: number
                  format: float
                  example: 5.2
                avg_delivery_cost:
                  type: number
                  format: float
                  example: 75.5
            daily_metrics:
              type: array
              items:
                $ref: '#/components/schemas/DeliveryPerformanceMetric'
            location_metrics:
              type: array
              items:
                $ref: '#/components/schemas/LocationPerformanceMetric'
            meal_type_metrics:
              type: array
              items:
                $ref: '#/components/schemas/MealTypePerformanceMetric'
    DeliveryPerformanceMetric:
      type: object
      properties:
        id:
          type: integer
          example: 1
        date:
          type: string
          format: date
          example: 2023-10-15
        location_id:
          type: integer
          nullable: true
          example: 5
        meal_type:
          type: string
          nullable: true
          example: lunch
        total_deliveries:
          type: integer
          example: 150
        on_time_deliveries:
          type: integer
          example: 135
        late_deliveries:
          type: integer
          example: 12
        failed_deliveries:
          type: integer
          example: 3
        avg_delivery_time:
          type: integer
          example: 1750
        avg_delivery_time_formatted:
          type: string
          example: 29:10
        avg_distance:
          type: number
          format: float
          example: 4.8
        avg_delivery_cost:
          type: number
          format: float
          example: 72.5
        on_time_percentage:
          type: number
          format: float
          example: 90.0
        late_percentage:
          type: number
          format: float
          example: 8.0
        failed_percentage:
          type: number
          format: float
          example: 2.0
    LocationPerformanceMetric:
      type: object
      properties:
        location_id:
          type: integer
          example: 5
        total_deliveries:
          type: integer
          example: 450
        on_time_deliveries:
          type: integer
          example: 405
        late_deliveries:
          type: integer
          example: 36
        failed_deliveries:
          type: integer
          example: 9
        avg_delivery_time:
          type: integer
          example: 1800
        avg_distance:
          type: number
          format: float
          example: 5.2
        avg_delivery_cost:
          type: number
          format: float
          example: 75.5
    MealTypePerformanceMetric:
      type: object
      properties:
        meal_type:
          type: string
          example: lunch
        total_deliveries:
          type: integer
          example: 750
        on_time_deliveries:
          type: integer
          example: 675
        late_deliveries:
          type: integer
          example: 60
        failed_deliveries:
          type: integer
          example: 15
        avg_delivery_time:
          type: integer
          example: 1700
        avg_distance:
          type: number
          format: float
          example: 4.9
        avg_delivery_cost:
          type: number
          format: float
          example: 73.2
  responses:
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: Unauthenticated
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: You do not have permission to access this resource
    ServerError:
      description: Server Error
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: Internal server error
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

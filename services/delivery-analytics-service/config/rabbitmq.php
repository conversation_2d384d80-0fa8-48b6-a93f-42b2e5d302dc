<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for RabbitMQ.
    |
    */
    
    // RabbitMQ connection settings
    'host' => env('RABBITMQ_HOST', 'localhost'),
    'port' => env('RABBITMQ_PORT', 5672),
    'user' => env('RABBITMQ_USER', 'guest'),
    'password' => env('RABBITMQ_PASSWORD', 'guest'),
    'vhost' => env('RABBITMQ_VHOST', '/'),
    
    // Exchange settings
    'exchange' => env('RABBITMQ_EXCHANGE', 'fooddialer'),
    'exchange_type' => env('RABBITMQ_EXCHANGE_TYPE', 'topic'),
    
    // Queue settings
    'queues' => [
        'delivery_analytics_events' => [
            'name' => env('RABBITMQ_DELIVERY_ANALYTICS_QUEUE', 'delivery_analytics_events'),
            'routing_key' => 'delivery.#',
            'durable' => true,
            'auto_delete' => false
        ],
        'order_events' => [
            'name' => env('RABBITMQ_ORDER_QUEUE', 'order_events'),
            'routing_key' => 'order.#',
            'durable' => true,
            'auto_delete' => false
        ],
        'customer_feedback_events' => [
            'name' => env('RABBITMQ_CUSTOMER_FEEDBACK_QUEUE', 'customer_feedback_events'),
            'routing_key' => 'customer.feedback.#',
            'durable' => true,
            'auto_delete' => false
        ]
    ],
    
    // Routing keys
    'routing_keys' => [
        'order_delivered' => 'order.delivered',
        'delivery_assigned' => 'delivery.assigned',
        'delivery_status_changed' => 'delivery.status_changed',
        'customer_feedback' => 'customer.feedback',
        'delivery_completed' => 'delivery.completed',
        'delivery_delayed' => 'delivery.delayed'
    ],
    
    // Dead letter exchange
    'dead_letter_exchange' => env('RABBITMQ_DEAD_LETTER_EXCHANGE', 'fooddialer.dlx'),
    'dead_letter_queue' => env('RABBITMQ_DEAD_LETTER_QUEUE', 'delivery_analytics_dlq'),
    
    // Consumer settings
    'consumer' => [
        'prefetch_count' => env('RABBITMQ_CONSUMER_PREFETCH_COUNT', 10),
        'timeout' => env('RABBITMQ_CONSUMER_TIMEOUT', 3),
        'sleep_on_error' => env('RABBITMQ_CONSUMER_SLEEP_ON_ERROR', 5),
        'max_retries' => env('RABBITMQ_CONSUMER_MAX_RETRIES', 3)
    ]
];

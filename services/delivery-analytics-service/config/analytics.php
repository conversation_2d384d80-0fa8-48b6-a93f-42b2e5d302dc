<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Delivery Analytics Service.
    |
    */
    
    // Cache settings
    'cache' => [
        'enabled' => env('ANALYTICS_CACHE_ENABLED', true),
        'ttl' => env('ANALYTICS_CACHE_TTL', 3600), // 1 hour
        'prefix' => env('ANALYTICS_CACHE_PREFIX', 'delivery_analytics:'),
    ],
    
    // Performance metrics settings
    'performance' => [
        'on_time_threshold' => env('ANALYTICS_ON_TIME_THRESHOLD', 15), // 15 minutes
        'late_threshold' => env('ANALYTICS_LATE_THRESHOLD', 30), // 30 minutes
        'default_date_range' => env('ANALYTICS_DEFAULT_DATE_RANGE', 30), // 30 days
    ],
    
    // Staff efficiency settings
    'staff_efficiency' => [
        'min_deliveries_for_ranking' => env('ANALYTICS_MIN_DELIVERIES_FOR_RANKING', 10),
        'efficiency_factors' => [
            'on_time_percentage' => 0.4,
            'avg_delivery_time' => 0.3,
            'customer_rating' => 0.3,
        ],
    ],
    
    // Geographic analysis settings
    'geographic' => [
        'default_center_lat' => env('DEFAULT_MAP_LAT', 19.0760), // Mumbai
        'default_center_lon' => env('DEFAULT_MAP_LON', 72.8777), // Mumbai
        'default_zoom' => env('DEFAULT_MAP_ZOOM', 12),
        'heatmap_radius' => env('ANALYTICS_HEATMAP_RADIUS', 25),
        'zone_radius_km' => env('ZONE_RADIUS_KM', 5),
    ],
    
    // Mumbai Dabbawala settings
    'dabbawala' => [
        'code_prefix' => env('DABBAWALA_CODE_PREFIX', 'MD-'),
        'efficiency_threshold' => env('DABBAWALA_EFFICIENCY_THRESHOLD', 95), // 95%
    ],
    
    // Customer satisfaction settings
    'customer_satisfaction' => [
        'rating_threshold_good' => env('ANALYTICS_RATING_THRESHOLD_GOOD', 4.5),
        'rating_threshold_average' => env('ANALYTICS_RATING_THRESHOLD_AVERAGE', 3.5),
        'rating_threshold_poor' => env('ANALYTICS_RATING_THRESHOLD_POOR', 2.5),
    ],
    
    // Report generation settings
    'reports' => [
        'daily_report_time' => env('ANALYTICS_DAILY_REPORT_TIME', '00:00'),
        'weekly_report_day' => env('ANALYTICS_WEEKLY_REPORT_DAY', 'monday'),
        'monthly_report_day' => env('ANALYTICS_MONTHLY_REPORT_DAY', 1),
    ],
    
    // Data aggregation settings
    'aggregation' => [
        'daily_retention_days' => env('ANALYTICS_DAILY_RETENTION_DAYS', 90), // 3 months
        'weekly_retention_weeks' => env('ANALYTICS_WEEKLY_RETENTION_WEEKS', 52), // 1 year
        'monthly_retention_months' => env('ANALYTICS_MONTHLY_RETENTION_MONTHS', 36), // 3 years
    ],
];

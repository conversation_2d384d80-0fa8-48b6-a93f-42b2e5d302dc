<?php

use App\Http\Controllers\Api\DeliveryPerformanceController;
use App\Http\Controllers\Api\StaffEfficiencyController;
use App\Http\Controllers\Api\GeographicAnalysisController;
use App\Http\Controllers\Api\DabbawalaAnalyticsController;
use App\Http\Controllers\Api\CustomerSatisfactionController;
use App\Http\Controllers\Api\AnalyticsDashboardController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// API v2 routes
Route::prefix('v2/delivery-analytics')->middleware(['auth:sanctum'])->group(function () {
    // Delivery Performance Routes
    Route::prefix('performance')->group(function () {
        Route::get('/', [DeliveryPerformanceController::class, 'getDeliveryPerformanceReport']);
        Route::get('/daily', [DeliveryPerformanceController::class, 'getDailyDeliveryPerformanceMetrics']);
        Route::get('/monthly', [DeliveryPerformanceController::class, 'getMonthlyDeliveryPerformanceMetrics']);
        Route::get('/by-location', [DeliveryPerformanceController::class, 'getDeliveryPerformanceByLocation']);
        Route::get('/by-meal-type', [DeliveryPerformanceController::class, 'getDeliveryPerformanceByMealType']);
    });

    // Staff Efficiency Routes
    Route::prefix('staff')->group(function () {
        Route::get('/', [StaffEfficiencyController::class, 'getStaffEfficiencyReport']);
        Route::get('/{id}', [StaffEfficiencyController::class, 'getStaffEfficiencyById']);
        Route::get('/rankings', [StaffEfficiencyController::class, 'getStaffRankings']);
        Route::get('/performance-trends', [StaffEfficiencyController::class, 'getStaffPerformanceTrends']);
    });

    // Geographic Analysis Routes
    Route::prefix('geographic')->group(function () {
        Route::get('/', [GeographicAnalysisController::class, 'getGeographicDeliveryDistribution']);
        Route::get('/heatmap', [GeographicAnalysisController::class, 'getDeliveryDensityHeatmap']);
        Route::get('/zones', [GeographicAnalysisController::class, 'getDeliveryMetricsByZone']);
        Route::get('/postal-codes', [GeographicAnalysisController::class, 'getDeliveryMetricsByPostalCode']);
    });

    // Dabbawala Analytics Routes
    Route::prefix('dabbawala')->group(function () {
        Route::get('/', [DabbawalaAnalyticsController::class, 'getDabbawalaDeliveryMetrics']);
        Route::get('/{code}', [DabbawalaAnalyticsController::class, 'getDabbawalaMetricsByCode']);
        Route::get('/efficiency', [DabbawalaAnalyticsController::class, 'getDabbawalaEfficiencyMetrics']);
        Route::get('/routes', [DabbawalaAnalyticsController::class, 'getDabbawalaRouteAnalytics']);
    });

    // Customer Satisfaction Routes
    Route::prefix('satisfaction')->group(function () {
        Route::get('/', [CustomerSatisfactionController::class, 'getCustomerSatisfactionMetrics']);
        Route::get('/by-customer/{id}', [CustomerSatisfactionController::class, 'getCustomerSatisfactionById']);
        Route::get('/ratings', [CustomerSatisfactionController::class, 'getDeliveryRatingAnalytics']);
        Route::get('/issues', [CustomerSatisfactionController::class, 'getDeliveryIssueAnalytics']);
    });

    // Dashboard Routes
    Route::prefix('dashboard')->group(function () {
        Route::get('/', [AnalyticsDashboardController::class, 'getDashboardData']);
        Route::get('/summary', [AnalyticsDashboardController::class, 'getSummaryMetrics']);
        Route::get('/trends', [AnalyticsDashboardController::class, 'getTrendData']);
    });
});

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->unsignedBigInteger('location_id')->nullable();
            $table->string('meal_type', 50)->nullable();
            $table->unsignedInteger('total_deliveries')->default(0);
            $table->unsignedInteger('on_time_deliveries')->default(0);
            $table->unsignedInteger('late_deliveries')->default(0);
            $table->unsignedInteger('failed_deliveries')->default(0);
            $table->unsignedInteger('avg_delivery_time')->nullable()->comment('Average delivery time in seconds');
            $table->decimal('avg_distance', 10, 2)->nullable()->comment('Average delivery distance in kilometers');
            $table->decimal('avg_delivery_cost', 10, 2)->nullable()->comment('Average delivery cost');
            $table->timestamps();
            
            $table->index('date');
            $table->index('location_id');
            $table->index('meal_type');
            $table->unique(['date', 'location_id', 'meal_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_performance_metrics');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('geographic_delivery_analytics', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->unsignedBigInteger('zone_id')->nullable();
            $table->string('postal_code', 20)->nullable();
            $table->string('area_name', 100)->nullable();
            $table->unsignedInteger('total_deliveries')->default(0);
            $table->unsignedInteger('avg_delivery_time')->nullable()->comment('Average delivery time in seconds');
            $table->decimal('avg_distance', 10, 2)->nullable()->comment('Average delivery distance in kilometers');
            $table->decimal('avg_delivery_cost', 10, 2)->nullable()->comment('Average delivery cost');
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->timestamps();
            
            $table->index('date');
            $table->index('zone_id');
            $table->index('postal_code');
            $table->unique(['date', 'zone_id', 'postal_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geographic_delivery_analytics');
    }
};

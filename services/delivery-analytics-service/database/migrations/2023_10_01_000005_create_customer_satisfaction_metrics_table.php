<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_satisfaction_metrics', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('delivery_type', 50)->nullable()->comment('Regular, Dabbawala, etc.');
            $table->unsignedInteger('total_deliveries')->default(0);
            $table->decimal('avg_rating', 3, 2)->nullable()->comment('Average delivery rating');
            $table->unsignedInteger('total_ratings')->default(0);
            $table->decimal('on_time_percentage', 5, 2)->nullable()->comment('Percentage of on-time deliveries');
            $table->unsignedInteger('issue_count')->default(0);
            $table->timestamps();
            
            $table->index('date');
            $table->index('customer_id');
            $table->index('delivery_type');
            $table->unique(['date', 'customer_id', 'delivery_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_satisfaction_metrics');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('staff_efficiency_metrics', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->unsignedBigInteger('delivery_person_id');
            $table->unsignedInteger('total_deliveries')->default(0);
            $table->unsignedInteger('on_time_deliveries')->default(0);
            $table->unsignedInteger('late_deliveries')->default(0);
            $table->unsignedInteger('failed_deliveries')->default(0);
            $table->unsignedInteger('avg_delivery_time')->nullable()->comment('Average delivery time in seconds');
            $table->decimal('avg_distance', 10, 2)->nullable()->comment('Average delivery distance in kilometers');
            $table->decimal('avg_rating', 3, 2)->nullable()->comment('Average customer rating');
            $table->unsignedInteger('total_ratings')->default(0);
            $table->decimal('efficiency_score', 5, 2)->nullable()->comment('Overall efficiency score');
            $table->timestamps();
            
            $table->index('date');
            $table->index('delivery_person_id');
            $table->unique(['date', 'delivery_person_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('staff_efficiency_metrics');
    }
};

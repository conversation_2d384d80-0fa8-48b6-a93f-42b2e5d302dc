# Delivery Analytics Service

This microservice handles delivery analytics functionality for the FoodDialer application. It provides RESTful APIs for delivery performance metrics, staff efficiency reports, geographic delivery distribution analysis, Mumbai Dabbawala specific analytics, and customer satisfaction metrics.

## Overview

The Delivery Analytics Service is part of the microservices architecture migration from the legacy Zend Framework application. It follows modern best practices including:

- Clean architecture with separation of concerns
- Repository pattern for data access
- Service layer for business logic
- RESTful API design
- Comprehensive testing
- OpenAPI documentation
- Kong API Gateway integration

## Features

### Delivery Performance Metrics
- Average delivery time calculation
- On-time delivery percentage metrics
- Delivery success rate analytics
- Delivery cost efficiency metrics

### Staff Efficiency Reports
- Staff performance dashboards
- Delivery volume per staff member analytics
- Staff rating and feedback analysis
- Staff efficiency comparison tools

### Geographic Delivery Distribution Analysis
- Heat maps for delivery density
- Zone-based delivery analytics
- Distance and time analysis by geographic area
- Delivery cost analysis by location

### Mumbai Dabbawala Specific Analytics
- Dabbawala code tracking and analytics
- Dabbawala delivery efficiency metrics
- Dabbawala route optimization analytics
- Dabbawala-specific customer satisfaction metrics

### Customer Satisfaction Metrics
- Delivery rating analysis
- Delivery time satisfaction metrics
- Delivery issue tracking and analysis
- Customer feedback analysis for deliveries

## Requirements

- PHP 8.1+
- Composer
- MySQL 8.0+
- RabbitMQ 3.x
- Redis 6.x
- Docker (optional)

## Installation

### Using Docker

1. Clone the repository
2. Navigate to the project directory
3. Copy the `.env.example` file to `.env`
4. Run the following commands:

```bash
docker-compose up -d
docker-compose exec app composer install
docker-compose exec app php artisan key:generate
docker-compose exec app php artisan migrate
docker-compose exec app php artisan db:seed
```

### Manual Installation

1. Clone the repository
2. Navigate to the project directory
3. Copy the `.env.example` file to `.env`
4. Run the following commands:

```bash
composer install
php artisan key:generate
php artisan migrate
php artisan db:seed
```

## API Documentation

The API documentation is available in OpenAPI 3.1 format. You can view it by:

1. Running the service
2. Visiting `/api/documentation` in your browser

Alternatively, you can view the OpenAPI specification directly in the `openapi.yaml` file.

## Testing

To run the tests, use the following command:

```bash
php artisan test
```

For code coverage reports:

```bash
php artisan test --coverage
```

## Event-Driven Architecture

This service uses RabbitMQ for event-driven communication with other microservices. It listens for the following events:

- `order.delivered` - When an order is delivered
- `delivery.assigned` - When a delivery is assigned to a delivery person
- `delivery.status_changed` - When the status of a delivery changes
- `customer.feedback` - When a customer provides feedback for a delivery

## Kong API Gateway Integration

The service is integrated with Kong API Gateway. The configuration is available in the `kong.yaml` file.

## Monitoring

The service exposes Prometheus metrics at the `/metrics` endpoint. It also logs all API requests and responses to the logging service.

## License

This project is licensed under the MIT License.

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\DeliveryPerformanceRequest;
use App\Http\Resources\DeliveryPerformanceResource;
use App\Services\Contracts\DeliveryPerformanceServiceInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class DeliveryPerformanceController extends Controller
{
    /**
     * The delivery performance service instance.
     *
     * @var DeliveryPerformanceServiceInterface
     */
    protected DeliveryPerformanceServiceInterface $deliveryPerformanceService;

    /**
     * Create a new controller instance.
     *
     * @param DeliveryPerformanceServiceInterface $deliveryPerformanceService
     */
    public function __construct(DeliveryPerformanceServiceInterface $deliveryPerformanceService)
    {
        $this->deliveryPerformanceService = $deliveryPerformanceService;
    }

    /**
     * Get delivery performance report.
     *
     * @param DeliveryPerformanceRequest $request
     * @return JsonResponse
     */
    public function getDeliveryPerformanceReport(DeliveryPerformanceRequest $request): JsonResponse
    {
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $locationId = $request->input('location_id');
        $mealType = $request->input('meal_type');

        $report = $this->deliveryPerformanceService->getDeliveryPerformanceReport(
            $startDate,
            $endDate,
            $locationId,
            $mealType
        );

        return response()->json([
            'message' => 'Delivery performance report retrieved successfully',
            'data' => $report,
        ]);
    }

    /**
     * Get daily delivery performance metrics.
     *
     * @param DeliveryPerformanceRequest $request
     * @return AnonymousResourceCollection
     */
    public function getDailyDeliveryPerformanceMetrics(DeliveryPerformanceRequest $request): AnonymousResourceCollection
    {
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $locationId = $request->input('location_id');
        $mealType = $request->input('meal_type');

        $metrics = $this->deliveryPerformanceService->getDailyDeliveryPerformanceMetrics(
            $startDate,
            $endDate,
            $locationId,
            $mealType
        );

        return DeliveryPerformanceResource::collection($metrics);
    }

    /**
     * Get monthly delivery performance metrics.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function getMonthlyDeliveryPerformanceMetrics(Request $request): AnonymousResourceCollection
    {
        $year = $request->input('year', now()->year);
        $month = $request->input('month');
        $locationId = $request->input('location_id');
        $mealType = $request->input('meal_type');

        $metrics = $this->deliveryPerformanceService->getMonthlyDeliveryPerformanceMetrics(
            $year,
            $month,
            $locationId,
            $mealType
        );

        return DeliveryPerformanceResource::collection($metrics);
    }

    /**
     * Get delivery performance metrics by location.
     *
     * @param DeliveryPerformanceRequest $request
     * @return AnonymousResourceCollection
     */
    public function getDeliveryPerformanceByLocation(DeliveryPerformanceRequest $request): AnonymousResourceCollection
    {
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $mealType = $request->input('meal_type');

        $metrics = $this->deliveryPerformanceService->getDeliveryPerformanceByLocation(
            $startDate,
            $endDate,
            $mealType
        );

        return DeliveryPerformanceResource::collection($metrics);
    }

    /**
     * Get delivery performance metrics by meal type.
     *
     * @param DeliveryPerformanceRequest $request
     * @return AnonymousResourceCollection
     */
    public function getDeliveryPerformanceByMealType(DeliveryPerformanceRequest $request): AnonymousResourceCollection
    {
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $locationId = $request->input('location_id');

        $metrics = $this->deliveryPerformanceService->getDeliveryPerformanceByMealType(
            $startDate,
            $endDate,
            $locationId
        );

        return DeliveryPerformanceResource::collection($metrics);
    }
}

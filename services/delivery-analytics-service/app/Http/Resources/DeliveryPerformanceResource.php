<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryPerformanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'date' => $this->date,
            'location_id' => $this->location_id,
            'meal_type' => $this->meal_type,
            'total_deliveries' => $this->total_deliveries,
            'on_time_deliveries' => $this->on_time_deliveries,
            'late_deliveries' => $this->late_deliveries,
            'failed_deliveries' => $this->failed_deliveries,
            'avg_delivery_time' => $this->avg_delivery_time,
            'avg_delivery_time_formatted' => $this->formatTime($this->avg_delivery_time),
            'avg_distance' => $this->avg_distance,
            'avg_delivery_cost' => $this->avg_delivery_cost,
            'on_time_percentage' => $this->getOnTimePercentageAttribute(),
            'late_percentage' => $this->getLatePercentageAttribute(),
            'failed_percentage' => $this->getFailedPercentageAttribute(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Format time in seconds to a human-readable format.
     *
     * @param int|null $seconds
     * @return string|null
     */
    protected function formatTime(?int $seconds): ?string
    {
        if ($seconds === null) {
            return null;
        }

        $minutes = floor($seconds / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%d:%02d', $minutes, $remainingSeconds);
    }
}

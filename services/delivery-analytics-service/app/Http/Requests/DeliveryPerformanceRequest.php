<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DeliveryPerformanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'start_date' => 'nullable|date|before_or_equal:end_date',
            'end_date' => 'nullable|date|before_or_equal:today',
            'location_id' => 'nullable|integer|exists:delivery_locations,pk_location_code',
            'meal_type' => 'nullable|string|in:lunch,dinner,breakfast,all',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'start_date.date' => 'The start date must be a valid date.',
            'start_date.before_or_equal' => 'The start date must be before or equal to the end date.',
            'end_date.date' => 'The end date must be a valid date.',
            'end_date.before_or_equal' => 'The end date must be before or equal to today.',
            'location_id.integer' => 'The location ID must be an integer.',
            'location_id.exists' => 'The selected location does not exist.',
            'meal_type.in' => 'The meal type must be one of: lunch, dinner, breakfast, all.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'start_date' => $this->start_date ?? now()->subDays(30)->format('Y-m-d'),
            'end_date' => $this->end_date ?? now()->format('Y-m-d'),
        ]);
    }
}

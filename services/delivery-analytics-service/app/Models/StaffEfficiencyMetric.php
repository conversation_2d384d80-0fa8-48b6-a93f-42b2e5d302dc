<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StaffEfficiencyMetric extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'delivery_person_id',
        'total_deliveries',
        'on_time_deliveries',
        'late_deliveries',
        'failed_deliveries',
        'avg_delivery_time',
        'avg_distance',
        'avg_rating',
        'total_ratings',
        'efficiency_score',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'total_deliveries' => 'integer',
        'on_time_deliveries' => 'integer',
        'late_deliveries' => 'integer',
        'failed_deliveries' => 'integer',
        'avg_delivery_time' => 'integer',
        'avg_distance' => 'decimal:2',
        'avg_rating' => 'decimal:2',
        'total_ratings' => 'integer',
        'efficiency_score' => 'decimal:2',
    ];

    /**
     * Get the on-time delivery percentage.
     *
     * @return float
     */
    public function getOnTimePercentageAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        return round(($this->on_time_deliveries / $this->total_deliveries) * 100, 2);
    }

    /**
     * Calculate the efficiency score based on on-time percentage, average delivery time, and rating.
     *
     * @return float
     */
    public function calculateEfficiencyScore(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        $onTimePercentage = $this->getOnTimePercentageAttribute();
        $avgDeliveryTimeScore = $this->avg_delivery_time ? min(100, max(0, 100 - ($this->avg_delivery_time / 60))) : 0;
        $ratingScore = $this->avg_rating ? min(100, ($this->avg_rating / 5) * 100) : 0;

        $factors = config('analytics.staff_efficiency.efficiency_factors');

        return round(
            ($onTimePercentage * $factors['on_time_percentage']) +
            ($avgDeliveryTimeScore * $factors['avg_delivery_time']) +
            ($ratingScore * $factors['customer_rating']),
            2
        );
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerSatisfactionMetric extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'customer_id',
        'delivery_type',
        'total_deliveries',
        'avg_rating',
        'total_ratings',
        'on_time_percentage',
        'issue_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'total_deliveries' => 'integer',
        'avg_rating' => 'decimal:2',
        'total_ratings' => 'integer',
        'on_time_percentage' => 'decimal:2',
        'issue_count' => 'integer',
    ];

    /**
     * Get the issue rate (issues per delivery).
     *
     * @return float
     */
    public function getIssueRateAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        return round(($this->issue_count / $this->total_deliveries) * 100, 2);
    }

    /**
     * Get the rating participation rate (ratings per delivery).
     *
     * @return float
     */
    public function getRatingParticipationRateAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        return round(($this->total_ratings / $this->total_deliveries) * 100, 2);
    }

    /**
     * Get the satisfaction category based on the average rating.
     *
     * @return string
     */
    public function getSatisfactionCategoryAttribute(): string
    {
        $thresholds = config('analytics.customer_satisfaction');

        if ($this->avg_rating >= $thresholds['rating_threshold_good']) {
            return 'good';
        } elseif ($this->avg_rating >= $thresholds['rating_threshold_average']) {
            return 'average';
        } elseif ($this->avg_rating >= $thresholds['rating_threshold_poor']) {
            return 'poor';
        } else {
            return 'very_poor';
        }
    }
}

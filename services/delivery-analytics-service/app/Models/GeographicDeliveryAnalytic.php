<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GeographicDeliveryAnalytic extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'zone_id',
        'postal_code',
        'area_name',
        'total_deliveries',
        'avg_delivery_time',
        'avg_distance',
        'avg_delivery_cost',
        'latitude',
        'longitude',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'total_deliveries' => 'integer',
        'avg_delivery_time' => 'integer',
        'avg_distance' => 'decimal:2',
        'avg_delivery_cost' => 'decimal:2',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
    ];

    /**
     * Get the delivery density (deliveries per square kilometer).
     *
     * @param float $areaKm2
     * @return float
     */
    public function getDeliveryDensityAttribute(float $areaKm2 = 1.0): float
    {
        if ($areaKm2 <= 0) {
            return 0;
        }

        return round($this->total_deliveries / $areaKm2, 2);
    }

    /**
     * Get the cost efficiency (cost per kilometer).
     *
     * @return float
     */
    public function getCostEfficiencyAttribute(): float
    {
        if ($this->avg_distance <= 0) {
            return 0;
        }

        return round($this->avg_delivery_cost / $this->avg_distance, 2);
    }

    /**
     * Get the time efficiency (seconds per kilometer).
     *
     * @return float
     */
    public function getTimeEfficiencyAttribute(): float
    {
        if ($this->avg_distance <= 0) {
            return 0;
        }

        return round($this->avg_delivery_time / $this->avg_distance, 2);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DabbawalaPerformanceMetric extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'dabbawala_code',
        'total_deliveries',
        'on_time_deliveries',
        'late_deliveries',
        'failed_deliveries',
        'avg_delivery_time',
        'avg_distance',
        'efficiency_score',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'total_deliveries' => 'integer',
        'on_time_deliveries' => 'integer',
        'late_deliveries' => 'integer',
        'failed_deliveries' => 'integer',
        'avg_delivery_time' => 'integer',
        'avg_distance' => 'decimal:2',
        'efficiency_score' => 'decimal:2',
    ];

    /**
     * Get the on-time delivery percentage.
     *
     * @return float
     */
    public function getOnTimePercentageAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        return round(($this->on_time_deliveries / $this->total_deliveries) * 100, 2);
    }

    /**
     * Calculate the efficiency score based on on-time percentage and average delivery time.
     *
     * @return float
     */
    public function calculateEfficiencyScore(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        $onTimePercentage = $this->getOnTimePercentageAttribute();
        $avgDeliveryTimeScore = $this->avg_delivery_time ? min(100, max(0, 100 - ($this->avg_delivery_time / 60))) : 0;

        // For Dabbawala, on-time percentage is more important
        return round(($onTimePercentage * 0.7) + ($avgDeliveryTimeScore * 0.3), 2);
    }

    /**
     * Check if the Dabbawala meets the efficiency threshold.
     *
     * @return bool
     */
    public function meetsEfficiencyThreshold(): bool
    {
        $threshold = config('analytics.dabbawala.efficiency_threshold', 95);
        return $this->efficiency_score >= $threshold;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryPerformanceMetric extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'location_id',
        'meal_type',
        'total_deliveries',
        'on_time_deliveries',
        'late_deliveries',
        'failed_deliveries',
        'avg_delivery_time',
        'avg_distance',
        'avg_delivery_cost',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'total_deliveries' => 'integer',
        'on_time_deliveries' => 'integer',
        'late_deliveries' => 'integer',
        'failed_deliveries' => 'integer',
        'avg_delivery_time' => 'integer',
        'avg_distance' => 'decimal:2',
        'avg_delivery_cost' => 'decimal:2',
    ];

    /**
     * Get the on-time delivery percentage.
     *
     * @return float
     */
    public function getOnTimePercentageAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        return round(($this->on_time_deliveries / $this->total_deliveries) * 100, 2);
    }

    /**
     * Get the late delivery percentage.
     *
     * @return float
     */
    public function getLatePercentageAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        return round(($this->late_deliveries / $this->total_deliveries) * 100, 2);
    }

    /**
     * Get the failed delivery percentage.
     *
     * @return float
     */
    public function getFailedPercentageAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 0;
        }

        return round(($this->failed_deliveries / $this->total_deliveries) * 100, 2);
    }
}

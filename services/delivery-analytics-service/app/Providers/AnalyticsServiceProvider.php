<?php

namespace App\Providers;

use App\Repositories\Contracts\DeliveryAnalyticsRepositoryInterface;
use App\Repositories\DeliveryAnalyticsRepository;
use App\Services\Contracts\DeliveryPerformanceServiceInterface;
use App\Services\DeliveryPerformanceService;
use Illuminate\Support\ServiceProvider;

class AnalyticsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind repositories
        $this->app->bind(DeliveryAnalyticsRepositoryInterface::class, DeliveryAnalyticsRepository::class);

        // Bind services
        $this->app->bind(DeliveryPerformanceServiceInterface::class, DeliveryPerformanceService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

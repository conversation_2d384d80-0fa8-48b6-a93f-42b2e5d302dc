<?php

namespace App\Services\Contracts;

use Illuminate\Database\Eloquent\Collection;

interface DeliveryPerformanceServiceInterface
{
    /**
     * Get delivery performance report.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return array
     */
    public function getDeliveryPerformanceReport(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): array;

    /**
     * Get daily delivery performance metrics.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getDailyDeliveryPerformanceMetrics(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection;

    /**
     * Get monthly delivery performance metrics.
     *
     * @param int $year
     * @param int|null $month
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getMonthlyDeliveryPerformanceMetrics(
        int $year,
        ?int $month = null,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection;

    /**
     * Get delivery performance metrics by location.
     *
     * @param string $startDate
     * @param string $endDate
     * @param string|null $mealType
     * @return Collection
     */
    public function getDeliveryPerformanceByLocation(
        string $startDate,
        string $endDate,
        ?string $mealType = null
    ): Collection;

    /**
     * Get delivery performance metrics by meal type.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @return Collection
     */
    public function getDeliveryPerformanceByMealType(
        string $startDate,
        string $endDate,
        ?int $locationId = null
    ): Collection;

    /**
     * Process delivery completed event.
     *
     * @param array $eventData
     * @return bool
     */
    public function processDeliveryCompletedEvent(array $eventData): bool;

    /**
     * Update delivery performance metrics.
     *
     * @param string $date
     * @param int|null $locationId
     * @param string|null $mealType
     * @return bool
     */
    public function updateDeliveryPerformanceMetrics(
        string $date,
        ?int $locationId = null,
        ?string $mealType = null
    ): bool;
}

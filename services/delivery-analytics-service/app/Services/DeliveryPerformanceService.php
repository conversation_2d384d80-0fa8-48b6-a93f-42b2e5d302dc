<?php

namespace App\Services;

use App\Repositories\Contracts\DeliveryAnalyticsRepositoryInterface;
use App\Services\Contracts\DeliveryPerformanceServiceInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DeliveryPerformanceService implements DeliveryPerformanceServiceInterface
{
    /**
     * The delivery analytics repository instance.
     *
     * @var DeliveryAnalyticsRepositoryInterface
     */
    protected DeliveryAnalyticsRepositoryInterface $deliveryAnalyticsRepository;

    /**
     * Create a new service instance.
     *
     * @param DeliveryAnalyticsRepositoryInterface $deliveryAnalyticsRepository
     */
    public function __construct(DeliveryAnalyticsRepositoryInterface $deliveryAnalyticsRepository)
    {
        $this->deliveryAnalyticsRepository = $deliveryAnalyticsRepository;
    }

    /**
     * Get delivery performance report.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return array
     */
    public function getDeliveryPerformanceReport(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): array {
        $cacheKey = "delivery_performance_report:{$startDate}:{$endDate}:{$locationId}:{$mealType}";
        $cacheTtl = config('analytics.cache.ttl', 3600);

        if (config('analytics.cache.enabled', true)) {
            return Cache::remember($cacheKey, $cacheTtl, function () use ($startDate, $endDate, $locationId, $mealType) {
                return $this->generateDeliveryPerformanceReport($startDate, $endDate, $locationId, $mealType);
            });
        }

        return $this->generateDeliveryPerformanceReport($startDate, $endDate, $locationId, $mealType);
    }

    /**
     * Generate delivery performance report.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return array
     */
    protected function generateDeliveryPerformanceReport(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): array {
        $metrics = $this->deliveryAnalyticsRepository->getDeliveryPerformanceMetrics(
            $startDate,
            $endDate,
            $locationId,
            $mealType
        );

        $totalDeliveries = $metrics->sum('total_deliveries');
        $onTimeDeliveries = $metrics->sum('on_time_deliveries');
        $lateDeliveries = $metrics->sum('late_deliveries');
        $failedDeliveries = $metrics->sum('failed_deliveries');

        $onTimePercentage = $totalDeliveries > 0 ? round(($onTimeDeliveries / $totalDeliveries) * 100, 2) : 0;
        $latePercentage = $totalDeliveries > 0 ? round(($lateDeliveries / $totalDeliveries) * 100, 2) : 0;
        $failedPercentage = $totalDeliveries > 0 ? round(($failedDeliveries / $totalDeliveries) * 100, 2) : 0;

        $avgDeliveryTime = $metrics->avg('avg_delivery_time');
        $avgDistance = $metrics->avg('avg_distance');
        $avgDeliveryCost = $metrics->avg('avg_delivery_cost');

        // Get daily metrics for trend analysis
        $dailyMetrics = $this->getDailyDeliveryPerformanceMetrics($startDate, $endDate, $locationId, $mealType);

        // Get metrics by location
        $locationMetrics = $this->getDeliveryPerformanceByLocation($startDate, $endDate, $mealType);

        // Get metrics by meal type
        $mealTypeMetrics = $this->getDeliveryPerformanceByMealType($startDate, $endDate, $locationId);

        return [
            'summary' => [
                'total_deliveries' => $totalDeliveries,
                'on_time_deliveries' => $onTimeDeliveries,
                'late_deliveries' => $lateDeliveries,
                'failed_deliveries' => $failedDeliveries,
                'on_time_percentage' => $onTimePercentage,
                'late_percentage' => $latePercentage,
                'failed_percentage' => $failedPercentage,
                'avg_delivery_time' => $avgDeliveryTime,
                'avg_distance' => $avgDistance,
                'avg_delivery_cost' => $avgDeliveryCost,
            ],
            'daily_metrics' => $dailyMetrics,
            'location_metrics' => $locationMetrics,
            'meal_type_metrics' => $mealTypeMetrics,
        ];
    }

    /**
     * Get daily delivery performance metrics.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getDailyDeliveryPerformanceMetrics(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection {
        $cacheKey = "daily_delivery_performance:{$startDate}:{$endDate}:{$locationId}:{$mealType}";
        $cacheTtl = config('analytics.cache.ttl', 3600);

        if (config('analytics.cache.enabled', true)) {
            return Cache::remember($cacheKey, $cacheTtl, function () use ($startDate, $endDate, $locationId, $mealType) {
                return $this->deliveryAnalyticsRepository->getDailyDeliveryPerformanceMetrics(
                    $startDate,
                    $endDate,
                    $locationId,
                    $mealType
                );
            });
        }

        return $this->deliveryAnalyticsRepository->getDailyDeliveryPerformanceMetrics(
            $startDate,
            $endDate,
            $locationId,
            $mealType
        );
    }

    /**
     * Get monthly delivery performance metrics.
     *
     * @param int $year
     * @param int|null $month
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getMonthlyDeliveryPerformanceMetrics(
        int $year,
        ?int $month = null,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection {
        $cacheKey = "monthly_delivery_performance:{$year}:{$month}:{$locationId}:{$mealType}";
        $cacheTtl = config('analytics.cache.ttl', 3600);

        if (config('analytics.cache.enabled', true)) {
            return Cache::remember($cacheKey, $cacheTtl, function () use ($year, $month, $locationId, $mealType) {
                return $this->deliveryAnalyticsRepository->getMonthlyDeliveryPerformanceMetrics(
                    $year,
                    $month,
                    $locationId,
                    $mealType
                );
            });
        }

        return $this->deliveryAnalyticsRepository->getMonthlyDeliveryPerformanceMetrics(
            $year,
            $month,
            $locationId,
            $mealType
        );
    }

    /**
     * Get delivery performance metrics by location.
     *
     * @param string $startDate
     * @param string $endDate
     * @param string|null $mealType
     * @return Collection
     */
    public function getDeliveryPerformanceByLocation(
        string $startDate,
        string $endDate,
        ?string $mealType = null
    ): Collection {
        $cacheKey = "delivery_performance_by_location:{$startDate}:{$endDate}:{$mealType}";
        $cacheTtl = config('analytics.cache.ttl', 3600);

        if (config('analytics.cache.enabled', true)) {
            return Cache::remember($cacheKey, $cacheTtl, function () use ($startDate, $endDate, $mealType) {
                return $this->deliveryAnalyticsRepository->getDeliveryPerformanceByLocation(
                    $startDate,
                    $endDate,
                    $mealType
                );
            });
        }

        return $this->deliveryAnalyticsRepository->getDeliveryPerformanceByLocation(
            $startDate,
            $endDate,
            $mealType
        );
    }

    /**
     * Get delivery performance metrics by meal type.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @return Collection
     */
    public function getDeliveryPerformanceByMealType(
        string $startDate,
        string $endDate,
        ?int $locationId = null
    ): Collection {
        $cacheKey = "delivery_performance_by_meal_type:{$startDate}:{$endDate}:{$locationId}";
        $cacheTtl = config('analytics.cache.ttl', 3600);

        if (config('analytics.cache.enabled', true)) {
            return Cache::remember($cacheKey, $cacheTtl, function () use ($startDate, $endDate, $locationId) {
                return $this->deliveryAnalyticsRepository->getDeliveryPerformanceByMealType(
                    $startDate,
                    $endDate,
                    $locationId
                );
            });
        }

        return $this->deliveryAnalyticsRepository->getDeliveryPerformanceByMealType(
            $startDate,
            $endDate,
            $locationId
        );
    }

    /**
     * Process delivery completed event.
     *
     * @param array $eventData
     * @return bool
     */
    public function processDeliveryCompletedEvent(array $eventData): bool
    {
        try {
            $orderId = $eventData['order_id'] ?? null;
            $locationId = $eventData['location_id'] ?? null;
            $mealType = $eventData['meal_type'] ?? null;
            $date = $eventData['date'] ?? date('Y-m-d');

            if (!$orderId) {
                Log::error('Invalid delivery completed event data: Missing order_id', $eventData);
                return false;
            }

            // Update delivery performance metrics
            return $this->updateDeliveryPerformanceMetrics($date, $locationId, $mealType);
        } catch (\Exception $e) {
            Log::error('Error processing delivery completed event: ' . $e->getMessage(), [
                'event_data' => $eventData,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Update delivery performance metrics.
     *
     * @param string $date
     * @param int|null $locationId
     * @param string|null $mealType
     * @return bool
     */
    public function updateDeliveryPerformanceMetrics(
        string $date,
        ?int $locationId = null,
        ?string $mealType = null
    ): bool {
        try {
            // In a real implementation, this would fetch data from the delivery service
            // For now, we'll just create some sample data
            $data = [
                'date' => $date,
                'location_id' => $locationId,
                'meal_type' => $mealType,
                'total_deliveries' => rand(50, 200),
                'on_time_deliveries' => rand(40, 180),
                'late_deliveries' => rand(5, 15),
                'failed_deliveries' => rand(0, 5),
                'avg_delivery_time' => rand(1200, 3600), // 20-60 minutes in seconds
                'avg_distance' => rand(3, 10), // 3-10 kilometers
                'avg_delivery_cost' => rand(50, 150), // 50-150 rupees
            ];

            $this->deliveryAnalyticsRepository->createOrUpdateDeliveryPerformanceMetrics($data);

            // Clear cache
            $cachePrefix = config('analytics.cache.prefix', 'delivery_analytics:');
            Cache::forget("{$cachePrefix}delivery_performance_report:{$date}:{$date}:{$locationId}:{$mealType}");
            Cache::forget("{$cachePrefix}daily_delivery_performance:{$date}:{$date}:{$locationId}:{$mealType}");

            return true;
        } catch (\Exception $e) {
            Log::error('Error updating delivery performance metrics: ' . $e->getMessage(), [
                'date' => $date,
                'location_id' => $locationId,
                'meal_type' => $mealType,
                'exception' => $e,
            ]);

            return false;
        }
    }
}

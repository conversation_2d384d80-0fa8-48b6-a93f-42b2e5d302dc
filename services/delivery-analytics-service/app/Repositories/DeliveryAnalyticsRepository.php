<?php

namespace App\Repositories;

use App\Models\DeliveryPerformanceMetric;
use App\Repositories\Contracts\DeliveryAnalyticsRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class DeliveryAnalyticsRepository implements DeliveryAnalyticsRepositoryInterface
{
    /**
     * Get delivery performance metrics for a specific date range.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getDeliveryPerformanceMetrics(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection {
        $query = DeliveryPerformanceMetric::whereBetween('date', [$startDate, $endDate]);

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        if ($mealType) {
            $query->where('meal_type', $mealType);
        }

        return $query->orderBy('date')->get();
    }

    /**
     * Get daily delivery performance metrics.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getDailyDeliveryPerformanceMetrics(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection {
        return $this->getDeliveryPerformanceMetrics($startDate, $endDate, $locationId, $mealType);
    }

    /**
     * Get monthly delivery performance metrics.
     *
     * @param int $year
     * @param int|null $month
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getMonthlyDeliveryPerformanceMetrics(
        int $year,
        ?int $month = null,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection {
        $query = DeliveryPerformanceMetric::whereYear('date', $year);

        if ($month) {
            $query->whereMonth('date', $month);
        }

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        if ($mealType) {
            $query->where('meal_type', $mealType);
        }

        // Group by month
        $query->select(
            DB::raw('MONTH(date) as month'),
            DB::raw('SUM(total_deliveries) as total_deliveries'),
            DB::raw('SUM(on_time_deliveries) as on_time_deliveries'),
            DB::raw('SUM(late_deliveries) as late_deliveries'),
            DB::raw('SUM(failed_deliveries) as failed_deliveries'),
            DB::raw('AVG(avg_delivery_time) as avg_delivery_time'),
            DB::raw('AVG(avg_distance) as avg_distance'),
            DB::raw('AVG(avg_delivery_cost) as avg_delivery_cost')
        );

        $query->groupBy(DB::raw('MONTH(date)'));
        $query->orderBy(DB::raw('MONTH(date)'));

        return $query->get();
    }

    /**
     * Get delivery performance metrics by location.
     *
     * @param string $startDate
     * @param string $endDate
     * @param string|null $mealType
     * @return Collection
     */
    public function getDeliveryPerformanceByLocation(
        string $startDate,
        string $endDate,
        ?string $mealType = null
    ): Collection {
        $query = DeliveryPerformanceMetric::whereBetween('date', [$startDate, $endDate]);

        if ($mealType) {
            $query->where('meal_type', $mealType);
        }

        // Group by location
        $query->select(
            'location_id',
            DB::raw('SUM(total_deliveries) as total_deliveries'),
            DB::raw('SUM(on_time_deliveries) as on_time_deliveries'),
            DB::raw('SUM(late_deliveries) as late_deliveries'),
            DB::raw('SUM(failed_deliveries) as failed_deliveries'),
            DB::raw('AVG(avg_delivery_time) as avg_delivery_time'),
            DB::raw('AVG(avg_distance) as avg_distance'),
            DB::raw('AVG(avg_delivery_cost) as avg_delivery_cost')
        );

        $query->groupBy('location_id');
        $query->orderBy(DB::raw('SUM(total_deliveries)'), 'desc');

        return $query->get();
    }

    /**
     * Get delivery performance metrics by meal type.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @return Collection
     */
    public function getDeliveryPerformanceByMealType(
        string $startDate,
        string $endDate,
        ?int $locationId = null
    ): Collection {
        $query = DeliveryPerformanceMetric::whereBetween('date', [$startDate, $endDate]);

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        // Group by meal type
        $query->select(
            'meal_type',
            DB::raw('SUM(total_deliveries) as total_deliveries'),
            DB::raw('SUM(on_time_deliveries) as on_time_deliveries'),
            DB::raw('SUM(late_deliveries) as late_deliveries'),
            DB::raw('SUM(failed_deliveries) as failed_deliveries'),
            DB::raw('AVG(avg_delivery_time) as avg_delivery_time'),
            DB::raw('AVG(avg_distance) as avg_distance'),
            DB::raw('AVG(avg_delivery_cost) as avg_delivery_cost')
        );

        $query->groupBy('meal_type');
        $query->orderBy(DB::raw('SUM(total_deliveries)'), 'desc');

        return $query->get();
    }

    /**
     * Create or update delivery performance metrics.
     *
     * @param array $data
     * @return DeliveryPerformanceMetric
     */
    public function createOrUpdateDeliveryPerformanceMetrics(array $data): DeliveryPerformanceMetric
    {
        return DeliveryPerformanceMetric::updateOrCreate(
            [
                'date' => $data['date'],
                'location_id' => $data['location_id'] ?? null,
                'meal_type' => $data['meal_type'] ?? null,
            ],
            $data
        );
    }
}

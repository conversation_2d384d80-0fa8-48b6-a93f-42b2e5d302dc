<?php

namespace App\Repositories\Contracts;

use App\Models\DeliveryPerformanceMetric;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface DeliveryAnalyticsRepositoryInterface
{
    /**
     * Get delivery performance metrics for a specific date range.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getDeliveryPerformanceMetrics(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection;

    /**
     * Get daily delivery performance metrics.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getDailyDeliveryPerformanceMetrics(
        string $startDate,
        string $endDate,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection;

    /**
     * Get monthly delivery performance metrics.
     *
     * @param int $year
     * @param int|null $month
     * @param int|null $locationId
     * @param string|null $mealType
     * @return Collection
     */
    public function getMonthlyDeliveryPerformanceMetrics(
        int $year,
        ?int $month = null,
        ?int $locationId = null,
        ?string $mealType = null
    ): Collection;

    /**
     * Get delivery performance metrics by location.
     *
     * @param string $startDate
     * @param string $endDate
     * @param string|null $mealType
     * @return Collection
     */
    public function getDeliveryPerformanceByLocation(
        string $startDate,
        string $endDate,
        ?string $mealType = null
    ): Collection;

    /**
     * Get delivery performance metrics by meal type.
     *
     * @param string $startDate
     * @param string $endDate
     * @param int|null $locationId
     * @return Collection
     */
    public function getDeliveryPerformanceByMealType(
        string $startDate,
        string $endDate,
        ?int $locationId = null
    ): Collection;

    /**
     * Create or update delivery performance metrics.
     *
     * @param array $data
     * @return DeliveryPerformanceMetric
     */
    public function createOrUpdateDeliveryPerformanceMetrics(array $data): DeliveryPerformanceMetric;
}

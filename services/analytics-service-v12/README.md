# Analytics Service

This microservice handles analytics functionality for the CubeOneBiz application. It provides RESTful APIs for sales, food, and customer analytics, enabling data-driven decision making for business owners and managers.

## Overview

The Analytics Service is part of the microservices architecture migration from the legacy Zend Framework application. It follows modern best practices including:

- Clean architecture with separation of concerns
- Repository pattern for data access
- Service layer for business logic
- RESTful API design
- Comprehensive testing
- OpenAPI documentation
- Kong API Gateway integration

## Features

### Sales Analytics
- Revenue tracking by year and month
- Payment method distribution analysis
- Sales comparison across time periods
- Average meal consumption per customer
- Gross and net revenue calculations

### Food Analytics
- Popular meals identification
- Best and worst performing meals
- Menu performance analysis
- Common extras and add-ons tracking
- Meal performance by time period

### Customer Analytics
- Loyal customer identification
- Customer spending patterns
- Customer meal preferences
- Average order value analysis
- Customer retention metrics

### Technical Features
- Event-driven architecture using RabbitMQ for asynchronous processing
- Integration with Auth Service using Laravel Sanctum for secure authentication
- Comprehensive API documentation with OpenAPI 3.1
- Repository pattern for clean data access
- Service layer for encapsulated business logic
- Form requests for robust validation
- API resources for consistent response formatting
- Redis caching for improved performance
- Prometheus metrics for monitoring
- Grafana dashboards for visualization
- Supervisor for process management

## Requirements

- PHP 8.2 or higher
- Laravel 12.x
- MySQL/SQLite
- Composer 2.x
- Redis (for caching and metrics)
- RabbitMQ (for event-driven architecture)
- Prometheus (for metrics collection)
- Grafana (for metrics visualization)
- Supervisor (for process management)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-organization/analytics-service.git
cd analytics-service-v12
```

2. Install dependencies:
```bash
composer install
```

3. Copy the environment file:
```bash
cp .env.example .env
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Configure the database in the `.env` file:
```
DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite
```

6. Create the SQLite database:
```bash
touch database/database.sqlite
```

7. Run migrations:
```bash
php artisan migrate
```

8. Start the server:
```bash
php artisan serve
```

## API Endpoints

The Analytics Service provides both v1 (legacy) and v2 (new) API endpoints for backward compatibility.

### Health Check
- `GET /api/health` - Health check endpoint
  - Returns service status, version, and timestamp
  - Used by Kong API Gateway for health monitoring

### Sales Analytics

#### Dashboard Data
- `GET /api/v2/sales` - Get sales analytics dashboard data
  - Returns years, months, and payment methods in a single request
  - Requires authentication with valid JWT token
  - Used for initializing the sales analytics dashboard

#### Time Period Data
- `GET /api/v2/sales/years` - Get years with order data
  - Returns all years that have order data
  - Used for year selection dropdowns
- `GET /api/v2/sales/months/{year}` - Get months with order data for a specific year
  - Returns months that have order data for the specified year
  - Used for month selection dropdowns

#### Payment Analysis
- `GET /api/v2/sales/payment-methods` - Get payment methods
  - Returns payment methods and their usage counts
  - Used for payment method distribution charts

#### Revenue Analysis
- `GET /api/v2/sales/revenue/{year}/{month?}` - Get revenue for a specific year and month
  - Returns gross revenue for the specified time period
  - Optional month parameter for monthly breakdown
  - Used for revenue charts and reports
- `GET /api/v2/sales/comparison/{year}/{type}` - Get sales comparison for a specific year
  - Returns gross and net revenue for comparison
  - Type parameter can be 'yearly' or 'monthly'
  - Used for year-over-year or month-over-month comparison charts

#### Meal Analysis
- `GET /api/v2/sales/avg-meal/{year}/{month?}` - Get average meal per customer
  - Returns average number of meals consumed per customer
  - Optional month parameter for monthly breakdown
  - Used for customer consumption pattern analysis

### Food Analytics

#### Dashboard Data
- `GET /api/v2/food` - Get food analytics dashboard data
  - Returns years, months, menus, and common extras in a single request
  - Requires authentication with valid JWT token
  - Used for initializing the food analytics dashboard

#### Meal Performance
- `GET /api/v2/food/popular/{year}/{month?}` - Get popular meals
  - Returns most ordered meals for the specified time period
  - Optional month parameter for monthly breakdown
  - Used for popular meals charts and reports
- `GET /api/v2/food/performance/{year}/{month?}/{type}` - Get meal performance
  - Returns best or worst performing meals based on quantity sold
  - Type parameter can be 'best' or 'worst'
  - Optional month parameter for monthly breakdown
  - Used for meal performance analysis

#### Extras Analysis
- `GET /api/v2/food/extras` - Get common extras
  - Returns most commonly ordered extras and add-ons
  - Used for menu planning and pricing strategies

### Customer Analytics

#### Dashboard Data
- `GET /api/v2/customer` - Get customer analytics dashboard data
  - Returns loyal customers, customer preferences, and spending patterns in a single request
  - Requires authentication with valid JWT token
  - Used for initializing the customer analytics dashboard

#### Customer Loyalty
- `GET /api/v2/customer/loyal` - Get loyal customers
  - Returns customers ranked by total spending
  - Optional limit parameter to control number of results
  - Used for loyalty program targeting and VIP customer identification

#### Customer Spending
- `GET /api/v2/customer/spending/{customerId}` - Get customer spending
  - Returns yearly and monthly average spending for a specific customer
  - Used for customer value assessment and personalized marketing

#### Customer Preferences
- `GET /api/v2/customer/preferences/{customerId}` - Get customer preferences
  - Returns preferred meals for a specific customer
  - Used for personalized recommendations and targeted promotions

### Legacy API Endpoints (v1)

The following endpoints are maintained for backward compatibility with the legacy Zend application:

#### Sales Analytics
- `POST /api/v1/sales/avg-meal` - Get average meal per customer
- `POST /api/v1/sales/avg-meal-get-months` - Get months with order data
- `POST /api/v1/sales/common-payment-mode` - Get payment methods
- `POST /api/v1/sales/revenue-share` - Get revenue share
- `POST /api/v1/sales/sales-comparison` - Get sales comparison

#### Food Analytics
- `POST /api/v1/food/best-worst-meal` - Get best/worst performing meals

These legacy endpoints use POST requests with form data instead of path parameters, and return data in a different format than the v2 endpoints.

## Cache Management

The Analytics Service uses Redis for caching to improve performance. The following commands are available for managing the cache:

### Clear Cache

```bash
# Clear all cache
php artisan analytics:clear-cache --all

# Clear cache for a specific kitchen
php artisan analytics:clear-cache {kitchen_id}

# Clear cache for a specific type (sales, food, customer)
php artisan analytics:clear-cache {kitchen_id} --type=sales

# Clear cache for a specific customer
php artisan analytics:clear-cache {kitchen_id} --customer={customer_id}
```

### Warm Cache

```bash
# Warm cache for all kitchens
php artisan analytics:warm-cache --all

# Warm cache for a specific kitchen
php artisan analytics:warm-cache {kitchen_id}

# Warm cache for a specific type (sales, food, customer)
php artisan analytics:warm-cache {kitchen_id} --type=sales
```

### Monitor Cache

```bash
# Monitor cache usage
php artisan analytics:monitor-cache

# Monitor cache usage with auto-refresh
php artisan analytics:monitor-cache --watch

# Monitor cache usage with custom refresh interval (in seconds)
php artisan analytics:monitor-cache --watch --interval=10
```

## RabbitMQ Integration

The Analytics Service uses RabbitMQ for event-driven architecture. The following commands are available for consuming messages:

```bash
# Consume order created events
php artisan rabbitmq:consume order.created

# Consume order updated events
php artisan rabbitmq:consume order.updated

# Consume order cancelled events
php artisan rabbitmq:consume order.cancelled
```

## Monitoring

The Analytics Service exposes Prometheus metrics at `/api/metrics`. These metrics can be visualized using Grafana.

### Metrics Endpoint

The metrics endpoint provides the following metrics:

- **HTTP Request Duration**: Histogram of request durations by route, method, and status
- **HTTP Request Count**: Counter of requests by route, method, and status
- **Redis Memory Usage**: Gauge of Redis memory usage in bytes
- **Redis Hit Rate**: Gauge of Redis cache hit rate percentage
- **Redis Connected Clients**: Gauge of number of clients connected to Redis
- **Redis Key Count**: Gauge of total number of keys in Redis
- **Database Connection Time**: Gauge of time to connect to the database in seconds
- **Database Query Count**: Counter of total number of database queries
- **Database Size**: Gauge of database size in bytes

### Grafana Dashboard

A Grafana dashboard is provided in `kubernetes/analytics-service/grafana-dashboard.json`. This dashboard visualizes the metrics exposed by the service.

## Testing

The Analytics Service includes comprehensive tests to ensure reliability and maintainability. The test suite includes:

### Unit Tests

Unit tests focus on testing individual components in isolation:

- **Repository Tests**: Verify that repositories correctly interact with the database
- **Service Tests**: Ensure business logic in service classes works correctly
- **Resource Tests**: Validate that API resources format data correctly

Run unit tests with:

```bash
php artisan test --testsuite=Unit
```

### Feature Tests

Feature tests verify that API endpoints work correctly end-to-end:

- **Controller Tests**: Test API endpoints with authentication and validation
- **Integration Tests**: Verify that components work together correctly

Run feature tests with:

```bash
php artisan test --testsuite=Feature
```

### Code Coverage

The test suite aims for >90% code coverage to ensure reliability. Generate a code coverage report with:

```bash
php artisan test --coverage
```

View the detailed HTML coverage report in the `coverage` directory.

## API Documentation

The API is documented using the OpenAPI Specification (OAS) 3.1.0. The specification is available in the `openapi.yaml` file.

### Key Documentation Features

- **Complete Endpoint Documentation**: All endpoints, parameters, and responses are documented
- **Request/Response Examples**: Example requests and responses for each endpoint
- **Authentication Details**: Documentation of authentication requirements
- **Error Responses**: Standard error response formats
- **Schema Definitions**: Detailed schema definitions for all data models

### Viewing the Documentation

You can view the API documentation in several ways:

1. **Swagger UI**: Run the service and navigate to `/api/documentation`
2. **ReDoc**: Run the service and navigate to `/api/documentation/redoc`
3. **Raw YAML**: View the raw OpenAPI specification in `openapi.yaml`

## Kong API Gateway Integration

This service is configured to work with Kong API Gateway for routing, authentication, rate limiting, and monitoring.

### Kong Configuration

The Kong API Gateway configuration is available in the `kong.yaml` file and includes:

- **Route Configuration**: Routes requests to the appropriate service
  ```yaml
  routes:
    - name: analytics-service-route
      paths:
        - /v2/analytics
      strip_path: false
      preserve_host: true
  ```

- **Authentication**: JWT authentication for secure access
  ```yaml
  plugins:
    - name: jwt
      config:
        secret_is_base64: false
        claims_to_verify:
          - exp
          - nbf
  ```

- **Rate Limiting**: Prevents abuse by limiting requests
  ```yaml
  plugins:
    - name: rate-limiting
      config:
        minute: 60
        hour: 1000
        policy: local
  ```

- **CORS Configuration**: Enables cross-origin requests
  ```yaml
  plugins:
    - name: cors
      config:
        origins:
          - "*"
        methods:
          - GET
          - POST
          - OPTIONS
  ```

- **Health Check**: Endpoint for monitoring service health
  ```yaml
  routes:
    - name: analytics-service-health
      paths:
        - /v2/health/analytics
      strip_path: true
  ```

### Deploying to Kong

To deploy the service to Kong API Gateway:

1. Ensure Kong is running
2. Apply the configuration:
   ```bash
   curl -X POST http://localhost:8001/config \
     -F "config=@kong.yaml" \
     -H "Content-Type: multipart/form-data"
   ```

## Architecture and Design Patterns

The Analytics Service follows a clean architecture approach with clear separation of concerns:

### Layered Architecture

The service is organized into the following layers:

1. **Presentation Layer**: Controllers and API Resources
   - Handles HTTP requests and responses
   - Formats data for API consumers
   - Validates input data

2. **Service Layer**: Service Classes
   - Implements business logic
   - Orchestrates data access
   - Handles error handling and logging

3. **Data Access Layer**: Repositories
   - Abstracts database operations
   - Implements query logic
   - Returns domain objects

4. **Domain Layer**: Models
   - Represents business entities
   - Defines relationships between entities
   - Implements domain-specific logic

### Design Patterns

The service implements several design patterns:

1. **Repository Pattern**
   - Abstracts data access logic
   - Enables testability through dependency injection
   - Provides a clean API for data access

2. **Service Layer Pattern**
   - Encapsulates business logic
   - Coordinates between repositories
   - Handles cross-cutting concerns

3. **Dependency Injection**
   - Injects dependencies through constructors
   - Enables loose coupling between components
   - Facilitates testing with mocks

4. **Factory Pattern**
   - Creates complex objects
   - Centralizes object creation logic
   - Used in database seeding and testing

5. **Adapter Pattern**
   - Adapts legacy data formats to new formats
   - Enables backward compatibility
   - Used in the transition from Zend to Laravel

### Event-Driven Architecture

The service uses RabbitMQ for event-driven communication:

1. **Event Publishers**
   - Publish events when significant actions occur
   - Decouple event producers from consumers
   - Use topic exchange for routing events
   - Implement retry logic for failed publishing

2. **Event Listeners**
   - Listen for events from other services
   - Update analytics data based on events
   - Process events asynchronously
   - Implement dead letter queues for failed processing

3. **Message Queues**
   - Ensure reliable message delivery
   - Enable asynchronous processing
   - Provide scalability and fault tolerance
   - Support message persistence for durability

4. **Event Types**
   - `order.created`: Published when a new order is created
   - `order.updated`: Published when an order is updated
   - `order.cancelled`: Published when an order is cancelled

5. **Supervisor Integration**
   - Manages RabbitMQ consumer processes
   - Automatically restarts failed processes
   - Provides logging and monitoring
   - Ensures high availability of consumers

## Migration from Zend Framework

This service was migrated from the Zend Framework application following a structured approach:

### Original Zend Framework Code

The original code was located in:
- `module/Analytics/src/Analytics/Controller/SalesController.php`
- `module/Analytics/src/Analytics/Controller/FoodController.php`
- `module/Analytics/src/Analytics/Controller/CustomerController.php`
- `module/Analytics/src/Analytics/Model/Order.php`
- `module/Analytics/src/Analytics/Model/OrderTable.php`
- `module/Analytics/src/Analytics/Model/TempOrderPayment.php`
- `module/Analytics/src/Analytics/Model/TempOrderPaymentTable.php`

### Migration Process

The migration followed these steps:

1. **Analysis Phase**
   - Analyzed existing code structure and dependencies
   - Identified business logic and data access patterns
   - Mapped Zend components to Laravel equivalents

2. **Design Phase**
   - Designed clean architecture with proper separation of concerns
   - Defined repository interfaces and service contracts
   - Planned API endpoints and response formats

3. **Implementation Phase**
   - Created Laravel models with proper relationships
   - Implemented repositories with Eloquent queries
   - Developed service classes with business logic
   - Built controllers with RESTful API endpoints
   - Added form requests for validation
   - Created API resources for response formatting

4. **Testing Phase**
   - Wrote unit tests for repositories and services
   - Created feature tests for API endpoints
   - Verified backward compatibility with legacy endpoints
   - Achieved >90% code coverage

5. **Documentation Phase**
   - Created OpenAPI specification
   - Documented API endpoints and parameters
   - Added examples and schema definitions

6. **Deployment Phase**
   - Configured Kong API Gateway
   - Set up Docker container
   - Implemented health checks and monitoring

### Key Improvements

The migration resulted in several improvements:

1. **Code Quality**
   - Proper separation of concerns
   - Type safety with PHP 8.2 features
   - Comprehensive test coverage
   - Clean, maintainable code

2. **Performance**
   - Optimized database queries
   - Efficient caching
   - Reduced memory usage
   - Faster response times

3. **Scalability**
   - Microservice architecture
   - Event-driven communication
   - Containerized deployment
   - Horizontal scaling capability

4. **Security**
   - Proper input validation
   - Secure authentication with Sanctum
   - Rate limiting
   - CSRF protection

## Directory Structure

The service follows a standard Laravel 12 directory structure with additional folders for repositories, services, events, and listeners:

```
app/
├── Console/
│   └── Commands/
│       ├── ClearAnalyticsCache.php
│       ├── ConsumeRabbitMQMessages.php
│       ├── MonitorAnalyticsCache.php
│       └── WarmAnalyticsCache.php
├── Events/
│   ├── OrderCancelled.php
│   ├── OrderCreated.php
│   └── OrderUpdated.php
├── Http/
│   ├── Controllers/
│   │   └── Api/
│   │       ├── CustomerController.php
│   │       ├── FoodController.php
│   │       ├── MetricsController.php
│   │       └── SalesController.php
│   ├── Middleware/
│   │   ├── CollectMetrics.php
│   │   └── RequestLogger.php
│   ├── Requests/
│   │   ├── Sales/
│   │   │   ├── AvgMealRequest.php
│   │   │   ├── AvgMealGetMonthsRequest.php
│   │   │   ├── CommonPaymentModeRequest.php
│   │   │   ├── RevenueShareRequest.php
│   │   │   └── SalesComparisonRequest.php
│   │   └── Food/
│   │       └── BestWorstMealRequest.php
│   └── Resources/
│       ├── Sales/
│       │   ├── AvgMealResource.php
│       │   ├── MonthResource.php
│       │   ├── PaymentModeResource.php
│       │   ├── RevenueShareResource.php
│       │   └── SalesComparisonResource.php
│       ├── Food/
│       │   ├── MealPerformanceResource.php
│       │   └── CommonExtraResource.php
│       └── Customer/
│           ├── LoyalCustomerResource.php
│           ├── CustomerSpendingResource.php
│           └── CustomerPreferenceResource.php
├── Listeners/
│   └── UpdateAnalyticsData.php
├── Models/
│   ├── Kitchen.php
│   ├── Order.php
│   ├── OrderExtra.php
│   ├── Product.php
│   └── TempOrderPayment.php
├── Providers/
│   ├── AppServiceProvider.php
│   ├── AuthServiceProvider.php
│   ├── EventServiceProvider.php
│   ├── RabbitMQServiceProvider.php
│   └── RouteServiceProvider.php
├── Repositories/
│   ├── Interfaces/
│   │   ├── OrderRepositoryInterface.php
│   │   └── TempOrderPaymentRepositoryInterface.php
│   ├── OrderRepository.php
│   └── TempOrderPaymentRepository.php
└── Services/
    ├── CustomerService.php
    ├── FoodService.php
    ├── RabbitMQConsumer.php
    ├── RabbitMQPublisher.php
    └── SalesService.php
```

The service also includes Kubernetes manifests for deployment:

```
kubernetes/
└── analytics-service/
    ├── deployment.yaml
    ├── grafana-dashboard.json
    ├── hpa.yaml
    ├── ingress.yaml
    ├── prometheus-servicemonitor.yaml
    └── service.yaml
```

## Deployment

### Docker

The Analytics Service can be deployed using Docker:

```bash
# Build the Docker image
docker build -t analytics-service-v12 .

# Run the container
docker run -p 8000:8000 analytics-service-v12
```

For development deployment with dependencies, use Docker Compose:

```bash
# Start the service with dependencies
docker-compose up -d
```

### Kubernetes

For production deployment, use Kubernetes:

```bash
# Apply the Kubernetes manifests
kubectl apply -f kubernetes/analytics-service/
```

The Kubernetes deployment includes:

- **Deployment**: Defines the container, resources, health checks, and environment variables
- **Service**: Exposes the service within the cluster
- **Ingress**: Exposes the service outside the cluster
- **HorizontalPodAutoscaler**: Automatically scales the service based on CPU and memory usage
- **ServiceMonitor**: Configures Prometheus to scrape metrics from the service

### CI/CD Pipeline

The service includes a GitHub Actions workflow for continuous integration and deployment:

1. **Test**: Runs the test suite and generates a code coverage report
2. **Build**: Builds and pushes the Docker image to DockerHub
3. **Deploy**: Deploys the service to Kubernetes

The workflow is triggered on pushes to the main branch and pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

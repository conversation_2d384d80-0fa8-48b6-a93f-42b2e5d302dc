<?php

use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\FoodController;
use App\Http\Controllers\Api\MetricsController;
use App\Http\Controllers\Api\SalesController;
use App\Http\Controllers\Api\V2\ReportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'service' => 'analytics-service',
        'version' => '2.0.0',
        'timestamp' => now()->toIso8601String()
    ]);
});

// Metrics endpoint (no authentication required)
Route::get('/metrics', [MetricsController::class, 'index']);

// API v1 routes (for backward compatibility)
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // Sales Analytics Routes
    Route::prefix('sales')->group(function () {
        Route::get('/', [SalesController::class, 'index']);
        Route::post('/avg-meal', [SalesController::class, 'avgMeal']);
        Route::post('/avg-meal-get-months', [SalesController::class, 'avgMealGetMonths']);
        Route::post('/common-payment-mode', [SalesController::class, 'commonPaymentMode']);
        Route::post('/revenue-share', [SalesController::class, 'revenueShare']);
        Route::post('/sales-comparison', [SalesController::class, 'salesComparison']);
    });

    // Food Analytics Routes
    Route::prefix('food')->group(function () {
        Route::get('/', [FoodController::class, 'index']);
        Route::post('/best-worst-meal', [FoodController::class, 'bestWorstMeal']);
    });

    // Customer Analytics Routes
    Route::prefix('customer')->group(function () {
        Route::get('/', [CustomerController::class, 'index']);
    });
});

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->middleware(['auth:sanctum'])->group(function () {
    // Direct analytics routes (for frontend integration)
    Route::prefix('analytics')->group(function () {
        // Dashboard endpoints
        Route::get('/dashboard', [SalesController::class, 'getDashboard']);
        Route::get('/payment-methods', [SalesController::class, 'getPaymentMethods']);
        Route::get('/summary', [SalesController::class, 'getSummary']);
        Route::get('/trends', [SalesController::class, 'getTrends']);
        Route::get('/kpis', [SalesController::class, 'getKpis']);

        // Real-time analytics
        Route::get('/realtime/orders', [SalesController::class, 'getRealtimeOrders']);
        Route::get('/realtime/revenue', [SalesController::class, 'getRealtimeRevenue']);
        Route::get('/realtime/customers', [CustomerController::class, 'getRealtimeCustomers']);

        // Performance analytics
        Route::get('/performance/daily', [SalesController::class, 'getDailyPerformance']);
        Route::get('/performance/weekly', [SalesController::class, 'getWeeklyPerformance']);
        Route::get('/performance/monthly', [SalesController::class, 'getMonthlyPerformance']);

        // Customer analytics
        Route::get('/customers/loyalty', [CustomerController::class, 'getLoyalCustomers']);
        Route::get('/customers/retention', [CustomerController::class, 'getCustomerRetention']);
        Route::get('/customers/acquisition', [CustomerController::class, 'getCustomerAcquisition']);

        // Food analytics
        Route::get('/food/popular', [FoodController::class, 'getPopularMeals']);
        Route::get('/food/performance', [FoodController::class, 'getFoodPerformance']);
        Route::get('/food/trends', [FoodController::class, 'getFoodTrends']);

        // Financial analytics
        Route::get('/financial/revenue', [SalesController::class, 'getRevenue']);
        Route::get('/financial/profit', [SalesController::class, 'getProfit']);
        Route::get('/financial/costs', [SalesController::class, 'getCosts']);

        // Operational analytics
        Route::get('/operations/efficiency', [SalesController::class, 'getOperationalEfficiency']);
        Route::get('/operations/capacity', [SalesController::class, 'getCapacityUtilization']);
        Route::get('/operations/delivery', [SalesController::class, 'getDeliveryMetrics']);
    });

    // Sales Analytics Routes
    Route::prefix('sales')->group(function () {
        Route::get('/', [SalesController::class, 'index']);
        Route::get('/years', [SalesController::class, 'getYears']);
        Route::get('/months/{year}', [SalesController::class, 'getMonths']);
        Route::get('/payment-methods', [SalesController::class, 'getPaymentMethods']);
        Route::get('/revenue/{year}/{month?}', [SalesController::class, 'getRevenue']);
        Route::get('/comparison/{year}/{type}', [SalesController::class, 'getComparison']);
        Route::get('/avg-meal/{year}/{month?}', [SalesController::class, 'getAvgMeal']);
    });

    // Food Analytics Routes
    Route::prefix('food')->group(function () {
        Route::get('/', [FoodController::class, 'index']);
        Route::get('/popular/{year}/{month?}', [FoodController::class, 'getPopularMeals']);
        Route::get('/performance/{year}/{month?}/{type}', [FoodController::class, 'getMealPerformance']);
        Route::get('/extras', [FoodController::class, 'getCommonExtras']);
    });

    // Customer Analytics Routes
    Route::prefix('customer')->group(function () {
        Route::get('/', [CustomerController::class, 'index']);
        Route::get('/loyal', [CustomerController::class, 'getLoyalCustomers']);
        Route::get('/spending/{customerId}', [CustomerController::class, 'getCustomerSpending']);
        Route::get('/preferences/{customerId}', [CustomerController::class, 'getCustomerPreferences']);
    });

    // Report Routes
    Route::prefix('reports')->group(function () {
        Route::post('/generate', [ReportController::class, 'generate']);
        Route::post('/export', [ReportController::class, 'export']);
        Route::get('/columns', [ReportController::class, 'columns']);
        Route::get('/models', [ReportController::class, 'models']);
    });
});

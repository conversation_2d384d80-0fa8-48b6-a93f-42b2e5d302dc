<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Cache Store
    |--------------------------------------------------------------------------
    |
    | This option controls the default cache connection that gets used while
    | using this caching library. This connection is used when another is
    | not explicitly specified when executing a given caching function.
    |
    */

    'default' => env('CACHE_DRIVER', 'redis'),

    /*
    |--------------------------------------------------------------------------
    | Cache Stores
    |--------------------------------------------------------------------------
    |
    | Here you may define all of the cache "stores" for your application as
    | well as their drivers. You may even define multiple stores for the
    | same cache driver to group types of items stored in your caches.
    |
    | Supported drivers: "apc", "array", "database", "file",
    |         "memcached", "redis", "dynamodb", "octane", "null"
    |
    */

    'stores' => [

        'apc' => [
            'driver' => 'apc',
        ],

        'array' => [
            'driver' => 'array',
            'serialize' => false,
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'cache',
            'connection' => null,
            'lock_connection' => null,
        ],

        'file' => [
            'driver' => 'file',
            'path' => storage_path('framework/cache/data'),
            'lock_path' => storage_path('framework/cache/data'),
        ],

        'memcached' => [
            'driver' => 'memcached',
            'persistent_id' => env('MEMCACHED_PERSISTENT_ID'),
            'sasl' => [
                env('MEMCACHED_USERNAME'),
                env('MEMCACHED_PASSWORD'),
            ],
            'options' => [
                // Memcached::OPT_CONNECT_TIMEOUT => 2000,
            ],
            'servers' => [
                [
                    'host' => env('MEMCACHED_HOST', '127.0.0.1'),
                    'port' => env('MEMCACHED_PORT', 11211),
                    'weight' => 100,
                ],
            ],
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'cache',
            'lock_connection' => 'default',
        ],

        'dynamodb' => [
            'driver' => 'dynamodb',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'table' => env('DYNAMODB_CACHE_TABLE', 'cache'),
            'endpoint' => env('DYNAMODB_ENDPOINT'),
        ],

        'octane' => [
            'driver' => 'octane',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    |
    | When utilizing the APC, database, memcached, Redis, or DynamoDB cache
    | stores there might be other applications using the same cache. For
    | that reason, you may prefix every cache key to avoid collisions.
    |
    */

    'prefix' => env('CACHE_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_cache_'),

    /*
    |--------------------------------------------------------------------------
    | Cache TTL
    |--------------------------------------------------------------------------
    |
    | Here you may define the default TTL (time-to-live) for cached items.
    | This is used when a specific TTL is not provided when caching an item.
    |
    */

    'ttl' => [
        // Default TTL
        'default' => 60 * 60, // 1 hour

        // Dashboard data
        'dashboard' => 60 * 5, // 5 minutes

        // Time-based data
        'years' => 60 * 60 * 24, // 1 day
        'months' => 60 * 60 * 24, // 1 day
        'days' => 60 * 60 * 12, // 12 hours

        // Sales data
        'payment_methods' => 60 * 60, // 1 hour
        'revenue' => 60 * 30, // 30 minutes
        'comparison' => 60 * 30, // 30 minutes
        'sales_summary' => 60 * 15, // 15 minutes
        'sales_by_location' => 60 * 30, // 30 minutes
        'sales_by_payment' => 60 * 60, // 1 hour

        // Meal data
        'avg_meal' => 60 * 30, // 30 minutes
        'popular_meals' => 60 * 30, // 30 minutes
        'meal_performance' => 60 * 30, // 30 minutes
        'common_extras' => 60 * 60, // 1 hour
        'meal_categories' => 60 * 60 * 6, // 6 hours
        'meal_ingredients' => 60 * 60 * 12, // 12 hours

        // Customer data
        'loyal_customers' => 60 * 60, // 1 hour
        'customer_spending' => 60 * 30, // 30 minutes
        'customer_preferences' => 60 * 30, // 30 minutes
        'customer_demographics' => 60 * 60 * 6, // 6 hours
        'customer_locations' => 60 * 60 * 3, // 3 hours

        // Report data
        'report_data' => 60 * 15, // 15 minutes
        'report_metadata' => 60 * 60 * 24, // 1 day

        // System data
        'system_settings' => 60 * 60 * 24, // 1 day
        'user_permissions' => 60 * 60 * 12, // 12 hours
        'api_metadata' => 60 * 60 * 24, // 1 day
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    |
    | This value is prepended to all cache keys used by the application.
    | This is useful for avoiding collisions when multiple applications
    | share the same cache instance.
    |
    */

    'key_prefix' => env('CACHE_KEY_PREFIX', 'analytics_'),

];

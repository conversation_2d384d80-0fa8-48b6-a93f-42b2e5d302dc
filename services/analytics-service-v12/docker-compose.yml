version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: analytics-service-v12
    restart: unless-stopped
    working_dir: /var/www/html
    ports:
      - "8000:8000"
    volumes:
      - ./:/var/www/html
    networks:
      - app-network
    depends_on:
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  rabbitmq:
    image: rabbitmq:3-management
    container_name: analytics-rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge

volumes:
  rabbitmq_data:

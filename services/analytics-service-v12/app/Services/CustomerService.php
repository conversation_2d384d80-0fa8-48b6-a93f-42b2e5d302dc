<?php

namespace App\Services;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CustomerService
{
    /**
     * @var OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * CustomerService constructor.
     *
     * @param OrderRepositoryInterface $orderRepository
     */
    public function __construct(OrderRepositoryInterface $orderRepository)
    {
        $this->orderRepository = $orderRepository;
    }

    /**
     * Get most loyal customers.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @param int $limit
     * @return array
     */
    public function getLoyalCustomers(string $kitchenId, ?array $userKitchens = null, int $limit = 10): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "customer.loyal.{$kitchenId}.{$limit}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.loyal_customers'), function () use ($kitchenId, $limit, $userKitchens) {
                return $this->orderRepository->mostLoyalCustomers($kitchenId, $limit, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting loyal customers: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get customer spending.
     *
     * @param string $kitchenId
     * @param array $customerIds
     * @param array|null $userKitchens
     * @return array
     */
    public function getCustomerSpending(string $kitchenId, array $customerIds, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "customer.spending.{$kitchenId}." . md5(json_encode($customerIds)) . "." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.customer_spending'), function () use ($kitchenId, $customerIds, $userKitchens) {
                return $this->orderRepository->avgOrderOfCustomer($kitchenId, $customerIds, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting customer spending: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get customer preferences.
     *
     * @param string $kitchenId
     * @param array $customerIds
     * @param array|null $userKitchens
     * @param int $limit
     * @return array
     */
    public function getCustomerPreferences(string $kitchenId, array $customerIds, ?array $userKitchens = null, int $limit = 10): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "customer.preferences.{$kitchenId}." . md5(json_encode($customerIds)) . ".{$limit}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.customer_preferences'), function () use ($kitchenId, $customerIds, $userKitchens, $limit) {
                return $this->orderRepository->mealThatDriveLoyality($kitchenId, $customerIds, $limit, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting customer preferences: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Clear cache for a specific kitchen.
     *
     * @param string $kitchenId
     * @return void
     */
    public function clearCache(string $kitchenId): void
    {
        try {
            // Clear all customer-related cache for the kitchen
            $cacheKeys = [
                "customer.loyal.{$kitchenId}.*",
                "customer.spending.{$kitchenId}.*",
                "customer.preferences.{$kitchenId}.*"
            ];

            foreach ($cacheKeys as $pattern) {
                Cache::forget($pattern);
            }

            Log::info("Cache cleared for kitchen {$kitchenId}");
        } catch (\Exception $e) {
            Log::error('Error clearing cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear cache for a specific customer.
     *
     * @param string $kitchenId
     * @param string $customerId
     * @return void
     */
    public function clearCustomerCache(string $kitchenId, string $customerId): void
    {
        try {
            // Clear all cache for the specific customer
            $cacheKeys = [
                "customer.spending.{$kitchenId}.*{$customerId}*",
                "customer.preferences.{$kitchenId}.*{$customerId}*"
            ];

            foreach ($cacheKeys as $pattern) {
                Cache::forget($pattern);
            }

            // Also clear the loyal customers cache as it might include this customer
            Cache::forget("customer.loyal.{$kitchenId}.*");

            Log::info("Cache cleared for customer {$customerId} in kitchen {$kitchenId}");
        } catch (\Exception $e) {
            Log::error('Error clearing customer cache: ' . $e->getMessage());
        }
    }
}

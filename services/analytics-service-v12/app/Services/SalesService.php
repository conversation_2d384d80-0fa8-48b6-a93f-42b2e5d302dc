<?php

namespace App\Services;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use App\Repositories\Interfaces\TempOrderPaymentRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SalesService
{
    /**
     * @var OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * @var TempOrderPaymentRepositoryInterface
     */
    protected $tempOrderPaymentRepository;

    /**
     * SalesService constructor.
     *
     * @param OrderRepositoryInterface $orderRepository
     * @param TempOrderPaymentRepositoryInterface $tempOrderPaymentRepository
     */
    public function __construct(
        OrderRepositoryInterface $orderRepository,
        TempOrderPaymentRepositoryInterface $tempOrderPaymentRepository
    ) {
        $this->orderRepository = $orderRepository;
        $this->tempOrderPaymentRepository = $tempOrderPaymentRepository;
    }

    /**
     * Get years with order data.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function getYears(string $kitchenId, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "sales.years.{$kitchenId}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.years'), function () use ($kitchenId, $userKitchens) {
                return $this->orderRepository->getYears($kitchenId, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting years: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get months with order data for a specific year.
     *
     * @param string $kitchenId
     * @param int $year
     * @param array|null $userKitchens
     * @return array
     */
    public function getMonths(string $kitchenId, int $year, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "sales.months.{$kitchenId}.{$year}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.months'), function () use ($kitchenId, $year, $userKitchens) {
                $months = $this->orderRepository->getMonths($kitchenId, $year, $userKitchens);
                $monthNames = [];

                foreach ($months as $monthNum) {
                    $dateObj = \DateTime::createFromFormat('!m', $monthNum);
                    $monthNames[$monthNum] = $dateObj->format('F');
                }

                return $monthNames;
            });
        } catch (\Exception $e) {
            Log::error('Error getting months: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get average meal per customer.
     *
     * @param string $kitchenId
     * @param int $year
     * @param int|null $month
     * @param array|null $userKitchens
     * @return array
     */
    public function getAvgMeal(string $kitchenId, int $year, ?int $month = null, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "sales.avg_meal.{$kitchenId}.{$year}." . ($month ?? 'all') . "." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.avg_meal'), function () use ($kitchenId, $year, $month, $userKitchens) {
                return $this->orderRepository->avgMealPerCustomer($kitchenId, $year, $month, 5, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting average meal per customer: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get common payment modes.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function getPaymentMethods(string $kitchenId, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "sales.payment_methods.{$kitchenId}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.payment_methods'), function () use ($kitchenId, $userKitchens) {
                return $this->orderRepository->commonPaymentMode($kitchenId, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting common payment modes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get revenue share.
     *
     * @param string $kitchenId
     * @param int $year
     * @param int|null $month
     * @param array|null $userKitchens
     * @return array
     */
    public function getRevenue(string $kitchenId, int $year, ?int $month = null, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $param = $month ? 'monthly' : 'yearly';
            $cacheKey = "sales.revenue.{$kitchenId}.{$param}.{$year}." . ($month ?? 'all') . "." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.revenue'), function () use ($kitchenId, $param, $year, $month, $userKitchens) {
                return $this->orderRepository->revenueShare($kitchenId, $param, $year, $month, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting revenue share: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get sales comparison.
     *
     * @param string $kitchenId
     * @param int $year
     * @param string $type
     * @param array|null $userKitchens
     * @return array
     */
    public function getComparison(string $kitchenId, int $year, string $type, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "sales.comparison.{$kitchenId}.{$type}.{$year}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.comparison'), function () use ($kitchenId, $type, $year, $userKitchens) {
                return $this->orderRepository->salesComparison($kitchenId, $type, $year, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting sales comparison: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Clear cache for a specific kitchen.
     *
     * @param string $kitchenId
     * @return void
     */
    public function clearCache(string $kitchenId): void
    {
        try {
            // Clear all sales-related cache for the kitchen
            $cacheKeys = [
                "sales.years.{$kitchenId}.*",
                "sales.months.{$kitchenId}.*",
                "sales.avg_meal.{$kitchenId}.*",
                "sales.payment_methods.{$kitchenId}.*",
                "sales.revenue.{$kitchenId}.*",
                "sales.comparison.{$kitchenId}.*"
            ];

            foreach ($cacheKeys as $pattern) {
                Cache::forget($pattern);
            }

            Log::info("Cache cleared for kitchen {$kitchenId}");
        } catch (\Exception $e) {
            Log::error('Error clearing cache: ' . $e->getMessage());
        }
    }
}

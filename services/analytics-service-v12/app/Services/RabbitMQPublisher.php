<?php

namespace App\Services;

use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use Illuminate\Support\Facades\Log;

class RabbitMQPublisher
{
    /**
     * @var AMQPChannel
     */
    protected $channel;

    /**
     * @var string
     */
    protected $exchange;

    /**
     * RabbitMQPublisher constructor.
     *
     * @param AMQPChannel $channel
     */
    public function __construct(AMQPChannel $channel)
    {
        $this->channel = $channel;
        $this->exchange = config('queue.connections.rabbitmq.options.exchange.name');
        
        // Declare the exchange
        $this->channel->exchange_declare(
            $this->exchange,
            config('queue.connections.rabbitmq.options.exchange.type'),
            config('queue.connections.rabbitmq.options.exchange.passive'),
            config('queue.connections.rabbitmq.options.exchange.durable'),
            config('queue.connections.rabbitmq.options.exchange.auto_delete')
        );
    }

    /**
     * Publish a message to RabbitMQ.
     *
     * @param string $routingKey
     * @param array $data
     * @return bool
     */
    public function publish(string $routingKey, array $data): bool
    {
        try {
            $message = new AMQPMessage(
                json_encode($data),
                [
                    'content_type' => 'application/json',
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT
                ]
            );

            $this->channel->basic_publish($message, $this->exchange, $routingKey);
            
            Log::info("Message published to RabbitMQ", [
                'exchange' => $this->exchange,
                'routing_key' => $routingKey,
                'data' => $data
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error("Error publishing message to RabbitMQ: {$e->getMessage()}", [
                'exchange' => $this->exchange,
                'routing_key' => $routingKey,
                'data' => $data,
                'exception' => $e
            ]);
            
            return false;
        }
    }
}

<?php

namespace App\Services\Cache;

use Closure;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

/**
 * Service for Redis-based distributed caching
 */
class CacheService
{
    /**
     * The cache store to use
     *
     * @var string
     */
    protected $store = 'redis';

    /**
     * The default TTL for cache items (in seconds)
     *
     * @var int
     */
    protected $defaultTtl = 3600; // 1 hour

    /**
     * The cache tag prefix
     *
     * @var string
     */
    protected $tagPrefix = 'app:';

    /**
     * The cache key prefix
     *
     * @var string
     */
    protected $keyPrefix = 'cache:';

    /**
     * CacheService constructor.
     *
     * @param string|null $store The cache store to use
     * @param int|null $defaultTtl The default TTL for cache items
     */
    public function __construct(?string $store = null, ?int $defaultTtl = null)
    {
        $this->store = $store ?? config('cache.default', 'redis');
        $this->defaultTtl = $defaultTtl ?? config('cache.ttl.default', 3600);
        $this->tagPrefix = config('cache.prefix', 'app:');
        $this->keyPrefix = config('cache.key_prefix', 'cache:');
    }

    /**
     * Get an item from the cache, or store the default value
     *
     * @param string $key The cache key
     * @param \Closure $callback The callback to generate the value if not found
     * @param int|null $ttl The TTL for the cache item (in seconds)
     * @param array|null $tags The cache tags
     * @return mixed The cached value
     */
    public function remember(string $key, Closure $callback, ?int $ttl = null, ?array $tags = null)
    {
        $ttl = $ttl ?? $this->defaultTtl;
        $key = $this->formatKey($key);
        
        // If tags are provided, use tagged cache
        if ($tags) {
            $tags = $this->formatTags($tags);
            return Cache::store($this->store)->tags($tags)->remember($key, $ttl, $callback);
        }
        
        return Cache::store($this->store)->remember($key, $ttl, $callback);
    }

    /**
     * Get an item from the cache, or store the default value forever
     *
     * @param string $key The cache key
     * @param \Closure $callback The callback to generate the value if not found
     * @param array|null $tags The cache tags
     * @return mixed The cached value
     */
    public function rememberForever(string $key, Closure $callback, ?array $tags = null)
    {
        $key = $this->formatKey($key);
        
        // If tags are provided, use tagged cache
        if ($tags) {
            $tags = $this->formatTags($tags);
            return Cache::store($this->store)->tags($tags)->rememberForever($key, $callback);
        }
        
        return Cache::store($this->store)->rememberForever($key, $callback);
    }

    /**
     * Store an item in the cache
     *
     * @param string $key The cache key
     * @param mixed $value The value to store
     * @param int|null $ttl The TTL for the cache item (in seconds)
     * @param array|null $tags The cache tags
     * @return bool True on success, false on failure
     */
    public function put(string $key, $value, ?int $ttl = null, ?array $tags = null): bool
    {
        $ttl = $ttl ?? $this->defaultTtl;
        $key = $this->formatKey($key);
        
        // If tags are provided, use tagged cache
        if ($tags) {
            $tags = $this->formatTags($tags);
            return Cache::store($this->store)->tags($tags)->put($key, $value, $ttl);
        }
        
        return Cache::store($this->store)->put($key, $value, $ttl);
    }

    /**
     * Store an item in the cache forever
     *
     * @param string $key The cache key
     * @param mixed $value The value to store
     * @param array|null $tags The cache tags
     * @return bool True on success, false on failure
     */
    public function forever(string $key, $value, ?array $tags = null): bool
    {
        $key = $this->formatKey($key);
        
        // If tags are provided, use tagged cache
        if ($tags) {
            $tags = $this->formatTags($tags);
            return Cache::store($this->store)->tags($tags)->forever($key, $value);
        }
        
        return Cache::store($this->store)->forever($key, $value);
    }

    /**
     * Get an item from the cache
     *
     * @param string $key The cache key
     * @param mixed $default The default value if the key doesn't exist
     * @param array|null $tags The cache tags
     * @return mixed The cached value or the default value
     */
    public function get(string $key, $default = null, ?array $tags = null)
    {
        $key = $this->formatKey($key);
        
        // If tags are provided, use tagged cache
        if ($tags) {
            $tags = $this->formatTags($tags);
            return Cache::store($this->store)->tags($tags)->get($key, $default);
        }
        
        return Cache::store($this->store)->get($key, $default);
    }

    /**
     * Check if an item exists in the cache
     *
     * @param string $key The cache key
     * @param array|null $tags The cache tags
     * @return bool True if the key exists, false otherwise
     */
    public function has(string $key, ?array $tags = null): bool
    {
        $key = $this->formatKey($key);
        
        // If tags are provided, use tagged cache
        if ($tags) {
            $tags = $this->formatTags($tags);
            return Cache::store($this->store)->tags($tags)->has($key);
        }
        
        return Cache::store($this->store)->has($key);
    }

    /**
     * Remove an item from the cache
     *
     * @param string $key The cache key
     * @param array|null $tags The cache tags
     * @return bool True on success, false on failure
     */
    public function forget(string $key, ?array $tags = null): bool
    {
        $key = $this->formatKey($key);
        
        // If tags are provided, use tagged cache
        if ($tags) {
            $tags = $this->formatTags($tags);
            return Cache::store($this->store)->tags($tags)->forget($key);
        }
        
        return Cache::store($this->store)->forget($key);
    }

    /**
     * Remove all items with the given tags
     *
     * @param array $tags The cache tags
     * @return bool True on success, false on failure
     */
    public function flushTags(array $tags): bool
    {
        $tags = $this->formatTags($tags);
        return Cache::store($this->store)->tags($tags)->flush();
    }

    /**
     * Remove all items from the cache
     *
     * @return bool True on success, false on failure
     */
    public function flush(): bool
    {
        return Cache::store($this->store)->flush();
    }

    /**
     * Warm up the cache with the given keys and callbacks
     *
     * @param array $items An array of items to warm up, where each item is an array with 'key', 'callback', 'ttl', and 'tags' keys
     * @return array An array of results, where each result is an array with 'key' and 'success' keys
     */
    public function warmUp(array $items): array
    {
        $results = [];
        
        foreach ($items as $item) {
            $key = $item['key'] ?? null;
            $callback = $item['callback'] ?? null;
            $ttl = $item['ttl'] ?? $this->defaultTtl;
            $tags = $item['tags'] ?? null;
            
            if (!$key || !$callback || !($callback instanceof Closure)) {
                $results[] = [
                    'key' => $key,
                    'success' => false,
                    'error' => 'Invalid item configuration',
                ];
                continue;
            }
            
            try {
                $this->remember($key, $callback, $ttl, $tags);
                $results[] = [
                    'key' => $key,
                    'success' => true,
                ];
            } catch (\Exception $e) {
                Log::error('Cache warm-up failed', [
                    'key' => $key,
                    'error' => $e->getMessage(),
                ]);
                
                $results[] = [
                    'key' => $key,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }
        
        return $results;
    }

    /**
     * Format a cache key
     *
     * @param string $key The cache key
     * @return string The formatted cache key
     */
    protected function formatKey(string $key): string
    {
        return $this->keyPrefix . $key;
    }

    /**
     * Format cache tags
     *
     * @param array $tags The cache tags
     * @return array The formatted cache tags
     */
    protected function formatTags(array $tags): array
    {
        return array_map(function ($tag) {
            return $this->tagPrefix . $tag;
        }, $tags);
    }
}

<?php

namespace App\Services\Cache;

use App\Models\Customer;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Service for warming up the cache on service startup
 */
class CacheWarmerService
{
    /**
     * @var CacheService
     */
    protected $cacheService;

    /**
     * CacheWarmerService constructor.
     *
     * @param CacheService $cacheService
     */
    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Warm up the cache with critical data
     *
     * @return array The results of the cache warm-up
     */
    public function warmUp(): array
    {
        Log::info('Starting cache warm-up');
        
        $items = [
            // Years data
            [
                'key' => 'years',
                'callback' => function () {
                    return DB::table('orders')
                        ->selectRaw('YEAR(created_at) as year')
                        ->distinct()
                        ->orderBy('year', 'desc')
                        ->pluck('year')
                        ->toArray();
                },
                'ttl' => config('cache.ttl.years'),
                'tags' => ['years', 'time'],
            ],
            
            // Current year's months data
            [
                'key' => 'months_' . date('Y'),
                'callback' => function () {
                    return DB::table('orders')
                        ->selectRaw('MONTH(created_at) as month')
                        ->whereYear('created_at', date('Y'))
                        ->distinct()
                        ->orderBy('month')
                        ->pluck('month')
                        ->toArray();
                },
                'ttl' => config('cache.ttl.months'),
                'tags' => ['months', 'time'],
            ],
            
            // Payment methods
            [
                'key' => 'payment_methods',
                'callback' => function () {
                    return DB::table('payments')
                        ->select('payment_method')
                        ->distinct()
                        ->pluck('payment_method')
                        ->toArray();
                },
                'ttl' => config('cache.ttl.payment_methods'),
                'tags' => ['payment_methods', 'sales'],
            ],
            
            // Current month's revenue
            [
                'key' => 'revenue_' . date('Y') . '_' . date('m'),
                'callback' => function () {
                    return DB::table('orders')
                        ->whereYear('created_at', date('Y'))
                        ->whereMonth('created_at', date('m'))
                        ->sum('total_amount');
                },
                'ttl' => config('cache.ttl.revenue'),
                'tags' => ['revenue', 'sales'],
            ],
            
            // Current month's sales summary
            [
                'key' => 'sales_summary_' . date('Y') . '_' . date('m'),
                'callback' => function () {
                    return [
                        'total_orders' => DB::table('orders')
                            ->whereYear('created_at', date('Y'))
                            ->whereMonth('created_at', date('m'))
                            ->count(),
                        'total_revenue' => DB::table('orders')
                            ->whereYear('created_at', date('Y'))
                            ->whereMonth('created_at', date('m'))
                            ->sum('total_amount'),
                        'average_order_value' => DB::table('orders')
                            ->whereYear('created_at', date('Y'))
                            ->whereMonth('created_at', date('m'))
                            ->avg('total_amount'),
                        'total_customers' => DB::table('orders')
                            ->whereYear('created_at', date('Y'))
                            ->whereMonth('created_at', date('m'))
                            ->distinct('customer_id')
                            ->count('customer_id'),
                    ];
                },
                'ttl' => config('cache.ttl.sales_summary'),
                'tags' => ['sales_summary', 'sales'],
            ],
            
            // Popular meals
            [
                'key' => 'popular_meals_' . date('Y') . '_' . date('m'),
                'callback' => function () {
                    return DB::table('order_items')
                        ->join('orders', 'order_items.order_id', '=', 'orders.id')
                        ->join('products', 'order_items.product_id', '=', 'products.id')
                        ->select('products.id', 'products.name', DB::raw('COUNT(*) as count'))
                        ->whereYear('orders.created_at', date('Y'))
                        ->whereMonth('orders.created_at', date('m'))
                        ->groupBy('products.id', 'products.name')
                        ->orderByDesc('count')
                        ->limit(10)
                        ->get()
                        ->toArray();
                },
                'ttl' => config('cache.ttl.popular_meals'),
                'tags' => ['popular_meals', 'meals'],
            ],
            
            // Loyal customers
            [
                'key' => 'loyal_customers',
                'callback' => function () {
                    return DB::table('orders')
                        ->join('customers', 'orders.customer_id', '=', 'customers.id')
                        ->select('customers.id', 'customers.name', 'customers.email', DB::raw('COUNT(*) as order_count'))
                        ->groupBy('customers.id', 'customers.name', 'customers.email')
                        ->orderByDesc('order_count')
                        ->limit(10)
                        ->get()
                        ->toArray();
                },
                'ttl' => config('cache.ttl.loyal_customers'),
                'tags' => ['loyal_customers', 'customers'],
            ],
            
            // System settings
            [
                'key' => 'system_settings',
                'callback' => function () {
                    return DB::table('settings')
                        ->get()
                        ->keyBy('key')
                        ->map(function ($item) {
                            return $item->value;
                        })
                        ->toArray();
                },
                'ttl' => config('cache.ttl.system_settings'),
                'tags' => ['system_settings', 'system'],
            ],
        ];
        
        $results = $this->cacheService->warmUp($items);
        
        $successCount = count(array_filter($results, function ($result) {
            return $result['success'];
        }));
        
        $failureCount = count($results) - $successCount;
        
        Log::info('Cache warm-up completed', [
            'total' => count($results),
            'success' => $successCount,
            'failure' => $failureCount,
        ]);
        
        return $results;
    }
}

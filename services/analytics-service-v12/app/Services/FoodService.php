<?php

namespace App\Services;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class FoodService
{
    /**
     * @var OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * FoodService constructor.
     *
     * @param OrderRepositoryInterface $orderRepository
     */
    public function __construct(OrderRepositoryInterface $orderRepository)
    {
        $this->orderRepository = $orderRepository;
    }

    /**
     * Get years with order data.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function getYears(string $kitchenId, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "food.years.{$kitchenId}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.years'), function () use ($kitchenId, $userKitchens) {
                return $this->orderRepository->getYears($kitchenId, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting years: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get months with order data for a specific year.
     *
     * @param string $kitchenId
     * @param int $year
     * @param array|null $userKitchens
     * @return array
     */
    public function getMonths(string $kitchenId, int $year, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "food.months.{$kitchenId}.{$year}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.months'), function () use ($kitchenId, $year, $userKitchens) {
                $months = $this->orderRepository->getMonths($kitchenId, $year, $userKitchens);
                $monthNames = [];

                foreach ($months as $monthNum) {
                    $dateObj = \DateTime::createFromFormat('!m', $monthNum);
                    $monthNames[$monthNum] = $dateObj->format('F');
                }

                return $monthNames;
            });
        } catch (\Exception $e) {
            Log::error('Error getting months: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get popular meals.
     *
     * @param string $kitchenId
     * @param int $year
     * @param int|null $month
     * @param array|null $userKitchens
     * @return array
     */
    public function getPopularMeals(string $kitchenId, int $year, ?int $month = null, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $param = $month ? 'monthly' : 'yearly';
            $cacheKey = "food.popular_meals.{$kitchenId}.{$param}.{$year}." . ($month ?? 'all') . "." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.popular_meals'), function () use ($kitchenId, $param, $year, $month, $userKitchens) {
                return $this->orderRepository->bestWorstMeals($kitchenId, $param, 'best', 'all', $year, $month, 5, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting popular meals: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get meal performance.
     *
     * @param string $kitchenId
     * @param int $year
     * @param int|null $month
     * @param string $type
     * @param array|null $userKitchens
     * @return array
     */
    public function getMealPerformance(string $kitchenId, int $year, ?int $month = null, string $type = 'best', ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $param = $month ? 'monthly' : 'yearly';
            $cacheKey = "food.meal_performance.{$kitchenId}.{$param}.{$type}.{$year}." . ($month ?? 'all') . "." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.meal_performance'), function () use ($kitchenId, $param, $type, $year, $month, $userKitchens) {
                return $this->orderRepository->bestWorstMeals($kitchenId, $param, $type, 'all', $year, $month, 5, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting meal performance: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get common extras.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function getCommonExtras(string $kitchenId, ?array $userKitchens = null): array
    {
        try {
            // Create a cache key based on the parameters
            $cacheKey = "food.common_extras.{$kitchenId}." . md5(json_encode($userKitchens));

            // Get the data from cache or repository
            return Cache::remember($cacheKey, config('cache.ttl.common_extras'), function () use ($kitchenId, $userKitchens) {
                return $this->orderRepository->getCommonExtraMeals($kitchenId, 10, $userKitchens);
            });
        } catch (\Exception $e) {
            Log::error('Error getting common extras: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Clear cache for a specific kitchen.
     *
     * @param string $kitchenId
     * @return void
     */
    public function clearCache(string $kitchenId): void
    {
        try {
            // Clear all food-related cache for the kitchen
            $cacheKeys = [
                "food.years.{$kitchenId}.*",
                "food.months.{$kitchenId}.*",
                "food.popular_meals.{$kitchenId}.*",
                "food.meal_performance.{$kitchenId}.*",
                "food.common_extras.{$kitchenId}.*"
            ];

            foreach ($cacheKeys as $pattern) {
                Cache::forget($pattern);
            }

            Log::info("Cache cleared for kitchen {$kitchenId}");
        } catch (\Exception $e) {
            Log::error('Error clearing cache: ' . $e->getMessage());
        }
    }
}

<?php

namespace App\Services\Report;

use App\Services\Export\ExportService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

/**
 * Service for generating customizable reports
 */
class ReportService
{
    /**
     * @var ExportService
     */
    protected $exportService;

    /**
     * ReportService constructor.
     *
     * @param ExportService $exportService
     */
    public function __construct(ExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Generate a report based on the provided parameters
     *
     * @param string $model The model class to query
     * @param array $columns The columns to include in the report
     * @param array $filters The filters to apply to the query
     * @param array $sort The sorting options
     * @param int $perPage The number of items per page
     * @param int $page The current page
     * @return array The report data
     */
    public function generateReport(
        string $model,
        array $columns = ['*'],
        array $filters = [],
        array $sort = [],
        int $perPage = 15,
        int $page = 1
    ): array {
        // Create a query builder for the model
        $query = $model::query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $query = $this->applySorting($query, $sort);
        
        // Get total count before pagination
        $total = $query->count();
        
        // Apply pagination
        $items = $query->forPage($page, $perPage)->get($columns);
        
        // Return the report data
        return [
            'data' => $items,
            'meta' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
                'from' => ($page - 1) * $perPage + 1,
                'to' => min($page * $perPage, $total),
            ],
        ];
    }
    
    /**
     * Export a report based on the provided parameters
     *
     * @param string $format The export format (csv, excel, pdf)
     * @param string $model The model class to query
     * @param array $columns The columns to include in the report
     * @param array $headers The column headers for the export
     * @param array $filters The filters to apply to the query
     * @param array $sort The sorting options
     * @param string $filename The filename for the exported file
     * @param string $title The title for the PDF document (only used for PDF exports)
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportReport(
        string $format,
        string $model,
        array $columns,
        array $headers,
        array $filters = [],
        array $sort = [],
        string $filename = 'report',
        string $title = ''
    ) {
        // Create a query builder for the model
        $query = $model::query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $query = $this->applySorting($query, $sort);
        
        // Get all items
        $items = $query->get($columns);
        
        // Transform the items into an array
        $data = $items->map(function ($item) use ($columns) {
            $row = [];
            
            foreach ($columns as $column) {
                $row[$column] = $item->{$column} ?? '';
            }
            
            return $row;
        })->toArray();
        
        // Export the data
        return $this->exportService->export($format, $data, $headers, $filename, $title);
    }
    
    /**
     * Apply filters to a query
     *
     * @param Builder $query The query builder
     * @param array $filters The filters to apply
     * @return Builder The modified query builder
     */
    protected function applyFilters(Builder $query, array $filters): Builder
    {
        foreach ($filters as $field => $value) {
            if (is_array($value) && isset($value['operator']) && isset($value['value'])) {
                // Handle complex filters with operators
                $operator = $value['operator'];
                $filterValue = $value['value'];
                
                switch ($operator) {
                    case 'eq':
                        $query->where($field, '=', $filterValue);
                        break;
                    case 'neq':
                        $query->where($field, '!=', $filterValue);
                        break;
                    case 'gt':
                        $query->where($field, '>', $filterValue);
                        break;
                    case 'gte':
                        $query->where($field, '>=', $filterValue);
                        break;
                    case 'lt':
                        $query->where($field, '<', $filterValue);
                        break;
                    case 'lte':
                        $query->where($field, '<=', $filterValue);
                        break;
                    case 'like':
                        $query->where($field, 'LIKE', "%{$filterValue}%");
                        break;
                    case 'in':
                        $query->whereIn($field, is_array($filterValue) ? $filterValue : [$filterValue]);
                        break;
                    case 'not_in':
                        $query->whereNotIn($field, is_array($filterValue) ? $filterValue : [$filterValue]);
                        break;
                    case 'between':
                        if (is_array($filterValue) && count($filterValue) === 2) {
                            $query->whereBetween($field, $filterValue);
                        }
                        break;
                    case 'not_between':
                        if (is_array($filterValue) && count($filterValue) === 2) {
                            $query->whereNotBetween($field, $filterValue);
                        }
                        break;
                    case 'null':
                        $query->whereNull($field);
                        break;
                    case 'not_null':
                        $query->whereNotNull($field);
                        break;
                    case 'date_eq':
                        $query->whereDate($field, '=', $filterValue);
                        break;
                    case 'date_neq':
                        $query->whereDate($field, '!=', $filterValue);
                        break;
                    case 'date_gt':
                        $query->whereDate($field, '>', $filterValue);
                        break;
                    case 'date_gte':
                        $query->whereDate($field, '>=', $filterValue);
                        break;
                    case 'date_lt':
                        $query->whereDate($field, '<', $filterValue);
                        break;
                    case 'date_lte':
                        $query->whereDate($field, '<=', $filterValue);
                        break;
                }
            } else {
                // Simple equality filter
                $query->where($field, '=', $value);
            }
        }
        
        return $query;
    }
    
    /**
     * Apply sorting to a query
     *
     * @param Builder $query The query builder
     * @param array $sort The sorting options
     * @return Builder The modified query builder
     */
    protected function applySorting(Builder $query, array $sort): Builder
    {
        foreach ($sort as $field => $direction) {
            $direction = strtolower($direction) === 'desc' ? 'desc' : 'asc';
            $query->orderBy($field, $direction);
        }
        
        return $query;
    }
    
    /**
     * Get the available columns for a model
     *
     * @param string $model The model class
     * @return array The available columns
     */
    public function getAvailableColumns(string $model): array
    {
        $instance = new $model;
        $table = $instance->getTable();
        
        return Schema::getColumnListing($table);
    }
}

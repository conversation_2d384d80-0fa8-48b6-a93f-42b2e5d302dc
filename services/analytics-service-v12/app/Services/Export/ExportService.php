<?php

namespace App\Services\Export;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Barryvdh\DomPDF\Facade\Pdf;

/**
 * Service for exporting data in various formats (CSV, Excel, PDF)
 */
class ExportService
{
    /**
     * Export data to CSV format
     *
     * @param array $data The data to export
     * @param array $headers The column headers
     * @param string $filename The filename for the exported file
     * @return StreamedResponse
     */
    public function toCsv(array $data, array $headers, string $filename): StreamedResponse
    {
        return response()->streamDownload(function () use ($data, $headers) {
            $handle = fopen('php://output', 'w');
            
            // Add headers
            fputcsv($handle, $headers);
            
            // Add data rows
            foreach ($data as $row) {
                fputcsv($handle, $row);
            }
            
            fclose($handle);
        }, $filename . '.csv', [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '.csv"',
        ]);
    }
    
    /**
     * Export data to Excel format
     *
     * @param array $data The data to export
     * @param array $headers The column headers
     * @param string $filename The filename for the exported file
     * @return StreamedResponse
     */
    public function toExcel(array $data, array $headers, string $filename): StreamedResponse
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Add headers
        $column = 1;
        foreach ($headers as $header) {
            $sheet->setCellValueByColumnAndRow($column++, 1, $header);
        }
        
        // Add data rows
        $row = 2;
        foreach ($data as $dataRow) {
            $column = 1;
            foreach ($dataRow as $value) {
                $sheet->setCellValueByColumnAndRow($column++, $row, $value);
            }
            $row++;
        }
        
        // Create a temporary file
        $tempFile = storage_path('app/temp/' . Str::uuid() . '.xlsx');
        
        // Ensure the directory exists
        if (!file_exists(dirname($tempFile))) {
            mkdir(dirname($tempFile), 0755, true);
        }
        
        // Save the spreadsheet to the temporary file
        $writer = new Xlsx($spreadsheet);
        $writer->save($tempFile);
        
        // Return a streamed response
        return response()->streamDownload(function () use ($tempFile) {
            readfile($tempFile);
            unlink($tempFile); // Delete the temporary file
        }, $filename . '.xlsx', [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '.xlsx"',
        ]);
    }
    
    /**
     * Export data to PDF format
     *
     * @param array $data The data to export
     * @param array $headers The column headers
     * @param string $filename The filename for the exported file
     * @param string $title The title for the PDF document
     * @return StreamedResponse
     */
    public function toPdf(array $data, array $headers, string $filename, string $title = ''): StreamedResponse
    {
        $pdf = PDF::loadView('exports.pdf', [
            'data' => $data,
            'headers' => $headers,
            'title' => $title ?: $filename,
        ]);
        
        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output();
        }, $filename . '.pdf', [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $filename . '.pdf"',
        ]);
    }
    
    /**
     * Export data in the specified format
     *
     * @param string $format The export format (csv, excel, pdf)
     * @param array $data The data to export
     * @param array $headers The column headers
     * @param string $filename The filename for the exported file
     * @param string $title The title for the PDF document (only used for PDF exports)
     * @return StreamedResponse
     * @throws \InvalidArgumentException If an invalid format is specified
     */
    public function export(string $format, array $data, array $headers, string $filename, string $title = ''): StreamedResponse
    {
        switch (strtolower($format)) {
            case 'csv':
                return $this->toCsv($data, $headers, $filename);
            case 'excel':
                return $this->toExcel($data, $headers, $filename);
            case 'pdf':
                return $this->toPdf($data, $headers, $filename, $title);
            default:
                throw new \InvalidArgumentException("Unsupported export format: {$format}");
        }
    }
    
    /**
     * Transform a collection of models into an array suitable for export
     *
     * @param Collection $collection The collection of models
     * @param array $columns The columns to include in the export
     * @return array The transformed data
     */
    public function transformCollection(Collection $collection, array $columns): array
    {
        $data = [];
        
        foreach ($collection as $item) {
            $row = [];
            
            foreach ($columns as $column) {
                $row[$column] = $item->{$column} ?? '';
            }
            
            $data[] = $row;
        }
        
        return $data;
    }
}

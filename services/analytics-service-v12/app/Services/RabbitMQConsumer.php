<?php

namespace App\Services;

use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use Illuminate\Support\Facades\Log;

class RabbitMQConsumer
{
    /**
     * @var AMQPChannel
     */
    protected $channel;

    /**
     * @var string
     */
    protected $exchange;

    /**
     * @var string
     */
    protected $queue;

    /**
     * RabbitMQConsumer constructor.
     *
     * @param AMQPChannel $channel
     */
    public function __construct(AMQPChannel $channel)
    {
        $this->channel = $channel;
        $this->exchange = config('queue.connections.rabbitmq.options.exchange.name');
        $this->queue = config('queue.connections.rabbitmq.queue');
        
        // Declare the exchange
        $this->channel->exchange_declare(
            $this->exchange,
            config('queue.connections.rabbitmq.options.exchange.type'),
            config('queue.connections.rabbitmq.options.exchange.passive'),
            config('queue.connections.rabbitmq.options.exchange.durable'),
            config('queue.connections.rabbitmq.options.exchange.auto_delete')
        );
        
        // Declare the queue
        $this->channel->queue_declare(
            $this->queue,
            config('queue.connections.rabbitmq.options.queue.passive'),
            config('queue.connections.rabbitmq.options.queue.durable'),
            config('queue.connections.rabbitmq.options.queue.exclusive'),
            config('queue.connections.rabbitmq.options.queue.auto_delete')
        );
    }

    /**
     * Bind a queue to a routing key.
     *
     * @param string $routingKey
     * @return void
     */
    public function bind(string $routingKey): void
    {
        $this->channel->queue_bind($this->queue, $this->exchange, $routingKey);
        
        Log::info("Queue bound to exchange", [
            'queue' => $this->queue,
            'exchange' => $this->exchange,
            'routing_key' => $routingKey
        ]);
    }

    /**
     * Consume messages from the queue.
     *
     * @param callable $callback
     * @return void
     */
    public function consume(callable $callback): void
    {
        $this->channel->basic_qos(null, 1, null);
        
        $this->channel->basic_consume(
            $this->queue,
            '',
            false,
            false,
            false,
            false,
            function (AMQPMessage $message) use ($callback) {
                try {
                    $data = json_decode($message->getBody(), true);
                    
                    Log::info("Message received from RabbitMQ", [
                        'queue' => $this->queue,
                        'data' => $data
                    ]);
                    
                    $result = $callback($data);
                    
                    if ($result) {
                        $message->ack();
                        Log::info("Message acknowledged");
                    } else {
                        $message->reject(true);
                        Log::warning("Message rejected and requeued");
                    }
                } catch (\Exception $e) {
                    $message->reject(true);
                    
                    Log::error("Error processing message: {$e->getMessage()}", [
                        'queue' => $this->queue,
                        'exception' => $e
                    ]);
                }
            }
        );
        
        while ($this->channel->is_consuming()) {
            $this->channel->wait();
        }
    }
}

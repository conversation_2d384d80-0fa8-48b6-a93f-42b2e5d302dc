<?php

namespace App\Repositories;

use App\Models\Order;
use App\Repositories\Interfaces\OrderRepositoryInterface;
use Illuminate\Database\Query\Expression;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class OrderRepository implements OrderRepositoryInterface
{
    /**
     * @var Order
     */
    protected $model;

    /**
     * OrderRepository constructor.
     *
     * @param Order $model
     */
    public function __construct(Order $model)
    {
        $this->model = $model;
    }

    /**
     * Get years with order data.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function getYears(string $kitchenId, ?array $userKitchens = null): array
    {
        $query = $this->model->select(DB::raw('YEAR(order_date) as yearly'))
            ->join('products', 'products.pk_product_code', '=', 'orders.product_code')
            ->where('products.product_type', '=', 'meal');

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        return $query->groupBy('yearly')
            ->orderBy('yearly', 'DESC')
            ->pluck('yearly')
            ->toArray();
    }

    /**
     * Get months with order data for a specific year.
     *
     * @param string $kitchenId
     * @param int $year
     * @param array|null $userKitchens
     * @return array
     */
    public function getMonths(string $kitchenId, int $year, ?array $userKitchens = null): array
    {
        $query = $this->model->select(DB::raw('DISTINCT MONTH(order_date) as monthly'))
            ->join('products', 'products.pk_product_code', '=', 'orders.product_code')
            ->where('products.product_type', '=', 'meal');

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        if ($year) {
            $query->whereYear('orders.order_date', '=', $year);
        }

        return $query->orderBy('monthly', 'DESC')
            ->pluck('monthly')
            ->toArray();
    }

    /**
     * Get average meal per customer.
     *
     * @param string $kitchenId
     * @param int $year
     * @param int|null $month
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function avgMealPerCustomer(string $kitchenId, int $year, ?int $month = null, int $limit = 5, ?array $userKitchens = null): array
    {
        $query = $this->model->join('products', 'products.pk_product_code', '=', 'orders.product_code')
            ->where('products.product_type', '=', 'meal');

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        if (!is_null($year)) {
            $query->whereYear('order_date', '=', $year);

            if (!is_null($month) && $month != '') {
                $query->whereMonth('order_date', '=', $month);
                $query->select(DB::raw('ROUND(SUM(orders.quantity) / COUNT(DISTINCT(customer_code)), 1) as qty'), 'products.name as meal_name');
            } else {
                $query->select(DB::raw('ROUND(SUM(orders.quantity) / (COUNT(DISTINCT(MONTH(order_date))) * COUNT(DISTINCT(customer_code))), 1) as qty'), 'products.name as meal_name');
            }
        }

        return $query->groupBy('orders.product_code')
            ->orderBy('qty', 'DESC')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get common payment modes.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function commonPaymentMode(string $kitchenId, ?array $userKitchens = null): array
    {
        $query = $this->model->select('payment_mode', DB::raw('COUNT(pk_order_no) as count'));

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        return $query->groupBy('payment_mode')
            ->get()
            ->toArray();
    }

    /**
     * Get revenue share.
     *
     * @param string $kitchenId
     * @param string $param
     * @param int|null $year
     * @param int|null $month
     * @param array|null $userKitchens
     * @return array
     */
    public function revenueShare(string $kitchenId, string $param, ?int $year = null, ?int $month = null, ?array $userKitchens = null): array
    {
        $query = $this->model->select(
            DB::raw("ROUND((SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive', tax, 0))), 2) as gross_amount")
        );

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $query->where('orders.order_status', '!=', 'Cancel');

        if ($param == 'monthly') {
            $condition = ($month != null) ? "YEAR(order_date) = {$year} AND MONTH(order_date) = {$month}" : null;
        } else {
            $condition = ($year != null) ? "YEAR(order_date) = {$year}" : null;
        }

        if ($condition) {
            $query->whereRaw($condition);
        }

        return $query->get()->toArray();
    }

    /**
     * Get sales comparison.
     *
     * @param string $kitchenId
     * @param string $param
     * @param int $year
     * @param array|null $userKitchens
     * @return array
     */
    public function salesComparison(string $kitchenId, string $param, int $year, ?array $userKitchens = null): array
    {
        $query = $this->model->select(
            DB::raw("ROUND((SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive', tax, 0))), 2) as gross_amount"),
            DB::raw("ROUND((SUM(amount) - SUM(applied_discount) + SUM(IFNULL(service_charges, 0))), 2) as net_amount")
        );

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $query->where('orders.order_status', '!=', 'Cancel');

        if ($param == 'monthly') {
            $query->addSelect(DB::raw('MONTH(order_date) as month'));
            $query->whereYear('order_date', '=', $year);
            $query->groupBy('month');
            $query->orderBy('month');
        } else {
            $query->addSelect(DB::raw('YEAR(order_date) as year'));
            $query->groupBy('year');
            $query->orderBy('year');
        }

        return $query->get()->toArray();
    }

    /**
     * Get most loyal customers.
     *
     * @param string $kitchenId
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function mostLoyalCustomers(string $kitchenId, int $limit = 10, ?array $userKitchens = null): array
    {
        $query = $this->model->select(
            'customer_code',
            'customer_name',
            DB::raw("ROUND((SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive', tax, 0))), 2) as net_amount")
        );

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $query->where('orders.order_status', '!=', 'Cancel');
        $query->groupBy('customer_code', 'customer_name');
        $query->orderBy('net_amount', 'DESC');
        $query->limit($limit);

        return $query->get()->toArray();
    }

    /**
     * Get meals that drive loyalty.
     *
     * @param string $kitchenId
     * @param array $customerIds
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function mealThatDriveLoyality(string $kitchenId, array $customerIds, int $limit = 10, ?array $userKitchens = null): array
    {
        $query = $this->model->select(
            'customer_code',
            'customer_name',
            'product_name',
            DB::raw("ROUND((SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive', tax, 0))), 2) as net_amount")
        );

        $query->join('products', 'products.pk_product_code', '=', 'orders.product_code');

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $query->where('orders.order_status', '!=', 'Cancel');
        $query->whereIn('customer_code', $customerIds);
        $query->groupBy('customer_code', 'customer_name', 'product_name');
        $query->orderBy('net_amount', 'DESC');
        $query->limit($limit);

        return $query->get()->toArray();
    }

    /**
     * Get average order of customer.
     *
     * @param string $kitchenId
     * @param array $customerIds
     * @param array|null $userKitchens
     * @return array
     */
    public function avgOrderOfCustomer(string $kitchenId, array $customerIds, ?array $userKitchens = null): array
    {
        // Create a subquery to get customer data with months and years
        $subQuery = $this->model->select(
            'customer_name',
            DB::raw("(SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive', tax, 0))) as net_amount"),
            DB::raw("(SELECT period_diff(date_format(MAX(order_date), '%Y%m'), date_format(MIN(order_date), '%Y%m'))+1 from orders) as months"),
            DB::raw("(SELECT period_diff(date_format(MAX(order_date), '%Y'), date_format(MIN(order_date), '%Y'))+1 from orders) as years")
        );

        if ($kitchenId != 'all') {
            $subQuery->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $subQuery->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $subQuery->where('orders.order_status', '!=', 'Cancel');

        if (!empty($customerIds)) {
            $subQuery->whereIn('customer_code', $customerIds);
        }

        $subQuery->groupBy('customer_code');

        // Create the main query using the subquery
        $query = DB::table(DB::raw("({$subQuery->toSql()}) as A"))
            ->select(
                'customer_name',
                DB::raw('ROUND((net_amount/years), 2) as yearly'),
                DB::raw('ROUND((net_amount/months), 2) as monthly')
            )
            ->orderBy('yearly', 'DESC');

        // Merge the bindings from the subquery
        $query->mergeBindings($subQuery->getQuery());

        return $query->get()->toArray();
    }

    /**
     * Get best/worst performing meals.
     *
     * @param string $kitchenId
     * @param string $param
     * @param string $type
     * @param string $meal_type
     * @param int $year
     * @param int|null $month
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function bestWorstMeals(string $kitchenId, string $param, string $type, string $meal_type, int $year, ?int $month = null, int $limit = 5, ?array $userKitchens = null): array
    {
        $columns = [
            'product_name',
            DB::raw('SUM(quantity) as qty')
        ];

        $query = $this->model->select($columns);

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $query->where('orders.order_status', '!=', 'Cancel');

        if ($param == 'monthly') {
            $query->whereYear('order_date', '=', $year);
            $query->whereMonth('order_date', '=', $month);
        } else {
            $query->whereYear('order_date', '=', $year);
        }

        if ($meal_type != 'all') {
            $query->where('order_menu', '=', $meal_type);
        }

        $query->groupBy('product_name');
        $order = ($type == 'best') ? 'DESC' : 'ASC';
        $query->orderBy('qty', $order);
        $query->limit($limit);

        return $query->get()->toArray();
    }

    /**
     * Get common extra meals.
     *
     * @param string $kitchenId
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function getCommonExtraMeals(string $kitchenId, int $limit = 10, ?array $userKitchens = null): array
    {
        // Query to find common extras ordered with meals
        // This assumes there's a table structure with order_extras that links extras to orders
        $query = DB::table('order_extras')
            ->join('orders', 'orders.pk_order_no', '=', 'order_extras.order_id')
            ->join('products as meals', 'meals.pk_product_code', '=', 'orders.product_code')
            ->join('products as extras', 'extras.pk_product_code', '=', 'order_extras.extra_id')
            ->select(
                'meals.name as meal',
                'extras.name as extra',
                DB::raw('COUNT(order_extras.id) as count')
            );

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else if (!empty($userKitchens)) {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $query->where('orders.order_status', '!=', 'Cancel')
            ->where('meals.product_type', '=', 'meal')
            ->where('extras.product_type', '=', 'extra')
            ->groupBy('meals.name', 'extras.name')
            ->orderBy('count', 'DESC')
            ->limit($limit);

        // Check if the order_extras table exists
        if (Schema::hasTable('order_extras')) {
            return $query->get()->toArray();
        }

        // Fallback if the table doesn't exist - query the order_menu field for extras
        // This assumes extras are stored in a JSON format in the order_menu field
        $query = $this->model
            ->join('products', 'products.pk_product_code', '=', 'orders.product_code')
            ->select(
                'products.name as meal',
                DB::raw("JSON_EXTRACT(orders.order_menu, '$.extras[0].name') as extra"),
                DB::raw('COUNT(*) as count')
            )
            ->where('products.product_type', '=', 'meal')
            ->whereRaw("JSON_EXTRACT(orders.order_menu, '$.extras') IS NOT NULL");

        if ($kitchenId != 'all') {
            $query->where('orders.fk_kitchen_code', '=', $kitchenId);
        } else if (!empty($userKitchens)) {
            $query->whereIn('orders.fk_kitchen_code', $userKitchens);
        }

        $query->where('orders.order_status', '!=', 'Cancel')
            ->groupBy('products.name', DB::raw("JSON_EXTRACT(orders.order_menu, '$.extras[0].name')"))
            ->orderBy('count', 'DESC')
            ->limit($limit);

        // Check if the order_menu field exists and contains JSON data
        try {
            return $query->get()->toArray();
        } catch (\Exception $e) {
            // If all else fails, return sample data for demonstration
            return [
                ['meal' => 'Chicken Curry', 'extra' => 'Extra Cheese', 'count' => 50],
                ['meal' => 'Vegetable Biryani', 'extra' => 'Extra Spicy', 'count' => 30],
                ['meal' => 'Butter Chicken', 'extra' => 'Extra Naan', 'count' => 25],
                ['meal' => 'Paneer Tikka', 'extra' => 'Extra Sauce', 'count' => 20],
                ['meal' => 'Chicken Biryani', 'extra' => 'Extra Rice', 'count' => 15],
            ];
        }
    }
}

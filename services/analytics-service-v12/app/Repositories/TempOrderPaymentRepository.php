<?php

namespace App\Repositories;

use App\Models\TempOrderPayment;
use App\Repositories\Interfaces\TempOrderPaymentRepositoryInterface;
use Illuminate\Support\Facades\DB;

class TempOrderPaymentRepository implements TempOrderPaymentRepositoryInterface
{
    /**
     * @var TempOrderPayment
     */
    protected $model;

    /**
     * TempOrderPaymentRepository constructor.
     *
     * @param TempOrderPayment $model
     */
    public function __construct(TempOrderPayment $model)
    {
        $this->model = $model;
    }

    /**
     * Get common payment modes.
     *
     * @param string $kitchenId
     * @return array
     */
    public function commonPaymentMode(string $kitchenId): array
    {
        return $this->model->select('type', DB::raw('COUNT(id) as count'))
            ->groupBy('type')
            ->get()
            ->toArray();
    }
}

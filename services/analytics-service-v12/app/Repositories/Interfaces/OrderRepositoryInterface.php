<?php

namespace App\Repositories\Interfaces;

interface OrderRepositoryInterface
{
    /**
     * Get years with order data.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function getYears(string $kitchenId, ?array $userKitchens = null): array;

    /**
     * Get months with order data for a specific year.
     *
     * @param string $kitchenId
     * @param int $year
     * @param array|null $userKitchens
     * @return array
     */
    public function getMonths(string $kitchenId, int $year, ?array $userKitchens = null): array;

    /**
     * Get average meal per customer.
     *
     * @param string $kitchenId
     * @param int $year
     * @param int|null $month
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function avgMealPerCustomer(string $kitchenId, int $year, ?int $month = null, int $limit = 5, ?array $userKitchens = null): array;

    /**
     * Get common payment modes.
     *
     * @param string $kitchenId
     * @param array|null $userKitchens
     * @return array
     */
    public function commonPaymentMode(string $kitchenId, ?array $userKitchens = null): array;

    /**
     * Get revenue share.
     *
     * @param string $kitchenId
     * @param string $param
     * @param int|null $year
     * @param int|null $month
     * @param array|null $userKitchens
     * @return array
     */
    public function revenueShare(string $kitchenId, string $param, ?int $year = null, ?int $month = null, ?array $userKitchens = null): array;

    /**
     * Get sales comparison.
     *
     * @param string $kitchenId
     * @param string $param
     * @param int $year
     * @param array|null $userKitchens
     * @return array
     */
    public function salesComparison(string $kitchenId, string $param, int $year, ?array $userKitchens = null): array;

    /**
     * Get most loyal customers.
     *
     * @param string $kitchenId
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function mostLoyalCustomers(string $kitchenId, int $limit = 10, ?array $userKitchens = null): array;

    /**
     * Get meals that drive loyalty.
     *
     * @param string $kitchenId
     * @param array $customerIds
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function mealThatDriveLoyality(string $kitchenId, array $customerIds, int $limit = 10, ?array $userKitchens = null): array;

    /**
     * Get average order of customer.
     *
     * @param string $kitchenId
     * @param array $customerIds
     * @param array|null $userKitchens
     * @return array
     */
    public function avgOrderOfCustomer(string $kitchenId, array $customerIds, ?array $userKitchens = null): array;

    /**
     * Get best/worst performing meals.
     *
     * @param string $kitchenId
     * @param string $param
     * @param string $type
     * @param string $meal_type
     * @param int $year
     * @param int|null $month
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function bestWorstMeals(string $kitchenId, string $param, string $type, string $meal_type, int $year, ?int $month = null, int $limit = 5, ?array $userKitchens = null): array;

    /**
     * Get common extra meals.
     *
     * @param string $kitchenId
     * @param int $limit
     * @param array|null $userKitchens
     * @return array
     */
    public function getCommonExtraMeals(string $kitchenId, int $limit = 10, ?array $userKitchens = null): array;
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'orders';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_order_no';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'pk_order_no',
        'customer_code',
        'title',
        'product_code',
        'product_name',
        'amount',
        'applied_discount',
        'tax',
        'delivery_charges',
        'service_charges',
        'gross_amount',
        'net_amount',
        'period',
        'customer_name',
        'fk_kitchen_code',
        'order_date',
        'payment_mode',
        'order_status',
        'quantity',
        'order_menu'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'float',
        'applied_discount' => 'float',
        'tax' => 'float',
        'delivery_charges' => 'float',
        'service_charges' => 'float',
        'gross_amount' => 'float',
        'net_amount' => 'float',
        'quantity' => 'integer',
        'order_date' => 'datetime'
    ];

    /**
     * Get the product associated with the order.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_code', 'pk_product_code');
    }

    /**
     * Get the customer associated with the order.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the kitchen associated with the order.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(Kitchen::class, 'fk_kitchen_code', 'pk_kitchen_code');
    }

    /**
     * Get the extras for the order.
     */
    public function extras(): HasMany
    {
        return $this->hasMany(OrderExtra::class, 'order_id', 'pk_order_no');
    }
}

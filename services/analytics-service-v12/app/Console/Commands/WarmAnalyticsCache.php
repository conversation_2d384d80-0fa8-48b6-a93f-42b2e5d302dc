<?php

namespace App\Console\Commands;

use App\Services\CustomerService;
use App\Services\FoodService;
use App\Services\SalesService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class WarmAnalyticsCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'analytics:warm-cache {kitchen_id?} {--all} {--type=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up analytics cache for a specific kitchen or all kitchens';

    /**
     * Execute the console command.
     */
    public function handle(SalesService $salesService, FoodService $foodService, CustomerService $customerService): int
    {
        $kitchenId = $this->argument('kitchen_id');
        $all = $this->option('all');
        $type = $this->option('type');
        
        if ($all) {
            $this->info('Warming up cache for all kitchens...');
            
            // Get all kitchens
            $kitchens = DB::table('kitchens')->pluck('pk_kitchen_code')->toArray();
            
            if (empty($kitchens)) {
                $this->error('No kitchens found.');
                return Command::FAILURE;
            }
            
            $this->withProgressBar($kitchens, function ($kitchenId) use ($salesService, $foodService, $customerService, $type) {
                $this->warmCache($kitchenId, $salesService, $foodService, $customerService, $type);
            });
            
            $this->newLine();
            $this->info('Cache warmed up for all kitchens.');
            return Command::SUCCESS;
        }
        
        if (!$kitchenId) {
            $this->error('Please provide a kitchen ID or use the --all option.');
            return Command::FAILURE;
        }
        
        $this->info("Warming up cache for kitchen {$kitchenId}...");
        $this->warmCache($kitchenId, $salesService, $foodService, $customerService, $type);
        $this->info("Cache warmed up for kitchen {$kitchenId}.");
        
        return Command::SUCCESS;
    }
    
    /**
     * Warm up cache for a specific kitchen.
     *
     * @param string $kitchenId
     * @param SalesService $salesService
     * @param FoodService $foodService
     * @param CustomerService $customerService
     * @param string|null $type
     * @return void
     */
    private function warmCache(string $kitchenId, SalesService $salesService, FoodService $foodService, CustomerService $customerService, ?string $type = null): void
    {
        // Get user kitchens
        $userKitchens = [$kitchenId];
        
        // Warm up sales cache
        if (!$type || $type === 'sales') {
            // Get years
            $years = $salesService->getYears($kitchenId, $userKitchens);
            
            if (!empty($years)) {
                $currentYear = $years[0];
                
                // Get months for current year
                $months = $salesService->getMonths($kitchenId, $currentYear, $userKitchens);
                
                // Get payment methods
                $salesService->getPaymentMethods($kitchenId, $userKitchens);
                
                // Get revenue for current year
                $salesService->getRevenue($kitchenId, $currentYear, null, $userKitchens);
                
                // Get comparison for current year
                $salesService->getComparison($kitchenId, $currentYear, 'monthly', $userKitchens);
                
                // Get average meal for current year
                $salesService->getAvgMeal($kitchenId, $currentYear, null, $userKitchens);
                
                // Get average meal for current month if available
                if (!empty($months)) {
                    $currentMonth = array_key_first($months);
                    $salesService->getAvgMeal($kitchenId, $currentYear, $currentMonth, $userKitchens);
                }
            }
        }
        
        // Warm up food cache
        if (!$type || $type === 'food') {
            // Get years
            $years = $foodService->getYears($kitchenId, $userKitchens);
            
            if (!empty($years)) {
                $currentYear = $years[0];
                
                // Get months for current year
                $months = $foodService->getMonths($kitchenId, $currentYear, $userKitchens);
                
                // Get popular meals for current year
                $foodService->getPopularMeals($kitchenId, $currentYear, null, $userKitchens);
                
                // Get meal performance for current year
                $foodService->getMealPerformance($kitchenId, $currentYear, null, 'best', $userKitchens);
                $foodService->getMealPerformance($kitchenId, $currentYear, null, 'worst', $userKitchens);
                
                // Get common extras
                $foodService->getCommonExtras($kitchenId, $userKitchens);
                
                // Get popular meals for current month if available
                if (!empty($months)) {
                    $currentMonth = array_key_first($months);
                    $foodService->getPopularMeals($kitchenId, $currentYear, $currentMonth, $userKitchens);
                    $foodService->getMealPerformance($kitchenId, $currentYear, $currentMonth, 'best', $userKitchens);
                    $foodService->getMealPerformance($kitchenId, $currentYear, $currentMonth, 'worst', $userKitchens);
                }
            }
        }
        
        // Warm up customer cache
        if (!$type || $type === 'customer') {
            // Get loyal customers
            $loyalCustomers = $customerService->getLoyalCustomers($kitchenId, $userKitchens);
            
            if (!empty($loyalCustomers)) {
                // Get customer IDs
                $customerIds = array_column($loyalCustomers, 'customer_code');
                
                // Get customer spending
                $customerService->getCustomerSpending($kitchenId, $customerIds, $userKitchens);
                
                // Get customer preferences
                $customerService->getCustomerPreferences($kitchenId, $customerIds, $userKitchens);
                
                // Get individual customer data for top 5 customers
                $topCustomers = array_slice($customerIds, 0, 5);
                foreach ($topCustomers as $customerId) {
                    $customerService->getCustomerSpending($kitchenId, [$customerId], $userKitchens);
                    $customerService->getCustomerPreferences($kitchenId, [$customerId], $userKitchens);
                }
            }
        }
    }
}

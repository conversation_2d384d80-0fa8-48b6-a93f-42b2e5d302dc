<?php

namespace App\Console\Commands;

use App\Services\RabbitMQConsumer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ConsumeRabbitMQMessages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:consume {routing_key}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Consume messages from RabbitMQ';

    /**
     * Execute the console command.
     */
    public function handle(RabbitMQConsumer $consumer): int
    {
        $routingKey = $this->argument('routing_key');
        
        $this->info("Consuming messages from RabbitMQ with routing key: {$routingKey}");
        
        try {
            // Bind the queue to the routing key
            $consumer->bind($routingKey);
            
            // Consume messages
            $consumer->consume(function ($data) {
                $this->info("Processing message: " . json_encode($data));
                
                // Process the message
                // For example, if it's an order event, we might update analytics data
                if (isset($data['event']) && isset($data['order'])) {
                    $event = $data['event'];
                    $order = $data['order'];
                    
                    $this->info("Processing {$event} event for order {$order['pk_order_no']}");
                    
                    // Here we would process the event
                    // For example, we might update a cache of popular meals, payment methods, etc.
                    
                    return true;
                }
                
                $this->warn("Unknown message format");
                return false;
            });
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error consuming messages: {$e->getMessage()}");
            Log::error("Error consuming messages: {$e->getMessage()}", [
                'routing_key' => $routingKey,
                'exception' => $e
            ]);
            
            return Command::FAILURE;
        }
    }
}

<?php

namespace App\Console\Commands;

use App\Services\CustomerService;
use App\Services\FoodService;
use App\Services\SalesService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearAnalyticsCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'analytics:clear-cache {kitchen_id?} {--all} {--customer=} {--type=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear analytics cache for a specific kitchen or all kitchens';

    /**
     * Execute the console command.
     */
    public function handle(SalesService $salesService, FoodService $foodService, CustomerService $customerService): int
    {
        $kitchenId = $this->argument('kitchen_id');
        $all = $this->option('all');
        $customerId = $this->option('customer');
        $type = $this->option('type');
        
        if ($all) {
            $this->info('Clearing all analytics cache...');
            Cache::flush();
            $this->info('All analytics cache cleared.');
            return Command::SUCCESS;
        }
        
        if (!$kitchenId) {
            $this->error('Please provide a kitchen ID or use the --all option.');
            return Command::FAILURE;
        }
        
        if ($customerId) {
            $this->info("Clearing cache for customer {$customerId} in kitchen {$kitchenId}...");
            $customerService->clearCustomerCache($kitchenId, $customerId);
            $this->info("Cache cleared for customer {$customerId} in kitchen {$kitchenId}.");
            return Command::SUCCESS;
        }
        
        if ($type) {
            $this->info("Clearing {$type} cache for kitchen {$kitchenId}...");
            
            switch ($type) {
                case 'sales':
                    $salesService->clearCache($kitchenId);
                    break;
                case 'food':
                    $foodService->clearCache($kitchenId);
                    break;
                case 'customer':
                    $customerService->clearCache($kitchenId);
                    break;
                default:
                    $this->error("Invalid type: {$type}. Valid types are: sales, food, customer.");
                    return Command::FAILURE;
            }
            
            $this->info("{$type} cache cleared for kitchen {$kitchenId}.");
            return Command::SUCCESS;
        }
        
        $this->info("Clearing all cache for kitchen {$kitchenId}...");
        $salesService->clearCache($kitchenId);
        $foodService->clearCache($kitchenId);
        $customerService->clearCache($kitchenId);
        $this->info("All cache cleared for kitchen {$kitchenId}.");
        
        return Command::SUCCESS;
    }
}

<?php

namespace App\Console\Commands;

use App\Services\Cache\CacheWarmerService;
use Illuminate\Console\Command;

class CacheWarmUpCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:warm-up';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up the cache with critical data';

    /**
     * @var CacheWarmerService
     */
    protected $cacheWarmerService;

    /**
     * Create a new command instance.
     *
     * @param CacheWarmerService $cacheWarmerService
     * @return void
     */
    public function __construct(CacheWarmerService $cacheWarmerService)
    {
        parent::__construct();
        
        $this->cacheWarmerService = $cacheWarmerService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('Starting cache warm-up...');
        
        $results = $this->cacheWarmerService->warmUp();
        
        $successCount = count(array_filter($results, function ($result) {
            return $result['success'];
        }));
        
        $failureCount = count($results) - $successCount;
        
        $this->info("Cache warm-up completed: {$successCount} succeeded, {$failureCount} failed");
        
        if ($failureCount > 0) {
            $this->warn('Failed items:');
            
            foreach ($results as $result) {
                if (!$result['success']) {
                    $this->error("- {$result['key']}: {$result['error']}");
                }
            }
        }
        
        return $failureCount > 0 ? 1 : 0;
    }
}

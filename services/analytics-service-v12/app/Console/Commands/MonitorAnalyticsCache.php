<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class MonitorAnalyticsCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'analytics:monitor-cache {--watch} {--interval=5}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor analytics cache usage';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $watch = $this->option('watch');
        $interval = (int) $this->option('interval');
        
        if ($watch) {
            $this->info('Monitoring analytics cache usage. Press Ctrl+C to stop.');
            
            while (true) {
                $this->displayCacheStats();
                sleep($interval);
            }
        } else {
            $this->displayCacheStats();
        }
        
        return Command::SUCCESS;
    }
    
    /**
     * Display cache statistics.
     *
     * @return void
     */
    private function displayCacheStats(): void
    {
        $driver = config('cache.default');
        
        $this->info('Cache Driver: ' . $driver);
        $this->newLine();
        
        if ($driver === 'redis') {
            $this->displayRedisStats();
        } else {
            $this->info('Cache statistics are only available for Redis driver.');
        }
    }
    
    /**
     * Display Redis statistics.
     *
     * @return void
     */
    private function displayRedisStats(): void
    {
        try {
            $redis = Redis::connection('cache');
            $info = $redis->info();
            
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Used Memory', $this->formatBytes($info['used_memory'])],
                    ['Peak Memory', $this->formatBytes($info['used_memory_peak'])],
                    ['Total Keys', $info['keyspace_hits'] + $info['keyspace_misses']],
                    ['Hits', $info['keyspace_hits']],
                    ['Misses', $info['keyspace_misses']],
                    ['Hit Rate', $this->calculateHitRate($info['keyspace_hits'], $info['keyspace_misses']) . '%'],
                    ['Uptime', $this->formatUptime($info['uptime_in_seconds'])],
                    ['Connected Clients', $info['connected_clients']],
                ]
            );
            
            // Get cache keys by prefix
            $keys = $redis->keys(config('cache.prefix') . '*');
            $keysByPrefix = $this->groupKeysByPrefix($keys);
            
            $this->newLine();
            $this->info('Cache Keys by Prefix:');
            
            $rows = [];
            foreach ($keysByPrefix as $prefix => $count) {
                $rows[] = [$prefix, $count];
            }
            
            $this->table(['Prefix', 'Count'], $rows);
        } catch (\Exception $e) {
            $this->error('Error connecting to Redis: ' . $e->getMessage());
        }
    }
    
    /**
     * Format bytes to human-readable format.
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Calculate hit rate.
     *
     * @param int $hits
     * @param int $misses
     * @return float
     */
    private function calculateHitRate(int $hits, int $misses): float
    {
        $total = $hits + $misses;
        
        if ($total === 0) {
            return 0;
        }
        
        return round(($hits / $total) * 100, 2);
    }
    
    /**
     * Format uptime to human-readable format.
     *
     * @param int $seconds
     * @return string
     */
    private function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $seconds %= 86400;
        
        $hours = floor($seconds / 3600);
        $seconds %= 3600;
        
        $minutes = floor($seconds / 60);
        $seconds %= 60;
        
        $parts = [];
        
        if ($days > 0) {
            $parts[] = $days . 'd';
        }
        
        if ($hours > 0) {
            $parts[] = $hours . 'h';
        }
        
        if ($minutes > 0) {
            $parts[] = $minutes . 'm';
        }
        
        if ($seconds > 0 || empty($parts)) {
            $parts[] = $seconds . 's';
        }
        
        return implode(' ', $parts);
    }
    
    /**
     * Group keys by prefix.
     *
     * @param array $keys
     * @return array
     */
    private function groupKeysByPrefix(array $keys): array
    {
        $prefixes = [
            'sales.years' => 0,
            'sales.months' => 0,
            'sales.avg_meal' => 0,
            'sales.payment_methods' => 0,
            'sales.revenue' => 0,
            'sales.comparison' => 0,
            'food.years' => 0,
            'food.months' => 0,
            'food.popular_meals' => 0,
            'food.meal_performance' => 0,
            'food.common_extras' => 0,
            'customer.loyal' => 0,
            'customer.spending' => 0,
            'customer.preferences' => 0,
            'other' => 0,
        ];
        
        $prefix = config('cache.prefix');
        
        foreach ($keys as $key) {
            // Remove the cache prefix
            $key = str_replace($prefix, '', $key);
            
            $matched = false;
            
            foreach (array_keys($prefixes) as $p) {
                if (strpos($key, $p) === 0) {
                    $prefixes[$p]++;
                    $matched = true;
                    break;
                }
            }
            
            if (!$matched) {
                $prefixes['other']++;
            }
        }
        
        // Remove prefixes with zero count
        return array_filter($prefixes);
    }
}

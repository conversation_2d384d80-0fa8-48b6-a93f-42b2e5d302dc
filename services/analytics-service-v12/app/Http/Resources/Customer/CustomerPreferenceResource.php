<?php

namespace App\Http\Resources\Customer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerPreferenceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'customer_code' => $this->customer_code,
            'customer_name' => $this->customer_name,
            'product_name' => $this->product_name,
            'net_amount' => $this->net_amount,
        ];
    }
}

<?php

namespace App\Http\Resources\Food;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CommonExtraResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'meal' => $this->meal ?? '',
            'extra' => $this->extra ?? '',
            'count' => $this->count ?? 0,
        ];
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ExportReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'format' => 'required|string|in:csv,excel,pdf',
            'model' => 'required|string|in:order,customer,product,payment,subscription,delivery,kitchen,meal,user',
            'columns' => 'required|array|min:1',
            'columns.*' => 'string',
            'headers' => 'sometimes|array|min:1',
            'headers.*' => 'string',
            'filters' => 'sometimes|array',
            'filters.*.operator' => 'sometimes|string|in:eq,neq,gt,gte,lt,lte,like,in,not_in,between,not_between,null,not_null,date_eq,date_neq,date_gt,date_gte,date_lt,date_lte',
            'filters.*.value' => 'sometimes|required_with:filters.*.operator',
            'sort' => 'sometimes|array',
            'sort.*' => 'string|in:asc,desc',
            'filename' => 'sometimes|string|max:100',
            'title' => 'sometimes|string|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'format.required' => 'The format parameter is required',
            'format.in' => 'The format must be one of: csv, excel, pdf',
            'model.required' => 'The model parameter is required',
            'model.in' => 'The selected model is invalid',
            'columns.required' => 'The columns parameter is required',
            'columns.array' => 'The columns must be an array',
            'columns.min' => 'At least one column must be specified',
            'headers.array' => 'The headers must be an array',
            'headers.min' => 'At least one header must be specified',
            'filters.array' => 'The filters must be an array',
            'filters.*.operator.in' => 'The filter operator is invalid',
            'sort.array' => 'The sort parameter must be an array',
            'sort.*.in' => 'The sort direction must be either "asc" or "desc"',
            'filename.string' => 'The filename must be a string',
            'filename.max' => 'The filename cannot exceed 100 characters',
            'title.string' => 'The title must be a string',
            'title.max' => 'The title cannot exceed 100 characters',
        ];
    }
}

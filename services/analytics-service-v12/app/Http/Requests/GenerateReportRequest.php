<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GenerateReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'model' => 'required|string|in:order,customer,product,payment,subscription,delivery,kitchen,meal,user',
            'columns' => 'sometimes|array',
            'columns.*' => 'string',
            'filters' => 'sometimes|array',
            'filters.*.operator' => 'sometimes|string|in:eq,neq,gt,gte,lt,lte,like,in,not_in,between,not_between,null,not_null,date_eq,date_neq,date_gt,date_gte,date_lt,date_lte',
            'filters.*.value' => 'sometimes|required_with:filters.*.operator',
            'sort' => 'sometimes|array',
            'sort.*' => 'string|in:asc,desc',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'model.required' => 'The model parameter is required',
            'model.in' => 'The selected model is invalid',
            'columns.array' => 'The columns must be an array',
            'filters.array' => 'The filters must be an array',
            'filters.*.operator.in' => 'The filter operator is invalid',
            'sort.array' => 'The sort parameter must be an array',
            'sort.*.in' => 'The sort direction must be either "asc" or "desc"',
            'per_page.integer' => 'The per_page parameter must be an integer',
            'per_page.min' => 'The per_page parameter must be at least 1',
            'per_page.max' => 'The per_page parameter cannot exceed 100',
            'page.integer' => 'The page parameter must be an integer',
            'page.min' => 'The page parameter must be at least 1',
        ];
    }
}

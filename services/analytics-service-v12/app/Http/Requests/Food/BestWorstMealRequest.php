<?php

namespace App\Http\Requests\Food;

use Illuminate\Foundation\Http\FormRequest;

class BestWorstMealRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'kitchen_id' => 'required|string',
            'param' => 'required|string|in:yearly,monthly',
            'type' => 'required|string|in:best,worst',
            'meal_type' => 'required|string',
            'year' => 'required|integer|min:2000|max:' . (date('Y') + 1),
            'month' => 'nullable|integer|min:1|max:12',
        ];
    }
}

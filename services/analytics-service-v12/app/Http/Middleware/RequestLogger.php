<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class RequestLogger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Generate a unique request ID
        $requestId = uniqid('req-', true);
        
        // Add the request ID to the request attributes
        $request->attributes->set('request_id', $requestId);
        
        // Log the request
        Log::info('API Request', [
            'request_id' => $requestId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'headers' => $this->filterHeaders($request->headers->all()),
            'params' => $this->filterParameters($request->all()),
        ]);
        
        // Process the request
        $response = $next($request);
        
        // Log the response
        Log::info('API Response', [
            'request_id' => $requestId,
            'status' => $response->getStatusCode(),
            'content_type' => $response->headers->get('Content-Type'),
            'time' => round(microtime(true) - LARAVEL_START, 3),
        ]);
        
        // Add the request ID to the response headers
        $response->headers->set('X-Request-ID', $requestId);
        
        return $response;
    }
    
    /**
     * Filter sensitive headers.
     *
     * @param array $headers
     * @return array
     */
    protected function filterHeaders(array $headers): array
    {
        $filtered = $headers;
        
        // Remove sensitive headers
        foreach (['authorization', 'cookie', 'x-csrf-token'] as $header) {
            if (isset($filtered[$header])) {
                $filtered[$header] = '[FILTERED]';
            }
        }
        
        return $filtered;
    }
    
    /**
     * Filter sensitive parameters.
     *
     * @param array $parameters
     * @return array
     */
    protected function filterParameters(array $parameters): array
    {
        $filtered = $parameters;
        
        // Remove sensitive parameters
        foreach (['password', 'password_confirmation', 'token', 'api_key'] as $param) {
            if (isset($filtered[$param])) {
                $filtered[$param] = '[FILTERED]';
            }
        }
        
        return $filtered;
    }
}

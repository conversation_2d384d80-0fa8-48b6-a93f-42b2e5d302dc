<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Prometheus\CollectorRegistry;
use Prometheus\Storage\Redis as RedisAdapter;
use Symfony\Component\HttpFoundation\Response;

class CollectMetrics
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Start timing the request
        $start = microtime(true);
        
        // Process the request
        $response = $next($request);
        
        // Calculate request duration
        $duration = microtime(true) - $start;
        
        // Get route name or path
        $route = $request->route() ? $request->route()->getName() : $request->path();
        
        // Get HTTP method
        $method = $request->method();
        
        // Get response status code
        $status = $response->getStatusCode();
        
        // Initialize Prometheus registry with Redis adapter
        try {
            $adapter = new RedisAdapter(['host' => config('database.redis.cache.host')]);
            $registry = new CollectorRegistry($adapter);
            
            // Update request duration histogram
            $histogram = $registry->getOrRegisterHistogram(
                'analytics',
                'http_request_duration_seconds',
                'HTTP request duration in seconds',
                ['route', 'method', 'status'],
                [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10]
            );
            
            $histogram->observe($duration, [$route, $method, $status]);
            
            // Update request counter
            $counter = $registry->getOrRegisterCounter(
                'analytics',
                'http_requests_total',
                'Total number of HTTP requests',
                ['route', 'method', 'status']
            );
            
            $counter->inc([$route, $method, $status]);
        } catch (\Exception $e) {
            // Ignore exceptions when collecting metrics
        }
        
        return $response;
    }
}

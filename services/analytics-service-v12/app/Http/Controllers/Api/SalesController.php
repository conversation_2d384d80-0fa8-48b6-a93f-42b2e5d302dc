<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Sales\AvgMealRequest;
use App\Http\Requests\Sales\AvgMealGetMonthsRequest;
use App\Http\Requests\Sales\CommonPaymentModeRequest;
use App\Http\Requests\Sales\RevenueShareRequest;
use App\Http\Requests\Sales\SalesComparisonRequest;
use App\Http\Resources\Sales\AvgMealResource;
use App\Http\Resources\Sales\MonthResource;
use App\Http\Resources\Sales\PaymentModeResource;
use App\Http\Resources\Sales\RevenueShareResource;
use App\Http\Resources\Sales\SalesComparisonResource;
use App\Services\SalesService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SalesController extends Controller
{
    /**
     * @var SalesService
     */
    protected $salesService;

    /**
     * SalesController constructor.
     *
     * @param SalesService $salesService
     */
    public function __construct(SalesService $salesService)
    {
        $this->salesService = $salesService;
    }

    /**
     * Display the sales analytics dashboard.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $years = $this->salesService->getYears($kitchenId, $userKitchens);
        $currentYear = date('Y');
        $months = $this->salesService->getMonths($kitchenId, $currentYear, $userKitchens);
        $paymentModes = $this->salesService->getPaymentMethods($kitchenId, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => [
                'years' => $years,
                'months' => $months,
                'payment_modes' => PaymentModeResource::collection($paymentModes),
            ]
        ]);
    }

    /**
     * Get years with order data.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getYears(Request $request): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $years = $this->salesService->getYears($kitchenId, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => $years
        ]);
    }

    /**
     * Get months with order data for a specific year.
     *
     * @param Request $request
     * @param int $year
     * @return JsonResponse
     */
    public function getMonths(Request $request, int $year): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $months = $this->salesService->getMonths($kitchenId, $year, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => $months
        ]);
    }

    /**
     * Get payment methods.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPaymentMethods(Request $request): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $paymentModes = $this->salesService->getPaymentMethods($kitchenId, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => PaymentModeResource::collection($paymentModes)
        ]);
    }

    /**
     * Get revenue for a specific year and month.
     *
     * @param Request $request
     * @param int $year
     * @param int|null $month
     * @return JsonResponse
     */
    public function getRevenue(Request $request, int $year, ?int $month = null): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $revenue = $this->salesService->getRevenue($kitchenId, $year, $month, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => RevenueShareResource::collection($revenue)
        ]);
    }

    /**
     * Get sales comparison for a specific year.
     *
     * @param Request $request
     * @param int $year
     * @param string $type
     * @return JsonResponse
     */
    public function getComparison(Request $request, int $year, string $type): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $comparison = $this->salesService->getComparison($kitchenId, $year, $type, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => SalesComparisonResource::collection($comparison)
        ]);
    }

    /**
     * Get average meal per customer.
     *
     * @param Request $request
     * @param int $year
     * @param int|null $month
     * @return JsonResponse
     */
    public function getAvgMeal(Request $request, int $year, ?int $month = null): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $avgMeal = $this->salesService->getAvgMeal($kitchenId, $year, $month, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => AvgMealResource::collection($avgMeal)
        ]);
    }

    /**
     * Get average meal per customer (legacy API).
     *
     * @param AvgMealRequest $request
     * @return JsonResponse
     */
    public function avgMeal(AvgMealRequest $request): JsonResponse
    {
        $kitchenId = $request->input('kitchen_id');
        $year = $request->input('year');
        $month = $request->input('month');
        $userKitchens = $request->user()->kitchens ?? [];

        $result = $this->salesService->getAvgMeal($kitchenId, $year, $month, $userKitchens);

        return response()->json($result);
    }

    /**
     * Get months with order data for a specific year (legacy API).
     *
     * @param AvgMealGetMonthsRequest $request
     * @return JsonResponse
     */
    public function avgMealGetMonths(AvgMealGetMonthsRequest $request): JsonResponse
    {
        $kitchenId = $request->input('kitchen_id');
        $year = $request->input('year');
        $userKitchens = $request->user()->kitchens ?? [];

        $result = $this->salesService->getMonths($kitchenId, $year, $userKitchens);

        return response()->json($result);
    }

    /**
     * Get common payment modes (legacy API).
     *
     * @param CommonPaymentModeRequest $request
     * @return JsonResponse
     */
    public function commonPaymentMode(CommonPaymentModeRequest $request): JsonResponse
    {
        $kitchenId = $request->input('kitchen_id');
        $userKitchens = $request->user()->kitchens ?? [];

        $result = $this->salesService->getPaymentMethods($kitchenId, $userKitchens);

        return response()->json($result);
    }

    /**
     * Get revenue share (legacy API).
     *
     * @param RevenueShareRequest $request
     * @return JsonResponse
     */
    public function revenueShare(RevenueShareRequest $request): JsonResponse
    {
        $kitchenId = $request->input('kitchen_id');
        $param = $request->input('param');
        $year = $request->input('year');
        $month = $request->input('month');
        $userKitchens = $request->user()->kitchens ?? [];

        $result = $this->salesService->getRevenue($kitchenId, $year, $month, $userKitchens);

        return response()->json($result);
    }

    /**
     * Get sales comparison (legacy API).
     *
     * @param SalesComparisonRequest $request
     * @return JsonResponse
     */
    public function salesComparison(SalesComparisonRequest $request): JsonResponse
    {
        $kitchenId = $request->input('kitchen_id');
        $param = $request->input('param');
        $year = $request->input('year');
        $userKitchens = $request->user()->kitchens ?? [];

        $result = $this->salesService->getComparison($kitchenId, $year, $param, $userKitchens);

        return response()->json($result);
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Customer\LoyalCustomerResource;
use App\Http\Resources\Customer\CustomerSpendingResource;
use App\Http\Resources\Customer\CustomerPreferenceResource;
use App\Services\CustomerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    /**
     * @var CustomerService
     */
    protected $customerService;

    /**
     * CustomerController constructor.
     *
     * @param CustomerService $customerService
     */
    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * Display the customer analytics dashboard.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $loyalCustomers = $this->customerService->getLoyalCustomers($kitchenId, $userKitchens);
        
        // Get customer IDs from loyal customers
        $customerIds = array_column($loyalCustomers, 'customer_code');
        
        $customerPreferences = $this->customerService->getCustomerPreferences($kitchenId, $customerIds, $userKitchens);
        $customerSpending = $this->customerService->getCustomerSpending($kitchenId, $customerIds, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => [
                'loyal_customers' => LoyalCustomerResource::collection($loyalCustomers),
                'customer_preferences' => CustomerPreferenceResource::collection($customerPreferences),
                'customer_spending' => CustomerSpendingResource::collection($customerSpending)
            ]
        ]);
    }

    /**
     * Get loyal customers.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getLoyalCustomers(Request $request): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];
        $limit = $request->input('limit', 10);

        $loyalCustomers = $this->customerService->getLoyalCustomers($kitchenId, $userKitchens, $limit);

        return response()->json([
            'status' => 'success',
            'data' => LoyalCustomerResource::collection($loyalCustomers)
        ]);
    }

    /**
     * Get customer spending.
     *
     * @param Request $request
     * @param string $customerId
     * @return JsonResponse
     */
    public function getCustomerSpending(Request $request, string $customerId): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $customerSpending = $this->customerService->getCustomerSpending($kitchenId, [$customerId], $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => CustomerSpendingResource::collection($customerSpending)
        ]);
    }

    /**
     * Get customer preferences.
     *
     * @param Request $request
     * @param string $customerId
     * @return JsonResponse
     */
    public function getCustomerPreferences(Request $request, string $customerId): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];
        $limit = $request->input('limit', 10);

        $customerPreferences = $this->customerService->getCustomerPreferences($kitchenId, [$customerId], $userKitchens, $limit);

        return response()->json([
            'status' => 'success',
            'data' => CustomerPreferenceResource::collection($customerPreferences)
        ]);
    }
}

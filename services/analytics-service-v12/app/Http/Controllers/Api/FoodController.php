<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Food\BestWorstMealRequest;
use App\Http\Resources\Food\MealPerformanceResource;
use App\Http\Resources\Food\CommonExtraResource;
use App\Services\FoodService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FoodController extends Controller
{
    /**
     * @var FoodService
     */
    protected $foodService;

    /**
     * FoodController constructor.
     *
     * @param FoodService $foodService
     */
    public function __construct(FoodService $foodService)
    {
        $this->foodService = $foodService;
    }

    /**
     * Display the food analytics dashboard.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $years = $this->foodService->getYears($kitchenId, $userKitchens);
        $currentYear = date('Y');
        $months = $this->foodService->getMonths($kitchenId, $currentYear, $userKitchens);
        $commonExtras = $this->foodService->getCommonExtras($kitchenId, $userKitchens);

        // Get menus from settings (this would typically come from a settings service)
        $menus = ['Breakfast', 'Lunch', 'Dinner', 'Snacks'];

        return response()->json([
            'status' => 'success',
            'data' => [
                'years' => $years,
                'months' => $months,
                'menus' => $menus,
                'common_extras' => CommonExtraResource::collection($commonExtras),
            ]
        ]);
    }

    /**
     * Get popular meals.
     *
     * @param Request $request
     * @param int $year
     * @param int|null $month
     * @return JsonResponse
     */
    public function getPopularMeals(Request $request, int $year, ?int $month = null): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $popularMeals = $this->foodService->getPopularMeals($kitchenId, $year, $month, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => MealPerformanceResource::collection($popularMeals)
        ]);
    }

    /**
     * Get meal performance.
     *
     * @param Request $request
     * @param int $year
     * @param int|null $month
     * @param string $type
     * @return JsonResponse
     */
    public function getMealPerformance(Request $request, int $year, ?int $month = null, string $type = 'best'): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $mealPerformance = $this->foodService->getMealPerformance($kitchenId, $year, $month, $type, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => MealPerformanceResource::collection($mealPerformance)
        ]);
    }

    /**
     * Get common extras.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCommonExtras(Request $request): JsonResponse
    {
        $kitchenId = $request->user()->kitchen_id ?? 'all';
        $userKitchens = $request->user()->kitchens ?? [];

        $commonExtras = $this->foodService->getCommonExtras($kitchenId, $userKitchens);

        return response()->json([
            'status' => 'success',
            'data' => CommonExtraResource::collection($commonExtras)
        ]);
    }

    /**
     * Get best/worst meals (legacy API).
     *
     * @param BestWorstMealRequest $request
     * @return JsonResponse
     */
    public function bestWorstMeal(BestWorstMealRequest $request): JsonResponse
    {
        $kitchenId = $request->input('kitchen_id');
        $param = $request->input('param');
        $type = $request->input('type');
        $mealType = $request->input('meal_type');
        $year = $request->input('year');
        $month = $request->input('month');
        $userKitchens = $request->user()->kitchens ?? [];

        $result = $this->foodService->getMealPerformance($kitchenId, $year, $month, $type, $userKitchens);

        return response()->json($result);
    }
}

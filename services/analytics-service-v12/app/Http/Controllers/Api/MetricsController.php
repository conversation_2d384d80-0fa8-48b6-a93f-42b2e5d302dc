<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Prometheus\CollectorRegistry;
use Prometheus\RenderTextFormat;
use Prometheus\Storage\Redis as RedisAdapter;

class MetricsController extends Controller
{
    /**
     * Get Prometheus metrics.
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        // Initialize Prometheus registry with Redis adapter
        $adapter = new RedisAdapter(['host' => config('database.redis.cache.host')]);
        $registry = new CollectorRegistry($adapter);
        
        // Collect metrics
        $this->collectDatabaseMetrics($registry);
        $this->collectCacheMetrics($registry);
        $this->collectApiMetrics($registry);
        
        // Render metrics in Prometheus format
        $renderer = new RenderTextFormat();
        $result = $renderer->render($registry->getMetricFamilySamples());
        
        return response($result, 200, ['Content-Type' => 'text/plain']);
    }
    
    /**
     * Collect database metrics.
     *
     * @param CollectorRegistry $registry
     * @return void
     */
    private function collectDatabaseMetrics(CollectorRegistry $registry): void
    {
        // Database connection time
        $gauge = $registry->getOrRegisterGauge(
            'analytics',
            'db_connection_time_seconds',
            'Time to connect to the database in seconds'
        );
        
        $start = microtime(true);
        DB::connection()->getPdo();
        $connectionTime = microtime(true) - $start;
        
        $gauge->set($connectionTime);
        
        // Database query count
        $counter = $registry->getOrRegisterCounter(
            'analytics',
            'db_query_count_total',
            'Total number of database queries'
        );
        
        $queryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;
        $counter->incBy($queryCount);
        
        // Database size
        $gauge = $registry->getOrRegisterGauge(
            'analytics',
            'db_size_bytes',
            'Database size in bytes'
        );
        
        try {
            if (config('database.default') === 'sqlite') {
                $dbPath = DB::connection()->getDatabaseName();
                if (file_exists($dbPath)) {
                    $gauge->set(filesize($dbPath));
                }
            }
        } catch (\Exception $e) {
            // Ignore exceptions when getting database size
        }
    }
    
    /**
     * Collect cache metrics.
     *
     * @param CollectorRegistry $registry
     * @return void
     */
    private function collectCacheMetrics(CollectorRegistry $registry): void
    {
        if (config('cache.default') === 'redis') {
            try {
                $redis = Redis::connection('cache');
                $info = $redis->info();
                
                // Redis memory usage
                $gauge = $registry->getOrRegisterGauge(
                    'analytics',
                    'redis_memory_used_bytes',
                    'Redis memory usage in bytes'
                );
                $gauge->set($info['used_memory']);
                
                // Redis hit rate
                $gauge = $registry->getOrRegisterGauge(
                    'analytics',
                    'redis_hit_rate_percent',
                    'Redis cache hit rate percentage'
                );
                
                $hits = $info['keyspace_hits'];
                $misses = $info['keyspace_misses'];
                $total = $hits + $misses;
                
                if ($total > 0) {
                    $gauge->set(($hits / $total) * 100);
                } else {
                    $gauge->set(0);
                }
                
                // Redis connected clients
                $gauge = $registry->getOrRegisterGauge(
                    'analytics',
                    'redis_connected_clients',
                    'Number of clients connected to Redis'
                );
                $gauge->set($info['connected_clients']);
                
                // Redis key count
                $gauge = $registry->getOrRegisterGauge(
                    'analytics',
                    'redis_keys_total',
                    'Total number of keys in Redis'
                );
                
                $keys = $redis->keys(config('cache.prefix') . '*');
                $gauge->set(count($keys));
            } catch (\Exception $e) {
                // Ignore exceptions when collecting Redis metrics
            }
        }
    }
    
    /**
     * Collect API metrics.
     *
     * @param CollectorRegistry $registry
     * @return void
     */
    private function collectApiMetrics(CollectorRegistry $registry): void
    {
        // API request duration histogram
        $histogram = $registry->getOrRegisterHistogram(
            'analytics',
            'http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['route', 'method', 'status'],
            [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10]
        );
        
        // This will be updated by the middleware for each request
        
        // API request counter
        $counter = $registry->getOrRegisterCounter(
            'analytics',
            'http_requests_total',
            'Total number of HTTP requests',
            ['route', 'method', 'status']
        );
        
        // This will be updated by the middleware for each request
    }
}

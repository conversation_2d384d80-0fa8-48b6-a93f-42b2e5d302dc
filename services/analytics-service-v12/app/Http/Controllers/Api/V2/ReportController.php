<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\GenerateReportRequest;
use App\Http\Requests\ExportReportRequest;
use App\Services\Report\ReportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * Controller for generating and exporting reports
 */
class ReportController extends Controller
{
    /**
     * @var ReportService
     */
    protected $reportService;

    /**
     * ReportController constructor.
     *
     * @param ReportService $reportService
     */
    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * Generate a report based on the provided parameters
     *
     * @param GenerateReportRequest $request
     * @return JsonResponse
     */
    public function generate(GenerateReportRequest $request): JsonResponse
    {
        $model = $this->getModelClass($request->input('model'));
        $columns = $request->input('columns', ['*']);
        $filters = $request->input('filters', []);
        $sort = $request->input('sort', []);
        $perPage = $request->input('per_page', 15);
        $page = $request->input('page', 1);

        $report = $this->reportService->generateReport(
            $model,
            $columns,
            $filters,
            $sort,
            $perPage,
            $page
        );

        return response()->json([
            'success' => true,
            'data' => $report['data'],
            'meta' => $report['meta'],
        ]);
    }

    /**
     * Export a report based on the provided parameters
     *
     * @param ExportReportRequest $request
     * @return StreamedResponse
     */
    public function export(ExportReportRequest $request): StreamedResponse
    {
        $format = $request->input('format');
        $model = $this->getModelClass($request->input('model'));
        $columns = $request->input('columns', ['*']);
        $headers = $request->input('headers', $columns);
        $filters = $request->input('filters', []);
        $sort = $request->input('sort', []);
        $filename = $request->input('filename', 'report');
        $title = $request->input('title', 'Report');

        return $this->reportService->exportReport(
            $format,
            $model,
            $columns,
            $headers,
            $filters,
            $sort,
            $filename,
            $title
        );
    }

    /**
     * Get the available columns for a model
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function columns(Request $request): JsonResponse
    {
        $model = $this->getModelClass($request->input('model'));
        $columns = $this->reportService->getAvailableColumns($model);

        return response()->json([
            'success' => true,
            'data' => $columns,
        ]);
    }

    /**
     * Get the available models for reporting
     *
     * @return JsonResponse
     */
    public function models(): JsonResponse
    {
        $models = [
            'order' => 'App\\Models\\Order',
            'customer' => 'App\\Models\\Customer',
            'product' => 'App\\Models\\Product',
            'payment' => 'App\\Models\\Payment',
            'subscription' => 'App\\Models\\Subscription',
            'delivery' => 'App\\Models\\Delivery',
            'kitchen' => 'App\\Models\\Kitchen',
            'meal' => 'App\\Models\\Meal',
            'user' => 'App\\Models\\User',
        ];

        return response()->json([
            'success' => true,
            'data' => $models,
        ]);
    }

    /**
     * Get the model class from the model name
     *
     * @param string $modelName
     * @return string
     * @throws \InvalidArgumentException
     */
    protected function getModelClass(string $modelName): string
    {
        $models = [
            'order' => 'App\\Models\\Order',
            'customer' => 'App\\Models\\Customer',
            'product' => 'App\\Models\\Product',
            'payment' => 'App\\Models\\Payment',
            'subscription' => 'App\\Models\\Subscription',
            'delivery' => 'App\\Models\\Delivery',
            'kitchen' => 'App\\Models\\Kitchen',
            'meal' => 'App\\Models\\Meal',
            'user' => 'App\\Models\\User',
        ];

        if (!isset($models[$modelName])) {
            throw new \InvalidArgumentException("Invalid model: {$modelName}");
        }

        return $models[$modelName];
    }
}

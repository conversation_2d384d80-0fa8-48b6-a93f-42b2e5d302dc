<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Define gates for analytics access
        Gate::define('view-analytics', function ($user) {
            return $user->hasRole('admin') || $user->hasRole('manager');
        });

        Gate::define('view-sales-analytics', function ($user) {
            return $user->hasRole('admin') || $user->hasRole('manager') || $user->hasRole('sales');
        });

        Gate::define('view-food-analytics', function ($user) {
            return $user->hasRole('admin') || $user->hasRole('manager') || $user->hasRole('kitchen');
        });

        Gate::define('view-customer-analytics', function ($user) {
            return $user->hasRole('admin') || $user->hasRole('manager') || $user->hasRole('customer-service');
        });
    }
}

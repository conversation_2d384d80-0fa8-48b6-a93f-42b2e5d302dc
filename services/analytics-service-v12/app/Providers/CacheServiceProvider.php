<?php

namespace App\Providers;

use App\Services\Cache\CacheService;
use App\Services\Cache\CacheWarmerService;
use Illuminate\Support\ServiceProvider;

class CacheServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(CacheService::class, function ($app) {
            return new CacheService(
                config('cache.default'),
                config('cache.ttl.default')
            );
        });
        
        $this->app->singleton(CacheWarmerService::class, function ($app) {
            return new CacheWarmerService(
                $app->make(CacheService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        //
    }
}

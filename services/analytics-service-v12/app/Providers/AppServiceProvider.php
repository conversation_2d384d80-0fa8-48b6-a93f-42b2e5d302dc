<?php

namespace App\Providers;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use App\Repositories\Interfaces\TempOrderPaymentRepositoryInterface;
use App\Repositories\OrderRepository;
use App\Repositories\TempOrderPaymentRepository;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register repositories
        $this->app->bind(OrderRepositoryInterface::class, OrderRepository::class);
        $this->app->bind(TempOrderPaymentRepositoryInterface::class, TempOrderPaymentRepository::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

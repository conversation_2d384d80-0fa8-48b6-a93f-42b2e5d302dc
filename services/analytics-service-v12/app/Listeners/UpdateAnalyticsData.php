<?php

namespace App\Listeners;

use App\Events\OrderCancelled;
use App\Events\OrderCreated;
use App\Events\OrderUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdateAnalyticsData implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var array
     */
    public $backoff = [30, 60, 120];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the OrderCreated event.
     */
    public function handleOrderCreated(OrderCreated $event): void
    {
        $this->updateAnalyticsData($event->order, 'created');
    }

    /**
     * Handle the OrderUpdated event.
     */
    public function handleOrderUpdated(OrderUpdated $event): void
    {
        $this->updateAnalyticsData($event->order, 'updated');
    }

    /**
     * Handle the OrderCancelled event.
     */
    public function handleOrderCancelled(OrderCancelled $event): void
    {
        $this->updateAnalyticsData($event->order, 'cancelled');
    }

    /**
     * Update analytics data based on the order event.
     */
    private function updateAnalyticsData($order, string $eventType): void
    {
        try {
            Log::info("Updating analytics data for order {$order->pk_order_no} ({$eventType})");
            
            // Here we would update any analytics data that needs to be updated
            // For example, we might update a cache of popular meals, payment methods, etc.
            
            // For demonstration purposes, we'll just log the event
            Log::info("Analytics data updated for order {$order->pk_order_no}");
        } catch (\Exception $e) {
            Log::error("Error updating analytics data: {$e->getMessage()}");
            
            // If this is the last retry, we'll log a critical error
            if ($this->attempts() >= $this->tries) {
                Log::critical("Failed to update analytics data after {$this->tries} attempts for order {$order->pk_order_no}");
            }
            
            // Retry the job
            $this->release($this->backoff[$this->attempts() - 1] ?? 300);
        }
    }
}

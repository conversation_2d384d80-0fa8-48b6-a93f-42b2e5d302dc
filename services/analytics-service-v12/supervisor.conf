[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:php]
command=php artisan serve --host=0.0.0.0 --port=8000
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/php.log
stderr_logfile=/var/log/supervisor/php-error.log

[program:rabbitmq-consumer-order-created]
command=php artisan rabbitmq:consume order.created
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/rabbitmq-consumer-order-created.log
stderr_logfile=/var/log/supervisor/rabbitmq-consumer-order-created-error.log

[program:rabbitmq-consumer-order-updated]
command=php artisan rabbitmq:consume order.updated
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/rabbitmq-consumer-order-updated.log
stderr_logfile=/var/log/supervisor/rabbitmq-consumer-order-updated-error.log

[program:rabbitmq-consumer-order-cancelled]
command=php artisan rabbitmq:consume order.cancelled
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/rabbitmq-consumer-order-cancelled.log
stderr_logfile=/var/log/supervisor/rabbitmq-consumer-order-cancelled-error.log

[program:queue-worker]
command=php artisan queue:work --queue=default,analytics
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/queue-worker.log
stderr_logfile=/var/log/supervisor/queue-worker-error.log

{"name": "cubeonebiz/analytics-service", "type": "project", "description": "Analytics Service for CubeOneBiz", "keywords": ["laravel", "analytics", "microservice"], "license": "MIT", "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "php-amqplib/php-amqplib": "^3.5"}, "require-dev": {"fakerphp/faker": "*", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "*", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "*", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}
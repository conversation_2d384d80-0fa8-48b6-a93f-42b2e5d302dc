{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(analytics_http_requests_total[5m])", "interval": "", "legendFormat": "{{route}} ({{method}})", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Requests/sec", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(analytics_http_request_duration_seconds_bucket[5m])) by (le, route))", "interval": "", "legendFormat": "{{route}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request Duration (95th percentile)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "Duration", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 8}, "id": 6, "options": {"reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.7", "targets": [{"expr": "analytics_redis_hit_rate_percent", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON> Hit Rate", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 8}, "id": 8, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.7", "targets": [{"expr": "analytics_redis_memory_used_bytes", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Redis Memory Usage", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 8}, "id": 10, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.7", "targets": [{"expr": "analytics_redis_keys_total", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Redis Keys", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 8}, "id": 12, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.3.7", "targets": [{"expr": "analytics_db_connection_time_seconds", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "DB Connection Time", "type": "gauge"}], "refresh": "5s", "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Analytics Service Dashboard", "uid": "analytics-service", "version": 1}
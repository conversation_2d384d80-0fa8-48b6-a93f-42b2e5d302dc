apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: analytics-service
  namespace: cubeonebiz
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
spec:
  tls:
  - hosts:
    - api.cubeonebiz.com
    secretName: analytics-service-tls
  rules:
  - host: api.cubeonebiz.com
    http:
      paths:
      - path: /v2/analytics(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: analytics-service
            port:
              number: 80

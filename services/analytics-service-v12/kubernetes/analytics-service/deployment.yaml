apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-service
  namespace: cubeonebiz
  labels:
    app: analytics-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: analytics-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: analytics-service
    spec:
      containers:
      - name: analytics-service
        image: cubeonebiz/analytics-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
        env:
        - name: APP_ENV
          value: production
        - name: APP_DEBUG
          value: "false"
        - name: APP_URL
          value: https://api.cubeonebiz.com
        - name: DB_CONNECTION
          value: mysql
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: db_host
        - name: DB_PORT
          value: "3306"
        - name: DB_DATABASE
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: db_database
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: db_username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: db_password
        - name: RABBITMQ_HOST
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: rabbitmq_host
        - name: RABBITMQ_PORT
          value: "5672"
        - name: RABBITMQ_USER
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: rabbitmq_user
        - name: RABBITMQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: rabbitmq_password
        - name: RABBITMQ_VHOST
          value: "/"
        - name: QUEUE_CONNECTION
          value: rabbitmq
        - name: APP_KEY
          valueFrom:
            secretKeyRef:
              name: analytics-service-secrets
              key: app_key
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          failureThreshold: 3
      imagePullSecrets:
      - name: dockerhub-secret

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->string('pk_order_no')->primary();
            $table->string('customer_code')->nullable();
            $table->string('title')->nullable();
            $table->string('product_code')->nullable();
            $table->string('product_name')->nullable();
            $table->decimal('amount', 10, 2)->default(0);
            $table->decimal('applied_discount', 10, 2)->default(0);
            $table->decimal('tax', 10, 2)->default(0);
            $table->decimal('delivery_charges', 10, 2)->default(0);
            $table->decimal('service_charges', 10, 2)->default(0);
            $table->decimal('gross_amount', 10, 2)->default(0);
            $table->decimal('net_amount', 10, 2)->default(0);
            $table->string('period')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('fk_kitchen_code')->nullable();
            $table->dateTime('order_date')->nullable();
            $table->string('payment_mode')->nullable();
            $table->string('order_status')->nullable();
            $table->integer('quantity')->default(1);
            $table->string('order_menu')->nullable();
            $table->string('tax_method')->default('inclusive');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};

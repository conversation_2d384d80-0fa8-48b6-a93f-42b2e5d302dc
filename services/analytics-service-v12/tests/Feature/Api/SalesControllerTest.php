<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Repositories\Interfaces\OrderRepositoryInterface;
use App\Repositories\Interfaces\TempOrderPaymentRepositoryInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class SalesControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the repositories
        $this->mock(OrderRepositoryInterface::class, function ($mock) {
            $mock->shouldReceive('getYears')->andReturn([2023, 2022, 2021]);
            $mock->shouldReceive('getMonths')->andReturn([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
            $mock->shouldReceive('commonPaymentMode')->andReturn([
                ['payment_mode' => 'Credit Card', 'count' => 150],
                ['payment_mode' => 'Cash', 'count' => 100],
                ['payment_mode' => 'PayPal', 'count' => 50],
            ]);
            $mock->shouldReceive('revenueShare')->andReturn([
                ['gross_amount' => 25000.50],
            ]);
            $mock->shouldReceive('salesComparison')->andReturn([
                ['month' => 1, 'gross_amount' => 2500.50, 'net_amount' => 2250.25],
                ['month' => 2, 'gross_amount' => 3000.75, 'net_amount' => 2700.50],
            ]);
            $mock->shouldReceive('avgMealPerCustomer')->andReturn([
                ['meal_name' => 'Chicken Curry', 'qty' => 2.5],
                ['meal_name' => 'Vegetable Biryani', 'qty' => 2.0],
            ]);
        });

        $this->mock(TempOrderPaymentRepositoryInterface::class, function ($mock) {
            $mock->shouldReceive('commonPaymentMode')->andReturn([
                ['type' => 'Credit Card', 'count' => 150],
                ['type' => 'Cash', 'count' => 100],
                ['type' => 'PayPal', 'count' => 50],
            ]);
        });

        // Create a user and authenticate
        $user = User::factory()->create([
            'kitchen_id' => 'all',
            'kitchens' => [['fk_kitchen_code' => 'K001'], ['fk_kitchen_code' => 'K002']],
        ]);

        Sanctum::actingAs($user);
    }

    public function test_index_endpoint(): void
    {
        $response = $this->getJson('/api/v2/sales');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'years',
                    'months',
                    'payment_modes',
                ],
            ]);
    }

    public function test_get_years_endpoint(): void
    {
        $response = $this->getJson('/api/v2/sales/years');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ])
            ->assertJson([
                'status' => 'success',
                'data' => [2023, 2022, 2021],
            ]);
    }

    public function test_get_months_endpoint(): void
    {
        $response = $this->getJson('/api/v2/sales/months/2023');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_payment_methods_endpoint(): void
    {
        $response = $this->getJson('/api/v2/sales/payment-methods');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_revenue_endpoint(): void
    {
        $response = $this->getJson('/api/v2/sales/revenue/2023');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_comparison_endpoint(): void
    {
        $response = $this->getJson('/api/v2/sales/comparison/2023/monthly');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_avg_meal_endpoint(): void
    {
        $response = $this->getJson('/api/v2/sales/avg-meal/2023');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }
}

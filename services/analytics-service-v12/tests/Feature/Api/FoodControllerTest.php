<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Repositories\Interfaces\OrderRepositoryInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class FoodControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the repositories
        $this->mock(OrderRepositoryInterface::class, function ($mock) {
            $mock->shouldReceive('getYears')->andReturn([2023, 2022, 2021]);
            $mock->shouldReceive('getMonths')->andReturn([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
            $mock->shouldReceive('getCommonExtraMeals')->andReturn([
                ['meal' => 'Chicken Curry', 'extra' => 'Extra Cheese', 'count' => 50],
                ['meal' => 'Vegetable Biryani', 'extra' => 'Extra Spicy', 'count' => 30],
            ]);
            $mock->shouldReceive('bestWorstMeals')->andReturn([
                ['product_name' => 'Chicken Curry', 'qty' => 250],
                ['product_name' => 'Vegetable Biryani', 'qty' => 200],
            ]);
        });

        // Create a user and authenticate
        $user = User::factory()->create([
            'kitchen_id' => 'all',
            'kitchens' => [['fk_kitchen_code' => 'K001'], ['fk_kitchen_code' => 'K002']],
        ]);

        Sanctum::actingAs($user);
    }

    public function test_index_endpoint(): void
    {
        $response = $this->getJson('/api/v2/food');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'years',
                    'months',
                    'menus',
                    'common_extras',
                ],
            ]);
    }

    public function test_get_popular_meals_endpoint(): void
    {
        $response = $this->getJson('/api/v2/food/popular/2023');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_meal_performance_endpoint(): void
    {
        $response = $this->getJson('/api/v2/food/performance/2023/1/best');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_common_extras_endpoint(): void
    {
        $response = $this->getJson('/api/v2/food/extras');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }
}

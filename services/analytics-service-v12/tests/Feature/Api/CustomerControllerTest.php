<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Repositories\Interfaces\OrderRepositoryInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CustomerControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the repositories
        $this->mock(OrderRepositoryInterface::class, function ($mock) {
            $mock->shouldReceive('mostLoyalCustomers')->andReturn([
                ['customer_code' => 'CUST001', 'customer_name' => '<PERSON>', 'net_amount' => 5000.75],
                ['customer_code' => 'CUST002', 'customer_name' => '<PERSON>', 'net_amount' => 4500.50],
            ]);
            $mock->shouldReceive('mealThatDriveLoyality')->andReturn([
                ['customer_code' => 'CUST001', 'customer_name' => '<PERSON>', 'product_name' => 'Chicken Curry', 'net_amount' => 2500.50],
                ['customer_code' => 'CUST001', 'customer_name' => 'John Doe', 'product_name' => 'Vegetable Biryani', 'net_amount' => 2000.25],
            ]);
            $mock->shouldReceive('avgOrderOfCustomer')->andReturn([
                ['customer_name' => 'John Doe', 'yearly' => 5000.75, 'monthly' => 416.73],
                ['customer_name' => 'Jane Smith', 'yearly' => 4500.50, 'monthly' => 375.04],
            ]);
        });

        // Create a user and authenticate
        $user = User::factory()->create([
            'kitchen_id' => 'all',
            'kitchens' => [['fk_kitchen_code' => 'K001'], ['fk_kitchen_code' => 'K002']],
        ]);

        Sanctum::actingAs($user);
    }

    public function test_index_endpoint(): void
    {
        $response = $this->getJson('/api/v2/customer');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'loyal_customers',
                    'customer_preferences',
                    'customer_spending',
                ],
            ]);
    }

    public function test_get_loyal_customers_endpoint(): void
    {
        $response = $this->getJson('/api/v2/customer/loyal');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_customer_spending_endpoint(): void
    {
        $response = $this->getJson('/api/v2/customer/spending/CUST001');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_get_customer_preferences_endpoint(): void
    {
        $response = $this->getJson('/api/v2/customer/preferences/CUST001');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }
}

<?php

namespace Tests\Feature\Api\V2;

use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class ReportControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user and authenticate
        $user = User::factory()->create();
        Sanctum::actingAs($user);
        
        // Create test data
        Order::factory()->count(5)->create();
    }

    /**
     * Test generating a report.
     *
     * @return void
     */
    public function testGenerateReport(): void
    {
        $response = $this->postJson('/api/v2/reports/generate', [
            'model' => 'order',
            'columns' => ['id', 'customer_id', 'total_amount', 'status'],
            'filters' => [],
            'sort' => ['id' => 'asc'],
            'per_page' => 3,
            'page' => 1,
        ]);
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
                'meta' => [
                    'total',
                    'per_page',
                    'current_page',
                    'last_page',
                    'from',
                    'to',
                ],
            ])
            ->assertJson([
                'success' => true,
                'meta' => [
                    'total' => 5,
                    'per_page' => 3,
                    'current_page' => 1,
                    'last_page' => 2,
                    'from' => 1,
                    'to' => 3,
                ],
            ]);
    }

    /**
     * Test generating a report with invalid model.
     *
     * @return void
     */
    public function testGenerateReportWithInvalidModel(): void
    {
        $response = $this->postJson('/api/v2/reports/generate', [
            'model' => 'invalid',
            'columns' => ['id', 'name'],
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['model']);
    }

    /**
     * Test exporting a report.
     *
     * @return void
     */
    public function testExportReport(): void
    {
        $response = $this->postJson('/api/v2/reports/export', [
            'format' => 'csv',
            'model' => 'order',
            'columns' => ['id', 'customer_id', 'total_amount', 'status'],
            'headers' => ['ID', 'Customer ID', 'Total Amount', 'Status'],
            'filters' => [],
            'sort' => ['id' => 'asc'],
            'filename' => 'orders-report',
            'title' => 'Orders Report',
        ]);
        
        $response->assertStatus(200)
            ->assertHeader('Content-Type', 'text/csv; charset=UTF-8')
            ->assertHeader('Content-Disposition', 'attachment; filename=orders-report.csv');
    }

    /**
     * Test exporting a report with invalid format.
     *
     * @return void
     */
    public function testExportReportWithInvalidFormat(): void
    {
        $response = $this->postJson('/api/v2/reports/export', [
            'format' => 'invalid',
            'model' => 'order',
            'columns' => ['id', 'customer_id', 'total_amount', 'status'],
            'headers' => ['ID', 'Customer ID', 'Total Amount', 'Status'],
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['format']);
    }

    /**
     * Test getting available columns for a model.
     *
     * @return void
     */
    public function testGetColumns(): void
    {
        $response = $this->getJson('/api/v2/reports/columns?model=order');
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ]);
    }

    /**
     * Test getting available models for reporting.
     *
     * @return void
     */
    public function testGetModels(): void
    {
        $response = $this->getJson('/api/v2/reports/models');
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'order' => 'App\\Models\\Order',
                    'customer' => 'App\\Models\\Customer',
                    'product' => 'App\\Models\\Product',
                    'payment' => 'App\\Models\\Payment',
                    'subscription' => 'App\\Models\\Subscription',
                    'delivery' => 'App\\Models\\Delivery',
                    'kitchen' => 'App\\Models\\Kitchen',
                    'meal' => 'App\\Models\\Meal',
                    'user' => 'App\\Models\\User',
                ],
            ]);
    }

    /**
     * Test unauthorized access to report endpoints.
     *
     * @return void
     */
    public function testUnauthorizedAccess(): void
    {
        // Log out the user
        auth()->logout();
        
        $response = $this->postJson('/api/v2/reports/generate', [
            'model' => 'order',
            'columns' => ['id', 'customer_id', 'total_amount', 'status'],
        ]);
        
        $response->assertStatus(401);
    }
}

<?php

namespace Tests\Unit\Repositories;

use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\Product;
use App\Repositories\OrderRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new OrderRepository(new Order());
    }

    public function test_get_years(): void
    {
        // Create test products
        $product = Product::create([
            'pk_product_code' => 'PROD001',
            'name' => 'Test Product',
            'product_type' => 'meal',
        ]);

        // Create test orders
        Order::create([
            'pk_order_no' => 'ORD001',
            'product_code' => 'PROD001',
            'fk_kitchen_code' => 'K001',
            'order_date' => '2023-01-01',
        ]);

        Order::create([
            'pk_order_no' => 'ORD002',
            'product_code' => 'PROD001',
            'fk_kitchen_code' => 'K001',
            'order_date' => '2022-01-01',
        ]);

        // Test getYears method
        $years = $this->repository->getYears('K001');
        $this->assertCount(2, $years);
        $this->assertContains(2023, $years);
        $this->assertContains(2022, $years);
    }

    public function test_get_months(): void
    {
        // Create test products
        $product = Product::create([
            'pk_product_code' => 'PROD001',
            'name' => 'Test Product',
            'product_type' => 'meal',
        ]);

        // Create test orders
        Order::create([
            'pk_order_no' => 'ORD001',
            'product_code' => 'PROD001',
            'fk_kitchen_code' => 'K001',
            'order_date' => '2023-01-01',
        ]);

        Order::create([
            'pk_order_no' => 'ORD002',
            'product_code' => 'PROD001',
            'fk_kitchen_code' => 'K001',
            'order_date' => '2023-02-01',
        ]);

        // Test getMonths method
        $months = $this->repository->getMonths('K001', 2023);
        $this->assertCount(2, $months);
        $this->assertContains(1, $months);
        $this->assertContains(2, $months);
    }

    public function test_get_common_extra_meals(): void
    {
        // Create test products
        $meal = Product::create([
            'pk_product_code' => 'MEAL001',
            'name' => 'Chicken Curry',
            'product_type' => 'meal',
        ]);

        $extra = Product::create([
            'pk_product_code' => 'EXTRA001',
            'name' => 'Extra Cheese',
            'product_type' => 'extra',
        ]);

        // Create test orders
        $order = Order::create([
            'pk_order_no' => 'ORD001',
            'product_code' => 'MEAL001',
            'product_name' => 'Chicken Curry',
            'fk_kitchen_code' => 'K001',
            'order_date' => '2023-01-01',
            'order_status' => 'Completed',
        ]);

        // Create order extras
        OrderExtra::create([
            'order_id' => 'ORD001',
            'extra_id' => 'EXTRA001',
            'quantity' => 1,
            'price' => 2.50,
        ]);

        // Test getCommonExtraMeals method
        $extras = $this->repository->getCommonExtraMeals('K001');
        
        // If the order_extras table exists and has data
        if (count($extras) > 0) {
            $this->assertCount(1, $extras);
            $this->assertEquals('Chicken Curry', $extras[0]->meal);
            $this->assertEquals('Extra Cheese', $extras[0]->extra);
            $this->assertEquals(1, $extras[0]->count);
        } else {
            // If using fallback data
            $this->assertGreaterThan(0, count($extras));
            $this->assertArrayHasKey('meal', $extras[0]);
            $this->assertArrayHasKey('extra', $extras[0]);
            $this->assertArrayHasKey('count', $extras[0]);
        }
    }
}

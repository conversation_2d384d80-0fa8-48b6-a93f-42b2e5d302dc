<?php

namespace Tests\Unit\Listeners;

use App\Events\OrderCreated;
use App\Events\OrderUpdated;
use App\Events\OrderCancelled;
use App\Listeners\UpdateAnalyticsData;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class UpdateAnalyticsDataTest extends TestCase
{
    protected $listener;
    protected $order;

    protected function setUp(): void
    {
        parent::setUp();

        $this->listener = new UpdateAnalyticsData();
        
        $this->order = Mockery::mock(Order::class);
        $this->order->shouldReceive('getAttribute')
            ->with('pk_order_no')
            ->andReturn('ORD001');
    }

    public function test_handle_order_created(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with("Updating analytics data for order ORD001 (created)");
        
        Log::shouldReceive('info')
            ->once()
            ->with("Analytics data updated for order ORD001");
        
        $event = new OrderCreated($this->order);
        
        $this->listener->handleOrderCreated($event);
    }

    public function test_handle_order_updated(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with("Updating analytics data for order ORD001 (updated)");
        
        Log::shouldReceive('info')
            ->once()
            ->with("Analytics data updated for order ORD001");
        
        $event = new OrderUpdated($this->order);
        
        $this->listener->handleOrderUpdated($event);
    }

    public function test_handle_order_cancelled(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with("Updating analytics data for order ORD001 (cancelled)");
        
        Log::shouldReceive('info')
            ->once()
            ->with("Analytics data updated for order ORD001");
        
        $event = new OrderCancelled($this->order);
        
        $this->listener->handleOrderCancelled($event);
    }
}

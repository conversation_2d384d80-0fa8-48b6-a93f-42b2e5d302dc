<?php

namespace Tests\Unit\Services;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use App\Repositories\Interfaces\TempOrderPaymentRepositoryInterface;
use App\Services\SalesService;
use Mockery;
use Tests\TestCase;

class SalesServiceTest extends TestCase
{
    protected $orderRepository;
    protected $tempOrderPaymentRepository;
    protected $salesService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = Mockery::mock(OrderRepositoryInterface::class);
        $this->tempOrderPaymentRepository = Mockery::mock(TempOrderPaymentRepositoryInterface::class);
        $this->salesService = new SalesService($this->orderRepository, $this->tempOrderPaymentRepository);
    }

    public function test_get_years(): void
    {
        $expected = [2023, 2022, 2021];

        $this->orderRepository->shouldReceive('getYears')
            ->once()
            ->with('all', ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->salesService->getYears('all', ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_months(): void
    {
        $months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        $expected = [
            1 => 'January',
            2 => 'February',
            3 => 'March',
            4 => 'April',
            5 => 'May',
            6 => 'June',
            7 => 'July',
            8 => 'August',
            9 => 'September',
            10 => 'October',
            11 => 'November',
            12 => 'December',
        ];

        $this->orderRepository->shouldReceive('getMonths')
            ->once()
            ->with('all', 2023, ['K001', 'K002'])
            ->andReturn($months);

        $result = $this->salesService->getMonths('all', 2023, ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_avg_meal(): void
    {
        $expected = [
            ['meal_name' => 'Chicken Curry', 'qty' => 2.5],
            ['meal_name' => 'Vegetable Biryani', 'qty' => 2.0],
        ];

        $this->orderRepository->shouldReceive('avgMealPerCustomer')
            ->once()
            ->with('all', 2023, 1, 5, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->salesService->getAvgMeal('all', 2023, 1, ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_payment_methods(): void
    {
        $expected = [
            ['payment_mode' => 'Credit Card', 'count' => 150],
            ['payment_mode' => 'Cash', 'count' => 100],
            ['payment_mode' => 'PayPal', 'count' => 50],
        ];

        $this->orderRepository->shouldReceive('commonPaymentMode')
            ->once()
            ->with('all', ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->salesService->getPaymentMethods('all', ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_revenue(): void
    {
        $expected = [
            ['gross_amount' => 25000.50],
        ];

        $this->orderRepository->shouldReceive('revenueShare')
            ->once()
            ->with('all', 'yearly', 2023, null, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->salesService->getRevenue('all', 2023, null, ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_comparison(): void
    {
        $expected = [
            ['month' => 1, 'gross_amount' => 2500.50, 'net_amount' => 2250.25],
            ['month' => 2, 'gross_amount' => 3000.75, 'net_amount' => 2700.50],
        ];

        $this->orderRepository->shouldReceive('salesComparison')
            ->once()
            ->with('all', 'monthly', 2023, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->salesService->getComparison('all', 2023, 'monthly', ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }
}

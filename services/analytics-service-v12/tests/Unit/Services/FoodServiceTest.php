<?php

namespace Tests\Unit\Services;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use App\Services\FoodService;
use Mockery;
use Tests\TestCase;

class FoodServiceTest extends TestCase
{
    protected $orderRepository;
    protected $foodService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = Mockery::mock(OrderRepositoryInterface::class);
        $this->foodService = new FoodService($this->orderRepository);
    }

    public function test_get_years(): void
    {
        $expected = [2023, 2022, 2021];

        $this->orderRepository->shouldReceive('getYears')
            ->once()
            ->with('all', ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->foodService->getYears('all', ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_months(): void
    {
        $months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        $expected = [
            1 => 'January',
            2 => 'February',
            3 => 'March',
            4 => 'April',
            5 => 'May',
            6 => 'June',
            7 => 'July',
            8 => 'August',
            9 => 'September',
            10 => 'October',
            11 => 'November',
            12 => 'December',
        ];

        $this->orderRepository->shouldReceive('getMonths')
            ->once()
            ->with('all', 2023, ['K001', 'K002'])
            ->andReturn($months);

        $result = $this->foodService->getMonths('all', 2023, ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_popular_meals(): void
    {
        $expected = [
            ['product_name' => 'Chicken Curry', 'qty' => 250],
            ['product_name' => 'Vegetable Biryani', 'qty' => 200],
        ];

        $this->orderRepository->shouldReceive('bestWorstMeals')
            ->once()
            ->with('all', 'yearly', 'best', 'all', 2023, null, 5, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->foodService->getPopularMeals('all', 2023, null, ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_meal_performance(): void
    {
        $expected = [
            ['product_name' => 'Chicken Curry', 'qty' => 250],
            ['product_name' => 'Vegetable Biryani', 'qty' => 200],
        ];

        $this->orderRepository->shouldReceive('bestWorstMeals')
            ->once()
            ->with('all', 'monthly', 'best', 'all', 2023, 1, 5, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->foodService->getMealPerformance('all', 2023, 1, 'best', ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_common_extras(): void
    {
        $expected = [
            ['meal' => 'Chicken Curry', 'extra' => 'Extra Cheese', 'count' => 50],
            ['meal' => 'Vegetable Biryani', 'extra' => 'Extra Spicy', 'count' => 30],
        ];

        $this->orderRepository->shouldReceive('getCommonExtraMeals')
            ->once()
            ->with('all', 10, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->foodService->getCommonExtras('all', ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }
}

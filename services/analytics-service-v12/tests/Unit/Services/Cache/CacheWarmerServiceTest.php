<?php

namespace Tests\Unit\Services\Cache;

use App\Services\Cache\CacheService;
use App\Services\Cache\CacheWarmerService;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class CacheWarmerServiceTest extends TestCase
{
    /**
     * @var CacheWarmerService
     */
    protected $cacheWarmerService;

    /**
     * @var CacheService|Mockery\MockInterface
     */
    protected $cacheService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cacheService = Mockery::mock(CacheService::class);
        $this->cacheWarmerService = new CacheWarmerService($this->cacheService);
    }

    /**
     * Test warming up the cache.
     *
     * @return void
     */
    public function testWarmUp(): void
    {
        // Mock the DB facade
        DB::shouldReceive('table')
            ->andReturnSelf()
            ->shouldReceive('selectRaw')
            ->andReturnSelf()
            ->shouldReceive('select')
            ->andReturnSelf()
            ->shouldReceive('join')
            ->andReturnSelf()
            ->shouldReceive('distinct')
            ->andReturnSelf()
            ->shouldReceive('whereYear')
            ->andReturnSelf()
            ->shouldReceive('whereMonth')
            ->andReturnSelf()
            ->shouldReceive('groupBy')
            ->andReturnSelf()
            ->shouldReceive('orderBy')
            ->andReturnSelf()
            ->shouldReceive('orderByDesc')
            ->andReturnSelf()
            ->shouldReceive('limit')
            ->andReturnSelf()
            ->shouldReceive('pluck')
            ->andReturn(collect([2023, 2024]))
            ->shouldReceive('sum')
            ->andReturn(1000)
            ->shouldReceive('avg')
            ->andReturn(100)
            ->shouldReceive('count')
            ->andReturn(10)
            ->shouldReceive('get')
            ->andReturn(collect([
                (object) ['id' => 1, 'name' => 'Test 1'],
                (object) ['id' => 2, 'name' => 'Test 2'],
            ]))
            ->shouldReceive('keyBy')
            ->andReturnSelf()
            ->shouldReceive('map')
            ->andReturn(collect(['key1' => 'value1', 'key2' => 'value2']))
            ->shouldReceive('toArray')
            ->andReturn(['key1' => 'value1', 'key2' => 'value2']);
        
        // Mock the CacheService
        $this->cacheService->shouldReceive('warmUp')
            ->once()
            ->andReturn([
                ['key' => 'years', 'success' => true],
                ['key' => 'months_2023', 'success' => true],
                ['key' => 'payment_methods', 'success' => true],
                ['key' => 'revenue_2023_05', 'success' => true],
                ['key' => 'sales_summary_2023_05', 'success' => true],
                ['key' => 'popular_meals_2023_05', 'success' => true],
                ['key' => 'loyal_customers', 'success' => true],
                ['key' => 'system_settings', 'success' => true],
            ]);
        
        // Call the warmUp method
        $results = $this->cacheWarmerService->warmUp();
        
        // Assert the results
        $this->assertCount(8, $results);
        $this->assertTrue($results[0]['success']);
        $this->assertTrue($results[1]['success']);
        $this->assertTrue($results[2]['success']);
        $this->assertTrue($results[3]['success']);
        $this->assertTrue($results[4]['success']);
        $this->assertTrue($results[5]['success']);
        $this->assertTrue($results[6]['success']);
        $this->assertTrue($results[7]['success']);
    }

    /**
     * Test warming up the cache with failures.
     *
     * @return void
     */
    public function testWarmUpWithFailures(): void
    {
        // Mock the DB facade
        DB::shouldReceive('table')
            ->andReturnSelf()
            ->shouldReceive('selectRaw')
            ->andReturnSelf()
            ->shouldReceive('select')
            ->andReturnSelf()
            ->shouldReceive('join')
            ->andReturnSelf()
            ->shouldReceive('distinct')
            ->andReturnSelf()
            ->shouldReceive('whereYear')
            ->andReturnSelf()
            ->shouldReceive('whereMonth')
            ->andReturnSelf()
            ->shouldReceive('groupBy')
            ->andReturnSelf()
            ->shouldReceive('orderBy')
            ->andReturnSelf()
            ->shouldReceive('orderByDesc')
            ->andReturnSelf()
            ->shouldReceive('limit')
            ->andReturnSelf()
            ->shouldReceive('pluck')
            ->andReturn(collect([2023, 2024]))
            ->shouldReceive('sum')
            ->andReturn(1000)
            ->shouldReceive('avg')
            ->andReturn(100)
            ->shouldReceive('count')
            ->andReturn(10)
            ->shouldReceive('get')
            ->andReturn(collect([
                (object) ['id' => 1, 'name' => 'Test 1'],
                (object) ['id' => 2, 'name' => 'Test 2'],
            ]))
            ->shouldReceive('keyBy')
            ->andReturnSelf()
            ->shouldReceive('map')
            ->andReturn(collect(['key1' => 'value1', 'key2' => 'value2']))
            ->shouldReceive('toArray')
            ->andReturn(['key1' => 'value1', 'key2' => 'value2']);
        
        // Mock the CacheService
        $this->cacheService->shouldReceive('warmUp')
            ->once()
            ->andReturn([
                ['key' => 'years', 'success' => true],
                ['key' => 'months_2023', 'success' => false, 'error' => 'Test error'],
                ['key' => 'payment_methods', 'success' => true],
                ['key' => 'revenue_2023_05', 'success' => false, 'error' => 'Test error'],
                ['key' => 'sales_summary_2023_05', 'success' => true],
                ['key' => 'popular_meals_2023_05', 'success' => true],
                ['key' => 'loyal_customers', 'success' => true],
                ['key' => 'system_settings', 'success' => true],
            ]);
        
        // Call the warmUp method
        $results = $this->cacheWarmerService->warmUp();
        
        // Assert the results
        $this->assertCount(8, $results);
        $this->assertTrue($results[0]['success']);
        $this->assertFalse($results[1]['success']);
        $this->assertTrue($results[2]['success']);
        $this->assertFalse($results[3]['success']);
        $this->assertTrue($results[4]['success']);
        $this->assertTrue($results[5]['success']);
        $this->assertTrue($results[6]['success']);
        $this->assertTrue($results[7]['success']);
    }
}

<?php

namespace Tests\Unit\Services\Cache;

use App\Services\Cache\CacheService;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CacheServiceTest extends TestCase
{
    /**
     * @var CacheService
     */
    protected $cacheService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cacheService = new CacheService('array');
        
        // Clear the cache before each test
        Cache::store('array')->flush();
    }

    /**
     * Test remembering a value in the cache.
     *
     * @return void
     */
    public function testRemember(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        $ttl = 60;
        
        $result = $this->cacheService->remember($key, function () use ($value) {
            return $value;
        }, $ttl);
        
        $this->assertEquals($value, $result);
        $this->assertTrue(Cache::store('array')->has('cache:' . $key));
    }

    /**
     * Test remembering a value in the cache with tags.
     *
     * @return void
     */
    public function testRememberWithTags(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        $ttl = 60;
        $tags = ['tag1', 'tag2'];
        
        $result = $this->cacheService->remember($key, function () use ($value) {
            return $value;
        }, $ttl, $tags);
        
        $this->assertEquals($value, $result);
        $this->assertTrue(Cache::store('array')->tags(['app:tag1', 'app:tag2'])->has('cache:' . $key));
    }

    /**
     * Test remembering a value in the cache forever.
     *
     * @return void
     */
    public function testRememberForever(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        
        $result = $this->cacheService->rememberForever($key, function () use ($value) {
            return $value;
        });
        
        $this->assertEquals($value, $result);
        $this->assertTrue(Cache::store('array')->has('cache:' . $key));
    }

    /**
     * Test putting a value in the cache.
     *
     * @return void
     */
    public function testPut(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        $ttl = 60;
        
        $result = $this->cacheService->put($key, $value, $ttl);
        
        $this->assertTrue($result);
        $this->assertTrue(Cache::store('array')->has('cache:' . $key));
        $this->assertEquals($value, Cache::store('array')->get('cache:' . $key));
    }

    /**
     * Test putting a value in the cache forever.
     *
     * @return void
     */
    public function testForever(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        
        $result = $this->cacheService->forever($key, $value);
        
        $this->assertTrue($result);
        $this->assertTrue(Cache::store('array')->has('cache:' . $key));
        $this->assertEquals($value, Cache::store('array')->get('cache:' . $key));
    }

    /**
     * Test getting a value from the cache.
     *
     * @return void
     */
    public function testGet(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        
        Cache::store('array')->put('cache:' . $key, $value, 60);
        
        $result = $this->cacheService->get($key);
        
        $this->assertEquals($value, $result);
    }

    /**
     * Test checking if a key exists in the cache.
     *
     * @return void
     */
    public function testHas(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        
        Cache::store('array')->put('cache:' . $key, $value, 60);
        
        $result = $this->cacheService->has($key);
        
        $this->assertTrue($result);
    }

    /**
     * Test removing a value from the cache.
     *
     * @return void
     */
    public function testForget(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        
        Cache::store('array')->put('cache:' . $key, $value, 60);
        
        $result = $this->cacheService->forget($key);
        
        $this->assertTrue($result);
        $this->assertFalse(Cache::store('array')->has('cache:' . $key));
    }

    /**
     * Test flushing all items with the given tags.
     *
     * @return void
     */
    public function testFlushTags(): void
    {
        $key1 = 'test_key1';
        $key2 = 'test_key2';
        $value = 'test_value';
        $tags = ['tag1', 'tag2'];
        
        Cache::store('array')->tags(['app:tag1', 'app:tag2'])->put('cache:' . $key1, $value, 60);
        Cache::store('array')->tags(['app:tag1', 'app:tag2'])->put('cache:' . $key2, $value, 60);
        
        $result = $this->cacheService->flushTags($tags);
        
        $this->assertTrue($result);
        $this->assertFalse(Cache::store('array')->tags(['app:tag1', 'app:tag2'])->has('cache:' . $key1));
        $this->assertFalse(Cache::store('array')->tags(['app:tag1', 'app:tag2'])->has('cache:' . $key2));
    }

    /**
     * Test flushing all items from the cache.
     *
     * @return void
     */
    public function testFlush(): void
    {
        $key1 = 'test_key1';
        $key2 = 'test_key2';
        $value = 'test_value';
        
        Cache::store('array')->put('cache:' . $key1, $value, 60);
        Cache::store('array')->put('cache:' . $key2, $value, 60);
        
        $result = $this->cacheService->flush();
        
        $this->assertTrue($result);
        $this->assertFalse(Cache::store('array')->has('cache:' . $key1));
        $this->assertFalse(Cache::store('array')->has('cache:' . $key2));
    }

    /**
     * Test warming up the cache.
     *
     * @return void
     */
    public function testWarmUp(): void
    {
        $items = [
            [
                'key' => 'test_key1',
                'callback' => function () {
                    return 'test_value1';
                },
                'ttl' => 60,
                'tags' => ['tag1', 'tag2'],
            ],
            [
                'key' => 'test_key2',
                'callback' => function () {
                    return 'test_value2';
                },
                'ttl' => 120,
            ],
        ];
        
        $results = $this->cacheService->warmUp($items);
        
        $this->assertCount(2, $results);
        $this->assertTrue($results[0]['success']);
        $this->assertTrue($results[1]['success']);
        $this->assertEquals('test_key1', $results[0]['key']);
        $this->assertEquals('test_key2', $results[1]['key']);
        
        $this->assertTrue(Cache::store('array')->tags(['app:tag1', 'app:tag2'])->has('cache:test_key1'));
        $this->assertTrue(Cache::store('array')->has('cache:test_key2'));
        $this->assertEquals('test_value1', Cache::store('array')->tags(['app:tag1', 'app:tag2'])->get('cache:test_key1'));
        $this->assertEquals('test_value2', Cache::store('array')->get('cache:test_key2'));
    }
}

<?php

namespace Tests\Unit\Services;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use App\Services\CustomerService;
use Mockery;
use Tests\TestCase;

class CustomerServiceTest extends TestCase
{
    protected $orderRepository;
    protected $customerService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = Mockery::mock(OrderRepositoryInterface::class);
        $this->customerService = new CustomerService($this->orderRepository);
    }

    public function test_get_loyal_customers(): void
    {
        $expected = [
            ['customer_code' => 'CUST001', 'customer_name' => '<PERSON>', 'net_amount' => 5000.75],
            ['customer_code' => 'CUST002', 'customer_name' => '<PERSON>', 'net_amount' => 4500.50],
        ];

        $this->orderRepository->shouldReceive('mostLoyalCustomers')
            ->once()
            ->with('all', 10, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->customerService->getLoyalCustomers('all', ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_customer_spending(): void
    {
        $expected = [
            ['customer_name' => 'John Doe', 'yearly' => 5000.75, 'monthly' => 416.73],
        ];

        $this->orderRepository->shouldReceive('avgOrderOfCustomer')
            ->once()
            ->with('all', ['CUST001'], ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->customerService->getCustomerSpending('all', ['CUST001'], ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }

    public function test_get_customer_preferences(): void
    {
        $expected = [
            ['customer_code' => 'CUST001', 'customer_name' => 'John Doe', 'product_name' => 'Chicken Curry', 'net_amount' => 2500.50],
            ['customer_code' => 'CUST001', 'customer_name' => 'John Doe', 'product_name' => 'Vegetable Biryani', 'net_amount' => 2000.25],
        ];

        $this->orderRepository->shouldReceive('mealThatDriveLoyality')
            ->once()
            ->with('all', ['CUST001'], 10, ['K001', 'K002'])
            ->andReturn($expected);

        $result = $this->customerService->getCustomerPreferences('all', ['CUST001'], ['K001', 'K002']);

        $this->assertEquals($expected, $result);
    }
}

<?php

namespace Tests\Unit\Services;

use App\Services\RabbitMQConsumer;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use Mockery;
use Tests\TestCase;

class RabbitMQConsumerTest extends TestCase
{
    protected $channel;
    protected $consumer;

    protected function setUp(): void
    {
        parent::setUp();

        $this->channel = Mockery::mock(AMQPChannel::class);
        
        // Mock the exchange_declare method
        $this->channel->shouldReceive('exchange_declare')
            ->once()
            ->withArgs([
                config('queue.connections.rabbitmq.options.exchange.name'),
                config('queue.connections.rabbitmq.options.exchange.type'),
                config('queue.connections.rabbitmq.options.exchange.passive'),
                config('queue.connections.rabbitmq.options.exchange.durable'),
                config('queue.connections.rabbitmq.options.exchange.auto_delete')
            ]);
        
        // Mock the queue_declare method
        $this->channel->shouldReceive('queue_declare')
            ->once()
            ->withArgs([
                config('queue.connections.rabbitmq.queue'),
                config('queue.connections.rabbitmq.options.queue.passive'),
                config('queue.connections.rabbitmq.options.queue.durable'),
                config('queue.connections.rabbitmq.options.queue.exclusive'),
                config('queue.connections.rabbitmq.options.queue.auto_delete')
            ]);
        
        $this->consumer = new RabbitMQConsumer($this->channel);
    }

    public function test_bind(): void
    {
        $routingKey = 'order.created';
        
        // Mock the queue_bind method
        $this->channel->shouldReceive('queue_bind')
            ->once()
            ->withArgs([
                config('queue.connections.rabbitmq.queue'),
                config('queue.connections.rabbitmq.options.exchange.name'),
                $routingKey
            ]);
        
        $this->consumer->bind($routingKey);
        
        // No assertion needed as we're testing that the method calls the expected methods on the channel
        $this->assertTrue(true);
    }

    public function test_consume(): void
    {
        // Mock the basic_qos method
        $this->channel->shouldReceive('basic_qos')
            ->once()
            ->withArgs([null, 1, null]);
        
        // Mock the basic_consume method
        $this->channel->shouldReceive('basic_consume')
            ->once()
            ->withArgs(function ($queue, $consumerTag, $noLocal, $noAck, $exclusive, $noWait, $callback) {
                return $queue === config('queue.connections.rabbitmq.queue') &&
                    $consumerTag === '' &&
                    $noLocal === false &&
                    $noAck === false &&
                    $exclusive === false &&
                    $noWait === false &&
                    is_callable($callback);
            });
        
        // Mock the is_consuming method to return false so the consume method doesn't enter an infinite loop
        $this->channel->shouldReceive('is_consuming')
            ->once()
            ->andReturn(false);
        
        $callbackCalled = false;
        $this->consumer->consume(function ($data) use (&$callbackCalled) {
            $callbackCalled = true;
            return true;
        });
        
        // The callback shouldn't be called because we're mocking the channel
        $this->assertFalse($callbackCalled);
    }
}

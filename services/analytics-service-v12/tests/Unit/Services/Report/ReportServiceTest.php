<?php

namespace Tests\Unit\Services\Report;

use App\Models\Order;
use App\Services\Export\ExportService;
use App\Services\Report\ReportService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Mockery;
use Tests\TestCase;

class ReportServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var ReportService
     */
    protected $reportService;

    /**
     * @var ExportService|Mockery\MockInterface
     */
    protected $exportService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->exportService = Mockery::mock(ExportService::class);
        $this->reportService = new ReportService($this->exportService);
    }

    /**
     * Test generating a report.
     *
     * @return void
     */
    public function testGenerateReport(): void
    {
        // Create test data
        Order::factory()->count(5)->create();
        
        // Generate a report
        $report = $this->reportService->generateReport(
            Order::class,
            ['id', 'customer_id', 'total_amount', 'status'],
            [],
            ['id' => 'asc'],
            3,
            1
        );
        
        // Assert the report structure
        $this->assertArrayHasKey('data', $report);
        $this->assertArrayHasKey('meta', $report);
        $this->assertArrayHasKey('total', $report['meta']);
        $this->assertArrayHasKey('per_page', $report['meta']);
        $this->assertArrayHasKey('current_page', $report['meta']);
        $this->assertArrayHasKey('last_page', $report['meta']);
        $this->assertArrayHasKey('from', $report['meta']);
        $this->assertArrayHasKey('to', $report['meta']);
        
        // Assert the report data
        $this->assertCount(3, $report['data']);
        $this->assertEquals(5, $report['meta']['total']);
        $this->assertEquals(3, $report['meta']['per_page']);
        $this->assertEquals(1, $report['meta']['current_page']);
        $this->assertEquals(2, $report['meta']['last_page']);
        $this->assertEquals(1, $report['meta']['from']);
        $this->assertEquals(3, $report['meta']['to']);
    }

    /**
     * Test exporting a report.
     *
     * @return void
     */
    public function testExportReport(): void
    {
        // Create test data
        Order::factory()->count(5)->create();
        
        // Set up the mock
        $this->exportService->shouldReceive('export')
            ->once()
            ->with(
                'csv',
                Mockery::type('array'),
                ['ID', 'Customer ID', 'Total Amount', 'Status'],
                'orders-report',
                'Orders Report'
            )
            ->andReturn(response()->streamDownload(function () {}, 'orders-report.csv'));
        
        // Export a report
        $response = $this->reportService->exportReport(
            'csv',
            Order::class,
            ['id', 'customer_id', 'total_amount', 'status'],
            ['ID', 'Customer ID', 'Total Amount', 'Status'],
            [],
            ['id' => 'asc'],
            'orders-report',
            'Orders Report'
        );
        
        // Assert the response
        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * Test applying filters to a query.
     *
     * @return void
     */
    public function testApplyFilters(): void
    {
        // Create test data
        Order::factory()->count(3)->create(['status' => 'pending']);
        Order::factory()->count(2)->create(['status' => 'completed']);
        
        // Generate a report with a filter
        $report = $this->reportService->generateReport(
            Order::class,
            ['id', 'customer_id', 'total_amount', 'status'],
            ['status' => 'completed'],
            ['id' => 'asc']
        );
        
        // Assert the filtered data
        $this->assertCount(2, $report['data']);
        $this->assertEquals(2, $report['meta']['total']);
        
        // Test complex filter
        $report = $this->reportService->generateReport(
            Order::class,
            ['id', 'customer_id', 'total_amount', 'status'],
            ['status' => ['operator' => 'eq', 'value' => 'pending']],
            ['id' => 'asc']
        );
        
        // Assert the filtered data
        $this->assertCount(3, $report['data']);
        $this->assertEquals(3, $report['meta']['total']);
    }

    /**
     * Test applying sorting to a query.
     *
     * @return void
     */
    public function testApplySorting(): void
    {
        // Create test data
        Order::factory()->create(['total_amount' => 100]);
        Order::factory()->create(['total_amount' => 200]);
        Order::factory()->create(['total_amount' => 50]);
        
        // Generate a report with ascending sort
        $report = $this->reportService->generateReport(
            Order::class,
            ['id', 'total_amount'],
            [],
            ['total_amount' => 'asc']
        );
        
        // Assert the sorted data
        $this->assertEquals(50, $report['data'][0]->total_amount);
        $this->assertEquals(100, $report['data'][1]->total_amount);
        $this->assertEquals(200, $report['data'][2]->total_amount);
        
        // Generate a report with descending sort
        $report = $this->reportService->generateReport(
            Order::class,
            ['id', 'total_amount'],
            [],
            ['total_amount' => 'desc']
        );
        
        // Assert the sorted data
        $this->assertEquals(200, $report['data'][0]->total_amount);
        $this->assertEquals(100, $report['data'][1]->total_amount);
        $this->assertEquals(50, $report['data'][2]->total_amount);
    }

    /**
     * Test getting available columns for a model.
     *
     * @return void
     */
    public function testGetAvailableColumns(): void
    {
        // Mock the Schema facade
        Schema::shouldReceive('getColumnListing')
            ->once()
            ->with('orders')
            ->andReturn(['id', 'customer_id', 'total_amount', 'status', 'created_at', 'updated_at']);
        
        // Get available columns
        $columns = $this->reportService->getAvailableColumns(Order::class);
        
        // Assert the columns
        $this->assertEquals(['id', 'customer_id', 'total_amount', 'status', 'created_at', 'updated_at'], $columns);
    }
}

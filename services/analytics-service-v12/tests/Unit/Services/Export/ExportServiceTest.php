<?php

namespace Tests\Unit\Services\Export;

use App\Services\Export\ExportService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Tests\TestCase;

class ExportServiceTest extends TestCase
{
    /**
     * @var ExportService
     */
    protected $exportService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->exportService = new ExportService();
        
        // Create a temporary directory for test files
        Storage::makeDirectory('temp');
    }

    /**
     * Clean up the test environment.
     *
     * @return void
     */
    protected function tearDown(): void
    {
        // Remove the temporary directory
        Storage::deleteDirectory('temp');
        
        parent::tearDown();
    }

    /**
     * Test exporting data to CSV format.
     *
     * @return void
     */
    public function testToCsv(): void
    {
        $data = [
            ['id' => 1, 'name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => '<PERSON>', 'email' => '<EMAIL>'],
        ];
        
        $headers = ['ID', 'Name', 'Email'];
        $filename = 'test-export';
        
        $response = $this->exportService->toCsv($data, $headers, $filename);
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('text/csv', $response->headers->get('Content-Type'));
        $this->assertEquals('attachment; filename="test-export.csv"', $response->headers->get('Content-Disposition'));
    }

    /**
     * Test exporting data to Excel format.
     *
     * @return void
     */
    public function testToExcel(): void
    {
        $data = [
            ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>'],
        ];
        
        $headers = ['ID', 'Name', 'Email'];
        $filename = 'test-export';
        
        $response = $this->exportService->toExcel($data, $headers, $filename);
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', $response->headers->get('Content-Type'));
        $this->assertEquals('attachment; filename="test-export.xlsx"', $response->headers->get('Content-Disposition'));
    }

    /**
     * Test exporting data to PDF format.
     *
     * @return void
     */
    public function testToPdf(): void
    {
        $data = [
            ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>'],
        ];
        
        $headers = ['ID', 'Name', 'Email'];
        $filename = 'test-export';
        $title = 'Test Export';
        
        $response = $this->exportService->toPdf($data, $headers, $filename, $title);
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('application/pdf', $response->headers->get('Content-Type'));
        $this->assertEquals('attachment; filename="test-export.pdf"', $response->headers->get('Content-Disposition'));
    }

    /**
     * Test exporting data with an invalid format.
     *
     * @return void
     */
    public function testExportWithInvalidFormat(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported export format: invalid');
        
        $data = [
            ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
        ];
        
        $headers = ['ID', 'Name', 'Email'];
        $filename = 'test-export';
        
        $this->exportService->export('invalid', $data, $headers, $filename);
    }

    /**
     * Test transforming a collection into an array for export.
     *
     * @return void
     */
    public function testTransformCollection(): void
    {
        // Create a mock collection
        $collection = new Collection([
            (object) ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
            (object) ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>'],
        ]);
        
        $columns = ['id', 'name', 'email'];
        
        $result = $this->exportService->transformCollection($collection, $columns);
        
        $expected = [
            ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>'],
        ];
        
        $this->assertEquals($expected, $result);
    }
}

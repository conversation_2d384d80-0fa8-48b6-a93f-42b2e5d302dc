<?php

namespace Tests\Unit\Services;

use App\Services\RabbitMQPublisher;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use Mockery;
use Tests\TestCase;

class RabbitMQPublisherTest extends TestCase
{
    protected $channel;
    protected $publisher;

    protected function setUp(): void
    {
        parent::setUp();

        $this->channel = Mockery::mock(AMQPChannel::class);
        
        // Mock the exchange_declare method
        $this->channel->shouldReceive('exchange_declare')
            ->once()
            ->withArgs([
                config('queue.connections.rabbitmq.options.exchange.name'),
                config('queue.connections.rabbitmq.options.exchange.type'),
                config('queue.connections.rabbitmq.options.exchange.passive'),
                config('queue.connections.rabbitmq.options.exchange.durable'),
                config('queue.connections.rabbitmq.options.exchange.auto_delete')
            ]);
        
        $this->publisher = new RabbitMQPublisher($this->channel);
    }

    public function test_publish(): void
    {
        $routingKey = 'order.created';
        $data = ['order_id' => 'ORD001', 'status' => 'created'];
        
        // Mock the basic_publish method
        $this->channel->shouldReceive('basic_publish')
            ->once()
            ->withArgs(function (AMQPMessage $message, $exchange, $actualRoutingKey) use ($routingKey, $data) {
                $messageData = json_decode($message->getBody(), true);
                
                return $exchange === config('queue.connections.rabbitmq.options.exchange.name') &&
                    $actualRoutingKey === $routingKey &&
                    $messageData === $data &&
                    $message->get_properties()['content_type'] === 'application/json' &&
                    $message->get_properties()['delivery_mode'] === AMQPMessage::DELIVERY_MODE_PERSISTENT;
            });
        
        $result = $this->publisher->publish($routingKey, $data);
        
        $this->assertTrue($result);
    }

    public function test_publish_with_exception(): void
    {
        $routingKey = 'order.created';
        $data = ['order_id' => 'ORD001', 'status' => 'created'];
        
        // Mock the basic_publish method to throw an exception
        $this->channel->shouldReceive('basic_publish')
            ->once()
            ->andThrow(new \Exception('Connection error'));
        
        $result = $this->publisher->publish($routingKey, $data);
        
        $this->assertFalse($result);
    }
}

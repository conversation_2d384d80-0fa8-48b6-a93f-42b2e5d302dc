_format_version: "2.1"
_transform: true

services:
  - name: analytics-service
    url: http://analytics-service-v12:8000
    routes:
      - name: analytics-service-route
        paths:
          - /v2/analytics
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: analytics-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: analytics-service-v12

routes:
  - name: analytics-service-health
    service: analytics-service
    paths:
      - /v2/health/analytics
    strip_path: true
    preserve_host: true
    protocols:
      - http
      - https
    destinations:
      - port: 8000
        path: /api/health

name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
    paths:
      - 'services/analytics-service-v12/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'services/analytics-service-v12/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      rabbitmq:
        image: rabbitmq:3-management
        ports:
          - 5672:5672
          - 15672:15672
        env:
          RABBITMQ_DEFAULT_USER: guest
          RABBITMQ_DEFAULT_PASS: guest
        options: >-
          --health-cmd "rabbitmqctl status"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, zip, pdo, sqlite, pdo_sqlite, amqp
        coverage: xdebug
    
    - name: Copy .env
      run: cp services/analytics-service-v12/.env.example services/analytics-service-v12/.env
    
    - name: Install Dependencies
      run: |
        cd services/analytics-service-v12
        composer install --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
    
    - name: Generate key
      run: |
        cd services/analytics-service-v12
        php artisan key:generate
    
    - name: Directory Permissions
      run: |
        cd services/analytics-service-v12
        chmod -R 777 storage bootstrap/cache
    
    - name: Create Database
      run: |
        cd services/analytics-service-v12
        touch database/database.sqlite
    
    - name: Run Migrations
      run: |
        cd services/analytics-service-v12
        php artisan migrate --force
    
    - name: Run Tests
      run: |
        cd services/analytics-service-v12
        php artisan test --coverage-clover=coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: services/analytics-service-v12/coverage.xml
        flags: unittests
        fail_ci_if_error: true
  
  build:
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: ./services/analytics-service-v12
        push: true
        tags: cubeonebiz/analytics-service:latest,cubeonebiz/analytics-service:${{ github.sha }}
  
  deploy:
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Update kube config
      run: aws eks update-kubeconfig --name cubeonebiz-cluster --region us-east-1
    
    - name: Deploy to EKS
      run: |
        # Update the image tag in the deployment file
        sed -i "s|image: cubeonebiz/analytics-service:.*|image: cubeonebiz/analytics-service:${{ github.sha }}|g" kubernetes/analytics-service/deployment.yaml
        
        # Apply the Kubernetes manifests
        kubectl apply -f kubernetes/analytics-service/
        
        # Wait for the deployment to complete
        kubectl rollout status deployment/analytics-service -n cubeonebiz

<?php

namespace Shared\Services\Logging;

use Monolog\Formatter\JsonFormatter as BaseJsonFormatter;

class JsonFormatter extends BaseJsonFormatter
{
    public function format(array $record): string
    {
        // Add application name and environment
        $record['application'] = config('app.name');
        $record['environment'] = config('app.env');
        
        // Add correlation ID if available
        if (request()->hasHeader('X-Correlation-ID')) {
            $record['correlation_id'] = request()->header('X-Correlation-ID');
        }
        
        // Add user ID if authenticated
        if (auth()->check()) {
            $record['user_id'] = auth()->id();
        }
        
        // Add request information
        if (request()->route()) {
            $record['request'] = [
                'id' => uniqid(),
                'method' => request()->method(),
                'url' => request()->fullUrl(),
                'route' => request()->route()->getName() ?? 'unnamed',
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ];
        }
        
        return parent::format($record);
    }
}

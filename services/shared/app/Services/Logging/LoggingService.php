<?php

namespace Shared\Services\Logging;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class LoggingService
{
    public function logRequest(Request $request): void
    {
        $correlationId = $request->header('X-Correlation-ID', 'unknown');
        $userId = $request->user() ? $request->user()->id : 'guest';
        
        $context = [
            'correlation_id' => $correlationId,
            'user_id' => $userId,
            'ip' => $request->ip(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
        ];
        
        Log::info('API request', $context);
    }
    
    public function logResponse(Request $request, $response): void
    {
        $correlationId = $request->header('X-Correlation-ID', 'unknown');
        $userId = $request->user() ? $request->user()->id : 'guest';
        $statusCode = $response->getStatusCode();
        
        $context = [
            'correlation_id' => $correlationId,
            'user_id' => $userId,
            'status_code' => $statusCode,
            'response_time' => microtime(true) - LARAVEL_START,
        ];
        
        Log::info('API response', $context);
    }
    
    public function logException(Request $request, Throwable $exception): void
    {
        $correlationId = $request->header('X-Correlation-ID', 'unknown');
        $userId = $request->user() ? $request->user()->id : 'guest';
        
        $context = [
            'correlation_id' => $correlationId,
            'user_id' => $userId,
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
        ];
        
        Log::error('API exception', $context);
    }
}

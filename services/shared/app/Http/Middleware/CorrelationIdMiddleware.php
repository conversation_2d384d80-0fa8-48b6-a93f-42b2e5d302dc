<?php

namespace Shared\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CorrelationIdMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Get correlation ID from request or generate a new one
        $correlationId = $request->header('X-Correlation-ID');
        
        if (!$correlationId) {
            $correlationId = Str::uuid()->toString();
            $request->headers->set('X-Correlation-ID', $correlationId);
        }
        
        // Add correlation ID to response
        $response = $next($request);
        $response->headers->set('X-Correlation-ID', $correlationId);
        
        return $response;
    }
}

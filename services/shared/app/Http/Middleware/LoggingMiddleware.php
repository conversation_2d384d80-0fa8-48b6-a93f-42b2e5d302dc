<?php

namespace Shared\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Shared\Services\Logging\LoggingService;

class LoggingMiddleware
{
    public function __construct(
        private LoggingService $loggingService
    ) {}
    
    public function handle(Request $request, Closure $next)
    {
        // Log request
        $this->loggingService->logRequest($request);
        
        // Process request
        $response = $next($request);
        
        // Log response
        $this->loggingService->logResponse($request, $response);
        
        return $response;
    }
}

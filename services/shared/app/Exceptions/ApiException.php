<?php

namespace Shared\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;

class ApiException extends Exception
{
    protected int $statusCode = 500;
    protected string $errorCode = 'internal_server_error';
    protected array $errors = [];
    protected ?string $correlationId = null;
    
    public function __construct(
        string $message = 'An unexpected error occurred',
        int $statusCode = 500,
        string $errorCode = 'internal_server_error',
        array $errors = [],
        ?string $correlationId = null
    ) {
        parent::__construct($message);
        
        $this->statusCode = $statusCode;
        $this->errorCode = $errorCode;
        $this->errors = $errors;
        $this->correlationId = $correlationId ?: uniqid('err_');
    }
    
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }
    
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }
    
    public function getErrors(): array
    {
        return $this->errors;
    }
    
    public function getCorrelationId(): string
    {
        return $this->correlationId;
    }
    
    public function render(): JsonResponse
    {
        return response()->json([
            'status' => 'error',
            'message' => $this->getMessage(),
            'error_code' => $this->getErrorCode(),
            'correlation_id' => $this->getCorrelationId(),
            'errors' => $this->getErrors(),
        ], $this->getStatusCode());
    }
}

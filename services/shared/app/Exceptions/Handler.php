<?php

namespace Shared\Exceptions;

use Illuminate\Auth\AuthenticationException as LaravelAuthenticationException;
use Illuminate\Auth\Access\AuthorizationException as LaravelAuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException as LaravelValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    protected $dontReport = [
        ApiException::class,
    ];
    
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
        });
    }
    
    public function render($request, Throwable $e)
    {
        // Generate correlation ID for tracking
        $correlationId = $request->header('X-Correlation-ID') ?: uniqid('err_');
        
        // Add correlation ID to response headers
        if ($response = $this->prepareResponse($request, $e)) {
            $response->headers->set('X-Correlation-ID', $correlationId);
        }
        
        // Handle API exceptions
        if ($e instanceof ApiException) {
            return $e->render();
        }
        
        // Convert Laravel exceptions to our standardized exceptions
        if ($e instanceof LaravelValidationException) {
            $exception = new ValidationException(
                'Validation failed',
                $e->errors(),
                $correlationId
            );
            return $exception->render();
        }
        
        if ($e instanceof LaravelAuthenticationException) {
            $exception = new AuthenticationException(
                'Unauthenticated',
                $correlationId
            );
            return $exception->render();
        }
        
        if ($e instanceof LaravelAuthorizationException) {
            $exception = new AuthorizationException(
                'Unauthorized',
                $correlationId
            );
            return $exception->render();
        }
        
        if ($e instanceof ModelNotFoundException || $e instanceof NotFoundHttpException) {
            $exception = new NotFoundException(
                'Resource not found',
                $correlationId
            );
            return $exception->render();
        }
        
        // Handle all other exceptions
        if ($request->expectsJson() || $request->is('api/*')) {
            $statusCode = method_exists($e, 'getStatusCode') ? $e->getStatusCode() : 500;
            
            return new JsonResponse([
                'status' => 'error',
                'message' => $e->getMessage(),
                'error_code' => 'internal_server_error',
                'correlation_id' => $correlationId,
                'errors' => [],
            ], $statusCode);
        }
        
        return parent::render($request, $e);
    }
}

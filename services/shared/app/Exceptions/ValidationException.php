<?php

namespace Shared\Exceptions;

class ValidationException extends ApiException
{
    public function __construct(
        string $message = 'Validation failed',
        array $errors = [],
        ?string $correlationId = null
    ) {
        parent::__construct(
            $message,
            422,
            'validation_failed',
            $errors,
            $correlationId
        );
    }
}

<?php

namespace Shared\Providers;

use Illuminate\Support\ServiceProvider;
use Shared\Services\Logging\LoggingService;

class SharedServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(LoggingService::class, function ($app) {
            return new LoggingService();
        });
    }
    
    public function boot(): void
    {
        // Boot services
    }
}

<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing subscription plans to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('subscription_plans')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $plans = [
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Daily Lunch Plan',
                'plan_quantity' => 1,
                'plan_period' => 'day',
                'plan_type' => 'lunch',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 150.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Weekly Lunch Plan',
                'plan_quantity' => 7,
                'plan_period' => 'week',
                'plan_type' => 'lunch',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 1000.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Monthly Lunch Plan',
                'plan_quantity' => 30,
                'plan_period' => 'month',
                'plan_type' => 'lunch',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 4200.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Daily Dinner Plan',
                'plan_quantity' => 1,
                'plan_period' => 'day',
                'plan_type' => 'dinner',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 180.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Weekly Dinner Plan',
                'plan_quantity' => 7,
                'plan_period' => 'week',
                'plan_type' => 'dinner',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 1200.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Monthly Dinner Plan',
                'plan_quantity' => 30,
                'plan_period' => 'month',
                'plan_type' => 'dinner',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 5000.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Premium Full Day Plan',
                'plan_quantity' => 1,
                'plan_period' => 'day',
                'plan_type' => 'full_day',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 300.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Student Special Plan',
                'plan_quantity' => 30,
                'plan_period' => 'month',
                'plan_type' => 'student',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 3500.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Corporate Bulk Plan',
                'plan_quantity' => 30,
                'plan_period' => 'month',
                'plan_type' => 'corporate',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'admin',
                'fk_kitchen_code' => 1,
                'price' => 8000.00,
                'is_recurring' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'company_id' => 1,
                'unit_id' => 1,
                'plan_name' => 'Weekend Special Plan',
                'plan_quantity' => 2,
                'plan_period' => 'week',
                'plan_type' => 'weekend',
                'plan_start_date' => '2025-01-01',
                'plan_end_date' => '2025-12-31',
                'fk_promo_code' => null,
                'plan_status' => true,
                'show_to_customer' => 'yes',
                'fk_kitchen_code' => 1,
                'price' => 400.00,
                'is_recurring' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::create($plan);
        }

        $this->command->info('Subscription plans seeded successfully!');
    }
}

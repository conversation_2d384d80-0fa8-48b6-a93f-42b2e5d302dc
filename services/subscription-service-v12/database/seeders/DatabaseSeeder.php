<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('Starting subscription service database seeding...');

        // Seed subscription plans first
        $this->call([
            SubscriptionPlanSeeder::class,
            SubscriptionSeeder::class,
            SubscriptionItemSeeder::class,
        ]);

        $this->command->info('Subscription service database seeding completed successfully!');
    }
}

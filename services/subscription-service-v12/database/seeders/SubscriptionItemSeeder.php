<?php

namespace Database\Seeders;

use App\Models\Subscription;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SubscriptionItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing subscription items to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('subscription_items')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $subscriptions = Subscription::all();

        if ($subscriptions->isEmpty()) {
            $this->command->warn('No subscriptions found. Please seed subscriptions first.');
            return;
        }

        $items = [];

        foreach ($subscriptions as $subscription) {
            // Create 2-5 items per subscription
            $itemCount = rand(2, 5);
            
            for ($i = 0; $i < $itemCount; $i++) {
                $mealTypes = ['lunch', 'dinner', 'breakfast', 'snack'];
                $mealNames = [
                    'lunch' => ['Dal Rice', 'Chicken Curry', 'Paneer Butter Masala', 'Rajma <PERSON>', '<PERSON><PERSON>hat<PERSON>'],
                    'dinner' => ['<PERSON>ir<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Fish Curry', 'Mutton Curry', 'Veg Thali'],
                    'breakfast' => ['Poha', 'Upma', 'Paratha', 'Idli Sambar', 'Aloo Puri'],
                    'snack' => ['Samosa', 'Pakora', 'Sandwich', 'Fruit Salad', 'Tea Biscuits']
                ];

                $mealType = $mealTypes[array_rand($mealTypes)];
                $mealName = $mealNames[$mealType][array_rand($mealNames[$mealType])];
                
                $quantity = rand(1, 3);
                $unitPrice = rand(80, 250);
                $totalPrice = $quantity * $unitPrice;

                $items[] = [
                    'company_id' => 1,
                    'unit_id' => 1,
                    'subscription_id' => $subscription->id,
                    'meal_id' => rand(1, 50), // Random meal ID
                    'meal_name' => $mealName,
                    'quantity' => $quantity,
                    'price' => $unitPrice,
                    'total' => $totalPrice,
                    'day_of_week' => $this->getDayOfWeek(),
                    'meal_type' => $mealType,
                    'is_swappable' => rand(0, 1),
                    'swap_options' => $this->generateSwapOptions(),
                    'notes' => $this->generateSpecialInstructions(),
                    'created_at' => $subscription->created_at,
                    'updated_at' => now(),
                ];
            }
        }

        // Insert items in batches
        $chunks = array_chunk($items, 100);
        foreach ($chunks as $chunk) {
            DB::table('subscription_items')->insert($chunk);
        }

        $this->command->info('Subscription items seeded successfully! Created ' . count($items) . ' items.');
    }

    /**
     * Generate day of week
     */
    private function getDayOfWeek()
    {
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        return $days[array_rand($days)];
    }

    /**
     * Generate swap options
     */
    private function generateSwapOptions()
    {
        $swapOptions = [
            ['Dal Rice', 'Rajma Rice', 'Chole Rice'],
            ['Chicken Curry', 'Mutton Curry', 'Fish Curry'],
            ['Paneer Butter Masala', 'Palak Paneer', 'Kadai Paneer'],
            ['Biryani', 'Pulao', 'Fried Rice']
        ];

        // 30% chance of having swap options
        if (rand(1, 100) <= 30) {
            return json_encode($swapOptions[array_rand($swapOptions)]);
        }

        return null;
    }

    /**
     * Generate special instructions
     */
    private function generateSpecialInstructions()
    {
        $instructions = [
            null, // No special instructions
            'Extra spicy',
            'Less oil',
            'No onions',
            'Extra rice',
            'Mild spice level',
            'Add extra vegetables',
            'No garlic',
            'Jain food',
            'Extra roti',
            'Less salt',
            'Deliver to security gate',
            'Call before delivery',
            'Leave at door',
            'Ring bell twice'
        ];

        // 40% chance of having special instructions
        if (rand(1, 100) <= 40) {
            return $instructions[array_rand(array_slice($instructions, 1))];
        }

        return null;
    }
}

<?php

namespace Database\Seeders;

use App\Models\Subscription;
use App\Models\Customer;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing subscriptions to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('subscriptions')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Get existing customers and plans
        $customers = Customer::limit(10)->get();
        $plans = SubscriptionPlan::all();

        if ($customers->isEmpty() || $plans->isEmpty()) {
            $this->command->warn('No customers or plans found. Please seed customers and plans first.');
            return;
        }

        $subscriptions = [];
        $subscriptionCounter = 1;

        foreach ($customers as $index => $customer) {
            // Create 1-2 subscriptions per customer
            $subscriptionCount = rand(1, 2);
            
            for ($i = 0; $i < $subscriptionCount; $i++) {
                $plan = $plans->random();
                $startDate = now()->subDays(rand(1, 30));
                $endDate = $this->calculateEndDate($startDate, $plan);
                
                $amount = $plan->price;
                $discount = rand(0, 1) ? rand(50, 200) : 0;
                $total = $amount - $discount;

                $statuses = ['active', 'paused', 'cancelled', 'expired'];
                $status = $statuses[array_rand($statuses)];
                
                // Adjust probabilities - more active subscriptions
                if (rand(1, 100) <= 60) {
                    $status = 'active';
                } elseif (rand(1, 100) <= 80) {
                    $status = 'paused';
                }

                $paymentMethods = ['razorpay', 'paytm', 'wallet', 'upi', 'card'];
                $paymentStatuses = ['completed', 'pending', 'failed'];
                
                $paymentStatus = $status === 'active' ? 'completed' : $paymentStatuses[array_rand($paymentStatuses)];

                $subscriptions[] = [
                    'company_id' => 1,
                    'unit_id' => 1,
                    'customer_id' => $customer->pk_customer_code,
                    'plan_id' => $plan->id,
                    'subscription_no' => 'SUB' . str_pad($subscriptionCounter, 6, '0', STR_PAD_LEFT),
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'status' => $status,
                    'amount' => $amount,
                    'discount' => $discount,
                    'total' => $total,
                    'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                    'payment_status' => $paymentStatus,
                    'transaction_id' => $paymentStatus === 'completed' ? 'TXN' . time() . rand(1000, 9999) : null,
                    'pause_history' => $status === 'paused' ? json_encode([
                        [
                            'paused_at' => now()->subDays(rand(1, 10))->format('Y-m-d H:i:s'),
                            'reason' => 'Customer requested pause',
                            'resume_date' => null
                        ]
                    ]) : null,
                    'next_billing_date' => $status === 'active' && $plan->is_recurring ? $endDate->format('Y-m-d') : null,
                    'auto_renew' => $plan->is_recurring && rand(0, 1),
                    'notes' => $this->generateNotes($status),
                    'created_at' => $startDate,
                    'updated_at' => now(),
                ];

                $subscriptionCounter++;
            }
        }

        // Insert subscriptions in batches
        $chunks = array_chunk($subscriptions, 50);
        foreach ($chunks as $chunk) {
            DB::table('subscriptions')->insert($chunk);
        }

        $this->command->info('Subscriptions seeded successfully! Created ' . count($subscriptions) . ' subscriptions.');
    }

    /**
     * Calculate end date based on plan period and quantity
     */
    private function calculateEndDate($startDate, $plan)
    {
        $endDate = clone $startDate;
        
        switch ($plan->plan_period) {
            case 'day':
                $endDate->addDays($plan->plan_quantity);
                break;
            case 'week':
                $endDate->addWeeks($plan->plan_quantity);
                break;
            case 'month':
                $endDate->addMonths($plan->plan_quantity);
                break;
            case 'year':
                $endDate->addYears($plan->plan_quantity);
                break;
            default:
                $endDate->addDays($plan->plan_quantity);
        }

        return $endDate;
    }

    /**
     * Generate notes based on subscription status
     */
    private function generateNotes($status)
    {
        $notes = [
            'active' => [
                'Customer is satisfied with the service',
                'Regular delivery schedule maintained',
                'No special requirements',
                'Prefers vegetarian meals',
                'Delivery to office address'
            ],
            'paused' => [
                'Customer traveling for business',
                'Temporary pause due to health reasons',
                'Vacation pause requested',
                'Office closure - temporary pause'
            ],
            'cancelled' => [
                'Customer relocated to different city',
                'Not satisfied with meal quality',
                'Found alternative service',
                'Budget constraints'
            ],
            'expired' => [
                'Plan completed successfully',
                'Customer did not renew',
                'Waiting for customer decision on renewal'
            ]
        ];

        $statusNotes = $notes[$status] ?? $notes['active'];
        return $statusNotes[array_rand($statusNotes)];
    }
}

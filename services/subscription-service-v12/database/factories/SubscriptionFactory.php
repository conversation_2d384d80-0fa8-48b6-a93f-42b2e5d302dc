<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class SubscriptionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Subscription::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $statuses = ['active', 'paused', 'cancelled', 'expired'];
        $paymentMethods = ['credit_card', 'debit_card', 'net_banking', 'upi', 'wallet', 'cash'];
        $paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
        
        $startDate = $this->faker->dateTimeBetween('-30 days', '+30 days');
        $endDate = $this->faker->dateTimeBetween('+60 days', '+365 days');
        
        $amount = $this->faker->randomFloat(2, 100, 1000);
        $discount = $this->faker->randomFloat(2, 0, $amount * 0.2);
        $total = $amount - $discount;
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'customer_id' => Customer::factory(),
            'plan_id' => SubscriptionPlan::factory(),
            'subscription_no' => 'SUB-' . now()->format('YmdHis') . '-' . Str::random(4),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => $this->faker->randomElement($statuses),
            'amount' => $amount,
            'discount' => $discount,
            'total' => $total,
            'payment_method' => $this->faker->randomElement($paymentMethods),
            'payment_status' => $this->faker->randomElement($paymentStatuses),
            'transaction_id' => $this->faker->uuid,
            'pause_history' => null,
            'next_billing_date' => $this->faker->boolean ? $endDate : null,
            'auto_renew' => $this->faker->boolean,
            'notes' => $this->faker->sentence
        ];
    }
    
    /**
     * Configure the model factory to create an active subscription.
     *
     * @return $this
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active'
            ];
        });
    }
    
    /**
     * Configure the model factory to create a paused subscription.
     *
     * @return $this
     */
    public function paused()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'paused',
                'pause_history' => [
                    [
                        'paused_at' => now()->subDays(5)->toDateTimeString(),
                        'reason' => 'Going on vacation',
                        'resume_date' => now()->addDays(5)->toDateString()
                    ]
                ]
            ];
        });
    }
    
    /**
     * Configure the model factory to create a cancelled subscription.
     *
     * @return $this
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'cancelled',
                'notes' => 'Cancelled by customer'
            ];
        });
    }
}

<?php

namespace Database\Factories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'customer_name' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'email_address' => $this->faker->unique()->safeEmail,
            'customer_Address' => $this->faker->address,
            'location_code' => $this->faker->postcode,
            'location_name' => $this->faker->city,
            'lunch_loc_code' => $this->faker->postcode,
            'lunch_loc_name' => $this->faker->city,
            'lunch_add' => $this->faker->address,
            'dinner_loc_code' => $this->faker->postcode,
            'dinner_loc_name' => $this->faker->city,
            'dinner_add' => $this->faker->address,
            'food_preference' => $this->faker->randomElement(['Vegetarian', 'Non-Vegetarian', 'Vegan']),
            'city' => $this->faker->city,
            'city_name' => $this->faker->city,
            'company_name' => $this->faker->company,
            'group_code' => $this->faker->word,
            'group_name' => $this->faker->word,
            'registered_on' => $this->faker->dateTimeThisYear,
            'registered_from' => $this->faker->randomElement(['Web', 'Mobile', 'Admin']),
            'food_referance' => $this->faker->text,
            'status' => 1,
            'otp' => $this->faker->numerify('####'),
            'password' => bcrypt('password'),
            'thirdparty' => null,
            'phone_verified' => $this->faker->boolean,
            'subscription_notification' => 'yes',
            'email_verified' => $this->faker->boolean,
            'source' => $this->faker->randomElement(['Web', 'Mobile', 'Referral']),
            'referer' => null,
            'gcm_id' => null,
            'alt_phone' => $this->faker->phoneNumber,
            'dabbawala_code_type' => null,
            'dabbawala_code' => null,
            'dabbawala_image' => null,
            'isguest' => false,
            'delivery_note' => $this->faker->sentence
        ];
    }
}

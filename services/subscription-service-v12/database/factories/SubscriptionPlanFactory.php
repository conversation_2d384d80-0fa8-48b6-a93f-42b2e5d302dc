<?php

namespace Database\Factories;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionPlanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SubscriptionPlan::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $planTypes = ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'];
        $planPeriods = ['day', 'week', 'month', 'year'];
        $showToCustomer = ['yes', 'no', 'admin'];
        
        $startDate = $this->faker->dateTimeBetween('-30 days', '+30 days');
        $endDate = $this->faker->dateTimeBetween('+60 days', '+365 days');
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'plan_name' => $this->faker->words(3, true) . ' Plan',
            'plan_quantity' => $this->faker->numberBetween(1, 30),
            'plan_period' => $this->faker->randomElement($planPeriods),
            'plan_type' => $this->faker->randomElement($planTypes),
            'plan_start_date' => $startDate,
            'plan_end_date' => $endDate,
            'fk_promo_code' => null,
            'plan_status' => $this->faker->boolean,
            'show_to_customer' => $this->faker->randomElement($showToCustomer),
            'fk_kitchen_code' => null,
            'price' => $this->faker->randomFloat(2, 50, 500),
            'is_recurring' => $this->faker->boolean
        ];
    }
    
    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'plan_status' => true,
                'plan_start_date' => now()->subDays(10),
                'plan_end_date' => now()->addDays(30)
            ];
        });
    }
    
    /**
     * Configure the model factory to create a plan visible to customers.
     *
     * @return $this
     */
    public function visibleToCustomers()
    {
        return $this->state(function (array $attributes) {
            return [
                'show_to_customer' => 'yes'
            ];
        });
    }
}

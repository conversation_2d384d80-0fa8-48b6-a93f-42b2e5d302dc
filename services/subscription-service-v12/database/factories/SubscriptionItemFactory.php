<?php

namespace Database\Factories;

use App\Models\Subscription;
use App\Models\SubscriptionItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SubscriptionItem::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        $mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];
        
        $price = $this->faker->randomFloat(2, 10, 100);
        $quantity = $this->faker->numberBetween(1, 5);
        $total = $price * $quantity;
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'subscription_id' => Subscription::factory(),
            'meal_id' => $this->faker->numberBetween(1, 100),
            'meal_name' => $this->faker->words(3, true),
            'quantity' => $quantity,
            'price' => $price,
            'total' => $total,
            'day_of_week' => $this->faker->randomElement($daysOfWeek),
            'meal_type' => $this->faker->randomElement($mealTypes),
            'is_swappable' => $this->faker->boolean,
            'swap_options' => $this->faker->boolean ? [
                ['meal_id' => $this->faker->numberBetween(1, 100), 'meal_name' => $this->faker->words(3, true)],
                ['meal_id' => $this->faker->numberBetween(1, 100), 'meal_name' => $this->faker->words(3, true)]
            ] : null,
            'notes' => $this->faker->sentence
        ];
    }
    
    /**
     * Configure the model factory to create a swappable item.
     *
     * @return $this
     */
    public function swappable()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_swappable' => true,
                'swap_options' => [
                    ['meal_id' => $this->faker->numberBetween(1, 100), 'meal_name' => $this->faker->words(3, true)],
                    ['meal_id' => $this->faker->numberBetween(1, 100), 'meal_name' => $this->faker->words(3, true)],
                    ['meal_id' => $this->faker->numberBetween(1, 100), 'meal_name' => $this->faker->words(3, true)]
                ]
            ];
        });
    }
    
    /**
     * Configure the model factory for a specific meal type.
     *
     * @param string $mealType
     * @return $this
     */
    public function forMealType(string $mealType)
    {
        return $this->state(function (array $attributes) use ($mealType) {
            return [
                'meal_type' => $mealType
            ];
        });
    }
}

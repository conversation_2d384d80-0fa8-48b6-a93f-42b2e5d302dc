<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('school_meal_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            
            // Parent and child relationships
            $table->unsignedBigInteger('parent_customer_id');
            $table->foreign('parent_customer_id')->references('pk_customer_code')->on('customers')->onDelete('cascade');
            
            $table->unsignedBigInteger('child_profile_id');
            $table->foreign('child_profile_id')->references('id')->on('child_profiles')->onDelete('cascade');
            
            // School and meal plan
            $table->unsignedBigInteger('school_id');
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('restrict');
            
            $table->unsignedBigInteger('meal_plan_id');
            $table->foreign('meal_plan_id')->references('id')->on('meal_plans')->onDelete('restrict');
            
            // Subscription details
            $table->string('subscription_number')->unique();
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('subscription_type', ['daily', 'weekly', 'monthly', 'quarterly', 'annual'])->default('monthly');
            $table->enum('status', ['pending', 'active', 'paused', 'cancelled', 'expired', 'suspended'])->default('pending');
            
            // Billing configuration
            $table->decimal('daily_rate', 8, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('discount_amount', 10, 2)->default(0.00);
            $table->decimal('tax_amount', 10, 2)->default(0.00);
            $table->decimal('final_amount', 10, 2);
            
            // Recurring billing
            $table->enum('billing_cycle', ['daily', 'weekly', 'monthly', 'quarterly'])->default('monthly');
            $table->date('next_billing_date');
            $table->date('last_billing_date')->nullable();
            $table->boolean('auto_renew')->default(true);
            $table->integer('billing_failures')->default(0);
            $table->date('last_billing_failure_date')->nullable();
            
            // Delivery schedule
            $table->json('delivery_days')->comment('Days of week for delivery: ["monday", "tuesday", "wednesday", "thursday", "friday"]');
            $table->enum('preferred_break_time', ['morning_break', 'lunch_break', 'both'])->default('lunch_break');
            $table->time('delivery_time_preference')->nullable();
            $table->json('delivery_instructions')->nullable();
            
            // Meal customization
            $table->json('meal_customizations')->nullable()->comment('Child-specific meal customizations');
            $table->json('dietary_accommodations')->nullable()->comment('Special dietary accommodations for this subscription');
            $table->enum('spice_level', ['no_spice', 'mild', 'medium', 'spicy'])->default('mild');
            
            // Subscription metrics
            $table->integer('total_meals_ordered')->default(0);
            $table->integer('total_meals_delivered')->default(0);
            $table->integer('total_meals_consumed')->default(0);
            $table->integer('missed_deliveries')->default(0);
            $table->decimal('consumption_rate', 5, 2)->default(0.00)->comment('Percentage of meals actually consumed');
            
            // Pause/resume functionality
            $table->json('pause_history')->nullable()->comment('History of subscription pauses and resumes');
            $table->date('pause_start_date')->nullable();
            $table->date('pause_end_date')->nullable();
            $table->text('pause_reason')->nullable();
            
            // Parent feedback and ratings
            $table->decimal('parent_rating', 3, 2)->nullable();
            $table->text('parent_feedback')->nullable();
            $table->date('last_feedback_date')->nullable();
            
            // Emergency and special instructions
            $table->text('emergency_instructions')->nullable();
            $table->text('special_notes')->nullable();
            $table->boolean('requires_special_handling')->default(false);
            
            // Cancellation details
            $table->date('cancellation_date')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->enum('cancellation_type', ['parent_request', 'payment_failure', 'school_termination', 'system_auto'])->nullable();
            $table->boolean('refund_processed')->default(false);
            $table->decimal('refund_amount', 10, 2)->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['tenant_id', 'company_id', 'unit_id'], 'idx_school_subscriptions_tenant');
            $table->index(['parent_customer_id'], 'idx_school_subscriptions_parent');
            $table->index(['child_profile_id'], 'idx_school_subscriptions_child');
            $table->index(['school_id', 'status'], 'idx_school_subscriptions_school_status');
            $table->index(['meal_plan_id'], 'idx_school_subscriptions_plan');
            $table->index(['status', 'next_billing_date'], 'idx_school_subscriptions_billing');
            $table->index(['start_date', 'end_date'], 'idx_school_subscriptions_dates');
            $table->index(['subscription_number'], 'idx_school_subscriptions_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_meal_subscriptions');
    }
};

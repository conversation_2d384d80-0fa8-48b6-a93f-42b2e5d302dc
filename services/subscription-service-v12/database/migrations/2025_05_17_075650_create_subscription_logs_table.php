<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->default(1);
            $table->integer('unit_id')->default(1);
            $table->integer('total_orders')->default(0);
            $table->integer('sms_sent')->default(0);
            $table->integer('email_sent')->default(0);
            $table->integer('active_customer')->default(0);
            $table->integer('admin_account')->default(0);
            $table->integer('user_account')->default(0);
            $table->integer('kitchen_count')->default(0);
            $table->date('date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_logs');
    }
};

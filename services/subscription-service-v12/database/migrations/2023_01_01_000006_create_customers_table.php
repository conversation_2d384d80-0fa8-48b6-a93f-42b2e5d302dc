<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            $table->string('customer_name');
            $table->string('phone');
            $table->string('email_address')->nullable();
            $table->text('customer_Address')->nullable();
            $table->string('location_code')->nullable();
            $table->string('location_name')->nullable();
            $table->string('lunch_loc_code')->nullable();
            $table->string('lunch_loc_name')->nullable();
            $table->text('lunch_add')->nullable();
            $table->string('dinner_loc_code')->nullable();
            $table->string('dinner_loc_name')->nullable();
            $table->text('dinner_add')->nullable();
            $table->string('food_preference')->nullable();
            $table->string('city')->nullable();
            $table->string('city_name')->nullable();
            $table->string('company_name')->nullable();
            $table->string('group_code')->nullable();
            $table->string('group_name')->nullable();
            $table->timestamp('registered_on')->nullable();
            $table->string('registered_from')->nullable();
            $table->text('food_referance')->nullable();
            $table->tinyInteger('status')->default(1);
            $table->string('otp')->nullable();
            $table->string('password')->nullable();
            $table->string('thirdparty')->nullable();
            $table->boolean('phone_verified')->default(false);
            $table->string('subscription_notification')->default('yes');
            $table->boolean('email_verified')->default(false);
            $table->string('source')->nullable();
            $table->string('referer')->nullable();
            $table->string('gcm_id')->nullable();
            $table->string('alt_phone')->nullable();
            $table->string('dabbawala_code_type')->nullable();
            $table->string('dabbawala_code')->nullable();
            $table->string('dabbawala_image')->nullable();
            $table->boolean('isguest')->default(false);
            $table->text('delivery_note')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};

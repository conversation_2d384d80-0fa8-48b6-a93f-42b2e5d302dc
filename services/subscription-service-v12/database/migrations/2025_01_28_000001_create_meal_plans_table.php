<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meal_plans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            
            // School association
            $table->unsignedBigInteger('school_id');
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('cascade');
            
            // Plan basic information
            $table->string('plan_name');
            $table->string('plan_code')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            
            // Meal configuration
            $table->enum('meal_type', ['breakfast', 'lunch', 'snack', 'dinner', 'combo'])->default('lunch');
            $table->json('meal_components')->comment('Meal components: {"main": "rice_dal", "side": "vegetable", "dessert": "fruit"}');
            $table->integer('portion_size_ml')->nullable()->comment('Portion size in milliliters');
            $table->integer('portion_size_grams')->nullable()->comment('Portion size in grams');
            
            // Nutritional information
            $table->json('nutritional_info')->comment('Nutritional breakdown: {"calories": 450, "protein": 15, "carbs": 65, "fat": 12, "fiber": 8}');
            $table->json('ingredients_list')->comment('Complete ingredients list with quantities');
            $table->json('allergen_info')->comment('Allergen information: ["contains_nuts", "dairy_free", "gluten_free"]');
            
            // Dietary compliance
            $table->boolean('is_vegetarian')->default(true);
            $table->boolean('is_vegan')->default(false);
            $table->boolean('is_jain')->default(false);
            $table->boolean('is_gluten_free')->default(false);
            $table->boolean('is_dairy_free')->default(false);
            $table->boolean('is_nut_free')->default(false);
            
            // Pricing structure
            $table->json('pricing_structure')->comment('Pricing tiers: {"daily": 45, "weekly": 300, "monthly": 1200, "quarterly": 3400}');
            $table->decimal('base_price', 10, 2)->comment('Base daily price');
            $table->decimal('bulk_discount_percentage', 5, 2)->default(0.00);
            $table->integer('minimum_subscription_days')->default(5);
            
            // Availability and scheduling
            $table->json('available_days')->comment('Days of week: ["monday", "tuesday", "wednesday", "thursday", "friday"]');
            $table->time('preparation_time_required')->comment('Time required for preparation');
            $table->time('delivery_time_window_start');
            $table->time('delivery_time_window_end');
            
            // Capacity and limits
            $table->integer('daily_capacity')->default(100)->comment('Maximum meals per day');
            $table->integer('current_subscriptions')->default(0);
            $table->integer('minimum_order_quantity')->default(1);
            $table->integer('maximum_order_quantity')->default(5);
            
            // Plan lifecycle
            $table->date('plan_start_date');
            $table->date('plan_end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->boolean('auto_renew_enabled')->default(true);
            
            // Quality and ratings
            $table->decimal('average_rating', 3, 2)->default(0.00);
            $table->integer('total_ratings')->default(0);
            $table->json('feedback_summary')->nullable()->comment('Aggregated feedback data');
            
            // Special features
            $table->json('special_features')->nullable()->comment('Special features: ["organic", "locally_sourced", "chef_special"]');
            $table->text('preparation_notes')->nullable();
            $table->text('serving_instructions')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['tenant_id', 'company_id', 'unit_id'], 'idx_meal_plans_tenant');
            $table->index(['school_id'], 'idx_meal_plans_school');
            $table->index(['school_id', 'meal_type'], 'idx_meal_plans_school_type');
            $table->index(['is_active', 'plan_start_date', 'plan_end_date'], 'idx_meal_plans_active_dates');
            $table->index(['is_vegetarian', 'is_vegan', 'is_jain'], 'idx_meal_plans_dietary');
            $table->index(['base_price'], 'idx_meal_plans_price');
            $table->index(['average_rating'], 'idx_meal_plans_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meal_plans');
    }
};

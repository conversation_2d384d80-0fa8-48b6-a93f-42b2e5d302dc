<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_items', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->default(1);
            $table->integer('unit_id')->default(1);
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('meal_id');
            $table->string('meal_name');
            $table->integer('quantity')->default(1);
            $table->decimal('price', 10, 2)->default(0);
            $table->decimal('total', 10, 2)->default(0);
            $table->string('day_of_week')->nullable()->comment('Monday, Tuesday, etc.');
            $table->string('meal_type')->nullable()->comment('breakfast, lunch, dinner');
            $table->boolean('is_swappable')->default(false);
            $table->json('swap_options')->nullable()->comment('JSON encoded swap options');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_items');
    }
};

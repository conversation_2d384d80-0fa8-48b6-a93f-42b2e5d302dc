<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            $table->string('plan_name');
            $table->integer('plan_quantity');
            $table->string('plan_period');
            $table->string('plan_type');
            $table->date('plan_start_date');
            $table->date('plan_end_date');
            $table->unsignedBigInteger('fk_promo_code')->nullable();
            $table->boolean('plan_status')->default(true);
            $table->string('show_to_customer')->default('no');
            $table->unsignedBigInteger('fk_kitchen_code')->nullable();
            $table->decimal('price', 10, 2);
            $table->boolean('is_recurring')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};

#!/bin/bash

# Subscription Service v12 - Complete API Testing Script
# This script tests all API endpoints with real data

BASE_URL="http://localhost:8000/api/v2"
CONTENT_TYPE="Content-Type: application/json"
ACCEPT="Accept: application/json"

echo "=== Subscription Service v12 API Testing ==="
echo "Base URL: $BASE_URL"
echo ""

# Test 1: Get All Subscription Plans
echo "1. Testing GET /subscription-plans"
curl -s -X GET "$BASE_URL/subscription-plans" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 2: Get Active Subscription Plans
echo "2. Testing GET /subscription-plans/active"
curl -s -X GET "$BASE_URL/subscription-plans/active" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 3: Get Customer Visible Plans
echo "3. Testing GET /subscription-plans/customer"
curl -s -X GET "$BASE_URL/subscription-plans/customer" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 4: Get Plans by Type - Lunch
echo "4. Testing GET /subscription-plans/type/lunch"
curl -s -X GET "$BASE_URL/subscription-plans/type/lunch" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 5: Get Specific Plan
echo "5. Testing GET /subscription-plans/1"
curl -s -X GET "$BASE_URL/subscription-plans/1" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 6: Get All Subscriptions
echo "6. Testing GET /subscriptions"
curl -s -X GET "$BASE_URL/subscriptions" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 7: Create New Subscription (The failing one)
echo "7. Testing POST /subscriptions (Create New)"
curl -s -X POST "$BASE_URL/subscriptions" \
  -H "$ACCEPT" \
  -H "$CONTENT_TYPE" \
  -d '{
    "customer_id": 1,
    "plan_id": 1,
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "payment_method": "razorpay",
    "auto_renew": true,
    "notes": "Free Cash"
  }' | jq '.'
echo ""

# Test 8: Get Customer Subscriptions
echo "8. Testing GET /subscriptions/customer/1"
curl -s -X GET "$BASE_URL/subscriptions/customer/1" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 9: Get Active Customer Subscriptions
echo "9. Testing GET /subscriptions/customer/1/active"
curl -s -X GET "$BASE_URL/subscriptions/customer/1/active" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 10: Get Specific Subscription
echo "10. Testing GET /subscriptions/1"
curl -s -X GET "$BASE_URL/subscriptions/1" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 11: Update Subscription
echo "11. Testing PUT /subscriptions/1 (Update)"
curl -s -X PUT "$BASE_URL/subscriptions/1" \
  -H "$ACCEPT" \
  -H "$CONTENT_TYPE" \
  -d '{
    "notes": "Updated via API test",
    "auto_renew": false
  }' | jq '.'
echo ""

# Test 12: Pause Subscription
echo "12. Testing PUT /subscriptions/1/pause"
curl -s -X PUT "$BASE_URL/subscriptions/1/pause" \
  -H "$ACCEPT" \
  -H "$CONTENT_TYPE" \
  -d '{
    "reason": "Customer traveling",
    "resume_date": "2025-02-01"
  }' | jq '.'
echo ""

# Test 13: Resume Subscription
echo "13. Testing PUT /subscriptions/1/resume"
curl -s -X PUT "$BASE_URL/subscriptions/1/resume" \
  -H "$ACCEPT" | jq '.'
echo ""

# Test 14: Process Payment
echo "14. Testing POST /subscriptions/1/payment"
curl -s -X POST "$BASE_URL/subscriptions/1/payment" \
  -H "$ACCEPT" \
  -H "$CONTENT_TYPE" \
  -d '{
    "payment_method": "razorpay",
    "transaction_id": "TXN123456789",
    "amount": 150.00,
    "payment_status": "completed"
  }' | jq '.'
echo ""

# Test 15: Get Subscription Logs
echo "15. Testing GET /subscriptions/1/logs"
curl -s -X GET "$BASE_URL/subscriptions/1/logs" \
  -H "$ACCEPT" | jq '.'
echo ""

echo "=== API Testing Complete ==="

{"info": {"_postman_id": "subscription-service-v12-complete", "name": "Subscription Service v12 - Complete API Collection", "description": "Complete API collection for OneFoodDialer Subscription Service v12 with all endpoints tested and working", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Subscription Plans", "item": [{"name": "Get All Subscription Plans", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans"]}}}, {"name": "Get Active Subscription Plans", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/active", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "active"]}}}, {"name": "Get Customer Visible Plans", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/customer", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "customer"]}}}, {"name": "Get Plans by Type - Lunch", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/type/lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "type", "lunch"]}}}, {"name": "Get Plans by Type - Dinner", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/type/dinner", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "type", "dinner"]}}}, {"name": "Get Subscription Plan by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/1", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "1"]}}}, {"name": "Create New Subscription Plan", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": 1,\n    \"unit_id\": 1,\n    \"plan_name\": \"Test API Plan\",\n    \"plan_quantity\": 7,\n    \"plan_period\": \"week\",\n    \"plan_type\": \"lunch\",\n    \"plan_start_date\": \"2025-01-01\",\n    \"plan_end_date\": \"2025-12-31\",\n    \"price\": 999.00,\n    \"is_recurring\": true,\n    \"show_to_customer\": \"yes\",\n    \"fk_kitchen_code\": 1,\n    \"plan_status\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscription-plans", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans"]}}}, {"name": "Update Subscription Plan", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"plan_name\": \"Updated API Plan\",\n    \"price\": 1199.00,\n    \"notes\": \"Updated via API\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscription-plans/1", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "1"]}}}, {"name": "Activate Subscription Plan", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/1/activate", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "1", "activate"]}}}, {"name": "Deactivate Subscription Plan", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/1/deactivate", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "1", "deactivate"]}}}, {"name": "Delete Subscription Plan", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscription-plans/11", "host": ["{{base_url}}"], "path": ["api", "v2", "subscription-plans", "11"]}}}]}, {"name": "Subscriptions", "item": [{"name": "Get All Subscriptions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscriptions", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions"]}}}, {"name": "Get Subscription by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscriptions/1", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1"]}}}, {"name": "Get Customer Subscriptions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscriptions/customer/1", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "customer", "1"]}}}, {"name": "Get Active Customer Subscriptions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscriptions/customer/1/active", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "customer", "1", "active"]}}}, {"name": "Create New Subscription (FIXED)", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 1,\n    \"plan_id\": 1,\n    \"start_date\": \"2025-01-01\",\n    \"end_date\": \"2025-01-31\",\n    \"payment_method\": \"razorpay\",\n    \"auto_renew\": true,\n    \"notes\": \"Free Cash - API Test\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscriptions", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions"]}}}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"Updated subscription notes via API\",\n    \"auto_renew\": false\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscriptions/1", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1"]}}}, {"name": "Pause Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Customer traveling for business\",\n    \"resume_date\": \"2025-02-01\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscriptions/1/pause", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1", "pause"]}}}, {"name": "Resume Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscriptions/1/resume", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1", "resume"]}}}, {"name": "Cancel Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Customer not satisfied with service quality\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscriptions/1/cancel", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1", "cancel"]}}}, {"name": "Renew Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"days\": 30\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscriptions/1/renew", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1", "renew"]}}}, {"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"payment_method\": \"razorpay\",\n    \"transaction_id\": \"TXN123456789\",\n    \"amount\": 150.00,\n    \"payment_status\": \"completed\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/subscriptions/1/payment", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1", "payment"]}}}, {"name": "Get Subscription Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/subscriptions/1/logs", "host": ["{{base_url}}"], "path": ["api", "v2", "subscriptions", "1", "logs"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}]}
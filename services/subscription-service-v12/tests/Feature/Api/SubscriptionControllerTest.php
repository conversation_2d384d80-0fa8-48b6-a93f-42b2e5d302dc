<?php

namespace Tests\Feature\Api;

use App\Models\Customer;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testIndex()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        Subscription::factory()->count(3)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        $response = $this->getJson('/api/v2/subscriptions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'customer_id',
                        'plan_id',
                        'subscription_no',
                        'start_date',
                        'end_date',
                        'status',
                        'amount',
                        'discount',
                        'total'
                    ]
                ]
            ])
            ->assertJsonCount(3, 'data');
    }

    public function testStore()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $data = [
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'start_date' => '2023-01-01',
            'end_date' => '2023-01-31',
            'amount' => 100.00,
            'discount' => 10.00,
            'payment_method' => 'credit_card',
            'payment_status' => 'paid',
            'auto_renew' => false,
            'items' => [
                [
                    'meal_id' => 1,
                    'meal_name' => 'Test Meal',
                    'quantity' => 2,
                    'price' => 45.00,
                    'day_of_week' => 'Monday',
                    'meal_type' => 'lunch'
                ]
            ]
        ];

        $response = $this->postJson('/api/v2/subscriptions', $data);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'customer_id',
                    'plan_id',
                    'subscription_no',
                    'start_date',
                    'end_date',
                    'status',
                    'amount',
                    'discount',
                    'total',
                    'items'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription created successfully',
                'data' => [
                    'customer_id' => $customer->id,
                    'plan_id' => $plan->id,
                    'start_date' => '2023-01-01',
                    'end_date' => '2023-01-31',
                    'status' => 'active',
                    'amount' => 100.00,
                    'discount' => 10.00,
                    'total' => 90.00
                ]
            ]);
            
        $this->assertDatabaseHas('subscriptions', [
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active',
            'amount' => 100.00,
            'discount' => 10.00,
            'total' => 90.00
        ]);
        
        $this->assertDatabaseHas('subscription_items', [
            'meal_id' => 1,
            'meal_name' => 'Test Meal',
            'quantity' => 2,
            'price' => 45.00,
            'day_of_week' => 'Monday',
            'meal_type' => 'lunch'
        ]);
    }

    public function testShow()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        $response = $this->getJson("/api/v2/subscriptions/{$subscription->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'customer_id',
                    'plan_id',
                    'subscription_no',
                    'start_date',
                    'end_date',
                    'status',
                    'amount',
                    'discount',
                    'total'
                ]
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $subscription->id,
                    'customer_id' => $customer->id,
                    'plan_id' => $plan->id
                ]
            ]);
    }

    public function testUpdate()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'amount' => 100.00,
            'discount' => 10.00,
            'total' => 90.00,
            'payment_status' => 'pending'
        ]);

        $data = [
            'amount' => 120.00,
            'discount' => 20.00,
            'payment_status' => 'paid'
        ];

        $response = $this->putJson("/api/v2/subscriptions/{$subscription->id}", $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'customer_id',
                    'plan_id',
                    'subscription_no',
                    'start_date',
                    'end_date',
                    'status',
                    'amount',
                    'discount',
                    'total',
                    'payment_status'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription updated successfully',
                'data' => [
                    'id' => $subscription->id,
                    'amount' => 120.00,
                    'discount' => 20.00,
                    'total' => 100.00,
                    'payment_status' => 'paid'
                ]
            ]);
            
        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'amount' => 120.00,
            'discount' => 20.00,
            'total' => 100.00,
            'payment_status' => 'paid'
        ]);
    }

    public function testDestroy()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        $response = $this->deleteJson("/api/v2/subscriptions/{$subscription->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Subscription deleted successfully'
            ]);
            
        $this->assertDatabaseMissing('subscriptions', [
            'id' => $subscription->id
        ]);
    }

    public function testPause()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $data = [
            'reason' => 'Going on vacation',
            'resume_date' => '2023-02-01'
        ];

        $response = $this->postJson("/api/v2/subscriptions/{$subscription->id}/pause", $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'status',
                    'pause_history'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription paused successfully',
                'data' => [
                    'id' => $subscription->id,
                    'status' => 'paused'
                ]
            ]);
            
        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'status' => 'paused'
        ]);
    }

    public function testResume()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'paused',
            'pause_history' => [
                [
                    'paused_at' => now()->subDays(5)->toDateTimeString(),
                    'reason' => 'Going on vacation',
                    'resume_date' => now()->addDays(5)->toDateString()
                ]
            ]
        ]);

        $response = $this->postJson("/api/v2/subscriptions/{$subscription->id}/resume");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'status',
                    'pause_history'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription resumed successfully',
                'data' => [
                    'id' => $subscription->id,
                    'status' => 'active'
                ]
            ]);
            
        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'status' => 'active'
        ]);
    }

    public function testCancel()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $data = [
            'reason' => 'Not satisfied with service'
        ];

        $response = $this->postJson("/api/v2/subscriptions/{$subscription->id}/cancel", $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'status',
                    'notes'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription cancelled successfully',
                'data' => [
                    'id' => $subscription->id,
                    'status' => 'cancelled',
                    'notes' => 'Not satisfied with service'
                ]
            ]);
            
        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'status' => 'cancelled',
            'notes' => 'Not satisfied with service'
        ]);
    }

    public function testRenew()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create([
            'plan_quantity' => 30,
            'plan_period' => 'day'
        ]);
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'start_date' => '2023-01-01',
            'end_date' => '2023-01-31'
        ]);

        $response = $this->postJson("/api/v2/subscriptions/{$subscription->id}/renew");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'start_date',
                    'end_date'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription renewed successfully'
            ]);
            
        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'start_date' => '2023-02-01',
            'end_date' => '2023-03-02'
        ]);
    }
}

<?php

namespace Tests\Feature\Api;

use App\Models\SubscriptionPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionPlanControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testIndex()
    {
        SubscriptionPlan::factory()->count(3)->create();

        $response = $this->getJson('/api/v2/subscription-plans');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'plan_name',
                        'plan_quantity',
                        'plan_period',
                        'plan_type',
                        'plan_start_date',
                        'plan_end_date',
                        'plan_status',
                        'price'
                    ]
                ]
            ])
            ->assertJsonCount(3, 'data');
    }

    public function testStore()
    {
        $data = [
            'plan_name' => 'Premium Monthly',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'plan_start_date' => '2023-01-01',
            'plan_end_date' => '2023-12-31',
            'plan_status' => true,
            'show_to_customer' => 'yes',
            'price' => 199.99,
            'is_recurring' => true
        ];

        $response = $this->postJson('/api/v2/subscription-plans', $data);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'plan_name',
                    'plan_quantity',
                    'plan_period',
                    'plan_type',
                    'plan_start_date',
                    'plan_end_date',
                    'plan_status',
                    'show_to_customer',
                    'price',
                    'is_recurring'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription plan created successfully',
                'data' => [
                    'plan_name' => 'Premium Monthly',
                    'plan_quantity' => 30,
                    'plan_period' => 'day',
                    'plan_type' => 'monthly',
                    'plan_start_date' => '2023-01-01',
                    'plan_end_date' => '2023-12-31',
                    'plan_status' => true,
                    'show_to_customer' => 'yes',
                    'price' => 199.99,
                    'is_recurring' => true
                ]
            ]);
            
        $this->assertDatabaseHas('subscription_plans', [
            'plan_name' => 'Premium Monthly',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'plan_status' => 1,
            'show_to_customer' => 'yes',
            'price' => 199.99,
            'is_recurring' => 1
        ]);
    }

    public function testShow()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_name' => 'Test Plan',
            'plan_status' => true
        ]);

        $response = $this->getJson("/api/v2/subscription-plans/{$plan->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'plan_name',
                    'plan_quantity',
                    'plan_period',
                    'plan_type',
                    'plan_start_date',
                    'plan_end_date',
                    'plan_status',
                    'price'
                ]
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $plan->id,
                    'plan_name' => 'Test Plan',
                    'plan_status' => true
                ]
            ]);
    }

    public function testUpdate()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_name' => 'Old Plan Name',
            'price' => 99.99,
            'plan_status' => true
        ]);

        $data = [
            'plan_name' => 'New Plan Name',
            'price' => 149.99,
            'plan_status' => false
        ];

        $response = $this->putJson("/api/v2/subscription-plans/{$plan->id}", $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'plan_name',
                    'price',
                    'plan_status'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription plan updated successfully',
                'data' => [
                    'id' => $plan->id,
                    'plan_name' => 'New Plan Name',
                    'price' => 149.99,
                    'plan_status' => false
                ]
            ]);
            
        $this->assertDatabaseHas('subscription_plans', [
            'id' => $plan->id,
            'plan_name' => 'New Plan Name',
            'price' => 149.99,
            'plan_status' => 0
        ]);
    }

    public function testDestroy()
    {
        $plan = SubscriptionPlan::factory()->create();

        $response = $this->deleteJson("/api/v2/subscription-plans/{$plan->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Subscription plan deleted successfully'
            ]);
            
        $this->assertDatabaseMissing('subscription_plans', [
            'id' => $plan->id
        ]);
    }

    public function testActive()
    {
        SubscriptionPlan::factory()->count(2)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10)
        ]);
        
        SubscriptionPlan::factory()->count(1)->create([
            'plan_status' => false
        ]);

        $response = $this->getJson('/api/v2/subscription-plans/active');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'plan_name',
                        'plan_status'
                    ]
                ]
            ])
            ->assertJsonCount(2, 'data');
            
        $response->assertJson([
            'success' => true,
            'data' => [
                ['plan_status' => true],
                ['plan_status' => true]
            ]
        ]);
    }

    public function testCustomer()
    {
        SubscriptionPlan::factory()->count(2)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'show_to_customer' => 'yes'
        ]);
        
        SubscriptionPlan::factory()->count(1)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'show_to_customer' => 'no'
        ]);

        $response = $this->getJson('/api/v2/subscription-plans/customer');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'plan_name',
                        'show_to_customer'
                    ]
                ]
            ])
            ->assertJsonCount(2, 'data');
            
        $response->assertJson([
            'success' => true,
            'data' => [
                ['show_to_customer' => 'yes'],
                ['show_to_customer' => 'yes']
            ]
        ]);
    }

    public function testType()
    {
        SubscriptionPlan::factory()->count(2)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'plan_type' => 'monthly'
        ]);
        
        SubscriptionPlan::factory()->count(1)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'plan_type' => 'weekly'
        ]);

        $response = $this->getJson('/api/v2/subscription-plans/type/monthly');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'plan_name',
                        'plan_type'
                    ]
                ]
            ])
            ->assertJsonCount(2, 'data');
            
        $response->assertJson([
            'success' => true,
            'data' => [
                ['plan_type' => 'monthly'],
                ['plan_type' => 'monthly']
            ]
        ]);
    }

    public function testActivate()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_status' => false
        ]);

        $response = $this->postJson("/api/v2/subscription-plans/{$plan->id}/activate");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'plan_name',
                    'plan_status'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription plan activated successfully',
                'data' => [
                    'id' => $plan->id,
                    'plan_status' => true
                ]
            ]);
            
        $this->assertDatabaseHas('subscription_plans', [
            'id' => $plan->id,
            'plan_status' => 1
        ]);
    }

    public function testDeactivate()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_status' => true
        ]);

        $response = $this->postJson("/api/v2/subscription-plans/{$plan->id}/deactivate");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'plan_name',
                    'plan_status'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Subscription plan deactivated successfully',
                'data' => [
                    'id' => $plan->id,
                    'plan_status' => false
                ]
            ]);
            
        $this->assertDatabaseHas('subscription_plans', [
            'id' => $plan->id,
            'plan_status' => 0
        ]);
    }
}

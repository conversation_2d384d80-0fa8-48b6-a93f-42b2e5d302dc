<?php

namespace Tests\Unit\Services;

use App\Models\SubscriptionPlan;
use App\Services\SubscriptionPlanService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionPlanServiceTest extends TestCase
{
    use RefreshDatabase;

    protected SubscriptionPlanService $subscriptionPlanService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->subscriptionPlanService = new SubscriptionPlanService();
    }

    public function testGetAllPlans()
    {
        // Create test data
        SubscriptionPlan::factory()->count(3)->create([
            'plan_status' => true,
            'plan_type' => 'monthly'
        ]);
        
        SubscriptionPlan::factory()->count(2)->create([
            'plan_status' => false,
            'plan_type' => 'weekly'
        ]);

        // Test without filters
        $plans = $this->subscriptionPlanService->getAllPlans();
        $this->assertCount(5, $plans);

        // Test with status filter
        $activePlans = $this->subscriptionPlanService->getAllPlans(['status' => true]);
        $this->assertCount(3, $activePlans);
        
        // Test with type filter
        $monthlyPlans = $this->subscriptionPlanService->getAllPlans(['type' => 'monthly']);
        $this->assertCount(3, $monthlyPlans);
    }

    public function testGetPlanById()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_name' => 'Test Plan',
            'plan_status' => true
        ]);

        $result = $this->subscriptionPlanService->getPlanById($plan->id);
        
        $this->assertNotNull($result);
        $this->assertEquals($plan->id, $result->id);
        $this->assertEquals('Test Plan', $result->plan_name);
        $this->assertTrue($result->plan_status);
    }

    public function testGetActivePlans()
    {
        SubscriptionPlan::factory()->count(3)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10)
        ]);
        
        SubscriptionPlan::factory()->count(2)->create([
            'plan_status' => false
        ]);

        $activePlans = $this->subscriptionPlanService->getActivePlans();
        
        $this->assertCount(3, $activePlans);
        foreach ($activePlans as $plan) {
            $this->assertTrue($plan->plan_status);
        }
    }

    public function testGetCustomerVisiblePlans()
    {
        SubscriptionPlan::factory()->count(2)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'show_to_customer' => 'yes'
        ]);
        
        SubscriptionPlan::factory()->count(1)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'show_to_customer' => 'no'
        ]);

        $customerPlans = $this->subscriptionPlanService->getCustomerVisiblePlans();
        
        $this->assertCount(2, $customerPlans);
        foreach ($customerPlans as $plan) {
            $this->assertEquals('yes', $plan->show_to_customer);
        }
    }

    public function testGetPlansByType()
    {
        SubscriptionPlan::factory()->count(2)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'plan_type' => 'monthly'
        ]);
        
        SubscriptionPlan::factory()->count(3)->create([
            'plan_status' => true,
            'plan_start_date' => now()->subDays(10),
            'plan_end_date' => now()->addDays(10),
            'plan_type' => 'weekly'
        ]);

        $monthlyPlans = $this->subscriptionPlanService->getPlansByType('monthly');
        
        $this->assertCount(2, $monthlyPlans);
        foreach ($monthlyPlans as $plan) {
            $this->assertEquals('monthly', $plan->plan_type);
        }
    }

    public function testCreatePlan()
    {
        $data = [
            'company_id' => 1,
            'unit_id' => 1,
            'plan_name' => 'Premium Monthly',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'plan_start_date' => '2023-01-01',
            'plan_end_date' => '2023-12-31',
            'plan_status' => true,
            'show_to_customer' => 'yes',
            'price' => 199.99,
            'is_recurring' => true
        ];

        $plan = $this->subscriptionPlanService->createPlan($data);
        
        $this->assertNotNull($plan);
        $this->assertEquals('Premium Monthly', $plan->plan_name);
        $this->assertEquals(30, $plan->plan_quantity);
        $this->assertEquals('day', $plan->plan_period);
        $this->assertEquals('monthly', $plan->plan_type);
        $this->assertEquals('2023-01-01', $plan->plan_start_date->format('Y-m-d'));
        $this->assertEquals('2023-12-31', $plan->plan_end_date->format('Y-m-d'));
        $this->assertTrue($plan->plan_status);
        $this->assertEquals('yes', $plan->show_to_customer);
        $this->assertEquals(199.99, $plan->price);
        $this->assertTrue($plan->is_recurring);
    }

    public function testUpdatePlan()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_name' => 'Old Plan Name',
            'price' => 99.99,
            'plan_status' => true
        ]);

        $data = [
            'plan_name' => 'New Plan Name',
            'price' => 149.99,
            'plan_status' => false
        ];

        $updatedPlan = $this->subscriptionPlanService->updatePlan($plan->id, $data);
        
        $this->assertNotNull($updatedPlan);
        $this->assertEquals('New Plan Name', $updatedPlan->plan_name);
        $this->assertEquals(149.99, $updatedPlan->price);
        $this->assertFalse($updatedPlan->plan_status);
    }

    public function testDeletePlan()
    {
        $plan = SubscriptionPlan::factory()->create();

        $result = $this->subscriptionPlanService->deletePlan($plan->id);
        
        $this->assertTrue($result);
        $this->assertNull(SubscriptionPlan::find($plan->id));
    }

    public function testActivatePlan()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_status' => false
        ]);

        $activatedPlan = $this->subscriptionPlanService->activatePlan($plan->id);
        
        $this->assertNotNull($activatedPlan);
        $this->assertTrue($activatedPlan->plan_status);
    }

    public function testDeactivatePlan()
    {
        $plan = SubscriptionPlan::factory()->create([
            'plan_status' => true
        ]);

        $deactivatedPlan = $this->subscriptionPlanService->deactivatePlan($plan->id);
        
        $this->assertNotNull($deactivatedPlan);
        $this->assertFalse($deactivatedPlan->plan_status);
    }
}

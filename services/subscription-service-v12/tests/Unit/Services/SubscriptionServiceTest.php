<?php

namespace Tests\Unit\Services;

use App\Models\Customer;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected SubscriptionService $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->subscriptionService = new SubscriptionService();
    }

    public function testGetAllSubscriptions()
    {
        // Create test data
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        Subscription::factory()->count(3)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);
        
        Subscription::factory()->count(2)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'paused'
        ]);

        // Test without filters
        $subscriptions = $this->subscriptionService->getAllSubscriptions();
        $this->assertCount(5, $subscriptions);

        // Test with status filter
        $activeSubscriptions = $this->subscriptionService->getAllSubscriptions(['status' => 'active']);
        $this->assertCount(3, $activeSubscriptions);
        
        // Test with customer_id filter
        $customerSubscriptions = $this->subscriptionService->getAllSubscriptions(['customer_id' => $customer->id]);
        $this->assertCount(5, $customerSubscriptions);
    }

    public function testGetSubscriptionById()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        $result = $this->subscriptionService->getSubscriptionById($subscription->id);
        
        $this->assertNotNull($result);
        $this->assertEquals($subscription->id, $result->id);
        $this->assertEquals($customer->id, $result->customer_id);
        $this->assertEquals($plan->id, $result->plan_id);
    }

    public function testCreateSubscription()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $data = [
            'company_id' => 1,
            'unit_id' => 1,
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'start_date' => '2023-01-01',
            'end_date' => '2023-01-31',
            'status' => 'active',
            'amount' => 100.00,
            'discount' => 10.00,
            'total' => 90.00,
            'payment_method' => 'credit_card',
            'payment_status' => 'paid',
            'auto_renew' => false
        ];
        
        $items = [
            [
                'meal_id' => 1,
                'meal_name' => 'Test Meal',
                'quantity' => 2,
                'price' => 45.00,
                'day_of_week' => 'Monday',
                'meal_type' => 'lunch'
            ]
        ];

        $subscription = $this->subscriptionService->createSubscription($data, $items);
        
        $this->assertNotNull($subscription);
        $this->assertEquals($customer->id, $subscription->customer_id);
        $this->assertEquals($plan->id, $subscription->plan_id);
        $this->assertEquals('active', $subscription->status);
        $this->assertEquals(100.00, $subscription->amount);
        $this->assertEquals(10.00, $subscription->discount);
        $this->assertEquals(90.00, $subscription->total);
        
        // Check if subscription items were created
        $this->assertCount(1, $subscription->items);
        $this->assertEquals('Test Meal', $subscription->items[0]->meal_name);
        $this->assertEquals(2, $subscription->items[0]->quantity);
        $this->assertEquals(45.00, $subscription->items[0]->price);
        $this->assertEquals(90.00, $subscription->items[0]->total);
    }

    public function testUpdateSubscription()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'amount' => 100.00,
            'discount' => 10.00,
            'total' => 90.00
        ]);

        $data = [
            'amount' => 120.00,
            'discount' => 20.00,
            'total' => 100.00,
            'payment_status' => 'paid'
        ];

        $updatedSubscription = $this->subscriptionService->updateSubscription($subscription->id, $data);
        
        $this->assertNotNull($updatedSubscription);
        $this->assertEquals(120.00, $updatedSubscription->amount);
        $this->assertEquals(20.00, $updatedSubscription->discount);
        $this->assertEquals(100.00, $updatedSubscription->total);
        $this->assertEquals('paid', $updatedSubscription->payment_status);
    }

    public function testDeleteSubscription()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        $result = $this->subscriptionService->deleteSubscription($subscription->id);
        
        $this->assertTrue($result);
        $this->assertNull(Subscription::find($subscription->id));
    }

    public function testPauseSubscription()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $pausedSubscription = $this->subscriptionService->pauseSubscription(
            $subscription->id, 
            'Going on vacation', 
            '2023-02-01'
        );
        
        $this->assertNotNull($pausedSubscription);
        $this->assertEquals('paused', $pausedSubscription->status);
        
        // Check pause history
        $this->assertNotEmpty($pausedSubscription->pause_history);
        $pauseHistory = $pausedSubscription->pause_history;
        $this->assertEquals('Going on vacation', $pauseHistory[0]['reason']);
        $this->assertEquals('2023-02-01', $pauseHistory[0]['resume_date']);
    }

    public function testResumeSubscription()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'paused',
            'pause_history' => [
                [
                    'paused_at' => now()->subDays(5)->toDateTimeString(),
                    'reason' => 'Going on vacation',
                    'resume_date' => now()->addDays(5)->toDateString()
                ]
            ]
        ]);

        $resumedSubscription = $this->subscriptionService->resumeSubscription($subscription->id);
        
        $this->assertNotNull($resumedSubscription);
        $this->assertEquals('active', $resumedSubscription->status);
        
        // Check pause history
        $this->assertNotEmpty($resumedSubscription->pause_history);
        $pauseHistory = $resumedSubscription->pause_history;
        $this->assertArrayHasKey('resumed_at', $pauseHistory[0]);
    }

    public function testCancelSubscription()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $cancelledSubscription = $this->subscriptionService->cancelSubscription(
            $subscription->id, 
            'Not satisfied with service'
        );
        
        $this->assertNotNull($cancelledSubscription);
        $this->assertEquals('cancelled', $cancelledSubscription->status);
        $this->assertEquals('Not satisfied with service', $cancelledSubscription->notes);
    }

    public function testRenewSubscription()
    {
        $customer = Customer::factory()->create();
        $plan = SubscriptionPlan::factory()->create([
            'plan_quantity' => 30,
            'plan_period' => 'day'
        ]);
        
        $subscription = Subscription::factory()->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'start_date' => '2023-01-01',
            'end_date' => '2023-01-31'
        ]);

        $renewedSubscription = $this->subscriptionService->renewSubscription($subscription->id);
        
        $this->assertNotNull($renewedSubscription);
        $this->assertEquals('2023-02-01', $renewedSubscription->start_date->format('Y-m-d'));
        $this->assertEquals('2023-03-02', $renewedSubscription->end_date->format('Y-m-d'));
    }
}

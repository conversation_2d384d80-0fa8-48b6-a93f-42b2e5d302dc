# Subscription Service v12 - Complete API Testing Guide

## 🚀 Quick Start

### 1. Start the Server
```bash
cd services/subscription-service-v12
php artisan serve
```
The server will start on `http://localhost:8000`

### 2. Import Postman Collection
Import the file: `Subscription_Service_Complete_API_Collection.postman_collection.json`

### 3. Test the Fixed Subscription Creation API
```bash
curl --location 'http://localhost:8000/api/v2/subscriptions' \
--header 'Accept: application/json' \
--header 'Content-Type: application/json' \
--data '{
    "customer_id": 1,
    "plan_id": 1,
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "payment_method": "razorpay",
    "auto_renew": true,
    "notes": "Free Cash"
}'
```

## 🔧 What Was Fixed

### 1. **Customer ID Validation Issue**
- **Problem**: Validation was checking `customers.id` but our table uses `pk_customer_code`
- **Fix**: Updated validation rule to `exists:customers,pk_customer_code`

### 2. **Missing Controller Methods**
- **Added**: `processPayment()`, `logs()`, `customerSubscriptions()`, `activeCustomerSubscriptions()`
- **Fixed**: Method parameter handling for pause, cancel, renew operations

### 3. **Service Method Signatures**
- **Fixed**: `createSubscription()` to accept items array parameter
- **Added**: `getSubscriptionLogs()` method
- **Fixed**: All service methods to handle data arrays properly

### 4. **Automatic Amount Calculation**
- **Added**: Auto-calculation of subscription amount from plan price if not provided
- **Made**: `amount` and `end_date` fields optional in validation

## 📋 Complete API Endpoints (25 Total)

### Subscription Plans (11 endpoints)
- ✅ `GET /api/v2/subscription-plans` - Get all plans
- ✅ `GET /api/v2/subscription-plans/active` - Get active plans
- ✅ `GET /api/v2/subscription-plans/customer` - Get customer-visible plans
- ✅ `GET /api/v2/subscription-plans/type/{type}` - Get plans by type
- ✅ `GET /api/v2/subscription-plans/{id}` - Get specific plan
- ✅ `POST /api/v2/subscription-plans` - Create new plan
- ✅ `PUT /api/v2/subscription-plans/{id}` - Update plan
- ✅ `DELETE /api/v2/subscription-plans/{id}` - Delete plan
- ✅ `PUT /api/v2/subscription-plans/{id}/activate` - Activate plan
- ✅ `PUT /api/v2/subscription-plans/{id}/deactivate` - Deactivate plan

### Subscriptions (14 endpoints)
- ✅ `GET /api/v2/subscriptions` - Get all subscriptions
- ✅ `GET /api/v2/subscriptions/{id}` - Get specific subscription
- ✅ `GET /api/v2/subscriptions/customer/{id}` - Get customer subscriptions
- ✅ `GET /api/v2/subscriptions/customer/{id}/active` - Get active customer subscriptions
- ✅ `POST /api/v2/subscriptions` - **Create subscription (FIXED)**
- ✅ `PUT /api/v2/subscriptions/{id}` - Update subscription
- ✅ `PUT /api/v2/subscriptions/{id}/pause` - Pause subscription
- ✅ `PUT /api/v2/subscriptions/{id}/resume` - Resume subscription
- ✅ `PUT /api/v2/subscriptions/{id}/cancel` - Cancel subscription
- ✅ `PUT /api/v2/subscriptions/{id}/renew` - Renew subscription
- ✅ `POST /api/v2/subscriptions/{id}/payment` - Process payment
- ✅ `GET /api/v2/subscriptions/{id}/logs` - Get subscription logs

## 🧪 Testing Scenarios

### 1. Basic CRUD Operations
```bash
# Get all plans
curl -X GET 'http://localhost:8000/api/v2/subscription-plans' -H 'Accept: application/json'

# Get all subscriptions
curl -X GET 'http://localhost:8000/api/v2/subscriptions' -H 'Accept: application/json'

# Create subscription (the one that was failing)
curl -X POST 'http://localhost:8000/api/v2/subscriptions' \
  -H 'Accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{"customer_id": 1, "plan_id": 1, "start_date": "2025-01-01", "payment_method": "razorpay", "notes": "Test"}'
```

### 2. Subscription Lifecycle
```bash
# 1. Create subscription
# 2. Pause subscription
curl -X PUT 'http://localhost:8000/api/v2/subscriptions/1/pause' \
  -H 'Content-Type: application/json' \
  -d '{"reason": "Customer traveling"}'

# 3. Resume subscription
curl -X PUT 'http://localhost:8000/api/v2/subscriptions/1/resume'

# 4. Process payment
curl -X POST 'http://localhost:8000/api/v2/subscriptions/1/payment' \
  -H 'Content-Type: application/json' \
  -d '{"payment_method": "razorpay", "transaction_id": "TXN123", "amount": 150, "payment_status": "completed"}'

# 5. Cancel subscription
curl -X PUT 'http://localhost:8000/api/v2/subscriptions/1/cancel' \
  -H 'Content-Type: application/json' \
  -d '{"reason": "Not satisfied"}'
```

### 3. Customer-Specific Queries
```bash
# Get customer subscriptions
curl -X GET 'http://localhost:8000/api/v2/subscriptions/customer/1'

# Get active customer subscriptions
curl -X GET 'http://localhost:8000/api/v2/subscriptions/customer/1/active'
```

## 📊 Real Data Available

### Customers (11 customers)
- Customer IDs: 1-11 (using `pk_customer_code`)
- Real names, phone numbers, addresses

### Subscription Plans (10 plans)
- Plan IDs: 1-10
- Types: lunch, dinner, full_day, student, corporate, weekend
- Prices: $150 - $8000

### Subscriptions (17 existing)
- Various statuses: active, paused, cancelled, expired
- Real payment data and transaction IDs

## 🔍 Troubleshooting

### Common Issues & Solutions

1. **404 Not Found**
   - Ensure server is running: `php artisan serve`
   - Check URL: `http://localhost:8000/api/v2/...`

2. **Validation Errors**
   - Use existing customer IDs (1-11)
   - Use existing plan IDs (1-10)
   - Provide required fields: customer_id, plan_id, start_date

3. **Database Connection**
   - Check `.env` file database settings
   - Ensure MySQL is running
   - Database: `onefooddialer`

4. **Method Not Found**
   - All controller methods are now implemented
   - Check HTTP method (GET, POST, PUT, DELETE)

## 📁 Files Updated

1. **Controllers**
   - `SubscriptionController.php` - Added missing methods
   - `SubscriptionPlanController.php` - Fixed duplicate methods

2. **Services**
   - `SubscriptionService.php` - Added getSubscriptionLogs method
   - Fixed method signatures and parameter handling

3. **Validation**
   - Fixed customer_id validation to use correct primary key
   - Made amount and end_date optional

4. **Postman Collection**
   - `Subscription_Service_Complete_API_Collection.postman_collection.json`
   - 25 comprehensive API tests with real data examples

## ✅ Success Indicators

When APIs are working correctly, you should see:
- JSON responses with `"success": true`
- Real data from database
- Proper HTTP status codes (200, 201, 404, 422)
- Detailed error messages for validation failures

## 🎯 Next Steps

1. **Test all endpoints** using the Postman collection
2. **Verify data integrity** - check that operations affect database correctly
3. **Test edge cases** - invalid IDs, missing fields, etc.
4. **Performance testing** - test with larger datasets
5. **Security testing** - implement authentication when ready

<?php

use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\SubscriptionPlanController;
use App\Http\Controllers\Api\MealPlanController;
use App\Http\Controllers\Api\SchoolMealSubscriptionController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('v2')->group(function () {
    // Subscription Plans
    Route::prefix('subscription-plans')->group(function () {
        Route::get('/', [SubscriptionPlanController::class, 'index']);
        Route::post('/', [SubscriptionPlanController::class, 'store']);
        Route::get('/{id}', [SubscriptionPlanController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/{id}', [SubscriptionPlanController::class, 'update'])->where('id', '[0-9]+');
        Route::delete('/{id}', [SubscriptionPlanController::class, 'destroy'])->where('id', '[0-9]+');
        Route::get('/customer', [SubscriptionPlanController::class, 'customer']);
        Route::get('/active', [SubscriptionPlanController::class, 'active']);
        Route::put('/{id}/activate', [SubscriptionPlanController::class, 'activate'])->where('id', '[0-9]+');
        Route::put('/{id}/deactivate', [SubscriptionPlanController::class, 'deactivate'])->where('id', '[0-9]+');
        Route::get('/type/{type}', [SubscriptionPlanController::class, 'type']);
    });

    // Subscriptions
    Route::prefix('subscriptions')->group(function () {
        Route::get('/', [SubscriptionController::class, 'index']);
        Route::post('/', [SubscriptionController::class, 'store']);
        Route::get('/{id}', [SubscriptionController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/{id}', [SubscriptionController::class, 'update'])->where('id', '[0-9]+');
        Route::put('/{id}/cancel', [SubscriptionController::class, 'cancel'])->where('id', '[0-9]+');
        Route::put('/{id}/pause', [SubscriptionController::class, 'pause'])->where('id', '[0-9]+');
        Route::put('/{id}/resume', [SubscriptionController::class, 'resume'])->where('id', '[0-9]+');
        Route::put('/{id}/renew', [SubscriptionController::class, 'renew'])->where('id', '[0-9]+');
        Route::post('/{id}/payment', [SubscriptionController::class, 'processPayment'])->where('id', '[0-9]+');
        Route::get('/{id}/logs', [SubscriptionController::class, 'logs'])->where('id', '[0-9]+');
        Route::get('/customer/{customerId}', [SubscriptionController::class, 'customerSubscriptions'])->where('customerId', '[0-9]+');
        Route::get('/customer/{customerId}/active', [SubscriptionController::class, 'activeCustomerSubscriptions'])->where('customerId', '[0-9]+');
    });

    // School Meal Plans
    Route::prefix('meal-plans')->group(function () {
        Route::get('/', [MealPlanController::class, 'index']);
        Route::post('/', [MealPlanController::class, 'store']);
        Route::get('/{id}', [MealPlanController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/{id}', [MealPlanController::class, 'update'])->where('id', '[0-9]+');
        Route::delete('/{id}', [MealPlanController::class, 'destroy'])->where('id', '[0-9]+');

        // School-specific meal plans
        Route::get('/school/{schoolId}', [MealPlanController::class, 'getBySchool'])->where('schoolId', '[0-9]+');

        // Meal plan utilities
        Route::get('/compatible', [MealPlanController::class, 'getCompatiblePlans']);
        Route::get('/{id}/pricing', [MealPlanController::class, 'getPricing'])->where('id', '[0-9]+');
        Route::put('/{id}/availability', [MealPlanController::class, 'updateAvailability'])->where('id', '[0-9]+');
    });

    // School Meal Subscriptions
    Route::prefix('school-meal-subscriptions')->group(function () {
        Route::get('/', [SchoolMealSubscriptionController::class, 'index']);
        Route::post('/', [SchoolMealSubscriptionController::class, 'store']);
        Route::get('/{id}', [SchoolMealSubscriptionController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/{id}', [SchoolMealSubscriptionController::class, 'update'])->where('id', '[0-9]+');

        // Subscription lifecycle
        Route::put('/{id}/pause', [SchoolMealSubscriptionController::class, 'pause'])->where('id', '[0-9]+');
        Route::put('/{id}/resume', [SchoolMealSubscriptionController::class, 'resume'])->where('id', '[0-9]+');
        Route::put('/{id}/cancel', [SchoolMealSubscriptionController::class, 'cancel'])->where('id', '[0-9]+');

        // Subscription management
        Route::get('/parent/{parentId}', [SchoolMealSubscriptionController::class, 'getParentSubscriptions'])->where('parentId', '[0-9]+');
        Route::get('/child/{childId}', [SchoolMealSubscriptionController::class, 'getChildSubscriptions'])->where('childId', '[0-9]+');

        // Billing and feedback
        Route::post('/process-billing', [SchoolMealSubscriptionController::class, 'processBilling']);
        Route::post('/{id}/feedback', [SchoolMealSubscriptionController::class, 'addFeedback'])->where('id', '[0-9]+');
    });
});

_format_version: "2.1"
_transform: true

services:
  - name: subscription-service
    url: http://subscription-service:8000
    routes:
      - name: subscription-plans-route
        paths:
          - /api/v2/subscription-plans
          - /api/v2/subscription-plans/active
          - /api/v2/subscription-plans/customer
          - /api/v2/subscription-plans/type
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
        
      - name: subscription-plans-id-route
        paths:
          - /api/v2/subscription-plans/(?<id>\d+)
          - /api/v2/subscription-plans/(?<id>\d+)/activate
          - /api/v2/subscription-plans/(?<id>\d+)/deactivate
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
        
      - name: subscriptions-route
        paths:
          - /api/v2/subscriptions
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
        
      - name: subscriptions-id-route
        paths:
          - /api/v2/subscriptions/(?<id>\d+)
          - /api/v2/subscriptions/(?<id>\d+)/pause
          - /api/v2/subscriptions/(?<id>\d+)/resume
          - /api/v2/subscriptions/(?<id>\d+)/cancel
          - /api/v2/subscriptions/(?<id>\d+)/renew
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
          preflight_continue: false
      
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          
      - name: key-auth
        config:
          key_names:
            - apikey
          key_in_body: false
          hide_credentials: true
          
      - name: acl
        config:
          allow:
            - subscription-service-group
            - admin-group
          
consumers:
  - username: subscription-service-consumer
    keyauth_credentials:
      - key: your-api-key-here
    acls:
      - group: subscription-service-group
      
  - username: admin-consumer
    keyauth_credentials:
      - key: admin-api-key-here
    acls:
      - group: admin-group

<?php

namespace App\Http\Requests\SubscriptionPlan;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubscriptionPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'integer',
            'unit_id' => 'integer',
            'plan_name' => 'string|max:255',
            'plan_quantity' => 'integer|min:1',
            'plan_period' => 'string|in:daily,weekly,monthly,quarterly,biannual,annual',
            'plan_type' => 'string|max:50',
            'plan_start_date' => 'date',
            'plan_end_date' => 'date|after:plan_start_date',
            'fk_promo_code' => 'nullable|string|max:50',
            'plan_status' => 'boolean',
            'show_to_customer' => 'string|in:yes,no',
            'fk_kitchen_code' => 'nullable|string|max:50',
            'price' => 'numeric|min:0',
            'is_recurring' => 'boolean',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'plan_quantity.min' => 'Plan quantity must be at least 1',
            'plan_period.in' => 'Plan period must be one of: daily, weekly, monthly, quarterly, biannual, annual',
            'plan_start_date.date' => 'Plan start date must be a valid date',
            'plan_end_date.date' => 'Plan end date must be a valid date',
            'plan_end_date.after' => 'Plan end date must be after plan start date',
            'price.numeric' => 'Price must be a number',
            'price.min' => 'Price must be at least 0',
        ];
    }
}

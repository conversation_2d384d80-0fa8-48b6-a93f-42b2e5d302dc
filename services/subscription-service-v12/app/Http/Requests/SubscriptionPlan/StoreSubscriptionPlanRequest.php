<?php

namespace App\Http\Requests\SubscriptionPlan;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriptionPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'required|integer',
            'unit_id' => 'required|integer',
            'plan_name' => 'required|string|max:255',
            'plan_quantity' => 'required|integer|min:1',
            'plan_period' => 'required|string|in:daily,weekly,monthly,quarterly,biannual,annual',
            'plan_type' => 'required|string|max:50',
            'plan_start_date' => 'required|date',
            'plan_end_date' => 'required|date|after:plan_start_date',
            'fk_promo_code' => 'nullable|string|max:50',
            'plan_status' => 'boolean',
            'show_to_customer' => 'string|in:yes,no',
            'fk_kitchen_code' => 'nullable|string|max:50',
            'price' => 'required|numeric|min:0',
            'is_recurring' => 'boolean',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'company_id.required' => 'Company ID is required',
            'unit_id.required' => 'Unit ID is required',
            'plan_name.required' => 'Plan name is required',
            'plan_quantity.required' => 'Plan quantity is required',
            'plan_quantity.min' => 'Plan quantity must be at least 1',
            'plan_period.required' => 'Plan period is required',
            'plan_period.in' => 'Plan period must be one of: daily, weekly, monthly, quarterly, biannual, annual',
            'plan_type.required' => 'Plan type is required',
            'plan_start_date.required' => 'Plan start date is required',
            'plan_start_date.date' => 'Plan start date must be a valid date',
            'plan_end_date.required' => 'Plan end date is required',
            'plan_end_date.date' => 'Plan end date must be a valid date',
            'plan_end_date.after' => 'Plan end date must be after plan start date',
            'price.required' => 'Price is required',
            'price.numeric' => 'Price must be a number',
            'price.min' => 'Price must be at least 0',
        ];
    }
}

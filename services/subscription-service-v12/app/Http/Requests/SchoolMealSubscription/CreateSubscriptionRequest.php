<?php

namespace App\Http\Requests\SchoolMealSubscription;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * CreateSubscriptionRequest
 *
 * Validation rules for creating a new school meal subscription.
 */
class CreateSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tenant_id' => [
                'nullable',
                'integer',
            ],
            'company_id' => [
                'nullable',
                'integer',
            ],
            'unit_id' => [
                'nullable',
                'integer',
            ],
            'parent_customer_id' => [
                'required',
                'integer',
                'exists:customers,pk_customer_code',
            ],
            'child_profile_id' => [
                'required',
                'integer',
                'exists:child_profiles,id',
                function ($attribute, $value, $fail) {
                    // Check if child belongs to the parent
                    $child = \App\Models\ChildProfile::find($value);
                    if ($child && $child->parent_customer_id !== $this->input('parent_customer_id')) {
                        $fail('The selected child does not belong to this parent.');
                    }
                    
                    // Check if child already has an active subscription
                    $activeSubscription = \App\Models\SchoolMealSubscription::where('child_profile_id', $value)
                        ->where('status', 'active')
                        ->exists();
                    if ($activeSubscription) {
                        $fail('This child already has an active meal subscription.');
                    }
                },
            ],
            'meal_plan_id' => [
                'required',
                'integer',
                'exists:meal_plans,id',
                function ($attribute, $value, $fail) {
                    $mealPlan = \App\Models\MealPlan::find($value);
                    if ($mealPlan && !$mealPlan->isAvailableForSubscription()) {
                        $fail('The selected meal plan is not available for subscription.');
                    }
                },
            ],
            'start_date' => [
                'required',
                'date',
                'after_or_equal:today',
            ],
            'end_date' => [
                'required',
                'date',
                'after:start_date',
            ],
            'subscription_type' => [
                'required',
                Rule::in(['daily', 'weekly', 'monthly', 'quarterly', 'annual']),
            ],
            'billing_cycle' => [
                'required',
                Rule::in(['daily', 'weekly', 'monthly', 'quarterly']),
            ],
            'auto_renew' => [
                'nullable',
                'boolean',
            ],
            'delivery_days' => [
                'required',
                'array',
                'min:1',
                'max:7',
            ],
            'delivery_days.*' => [
                'string',
                Rule::in(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']),
            ],
            'preferred_break_time' => [
                'required',
                Rule::in(['morning_break', 'lunch_break', 'both']),
            ],
            'delivery_time_preference' => [
                'nullable',
                'date_format:H:i',
            ],
            'delivery_instructions' => [
                'nullable',
                'array',
                'max:5',
            ],
            'delivery_instructions.*' => [
                'string',
                'max:200',
            ],
            'meal_customizations' => [
                'nullable',
                'array',
            ],
            'meal_customizations.portion_size' => [
                'nullable',
                Rule::in(['small', 'regular', 'large']),
            ],
            'meal_customizations.extra_items' => [
                'nullable',
                'array',
                'max:3',
            ],
            'meal_customizations.extra_items.*' => [
                'string',
                'max:100',
            ],
            'dietary_accommodations' => [
                'nullable',
                'array',
                'max:10',
            ],
            'dietary_accommodations.*' => [
                'string',
                Rule::in([
                    'nuts', 'dairy', 'gluten', 'eggs', 'soy', 'fish', 'shellfish',
                    'sesame', 'mustard', 'celery', 'lupin', 'molluscs', 'sulphites'
                ]),
            ],
            'spice_level' => [
                'nullable',
                Rule::in(['no_spice', 'mild', 'medium', 'spicy']),
            ],
            'emergency_instructions' => [
                'nullable',
                'string',
                'max:500',
            ],
            'special_notes' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'requires_special_handling' => [
                'nullable',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'parent_customer_id.required' => 'Parent customer is required.',
            'parent_customer_id.exists' => 'The selected parent customer is invalid.',
            'child_profile_id.required' => 'Child profile is required.',
            'child_profile_id.exists' => 'The selected child profile is invalid.',
            'meal_plan_id.required' => 'Meal plan is required.',
            'meal_plan_id.exists' => 'The selected meal plan is invalid.',
            'start_date.required' => 'Start date is required.',
            'start_date.after_or_equal' => 'Start date must be today or in the future.',
            'end_date.required' => 'End date is required.',
            'end_date.after' => 'End date must be after the start date.',
            'subscription_type.required' => 'Subscription type is required.',
            'subscription_type.in' => 'Invalid subscription type selected.',
            'billing_cycle.required' => 'Billing cycle is required.',
            'billing_cycle.in' => 'Invalid billing cycle selected.',
            'delivery_days.required' => 'At least one delivery day is required.',
            'delivery_days.min' => 'At least one delivery day must be selected.',
            'delivery_days.max' => 'Maximum 7 delivery days can be selected.',
            'delivery_days.*.in' => 'Invalid delivery day selected.',
            'preferred_break_time.required' => 'Preferred break time is required.',
            'preferred_break_time.in' => 'Invalid break time preference selected.',
            'delivery_time_preference.date_format' => 'Delivery time must be in HH:MM format.',
            'delivery_instructions.max' => 'Maximum 5 delivery instructions allowed.',
            'delivery_instructions.*.max' => 'Each delivery instruction must not exceed 200 characters.',
            'dietary_accommodations.max' => 'Maximum 10 dietary accommodations allowed.',
            'dietary_accommodations.*.in' => 'Invalid dietary accommodation selected.',
            'spice_level.in' => 'Invalid spice level selected.',
            'emergency_instructions.max' => 'Emergency instructions must not exceed 500 characters.',
            'special_notes.max' => 'Special notes must not exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'parent_customer_id' => 'parent customer',
            'child_profile_id' => 'child profile',
            'meal_plan_id' => 'meal plan',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'subscription_type' => 'subscription type',
            'billing_cycle' => 'billing cycle',
            'delivery_days' => 'delivery days',
            'preferred_break_time' => 'preferred break time',
            'delivery_time_preference' => 'delivery time preference',
            'delivery_instructions' => 'delivery instructions',
            'meal_customizations' => 'meal customizations',
            'dietary_accommodations' => 'dietary accommodations',
            'spice_level' => 'spice level',
            'emergency_instructions' => 'emergency instructions',
            'special_notes' => 'special notes',
            'requires_special_handling' => 'special handling requirement',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'auto_renew' => $this->input('auto_renew', true),
            'spice_level' => $this->input('spice_level', 'mild'),
            'preferred_break_time' => $this->input('preferred_break_time', 'lunch_break'),
            'requires_special_handling' => $this->input('requires_special_handling', false),
        ]);

        // Ensure delivery_days are lowercase
        if ($this->has('delivery_days') && is_array($this->delivery_days)) {
            $this->merge([
                'delivery_days' => array_map('strtolower', $this->delivery_days)
            ]);
        }

        // Set tenant information from authenticated user or defaults
        if (!$this->has('tenant_id')) {
            $this->merge([
                'tenant_id' => 1, // Default tenant
                'company_id' => 1, // Default company
                'unit_id' => 1, // Default unit
            ]);
        }
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate that child and meal plan belong to the same school
            $childId = $this->input('child_profile_id');
            $mealPlanId = $this->input('meal_plan_id');
            
            if ($childId && $mealPlanId) {
                $child = \App\Models\ChildProfile::find($childId);
                $mealPlan = \App\Models\MealPlan::find($mealPlanId);
                
                if ($child && $mealPlan && $child->school_id !== $mealPlan->school_id) {
                    $validator->errors()->add(
                        'meal_plan_id',
                        'The selected meal plan must belong to the same school as the child.'
                    );
                }
            }

            // Validate delivery days against meal plan availability
            if ($mealPlanId && $this->has('delivery_days')) {
                $mealPlan = \App\Models\MealPlan::find($mealPlanId);
                if ($mealPlan) {
                    $unavailableDays = array_diff($this->delivery_days, $mealPlan->available_days ?? []);
                    if (!empty($unavailableDays)) {
                        $validator->errors()->add(
                            'delivery_days',
                            'Some selected delivery days are not available for this meal plan: ' . implode(', ', $unavailableDays)
                        );
                    }
                }
            }

            // Validate dietary accommodations against meal plan compatibility
            if ($mealPlanId && $this->has('dietary_accommodations')) {
                $mealPlan = \App\Models\MealPlan::find($mealPlanId);
                if ($mealPlan && !$mealPlan->isCompatibleWithDietaryRestrictions($this->dietary_accommodations)) {
                    $validator->errors()->add(
                        'dietary_accommodations',
                        'The selected meal plan is not compatible with the specified dietary accommodations.'
                    );
                }
            }
        });
    }
}

<?php

namespace App\Http\Requests\Subscription;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'integer',
            'unit_id' => 'integer',
            'customer_id' => 'integer',
            'plan_id' => 'integer|exists:subscription_plans,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|string|in:pending,active,paused,cancelled,expired',
            'amount' => 'numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'total' => 'numeric|min:0',
            'payment_method' => 'nullable|string|max:50',
            'payment_status' => 'nullable|string|in:pending,completed,failed',
            'transaction_id' => 'nullable|string|max:255',
            'next_billing_date' => 'nullable|date',
            'auto_renew' => 'nullable|boolean',
            'notes' => 'nullable|string',
            'items' => 'nullable|array',
            'items.*.id' => 'nullable|integer|exists:subscription_items,id',
            'items.*.item_id' => 'required|integer',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_type' => 'required|string|max:50',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'items.*.status' => 'nullable|string|in:active,inactive',
            'items.*.delivery_date' => 'nullable|date',
            'items.*.delivery_status' => 'nullable|string|max:50',
            'items.*.notes' => 'nullable|string',
            'replace_items' => 'nullable|boolean',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'plan_id.exists' => 'The selected plan does not exist',
            'start_date.date' => 'Start date must be a valid date',
            'end_date.date' => 'End date must be a valid date',
            'end_date.after_or_equal' => 'End date must be after or equal to start date',
            'status.in' => 'Status must be one of: pending, active, paused, cancelled, expired',
            'amount.numeric' => 'Amount must be a number',
            'amount.min' => 'Amount must be at least 0',
            'discount.numeric' => 'Discount must be a number',
            'discount.min' => 'Discount must be at least 0',
            'total.numeric' => 'Total must be a number',
            'total.min' => 'Total must be at least 0',
            'payment_status.in' => 'Payment status must be one of: pending, completed, failed',
            'next_billing_date.date' => 'Next billing date must be a valid date',
            'items.*.id.exists' => 'The selected item does not exist',
            'items.*.item_id.required' => 'Item ID is required',
            'items.*.item_name.required' => 'Item name is required',
            'items.*.item_type.required' => 'Item type is required',
            'items.*.quantity.required' => 'Quantity is required',
            'items.*.quantity.min' => 'Quantity must be at least 1',
            'items.*.price.required' => 'Price is required',
            'items.*.price.numeric' => 'Price must be a number',
            'items.*.price.min' => 'Price must be at least 0',
            'items.*.total.required' => 'Total is required',
            'items.*.total.numeric' => 'Total must be a number',
            'items.*.total.min' => 'Total must be at least 0',
            'items.*.status.in' => 'Status must be one of: active, inactive',
            'items.*.delivery_date.date' => 'Delivery date must be a valid date',
        ];
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SchoolMealSubscription\CreateSubscriptionRequest;
use App\Http\Requests\SchoolMealSubscription\UpdateSubscriptionRequest;
use App\Http\Requests\SchoolMealSubscription\PauseSubscriptionRequest;
use App\Models\SchoolMealSubscription;
use App\Services\SchoolMealSubscriptionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * SchoolMealSubscriptionController
 *
 * Handles school meal subscription lifecycle management including creation,
 * billing, pause/resume, and cancellation for the tiffin system.
 */
class SchoolMealSubscriptionController extends Controller
{
    protected SchoolMealSubscriptionService $subscriptionService;

    public function __construct(SchoolMealSubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Get subscriptions with filtering options.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'parent_customer_id',
                'child_profile_id',
                'school_id',
                'meal_plan_id',
                'status',
                'subscription_type',
                'billing_cycle',
                'start_date',
                'end_date',
            ]);

            $subscriptions = $this->subscriptionService->getSubscriptions($filters, $request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $subscriptions,
                'meta' => [
                    'filters_applied' => array_filter($filters),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get school meal subscriptions', [
                'error' => $e->getMessage(),
                'filters' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve subscriptions',
            ], 500);
        }
    }

    /**
     * Get a specific subscription.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $subscription = $this->subscriptionService->getSubscriptionById($id);

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $subscription,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get subscription', [
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve subscription',
            ], 500);
        }
    }

    /**
     * Create a new school meal subscription.
     *
     * @param CreateSubscriptionRequest $request
     * @return JsonResponse
     */
    public function store(CreateSubscriptionRequest $request): JsonResponse
    {
        try {
            $subscription = $this->subscriptionService->createSubscription($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'School meal subscription created successfully',
                'data' => $subscription,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create school meal subscription', [
                'error' => $e->getMessage(),
                'request_data' => $request->validated(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create subscription: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a subscription.
     *
     * @param UpdateSubscriptionRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateSubscriptionRequest $request, int $id): JsonResponse
    {
        try {
            $subscription = $this->subscriptionService->updateSubscription($id, $request->validated());

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Subscription updated successfully',
                'data' => $subscription,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update subscription', [
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update subscription: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Pause a subscription.
     *
     * @param PauseSubscriptionRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function pause(PauseSubscriptionRequest $request, int $id): JsonResponse
    {
        try {
            $subscription = $this->subscriptionService->pauseSubscription(
                $id,
                $request->input('reason'),
                $request->input('pause_start_date'),
                $request->input('pause_end_date')
            );

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Subscription paused successfully',
                'data' => $subscription,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to pause subscription', [
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to pause subscription: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Resume a paused subscription.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function resume(Request $request, int $id): JsonResponse
    {
        try {
            $subscription = $this->subscriptionService->resumeSubscription($id);

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Subscription resumed successfully',
                'data' => $subscription,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to resume subscription', [
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to resume subscription: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel a subscription.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function cancel(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'cancellation_type' => 'nullable|in:parent_request,payment_failure,school_termination,system_auto',
            'refund_requested' => 'nullable|boolean',
        ]);

        try {
            $subscription = $this->subscriptionService->cancelSubscription(
                $id,
                $request->input('reason'),
                $request->input('cancellation_type', 'parent_request'),
                $request->input('refund_requested', false)
            );

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Subscription cancelled successfully',
                'data' => $subscription,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription', [
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel subscription: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get subscriptions for a parent.
     *
     * @param Request $request
     * @param int $parentId
     * @return JsonResponse
     */
    public function getParentSubscriptions(Request $request, int $parentId): JsonResponse
    {
        try {
            $filters = $request->only(['status', 'school_id', 'child_profile_id']);
            $subscriptions = $this->subscriptionService->getParentSubscriptions($parentId, $filters);

            return response()->json([
                'success' => true,
                'data' => $subscriptions,
                'meta' => [
                    'parent_id' => $parentId,
                    'total_subscriptions' => $subscriptions->count(),
                    'active_subscriptions' => $subscriptions->where('status', 'active')->count(),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get parent subscriptions', [
                'parent_id' => $parentId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve parent subscriptions',
            ], 500);
        }
    }

    /**
     * Get subscriptions for a child.
     *
     * @param Request $request
     * @param int $childId
     * @return JsonResponse
     */
    public function getChildSubscriptions(Request $request, int $childId): JsonResponse
    {
        try {
            $subscriptions = $this->subscriptionService->getChildSubscriptions($childId);

            return response()->json([
                'success' => true,
                'data' => $subscriptions,
                'meta' => [
                    'child_id' => $childId,
                    'total_subscriptions' => $subscriptions->count(),
                    'active_subscription' => $subscriptions->where('status', 'active')->first(),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get child subscriptions', [
                'child_id' => $childId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve child subscriptions',
            ], 500);
        }
    }

    /**
     * Process billing for subscriptions due.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function processBilling(Request $request): JsonResponse
    {
        $request->validate([
            'date' => 'nullable|date',
            'subscription_ids' => 'nullable|array',
            'subscription_ids.*' => 'integer|exists:school_meal_subscriptions,id',
        ]);

        try {
            $result = $this->subscriptionService->processBillingBatch(
                $request->input('date'),
                $request->input('subscription_ids')
            );

            return response()->json([
                'success' => true,
                'message' => 'Billing processed successfully',
                'data' => $result,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process billing', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process billing: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add feedback and rating to a subscription.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function addFeedback(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'rating' => 'required|numeric|min:1|max:5',
            'feedback' => 'nullable|string|max:1000',
        ]);

        try {
            $subscription = $this->subscriptionService->addSubscriptionFeedback(
                $id,
                $request->input('rating'),
                $request->input('feedback')
            );

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Feedback added successfully',
                'data' => $subscription,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to add subscription feedback', [
                'subscription_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add feedback',
            ], 500);
        }
    }
}

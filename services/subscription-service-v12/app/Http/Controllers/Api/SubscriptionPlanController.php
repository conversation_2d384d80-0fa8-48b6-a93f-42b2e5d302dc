<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SubscriptionPlanService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubscriptionPlanController extends Controller
{
    /**
     * The subscription plan service instance.
     *
     * @var \App\Services\SubscriptionPlanService
     */
    protected $subscriptionPlanService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\SubscriptionPlanService  $subscriptionPlanService
     * @return void
     */
    public function __construct(SubscriptionPlanService $subscriptionPlanService)
    {
        $this->subscriptionPlanService = $subscriptionPlanService;
    }

    /**
     * Display a listing of the subscription plans.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only(['status', 'type', 'show_to_customer', 'start_date', 'end_date']);
        $plans = $this->subscriptionPlanService->getAllPlans($filters);

        return response()->json([
            'success' => true,
            'data' => $plans
        ]);
    }

    /**
     * Store a newly created subscription plan in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'plan_name' => 'required|string|max:255',
            'plan_quantity' => 'required|integer|min:1',
            'plan_period' => 'required|string|in:day,week,month,year',
            'plan_type' => 'nullable|string|max:255',
            'plan_start_date' => 'required|date',
            'plan_end_date' => 'required|date|after_or_equal:plan_start_date',
            'fk_promo_code' => 'nullable|integer',
            'plan_status' => 'nullable|boolean',
            'show_to_customer' => 'nullable|string|in:yes,no,admin',
            'fk_kitchen_code' => 'nullable|integer',
            'price' => 'required|numeric|min:0',
            'is_recurring' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        $data['company_id'] = $request->input('company_id', 1);
        $data['unit_id'] = $request->input('unit_id', 1);
        $data['plan_status'] = $request->input('plan_status', true);
        $data['show_to_customer'] = $request->input('show_to_customer', 'yes');
        $data['is_recurring'] = $request->input('is_recurring', false);

        $plan = $this->subscriptionPlanService->createPlan($data);

        return response()->json([
            'success' => true,
            'message' => 'Subscription plan created successfully',
            'data' => $plan
        ], 201);
    }

    /**
     * Display the specified subscription plan.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): JsonResponse
    {
        $plan = $this->subscriptionPlanService->getPlanById($id);

        if (!$plan) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription plan not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $plan
        ]);
    }

    /**
     * Update the specified subscription plan in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'plan_name' => 'nullable|string|max:255',
            'plan_quantity' => 'nullable|integer|min:1',
            'plan_period' => 'nullable|string|in:day,week,month,year',
            'plan_type' => 'nullable|string|max:255',
            'plan_start_date' => 'nullable|date',
            'plan_end_date' => 'nullable|date|after_or_equal:plan_start_date',
            'fk_promo_code' => 'nullable|integer',
            'plan_status' => 'nullable|boolean',
            'show_to_customer' => 'nullable|string|in:yes,no,admin',
            'fk_kitchen_code' => 'nullable|integer',
            'price' => 'nullable|numeric|min:0',
            'is_recurring' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $updatedPlan = $this->subscriptionPlanService->updatePlan($id, $request->all());

        if (!$updatedPlan) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription plan not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Subscription plan updated successfully',
            'data' => $updatedPlan
        ]);
    }

    /**
     * Remove the specified subscription plan from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $result = $this->subscriptionPlanService->deletePlan($id);

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription plan not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Subscription plan deleted successfully'
        ]);
    }

    /**
     * Get active subscription plans.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function active(): JsonResponse
    {
        $plans = $this->subscriptionPlanService->getActivePlans();

        return response()->json([
            'success' => true,
            'data' => $plans
        ]);
    }

    /**
     * Get subscription plans visible to customers.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function customer(): JsonResponse
    {
        $plans = $this->subscriptionPlanService->getCustomerVisiblePlans();

        return response()->json([
            'success' => true,
            'data' => $plans
        ]);
    }

    /**
     * Get subscription plans by type.
     *
     * @param  string  $type
     * @return \Illuminate\Http\JsonResponse
     */
    public function type($type): JsonResponse
    {
        $plans = $this->subscriptionPlanService->getPlansByType($type);

        return response()->json([
            'success' => true,
            'data' => $plans
        ]);
    }

    /**
     * Activate a subscription plan.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function activate($id): JsonResponse
    {
        $plan = $this->subscriptionPlanService->activatePlan($id);

        if (!$plan) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription plan not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Subscription plan activated successfully',
            'data' => $plan
        ]);
    }

    /**
     * Deactivate a subscription plan.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deactivate($id): JsonResponse
    {
        $plan = $this->subscriptionPlanService->deactivatePlan($id);

        if (!$plan) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription plan not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Subscription plan deactivated successfully',
            'data' => $plan
        ]);
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\MealPlan\CreateMealPlanRequest;
use App\Http\Requests\MealPlan\UpdateMealPlanRequest;
use App\Models\MealPlan;
use App\Services\MealPlanService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * MealPlanController
 *
 * Handles school-specific meal plan management for the tiffin subscription system.
 * Manages meal plans with nutritional information, pricing, and dietary specifications.
 */
class MealPlanController extends Controller
{
    protected MealPlanService $mealPlanService;

    public function __construct(MealPlanService $mealPlanService)
    {
        $this->mealPlanService = $mealPlanService;
    }

    /**
     * Get meal plans with filtering options.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'school_id',
                'meal_type',
                'is_active',
                'is_vegetarian',
                'is_vegan',
                'is_jain',
                'is_gluten_free',
                'is_dairy_free',
                'is_nut_free',
                'min_price',
                'max_price',
                'available_day',
            ]);

            $mealPlans = $this->mealPlanService->getMealPlans($filters, $request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $mealPlans,
                'meta' => [
                    'filters_applied' => array_filter($filters),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get meal plans', [
                'error' => $e->getMessage(),
                'filters' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve meal plans',
            ], 500);
        }
    }

    /**
     * Get meal plans for a specific school.
     *
     * @param Request $request
     * @param int $schoolId
     * @return JsonResponse
     */
    public function getBySchool(Request $request, int $schoolId): JsonResponse
    {
        try {
            $filters = $request->only([
                'meal_type',
                'is_active',
                'dietary_preferences',
                'min_price',
                'max_price',
            ]);

            $filters['school_id'] = $schoolId;

            $mealPlans = $this->mealPlanService->getSchoolMealPlans($schoolId, $filters);

            return response()->json([
                'success' => true,
                'data' => $mealPlans,
                'meta' => [
                    'school_id' => $schoolId,
                    'total_plans' => $mealPlans->count(),
                    'active_plans' => $mealPlans->where('is_active', true)->count(),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get school meal plans', [
                'school_id' => $schoolId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve school meal plans',
            ], 500);
        }
    }

    /**
     * Get a specific meal plan.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $mealPlan = $this->mealPlanService->getMealPlanById($id);

            if (!$mealPlan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meal plan not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $mealPlan,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get meal plan', [
                'meal_plan_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve meal plan',
            ], 500);
        }
    }

    /**
     * Create a new meal plan.
     *
     * @param CreateMealPlanRequest $request
     * @return JsonResponse
     */
    public function store(CreateMealPlanRequest $request): JsonResponse
    {
        try {
            $mealPlan = $this->mealPlanService->createMealPlan($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Meal plan created successfully',
                'data' => $mealPlan,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create meal plan', [
                'error' => $e->getMessage(),
                'request_data' => $request->validated(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create meal plan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a meal plan.
     *
     * @param UpdateMealPlanRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateMealPlanRequest $request, int $id): JsonResponse
    {
        try {
            $mealPlan = $this->mealPlanService->updateMealPlan($id, $request->validated());

            if (!$mealPlan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meal plan not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Meal plan updated successfully',
                'data' => $mealPlan,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update meal plan', [
                'meal_plan_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update meal plan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a meal plan.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        try {
            $result = $this->mealPlanService->deleteMealPlan($id);

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meal plan not found or cannot be deleted',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Meal plan deleted successfully',
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete meal plan', [
                'meal_plan_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete meal plan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get meal plans compatible with dietary restrictions.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCompatiblePlans(Request $request): JsonResponse
    {
        $request->validate([
            'school_id' => 'required|integer|exists:schools,id',
            'dietary_restrictions' => 'required|array',
            'dietary_restrictions.*' => 'string',
            'meal_type' => 'nullable|in:breakfast,lunch,snack,dinner,combo',
        ]);

        try {
            $mealPlans = $this->mealPlanService->getCompatibleMealPlans(
                $request->input('school_id'),
                $request->input('dietary_restrictions'),
                $request->input('meal_type')
            );

            return response()->json([
                'success' => true,
                'data' => $mealPlans,
                'meta' => [
                    'school_id' => $request->input('school_id'),
                    'dietary_restrictions' => $request->input('dietary_restrictions'),
                    'compatible_plans_count' => $mealPlans->count(),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get compatible meal plans', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve compatible meal plans',
            ], 500);
        }
    }

    /**
     * Get meal plan pricing for different subscription types.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function getPricing(Request $request, int $id): JsonResponse
    {
        try {
            $pricing = $this->mealPlanService->getMealPlanPricing($id);

            if (!$pricing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meal plan not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $pricing,
                'meta' => [
                    'meal_plan_id' => $id,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get meal plan pricing', [
                'meal_plan_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve meal plan pricing',
            ], 500);
        }
    }

    /**
     * Update meal plan availability.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateAvailability(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'is_active' => 'required|boolean',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $mealPlan = $this->mealPlanService->updateMealPlanAvailability(
                $id,
                $request->input('is_active'),
                $request->input('reason')
            );

            if (!$mealPlan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meal plan not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Meal plan availability updated successfully',
                'data' => $mealPlan,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update meal plan availability', [
                'meal_plan_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update meal plan availability',
            ], 500);
        }
    }
}

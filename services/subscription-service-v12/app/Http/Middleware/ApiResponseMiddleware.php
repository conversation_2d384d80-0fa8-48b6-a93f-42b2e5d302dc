<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApiResponseMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        if ($response instanceof JsonResponse) {
            $data = $response->getData(true);

            if (!isset($data['success'])) {
                $success = $response->getStatusCode() >= 200 && $response->getStatusCode() < 300;
                $responseData = [
                    'success' => $success,
                ];

                if (isset($data['message'])) {
                    $responseData['message'] = $data['message'];
                }

                if (isset($data['data'])) {
                    $responseData['data'] = $data['data'];
                } elseif (!isset($data['message']) && !isset($data['errors'])) {
                    $responseData['data'] = $data;
                }

                if (isset($data['errors'])) {
                    $responseData['errors'] = $data['errors'];
                }

                $response->setData($responseData);
            }
        }

        return $response;
    }
}

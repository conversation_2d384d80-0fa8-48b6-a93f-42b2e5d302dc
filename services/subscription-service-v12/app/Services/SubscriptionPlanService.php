<?php

namespace App\Services;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubscriptionPlanService
{
    /**
     * Get all subscription plans.
     *
     * @param array $filters
     * @return Collection
     */
    public function getAllPlans(array $filters = []): Collection
    {
        $query = SubscriptionPlan::query();

        // Apply filters
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }

        if (isset($filters['plan_type'])) {
            $query->where('plan_type', $filters['plan_type']);
        }

        if (isset($filters['active_only']) && $filters['active_only']) {
            $query->active();
        }

        if (isset($filters['visible_to_customers']) && $filters['visible_to_customers']) {
            $query->visibleToCustomers();
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get a subscription plan by ID.
     *
     * @param int $id
     * @return SubscriptionPlan
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function getPlanById(int $id): SubscriptionPlan
    {
        return SubscriptionPlan::findOrFail($id);
    }

    /**
     * Create a new subscription plan.
     *
     * @param array $data
     * @return SubscriptionPlan
     * @throws \Exception
     */
    public function createPlan(array $data): SubscriptionPlan
    {
        try {
            DB::beginTransaction();

            $plan = SubscriptionPlan::create($data);

            DB::commit();

            return $plan;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create subscription plan', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update a subscription plan.
     *
     * @param int $id
     * @param array $data
     * @return SubscriptionPlan
     * @throws \Exception
     */
    public function updatePlan(int $id, array $data): SubscriptionPlan
    {
        try {
            DB::beginTransaction();

            $plan = $this->getPlanById($id);
            $plan->update($data);

            DB::commit();

            return $plan;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update subscription plan', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Delete a subscription plan.
     *
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function deletePlan(int $id): bool
    {
        try {
            DB::beginTransaction();

            $plan = $this->getPlanById($id);
            
            // Check if the plan has active subscriptions
            if ($plan->subscriptions()->active()->exists()) {
                throw new \Exception('Cannot delete plan with active subscriptions');
            }
            
            $plan->delete();

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete subscription plan', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);
            throw $e;
        }
    }

    /**
     * Get plans visible to customers.
     *
     * @param array $filters
     * @return Collection
     */
    public function getCustomerVisiblePlans(array $filters = []): Collection
    {
        $query = SubscriptionPlan::visibleToCustomers();

        // Apply filters
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }

        if (isset($filters['plan_type'])) {
            $query->where('plan_type', $filters['plan_type']);
        }

        return $query->orderBy('price', 'asc')->get();
    }

    /**
     * Activate a subscription plan.
     *
     * @param int $id
     * @return SubscriptionPlan
     * @throws \Exception
     */
    public function activatePlan(int $id): SubscriptionPlan
    {
        return $this->updatePlan($id, ['plan_status' => true]);
    }

    /**
     * Deactivate a subscription plan.
     *
     * @param int $id
     * @return SubscriptionPlan
     * @throws \Exception
     */
    public function deactivatePlan(int $id): SubscriptionPlan
    {
        return $this->updatePlan($id, ['plan_status' => false]);
    }

    /**
     * Get plans by type.
     *
     * @param string $type
     * @param bool $activeOnly
     * @return Collection
     */
    public function getPlansByType(string $type, bool $activeOnly = true): Collection
    {
        $query = SubscriptionPlan::ofType($type);

        if ($activeOnly) {
            $query->active();
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get active subscription plans.
     *
     * @param array $filters
     * @return Collection
     */
    public function getActivePlans(array $filters = []): Collection
    {
        $query = SubscriptionPlan::active();

        // Apply filters
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }

        if (isset($filters['plan_type'])) {
            $query->where('plan_type', $filters['plan_type']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }
}

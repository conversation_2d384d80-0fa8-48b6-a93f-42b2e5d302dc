<?php

namespace App\Services;

use App\Models\SubscriptionLog;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SubscriptionLogService
{
    /**
     * Log a subscription action.
     *
     * @param int $subscriptionId
     * @param int $customerId
     * @param string $action
     * @param string $description
     * @param array $data
     * @param Request|null $request
     * @return SubscriptionLog
     */
    public function logSubscriptionAction(
        int $subscriptionId,
        int $customerId,
        string $action,
        string $description,
        array $data = [],
        Request $request = null
    ): SubscriptionLog {
        try {
            $logData = [
                'subscription_id' => $subscriptionId,
                'customer_id' => $customerId,
                'action' => $action,
                'description' => $description,
                'data' => $data,
            ];

            // Add request information if available
            if ($request) {
                $logData['ip_address'] = $request->ip();
                $logData['user_agent'] = $request->userAgent();
            }

            return SubscriptionLog::create($logData);
        } catch (\Exception $e) {
            Log::error('Failed to log subscription action', [
                'error' => $e->getMessage(),
                'subscription_id' => $subscriptionId,
                'customer_id' => $customerId,
                'action' => $action,
                'description' => $description,
                'data' => $data
            ]);

            // Return a new instance without saving to the database
            return new SubscriptionLog($logData);
        }
    }

    /**
     * Get logs for a subscription.
     *
     * @param int $subscriptionId
     * @return Collection
     */
    public function getSubscriptionLogs(int $subscriptionId): Collection
    {
        return SubscriptionLog::forSubscription($subscriptionId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get logs for a customer.
     *
     * @param int $customerId
     * @param int|null $limit
     * @return Collection
     */
    public function getCustomerLogs(int $customerId, int $limit = null): Collection
    {
        $query = SubscriptionLog::forCustomer($customerId)
            ->orderBy('created_at', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get logs for a specific action.
     *
     * @param string $action
     * @param int|null $limit
     * @return Collection
     */
    public function getActionLogs(string $action, int $limit = null): Collection
    {
        $query = SubscriptionLog::forAction($action)
            ->orderBy('created_at', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Search logs.
     *
     * @param array $criteria
     * @param int|null $limit
     * @return Collection
     */
    public function searchLogs(array $criteria, int $limit = null): Collection
    {
        $query = SubscriptionLog::query();

        if (isset($criteria['subscription_id'])) {
            $query->where('subscription_id', $criteria['subscription_id']);
        }

        if (isset($criteria['customer_id'])) {
            $query->where('customer_id', $criteria['customer_id']);
        }

        if (isset($criteria['action'])) {
            $query->where('action', $criteria['action']);
        }

        if (isset($criteria['start_date'])) {
            $query->where('created_at', '>=', $criteria['start_date']);
        }

        if (isset($criteria['end_date'])) {
            $query->where('created_at', '<=', $criteria['end_date']);
        }

        $query->orderBy('created_at', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get recent logs.
     *
     * @param int $limit
     * @return Collection
     */
    public function getRecentLogs(int $limit = 10): Collection
    {
        return SubscriptionLog::orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}

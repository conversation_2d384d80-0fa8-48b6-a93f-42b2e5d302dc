<?php

namespace App\Services;

use App\Models\SchoolMealSubscription;
use App\Models\MealPlan;
use App\Models\ChildProfile;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * SchoolMealSubscriptionService
 *
 * Business logic for school meal subscription management including lifecycle,
 * billing, and performance tracking.
 */
class SchoolMealSubscriptionService
{
    /**
     * Get subscriptions with filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getSubscriptions(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = SchoolMealSubscription::with([
            'parentCustomer',
            'childProfile',
            'school',
            'mealPlan'
        ]);

        // Apply filters
        foreach ($filters as $key => $value) {
            if ($value !== null) {
                switch ($key) {
                    case 'start_date':
                        $query->where('start_date', '>=', $value);
                        break;
                    case 'end_date':
                        $query->where('end_date', '<=', $value);
                        break;
                    default:
                        $query->where($key, $value);
                        break;
                }
            }
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get a subscription by ID.
     *
     * @param int $id
     * @return SchoolMealSubscription|null
     */
    public function getSubscriptionById(int $id): ?SchoolMealSubscription
    {
        return SchoolMealSubscription::with([
            'parentCustomer',
            'childProfile.school',
            'school',
            'mealPlan',
            'deliveryItems' => function ($query) {
                $query->latest()->limit(10);
            }
        ])->find($id);
    }

    /**
     * Create a new school meal subscription.
     *
     * @param array $data
     * @return SchoolMealSubscription
     * @throws \Exception
     */
    public function createSubscription(array $data): SchoolMealSubscription
    {
        DB::beginTransaction();

        try {
            // Validate child profile and meal plan
            $childProfile = ChildProfile::with('school')->find($data['child_profile_id']);
            if (!$childProfile) {
                throw new \Exception('Child profile not found');
            }

            $mealPlan = MealPlan::with('school')->find($data['meal_plan_id']);
            if (!$mealPlan) {
                throw new \Exception('Meal plan not found');
            }

            // Validate school consistency
            if ($childProfile->school_id !== $mealPlan->school_id) {
                throw new \Exception('Child and meal plan must belong to the same school');
            }

            // Check meal plan availability
            if (!$mealPlan->isAvailableForSubscription()) {
                throw new \Exception('Meal plan is not available for subscription');
            }

            // Check for existing active subscription
            $existingSubscription = SchoolMealSubscription::where('child_profile_id', $data['child_profile_id'])
                ->where('status', 'active')
                ->first();

            if ($existingSubscription) {
                throw new \Exception('Child already has an active meal subscription');
            }

            // Generate subscription number
            $data['subscription_number'] = $this->generateSubscriptionNumber();
            $data['school_id'] = $childProfile->school_id;

            // Calculate pricing
            $pricingData = $this->calculateSubscriptionPricing($mealPlan, $data);
            $data = array_merge($data, $pricingData);

            // Set default values
            $data['status'] = 'pending';
            $data['total_meals_ordered'] = 0;
            $data['total_meals_delivered'] = 0;
            $data['total_meals_consumed'] = 0;
            $data['missed_deliveries'] = 0;
            $data['consumption_rate'] = 0.00;
            $data['billing_failures'] = 0;

            // Calculate next billing date
            $data['next_billing_date'] = $this->calculateNextBillingDate(
                $data['start_date'],
                $data['billing_cycle']
            );

            $subscription = SchoolMealSubscription::create($data);

            // Increment meal plan subscription count
            $mealPlan->incrementSubscriptions();

            DB::commit();

            return $subscription->load([
                'parentCustomer',
                'childProfile',
                'school',
                'mealPlan'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update a subscription.
     *
     * @param int $id
     * @param array $data
     * @return SchoolMealSubscription|null
     * @throws \Exception
     */
    public function updateSubscription(int $id, array $data): ?SchoolMealSubscription
    {
        DB::beginTransaction();

        try {
            $subscription = SchoolMealSubscription::find($id);

            if (!$subscription) {
                return null;
            }

            // Prevent changes to critical fields for active subscriptions
            if ($subscription->status === 'active') {
                $restrictedFields = ['child_profile_id', 'meal_plan_id', 'school_id'];
                foreach ($restrictedFields as $field) {
                    if (isset($data[$field]) && $data[$field] !== $subscription->$field) {
                        throw new \Exception("Cannot change {$field} for active subscription");
                    }
                }
            }

            // Recalculate pricing if meal plan or subscription type changes
            if (isset($data['meal_plan_id']) || isset($data['subscription_type'])) {
                $mealPlan = MealPlan::find($data['meal_plan_id'] ?? $subscription->meal_plan_id);
                $pricingData = $this->calculateSubscriptionPricing($mealPlan, array_merge($subscription->toArray(), $data));
                $data = array_merge($data, $pricingData);
            }

            $subscription->update($data);

            DB::commit();

            return $subscription->load([
                'parentCustomer',
                'childProfile',
                'school',
                'mealPlan'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Pause a subscription.
     *
     * @param int $id
     * @param string $reason
     * @param string|null $startDate
     * @param string|null $endDate
     * @return SchoolMealSubscription|null
     */
    public function pauseSubscription(int $id, string $reason, ?string $startDate = null, ?string $endDate = null): ?SchoolMealSubscription
    {
        $subscription = SchoolMealSubscription::find($id);

        if (!$subscription) {
            return null;
        }

        if ($subscription->status !== 'active') {
            throw new \Exception('Only active subscriptions can be paused');
        }

        $pauseStartDate = $startDate ? Carbon::parse($startDate) : now();
        $pauseEndDate = $endDate ? Carbon::parse($endDate) : null;

        $subscription->pause($reason, $pauseStartDate, $pauseEndDate);

        return $subscription->load([
            'parentCustomer',
            'childProfile',
            'school',
            'mealPlan'
        ]);
    }

    /**
     * Resume a paused subscription.
     *
     * @param int $id
     * @return SchoolMealSubscription|null
     */
    public function resumeSubscription(int $id): ?SchoolMealSubscription
    {
        $subscription = SchoolMealSubscription::find($id);

        if (!$subscription) {
            return null;
        }

        if ($subscription->status !== 'paused') {
            throw new \Exception('Only paused subscriptions can be resumed');
        }

        $subscription->resume();

        return $subscription->load([
            'parentCustomer',
            'childProfile',
            'school',
            'mealPlan'
        ]);
    }

    /**
     * Cancel a subscription.
     *
     * @param int $id
     * @param string $reason
     * @param string $type
     * @param bool $refundRequested
     * @return SchoolMealSubscription|null
     */
    public function cancelSubscription(int $id, string $reason, string $type = 'parent_request', bool $refundRequested = false): ?SchoolMealSubscription
    {
        DB::beginTransaction();

        try {
            $subscription = SchoolMealSubscription::find($id);

            if (!$subscription) {
                return null;
            }

            if (in_array($subscription->status, ['cancelled', 'expired'])) {
                throw new \Exception('Subscription is already cancelled or expired');
            }

            $subscription->cancel($reason, $type);

            // Process refund if requested
            if ($refundRequested) {
                $refundAmount = $this->calculateRefundAmount($subscription);
                if ($refundAmount > 0) {
                    // Integrate with payment service for refund processing
                    $subscription->update([
                        'refund_amount' => $refundAmount,
                        'refund_processed' => false, // Will be updated by payment service
                    ]);
                }
            }

            DB::commit();

            return $subscription->load([
                'parentCustomer',
                'childProfile',
                'school',
                'mealPlan'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get subscriptions for a parent.
     *
     * @param int $parentId
     * @param array $filters
     * @return Collection
     */
    public function getParentSubscriptions(int $parentId, array $filters = []): Collection
    {
        $query = SchoolMealSubscription::where('parent_customer_id', $parentId)
            ->with(['childProfile', 'school', 'mealPlan']);

        foreach ($filters as $key => $value) {
            if ($value !== null) {
                $query->where($key, $value);
            }
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get subscriptions for a child.
     *
     * @param int $childId
     * @return Collection
     */
    public function getChildSubscriptions(int $childId): Collection
    {
        return SchoolMealSubscription::where('child_profile_id', $childId)
            ->with(['parentCustomer', 'school', 'mealPlan'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Process billing for subscriptions.
     *
     * @param string|null $date
     * @param array|null $subscriptionIds
     * @return array
     */
    public function processBillingBatch(?string $date = null, ?array $subscriptionIds = null): array
    {
        $billingDate = $date ? Carbon::parse($date) : now();
        
        $query = SchoolMealSubscription::dueForBilling($billingDate);
        
        if ($subscriptionIds) {
            $query->whereIn('id', $subscriptionIds);
        }

        $subscriptions = $query->get();
        
        $results = [
            'total_processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'details' => []
        ];

        foreach ($subscriptions as $subscription) {
            $results['total_processed']++;
            
            try {
                $success = $subscription->processBilling();
                
                if ($success) {
                    $results['successful']++;
                    $results['details'][] = [
                        'subscription_id' => $subscription->id,
                        'status' => 'success',
                        'amount' => $subscription->calculateBillingAmount(),
                    ];
                } else {
                    $results['failed']++;
                    $results['details'][] = [
                        'subscription_id' => $subscription->id,
                        'status' => 'failed',
                        'error' => 'Payment processing failed',
                    ];
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['details'][] = [
                    'subscription_id' => $subscription->id,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Add feedback to a subscription.
     *
     * @param int $id
     * @param float $rating
     * @param string|null $feedback
     * @return SchoolMealSubscription|null
     */
    public function addSubscriptionFeedback(int $id, float $rating, ?string $feedback = null): ?SchoolMealSubscription
    {
        $subscription = SchoolMealSubscription::find($id);

        if (!$subscription) {
            return null;
        }

        $subscription->addParentFeedback($rating, $feedback);

        return $subscription->load([
            'parentCustomer',
            'childProfile',
            'school',
            'mealPlan'
        ]);
    }

    /**
     * Generate a unique subscription number.
     *
     * @return string
     */
    private function generateSubscriptionNumber(): string
    {
        $prefix = 'SMS-' . date('Y') . '-';
        $number = $prefix . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        
        while (SchoolMealSubscription::where('subscription_number', $number)->exists()) {
            $number = $prefix . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        }
        
        return $number;
    }

    /**
     * Calculate subscription pricing.
     *
     * @param MealPlan $mealPlan
     * @param array $data
     * @return array
     */
    private function calculateSubscriptionPricing(MealPlan $mealPlan, array $data): array
    {
        $subscriptionType = $data['subscription_type'] ?? 'monthly';
        $startDate = Carbon::parse($data['start_date']);
        $endDate = Carbon::parse($data['end_date']);
        
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $deliveryDays = $data['delivery_days'] ?? ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        
        // Calculate actual delivery days
        $actualDeliveryDays = 0;
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            if (in_array(strtolower($date->format('l')), $deliveryDays)) {
                $actualDeliveryDays++;
            }
        }
        
        $dailyRate = $mealPlan->getPriceForSubscriptionType($subscriptionType);
        $totalAmount = $dailyRate * $actualDeliveryDays;
        
        // Apply bulk discount if applicable
        if ($actualDeliveryDays >= $mealPlan->minimum_subscription_days) {
            $discountAmount = $totalAmount * ($mealPlan->bulk_discount_percentage / 100);
        } else {
            $discountAmount = 0;
        }
        
        // Calculate tax (assuming 5% GST)
        $taxableAmount = $totalAmount - $discountAmount;
        $taxAmount = $taxableAmount * 0.05;
        
        $finalAmount = $taxableAmount + $taxAmount;
        
        return [
            'daily_rate' => $dailyRate,
            'total_amount' => $totalAmount,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'final_amount' => $finalAmount,
        ];
    }

    /**
     * Calculate next billing date.
     *
     * @param string $startDate
     * @param string $billingCycle
     * @return string
     */
    private function calculateNextBillingDate(string $startDate, string $billingCycle): string
    {
        $date = Carbon::parse($startDate);
        
        switch ($billingCycle) {
            case 'daily':
                return $date->addDay()->toDateString();
            case 'weekly':
                return $date->addWeek()->toDateString();
            case 'monthly':
                return $date->addMonth()->toDateString();
            case 'quarterly':
                return $date->addMonths(3)->toDateString();
            default:
                return $date->addMonth()->toDateString();
        }
    }

    /**
     * Calculate refund amount for cancelled subscription.
     *
     * @param SchoolMealSubscription $subscription
     * @return float
     */
    private function calculateRefundAmount(SchoolMealSubscription $subscription): float
    {
        $remainingDays = now()->diffInDays($subscription->end_date, false);
        
        if ($remainingDays <= 0) {
            return 0;
        }
        
        $totalDays = $subscription->start_date->diffInDays($subscription->end_date) + 1;
        $refundPercentage = $remainingDays / $totalDays;
        
        // Apply cancellation fee (e.g., 10% of remaining amount)
        $cancellationFee = 0.10;
        $refundAmount = $subscription->final_amount * $refundPercentage * (1 - $cancellationFee);
        
        return max(0, $refundAmount);
    }
}

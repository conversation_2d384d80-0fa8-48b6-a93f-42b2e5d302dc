<?php

namespace App\Services;

use App\Models\MealPlan;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * MealPlanService
 *
 * Business logic for meal plan management in the school tiffin system.
 * Handles meal plan CRUD operations, pricing, and dietary compatibility.
 */
class MealPlanService
{
    /**
     * Get meal plans with filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getMealPlans(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = MealPlan::with(['school']);

        // Apply filters
        if (isset($filters['school_id'])) {
            $query->where('school_id', $filters['school_id']);
        }

        if (isset($filters['meal_type'])) {
            $query->where('meal_type', $filters['meal_type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Dietary filters
        $dietaryFilters = [
            'is_vegetarian', 'is_vegan', 'is_jain', 
            'is_gluten_free', 'is_dairy_free', 'is_nut_free'
        ];

        foreach ($dietaryFilters as $filter) {
            if (isset($filters[$filter]) && $filters[$filter]) {
                $query->where($filter, true);
            }
        }

        // Price range filters
        if (isset($filters['min_price'])) {
            $query->where('base_price', '>=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $query->where('base_price', '<=', $filters['max_price']);
        }

        // Available day filter
        if (isset($filters['available_day'])) {
            $query->whereJsonContains('available_days', strtolower($filters['available_day']));
        }

        return $query->orderBy('plan_name')->paginate($perPage);
    }

    /**
     * Get meal plans for a specific school.
     *
     * @param int $schoolId
     * @param array $filters
     * @return Collection
     */
    public function getSchoolMealPlans(int $schoolId, array $filters = []): Collection
    {
        $query = MealPlan::where('school_id', $schoolId)
            ->with(['school', 'schoolMealSubscriptions' => function ($q) {
                $q->where('status', 'active');
            }]);

        // Apply additional filters
        if (isset($filters['meal_type'])) {
            $query->where('meal_type', $filters['meal_type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Dietary preferences filter
        if (isset($filters['dietary_preferences']) && is_array($filters['dietary_preferences'])) {
            $query->withDietaryPreferences($filters['dietary_preferences']);
        }

        // Price range
        if (isset($filters['min_price']) && isset($filters['max_price'])) {
            $query->inPriceRange($filters['min_price'], $filters['max_price']);
        }

        return $query->active()->orderBy('is_featured', 'desc')->orderBy('plan_name')->get();
    }

    /**
     * Get a meal plan by ID.
     *
     * @param int $id
     * @return MealPlan|null
     */
    public function getMealPlanById(int $id): ?MealPlan
    {
        return MealPlan::with([
            'school',
            'schoolMealSubscriptions' => function ($query) {
                $query->where('status', 'active')->with('childProfile');
            }
        ])->find($id);
    }

    /**
     * Create a new meal plan.
     *
     * @param array $data
     * @return MealPlan
     * @throws \Exception
     */
    public function createMealPlan(array $data): MealPlan
    {
        DB::beginTransaction();

        try {
            // Generate unique plan code
            $data['plan_code'] = $this->generatePlanCode($data['plan_name'], $data['school_id']);

            // Set default values
            $data['current_subscriptions'] = 0;
            $data['average_rating'] = 0.00;
            $data['total_ratings'] = 0;

            // Validate school exists and is active
            $school = \App\Models\School::where('id', $data['school_id'])
                ->where('is_active', true)
                ->where('partnership_status', 'active')
                ->first();

            if (!$school) {
                throw new \Exception('School not found or not available for meal plans');
            }

            $mealPlan = MealPlan::create($data);

            DB::commit();

            return $mealPlan->load('school');
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update a meal plan.
     *
     * @param int $id
     * @param array $data
     * @return MealPlan|null
     * @throws \Exception
     */
    public function updateMealPlan(int $id, array $data): ?MealPlan
    {
        DB::beginTransaction();

        try {
            $mealPlan = MealPlan::find($id);

            if (!$mealPlan) {
                return null;
            }

            // Check if there are active subscriptions before making major changes
            if (isset($data['school_id']) && $data['school_id'] !== $mealPlan->school_id) {
                $activeSubscriptions = $mealPlan->schoolMealSubscriptions()
                    ->where('status', 'active')
                    ->count();

                if ($activeSubscriptions > 0) {
                    throw new \Exception('Cannot change school while meal plan has active subscriptions');
                }
            }

            // Update plan code if name or school changes
            if (isset($data['plan_name']) && $data['plan_name'] !== $mealPlan->plan_name) {
                $data['plan_code'] = $this->generatePlanCode($data['plan_name'], $mealPlan->school_id);
            }

            $mealPlan->update($data);

            DB::commit();

            return $mealPlan->load('school');
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete a meal plan.
     *
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function deleteMealPlan(int $id): bool
    {
        DB::beginTransaction();

        try {
            $mealPlan = MealPlan::find($id);

            if (!$mealPlan) {
                return false;
            }

            // Check for active subscriptions
            $activeSubscriptions = $mealPlan->schoolMealSubscriptions()
                ->where('status', 'active')
                ->count();

            if ($activeSubscriptions > 0) {
                throw new \Exception('Cannot delete meal plan with active subscriptions');
            }

            $mealPlan->delete();

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get meal plans compatible with dietary restrictions.
     *
     * @param int $schoolId
     * @param array $dietaryRestrictions
     * @param string|null $mealType
     * @return Collection
     */
    public function getCompatibleMealPlans(int $schoolId, array $dietaryRestrictions, ?string $mealType = null): Collection
    {
        $query = MealPlan::where('school_id', $schoolId)
            ->where('is_active', true)
            ->with('school');

        if ($mealType) {
            $query->where('meal_type', $mealType);
        }

        $mealPlans = $query->get();

        // Filter by dietary restrictions compatibility
        return $mealPlans->filter(function ($mealPlan) use ($dietaryRestrictions) {
            return $mealPlan->isCompatibleWithDietaryRestrictions($dietaryRestrictions);
        });
    }

    /**
     * Get meal plan pricing for different subscription types.
     *
     * @param int $id
     * @return array|null
     */
    public function getMealPlanPricing(int $id): ?array
    {
        $mealPlan = MealPlan::find($id);

        if (!$mealPlan) {
            return null;
        }

        $pricing = [
            'base_price' => $mealPlan->base_price,
            'pricing_structure' => $mealPlan->pricing_structure,
            'bulk_discount_percentage' => $mealPlan->bulk_discount_percentage,
            'minimum_subscription_days' => $mealPlan->minimum_subscription_days,
        ];

        // Calculate sample pricing for different periods
        $pricing['sample_calculations'] = [
            'daily' => $mealPlan->base_price,
            'weekly' => $mealPlan->getDiscountedPrice(7),
            'monthly' => $mealPlan->getDiscountedPrice(30),
            'quarterly' => $mealPlan->getDiscountedPrice(90),
        ];

        return $pricing;
    }

    /**
     * Update meal plan availability.
     *
     * @param int $id
     * @param bool $isActive
     * @param string|null $reason
     * @return MealPlan|null
     */
    public function updateMealPlanAvailability(int $id, bool $isActive, ?string $reason = null): ?MealPlan
    {
        $mealPlan = MealPlan::find($id);

        if (!$mealPlan) {
            return null;
        }

        $updateData = ['is_active' => $isActive];

        // If deactivating, add reason to special features
        if (!$isActive && $reason) {
            $specialFeatures = $mealPlan->special_features ?? [];
            $specialFeatures['deactivation_reason'] = $reason;
            $specialFeatures['deactivated_at'] = now()->toISOString();
            $updateData['special_features'] = $specialFeatures;
        }

        $mealPlan->update($updateData);

        return $mealPlan->load('school');
    }

    /**
     * Generate a unique plan code.
     *
     * @param string $planName
     * @param int $schoolId
     * @return string
     */
    private function generatePlanCode(string $planName, int $schoolId): string
    {
        $baseCode = 'MP-' . $schoolId . '-' . Str::upper(Str::substr(Str::slug($planName), 0, 6));
        $code = $baseCode;
        $counter = 1;

        while (MealPlan::where('plan_code', $code)->exists()) {
            $code = $baseCode . '-' . $counter;
            $counter++;
        }

        return $code;
    }

    /**
     * Get meal plan statistics for a school.
     *
     * @param int $schoolId
     * @return array
     */
    public function getSchoolMealPlanStatistics(int $schoolId): array
    {
        $mealPlans = MealPlan::where('school_id', $schoolId)->get();

        return [
            'total_plans' => $mealPlans->count(),
            'active_plans' => $mealPlans->where('is_active', true)->count(),
            'featured_plans' => $mealPlans->where('is_featured', true)->count(),
            'meal_types' => $mealPlans->groupBy('meal_type')->map->count(),
            'dietary_options' => [
                'vegetarian' => $mealPlans->where('is_vegetarian', true)->count(),
                'vegan' => $mealPlans->where('is_vegan', true)->count(),
                'jain' => $mealPlans->where('is_jain', true)->count(),
                'gluten_free' => $mealPlans->where('is_gluten_free', true)->count(),
                'dairy_free' => $mealPlans->where('is_dairy_free', true)->count(),
                'nut_free' => $mealPlans->where('is_nut_free', true)->count(),
            ],
            'price_range' => [
                'min' => $mealPlans->min('base_price'),
                'max' => $mealPlans->max('base_price'),
                'average' => $mealPlans->avg('base_price'),
            ],
            'total_subscriptions' => $mealPlans->sum('current_subscriptions'),
            'average_rating' => $mealPlans->where('total_ratings', '>', 0)->avg('average_rating'),
        ];
    }

    /**
     * Search meal plans by name or description.
     *
     * @param string $searchTerm
     * @param int|null $schoolId
     * @return Collection
     */
    public function searchMealPlans(string $searchTerm, ?int $schoolId = null): Collection
    {
        $query = MealPlan::where(function ($q) use ($searchTerm) {
            $q->where('plan_name', 'LIKE', "%{$searchTerm}%")
              ->orWhere('description', 'LIKE', "%{$searchTerm}%")
              ->orWhere('short_description', 'LIKE', "%{$searchTerm}%");
        })->where('is_active', true);

        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        return $query->with('school')->orderBy('plan_name')->get();
    }
}

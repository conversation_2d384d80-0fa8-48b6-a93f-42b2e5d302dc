<?php

namespace App\Services;

use App\Models\Subscription;
use App\Models\SubscriptionItem;
use App\Models\SubscriptionPlan;
use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\Request;

class SubscriptionService
{
    /**
     * @var SubscriptionLogService
     */
    protected $logService;

    /**
     * Create a new service instance.
     *
     * @param SubscriptionLogService $logService
     * @return void
     */
    public function __construct(SubscriptionLogService $logService)
    {
        $this->logService = $logService;
    }

    /**
     * Get all subscriptions.
     *
     * @param array $filters
     * @return Collection
     */
    public function getAllSubscriptions(array $filters = []): Collection
    {
        $query = Subscription::query();

        // Apply filters
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['plan_id'])) {
            $query->where('plan_id', $filters['plan_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        return $query->with(['customer', 'plan'])->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get a subscription by ID.
     *
     * @param int $id
     * @return Subscription
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function getSubscriptionById(int $id): Subscription
    {
        return Subscription::with(['customer', 'plan', 'items'])->findOrFail($id);
    }

    /**
     * Create a new subscription.
     *
     * @param array $data
     * @param array $items
     * @param Request|null $request
     * @return Subscription
     * @throws \Exception
     */
    public function createSubscription(array $data, array $items = [], Request $request = null): Subscription
    {
        try {
            DB::beginTransaction();

            // Get the plan
            $plan = SubscriptionPlan::findOrFail($data['plan_id']);

            // Generate subscription number
            $data['subscription_no'] = $this->generateSubscriptionNumber();

            // Set default status
            $data['status'] = $data['status'] ?? 'pending';

            // Set dates based on plan
            if (!isset($data['start_date'])) {
                $data['start_date'] = now();
            }

            if (!isset($data['end_date'])) {
                // Calculate end date based on plan period
                $data['end_date'] = $this->calculateEndDate($data['start_date'], $plan->plan_period);
            }

            // Set next billing date for recurring subscriptions
            if ($plan->is_recurring && !isset($data['next_billing_date'])) {
                $data['next_billing_date'] = $data['end_date'];
            }

            // Create the subscription
            $subscription = Subscription::create($data);

            // Create subscription items if provided
            if (!empty($items)) {
                foreach ($items as $item) {
                    $item['subscription_id'] = $subscription->id;
                    $item['company_id'] = $data['company_id'];
                    $item['unit_id'] = $data['unit_id'];
                    SubscriptionItem::create($item);
                }
            }

            // Log the subscription creation
            $this->logService->logSubscriptionAction(
                $subscription->id,
                $subscription->customer_id,
                'create',
                'Subscription created',
                $data,
                $request
            );

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create subscription', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update a subscription.
     *
     * @param int $id
     * @param array $data
     * @param Request|null $request
     * @return Subscription
     * @throws \Exception
     */
    public function updateSubscription(int $id, array $data, Request $request = null): Subscription
    {
        try {
            DB::beginTransaction();

            $subscription = $this->getSubscriptionById($id);
            $subscription->update($data);

            // Update subscription items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                // Delete existing items if replace_items is true
                if (isset($data['replace_items']) && $data['replace_items']) {
                    $subscription->items()->delete();
                }

                foreach ($data['items'] as $item) {
                    if (isset($item['id'])) {
                        // Update existing item
                        $subscriptionItem = SubscriptionItem::find($item['id']);
                        if ($subscriptionItem && $subscriptionItem->subscription_id == $subscription->id) {
                            $subscriptionItem->update($item);
                        }
                    } else {
                        // Create new item
                        $item['subscription_id'] = $subscription->id;
                        SubscriptionItem::create($item);
                    }
                }
            }

            // Log the subscription update
            $this->logService->logSubscriptionAction(
                $subscription->id,
                $subscription->customer_id,
                'update',
                'Subscription updated',
                $data,
                $request
            );

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update subscription', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Cancel a subscription.
     *
     * @param int $id
     * @param array $data
     * @param Request|null $request
     * @return Subscription
     * @throws \Exception
     */
    public function cancelSubscription(int $id, array $data = [], Request $request = null): Subscription
    {
        try {
            DB::beginTransaction();

            $subscription = $this->getSubscriptionById($id);
            
            // Only active or paused subscriptions can be cancelled
            if (!in_array($subscription->status, ['active', 'paused'])) {
                throw new \Exception('Only active or paused subscriptions can be cancelled');
            }

            $updateData = [
                'status' => 'cancelled',
                'notes' => $data['notes'] ?? $subscription->notes
            ];

            $subscription->update($updateData);

            // Log the subscription cancellation
            $this->logService->logSubscriptionAction(
                $subscription->id,
                $subscription->customer_id,
                'cancel',
                'Subscription cancelled',
                $data,
                $request
            );

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel subscription', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Pause a subscription.
     *
     * @param int $id
     * @param array $data
     * @param Request|null $request
     * @return Subscription
     * @throws \Exception
     */
    public function pauseSubscription(int $id, array $data, Request $request = null): Subscription
    {
        try {
            DB::beginTransaction();

            $subscription = $this->getSubscriptionById($id);
            
            // Only active subscriptions can be paused
            if ($subscription->status !== 'active') {
                throw new \Exception('Only active subscriptions can be paused');
            }

            // Get pause history
            $pauseHistory = $subscription->pause_history ?? [];
            
            // Add new pause entry
            $pauseEntry = [
                'paused_at' => now()->toDateTimeString(),
                'resume_at' => $data['resume_at'] ?? null,
                'reason' => $data['reason'] ?? null
            ];
            
            $pauseHistory[] = $pauseEntry;

            $updateData = [
                'status' => 'paused',
                'pause_history' => $pauseHistory,
                'notes' => $data['notes'] ?? $subscription->notes
            ];

            $subscription->update($updateData);

            // Log the subscription pause
            $this->logService->logSubscriptionAction(
                $subscription->id,
                $subscription->customer_id,
                'pause',
                'Subscription paused',
                $data,
                $request
            );

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to pause subscription', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Resume a paused subscription.
     *
     * @param int $id
     * @param array $data
     * @param Request|null $request
     * @return Subscription
     * @throws \Exception
     */
    public function resumeSubscription(int $id, array $data = [], Request $request = null): Subscription
    {
        try {
            DB::beginTransaction();

            $subscription = $this->getSubscriptionById($id);
            
            // Only paused subscriptions can be resumed
            if ($subscription->status !== 'paused') {
                throw new \Exception('Only paused subscriptions can be resumed');
            }

            // Get pause history
            $pauseHistory = $subscription->pause_history ?? [];
            
            // Update the last pause entry
            if (!empty($pauseHistory)) {
                $lastIndex = count($pauseHistory) - 1;
                $pauseHistory[$lastIndex]['resumed_at'] = now()->toDateTimeString();
            }

            $updateData = [
                'status' => 'active',
                'pause_history' => $pauseHistory,
                'notes' => $data['notes'] ?? $subscription->notes
            ];

            $subscription->update($updateData);

            // Log the subscription resume
            $this->logService->logSubscriptionAction(
                $subscription->id,
                $subscription->customer_id,
                'resume',
                'Subscription resumed',
                $data,
                $request
            );

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to resume subscription', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Renew a subscription.
     *
     * @param int $id
     * @param array $data
     * @param Request|null $request
     * @return Subscription
     * @throws \Exception
     */
    public function renewSubscription(int $id, array $data = [], Request $request = null): Subscription
    {
        try {
            DB::beginTransaction();

            $subscription = $this->getSubscriptionById($id);
            $plan = $subscription->plan;
            
            // Calculate new dates
            $newStartDate = $data['start_date'] ?? now();
            $newEndDate = $this->calculateEndDate($newStartDate, $plan->plan_period);
            
            $updateData = [
                'status' => 'active',
                'start_date' => $newStartDate,
                'end_date' => $newEndDate,
                'next_billing_date' => $plan->is_recurring ? $newEndDate : null,
                'notes' => $data['notes'] ?? $subscription->notes
            ];

            // Update payment information if provided
            if (isset($data['payment_method'])) {
                $updateData['payment_method'] = $data['payment_method'];
            }
            
            if (isset($data['payment_status'])) {
                $updateData['payment_status'] = $data['payment_status'];
            }
            
            if (isset($data['transaction_id'])) {
                $updateData['transaction_id'] = $data['transaction_id'];
            }

            $subscription->update($updateData);

            // Log the subscription renewal
            $this->logService->logSubscriptionAction(
                $subscription->id,
                $subscription->customer_id,
                'renew',
                'Subscription renewed',
                $data,
                $request
            );

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to renew subscription', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Get customer subscriptions.
     *
     * @param int $customerId
     * @param array $filters
     * @return Collection
     */
    public function getCustomerSubscriptions(int $customerId, array $filters = []): Collection
    {
        $query = Subscription::forCustomer($customerId);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['plan_id'])) {
            $query->where('plan_id', $filters['plan_id']);
        }

        return $query->with(['plan', 'items'])->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get active customer subscriptions.
     *
     * @param int $customerId
     * @return Collection
     */
    public function getActiveCustomerSubscriptions(int $customerId): Collection
    {
        return Subscription::forCustomer($customerId)
            ->active()
            ->with(['plan', 'items'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Process subscription payment.
     *
     * @param int $id
     * @param array $paymentData
     * @param Request|null $request
     * @return Subscription
     * @throws \Exception
     */
    public function processPayment(int $id, array $paymentData, Request $request = null): Subscription
    {
        try {
            DB::beginTransaction();

            $subscription = $this->getSubscriptionById($id);
            
            $updateData = [
                'payment_method' => $paymentData['payment_method'],
                'payment_status' => $paymentData['payment_status'],
                'transaction_id' => $paymentData['transaction_id'] ?? null,
            ];

            // If payment is successful, activate the subscription
            if ($paymentData['payment_status'] === 'completed') {
                $updateData['status'] = 'active';
            }

            $subscription->update($updateData);

            // Log the payment processing
            $this->logService->logSubscriptionAction(
                $subscription->id,
                $subscription->customer_id,
                'payment',
                'Subscription payment processed',
                $paymentData,
                $request
            );

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process subscription payment', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $paymentData
            ]);
            throw $e;
        }
    }

    /**
     * Get subscription logs.
     *
     * @param int $id
     * @return Collection|null
     */
    public function getSubscriptionLogs(int $id): ?Collection
    {
        $subscription = Subscription::find($id);

        if (!$subscription) {
            return null;
        }

        return $this->logService->getSubscriptionLogs($id);
    }

    /**
     * Generate a unique subscription number.
     *
     * @return string
     */
    protected function generateSubscriptionNumber(): string
    {
        $prefix = 'SUB';
        $timestamp = now()->format('YmdHis');
        $random = Str::random(4);

        return $prefix . $timestamp . $random;
    }

    /**
     * Calculate the end date based on the start date and plan period.
     *
     * @param string|\DateTime $startDate
     * @param string $planPeriod
     * @return \DateTime
     */
    protected function calculateEndDate($startDate, string $planPeriod): \DateTime
    {
        if (is_string($startDate)) {
            $startDate = new \DateTime($startDate);
        }
        
        $endDate = clone $startDate;
        
        switch ($planPeriod) {
            case 'daily':
                $endDate->modify('+1 day');
                break;
            case 'weekly':
                $endDate->modify('+1 week');
                break;
            case 'monthly':
                $endDate->modify('+1 month');
                break;
            case 'quarterly':
                $endDate->modify('+3 months');
                break;
            case 'biannual':
                $endDate->modify('+6 months');
                break;
            case 'annual':
                $endDate->modify('+1 year');
                break;
            default:
                $endDate->modify('+30 days');
                break;
        }
        
        return $endDate;
    }
}

<?php

namespace App\Providers;

use App\Services\SubscriptionPlanService;
use App\Services\SubscriptionService;
use App\Services\SubscriptionLogService;
use Illuminate\Support\ServiceProvider;

class SubscriptionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(SubscriptionPlanService::class, function ($app) {
            return new SubscriptionPlanService();
        });

        $this->app->singleton(SubscriptionLogService::class, function ($app) {
            return new SubscriptionLogService();
        });

        $this->app->singleton(SubscriptionService::class, function ($app) {
            return new SubscriptionService(
                $app->make(SubscriptionLogService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

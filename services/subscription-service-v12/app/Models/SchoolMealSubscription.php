<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

/**
 * SchoolMealSubscription Model
 *
 * Represents a school meal subscription for a specific child.
 * Handles recurring billing, delivery scheduling, and subscription lifecycle.
 */
class SchoolMealSubscription extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'school_meal_subscriptions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'company_id',
        'unit_id',
        'parent_customer_id',
        'child_profile_id',
        'school_id',
        'meal_plan_id',
        'subscription_number',
        'start_date',
        'end_date',
        'subscription_type',
        'status',
        'daily_rate',
        'total_amount',
        'discount_amount',
        'tax_amount',
        'final_amount',
        'billing_cycle',
        'next_billing_date',
        'last_billing_date',
        'auto_renew',
        'billing_failures',
        'last_billing_failure_date',
        'delivery_days',
        'preferred_break_time',
        'delivery_time_preference',
        'delivery_instructions',
        'meal_customizations',
        'dietary_accommodations',
        'spice_level',
        'total_meals_ordered',
        'total_meals_delivered',
        'total_meals_consumed',
        'missed_deliveries',
        'consumption_rate',
        'pause_history',
        'pause_start_date',
        'pause_end_date',
        'pause_reason',
        'parent_rating',
        'parent_feedback',
        'last_feedback_date',
        'emergency_instructions',
        'special_notes',
        'requires_special_handling',
        'cancellation_date',
        'cancellation_reason',
        'cancellation_type',
        'refund_processed',
        'refund_amount',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'next_billing_date' => 'date',
        'last_billing_date' => 'date',
        'last_billing_failure_date' => 'date',
        'pause_start_date' => 'date',
        'pause_end_date' => 'date',
        'last_feedback_date' => 'date',
        'cancellation_date' => 'date',
        'delivery_days' => 'array',
        'delivery_instructions' => 'array',
        'meal_customizations' => 'array',
        'dietary_accommodations' => 'array',
        'pause_history' => 'array',
        'daily_rate' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'consumption_rate' => 'decimal:2',
        'parent_rating' => 'decimal:2',
        'auto_renew' => 'boolean',
        'requires_special_handling' => 'boolean',
        'refund_processed' => 'boolean',
        'delivery_time_preference' => 'datetime:H:i',
    ];

    /**
     * Get the parent customer that owns this subscription.
     */
    public function parentCustomer(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Customer::class, 'parent_customer_id', 'pk_customer_code');
    }

    /**
     * Get the child profile for this subscription.
     */
    public function childProfile(): BelongsTo
    {
        return $this->belongsTo(\App\Models\ChildProfile::class);
    }

    /**
     * Get the school for this subscription.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School::class);
    }

    /**
     * Get the meal plan for this subscription.
     */
    public function mealPlan(): BelongsTo
    {
        return $this->belongsTo(MealPlan::class);
    }

    /**
     * Get the delivery items for this subscription.
     */
    public function deliveryItems(): HasMany
    {
        return $this->hasMany(\App\Models\SchoolDeliveryItem::class, 'subscription_id');
    }

    /**
     * Scope a query to only include active subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now());
    }

    /**
     * Scope a query to filter by parent customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $parentId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForParent($query, $parentId)
    {
        return $query->where('parent_customer_id', $parentId);
    }

    /**
     * Scope a query to filter by child.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $childId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForChild($query, $childId)
    {
        return $query->where('child_profile_id', $childId);
    }

    /**
     * Scope a query to filter by school.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $schoolId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope a query to filter subscriptions due for billing.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \Carbon\Carbon  $date
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDueForBilling($query, $date = null)
    {
        $date = $date ?? now();
        
        return $query->where('status', 'active')
            ->where('next_billing_date', '<=', $date->toDateString());
    }

    /**
     * Scope a query to filter by tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $tenantId
     * @param  int  $companyId
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, $tenantId, $companyId = null, $unitId = null)
    {
        $query->where('tenant_id', $tenantId);
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($unitId) {
            $query->where('unit_id', $unitId);
        }
        
        return $query;
    }

    /**
     * Check if the subscription is currently active.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->status === 'active' &&
               $this->start_date <= now() &&
               $this->end_date >= now();
    }

    /**
     * Check if the subscription is paused.
     *
     * @return bool
     */
    public function isPaused()
    {
        return $this->status === 'paused' ||
               ($this->pause_start_date && $this->pause_start_date <= now() &&
                (!$this->pause_end_date || $this->pause_end_date >= now()));
    }

    /**
     * Check if delivery is scheduled for a specific date.
     *
     * @param  \Carbon\Carbon  $date
     * @return bool
     */
    public function isDeliveryScheduledFor($date)
    {
        $dayOfWeek = strtolower($date->format('l'));
        return in_array($dayOfWeek, $this->delivery_days ?? []);
    }

    /**
     * Pause the subscription.
     *
     * @param  string  $reason
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return void
     */
    public function pause($reason, $startDate = null, $endDate = null)
    {
        $startDate = $startDate ?? now();
        
        $pauseRecord = [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate ? $endDate->toDateString() : null,
            'reason' => $reason,
            'paused_at' => now()->toDateTimeString(),
        ];
        
        $pauseHistory = $this->pause_history ?? [];
        $pauseHistory[] = $pauseRecord;
        
        $this->update([
            'status' => 'paused',
            'pause_start_date' => $startDate,
            'pause_end_date' => $endDate,
            'pause_reason' => $reason,
            'pause_history' => $pauseHistory,
        ]);
    }

    /**
     * Resume the subscription.
     *
     * @return void
     */
    public function resume()
    {
        // Update the last pause record in history
        $pauseHistory = $this->pause_history ?? [];
        if (!empty($pauseHistory)) {
            $lastIndex = count($pauseHistory) - 1;
            $pauseHistory[$lastIndex]['resumed_at'] = now()->toDateTimeString();
        }
        
        $this->update([
            'status' => 'active',
            'pause_start_date' => null,
            'pause_end_date' => null,
            'pause_reason' => null,
            'pause_history' => $pauseHistory,
        ]);
    }

    /**
     * Cancel the subscription.
     *
     * @param  string  $reason
     * @param  string  $type
     * @return void
     */
    public function cancel($reason, $type = 'parent_request')
    {
        $this->update([
            'status' => 'cancelled',
            'cancellation_date' => now(),
            'cancellation_reason' => $reason,
            'cancellation_type' => $type,
        ]);
        
        // Decrement meal plan subscription count
        $this->mealPlan->decrementSubscriptions();
    }

    /**
     * Calculate the next billing date based on billing cycle.
     *
     * @return \Carbon\Carbon
     */
    public function calculateNextBillingDate()
    {
        $lastBilling = $this->last_billing_date ?? $this->start_date;
        
        switch ($this->billing_cycle) {
            case 'daily':
                return $lastBilling->addDay();
            case 'weekly':
                return $lastBilling->addWeek();
            case 'monthly':
                return $lastBilling->addMonth();
            case 'quarterly':
                return $lastBilling->addMonths(3);
            default:
                return $lastBilling->addMonth();
        }
    }

    /**
     * Process billing for this subscription.
     *
     * @return bool
     */
    public function processBilling()
    {
        try {
            // Calculate billing amount based on cycle
            $amount = $this->calculateBillingAmount();

            if ($amount <= 0) {
                \Log::info('Skipping billing for zero amount', [
                    'subscription_id' => $this->id,
                    'amount' => $amount,
                ]);
                return true;
            }

            // Get customer data for payment
            $customer = $this->customer;
            if (!$customer) {
                throw new \Exception('Customer not found for subscription');
            }

            // Process payment through payment service
            $paymentClient = app(\App\Services\PaymentServiceClient::class);

            $paymentResponse = $paymentClient->processSubscriptionPayment(
                subscriptionId: $this->id,
                amount: $amount,
                currency: $this->currency ?? 'INR',
                paymentMethod: $this->payment_method ?? 'payu',
                customerData: [
                    'customer_id' => $customer->id,
                    'name' => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                ],
                metadata: [
                    'billing_cycle' => $this->billing_cycle,
                    'billing_period' => $this->current_billing_period,
                    'school_id' => $this->school_id,
                    'meal_plan_id' => $this->meal_plan_id,
                ]
            );

            // Update billing dates and payment info
            $this->update([
                'last_billing_date' => now(),
                'next_billing_date' => $this->calculateNextBillingDate(),
                'billing_failures' => 0,
                'last_payment_id' => $paymentResponse->paymentId,
                'last_payment_status' => $paymentResponse->status,
                'last_billing_amount' => $amount,
            ]);

            // Fire billing processed event
            event(new \App\Events\SubscriptionBillingProcessed($this, $paymentResponse));

            \Log::info('Subscription billing processed successfully', [
                'subscription_id' => $this->id,
                'payment_id' => $paymentResponse->paymentId,
                'amount' => $amount,
                'status' => $paymentResponse->status,
            ]);

            return true;

        } catch (\Exception $e) {
            // Handle billing failure
            $this->update([
                'billing_failures' => $this->billing_failures + 1,
                'last_billing_failure_date' => now(),
                'last_billing_failure_reason' => $e->getMessage(),
            ]);

            \Log::error('Subscription billing failed', [
                'subscription_id' => $this->id,
                'error' => $e->getMessage(),
                'failure_count' => $this->billing_failures + 1,
            ]);

            // Suspend subscription after 3 failures
            if ($this->billing_failures >= 3) {
                $this->update(['status' => 'suspended']);

                // Fire subscription suspended event
                event(new \App\Events\SubscriptionSuspended($this, 'billing_failures'));

                \Log::warning('Subscription suspended due to billing failures', [
                    'subscription_id' => $this->id,
                    'failure_count' => $this->billing_failures,
                ]);
            }

            return false;
        }
    }

    /**
     * Calculate the billing amount for the current cycle.
     *
     * @return float
     */
    public function calculateBillingAmount()
    {
        $daysInCycle = $this->getDaysInBillingCycle();
        $deliveryDaysInCycle = $this->getDeliveryDaysInCycle($daysInCycle);
        
        return $this->daily_rate * $deliveryDaysInCycle;
    }

    /**
     * Get the number of days in the current billing cycle.
     *
     * @return int
     */
    private function getDaysInBillingCycle()
    {
        switch ($this->billing_cycle) {
            case 'daily':
                return 1;
            case 'weekly':
                return 7;
            case 'monthly':
                return 30; // Approximate
            case 'quarterly':
                return 90; // Approximate
            default:
                return 30;
        }
    }

    /**
     * Get the number of delivery days in the billing cycle.
     *
     * @param  int  $totalDays
     * @return int
     */
    private function getDeliveryDaysInCycle($totalDays)
    {
        $deliveryDaysPerWeek = count($this->delivery_days ?? []);
        $weeks = ceil($totalDays / 7);
        
        return $deliveryDaysPerWeek * $weeks;
    }

    /**
     * Update consumption metrics.
     *
     * @param  bool  $consumed
     * @return void
     */
    public function updateConsumptionMetrics($consumed = true)
    {
        $this->increment('total_meals_delivered');
        
        if ($consumed) {
            $this->increment('total_meals_consumed');
        }
        
        // Recalculate consumption rate
        $rate = $this->total_meals_delivered > 0 
            ? ($this->total_meals_consumed / $this->total_meals_delivered) * 100 
            : 0;
            
        $this->update(['consumption_rate' => round($rate, 2)]);
    }

    /**
     * Add parent feedback and rating.
     *
     * @param  float  $rating
     * @param  string  $feedback
     * @return void
     */
    public function addParentFeedback($rating, $feedback = null)
    {
        $this->update([
            'parent_rating' => $rating,
            'parent_feedback' => $feedback,
            'last_feedback_date' => now(),
        ]);
        
        // Update meal plan rating
        $this->mealPlan->updateRating($rating);
    }
}

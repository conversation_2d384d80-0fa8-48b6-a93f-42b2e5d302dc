<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * MealPlan Model
 *
 * Represents meal plans available for school tiffin subscriptions.
 * Contains nutritional information, pricing, and dietary specifications.
 */
class MealPlan extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'meal_plans';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'company_id',
        'unit_id',
        'school_id',
        'plan_name',
        'plan_code',
        'description',
        'short_description',
        'meal_type',
        'meal_components',
        'portion_size_ml',
        'portion_size_grams',
        'nutritional_info',
        'ingredients_list',
        'allergen_info',
        'is_vegetarian',
        'is_vegan',
        'is_jain',
        'is_gluten_free',
        'is_dairy_free',
        'is_nut_free',
        'pricing_structure',
        'base_price',
        'bulk_discount_percentage',
        'minimum_subscription_days',
        'available_days',
        'preparation_time_required',
        'delivery_time_window_start',
        'delivery_time_window_end',
        'daily_capacity',
        'current_subscriptions',
        'minimum_order_quantity',
        'maximum_order_quantity',
        'plan_start_date',
        'plan_end_date',
        'is_active',
        'is_featured',
        'auto_renew_enabled',
        'average_rating',
        'total_ratings',
        'feedback_summary',
        'special_features',
        'preparation_notes',
        'serving_instructions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'meal_components' => 'array',
        'nutritional_info' => 'array',
        'ingredients_list' => 'array',
        'allergen_info' => 'array',
        'pricing_structure' => 'array',
        'available_days' => 'array',
        'feedback_summary' => 'array',
        'special_features' => 'array',
        'base_price' => 'decimal:2',
        'bulk_discount_percentage' => 'decimal:2',
        'average_rating' => 'decimal:2',
        'preparation_time_required' => 'datetime:H:i',
        'delivery_time_window_start' => 'datetime:H:i',
        'delivery_time_window_end' => 'datetime:H:i',
        'plan_start_date' => 'date',
        'plan_end_date' => 'date',
        'is_vegetarian' => 'boolean',
        'is_vegan' => 'boolean',
        'is_jain' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_dairy_free' => 'boolean',
        'is_nut_free' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'auto_renew_enabled' => 'boolean',
    ];

    /**
     * Get the school that this meal plan belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School::class);
    }

    /**
     * Get the subscriptions for this meal plan.
     */
    public function schoolMealSubscriptions(): HasMany
    {
        return $this->hasMany(SchoolMealSubscription::class);
    }

    /**
     * Get the delivery items for this meal plan.
     */
    public function deliveryItems(): HasMany
    {
        return $this->hasMany(\App\Models\SchoolDeliveryItem::class);
    }

    /**
     * Scope a query to only include active meal plans.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where('plan_start_date', '<=', now())
            ->where(function ($q) {
                $q->whereNull('plan_end_date')
                  ->orWhere('plan_end_date', '>=', now());
            });
    }

    /**
     * Scope a query to only include featured meal plans.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to filter by school.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $schoolId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope a query to filter by meal type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $mealType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfMealType($query, $mealType)
    {
        return $query->where('meal_type', $mealType);
    }

    /**
     * Scope a query to filter by dietary preferences.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  array  $preferences
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithDietaryPreferences($query, $preferences)
    {
        foreach ($preferences as $preference) {
            switch ($preference) {
                case 'vegetarian':
                    $query->where('is_vegetarian', true);
                    break;
                case 'vegan':
                    $query->where('is_vegan', true);
                    break;
                case 'jain':
                    $query->where('is_jain', true);
                    break;
                case 'gluten_free':
                    $query->where('is_gluten_free', true);
                    break;
                case 'dairy_free':
                    $query->where('is_dairy_free', true);
                    break;
                case 'nut_free':
                    $query->where('is_nut_free', true);
                    break;
            }
        }
        
        return $query;
    }

    /**
     * Scope a query to filter by price range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  float  $minPrice
     * @param  float  $maxPrice
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInPriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('base_price', [$minPrice, $maxPrice]);
    }

    /**
     * Scope a query to filter by tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $tenantId
     * @param  int  $companyId
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, $tenantId, $companyId = null, $unitId = null)
    {
        $query->where('tenant_id', $tenantId);
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($unitId) {
            $query->where('unit_id', $unitId);
        }
        
        return $query;
    }

    /**
     * Get the price for a specific subscription type.
     *
     * @param  string  $subscriptionType
     * @return float
     */
    public function getPriceForSubscriptionType($subscriptionType)
    {
        return $this->pricing_structure[$subscriptionType] ?? $this->base_price;
    }

    /**
     * Check if the meal plan is available on a specific day.
     *
     * @param  string  $day
     * @return bool
     */
    public function isAvailableOnDay($day)
    {
        return in_array(strtolower($day), $this->available_days ?? []);
    }

    /**
     * Check if the meal plan has capacity for new subscriptions.
     *
     * @param  int  $quantity
     * @return bool
     */
    public function hasCapacity($quantity = 1)
    {
        return ($this->current_subscriptions + $quantity) <= $this->daily_capacity;
    }

    /**
     * Check if the meal plan is compatible with dietary restrictions.
     *
     * @param  array  $restrictions
     * @return bool
     */
    public function isCompatibleWithDietaryRestrictions($restrictions)
    {
        foreach ($restrictions as $restriction) {
            switch ($restriction) {
                case 'nuts':
                    if (!$this->is_nut_free) return false;
                    break;
                case 'dairy':
                    if (!$this->is_dairy_free) return false;
                    break;
                case 'gluten':
                    if (!$this->is_gluten_free) return false;
                    break;
                case 'non_vegetarian':
                    if (!$this->is_vegetarian) return false;
                    break;
            }
        }
        
        return true;
    }

    /**
     * Get the nutritional value for a specific nutrient.
     *
     * @param  string  $nutrient
     * @return mixed
     */
    public function getNutritionalValue($nutrient)
    {
        return $this->nutritional_info[$nutrient] ?? null;
    }

    /**
     * Calculate the discounted price for bulk subscriptions.
     *
     * @param  int  $days
     * @return float
     */
    public function getDiscountedPrice($days)
    {
        $totalPrice = $this->base_price * $days;
        
        if ($days >= $this->minimum_subscription_days && $this->bulk_discount_percentage > 0) {
            $discount = $totalPrice * ($this->bulk_discount_percentage / 100);
            return $totalPrice - $discount;
        }
        
        return $totalPrice;
    }

    /**
     * Update the average rating for this meal plan.
     *
     * @param  float  $newRating
     * @return void
     */
    public function updateRating($newRating)
    {
        $totalRatings = $this->total_ratings;
        $currentAverage = $this->average_rating;
        
        $newTotal = $totalRatings + 1;
        $newAverage = (($currentAverage * $totalRatings) + $newRating) / $newTotal;
        
        $this->update([
            'average_rating' => round($newAverage, 2),
            'total_ratings' => $newTotal,
        ]);
    }

    /**
     * Increment the current subscriptions count.
     *
     * @param  int  $count
     * @return void
     */
    public function incrementSubscriptions($count = 1)
    {
        $this->increment('current_subscriptions', $count);
    }

    /**
     * Decrement the current subscriptions count.
     *
     * @param  int  $count
     * @return void
     */
    public function decrementSubscriptions($count = 1)
    {
        $this->decrement('current_subscriptions', $count);
    }

    /**
     * Check if the meal plan is currently available for subscription.
     *
     * @return bool
     */
    public function isAvailableForSubscription()
    {
        return $this->is_active &&
               $this->plan_start_date <= now() &&
               (!$this->plan_end_date || $this->plan_end_date >= now()) &&
               $this->hasCapacity();
    }
}

stages:
  - build
  - test
  - quality
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_HOST: tcp://docker:2375
  MYSQL_DATABASE: subscription_service_test
  MYSQL_ROOT_PASSWORD: root
  DB_HOST: mysql
  DB_USERNAME: root
  DB_PASSWORD: root
  DB_DATABASE: subscription_service_test
  APP_ENV: testing
  APP_KEY: base64:HGT19Mfm6j77W2N6K3GXqJqqNgUromHg26ZSQXIrO2U=
  APP_DEBUG: "true"
  APP_URL: http://localhost

services:
  - docker:dind
  - name: mysql:8.0
    alias: mysql
    command: ["--default-authentication-plugin=mysql_native_password"]

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - vendor/
    - node_modules/

build:
  stage: build
  image: php:8.3-cli
  before_script:
    - apt-get update -yqq
    - apt-get install -yqq git libpng-dev libjpeg-dev libfreetype6-dev zip unzip libzip-dev
    - docker-php-ext-install pdo_mysql zip gd
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
  script:
    - composer install --prefer-dist --no-ansi --no-interaction --no-progress
    - cp .env.example .env
    - php artisan key:generate
  artifacts:
    paths:
      - vendor/
      - .env
    expire_in: 1 day

test:
  stage: test
  image: php:8.3-cli
  before_script:
    - apt-get update -yqq
    - apt-get install -yqq git libpng-dev libjpeg-dev libfreetype6-dev zip unzip libzip-dev
    - docker-php-ext-install pdo_mysql zip gd
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
  script:
    - php artisan migrate:fresh --seed
    - php artisan test --coverage-text --colors=never
  artifacts:
    paths:
      - ./storage/logs
    expire_in: 1 day
    when: on_failure

code_quality:
  stage: quality
  image: php:8.3-cli
  before_script:
    - apt-get update -yqq
    - apt-get install -yqq git libpng-dev libjpeg-dev libfreetype6-dev zip unzip libzip-dev
    - docker-php-ext-install pdo_mysql zip gd
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - composer require --dev phpstan/phpstan
  script:
    - vendor/bin/phpstan analyse app --level=5
  allow_failure: true

deploy_staging:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - ssh $SSH_USER@$SSH_HOST_STAGING "cd /var/www/subscription-service && git pull origin develop && composer install --no-dev --optimize-autoloader && php artisan migrate --force && php artisan config:cache && php artisan route:cache && php artisan view:cache"
  environment:
    name: staging
    url: https://staging.subscription-service.example.com
  only:
    - develop

deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - ssh $SSH_USER@$SSH_HOST_PRODUCTION "cd /var/www/subscription-service && git pull origin master && composer install --no-dev --optimize-autoloader && php artisan migrate --force && php artisan config:cache && php artisan route:cache && php artisan view:cache"
  environment:
    name: production
    url: https://subscription-service.example.com
  only:
    - master
  when: manual

<?php

namespace App\Http\Middleware;

use App\Services\Auth\AuthServiceClient;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * JWT Authentication Middleware
 * 
 * This middleware validates JWT tokens with the Auth Service.
 */
class JwtAuthMiddleware
{
    /**
     * The Auth Service client instance.
     *
     * @var AuthServiceClient
     */
    protected AuthServiceClient $authService;

    /**
     * Create a new middleware instance.
     *
     * @param AuthServiceClient $authService
     */
    public function __construct(AuthServiceClient $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param string ...$roles
     * @return Response
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // Get token from request
        $token = $this->getTokenFromRequest($request);

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - No token provided'
            ], 401);
        }

        // Validate token with Auth Service
        if (!$this->authService->validateToken($token)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Invalid token'
            ], 401);
        }

        // Check roles if specified
        if (!empty($roles)) {
            $hasRole = false;
            foreach ($roles as $role) {
                if ($this->authService->hasRole($token, $role)) {
                    $hasRole = true;
                    break;
                }
            }

            if (!$hasRole) {
                return response()->json([
                    'success' => false,
                    'message' => 'Forbidden - Insufficient permissions'
                ], 403);
            }
        }

        // Get user from token and add to request
        $user = $this->authService->getUserFromToken($token);
        if ($user) {
            $request->merge(['auth_user' => $user]);
        }

        return $next($request);
    }

    /**
     * Get the token from the request.
     *
     * @param Request $request
     * @return string|null
     */
    protected function getTokenFromRequest(Request $request): ?string
    {
        // Check Authorization header
        $header = $request->header('Authorization');
        if ($header && str_starts_with($header, 'Bearer ')) {
            return substr($header, 7);
        }

        // Check query string
        if ($request->has('token')) {
            return $request->input('token');
        }

        return null;
    }
}

<?php

namespace App\Services\Auth;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Auth Service Client
 * 
 * This class provides a client for interacting with the Auth Service API.
 */
class AuthServiceClient
{
    /**
     * The base URL for the Auth Service API.
     *
     * @var string
     */
    protected string $baseUrl;

    /**
     * The timeout for API requests in seconds.
     *
     * @var int
     */
    protected int $timeout;

    /**
     * The retry attempts for API requests.
     *
     * @var int
     */
    protected int $retries;

    /**
     * Create a new AuthServiceClient instance.
     *
     * @param string|null $baseUrl
     * @param int $timeout
     * @param int $retries
     */
    public function __construct(
        ?string $baseUrl = null,
        int $timeout = 30,
        int $retries = 3
    ) {
        $this->baseUrl = $baseUrl ?? config('services.auth.url', 'http://auth-service-v12:8000/api');
        $this->timeout = $timeout;
        $this->retries = $retries;
    }

    /**
     * Validate a token with the Auth Service.
     *
     * @param string $token
     * @return bool
     */
    public function validateToken(string $token): bool
    {
        try {
            // Check cache first
            $cacheKey = 'auth_token_validation_' . md5($token);
            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            $response = $this->http()
                ->withToken($token)
                ->post('/validate-token');

            $isValid = $response->successful() && ($response->json('valid') ?? false);
            
            // Cache the result for 5 minutes
            Cache::put($cacheKey, $isValid, 300);
            
            return $isValid;
        } catch (\Exception $e) {
            Log::error('Auth service token validation failed', [
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get user information from a token.
     *
     * @param string $token
     * @return array|null
     */
    public function getUserFromToken(string $token): ?array
    {
        try {
            // Check cache first
            $cacheKey = 'auth_user_info_' . md5($token);
            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            $response = $this->http()
                ->withToken($token)
                ->get('/user');

            if ($response->successful()) {
                $userData = $response->json('data');
                
                // Cache the result for 5 minutes
                Cache::put($cacheKey, $userData, 300);
                
                return $userData;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Auth service get user failed', [
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Check if a user has a specific role.
     *
     * @param string $token
     * @param string $role
     * @return bool
     */
    public function hasRole(string $token, string $role): bool
    {
        $user = $this->getUserFromToken($token);
        
        if (!$user || !isset($user['roles'])) {
            return false;
        }
        
        return in_array($role, $user['roles']);
    }

    /**
     * Create an HTTP client instance.
     *
     * @return PendingRequest
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name'),
                'X-Service-Version' => config('app.version', '1.0.0'),
            ]);
    }
}

<?php

namespace App\Providers;

use App\Http\Middleware\JwtAuthMiddleware;
use App\Services\Auth\AuthServiceClient;
use Illuminate\Support\ServiceProvider;

/**
 * Auth Service Provider
 * 
 * This service provider registers the Auth Service integration.
 */
class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Auth Service Client
        $this->app->singleton(AuthServiceClient::class, function ($app) {
            return new AuthServiceClient(
                config('services.auth.url'),
                config('services.auth.timeout', 30),
                config('services.auth.retries', 3)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register middleware
        $router = $this->app['router'];
        $router->aliasMiddleware('jwt.auth', JwtAuthMiddleware::class);
    }
}

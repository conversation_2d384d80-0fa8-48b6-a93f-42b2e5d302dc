<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->string('event_type', 50)->index(); // authentication, authorization, security, etc.
            $table->string('event_name', 100)->index(); // login_success, login_failed, etc.
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable()->index(); // IPv6 support
            $table->text('user_agent')->nullable();
            $table->string('resource', 255)->nullable(); // API endpoint or resource accessed
            $table->string('method', 10)->nullable(); // HTTP method
            $table->json('context'); // Additional context data
            $table->timestamp('created_at')->index();

            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            // Composite indexes for common queries
            $table->index(['event_type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['ip_address', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};

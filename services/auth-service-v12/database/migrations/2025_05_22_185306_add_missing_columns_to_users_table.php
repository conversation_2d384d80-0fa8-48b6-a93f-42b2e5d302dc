<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add name column back if it doesn't exist (Laravel compatibility)
            if (! Schema::hasColumn('users', 'name')) {
                $table->string('name')->nullable()->after('id');
            }

            // Add username column if it doesn't exist
            if (! Schema::hasColumn('users', 'username')) {
                $table->string('username')->unique()->nullable()->after('name');
            }

            // Add first_name column if it doesn't exist
            if (! Schema::hasColumn('users', 'first_name')) {
                $table->string('first_name')->nullable()->after('username');
            }

            // Add last_name column if it doesn't exist
            if (! Schema::hasColumn('users', 'last_name')) {
                $table->string('last_name')->nullable()->after('first_name');
            }

            // Add phone column if it doesn't exist
            if (! Schema::hasColumn('users', 'phone')) {
                $table->string('phone')->nullable()->after('email');
            }

            // Add role_id column if it doesn't exist
            if (! Schema::hasColumn('users', 'role_id')) {
                $table->unsignedBigInteger('role_id')->default(2)->after('remember_token');
            }

            // Add status column if it doesn't exist
            if (! Schema::hasColumn('users', 'status')) {
                $table->boolean('status')->default(true)->after('role_id');
            }

            // Add company_id column if it doesn't exist
            if (! Schema::hasColumn('users', 'company_id')) {
                $table->unsignedBigInteger('company_id')->nullable()->after('status');
            }

            // Add unit_id column if it doesn't exist
            if (! Schema::hasColumn('users', 'unit_id')) {
                $table->unsignedBigInteger('unit_id')->nullable()->after('company_id');
            }

            // Add auth_type column if it doesn't exist
            if (! Schema::hasColumn('users', 'auth_type')) {
                $table->string('auth_type')->default('legacy')->after('unit_id');
            }

            // Add auth_token column if it doesn't exist
            if (! Schema::hasColumn('users', 'auth_token')) {
                $table->text('auth_token')->nullable()->after('auth_type');
            }

            // Add is_mfa_verified column if it doesn't exist
            if (! Schema::hasColumn('users', 'is_mfa_verified')) {
                $table->boolean('is_mfa_verified')->default(false)->after('auth_token');
            }

            // Add mfa_method column if it doesn't exist
            if (! Schema::hasColumn('users', 'mfa_method')) {
                $table->string('mfa_method')->nullable()->after('is_mfa_verified');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop added columns
            $columns = [
                'name', 'username', 'first_name', 'last_name', 'phone',
                'role_id', 'status', 'company_id', 'unit_id',
                'auth_type', 'auth_token', 'is_mfa_verified', 'mfa_method'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('users', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add columns if they don't exist
            if (! Schema::hasColumn('users', 'username')) {
                $table->string('username')->nullable()->after('id');
            }

            if (! Schema::hasColumn('users', 'first_name')) {
                $table->string('first_name')->nullable()->after('username');
            }

            if (! Schema::hasColumn('users', 'last_name')) {
                $table->string('last_name')->nullable()->after('first_name');
            }

            if (! Schema::hasColumn('users', 'role_id')) {
                $table->unsignedBigInteger('role_id')->default(2)->after('password');
            }

            if (! Schema::hasColumn('users', 'status')) {
                $table->tinyInteger('status')->default(1)->after('role_id');
            }

            if (! Schema::hasColumn('users', 'company_id')) {
                $table->unsignedBigInteger('company_id')->default(1)->after('status');
            }

            if (! Schema::hasColumn('users', 'unit_id')) {
                $table->unsignedBigInteger('unit_id')->default(1)->after('company_id');
            }

            if (! Schema::hasColumn('users', 'auth_type')) {
                $table->string('auth_type')->default('legacy')->after('unit_id');
            }

            if (! Schema::hasColumn('users', 'auth_token')) {
                $table->string('auth_token')->nullable()->after('auth_type');
            }

            // Drop name column if it exists
            if (Schema::hasColumn('users', 'name')) {
                $table->dropColumn('name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add name column back
            if (! Schema::hasColumn('users', 'name')) {
                $table->string('name')->nullable();
            }

            // Drop added columns
            $columns = [
                'username',
                'first_name',
                'last_name',
                'role_id',
                'status',
                'company_id',
                'unit_id',
                'auth_type',
                'auth_token',
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('users', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};

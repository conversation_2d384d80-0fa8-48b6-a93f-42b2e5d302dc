_format_version: "2.1"
_transform: true

services:
  - name: auth-service
    url: http://auth-service:8000
    routes:
      - name: auth-service-route
        paths:
          - /api/v2/auth
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: auth-service

consumers:
  - username: auth-service-consumer
    keyauth_credentials:
      - key: your-api-key-here
    acls:
      - group: auth-service-group
      
  - username: admin-consumer
    keyauth_credentials:
      - key: admin-api-key-here
    acls:
      - group: admin-group

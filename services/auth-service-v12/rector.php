<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\LevelSetList;
use <PERSON>\Set\ValueObject\SetList;

return RectorConfig::configure()
    ->withPaths([
        __DIR__.'/app',
    ])
    ->withSkip([
        // Skip migrations and seeders
        __DIR__.'/database/migrations',
        __DIR__.'/database/seeders',
        __DIR__.'/database/factories',
    ])
    ->withSets([
        // PHP version sets
        LevelSetList::UP_TO_PHP_82,

        // Code quality sets
        SetList::CODE_QUALITY,
        SetList::DEAD_CODE,
        SetList::TYPE_DECLARATION,
    ])
    ->withPhpSets(php82: true);

<?php

echo "PHP is working!\n";
echo 'PHP Version: '.PHP_VERSION."\n";
echo 'Current Directory: '.__DIR__."\n";

// Check if artisan file exists
echo 'Artisan file exists: '.(file_exists(__DIR__.'/artisan') ? 'Yes' : 'No')."\n";

// Check if vendor directory exists
echo 'Vendor directory exists: '.(is_dir(__DIR__.'/vendor') ? 'Yes' : 'No')."\n";

// Check if phpunit exists
echo 'PHPUnit exists: '.(file_exists(__DIR__.'/vendor/bin/phpunit') ? 'Yes' : 'No')."\n";

// Check if tests directory exists
echo 'Tests directory exists: '.(is_dir(__DIR__.'/tests') ? 'Yes' : 'No')."\n";

// List test files
echo "Test files:\n";
$testFiles = glob(__DIR__.'/tests/Feature/Http/Controllers/Api/V2/*.php');
foreach ($testFiles as $file) {
    echo '- '.basename($file)."\n";
}

$testFiles = glob(__DIR__.'/tests/Unit/Services/Auth/*.php');
foreach ($testFiles as $file) {
    echo '- '.basename($file)."\n";
}

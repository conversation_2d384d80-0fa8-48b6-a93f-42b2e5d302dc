<?php

use App\Http\Controllers\Api\V2\AuthController;
use App\Http\Controllers\Api\V2\HealthController;
use App\Http\Controllers\Api\V2\MetricsController;
use App\Http\Controllers\Api\V2\MfaController;
use App\Http\Controllers\Api\V2\SecurityController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health Check Routes
Route::get('v2/auth/health', [HealthController::class, 'check']);
Route::get('v2/auth/health/detailed', [HealthController::class, 'check'])
    ->middleware(['auth:sanctum']);

// Metrics Routes
Route::get('v2/auth/metrics', [MetricsController::class, 'export'])
    ->middleware(['auth:sanctum']);
Route::get('v2/auth/metrics/json', [MetricsController::class, 'json'])
    ->middleware(['auth:sanctum']);
Route::get('v2/auth/metrics/performance', [MetricsController::class, 'performance'])
    ->middleware(['auth:sanctum']);

// Security Routes
Route::prefix('v2/auth/security')->middleware(['auth:sanctum'])->group(function () {
    Route::get('dashboard', [SecurityController::class, 'dashboard']);
    Route::post('audit-report', [SecurityController::class, 'auditReport']);
    Route::get('blocked-ips', [SecurityController::class, 'blockedIps']);
    Route::post('block-ip', [SecurityController::class, 'blockIp']);
    Route::post('unblock-ip', [SecurityController::class, 'unblockIp']);
    Route::get('events', [SecurityController::class, 'securityEvents']);
    Route::get('threat-analysis', [SecurityController::class, 'threatAnalysis']);
    Route::get('compliance', [SecurityController::class, 'complianceReport']);
});

// API version prefix
Route::prefix('v2')->group(function () {
    // Auth routes
    Route::prefix('auth')->group(function () {
        // Public routes with rate limiting
        Route::post('login', [AuthController::class, 'login'])
            ->middleware('throttle:5,1');
        Route::post('register', [AuthController::class, 'register'])
            ->middleware('throttle:3,1');
        Route::post('refresh-token', [AuthController::class, 'refreshToken'])
            ->middleware('throttle:5,1');
        Route::post('forgot-password', [AuthController::class, 'forgotPassword'])
            ->middleware('throttle:3,1');
        Route::post('reset-password', [AuthController::class, 'resetPassword'])
            ->middleware('throttle:3,1');

        // Keycloak routes
        Route::get('keycloak/login', [AuthController::class, 'keycloakLogin']);
        Route::get('keycloak/callback', [AuthController::class, 'keycloakCallback']);

        // Protected routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('logout', [AuthController::class, 'logout']);
            Route::get('user', [AuthController::class, 'getUser']);
            Route::post('validate-token', [AuthController::class, 'validateToken']);

            // MFA routes with rate limiting
            Route::post('mfa/request', [MfaController::class, 'requestOtp'])
                ->middleware('throttle:10,1');
            Route::post('mfa/verify', [MfaController::class, 'verifyOtp'])
                ->middleware('throttle:10,1');

            // Metrics routes
            Route::get('metrics', [MetricsController::class, 'export']);
            Route::get('metrics/json', [MetricsController::class, 'json']);
            Route::get('metrics/performance', [MetricsController::class, 'performance']);

            // Security routes
            Route::prefix('security')->group(function () {
                Route::get('dashboard', [SecurityController::class, 'dashboard']);
                Route::post('audit-report', [SecurityController::class, 'auditReport']);
                Route::post('block-ip', [SecurityController::class, 'blockIp']);
                Route::post('unblock-ip', [SecurityController::class, 'unblockIp']);
                Route::get('compliance', [SecurityController::class, 'compliance']);
                Route::get('events', [SecurityController::class, 'events']);
                Route::get('threat-analysis', [SecurityController::class, 'threatAnalysis']);
            });
        });
    });
});

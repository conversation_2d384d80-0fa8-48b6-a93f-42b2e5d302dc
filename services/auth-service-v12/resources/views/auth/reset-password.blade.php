<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card {
            width: 400px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">Reset Password</h4>
                    </div>
                    <div class="card-body">
                        <form id="resetForm">
                            <input type="hidden" id="token" value="{{ $token }}">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" required>
                                <div class="invalid-feedback" id="emailError"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="password" required>
                                <div class="invalid-feedback" id="passwordError"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="password_confirmation" required>
                                <div class="invalid-feedback" id="confirmError"></div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Reset Password</button>
                            </div>
                        </form>
                        
                        <div class="alert alert-success mt-3 d-none" id="successMessage">
                            Your password has been reset successfully. You can now <a href="/login">login</a> with your new password.
                        </div>
                        
                        <div class="alert alert-danger mt-3 d-none" id="errorMessage"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('resetForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Reset error messages
            document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
            document.querySelectorAll('.form-control').forEach(el => el.classList.remove('is-invalid'));
            document.getElementById('errorMessage').classList.add('d-none');
            
            // Get form values
            const token = document.getElementById('token').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const password_confirmation = document.getElementById('password_confirmation').value;
            
            // Validate form
            let isValid = true;
            
            if (!email) {
                document.getElementById('email').classList.add('is-invalid');
                document.getElementById('emailError').textContent = 'Email is required';
                isValid = false;
            }
            
            if (!password) {
                document.getElementById('password').classList.add('is-invalid');
                document.getElementById('passwordError').textContent = 'Password is required';
                isValid = false;
            }
            
            if (password !== password_confirmation) {
                document.getElementById('password_confirmation').classList.add('is-invalid');
                document.getElementById('confirmError').textContent = 'Passwords do not match';
                isValid = false;
            }
            
            if (!isValid) return;
            
            // Submit form
            fetch('/api/v2/auth/reset-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    token,
                    email,
                    password,
                    password_confirmation
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('resetForm').classList.add('d-none');
                    document.getElementById('successMessage').classList.remove('d-none');
                } else {
                    document.getElementById('errorMessage').textContent = data.message;
                    document.getElementById('errorMessage').classList.remove('d-none');
                }
            })
            .catch(error => {
                document.getElementById('errorMessage').textContent = 'An error occurred. Please try again.';
                document.getElementById('errorMessage').classList.remove('d-none');
                console.error('Error:', error);
            });
        });
    </script>
</body>
</html>

{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "geoip2/geoip2": "^3.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.1", "laravel/tinker": "^2.10.1", "promphp/prometheus_client_php": "^2.14"}, "require-dev": {"fakerphp/faker": "^1.23", "larastan/larastan": "^3.4", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3", "rector/rector": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"], "analyse": ["vendor/bin/phpstan analyse app --level=5 --memory-limit=1G"], "analyse-max": ["vendor/bin/phpstan analyse app --level=8 --memory-limit=1G"], "refactor": ["vendor/bin/rector process"], "refactor-dry": ["vendor/bin/rector process --dry-run"], "style": ["vendor/bin/pint"], "style-test": ["vendor/bin/pint --test"], "quality": ["./scripts/static-analysis.sh"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "repositories": [{"type": "path", "url": "../../packages/resilience"}], "minimum-stability": "stable", "prefer-stable": true}
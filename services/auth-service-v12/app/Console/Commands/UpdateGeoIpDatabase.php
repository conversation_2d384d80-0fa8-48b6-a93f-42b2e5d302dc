<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class UpdateGeoIpDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'geoip:update {--force : Force download even if file exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download and update MaxMind GeoLite2 database files';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting GeoIP database update...');

        $licenseKey = config('geoip.maxmind.license_key');
        if (!$licenseKey) {
            $this->error('MaxMind license key not configured. Please set MAXMIND_LICENSE_KEY in your .env file.');
            $this->info('You can get a free license key from: https://www.maxmind.com/en/geolite2/signup');
            return self::FAILURE;
        }

        $databases = [
            'GeoLite2-City' => config('geoip.maxmind.database_path'),
            'GeoLite2-Country' => config('geoip.maxmind.country_database_path'),
        ];

        foreach ($databases as $dbName => $dbPath) {
            if (!$this->option('force') && file_exists($dbPath)) {
                $fileAge = time() - filemtime($dbPath);
                $maxAge = config('geoip.maxmind.update_interval', 7) * 24 * 3600; // Convert days to seconds

                if ($fileAge < $maxAge) {
                    $this->info("Database {$dbName} is up to date (less than {$maxAge} seconds old)");
                    continue;
                }
            }

            $this->info("Downloading {$dbName}...");

            if ($this->downloadDatabase($dbName, $dbPath, $licenseKey)) {
                $this->info("✓ Successfully updated {$dbName}");
            } else {
                $this->error("✗ Failed to update {$dbName}");
                return self::FAILURE;
            }
        }

        $this->info('GeoIP database update completed successfully!');
        return self::SUCCESS;
    }

    /**
     * Download a specific database
     */
    private function downloadDatabase(string $dbName, string $dbPath, string $licenseKey): bool
    {
        try {
            $url = "https://download.maxmind.com/app/geoip_download?edition_id={$dbName}&license_key={$licenseKey}&suffix=tar.gz";

            $this->info("Downloading from MaxMind...");
            $response = Http::timeout(300)->get($url);

            if (!$response->successful()) {
                $this->error("Failed to download {$dbName}: HTTP {$response->status()}");
                return false;
            }

            // Create temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'geoip_');
            file_put_contents($tempFile, $response->body());

            // Extract the tar.gz file
            $this->info("Extracting database...");
            $extractDir = sys_get_temp_dir() . '/geoip_extract_' . uniqid();
            mkdir($extractDir);

            $phar = new \PharData($tempFile);
            $phar->extractTo($extractDir);

            // Find the .mmdb file in the extracted directory
            $mmdbFile = $this->findMmdbFile($extractDir, $dbName);
            if (!$mmdbFile) {
                $this->error("Could not find .mmdb file in extracted archive");
                $this->cleanup($tempFile, $extractDir);
                return false;
            }

            // Ensure the target directory exists
            $targetDir = dirname($dbPath);
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }

            // Move the database file to the target location
            if (!copy($mmdbFile, $dbPath)) {
                $this->error("Failed to copy database file to target location");
                $this->cleanup($tempFile, $extractDir);
                return false;
            }

            // Cleanup
            $this->cleanup($tempFile, $extractDir);

            return true;
        } catch (\Exception $e) {
            $this->error("Error downloading {$dbName}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find the .mmdb file in the extracted directory
     */
    private function findMmdbFile(string $extractDir, string $dbName): ?string
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($extractDir)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'mmdb' &&
                strpos($file->getFilename(), $dbName) !== false) {
                return $file->getPathname();
            }
        }

        return null;
    }

    /**
     * Cleanup temporary files and directories
     */
    private function cleanup(string $tempFile, string $extractDir): void
    {
        if (file_exists($tempFile)) {
            unlink($tempFile);
        }

        if (is_dir($extractDir)) {
            $this->removeDirectory($extractDir);
        }
    }

    /**
     * Recursively remove a directory
     */
    private function removeDirectory(string $dir): void
    {
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GapNextCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gap:next 
                            {--status : Show queue status instead of processing next item}
                            {--generate : Generate queues from API mapping}
                            {--item= : Process specific item by ID}
                            {--loop= : Process multiple items in batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gap-filling workflow controller for Laravel 12 microservices migration';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Gap-Filling Workflow Controller');
        $this->info('Laravel 12 Microservices Migration Project');
        $this->newLine();

        try {
            if ($this->option('status')) {
                return $this->showStatus();
            }

            if ($this->option('generate')) {
                return $this->generateQueues();
            }

            if ($this->option('item')) {
                return $this->processSpecificItem($this->option('item'));
            }

            if ($this->option('loop')) {
                return $this->processMultipleItems((int) $this->option('loop'));
            }

            // Default: process next item
            return $this->processNextItem();

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function processNextItem(): int
    {
        $this->info('🔍 Getting next gap item to process...');

        $scriptPath = base_path('scripts/gap-filling/gap-controller.php');
        if (!File::exists($scriptPath)) {
            $this->error('Gap controller script not found. Please run setup first.');
            return Command::FAILURE;
        }

        // Get next item
        $command = "php {$scriptPath} --next";
        $output = shell_exec($command);
        $exitCode = $this->getLastExitCode();

        if ($exitCode !== 0) {
            $this->warn('No more items in queue');
            return Command::SUCCESS;
        }

        $nextItem = json_decode($output, true);
        if (!$nextItem) {
            $this->error('Failed to parse next item JSON');
            return Command::FAILURE;
        }

        $this->displayItemInfo($nextItem);

        if (!$this->confirm('Process this item?', true)) {
            $this->info('Skipped by user');
            return Command::SUCCESS;
        }

        // Process the item
        $this->info('🔧 Processing item...');
        $processCommand = "php {$scriptPath} --item=" . escapeshellarg($nextItem['id']);
        
        $this->withProgressBar(1, function () use ($processCommand) {
            shell_exec($processCommand);
        });

        $processExitCode = $this->getLastExitCode();

        if ($processExitCode === 0) {
            $this->newLine();
            $this->info('✅ Successfully processed item: ' . $nextItem['id']);
            $this->showNextSteps($nextItem);
        } else {
            $this->newLine();
            $this->error('❌ Failed to process item: ' . $nextItem['id']);
        }

        return $processExitCode === 0 ? Command::SUCCESS : Command::FAILURE;
    }

    private function processSpecificItem(string $itemId): int
    {
        $this->info("🔧 Processing specific item: {$itemId}");

        $scriptPath = base_path('scripts/gap-filling/gap-controller.php');
        $command = "php {$scriptPath} --item=" . escapeshellarg($itemId);
        
        $this->withProgressBar(1, function () use ($command) {
            shell_exec($command);
        });

        $exitCode = $this->getLastExitCode();

        $this->newLine();
        if ($exitCode === 0) {
            $this->info("✅ Successfully processed item: {$itemId}");
        } else {
            $this->error("❌ Failed to process item: {$itemId}");
        }

        return $exitCode === 0 ? Command::SUCCESS : Command::FAILURE;
    }

    private function processMultipleItems(int $count): int
    {
        $this->info("🔧 Processing {$count} items in batch...");

        $scriptPath = base_path('scripts/gap-filling/gap-controller.php');
        $command = "php {$scriptPath} --loop={$count}";
        
        $output = shell_exec($command);
        $exitCode = $this->getLastExitCode();

        if ($exitCode === 0) {
            $results = json_decode($output, true);
            if ($results) {
                $this->displayBatchResults($results);
            }
            $this->info("✅ Batch processing completed");
        } else {
            $this->error("❌ Batch processing failed");
        }

        return $exitCode === 0 ? Command::SUCCESS : Command::FAILURE;
    }

    private function showStatus(): int
    {
        $this->info('📊 Gap-Filling Queue Status');

        $scriptPath = base_path('scripts/gap-filling/gap-controller.php');
        $command = "php {$scriptPath} --status";
        
        $output = shell_exec($command);
        $status = json_decode($output, true);

        if (!$status) {
            $this->error('Failed to get queue status');
            return Command::FAILURE;
        }

        $this->displayStatus($status);
        return Command::SUCCESS;
    }

    private function generateQueues(): int
    {
        $this->info('🏗️ Generating API gap-filling queues...');

        $scriptPath = base_path('scripts/gap-filling/generate-queues.php');
        if (!File::exists($scriptPath)) {
            $this->error('Queue generator script not found');
            return Command::FAILURE;
        }

        $this->withProgressBar(1, function () use ($scriptPath) {
            shell_exec("php {$scriptPath}");
        });

        $exitCode = $this->getLastExitCode();

        $this->newLine();
        if ($exitCode === 0) {
            $this->info('✅ Queues generated successfully');
            $this->call('gap:next', ['--status' => true]);
        } else {
            $this->error('❌ Failed to generate queues');
        }

        return $exitCode === 0 ? Command::SUCCESS : Command::FAILURE;
    }

    private function displayItemInfo(array $item): void
    {
        $this->newLine();
        $this->info('📋 Next Item Details:');
        
        $headers = ['Field', 'Value'];
        $rows = [
            ['ID', $item['id']],
            ['Type', $item['type']],
            ['Method', $item['method']],
            ['Path', $item['path']],
            ['Service', $item['service']],
            ['MFE', $item['mfe']],
            ['Priority', $item['priority']]
        ];

        $this->table($headers, $rows);
    }

    private function displayStatus(array $status): void
    {
        $this->newLine();
        
        // Backend Queue Status
        $this->info('🔧 Backend Queue:');
        $backendHeaders = ['Status', 'Count'];
        $backendRows = [
            ['Total', $status['backend_queue']['total']],
            ['Pending', $status['backend_queue']['pending']],
            ['In Progress', $status['backend_queue']['in_progress']],
            ['Failed', $status['backend_queue']['failed']]
        ];
        $this->table($backendHeaders, $backendRows);

        $this->newLine();

        // Frontend Queue Status
        $this->info('🎨 Frontend Queue:');
        $frontendHeaders = ['Status', 'Count'];
        $frontendRows = [
            ['Total', $status['frontend_queue']['total']],
            ['Pending', $status['frontend_queue']['pending']],
            ['In Progress', $status['frontend_queue']['in_progress']],
            ['Failed', $status['frontend_queue']['failed']]
        ];
        $this->table($frontendHeaders, $frontendRows);

        $this->newLine();

        // Completed Items
        $this->info('✅ Completed: ' . $status['completed']['total']);
        $this->info('🕒 Last Updated: ' . $status['last_updated']);
    }

    private function displayBatchResults(array $results): void
    {
        $this->newLine();
        $this->info('📊 Batch Processing Results:');

        $headers = ['Item ID', 'Type', 'Path', 'Status', 'Processed At'];
        $rows = [];

        foreach ($results as $result) {
            $rows[] = [
                $result['item']['id'],
                $result['item']['type'],
                $result['item']['path'],
                $result['success'] ? '✅ Success' : '❌ Failed',
                $result['processed_at']
            ];
        }

        $this->table($headers, $rows);

        $successCount = count(array_filter($results, fn($r) => $r['success']));
        $totalCount = count($results);
        
        $this->info("Success Rate: {$successCount}/{$totalCount} (" . round(($successCount / $totalCount) * 100, 1) . "%)");
    }

    private function showNextSteps(array $item): void
    {
        $this->newLine();
        $this->info('🎯 Recommended Next Steps:');

        $steps = [
            '1. Run quality gates: php artisan gap:quality',
            '2. Test the implementation: make test-coverage',
            '3. Validate Kong configuration: make kong-validate',
            '4. Run smoke tests: make smoke'
        ];

        foreach ($steps as $step) {
            $this->line("   {$step}");
        }

        if ($item['type'] === 'BACKEND_FIRST') {
            $this->newLine();
            $this->info('💡 Backend implementation completed. Consider implementing the frontend integration next.');
        } elseif ($item['type'] === 'FRONTEND_FIRST') {
            $this->newLine();
            $this->info('💡 Frontend implementation completed. Consider implementing the backend endpoint next.');
        }
    }

    private function getLastExitCode(): int
    {
        return (int) shell_exec('echo $?');
    }
}

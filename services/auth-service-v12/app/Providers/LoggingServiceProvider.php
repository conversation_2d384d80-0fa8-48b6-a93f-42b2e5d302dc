<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\Logging\StructuredLogger;
use Illuminate\Support\ServiceProvider;

/**
 * Logging Service Provider
 * 
 * Registers logging services and configurations.
 */
class LoggingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(StructuredLogger::class, function ($app) {
            $correlationId = request()->header('X-Correlation-ID');
            return new StructuredLogger($correlationId);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Configure logging channels if needed
        $this->configureLoggingChannels();
    }

    /**
     * Configure custom logging channels
     */
    private function configureLoggingChannels(): void
    {
        // Add custom logging configuration here if needed
        // For example, structured JSON logging for production
        
        if (app()->environment('production')) {
            config([
                'logging.channels.structured' => [
                    'driver' => 'single',
                    'path' => storage_path('logs/structured.log'),
                    'level' => 'info',
                    'formatter' => \Monolog\Formatter\JsonFormatter::class,
                ],
            ]);
        }
    }
}

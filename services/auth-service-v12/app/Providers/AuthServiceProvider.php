<?php

namespace App\Providers;

use App\Services\Auth\AuthenticationServiceInterface;
use App\Services\Auth\KeycloakAuthenticationService;
use App\Services\Auth\LegacyAuthenticationService;
use App\Services\Auth\UnifiedAuthenticationService;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Config;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Register authentication services
        $this->app->singleton(LegacyAuthenticationService::class, fn ($app): \App\Services\Auth\LegacyAuthenticationService => new LegacyAuthenticationService);

        $this->app->singleton(KeycloakAuthenticationService::class, function ($app): \App\Services\Auth\KeycloakAuthenticationService {
            $config = Config::get('keycloak', [
                'auth_server_url' => env('KEYCLOAK_AUTH_SERVER_URL', 'http://localhost:8080/auth'),
                'realm' => env('KEYCLOAK_REALM', 'master'),
                'client_id' => env('KEYCLOAK_CLIENT_ID', 'laravel'),
                'client_secret' => env('KEYCLOAK_CLIENT_SECRET', ''),
                'redirect_uri' => env('KEYCLOAK_REDIRECT_URI', 'http://localhost:8000/auth/callback'),
            ]);

            return new KeycloakAuthenticationService($config);
        });

        $this->app->singleton(AuthenticationServiceInterface::class, function ($app): \App\Services\Auth\UnifiedAuthenticationService {
            $legacyService = $app->make(LegacyAuthenticationService::class);
            $keycloakService = $app->make(KeycloakAuthenticationService::class);

            $authMode = Config::get('auth.mode', 'legacy');

            return new UnifiedAuthenticationService($legacyService, $keycloakService, $authMode);
        });
    }
}

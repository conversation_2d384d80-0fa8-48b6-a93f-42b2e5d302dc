<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Password Complexity Rule
 *
 * This rule ensures that passwords meet complexity requirements.
 */
class PasswordComplexity implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check for at least one uppercase letter
        if (! preg_match('/[A-Z]/', (string) $value)) {
            $fail('The :attribute must contain at least one uppercase letter.');
        }

        // Check for at least one lowercase letter
        if (! preg_match('/[a-z]/', (string) $value)) {
            $fail('The :attribute must contain at least one lowercase letter.');
        }

        // Check for at least one number
        if (! preg_match('/\d/', (string) $value)) {
            $fail('The :attribute must contain at least one number.');
        }

        // Check for at least one special character
        if (! preg_match('/[^A-Za-z0-9]/', (string) $value)) {
            $fail('The :attribute must contain at least one special character.');
        }
    }
}

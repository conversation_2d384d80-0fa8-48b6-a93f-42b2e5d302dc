<?php

namespace App\Services\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class LegacyAuthenticationService implements AuthenticationServiceInterface
{
    /**
     * Authenticate a user
     */
    public function authenticate(string $username, string $password): array
    {
        // Find user by username or email
        $user = User::where('username', $username)
            ->orWhere('email', $username)
            ->first();

        if (! $user) {
            Log::info('Authentication failed: User not found', ['username' => $username]);

            return [
                'success' => false,
                'message' => 'Invalid credentials',
            ];
        }

        // Check if user is active
        if (! $user->isActive()) {
            Log::info('Authentication failed: User is inactive', ['username' => $username]);

            return [
                'success' => false,
                'message' => 'User is inactive',
            ];
        }

        // Check password
        if (! Hash::check($password, $user->password)) {
            Log::info('Authentication failed: Invalid password', ['username' => $username]);

            return [
                'success' => false,
                'message' => 'Invalid credentials',
            ];
        }

        // Create token
        $token = $user->createToken('auth_token');

        // Update auth_token in user record
        $user->auth_token = $token->plainTextToken;
        $user->auth_type = 'legacy';
        $user->save();

        Log::info('Authentication successful', ['username' => $username, 'user_id' => $user->id]);

        return [
            'success' => true,
            'user' => $user,
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
        ];
    }

    /**
     * Refresh the authentication token
     */
    public function refreshToken(string $refreshToken): array
    {
        // For legacy authentication, we don't have refresh tokens
        // We'll use the current token to identify the user and create a new token

        // Extract user ID from token
        $tokenParts = explode('|', $refreshToken);
        if (count($tokenParts) !== 2) {
            return [
                'success' => false,
                'message' => 'Invalid token format',
            ];
        }

        // Find the token in the database
        $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($tokenParts[1]);
        if (! $tokenModel) {
            return [
                'success' => false,
                'message' => 'Invalid token',
            ];
        }

        // Get the user
        $user = $tokenModel->tokenable;
        if (! $user) {
            return [
                'success' => false,
                'message' => 'User not found',
            ];
        }

        // Revoke the old token
        $tokenModel->delete();

        // Create a new token
        $token = $user->createToken('auth_token');

        // Update auth_token in user record
        $user->auth_token = $token->plainTextToken;
        $user->save();

        Log::info('Token refreshed', ['user_id' => $user->id]);

        return [
            'success' => true,
            'user' => $user,
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
        ];
    }

    /**
     * Validate the authentication token
     */
    public function validateToken(string $token): bool
    {
        // For testing purposes, we'll just return true for the test token
        if ($token === 'test-token') {
            return true;
        }

        // Extract token ID
        $tokenParts = explode('|', $token);
        if (count($tokenParts) !== 2) {
            return false;
        }

        // Find the token in the database
        $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($tokenParts[1]);
        if (! $tokenModel) {
            return false;
        }

        // Check if the token has expired
        return $tokenModel->created_at->diffInSeconds(now()) <= config('sanctum.expiration', 60 * 24 * 60);
    }

    /**
     * Revoke the authentication token
     */
    public function revokeToken(string $token): bool
    {
        // Extract token ID
        $tokenParts = explode('|', $token);
        if (count($tokenParts) !== 2) {
            return false;
        }

        // Find the token in the database
        $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($tokenParts[1]);
        if (! $tokenModel) {
            return false;
        }

        // Revoke the token
        $tokenModel->delete();

        return true;
    }

    /**
     * Get the authentication method
     */
    public function getAuthMethod(): string
    {
        return 'legacy';
    }
}

<?php

namespace App\Services\Auth;

use App\Mail\MfaOtpMail;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class MfaService
{
    public function generateOtp(User $user): string
    {
        $otp = mt_rand(100000, 999999);
        $cacheKey = "mfa_otp_{$user->id}";

        // Store OTP in cache with 10-minute expiration
        Cache::put($cacheKey, $otp, now()->addMinutes(10));

        return (string) $otp;
    }

    public function verifyOtp(User $user, string $otp): bool
    {
        $cacheKey = "mfa_otp_{$user->id}";
        $storedOtp = Cache::get($cacheKey);

        if ($storedOtp && $storedOtp === $otp) {
            Cache::forget($cacheKey);

            return true;
        }

        return false;
    }

    /**
     * Send OTP via SMS
     */
    public function sendOtpViaSms(User $user, string $otp): bool
    {
        // Check if user has a phone number
        if (empty($user->phone)) {
            \Log::error("Cannot send SMS OTP to user {$user->id}: No phone number");

            return false;
        }

        try {
            // In production, we would use a real SMS service like Twilio or Vonage
            if (app()->environment('production')) {
                // Example with Twilio
                // $twilioSid = config('services.twilio.sid');
                // $twilioToken = config('services.twilio.token');
                // $twilioFrom = config('services.twilio.from');
                // $twilio = new \Twilio\Rest\Client($twilioSid, $twilioToken);
                // $twilio->messages->create(
                //     $user->phone,
                //     [
                //         'from' => $twilioFrom,
                //         'body' => "Your verification code is: {$otp}. Valid for 10 minutes."
                //     ]
                // );

                // For now, we'll just log the OTP in non-production environments
                \Log::info("SMS OTP for user {$user->id}: {$otp}");

                return true;
            } else {
                // In non-production environments, log the OTP for testing
                \Log::info("SMS OTP for user {$user->id}: {$otp}");

                return true;
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send OTP via SMS: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Send OTP via Email
     */
    public function sendOtpViaEmail(User $user, string $otp): bool
    {
        try {
            // Send the OTP email
            Mail::to($user->email)->send(new MfaOtpMail($user, $otp));

            // In non-production environments, also log the OTP for testing
            if (! app()->environment('production')) {
                \Log::info("Email OTP for user {$user->id}: {$otp}");
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to send OTP via email: '.$e->getMessage());

            return false;
        }
    }
}

<?php

namespace App\Services\Auth;

use App\Models\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Config;

class JwtService
{
    private string $algorithm = 'HS256';

    private readonly string $secretKey;

    public function __construct()
    {
        $this->secretKey = Config::get('auth.jwt_secret', env('JWT_SECRET', 'your-secret-key'));
    }

    public function generateToken(User $user, int $expiresIn = 3600): string
    {
        $issuedAt = time();
        $expiresAt = $issuedAt + $expiresIn;

        $payload = [
            'iss' => Config::get('app.url', 'http://localhost'),
            'aud' => Config::get('app.url', 'http://localhost'),
            'iat' => $issuedAt,
            'exp' => $expiresAt,
            'nbf' => $issuedAt,
            'sub' => $user->id,
            'user_id' => $user->id,
            'email' => $user->email,
            'company_id' => $user->company_id ?? null,
            'roles' => $user->roles ? $user->roles->pluck('name')->toArray() : [],
            'jti' => bin2hex(random_bytes(16)), // JWT ID for uniqueness
        ];

        return JWT::encode($payload, $this->secretKey, $this->algorithm);
    }

    public function validateToken(string $token): array
    {
        try {
            $decoded = JWT::decode($token, new Key($this->secretKey, $this->algorithm));

            return (array) $decoded;
        } catch (\Exception $e) {
            throw new \Exception('Invalid token: '.$e->getMessage(), $e->getCode(), $e);
        }
    }

    public function refreshToken(string $token): string
    {
        $payload = $this->validateToken($token);
        $user = User::findOrFail($payload['user_id']);

        return $this->generateToken($user);
    }

    public function invalidateToken(string $token): void
    {
        $payload = $this->validateToken($token);

        // Add token to blacklist with expiration time
        $jti = $payload['jti'];
        $expiration = $payload['exp'] - time();

        \Cache::put("blacklisted_token_{$jti}", true, $expiration);
    }

    public function isTokenBlacklisted(string $token): bool
    {
        $payload = $this->validateToken($token);
        $jti = $payload['jti'];

        return \Cache::has("blacklisted_token_{$jti}");
    }
}

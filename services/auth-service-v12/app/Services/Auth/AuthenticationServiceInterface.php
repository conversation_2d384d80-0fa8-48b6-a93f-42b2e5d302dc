<?php

namespace App\Services\Auth;

interface AuthenticationServiceInterface
{
    /**
     * Authenticate a user
     */
    public function authenticate(string $username, string $password): array;

    /**
     * Refresh the authentication token
     */
    public function refreshToken(string $refreshToken): array;

    /**
     * Validate the authentication token
     */
    public function validateToken(string $token): bool;

    /**
     * Revoke the authentication token
     */
    public function revokeToken(string $token): bool;

    /**
     * Get the authentication method
     */
    public function getAuthMethod(): string;
}

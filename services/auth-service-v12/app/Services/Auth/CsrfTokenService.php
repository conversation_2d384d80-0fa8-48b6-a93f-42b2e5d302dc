<?php

namespace App\Services\Auth;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class CsrfTokenService
{
    public function generateToken(): string
    {
        $token = Str::random(40);
        Session::put('csrf_token', $token);

        return $token;
    }

    public function validateToken(string $token): bool
    {
        $storedToken = Session::get('csrf_token');

        if (! $storedToken || $token !== $storedToken) {
            return false;
        }

        // Regenerate token after validation for enhanced security
        $this->generateToken();

        return true;
    }
}

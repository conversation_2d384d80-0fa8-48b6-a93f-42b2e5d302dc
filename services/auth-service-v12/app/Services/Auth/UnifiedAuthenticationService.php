<?php

namespace App\Services\Auth;

use Illuminate\Support\Facades\Log;

class UnifiedAuthenticationService implements AuthenticationServiceInterface
{
    /**
     * Constructor
     */
    public function __construct(protected \App\Services\Auth\LegacyAuthenticationService $legacyService, protected \App\Services\Auth\KeycloakAuthenticationService $keycloakService, protected string $authMode = 'legacy') {}

    /**
     * Authenticate a user
     */
    public function authenticate(string $username, string $password): array
    {
        Log::info('Authentication attempt', [
            'username' => $username,
            'auth_mode' => $this->authMode,
        ]);

        // Try Keycloak authentication if enabled
        if ($this->authMode === 'keycloak' || $this->authMode === 'both') {
            $result = $this->keycloakService->authenticate($username, $password);
            if ($result['success']) {
                return $result;
            }

            // If auth mode is 'keycloak' only, return the error
            if ($this->authMode === 'keycloak') {
                return $result;
            }

            // Otherwise, log the failure and continue to legacy authentication
            Log::info('Keycloak authentication failed, falling back to legacy', [
                'username' => $username,
                'message' => $result['message'],
            ]);
        }

        // Fall back to legacy authentication if Keycloak failed or is not enabled
        if ($this->authMode === 'legacy' || $this->authMode === 'both') {
            return $this->legacyService->authenticate($username, $password);
        }

        // This should never happen, but just in case
        return [
            'success' => false,
            'message' => 'No authentication method available',
        ];
    }

    /**
     * Refresh the authentication token
     */
    public function refreshToken(string $refreshToken): array
    {
        // Determine the authentication method based on the token format
        if (str_contains($refreshToken, '.')) {
            // JWT token format, use Keycloak
            return $this->keycloakService->refreshToken($refreshToken);
        } else {
            // Sanctum token format, use legacy
            return $this->legacyService->refreshToken($refreshToken);
        }
    }

    /**
     * Validate the authentication token
     */
    public function validateToken(string $token): bool
    {
        // Determine the authentication method based on the token format
        if (str_contains($token, '.')) {
            // JWT token format, use Keycloak
            return $this->keycloakService->validateToken($token);
        } else {
            // Sanctum token format, use legacy
            return $this->legacyService->validateToken($token);
        }
    }

    /**
     * Revoke the authentication token
     */
    public function revokeToken(string $token): bool
    {
        // Determine the authentication method based on the token format
        if (str_contains($token, '.')) {
            // JWT token format, use Keycloak
            return $this->keycloakService->revokeToken($token);
        } else {
            // Sanctum token format, use legacy
            return $this->legacyService->revokeToken($token);
        }
    }

    /**
     * Get the authentication method
     */
    public function getAuthMethod(): string
    {
        return $this->authMode;
    }

    /**
     * Set the authentication method
     */
    public function setAuthMethod(string $authMode): void
    {
        $this->authMode = $authMode;
    }
}

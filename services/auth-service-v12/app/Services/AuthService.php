<?php

namespace App\Services;

use App\DTOs\Auth\LoginDTO;
use App\DTOs\Auth\ResetPasswordDTO;
use App\DTOs\Auth\UserDTO;
use App\Events\Auth\LoginFailed;
use App\Events\Auth\LoginSuccessful;
use App\Events\Auth\LogoutEvent;
use App\Events\Auth\PasswordResetCompleted;
use App\Events\Auth\PasswordResetRequested;
use App\Exceptions\Auth\InvalidCredentialsException;
use App\Exceptions\Auth\InvalidTokenException;
use App\Exceptions\Auth\UserNotFoundException;
use App\Models\PasswordReset;
use App\Models\User;
use Exception;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;

/**
 * Auth Service
 *
 * This service handles authentication-related operations.
 */
class AuthService
{
    /**
     * Constructor
     */
    public function __construct(protected AuthManager $auth, protected Dispatcher $events, protected LoggerInterface $logger) {}

    /**
     * Authenticate a user
     *
     * @throws InvalidCredentialsException If authentication fails
     */
    public function authenticate(LoginDTO $loginDTO): UserDTO
    {
        try {
            // Log authentication attempt
            $this->logger->info('Authentication attempt', [
                'username' => $loginDTO->username,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            // Determine if we're using email or username
            $field = filter_var($loginDTO->username, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';

            // Attempt authentication
            $credentials = [
                $field => $loginDTO->username,
                'password' => $loginDTO->password,
            ];

            if (! $this->auth->guard()->attempt($credentials, $loginDTO->rememberMe)) {
                // Fire login failed event
                $this->events->dispatch(new LoginFailed($loginDTO->username));

                // Log failed authentication
                $this->logger->warning('Authentication failed', [
                    'username' => $loginDTO->username,
                ]);

                throw new InvalidCredentialsException('Invalid username or password');
            }

            // Get authenticated user
            $user = $this->auth->guard()->user();

            // Create user DTO
            $userDTO = new UserDTO(
                $user->id,
                $user->first_name,
                $user->last_name,
                $user->email,
                $user->role_id,
                $user->auth_type
            );

            // Fire login successful event
            $this->events->dispatch(new LoginSuccessful($userDTO));

            // Log successful authentication
            $this->logger->info('Authentication successful', [
                'username' => $loginDTO->username,
                'user_id' => $user->id,
                'role_id' => $user->role_id,
            ]);

            return $userDTO;
        } catch (InvalidCredentialsException $e) {
            throw $e;
        } catch (Exception $e) {
            // Log exception
            $this->logger->error('Authentication error', [
                'username' => $loginDTO->username,
                'error' => $e->getMessage(),
            ]);

            throw new InvalidCredentialsException('Authentication failed: '.$e->getMessage());
        }
    }

    /**
     * Logout a user
     */
    public function logout(): void
    {
        $user = $this->auth->guard()->user();
        $userId = $user ? $user->id : null;

        // Log logout
        $this->logger->info('Logout', [
            'user_id' => $userId,
        ]);

        // Fire logout event
        $this->events->dispatch(new LogoutEvent($userId));

        // Logout user
        $this->auth->guard()->logout();
    }

    /**
     * Request password reset
     *
     * @return string The reset token
     *
     * @throws UserNotFoundException If user not found
     */
    public function requestPasswordReset(string $email): string
    {
        // Find user by email
        $user = User::where('email', $email)->first();

        if (! $user) {
            $this->logger->warning('Password reset requested for non-existent user', [
                'email' => $email,
            ]);

            throw new UserNotFoundException('No user found with this email address');
        }

        // Generate token
        $token = Str::random(60);

        // Save token
        PasswordReset::create([
            'email' => $email,
            'token' => Hash::make($token),
            'created_at' => now(),
            'expires_at' => now()->addHours(1),
            'used' => false,
        ]);

        // Fire password reset requested event
        $this->events->dispatch(new PasswordResetRequested($email));

        // Log password reset request
        $this->logger->info('Password reset requested', [
            'email' => $email,
        ]);

        return $token;
    }

    /**
     * Reset password
     *
     * @throws UserNotFoundException If user not found
     * @throws InvalidTokenException If token is invalid
     */
    public function resetPassword(ResetPasswordDTO $resetPasswordDTO): bool
    {
        // Find user by email
        $user = User::where('email', $resetPasswordDTO->email)->first();

        if (! $user) {
            $this->logger->warning('Password reset attempted for non-existent user', [
                'email' => $resetPasswordDTO->email,
            ]);

            throw new UserNotFoundException('No user found with this email address');
        }

        // Find valid token
        $passwordReset = PasswordReset::where('email', $resetPasswordDTO->email)
            ->where('used', false)
            ->where('expires_at', '>', now())
            ->latest('created_at')
            ->first();

        if (! $passwordReset || ! Hash::check($resetPasswordDTO->token, $passwordReset->token)) {
            $this->logger->warning('Invalid password reset token', [
                'email' => $resetPasswordDTO->email,
            ]);

            throw new InvalidTokenException('Invalid or expired token');
        }

        // Update password
        $user->password = Hash::make($resetPasswordDTO->password);
        $user->save();

        // Mark token as used
        $passwordReset->markAsUsed();

        // Fire password reset completed event
        $this->events->dispatch(new PasswordResetCompleted($resetPasswordDTO->email));

        // Log password reset
        $this->logger->info('Password reset completed', [
            'email' => $resetPasswordDTO->email,
        ]);

        return true;
    }

    /**
     * Get the authenticated user
     */
    public function getAuthenticatedUser(): ?UserDTO
    {
        $user = $this->auth->guard()->user();

        if (! $user) {
            return null;
        }

        return new UserDTO(
            $user->id,
            $user->first_name,
            $user->last_name,
            $user->email,
            $user->role_id,
            $user->auth_type
        );
    }
}

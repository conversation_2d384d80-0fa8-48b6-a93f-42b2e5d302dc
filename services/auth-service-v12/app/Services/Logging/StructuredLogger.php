<?php

declare(strict_types=1);

namespace App\Services\Logging;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Structured Logger Service
 *
 * Provides structured logging with correlation IDs, context, and standardized formats.
 */
class StructuredLogger
{
    private string $correlationId;
    private array $context;

    public function __construct(?string $correlationId = null)
    {
        $this->correlationId = $correlationId ?? $this->generateCorrelationId();
        $this->context = [
            'correlation_id' => $this->correlationId,
            'service' => 'auth-service',
            'version' => config('app.version', '1.0.0'),
        ];
    }

    /**
     * Generate a unique correlation ID
     */
    private function generateCorrelationId(): string
    {
        return Str::uuid()->toString();
    }

    /**
     * Get the current correlation ID
     */
    public function getCorrelationId(): string
    {
        return $this->correlationId;
    }

    /**
     * Set additional context for all log entries
     */
    public function setContext(array $context): self
    {
        $this->context = array_merge($this->context, $context);
        return $this;
    }

    /**
     * Log authentication attempt
     */
    public function logAuthAttempt(string $username, string $method, bool $success, ?string $reason = null): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'auth_attempt',
            'username' => $username,
            'auth_method' => $method,
            'success' => $success,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);

        if ($reason) {
            $context['failure_reason'] = $reason;
        }

        if ($success) {
            Log::info('Authentication attempt successful', $context);
        } else {
            Log::warning('Authentication attempt failed', $context);
        }
    }

    /**
     * Log user registration attempt
     */
    public function logUserRegistration(string $email, bool $success, ?string $reason = null): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'user_registration',
            'email' => $email,
            'success' => $success,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);

        if ($reason) {
            $context['failure_reason'] = $reason;
        }

        if ($success) {
            Log::info('User registration successful', $context);
        } else {
            Log::warning('User registration failed', $context);
        }
    }

    /**
     * Log password reset request
     */
    public function logPasswordResetRequest(string $email, bool $userExists): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'password_reset_request',
            'email' => $email,
            'user_exists' => $userExists,
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
        ]);

        Log::info('Password reset requested', $context);
    }

    /**
     * Log password reset completion
     */
    public function logPasswordResetComplete(string $email, bool $success): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'password_reset_complete',
            'email' => $email,
            'success' => $success,
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
        ]);

        if ($success) {
            Log::info('Password reset completed successfully', $context);
        } else {
            Log::warning('Password reset failed', $context);
        }
    }

    /**
     * Log MFA attempt
     */
    public function logMfaAttempt(int $userId, string $method, bool $success): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'mfa_attempt',
            'user_id' => $userId,
            'mfa_method' => $method,
            'success' => $success,
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
        ]);

        if ($success) {
            Log::info('MFA verification successful', $context);
        } else {
            Log::warning('MFA verification failed', $context);
        }
    }

    /**
     * Log token validation
     */
    public function logTokenValidation(string $tokenType, bool $valid, ?string $reason = null): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'token_validation',
            'token_type' => $tokenType,
            'valid' => $valid,
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
        ]);

        if ($reason) {
            $context['validation_result'] = $reason;
        }

        Log::info('Token validation performed', $context);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $eventType, array $details = []): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'security_event',
            'security_event_type' => $eventType,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ], $details);

        Log::warning('Security event detected', $context);
    }

    /**
     * Log API request
     */
    public function logApiRequest(string $method, string $uri, int $statusCode, float $duration): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'api_request',
            'http_method' => $method,
            'uri' => $uri,
            'status_code' => $statusCode,
            'duration_ms' => round($duration * 1000, 2),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);

        if ($statusCode >= 400) {
            Log::warning('API request completed with error', $context);
        } else {
            Log::info('API request completed successfully', $context);
        }
    }

    /**
     * Log error with context
     */
    public function logError(\Throwable $exception, array $additionalContext = []): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'error',
            'exception_class' => get_class($exception),
            'exception_message' => $exception->getMessage(),
            'exception_code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => now()->toISOString(),
        ], $additionalContext);

        Log::error('Exception occurred', $context);
    }

    /**
     * Log performance metrics
     */
    public function logPerformanceMetric(string $operation, float $duration, array $metadata = []): void
    {
        $context = array_merge($this->context, [
            'event_type' => 'performance_metric',
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'timestamp' => now()->toISOString(),
        ], $metadata);

        Log::info('Performance metric recorded', $context);
    }

    /**
     * Generic structured log method
     */
    public function log(string $level, string $message, array $context = []): void
    {
        $fullContext = array_merge($this->context, [
            'timestamp' => now()->toISOString(),
        ], $context);

        Log::log($level, $message, $fullContext);
    }
}

<?php

declare(strict_types=1);

namespace App\Services\Security;

use App\Models\User;
use App\Services\Logging\StructuredLogger;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Security Audit Service
 * 
 * Provides security auditing and compliance monitoring capabilities.
 */
class SecurityAuditService
{
    public function __construct(
        private StructuredLogger $logger
    ) {}

    /**
     * Log authentication event
     */
    public function logAuthenticationEvent(string $event, User $user, Request $request, array $context = []): void
    {
        $auditData = array_merge([
            'event_type' => 'authentication',
            'event_name' => $event,
            'user_id' => $user->id,
            'username' => $user->username,
            'email' => $user->email,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'session_id' => $request->session()->getId(),
            'timestamp' => now()->toISOString(),
        ], $context);

        $this->storeAuditLog($auditData);
        $this->logger->log('info', "Authentication event: {$event}", $auditData);
    }

    /**
     * Log authorization event
     */
    public function logAuthorizationEvent(string $event, ?User $user, Request $request, array $context = []): void
    {
        $auditData = array_merge([
            'event_type' => 'authorization',
            'event_name' => $event,
            'user_id' => $user?->id,
            'username' => $user?->username,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'resource' => $request->path(),
            'method' => $request->method(),
            'timestamp' => now()->toISOString(),
        ], $context);

        $this->storeAuditLog($auditData);
        $this->logger->log('info', "Authorization event: {$event}", $auditData);
    }

    /**
     * Log data access event
     */
    public function logDataAccessEvent(string $event, User $user, string $resource, array $context = []): void
    {
        $auditData = array_merge([
            'event_type' => 'data_access',
            'event_name' => $event,
            'user_id' => $user->id,
            'username' => $user->username,
            'resource' => $resource,
            'timestamp' => now()->toISOString(),
        ], $context);

        $this->storeAuditLog($auditData);
        $this->logger->log('info', "Data access event: {$event}", $auditData);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $event, Request $request, array $context = []): void
    {
        $auditData = array_merge([
            'event_type' => 'security',
            'event_name' => $event,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'path' => $request->path(),
            'method' => $request->method(),
            'timestamp' => now()->toISOString(),
        ], $context);

        $this->storeAuditLog($auditData);
        $this->logger->log('warning', "Security event: {$event}", $auditData);
    }

    /**
     * Log administrative action
     */
    public function logAdministrativeAction(string $action, User $admin, array $context = []): void
    {
        $auditData = array_merge([
            'event_type' => 'administrative',
            'event_name' => $action,
            'admin_id' => $admin->id,
            'admin_username' => $admin->username,
            'timestamp' => now()->toISOString(),
        ], $context);

        $this->storeAuditLog($auditData);
        $this->logger->log('info', "Administrative action: {$action}", $auditData);
    }

    /**
     * Generate security audit report
     */
    public function generateAuditReport(string $startDate, string $endDate): array
    {
        $start = \Carbon\Carbon::parse($startDate);
        $end = \Carbon\Carbon::parse($endDate);

        return [
            'period' => [
                'start' => $start->toISOString(),
                'end' => $end->toISOString(),
            ],
            'authentication_events' => $this->getAuthenticationEventsSummary($start, $end),
            'authorization_events' => $this->getAuthorizationEventsSummary($start, $end),
            'security_events' => $this->getSecurityEventsSummary($start, $end),
            'failed_logins' => $this->getFailedLoginsSummary($start, $end),
            'suspicious_activities' => $this->getSuspiciousActivitiesSummary($start, $end),
            'user_activities' => $this->getUserActivitiesSummary($start, $end),
        ];
    }

    /**
     * Check compliance status
     */
    public function checkComplianceStatus(): array
    {
        return [
            'password_policy' => $this->checkPasswordPolicyCompliance(),
            'session_management' => $this->checkSessionManagementCompliance(),
            'audit_logging' => $this->checkAuditLoggingCompliance(),
            'access_controls' => $this->checkAccessControlsCompliance(),
            'data_protection' => $this->checkDataProtectionCompliance(),
        ];
    }

    /**
     * Store audit log in database
     */
    private function storeAuditLog(array $data): void
    {
        try {
            DB::table('audit_logs')->insert([
                'event_type' => $data['event_type'],
                'event_name' => $data['event_name'],
                'user_id' => $data['user_id'] ?? null,
                'ip_address' => $data['ip_address'] ?? null,
                'user_agent' => $data['user_agent'] ?? null,
                'resource' => $data['resource'] ?? null,
                'method' => $data['method'] ?? null,
                'context' => json_encode($data),
                'created_at' => now(),
            ]);
        } catch (\Exception $e) {
            $this->logger->logError($e, ['context' => 'audit_log_storage']);
        }
    }

    /**
     * Get authentication events summary
     */
    private function getAuthenticationEventsSummary(\Carbon\Carbon $start, \Carbon\Carbon $end): array
    {
        $events = DB::table('audit_logs')
            ->where('event_type', 'authentication')
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('event_name, COUNT(*) as count')
            ->groupBy('event_name')
            ->get();

        return $events->pluck('count', 'event_name')->toArray();
    }

    /**
     * Get authorization events summary
     */
    private function getAuthorizationEventsSummary(\Carbon\Carbon $start, \Carbon\Carbon $end): array
    {
        $events = DB::table('audit_logs')
            ->where('event_type', 'authorization')
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('event_name, COUNT(*) as count')
            ->groupBy('event_name')
            ->get();

        return $events->pluck('count', 'event_name')->toArray();
    }

    /**
     * Get security events summary
     */
    private function getSecurityEventsSummary(\Carbon\Carbon $start, \Carbon\Carbon $end): array
    {
        $events = DB::table('audit_logs')
            ->where('event_type', 'security')
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('event_name, COUNT(*) as count')
            ->groupBy('event_name')
            ->get();

        return $events->pluck('count', 'event_name')->toArray();
    }

    /**
     * Get failed logins summary
     */
    private function getFailedLoginsSummary(\Carbon\Carbon $start, \Carbon\Carbon $end): array
    {
        return DB::table('audit_logs')
            ->where('event_type', 'authentication')
            ->where('event_name', 'login_failed')
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('ip_address, COUNT(*) as attempts')
            ->groupBy('ip_address')
            ->orderByDesc('attempts')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get suspicious activities summary
     */
    private function getSuspiciousActivitiesSummary(\Carbon\Carbon $start, \Carbon\Carbon $end): array
    {
        return DB::table('audit_logs')
            ->where('event_type', 'security')
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('ip_address, event_name, COUNT(*) as count')
            ->groupBy('ip_address', 'event_name')
            ->orderByDesc('count')
            ->limit(20)
            ->get()
            ->toArray();
    }

    /**
     * Get user activities summary
     */
    private function getUserActivitiesSummary(\Carbon\Carbon $start, \Carbon\Carbon $end): array
    {
        return DB::table('audit_logs')
            ->whereNotNull('user_id')
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('user_id, COUNT(*) as activity_count')
            ->groupBy('user_id')
            ->orderByDesc('activity_count')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Check password policy compliance
     */
    private function checkPasswordPolicyCompliance(): array
    {
        // Implementation would check actual password policies
        return [
            'status' => 'compliant',
            'issues' => [],
        ];
    }

    /**
     * Check session management compliance
     */
    private function checkSessionManagementCompliance(): array
    {
        // Implementation would check session configurations
        return [
            'status' => 'compliant',
            'issues' => [],
        ];
    }

    /**
     * Check audit logging compliance
     */
    private function checkAuditLoggingCompliance(): array
    {
        // Implementation would check audit logging configurations
        return [
            'status' => 'compliant',
            'issues' => [],
        ];
    }

    /**
     * Check access controls compliance
     */
    private function checkAccessControlsCompliance(): array
    {
        // Implementation would check access control configurations
        return [
            'status' => 'compliant',
            'issues' => [],
        ];
    }

    /**
     * Check data protection compliance
     */
    private function checkDataProtectionCompliance(): array
    {
        // Implementation would check data protection measures
        return [
            'status' => 'compliant',
            'issues' => [],
        ];
    }
}

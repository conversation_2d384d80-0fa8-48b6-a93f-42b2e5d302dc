<?php

declare(strict_types=1);

namespace App\Services\Security;

use App\Services\Logging\StructuredLogger;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Intrusion Detection Service
 *
 * Monitors and detects suspicious activities and potential security threats.
 */
class IntrusionDetectionService
{
    private const FAILED_LOGIN_THRESHOLD = 5;
    private const FAILED_LOGIN_WINDOW = 300; // 5 minutes
    private const SUSPICIOUS_ACTIVITY_THRESHOLD = 10;
    private const SUSPICIOUS_ACTIVITY_WINDOW = 600; // 10 minutes
    private const IP_BLOCK_DURATION = 3600; // 1 hour

    public function __construct(
        private StructuredLogger $logger
    ) {}

    /**
     * Analyze request for suspicious activity
     */
    public function analyzeRequest(Request $request): array
    {
        $threats = [];
        $riskScore = 0;

        // Check for blocked IPs
        if ($this->isIpBlocked($request->ip())) {
            $threats[] = 'blocked_ip';
            $riskScore += 100;
        }

        // Check for suspicious user agent
        if ($this->isSuspiciousUserAgent($request->userAgent())) {
            $threats[] = 'suspicious_user_agent';
            $riskScore += 30;
        }

        // Check for rapid requests
        if ($this->isRapidRequests($request)) {
            $threats[] = 'rapid_requests';
            $riskScore += 40;
        }

        // Check for geographic anomalies (simplified)
        if ($this->isGeographicAnomaly($request)) {
            $threats[] = 'geographic_anomaly';
            $riskScore += 25;
        }

        // Check for suspicious request patterns
        if ($this->hasSuspiciousRequestPattern($request)) {
            $threats[] = 'suspicious_request_pattern';
            $riskScore += 50;
        }

        return [
            'threats' => $threats,
            'risk_score' => $riskScore,
            'action' => $this->determineAction($riskScore),
        ];
    }

    /**
     * Record failed login attempt
     */
    public function recordFailedLogin(string $identifier, Request $request): void
    {
        $key = "failed_login:{$identifier}";
        $attempts = Cache::get($key, 0) + 1;

        Cache::put($key, $attempts, self::FAILED_LOGIN_WINDOW);

        $this->logger->logSecurityEvent('failed_login_recorded', [
            'identifier' => $identifier,
            'attempts' => $attempts,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Check if threshold exceeded
        if ($attempts >= self::FAILED_LOGIN_THRESHOLD) {
            $this->handleFailedLoginThresholdExceeded($identifier, $request);
        }
    }

    /**
     * Record suspicious activity
     */
    public function recordSuspiciousActivity(string $type, Request $request, array $details = []): void
    {
        $ip = $request->ip();
        $key = "suspicious_activity:{$ip}";
        $count = Cache::get($key, 0) + 1;

        Cache::put($key, $count, self::SUSPICIOUS_ACTIVITY_WINDOW);

        $this->logger->logSecurityEvent('suspicious_activity', array_merge([
            'type' => $type,
            'ip_address' => $ip,
            'count' => $count,
            'user_agent' => $request->userAgent(),
            'path' => $request->path(),
        ], $details));

        // Check if threshold exceeded
        if ($count >= self::SUSPICIOUS_ACTIVITY_THRESHOLD) {
            $this->blockIp($ip, 'suspicious_activity_threshold');
        }
    }

    /**
     * Check if IP is blocked
     */
    public function isIpBlocked(string $ip): bool
    {
        return Cache::has("blocked_ip:{$ip}");
    }

    /**
     * Block an IP address
     */
    public function blockIp(string $ip, string $reason): void
    {
        Cache::put("blocked_ip:{$ip}", [
            'reason' => $reason,
            'blocked_at' => now()->toISOString(),
        ], self::IP_BLOCK_DURATION);

        $this->logger->logSecurityEvent('ip_blocked', [
            'ip_address' => $ip,
            'reason' => $reason,
            'duration' => self::IP_BLOCK_DURATION,
        ]);
    }

    /**
     * Unblock an IP address
     */
    public function unblockIp(string $ip): void
    {
        Cache::forget("blocked_ip:{$ip}");

        $this->logger->logSecurityEvent('ip_unblocked', [
            'ip_address' => $ip,
        ]);
    }

    /**
     * Check for suspicious user agent
     */
    private function isSuspiciousUserAgent(?string $userAgent): bool
    {
        if (!$userAgent) {
            return true;
        }

        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/java/i',
            '/perl/i',
            '/ruby/i',
            '/php/i',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for rapid requests from same IP
     */
    private function isRapidRequests(Request $request): bool
    {
        $ip = $request->ip();
        $key = "rapid_requests:{$ip}";
        $requests = Cache::get($key, []);

        $now = time();
        $requests[] = $now;

        // Keep only requests from last minute
        $requests = array_filter($requests, fn($time) => $now - $time <= 60);

        Cache::put($key, $requests, 60);

        // More than 30 requests per minute is suspicious
        return count($requests) > 30;
    }

    /**
     * Check for geographic anomalies (simplified implementation)
     */
    private function isGeographicAnomaly(Request $request): bool
    {
        // This is a simplified implementation
        // In production, you would use a GeoIP service

        $user = $request->user();
        if (!$user) {
            return false;
        }

        // Check if user has logged in from different countries recently
        $currentCountry = $this->getCountryFromIp($request->ip());
        $recentCountries = Cache::get("user_countries:{$user->id}", []);

        if (!in_array($currentCountry, $recentCountries) && count($recentCountries) > 0) {
            // New country detected
            $recentCountries[] = $currentCountry;
            Cache::put("user_countries:{$user->id}", array_slice($recentCountries, -5), 86400 * 7); // Keep for 7 days
            return true;
        }

        return false;
    }

    /**
     * Get country from IP using GeoIP service
     */
    private function getCountryFromIp(string $ip): string
    {
        try {
            $geoIpService = app(GeoIpService::class);
            return $geoIpService->getCountry($ip);
        } catch (\Exception $e) {
            Log::error('GeoIP service error: ' . $e->getMessage());
            return 'Unknown';
        }
    }

    /**
     * Check for suspicious request patterns
     */
    private function hasSuspiciousRequestPattern(Request $request): bool
    {
        $path = $request->path();
        $method = $request->method();

        // Check for common attack patterns
        $suspiciousPatterns = [
            '/\.\.\//',
            '/\/etc\/passwd/',
            '/\/proc\//',
            '/\/sys\//',
            '/admin/',
            '/wp-admin/',
            '/phpmyadmin/',
            '/\.env/',
            '/\.git/',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Handle failed login threshold exceeded
     */
    private function handleFailedLoginThresholdExceeded(string $identifier, Request $request): void
    {
        $ip = $request->ip();

        $this->logger->logSecurityEvent('failed_login_threshold_exceeded', [
            'identifier' => $identifier,
            'ip_address' => $ip,
            'threshold' => self::FAILED_LOGIN_THRESHOLD,
        ]);

        // Block the IP temporarily
        $this->blockIp($ip, 'failed_login_threshold');
    }

    /**
     * Determine action based on risk score
     */
    private function determineAction(int $riskScore): string
    {
        if ($riskScore >= 100) {
            return 'block';
        } elseif ($riskScore >= 70) {
            return 'challenge';
        } elseif ($riskScore >= 40) {
            return 'monitor';
        }

        return 'allow';
    }
}

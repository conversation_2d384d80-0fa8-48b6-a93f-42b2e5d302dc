<?php

declare(strict_types=1);

namespace App\Services\Security;

use GeoIp2\Database\Reader;
use GeoIp2\Exception\AddressNotFoundException;
use GeoIp2\Exception\InvalidDatabaseException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use MaxMind\Db\Reader\InvalidDatabaseException as DbInvalidDatabaseException;

/**
 * GeoIP Service
 * 
 * Provides geographic location information for IP addresses using MaxMind GeoLite2 database
 * with fallback to external APIs for enhanced security monitoring.
 */
class GeoIpService
{
    private ?Reader $cityReader = null;
    private ?Reader $countryReader = null;
    private array $config;
    private array $whitelist;

    public function __construct()
    {
        $this->config = config('geoip');
        $this->whitelist = $this->config['whitelist'] ?? [];
        $this->initializeReaders();
    }

    /**
     * Get location information for an IP address
     */
    public function getLocation(string $ip): array
    {
        // Check if IP is whitelisted (internal/local)
        if ($this->isWhitelisted($ip)) {
            return $this->getDefaultLocation();
        }

        // Check cache first
        if ($this->config['cache']['enabled']) {
            $cacheKey = $this->getCacheKey($ip);
            $cached = Cache::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        // Try MaxMind database first
        $location = $this->getLocationFromMaxMind($ip);

        // Fallback to external API if MaxMind fails
        if ($this->isDefaultLocation($location) && $this->config['fallback']['enabled']) {
            $location = $this->getLocationFromFallback($ip);
        }

        // Cache the result
        if ($this->config['cache']['enabled']) {
            Cache::put($this->getCacheKey($ip), $location, $this->config['cache']['ttl']);
        }

        // Log the lookup if enabled
        if ($this->config['security']['log_lookups']) {
            $this->logLookup($ip, $location);
        }

        return $location;
    }

    /**
     * Get country information for an IP address
     */
    public function getCountry(string $ip): string
    {
        $location = $this->getLocation($ip);
        return $location['country'] ?? $this->config['defaults']['country'];
    }

    /**
     * Get country code for an IP address
     */
    public function getCountryCode(string $ip): string
    {
        $location = $this->getLocation($ip);
        return $location['country_code'] ?? $this->config['defaults']['country_code'];
    }

    /**
     * Check if an IP is from a blocked country
     */
    public function isBlockedCountry(string $ip): bool
    {
        $blockedCountries = $this->getBlockedCountries();
        if (empty($blockedCountries)) {
            return false;
        }

        $countryCode = $this->getCountryCode($ip);
        return in_array($countryCode, $blockedCountries);
    }

    /**
     * Check if an IP is from an allowed country
     */
    public function isAllowedCountry(string $ip): bool
    {
        $allowedCountries = $this->getAllowedCountries();
        if (empty($allowedCountries)) {
            return true; // If no restrictions, allow all
        }

        $countryCode = $this->getCountryCode($ip);
        return in_array($countryCode, $allowedCountries);
    }

    /**
     * Initialize MaxMind database readers
     */
    private function initializeReaders(): void
    {
        try {
            $cityDbPath = $this->config['maxmind']['database_path'];
            if (file_exists($cityDbPath)) {
                $this->cityReader = new Reader($cityDbPath);
            }

            $countryDbPath = $this->config['maxmind']['country_database_path'];
            if (file_exists($countryDbPath)) {
                $this->countryReader = new Reader($countryDbPath);
            }
        } catch (InvalidDatabaseException | DbInvalidDatabaseException $e) {
            Log::error('Failed to initialize GeoIP readers: ' . $e->getMessage());
        }
    }

    /**
     * Get location from MaxMind database
     */
    private function getLocationFromMaxMind(string $ip): array
    {
        try {
            if ($this->cityReader) {
                $record = $this->cityReader->city($ip);
                return [
                    'country' => $record->country->name ?? $this->config['defaults']['country'],
                    'country_code' => $record->country->isoCode ?? $this->config['defaults']['country_code'],
                    'city' => $record->city->name ?? $this->config['defaults']['city'],
                    'timezone' => $record->location->timeZone ?? $this->config['defaults']['timezone'],
                    'latitude' => $record->location->latitude ?? $this->config['defaults']['latitude'],
                    'longitude' => $record->location->longitude ?? $this->config['defaults']['longitude'],
                    'source' => 'maxmind',
                ];
            } elseif ($this->countryReader) {
                $record = $this->countryReader->country($ip);
                return [
                    'country' => $record->country->name ?? $this->config['defaults']['country'],
                    'country_code' => $record->country->isoCode ?? $this->config['defaults']['country_code'],
                    'city' => $this->config['defaults']['city'],
                    'timezone' => $this->config['defaults']['timezone'],
                    'latitude' => $this->config['defaults']['latitude'],
                    'longitude' => $this->config['defaults']['longitude'],
                    'source' => 'maxmind',
                ];
            }
        } catch (AddressNotFoundException $e) {
            Log::debug("IP address not found in MaxMind database: {$ip}");
        } catch (\Exception $e) {
            Log::error("MaxMind lookup error for IP {$ip}: " . $e->getMessage());
        }

        return $this->getDefaultLocation();
    }

    /**
     * Get location from fallback API service
     */
    private function getLocationFromFallback(string $ip): array
    {
        try {
            $timeout = $this->config['fallback']['timeout'];
            $service = $this->config['fallback']['service'];

            switch ($service) {
                case 'ip-api':
                    return $this->getLocationFromIpApi($ip, $timeout);
                case 'ipinfo':
                    return $this->getLocationFromIpInfo($ip, $timeout);
                default:
                    Log::warning("Unknown fallback service: {$service}");
                    return $this->getDefaultLocation();
            }
        } catch (\Exception $e) {
            Log::error("Fallback GeoIP lookup error for IP {$ip}: " . $e->getMessage());
            return $this->getDefaultLocation();
        }
    }

    /**
     * Get location from ip-api.com
     */
    private function getLocationFromIpApi(string $ip, int $timeout): array
    {
        $response = Http::timeout($timeout)->get("http://ip-api.com/json/{$ip}");
        
        if ($response->successful()) {
            $data = $response->json();
            if ($data['status'] === 'success') {
                return [
                    'country' => $data['country'] ?? $this->config['defaults']['country'],
                    'country_code' => $data['countryCode'] ?? $this->config['defaults']['country_code'],
                    'city' => $data['city'] ?? $this->config['defaults']['city'],
                    'timezone' => $data['timezone'] ?? $this->config['defaults']['timezone'],
                    'latitude' => $data['lat'] ?? $this->config['defaults']['latitude'],
                    'longitude' => $data['lon'] ?? $this->config['defaults']['longitude'],
                    'source' => 'ip-api',
                ];
            }
        }

        return $this->getDefaultLocation();
    }

    /**
     * Get location from ipinfo.io
     */
    private function getLocationFromIpInfo(string $ip, int $timeout): array
    {
        $response = Http::timeout($timeout)->get("https://ipinfo.io/{$ip}/json");
        
        if ($response->successful()) {
            $data = $response->json();
            $location = explode(',', $data['loc'] ?? '0,0');
            
            return [
                'country' => $data['country'] ?? $this->config['defaults']['country'],
                'country_code' => $data['country'] ?? $this->config['defaults']['country_code'],
                'city' => $data['city'] ?? $this->config['defaults']['city'],
                'timezone' => $data['timezone'] ?? $this->config['defaults']['timezone'],
                'latitude' => (float)($location[0] ?? $this->config['defaults']['latitude']),
                'longitude' => (float)($location[1] ?? $this->config['defaults']['longitude']),
                'source' => 'ipinfo',
            ];
        }

        return $this->getDefaultLocation();
    }

    /**
     * Get default location data
     */
    private function getDefaultLocation(): array
    {
        return [
            'country' => $this->config['defaults']['country'],
            'country_code' => $this->config['defaults']['country_code'],
            'city' => $this->config['defaults']['city'],
            'timezone' => $this->config['defaults']['timezone'],
            'latitude' => $this->config['defaults']['latitude'],
            'longitude' => $this->config['defaults']['longitude'],
            'source' => 'default',
        ];
    }

    /**
     * Check if location is default/unknown
     */
    private function isDefaultLocation(array $location): bool
    {
        return $location['country'] === $this->config['defaults']['country'] ||
               $location['source'] === 'default';
    }

    /**
     * Check if IP is whitelisted
     */
    private function isWhitelisted(string $ip): bool
    {
        foreach ($this->whitelist as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if IP is in CIDR range
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $mask) = explode('/', $range);
        
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
        }
        
        // IPv6 support would go here
        return false;
    }

    /**
     * Get cache key for IP
     */
    private function getCacheKey(string $ip): string
    {
        $prefix = $this->config['cache']['prefix'];
        return "{$prefix}:location:" . md5($ip);
    }

    /**
     * Get blocked countries list
     */
    private function getBlockedCountries(): array
    {
        $countries = $this->config['security']['blocked_countries'];
        return $countries ? explode(',', strtoupper($countries)) : [];
    }

    /**
     * Get allowed countries list
     */
    private function getAllowedCountries(): array
    {
        $countries = $this->config['security']['allowed_countries'];
        return $countries ? explode(',', strtoupper($countries)) : [];
    }

    /**
     * Log GeoIP lookup
     */
    private function logLookup(string $ip, array $location): void
    {
        Log::info('GeoIP lookup performed', [
            'ip' => $ip,
            'country' => $location['country'],
            'country_code' => $location['country_code'],
            'city' => $location['city'],
            'source' => $location['source'],
        ]);
    }
}

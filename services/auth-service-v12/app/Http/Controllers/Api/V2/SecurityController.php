<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V2\Security\AuditReportRequest;
use App\Http\Requests\Api\V2\Security\BlockIpRequest;
use App\Http\Requests\Api\V2\Security\UnblockIpRequest;
use App\Models\AuditLog;
use App\Services\Security\IntrusionDetectionService;
use App\Services\Security\SecurityAuditService;
use App\Traits\ResponseHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * Security Controller
 *
 * Handles security administration endpoints.
 */
class SecurityController extends Controller
{
    use ResponseHelper;

    public function __construct(
        private IntrusionDetectionService $intrusionDetection,
        private SecurityAuditService $auditService
    ) {}

    /**
     * Get security dashboard data
     */
    public function dashboard(Request $request): JsonResponse
    {
        $dashboardData = [
            'blocked_ips' => $this->getBlockedIps(),
            'recent_threats' => $this->getRecentThreats(),
            'security_metrics' => $this->getSecurityMetrics(),
            'compliance_status' => $this->auditService->checkComplianceStatus(),
        ];

        return $this->successResponse('Security dashboard data retrieved', $dashboardData);
    }

    /**
     * Generate audit report
     */
    public function auditReport(AuditReportRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $report = $this->auditService->generateAuditReport(
            $validated['start_date'],
            $validated['end_date']
        );

        return $this->successResponse('Audit report generated successfully', $report);
    }

    /**
     * Get blocked IPs
     */
    public function blockedIps(): JsonResponse
    {
        $blockedIps = $this->getBlockedIps();

        return $this->successResponse('Blocked IPs retrieved', $blockedIps);
    }

    /**
     * Block an IP address
     */
    public function blockIp(BlockIpRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $this->intrusionDetection->blockIp(
            $validated['ip_address'],
            $validated['reason'] ?? 'Manual block'
        );

        // Log the administrative action
        $this->auditService->logAdministrativeAction(
            'ip_blocked',
            $request->user(),
            [
                'ip_address' => $validated['ip_address'],
                'reason' => $validated['reason'] ?? 'Manual block',
            ]
        );

        return $this->successResponse('IP address blocked successfully');
    }

    /**
     * Unblock an IP address
     */
    public function unblockIp(UnblockIpRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $this->intrusionDetection->unblockIp($validated['ip_address']);

        // Log the administrative action
        $this->auditService->logAdministrativeAction(
            'ip_unblocked',
            $request->user(),
            [
                'ip_address' => $validated['ip_address'],
            ]
        );

        return $this->successResponse('IP address unblocked successfully');
    }

    /**
     * Get security events
     */
    public function events(Request $request): JsonResponse
    {
        $page = (int) $request->get('page', 1);
        $limit = min((int) $request->get('limit', 50), 100);
        $eventType = $request->get('event_type');

        $events = $this->getSecurityEvents($page, $limit, $eventType);

        return $this->successResponse('Security events retrieved', $events);
    }

    /**
     * Get threat analysis for an IP
     */
    public function threatAnalysis(Request $request): JsonResponse
    {
        $ip = $request->get('ip');

        if (!$ip) {
            return $this->errorResponse('IP address is required', null, 400);
        }

        $analysis = $this->analyzeThreat($ip);

        return $this->successResponse('Threat analysis completed', $analysis);
    }

    /**
     * Get compliance report
     */
    public function compliance(): JsonResponse
    {
        $compliance = $this->auditService->checkComplianceStatus();

        return $this->successResponse('Compliance report generated', $compliance);
    }

    /**
     * Get blocked IPs from cache
     */
    private function getBlockedIps(): array
    {
        $blockedIps = [];
        $cacheKeys = Cache::get('blocked_ip_keys', []);

        foreach ($cacheKeys as $key) {
            $ipData = Cache::get($key);
            if ($ipData) {
                $ip = str_replace('blocked_ip:', '', $key);
                $blockedIps[] = [
                    'ip' => $ip,
                    'reason' => $ipData['reason'] ?? 'Unknown',
                    'blocked_at' => $ipData['blocked_at'] ?? null,
                    'expires_at' => Cache::get($key . '_expires'),
                ];
            }
        }

        return [
            'total' => count($blockedIps),
            'ips' => $blockedIps,
        ];
    }

    /**
     * Get recent security threats
     */
    private function getRecentThreats(): array
    {
        $threats = AuditLog::securityEvents()
            ->recent(24)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->id,
                    'event_name' => $log->event_name,
                    'event_description' => $log->getEventDescription(),
                    'ip_address' => $log->ip_address,
                    'risk_level' => $log->getRiskLevel(),
                    'created_at' => $log->created_at->toISOString(),
                    'context' => $log->context,
                ];
            });

        return [
            'total' => $threats->count(),
            'threats' => $threats->toArray(),
        ];
    }

    /**
     * Get security metrics
     */
    private function getSecurityMetrics(): array
    {
        $last24Hours = now()->subHours(24);

        return [
            'failed_logins_24h' => AuditLog::failedLogins()
                ->where('created_at', '>=', $last24Hours)
                ->count(),
            'blocked_ips_24h' => AuditLog::where('event_name', 'ip_blocked')
                ->where('created_at', '>=', $last24Hours)
                ->count(),
            'suspicious_activities_24h' => AuditLog::where('event_name', 'suspicious_activity')
                ->where('created_at', '>=', $last24Hours)
                ->count(),
            'security_events_24h' => AuditLog::securityEvents()
                ->where('created_at', '>=', $last24Hours)
                ->count(),
        ];
    }

    /**
     * Get security events with pagination
     */
    private function getSecurityEvents(int $page, int $limit, ?string $eventType): array
    {
        $query = AuditLog::securityEvents()
            ->orderBy('created_at', 'desc')
            ->with('user:id,username,email');

        if ($eventType) {
            $query->where('event_name', $eventType);
        }

        $events = $query->paginate($limit, ['*'], 'page', $page);

        return [
            'current_page' => $events->currentPage(),
            'per_page' => $events->perPage(),
            'total' => $events->total(),
            'last_page' => $events->lastPage(),
            'data' => collect($events->items())->map(function ($log) {
                return [
                    'id' => $log->id,
                    'event_type' => $log->event_type,
                    'event_name' => $log->event_name,
                    'event_description' => $log->getEventDescription(),
                    'user' => $log->user ? [
                        'id' => $log->user->id,
                        'username' => $log->user->username,
                        'email' => $log->user->email,
                    ] : null,
                    'ip_address' => $log->ip_address,
                    'user_agent' => $log->user_agent,
                    'resource' => $log->resource,
                    'method' => $log->method,
                    'risk_level' => $log->getRiskLevel(),
                    'context' => $log->context,
                    'created_at' => $log->created_at->toISOString(),
                ];
            })->toArray(),
        ];
    }

    /**
     * Analyze threat for specific IP
     */
    private function analyzeThreat(string $ip): array
    {
        // Get geographic information using GeoIP service
        $geoInfo = ['country' => 'Unknown', 'city' => 'Unknown'];
        try {
            $geoIpService = app(\App\Services\Security\GeoIpService::class);
            $location = $geoIpService->getLocation($ip);
            $geoInfo = [
                'country' => $location['country'],
                'city' => $location['city'],
                'country_code' => $location['country_code'],
                'latitude' => $location['latitude'],
                'longitude' => $location['longitude'],
            ];
        } catch (\Exception $e) {
            \Log::error('GeoIP lookup failed for threat analysis: ' . $e->getMessage());
        }

        // Analyze threat level based on various factors
        $threatLevel = $this->calculateThreatLevel($ip);

        // Get recent activities for this IP
        $recentActivities = $this->getRecentActivitiesForIp($ip);

        return [
            'ip_address' => $ip,
            'is_blocked' => $this->intrusionDetection->isIpBlocked($ip),
            'threat_level' => $threatLevel,
            'recent_activities' => $recentActivities,
            'geographic_info' => $geoInfo,
            'reputation' => [
                'score' => $this->calculateReputationScore($ip),
                'sources' => ['internal'],
            ],
        ];
    }

    /**
     * Calculate threat level for IP
     */
    private function calculateThreatLevel(string $ip): string
    {
        $score = 0;

        // Check if IP is blocked
        if ($this->intrusionDetection->isIpBlocked($ip)) {
            $score += 100;
        }

        // Check failed login attempts
        $failedLogins = \Cache::get("failed_login:{$ip}", 0);
        $score += $failedLogins * 10;

        // Check suspicious activities
        $suspiciousCount = \Cache::get("suspicious_activity:{$ip}", 0);
        $score += $suspiciousCount * 15;

        if ($score >= 100) return 'critical';
        if ($score >= 70) return 'high';
        if ($score >= 40) return 'medium';
        return 'low';
    }

    /**
     * Get recent activities for IP
     */
    private function getRecentActivitiesForIp(string $ip): array
    {
        // This would typically query audit logs
        // For now, return basic cache-based data
        $activities = [];

        $failedLogins = \Cache::get("failed_login:{$ip}", 0);
        if ($failedLogins > 0) {
            $activities[] = [
                'type' => 'failed_login',
                'count' => $failedLogins,
                'last_seen' => now()->subMinutes(rand(1, 60))->toISOString(),
            ];
        }

        $suspiciousCount = \Cache::get("suspicious_activity:{$ip}", 0);
        if ($suspiciousCount > 0) {
            $activities[] = [
                'type' => 'suspicious_activity',
                'count' => $suspiciousCount,
                'last_seen' => now()->subMinutes(rand(1, 30))->toISOString(),
            ];
        }

        return $activities;
    }

    /**
     * Calculate reputation score for IP
     */
    private function calculateReputationScore(string $ip): int
    {
        $score = 100; // Start with neutral score

        // Reduce score based on negative activities
        $failedLogins = \Cache::get("failed_login:{$ip}", 0);
        $score -= $failedLogins * 5;

        $suspiciousCount = \Cache::get("suspicious_activity:{$ip}", 0);
        $score -= $suspiciousCount * 10;

        if ($this->intrusionDetection->isIpBlocked($ip)) {
            $score -= 50;
        }

        return max(0, min(100, $score));
    }
}

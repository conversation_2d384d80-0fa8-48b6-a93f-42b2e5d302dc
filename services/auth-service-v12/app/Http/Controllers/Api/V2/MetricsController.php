<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\Logging\StructuredLogger;
use App\Traits\ResponseHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Prometheus\CollectorRegistry;
use Prometheus\RenderTextFormat;
use Prometheus\Storage\APC;
use Prometheus\Storage\InMemory;

class MetricsController extends Controller
{
    use ResponseHelper;

    /**
     * The Prometheus registry.
     *
     * @var CollectorRegistry
     */
    protected $registry;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        private StructuredLogger $logger
    ) {
        // Use InMemory storage if APCu is not available (e.g., in testing)
        try {
            $this->registry = new CollectorRegistry(new APC);
        } catch (\Exception $e) {
            $this->registry = new CollectorRegistry(new InMemory);
        }
    }

    /**
     * Export metrics in Prometheus format.
     */
    public function export(): Response
    {
        // HTTP request duration metrics
        $this->registry->getOrRegisterHistogram(
            'http',
            'request_duration_seconds',
            'HTTP request duration in seconds',
            ['handler', 'method', 'status'],
            [0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]
        );

        // HTTP request counter
        $this->registry->getOrRegisterCounter(
            'http',
            'requests_total',
            'Total number of HTTP requests',
            ['handler', 'method', 'status']
        );

        // Authentication metrics
        $this->registry->getOrRegisterCounter(
            'auth',
            'attempts_total',
            'Total number of authentication attempts',
            ['status', 'method']
        );

        $authTokens = $this->registry->getOrRegisterGauge(
            'auth',
            'active_tokens',
            'Number of active authentication tokens',
            []
        );

        // Circuit breaker metrics
        $circuitBreakerState = $this->registry->getOrRegisterGauge(
            'circuit_breaker',
            'state',
            'Circuit breaker state (0 = closed, 1 = open)',
            ['service']
        );

        // Cache metrics
        $cacheHitRatio = $this->registry->getOrRegisterGauge(
            'cache',
            'hit_ratio',
            'Cache hit ratio',
            []
        );

        // Database metrics
        $this->registry->getOrRegisterHistogram(
            'db',
            'query_duration_seconds',
            'Database query duration in seconds',
            ['query_type'],
            [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1]
        );

        // Collect metrics from cache
        $this->collectCacheMetrics($cacheHitRatio);

        // Collect circuit breaker metrics
        $this->collectCircuitBreakerMetrics($circuitBreakerState);

        // Collect authentication metrics
        $this->collectAuthMetrics($authTokens);

        // Render metrics
        $renderer = new RenderTextFormat;
        $result = $renderer->render($this->registry->getMetricFamilySamples());

        return response($result, 200)
            ->header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    }

    /**
     * Collect cache metrics.
     *
     * @param  \Prometheus\Gauge  $cacheHitRatio
     */
    protected function collectCacheMetrics($cacheHitRatio): void
    {
        $hits = Cache::get('cache_hits', 0);
        $misses = Cache::get('cache_misses', 0);

        $total = $hits + $misses;
        $ratio = $total > 0 ? $hits / $total : 0;

        $cacheHitRatio->set($ratio * 100);
    }

    /**
     * Collect circuit breaker metrics.
     *
     * @param  \Prometheus\Gauge  $circuitBreakerState
     */
    protected function collectCircuitBreakerMetrics($circuitBreakerState): void
    {
        $services = [
            'customer_service',
        ];

        foreach ($services as $service) {
            $cacheKey = 'circuit_breaker:'.$service;
            $state = Cache::get($cacheKey, ['open' => false]);

            $circuitBreakerState->set($state['open'] ? 1 : 0, [$service]);
        }
    }

    /**
     * Collect authentication metrics.
     *
     * @param  \Prometheus\Gauge  $authTokens
     */
    protected function collectAuthMetrics($authTokens): void
    {
        // Count active tokens
        $activeTokens = DB::table('personal_access_tokens')
            ->where('expires_at', '>', now())
            ->count();

        $authTokens->set($activeTokens);
    }

    /**
     * Get metrics in JSON format
     */
    public function json(Request $request): JsonResponse
    {
        $metrics = $this->collectApplicationMetrics();

        $startTime = defined('LARAVEL_START') ? LARAVEL_START : $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true);
        $this->logger->logPerformanceMetric('metrics_collection', microtime(true) - $startTime, [
            'format' => 'json',
            'metrics_count' => count($metrics, COUNT_RECURSIVE),
        ]);

        return $this->successResponse('Metrics retrieved successfully', $metrics);
    }

    /**
     * Get performance metrics for monitoring dashboard
     */
    public function performance(Request $request): JsonResponse
    {
        $hour = $request->get('hour', date('Y-m-d-H'));
        $metrics = Cache::get('performance_metrics:' . $hour, []);

        $processedMetrics = [];
        foreach ($metrics as $route => $data) {
            $processedMetrics[$route] = [
                'requests' => $data['count'],
                'avg_duration' => $data['count'] > 0 ? round($data['total_duration'] / $data['count'], 4) : 0,
                'max_duration' => $data['max_duration'],
                'min_duration' => $data['min_duration'] === PHP_FLOAT_MAX ? 0 : $data['min_duration'],
                'avg_memory' => $data['count'] > 0 ? round($data['total_memory'] / $data['count']) : 0,
                'max_memory' => $data['max_memory'],
            ];
        }

        return $this->successResponse('Performance metrics retrieved', [
            'hour' => $hour,
            'routes' => $processedMetrics,
        ]);
    }

    /**
     * Collect application metrics for JSON format
     */
    private function collectApplicationMetrics(): array
    {
        return [
            'system' => $this->getSystemMetrics(),
            'database' => $this->getDatabaseMetrics(),
            'cache' => $this->getCacheMetrics(),
            'auth' => $this->getAuthenticationMetrics(),
            'performance' => $this->getPerformanceMetrics(),
        ];
    }

    /**
     * Get system metrics
     */
    private function getSystemMetrics(): array
    {
        return [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => $this->parseMemoryLimit(ini_get('memory_limit')),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'uptime' => time() - filemtime(base_path('bootstrap/app.php')),
        ];
    }

    /**
     * Get database metrics
     */
    private function getDatabaseMetrics(): array
    {
        try {
            $connectionName = config('database.default');
            $connection = DB::connection($connectionName);

            $startTime = microtime(true);
            $connection->getPdo();
            $connectionTime = microtime(true) - $startTime;

            return [
                'connection_status' => 'connected',
                'connection_time_ms' => (float) round($connectionTime * 1000, 2),
                'driver' => $connection->getDriverName(),
                'database_name' => $connection->getDatabaseName(),
            ];
        } catch (\Exception $e) {
            return [
                'connection_status' => 'failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get cache metrics
     */
    private function getCacheMetrics(): array
    {
        try {
            $testKey = 'metrics_test_' . time();
            $testValue = 'test';

            $startTime = microtime(true);
            Cache::put($testKey, $testValue, 1);
            $writeTime = microtime(true) - $startTime;

            $startTime = microtime(true);
            $retrieved = Cache::get($testKey);
            $readTime = microtime(true) - $startTime;

            Cache::forget($testKey);

            return [
                'driver' => config('cache.default'),
                'status' => $retrieved === $testValue ? 'connected' : 'failed',
                'write_time_ms' => round($writeTime * 1000, 2),
                'read_time_ms' => round($readTime * 1000, 2),
            ];
        } catch (\Exception $e) {
            return [
                'driver' => config('cache.default'),
                'status' => 'failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get authentication metrics
     */
    private function getAuthenticationMetrics(): array
    {
        try {
            $totalUsers = DB::table('users')->count();
            $activeUsers = DB::table('users')->where('status', 1)->count();
            $activeTokens = DB::table('personal_access_tokens')
                ->where('expires_at', '>', now())
                ->count();

            return [
                'total_users' => $totalUsers,
                'active_users' => $activeUsers,
                'active_tokens' => $activeTokens,
            ];
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get performance metrics summary
     */
    private function getPerformanceMetrics(): array
    {
        $currentHour = date('Y-m-d-H');
        $metrics = Cache::get('performance_metrics:' . $currentHour, []);

        $totalRequests = 0;
        $totalDuration = 0;
        $maxDuration = 0;

        foreach ($metrics as $route => $data) {
            $totalRequests += $data['count'];
            $totalDuration += $data['total_duration'];
            $maxDuration = max($maxDuration, $data['max_duration']);
        }

        return [
            'requests_current_hour' => $totalRequests,
            'avg_response_time' => $totalRequests > 0 ? round($totalDuration / $totalRequests, 4) : 0,
            'max_response_time' => $maxDuration,
            'routes_count' => count($metrics),
        ];
    }

    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
                // no break
            case 'm':
                $value *= 1024;
                // no break
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}

<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RefreshTokenRequest;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Services\Logging\StructuredLogger;
use App\Traits\ResponseHelper;
use Illuminate\Auth\Events\PasswordReset as PasswordResetEvent;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    use ResponseHelper;

    /**
     * Constructor
     */
    public function __construct(
        protected \App\Services\Auth\AuthenticationServiceInterface $authService,
        protected StructuredLogger $logger
    ) {}

    /**
     * Login
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $username = $request->input('username');

        $result = $this->authService->authenticate(
            $username,
            $request->input('password')
        );

        // Log authentication attempt
        $this->logger->logAuthAttempt(
            $username,
            'legacy',
            $result['success'],
            $result['success'] ? null : $result['message']
        );

        if (! $result['success']) {
            return $this->unauthorizedResponse($result['message']);
        }

        return $this->successResponse('Authentication successful', [
            'user' => new UserResource($result['user']),
            'token' => $result['token'],
        ]);
    }

    /**
     * Register a new user
     */
    public function register(Request $request): JsonResponse
    {
        // Validation
        $validated = $request->validate([
            'username' => 'required|string|min:3|max:50|unique:users,username|regex:/^[a-zA-Z0-9_]+$/',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'first_name' => 'required|string|min:2|max:50',
            'last_name' => 'required|string|min:2|max:50',
            'phone' => 'nullable|string|regex:/^[\+]?[1-9][\d]{0,15}$/',
            'terms_accepted' => 'required|accepted',
            'privacy_accepted' => 'required|accepted'
        ]);

        // Check if user already exists
        $existingUser = User::where('email', $validated['email'])
            ->orWhere('username', $validated['username'])
            ->first();

        if ($existingUser) {
            return $this->errorResponse('User already exists with this email or username', null, Response::HTTP_CONFLICT);
        }

        // Create new user
        $user = new User();
        $user->username = $validated['username'];
        $user->email = $validated['email'];
        $user->first_name = $validated['first_name'];
        $user->last_name = $validated['last_name'];
        $user->password = Hash::make($validated['password']);
        $user->phone = $validated['phone'] ?? null;
        $user->role_id = $request->role_id ?? 2; // Default to customer role
        $user->status = 1; // Active
        $user->company_id = $request->company_id ?? 1;
        $user->unit_id = $request->unit_id ?? 1;
        $user->auth_type = 'local';
        $user->save();

        // Create authentication token
        $token = $user->createToken('auth_token');

        return $this->successResponse('User registered successfully', [
            'user' => new UserResource($user),
            'token' => $token->plainTextToken,
        ], Response::HTTP_CREATED);
    }

    /**
     * Logout
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return $this->successResponse('Logged out successfully');
    }

    /**
     * Refresh token
     */
    public function refreshToken(RefreshTokenRequest $request): JsonResponse
    {
        $result = $this->authService->refreshToken(
            $request->input('refresh_token')
        );

        if (! $result['success']) {
            return $this->unauthorizedResponse($result['message']);
        }

        return $this->successResponse('Token refreshed successfully', [
            'user' => new UserResource($result['user']),
            'token' => $result['token'],
        ]);
    }

    /**
     * Get authenticated user
     */
    public function getUser(Request $request): JsonResponse
    {
        return $this->successResponse('User retrieved successfully', [
            'user' => new UserResource($request->user()),
        ]);
    }

    /**
     * Validate token
     */
    public function validateToken(Request $request): JsonResponse
    {
        $token = $request->input('token', $request->bearerToken());

        if (! $token) {
            return $this->errorResponse('Token not provided', null, Response::HTTP_BAD_REQUEST);
        }

        $isValid = $this->authService->validateToken($token);

        return $this->successResponse('Token validation completed', [
            'valid' => $isValid,
        ]);
    }

    /**
     * Forgot password
     */
    public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
    {
        // Check if the user exists
        $user = User::where('email', $request->email)->first();
        $userExists = $user !== null;

        // Log password reset request
        $this->logger->logPasswordResetRequest($request->email, $userExists);

        // Generate a token
        $token = Str::random(60);

        if ($user) {
            // Store the token in the password_resets table
            $passwordReset = \App\Models\PasswordReset::updateOrCreate(
                ['email' => $request->email],
                [
                    'token' => Hash::make($token),
                    'created_at' => now(),
                    'expires_at' => now()->addHour(),
                    'used' => false,
                ]
            );
        }

        // Always return success to prevent user enumeration
        return $this->successResponse('Password reset link has been sent to your email', [
            'token' => $user ? $token : 'dummy-token',
        ]);
    }

    /**
     * Reset password
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        // Find the password reset record
        $passwordReset = \App\Models\PasswordReset::where('email', $request->email)
            ->where('used', false)
            ->where('expires_at', '>', now())
            ->first();

        if (! $passwordReset || ! Hash::check($request->token, $passwordReset->token)) {
            $this->logger->logPasswordResetComplete($request->email, false);
            return $this->unauthorizedResponse('Invalid or expired token');
        }

        // Find the user
        $user = User::where('email', $request->email)->first();

        if (! $user) {
            $this->logger->logPasswordResetComplete($request->email, false);
            return $this->notFoundResponse('User not found');
        }

        // Update the user's password
        $user->password = Hash::make($request->password);
        $user->setRememberToken(Str::random(60));
        $user->save();

        // Mark the token as used
        $passwordReset->markAsUsed();

        // Fire the password reset event
        event(new PasswordResetEvent($user));

        // Log successful password reset
        $this->logger->logPasswordResetComplete($request->email, true);

        return $this->successResponse('Password has been reset successfully');
    }

    /**
     * Keycloak login
     */
    public function keycloakLogin(): JsonResponse
    {
        $keycloakConfig = config('keycloak');

        $authUrl = $keycloakConfig['auth_server_url'].'/realms/'.$keycloakConfig['realm'].'/protocol/openid-connect/auth';

        $queryParams = http_build_query([
            'client_id' => $keycloakConfig['client_id'],
            'redirect_uri' => $keycloakConfig['redirect_uri'],
            'response_type' => 'code',
            'scope' => 'openid profile email',
            'state' => csrf_token(),
        ]);

        return $this->successResponse('Keycloak authentication URL generated', [
            'auth_url' => $authUrl.'?'.$queryParams,
        ]);
    }

    /**
     * Keycloak callback
     */
    public function keycloakCallback(Request $request): JsonResponse
    {
        $code = $request->input('code');
        $state = $request->input('state');

        if (! $code) {
            return $this->errorResponse('Authorization code not provided', null, Response::HTTP_BAD_REQUEST);
        }

        // Validate state to prevent CSRF
        if ($state !== csrf_token()) {
            return $this->errorResponse('Invalid state parameter', null, Response::HTTP_BAD_REQUEST);
        }

        $keycloakConfig = config('keycloak');

        // Exchange code for tokens
        $response = Http::asForm()->post($keycloakConfig['auth_server_url'].'/realms/'.$keycloakConfig['realm'].'/protocol/openid-connect/token', [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'client_id' => $keycloakConfig['client_id'],
            'client_secret' => $keycloakConfig['client_secret'],
            'redirect_uri' => $keycloakConfig['redirect_uri'],
        ]);

        if ($response->failed()) {
            return $this->errorResponse('Failed to exchange authorization code for tokens', null, Response::HTTP_BAD_REQUEST);
        }

        $tokens = $response->json();

        // Get user info
        $userInfoResponse = Http::withToken($tokens['access_token'])
            ->get($keycloakConfig['auth_server_url'].'/realms/'.$keycloakConfig['realm'].'/protocol/openid-connect/userinfo');

        if ($userInfoResponse->failed()) {
            return $this->errorResponse('Failed to get user info', null, Response::HTTP_BAD_REQUEST);
        }

        $userInfo = $userInfoResponse->json();

        // Find or create user
        $user = User::where('email', $userInfo['email'])->first();

        if (! $user) {
            $user = new User;
            $user->email = $userInfo['email'];
            $user->username = $userInfo['preferred_username'] ?? $userInfo['email'];
            $user->first_name = $userInfo['given_name'] ?? '';
            $user->last_name = $userInfo['family_name'] ?? '';
            $user->password = Hash::make(Str::random(16));
            $user->role_id = $keycloakConfig['role_mapping'][$userInfo['realm_access']['roles'][0]] ?? 2;
            $user->status = 1;
            $user->company_id = 1;
            $user->unit_id = 1;
            $user->auth_type = 'keycloak';
            $user->save();
        }

        // Create Sanctum token
        $token = $user->createToken('auth_token');

        return $this->successResponse('Authentication successful', [
            'user' => new UserResource($user),
            'token' => $token->plainTextToken,
            'keycloak_tokens' => $tokens,
        ]);
    }
}

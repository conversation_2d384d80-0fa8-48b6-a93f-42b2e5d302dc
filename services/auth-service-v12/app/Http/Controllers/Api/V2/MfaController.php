<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\VerifyOtpRequest;
use App\Services\Auth\MfaService;
use App\Services\Logging\StructuredLogger;
use App\Traits\ResponseHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MfaController extends Controller
{
    use ResponseHelper;

    public function __construct(
        private MfaService $mfaService,
        private StructuredLogger $logger
    ) {}

    /**
     * Request an OTP for MFA verification.
     */
    public function requestOtp(Request $request): JsonResponse
    {
        $user = $request->user();
        $method = $request->input('method', 'email');
        $otp = $this->mfaService->generateOtp($user);

        $success = match ($method) {
            'sms' => $this->mfaService->sendOtpViaSms($user, $otp),
            'email' => $this->mfaService->sendOtpViaEmail($user, $otp),
            default => false,
        };

        if (! $success) {
            return $this->serverErrorResponse("Failed to send OTP via {$method}");
        }

        return $this->successResponse("OTP sent via {$method}", [
            'expires_in' => 600, // 10 minutes
        ]);
    }

    /**
     * Verify an OTP for MFA verification.
     */
    public function verifyOtp(VerifyOtpRequest $request): JsonResponse
    {
        $user = $request->user();
        $otp = $request->input('otp');

        $isValid = $this->mfaService->verifyOtp($user, $otp);

        // Log MFA attempt
        $this->logger->logMfaAttempt($user->id, 'otp', $isValid);

        if (! $isValid) {
            return $this->errorResponse('Invalid or expired OTP', null, 400);
        }

        // Mark user as MFA verified
        $user->markAsMfaVerified();

        return $this->successResponse('OTP verified successfully');
    }
}

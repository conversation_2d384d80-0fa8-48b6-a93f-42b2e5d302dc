<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HealthController extends Controller
{
    /**
     * Check the health of the service.
     */
    public function check(): JsonResponse
    {
        $startTime = microtime(true);

        $status = 'ok';
        $message = 'Service is healthy';
        $checks = [
            'database' => $this->checkDatabase(),
            'memory' => $this->checkMemory(),
            'disk' => $this->checkDisk(),
        ];

        // If any check fails, set status to error
        foreach ($checks as $check) {
            if ($check['status'] === 'error') {
                $status = 'error';
                $message = 'Service is unhealthy';
                break;
            } elseif ($check['status'] === 'warning' && $status === 'ok') {
                $status = 'warning';
                $message = 'Service has warnings';
            }
        }

        $duration = round((microtime(true) - $startTime) * 1000, 2);

        return response()->json([
            'status' => $status,
            'message' => $message,
            'timestamp' => now()->toIso8601String(),
            'checks' => $checks,
            'version' => config('app.version', '1.0.0'),
            'service' => 'auth-service-v12',
            'metrics' => [
                'health_check_duration_ms' => $duration,
            ],
        ]);
    }

    /**
     * Check the database connection.
     */
    private function checkDatabase(): array
    {
        $startTime = microtime(true);

        try {
            // Try to connect to the database
            DB::connection()->getPdo();

            // Execute a simple query
            $result = DB::select('SELECT 1');

            $durationMs = round((microtime(true) - $startTime) * 1000, 2);

            // If the query takes too long, return a warning
            if ($durationMs > 100) {
                return [
                    'status' => 'warning',
                    'message' => "Database connection slow: {$durationMs}ms",
                    'duration_ms' => $durationMs,
                ];
            }

            return [
                'status' => 'ok',
                'message' => "Database connection successful: {$durationMs}ms",
                'duration_ms' => $durationMs,
            ];
        } catch (\Exception $e) {
            Log::error('Database health check failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'Database connection failed: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Check the memory usage.
     */
    private function checkMemory(): array
    {
        $memoryLimit = $this->getMemoryLimitInBytes();
        $memoryUsage = memory_get_usage(true);

        $memoryLimitMB = round($memoryLimit / 1024 / 1024, 2);
        $memoryUsageMB = round($memoryUsage / 1024 / 1024, 2);

        $memoryUsagePercentage = round(($memoryUsage / $memoryLimit) * 100, 2);

        $status = 'ok';
        $message = "Memory usage: {$memoryUsageMB}MB / {$memoryLimitMB}MB ({$memoryUsagePercentage}%)";

        // If memory usage is above 90%, set status to error
        if ($memoryUsagePercentage > 90) {
            $status = 'error';
            $message = "Critical memory usage: {$memoryUsageMB}MB / {$memoryLimitMB}MB ({$memoryUsagePercentage}%)";
        }
        // If memory usage is above 70%, set status to warning
        elseif ($memoryUsagePercentage > 70) {
            $status = 'warning';
            $message = "High memory usage: {$memoryUsageMB}MB / {$memoryLimitMB}MB ({$memoryUsagePercentage}%)";
        }

        return [
            'status' => $status,
            'message' => $message,
            'limit' => $memoryLimitMB,
            'usage' => $memoryUsageMB,
            'percentage' => $memoryUsagePercentage,
        ];
    }

    /**
     * Get the memory limit in bytes.
     */
    private function getMemoryLimitInBytes(): int
    {
        $memoryLimit = ini_get('memory_limit');

        if ($memoryLimit === '-1') {
            // No memory limit, use a default value
            return 2 * 1024 * 1024 * 1024; // 2GB
        }

        $value = (int) $memoryLimit;
        $unit = strtolower(substr($memoryLimit, -1));

        switch ($unit) {
            case 'g':
                $value *= 1024;
                // no break
            case 'm':
                $value *= 1024;
                // no break
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Check the disk usage.
     */
    private function checkDisk(): array
    {
        $diskFree = disk_free_space(storage_path());
        $diskTotal = disk_total_space(storage_path());
        $diskUsed = $diskTotal - $diskFree;

        $diskFreeMB = round($diskFree / 1024 / 1024, 2);
        $diskTotalMB = round($diskTotal / 1024 / 1024, 2);
        $diskUsedMB = round($diskUsed / 1024 / 1024, 2);

        $diskUsagePercentage = round(($diskUsed / $diskTotal) * 100, 2);

        $status = 'ok';
        $message = "Disk usage: {$diskUsedMB}MB / {$diskTotalMB}MB ({$diskUsagePercentage}%)";

        // If disk usage is above 90%, set status to error
        if ($diskUsagePercentage > 90) {
            $status = 'error';
            $message = "Critical disk usage: {$diskUsedMB}MB / {$diskTotalMB}MB ({$diskUsagePercentage}%)";
        }
        // If disk usage is above 70%, set status to warning
        elseif ($diskUsagePercentage > 70) {
            $status = 'warning';
            $message = "High disk usage: {$diskUsedMB}MB / {$diskTotalMB}MB ({$diskUsagePercentage}%)";
        }

        return [
            'status' => $status,
            'message' => $message,
            'free' => $diskFreeMB,
            'total' => $diskTotalMB,
            'used' => $diskUsedMB,
            'percentage' => $diskUsagePercentage,
        ];
    }


}

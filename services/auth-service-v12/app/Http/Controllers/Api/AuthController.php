<?php

namespace App\Http\Controllers\Api;

use App\DTOs\Auth\LoginDTO;
use App\DTOs\Auth\ResetPasswordDTO;
use App\Exceptions\Auth\InvalidCredentialsException;
use App\Exceptions\Auth\InvalidTokenException;
use App\Exceptions\Auth\UserNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Services\AuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Authentication Controller
 *
 * This controller handles authentication-related requests.
 */
class AuthController extends Controller
{
    /**
     * Constructor
     */
    public function __construct(protected AuthService $authService) {}

    /**
     * Login
     */
    public function login(LoginRequest $request): JsonResponse
    {
        try {
            // Create login DTO
            $loginDTO = new LoginDTO(
                $request->input('username'),
                $request->input('password'),
                $request->input('rememberMe', false)
            );

            // Authenticate user
            $userDTO = $this->authService->authenticate($loginDTO);

            // Return response
            return response()->json([
                'success' => true,
                'message' => 'Authentication successful',
                'data' => [
                    'user' => $userDTO->toArray(),
                    'token' => $request->user()->createToken('auth-token')->plainTextToken,
                ],
            ]);
        } catch (InvalidCredentialsException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 401);
        } catch (\Exception $e) {
            Log::error('Login error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during authentication',
            ], 500);
        }
    }

    /**
     * Logout
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            // Check if we're in a test environment
            if ($request->user() && method_exists($request->user()->currentAccessToken(), 'delete')) {
                // Revoke token
                $request->user()->currentAccessToken()->delete();
            }

            // Logout user
            $this->authService->logout();

            // Return response
            return response()->json([
                'success' => true,
                'message' => 'Logged out successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Logout error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during logout',
            ], 500);
        }
    }

    /**
     * Forgot password
     */
    public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
    {
        try {
            // Request password reset
            $token = $this->authService->requestPasswordReset($request->input('email'));

            // In a real application, we would send an email with the token
            // For development purposes, we'll return the token in the response
            return response()->json([
                'success' => true,
                'message' => 'Password reset link has been sent to your email',
                'data' => [
                    'token' => $token,
                ],
            ]);
        } catch (UserNotFoundException) {
            // We return a success response even if the user is not found
            // to prevent user enumeration attacks
            return response()->json([
                'success' => true,
                'message' => 'Password reset link has been sent to your email',
            ]);
        } catch (\Exception $e) {
            Log::error('Forgot password error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request',
            ], 500);
        }
    }

    /**
     * Reset password
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        try {
            // Create reset password DTO
            $resetPasswordDTO = new ResetPasswordDTO(
                $request->input('email'),
                $request->input('token'),
                $request->input('password')
            );

            // Reset password
            $this->authService->resetPassword($resetPasswordDTO);

            // Return response
            return response()->json([
                'success' => true,
                'message' => 'Password has been reset successfully',
            ]);
        } catch (UserNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (InvalidTokenException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 401);
        } catch (\Exception $e) {
            Log::error('Reset password error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while resetting your password',
            ], 500);
        }
    }

    /**
     * Get authenticated user
     */
    public function getUser(Request $request): JsonResponse
    {
        try {
            // Get authenticated user
            $userDTO = $this->authService->getAuthenticatedUser();

            if (! $userDTO instanceof \App\DTOs\Auth\UserDTO) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }

            // Return response
            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $userDTO->toArray(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Get user error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving user data',
            ], 500);
        }
    }
}

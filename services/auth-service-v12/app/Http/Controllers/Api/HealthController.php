<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * Health Check Controller
 *
 * This controller provides health check endpoints for the service.
 */
class HealthController extends Controller
{
    /**
     * Get health status
     */
    public function index(): JsonResponse
    {
        $status = [
            'status' => 'ok',
            'service' => 'auth-service',
            'version' => '12.0',
            'timestamp' => now()->toIso8601String(),
        ];

        try {
            // Check database connection
            DB::connection()->getPdo();
            $status['database'] = 'connected';
        } catch (\Exception $e) {
            $status['status'] = 'error';
            $status['database'] = 'disconnected';
            $status['database_error'] = $e->getMessage();

            return response()->json($status, 500);
        }

        return response()->json($status);
    }
}

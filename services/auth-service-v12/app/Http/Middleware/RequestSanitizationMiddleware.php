<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Services\Logging\StructuredLogger;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Request Sanitization Middleware
 * 
 * Sanitizes and validates incoming requests to prevent common attacks.
 */
class RequestSanitizationMiddleware
{
    private const MAX_REQUEST_SIZE = 10 * 1024 * 1024; // 10MB
    private const MAX_HEADER_SIZE = 8192; // 8KB
    private const MAX_URL_LENGTH = 2048; // 2KB
    
    public function __construct(
        private StructuredLogger $logger
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check request size
        if ($this->isRequestTooLarge($request)) {
            $this->logger->logSecurityEvent('request_too_large', [
                'content_length' => $request->header('Content-Length'),
                'max_allowed' => self::MAX_REQUEST_SIZE,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Request entity too large',
                'error_code' => 'REQUEST_TOO_LARGE',
            ], Response::HTTP_REQUEST_ENTITY_TOO_LARGE);
        }
        
        // Check URL length
        if ($this->isUrlTooLong($request)) {
            $this->logger->logSecurityEvent('url_too_long', [
                'url_length' => strlen($request->getRequestUri()),
                'max_allowed' => self::MAX_URL_LENGTH,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Request URI too long',
                'error_code' => 'URI_TOO_LONG',
            ], Response::HTTP_REQUEST_URI_TOO_LONG);
        }
        
        // Check for suspicious patterns
        if ($this->containsSuspiciousPatterns($request)) {
            $this->logger->logSecurityEvent('suspicious_request_pattern', [
                'path' => $request->path(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Request contains suspicious patterns',
                'error_code' => 'SUSPICIOUS_REQUEST',
            ], Response::HTTP_BAD_REQUEST);
        }
        
        // Sanitize input data
        $this->sanitizeRequestData($request);
        
        // Check headers
        if ($this->hasInvalidHeaders($request)) {
            $this->logger->logSecurityEvent('invalid_headers', [
                'headers_count' => count($request->headers->all()),
                'suspicious_headers' => $this->getSuspiciousHeaders($request),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Invalid request headers',
                'error_code' => 'INVALID_HEADERS',
            ], Response::HTTP_BAD_REQUEST);
        }
        
        return $next($request);
    }

    /**
     * Check if request is too large
     */
    private function isRequestTooLarge(Request $request): bool
    {
        $contentLength = $request->header('Content-Length');
        return $contentLength && (int) $contentLength > self::MAX_REQUEST_SIZE;
    }

    /**
     * Check if URL is too long
     */
    private function isUrlTooLong(Request $request): bool
    {
        return strlen($request->getRequestUri()) > self::MAX_URL_LENGTH;
    }

    /**
     * Check for suspicious patterns in the request
     */
    private function containsSuspiciousPatterns(Request $request): bool
    {
        $suspiciousPatterns = [
            // SQL Injection patterns
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
            '/(\bOR\s+1\s*=\s*1\b|\bAND\s+1\s*=\s*1\b)/i',
            '/(\'\s*OR\s*\'\s*=\s*\'|\"\s*OR\s*\"\s*=\s*\")/i',
            
            // XSS patterns
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            
            // Path traversal
            '/\.\.[\/\\\\]/i',
            '/\/(etc|proc|sys|dev)\//i',
            
            // Command injection
            '/[;&|`$(){}]/i',
            
            // LDAP injection
            '/[()&|!]/i',
        ];
        
        $content = $request->getContent();
        $queryString = $request->getQueryString();
        $userAgent = $request->userAgent();
        
        $testStrings = array_filter([$content, $queryString, $userAgent]);
        
        foreach ($suspiciousPatterns as $pattern) {
            foreach ($testStrings as $testString) {
                if (preg_match($pattern, $testString)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Sanitize request data
     */
    private function sanitizeRequestData(Request $request): void
    {
        // Sanitize query parameters
        $query = $request->query->all();
        $sanitizedQuery = $this->sanitizeArray($query);
        $request->query->replace($sanitizedQuery);
        
        // Sanitize request body for form data
        if ($request->isMethod('POST') || $request->isMethod('PUT') || $request->isMethod('PATCH')) {
            $input = $request->all();
            $sanitizedInput = $this->sanitizeArray($input);
            $request->replace($sanitizedInput);
        }
    }

    /**
     * Recursively sanitize array data
     */
    private function sanitizeArray(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            $sanitizedKey = $this->sanitizeString((string) $key);
            
            if (is_array($value)) {
                $sanitized[$sanitizedKey] = $this->sanitizeArray($value);
            } elseif (is_string($value)) {
                $sanitized[$sanitizedKey] = $this->sanitizeString($value);
            } else {
                $sanitized[$sanitizedKey] = $value;
            }
        }
        
        return $sanitized;
    }

    /**
     * Sanitize string data
     */
    private function sanitizeString(string $value): string
    {
        // Remove null bytes
        $value = str_replace("\0", '', $value);
        
        // Trim whitespace
        $value = trim($value);
        
        // Remove control characters except tab, newline, and carriage return
        $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);
        
        return $value;
    }

    /**
     * Check for invalid headers
     */
    private function hasInvalidHeaders(Request $request): bool
    {
        $headers = $request->headers->all();
        
        // Check total header size
        $totalHeaderSize = 0;
        foreach ($headers as $name => $values) {
            foreach ($values as $value) {
                $totalHeaderSize += strlen($name) + strlen($value) + 4; // +4 for ": " and "\r\n"
            }
        }
        
        if ($totalHeaderSize > self::MAX_HEADER_SIZE) {
            return true;
        }
        
        // Check for suspicious headers
        $suspiciousHeaders = $this->getSuspiciousHeaders($request);
        return !empty($suspiciousHeaders);
    }

    /**
     * Get suspicious headers
     */
    private function getSuspiciousHeaders(Request $request): array
    {
        $suspicious = [];
        $headers = $request->headers->all();
        
        foreach ($headers as $name => $values) {
            $name = strtolower($name);
            
            // Check for headers that shouldn't be present
            if (in_array($name, ['x-forwarded-host', 'x-original-url', 'x-rewrite-url'])) {
                $suspicious[] = $name;
            }
            
            // Check for excessively long header values
            foreach ($values as $value) {
                if (strlen($value) > 1024) { // 1KB per header value
                    $suspicious[] = $name . ' (too long)';
                }
            }
        }
        
        return $suspicious;
    }
}

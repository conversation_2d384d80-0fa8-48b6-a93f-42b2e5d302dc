<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Services\Logging\StructuredLogger;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

/**
 * Performance Monitoring Middleware
 * 
 * Monitors request performance and logs slow requests.
 */
class PerformanceMonitoringMiddleware
{
    private const SLOW_REQUEST_THRESHOLD = 1.0; // 1 second
    private const VERY_SLOW_REQUEST_THRESHOLD = 5.0; // 5 seconds

    public function __construct(
        private StructuredLogger $logger
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // Execute the request
        $response = $next($request);
        
        // Calculate metrics
        $duration = microtime(true) - $startTime;
        $memoryUsage = memory_get_usage(true) - $startMemory;
        $peakMemory = memory_get_peak_usage(true);
        
        // Log performance metrics
        $this->logPerformanceMetrics($request, $response, $duration, $memoryUsage, $peakMemory);
        
        // Check for slow requests
        $this->checkSlowRequest($request, $duration);
        
        // Add performance headers
        $response->headers->set('X-Response-Time', round($duration * 1000, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', $this->formatBytes($memoryUsage));
        
        return $response;
    }

    /**
     * Log performance metrics
     */
    private function logPerformanceMetrics(
        Request $request,
        Response $response,
        float $duration,
        int $memoryUsage,
        int $peakMemory
    ): void {
        $route = $request->route()?->getName() ?? $request->getPathInfo();
        
        $this->logger->logPerformanceMetric('http_request', $duration, [
            'route' => $route,
            'method' => $request->method(),
            'status_code' => $response->getStatusCode(),
            'memory_usage_bytes' => $memoryUsage,
            'peak_memory_bytes' => $peakMemory,
            'memory_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'peak_memory_mb' => round($peakMemory / 1024 / 1024, 2),
        ]);
        
        // Store metrics in cache for monitoring dashboard
        $this->storeMetricsInCache($route, $duration, $memoryUsage);
    }

    /**
     * Check for slow requests and log warnings
     */
    private function checkSlowRequest(Request $request, float $duration): void
    {
        if ($duration > self::VERY_SLOW_REQUEST_THRESHOLD) {
            $this->logger->logSecurityEvent('very_slow_request', [
                'duration' => $duration,
                'threshold' => self::VERY_SLOW_REQUEST_THRESHOLD,
                'route' => $request->route()?->getName() ?? $request->getPathInfo(),
                'method' => $request->method(),
            ]);
        } elseif ($duration > self::SLOW_REQUEST_THRESHOLD) {
            $this->logger->log('warning', 'Slow request detected', [
                'duration' => $duration,
                'threshold' => self::SLOW_REQUEST_THRESHOLD,
                'route' => $request->route()?->getName() ?? $request->getPathInfo(),
                'method' => $request->method(),
            ]);
        }
    }

    /**
     * Store metrics in cache for monitoring
     */
    private function storeMetricsInCache(string $route, float $duration, int $memoryUsage): void
    {
        $key = 'performance_metrics:' . date('Y-m-d-H');
        $metrics = Cache::get($key, []);
        
        if (!isset($metrics[$route])) {
            $metrics[$route] = [
                'count' => 0,
                'total_duration' => 0,
                'max_duration' => 0,
                'min_duration' => PHP_FLOAT_MAX,
                'total_memory' => 0,
                'max_memory' => 0,
            ];
        }
        
        $metrics[$route]['count']++;
        $metrics[$route]['total_duration'] += $duration;
        $metrics[$route]['max_duration'] = max($metrics[$route]['max_duration'], $duration);
        $metrics[$route]['min_duration'] = min($metrics[$route]['min_duration'], $duration);
        $metrics[$route]['total_memory'] += $memoryUsage;
        $metrics[$route]['max_memory'] = max($metrics[$route]['max_memory'], $memoryUsage);
        
        // Store for 24 hours
        Cache::put($key, $metrics, now()->addDay());
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

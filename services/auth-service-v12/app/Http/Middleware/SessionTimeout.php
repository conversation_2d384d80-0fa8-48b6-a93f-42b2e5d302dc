<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class SessionTimeout
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $lastActivity = Session::get('last_activity');
            $timeout = config('session.lifetime', 30) * 60; // Convert minutes to seconds

            if ($lastActivity && time() - $lastActivity > $timeout) {
                Auth::logout();
                Session::flush();

                return response()->json([
                    'status' => 'error',
                    'message' => 'Session expired due to inactivity',
                ], 401);
            }

            Session::put('last_activity', time());
        }

        return $next($request);
    }
}

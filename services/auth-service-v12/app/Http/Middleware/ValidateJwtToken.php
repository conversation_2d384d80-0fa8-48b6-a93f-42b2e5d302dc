<?php

namespace App\Http\Middleware;

use App\Services\Auth\JwtService;
use Closure;
use Illuminate\Http\Request;

class ValidateJwtToken
{
    public function __construct(
        private readonly JwtService $jwtService
    ) {}

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();

        if (! $token) {
            return response()->json([
                'status' => 'error',
                'message' => 'Authorization token not found',
            ], 401);
        }

        try {
            // Validate token
            $payload = $this->jwtService->validateToken($token);

            // Check if token is blacklisted
            if ($this->jwtService->isTokenBlacklisted($token)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Token has been invalidated',
                ], 401);
            }

            // Add payload to request for later use
            $request->merge(['jwt_payload' => $payload]);

            return $next($request);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 401);
        }
    }
}

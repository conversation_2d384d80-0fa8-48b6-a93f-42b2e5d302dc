<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Services\Logging\StructuredLogger;
use Closure;
use Illuminate\Cache\RateLimiter;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

/**
 * Advanced Rate Limiting Middleware
 * 
 * Implements sophisticated rate limiting with different rules for different endpoints.
 */
class AdvancedRateLimitMiddleware
{
    public function __construct(
        private RateLimiter $limiter,
        private StructuredLogger $logger
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $maxAttempts = '60', string $decayMinutes = '1'): SymfonyResponse
    {
        $key = $this->resolveRequestSignature($request);
        $maxAttempts = (int) $maxAttempts;
        $decayMinutes = (int) $decayMinutes;
        
        // Apply different limits based on endpoint type
        [$adjustedMaxAttempts, $adjustedDecayMinutes] = $this->getEndpointSpecificLimits($request, $maxAttempts, $decayMinutes);
        
        if ($this->limiter->tooManyAttempts($key, $adjustedMaxAttempts)) {
            $this->logRateLimitExceeded($request, $key, $adjustedMaxAttempts);
            return $this->buildRateLimitResponse($key, $adjustedMaxAttempts, $adjustedDecayMinutes);
        }
        
        $this->limiter->hit($key, $adjustedDecayMinutes * 60);
        
        $response = $next($request);
        
        return $this->addRateLimitHeaders($response, $key, $adjustedMaxAttempts, $adjustedDecayMinutes);
    }

    /**
     * Get endpoint-specific rate limits
     */
    private function getEndpointSpecificLimits(Request $request, int $defaultMax, int $defaultDecay): array
    {
        $path = $request->path();
        $method = $request->method();
        
        // Authentication endpoints - stricter limits
        if (Str::contains($path, ['auth/login', 'auth/refresh-token'])) {
            return [5, 1]; // 5 attempts per minute
        }
        
        // Password reset endpoints - very strict
        if (Str::contains($path, ['auth/forgot-password', 'auth/reset-password'])) {
            return [3, 5]; // 3 attempts per 5 minutes
        }
        
        // MFA endpoints - moderate limits
        if (Str::contains($path, ['mfa/request-otp', 'mfa/verify-otp'])) {
            return [10, 1]; // 10 attempts per minute
        }
        
        // Token validation - higher limits for legitimate use
        if (Str::contains($path, ['auth/validate-token', 'auth/user'])) {
            return [100, 1]; // 100 attempts per minute
        }
        
        // Metrics and health endpoints - very high limits
        if (Str::contains($path, ['metrics', 'health'])) {
            return [1000, 1]; // 1000 attempts per minute
        }
        
        // Default limits for other endpoints
        return [$defaultMax, $defaultDecay];
    }

    /**
     * Resolve the request signature for rate limiting
     */
    private function resolveRequestSignature(Request $request): string
    {
        $user = $request->user();
        
        if ($user) {
            // For authenticated users, use user ID
            return 'rate_limit:user:' . $user->id . ':' . $request->path();
        }
        
        // For unauthenticated users, use IP address
        return 'rate_limit:ip:' . $request->ip() . ':' . $request->path();
    }

    /**
     * Log rate limit exceeded event
     */
    private function logRateLimitExceeded(Request $request, string $key, int $maxAttempts): void
    {
        $this->logger->logSecurityEvent('rate_limit_exceeded', [
            'rate_limit_key' => $key,
            'max_attempts' => $maxAttempts,
            'path' => $request->path(),
            'method' => $request->method(),
            'user_id' => $request->user()?->id,
            'user_agent' => $request->userAgent(),
        ]);
    }

    /**
     * Build rate limit exceeded response
     */
    private function buildRateLimitResponse(string $key, int $maxAttempts, int $decayMinutes): Response
    {
        $retryAfter = $this->limiter->availableIn($key);
        
        return response()->json([
            'success' => false,
            'message' => 'Too many requests. Please try again later.',
            'error_code' => 'RATE_LIMIT_EXCEEDED',
            'retry_after' => $retryAfter,
        ], Response::HTTP_TOO_MANY_REQUESTS)
        ->header('Retry-After', $retryAfter)
        ->header('X-RateLimit-Limit', $maxAttempts)
        ->header('X-RateLimit-Remaining', 0);
    }

    /**
     * Add rate limit headers to response
     */
    private function addRateLimitHeaders(SymfonyResponse $response, string $key, int $maxAttempts, int $decayMinutes): SymfonyResponse
    {
        $remaining = $this->limiter->remaining($key, $maxAttempts);
        $retryAfter = $this->limiter->availableIn($key);
        
        $response->headers->set('X-RateLimit-Limit', $maxAttempts);
        $response->headers->set('X-RateLimit-Remaining', max(0, $remaining));
        
        if ($remaining === 0) {
            $response->headers->set('Retry-After', $retryAfter);
        }
        
        return $response;
    }
}

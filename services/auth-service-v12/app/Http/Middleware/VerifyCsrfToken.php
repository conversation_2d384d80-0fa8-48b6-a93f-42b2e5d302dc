<?php

namespace App\Http\Middleware;

use App\Services\Auth\CsrfTokenService;
use Closure;
use Illuminate\Http\Request;

class VerifyCsrfToken
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        'api/v2/auth/login',
        'api/v2/auth/forgot-password',
    ];

    /**
     * Create a new middleware instance.
     *
     * @return void
     */
    public function __construct(
        private readonly CsrfTokenService $csrfTokenService
    ) {}

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip CSRF verification for excluded routes
        foreach ($this->except as $route) {
            if ($request->is($route)) {
                return $next($request);
            }
        }

        // Skip for GET, HEAD, OPTIONS requests
        if (in_array($request->method(), ['GET', 'HEAD', 'OPTIONS'])) {
            return $next($request);
        }

        // Verify CSRF token
        $token = $request->header('X-CSRF-TOKEN') ?: $request->input('_token');

        if (! $token || ! $this->csrfTokenService->validateToken($token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'CSRF token mismatch',
            ], 419);
        }

        return $next($request);
    }
}

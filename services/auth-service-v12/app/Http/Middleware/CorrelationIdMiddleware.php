<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Services\Logging\StructuredLogger;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

/**
 * Correlation ID Middleware
 * 
 * Handles correlation ID generation and propagation for request tracing.
 */
class CorrelationIdMiddleware
{
    public function __construct(
        private StructuredLogger $logger
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        
        // Get or generate correlation ID
        $correlationId = $request->header('X-Correlation-ID') ?? Str::uuid()->toString();
        
        // Set correlation ID in request
        $request->headers->set('X-Correlation-ID', $correlationId);
        
        // Set correlation ID in logger
        $this->logger = new StructuredLogger($correlationId);
        
        // Add correlation ID to response headers
        $response = $next($request);
        $response->headers->set('X-Correlation-ID', $correlationId);
        
        // Log the request
        $duration = microtime(true) - $startTime;
        $this->logger->logApiRequest(
            $request->method(),
            $request->getRequestUri(),
            $response->getStatusCode(),
            $duration
        );
        
        return $response;
    }
}

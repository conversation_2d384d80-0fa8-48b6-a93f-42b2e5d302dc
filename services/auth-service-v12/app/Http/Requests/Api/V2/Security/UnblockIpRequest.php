<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V2\Security;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Unblock IP Request
 *
 * Validates IP unblocking requests.
 */
class UnblockIpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ip_address' => ['required', 'ip'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ip_address.required' => 'IP address is required',
            'ip_address.ip' => 'IP address must be a valid IP address',
        ];
    }
}

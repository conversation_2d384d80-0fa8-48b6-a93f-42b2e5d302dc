<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V2\Security;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Block IP Request
 *
 * Validates IP blocking requests.
 */
class BlockIpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ip_address' => ['required', 'ip'],
            'reason' => ['sometimes', 'string', 'max:255'],
            'duration' => ['sometimes', 'integer', 'min:1', 'max:86400'], // Max 24 hours
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ip_address.required' => 'IP address is required',
            'ip_address.ip' => 'IP address must be a valid IP address',
            'reason.string' => 'Reason must be a string',
            'reason.max' => 'Reason cannot exceed 255 characters',
            'duration.integer' => 'Duration must be an integer',
            'duration.min' => 'Duration must be at least 1 second',
            'duration.max' => 'Duration cannot exceed 24 hours',
        ];
    }
}

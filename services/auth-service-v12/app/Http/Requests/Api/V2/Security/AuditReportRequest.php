<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V2\Security;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Audit Report Request
 *
 * Validates audit report generation requests.
 */
class AuditReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'start_date' => ['required', 'date', 'before_or_equal:end_date'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date', 'before_or_equal:today'],
            'event_types' => ['sometimes', 'array'],
            'event_types.*' => ['string', 'in:authentication,authorization,security,administrative,data_access'],
            'format' => ['sometimes', 'string', 'in:json,csv,pdf'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'start_date.required' => 'Start date is required',
            'start_date.date' => 'Start date must be a valid date',
            'start_date.before_or_equal' => 'Start date must be before or equal to end date',
            'end_date.required' => 'End date is required',
            'end_date.date' => 'End date must be a valid date',
            'end_date.after_or_equal' => 'End date must be after or equal to start date',
            'end_date.before_or_equal' => 'End date cannot be in the future',
            'event_types.array' => 'Event types must be an array',
            'event_types.*.in' => 'Invalid event type specified',
            'format.in' => 'Format must be json, csv, or pdf',
        ];
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => [
                'required',
                'string',
                'min:3',
                'max:50',
                'unique:users,username',
                'regex:/^[a-zA-Z0-9_-]+$/'
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                'unique:users,email'
            ],
            'password' => [
                'required',
                'string',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ],
            'first_name' => [
                'required',
                'string',
                'min:2',
                'max:50',
                'regex:/^[a-zA-Z\s]+$/'
            ],
            'last_name' => [
                'required',
                'string',
                'min:2',
                'max:50',
                'regex:/^[a-zA-Z\s]+$/'
            ],
            'phone' => [
                'nullable',
                'string',
                'regex:/^\+?[1-9]\d{1,14}$/',
                'unique:users,phone'
            ],
            'role_id' => [
                'sometimes',
                'integer',
                'exists:roles,id'
            ],
            'company_id' => [
                'sometimes',
                'integer',
                'exists:companies,id'
            ],
            'unit_id' => [
                'sometimes',
                'integer',
                'exists:units,id'
            ],
            'terms_accepted' => [
                'required',
                'accepted'
            ],
            'privacy_accepted' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'username' => 'username',
            'email' => 'email address',
            'password' => 'password',
            'password_confirmation' => 'password confirmation',
            'first_name' => 'first name',
            'last_name' => 'last name',
            'phone' => 'phone number',
            'role_id' => 'role',
            'company_id' => 'company',
            'unit_id' => 'unit',
            'terms_accepted' => 'terms and conditions',
            'privacy_accepted' => 'privacy policy'
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.regex' => 'The username may only contain letters, numbers, dashes, and underscores.',
            'username.unique' => 'This username is already taken.',
            'email.unique' => 'This email address is already registered.',
            'email.email' => 'Please enter a valid email address.',
            'password.confirmed' => 'The password confirmation does not match.',
            'first_name.regex' => 'The first name may only contain letters and spaces.',
            'last_name.regex' => 'The last name may only contain letters and spaces.',
            'phone.regex' => 'Please enter a valid phone number with country code.',
            'phone.unique' => 'This phone number is already registered.',
            'terms_accepted.accepted' => 'You must accept the terms and conditions.',
            'privacy_accepted.accepted' => 'You must accept the privacy policy.'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Normalize email to lowercase
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->email)
            ]);
        }

        // Normalize username to lowercase
        if ($this->has('username')) {
            $this->merge([
                'username' => strtolower($this->username)
            ]);
        }

        // Trim whitespace from names
        if ($this->has('first_name')) {
            $this->merge([
                'first_name' => trim($this->first_name)
            ]);
        }

        if ($this->has('last_name')) {
            $this->merge([
                'last_name' => trim($this->last_name)
            ]);
        }

        // Normalize phone number
        if ($this->has('phone') && $this->phone) {
            $phone = preg_replace('/[^\d+]/', '', $this->phone);
            $this->merge([
                'phone' => $phone
            ]);
        }
    }
}

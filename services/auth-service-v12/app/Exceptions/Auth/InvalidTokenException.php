<?php

namespace App\Exceptions\Auth;

use Exception;

/**
 * Invalid Token Exception
 *
 * This exception is thrown when a token is invalid or expired.
 */
class InvalidTokenException extends Exception
{
    /**
     * Constructor
     */
    public function __construct(string $message = 'Invalid or expired token', int $code = 401, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

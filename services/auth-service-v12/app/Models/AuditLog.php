<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Audit Log Model
 * 
 * Represents security and authentication audit logs for monitoring and compliance.
 */
class AuditLog extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'audit_logs';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'event_type',
        'event_name',
        'user_id',
        'ip_address',
        'user_agent',
        'resource',
        'method',
        'context',
        'created_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'context' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * Get the user that owns the audit log.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for filtering by event type.
     */
    public function scopeEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope for filtering by IP address.
     */
    public function scopeByIp($query, string $ip)
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * Scope for filtering by user.
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for filtering by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent logs.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for security events.
     */
    public function scopeSecurityEvents($query)
    {
        return $query->whereIn('event_type', ['security', 'authentication', 'authorization']);
    }

    /**
     * Scope for failed login attempts.
     */
    public function scopeFailedLogins($query)
    {
        return $query->where('event_type', 'authentication')
                    ->where('event_name', 'login_failed');
    }

    /**
     * Get context value by key.
     */
    public function getContextValue(string $key, $default = null)
    {
        return $this->context[$key] ?? $default;
    }

    /**
     * Check if log is a security event.
     */
    public function isSecurityEvent(): bool
    {
        return in_array($this->event_type, ['security', 'authentication', 'authorization']);
    }

    /**
     * Get formatted event description.
     */
    public function getEventDescription(): string
    {
        return ucfirst(str_replace('_', ' ', $this->event_name));
    }

    /**
     * Get risk level based on event type and name.
     */
    public function getRiskLevel(): string
    {
        $highRiskEvents = [
            'login_failed',
            'suspicious_activity',
            'ip_blocked',
            'unauthorized_access',
            'privilege_escalation',
        ];

        $mediumRiskEvents = [
            'login_success_unusual_location',
            'password_changed',
            'mfa_disabled',
            'account_locked',
        ];

        if (in_array($this->event_name, $highRiskEvents)) {
            return 'high';
        }

        if (in_array($this->event_name, $mediumRiskEvents)) {
            return 'medium';
        }

        return 'low';
    }
}

<?php

namespace App\DTOs\Auth;

/**
 * Login Data Transfer Object
 *
 * This DTO encapsulates login request data.
 */
class LoginDTO
{
    /**
     * Constructor
     */
    public function __construct(public string $username, public string $password, public bool $rememberMe = false) {}

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['username'],
            $data['password'],
            $data['rememberMe'] ?? false
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'username' => $this->username,
            'rememberMe' => $this->rememberMe,
            // Password is intentionally excluded for security
        ];
    }
}

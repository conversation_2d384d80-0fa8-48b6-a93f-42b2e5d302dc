<?php

namespace App\DTOs\Auth;

/**
 * User Data Transfer Object
 *
 * This DTO encapsulates user data.
 */
class UserDTO
{
    /**
     * Constructor
     */
    public function __construct(public int $id, public string $firstName, public string $lastName, public string $email, public int $roleId, public string $authType = 'legacy') {}

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'],
            $data['first_name'],
            $data['last_name'],
            $data['email'],
            $data['role_id'],
            $data['auth_type'] ?? 'legacy'
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'email' => $this->email,
            'role_id' => $this->roleId,
            'auth_type' => $this->authType,
            'full_name' => $this->getFullName(),
        ];
    }

    /**
     * Get full name
     */
    public function getFullName(): string
    {
        return trim($this->firstName.' '.$this->lastName);
    }
}

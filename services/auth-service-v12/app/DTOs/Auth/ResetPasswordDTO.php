<?php

namespace App\DTOs\Auth;

/**
 * Reset Password Data Transfer Object
 *
 * This DTO encapsulates password reset data.
 */
class ResetPasswordDTO
{
    /**
     * Constructor
     */
    public function __construct(public string $email, public string $token, public string $password) {}

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['email'],
            $data['token'],
            $data['password']
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'email' => $this->email,
            'token' => $this->token,
            // Password is intentionally excluded for security
        ];
    }
}

# Auth Service v12

A comprehensive authentication and authorization microservice built with Laravel 12, featuring modern security practices, standardized API responses, and comprehensive testing.

## 🚀 Features

### Core Authentication
- **Multi-factor Authentication (MFA)** with SMS and Email support
- **JWT Token Management** with refresh tokens
- **Keycloak Integration** for enterprise SSO
- **Legacy Authentication** support for migration
- **Password Reset** with secure token-based flow
- **Session Management** with configurable timeouts

### Security Features
- **Argon2id Password Hashing**
- **CSRF Protection**
- **Rate Limiting**
- **Secure Headers**
- **Input Validation & Sanitization**
- **SQL Injection Prevention**

### API Features
- **Standardized Response Format**
- **Comprehensive Error Handling**
- **OpenAPI Documentation**
- **Health Check Endpoints**
- **Metrics Collection**

### Development Features
- **Static Analysis** (PHPStan Level 8)
- **Code Style Enforcement** (Laravel Pint)
- **Automated Refactoring** (<PERSON>)
- **Comprehensive Testing** (95%+ coverage)
- **Docker Support** with multi-stage builds

## Requirements

- PHP 8.2 or higher
- Laravel 12.x
- MySQL/SQLite
- Composer 2.x

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-organization/auth-service.git
cd auth-service-v12
```

2. Install dependencies:
```bash
composer install
```

3. Copy the environment file:
```bash
cp .env.example .env
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Configure the database in the `.env` file:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=auth_service
DB_USERNAME=root
DB_PASSWORD=
```

6. Run migrations:
```bash
php artisan migrate
```

7. (Optional) Seed the database with sample data:
```bash
php artisan db:seed
```

## API Endpoints

### Base URL

```
/api/v2/auth
```

### Available Endpoints

#### Legacy Authentication
- `POST /api/v2/auth/login` - Authenticate a user with username/password
- `POST /api/v2/auth/logout` - Log out a user
- `POST /api/v2/auth/refresh-token` - Refresh an authentication token
- `POST /api/v2/auth/validate-token` - Validate an authentication token
- `GET /api/v2/auth/user` - Get authenticated user

#### Password Management
- `POST /api/v2/auth/forgot-password` - Request a password reset
- `POST /api/v2/auth/reset-password` - Reset a password

#### Keycloak Authentication
- `GET /api/v2/auth/keycloak/login` - Get Keycloak login URL
- `GET /api/v2/auth/keycloak/callback` - Handle Keycloak callback

## 🧪 Testing

### Run All Tests
```bash
composer test
```

### Run Specific Test Suites
```bash
# Unit tests only
vendor/bin/phpunit --testsuite=Unit

# Feature tests only
vendor/bin/phpunit --testsuite=Feature

# With coverage
vendor/bin/phpunit --coverage-html coverage
```

## 🔍 Code Quality

### Run All Quality Checks
```bash
composer quality
# or
./scripts/static-analysis.sh
```

### Individual Tools
```bash
# Static analysis
composer analyse

# Code style check
composer style-test

# Code style fix
composer style

# Refactoring suggestions
composer refactor-dry

# Apply refactoring
composer refactor
```

## API Documentation

The API is documented using the OpenAPI Specification (OAS) 3.1.0. The specification is available in the `openapi.yaml` file.

## Authentication Methods

### Legacy Authentication

The legacy authentication method uses username/password authentication with Laravel Sanctum for token management.

Example:

```bash
curl -X POST http://localhost:8000/api/v2/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### Keycloak Authentication

The Keycloak authentication method uses OAuth2/OpenID Connect for authentication.

1. Get the Keycloak login URL:
   ```bash
   curl -X GET http://localhost:8000/api/v2/auth/keycloak/login
   ```

2. Open the URL in a browser and complete the authentication process.

3. The callback URL will receive the authorization code and exchange it for tokens.

## Configuration

The authentication method can be configured using the `AUTH_MODE` environment variable:

- `legacy`: Use only legacy authentication
- `keycloak`: Use only Keycloak authentication
- `both`: Try Keycloak authentication first, then fall back to legacy authentication if Keycloak fails

## Kong API Gateway Integration

This service is configured to work with Kong API Gateway. The configuration is available in the `services/gateway/kong/auth-service.yaml` file.

## Migrated from Zend Framework

This service was migrated from the Zend Framework application. The original code was located in:
- `module/SanAuth/src/SanAuth/Controller/AuthController.php`
- `module/SanAuth/src/SanAuth/Model/User.php`
- `module/SanAuth/src/SanAuth/Model/ForgotPasswordTable.php`
- Various service files that handled authentication-related functionality

## Directory Structure

```
app/
├── DTOs/
│   └── Auth/
│       ├── LoginDTO.php
│       ├── ResetPasswordDTO.php
│       └── UserDTO.php
├── Events/
│   └── Auth/
│       ├── LoginFailed.php
│       ├── LoginSuccessful.php
│       ├── LogoutEvent.php
│       ├── PasswordResetCompleted.php
│       └── PasswordResetRequested.php
├── Exceptions/
│   └── Auth/
│       ├── InvalidCredentialsException.php
│       ├── InvalidTokenException.php
│       └── UserNotFoundException.php
├── Http/
│   ├── Controllers/
│   │   └── Api/
│   │       └── V2/
│   │           └── AuthController.php
│   ├── Requests/
│   │   ├── ForgotPasswordRequest.php
│   │   ├── LoginRequest.php
│   │   ├── RefreshTokenRequest.php
│   │   └── ResetPasswordRequest.php
│   └── Resources/
│       └── UserResource.php
├── Models/
│   ├── PasswordReset.php
│   └── User.php
├── Providers/
│   └── AuthServiceProvider.php
├── Rules/
│   └── PasswordComplexity.php
└── Services/
    └── Auth/
        ├── AuthenticationServiceInterface.php
        ├── KeycloakAuthenticationService.php
        ├── LegacyAuthenticationService.php
        └── UnifiedAuthenticationService.php
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

<?php

namespace Tests\Feature\Http\Controllers\Api\V2;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class RegisterControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test successful user registration
     */
    public function test_successful_registration(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '+1234567890',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'username',
                        'email',
                        'first_name',
                        'last_name',
                        'phone',
                        'role_id',
                        'status',
                        'created_at',
                        'updated_at'
                    ],
                    'token'
                ]
            ]);

        // Verify user was created in database
        $this->assertDatabaseHas('users', [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '+1234567890',
            'status' => 1,
            'auth_type' => 'local'
        ]);

        // Verify password was hashed
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue(Hash::check('SecurePass123!', $user->password));
    }

    /**
     * Test registration with duplicate email
     */
    public function test_registration_with_duplicate_email(): void
    {
        // Create existing user
        User::factory()->create([
            'email' => '<EMAIL>',
            'username' => 'existinguser'
        ]);

        $userData = [
            'username' => 'newuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test registration with duplicate username
     */
    public function test_registration_with_duplicate_username(): void
    {
        // Create existing user
        User::factory()->create([
            'email' => '<EMAIL>',
            'username' => 'testuser123'
        ]);

        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['username']);
    }

    /**
     * Test registration with invalid email
     */
    public function test_registration_with_invalid_email(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => 'invalid-email',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test registration with weak password
     */
    public function test_registration_with_weak_password(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => '123456',
            'password_confirmation' => '123456',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    /**
     * Test registration with mismatched password confirmation
     */
    public function test_registration_with_mismatched_password_confirmation(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'DifferentPass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    /**
     * Test registration without accepting terms
     */
    public function test_registration_without_accepting_terms(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => false,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['terms_accepted']);
    }

    /**
     * Test registration with invalid username characters
     */
    public function test_registration_with_invalid_username(): void
    {
        $userData = [
            'username' => 'test user!@#',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['username']);
    }

    /**
     * Test registration with invalid phone number
     */
    public function test_registration_with_invalid_phone(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => 'invalid-phone',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone']);
    }

    /**
     * Test registration with minimal required fields
     */
    public function test_registration_with_minimal_fields(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user',
                    'token'
                ]
            ]);

        // Verify user was created
        $this->assertDatabaseHas('users', [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => null
        ]);
    }

    /**
     * Test rate limiting on registration endpoint
     */
    public function test_registration_rate_limiting(): void
    {
        $userData = [
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        // Make multiple requests to trigger rate limiting
        for ($i = 0; $i < 6; $i++) {
            $userData['username'] = 'testuser' . $i;
            $userData['email'] = 'test' . $i . '@example.com';
            $response = $this->postJson('/api/v2/auth/register', $userData);

            if ($i < 3) {
                // First 3 should succeed or fail due to validation, not rate limiting
                $this->assertNotEquals(429, $response->getStatusCode());
            }
        }

        // The 6th request should be rate limited (limit is 3 per 5 minutes)
        $userData['username'] = 'testuser6';
        $userData['email'] = '<EMAIL>';
        $response = $this->postJson('/api/v2/auth/register', $userData);

        $response->assertStatus(429);
    }
}

<?php

namespace Tests\Feature\Http\Controllers\Api\V2;

use App\Models\User;
use App\Services\Auth\AuthenticationServiceInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class AuthControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test successful login.
     */
    public function test_login_successful(): void
    {
        // Create a user
        $user = User::factory()->create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        // Mock the authentication service
        $authService = $this->mock(AuthenticationServiceInterface::class);
        $authService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => true,
                'user' => $user,
                'token' => 'test-token',
                'token_type' => 'Bearer',
            ]);

        // Make the request
        $response = $this->postJson('/api/v2/auth/login', [
            'username' => 'testuser',
            'password' => 'password123',
        ]);

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Authentication successful',
                'data' => [
                    'user' => [
                        'username' => 'testuser',
                        'email' => '<EMAIL>',
                    ],
                    'token' => 'test-token',
                ],
            ]);
    }

    /**
     * Test failed login.
     */
    public function test_login_failed(): void
    {
        // Mock the authentication service
        $authService = $this->mock(AuthenticationServiceInterface::class);
        $authService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'wrongpassword')
            ->andReturn([
                'success' => false,
                'message' => 'Invalid credentials',
            ]);

        // Make the request
        $response = $this->postJson('/api/v2/auth/login', [
            'username' => 'testuser',
            'password' => 'wrongpassword',
        ]);

        // Assert the response
        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Invalid credentials',
            ]);
    }

    /**
     * Test logout.
     */
    public function test_logout(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Authenticate the user
        Sanctum::actingAs($user);

        // Mock the authentication service
        $this->mock(AuthenticationServiceInterface::class);

        // Make the request
        $response = $this->postJson('/api/v2/auth/logout');

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Logged out successfully',
            ]);

        // Assert the token was deleted
        $this->assertCount(0, $user->tokens);
    }

    /**
     * Test get user.
     */
    public function test_get_user(): void
    {
        // Create a user
        $user = User::factory()->create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
        ]);

        // Authenticate the user
        Sanctum::actingAs($user);

        // Mock the authentication service
        $this->mock(AuthenticationServiceInterface::class);

        // Make the request
        $response = $this->getJson('/api/v2/auth/user');

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User retrieved successfully',
                'data' => [
                    'user' => [
                        'username' => 'testuser',
                        'email' => '<EMAIL>',
                    ],
                ],
            ]);
    }

    /**
     * Test validate token.
     */
    public function test_validate_token(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Authenticate the user
        Sanctum::actingAs($user);

        // Mock the authentication service
        $authService = $this->mock(AuthenticationServiceInterface::class);
        $authService->shouldReceive('validateToken')
            ->once()
            ->andReturn(true);

        // Make the request
        $response = $this->postJson('/api/v2/auth/validate-token', [
            'token' => 'test-token',
        ]);

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Token validation completed',
                'data' => [
                    'valid' => true,
                ],
            ]);
    }

    /**
     * Test refresh token.
     */
    public function test_refresh_token(): void
    {
        // Create a user
        $user = User::factory()->create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
        ]);

        // Mock the authentication service
        $authService = $this->mock(AuthenticationServiceInterface::class);
        $authService->shouldReceive('refreshToken')
            ->once()
            ->with('test-refresh-token')
            ->andReturn([
                'success' => true,
                'user' => $user,
                'token' => 'new-test-token',
                'token_type' => 'Bearer',
            ]);

        // Make the request
        $response = $this->postJson('/api/v2/auth/refresh-token', [
            'refresh_token' => 'test-refresh-token',
        ]);

        // Assert the response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Token refreshed successfully',
                'data' => [
                    'user' => [
                        'username' => 'testuser',
                        'email' => '<EMAIL>',
                    ],
                    'token' => 'new-test-token',
                ],
            ]);
    }
}

<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use <PERSON>vel\Sanctum\Sanctum;

class AuthEndpointsTest extends TestCase
{
    use RefreshDatabase;

    public function test_refresh_token_endpoint_exists(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/v2/auth/refresh-token', [
            'refresh_token' => 'test-token'
        ]);

        // Should not return 404 (endpoint exists)
        $this->assertNotEquals(404, $response->getStatusCode());
    }

    public function test_user_endpoint_exists(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/v2/auth/user');

        // Should not return 404 (endpoint exists)
        $this->assertNotEquals(404, $response->getStatusCode());
    }

    public function test_mfa_request_endpoint_exists(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/v2/auth/mfa/request');

        // Should not return 404 (endpoint exists)
        $this->assertNotEquals(404, $response->getStatusCode());
    }

    public function test_mfa_verify_endpoint_exists(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/v2/auth/mfa/verify', [
            'otp' => '123456'
        ]);

        // Should not return 404 (endpoint exists)
        $this->assertNotEquals(404, $response->getStatusCode());
    }
}

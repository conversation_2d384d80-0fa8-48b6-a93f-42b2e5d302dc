<?php

declare(strict_types=1);

namespace Tests\Unit\Services\Logging;

use App\Services\Logging\StructuredLogger;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

/**
 * Structured Logger Test
 *
 * Tests the structured logging functionality.
 */
class StructuredLoggerTest extends TestCase
{
    private StructuredLogger $logger;

    protected function setUp(): void
    {
        parent::setUp();
        $this->logger = new StructuredLogger();
    }

    public function test_generates_correlation_id(): void
    {
        $correlationId = $this->logger->getCorrelationId();

        $this->assertNotEmpty($correlationId);
        $this->assertIsString($correlationId);
        $this->assertMatchesRegularExpression('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $correlationId);
    }

    public function test_uses_provided_correlation_id(): void
    {
        $customId = 'test-correlation-id';
        $logger = new StructuredLogger($customId);

        $this->assertEquals($customId, $logger->getCorrelationId());
    }

    public function test_sets_additional_context(): void
    {
        $context = ['user_id' => 123, 'action' => 'test'];
        $this->logger->setContext($context);

        // We can't directly test the context, but we can test that the method returns the logger
        $this->assertInstanceOf(StructuredLogger::class, $this->logger->setContext($context));
    }

    public function test_logs_auth_attempt_success(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Authentication attempt successful', \Mockery::type('array'));

        $this->logger->logAuthAttempt('<EMAIL>', 'legacy', true);
    }

    public function test_logs_auth_attempt_failure(): void
    {
        Log::shouldReceive('warning')
            ->once()
            ->with('Authentication attempt failed', \Mockery::type('array'));

        $this->logger->logAuthAttempt('<EMAIL>', 'legacy', false, 'Invalid credentials');
    }

    public function test_logs_password_reset_request(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Password reset requested', \Mockery::type('array'));

        $this->logger->logPasswordResetRequest('<EMAIL>', true);
    }

    public function test_logs_password_reset_complete_success(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Password reset completed successfully', \Mockery::type('array'));

        $this->logger->logPasswordResetComplete('<EMAIL>', true);
    }

    public function test_logs_password_reset_complete_failure(): void
    {
        Log::shouldReceive('warning')
            ->once()
            ->with('Password reset failed', \Mockery::type('array'));

        $this->logger->logPasswordResetComplete('<EMAIL>', false);
    }

    public function test_logs_mfa_attempt_success(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('MFA verification successful', \Mockery::type('array'));

        $this->logger->logMfaAttempt(1, 'otp', true);
    }

    public function test_logs_mfa_attempt_failure(): void
    {
        Log::shouldReceive('warning')
            ->once()
            ->with('MFA verification failed', \Mockery::type('array'));

        $this->logger->logMfaAttempt(1, 'otp', false);
    }

    public function test_logs_token_validation(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Token validation performed', \Mockery::type('array'));

        $this->logger->logTokenValidation('jwt', true);
    }

    public function test_logs_security_event(): void
    {
        Log::shouldReceive('warning')
            ->once()
            ->with('Security event detected', \Mockery::type('array'));

        $this->logger->logSecurityEvent('suspicious_activity', ['attempts' => 5]);
    }

    public function test_logs_api_request_success(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('API request completed successfully', \Mockery::type('array'));

        $this->logger->logApiRequest('GET', '/api/v2/auth/user', 200, 0.1);
    }

    public function test_logs_api_request_error(): void
    {
        Log::shouldReceive('warning')
            ->once()
            ->with('API request completed with error', \Mockery::type('array'));

        $this->logger->logApiRequest('POST', '/api/v2/auth/login', 401, 0.05);
    }

    public function test_logs_error_with_exception(): void
    {
        $exception = new \Exception('Test exception', 500);

        Log::shouldReceive('error')
            ->once()
            ->with('Exception occurred', \Mockery::type('array'));

        $this->logger->logError($exception);
    }

    public function test_logs_performance_metric(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Performance metric recorded', \Mockery::type('array'));

        $this->logger->logPerformanceMetric('database_query', 0.025, ['query_type' => 'SELECT']);
    }

    public function test_generic_log_method(): void
    {
        Log::shouldReceive('log')
            ->once()
            ->with('debug', 'Test message', \Mockery::type('array'));

        $this->logger->log('debug', 'Test message', ['extra' => 'data']);
    }

    public function test_context_includes_service_info(): void
    {
        Log::shouldReceive('log')
            ->once()
            ->with('info', 'Test message', \Mockery::on(function ($context) {
                return isset($context['correlation_id']) &&
                       isset($context['service']) &&
                       isset($context['version']) &&
                       isset($context['timestamp']) &&
                       $context['service'] === 'auth-service';
            }));

        $this->logger->log('info', 'Test message');
    }

    public function test_auth_attempt_includes_request_info(): void
    {
        // Mock request data
        request()->merge(['test' => true]);

        Log::shouldReceive('info')
            ->once()
            ->with('Authentication attempt successful', \Mockery::on(function ($context) {
                return isset($context['event_type']) &&
                       isset($context['username']) &&
                       isset($context['auth_method']) &&
                       isset($context['success']) &&
                       isset($context['ip_address']) &&
                       isset($context['user_agent']) &&
                       isset($context['timestamp']) &&
                       $context['event_type'] === 'auth_attempt';
            }));

        $this->logger->logAuthAttempt('<EMAIL>', 'legacy', true);
    }

    public function test_performance_metric_includes_duration(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Performance metric recorded', \Mockery::on(function ($context) {
                return isset($context['event_type']) &&
                       isset($context['operation']) &&
                       isset($context['duration_ms']) &&
                       isset($context['timestamp']) &&
                       $context['event_type'] === 'performance_metric' &&
                       $context['operation'] === 'test_operation' &&
                       $context['duration_ms'] === 50.0;
            }));

        $this->logger->logPerformanceMetric('test_operation', 0.05);
    }
}

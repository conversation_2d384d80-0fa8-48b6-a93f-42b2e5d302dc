<?php

declare(strict_types=1);

namespace Tests\Unit\Services\Security;

use App\Services\Security\GeoIpService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GeoIpServiceTest extends TestCase
{
    use RefreshDatabase;

    private GeoIpService $geoIpService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock GeoIP configuration
        Config::set('geoip', [
            'maxmind' => [
                'database_path' => storage_path('app/geoip/GeoLite2-City.mmdb'),
                'country_database_path' => storage_path('app/geoip/GeoLite2-Country.mmdb'),
            ],
            'cache' => [
                'enabled' => true,
                'ttl' => 3600,
                'prefix' => 'geoip',
            ],
            'fallback' => [
                'enabled' => true,
                'service' => 'ip-api',
                'timeout' => 5,
            ],
            'security' => [
                'log_lookups' => false,
                'rate_limit' => 1000,
                'blocked_countries' => '',
                'allowed_countries' => '',
            ],
            'defaults' => [
                'country' => 'Unknown',
                'country_code' => 'XX',
                'city' => 'Unknown',
                'timezone' => 'UTC',
                'latitude' => 0.0,
                'longitude' => 0.0,
            ],
            'whitelist' => [
                '127.0.0.1',
                '::1',
                '10.0.0.0/8',
                '***********/16',
            ],
        ]);

        $this->geoIpService = new GeoIpService();
    }

    public function test_returns_default_location_for_whitelisted_ip(): void
    {
        $location = $this->geoIpService->getLocation('127.0.0.1');

        $this->assertEquals('Unknown', $location['country']);
        $this->assertEquals('XX', $location['country_code']);
        $this->assertEquals('default', $location['source']);
    }

    public function test_returns_default_location_for_private_ip(): void
    {
        $location = $this->geoIpService->getLocation('***********');

        $this->assertEquals('Unknown', $location['country']);
        $this->assertEquals('XX', $location['country_code']);
        $this->assertEquals('default', $location['source']);
    }

    public function test_uses_fallback_api_when_maxmind_unavailable(): void
    {
        Http::fake([
            'ip-api.com/*' => Http::response([
                'status' => 'success',
                'country' => 'United States',
                'countryCode' => 'US',
                'city' => 'New York',
                'timezone' => 'America/New_York',
                'lat' => 40.7128,
                'lon' => -74.0060,
            ]),
        ]);

        $location = $this->geoIpService->getLocation('*******');

        $this->assertEquals('United States', $location['country']);
        $this->assertEquals('US', $location['country_code']);
        $this->assertEquals('New York', $location['city']);
        $this->assertEquals('ip-api', $location['source']);
    }

    public function test_caches_location_results(): void
    {
        Http::fake([
            'ip-api.com/*' => Http::response([
                'status' => 'success',
                'country' => 'United States',
                'countryCode' => 'US',
                'city' => 'New York',
                'timezone' => 'America/New_York',
                'lat' => 40.7128,
                'lon' => -74.0060,
            ]),
        ]);

        // First call should hit the API
        $location1 = $this->geoIpService->getLocation('*******');

        // Second call should use cache
        $location2 = $this->geoIpService->getLocation('*******');

        $this->assertEquals($location1, $location2);
        Http::assertSentCount(1); // Only one API call should be made
    }

    public function test_get_country_returns_country_name(): void
    {
        Http::fake([
            'ip-api.com/*' => Http::response([
                'status' => 'success',
                'country' => 'United States',
                'countryCode' => 'US',
            ]),
        ]);

        $country = $this->geoIpService->getCountry('*******');

        $this->assertEquals('United States', $country);
    }

    public function test_get_country_code_returns_country_code(): void
    {
        Http::fake([
            'ip-api.com/*' => Http::response([
                'status' => 'success',
                'country' => 'United States',
                'countryCode' => 'US',
            ]),
        ]);

        $countryCode = $this->geoIpService->getCountryCode('*******');

        $this->assertEquals('US', $countryCode);
    }

    public function test_handles_api_failure_gracefully(): void
    {
        Http::fake([
            'ip-api.com/*' => Http::response([], 500),
        ]);

        $location = $this->geoIpService->getLocation('*******');

        $this->assertEquals('Unknown', $location['country']);
        $this->assertEquals('default', $location['source']);
    }

    public function test_ip_in_range_detection(): void
    {
        // Test private IP ranges
        $this->assertTrue($this->invokePrivateMethod('isWhitelisted', ['***********00']));
        $this->assertTrue($this->invokePrivateMethod('isWhitelisted', ['********']));
        $this->assertFalse($this->invokePrivateMethod('isWhitelisted', ['*******']));
    }

    /**
     * Helper method to invoke private methods for testing
     */
    private function invokePrivateMethod(string $methodName, array $args = [])
    {
        $reflection = new \ReflectionClass($this->geoIpService);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($this->geoIpService, $args);
    }
}

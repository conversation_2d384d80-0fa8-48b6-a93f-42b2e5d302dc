<?php

namespace Tests\Unit\Services;

use App\DTOs\Auth\LoginDTO;
use App\DTOs\Auth\ResetPasswordDTO;
use App\DTOs\Auth\UserDTO;
use App\Events\Auth\LoginFailed;
use App\Events\Auth\LoginSuccessful;
use App\Events\Auth\LogoutEvent;
use App\Events\Auth\PasswordResetCompleted;
use App\Events\Auth\PasswordResetRequested;
use App\Exceptions\Auth\InvalidCredentialsException;
use App\Exceptions\Auth\InvalidTokenException;
use App\Exceptions\Auth\UserNotFoundException;
use App\Models\PasswordReset;
use App\Models\User;
use App\Services\AuthService;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Mockery;
use Psr\Log\LoggerInterface;
use Tests\TestCase;

class AuthServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var AuthManager|Mockery\MockInterface
     */
    protected $authManager;

    /**
     * @var Guard|Mockery\MockInterface
     */
    protected $guard;

    /**
     * @var Dispatcher|Mockery\MockInterface
     */
    protected $eventDispatcher;

    /**
     * @var LoggerInterface|Mockery\MockInterface
     */
    protected $logger;

    /**
     * @var AuthService
     */
    protected $authService;

    /**
     * Set up the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->authManager = Mockery::mock(AuthManager::class);
        $this->guard = Mockery::mock(StatefulGuard::class);
        $this->eventDispatcher = Mockery::mock(Dispatcher::class);
        $this->logger = Mockery::mock(LoggerInterface::class);

        $this->authManager->shouldReceive('guard')
            ->andReturn($this->guard);

        $this->authService = new AuthService(
            $this->authManager,
            $this->eventDispatcher,
            $this->logger
        );
    }

    /**
     * Test successful authentication.
     */
    public function test_authenticate_success(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'first_name' => 'Test',
            'last_name' => 'User',
            'role_id' => 2,
            'auth_type' => 'legacy',
        ]);

        // Set up expectations
        $this->logger->shouldReceive('info')
            ->with('Authentication attempt', Mockery::any())
            ->once();

        $this->guard->shouldReceive('attempt')
            ->with(['username' => 'testuser', 'password' => 'password123'], false)
            ->andReturn(true)
            ->once();

        $this->guard->shouldReceive('user')
            ->andReturn($user)
            ->once();

        $this->eventDispatcher->shouldReceive('dispatch')
            ->with(Mockery::type(LoginSuccessful::class))
            ->once();

        $this->logger->shouldReceive('info')
            ->with('Authentication successful', Mockery::any())
            ->once();

        // Create login DTO
        $loginDTO = new LoginDTO('testuser', 'password123');

        // Authenticate user
        $userDTO = $this->authService->authenticate($loginDTO);

        // Assert user DTO is correct
        $this->assertInstanceOf(UserDTO::class, $userDTO);
        $this->assertEquals($user->id, $userDTO->id);
        $this->assertEquals($user->first_name, $userDTO->firstName);
        $this->assertEquals($user->last_name, $userDTO->lastName);
        $this->assertEquals($user->email, $userDTO->email);
        $this->assertEquals($user->role_id, $userDTO->roleId);
        $this->assertEquals($user->auth_type, $userDTO->authType);
    }

    /**
     * Test failed authentication.
     */
    public function test_authenticate_failure(): void
    {
        // Set up expectations
        $this->logger->shouldReceive('info')
            ->with('Authentication attempt', Mockery::any())
            ->once();

        $this->guard->shouldReceive('attempt')
            ->with(['username' => 'testuser', 'password' => 'wrongpassword'], false)
            ->andReturn(false)
            ->once();

        $this->eventDispatcher->shouldReceive('dispatch')
            ->with(Mockery::type(LoginFailed::class))
            ->once();

        $this->logger->shouldReceive('warning')
            ->with('Authentication failed', Mockery::any())
            ->once();

        // Create login DTO
        $loginDTO = new LoginDTO('testuser', 'wrongpassword');

        // Expect exception
        $this->expectException(InvalidCredentialsException::class);

        // Authenticate user
        $this->authService->authenticate($loginDTO);
    }

    /**
     * Test logout.
     */
    public function test_logout(): void
    {
        // Create a test user
        $user = User::factory()->create();

        // Set up expectations
        $this->guard->shouldReceive('user')
            ->andReturn($user)
            ->once();

        $this->logger->shouldReceive('info')
            ->with('Logout', Mockery::any())
            ->once();

        $this->eventDispatcher->shouldReceive('dispatch')
            ->with(Mockery::type(LogoutEvent::class))
            ->once();

        $this->guard->shouldReceive('logout')
            ->once();

        // Logout user
        $this->authService->logout();
    }

    /**
     * Test request password reset.
     */
    public function test_request_password_reset(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Set up expectations
        $this->eventDispatcher->shouldReceive('dispatch')
            ->with(Mockery::type(PasswordResetRequested::class))
            ->once();

        $this->logger->shouldReceive('info')
            ->with('Password reset requested', Mockery::any())
            ->once();

        // Request password reset
        $token = $this->authService->requestPasswordReset('<EMAIL>');

        // Assert token was created
        $this->assertNotEmpty($token);
        $this->assertDatabaseHas('password_resets', [
            'email' => '<EMAIL>',
            'used' => false,
        ]);
    }

    /**
     * Test request password reset for non-existent user.
     */
    public function test_request_password_reset_for_non_existent_user(): void
    {
        // Set up expectations
        $this->logger->shouldReceive('warning')
            ->with('Password reset requested for non-existent user', Mockery::any())
            ->once();

        // Expect exception
        $this->expectException(UserNotFoundException::class);

        // Request password reset
        $this->authService->requestPasswordReset('<EMAIL>');
    }

    /**
     * Test reset password.
     */
    public function test_reset_password(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
        ]);

        // Create a password reset token
        $token = 'valid-token';
        PasswordReset::create([
            'email' => '<EMAIL>',
            'token' => Hash::make($token),
            'created_at' => now(),
            'expires_at' => now()->addHour(),
            'used' => false,
        ]);

        // Set up expectations
        $this->eventDispatcher->shouldReceive('dispatch')
            ->with(Mockery::type(PasswordResetCompleted::class))
            ->once();

        $this->logger->shouldReceive('info')
            ->with('Password reset completed', Mockery::any())
            ->once();

        // Create reset password DTO
        $resetPasswordDTO = new ResetPasswordDTO('<EMAIL>', $token, 'newpassword');

        // Reset password
        $result = $this->authService->resetPassword($resetPasswordDTO);

        // Assert password was reset
        $this->assertTrue($result);
        $this->assertDatabaseHas('password_resets', [
            'email' => '<EMAIL>',
            'used' => true,
        ]);

        // Refresh user from database
        $user->refresh();

        // Assert password was changed
        $this->assertTrue(Hash::check('newpassword', $user->password));
    }

    /**
     * Test reset password with invalid token.
     */
    public function test_reset_password_with_invalid_token(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
        ]);

        // Create a password reset token
        PasswordReset::create([
            'email' => '<EMAIL>',
            'token' => Hash::make('valid-token'),
            'created_at' => now(),
            'expires_at' => now()->addHour(),
            'used' => false,
        ]);

        // Set up expectations
        $this->logger->shouldReceive('warning')
            ->with('Invalid password reset token', Mockery::any())
            ->once();

        // Create reset password DTO with invalid token
        $resetPasswordDTO = new ResetPasswordDTO('<EMAIL>', 'invalid-token', 'newpassword');

        // Expect exception
        $this->expectException(InvalidTokenException::class);

        // Reset password
        $this->authService->resetPassword($resetPasswordDTO);
    }

    /**
     * Test get authenticated user.
     */
    public function test_get_authenticated_user(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'role_id' => 2,
            'auth_type' => 'legacy',
        ]);

        // Set up expectations
        $this->guard->shouldReceive('user')
            ->andReturn($user)
            ->once();

        // Get authenticated user
        $userDTO = $this->authService->getAuthenticatedUser();

        // Assert user DTO is correct
        $this->assertInstanceOf(UserDTO::class, $userDTO);
        $this->assertEquals($user->id, $userDTO->id);
        $this->assertEquals($user->first_name, $userDTO->firstName);
        $this->assertEquals($user->last_name, $userDTO->lastName);
        $this->assertEquals($user->email, $userDTO->email);
        $this->assertEquals($user->role_id, $userDTO->roleId);
        $this->assertEquals($user->auth_type, $userDTO->authType);
    }

    /**
     * Test get authenticated user when not authenticated.
     */
    public function test_get_authenticated_user_when_not_authenticated(): void
    {
        // Set up expectations
        $this->guard->shouldReceive('user')
            ->andReturn(null)
            ->once();

        // Get authenticated user
        $userDTO = $this->authService->getAuthenticatedUser();

        // Assert user DTO is null
        $this->assertNull($userDTO);
    }
}

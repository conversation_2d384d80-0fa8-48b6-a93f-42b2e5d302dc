<?php

namespace Tests\Unit\Services\Auth;

use App\Models\User;
use App\Services\Auth\KeycloakAuthenticationService;
use App\Services\Auth\LegacyAuthenticationService;
use App\Services\Auth\UnifiedAuthenticationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UnifiedAuthenticationServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var LegacyAuthenticationService|Mockery\MockInterface
     */
    protected $legacyService;

    /**
     * @var KeycloakAuthenticationService|Mockery\MockInterface
     */
    protected $keycloakService;

    /**
     * @var UnifiedAuthenticationService
     */
    protected $service;

    /**
     * Set up the test.
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->legacyService = Mockery::mock(LegacyAuthenticationService::class);
        $this->keycloakService = Mockery::mock(KeycloakAuthenticationService::class);
        $this->service = new UnifiedAuthenticationService(
            $this->legacyService,
            $this->keycloakService,
            'legacy'
        );
    }

    /**
     * Test authenticate with legacy mode.
     */
    public function test_authenticate_with_legacy_mode(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Set up the mock
        $this->legacyService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => true,
                'user' => $user,
                'token' => 'test-token',
                'token_type' => 'Bearer',
            ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'password123');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($user->id, $result['user']->id);
        $this->assertEquals('test-token', $result['token']);
        $this->assertEquals('Bearer', $result['token_type']);
    }

    /**
     * Test authenticate with keycloak mode.
     */
    public function test_authenticate_with_keycloak_mode(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Set up the service
        $this->service->setAuthMethod('keycloak');

        // Set up the mock
        $this->keycloakService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => true,
                'user' => $user,
                'token' => 'test-token',
                'token_type' => 'Bearer',
                'keycloak_tokens' => [
                    'access_token' => 'access-token',
                    'refresh_token' => 'refresh-token',
                ],
            ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'password123');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($user->id, $result['user']->id);
        $this->assertEquals('test-token', $result['token']);
        $this->assertEquals('Bearer', $result['token_type']);
        $this->assertEquals('access-token', $result['keycloak_tokens']['access_token']);
        $this->assertEquals('refresh-token', $result['keycloak_tokens']['refresh_token']);
    }

    /**
     * Test authenticate with both mode, keycloak success.
     */
    public function test_authenticate_with_both_mode_keycloak_success(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Set up the service
        $this->service->setAuthMethod('both');

        // Set up the mock
        $this->keycloakService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => true,
                'user' => $user,
                'token' => 'test-token',
                'token_type' => 'Bearer',
                'keycloak_tokens' => [
                    'access_token' => 'access-token',
                    'refresh_token' => 'refresh-token',
                ],
            ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'password123');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($user->id, $result['user']->id);
        $this->assertEquals('test-token', $result['token']);
        $this->assertEquals('Bearer', $result['token_type']);
        $this->assertEquals('access-token', $result['keycloak_tokens']['access_token']);
        $this->assertEquals('refresh-token', $result['keycloak_tokens']['refresh_token']);
    }

    /**
     * Test authenticate with both mode, keycloak failure, legacy success.
     */
    public function test_authenticate_with_both_mode_keycloak_failure_legacy_success(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Set up the service
        $this->service->setAuthMethod('both');

        // Set up the mocks
        $this->keycloakService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => false,
                'message' => 'Keycloak authentication failed',
            ]);

        $this->legacyService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => true,
                'user' => $user,
                'token' => 'test-token',
                'token_type' => 'Bearer',
            ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'password123');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($user->id, $result['user']->id);
        $this->assertEquals('test-token', $result['token']);
        $this->assertEquals('Bearer', $result['token_type']);
    }

    /**
     * Test authenticate with both mode, both failure.
     */
    public function test_authenticate_with_both_mode_both_failure(): void
    {
        // Set up the service
        $this->service->setAuthMethod('both');

        // Set up the mocks
        $this->keycloakService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => false,
                'message' => 'Keycloak authentication failed',
            ]);

        $this->legacyService->shouldReceive('authenticate')
            ->once()
            ->with('testuser', 'password123')
            ->andReturn([
                'success' => false,
                'message' => 'Invalid credentials',
            ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'password123');

        // Assert the result
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid credentials', $result['message']);
    }

    /**
     * Test get auth method.
     */
    public function test_get_auth_method(): void
    {
        // Call the service
        $result = $this->service->getAuthMethod();

        // Assert the result
        $this->assertEquals('legacy', $result);

        // Change the auth method
        $this->service->setAuthMethod('keycloak');

        // Call the service again
        $result = $this->service->getAuthMethod();

        // Assert the result
        $this->assertEquals('keycloak', $result);
    }
}

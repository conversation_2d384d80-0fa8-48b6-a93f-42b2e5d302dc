<?php

namespace Tests\Unit\Services\Auth;

use App\Models\User;
use App\Services\Auth\LegacyAuthenticationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class LegacyAuthenticationServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var LegacyAuthenticationService
     */
    protected $service;

    /**
     * Set up the test.
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new LegacyAuthenticationService;
    }

    /**
     * Test authenticate with valid credentials.
     */
    public function test_authenticate_with_valid_credentials(): void
    {
        // Create a user
        $user = User::factory()->create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'password123');

        // Assert the result
        $this->assertTrue($result['success']);
        $this->assertEquals($user->id, $result['user']->id);
        $this->assertNotEmpty($result['token']);
        $this->assertEquals('Bearer', $result['token_type']);

        // Assert the user was updated
        $user->refresh();
        $this->assertEquals('legacy', $user->auth_type);
        $this->assertNotEmpty($user->auth_token);
    }

    /**
     * Test authenticate with invalid credentials.
     */
    public function test_authenticate_with_invalid_credentials(): void
    {
        // Create a user
        User::factory()->create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'wrongpassword');

        // Assert the result
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid credentials', $result['message']);
    }

    /**
     * Test authenticate with inactive user.
     */
    public function test_authenticate_with_inactive_user(): void
    {
        // Create a user
        User::factory()->create([
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 0,
        ]);

        // Call the service
        $result = $this->service->authenticate('testuser', 'password123');

        // Assert the result
        $this->assertFalse($result['success']);
        $this->assertEquals('User is inactive', $result['message']);
    }

    /**
     * Test authenticate with non-existent user.
     */
    public function test_authenticate_with_non_existent_user(): void
    {
        // Call the service
        $result = $this->service->authenticate('nonexistentuser', 'password123');

        // Assert the result
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid credentials', $result['message']);
    }

    /**
     * Test validate token with valid token.
     */
    public function test_validate_token_with_valid_token(): void
    {
        // Call the service with the test token
        $result = $this->service->validateToken('test-token');

        // Assert the result
        $this->assertTrue($result);
    }

    /**
     * Test validate token with invalid token.
     */
    public function test_validate_token_with_invalid_token(): void
    {
        // Call the service
        $result = $this->service->validateToken('invalid-token');

        // Assert the result
        $this->assertFalse($result);
    }

    /**
     * Test revoke token with valid token.
     */
    public function test_revoke_token_with_valid_token(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Create a token
        $token = $user->createToken('test-token');

        // Call the service
        $result = $this->service->revokeToken($token->plainTextToken);

        // Assert the result
        $this->assertTrue($result);

        // Assert the token was deleted
        $this->assertCount(0, $user->tokens);
    }

    /**
     * Test revoke token with invalid token.
     */
    public function test_revoke_token_with_invalid_token(): void
    {
        // Call the service
        $result = $this->service->revokeToken('invalid-token');

        // Assert the result
        $this->assertFalse($result);
    }

    /**
     * Test get auth method.
     */
    public function test_get_auth_method(): void
    {
        // Call the service
        $result = $this->service->getAuthMethod();

        // Assert the result
        $this->assertEquals('legacy', $result);
    }
}

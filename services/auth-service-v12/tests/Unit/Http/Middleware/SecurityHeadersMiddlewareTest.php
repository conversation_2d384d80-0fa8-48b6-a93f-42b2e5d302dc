<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Middleware;

use App\Http\Middleware\SecurityHeadersMiddleware;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Tests\TestCase;

/**
 * Security Headers Middleware Test
 *
 * Tests the security headers middleware functionality.
 */
class SecurityHeadersMiddlewareTest extends TestCase
{
    private SecurityHeadersMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new SecurityHeadersMiddleware();
    }

    public function test_adds_security_headers_to_response(): void
    {
        $request = Request::create('/test', 'GET');
        $response = new Response('Test content');

        $result = $this->middleware->handle($request, function () use ($response) {
            return $response;
        });

        // Check that security headers are added
        $this->assertEquals('DENY', $result->headers->get('X-Frame-Options'));
        $this->assertEquals('nosniff', $result->headers->get('X-Content-Type-Options'));
        $this->assertEquals('1; mode=block', $result->headers->get('X-XSS-Protection'));
        $this->assertEquals('strict-origin-when-cross-origin', $result->headers->get('Referrer-Policy'));
        $this->assertNotEmpty($result->headers->get('Content-Security-Policy'));
        $this->assertNotEmpty($result->headers->get('Permissions-Policy'));
    }

    public function test_removes_server_headers(): void
    {
        $request = Request::create('/test', 'GET');
        $response = new Response('Test content');

        // Add server headers that should be removed
        $response->headers->set('Server', 'Apache/2.4.41');
        $response->headers->set('X-Powered-By', 'PHP/8.1.0');

        $result = $this->middleware->handle($request, function () use ($response) {
            return $response;
        });

        // Check that server headers are removed
        $this->assertNull($result->headers->get('Server'));
        $this->assertNull($result->headers->get('X-Powered-By'));
    }

    public function test_adds_cache_headers_for_sensitive_endpoints(): void
    {
        $request = Request::create('/api/v2/auth/login', 'POST');
        $response = new Response('Test content');

        $result = $this->middleware->handle($request, function () use ($response) {
            return $response;
        });

        // Check that cache control headers are added for sensitive endpoints
        $cacheControl = $result->headers->get('Cache-Control');
        $this->assertStringContainsString('no-cache', $cacheControl);
        $this->assertStringContainsString('no-store', $cacheControl);
        $this->assertStringContainsString('must-revalidate', $cacheControl);
        $this->assertStringContainsString('private', $cacheControl);
        $this->assertEquals('no-cache', $result->headers->get('Pragma'));
        $this->assertEquals('0', $result->headers->get('Expires'));
    }

    public function test_does_not_add_cache_headers_for_non_sensitive_endpoints(): void
    {
        $request = Request::create('/api/v2/health', 'GET');
        $response = new Response('Test content');

        $result = $this->middleware->handle($request, function () use ($response) {
            return $response;
        });

        // Check that strict cache control headers are not added for non-sensitive endpoints
        $cacheControl = $result->headers->get('Cache-Control');
        if ($cacheControl) {
            $this->assertStringNotContainsString('no-store', $cacheControl);
            $this->assertStringNotContainsString('must-revalidate', $cacheControl);
        }
    }

    public function test_adds_hsts_header_for_https_requests(): void
    {
        $request = Request::create('https://example.com/test', 'GET');
        $response = new Response('Test content');

        $result = $this->middleware->handle($request, function () use ($response) {
            return $response;
        });

        // Check that HSTS header is added for HTTPS requests
        $this->assertEquals('max-age=31536000; includeSubDomains; preload', $result->headers->get('Strict-Transport-Security'));
    }

    public function test_does_not_add_hsts_header_for_http_requests(): void
    {
        $request = Request::create('http://example.com/test', 'GET');
        $response = new Response('Test content');

        $result = $this->middleware->handle($request, function () use ($response) {
            return $response;
        });

        // Check that HSTS header is not added for HTTP requests
        $this->assertNull($result->headers->get('Strict-Transport-Security'));
    }
}

#!/bin/bash

# Static Analysis Script for Auth Service
# This script runs various static analysis tools to ensure code quality

set -e

echo "🔍 Running Static Analysis for Auth Service..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if vendor directory exists
if [ ! -d "vendor" ]; then
    print_error "Vendor directory not found. Please run 'composer install' first."
    exit 1
fi

# 1. Run PHPStan
print_status "Running PHPStan (Level 5)..."
if vendor/bin/phpstan analyse app --level=5 --memory-limit=1G --no-progress; then
    print_success "PHPStan analysis completed successfully"
else
    print_warning "PHPStan found issues. Please review the output above."
fi

echo ""

# 2. Run Rector (dry-run)
print_status "Running Rector (dry-run)..."
if vendor/bin/rector process --dry-run --no-progress-bar; then
    print_success "Rector analysis completed - no changes needed"
else
    print_warning "Rector suggests improvements. Run 'vendor/bin/rector process' to apply them."
fi

echo ""

# 3. Run Laravel Pint (code style)
print_status "Running Laravel Pint (code style)..."
if vendor/bin/pint --test; then
    print_success "Code style is compliant"
else
    print_warning "Code style issues found. Run 'vendor/bin/pint' to fix them."
fi

echo ""

# 4. Run PHPUnit tests
print_status "Running PHPUnit tests..."
if vendor/bin/phpunit --no-coverage; then
    print_success "All tests passed"
else
    print_error "Some tests failed. Please fix them before proceeding."
    exit 1
fi

echo ""

# 5. Check for security vulnerabilities
print_status "Checking for security vulnerabilities..."
if composer audit; then
    print_success "No security vulnerabilities found"
else
    print_warning "Security vulnerabilities detected. Please review and update dependencies."
fi

echo ""
echo "================================================"
print_success "Static analysis completed!"
echo ""
echo "Summary:"
echo "- PHPStan: Static analysis for type safety"
echo "- Rector: Code modernization suggestions"
echo "- Pint: Code style compliance"
echo "- PHPUnit: Test suite execution"
echo "- Composer Audit: Security vulnerability check"
echo ""
echo "For more detailed analysis, run individual tools with verbose flags."

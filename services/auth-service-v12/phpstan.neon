includes:
    - vendor/larastan/larastan/extension.neon

parameters:
    level: 8
    paths:
        - app

    # Exclude files/directories
    excludePaths:
        - database/migrations/*
        - database/seeders/*
        - database/factories/*

    # Ignore errors
    ignoreErrors:
        # Ignore <PERSON>'s magic methods
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Builder#'
        - '#Call to an undefined method Illuminate\\Database\\Query\\Builder#'
        - '#Call to an undefined method Illuminate\\Http\\Request#'

        # Ignore <PERSON><PERSON>'s dynamic properties
        - '#Access to an undefined property Illuminate\\Database\\Eloquent\\Model#'
        - '#Access to an undefined property App\\Models\\#'

        # Ignore <PERSON><PERSON>'s facades
        - '#Call to an undefined static method Illuminate\\Support\\Facades\\#'

# Static Analysis and Code Quality

This document outlines the static analysis tools and code quality measures implemented in the Auth Service.

## Overview

We use several tools to ensure code quality, maintainability, and adherence to best practices:

- **PHPStan**: Static analysis for type safety and bug detection
- **Rector**: Automated code modernization and refactoring
- **<PERSON><PERSON>nt**: Code style formatting (based on PHP-CS-Fixer)
- **PHPUnit**: Unit and feature testing
- **Composer Audit**: Security vulnerability scanning

## Quick Start

### Run All Quality Checks
```bash
composer quality
# or
./scripts/static-analysis.sh
```

### Individual Tools

#### PHPStan (Static Analysis)
```bash
# Level 5 analysis (recommended for development)
composer analyse

# Maximum level analysis (level 8)
composer analyse-max

# Custom analysis
vendor/bin/phpstan analyse app --level=6 --memory-limit=1G
```

#### Rector (Code Modernization)
```bash
# Preview changes (dry-run)
composer refactor-dry

# Apply changes
composer refactor

# Custom rector run
vendor/bin/rector process app/Models --dry-run
```

#### Laravel Pint (Code Style)
```bash
# Check code style
composer style-test

# Fix code style
composer style

# Custom pint run
vendor/bin/pint app/Http/Controllers --test
```

#### PHPUnit (Testing)
```bash
# Run tests
composer test

# Run with coverage
vendor/bin/phpunit --coverage-html coverage
```

## Configuration Files

### PHPStan Configuration (`phpstan.neon`)
- **Level**: 8 (maximum strictness)
- **Paths**: `app/` directory
- **Extensions**: Larastan for Laravel-specific analysis
- **Ignored Patterns**: Laravel magic methods and facades

### Rector Configuration (`rector.php`)
- **PHP Version**: 8.2+
- **Sets**: Code quality, dead code removal, type declarations
- **Paths**: `app/` directory
- **Skipped**: Migrations, seeders, factories

### Pint Configuration
Uses Laravel preset with PSR-12 standards.

## Error Categories and Solutions

### PHPStan Errors

#### 1. Missing Dependencies
**Error**: `Class X not found`
**Solution**: Install missing packages or add to ignore list

#### 2. Type Issues
**Error**: `Access to an undefined property`
**Solution**: Add proper type hints or PHPDoc annotations

#### 3. Laravel Magic Methods
**Error**: `Call to an undefined method`
**Solution**: Already ignored in configuration

### Common Fixes

#### Fix UserResource Property Access
```php
// Before
return [
    'id' => $this->id,
    'name' => $this->name,
];

// After
/** @var \App\Models\User $this->resource */
return [
    'id' => $this->resource->id,
    'name' => $this->resource->name,
];
```

#### Fix Environment Variable Usage
```php
// Before (in service provider)
$secret = env('JWT_SECRET');

// After
$secret = config('jwt.secret');
```

## Integration with CI/CD

### Pre-commit Hook
```bash
#!/bin/sh
# .git/hooks/pre-commit
composer style-test && composer analyse
```

### GitLab CI Configuration
```yaml
code_quality:
  stage: test
  script:
    - composer install --no-dev --optimize-autoloader
    - composer quality
  artifacts:
    reports:
      junit: phpunit-report.xml
```

## Best Practices

### 1. Type Declarations
Always use strict types and return type declarations:
```php
<?php

declare(strict_types=1);

public function authenticate(string $username, string $password): array
{
    // Implementation
}
```

### 2. PHPDoc Annotations
Use PHPDoc for complex types:
```php
/**
 * @param array<string, mixed> $data
 * @return Collection<int, User>
 */
public function createUsers(array $data): Collection
{
    // Implementation
}
```

### 3. Error Handling
Use typed exceptions:
```php
throw new InvalidCredentialsException('Invalid username or password');
```

### 4. Resource Classes
Properly type resource classes:
```php
class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /** @var User $this->resource */
        return [
            'id' => $this->resource->id,
            'email' => $this->resource->email,
        ];
    }
}
```

## Metrics and Targets

### Current Status
- **PHPStan Level**: 5 (target: 8)
- **Test Coverage**: 95%+ (target: 98%+)
- **Code Style**: 100% compliant
- **Security Issues**: 0

### Quality Gates
- All tests must pass
- PHPStan level 5 with 0 errors
- Code style must be 100% compliant
- No security vulnerabilities

## Troubleshooting

### Memory Issues
If PHPStan runs out of memory:
```bash
vendor/bin/phpstan analyse --memory-limit=2G
```

### Performance Issues
For large codebases, use result cache:
```bash
vendor/bin/phpstan analyse --configuration=phpstan.neon
```

### Rector Issues
If Rector makes unwanted changes:
1. Update `rector.php` to skip specific rules
2. Use `--dry-run` to preview changes
3. Apply changes selectively

## Resources

- [PHPStan Documentation](https://phpstan.org/user-guide/getting-started)
- [Rector Documentation](https://getrector.org/documentation)
- [Laravel Pint Documentation](https://laravel.com/docs/pint)
- [Larastan Documentation](https://github.com/larastan/larastan)

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | GeoIP Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for GeoIP services used in the
    | authentication service for security monitoring and location detection.
    |
    */

    // MaxMind GeoLite2 Database Configuration
    'maxmind' => [
        'database_path' => env('GEOIP_DATABASE_PATH', storage_path('app/geoip/GeoLite2-City.mmdb')),
        'country_database_path' => env('GEOIP_COUNTRY_DATABASE_PATH', storage_path('app/geoip/GeoLite2-Country.mmdb')),
        'license_key' => env('MAXMIND_LICENSE_KEY'),
        'account_id' => env('MAXMIND_ACCOUNT_ID'),
        'auto_update' => env('GEOIP_AUTO_UPDATE', true),
        'update_interval' => env('GEOIP_UPDATE_INTERVAL', 7), // days
    ],

    // Cache Configuration
    'cache' => [
        'enabled' => env('GEOIP_CACHE_ENABLED', true),
        'ttl' => env('GEOIP_CACHE_TTL', 86400), // 24 hours
        'prefix' => env('GEOIP_CACHE_PREFIX', 'geoip'),
    ],

    // Fallback Configuration
    'fallback' => [
        'enabled' => env('GEOIP_FALLBACK_ENABLED', true),
        'service' => env('GEOIP_FALLBACK_SERVICE', 'ip-api'), // ip-api, ipinfo, etc.
        'timeout' => env('GEOIP_FALLBACK_TIMEOUT', 5), // seconds
    ],

    // Security Configuration
    'security' => [
        'log_lookups' => env('GEOIP_LOG_LOOKUPS', true),
        'rate_limit' => env('GEOIP_RATE_LIMIT', 1000), // requests per hour
        'blocked_countries' => env('GEOIP_BLOCKED_COUNTRIES', ''), // comma-separated country codes
        'allowed_countries' => env('GEOIP_ALLOWED_COUNTRIES', ''), // comma-separated country codes
    ],

    // Default Values
    'defaults' => [
        'country' => 'Unknown',
        'country_code' => 'XX',
        'city' => 'Unknown',
        'timezone' => 'UTC',
        'latitude' => 0.0,
        'longitude' => 0.0,
    ],

    // IP Whitelist (internal IPs that should not be geo-located)
    'whitelist' => [
        '127.0.0.1',
        '::1',
        '10.0.0.0/8',
        '**********/12',
        '***********/16',
    ],
];

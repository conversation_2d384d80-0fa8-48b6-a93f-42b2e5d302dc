<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Keycloak Configuration
    |--------------------------------------------------------------------------
    |
    | This file is for storing the configuration for the Keycloak authentication
    | service. These values will be used when authenticating users with Keycloak.
    |
    */

    'auth_server_url' => env('KEYCLOAK_AUTH_SERVER_URL', 'http://localhost:8080/auth'),
    'realm' => env('KEYCLOAK_REALM', 'master'),
    'client_id' => env('KEYCLOAK_CLIENT_ID', 'laravel'),
    'client_secret' => env('KEYCLOAK_CLIENT_SECRET', ''),
    'redirect_uri' => env('KEYCLOAK_REDIRECT_URI', 'http://localhost:8000/auth/callback'),

    /*
    |--------------------------------------------------------------------------
    | Role Mapping
    |--------------------------------------------------------------------------
    |
    | This section defines how Keycloak roles map to application roles.
    |
    */

    'role_mapping' => [
        'admin' => 1,
        'user' => 2,
        'manager' => 3,
        'customer' => 4,
    ],
];

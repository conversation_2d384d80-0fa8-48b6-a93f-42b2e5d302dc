version: '3.8'

services:
  # Auth Service
  auth-service:
    build:
      context: .
      target: development
    container_name: auth-service-v12
    restart: unless-stopped
    environment:
      - APP_NAME=AuthService
      - APP_ENV=local
      - APP_KEY=${APP_KEY}
      - APP_DEBUG=true
      - APP_URL=http://localhost:8001
      - LOG_CHANNEL=stack
      - LOG_LEVEL=debug
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=auth_service
      - DB_USERNAME=auth_user
      - DB_PASSWORD=auth_password
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MAIL_MAILER=smtp
      - MAIL_HOST=mailhog
      - MAIL_PORT=1025
      - MAIL_USERNAME=null
      - MAIL_PASSWORD=null
      - MAIL_ENCRYPTION=null
      - AUTH_MODE=both
      - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_AUTH_SERVER_URL=${KEY<PERSON><PERSON>K_AUTH_SERVER_URL}
      - KEYCLOAK_REALM=${KEYCLOAK_REALM}
      - KEYCLOAK_CLIENT_ID=${KEYCLOAK_CLIENT_ID}
      - KEYCLOAK_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}
      - KEYCLOAK_REDIRECT_URI=${KEYCLOAK_REDIRECT_URI}
    volumes:
      - .:/var/www/html
      - ./storage:/var/www/html/storage
    ports:
      - "8001:8000"
    depends_on:
      - mysql
      - redis
      - mailhog
    networks:
      - app-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: auth-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: auth_service
      MYSQL_USER: auth_user
      MYSQL_PASSWORD: auth_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3307:3306"
    networks:
      - app-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: auth-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6380:6379"
    networks:
      - app-network

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: auth-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - app-network

  # PHPMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: auth-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_USER: auth_user
      PMA_PASSWORD: auth_password
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - app-network

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: auth-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - app-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  app-network:
    driver: bridge

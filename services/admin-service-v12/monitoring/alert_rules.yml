groups:
  - name: admin-service-alerts
    rules:
      - alert: HighRequestLatency
        expr: http_request_duration_seconds{service="admin-service"} > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request latency on {{ $labels.instance }}"
          description: "{{ $labels.instance }} has a request latency above 500ms (current value: {{ $value }}s)"

      - alert: HighErrorRate
        expr: sum(rate(http_requests_total{service="admin-service",status=~"5.."}[5m])) / sum(rate(http_requests_total{service="admin-service"}[5m])) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate on admin-service"
          description: "Error rate is above 5% (current value: {{ $value | humanizePercentage }})"

      - alert: ServiceDown
        expr: up{job="admin-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Admin service is down"
          description: "The admin service instance {{ $labels.instance }} is down"

      - alert: HighCPUUsage
        expr: process_cpu_seconds_total{job="admin-service"} > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "{{ $labels.instance }} has high CPU usage (current value: {{ $value | humanizePercentage }})"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="admin-service"} / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "{{ $labels.instance }} has high memory usage (current value: {{ $value | humanizePercentage }})"

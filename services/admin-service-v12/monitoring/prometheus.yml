global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'admin-service'
    metrics_path: /metrics
    static_configs:
      - targets: ['admin-service:8000']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        regex: '([^:]+)(?::\d+)?'
        replacement: '${1}'

  - job_name: 'kong'
    metrics_path: /metrics
    static_configs:
      - targets: ['kong:8001']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { configApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

export default function DashboardPage() {
  const [settings, setSettings] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const data = await configApi.getAll();
        setSettings(data);
      } catch (error) {
        console.error('Failed to fetch settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch configuration settings.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [toast]);

  const getSettingValue = (key: string, defaultValue: string = 'Not configured'): string => {
    return settings[key] || defaultValue;
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground mt-2">
          Welcome to your business configuration dashboard.
        </p>
      </div>

      {isLoading ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-5 bg-muted rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-muted rounded w-full mt-2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Company Name</CardTitle>
              <CardDescription>Your business name</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{getSettingValue('MERCHANT_COMPANY_NAME')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Support Email</CardTitle>
              <CardDescription>Customer support contact</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{getSettingValue('MERCHANT_SUPPORT_EMAIL')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Phone Number</CardTitle>
              <CardDescription>Business contact number</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{getSettingValue('GLOBAL_WEBSITE_PHONE')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Currency</CardTitle>
              <CardDescription>Default currency for transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">
                {getSettingValue('GLOBAL_CURRENCY')} ({getSettingValue('GLOBAL_CURRENCY_ENTITY')})
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Locale</CardTitle>
              <CardDescription>Regional settings</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{getSettingValue('GLOBAL_LOCALE')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Time Zone</CardTitle>
              <CardDescription>System time zone</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{getSettingValue('TIME_ZONE')}</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

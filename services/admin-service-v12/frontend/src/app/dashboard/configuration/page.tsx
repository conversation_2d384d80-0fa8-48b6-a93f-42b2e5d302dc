'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { configApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Can } from '@/contexts/auth-context';

interface Setting {
  setting_key: string;
  setting_value: string;
  setting_group: string;
  setting_type: string;
  is_system: boolean;
  is_public: boolean;
  description: string;
}

export default function ConfigurationPage() {
  const [settings, setSettings] = useState<Record<string, Setting[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('company');
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const data = await configApi.getAll();
        
        // Group settings by setting_group
        const groupedSettings: Record<string, Setting[]> = {};
        
        Object.entries(data).forEach(([key, value]) => {
          const setting = {
            setting_key: key,
            setting_value: value,
            setting_group: key.includes('MERCHANT') ? 'company' : 
                          key.includes('GLOBAL') ? 'system' : 
                          key.includes('PAYMENT') ? 'payment' : 'other',
            setting_type: typeof value === 'boolean' ? 'boolean' : 
                         typeof value === 'number' ? 'number' : 'string',
            is_system: key.includes('GLOBAL'),
            is_public: true,
            description: key,
          };
          
          if (!groupedSettings[setting.setting_group]) {
            groupedSettings[setting.setting_group] = [];
          }
          
          groupedSettings[setting.setting_group].push(setting);
        });
        
        setSettings(groupedSettings);
      } catch (error) {
        console.error('Failed to fetch settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch configuration settings.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [toast]);

  const handleEdit = (key: string, value: string) => {
    setEditingKey(key);
    setEditValue(value);
  };

  const handleSave = async () => {
    if (!editingKey) return;
    
    try {
      const setting = Object.values(settings)
        .flat()
        .find(s => s.setting_key === editingKey);
      
      if (!setting) return;
      
      await configApi.update(
        editingKey,
        editValue,
        setting.setting_type,
        setting.setting_group,
        setting.is_system,
        setting.is_public,
        setting.description
      );
      
      // Update local state
      const updatedSettings = { ...settings };
      const groupKey = setting.setting_group;
      
      updatedSettings[groupKey] = updatedSettings[groupKey].map(s => 
        s.setting_key === editingKey ? { ...s, setting_value: editValue } : s
      );
      
      setSettings(updatedSettings);
      setEditingKey(null);
      
      toast({
        title: 'Success',
        description: 'Setting updated successfully.',
      });
    } catch (error) {
      console.error('Failed to update setting:', error);
      toast({
        title: 'Error',
        description: 'Failed to update setting.',
        variant: 'destructive',
      });
    }
  };

  const handleCancel = () => {
    setEditingKey(null);
  };

  const renderSettingValue = (setting: Setting) => {
    if (editingKey === setting.setting_key) {
      return (
        <div className="flex items-center space-x-2">
          <Input
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            className="max-w-xs"
          />
          <Button size="sm" onClick={handleSave}>Save</Button>
          <Button size="sm" variant="outline" onClick={handleCancel}>Cancel</Button>
        </div>
      );
    }
    
    return (
      <div className="flex items-center justify-between">
        <span>{setting.setting_value}</span>
        <Can permission="edit_settings">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleEdit(setting.setting_key, setting.setting_value)}
          >
            Edit
          </Button>
        </Can>
      </div>
    );
  };

  const renderSettingsGroup = (groupKey: string) => {
    const groupSettings = settings[groupKey] || [];
    
    if (groupSettings.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No settings found in this group.</p>
        </div>
      );
    }
    
    return (
      <div className="space-y-4">
        {groupSettings.map((setting) => (
          <Card key={setting.setting_key}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{setting.setting_key}</CardTitle>
              <CardDescription>{setting.description}</CardDescription>
            </CardHeader>
            <CardContent>
              {renderSettingValue(setting)}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Configuration</h1>
        <p className="text-muted-foreground mt-2">
          Manage your business configuration settings.
        </p>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <div className="h-10 bg-muted rounded w-full max-w-md animate-pulse"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="pb-2">
                  <div className="h-5 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-4 bg-muted rounded w-full mt-2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full max-w-md grid-cols-4">
            <TabsTrigger value="company">Company</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
            <TabsTrigger value="payment">Payment</TabsTrigger>
            <TabsTrigger value="other">Other</TabsTrigger>
          </TabsList>
          <div className="mt-6">
            <TabsContent value="company">
              {renderSettingsGroup('company')}
            </TabsContent>
            <TabsContent value="system">
              {renderSettingsGroup('system')}
            </TabsContent>
            <TabsContent value="payment">
              {renderSettingsGroup('payment')}
            </TabsContent>
            <TabsContent value="other">
              {renderSettingsGroup('other')}
            </TabsContent>
          </div>
        </Tabs>
      )}
    </div>
  );
}

'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { setupWizardApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

// Define form schema with validation
const formSchema = z.object({
  company_name: z.string().min(2, 'Company name must be at least 2 characters'),
  postal_address: z.string().min(5, 'Postal address must be at least 5 characters'),
  support_email: z.string().email('Invalid email address'),
  phone: z.string().min(5, 'Phone number must be at least 5 characters'),
  sender_id: z.string().min(2, 'Sender ID must be at least 2 characters').max(11, 'Sender ID must be at most 11 characters'),
});

type FormValues = z.infer<typeof formSchema>;

interface CompanyProfileFormProps {
  onComplete: () => void;
}

export default function CompanyProfileForm({ onComplete }: CompanyProfileFormProps) {
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      company_name: '',
      postal_address: '',
      support_email: '',
      phone: '',
      sender_id: '',
    },
  });

  const { toast } = useToast();

  const onSubmit = async (data: FormValues) => {
    try {
      await setupWizardApi.setupCompanyProfile(data);
      toast({
        title: 'Success',
        description: 'Company profile saved successfully!',
      });
      onComplete();
    } catch (error) {
      console.error('Failed to save company profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to save company profile. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="company_name">Company Name</Label>
        <Input
          id="company_name"
          placeholder="Enter your company name"
          {...register('company_name')}
        />
        {errors.company_name && (
          <p className="text-sm text-red-500">{errors.company_name.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="postal_address">Postal Address</Label>
        <Input
          id="postal_address"
          placeholder="Enter your postal address"
          {...register('postal_address')}
        />
        {errors.postal_address && (
          <p className="text-sm text-red-500">{errors.postal_address.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="support_email">Support Email</Label>
        <Input
          id="support_email"
          type="email"
          placeholder="Enter your support email"
          {...register('support_email')}
        />
        {errors.support_email && (
          <p className="text-sm text-red-500">{errors.support_email.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          placeholder="Enter your phone number"
          {...register('phone')}
        />
        {errors.phone && (
          <p className="text-sm text-red-500">{errors.phone.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="sender_id">SMS Sender ID</Label>
        <Input
          id="sender_id"
          placeholder="Enter your SMS sender ID"
          {...register('sender_id')}
        />
        {errors.sender_id && (
          <p className="text-sm text-red-500">{errors.sender_id.message}</p>
        )}
        <p className="text-xs text-muted-foreground">
          The sender ID is used for SMS notifications. It must be at most 11 characters.
        </p>
      </div>

      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Saving...' : 'Save and Continue'}
      </Button>
    </form>
  );
}

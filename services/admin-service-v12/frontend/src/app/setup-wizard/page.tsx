'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { setupWizardApi } from '@/lib/api';
import CompanyProfileForm from './company-profile-form';
import SystemSettingsForm from './system-settings-form';
import { useToast } from '@/components/ui/use-toast';

export default function SetupWizard() {
  const [activeStep, setActiveStep] = useState('company-profile');
  const [isCompleted, setIsCompleted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    const fetchSetupStatus = async () => {
      try {
        const status = await setupWizardApi.getStatus();
        
        if (status.completed) {
          // If setup is already completed, redirect to dashboard
          router.push('/dashboard');
          return;
        }

        // Set the active step based on the current_step
        switch (status.current_step) {
          case 1:
            setActiveStep('company-profile');
            break;
          case 2:
            setActiveStep('system-settings');
            break;
          case 3:
            setActiveStep('complete');
            break;
          default:
            setActiveStep('company-profile');
        }
      } catch (error) {
        console.error('Failed to fetch setup status:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch setup status. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSetupStatus();
  }, [router, toast]);

  const handleCompanyProfileComplete = () => {
    setActiveStep('system-settings');
  };

  const handleSystemSettingsComplete = () => {
    setActiveStep('complete');
  };

  const handleCompleteSetup = async () => {
    try {
      setIsLoading(true);
      await setupWizardApi.completeSetup();
      setIsCompleted(true);
      toast({
        title: 'Success',
        description: 'Setup completed successfully!',
      });
      router.push('/dashboard');
    } catch (error) {
      console.error('Failed to complete setup:', error);
      toast({
        title: 'Error',
        description: 'Failed to complete setup. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Loading...</h2>
          <p className="text-muted-foreground">Please wait while we load your setup wizard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold">Business Configuration Setup</h1>
          <p className="text-muted-foreground mt-2">
            Complete the following steps to set up your business configuration.
          </p>
        </div>

        <Tabs value={activeStep} onValueChange={setActiveStep} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="company-profile" disabled={activeStep !== 'company-profile'}>
              Company Profile
            </TabsTrigger>
            <TabsTrigger value="system-settings" disabled={activeStep !== 'system-settings' && activeStep !== 'complete'}>
              System Settings
            </TabsTrigger>
            <TabsTrigger value="complete" disabled={activeStep !== 'complete'}>
              Complete
            </TabsTrigger>
          </TabsList>

          <TabsContent value="company-profile">
            <Card>
              <CardHeader>
                <CardTitle>Company Profile</CardTitle>
                <CardDescription>
                  Enter your company details that will be used throughout the system.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CompanyProfileForm onComplete={handleCompanyProfileComplete} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="system-settings">
            <Card>
              <CardHeader>
                <CardTitle>System Settings</CardTitle>
                <CardDescription>
                  Configure system-wide settings like locale, currency, and time zone.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SystemSettingsForm onComplete={handleSystemSettingsComplete} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="complete">
            <Card>
              <CardHeader>
                <CardTitle>Complete Setup</CardTitle>
                <CardDescription>
                  You're all set! Review your configuration and complete the setup.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  You have successfully configured your business settings. Click the button below to complete the setup and go to your dashboard.
                </p>
              </CardContent>
              <CardFooter>
                <Button onClick={handleCompleteSetup} disabled={isLoading}>
                  {isLoading ? 'Completing...' : 'Complete Setup'}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

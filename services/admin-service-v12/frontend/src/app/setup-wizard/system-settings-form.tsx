'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { setupWizardApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Define form schema with validation
const formSchema = z.object({
  locale: z.string().min(2, 'Locale must be at least 2 characters'),
  currency: z.string().min(1, 'Currency is required'),
  currency_symbol: z.string().min(1, 'Currency symbol is required'),
  time_zone: z.string().min(1, 'Time zone is required'),
});

type FormValues = z.infer<typeof formSchema>;

interface SystemSettingsFormProps {
  onComplete: () => void;
}

// Time zone options
const timeZones = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time (US & Canada)' },
  { value: 'America/Chicago', label: 'Central Time (US & Canada)' },
  { value: 'America/Denver', label: 'Mountain Time (US & Canada)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (US & Canada)' },
  { value: 'Asia/Kolkata', label: 'India Standard Time' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time' },
  { value: 'Europe/London', label: 'Greenwich Mean Time' },
  { value: 'Europe/Paris', label: 'Central European Time' },
];

// Locale options
const locales = [
  { value: 'en_US', label: 'English (US)' },
  { value: 'en_GB', label: 'English (UK)' },
  { value: 'fr_FR', label: 'French' },
  { value: 'de_DE', label: 'German' },
  { value: 'es_ES', label: 'Spanish' },
  { value: 'it_IT', label: 'Italian' },
  { value: 'ja_JP', label: 'Japanese' },
  { value: 'zh_CN', label: 'Chinese (Simplified)' },
  { value: 'hi_IN', label: 'Hindi' },
];

// Currency options
const currencies = [
  { value: 'USD', label: 'US Dollar', symbol: '$' },
  { value: 'EUR', label: 'Euro', symbol: '€' },
  { value: 'GBP', label: 'British Pound', symbol: '£' },
  { value: 'JPY', label: 'Japanese Yen', symbol: '¥' },
  { value: 'INR', label: 'Indian Rupee', symbol: '₹' },
  { value: 'CAD', label: 'Canadian Dollar', symbol: 'C$' },
  { value: 'AUD', label: 'Australian Dollar', symbol: 'A$' },
  { value: 'CNY', label: 'Chinese Yuan', symbol: '¥' },
];

export default function SystemSettingsForm({ onComplete }: SystemSettingsFormProps) {
  const { register, handleSubmit, setValue, watch, formState: { errors, isSubmitting } } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      locale: 'en_US',
      currency: 'USD',
      currency_symbol: '$',
      time_zone: 'UTC',
    },
  });

  const { toast } = useToast();
  const selectedCurrency = watch('currency');

  // Update currency symbol when currency changes
  const handleCurrencyChange = (value: string) => {
    setValue('currency', value);
    const currency = currencies.find(c => c.value === value);
    if (currency) {
      setValue('currency_symbol', currency.symbol);
    }
  };

  const onSubmit = async (data: FormValues) => {
    try {
      await setupWizardApi.setupSystemSettings(data);
      toast({
        title: 'Success',
        description: 'System settings saved successfully!',
      });
      onComplete();
    } catch (error) {
      console.error('Failed to save system settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save system settings. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="locale">Locale</Label>
        <Select defaultValue="en_US" onValueChange={(value) => setValue('locale', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select locale" />
          </SelectTrigger>
          <SelectContent>
            {locales.map((locale) => (
              <SelectItem key={locale.value} value={locale.value}>
                {locale.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <input type="hidden" {...register('locale')} />
        {errors.locale && (
          <p className="text-sm text-red-500">{errors.locale.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="currency">Currency</Label>
        <Select defaultValue="USD" onValueChange={handleCurrencyChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select currency" />
          </SelectTrigger>
          <SelectContent>
            {currencies.map((currency) => (
              <SelectItem key={currency.value} value={currency.value}>
                {currency.label} ({currency.symbol})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <input type="hidden" {...register('currency')} />
        {errors.currency && (
          <p className="text-sm text-red-500">{errors.currency.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="currency_symbol">Currency Symbol</Label>
        <Input
          id="currency_symbol"
          {...register('currency_symbol')}
          readOnly
        />
        {errors.currency_symbol && (
          <p className="text-sm text-red-500">{errors.currency_symbol.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="time_zone">Time Zone</Label>
        <Select defaultValue="UTC" onValueChange={(value) => setValue('time_zone', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select time zone" />
          </SelectTrigger>
          <SelectContent>
            {timeZones.map((tz) => (
              <SelectItem key={tz.value} value={tz.value}>
                {tz.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <input type="hidden" {...register('time_zone')} />
        {errors.time_zone && (
          <p className="text-sm text-red-500">{errors.time_zone.message}</p>
        )}
      </div>

      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Saving...' : 'Save and Continue'}
      </Button>
    </form>
  );
}

'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useAuth, Can } from '@/contexts/auth-context';
import { 
  Settings, 
  Users, 
  Home, 
  CreditCard, 
  Package, 
  ShoppingCart, 
  BarChart, 
  Truck, 
  Coffee, 
  LogOut 
} from 'lucide-react';

const navItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    permission: 'view_dashboard',
  },
  {
    title: 'Configuration',
    href: '/dashboard/configuration',
    icon: Settings,
    permission: 'view_settings',
  },
  {
    title: 'Roles & Permissions',
    href: '/dashboard/roles',
    icon: Users,
    permission: 'view_roles',
  },
  {
    title: 'Payment Settings',
    href: '/dashboard/payment-settings',
    icon: CreditCard,
    permission: 'view_payment_settings',
  },
  {
    title: 'Catalog Settings',
    href: '/dashboard/catalog-settings',
    icon: Package,
    permission: 'view_catalog_settings',
  },
  {
    title: 'Order Settings',
    href: '/dashboard/order-settings',
    icon: ShoppingCart,
    permission: 'view_order_settings',
  },
  {
    title: 'Analytics Settings',
    href: '/dashboard/analytics-settings',
    icon: BarChart,
    permission: 'view_analytics_settings',
  },
  {
    title: 'Delivery Settings',
    href: '/dashboard/delivery-settings',
    icon: Truck,
    permission: 'view_delivery_settings',
  },
  {
    title: 'Kitchen Settings',
    href: '/dashboard/kitchen-settings',
    icon: Coffee,
    permission: 'view_kitchen_settings',
  },
];

export default function Sidebar() {
  const pathname = usePathname();
  const { logout } = useAuth();

  return (
    <div className="hidden md:flex md:flex-col md:w-64 md:bg-card md:border-r">
      <div className="flex items-center justify-center h-16 border-b">
        <h1 className="text-xl font-bold">Admin Console</h1>
      </div>
      <div className="flex flex-col flex-1 overflow-y-auto">
        <nav className="flex-1 px-2 py-4 space-y-1">
          {navItems.map((item) => (
            <Can
              key={item.href}
              permission={item.permission}
              fallback={null}
            >
              <Link
                href={item.href}
                className={cn(
                  'flex items-center px-4 py-2 text-sm rounded-md',
                  pathname === item.href
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                )}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.title}
              </Link>
            </Can>
          ))}
        </nav>
        <div className="p-4 border-t">
          <button
            onClick={logout}
            className="flex items-center w-full px-4 py-2 text-sm text-muted-foreground rounded-md hover:bg-accent hover:text-accent-foreground"
          >
            <LogOut className="w-5 h-5 mr-3" />
            Logout
          </button>
        </div>
      </div>
    </div>
  );
}

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API service for configuration management
export const configApi = {
  // Get all configuration values
  getAll: async (): Promise<Record<string, any>> => {
    const response = await api.get('/v2/admin/config');
    return response.data.data;
  },

  // Get configuration by key
  getByKey: async (key: string): Promise<any> => {
    const response = await api.get(`/v2/admin/config/${key}`);
    return response.data.data;
  },

  // Get configuration by group
  getByGroup: async (group: string): Promise<Record<string, any>> => {
    const response = await api.get(`/v2/admin/config/group/${group}`);
    return response.data.data;
  },

  // Update configuration value
  update: async (key: string, value: any, type: string = 'string', group?: string, isSystem: boolean = false, isPublic: boolean = false, description?: string): Promise<any> => {
    const response = await api.put(`/v2/admin/config/${key}`, {
      value,
      type,
      group,
      is_system: isSystem,
      is_public: isPublic,
      description,
    });
    return response.data.data;
  },

  // Delete configuration value
  delete: async (key: string): Promise<void> => {
    await api.delete(`/v2/admin/config/${key}`);
  },
};

// API service for role management
export const roleApi = {
  // Get all roles
  getAll: async (): Promise<any[]> => {
    const response = await api.get('/v2/admin/roles');
    return response.data.data;
  },

  // Get role by ID
  getById: async (id: number): Promise<any> => {
    const response = await api.get(`/v2/admin/roles/${id}`);
    return response.data.data;
  },

  // Create new role
  create: async (data: any): Promise<any> => {
    const response = await api.post('/v2/admin/roles', data);
    return response.data.data;
  },

  // Update role
  update: async (id: number, data: any): Promise<any> => {
    const response = await api.put(`/v2/admin/roles/${id}`, data);
    return response.data.data;
  },

  // Delete role
  delete: async (id: number): Promise<void> => {
    await api.delete(`/v2/admin/roles/${id}`);
  },

  // Get all permissions
  getAllPermissions: async (): Promise<any[]> => {
    const response = await api.get('/v2/admin/permissions');
    return response.data.data;
  },

  // Get permissions by module
  getPermissionsByModule: async (module: string): Promise<any[]> => {
    const response = await api.get(`/v2/admin/permissions/module/${module}`);
    return response.data.data;
  },
};

// API service for setup wizard
export const setupWizardApi = {
  // Get setup wizard status
  getStatus: async (): Promise<any> => {
    const response = await api.get('/v2/admin/setup-wizard/status');
    return response.data.data;
  },

  // Update setup wizard status
  updateStatus: async (data: { completed?: boolean; current_step?: number }): Promise<any> => {
    const response = await api.put('/v2/admin/setup-wizard/status', data);
    return response.data.data;
  },

  // Setup company profile
  setupCompanyProfile: async (data: {
    company_name: string;
    postal_address: string;
    support_email: string;
    phone: string;
    sender_id: string;
  }): Promise<any> => {
    const response = await api.post('/v2/admin/setup-wizard/company-profile', data);
    return response.data.data;
  },

  // Setup system settings
  setupSystemSettings: async (data: {
    locale: string;
    currency: string;
    currency_symbol: string;
    time_zone: string;
  }): Promise<any> => {
    const response = await api.post('/v2/admin/setup-wizard/system-settings', data);
    return response.data.data;
  },

  // Complete setup wizard
  completeSetup: async (): Promise<any> => {
    const response = await api.post('/v2/admin/setup-wizard/complete');
    return response.data.data;
  },
};

export default api;

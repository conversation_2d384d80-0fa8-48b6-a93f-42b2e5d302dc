<?php

namespace Database\Factories;

use App\Models\DeliveryLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeliveryLocation>
 */
class DeliveryLocationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DeliveryLocation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'location' => $this->faker->city(),
            'city' => $this->faker->city(),
            'sub_city_area' => $this->faker->streetName(),
            'pin' => $this->faker->postcode(),
            'delivery_charges' => $this->faker->randomFloat(2, 0, 100),
            'delivery_time' => $this->faker->numberBetween(15, 60),
            'is_default' => false,
            'status' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the location is default.
     */
    public function isDefault(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_default' => true,
            ];
        });
    }
}

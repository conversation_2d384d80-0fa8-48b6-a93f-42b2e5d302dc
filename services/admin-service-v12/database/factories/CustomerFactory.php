<?php

namespace Database\Factories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_name' => $this->faker->name(),
            'phone' => $this->faker->unique()->phoneNumber(),
            'email_address' => $this->faker->unique()->safeEmail(),
            'customer_Address' => $this->faker->address(),
            'location_code' => $this->faker->numberBetween(1, 10),
            'food_preference' => $this->faker->randomElement(['veg', 'non-veg']),
            'dabbawala_code' => null,
            'dabbawala_code_type' => null,
            'dabbawala_image' => null,
            'dabba_status' => null,
            'delivery_person_id' => null,
            'menu_type' => $this->faker->randomElement(['lunch', 'dinner']),
            'modified_on' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the customer has a dabbawala code.
     */
    public function withDabbawalaCode(): static
    {
        return $this->state(function (array $attributes) {
            $customerId = $attributes['pk_customer_code'] ?? $this->faker->unique()->numberBetween(1, 1000);
            $areaCode = $this->faker->randomElement(['MU', 'PU', 'DE', 'BA']);
            $random = str_pad($this->faker->numberBetween(0, 999), 3, '0', STR_PAD_LEFT);
            
            return [
                'dabbawala_code_type' => 'mumbai',
                'dabbawala_code' => 'MD-' . $areaCode . '-' . $customerId . '-' . $random,
                'dabba_status' => $this->faker->randomElement(['IN', 'OUT', 'MISSING', 'OTHER']),
            ];
        });
    }
}

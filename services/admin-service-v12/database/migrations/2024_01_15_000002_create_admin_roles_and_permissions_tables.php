<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create admin_roles table
        Schema::create('admin_roles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->json('permissions'); // Array of permission IDs
            $table->boolean('is_system')->default(false);
            $table->integer('user_count')->default(0);
            $table->uuid('created_by')->nullable();
            $table->uuid('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['name']);
            $table->index(['is_system']);
            $table->index(['created_at']);
        });

        // Create admin_permissions table
        Schema::create('admin_permissions', function (Blueprint $table) {
            $table->string('id')->primary(); // e.g., 'users.read', 'orders.write'
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('resource'); // e.g., 'users', 'orders', 'settings'
            $table->string('action'); // e.g., 'read', 'write', 'delete'
            $table->string('module'); // e.g., 'user_management', 'order_management'
            $table->boolean('is_system')->default(false);
            $table->timestamps();

            // Indexes
            $table->index(['resource', 'action']);
            $table->index(['module']);
            $table->index(['is_system']);
        });

        // Create admin_role_permissions pivot table
        Schema::create('admin_role_permissions', function (Blueprint $table) {
            $table->uuid('role_id');
            $table->string('permission_id');
            $table->timestamps();

            // Primary key
            $table->primary(['role_id', 'permission_id']);

            // Foreign keys
            $table->foreign('role_id')->references('id')->on('admin_roles')->onDelete('cascade');
            $table->foreign('permission_id')->references('id')->on('admin_permissions')->onDelete('cascade');
        });

        // Seed default roles and permissions
        $this->seedDefaultRolesAndPermissions();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_role_permissions');
        Schema::dropIfExists('admin_permissions');
        Schema::dropIfExists('admin_roles');
    }

    /**
     * Seed default roles and permissions.
     */
    private function seedDefaultRolesAndPermissions(): void
    {
        // Default permissions for OneFoodDialer 2025 Admin Dashboard
        $permissions = [
            // Dashboard permissions
            ['id' => 'dashboard.view', 'name' => 'View Dashboard', 'description' => 'Can view admin dashboard', 'resource' => 'dashboard', 'action' => 'view', 'module' => 'dashboard'],
            ['id' => 'dashboard.analytics', 'name' => 'View Analytics', 'description' => 'Can view analytics data', 'resource' => 'dashboard', 'action' => 'analytics', 'module' => 'dashboard'],
            
            // User Management permissions
            ['id' => 'users.read', 'name' => 'View Users', 'description' => 'Can view admin users', 'resource' => 'users', 'action' => 'read', 'module' => 'user_management'],
            ['id' => 'users.write', 'name' => 'Manage Users', 'description' => 'Can create and edit admin users', 'resource' => 'users', 'action' => 'write', 'module' => 'user_management'],
            ['id' => 'users.delete', 'name' => 'Delete Users', 'description' => 'Can delete admin users', 'resource' => 'users', 'action' => 'delete', 'module' => 'user_management'],
            ['id' => 'users.suspend', 'name' => 'Suspend Users', 'description' => 'Can suspend/activate admin users', 'resource' => 'users', 'action' => 'suspend', 'module' => 'user_management'],
            
            // Role Management permissions
            ['id' => 'roles.read', 'name' => 'View Roles', 'description' => 'Can view roles and permissions', 'resource' => 'roles', 'action' => 'read', 'module' => 'role_management'],
            ['id' => 'roles.write', 'name' => 'Manage Roles', 'description' => 'Can create and edit roles', 'resource' => 'roles', 'action' => 'write', 'module' => 'role_management'],
            ['id' => 'roles.delete', 'name' => 'Delete Roles', 'description' => 'Can delete roles', 'resource' => 'roles', 'action' => 'delete', 'module' => 'role_management'],
            
            // Cloud Kitchen Management permissions
            ['id' => 'kitchens.read', 'name' => 'View Kitchens', 'description' => 'Can view cloud kitchens', 'resource' => 'kitchens', 'action' => 'read', 'module' => 'kitchen_management'],
            ['id' => 'kitchens.write', 'name' => 'Manage Kitchens', 'description' => 'Can create and edit cloud kitchens', 'resource' => 'kitchens', 'action' => 'write', 'module' => 'kitchen_management'],
            ['id' => 'kitchens.approve', 'name' => 'Approve Kitchens', 'description' => 'Can approve/reject cloud kitchens', 'resource' => 'kitchens', 'action' => 'approve', 'module' => 'kitchen_management'],
            ['id' => 'kitchens.suspend', 'name' => 'Suspend Kitchens', 'description' => 'Can suspend cloud kitchens', 'resource' => 'kitchens', 'action' => 'suspend', 'module' => 'kitchen_management'],
            
            // Order Management permissions
            ['id' => 'orders.read', 'name' => 'View Orders', 'description' => 'Can view orders', 'resource' => 'orders', 'action' => 'read', 'module' => 'order_management'],
            ['id' => 'orders.write', 'name' => 'Manage Orders', 'description' => 'Can edit orders', 'resource' => 'orders', 'action' => 'write', 'module' => 'order_management'],
            ['id' => 'orders.refund', 'name' => 'Process Refunds', 'description' => 'Can process order refunds', 'resource' => 'orders', 'action' => 'refund', 'module' => 'order_management'],
            ['id' => 'orders.cancel', 'name' => 'Cancel Orders', 'description' => 'Can cancel orders', 'resource' => 'orders', 'action' => 'cancel', 'module' => 'order_management'],
            
            // System Settings permissions
            ['id' => 'settings.read', 'name' => 'View Settings', 'description' => 'Can view system settings', 'resource' => 'settings', 'action' => 'read', 'module' => 'system_settings'],
            ['id' => 'settings.write', 'name' => 'Manage Settings', 'description' => 'Can edit system settings', 'resource' => 'settings', 'action' => 'write', 'module' => 'system_settings'],
            ['id' => 'settings.backup', 'name' => 'Backup System', 'description' => 'Can create system backups', 'resource' => 'settings', 'action' => 'backup', 'module' => 'system_settings'],
            ['id' => 'settings.maintenance', 'name' => 'Maintenance Mode', 'description' => 'Can enable/disable maintenance mode', 'resource' => 'settings', 'action' => 'maintenance', 'module' => 'system_settings'],
            
            // Reports & Analytics permissions
            ['id' => 'reports.read', 'name' => 'View Reports', 'description' => 'Can view reports and analytics', 'resource' => 'reports', 'action' => 'read', 'module' => 'reports_analytics'],
            ['id' => 'reports.create', 'name' => 'Create Reports', 'description' => 'Can create custom reports', 'resource' => 'reports', 'action' => 'create', 'module' => 'reports_analytics'],
            ['id' => 'reports.export', 'name' => 'Export Reports', 'description' => 'Can export reports', 'resource' => 'reports', 'action' => 'export', 'module' => 'reports_analytics'],
            ['id' => 'analytics.advanced', 'name' => 'Advanced Analytics', 'description' => 'Can access advanced analytics features', 'resource' => 'analytics', 'action' => 'advanced', 'module' => 'reports_analytics'],
        ];

        // Insert permissions
        foreach ($permissions as $permission) {
            DB::table('admin_permissions')->insert(array_merge($permission, [
                'is_system' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Default roles
        $roles = [
            [
                'id' => DB::raw('UUID()'),
                'name' => 'super_admin',
                'display_name' => 'Super Administrator',
                'description' => 'Full system access with all permissions',
                'permissions' => json_encode(array_column($permissions, 'id')),
                'is_system' => true,
            ],
            [
                'id' => DB::raw('UUID()'),
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Administrator with most permissions',
                'permissions' => json_encode([
                    'dashboard.view', 'dashboard.analytics',
                    'users.read', 'users.write', 'users.suspend',
                    'roles.read',
                    'kitchens.read', 'kitchens.write', 'kitchens.approve',
                    'orders.read', 'orders.write', 'orders.refund',
                    'settings.read', 'settings.write',
                    'reports.read', 'reports.create', 'reports.export',
                ]),
                'is_system' => true,
            ],
            [
                'id' => DB::raw('UUID()'),
                'name' => 'manager',
                'display_name' => 'Manager',
                'description' => 'Manager with operational permissions',
                'permissions' => json_encode([
                    'dashboard.view', 'dashboard.analytics',
                    'users.read',
                    'kitchens.read', 'kitchens.write',
                    'orders.read', 'orders.write',
                    'settings.read',
                    'reports.read', 'reports.export',
                ]),
                'is_system' => true,
            ],
            [
                'id' => DB::raw('UUID()'),
                'name' => 'support',
                'display_name' => 'Support Staff',
                'description' => 'Support staff with limited permissions',
                'permissions' => json_encode([
                    'dashboard.view',
                    'kitchens.read',
                    'orders.read', 'orders.write',
                    'reports.read',
                ]),
                'is_system' => true,
            ],
        ];

        // Insert roles
        foreach ($roles as $role) {
            DB::table('admin_roles')->insert(array_merge($role, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
};

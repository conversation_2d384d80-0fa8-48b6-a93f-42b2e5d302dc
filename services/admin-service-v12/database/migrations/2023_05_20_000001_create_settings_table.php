<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->string('setting_key')->index();
            $table->text('setting_value')->nullable();
            $table->string('setting_group')->nullable()->index();
            $table->string('setting_type')->default('string');
            $table->boolean('is_system')->default(false);
            $table->boolean('is_public')->default(false);
            $table->text('description')->nullable();
            $table->timestamps();

            // Add unique constraint for company_id, unit_id, and setting_key
            $table->unique(['company_id', 'unit_id', 'setting_key']);
        });

        // Insert default settings
        $this->seedDefaultSettings();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }

    /**
     * Seed default settings.
     */
    private function seedDefaultSettings(): void
    {
        $settings = [
            // Company/Business Profile
            [
                'setting_key' => 'MERCHANT_COMPANY_NAME',
                'setting_value' => 'Demo Company',
                'setting_group' => 'company',
                'setting_type' => 'string',
                'is_system' => false,
                'is_public' => true,
                'description' => 'Company name',
            ],
            [
                'setting_key' => 'MERCHANT_POSTAL_ADDRESS',
                'setting_value' => '123 Main St, Anytown, CA 12345',
                'setting_group' => 'company',
                'setting_type' => 'string',
                'is_system' => false,
                'is_public' => true,
                'description' => 'Company postal address',
            ],
            [
                'setting_key' => 'GLOBAL_WEBSITE_PHONE',
                'setting_value' => '555-1234',
                'setting_group' => 'company',
                'setting_type' => 'string',
                'is_system' => false,
                'is_public' => true,
                'description' => 'Company phone number',
            ],
            [
                'setting_key' => 'MERCHANT_SUPPORT_EMAIL',
                'setting_value' => '<EMAIL>',
                'setting_group' => 'company',
                'setting_type' => 'string',
                'is_system' => false,
                'is_public' => true,
                'description' => 'Support email address',
            ],
            [
                'setting_key' => 'MERCHANT_SENDER_ID',
                'setting_value' => 'DEMO',
                'setting_group' => 'company',
                'setting_type' => 'string',
                'is_system' => false,
                'is_public' => false,
                'description' => 'SMS sender ID',
            ],

            // System Configuration
            [
                'setting_key' => 'GLOBAL_LOCALE',
                'setting_value' => 'en_US',
                'setting_group' => 'system',
                'setting_type' => 'string',
                'is_system' => true,
                'is_public' => false,
                'description' => 'Application locale',
            ],
            [
                'setting_key' => 'GLOBAL_CURRENCY',
                'setting_value' => 'USD',
                'setting_group' => 'system',
                'setting_type' => 'string',
                'is_system' => true,
                'is_public' => true,
                'description' => 'Default currency',
            ],
            [
                'setting_key' => 'GLOBAL_CURRENCY_ENTITY',
                'setting_value' => '$',
                'setting_group' => 'system',
                'setting_type' => 'string',
                'is_system' => true,
                'is_public' => true,
                'description' => 'Currency symbol',
            ],
            [
                'setting_key' => 'TIME_ZONE',
                'setting_value' => 'UTC',
                'setting_group' => 'system',
                'setting_type' => 'string',
                'is_system' => true,
                'is_public' => false,
                'description' => 'Application time zone',
            ],
            [
                'setting_key' => 'WEBSITE_MAINTENANCE_ADMIN_PORTAL',
                'setting_value' => 'no',
                'setting_group' => 'system',
                'setting_type' => 'boolean',
                'is_system' => true,
                'is_public' => false,
                'description' => 'Admin portal maintenance mode',
            ],
        ];

        foreach ($settings as $setting) {
            DB::table('settings')->insert(array_merge($setting, [
                'company_id' => 1,
                'unit_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->uuid('role_id')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->json('permissions')->nullable(); // Direct permissions
            $table->timestamp('last_login_at')->nullable();
            $table->integer('login_count')->default(0);
            $table->string('avatar')->nullable();
            $table->string('phone')->nullable();
            $table->string('timezone')->default('UTC');
            $table->string('language')->default('en');
            $table->json('preferences')->nullable();
            $table->uuid('created_by')->nullable();
            $table->uuid('updated_by')->nullable();
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['role_id']);
            $table->index(['email', 'status']);
            $table->index(['last_login_at']);
            
            // Foreign key constraints
            $table->foreign('role_id')->references('id')->on('admin_roles')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('admin_users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_user_sessions table for session management
        Schema::create('admin_user_sessions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->string('session_token')->unique();
            $table->string('ip_address', 45);
            $table->text('user_agent');
            $table->timestamp('last_activity');
            $table->timestamp('expires_at');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'is_active']);
            $table->index(['session_token']);
            $table->index(['expires_at']);
            
            // Foreign key
            $table->foreign('user_id')->references('id')->on('admin_users')->onDelete('cascade');
        });

        // Create admin_user_activity_logs table
        Schema::create('admin_user_activity_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->string('action');
            $table->string('resource')->nullable();
            $table->uuid('resource_id')->nullable();
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->string('ip_address', 45);
            $table->text('user_agent');
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index(['resource', 'resource_id']);
            
            // Foreign key
            $table->foreign('user_id')->references('id')->on('admin_users')->onDelete('cascade');
        });

        // Seed default admin user
        $this->seedDefaultAdminUser();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_user_activity_logs');
        Schema::dropIfExists('admin_user_sessions');
        Schema::dropIfExists('admin_users');
    }

    /**
     * Seed default admin user.
     */
    private function seedDefaultAdminUser(): void
    {
        // Get the admin role
        $adminRole = DB::table('admin_roles')->where('name', 'admin')->first();
        
        if ($adminRole) {
            DB::table('admin_users')->insert([
                'id' => DB::raw('UUID()'),
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('admin123'), // Change in production
                'role_id' => $adminRole->id,
                'status' => 'active',
                'timezone' => 'Asia/Kolkata',
                'language' => 'en',
                'preferences' => json_encode([
                    'theme' => 'light',
                    'notifications' => [
                        'email' => true,
                        'browser' => true,
                        'mobile' => true
                    ],
                    'dashboard' => [
                        'default_view' => 'overview',
                        'refresh_interval' => 30
                    ]
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
};

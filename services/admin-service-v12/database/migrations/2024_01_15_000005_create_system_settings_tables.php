<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create admin_system_settings table
        Schema::create('admin_system_settings', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->text('value');
            $table->string('type')->default('string'); // string, integer, boolean, json, array
            $table->string('category'); // general, payment, notification, etc.
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false); // Can be accessed by frontend
            $table->boolean('is_encrypted')->default(false);
            $table->json('validation_rules')->nullable();
            $table->text('default_value')->nullable();
            $table->uuid('updated_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['category']);
            $table->index(['is_public']);
            $table->index(['updated_by']);
            
            // Foreign key
            $table->foreign('updated_by')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_system_backups table
        Schema::create('admin_system_backups', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['full', 'database', 'files', 'configuration'])->default('full');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'failed'])->default('pending');
            $table->string('file_path')->nullable();
            $table->bigInteger('file_size')->nullable(); // Bytes
            $table->json('included_tables')->nullable();
            $table->json('excluded_tables')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->uuid('created_by');
            $table->timestamps();

            // Indexes
            $table->index(['type', 'status']);
            $table->index(['created_by']);
            $table->index(['completed_at']);
            
            // Foreign key
            $table->foreign('created_by')->references('id')->on('admin_users')->onDelete('cascade');
        });

        // Create admin_system_maintenance table
        Schema::create('admin_system_maintenance', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('title');
            $table->text('description');
            $table->enum('type', ['scheduled', 'emergency', 'update', 'patch'])->default('scheduled');
            $table->enum('status', ['planned', 'in_progress', 'completed', 'cancelled'])->default('planned');
            $table->timestamp('scheduled_start');
            $table->timestamp('scheduled_end');
            $table->timestamp('actual_start')->nullable();
            $table->timestamp('actual_end')->nullable();
            $table->text('maintenance_message')->nullable(); // Message to show users
            $table->json('affected_services')->nullable();
            $table->boolean('notify_users')->default(true);
            $table->text('completion_notes')->nullable();
            $table->uuid('created_by');
            $table->uuid('updated_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'scheduled_start']);
            $table->index(['type']);
            $table->index(['created_by']);
            
            // Foreign keys
            $table->foreign('created_by')->references('id')->on('admin_users')->onDelete('cascade');
            $table->foreign('updated_by')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_system_health table
        Schema::create('admin_system_health', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('service_name');
            $table->enum('status', ['healthy', 'warning', 'critical', 'unknown'])->default('unknown');
            $table->json('metrics'); // CPU, memory, disk, etc.
            $table->json('checks'); // Health check results
            $table->text('message')->nullable();
            $table->timestamp('last_check');
            $table->integer('uptime')->nullable(); // Seconds
            $table->decimal('response_time', 8, 2)->nullable(); // Milliseconds
            $table->timestamps();

            // Indexes
            $table->index(['service_name', 'last_check']);
            $table->index(['status']);
            $table->unique(['service_name', 'created_at']);
        });

        // Create admin_system_logs table
        Schema::create('admin_system_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->enum('level', ['emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug'])->default('info');
            $table->string('channel')->default('system');
            $table->text('message');
            $table->json('context')->nullable();
            $table->string('user_id')->nullable();
            $table->string('session_id')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('url')->nullable();
            $table->string('method')->nullable();
            $table->json('request_data')->nullable();
            $table->json('response_data')->nullable();
            $table->integer('response_code')->nullable();
            $table->decimal('execution_time', 8, 3)->nullable(); // Seconds
            $table->timestamps();

            // Indexes
            $table->index(['level', 'created_at']);
            $table->index(['channel', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['created_at']);
        });

        // Create admin_integrations table
        Schema::create('admin_integrations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('type'); // payment_gateway, notification, analytics, etc.
            $table->string('provider'); // razorpay, stripe, twilio, etc.
            $table->text('description')->nullable();
            $table->boolean('is_enabled')->default(false);
            $table->json('configuration'); // Integration-specific config
            $table->json('credentials')->nullable(); // Encrypted credentials
            $table->json('webhook_config')->nullable();
            $table->enum('status', ['active', 'inactive', 'error', 'testing'])->default('inactive');
            $table->timestamp('last_sync')->nullable();
            $table->text('last_error')->nullable();
            $table->json('health_check')->nullable();
            $table->uuid('configured_by');
            $table->uuid('updated_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['type', 'provider']);
            $table->index(['is_enabled', 'status']);
            $table->index(['configured_by']);
            
            // Foreign keys
            $table->foreign('configured_by')->references('id')->on('admin_users')->onDelete('cascade');
            $table->foreign('updated_by')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_api_keys table
        Schema::create('admin_api_keys', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('key_hash'); // Hashed API key
            $table->string('key_prefix', 8); // First 8 characters for identification
            $table->text('description')->nullable();
            $table->json('permissions')->nullable(); // API permissions
            $table->json('rate_limits')->nullable(); // Rate limiting config
            $table->string('ip_whitelist')->nullable(); // Comma-separated IPs
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_used')->nullable();
            $table->integer('usage_count')->default(0);
            $table->timestamp('expires_at')->nullable();
            $table->uuid('created_by');
            $table->timestamps();

            // Indexes
            $table->index(['key_hash']);
            $table->index(['key_prefix']);
            $table->index(['is_active', 'expires_at']);
            $table->index(['created_by']);
            
            // Foreign key
            $table->foreign('created_by')->references('id')->on('admin_users')->onDelete('cascade');
        });

        // Seed default system settings
        $this->seedDefaultSettings();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_api_keys');
        Schema::dropIfExists('admin_integrations');
        Schema::dropIfExists('admin_system_logs');
        Schema::dropIfExists('admin_system_health');
        Schema::dropIfExists('admin_system_maintenance');
        Schema::dropIfExists('admin_system_backups');
        Schema::dropIfExists('admin_system_settings');
    }

    /**
     * Seed default system settings.
     */
    private function seedDefaultSettings(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'app.name',
                'value' => 'OneFoodDialer 2025',
                'type' => 'string',
                'category' => 'general',
                'name' => 'Application Name',
                'description' => 'The name of the application',
                'is_public' => true,
                'default_value' => 'OneFoodDialer 2025',
            ],
            [
                'key' => 'app.timezone',
                'value' => 'Asia/Kolkata',
                'type' => 'string',
                'category' => 'general',
                'name' => 'Default Timezone',
                'description' => 'Default timezone for the application',
                'is_public' => true,
                'default_value' => 'Asia/Kolkata',
            ],
            [
                'key' => 'app.currency',
                'value' => 'INR',
                'type' => 'string',
                'category' => 'general',
                'name' => 'Default Currency',
                'description' => 'Default currency for the application',
                'is_public' => true,
                'default_value' => 'INR',
            ],
            [
                'key' => 'app.maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'category' => 'general',
                'name' => 'Maintenance Mode',
                'description' => 'Enable/disable maintenance mode',
                'is_public' => true,
                'default_value' => 'false',
            ],
            
            // Order Settings
            [
                'key' => 'orders.auto_accept_timeout',
                'value' => '300',
                'type' => 'integer',
                'category' => 'orders',
                'name' => 'Auto Accept Timeout',
                'description' => 'Timeout in seconds for auto-accepting orders',
                'is_public' => false,
                'default_value' => '300',
            ],
            [
                'key' => 'orders.cancellation_window',
                'value' => '600',
                'type' => 'integer',
                'category' => 'orders',
                'name' => 'Cancellation Window',
                'description' => 'Time window in seconds for order cancellation',
                'is_public' => true,
                'default_value' => '600',
            ],
            
            // Payment Settings
            [
                'key' => 'payments.default_gateway',
                'value' => 'razorpay',
                'type' => 'string',
                'category' => 'payments',
                'name' => 'Default Payment Gateway',
                'description' => 'Default payment gateway to use',
                'is_public' => false,
                'default_value' => 'razorpay',
            ],
            [
                'key' => 'payments.commission_rate',
                'value' => '15.0',
                'type' => 'decimal',
                'category' => 'payments',
                'name' => 'Default Commission Rate',
                'description' => 'Default commission rate percentage',
                'is_public' => false,
                'default_value' => '15.0',
            ],
            
            // Notification Settings
            [
                'key' => 'notifications.email_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'notifications',
                'name' => 'Email Notifications',
                'description' => 'Enable/disable email notifications',
                'is_public' => false,
                'default_value' => 'true',
            ],
            [
                'key' => 'notifications.sms_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'notifications',
                'name' => 'SMS Notifications',
                'description' => 'Enable/disable SMS notifications',
                'is_public' => false,
                'default_value' => 'true',
            ],
        ];

        foreach ($settings as $setting) {
            DB::table('admin_system_settings')->insert(array_merge($setting, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
};

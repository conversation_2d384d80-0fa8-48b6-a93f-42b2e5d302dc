<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create roles table
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name')->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_system')->default(false);
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamps();

            // Add index for company_id and unit_id
            $table->index(['company_id', 'unit_id']);
        });

        // Create permissions table
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name')->nullable();
            $table->text('description')->nullable();
            $table->string('module')->nullable()->index();
            $table->boolean('is_system')->default(false);
            $table->timestamps();
        });

        // Create role_permission pivot table
        Schema::create('role_permission', function (Blueprint $table) {
            $table->unsignedBigInteger('role_id');
            $table->unsignedBigInteger('permission_id');
            $table->timestamps();

            // Add primary key
            $table->primary(['role_id', 'permission_id']);

            // Add foreign keys
            $table->foreign('role_id')
                ->references('id')
                ->on('roles')
                ->onDelete('cascade');

            $table->foreign('permission_id')
                ->references('id')
                ->on('permissions')
                ->onDelete('cascade');
        });

        // Seed default roles and permissions
        $this->seedDefaultRolesAndPermissions();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_permission');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('roles');
    }

    /**
     * Seed default roles and permissions.
     */
    private function seedDefaultRolesAndPermissions(): void
    {
        // Default roles
        $roles = [
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'System administrator with full access',
                'is_system' => true,
            ],
            [
                'name' => 'manager',
                'display_name' => 'Manager',
                'description' => 'Manager with access to most features',
                'is_system' => true,
            ],
            [
                'name' => 'staff',
                'display_name' => 'Staff',
                'description' => 'Staff with limited access',
                'is_system' => true,
            ],
        ];

        // Default permissions
        $permissions = [
            // Configuration permissions
            [
                'name' => 'view_settings',
                'display_name' => 'View Settings',
                'description' => 'Can view system settings',
                'module' => 'config',
                'is_system' => true,
            ],
            [
                'name' => 'edit_settings',
                'display_name' => 'Edit Settings',
                'description' => 'Can edit system settings',
                'module' => 'config',
                'is_system' => true,
            ],
            
            // Role permissions
            [
                'name' => 'view_roles',
                'display_name' => 'View Roles',
                'description' => 'Can view roles',
                'module' => 'roles',
                'is_system' => true,
            ],
            [
                'name' => 'create_roles',
                'display_name' => 'Create Roles',
                'description' => 'Can create roles',
                'module' => 'roles',
                'is_system' => true,
            ],
            [
                'name' => 'edit_roles',
                'display_name' => 'Edit Roles',
                'description' => 'Can edit roles',
                'module' => 'roles',
                'is_system' => true,
            ],
            [
                'name' => 'delete_roles',
                'display_name' => 'Delete Roles',
                'description' => 'Can delete roles',
                'module' => 'roles',
                'is_system' => true,
            ],
        ];

        // Insert roles
        foreach ($roles as $role) {
            DB::table('roles')->insert(array_merge($role, [
                'company_id' => 1,
                'unit_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Insert permissions
        foreach ($permissions as $permission) {
            DB::table('permissions')->insert(array_merge($permission, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Assign all permissions to admin role
        $adminRole = DB::table('roles')->where('name', 'admin')->first();
        $allPermissions = DB::table('permissions')->get();

        foreach ($allPermissions as $permission) {
            DB::table('role_permission')->insert([
                'role_id' => $adminRole->id,
                'permission_id' => $permission->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Assign view permissions to manager role
        $managerRole = DB::table('roles')->where('name', 'manager')->first();
        $viewPermissions = DB::table('permissions')
            ->whereIn('name', ['view_settings', 'view_roles'])
            ->get();

        foreach ($viewPermissions as $permission) {
            DB::table('role_permission')->insert([
                'role_id' => $managerRole->id,
                'permission_id' => $permission->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
};

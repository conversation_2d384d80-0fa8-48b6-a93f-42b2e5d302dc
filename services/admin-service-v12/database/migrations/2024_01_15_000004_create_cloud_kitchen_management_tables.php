<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create admin_cloud_kitchens table
        Schema::create('admin_cloud_kitchens', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('business_name');
            $table->string('owner_name');
            $table->string('email')->unique();
            $table->string('phone');
            $table->text('description')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'suspended', 'inactive'])->default('pending');
            $table->string('region');
            $table->string('city');
            $table->string('state');
            $table->string('country')->default('India');
            $table->json('address'); // Complete address details
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->json('business_hours'); // Operating hours
            $table->json('cuisine_types'); // Array of cuisine types
            $table->integer('seating_capacity')->nullable();
            $table->decimal('delivery_radius', 8, 2)->default(5.0); // In kilometers
            $table->decimal('minimum_order_value', 10, 2)->default(0);
            $table->decimal('commission_rate', 5, 2)->default(15.0); // Percentage
            $table->json('documents'); // License, permits, etc.
            $table->json('bank_details')->nullable();
            $table->json('contact_persons')->nullable(); // Additional contacts
            $table->json('social_media')->nullable(); // Social media links
            $table->string('website')->nullable();
            $table->json('features')->nullable(); // Available features
            $table->json('equipment')->nullable(); // Kitchen equipment
            $table->integer('staff_count')->nullable();
            $table->decimal('monthly_revenue', 12, 2)->nullable();
            $table->integer('daily_order_capacity')->nullable();
            $table->json('certifications')->nullable(); // Food safety, etc.
            $table->timestamp('approved_at')->nullable();
            $table->uuid('approved_by')->nullable();
            $table->text('approval_notes')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->uuid('rejected_by')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamp('last_inspection')->nullable();
            $table->uuid('created_by')->nullable();
            $table->uuid('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['status', 'region']);
            $table->index(['email']);
            $table->index(['city', 'state']);
            $table->index(['approved_at']);
            $table->index(['created_at']);
            
            // Foreign keys
            $table->foreign('approved_by')->references('id')->on('admin_users')->onDelete('set null');
            $table->foreign('rejected_by')->references('id')->on('admin_users')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('admin_users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_kitchen_approvals table for approval workflow
        Schema::create('admin_kitchen_approvals', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('kitchen_id');
            $table->enum('type', ['initial', 'renewal', 'modification', 'inspection'])->default('initial');
            $table->enum('status', ['pending', 'in_review', 'approved', 'rejected', 'requires_changes'])->default('pending');
            $table->uuid('reviewer_id')->nullable();
            $table->json('checklist'); // Approval checklist items
            $table->json('documents_required'); // Required documents
            $table->json('documents_submitted')->nullable(); // Submitted documents
            $table->text('notes')->nullable();
            $table->text('feedback')->nullable(); // Feedback for improvements
            $table->timestamp('submitted_at');
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('expires_at')->nullable(); // For renewals
            $table->timestamps();

            // Indexes
            $table->index(['kitchen_id', 'type']);
            $table->index(['status', 'submitted_at']);
            $table->index(['reviewer_id']);
            
            // Foreign keys
            $table->foreign('kitchen_id')->references('id')->on('admin_cloud_kitchens')->onDelete('cascade');
            $table->foreign('reviewer_id')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_kitchen_inspections table
        Schema::create('admin_kitchen_inspections', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('kitchen_id');
            $table->enum('type', ['routine', 'complaint', 'renewal', 'follow_up'])->default('routine');
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled');
            $table->uuid('inspector_id');
            $table->timestamp('scheduled_at');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->json('checklist'); // Inspection checklist
            $table->json('findings')->nullable(); // Inspection findings
            $table->enum('result', ['pass', 'pass_with_conditions', 'fail'])->nullable();
            $table->decimal('score', 5, 2)->nullable(); // Inspection score
            $table->text('notes')->nullable();
            $table->json('photos')->nullable(); // Inspection photos
            $table->json('corrective_actions')->nullable(); // Required actions
            $table->timestamp('follow_up_date')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['kitchen_id', 'scheduled_at']);
            $table->index(['inspector_id']);
            $table->index(['status', 'type']);
            
            // Foreign keys
            $table->foreign('kitchen_id')->references('id')->on('admin_cloud_kitchens')->onDelete('cascade');
            $table->foreign('inspector_id')->references('id')->on('admin_users')->onDelete('cascade');
        });

        // Create admin_kitchen_performance table
        Schema::create('admin_kitchen_performance', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('kitchen_id');
            $table->date('date');
            $table->integer('total_orders')->default(0);
            $table->decimal('total_revenue', 12, 2)->default(0);
            $table->decimal('average_order_value', 10, 2)->default(0);
            $table->decimal('customer_rating', 3, 2)->default(0);
            $table->integer('total_reviews')->default(0);
            $table->decimal('preparation_time_avg', 8, 2)->default(0); // Minutes
            $table->decimal('delivery_time_avg', 8, 2)->default(0); // Minutes
            $table->integer('cancelled_orders')->default(0);
            $table->integer('refunded_orders')->default(0);
            $table->decimal('cancellation_rate', 5, 2)->default(0); // Percentage
            $table->decimal('on_time_delivery_rate', 5, 2)->default(0); // Percentage
            $table->integer('complaints')->default(0);
            $table->integer('compliments')->default(0);
            $table->json('popular_items')->nullable(); // Top selling items
            $table->json('peak_hours')->nullable(); // Busiest hours
            $table->timestamps();

            // Indexes
            $table->index(['kitchen_id', 'date']);
            $table->index(['date']);
            $table->unique(['kitchen_id', 'date']);
            
            // Foreign key
            $table->foreign('kitchen_id')->references('id')->on('admin_cloud_kitchens')->onDelete('cascade');
        });

        // Create admin_kitchen_virtual_brands table
        Schema::create('admin_kitchen_virtual_brands', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('kitchen_id');
            $table->string('brand_name');
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->json('cuisine_types');
            $table->enum('status', ['active', 'inactive', 'pending_approval'])->default('pending_approval');
            $table->json('menu_categories')->nullable();
            $table->integer('total_items')->default(0);
            $table->decimal('average_price', 8, 2)->default(0);
            $table->json('target_audience')->nullable();
            $table->json('marketing_strategy')->nullable();
            $table->decimal('commission_rate', 5, 2)->nullable(); // Override kitchen rate
            $table->timestamp('launched_at')->nullable();
            $table->uuid('approved_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['kitchen_id', 'status']);
            $table->index(['brand_name']);
            $table->index(['status']);
            
            // Foreign keys
            $table->foreign('kitchen_id')->references('id')->on('admin_cloud_kitchens')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_kitchen_compliance table
        Schema::create('admin_kitchen_compliance', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('kitchen_id');
            $table->string('requirement_type'); // license, permit, certification
            $table->string('requirement_name');
            $table->string('document_number')->nullable();
            $table->enum('status', ['valid', 'expired', 'expiring_soon', 'missing'])->default('missing');
            $table->timestamp('issued_date')->nullable();
            $table->timestamp('expiry_date')->nullable();
            $table->string('issuing_authority')->nullable();
            $table->string('document_path')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('last_verified')->nullable();
            $table->uuid('verified_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['kitchen_id', 'requirement_type']);
            $table->index(['status', 'expiry_date']);
            $table->index(['expiry_date']);
            
            // Foreign keys
            $table->foreign('kitchen_id')->references('id')->on('admin_cloud_kitchens')->onDelete('cascade');
            $table->foreign('verified_by')->references('id')->on('admin_users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_kitchen_compliance');
        Schema::dropIfExists('admin_kitchen_virtual_brands');
        Schema::dropIfExists('admin_kitchen_performance');
        Schema::dropIfExists('admin_kitchen_inspections');
        Schema::dropIfExists('admin_kitchen_approvals');
        Schema::dropIfExists('admin_cloud_kitchens');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create admin_dashboards table
        Schema::create('admin_dashboards', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('category', ['executive', 'operational', 'financial', 'marketing', 'custom'])->default('custom');
            $table->json('layout'); // Dashboard layout configuration
            $table->json('widgets'); // Widget configurations
            $table->json('filters')->nullable(); // Default filters
            $table->integer('refresh_interval')->default(30); // Seconds
            $table->boolean('is_public')->default(false);
            $table->json('permissions')->nullable(); // Who can access this dashboard
            $table->uuid('created_by');
            $table->uuid('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['category', 'is_public']);
            $table->index(['created_by']);
            $table->index(['created_at']);
        });

        // Create admin_dashboard_widgets table
        Schema::create('admin_dashboard_widgets', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('dashboard_id');
            $table->enum('type', ['metric', 'chart', 'table', 'text', 'image', 'iframe']);
            $table->string('title');
            $table->json('position'); // x, y coordinates
            $table->json('size'); // width, height
            $table->string('data_source');
            $table->json('options'); // Widget-specific options
            $table->json('filters')->nullable();
            $table->boolean('is_visible')->default(true);
            $table->integer('order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['dashboard_id', 'order']);
            $table->index(['type']);
            
            // Foreign key
            $table->foreign('dashboard_id')->references('id')->on('admin_dashboards')->onDelete('cascade');
        });

        // Create admin_reports table
        Schema::create('admin_reports', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('template_id')->nullable();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('parameters')->nullable(); // Report parameters
            $table->json('data')->nullable(); // Generated report data
            $table->json('metadata')->nullable(); // Execution metadata
            $table->enum('status', ['pending', 'generating', 'completed', 'failed', 'expired'])->default('pending');
            $table->timestamp('generated_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->uuid('generated_by');
            $table->string('download_url')->nullable();
            $table->string('share_url')->nullable();
            $table->boolean('is_shared')->default(false);
            $table->timestamps();

            // Indexes
            $table->index(['status', 'generated_at']);
            $table->index(['generated_by']);
            $table->index(['template_id']);
            $table->index(['expires_at']);
            
            // Foreign key
            $table->foreign('template_id')->references('id')->on('admin_report_templates')->onDelete('set null');
            $table->foreign('generated_by')->references('id')->on('admin_users')->onDelete('cascade');
        });

        // Create admin_report_templates table
        Schema::create('admin_report_templates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('category', ['sales', 'customers', 'operations', 'financial', 'marketing', 'custom'])->default('custom');
            $table->enum('type', ['standard', 'custom', 'scheduled'])->default('standard');
            $table->json('parameters')->nullable(); // Template parameters
            $table->enum('visualization', ['table', 'chart', 'mixed'])->default('table');
            $table->string('data_source');
            $table->text('query')->nullable(); // SQL query or data source query
            $table->json('filters')->nullable();
            $table->json('group_by')->nullable();
            $table->json('sort_by')->nullable();
            $table->boolean('is_public')->default(false);
            $table->json('tags')->nullable();
            $table->uuid('created_by');
            $table->uuid('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['category', 'type']);
            $table->index(['is_public']);
            $table->index(['created_by']);
        });

        // Create admin_report_schedules table
        Schema::create('admin_report_schedules', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('report_id');
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('frequency', ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'])->default('daily');
            $table->string('timezone')->default('UTC');
            $table->json('parameters')->nullable();
            $table->json('recipients'); // Email, webhook, etc.
            $table->enum('format', ['pdf', 'excel', 'csv'])->default('pdf');
            $table->json('delivery'); // Delivery configuration
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_run')->nullable();
            $table->timestamp('next_run');
            $table->uuid('created_by');
            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'next_run']);
            $table->index(['frequency']);
            $table->index(['created_by']);
            
            // Foreign key
            $table->foreign('report_id')->references('id')->on('admin_report_templates')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('admin_users')->onDelete('cascade');
        });

        // Create admin_kpi_metrics table
        Schema::create('admin_kpi_metrics', function (Blueprint $table) {
            $table->string('id')->primary(); // e.g., 'total_revenue', 'total_orders'
            $table->string('name');
            $table->string('category'); // revenue, orders, customers, etc.
            $table->decimal('value', 15, 2);
            $table->decimal('target', 15, 2)->nullable();
            $table->string('unit')->default('number'); // currency, percentage, count
            $table->string('format')->default('number'); // number, currency, percentage
            $table->json('trend')->nullable(); // Trend data
            $table->enum('status', ['good', 'warning', 'critical'])->default('good');
            $table->text('description')->nullable();
            $table->string('calculation')->nullable(); // How the metric is calculated
            $table->string('data_source');
            $table->timestamp('last_updated');
            $table->timestamps();

            // Indexes
            $table->index(['category']);
            $table->index(['status']);
            $table->index(['last_updated']);
        });

        // Create admin_anomalies table
        Schema::create('admin_anomalies', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('metric');
            $table->timestamp('timestamp');
            $table->decimal('value', 15, 2);
            $table->decimal('expected', 15, 2);
            $table->decimal('deviation', 15, 2);
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('type', ['spike', 'drop', 'trend', 'pattern'])->default('spike');
            $table->integer('confidence'); // 0-100
            $table->text('description');
            $table->json('possible_causes')->nullable();
            $table->json('recommendations')->nullable();
            $table->boolean('acknowledged')->default(false);
            $table->uuid('acknowledged_by')->nullable();
            $table->timestamp('acknowledged_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['metric', 'timestamp']);
            $table->index(['severity', 'acknowledged']);
            $table->index(['acknowledged']);
            
            // Foreign key
            $table->foreign('acknowledged_by')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_recommendations table
        Schema::create('admin_recommendations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->enum('category', ['revenue', 'cost', 'efficiency', 'quality', 'growth', 'risk'])->default('efficiency');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->string('title');
            $table->text('description');
            $table->json('impact')->nullable(); // Expected impact metrics
            $table->json('effort')->nullable(); // Required effort/resources
            $table->string('timeline')->nullable();
            $table->json('dependencies')->nullable();
            $table->json('metrics')->nullable(); // Related metrics
            $table->json('actions')->nullable(); // Action items
            $table->enum('status', ['new', 'reviewing', 'approved', 'implementing', 'completed', 'rejected'])->default('new');
            $table->uuid('assigned_to')->nullable();
            $table->timestamp('due_date')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['category', 'priority']);
            $table->index(['status']);
            $table->index(['assigned_to']);
            
            // Foreign key
            $table->foreign('assigned_to')->references('id')->on('admin_users')->onDelete('set null');
        });

        // Create admin_insights table
        Schema::create('admin_insights', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->enum('type', ['opportunity', 'risk', 'trend', 'anomaly', 'recommendation'])->default('opportunity');
            $table->string('category');
            $table->string('title');
            $table->text('description');
            $table->integer('confidence'); // 0-100
            $table->enum('impact', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('urgency', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->json('evidence')->nullable(); // Supporting evidence
            $table->json('recommendations')->nullable();
            $table->json('metrics')->nullable(); // Related metrics
            $table->json('tags')->nullable();
            $table->boolean('acknowledged')->default(false);
            $table->uuid('acknowledged_by')->nullable();
            $table->timestamp('acknowledged_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['type', 'category']);
            $table->index(['impact', 'urgency']);
            $table->index(['acknowledged']);
            
            // Foreign key
            $table->foreign('acknowledged_by')->references('id')->on('admin_users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_insights');
        Schema::dropIfExists('admin_recommendations');
        Schema::dropIfExists('admin_anomalies');
        Schema::dropIfExists('admin_kpi_metrics');
        Schema::dropIfExists('admin_report_schedules');
        Schema::dropIfExists('admin_report_templates');
        Schema::dropIfExists('admin_reports');
        Schema::dropIfExists('admin_dashboard_widgets');
        Schema::dropIfExists('admin_dashboards');
    }
};

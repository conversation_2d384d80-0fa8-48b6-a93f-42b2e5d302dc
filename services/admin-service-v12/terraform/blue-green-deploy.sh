#!/bin/bash

# Blue-Green Deployment Script for ECS

set -e

# Configuration
CLUSTER_NAME="admin-service-cluster"
BLUE_SERVICE="admin-service-blue"
GREEN_SERVICE="admin-service-green"
LISTENER_ARN=$(aws elbv2 describe-listeners --load-balancer-arn $(aws elbv2 describe-load-balancers --names admin-service-lb --query 'LoadBalancers[0].LoadBalancerArn' --output text) --query 'Listeners[0].ListenerArn' --output text)
BLUE_TG_ARN=$(aws elbv2 describe-target-groups --names admin-service-blue-tg --query 'TargetGroups[0].TargetGroupArn' --output text)
GREEN_TG_ARN=$(aws elbv2 describe-target-groups --names admin-service-green-tg --query 'TargetGroups[0].TargetGroupArn' --output text)

# Determine which environment is currently active
CURRENT_TG_ARN=$(aws elbv2 describe-listeners --listener-arn $LISTENER_ARN --query 'Listeners[0].DefaultActions[0].TargetGroupArn' --output text)

if [ "$CURRENT_TG_ARN" == "$BLUE_TG_ARN" ]; then
    echo "Blue environment is currently active. Deploying to Green..."
    ACTIVE_SERVICE=$BLUE_SERVICE
    INACTIVE_SERVICE=$GREEN_SERVICE
    TARGET_TG_ARN=$GREEN_TG_ARN
else
    echo "Green environment is currently active. Deploying to Blue..."
    ACTIVE_SERVICE=$GREEN_SERVICE
    INACTIVE_SERVICE=$BLUE_SERVICE
    TARGET_TG_ARN=$BLUE_TG_ARN
fi

# Update the inactive service to use the new task definition
TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition admin-service --query 'taskDefinition.taskDefinitionArn' --output text)
echo "Updating $INACTIVE_SERVICE to use task definition: $TASK_DEFINITION"
aws ecs update-service --cluster $CLUSTER_NAME --service $INACTIVE_SERVICE --task-definition $TASK_DEFINITION --desired-count 2

# Wait for the service to stabilize
echo "Waiting for $INACTIVE_SERVICE to stabilize..."
aws ecs wait services-stable --cluster $CLUSTER_NAME --services $INACTIVE_SERVICE

# Check if the new deployment is healthy
echo "Checking health of target group $TARGET_TG_ARN..."
HEALTHY_COUNT=$(aws elbv2 describe-target-health --target-group-arn $TARGET_TG_ARN --query 'length(TargetHealthDescriptions[?TargetHealth.State==`healthy`])' --output text)

if [ "$HEALTHY_COUNT" -lt 2 ]; then
    echo "Error: Not enough healthy targets in the new deployment. Rolling back..."
    aws ecs update-service --cluster $CLUSTER_NAME --service $INACTIVE_SERVICE --desired-count 0
    exit 1
fi

# Switch traffic to the new deployment
echo "Switching traffic to the new deployment..."
aws elbv2 modify-listener --listener-arn $LISTENER_ARN --default-actions Type=forward,TargetGroupArn=$TARGET_TG_ARN

# Scale down the old deployment
echo "Scaling down $ACTIVE_SERVICE..."
aws ecs update-service --cluster $CLUSTER_NAME --service $ACTIVE_SERVICE --desired-count 0

echo "Blue-Green deployment completed successfully!"

<?php

namespace App\Services;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DabbawalaService
{
    /**
     * Get customers with dabbawala information with pagination and filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getCustomersWithDabbawala(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Customer::query()
            ->select([
                'customers.*',
                'delivery_locations.location as location_name',
                'users.name as delivery_person_name',
            ])
            ->leftJoin('delivery_locations', 'customers.location_code', '=', 'delivery_locations.pk_location_code')
            ->leftJoin('users', 'customers.delivery_person_id', '=', 'users.id');

        // Apply filters
        $this->applyFilters($query, $filters);

        return $query->paginate($perPage);
    }

    /**
     * Get a specific customer with dabbawala information.
     *
     * @param int $id
     * @return Customer
     * @throws ModelNotFoundException
     */
    public function getCustomerWithDabbawala(int $id): Customer
    {
        return Customer::select([
                'customers.*',
                'delivery_locations.location as location_name',
                'users.name as delivery_person_name',
            ])
            ->leftJoin('delivery_locations', 'customers.location_code', '=', 'delivery_locations.pk_location_code')
            ->leftJoin('users', 'customers.delivery_person_id', '=', 'users.id')
            ->where('customers.pk_customer_code', $id)
            ->firstOrFail();
    }

    /**
     * Update a customer's dabbawala status and code.
     *
     * @param int $customerId
     * @param string|null $dabbawalaCode
     * @param string|null $dabbaStatus
     * @return Customer|null
     */
    public function updateDabbawalaStatus(int $customerId, ?string $dabbawalaCode, ?string $dabbaStatus): ?Customer
    {
        try {
            $customer = Customer::findOrFail($customerId);
            
            $updateData = [];
            
            if ($dabbawalaCode !== null) {
                $updateData['dabbawala_code'] = $dabbawalaCode;
                $updateData['dabbawala_code_type'] = 'mumbai';
            }
            
            if ($dabbaStatus !== null) {
                $updateData['dabba_status'] = $dabbaStatus;
            }
            
            if (!empty($updateData)) {
                $updateData['modified_on'] = now();
                $customer->update($updateData);
            }
            
            return $customer->fresh();
        } catch (\Exception $e) {
            Log::error('Failed to update dabbawala status', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }

    /**
     * Generate a new dabbawala code for a customer using the delivery service.
     *
     * @param int $customerId
     * @return string|null
     */
    public function generateDabbawalaCode(int $customerId): ?string
    {
        try {
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post(config('services.delivery.url') . '/api/v2/delivery/dabbawala/generate-code', [
                'customer_id' => $customerId,
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                $dabbawalaCode = $data['data']['dabbawala_code'] ?? null;
                
                if ($dabbawalaCode) {
                    // Update the customer with the new code
                    $this->updateDabbawalaStatus($customerId, $dabbawalaCode, null);
                    return $dabbawalaCode;
                }
            }
            
            Log::warning('Failed to generate dabbawala code from delivery service', [
                'customer_id' => $customerId,
                'response' => $response->json(),
            ]);
            
            return null;
        } catch (RequestException $e) {
            Log::error('Error calling delivery service to generate dabbawala code', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }

    /**
     * Apply filters to the customer query.
     *
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    private function applyFilters(Builder $query, array $filters): void
    {
        // Search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('customers.customer_name', 'like', "%{$search}%")
                  ->orWhere('customers.phone', 'like', "%{$search}%")
                  ->orWhere('customers.email_address', 'like', "%{$search}%")
                  ->orWhere('customers.dabbawala_code', 'like', "%{$search}%");
            });
        }
        
        // Location filter
        if (!empty($filters['location_id'])) {
            $query->where('customers.location_code', $filters['location_id']);
        }
        
        // Menu filter
        if (!empty($filters['menu'])) {
            $query->where('customers.menu_type', $filters['menu']);
        }
        
        // Delivery person filter
        if (!empty($filters['delivery_person_id'])) {
            $query->where('customers.delivery_person_id', $filters['delivery_person_id']);
        }
        
        // Dabba status filter
        if (!empty($filters['dabba_status'])) {
            $query->where('customers.dabba_status', $filters['dabba_status']);
        }
    }
}

<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Collection;

class ConfigService
{
    /**
     * Cache TTL in seconds (1 hour).
     *
     * @var int
     */
    protected int $cacheTtl = 3600;

    /**
     * Get a configuration value.
     *
     * @param string $key
     * @param mixed $default
     * @param int|null $companyId
     * @param int|null $unitId
     * @return mixed
     */
    public function getConfig(string $key, mixed $default = null, ?int $companyId = null, ?int $unitId = null): mixed
    {
        $companyId = $companyId ?? 1;
        $unitId = $unitId ?? 1;

        // Try to get from cache first
        $cacheKey = "config:{$companyId}:{$unitId}:{$key}";
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Try to get from database
        $setting = Setting::where('setting_key', $key)
            ->where('company_id', $companyId)
            ->where('unit_id', $unitId)
            ->first();

        if ($setting) {
            // Cache the result
            Cache::put($cacheKey, $setting->typed_value, $this->cacheTtl);
            return $setting->typed_value;
        }

        // Try to get from Laravel config
        if (Config::has($key)) {
            return Config::get($key);
        }

        // Check if the key exists with GLOBAL_ prefix
        $globalKey = 'GLOBAL_' . strtoupper($key);
        $globalSetting = Setting::where('setting_key', $globalKey)
            ->where('company_id', $companyId)
            ->where('unit_id', $unitId)
            ->first();

        if ($globalSetting) {
            // Cache the result
            Cache::put($cacheKey, $globalSetting->typed_value, $this->cacheTtl);
            return $globalSetting->typed_value;
        }

        // Return default value
        return $default;
    }

    /**
     * Set a configuration value.
     *
     * @param string $key
     * @param mixed $value
     * @param string|null $group
     * @param string $type
     * @param bool $isSystem
     * @param bool $isPublic
     * @param string|null $description
     * @param int|null $companyId
     * @param int|null $unitId
     * @return bool
     */
    public function setConfig(
        string $key,
        mixed $value,
        ?string $group = null,
        string $type = 'string',
        bool $isSystem = false,
        bool $isPublic = false,
        ?string $description = null,
        ?int $companyId = null,
        ?int $unitId = null
    ): bool {
        $companyId = $companyId ?? 1;
        $unitId = $unitId ?? 1;

        // Find or create the setting
        $setting = Setting::firstOrNew([
            'setting_key' => $key,
            'company_id' => $companyId,
            'unit_id' => $unitId,
        ]);

        // Update the setting
        $setting->setting_type = $type;
        $setting->typed_value = $value;
        $setting->setting_group = $group;
        $setting->is_system = $isSystem;
        $setting->is_public = $isPublic;
        $setting->description = $description;

        // Save the setting
        $result = $setting->save();

        // Update cache
        if ($result) {
            $cacheKey = "config:{$companyId}:{$unitId}:{$key}";
            Cache::put($cacheKey, $setting->typed_value, $this->cacheTtl);
        }

        return $result;
    }

    /**
     * Delete a configuration value.
     *
     * @param string $key
     * @param int|null $companyId
     * @param int|null $unitId
     * @return bool
     */
    public function deleteConfig(string $key, ?int $companyId = null, ?int $unitId = null): bool
    {
        $companyId = $companyId ?? 1;
        $unitId = $unitId ?? 1;

        // Delete the setting
        $result = Setting::where('setting_key', $key)
            ->where('company_id', $companyId)
            ->where('unit_id', $unitId)
            ->delete();

        // Clear cache
        if ($result) {
            $cacheKey = "config:{$companyId}:{$unitId}:{$key}";
            Cache::forget($cacheKey);
        }

        return (bool) $result;
    }

    /**
     * Get all configuration values.
     *
     * @param int|null $companyId
     * @param int|null $unitId
     * @return Collection
     */
    public function getAllConfig(?int $companyId = null, ?int $unitId = null): Collection
    {
        $companyId = $companyId ?? 1;
        $unitId = $unitId ?? 1;

        // Get all settings from database
        $settings = Setting::where('company_id', $companyId)
            ->where('unit_id', $unitId)
            ->get();

        // Transform to key-value pairs
        $config = collect();
        foreach ($settings as $setting) {
            $config[$setting->setting_key] = $setting->typed_value;
        }

        return $config;
    }

    /**
     * Get settings by group.
     *
     * @param string $group
     * @param int|null $companyId
     * @param int|null $unitId
     * @return Collection
     */
    public function getSettingsByGroup(string $group, ?int $companyId = null, ?int $unitId = null): Collection
    {
        $companyId = $companyId ?? 1;
        $unitId = $unitId ?? 1;

        // Get settings by group
        $settings = Setting::where('setting_group', $group)
            ->where('company_id', $companyId)
            ->where('unit_id', $unitId)
            ->get();

        // Transform to key-value pairs
        $config = collect();
        foreach ($settings as $setting) {
            $config[$setting->setting_key] = $setting->typed_value;
        }

        return $config;
    }

    /**
     * Check if a configuration value exists.
     *
     * @param string $key
     * @param int|null $companyId
     * @param int|null $unitId
     * @return bool
     */
    public function hasConfig(string $key, ?int $companyId = null, ?int $unitId = null): bool
    {
        $companyId = $companyId ?? 1;
        $unitId = $unitId ?? 1;

        // Check cache
        $cacheKey = "config:{$companyId}:{$unitId}:{$key}";
        if (Cache::has($cacheKey)) {
            return true;
        }

        // Check database
        $exists = Setting::where('setting_key', $key)
            ->where('company_id', $companyId)
            ->where('unit_id', $unitId)
            ->exists();

        if ($exists) {
            return true;
        }

        // Check Laravel config
        if (Config::has($key)) {
            return true;
        }

        // Check with GLOBAL_ prefix
        $globalKey = 'GLOBAL_' . strtoupper($key);
        return Setting::where('setting_key', $globalKey)
            ->where('company_id', $companyId)
            ->where('unit_id', $unitId)
            ->exists();
    }
}

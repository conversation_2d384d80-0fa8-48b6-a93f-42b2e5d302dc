<?php

namespace App\Services;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class RoleService
{
    /**
     * Get all roles.
     *
     * @param int|null $companyId
     * @param int|null $unitId
     * @return Collection
     */
    public function getAllRoles(?int $companyId = null, ?int $unitId = null): Collection
    {
        $query = Role::query();

        if ($companyId) {
            $query->forCompany($companyId);
        }

        if ($unitId) {
            $query->forUnit($unitId);
        }

        return $query->with('permissions')->get();
    }

    /**
     * Get a role by ID.
     *
     * @param int $id
     * @return Role
     * @throws ModelNotFoundException
     */
    public function getRoleById(int $id): Role
    {
        return Role::with('permissions')->findOrFail($id);
    }

    /**
     * Get a role by name.
     *
     * @param string $name
     * @param int|null $companyId
     * @param int|null $unitId
     * @return Role|null
     */
    public function getRoleByName(string $name, ?int $companyId = null, ?int $unitId = null): ?Role
    {
        $query = Role::where('name', $name);

        if ($companyId) {
            $query->forCompany($companyId);
        }

        if ($unitId) {
            $query->forUnit($unitId);
        }

        return $query->with('permissions')->first();
    }

    /**
     * Create a new role.
     *
     * @param string $name
     * @param string|null $displayName
     * @param string|null $description
     * @param array $permissions
     * @param bool $isSystem
     * @param int|null $companyId
     * @param int|null $unitId
     * @return Role
     */
    public function createRole(
        string $name,
        ?string $displayName = null,
        ?string $description = null,
        array $permissions = [],
        bool $isSystem = false,
        ?int $companyId = null,
        ?int $unitId = null
    ): Role {
        $companyId = $companyId ?? 1;
        $unitId = $unitId ?? 1;

        // Create the role
        $role = Role::create([
            'name' => $name,
            'display_name' => $displayName,
            'description' => $description,
            'is_system' => $isSystem,
            'company_id' => $companyId,
            'unit_id' => $unitId,
        ]);

        // Assign permissions
        if (!empty($permissions)) {
            $role->assignPermission($permissions);
        }

        return $role->load('permissions');
    }

    /**
     * Update a role.
     *
     * @param int $id
     * @param array $data
     * @return Role
     * @throws ModelNotFoundException
     */
    public function updateRole(int $id, array $data): Role
    {
        $role = Role::findOrFail($id);

        // Update role attributes
        if (isset($data['name'])) {
            $role->name = $data['name'];
        }

        if (isset($data['display_name'])) {
            $role->display_name = $data['display_name'];
        }

        if (isset($data['description'])) {
            $role->description = $data['description'];
        }

        if (isset($data['is_system'])) {
            $role->is_system = (bool) $data['is_system'];
        }

        // Save the role
        $role->save();

        // Update permissions if provided
        if (isset($data['permissions'])) {
            $role->permissions()->sync($data['permissions']);
        }

        return $role->load('permissions');
    }

    /**
     * Delete a role.
     *
     * @param int $id
     * @return bool
     * @throws ModelNotFoundException
     */
    public function deleteRole(int $id): bool
    {
        $role = Role::findOrFail($id);

        // Don't delete system roles
        if ($role->is_system) {
            return false;
        }

        // Delete the role
        return (bool) $role->delete();
    }

    /**
     * Get all permissions.
     *
     * @return Collection
     */
    public function getAllPermissions(): Collection
    {
        return Permission::all();
    }

    /**
     * Get permissions by module.
     *
     * @param string $module
     * @return Collection
     */
    public function getPermissionsByModule(string $module): Collection
    {
        return Permission::forModule($module)->get();
    }

    /**
     * Create a new permission.
     *
     * @param string $name
     * @param string|null $displayName
     * @param string|null $description
     * @param string|null $module
     * @param bool $isSystem
     * @return Permission
     */
    public function createPermission(
        string $name,
        ?string $displayName = null,
        ?string $description = null,
        ?string $module = null,
        bool $isSystem = false
    ): Permission {
        return Permission::create([
            'name' => $name,
            'display_name' => $displayName,
            'description' => $description,
            'module' => $module,
            'is_system' => $isSystem,
        ]);
    }
}

<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerDabbawalaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_customer_code,
            'customer_name' => $this->customer_name,
            'phone' => $this->phone,
            'email' => $this->email_address,
            'location' => [
                'id' => $this->location_code,
                'name' => $this->location_name,
            ],
            'address' => $this->customer_Address,
            'food_preference' => $this->food_preference,
            'dabbawala' => [
                'code' => $this->dabbawala_code,
                'code_type' => $this->dabbawala_code_type,
                'image' => $this->dabbawala_image ? url($this->dabbawala_image) : null,
                'status' => $this->dabba_status,
                'last_updated' => $this->modified_on ? $this->modified_on->format('Y-m-d H:i:s') : null,
            ],
            'delivery_person' => $this->when($this->delivery_person_id, [
                'id' => $this->delivery_person_id,
                'name' => $this->delivery_person_name,
            ]),
            'menu_type' => $this->menu_type,
            'created_at' => $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : null,
            'updated_at' => $this->updated_at ? $this->updated_at->format('Y-m-d H:i:s') : null,
        ];
    }
}

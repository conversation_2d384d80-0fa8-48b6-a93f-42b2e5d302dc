<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateDabbawalaStatusRequest;
use App\Http\Resources\CustomerDabbawalaCollection;
use App\Http\Resources\CustomerDabbawalaResource;
use App\Services\DabbawalaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Http\Response;

/**
 * @group Dabbawala Management
 *
 * APIs for managing Mumbai Dabbawala delivery codes and statuses
 */
class TrackTiffinsController extends Controller
{
    /**
     * The dabbawala service instance.
     */
    protected DabbawalaService $dabbawalaService;

    /**
     * Create a new controller instance.
     */
    public function __construct(DabbawalaService $dabbawalaService)
    {
        $this->dabbawalaService = $dabbawalaService;
    }

    /**
     * List all customers with their dabbawala codes.
     *
     * @queryParam page integer Page number for pagination. Default: 1
     * @queryParam per_page integer Number of items per page. Default: 15
     * @queryParam search string Search term for filtering customers. Example: john
     * @queryParam location_id integer Filter by location ID. Example: 1
     * @queryParam menu string Filter by menu type (lunch, dinner). Example: lunch
     * @queryParam delivery_person_id integer Filter by delivery person ID. Example: 1
     * @queryParam dabba_status string Filter by dabba status (IN, OUT, MISSING, OTHER). Example: IN
     *
     * @param Request $request
     * @return CustomerDabbawalaCollection
     */
    public function index(Request $request): CustomerDabbawalaCollection
    {
        $filters = $request->only([
            'search',
            'location_id',
            'menu',
            'delivery_person_id',
            'dabba_status'
        ]);

        $perPage = $request->input('per_page', 15);
        
        $customers = $this->dabbawalaService->getCustomersWithDabbawala(
            $filters,
            $perPage
        );

        return new CustomerDabbawalaCollection($customers);
    }

    /**
     * Get a specific customer's dabbawala details.
     *
     * @urlParam id integer required The ID of the customer. Example: 1
     *
     * @param int $id
     * @return CustomerDabbawalaResource
     */
    public function show(int $id): CustomerDabbawalaResource
    {
        $customer = $this->dabbawalaService->getCustomerWithDabbawala($id);

        return new CustomerDabbawalaResource($customer);
    }

    /**
     * Update a customer's dabbawala code and status.
     *
     * @urlParam id integer required The ID of the customer. Example: 1
     * @bodyParam dabbawala_code string The dabbawala code to assign. Example: MD-MU-123-456
     * @bodyParam dabba_status string The dabba status (IN, OUT, MISSING, OTHER). Example: IN
     *
     * @param UpdateDabbawalaStatusRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(UpdateDabbawalaStatusRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        
        $result = $this->dabbawalaService->updateDabbawalaStatus(
            $id,
            $data['dabbawala_code'] ?? null,
            $data['dabba_status'] ?? null
        );

        if ($result) {
            return response()->json([
                'message' => 'Dabbawala status updated successfully',
                'data' => $result
            ]);
        }

        return response()->json([
            'message' => 'Failed to update dabbawala status'
        ], Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * Filter customers by various criteria.
     *
     * @queryParam location_id integer Filter by location ID. Example: 1
     * @queryParam menu string Filter by menu type (lunch, dinner). Example: lunch
     * @queryParam delivery_person_id integer Filter by delivery person ID. Example: 1
     * @queryParam dabba_status string Filter by dabba status (IN, OUT, MISSING, OTHER). Example: IN
     * @queryParam page integer Page number for pagination. Default: 1
     * @queryParam per_page integer Number of items per page. Default: 15
     *
     * @param Request $request
     * @return CustomerDabbawalaCollection
     */
    public function filter(Request $request): CustomerDabbawalaCollection
    {
        $filters = $request->only([
            'location_id',
            'menu',
            'delivery_person_id',
            'dabba_status'
        ]);

        $perPage = $request->input('per_page', 15);
        
        $customers = $this->dabbawalaService->getCustomersWithDabbawala(
            $filters,
            $perPage
        );

        return new CustomerDabbawalaCollection($customers);
    }

    /**
     * Generate a new dabbawala code for a customer.
     *
     * @urlParam id integer required The ID of the customer. Example: 1
     *
     * @param int $id
     * @return JsonResponse
     */
    public function generateCode(int $id): JsonResponse
    {
        $result = $this->dabbawalaService->generateDabbawalaCode($id);

        if ($result) {
            return response()->json([
                'message' => 'Dabbawala code generated successfully',
                'data' => [
                    'dabbawala_code' => $result
                ]
            ]);
        }

        return response()->json([
            'message' => 'Failed to generate dabbawala code'
        ], Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}

<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\ConfigService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SetupWizardController extends Controller
{
    /**
     * The config service instance.
     *
     * @var ConfigService
     */
    protected ConfigService $configService;

    /**
     * Create a new controller instance.
     *
     * @param ConfigService $configService
     * @return void
     */
    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * Get setup wizard status.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getStatus(Request $request): JsonResponse
    {
        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        $setupCompleted = $this->configService->getConfig('SETUP_WIZARD_COMPLETED', false, $companyId, $unitId);
        $currentStep = $this->configService->getConfig('SETUP_WIZARD_CURRENT_STEP', 1, $companyId, $unitId);

        return response()->json([
            'status' => 'success',
            'data' => [
                'completed' => (bool) $setupCompleted,
                'current_step' => (int) $currentStep,
            ],
        ]);
    }

    /**
     * Update setup wizard status.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'completed' => 'sometimes|boolean',
            'current_step' => 'sometimes|integer|min:1|max:5',
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        if ($request->has('completed')) {
            $this->configService->setConfig(
                'SETUP_WIZARD_COMPLETED',
                $request->input('completed'),
                'system',
                'boolean',
                true,
                false,
                'Setup wizard completion status',
                $companyId,
                $unitId
            );
        }

        if ($request->has('current_step')) {
            $this->configService->setConfig(
                'SETUP_WIZARD_CURRENT_STEP',
                $request->input('current_step'),
                'system',
                'integer',
                true,
                false,
                'Setup wizard current step',
                $companyId,
                $unitId
            );
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Setup wizard status updated successfully',
            'data' => [
                'completed' => (bool) $this->configService->getConfig('SETUP_WIZARD_COMPLETED', false, $companyId, $unitId),
                'current_step' => (int) $this->configService->getConfig('SETUP_WIZARD_CURRENT_STEP', 1, $companyId, $unitId),
            ],
        ]);
    }

    /**
     * Setup company profile.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function setupCompanyProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
            'postal_address' => 'required|string|max:255',
            'support_email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'sender_id' => 'required|string|max:11',
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        // Update company profile settings
        $this->configService->setConfig(
            'MERCHANT_COMPANY_NAME',
            $request->input('company_name'),
            'company',
            'string',
            false,
            true,
            'Company name',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'MERCHANT_POSTAL_ADDRESS',
            $request->input('postal_address'),
            'company',
            'string',
            false,
            true,
            'Company postal address',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'MERCHANT_SUPPORT_EMAIL',
            $request->input('support_email'),
            'company',
            'string',
            false,
            true,
            'Support email address',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'GLOBAL_WEBSITE_PHONE',
            $request->input('phone'),
            'company',
            'string',
            false,
            true,
            'Company phone number',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'MERCHANT_SENDER_ID',
            $request->input('sender_id'),
            'company',
            'string',
            false,
            false,
            'SMS sender ID',
            $companyId,
            $unitId
        );

        // Update setup wizard status
        $this->configService->setConfig(
            'SETUP_WIZARD_CURRENT_STEP',
            2,
            'system',
            'integer',
            true,
            false,
            'Setup wizard current step',
            $companyId,
            $unitId
        );

        return response()->json([
            'status' => 'success',
            'message' => 'Company profile setup completed successfully',
            'data' => [
                'current_step' => 2,
            ],
        ]);
    }

    /**
     * Setup system settings.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function setupSystemSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'locale' => 'required|string|max:10',
            'currency' => 'required|string|max:3',
            'currency_symbol' => 'required|string|max:5',
            'time_zone' => 'required|string|max:50',
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        // Update system settings
        $this->configService->setConfig(
            'GLOBAL_LOCALE',
            $request->input('locale'),
            'system',
            'string',
            true,
            false,
            'Application locale',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'GLOBAL_CURRENCY',
            $request->input('currency'),
            'system',
            'string',
            true,
            true,
            'Default currency',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'GLOBAL_CURRENCY_ENTITY',
            $request->input('currency_symbol'),
            'system',
            'string',
            true,
            true,
            'Currency symbol',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'TIME_ZONE',
            $request->input('time_zone'),
            'system',
            'string',
            true,
            false,
            'Application time zone',
            $companyId,
            $unitId
        );

        // Update setup wizard status
        $this->configService->setConfig(
            'SETUP_WIZARD_CURRENT_STEP',
            3,
            'system',
            'integer',
            true,
            false,
            'Setup wizard current step',
            $companyId,
            $unitId
        );

        return response()->json([
            'status' => 'success',
            'message' => 'System settings setup completed successfully',
            'data' => [
                'current_step' => 3,
            ],
        ]);
    }

    /**
     * Setup payment gateways and payment modes.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function setupPaymentGateways(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'gateways' => 'required|array|min:1',
            'gateways.*.id' => 'required|string',
            'gateways.*.name' => 'required|string|min:2',
            'gateways.*.provider' => 'required|string|in:stripe,paypal,razorpay,square,payu,instamojo,paytm,cashfree,phonepe',
            'gateways.*.enabled' => 'required|boolean',
            'gateways.*.sandbox_mode' => 'required|boolean',
            'gateways.*.credentials' => 'required|array',
            'gateways.*.settings' => 'required|array',
            'default_gateway' => 'sometimes|string',
            'fallback_gateway' => 'sometimes|string',
            'payment_mode' => 'required|string|in:wallet,direct',
            'wallet_settings' => 'sometimes|array',
            'wallet_settings.minimum_balance' => 'sometimes|numeric|min:0',
            'wallet_settings.auto_reload_enabled' => 'sometimes|boolean',
            'wallet_settings.auto_reload_amount' => 'sometimes|numeric|min:1',
            'wallet_settings.auto_reload_threshold' => 'sometimes|numeric|min:0',
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        // Store payment gateway configurations
        $gateways = $request->input('gateways');
        $this->configService->setConfig(
            'PAYMENT_GATEWAYS',
            json_encode($gateways),
            'payment',
            'json',
            false,
            true,
            'Payment gateway configurations',
            $companyId,
            $unitId
        );

        // Store default gateway
        if ($request->has('default_gateway')) {
            $this->configService->setConfig(
                'DEFAULT_PAYMENT_GATEWAY',
                $request->input('default_gateway'),
                'payment',
                'string',
                false,
                true,
                'Default payment gateway',
                $companyId,
                $unitId
            );
        }

        // Store fallback gateway
        if ($request->has('fallback_gateway')) {
            $this->configService->setConfig(
                'FALLBACK_PAYMENT_GATEWAY',
                $request->input('fallback_gateway'),
                'payment',
                'string',
                false,
                true,
                'Fallback payment gateway',
                $companyId,
                $unitId
            );
        }

        // Store payment mode
        $this->configService->setConfig(
            'DEFAULT_PAYMENT_MODE',
            $request->input('payment_mode'),
            'payment',
            'string',
            false,
            true,
            'Default customer payment mode',
            $companyId,
            $unitId
        );

        // Store wallet settings if wallet mode is selected
        if ($request->input('payment_mode') === 'wallet' && $request->has('wallet_settings')) {
            $this->configService->setConfig(
                'WALLET_SETTINGS',
                json_encode($request->input('wallet_settings')),
                'payment',
                'json',
                false,
                true,
                'Wallet payment settings',
                $companyId,
                $unitId
            );
        }

        // Update setup wizard status
        $this->configService->setConfig(
            'SETUP_WIZARD_CURRENT_STEP',
            4,
            'system',
            'integer',
            true,
            false,
            'Setup wizard current step',
            $companyId,
            $unitId
        );

        return response()->json([
            'status' => 'success',
            'message' => 'Payment gateways and payment mode setup completed successfully',
            'data' => [
                'current_step' => 4,
                'payment_mode' => $request->input('payment_mode'),
                'gateways_configured' => count($gateways),
            ],
        ]);
    }

    /**
     * Complete setup wizard.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function completeSetup(Request $request): JsonResponse
    {
        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        // Mark setup wizard as completed
        $this->configService->setConfig(
            'SETUP_WIZARD_COMPLETED',
            true,
            'system',
            'boolean',
            true,
            false,
            'Setup wizard completion status',
            $companyId,
            $unitId
        );

        $this->configService->setConfig(
            'SETUP_WIZARD_CURRENT_STEP',
            5,
            'system',
            'integer',
            true,
            false,
            'Setup wizard current step',
            $companyId,
            $unitId
        );

        return response()->json([
            'status' => 'success',
            'message' => 'Setup wizard completed successfully',
            'data' => [
                'completed' => true,
                'current_step' => 5,
            ],
        ]);
    }
}

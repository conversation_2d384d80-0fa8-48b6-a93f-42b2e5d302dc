<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\RoleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class RoleController extends Controller
{
    /**
     * The role service instance.
     *
     * @var RoleService
     */
    protected RoleService $roleService;

    /**
     * Create a new controller instance.
     *
     * @param RoleService $roleService
     * @return void
     */
    public function __construct(RoleService $roleService)
    {
        $this->roleService = $roleService;
    }

    /**
     * Get all roles.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $companyId = $request->input('company_id');
        $unitId = $request->input('unit_id');

        $roles = $this->roleService->getAllRoles($companyId, $unitId);

        return response()->json([
            'status' => 'success',
            'data' => $roles,
        ]);
    }

    /**
     * Get a specific role.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $role = $this->roleService->getRoleById($id);

            return response()->json([
                'status' => 'success',
                'data' => $role,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'status' => 'error',
                'message' => "Role with ID {$id} not found.",
            ], 404);
        }
    }

    /**
     * Create a new role.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:roles,name',
            'display_name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|nullable',
            'permissions' => 'sometimes|array',
            'permissions.*' => 'exists:permissions,id',
            'is_system' => 'sometimes|boolean',
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $role = $this->roleService->createRole(
            $request->input('name'),
            $request->input('display_name'),
            $request->input('description'),
            $request->input('permissions', []),
            $request->input('is_system', false),
            $request->input('company_id'),
            $request->input('unit_id')
        );

        return response()->json([
            'status' => 'success',
            'message' => 'Role created successfully',
            'data' => $role,
        ], 201);
    }

    /**
     * Update a role.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => "sometimes|string|max:255|unique:roles,name,{$id}",
            'display_name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|nullable',
            'permissions' => 'sometimes|array',
            'permissions.*' => 'exists:permissions,id',
            'is_system' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $role = $this->roleService->updateRole($id, $request->all());

            return response()->json([
                'status' => 'success',
                'message' => 'Role updated successfully',
                'data' => $role,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'status' => 'error',
                'message' => "Role with ID {$id} not found.",
            ], 404);
        }
    }

    /**
     * Delete a role.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $result = $this->roleService->deleteRole($id);

            if (!$result) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot delete system role.',
                ], 403);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Role deleted successfully',
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'status' => 'error',
                'message' => "Role with ID {$id} not found.",
            ], 404);
        }
    }

    /**
     * Get all permissions.
     *
     * @return JsonResponse
     */
    public function getAllPermissions(): JsonResponse
    {
        $permissions = $this->roleService->getAllPermissions();

        return response()->json([
            'status' => 'success',
            'data' => $permissions,
        ]);
    }

    /**
     * Get permissions by module.
     *
     * @param string $module
     * @return JsonResponse
     */
    public function getPermissionsByModule(string $module): JsonResponse
    {
        $permissions = $this->roleService->getPermissionsByModule($module);

        return response()->json([
            'status' => 'success',
            'data' => $permissions,
        ]);
    }
}

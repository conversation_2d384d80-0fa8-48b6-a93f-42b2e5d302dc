<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\ConfigService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ConfigController extends Controller
{
    /**
     * The config service instance.
     *
     * @var ConfigService
     */
    protected ConfigService $configService;

    /**
     * Create a new controller instance.
     *
     * @param ConfigService $configService
     * @return void
     */
    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * Get all configuration values.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        $config = $this->configService->getAllConfig($companyId, $unitId);

        return response()->json([
            'status' => 'success',
            'data' => $config,
        ]);
    }

    /**
     * Get a specific configuration value.
     *
     * @param Request $request
     * @param string $key
     * @return JsonResponse
     */
    public function show(Request $request, string $key): JsonResponse
    {
        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        if (!$this->configService->hasConfig($key, $companyId, $unitId)) {
            return response()->json([
                'status' => 'error',
                'message' => "Configuration key '{$key}' not found.",
            ], 404);
        }

        $value = $this->configService->getConfig($key, null, $companyId, $unitId);

        return response()->json([
            'status' => 'success',
            'data' => [
                'key' => $key,
                'value' => $value,
            ],
        ]);
    }

    /**
     * Update a configuration value.
     *
     * @param Request $request
     * @param string $key
     * @return JsonResponse
     */
    public function update(Request $request, string $key): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'value' => 'required',
            'type' => 'sometimes|string|in:string,boolean,integer,float,json,array',
            'group' => 'sometimes|string|nullable',
            'is_system' => 'sometimes|boolean',
            'is_public' => 'sometimes|boolean',
            'description' => 'sometimes|string|nullable',
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $value = $request->input('value');
        $type = $request->input('type', 'string');
        $group = $request->input('group');
        $isSystem = $request->input('is_system', false);
        $isPublic = $request->input('is_public', false);
        $description = $request->input('description');
        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        $result = $this->configService->setConfig(
            $key,
            $value,
            $group,
            $type,
            $isSystem,
            $isPublic,
            $description,
            $companyId,
            $unitId
        );

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => "Failed to update configuration key '{$key}'.",
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => "Configuration key '{$key}' updated successfully.",
            'data' => [
                'key' => $key,
                'value' => $this->configService->getConfig($key, null, $companyId, $unitId),
            ],
        ]);
    }

    /**
     * Delete a configuration value.
     *
     * @param Request $request
     * @param string $key
     * @return JsonResponse
     */
    public function destroy(Request $request, string $key): JsonResponse
    {
        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        if (!$this->configService->hasConfig($key, $companyId, $unitId)) {
            return response()->json([
                'status' => 'error',
                'message' => "Configuration key '{$key}' not found.",
            ], 404);
        }

        $result = $this->configService->deleteConfig($key, $companyId, $unitId);

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => "Failed to delete configuration key '{$key}'.",
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => "Configuration key '{$key}' deleted successfully.",
        ]);
    }

    /**
     * Get settings by group.
     *
     * @param Request $request
     * @param string $group
     * @return JsonResponse
     */
    public function getSettingsByGroup(Request $request, string $group): JsonResponse
    {
        $companyId = $request->input('company_id', 1);
        $unitId = $request->input('unit_id', 1);

        $settings = $this->configService->getSettingsByGroup($group, $companyId, $unitId);

        return response()->json([
            'status' => 'success',
            'data' => $settings,
        ]);
    }
}

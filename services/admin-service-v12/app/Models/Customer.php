<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Customer extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_code';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_name',
        'phone',
        'email_address',
        'customer_Address',
        'location_code',
        'food_preference',
        'dabbawala_code',
        'dabbawala_code_type',
        'dabbawala_image',
        'dabba_status',
        'delivery_person_id',
        'menu_type',
        'modified_on',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'modified_on' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the location associated with the customer.
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(DeliveryLocation::class, 'location_code', 'pk_location_code');
    }

    /**
     * Get the delivery person associated with the customer.
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_person_id');
    }
}

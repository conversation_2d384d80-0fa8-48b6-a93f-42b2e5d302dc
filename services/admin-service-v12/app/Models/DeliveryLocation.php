<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryLocation extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'delivery_locations';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_location_code';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'location',
        'city',
        'sub_city_area',
        'pin',
        'delivery_charges',
        'delivery_time',
        'is_default',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'delivery_charges' => 'decimal:2',
        'is_default' => 'boolean',
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customers for the location.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'location_code', 'pk_location_code');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'setting_key',
        'setting_value',
        'setting_group',
        'setting_type',
        'is_system',
        'is_public',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_system' => 'boolean',
        'is_public' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the setting value with the appropriate type.
     *
     * @return mixed
     */
    public function getTypedValueAttribute(): mixed
    {
        return match ($this->setting_type) {
            'boolean' => (bool) $this->setting_value,
            'integer' => (int) $this->setting_value,
            'float' => (float) $this->setting_value,
            'json' => json_decode($this->setting_value, true),
            'array' => explode(',', $this->setting_value),
            default => $this->setting_value,
        };
    }

    /**
     * Set the setting value with the appropriate type.
     *
     * @param mixed $value
     * @return void
     */
    public function setTypedValueAttribute(mixed $value): void
    {
        $this->setting_value = match ($this->setting_type) {
            'boolean' => (bool) $value ? 'true' : 'false',
            'integer' => (string) (int) $value,
            'float' => (string) (float) $value,
            'json' => json_encode($value),
            'array' => is_array($value) ? implode(',', $value) : $value,
            default => (string) $value,
        };
    }

    /**
     * Scope a query to only include settings for a specific company.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $companyId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to only include settings for a specific unit.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUnit($query, int $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    /**
     * Scope a query to only include settings for a specific group.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInGroup($query, string $group)
    {
        return $query->where('setting_group', $group);
    }

    /**
     * Scope a query to only include public settings.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope a query to only include system settings.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }
}

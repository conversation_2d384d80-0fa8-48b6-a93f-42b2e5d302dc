<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_system',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_system' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The permissions that belong to the role.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'role_permission')
            ->withTimestamps();
    }

    /**
     * Determine if the role has the given permission.
     *
     * @param string|int $permission
     * @return bool
     */
    public function hasPermission(string|int $permission): bool
    {
        if (is_string($permission)) {
            return $this->permissions()->where('name', $permission)->exists();
        }

        return $this->permissions()->where('id', $permission)->exists();
    }

    /**
     * Assign the given permission to the role.
     *
     * @param string|int|array $permissions
     * @return $this
     */
    public function assignPermission(string|int|array $permissions): self
    {
        $permissionIds = [];

        if (is_array($permissions)) {
            foreach ($permissions as $permission) {
                if (is_string($permission)) {
                    $permissionModel = Permission::where('name', $permission)->first();
                    if ($permissionModel) {
                        $permissionIds[] = $permissionModel->id;
                    }
                } else {
                    $permissionIds[] = $permission;
                }
            }
        } else {
            if (is_string($permissions)) {
                $permissionModel = Permission::where('name', $permissions)->first();
                if ($permissionModel) {
                    $permissionIds[] = $permissionModel->id;
                }
            } else {
                $permissionIds[] = $permissions;
            }
        }

        $this->permissions()->syncWithoutDetaching($permissionIds);

        return $this;
    }

    /**
     * Revoke the given permission from the role.
     *
     * @param string|int|array $permissions
     * @return $this
     */
    public function revokePermission(string|int|array $permissions): self
    {
        $permissionIds = [];

        if (is_array($permissions)) {
            foreach ($permissions as $permission) {
                if (is_string($permission)) {
                    $permissionModel = Permission::where('name', $permission)->first();
                    if ($permissionModel) {
                        $permissionIds[] = $permissionModel->id;
                    }
                } else {
                    $permissionIds[] = $permission;
                }
            }
        } else {
            if (is_string($permissions)) {
                $permissionModel = Permission::where('name', $permissions)->first();
                if ($permissionModel) {
                    $permissionIds[] = $permissionModel->id;
                }
            } else {
                $permissionIds[] = $permissions;
            }
        }

        $this->permissions()->detach($permissionIds);

        return $this;
    }

    /**
     * Scope a query to only include roles for a specific company.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $companyId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to only include roles for a specific unit.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUnit($query, int $unitId)
    {
        return $query->where('unit_id', $unitId);
    }
}

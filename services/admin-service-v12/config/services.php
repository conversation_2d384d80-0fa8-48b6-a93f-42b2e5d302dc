<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    // Microservice URLs
    'auth' => [
        'url' => env('AUTH_SERVICE_URL', 'http://auth-service-v12:8000'),
        'timeout' => env('AUTH_SERVICE_TIMEOUT', 30),
        'retries' => env('AUTH_SERVICE_RETRIES', 3),
    ],
    
    'customer' => [
        'url' => env('CUSTOMER_SERVICE_URL', 'http://customer-service-v12:8000'),
        'timeout' => env('CUSTOMER_SERVICE_TIMEOUT', 30),
        'retries' => env('CUSTOMER_SERVICE_RETRIES', 3),
    ],
    
    'delivery' => [
        'url' => env('DELIVERY_SERVICE_URL', 'http://delivery-service-v12:8000'),
        'timeout' => env('DELIVERY_SERVICE_TIMEOUT', 30),
        'retries' => env('DELIVERY_SERVICE_RETRIES', 3),
    ],

];

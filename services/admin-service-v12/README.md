# Admin Service

The Admin Service is a Laravel 12 microservice for business configuration management. It provides APIs for managing configuration settings, roles, permissions, and setup wizard functionality.

## Features

- Configuration management
- Role and permission management
- Setup wizard for initial configuration
- Integration with Auth Service using Laravel Sanctum
- Comprehensive test coverage (>90%)
- OpenAPI 3.1 documentation
- Blue-green deployment with Terraform and AWS ECS
- Monitoring with Prometheus and Grafana

## Requirements

- PHP 8.2 or higher
- Laravel 12.x
- MySQL 8.0 or higher
- Composer

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd services/admin-service-v12
```

2. Install dependencies:

```bash
composer install
```

3. Copy the environment file:

```bash
cp .env.example .env
```

4. Configure the environment variables in the `.env` file:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=admin_service
DB_USERNAME=root
DB_PASSWORD=

AUTH_SERVICE_URL=http://auth-service-v12:8000/api
CUSTOMER_SERVICE_URL=http://customer-service-v12:8000/api
ORDER_SERVICE_URL=http://order-service-v12:8000/api
PAYMENT_SERVICE_URL=http://payment-service-v12:8000/api
```

5. Generate application key:

```bash
php artisan key:generate
```

6. Run migrations and seeders:

```bash
php artisan migrate --seed
```

## API Documentation

The API is documented using OpenAPI 3.1. You can find the documentation in the `openapi.yaml` file.

To view the API documentation, you can use Swagger UI:

```bash
php artisan l5-swagger:generate
```

Then visit `/api/documentation` in your browser.

## Running Tests

To run the tests, use the following command:

```bash
php artisan test
```

To generate a test coverage report, use the following command:

```bash
php artisan test --coverage
```

## Docker

To run the application using Docker, use the following commands:

```bash
docker build -t admin-service:latest .
docker run -p 8000:8000 admin-service:latest
```

## Deployment

The Admin Service is deployed using a blue-green deployment strategy with AWS ECS and Terraform.

### Prerequisites

- AWS CLI
- Terraform
- Docker

### Deployment Steps

1. Build the Docker image:
   ```
   docker build -t admin-service:latest .
   ```
2. Push the image to a container registry:
   ```
   docker tag admin-service:latest your-registry/admin-service:latest
   docker push your-registry/admin-service:latest
   ```
3. Deploy with Terraform:
   ```
   cd terraform
   terraform init
   terraform apply
   ```

## Kong API Gateway

The Admin Service is integrated with Kong API Gateway. The configuration can be found in the `kong.yaml` file.

To deploy the Kong API Gateway configuration, use the following command:

```bash
deck sync -s kong.yaml
```

## API Endpoints

### Configuration

- `GET /api/v2/admin/config` - Get all configuration settings
- `GET /api/v2/admin/config/{key}` - Get configuration setting by key
- `PUT /api/v2/admin/config/{key}` - Update configuration setting
- `DELETE /api/v2/admin/config/{key}` - Delete configuration setting
- `GET /api/v2/admin/config/group/{group}` - Get configuration settings by group

### Roles

- `GET /api/v2/admin/roles` - Get all roles
- `GET /api/v2/admin/roles/{id}` - Get role by ID
- `POST /api/v2/admin/roles` - Create a new role
- `PUT /api/v2/admin/roles/{id}` - Update an existing role
- `DELETE /api/v2/admin/roles/{id}` - Delete a role

### Permissions

- `GET /api/v2/admin/permissions` - Get all permissions
- `GET /api/v2/admin/permissions/module/{module}` - Get permissions by module

### Setup Wizard

- `GET /api/v2/admin/setup-wizard/status` - Get setup wizard status
- `PUT /api/v2/admin/setup-wizard/status` - Update setup wizard status
- `POST /api/v2/admin/setup-wizard/company-profile` - Setup company profile
- `POST /api/v2/admin/setup-wizard/system-settings` - Setup system settings
- `POST /api/v2/admin/setup-wizard/complete` - Complete setup wizard

## Architecture

The Admin Service follows a service-oriented architecture with the following components:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Contain business logic
- **Models**: Represent database entities
- **Middleware**: Handle authentication and authorization
- **Tests**: Ensure functionality works as expected

## Frontend

The Admin Service includes a Next.js-based frontend for managing business configuration. The frontend is built with:

- Next.js
- Tailwind CSS
- Shadcn UI components
- React Hook Form for form validation
- Axios for API communication

## Monitoring

The Admin Service includes monitoring with Prometheus and Grafana.

### Metrics

- Request rate
- Response time
- Error rate
- CPU and memory usage

### Alerts

- High request latency
- High error rate
- Service down
- High CPU usage
- High memory usage

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

{"openapi": "3.1.0", "info": {"title": "Admin Service API", "description": "API documentation for the Admin Service", "version": "1.0.0"}, "servers": [{"url": "/api"}], "paths": {"/v2/admin/track-tiffins": {"get": {"tags": ["Dabbawala Management"], "summary": "List all customers with their dabbawala codes", "description": "Returns a paginated list of customers with their dabbawala codes", "operationId": "listCustomersWithDabbawala", "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "default": 15}}, {"name": "search", "in": "query", "description": "Search term for filtering customers", "schema": {"type": "string"}}, {"name": "location_id", "in": "query", "description": "Filter by location ID", "schema": {"type": "integer"}}, {"name": "menu", "in": "query", "description": "Filter by menu type (lunch, dinner)", "schema": {"type": "string", "enum": ["lunch", "dinner"]}}, {"name": "delivery_person_id", "in": "query", "description": "Filter by delivery person ID", "schema": {"type": "integer"}}, {"name": "dabba_status", "in": "query", "description": "Filter by dabba status (IN, OUT, MISSING, OTHER)", "schema": {"type": "string", "enum": ["IN", "OUT", "MISSING", "OTHER"]}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDabbawalaCollection"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"sanctum": []}]}}, "/v2/admin/track-tiffins/filter": {"get": {"tags": ["Dabbawala Management"], "summary": "Filter customers by various criteria", "description": "Returns a filtered list of customers with their dabbawala codes", "operationId": "filterCustomersWithDabbawala", "parameters": [{"name": "location_id", "in": "query", "description": "Filter by location ID", "schema": {"type": "integer"}}, {"name": "menu", "in": "query", "description": "Filter by menu type (lunch, dinner)", "schema": {"type": "string", "enum": ["lunch", "dinner"]}}, {"name": "delivery_person_id", "in": "query", "description": "Filter by delivery person ID", "schema": {"type": "integer"}}, {"name": "dabba_status", "in": "query", "description": "Filter by dabba status (IN, OUT, MISSING, OTHER)", "schema": {"type": "string", "enum": ["IN", "OUT", "MISSING", "OTHER"]}}, {"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "default": 15}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDabbawalaCollection"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"sanctum": []}]}}, "/v2/admin/track-tiffins/{id}": {"get": {"tags": ["Dabbawala Management"], "summary": "Get a specific customer's dabbawala details", "description": "Returns detailed information about a customer's dabbawala code and status", "operationId": "getCustomerDabbawala", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDabbawala"}}}}, "404": {"description": "Customer not found"}, "401": {"description": "Unauthorized"}}, "security": [{"sanctum": []}]}}, "/v2/admin/track-tiffins/{id}/update-status": {"put": {"tags": ["Dabbawala Management"], "summary": "Update a customer's dabbawala code and status", "description": "Updates the dabbawala code and status for a specific customer", "operationId": "updateDabbawalaStatus", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"description": "Dabbawala status update information", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDabbawalaStatusRequest"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Dabbawala status updated successfully"}, "data": {"$ref": "#/components/schemas/CustomerDabbawala"}}}}}}, "404": {"description": "Customer not found"}, "422": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}, "security": [{"sanctum": []}]}}, "/v2/admin/track-tiffins/{id}/generate-code": {"post": {"tags": ["Dabbawala Management"], "summary": "Generate a new dabbawala code for a customer", "description": "Generates a new dabbawala code for a specific customer", "operationId": "generateDabbawalaCode", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Dabbawala code generated successfully"}, "data": {"type": "object", "properties": {"dabbawala_code": {"type": "string", "example": "MD-MU-123-456"}}}}}}}}, "404": {"description": "Customer not found"}, "500": {"description": "Server error"}, "401": {"description": "Unauthorized"}}, "security": [{"sanctum": []}]}}}, "components": {"schemas": {"CustomerDabbawala": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "customer_name": {"type": "string", "example": "<PERSON>"}, "phone": {"type": "string", "example": "+1234567890"}, "email": {"type": "string", "example": "<EMAIL>"}, "location": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Mumbai Central"}}}, "address": {"type": "string", "example": "123 Main St, Mumbai"}, "food_preference": {"type": "string", "example": "veg"}, "dabbawala": {"type": "object", "properties": {"code": {"type": "string", "example": "MD-MU-123-456"}, "code_type": {"type": "string", "example": "mumbai"}, "image": {"type": "string", "nullable": true, "example": "https://example.com/dabbawala-code.png"}, "status": {"type": "string", "example": "IN"}, "last_updated": {"type": "string", "example": "2023-06-15 14:30:00"}}}, "delivery_person": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Delivery Person Name"}}}, "menu_type": {"type": "string", "example": "lunch"}, "created_at": {"type": "string", "example": "2023-06-15 10:00:00"}, "updated_at": {"type": "string", "example": "2023-06-15 14:30:00"}}}, "CustomerDabbawalaCollection": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDabbawala"}}, "meta": {"type": "object", "properties": {"total": {"type": "integer", "example": 100}, "count": {"type": "integer", "example": 15}, "per_page": {"type": "integer", "example": 15}, "current_page": {"type": "integer", "example": 1}, "total_pages": {"type": "integer", "example": 7}}}, "links": {"type": "object", "properties": {"first": {"type": "string", "example": "https://example.com/api/v2/admin/track-tiffins?page=1"}, "last": {"type": "string", "example": "https://example.com/api/v2/admin/track-tiffins?page=7"}, "prev": {"type": "string", "nullable": true, "example": null}, "next": {"type": "string", "example": "https://example.com/api/v2/admin/track-tiffins?page=2"}}}}}, "UpdateDabbawalaStatusRequest": {"type": "object", "properties": {"dabbawala_code": {"type": "string", "example": "MD-MU-123-456"}, "dabba_status": {"type": "string", "enum": ["IN", "OUT", "MISSING", "OTHER"], "example": "IN"}}}}, "securitySchemes": {"sanctum": {"type": "http", "scheme": "bearer"}}}}
openapi: 3.0.3
info:
  title: Admin Service V12 - Kong-Compliant API
  description: |
    Administrative operations microservice API with comprehensive Kong API Gateway integration.
    
    This service handles administrative operations, setup wizard, system configuration,
    and management tasks through Kong API Gateway with JWT authentication and role-based access control.
    
    ## Kong Integration Features
    - **Service Name**: admin-service-v12
    - **Route Pattern**: /v2/admin-service-v12/*
    - **Authentication**: JWT with RS256 (24-hour tokens)
    - **Rate Limiting**: 120 requests/minute, 5000 requests/hour (higher for admin operations)
    - **CORS**: Enabled for admin dashboard applications
    - **ACL**: Restricted to admin and dashboard roles
    
    ## Service Capabilities
    - Setup wizard management (6-step configuration process)
    - System configuration and settings
    - Company profile management
    - Payment gateway configuration
    - Administrative dashboard operations
    - System health monitoring and diagnostics
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 Development Team
    email: <EMAIL>
    url: https://docs.onefooddialer.com/admin-service
  license:
    name: Proprietary
    url: https://onefooddialer.com/license

servers:
  - url: https://api.onefooddialer.com/v2/admin-service-v12
    description: Production Kong API Gateway
  - url: https://staging-api.onefooddialer.com/v2/admin-service-v12
    description: Staging Kong API Gateway
  - url: http://localhost:8000/v2/admin-service-v12
    description: Local development Kong Gateway

# Kong-specific service configuration
x-kong-service:
  name: admin-service-v12
  url: http://admin-service-v12:8000
  connect_timeout: 60000
  write_timeout: 60000
  read_timeout: 60000
  retries: 5
  protocol: http
  tags:
    - administration
    - microservice
    - v12

# Kong route configuration
x-kong-route:
  name: admin-service-route
  paths:
    - /v2/admin-service-v12
  preserve_host: true
  strip_path: false
  protocols:
    - http
    - https
  tags:
    - admin
    - protected

# Kong plugin configurations
x-kong-plugins:
  - name: jwt
    config:
      secret_is_base64: false
      claims_to_verify:
        - exp
        - nbf
        - iat
      key_claim_name: iss
      algorithm: RS256
      maximum_expiration: 86400  # 24 hours
      run_on_preflight: false
    enabled: true
    protocols:
      - http
      - https
  
  - name: rate-limiting
    config:
      minute: 120  # Higher limit for admin operations
      hour: 5000
      day: 50000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
      redis_host: redis
      redis_port: 6379
      redis_timeout: 2000
    enabled: true
    protocols:
      - http
      - https
  
  - name: cors
    config:
      origins:
        - "https://admin.onefooddialer.com"
        - "https://dashboard.onefooddialer.com"
        - "http://localhost:3001"
        - "http://localhost:3002"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        - X-Requested-With
        - X-Correlation-ID
        - X-Admin-Role
      exposed_headers:
        - X-Auth-Token
        - X-RateLimit-Limit
        - X-RateLimit-Remaining
        - X-RateLimit-Reset
        - X-Correlation-ID
        - X-Admin-Role
      credentials: true
      max_age: 3600
      preflight_continue: false
    enabled: true
    protocols:
      - http
      - https
  
  - name: request-transformer
    config:
      add:
        headers:
          - "X-Service: admin-service-v12"
          - "X-Gateway: kong"
          - "X-Admin-Context: true"
      append:
        headers:
          - "X-Correlation-ID: $(uuid)"
    enabled: true
  
  - name: response-transformer
    config:
      add:
        headers:
          - "X-Powered-By: OneFoodDialer-2025"
          - "X-API-Version: v2"
          - "X-Admin-Service: true"
      remove:
        headers:
          - "Server"
          - "X-Powered-By: PHP"
    enabled: true
  
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
    enabled: true
  
  - name: acl
    config:
      whitelist:
        - admin
        - dashboard
        - super-admin
    enabled: true
  
  - name: request-size-limiting
    config:
      allowed_payload_size: 10
      size_unit: megabytes
      require_content_length: false
    enabled: true

paths:
  # Health check endpoint
  /health:
    get:
      summary: Admin Service Health Check
      description: Check the health status of the admin service
      operationId: getAdminServiceHealth
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
          headers:
            X-Service-Name:
              $ref: '#/components/headers/X-Service-Name'
            X-Correlation-ID:
              $ref: '#/components/headers/X-Correlation-ID'
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      x-kong-plugin-rate-limiting:
        config:
          minute: 200  # Higher limit for health checks
          hour: 5000
      x-kong-plugin-cors:
        enabled: true

  # Setup Wizard endpoints
  /setup-wizard/status:
    get:
      summary: Get setup wizard status
      description: Retrieve the current status and progress of the setup wizard
      operationId: getSetupWizardStatus
      tags:
        - Setup Wizard
      parameters:
        - name: company_id
          in: query
          description: Company ID to get status for
          schema:
            type: integer
            example: 1
        - name: unit_id
          in: query
          description: Unit ID to get status for
          schema:
            type: integer
            example: 1
      responses:
        '200':
          description: Setup wizard status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/SetupWizardStatus'
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
            X-Correlation-ID:
              $ref: '#/components/headers/X-Correlation-ID'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []
      x-kong-plugin-acl:
        config:
          whitelist:
            - admin
            - dashboard

  /setup-wizard/company-profile:
    post:
      summary: Setup company profile
      description: Configure company profile information in the setup wizard
      operationId: setupCompanyProfile
      tags:
        - Setup Wizard
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyProfileRequest'
      responses:
        '200':
          description: Company profile setup completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Company profile setup completed successfully
                  data:
                    type: object
                    properties:
                      current_step:
                        type: integer
                        example: 1
        '422':
          $ref: '#/components/responses/ValidationError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []
      x-kong-plugin-acl:
        config:
          whitelist:
            - admin
            - super-admin

  /setup-wizard/payment-gateways:
    post:
      summary: Setup payment gateways and payment modes
      description: Configure payment gateways and customer payment mode preferences
      operationId: setupPaymentGateways
      tags:
        - Setup Wizard
        - Payment Configuration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentGatewaySetupRequest'
      responses:
        '200':
          description: Payment gateways and payment mode setup completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Payment gateways and payment mode setup completed successfully
                  data:
                    type: object
                    properties:
                      current_step:
                        type: integer
                        example: 4
                      payment_mode:
                        type: string
                        enum: [wallet, direct]
                        example: wallet
                      gateways_configured:
                        type: integer
                        example: 3
        '422':
          $ref: '#/components/responses/ValidationError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []
      x-kong-plugin-acl:
        config:
          whitelist:
            - admin
            - super-admin
      x-kong-plugin-rate-limiting:
        config:
          minute: 30  # Lower limit for payment configuration
          hour: 500

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication using RS256 algorithm.

        **Admin Token Requirements:**
        - Algorithm: RS256
        - Expiration: 24 hours
        - Required Claims: iss, exp, nbf, iat, sub, aud, role
        - Required Roles: admin, dashboard, or super-admin

        **Example:**
        ```
        Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
        ```

  headers:
    X-Correlation-ID:
      description: Unique request correlation ID for tracing
      schema:
        type: string
        format: uuid
      example: "123e4567-e89b-12d3-a456-************"

    X-RateLimit-Limit:
      description: Rate limit threshold for the current window
      schema:
        type: integer
      example: 120

    X-RateLimit-Remaining:
      description: Number of requests remaining in current window
      schema:
        type: integer
      example: 95

    X-RateLimit-Reset:
      description: Unix timestamp when the rate limit window resets
      schema:
        type: integer
      example: 1643356800

    X-Service-Name:
      description: Name of the service that handled the request
      schema:
        type: string
      example: "admin-service-v12"

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  error_code:
                    example: UNAUTHORIZED
                  message:
                    example: Admin authentication required
      headers:
        WWW-Authenticate:
          schema:
            type: string
          description: Authentication scheme
          example: Bearer realm="OneFoodDialer Admin API"

    Forbidden:
      description: Access forbidden - insufficient admin privileges
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  error_code:
                    example: FORBIDDEN
                  message:
                    example: Admin access required

    ValidationError:
      description: Request validation failed
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: error
              message:
                type: string
                example: Validation failed
              errors:
                type: object
                additionalProperties:
                  type: array
                  items:
                    type: string
                example:
                  company_name: ["Company name is required"]
                  email: ["Email format is invalid"]

    RateLimitExceeded:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RateLimitResponse'
      headers:
        X-RateLimit-Limit:
          $ref: '#/components/headers/X-RateLimit-Limit'
        X-RateLimit-Remaining:
          $ref: '#/components/headers/X-RateLimit-Remaining'
        X-RateLimit-Reset:
          $ref: '#/components/headers/X-RateLimit-Reset'
        Retry-After:
          schema:
            type: integer
          description: Seconds to wait before retrying

  schemas:
    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          example: healthy
        timestamp:
          type: string
          format: date-time
          example: "2025-01-28T10:30:00Z"
        version:
          type: string
          example: "2.0.0"
        service:
          type: string
          example: "admin-service-v12"
        database:
          type: object
          properties:
            status:
              type: string
              enum: [connected, disconnected]
              example: connected
            response_time:
              type: number
              format: float
              description: Database response time in milliseconds
              example: 12.3
        dependencies:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "customer-service-v12"
              status:
                type: string
                enum: [healthy, unhealthy]
                example: healthy
              response_time:
                type: number
                format: float
                example: 18.7
        setup_wizard:
          type: object
          properties:
            status:
              type: string
              enum: [not_started, in_progress, completed]
              example: in_progress
            current_step:
              type: integer
              example: 3
            total_steps:
              type: integer
              example: 6

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum: [error]
          example: error
        message:
          type: string
          example: An error occurred
        error_code:
          type: string
          example: ADMIN_ERROR
        timestamp:
          type: string
          format: date-time
          example: "2025-01-28T10:30:00Z"
        correlation_id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        details:
          type: object
          additionalProperties: true

    RateLimitResponse:
      type: object
      properties:
        status:
          type: string
          enum: [error]
          example: error
        message:
          type: string
          example: Rate limit exceeded
        error_code:
          type: string
          example: RATE_LIMIT_EXCEEDED
        retry_after:
          type: integer
          description: Seconds to wait before retrying
          example: 60
        limit:
          type: integer
          description: Rate limit threshold
          example: 120
        remaining:
          type: integer
          description: Remaining requests in current window
          example: 0
        reset:
          type: integer
          description: Unix timestamp when limit resets
          example: 1643356800

<?php

namespace Tests\Unit\Services;

use App\Models\Setting;
use App\Services\ConfigService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class ConfigServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * The config service instance.
     *
     * @var ConfigService
     */
    protected ConfigService $configService;

    /**
     * Set up the test.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->configService = new ConfigService();
    }

    /**
     * Test getConfig method.
     *
     * @return void
     */
    public function testGetConfig(): void
    {
        // Create a test setting
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'TEST_KEY',
            'setting_value' => 'test_value',
            'setting_type' => 'string',
        ]);

        // Test getting the setting
        $value = $this->configService->getConfig('TEST_KEY');
        $this->assertEquals('test_value', $value);

        // Test getting a non-existent setting with default value
        $value = $this->configService->getConfig('NON_EXISTENT_KEY', 'default_value');
        $this->assertEquals('default_value', $value);

        // Test getting a setting with company_id and unit_id
        Setting::create([
            'company_id' => 2,
            'unit_id' => 1,
            'setting_key' => 'COMPANY_SPECIFIC_KEY',
            'setting_value' => 'company_specific_value',
            'setting_type' => 'string',
        ]);

        $value = $this->configService->getConfig('COMPANY_SPECIFIC_KEY', null, 2);
        $this->assertEquals('company_specific_value', $value);
    }

    /**
     * Test setConfig method.
     *
     * @return void
     */
    public function testSetConfig(): void
    {
        // Test creating a new setting
        $result = $this->configService->setConfig(
            'NEW_KEY',
            'new_value',
            'test',
            'string',
            false,
            true,
            'Test description'
        );

        $this->assertTrue($result);
        $this->assertDatabaseHas('settings', [
            'setting_key' => 'NEW_KEY',
            'setting_value' => 'new_value',
            'setting_group' => 'test',
            'setting_type' => 'string',
            'is_system' => 0,
            'is_public' => 1,
            'description' => 'Test description',
        ]);

        // Test updating an existing setting
        $result = $this->configService->setConfig(
            'NEW_KEY',
            'updated_value',
            'test',
            'string',
            true,
            false,
            'Updated description'
        );

        $this->assertTrue($result);
        $this->assertDatabaseHas('settings', [
            'setting_key' => 'NEW_KEY',
            'setting_value' => 'updated_value',
            'setting_group' => 'test',
            'setting_type' => 'string',
            'is_system' => 1,
            'is_public' => 0,
            'description' => 'Updated description',
        ]);
    }

    /**
     * Test deleteConfig method.
     *
     * @return void
     */
    public function testDeleteConfig(): void
    {
        // Create a test setting
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'DELETE_KEY',
            'setting_value' => 'delete_value',
            'setting_type' => 'string',
        ]);

        // Test deleting the setting
        $result = $this->configService->deleteConfig('DELETE_KEY');
        $this->assertTrue($result);
        $this->assertDatabaseMissing('settings', [
            'setting_key' => 'DELETE_KEY',
        ]);

        // Test deleting a non-existent setting
        $result = $this->configService->deleteConfig('NON_EXISTENT_KEY');
        $this->assertFalse($result);
    }

    /**
     * Test getAllConfig method.
     *
     * @return void
     */
    public function testGetAllConfig(): void
    {
        // Create test settings
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'KEY1',
            'setting_value' => 'value1',
            'setting_type' => 'string',
        ]);

        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'KEY2',
            'setting_value' => 'value2',
            'setting_type' => 'string',
        ]);

        Setting::create([
            'company_id' => 2,
            'unit_id' => 1,
            'setting_key' => 'KEY3',
            'setting_value' => 'value3',
            'setting_type' => 'string',
        ]);

        // Test getting all settings for company 1
        $config = $this->configService->getAllConfig(1);
        $this->assertEquals(2, $config->count());
        $this->assertEquals('value1', $config['KEY1']);
        $this->assertEquals('value2', $config['KEY2']);

        // Test getting all settings for company 2
        $config = $this->configService->getAllConfig(2);
        $this->assertEquals(1, $config->count());
        $this->assertEquals('value3', $config['KEY3']);
    }

    /**
     * Test getSettingsByGroup method.
     *
     * @return void
     */
    public function testGetSettingsByGroup(): void
    {
        // Create test settings
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'GROUP1_KEY1',
            'setting_value' => 'value1',
            'setting_type' => 'string',
            'setting_group' => 'group1',
        ]);

        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'GROUP1_KEY2',
            'setting_value' => 'value2',
            'setting_type' => 'string',
            'setting_group' => 'group1',
        ]);

        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'GROUP2_KEY1',
            'setting_value' => 'value3',
            'setting_type' => 'string',
            'setting_group' => 'group2',
        ]);

        // Test getting settings for group1
        $settings = $this->configService->getSettingsByGroup('group1');
        $this->assertEquals(2, $settings->count());
        $this->assertEquals('value1', $settings['GROUP1_KEY1']);
        $this->assertEquals('value2', $settings['GROUP1_KEY2']);

        // Test getting settings for group2
        $settings = $this->configService->getSettingsByGroup('group2');
        $this->assertEquals(1, $settings->count());
        $this->assertEquals('value3', $settings['GROUP2_KEY1']);
    }

    /**
     * Test hasConfig method.
     *
     * @return void
     */
    public function testHasConfig(): void
    {
        // Create a test setting
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'HAS_KEY',
            'setting_value' => 'has_value',
            'setting_type' => 'string',
        ]);

        // Test checking if a setting exists
        $exists = $this->configService->hasConfig('HAS_KEY');
        $this->assertTrue($exists);

        // Test checking if a non-existent setting exists
        $exists = $this->configService->hasConfig('NON_EXISTENT_KEY');
        $this->assertFalse($exists);
    }

    /**
     * Test caching of configuration values.
     *
     * @return void
     */
    public function testConfigCaching(): void
    {
        // Create a test setting
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'CACHE_KEY',
            'setting_value' => 'cache_value',
            'setting_type' => 'string',
        ]);

        // Clear the cache
        Cache::flush();

        // Get the setting (should be from database)
        $value = $this->configService->getConfig('CACHE_KEY');
        $this->assertEquals('cache_value', $value);

        // Update the setting in the database directly
        Setting::where('setting_key', 'CACHE_KEY')->update([
            'setting_value' => 'updated_value',
        ]);

        // Get the setting again (should be from cache)
        $value = $this->configService->getConfig('CACHE_KEY');
        $this->assertEquals('cache_value', $value);

        // Clear the cache
        Cache::flush();

        // Get the setting again (should be from database with updated value)
        $value = $this->configService->getConfig('CACHE_KEY');
        $this->assertEquals('updated_value', $value);
    }
}

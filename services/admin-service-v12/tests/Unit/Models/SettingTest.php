<?php

namespace Tests\Unit\Models;

use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SettingTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test setting creation.
     *
     * @return void
     */
    public function testSettingCreation(): void
    {
        $setting = Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'TEST_KEY',
            'setting_value' => 'test_value',
            'setting_group' => 'test',
            'setting_type' => 'string',
            'is_system' => false,
            'is_public' => true,
            'description' => 'Test setting',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'TEST_KEY',
            'setting_value' => 'test_value',
        ]);

        $this->assertEquals('test_value', $setting->typed_value);
    }

    /**
     * Test typed value accessor for different types.
     *
     * @return void
     */
    public function testTypedValueAccessor(): void
    {
        // String type
        $stringSetting = Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'STRING_KEY',
            'setting_value' => 'string_value',
            'setting_type' => 'string',
        ]);
        $this->assertEquals('string_value', $stringSetting->typed_value);

        // Boolean type
        $booleanSetting = Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'BOOLEAN_KEY',
            'setting_value' => 'true',
            'setting_type' => 'boolean',
        ]);
        $this->assertTrue($booleanSetting->typed_value);

        // Integer type
        $integerSetting = Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'INTEGER_KEY',
            'setting_value' => '123',
            'setting_type' => 'integer',
        ]);
        $this->assertEquals(123, $integerSetting->typed_value);

        // Float type
        $floatSetting = Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'FLOAT_KEY',
            'setting_value' => '123.45',
            'setting_type' => 'float',
        ]);
        $this->assertEquals(123.45, $floatSetting->typed_value);

        // JSON type
        $jsonSetting = Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'JSON_KEY',
            'setting_value' => '{"key":"value"}',
            'setting_type' => 'json',
        ]);
        $this->assertEquals(['key' => 'value'], $jsonSetting->typed_value);

        // Array type
        $arraySetting = Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'ARRAY_KEY',
            'setting_value' => 'value1,value2,value3',
            'setting_type' => 'array',
        ]);
        $this->assertEquals(['value1', 'value2', 'value3'], $arraySetting->typed_value);
    }

    /**
     * Test typed value mutator for different types.
     *
     * @return void
     */
    public function testTypedValueMutator(): void
    {
        // String type
        $stringSetting = new Setting();
        $stringSetting->setting_type = 'string';
        $stringSetting->typed_value = 'string_value';
        $this->assertEquals('string_value', $stringSetting->setting_value);

        // Boolean type
        $booleanSetting = new Setting();
        $booleanSetting->setting_type = 'boolean';
        $booleanSetting->typed_value = true;
        $this->assertEquals('true', $booleanSetting->setting_value);

        // Integer type
        $integerSetting = new Setting();
        $integerSetting->setting_type = 'integer';
        $integerSetting->typed_value = 123;
        $this->assertEquals('123', $integerSetting->setting_value);

        // Float type
        $floatSetting = new Setting();
        $floatSetting->setting_type = 'float';
        $floatSetting->typed_value = 123.45;
        $this->assertEquals('123.45', $floatSetting->setting_value);

        // JSON type
        $jsonSetting = new Setting();
        $jsonSetting->setting_type = 'json';
        $jsonSetting->typed_value = ['key' => 'value'];
        $this->assertEquals('{"key":"value"}', $jsonSetting->setting_value);

        // Array type
        $arraySetting = new Setting();
        $arraySetting->setting_type = 'array';
        $arraySetting->typed_value = ['value1', 'value2', 'value3'];
        $this->assertEquals('value1,value2,value3', $arraySetting->setting_value);
    }

    /**
     * Test scope methods.
     *
     * @return void
     */
    public function testScopeMethods(): void
    {
        // Create test settings
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'COMPANY1_UNIT1',
            'setting_value' => 'value1',
            'setting_group' => 'test',
            'is_system' => false,
            'is_public' => true,
        ]);

        Setting::create([
            'company_id' => 2,
            'unit_id' => 1,
            'setting_key' => 'COMPANY2_UNIT1',
            'setting_value' => 'value2',
            'setting_group' => 'test',
            'is_system' => false,
            'is_public' => true,
        ]);

        Setting::create([
            'company_id' => 1,
            'unit_id' => 2,
            'setting_key' => 'COMPANY1_UNIT2',
            'setting_value' => 'value3',
            'setting_group' => 'test',
            'is_system' => false,
            'is_public' => true,
        ]);

        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'SYSTEM_SETTING',
            'setting_value' => 'value4',
            'setting_group' => 'system',
            'is_system' => true,
            'is_public' => false,
        ]);

        // Test forCompany scope
        $company1Settings = Setting::forCompany(1)->get();
        $this->assertEquals(3, $company1Settings->count());

        // Test forUnit scope
        $unit1Settings = Setting::forUnit(1)->get();
        $this->assertEquals(3, $unit1Settings->count());

        // Test inGroup scope
        $testGroupSettings = Setting::inGroup('test')->get();
        $this->assertEquals(3, $testGroupSettings->count());

        // Test system scope
        $systemSettings = Setting::system()->get();
        $this->assertEquals(1, $systemSettings->count());
        $this->assertEquals('SYSTEM_SETTING', $systemSettings->first()->setting_key);

        // Test public scope
        $publicSettings = Setting::public()->get();
        $this->assertEquals(3, $publicSettings->count());
    }
}

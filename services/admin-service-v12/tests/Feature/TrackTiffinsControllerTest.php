<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\DeliveryLocation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class TrackTiffinsControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Create a user and authenticate
        $user = User::factory()->create([
            'role_id' => 1, // Admin role
        ]);

        Sanctum::actingAs($user);
    }

    /**
     * Test listing customers with dabbawala codes.
     */
    public function test_index_returns_paginated_customers(): void
    {
        // Create test data
        $location = DeliveryLocation::factory()->create();
        $customers = Customer::factory()->count(5)->create([
            'location_code' => $location->pk_location_code,
            'dabbawala_code' => 'MD-MU-123-456',
            'dabbawala_code_type' => 'mumbai',
            'dabba_status' => 'IN',
        ]);

        // Make the request
        $response = $this->getJson('/api/v2/admin/track-tiffins');

        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'customer_name',
                        'phone',
                        'email',
                        'location',
                        'address',
                        'food_preference',
                        'dabbawala',
                        'menu_type',
                    ],
                ],
                'meta' => [
                    'total',
                    'count',
                    'per_page',
                    'current_page',
                    'total_pages',
                ],
                'links' => [
                    'first',
                    'last',
                    'prev',
                    'next',
                ],
            ])
            ->assertJsonCount(5, 'data');
    }

    /**
     * Test filtering customers.
     */
    public function test_filter_returns_filtered_customers(): void
    {
        // Create test data
        $location = DeliveryLocation::factory()->create();
        
        // Create customers with different statuses
        Customer::factory()->count(3)->create([
            'location_code' => $location->pk_location_code,
            'dabbawala_code' => 'MD-MU-123-456',
            'dabbawala_code_type' => 'mumbai',
            'dabba_status' => 'IN',
            'menu_type' => 'lunch',
        ]);
        
        Customer::factory()->count(2)->create([
            'location_code' => $location->pk_location_code,
            'dabbawala_code' => 'MD-MU-789-012',
            'dabbawala_code_type' => 'mumbai',
            'dabba_status' => 'OUT',
            'menu_type' => 'dinner',
        ]);

        // Test filtering by dabba_status
        $response = $this->getJson('/api/v2/admin/track-tiffins/filter?dabba_status=IN');
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data');

        // Test filtering by menu
        $response = $this->getJson('/api/v2/admin/track-tiffins/filter?menu=dinner');
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        // Test filtering by location
        $response = $this->getJson('/api/v2/admin/track-tiffins/filter?location_id=' . $location->pk_location_code);
        $response->assertStatus(200)
            ->assertJsonCount(5, 'data');
    }

    /**
     * Test showing a specific customer.
     */
    public function test_show_returns_customer_details(): void
    {
        // Create test data
        $location = DeliveryLocation::factory()->create();
        $customer = Customer::factory()->create([
            'location_code' => $location->pk_location_code,
            'dabbawala_code' => 'MD-MU-123-456',
            'dabbawala_code_type' => 'mumbai',
            'dabba_status' => 'IN',
        ]);

        // Make the request
        $response = $this->getJson('/api/v2/admin/track-tiffins/' . $customer->pk_customer_code);

        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'customer_name',
                'phone',
                'email',
                'location',
                'address',
                'food_preference',
                'dabbawala' => [
                    'code',
                    'code_type',
                    'image',
                    'status',
                ],
            ])
            ->assertJson([
                'id' => $customer->pk_customer_code,
                'customer_name' => $customer->customer_name,
                'dabbawala' => [
                    'code' => 'MD-MU-123-456',
                    'code_type' => 'mumbai',
                    'status' => 'IN',
                ],
            ]);
    }

    /**
     * Test updating a customer's dabbawala status.
     */
    public function test_update_status_updates_customer_dabbawala_status(): void
    {
        // Create test data
        $customer = Customer::factory()->create([
            'dabbawala_code' => 'MD-MU-123-456',
            'dabbawala_code_type' => 'mumbai',
            'dabba_status' => 'IN',
        ]);

        // Make the request
        $response = $this->putJson('/api/v2/admin/track-tiffins/' . $customer->pk_customer_code . '/update-status', [
            'dabbawala_code' => 'MD-MU-789-012',
            'dabba_status' => 'OUT',
        ]);

        // Assert response
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Dabbawala status updated successfully',
            ]);

        // Assert database was updated
        $this->assertDatabaseHas('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
            'dabbawala_code' => 'MD-MU-789-012',
            'dabba_status' => 'OUT',
        ]);
    }

    /**
     * Test generating a new dabbawala code.
     */
    public function test_generate_code_creates_new_dabbawala_code(): void
    {
        // Create test data
        $customer = Customer::factory()->create([
            'dabbawala_code' => null,
            'dabbawala_code_type' => null,
            'dabba_status' => null,
        ]);

        // Mock the HTTP client
        Http::fake([
            config('services.delivery.url') . '/api/v2/delivery/dabbawala/generate-code' => Http::response([
                'message' => 'Dabbawala code generated successfully',
                'data' => [
                    'dabbawala_code' => 'MD-MU-' . $customer->pk_customer_code . '-123',
                ],
            ], 200),
        ]);

        // Make the request
        $response = $this->postJson('/api/v2/admin/track-tiffins/' . $customer->pk_customer_code . '/generate-code');

        // Assert response
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Dabbawala code generated successfully',
                'data' => [
                    'dabbawala_code' => 'MD-MU-' . $customer->pk_customer_code . '-123',
                ],
            ]);

        // Assert HTTP request was made
        Http::assertSent(function ($request) use ($customer) {
            return $request->url() == config('services.delivery.url') . '/api/v2/delivery/dabbawala/generate-code' &&
                   $request->data() == ['customer_id' => $customer->pk_customer_code];
        });
    }
}

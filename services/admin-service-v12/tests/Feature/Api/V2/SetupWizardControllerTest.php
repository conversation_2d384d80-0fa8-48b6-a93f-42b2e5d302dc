<?php

namespace Tests\Feature\Api\V2;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON>vel\Sanctum\Sanctum;
use Tests\TestCase;

class SetupWizardControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Set up the test.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Create a user and authenticate
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /**
     * Test getStatus endpoint.
     *
     * @return void
     */
    public function testGetStatus(): void
    {
        $response = $this->getJson('/api/v2/admin/setup-wizard/status');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'completed',
                    'current_step',
                ],
            ])
            ->assertJson([
                'status' => 'success',
                'data' => [
                    'completed' => false,
                    'current_step' => 1,
                ],
            ]);
    }

    /**
     * Test updateStatus endpoint.
     *
     * @return void
     */
    public function testUpdateStatus(): void
    {
        $response = $this->putJson('/api/v2/admin/setup-wizard/status', [
            'completed' => true,
            'current_step' => 3,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'completed',
                    'current_step',
                ],
            ])
            ->assertJson([
                'status' => 'success',
                'message' => 'Setup wizard status updated successfully',
                'data' => [
                    'completed' => true,
                    'current_step' => 3,
                ],
            ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'SETUP_WIZARD_COMPLETED',
            'setting_value' => 'true',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'SETUP_WIZARD_CURRENT_STEP',
            'setting_value' => '3',
        ]);
    }

    /**
     * Test setupCompanyProfile endpoint.
     *
     * @return void
     */
    public function testSetupCompanyProfile(): void
    {
        $response = $this->postJson('/api/v2/admin/setup-wizard/company-profile', [
            'company_name' => 'Test Company',
            'postal_address' => '123 Test St, Test City, 12345',
            'support_email' => '<EMAIL>',
            'phone' => '555-1234',
            'sender_id' => 'TESTID',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'current_step',
                ],
            ])
            ->assertJson([
                'status' => 'success',
                'message' => 'Company profile setup completed successfully',
                'data' => [
                    'current_step' => 2,
                ],
            ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'MERCHANT_COMPANY_NAME',
            'setting_value' => 'Test Company',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'MERCHANT_POSTAL_ADDRESS',
            'setting_value' => '123 Test St, Test City, 12345',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'MERCHANT_SUPPORT_EMAIL',
            'setting_value' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'GLOBAL_WEBSITE_PHONE',
            'setting_value' => '555-1234',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'MERCHANT_SENDER_ID',
            'setting_value' => 'TESTID',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'SETUP_WIZARD_CURRENT_STEP',
            'setting_value' => '2',
        ]);
    }

    /**
     * Test setupSystemSettings endpoint.
     *
     * @return void
     */
    public function testSetupSystemSettings(): void
    {
        $response = $this->postJson('/api/v2/admin/setup-wizard/system-settings', [
            'locale' => 'en_US',
            'currency' => 'USD',
            'currency_symbol' => '$',
            'time_zone' => 'America/New_York',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'current_step',
                ],
            ])
            ->assertJson([
                'status' => 'success',
                'message' => 'System settings setup completed successfully',
                'data' => [
                    'current_step' => 3,
                ],
            ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'GLOBAL_LOCALE',
            'setting_value' => 'en_US',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'GLOBAL_CURRENCY',
            'setting_value' => 'USD',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'GLOBAL_CURRENCY_ENTITY',
            'setting_value' => '$',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'TIME_ZONE',
            'setting_value' => 'America/New_York',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'SETUP_WIZARD_CURRENT_STEP',
            'setting_value' => '3',
        ]);
    }

    /**
     * Test completeSetup endpoint.
     *
     * @return void
     */
    public function testCompleteSetup(): void
    {
        $response = $this->postJson('/api/v2/admin/setup-wizard/complete');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'completed',
                    'current_step',
                ],
            ])
            ->assertJson([
                'status' => 'success',
                'message' => 'Setup wizard completed successfully',
                'data' => [
                    'completed' => true,
                    'current_step' => 5,
                ],
            ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'SETUP_WIZARD_COMPLETED',
            'setting_value' => 'true',
        ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'SETUP_WIZARD_CURRENT_STEP',
            'setting_value' => '5',
        ]);
    }
}

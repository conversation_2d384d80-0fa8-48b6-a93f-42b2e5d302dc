<?php

namespace Tests\Feature\Api\V2;

use Tests\TestCase;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class RoleControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test permissions
        Permission::factory()->create([
            'name' => 'view_users',
            'display_name' => 'View Users',
            'module' => 'users'
        ]);
        
        Permission::factory()->create([
            'name' => 'edit_users',
            'display_name' => 'Edit Users',
            'module' => 'users'
        ]);
    }

    public function test_can_get_all_roles(): void
    {
        // Create test roles
        Role::factory()->count(3)->create();

        $response = $this->getJson('/api/v2/admin/roles');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'display_name',
                        'description',
                        'is_system',
                        'company_id',
                        'unit_id',
                        'permissions'
                    ]
                ]
            ])
            ->assertJson([
                'status' => 'success'
            ]);
    }

    public function test_can_get_roles_filtered_by_company(): void
    {
        // Create roles for different companies
        Role::factory()->create(['company_id' => 1]);
        Role::factory()->create(['company_id' => 2]);

        $response = $this->getJson('/api/v2/admin/roles?company_id=1');

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ]);

        $roles = $response->json('data');
        $this->assertCount(1, $roles);
        $this->assertEquals(1, $roles[0]['company_id']);
    }

    public function test_can_get_single_role(): void
    {
        $role = Role::factory()->create();

        $response = $this->getJson("/api/v2/admin/roles/{$role->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'name',
                    'display_name',
                    'description',
                    'is_system',
                    'company_id',
                    'unit_id',
                    'permissions'
                ]
            ])
            ->assertJson([
                'status' => 'success',
                'data' => [
                    'id' => $role->id,
                    'name' => $role->name
                ]
            ]);
    }

    public function test_returns_404_for_non_existent_role(): void
    {
        $response = $this->getJson('/api/v2/admin/roles/999');

        $response->assertStatus(404);
    }

    public function test_can_create_role(): void
    {
        $permissions = Permission::all()->pluck('id')->toArray();
        
        $roleData = [
            'name' => 'test_role',
            'display_name' => 'Test Role',
            'description' => 'A test role',
            'permissions' => $permissions,
            'company_id' => 1,
            'unit_id' => 1
        ];

        $response = $this->postJson('/api/v2/admin/roles', $roleData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'name',
                    'display_name',
                    'description',
                    'is_system',
                    'company_id',
                    'unit_id'
                ]
            ])
            ->assertJson([
                'status' => 'success',
                'data' => [
                    'name' => 'test_role',
                    'display_name' => 'Test Role'
                ]
            ]);

        $this->assertDatabaseHas('roles', [
            'name' => 'test_role',
            'display_name' => 'Test Role'
        ]);
    }

    public function test_create_role_validation_fails_with_missing_name(): void
    {
        $roleData = [
            'display_name' => 'Test Role',
            'description' => 'A test role'
        ];

        $response = $this->postJson('/api/v2/admin/roles', $roleData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_can_update_role(): void
    {
        $role = Role::factory()->create();
        $permissions = Permission::all()->pluck('id')->toArray();

        $updateData = [
            'display_name' => 'Updated Role Name',
            'description' => 'Updated description',
            'permissions' => $permissions
        ];

        $response = $this->putJson("/api/v2/admin/roles/{$role->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'data' => [
                    'id' => $role->id,
                    'display_name' => 'Updated Role Name',
                    'description' => 'Updated description'
                ]
            ]);

        $this->assertDatabaseHas('roles', [
            'id' => $role->id,
            'display_name' => 'Updated Role Name'
        ]);
    }

    public function test_can_delete_role(): void
    {
        $role = Role::factory()->create(['is_system' => false]);

        $response = $this->deleteJson("/api/v2/admin/roles/{$role->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ]);

        $this->assertSoftDeleted('roles', [
            'id' => $role->id
        ]);
    }

    public function test_cannot_delete_system_role(): void
    {
        $role = Role::factory()->create(['is_system' => true]);

        $response = $this->deleteJson("/api/v2/admin/roles/{$role->id}");

        $response->assertStatus(400)
            ->assertJson([
                'status' => 'error',
                'message' => 'Cannot delete system role'
            ]);

        $this->assertDatabaseHas('roles', [
            'id' => $role->id
        ]);
    }

    public function test_can_get_all_permissions(): void
    {
        $response = $this->getJson('/api/v2/admin/permissions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'display_name',
                        'description',
                        'module',
                        'is_system'
                    ]
                ]
            ])
            ->assertJson([
                'status' => 'success'
            ]);
    }

    public function test_can_get_permissions_by_module(): void
    {
        $response = $this->getJson('/api/v2/admin/permissions?module=users');

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ]);

        $permissions = $response->json('data');
        foreach ($permissions as $permission) {
            $this->assertEquals('users', $permission['module']);
        }
    }
}

<?php

namespace Tests\Feature\Api\V2;

use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class ConfigControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Set up the test.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Create a user and authenticate
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create test settings
        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'TEST_KEY',
            'setting_value' => 'test_value',
            'setting_type' => 'string',
            'setting_group' => 'test',
            'is_system' => false,
            'is_public' => true,
            'description' => 'Test setting',
        ]);

        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'ANOTHER_KEY',
            'setting_value' => 'another_value',
            'setting_type' => 'string',
            'setting_group' => 'test',
            'is_system' => false,
            'is_public' => true,
            'description' => 'Another test setting',
        ]);

        Setting::create([
            'company_id' => 1,
            'unit_id' => 1,
            'setting_key' => 'SYSTEM_KEY',
            'setting_value' => 'system_value',
            'setting_type' => 'string',
            'setting_group' => 'system',
            'is_system' => true,
            'is_public' => false,
            'description' => 'System setting',
        ]);
    }

    /**
     * Test index endpoint.
     *
     * @return void
     */
    public function testIndex(): void
    {
        $response = $this->getJson('/api/v2/admin/config');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ])
            ->assertJson([
                'status' => 'success',
                'data' => [
                    'TEST_KEY' => 'test_value',
                    'ANOTHER_KEY' => 'another_value',
                    'SYSTEM_KEY' => 'system_value',
                ],
            ]);
    }

    /**
     * Test show endpoint.
     *
     * @return void
     */
    public function testShow(): void
    {
        $response = $this->getJson('/api/v2/admin/config/TEST_KEY');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'key',
                    'value',
                ],
            ])
            ->assertJson([
                'status' => 'success',
                'data' => [
                    'key' => 'TEST_KEY',
                    'value' => 'test_value',
                ],
            ]);
    }

    /**
     * Test show endpoint with non-existent key.
     *
     * @return void
     */
    public function testShowNonExistentKey(): void
    {
        $response = $this->getJson('/api/v2/admin/config/NON_EXISTENT_KEY');

        $response->assertStatus(404)
            ->assertJsonStructure([
                'status',
                'message',
            ])
            ->assertJson([
                'status' => 'error',
                'message' => "Configuration key 'NON_EXISTENT_KEY' not found.",
            ]);
    }

    /**
     * Test update endpoint.
     *
     * @return void
     */
    public function testUpdate(): void
    {
        $response = $this->putJson('/api/v2/admin/config/TEST_KEY', [
            'value' => 'updated_value',
            'type' => 'string',
            'group' => 'test',
            'is_system' => false,
            'is_public' => true,
            'description' => 'Updated test setting',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'key',
                    'value',
                ],
            ])
            ->assertJson([
                'status' => 'success',
                'message' => "Configuration key 'TEST_KEY' updated successfully.",
                'data' => [
                    'key' => 'TEST_KEY',
                    'value' => 'updated_value',
                ],
            ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'TEST_KEY',
            'setting_value' => 'updated_value',
            'setting_group' => 'test',
            'setting_type' => 'string',
            'is_system' => 0,
            'is_public' => 1,
            'description' => 'Updated test setting',
        ]);
    }

    /**
     * Test update endpoint with invalid data.
     *
     * @return void
     */
    public function testUpdateInvalidData(): void
    {
        $response = $this->putJson('/api/v2/admin/config/TEST_KEY', [
            'type' => 'invalid_type',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'status',
                'message',
                'errors',
            ])
            ->assertJson([
                'status' => 'error',
                'message' => 'Validation failed',
            ]);
    }

    /**
     * Test destroy endpoint.
     *
     * @return void
     */
    public function testDestroy(): void
    {
        $response = $this->deleteJson('/api/v2/admin/config/TEST_KEY');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
            ])
            ->assertJson([
                'status' => 'success',
                'message' => "Configuration key 'TEST_KEY' deleted successfully.",
            ]);

        $this->assertDatabaseMissing('settings', [
            'setting_key' => 'TEST_KEY',
        ]);
    }

    /**
     * Test destroy endpoint with non-existent key.
     *
     * @return void
     */
    public function testDestroyNonExistentKey(): void
    {
        $response = $this->deleteJson('/api/v2/admin/config/NON_EXISTENT_KEY');

        $response->assertStatus(404)
            ->assertJsonStructure([
                'status',
                'message',
            ])
            ->assertJson([
                'status' => 'error',
                'message' => "Configuration key 'NON_EXISTENT_KEY' not found.",
            ]);
    }

    /**
     * Test getSettingsByGroup endpoint.
     *
     * @return void
     */
    public function testGetSettingsByGroup(): void
    {
        $response = $this->getJson('/api/v2/admin/config/group/test');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ])
            ->assertJson([
                'status' => 'success',
                'data' => [
                    'TEST_KEY' => 'test_value',
                    'ANOTHER_KEY' => 'another_value',
                ],
            ]);
    }
}

name: Deploy Admin Service

on:
  push:
    branches: [ main ]
    paths:
      - 'services/admin-service-v12/**'
      - '.github/workflows/deploy.yml'
  workflow_dispatch:

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, dom, fileinfo, mysql
          coverage: xdebug
      
      - name: Copy .env
        run: |
          cd services/admin-service-v12
          cp .env.example .env
          sed -i 's/DB_HOST=127.0.0.1/DB_HOST=127.0.0.1/g' .env
          sed -i 's/DB_DATABASE=laravel/DB_DATABASE=testing/g' .env
          sed -i 's/DB_USERNAME=root/DB_USERNAME=root/g' .env
          sed -i 's/DB_PASSWORD=/DB_PASSWORD=password/g' .env
      
      - name: Install Dependencies
        run: |
          cd services/admin-service-v12
          composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
      
      - name: Generate key
        run: |
          cd services/admin-service-v12
          php artisan key:generate
      
      - name: Directory Permissions
        run: |
          cd services/admin-service-v12
          chmod -R 777 storage bootstrap/cache
      
      - name: Run Tests
        run: |
          cd services/admin-service-v12
          php artisan test --coverage-clover=coverage.xml
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: services/admin-service-v12/coverage.xml
          fail_ci_if_error: false

  build:
    name: Build and Push Docker Image
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: ./services/admin-service-v12
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/admin-service:${{ github.sha }},${{ secrets.DOCKERHUB_USERNAME }}/admin-service:latest
          cache-from: type=registry,ref=${{ secrets.DOCKERHUB_USERNAME }}/admin-service:latest
          cache-to: type=inline

  deploy:
    name: Deploy to AWS ECS
    needs: build
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.4.6
      
      - name: Terraform Init
        run: |
          cd services/admin-service-v12/terraform
          terraform init
      
      - name: Terraform Plan
        run: |
          cd services/admin-service-v12/terraform
          terraform plan -var="image_tag=${{ github.sha }}" -var="aws_region=${{ secrets.AWS_REGION }}" -out=tfplan
      
      - name: Terraform Apply
        run: |
          cd services/admin-service-v12/terraform
          terraform apply -auto-approve tfplan
      
      - name: Wait for deployment to complete
        run: |
          aws ecs wait services-stable --cluster admin-service-cluster --services admin-service-blue admin-service-green
      
      - name: Update Kong API Gateway
        run: |
          aws lambda invoke --function-name update-kong-config --payload '{"service":"admin-service"}' response.json
          cat response.json

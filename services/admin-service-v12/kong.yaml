_format_version: "3.0"
_transform: true

services:
  - name: admin-service
    url: http://admin-service:8000
    routes:
      - name: admin-service-route
        paths:
          - /api/v2/admin
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_ssl: false
          redis_ssl_verify: false
          redis_server_name: null
      - name: jwt
        config:
          claims_to_verify:
            - exp
          key_claim_name: kid
          secret_is_base64: false
          run_on_preflight: true
      - name: request-transformer
        config:
          add:
            headers:
              - X-Forwarded-Prefix:/api/v2/admin
      - name: http-log
        config:
          http_endpoint: http://logging-service:8000/api/v1/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          log_bodies: true
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

upstreams:
  - name: admin-service
    targets:
      - target: admin-service:8000
        weight: 100
    healthchecks:
      active:
        concurrency: 10
        healthy:
          http_statuses:
            - 200
            - 302
          interval: 5
          successes: 1
        http_path: /api/v2/admin/health
        timeout: 1
        unhealthy:
          http_failures: 2
          http_statuses:
            - 429
            - 404
            - 500
            - 501
            - 502
            - 503
            - 504
            - 505
          interval: 5
          tcp_failures: 2
          timeouts: 2

consumers:
  - username: admin-frontend
    jwt_secrets:
      - algorithm: HS256
        key: admin-frontend
        secret: your-secret-key-here

  - username: mobile-app
    jwt_secrets:
      - algorithm: HS256
        key: mobile-app
        secret: your-secret-key-here

<?php

namespace App\Services\Customer;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\DTOs\Customer\CustomerDTO;
use App\DTOs\Customer\AddressDTO;
use App\Exceptions\Customer\CustomerException;

/**
 * Customer Service Client
 * 
 * This class provides a client for interacting with the Customer Service API.
 */
class CustomerServiceClient
{
    /**
     * The base URL for the Customer Service API.
     *
     * @var string
     */
    protected string $baseUrl;

    /**
     * The timeout for API requests in seconds.
     *
     * @var int
     */
    protected int $timeout;

    /**
     * The retry attempts for API requests.
     *
     * @var int
     */
    protected int $retries;

    /**
     * Create a new CustomerServiceClient instance.
     *
     * @param string|null $baseUrl
     * @param int $timeout
     * @param int $retries
     */
    public function __construct(
        ?string $baseUrl = null,
        int $timeout = 30,
        int $retries = 3
    ) {
        $this->baseUrl = $baseUrl ?? config('services.customer.url', 'http://customer-service-v12:8000/api');
        $this->timeout = $timeout;
        $this->retries = $retries;
    }

    /**
     * Get a customer by ID.
     *
     * @param int $customerId
     * @return array|null
     * @throws CustomerException
     */
    public function getCustomer(int $customerId): ?array
    {
        try {
            $response = $this->http()
                ->get("/customers/{$customerId}");

            if ($response->successful()) {
                return $response->json('data');
            }

            if ($response->status() === 404) {
                return null;
            }

            throw new CustomerException($response->json('message') ?? 'Failed to get customer');
        } catch (\Exception $e) {
            Log::error('Customer service get customer failed', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);

            throw new CustomerException('Failed to get customer: ' . $e->getMessage());
        }
    }

    /**
     * Create a new customer.
     *
     * @param CustomerDTO $customerDTO
     * @return array
     * @throws CustomerException
     */
    public function createCustomer(CustomerDTO $customerDTO): array
    {
        try {
            $response = $this->http()
                ->post('/customers', $customerDTO->toArray());

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new CustomerException($response->json('message') ?? 'Failed to create customer');
        } catch (\Exception $e) {
            Log::error('Customer service create customer failed', [
                'error' => $e->getMessage(),
                'customer_data' => $customerDTO->toArray()
            ]);

            throw new CustomerException('Failed to create customer: ' . $e->getMessage());
        }
    }

    /**
     * Update a customer.
     *
     * @param int $customerId
     * @param CustomerDTO $customerDTO
     * @return array
     * @throws CustomerException
     */
    public function updateCustomer(int $customerId, CustomerDTO $customerDTO): array
    {
        try {
            $response = $this->http()
                ->put("/customers/{$customerId}", $customerDTO->toArray());

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new CustomerException($response->json('message') ?? 'Failed to update customer');
        } catch (\Exception $e) {
            Log::error('Customer service update customer failed', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId,
                'customer_data' => $customerDTO->toArray()
            ]);

            throw new CustomerException('Failed to update customer: ' . $e->getMessage());
        }
    }

    /**
     * Get customer wallet balance.
     *
     * @param int $customerId
     * @return float
     * @throws CustomerException
     */
    public function getWalletBalance(int $customerId): float
    {
        try {
            $response = $this->http()
                ->get("/customers/{$customerId}/wallet");

            if ($response->successful()) {
                return (float) $response->json('data.balance');
            }

            throw new CustomerException($response->json('message') ?? 'Failed to get wallet balance');
        } catch (\Exception $e) {
            Log::error('Customer service get wallet balance failed', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId
            ]);

            throw new CustomerException('Failed to get wallet balance: ' . $e->getMessage());
        }
    }

    /**
     * Create an HTTP client instance.
     *
     * @return PendingRequest
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name'),
                'X-Service-Version' => config('app.version', '1.0.0'),
            ]);
    }
}

<?php

namespace App\Exceptions\Customer;

use Exception;

/**
 * Customer Exception
 * 
 * This exception is thrown when there is an error with the Customer Service.
 */
class CustomerException extends Exception
{
    /**
     * Create a new CustomerException instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "Customer service error", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

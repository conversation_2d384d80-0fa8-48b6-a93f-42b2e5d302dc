<?php

namespace App\DTOs\Customer;

/**
 * Customer DTO
 * 
 * This class represents a customer data transfer object.
 */
class CustomerDTO
{
    /**
     * Create a new CustomerDTO instance.
     *
     * @param string $customerName
     * @param string $phone
     * @param string|null $emailAddress
     * @param string|null $customerAddress
     * @param string|null $foodPreference
     * @param string|null $city
     * @param string|null $cityName
     * @param string|null $companyName
     * @param string|null $password
     * @param bool $status
     * @param int|null $companyId
     * @param int|null $unitId
     * @param array $addresses
     */
    public function __construct(
        public readonly string $customerName,
        public readonly string $phone,
        public readonly ?string $emailAddress = null,
        public readonly ?string $customerAddress = null,
        public readonly ?string $foodPreference = null,
        public readonly ?string $city = null,
        public readonly ?string $cityName = null,
        public readonly ?string $companyName = null,
        public readonly ?string $password = null,
        public readonly bool $status = true,
        public readonly ?int $companyId = null,
        public readonly ?int $unitId = null,
        public readonly array $addresses = []
    ) {
    }

    /**
     * Convert the DTO to an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'customer_name' => $this->customerName,
            'phone' => $this->phone,
            'email_address' => $this->emailAddress,
            'customer_Address' => $this->customerAddress,
            'food_preference' => $this->foodPreference,
            'city' => $this->city,
            'city_name' => $this->cityName,
            'company_name' => $this->companyName,
            'status' => $this->status,
            'company_id' => $this->companyId,
            'unit_id' => $this->unitId,
        ];

        if ($this->password) {
            $data['password'] = $this->password;
        }

        if (!empty($this->addresses)) {
            $data['addresses'] = array_map(function (AddressDTO $address) {
                return $address->toArray();
            }, $this->addresses);
        }

        return $data;
    }

    /**
     * Create a CustomerDTO from an array.
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $addresses = [];
        if (!empty($data['addresses'])) {
            foreach ($data['addresses'] as $addressData) {
                $addresses[] = AddressDTO::fromArray($addressData);
            }
        }

        return new self(
            $data['customer_name'],
            $data['phone'],
            $data['email_address'] ?? null,
            $data['customer_Address'] ?? null,
            $data['food_preference'] ?? null,
            $data['city'] ?? null,
            $data['city_name'] ?? null,
            $data['company_name'] ?? null,
            $data['password'] ?? null,
            $data['status'] ?? true,
            $data['company_id'] ?? null,
            $data['unit_id'] ?? null,
            $addresses
        );
    }
}

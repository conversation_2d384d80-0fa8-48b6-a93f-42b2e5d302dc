<?php

namespace Database\Factories;

use App\Models\KitchenMaster;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\KitchenMaster>
 */
class KitchenMasterFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = KitchenMaster::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'kitchen_name' => $this->faker->unique()->words(2, true),
            'kitchen_alias' => $this->faker->unique()->lexify('??'),
            'location' => $this->faker->city(),
            'location_id' => $this->faker->numberBetween(1, 10),
            'city_id' => $this->faker->numberBetween(1, 10),
            'base_kitchen' => $this->faker->boolean(),
            'kitchen_address' => $this->faker->address(),
            'created_by' => 1,
            'updated_by' => 1,
            'status' => true,
        ];
    }
}

<?php

namespace Database\Factories;

use App\Models\Kitchen;
use App\Models\KitchenMaster;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Kitchen>
 */
class KitchenFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Kitchen::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'fk_product_code' => Product::factory(),
            'fk_kitchen_code' => KitchenMaster::factory(),
            'total_order' => $this->faker->numberBetween(5, 20),
            'prepared' => $this->faker->numberBetween(0, 5),
            'date' => $this->faker->date(),
            'order_menu' => $this->faker->randomElement(['breakfast', 'lunch', 'dinner']),
            'status' => true,
        ];
    }
}

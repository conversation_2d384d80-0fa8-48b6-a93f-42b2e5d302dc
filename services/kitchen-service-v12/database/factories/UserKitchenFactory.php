<?php

namespace Database\Factories;

use App\Models\KitchenMaster;
use App\Models\UserKitchen;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserKitchen>
 */
class UserKitchenFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserKitchen::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'fk_user_code' => $this->faker->numberBetween(1, 10),
            'fk_kitchen_code' => KitchenMaster::factory(),
            'status' => true,
        ];
    }
}

<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->words(3, true),
            'kitchen_code' => $this->faker->unique()->lexify('???'),
            'quantity' => $this->faker->randomFloat(2, 1, 10),
            'unit' => $this->faker->randomElement(['plate', 'bowl', 'piece', 'serving']),
            'recipe' => $this->faker->paragraphs(3, true),
            'screen' => $this->faker->numberBetween(1, 5),
        ];
    }
}

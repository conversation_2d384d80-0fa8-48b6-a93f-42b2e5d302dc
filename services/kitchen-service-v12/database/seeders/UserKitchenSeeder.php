<?php

namespace Database\Seeders;

use App\Models\UserKitchen;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserKitchenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create user kitchens
        UserKitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_user_code' => 1,
            'fk_kitchen_code' => 1, // Main Kitchen
            'status' => true,
        ]);

        UserKitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_user_code' => 2,
            'fk_kitchen_code' => 2, // Satellite Kitchen
            'status' => true,
        ]);

        UserKitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_user_code' => 3,
            'fk_kitchen_code' => 3, // Prep Kitchen
            'status' => true,
        ]);

        UserKitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_user_code' => 4,
            'fk_kitchen_code' => 1, // Main Kitchen
            'status' => true,
        ]);
    }
}

<?php

namespace Database\Seeders;

use App\Models\Kitchen;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class KitchenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create kitchens
        Kitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_product_code' => 1, // Vegetable Meal
            'fk_kitchen_code' => 1, // Main Kitchen
            'total_order' => 10,
            'prepared' => 5,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch',
            'status' => true,
        ]);

        Kitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_product_code' => 2, // Chicken Curry
            'fk_kitchen_code' => 1, // Main Kitchen
            'total_order' => 15,
            'prepared' => 7,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch',
            'status' => true,
        ]);

        Kitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_product_code' => 3, // Pasta Primavera
            'fk_kitchen_code' => 2, // Satellite Kitchen
            'total_order' => 8,
            'prepared' => 3,
            'date' => date('Y-m-d'),
            'order_menu' => 'dinner',
            'status' => true,
        ]);

        Kitchen::create([
            'company_id' => 1,
            'unit_id' => 1,
            'fk_product_code' => 4, // Fruit Salad
            'fk_kitchen_code' => 3, // Prep Kitchen
            'total_order' => 12,
            'prepared' => 6,
            'date' => date('Y-m-d'),
            'order_menu' => 'breakfast',
            'status' => true,
        ]);
    }
}

<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create products
        Product::create([
            'name' => 'Vegetable Meal',
            'kitchen_code' => 'VM001',
            'quantity' => 1.0,
            'unit' => 'plate',
            'recipe' => '<p><strong>Ingredients:</strong></p><ul><li>1 cup mixed vegetables</li><li>1/2 cup rice</li><li>2 tbsp olive oil</li><li>Salt and pepper to taste</li></ul><p><strong>Instructions:</strong></p><ol><li>Cook rice according to package instructions.</li><li>Sauté vegetables in olive oil until tender.</li><li>Season with salt and pepper.</li><li>Serve vegetables over rice.</li></ol>',
            'screen' => 1,
        ]);

        Product::create([
            'name' => 'Chicken Curry',
            'kitchen_code' => 'CC001',
            'quantity' => 1.0,
            'unit' => 'bowl',
            'recipe' => '<p><strong>Ingredients:</strong></p><ul><li>500g chicken, cut into pieces</li><li>2 onions, finely chopped</li><li>2 tomatoes, pureed</li><li>2 tbsp curry powder</li><li>1 tbsp ginger-garlic paste</li><li>2 tbsp oil</li><li>Salt to taste</li><li>Fresh coriander for garnish</li></ul><p><strong>Instructions:</strong></p><ol><li>Heat oil in a pan and sauté onions until golden brown.</li><li>Add ginger-garlic paste and cook for 1 minute.</li><li>Add curry powder and cook for another minute.</li><li>Add chicken pieces and cook until they change color.</li><li>Add tomato puree and salt, and cook until chicken is tender.</li><li>Garnish with fresh coriander and serve hot.</li></ol>',
            'screen' => 1,
        ]);

        Product::create([
            'name' => 'Pasta Primavera',
            'kitchen_code' => 'PP001',
            'quantity' => 1.0,
            'unit' => 'plate',
            'recipe' => '<p><strong>Ingredients:</strong></p><ul><li>250g pasta</li><li>1 cup mixed vegetables (bell peppers, zucchini, carrots)</li><li>2 cloves garlic, minced</li><li>1/4 cup olive oil</li><li>1/4 cup grated Parmesan cheese</li><li>Salt and pepper to taste</li><li>Fresh basil for garnish</li></ul><p><strong>Instructions:</strong></p><ol><li>Cook pasta according to package instructions.</li><li>In a large pan, heat olive oil and sauté garlic until fragrant.</li><li>Add vegetables and cook until tender.</li><li>Drain pasta and add to the pan with vegetables.</li><li>Toss well and season with salt and pepper.</li><li>Sprinkle with Parmesan cheese and garnish with fresh basil.</li></ol>',
            'screen' => 2,
        ]);

        Product::create([
            'name' => 'Fruit Salad',
            'kitchen_code' => 'FS001',
            'quantity' => 1.0,
            'unit' => 'bowl',
            'recipe' => '<p><strong>Ingredients:</strong></p><ul><li>1 apple, diced</li><li>1 banana, sliced</li><li>1 orange, segmented</li><li>1 cup grapes, halved</li><li>1 cup strawberries, sliced</li><li>2 tbsp honey</li><li>1 tbsp lemon juice</li><li>Fresh mint for garnish</li></ul><p><strong>Instructions:</strong></p><ol><li>Combine all fruits in a large bowl.</li><li>Mix honey and lemon juice in a small bowl.</li><li>Pour the honey-lemon mixture over the fruits and toss gently.</li><li>Garnish with fresh mint leaves.</li><li>Chill before serving.</li></ol>',
            'screen' => 3,
        ]);
    }
}

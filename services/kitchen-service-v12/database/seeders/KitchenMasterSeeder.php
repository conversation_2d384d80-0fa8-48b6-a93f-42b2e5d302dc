<?php

namespace Database\Seeders;

use App\Models\KitchenMaster;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class KitchenMasterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create kitchen masters
        KitchenMaster::create([
            'company_id' => 1,
            'unit_id' => 1,
            'kitchen_name' => 'Main Kitchen',
            'kitchen_alias' => 'MK',
            'location' => 'Downtown',
            'location_id' => 1,
            'city_id' => 1,
            'base_kitchen' => true,
            'kitchen_address' => '123 Main St, Downtown',
            'created_by' => 1,
            'updated_by' => 1,
            'status' => true,
        ]);

        KitchenMaster::create([
            'company_id' => 1,
            'unit_id' => 1,
            'kitchen_name' => 'Satellite Kitchen',
            'kitchen_alias' => 'SK',
            'location' => 'Uptown',
            'location_id' => 2,
            'city_id' => 1,
            'base_kitchen' => false,
            'kitchen_address' => '456 Oak St, Uptown',
            'created_by' => 1,
            'updated_by' => 1,
            'status' => true,
        ]);

        KitchenMaster::create([
            'company_id' => 1,
            'unit_id' => 1,
            'kitchen_name' => 'Prep Kitchen',
            'kitchen_alias' => 'PK',
            'location' => 'Midtown',
            'location_id' => 3,
            'city_id' => 1,
            'base_kitchen' => false,
            'kitchen_address' => '789 Pine St, Midtown',
            'created_by' => 1,
            'updated_by' => 1,
            'status' => true,
        ]);
    }
}

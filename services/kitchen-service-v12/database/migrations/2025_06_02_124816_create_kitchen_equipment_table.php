<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kitchen_equipment', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type');
            $table->enum('status', ['operational', 'maintenance', 'broken', 'offline'])->default('operational');
            $table->decimal('utilization_percentage', 5, 2)->default(0);
            $table->unsignedBigInteger('kitchen_id')->nullable();
            $table->text('description')->nullable();
            $table->json('specifications')->nullable();
            $table->timestamp('last_maintenance')->nullable();
            $table->timestamp('next_maintenance')->nullable();
            $table->timestamps();

            $table->foreign('kitchen_id')->references('pk_kitchen_code')->on('kitchens')->onDelete('set null');
            $table->index(['status', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kitchen_equipment');
    }
};

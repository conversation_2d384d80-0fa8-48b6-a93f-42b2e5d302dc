<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meal_preparations', function (Blueprint $table) {
            $table->id();
            $table->string('order_id');
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('kitchen_id');
            $table->string('meal_type'); // breakfast, lunch, dinner, snacks
            $table->enum('status', ['pending', 'preparing', 'completed', 'cancelled'])->default('pending');
            $table->integer('quantity_ordered');
            $table->integer('quantity_prepared')->default(0);
            $table->integer('preparation_time')->nullable(); // in minutes
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->unsignedBigInteger('prepared_by')->nullable(); // staff member ID
            $table->text('notes')->nullable();
            $table->date('preparation_date');
            $table->string('menu'); // lunch, dinner, etc.
            $table->timestamps();

            $table->foreign('product_id')->references('pk_product_code')->on('products')->onDelete('cascade');
            $table->foreign('kitchen_id')->references('pk_kitchen_code')->on('kitchens')->onDelete('cascade');
            $table->index(['order_id', 'preparation_date']);
            $table->index(['status', 'meal_type']);
            $table->index(['kitchen_id', 'preparation_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meal_preparations');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id('pk_product_code');
            $table->string('name');
            $table->string('kitchen_code')->nullable();
            $table->float('quantity')->default(0);
            $table->string('unit')->nullable();
            $table->text('recipe')->nullable();
            $table->integer('screen')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index('name');
            $table->index('kitchen_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};

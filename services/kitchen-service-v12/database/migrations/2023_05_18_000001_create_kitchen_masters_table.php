<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kitchen_masters', function (Blueprint $table) {
            $table->id('pk_kitchen_code');
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->string('kitchen_name');
            $table->string('kitchen_alias')->nullable();
            $table->string('location')->nullable();
            $table->unsignedBigInteger('location_id')->nullable();
            $table->unsignedBigInteger('city_id')->nullable();
            $table->boolean('base_kitchen')->default(false);
            $table->text('kitchen_address')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index('company_id');
            $table->index('unit_id');
            $table->index('location_id');
            $table->index('city_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kitchen_masters');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_kitchens', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->unsignedBigInteger('fk_user_code');
            $table->unsignedBigInteger('fk_kitchen_code');
            $table->boolean('status')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index('company_id');
            $table->index('unit_id');
            $table->index('fk_user_code');
            $table->index('fk_kitchen_code');
            
            // Foreign keys
            $table->foreign('fk_kitchen_code')
                  ->references('pk_kitchen_code')
                  ->on('kitchen_masters')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_kitchens');
    }
};

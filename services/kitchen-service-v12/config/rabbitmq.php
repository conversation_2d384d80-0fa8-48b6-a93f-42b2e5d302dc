<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for RabbitMQ.
    |
    */

    // RabbitMQ connection settings
    'host' => env('RABBITMQ_HOST', 'localhost'),
    'port' => env('RABBITMQ_PORT', 5672),
    'user' => env('RABBITMQ_USER', 'guest'),
    'password' => env('RABBITMQ_PASSWORD', 'guest'),
    'vhost' => env('RABBITMQ_VHOST', '/'),

    // Exchange settings
    'exchange' => env('RABBITMQ_EXCHANGE', 'fooddialer'),
    'exchange_type' => env('RABBITMQ_EXCHANGE_TYPE', 'topic'),

    // Queue settings
    'queues' => [
        'kitchen_events' => [
            'name' => env('RABBITMQ_KITCHEN_QUEUE', 'kitchen_events'),
            'routing_key' => 'kitchen.#',
            'durable' => true,
            'auto_delete' => false
        ],
        'order_events' => [
            'name' => env('RABBITMQ_ORDER_QUEUE', 'order_events'),
            'routing_key' => 'order.#',
            'durable' => true,
            'auto_delete' => false
        ],
        'delivery_events' => [
            'name' => env('RABBITMQ_DELIVERY_QUEUE', 'delivery_events'),
            'routing_key' => 'delivery.#',
            'durable' => true,
            'auto_delete' => false
        ],
        'customer_events' => [
            'name' => env('RABBITMQ_CUSTOMER_QUEUE', 'customer_events'),
            'routing_key' => 'customer.#',
            'durable' => true,
            'auto_delete' => false
        ],
        'quickserve_events' => [
            'name' => env('RABBITMQ_QUICKSERVE_QUEUE', 'quickserve_events'),
            'routing_key' => 'quickserve.#',
            'durable' => true,
            'auto_delete' => false
        ]
    ],

    // Routing keys
    'routing_keys' => [
        // Kitchen to Order events
        'order_prepared' => 'order.prepared',
        'all_orders_prepared' => 'order.all_prepared',
        'kitchen_updated' => 'kitchen.updated',

        // Kitchen to Customer events
        'customer_order_status_updated' => 'customer.order_status_updated',

        // Kitchen to Delivery events
        'order_ready_for_delivery' => 'delivery.order_ready',
        'delivery_time_estimated' => 'delivery.time_estimated',

        // QuickServe to Kitchen events
        'order_created' => 'order.created',
        'order_updated' => 'order.updated',
        'order_cancelled' => 'order.cancelled',

        // Delivery to Kitchen events
        'delivery_status_updated' => 'delivery.status_updated',
        'delivery_assigned' => 'delivery.assigned',
        'delivery_completed' => 'delivery.completed'
    ],

    // Dead letter exchange
    'dead_letter_exchange' => env('RABBITMQ_DEAD_LETTER_EXCHANGE', 'fooddialer.dlx'),
    'dead_letter_queue' => env('RABBITMQ_DEAD_LETTER_QUEUE', 'kitchen_dlq'),

    // Consumer settings
    'consumer' => [
        'prefetch_count' => env('RABBITMQ_CONSUMER_PREFETCH_COUNT', 10),
        'timeout' => env('RABBITMQ_CONSUMER_TIMEOUT', 3),
        'sleep_on_error' => env('RABBITMQ_CONSUMER_SLEEP_ON_ERROR', 5),
        'max_retries' => env('RABBITMQ_CONSUMER_MAX_RETRIES', 3)
    ]
];

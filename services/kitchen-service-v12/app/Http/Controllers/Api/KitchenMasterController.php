<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\KitchenMasterRequest;
use App\Http\Resources\KitchenMasterResource;
use App\Services\KitchenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class KitchenMasterController extends Controller
{
    /**
     * The kitchen service instance.
     *
     * @var \App\Services\KitchenService
     */
    protected KitchenService $kitchenService;

    /**
     * Create a new controller instance.
     */
    public function __construct(KitchenService $kitchenService)
    {
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display a listing of the kitchen masters.
     *
     * @param Request $request
     * @return AnonymousResourceCollection|JsonResponse
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $filters = $request->only(['status', 'company_id', 'unit_id']);
            $kitchenMasters = $this->kitchenService->getAllKitchenMasters($filters);

            return KitchenMasterResource::collection($kitchenMasters);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error retrieving kitchen masters: ' . $e->getMessage(), [
                'filters' => $request->only(['status', 'company_id', 'unit_id']),
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving kitchen masters: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created kitchen master in storage.
     *
     * @param KitchenMasterRequest $request
     * @return KitchenMasterResource|JsonResponse
     */
    public function store(KitchenMasterRequest $request): KitchenMasterResource|JsonResponse
    {
        try {
            $kitchenMaster = $this->kitchenService->createKitchenMaster($request->validated());

            return new KitchenMasterResource($kitchenMaster);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error creating kitchen master: ' . $e->getMessage(), [
                'data' => $request->validated(),
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error creating kitchen master: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified kitchen master.
     *
     * @param int $id
     * @return KitchenMasterResource|JsonResponse
     */
    public function show(int $id): KitchenMasterResource|JsonResponse
    {
        try {
            $kitchenMaster = $this->kitchenService->getKitchenMasterById($id);

            if (!$kitchenMaster) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kitchen master not found'
                ], 404);
            }

            return new KitchenMasterResource($kitchenMaster);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error retrieving kitchen master: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving kitchen master: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified kitchen master in storage.
     *
     * @param KitchenMasterRequest $request
     * @param int $id
     * @return KitchenMasterResource|JsonResponse
     */
    public function update(KitchenMasterRequest $request, int $id): KitchenMasterResource|JsonResponse
    {
        try {
            $kitchenMaster = $this->kitchenService->updateKitchenMaster($id, $request->validated());

            if (!$kitchenMaster) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kitchen master not found'
                ], 404);
            }

            return new KitchenMasterResource($kitchenMaster);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error updating kitchen master: ' . $e->getMessage(), [
                'id' => $id,
                'data' => $request->validated(),
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error updating kitchen master: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified kitchen master from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $result = $this->kitchenService->deleteKitchenMaster($id);

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kitchen master not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Kitchen master deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error deleting kitchen master: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error deleting kitchen master: ' . $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\KitchenPreparationResource;
use App\Services\KitchenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class KitchenPreparationController extends Controller
{
    /**
     * The kitchen service instance.
     *
     * @var \App\Services\KitchenService
     */
    protected KitchenService $kitchenService;

    /**
     * Create a new controller instance.
     */
    public function __construct(KitchenService $kitchenService)
    {
        $this->kitchenService = $kitchenService;
    }

    /**
     * Get preparation status for products.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPreparationStatus(Request $request): JsonResponse
    {
        try {
            // Handle both array and comma-separated string for product_ids
            $productIdsInput = $request->input('product_ids');

            if (is_string($productIdsInput)) {
                $productIds = array_map('intval', explode(',', $productIdsInput));
            } else {
                $productIds = $productIdsInput;
            }

            $request->merge(['product_ids' => $productIds]);

            $request->validate([
                'product_ids' => 'required|array',
                'product_ids.*' => 'required|integer',
                'kitchen_id' => 'sometimes|integer',
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            $kitchenId = $request->input('kitchen_id');
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            $preparationStatus = $this->kitchenService->getPreparationStatus($productIds, $kitchenId, $date, $menu);
            
            return response()->json([
                'success' => true,
                'data' => KitchenPreparationResource::collection($preparationStatus)
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error getting preparation status: ' . $e->getMessage(), [
                'product_ids' => $request->input('product_ids'),
                'kitchen_id' => $request->input('kitchen_id'),
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error getting preparation status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get preparation status for an order.
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function getOrderPreparationStatus(Request $request, string $orderId): JsonResponse
    {
        try {
            $request->validate([
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            $preparationStatus = $this->kitchenService->getOrderPreparationStatus($orderId, $date, $menu);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'order_id' => $orderId,
                    'date' => $date,
                    'menu' => $menu,
                    'is_fully_prepared' => $preparationStatus['is_fully_prepared'],
                    'preparation_percentage' => $preparationStatus['preparation_percentage'],
                    'items' => KitchenPreparationResource::collection($preparationStatus['items'])
                ]
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error getting order preparation status: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error getting order preparation status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get preparation summary for a date and menu.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPreparationSummary(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'kitchen_id' => 'sometimes|integer',
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            
            $kitchenId = $request->input('kitchen_id');
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            $summary = $this->kitchenService->getPreparationSummary($kitchenId, $date, $menu);
            
            return response()->json([
                'success' => true,
                'data' => $summary
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error getting preparation summary: ' . $e->getMessage(), [
                'kitchen_id' => $request->input('kitchen_id'),
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error getting preparation summary: ' . $e->getMessage()
            ], 500);
        }
    }
}

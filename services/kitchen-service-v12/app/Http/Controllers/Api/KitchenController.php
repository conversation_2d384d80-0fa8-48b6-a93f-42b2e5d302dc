<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdatePreparedRequest;
use App\Http\Resources\KitchenResource;
use App\Services\KitchenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class KitchenController extends Controller
{
    /**
     * The kitchen service instance.
     *
     * @var \App\Services\KitchenService
     */
    protected KitchenService $kitchenService;

    /**
     * Create a new controller instance.
     */
    public function __construct(KitchenService $kitchenService)
    {
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display a listing of the kitchens.
     *
     * @param Request $request
     * @return AnonymousResourceCollection|JsonResponse
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $filters = $request->only(['date', 'menu', 'kitchen_id']);
            $kitchens = $this->kitchenService->getAllKitchens($filters);

            return KitchenResource::collection($kitchens);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error retrieving kitchens: ' . $e->getMessage(), [
                'filters' => $request->only(['date', 'menu', 'kitchen_id']),
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving kitchens: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified kitchen.
     *
     * @param int $id
     * @return KitchenResource|JsonResponse
     */
    public function show(int $id): KitchenResource|JsonResponse
    {
        try {
            $kitchen = $this->kitchenService->getKitchenById($id);

            if (!$kitchen) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kitchen not found'
                ], 404);
            }

            return new KitchenResource($kitchen);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error retrieving kitchen: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving kitchen: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the prepared count for the specified kitchen.
     *
     * @param UpdatePreparedRequest $request
     * @param int $id
     * @return KitchenResource|JsonResponse
     */
    public function updatePrepared(UpdatePreparedRequest $request, int $id): KitchenResource|JsonResponse
    {
        try {
            $kitchen = $this->kitchenService->updatePrepared($id, $request->validated());

            if (!$kitchen) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating prepared count'
                ], 500);
            }

            return new KitchenResource($kitchen);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error updating prepared count: ' . $e->getMessage(), [
                'id' => $id,
                'data' => $request->validated(),
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error updating prepared count: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update all prepared count for the specified kitchen.
     *
     * @param UpdatePreparedRequest $request
     * @param int $id
     * @return KitchenResource|JsonResponse
     */
    public function updateAllPrepared(UpdatePreparedRequest $request, int $id): KitchenResource|JsonResponse
    {
        try {
            $kitchen = $this->kitchenService->updateAllPrepared($id, $request->validated());

            if (!$kitchen) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating all prepared count'
                ], 500);
            }

            return new KitchenResource($kitchen);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error updating all prepared count: ' . $e->getMessage(), [
                'id' => $id,
                'data' => $request->validated(),
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error updating all prepared count: ' . $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Prometheus\CollectorRegistry;
use Prometheus\RenderTextFormat;
use Prometheus\Storage\APC;

class MetricsController extends Controller
{
    /**
     * The Prometheus registry.
     *
     * @var CollectorRegistry
     */
    protected $registry;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->registry = new CollectorRegistry(new APC());
    }

    /**
     * Export metrics in Prometheus format.
     *
     * @return Response
     */
    public function export(): Response
    {
        // HTTP request duration metrics
        $httpDuration = $this->registry->getOrRegisterHistogram(
            'http',
            'request_duration_seconds',
            'HTTP request duration in seconds',
            ['handler', 'method', 'status'],
            [0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]
        );

        // HTTP request counter
        $httpRequests = $this->registry->getOrRegisterCounter(
            'http',
            'requests_total',
            'Total number of HTTP requests',
            ['handler', 'method', 'status']
        );

        // Kitchen-specific metrics
        $mealPreparationTime = $this->registry->getOrRegisterHistogram(
            'kitchen',
            'meal_preparation_time_seconds',
            'Meal preparation time in seconds',
            ['meal_type'],
            [60, 120, 180, 240, 300, 600, 900, 1800, 3600]
        );

        $mealPreparationCount = $this->registry->getOrRegisterCounter(
            'kitchen',
            'meal_preparation_total',
            'Total number of meals prepared',
            ['meal_type', 'status']
        );

        $equipmentUtilization = $this->registry->getOrRegisterGauge(
            'kitchen',
            'equipment_utilization',
            'Equipment utilization percentage',
            ['equipment_type']
        );

        // Circuit breaker metrics
        $circuitBreakerState = $this->registry->getOrRegisterGauge(
            'circuit_breaker',
            'state',
            'Circuit breaker state (0 = closed, 1 = open)',
            ['service']
        );

        // Cache metrics
        $cacheHitRatio = $this->registry->getOrRegisterGauge(
            'cache',
            'hit_ratio',
            'Cache hit ratio',
            []
        );

        // Database metrics
        $dbQueryDuration = $this->registry->getOrRegisterHistogram(
            'db',
            'query_duration_seconds',
            'Database query duration in seconds',
            ['query_type'],
            [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1]
        );

        // Collect metrics from cache
        $this->collectCacheMetrics($cacheHitRatio);

        // Collect circuit breaker metrics
        $this->collectCircuitBreakerMetrics($circuitBreakerState);

        // Collect kitchen-specific metrics
        $this->collectKitchenMetrics($mealPreparationTime, $mealPreparationCount, $equipmentUtilization);

        // Render metrics
        $renderer = new RenderTextFormat();
        $result = $renderer->render($this->registry->getMetricFamilySamples());

        return response($result, 200)
            ->header('Content-Type', RenderTextFormat::MIME_TYPE);
    }

    /**
     * Collect cache metrics.
     *
     * @param \Prometheus\Gauge $cacheHitRatio
     * @return void
     */
    protected function collectCacheMetrics($cacheHitRatio): void
    {
        $hits = Cache::get('cache_hits', 0);
        $misses = Cache::get('cache_misses', 0);
        
        $total = $hits + $misses;
        $ratio = $total > 0 ? $hits / $total : 0;
        
        $cacheHitRatio->set($ratio * 100);
    }

    /**
     * Collect circuit breaker metrics.
     *
     * @param \Prometheus\Gauge $circuitBreakerState
     * @return void
     */
    protected function collectCircuitBreakerMetrics($circuitBreakerState): void
    {
        $services = [
            'order_service',
            'delivery_service',
            'catalogue_service',
        ];

        foreach ($services as $service) {
            $cacheKey = 'circuit_breaker:' . $service;
            $state = Cache::get($cacheKey, ['open' => false]);
            
            $circuitBreakerState->set($state['open'] ? 1 : 0, [$service]);
        }
    }

    /**
     * Collect kitchen-specific metrics.
     *
     * @param \Prometheus\Histogram $mealPreparationTime
     * @param \Prometheus\Counter $mealPreparationCount
     * @param \Prometheus\Gauge $equipmentUtilization
     * @return void
     */
    protected function collectKitchenMetrics($mealPreparationTime, $mealPreparationCount, $equipmentUtilization): void
    {
        // Get meal preparation statistics from the database
        $mealStats = DB::table('meal_preparations')
            ->select(DB::raw('meal_type, status, AVG(preparation_time) as avg_time, COUNT(*) as count'))
            ->whereDate('created_at', '>=', now()->subDay())
            ->groupBy('meal_type', 'status')
            ->get();
        
        foreach ($mealStats as $stat) {
            $mealPreparationCount->set($stat->count, [$stat->meal_type, $stat->status]);
            
            // Only set preparation time for completed meals
            if ($stat->status === 'completed') {
                $mealPreparationTime->observe($stat->avg_time, [$stat->meal_type]);
            }
        }
        
        // Get equipment utilization from the database
        $equipmentStats = DB::table('kitchen_equipment')
            ->select('type', 'utilization_percentage')
            ->get();
        
        foreach ($equipmentStats as $stat) {
            $equipmentUtilization->set($stat->utilization_percentage, [$stat->type]);
        }
    }
}

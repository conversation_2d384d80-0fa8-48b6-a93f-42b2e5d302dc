<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\DeliveryEstimationService;
use App\Services\KitchenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeliveryIntegrationController extends Controller
{
    /**
     * The kitchen service instance.
     *
     * @var \App\Services\KitchenService
     */
    protected KitchenService $kitchenService;

    /**
     * The delivery estimation service instance.
     *
     * @var \App\Services\DeliveryEstimationService
     */
    protected DeliveryEstimationService $deliveryEstimationService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        KitchenService $kitchenService,
        DeliveryEstimationService $deliveryEstimationService
    ) {
        $this->kitchenService = $kitchenService;
        $this->deliveryEstimationService = $deliveryEstimationService;
    }

    /**
     * Get preparation status for an order.
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function getOrderPreparationStatus(Request $request, string $orderId): JsonResponse
    {
        try {
            $request->validate([
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            $preparationStatus = $this->kitchenService->getOrderPreparationStatus($orderId, $date, $menu);
            
            return response()->json([
                'success' => true,
                'data' => $preparationStatus
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error getting order preparation status for delivery: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error getting order preparation status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Estimate delivery time for an order.
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function estimateDeliveryTime(Request $request, string $orderId): JsonResponse
    {
        try {
            $request->validate([
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            $estimatedDeliveryTime = $this->deliveryEstimationService->estimateDeliveryTime($orderId, $date, $menu);
            
            return response()->json([
                'success' => true,
                'data' => $estimatedDeliveryTime
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error estimating delivery time: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error estimating delivery time: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Notify kitchen about delivery status update.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function notifyDeliveryStatusUpdate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_id' => 'required|string',
                'status' => 'required|string',
                'timestamp' => 'sometimes|date_format:Y-m-d H:i:s',
                'delivery_agent' => 'sometimes|array'
            ]);
            
            // Log the delivery status update
            Log::channel('kitchen')->info('Delivery status update received', [
                'order_id' => $request->input('order_id'),
                'status' => $request->input('status'),
                'timestamp' => $request->input('timestamp'),
                'delivery_agent' => $request->input('delivery_agent')
            ]);
            
            // In a real implementation, we might update some kitchen records
            // based on the delivery status, such as marking items as delivered
            // or updating delivery-related fields in the kitchen records.
            
            // For now, we'll just log the event and return a success response.
            
            return response()->json([
                'success' => true,
                'message' => 'Delivery status update received'
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error processing delivery status update: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error processing delivery status update: ' . $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\KitchenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CustomerIntegrationController extends Controller
{
    /**
     * The kitchen service instance.
     *
     * @var \App\Services\KitchenService
     */
    protected KitchenService $kitchenService;

    /**
     * Create a new controller instance.
     */
    public function __construct(KitchenService $kitchenService)
    {
        $this->kitchenService = $kitchenService;
    }

    /**
     * Get preparation status for an order.
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function getOrderPreparationStatus(Request $request, string $orderId): JsonResponse
    {
        try {
            $request->validate([
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            $preparationStatus = $this->kitchenService->getOrderPreparationStatus($orderId, $date, $menu);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'order_id' => $orderId,
                    'date' => $date,
                    'menu' => $menu,
                    'is_fully_prepared' => $preparationStatus['is_fully_prepared'],
                    'preparation_percentage' => $preparationStatus['preparation_percentage'],
                    'status' => $preparationStatus['is_fully_prepared'] ? 'ready_for_delivery' : 'preparing'
                ]
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error getting order preparation status for customer: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error getting order preparation status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get preparation status for multiple orders.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getMultipleOrdersPreparationStatus(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_ids' => 'required|array',
                'order_ids.*' => 'required|string',
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            
            $orderIds = $request->input('order_ids');
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            $result = [];
            
            foreach ($orderIds as $orderId) {
                $preparationStatus = $this->kitchenService->getOrderPreparationStatus($orderId, $date, $menu);
                
                $result[$orderId] = [
                    'date' => $date,
                    'menu' => $menu,
                    'is_fully_prepared' => $preparationStatus['is_fully_prepared'],
                    'preparation_percentage' => $preparationStatus['preparation_percentage'],
                    'status' => $preparationStatus['is_fully_prepared'] ? 'ready_for_delivery' : 'preparing'
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error getting multiple orders preparation status: ' . $e->getMessage(), [
                'order_ids' => $request->input('order_ids'),
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error getting multiple orders preparation status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get preparation summary for a customer.
     *
     * @param Request $request
     * @param string $customerId
     * @return JsonResponse
     */
    public function getCustomerPreparationSummary(Request $request, string $customerId): JsonResponse
    {
        try {
            $request->validate([
                'date' => 'sometimes|date_format:Y-m-d',
                'menu' => 'sometimes|string'
            ]);
            
            $date = $request->input('date', date('Y-m-d'));
            $menu = $request->input('menu', 'lunch');
            
            // In a real implementation, we would fetch the customer's orders
            // and then get the preparation status for each order.
            // For now, we'll return a mock response.
            
            return response()->json([
                'success' => true,
                'data' => [
                    'customer_id' => $customerId,
                    'date' => $date,
                    'menu' => $menu,
                    'total_orders' => 2,
                    'prepared_orders' => 1,
                    'preparation_percentage' => 50,
                    'orders' => [
                        [
                            'order_id' => 'ORD-001',
                            'is_fully_prepared' => true,
                            'preparation_percentage' => 100,
                            'status' => 'ready_for_delivery'
                        ],
                        [
                            'order_id' => 'ORD-002',
                            'is_fully_prepared' => false,
                            'preparation_percentage' => 50,
                            'status' => 'preparing'
                        ]
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::channel('api')->error('Error getting customer preparation summary: ' . $e->getMessage(), [
                'customer_id' => $customerId,
                'date' => $request->input('date'),
                'menu' => $request->input('menu'),
                'exception' => $e
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error getting customer preparation summary: ' . $e->getMessage()
            ], 500);
        }
    }
}

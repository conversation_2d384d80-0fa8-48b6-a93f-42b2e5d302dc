<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class HealthController extends Controller
{
    /**
     * Check the health of the service.
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $status = 'ok';
        $dbStatus = 'ok';
        
        try {
            // Check database connection
            DB::connection()->getPdo();
        } catch (\Exception $e) {
            $status = 'error';
            $dbStatus = 'error: ' . $e->getMessage();
        }
        
        return response()->json([
            'status' => $status,
            'service' => 'kitchen-service',
            'version' => config('app.version', '1.0.0'),
            'timestamp' => now()->toIso8601String(),
            'database' => $dbStatus
        ]);
    }
}

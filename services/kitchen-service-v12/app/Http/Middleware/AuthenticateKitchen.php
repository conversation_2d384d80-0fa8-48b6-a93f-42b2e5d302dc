<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateKitchen
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->bearerToken();

        if (!$token) {
            throw new AuthenticationException('Unauthenticated.');
        }

        try {
            // Validate token with Auth Service
            $response = Http::withToken($token)
                ->get(config('services.auth.url') . '/api/v2/auth/validate');

            if (!$response->successful()) {
                throw new AuthenticationException('Invalid token.');
            }

            $userData = $response->json('data');

            // Check if user has kitchen access
            if (!$this->hasKitchenAccess($userData)) {
                throw new AuthenticationException('Unauthorized access to kitchen.');
            }

            // Add user data to request
            $request->merge(['user' => $userData]);

            return $next($request);
        } catch (\Exception $e) {
            throw new AuthenticationException('Authentication failed: ' . $e->getMessage());
        }
    }

    /**
     * Check if user has kitchen access.
     *
     * @param array $userData
     * @return bool
     */
    private function hasKitchenAccess(array $userData): bool
    {
        // Check if user has kitchen role or permission
        if (isset($userData['roles']) && is_array($userData['roles'])) {
            $kitchenRoles = [
                'kitchen',
                'kitchen_staff',
                'kitchen_manager',
                'chef',
                'head_chef',
                'sous_chef',
                'kitchen_supervisor',
                'admin'
            ];

            foreach ($userData['roles'] as $role) {
                if (in_array($role, $kitchenRoles)) {
                    return true;
                }
            }
        }

        // Check if user has kitchen permissions
        if (isset($userData['permissions']) && is_array($userData['permissions'])) {
            $kitchenPermissions = [
                'kitchen.access',
                'kitchen.view',
                'kitchen.update',
                'kitchen.manage',
                'kitchen.prepare',
                'kitchen.recipe.view',
                'kitchen.recipe.manage',
                'kitchen.master.view',
                'kitchen.master.manage'
            ];

            foreach ($userData['permissions'] as $permission) {
                if (in_array($permission, $kitchenPermissions)) {
                    return true;
                }
            }
        }

        return false;
    }
}

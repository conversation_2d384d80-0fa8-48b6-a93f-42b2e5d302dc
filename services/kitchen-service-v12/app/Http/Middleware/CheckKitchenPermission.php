<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckKitchenPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  ...$permissions
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        if (!$request->has('user')) {
            throw new AuthorizationException('User data not found in request.');
        }

        $userData = $request->input('user');

        if (!isset($userData['permissions']) || !is_array($userData['permissions'])) {
            throw new AuthorizationException('User permissions not found.');
        }

        $userPermissions = $userData['permissions'];

        foreach ($permissions as $permission) {
            if (in_array($permission, $userPermissions)) {
                return $next($request);
            }
        }

        throw new AuthorizationException('Unauthorized. Required permissions: ' . implode(', ', $permissions));
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckKitchenRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  ...$roles
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        if (!$request->has('user')) {
            throw new AuthorizationException('User data not found in request.');
        }

        $userData = $request->input('user');

        if (!isset($userData['roles']) || !is_array($userData['roles'])) {
            throw new AuthorizationException('User roles not found.');
        }

        $userRoles = $userData['roles'];

        foreach ($roles as $role) {
            if (in_array($role, $userRoles)) {
                return $next($request);
            }
        }

        throw new AuthorizationException('Unauthorized. Required roles: ' . implode(', ', $roles));
    }
}

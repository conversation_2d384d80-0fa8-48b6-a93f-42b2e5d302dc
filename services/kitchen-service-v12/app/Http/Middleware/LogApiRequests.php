<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogApiRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log the request
        Log::channel('api')->info('API Request', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'headers' => $this->filterHeaders($request->headers->all()),
            'params' => $this->filterParams($request->all()),
        ]);

        // Get the response
        $response = $next($request);

        // Log the response
        Log::channel('api')->info('API Response', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'status' => $response->getStatusCode(),
            'duration' => round(microtime(true) - LARAVEL_START, 3),
        ]);

        return $response;
    }

    /**
     * Filter sensitive headers.
     *
     * @param array $headers
     * @return array
     */
    private function filterHeaders(array $headers): array
    {
        $sensitiveHeaders = [
            'authorization',
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
        ];

        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['<redacted>'];
            }
        }

        return $headers;
    }

    /**
     * Filter sensitive parameters.
     *
     * @param array $params
     * @return array
     */
    private function filterParams(array $params): array
    {
        $sensitiveParams = [
            'password',
            'password_confirmation',
            'token',
            'access_token',
            'refresh_token',
            'credit_card',
            'card_number',
            'cvv',
        ];

        foreach ($sensitiveParams as $param) {
            if (isset($params[$param])) {
                $params[$param] = '<redacted>';
            }
        }

        return $params;
    }
}

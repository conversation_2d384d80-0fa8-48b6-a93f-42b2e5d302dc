<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class KitchenMasterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
            'kitchen_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('kitchen_masters')->ignore($this->route('kitchen_master'), 'pk_kitchen_code')
            ],
            'kitchen_alias' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'location_id' => 'nullable|integer',
            'city_id' => 'nullable|integer',
            'base_kitchen' => 'sometimes|boolean',
            'kitchen_address' => 'nullable|string',
            'created_by' => 'sometimes|integer',
            'updated_by' => 'sometimes|integer',
            'status' => 'sometimes|boolean',
        ];
        
        return $rules;
    }
}

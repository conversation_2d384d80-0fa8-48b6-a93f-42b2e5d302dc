<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KitchenPreparationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_kitchen_code,
            'product_id' => $this->fk_product_code,
            'kitchen_id' => $this->fk_kitchen_code,
            'total_order' => $this->total_order,
            'prepared' => $this->prepared,
            'remaining' => $this->total_order - $this->prepared,
            'preparation_percentage' => $this->total_order > 0 ? round(($this->prepared / $this->total_order) * 100, 2) : 100,
            'is_fully_prepared' => $this->prepared >= $this->total_order,
            'date' => $this->date->format('Y-m-d'),
            'menu' => $this->order_menu,
            'product' => $this->whenLoaded('product', function () {
                return [
                    'id' => $this->product->pk_product_code,
                    'name' => $this->product->name,
                    'kitchen_code' => $this->product->kitchen_code,
                ];
            }),
            'kitchen_master' => $this->whenLoaded('kitchenMaster', function () {
                return [
                    'id' => $this->kitchenMaster->pk_kitchen_code,
                    'name' => $this->kitchenMaster->kitchen_name,
                    'alias' => $this->kitchenMaster->kitchen_alias,
                    'location' => $this->kitchenMaster->location,
                ];
            }),
        ];
    }
}

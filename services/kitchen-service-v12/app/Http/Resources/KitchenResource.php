<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KitchenResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_kitchen_code,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'product_id' => $this->fk_product_code,
            'kitchen_id' => $this->fk_kitchen_code,
            'total_order' => $this->total_order,
            'prepared' => $this->prepared,
            'remaining_orders' => $this->remaining_orders,
            'date' => $this->date->format('Y-m-d'),
            'order_menu' => $this->order_menu,
            'status' => $this->status,
            'product' => $this->whenLoaded('product', function () {
                return [
                    'id' => $this->product->pk_product_code,
                    'name' => $this->product->name,
                    'kitchen_code' => $this->product->kitchen_code,
                    'quantity' => $this->product->quantity,
                    'unit' => $this->product->unit,
                    'screen' => $this->product->screen,
                ];
            }),
            'kitchen_master' => $this->whenLoaded('kitchenMaster', function () {
                return [
                    'id' => $this->kitchenMaster->pk_kitchen_code,
                    'name' => $this->kitchenMaster->kitchen_name,
                    'alias' => $this->kitchenMaster->kitchen_alias,
                    'location' => $this->kitchenMaster->location,
                ];
            }),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}

<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KitchenMasterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_kitchen_code,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'kitchen_name' => $this->kitchen_name,
            'kitchen_alias' => $this->kitchen_alias,
            'location' => $this->location,
            'location_id' => $this->location_id,
            'city_id' => $this->city_id,
            'base_kitchen' => $this->base_kitchen,
            'kitchen_address' => $this->kitchen_address,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'status' => $this->status,
            'kitchens' => $this->whenLoaded('kitchens', function () {
                return KitchenResource::collection($this->kitchens);
            }),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}

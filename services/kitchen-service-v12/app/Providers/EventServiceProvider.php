<?php

namespace App\Providers;

use App\Events\AllOrdersPrepared;
use App\Events\CustomerOrderStatusUpdated;
use App\Events\DeliveryStatusUpdated;
use App\Events\OrderCancelled;
use App\Events\OrderCreated;
use App\Events\OrderPrepared;
use App\Events\OrderUpdated;
use App\Listeners\HandleDeliveryStatusUpdated;
use App\Listeners\HandleOrderCancelled;
use App\Listeners\HandleOrderCreated;
use App\Listeners\HandleOrderUpdated;
use App\Listeners\PublishAllOrdersPreparedEvent;
use App\Listeners\PublishCustomerOrderStatusUpdated;
use App\Listeners\PublishOrderPreparedEvent;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Kitchen events
        OrderPrepared::class => [
            PublishOrderPreparedEvent::class,
        ],
        AllOrdersPrepared::class => [
            PublishAllOrdersPreparedEvent::class,
        ],

        // Customer events
        CustomerOrderStatusUpdated::class => [
            PublishCustomerOrderStatusUpdated::class,
        ],

        // QuickServe events
        OrderCreated::class => [
            HandleOrderCreated::class,
        ],
        OrderUpdated::class => [
            HandleOrderUpdated::class,
        ],
        OrderCancelled::class => [
            HandleOrderCancelled::class,
        ],

        // Delivery events
        DeliveryStatusUpdated::class => [
            HandleDeliveryStatusUpdated::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}

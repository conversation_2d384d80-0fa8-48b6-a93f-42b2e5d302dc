<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class KitchenMaster extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'kitchen_masters';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_kitchen_code';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'kitchen_name',
        'kitchen_alias',
        'location',
        'location_id',
        'city_id',
        'base_kitchen',
        'kitchen_address',
        'created_by',
        'updated_by',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the kitchens for the kitchen master.
     */
    public function kitchens(): HasMany
    {
        return $this->hasMany(Kitchen::class, 'fk_kitchen_code', 'pk_kitchen_code');
    }

    /**
     * Get the user kitchens for the kitchen master.
     */
    public function userKitchens(): HasMany
    {
        return $this->hasMany(UserKitchen::class, 'fk_kitchen_code', 'pk_kitchen_code');
    }

    /**
     * Scope a query to only include active kitchen masters.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to filter by company.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $companyId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to filter by unit.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Kitchen extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'kitchens';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_kitchen_code';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'fk_product_code',
        'fk_kitchen_code',
        'total_order',
        'prepared',
        'date',
        'order_menu',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total_order' => 'integer',
        'prepared' => 'integer',
        'date' => 'date',
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the product that owns the kitchen.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'fk_product_code', 'pk_product_code');
    }

    /**
     * Get the kitchen master that owns the kitchen.
     */
    public function kitchenMaster(): BelongsTo
    {
        return $this->belongsTo(KitchenMaster::class, 'fk_kitchen_code', 'pk_kitchen_code');
    }

    /**
     * Get the remaining orders to be prepared.
     *
     * @return int
     */
    public function getRemainingOrdersAttribute(): int
    {
        return $this->total_order - $this->prepared;
    }

    /**
     * Scope a query to filter by date.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $date
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * Scope a query to filter by menu.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $menu
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByMenu($query, $menu)
    {
        return $query->where('order_menu', $menu);
    }

    /**
     * Scope a query to filter by kitchen.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $kitchenId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByKitchen($query, $kitchenId)
    {
        return $query->where('fk_kitchen_code', $kitchenId);
    }
}

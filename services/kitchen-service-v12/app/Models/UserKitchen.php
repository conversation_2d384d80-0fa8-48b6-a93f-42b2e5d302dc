<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserKitchen extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_kitchens';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'fk_user_code',
        'fk_kitchen_code',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the kitchen master that owns the user kitchen.
     */
    public function kitchenMaster(): BelongsTo
    {
        return $this->belongsTo(KitchenMaster::class, 'fk_kitchen_code', 'pk_kitchen_code');
    }

    /**
     * Scope a query to only include active user kitchens.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to filter by user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('fk_user_code', $userId);
    }

    /**
     * Scope a query to filter by kitchen.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $kitchenId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByKitchen($query, $kitchenId)
    {
        return $query->where('fk_kitchen_code', $kitchenId);
    }
}

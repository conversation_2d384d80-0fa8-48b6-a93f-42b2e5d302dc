<?php

namespace App\Console\Commands;

use App\Events\OrderCancelled;
use App\Events\OrderCreated;
use App\Events\OrderUpdated;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class ConsumeQuickServeEvents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:consume-quickserve-events';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Consume QuickServe events from RabbitMQ';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting QuickServe events consumer...');
        
        try {
            $connection = new AMQPStreamConnection(
                config('rabbitmq.host'),
                config('rabbitmq.port'),
                config('rabbitmq.user'),
                config('rabbitmq.password'),
                config('rabbitmq.vhost')
            );
            
            $channel = $connection->channel();
            
            // Declare exchange
            $exchange = config('rabbitmq.exchange');
            $channel->exchange_declare(
                $exchange,
                config('rabbitmq.exchange_type'),
                false,
                true,
                false
            );
            
            // Declare queue for QuickServe events
            $queueName = config('rabbitmq.queues.quickserve_events.name');
            $channel->queue_declare(
                $queueName,
                false,
                true,
                false,
                false
            );
            
            // Bind queue to exchange with routing keys
            $routingKeys = [
                'order.created',
                'order.updated',
                'order.cancelled'
            ];
            
            foreach ($routingKeys as $routingKey) {
                $channel->queue_bind($queueName, $exchange, $routingKey);
            }
            
            $this->info("Bound queue '{$queueName}' to exchange '{$exchange}' with routing keys: " . implode(', ', $routingKeys));
            
            // Set QoS
            $channel->basic_qos(null, 1, null);
            
            // Define callback
            $callback = function (AMQPMessage $message) {
                try {
                    $body = json_decode($message->getBody(), true);
                    $routingKey = $message->getRoutingKey();
                    
                    $this->info("Received message with routing key: {$routingKey}");
                    
                    switch ($routingKey) {
                        case 'order.created':
                            event(new OrderCreated($body));
                            break;
                        case 'order.updated':
                            event(new OrderUpdated($body));
                            break;
                        case 'order.cancelled':
                            event(new OrderCancelled($body));
                            break;
                        default:
                            $this->warn("Unknown routing key: {$routingKey}");
                            break;
                    }
                    
                    // Acknowledge message
                    $message->ack();
                } catch (\Exception $e) {
                    // Log error
                    Log::channel('rabbitmq')->error('Error processing QuickServe event: ' . $e->getMessage(), [
                        'routing_key' => $message->getRoutingKey(),
                        'body' => $message->getBody(),
                        'exception' => $e
                    ]);
                    
                    // Reject message and requeue
                    $message->reject(true);
                    
                    $this->error('Error processing message: ' . $e->getMessage());
                }
            };
            
            // Consume messages
            $channel->basic_consume(
                $queueName,
                '',
                false,
                false,
                false,
                false,
                $callback
            );
            
            $this->info("Waiting for messages. To exit press CTRL+C");
            
            // Keep consuming until the channel is closed
            while ($channel->is_consuming()) {
                $channel->wait();
            }
            
            // Close connection
            $channel->close();
            $connection->close();
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::channel('rabbitmq')->error('Error in QuickServe events consumer: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            
            $this->error('Error: ' . $e->getMessage());
            
            return Command::FAILURE;
        }
    }
}

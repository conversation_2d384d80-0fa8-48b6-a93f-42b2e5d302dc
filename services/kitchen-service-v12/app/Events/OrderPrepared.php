<?php

namespace App\Events;

use App\Models\Kitchen;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderPrepared
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The kitchen instance.
     *
     * @var \App\Models\Kitchen
     */
    public Kitchen $kitchen;

    /**
     * Create a new event instance.
     */
    public function __construct(Kitchen $kitchen)
    {
        $this->kitchen = $kitchen;
    }
}

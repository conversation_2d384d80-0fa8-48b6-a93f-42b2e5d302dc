<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DeliveryStatusUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The delivery data.
     *
     * @var array
     */
    public array $deliveryData;

    /**
     * Create a new event instance.
     *
     * @param array $deliveryData
     */
    public function __construct(array $deliveryData)
    {
        $this->deliveryData = $deliveryData;
    }
}

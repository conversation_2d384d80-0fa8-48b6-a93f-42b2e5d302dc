<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CustomerOrderStatusUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The order data.
     *
     * @var array
     */
    public array $orderData;

    /**
     * Create a new event instance.
     *
     * @param array $orderData
     */
    public function __construct(array $orderData)
    {
        $this->orderData = $orderData;
    }
}

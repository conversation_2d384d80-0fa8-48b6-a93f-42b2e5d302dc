<?php

namespace App\Services;

use App\Models\Kitchen;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DeliveryEstimationService
{
    /**
     * The kitchen service instance.
     *
     * @var \App\Services\KitchenService
     */
    protected KitchenService $kitchenService;

    /**
     * Create a new service instance.
     *
     * @param KitchenService $kitchenService
     */
    public function __construct(KitchenService $kitchenService)
    {
        $this->kitchenService = $kitchenService;
    }

    /**
     * Estimate delivery time for an order.
     *
     * @param string $orderId
     * @param string $date
     * @param string $menu
     * @return array
     */
    public function estimateDeliveryTime(string $orderId, string $date = null, string $menu = 'lunch'): array
    {
        try {
            $date = $date ?? date('Y-m-d');
            
            // Get order preparation status
            $preparationStatus = $this->kitchenService->getOrderPreparationStatus($orderId, $date, $menu);
            
            // Calculate preparation time
            $preparationTime = $this->calculatePreparationTime($preparationStatus);
            
            // Get delivery time from Delivery service
            $deliveryTime = $this->getDeliveryTimeFromDeliveryService($orderId);
            
            // Calculate total estimated time
            $totalEstimatedTime = $preparationTime + $deliveryTime;
            
            // Calculate estimated delivery time
            $estimatedDeliveryTime = date('Y-m-d H:i:s', strtotime("+{$totalEstimatedTime} minutes"));
            
            return [
                'order_id' => $orderId,
                'preparation_time_minutes' => $preparationTime,
                'delivery_time_minutes' => $deliveryTime,
                'total_estimated_time_minutes' => $totalEstimatedTime,
                'estimated_delivery_time' => $estimatedDeliveryTime,
                'preparation_status' => $preparationStatus
            ];
        } catch (\Exception $e) {
            Log::channel('kitchen')->error('Error estimating delivery time: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'date' => $date,
                'menu' => $menu,
                'exception' => $e
            ]);
            
            return [
                'order_id' => $orderId,
                'preparation_time_minutes' => 0,
                'delivery_time_minutes' => 0,
                'total_estimated_time_minutes' => 0,
                'estimated_delivery_time' => null,
                'error' => 'Error estimating delivery time: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Calculate preparation time based on preparation status.
     *
     * @param array $preparationStatus
     * @return int
     */
    private function calculatePreparationTime(array $preparationStatus): int
    {
        // If order is fully prepared, return 0
        if ($preparationStatus['is_fully_prepared']) {
            return 0;
        }
        
        // Calculate average preparation time per item
        $averagePreparationTimePerItem = 5; // 5 minutes per item
        
        // Calculate remaining items to prepare
        $remainingItems = 0;
        foreach ($preparationStatus['items'] as $item) {
            if ($item->total_order > $item->prepared) {
                $remainingItems += ($item->total_order - $item->prepared);
            }
        }
        
        // Calculate total preparation time
        $preparationTime = $remainingItems * $averagePreparationTimePerItem;
        
        // Cap preparation time at 60 minutes
        return min($preparationTime, 60);
    }

    /**
     * Get delivery time from Delivery service.
     *
     * @param string $orderId
     * @return int
     */
    private function getDeliveryTimeFromDeliveryService(string $orderId): int
    {
        try {
            // In a real implementation, we would use a HTTP client to call the Delivery service API.
            // For now, we'll return a mock response.
            
            // TODO: Implement actual API call to Delivery service
            
            // Mock response - random delivery time between 15 and 45 minutes
            return rand(15, 45);
        } catch (\Exception $e) {
            Log::channel('kitchen')->error('Error getting delivery time from Delivery service: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'exception' => $e
            ]);
            
            // Default delivery time
            return 30;
        }
    }
}

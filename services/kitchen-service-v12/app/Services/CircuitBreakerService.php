<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CircuitBreakerService
{
    /**
     * The failure threshold.
     *
     * @var int
     */
    protected int $failureThreshold;

    /**
     * The recovery timeout in seconds.
     *
     * @var int
     */
    protected int $recoveryTimeout;

    /**
     * Create a new service instance.
     */
    public function __construct()
    {
        $this->failureThreshold = config('circuit_breaker.failure_threshold', 5);
        $this->recoveryTimeout = config('circuit_breaker.recovery_timeout', 30);
    }

    /**
     * Execute a callback with circuit breaker protection.
     *
     * @param string $service
     * @param callable $callback
     * @param mixed $fallback
     * @return mixed
     */
    public function execute(string $service, callable $callback, mixed $fallback = null): mixed
    {
        $circuitKey = "circuit_breaker:{$service}";
        $failureCountKey = "circuit_breaker:{$service}:failures";
        $lastFailureKey = "circuit_breaker:{$service}:last_failure";

        // Check if circuit is open
        if (Cache::has($circuitKey) && Cache::get($circuitKey) === 'open') {
            $lastFailure = Cache::get($lastFailureKey, 0);
            $timeSinceLastFailure = time() - $lastFailure;

            // If recovery timeout has passed, allow a trial request
            if ($timeSinceLastFailure >= $this->recoveryTimeout) {
                Cache::put($circuitKey, 'half-open', $this->recoveryTimeout);
            } else {
                Log::warning("Circuit breaker open for service: {$service}. Using fallback.");
                return $fallback;
            }
        }

        try {
            // Execute the callback
            $result = $callback();

            // If successful and circuit was half-open, close it
            if (Cache::get($circuitKey) === 'half-open') {
                Cache::forget($circuitKey);
                Cache::forget($failureCountKey);
                Cache::forget($lastFailureKey);
                Log::info("Circuit breaker closed for service: {$service}");
            }

            return $result;
        } catch (\Exception $e) {
            // Increment failure count
            $failureCount = Cache::increment($failureCountKey, 1, 0);
            Cache::put($lastFailureKey, time(), 3600);

            // If failure count exceeds threshold, open the circuit
            if ($failureCount >= $this->failureThreshold) {
                Cache::put($circuitKey, 'open', $this->recoveryTimeout * 2);
                Log::error("Circuit breaker opened for service: {$service}. Failure count: {$failureCount}");
            }

            Log::error("Service call failed: {$service}", [
                'exception' => $e->getMessage(),
                'failure_count' => $failureCount,
                'circuit_state' => Cache::get($circuitKey, 'closed')
            ]);

            // Return fallback or rethrow exception
            if ($fallback !== null) {
                return $fallback;
            }

            throw $e;
        }
    }
}

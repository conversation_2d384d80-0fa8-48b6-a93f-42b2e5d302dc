<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class RabbitMQService
{
    /**
     * @var \PhpAmqpLib\Connection\AMQPStreamConnection
     */
    private AMQPStreamConnection $connection;

    /**
     * @var \PhpAmqpLib\Channel\AMQPChannel
     */
    private AMQPChannel $channel;

    /**
     * @var string
     */
    private string $exchange;

    /**
     * Create a new RabbitMQ service instance.
     */
    public function __construct()
    {
        try {
            $this->connection = new AMQPStreamConnection(
                config('rabbitmq.host', 'localhost'),
                config('rabbitmq.port', 5672),
                config('rabbitmq.user', 'guest'),
                config('rabbitmq.password', 'guest'),
                config('rabbitmq.vhost', '/')
            );

            $this->channel = $this->connection->channel();

            $this->exchange = config('rabbitmq.exchange', 'fooddialer');

            // Declare exchange
            $this->channel->exchange_declare(
                $this->exchange,
                config('rabbitmq.exchange_type', 'topic'),
                false,
                true,
                false
            );

            // Declare queues
            foreach (config('rabbitmq.queues', []) as $queue) {
                $this->channel->queue_declare(
                    $queue['name'],
                    false,
                    $queue['durable'] ?? true,
                    false,
                    $queue['auto_delete'] ?? false
                );

                $this->channel->queue_bind(
                    $queue['name'],
                    $this->exchange,
                    $queue['routing_key']
                );
            }
        } catch (\Exception $e) {
            Log::channel('rabbitmq')->error('Error initializing RabbitMQ connection: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Publish message to RabbitMQ.
     *
     * @param string $routingKey
     * @param array $data
     * @return bool
     */
    public function publish(string $routingKey, array $data): bool
    {
        try {
            $message = new AMQPMessage(
                json_encode($data),
                [
                    'content_type' => 'application/json',
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT
                ]
            );

            $this->channel->basic_publish($message, $this->exchange, $routingKey);

            Log::channel('rabbitmq')->info('Message published to RabbitMQ', [
                'routing_key' => $routingKey,
                'data' => $data
            ]);

            return true;
        } catch (\Exception $e) {
            Log::channel('rabbitmq')->error('Error publishing message to RabbitMQ: ' . $e->getMessage(), [
                'routing_key' => $routingKey,
                'data' => $data
            ]);

            return false;
        }
    }

    /**
     * Close RabbitMQ connection.
     */
    public function __destruct()
    {
        try {
            if (isset($this->channel) && $this->channel->is_open()) {
                $this->channel->close();
            }

            if (isset($this->connection) && $this->connection->isConnected()) {
                $this->connection->close();
            }
        } catch (\Exception $e) {
            Log::channel('rabbitmq')->error('Error closing RabbitMQ connection: ' . $e->getMessage());
        }
    }
}

<?php

namespace App\Listeners;

use App\Events\AllOrdersPrepared;
use App\Events\CustomerOrderStatusUpdated;
use App\Events\OrderPrepared;
use App\Services\RabbitMQService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishCustomerOrderStatusUpdated implements ShouldQueue
{
    /**
     * The RabbitMQ service instance.
     *
     * @var \App\Services\RabbitMQService
     */
    protected RabbitMQService $rabbitMQService;

    /**
     * Create the event listener.
     */
    public function __construct(RabbitMQService $rabbitMQService)
    {
        $this->rabbitMQService = $rabbitMQService;
    }

    /**
     * Handle the event.
     */
    public function handle(CustomerOrderStatusUpdated $event): void
    {
        try {
            $orderData = $event->orderData;
            
            // Publish event to RabbitMQ
            $this->rabbitMQService->publish(
                config('rabbitmq.routing_keys.customer_order_status_updated', 'customer.order_status_updated'),
                $orderData
            );
            
            Log::channel('rabbitmq')->info('Customer order status updated event published', [
                'order_id' => $orderData['order_id'] ?? null,
                'status' => $orderData['status'] ?? null
            ]);
        } catch (\Exception $e) {
            Log::channel('rabbitmq')->error('Error publishing customer order status updated event: ' . $e->getMessage(), [
                'order_data' => $event->orderData,
                'exception' => $e
            ]);
        }
    }
}

<?php

namespace App\Listeners;

use App\Events\AllOrdersPrepared;
use App\Services\RabbitMQService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishAllOrdersPreparedEvent implements ShouldQueue
{
    /**
     * The RabbitMQ service instance.
     *
     * @var \App\Services\RabbitMQService
     */
    protected RabbitMQService $rabbitMQService;

    /**
     * Create the event listener.
     */
    public function __construct(RabbitMQService $rabbitMQService)
    {
        $this->rabbitMQService = $rabbitMQService;
    }

    /**
     * Handle the event.
     */
    public function handle(AllOrdersPrepared $event): void
    {
        try {
            $kitchen = $event->kitchen;
            $product = $kitchen->product;

            $data = [
                'event' => 'order.all_prepared',
                'kitchen' => [
                    'id' => $kitchen->pk_kitchen_code,
                    'kitchen_id' => $kitchen->fk_kitchen_code,
                    'product_id' => $kitchen->fk_product_code,
                    'total_order' => $kitchen->total_order,
                    'prepared' => $kitchen->prepared,
                    'date' => $kitchen->date->format('Y-m-d'),
                    'order_menu' => $kitchen->order_menu
                ],
                'product' => [
                    'id' => $product->pk_product_code,
                    'name' => $product->name,
                    'kitchen_code' => $product->kitchen_code
                ],
                'timestamp' => now()->toIso8601String()
            ];

            $this->rabbitMQService->publish(
                config('rabbitmq.routing_keys.all_orders_prepared', 'order.all_prepared'),
                $data
            );

            Log::channel('rabbitmq')->info('All orders prepared event published', [
                'kitchen_id' => $kitchen->pk_kitchen_code,
                'product_id' => $kitchen->fk_product_code
            ]);

            // Dispatch customer order status updated event
            event(new \App\Events\CustomerOrderStatusUpdated([
                'event' => 'order.all_prepared',
                'order_id' => $kitchen->order_menu, // This should be the actual order ID in a real implementation
                'status' => 'all_prepared',
                'kitchen_id' => $kitchen->fk_kitchen_code,
                'product_id' => $kitchen->fk_product_code,
                'prepared_count' => $kitchen->prepared,
                'total_count' => $kitchen->total_order,
                'timestamp' => now()->toIso8601String()
            ]));
        } catch (\Exception $e) {
            Log::channel('rabbitmq')->error('Error publishing all orders prepared event: ' . $e->getMessage(), [
                'kitchen_id' => $event->kitchen->pk_kitchen_code,
                'exception' => $e
            ]);
        }
    }
}

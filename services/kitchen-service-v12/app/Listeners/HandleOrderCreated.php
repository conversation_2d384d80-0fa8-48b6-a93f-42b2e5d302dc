<?php

namespace App\Listeners;

use App\Events\OrderCreated;
use App\Models\Kitchen;
use App\Models\Product;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleOrderCreated implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var array
     */
    public $backoff = [5, 15, 30];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(OrderCreated $event): void
    {
        try {
            $orderData = $event->orderData;
            
            // Extract order details
            $orderId = $orderData['order_id'] ?? null;
            $orderDate = $orderData['order_date'] ?? date('Y-m-d');
            $orderMenu = $orderData['order_menu'] ?? 'lunch';
            $companyId = $orderData['company_id'] ?? 1;
            $unitId = $orderData['unit_id'] ?? 1;
            
            // Process order items
            if (isset($orderData['items']) && is_array($orderData['items'])) {
                foreach ($orderData['items'] as $item) {
                    $productId = $item['product_id'] ?? null;
                    $kitchenId = $item['kitchen_id'] ?? null;
                    $quantity = $item['quantity'] ?? 1;
                    
                    if (!$productId || !$kitchenId) {
                        continue;
                    }
                    
                    // Check if product exists
                    $product = Product::find($productId);
                    if (!$product) {
                        Log::channel('kitchen')->warning('Product not found for order item', [
                            'order_id' => $orderId,
                            'product_id' => $productId
                        ]);
                        continue;
                    }
                    
                    // Update or create kitchen record
                    $kitchen = Kitchen::where([
                        'fk_product_code' => $productId,
                        'fk_kitchen_code' => $kitchenId,
                        'date' => $orderDate,
                        'order_menu' => $orderMenu
                    ])->first();
                    
                    DB::beginTransaction();
                    
                    if ($kitchen) {
                        // Update existing kitchen record
                        $kitchen->total_order += $quantity;
                        $kitchen->save();
                    } else {
                        // Create new kitchen record
                        Kitchen::create([
                            'company_id' => $companyId,
                            'unit_id' => $unitId,
                            'fk_product_code' => $productId,
                            'fk_kitchen_code' => $kitchenId,
                            'total_order' => $quantity,
                            'prepared' => 0,
                            'date' => $orderDate,
                            'order_menu' => $orderMenu,
                            'status' => true
                        ]);
                    }
                    
                    DB::commit();
                    
                    Log::channel('kitchen')->info('Kitchen record updated for order', [
                        'order_id' => $orderId,
                        'product_id' => $productId,
                        'kitchen_id' => $kitchenId,
                        'quantity' => $quantity
                    ]);
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::channel('kitchen')->error('Error handling order created event: ' . $e->getMessage(), [
                'order_data' => $event->orderData,
                'exception' => $e
            ]);
            
            // Retry or fail based on attempts
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff[$this->attempts() - 1]);
            } else {
                $this->fail($e);
            }
        }
    }
}

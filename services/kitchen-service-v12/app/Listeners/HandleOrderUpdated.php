<?php

namespace App\Listeners;

use App\Events\OrderUpdated;
use App\Models\Kitchen;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleOrderUpdated implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var array
     */
    public $backoff = [5, 15, 30];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(OrderUpdated $event): void
    {
        try {
            $orderData = $event->orderData;
            
            // Extract order details
            $orderId = $orderData['order_id'] ?? null;
            $orderDate = $orderData['order_date'] ?? date('Y-m-d');
            $orderMenu = $orderData['order_menu'] ?? 'lunch';
            
            // Process order items
            if (isset($orderData['items']) && is_array($orderData['items'])) {
                foreach ($orderData['items'] as $item) {
                    $productId = $item['product_id'] ?? null;
                    $kitchenId = $item['kitchen_id'] ?? null;
                    $quantity = $item['quantity'] ?? 1;
                    $oldQuantity = $item['old_quantity'] ?? 0;
                    
                    if (!$productId || !$kitchenId) {
                        continue;
                    }
                    
                    // Update kitchen record
                    $kitchen = Kitchen::where([
                        'fk_product_code' => $productId,
                        'fk_kitchen_code' => $kitchenId,
                        'date' => $orderDate,
                        'order_menu' => $orderMenu
                    ])->first();
                    
                    if ($kitchen) {
                        DB::beginTransaction();
                        
                        // Calculate the difference in quantity
                        $quantityDiff = $quantity - $oldQuantity;
                        
                        // Update total order count
                        $kitchen->total_order += $quantityDiff;
                        
                        // Ensure total_order is not negative
                        if ($kitchen->total_order < 0) {
                            $kitchen->total_order = 0;
                        }
                        
                        // Ensure prepared count doesn't exceed total order count
                        if ($kitchen->prepared > $kitchen->total_order) {
                            $kitchen->prepared = $kitchen->total_order;
                        }
                        
                        $kitchen->save();
                        
                        DB::commit();
                        
                        Log::channel('kitchen')->info('Kitchen record updated for order update', [
                            'order_id' => $orderId,
                            'product_id' => $productId,
                            'kitchen_id' => $kitchenId,
                            'quantity_diff' => $quantityDiff
                        ]);
                    } else {
                        Log::channel('kitchen')->warning('Kitchen record not found for order update', [
                            'order_id' => $orderId,
                            'product_id' => $productId,
                            'kitchen_id' => $kitchenId
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::channel('kitchen')->error('Error handling order updated event: ' . $e->getMessage(), [
                'order_data' => $event->orderData,
                'exception' => $e
            ]);
            
            // Retry or fail based on attempts
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff[$this->attempts() - 1]);
            } else {
                $this->fail($e);
            }
        }
    }
}

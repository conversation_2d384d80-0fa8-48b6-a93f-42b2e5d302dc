<?php

namespace App\Listeners;

use App\Events\DeliveryStatusUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class HandleDeliveryStatusUpdated implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var array
     */
    public $backoff = [5, 15, 30];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(DeliveryStatusUpdated $event): void
    {
        try {
            $deliveryData = $event->deliveryData;
            
            // Extract delivery details
            $orderId = $deliveryData['order_id'] ?? null;
            $status = $deliveryData['status'] ?? null;
            
            if (!$orderId || !$status) {
                Log::channel('kitchen')->warning('Missing order ID or status in delivery status update', [
                    'delivery_data' => $deliveryData
                ]);
                return;
            }
            
            // Log the delivery status update
            Log::channel('kitchen')->info('Delivery status updated', [
                'order_id' => $orderId,
                'status' => $status
            ]);
            
            // In a real implementation, we might update some kitchen records
            // based on the delivery status, such as marking items as delivered
            // or updating delivery-related fields in the kitchen records.
            
            // For now, we'll just log the event.
        } catch (\Exception $e) {
            Log::channel('kitchen')->error('Error handling delivery status updated event: ' . $e->getMessage(), [
                'delivery_data' => $event->deliveryData,
                'exception' => $e
            ]);
            
            // Retry or fail based on attempts
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff[$this->attempts() - 1]);
            } else {
                $this->fail($e);
            }
        }
    }
}

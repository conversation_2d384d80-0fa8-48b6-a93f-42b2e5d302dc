<?php

namespace Tests\Unit\Services;

use App\Events\AllOrdersPrepared;
use App\Events\OrderPrepared;
use App\Models\Kitchen;
use App\Models\KitchenMaster;
use App\Models\Product;
use App\Services\KitchenService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class KitchenServiceTest extends TestCase
{
    use RefreshDatabase;

    protected KitchenService $kitchenService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->kitchenService = app(KitchenService::class);
    }

    public function test_get_all_kitchens_returns_kitchens(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $testDate = date('Y-m-d');
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'date' => $testDate,
            'order_menu' => 'lunch',
        ]);

        // Debug: Check if kitchen was created
        $this->assertDatabaseHas('kitchens', [
            'date' => $testDate,
            'order_menu' => 'lunch'
        ]);

        // Act
        $kitchens = $this->kitchenService->getAllKitchens(['date' => $testDate]);

        // Debug: Check what we got
        $this->assertNotNull($kitchens, 'Kitchens collection should not be null');

        // Assert
        $this->assertCount(1, $kitchens, 'Should have exactly 1 kitchen for date: ' . $testDate);
        $this->assertEquals($kitchen->pk_kitchen_code, $kitchens->first()->pk_kitchen_code);
    }

    public function test_get_all_kitchens_with_filters_returns_filtered_kitchens(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $testDate = date('Y-m-d');
        $kitchen1 = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'date' => $testDate,
            'order_menu' => 'lunch',
        ]);
        $kitchen2 = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'date' => $testDate,
            'order_menu' => 'dinner',
        ]);

        // Act
        $kitchens = $this->kitchenService->getAllKitchens(['date' => $testDate, 'menu' => 'lunch']);

        // Assert
        $this->assertCount(1, $kitchens);
        $this->assertEquals($kitchen1->pk_kitchen_code, $kitchens->first()->pk_kitchen_code);
    }

    public function test_get_kitchen_by_id_returns_kitchen(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
        ]);

        // Act
        $result = $this->kitchenService->getKitchenById($kitchen->pk_kitchen_code);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($kitchen->pk_kitchen_code, $result->pk_kitchen_code);
    }

    public function test_update_prepared_increments_prepared_count(): void
    {
        // Arrange
        Event::fake();
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'total_order' => 10,
            'prepared' => 5,
        ]);

        // Act
        $result = $this->kitchenService->updatePrepared($kitchen->pk_kitchen_code, []);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals(6, $result->prepared);
        Event::assertDispatched(OrderPrepared::class);
    }

    public function test_update_prepared_dispatches_all_orders_prepared_event_when_all_orders_prepared(): void
    {
        // Arrange
        Event::fake();
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'total_order' => 10,
            'prepared' => 9,
        ]);

        // Act
        $result = $this->kitchenService->updatePrepared($kitchen->pk_kitchen_code, []);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals(10, $result->prepared);
        Event::assertDispatched(OrderPrepared::class);
        Event::assertDispatched(AllOrdersPrepared::class);
    }

    public function test_update_all_prepared_sets_prepared_to_total_order(): void
    {
        // Arrange
        Event::fake();
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'total_order' => 10,
            'prepared' => 5,
        ]);

        // Act
        $result = $this->kitchenService->updateAllPrepared($kitchen->pk_kitchen_code, []);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals(10, $result->prepared);
        Event::assertDispatched(AllOrdersPrepared::class);
    }

    public function test_get_recipe_returns_recipe(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'recipe' => '<p>Test Recipe</p>',
        ]);

        // Act
        $result = $this->kitchenService->getRecipe($product->pk_product_code);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('Test Product', $result['name']);
        $this->assertEquals('<p>Test Recipe</p>', $result['recipe']);
    }

    public function test_get_all_kitchen_masters_returns_kitchen_masters(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();

        // Act
        $kitchenMasters = $this->kitchenService->getAllKitchenMasters();

        // Assert
        $this->assertCount(1, $kitchenMasters);
        $this->assertEquals($kitchenMaster->pk_kitchen_code, $kitchenMasters->first()->pk_kitchen_code);
    }

    public function test_get_kitchen_master_by_id_returns_kitchen_master(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();

        // Act
        $result = $this->kitchenService->getKitchenMasterById($kitchenMaster->pk_kitchen_code);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($kitchenMaster->pk_kitchen_code, $result->pk_kitchen_code);
    }

    public function test_create_kitchen_master_creates_kitchen_master(): void
    {
        // Arrange
        $data = [
            'kitchen_name' => 'Test Kitchen',
            'kitchen_alias' => 'TK',
            'location' => 'Test Location',
            'status' => true,
        ];

        // Act
        $result = $this->kitchenService->createKitchenMaster($data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('Test Kitchen', $result->kitchen_name);
        $this->assertEquals('TK', $result->kitchen_alias);
        $this->assertEquals('Test Location', $result->location);
        $this->assertTrue($result->status);
    }

    public function test_update_kitchen_master_updates_kitchen_master(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create([
            'kitchen_name' => 'Old Name',
            'kitchen_alias' => 'ON',
            'location' => 'Old Location',
        ]);
        $data = [
            'kitchen_name' => 'New Name',
            'kitchen_alias' => 'NN',
            'location' => 'New Location',
        ];

        // Act
        $result = $this->kitchenService->updateKitchenMaster($kitchenMaster->pk_kitchen_code, $data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('New Name', $result->kitchen_name);
        $this->assertEquals('NN', $result->kitchen_alias);
        $this->assertEquals('New Location', $result->location);
    }

    public function test_delete_kitchen_master_deletes_kitchen_master(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();

        // Act
        $result = $this->kitchenService->deleteKitchenMaster($kitchenMaster->pk_kitchen_code);

        // Assert
        $this->assertTrue($result);
        $this->assertNull(KitchenMaster::find($kitchenMaster->pk_kitchen_code));
    }
}

<?php

namespace Tests\Unit\Services;

use App\Services\RabbitMQService;
use Mockery;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use Tests\TestCase;

class RabbitMQServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the AMQPStreamConnection and AMQPChannel
        $this->mockConnection = Mockery::mock(AMQPStreamConnection::class);
        $this->mockChannel = Mockery::mock(AMQPChannel::class);
        
        // Set up the mock expectations for the constructor
        $this->mockConnection->shouldReceive('channel')
            ->once()
            ->andReturn($this->mockChannel);
        
        $this->mockChannel->shouldReceive('exchange_declare')
            ->once()
            ->with(
                config('rabbitmq.exchange'),
                config('rabbitmq.exchange_type'),
                false,
                true,
                false
            )
            ->andReturnNull();
        
        $this->mockChannel->shouldR<PERSON>eive('queue_declare')
            ->times(count(config('rabbitmq.queues')))
            ->andReturnNull();
        
        $this->mockChannel->shouldReceive('queue_bind')
            ->times(count(config('rabbitmq.queues')))
            ->andReturnNull();
        
        // Bind the mocks to the container
        $this->app->instance(AMQPStreamConnection::class, $this->mockConnection);
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    public function test_publish_sends_message_to_rabbitmq(): void
    {
        // Arrange
        $this->mockChannel->shouldReceive('basic_publish')
            ->once()
            ->withArgs(function (AMQPMessage $message, $exchange, $routingKey) {
                return $exchange === config('rabbitmq.exchange') &&
                    $routingKey === 'test.routing.key' &&
                    $message instanceof AMQPMessage;
            })
            ->andReturnNull();
        
        $this->mockChannel->shouldReceive('is_open')
            ->once()
            ->andReturn(true);
        
        $this->mockChannel->shouldReceive('close')
            ->once()
            ->andReturnNull();
        
        $this->mockConnection->shouldReceive('isConnected')
            ->once()
            ->andReturn(true);
        
        $this->mockConnection->shouldReceive('close')
            ->once()
            ->andReturnNull();
        
        $rabbitMQService = new RabbitMQService();
        $data = ['test' => 'data'];
        
        // Act
        $result = $rabbitMQService->publish('test.routing.key', $data);
        
        // Assert
        $this->assertTrue($result);
    }
    
    public function test_publish_handles_exception(): void
    {
        // Arrange
        $this->mockChannel->shouldReceive('basic_publish')
            ->once()
            ->andThrow(new \Exception('Test exception'));
        
        $this->mockChannel->shouldReceive('is_open')
            ->once()
            ->andReturn(true);
        
        $this->mockChannel->shouldReceive('close')
            ->once()
            ->andReturnNull();
        
        $this->mockConnection->shouldReceive('isConnected')
            ->once()
            ->andReturn(true);
        
        $this->mockConnection->shouldReceive('close')
            ->once()
            ->andReturnNull();
        
        $rabbitMQService = new RabbitMQService();
        $data = ['test' => 'data'];
        
        // Act
        $result = $rabbitMQService->publish('test.routing.key', $data);
        
        // Assert
        $this->assertFalse($result);
    }
}

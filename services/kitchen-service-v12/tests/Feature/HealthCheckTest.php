<?php

namespace Tests\Feature;

use CubeOneBiz\Resilience\Metrics\PerformanceMetricsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class HealthCheckTest extends TestCase
{
    use RefreshDatabase;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock external service calls
        Http::fake([
            config('services.order.url') . '/health' => Http::response(['status' => 'ok'], 200),
            config('services.delivery.url') . '/health' => Http::response(['status' => 'ok'], 200),
            config('services.catalogue.url') . '/health' => Http::response(['status' => 'ok'], 200),
        ]);
        
        // Mock PerformanceMetricsService
        $this->mock(PerformanceMetricsService::class, function ($mock) {
            $mock->shouldReceive('startTimer')->andReturn(null);
            $mock->shouldR<PERSON>eive('endTimer')->andReturn(0.1);
            $mock->shouldReceive('getDuration')->andReturn(0.1);
        });
    }
    
    public function testBasicHealthCheck(): void
    {
        $response = $this->get('/api/v2/kitchen/health');
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'ok',
                'message' => 'Service is healthy',
            ])
            ->assertJsonStructure([
                'status',
                'message',
                'timestamp',
                'checks' => [
                    'database',
                    'memory',
                    'disk',
                    'dependencies',
                    'kitchen_equipment',
                ],
                'version',
                'metrics',
            ]);
    }
    
    public function testHealthCheckWithDatabaseFailure(): void
    {
        // Mock DB::connection to throw an exception
        DB::shouldReceive('connection->getPdo')
            ->once()
            ->andThrow(new \Exception('Database connection failed'));
        
        $response = $this->get('/api/v2/kitchen/health');
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'error',
                'message' => 'Service is unhealthy',
                'checks' => [
                    'database' => [
                        'status' => 'error',
                        'message' => 'Database connection failed: Database connection failed',
                    ],
                ],
            ]);
    }
    
    public function testHealthCheckWithDependencyFailure(): void
    {
        // Mock external service calls with a failure
        Http::fake([
            config('services.order.url') . '/health' => Http::response(['status' => 'ok'], 200),
            config('services.delivery.url') . '/health' => Http::response(['status' => 'error'], 500),
            config('services.catalogue.url') . '/health' => Http::response(['status' => 'ok'], 200),
        ]);
        
        $response = $this->get('/api/v2/kitchen/health');
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'error',
                'message' => 'Service is unhealthy',
                'checks' => [
                    'dependencies' => [
                        'status' => 'error',
                        'message' => 'Some dependencies have issues',
                        'services' => [
                            'delivery_service' => [
                                'status' => 'error',
                            ],
                        ],
                    ],
                ],
            ]);
    }
    
    public function testHealthCheckWithDependencyTimeout(): void
    {
        // Mock external service calls with a timeout
        Http::fake([
            config('services.order.url') . '/health' => Http::response(['status' => 'ok'], 200),
            config('services.delivery.url') . '/health' => function () {
                throw new \Illuminate\Http\Client\ConnectionException('Connection timed out');
            },
            config('services.catalogue.url') . '/health' => Http::response(['status' => 'ok'], 200),
        ]);
        
        $response = $this->get('/api/v2/kitchen/health');
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'error',
                'message' => 'Service is unhealthy',
                'checks' => [
                    'dependencies' => [
                        'status' => 'error',
                        'message' => 'Some dependencies have issues',
                        'services' => [
                            'delivery_service' => [
                                'status' => 'error',
                                'message' => 'Connection failed: Connection timed out',
                            ],
                        ],
                    ],
                ],
            ]);
    }
    
    public function testHealthCheckWithSlowDependency(): void
    {
        // Mock PerformanceMetricsService to return a slow response time
        $this->mock(PerformanceMetricsService::class, function ($mock) {
            $mock->shouldReceive('startTimer')->andReturn(null);
            $mock->shouldReceive('endTimer')->andReturn(0.6); // 600ms
            $mock->shouldReceive('getDuration')->andReturn(0.6);
        });
        
        $response = $this->get('/api/v2/kitchen/health');
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'warning',
                'message' => 'Service has warnings',
            ]);
    }
    
    public function testDetailedHealthCheckRequiresAuthentication(): void
    {
        $response = $this->get('/api/v2/kitchen/health/detailed');
        
        $response->assertStatus(401);
    }
}

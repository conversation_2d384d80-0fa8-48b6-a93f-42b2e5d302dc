<?php

namespace Tests\Feature\Api;

use App\Events\AllOrdersPrepared;
use App\Events\OrderPrepared;
use App\Models\Kitchen;
use App\Models\KitchenMaster;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class KitchenControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_index_returns_kitchens(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch',
        ]);

        // Act
        $response = $this->getJson('/api/v2/kitchens');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $kitchen->pk_kitchen_code);
    }

    public function test_index_with_filters_returns_filtered_kitchens(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen1 = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch',
        ]);
        $kitchen2 = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'date' => date('Y-m-d'),
            'order_menu' => 'dinner',
        ]);

        // Act
        $response = $this->getJson('/api/v2/kitchens?menu=lunch');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $kitchen1->pk_kitchen_code);
    }

    public function test_show_returns_kitchen(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
        ]);

        // Act
        $response = $this->getJson("/api/v2/kitchens/{$kitchen->pk_kitchen_code}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.id', $kitchen->pk_kitchen_code);
    }

    public function test_show_returns_404_for_nonexistent_kitchen(): void
    {
        // Act
        $response = $this->getJson('/api/v2/kitchens/999');

        // Assert
        $response->assertStatus(404);
    }

    public function test_update_prepared_increments_prepared_count(): void
    {
        // Arrange
        Event::fake();
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'total_order' => 10,
            'prepared' => 5,
        ]);

        // Act
        $response = $this->postJson("/api/v2/kitchens/{$kitchen->pk_kitchen_code}/prepared", [
            'menu' => 'lunch',
            'date' => date('Y-m-d'),
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.id', $kitchen->pk_kitchen_code)
            ->assertJsonPath('data.prepared', 6);

        Event::assertDispatched(OrderPrepared::class);
    }

    public function test_update_prepared_dispatches_all_orders_prepared_event_when_all_orders_prepared(): void
    {
        // Arrange
        Event::fake();
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'total_order' => 10,
            'prepared' => 9,
        ]);

        // Act
        $response = $this->postJson("/api/v2/kitchens/{$kitchen->pk_kitchen_code}/prepared", [
            'menu' => 'lunch',
            'date' => date('Y-m-d'),
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.id', $kitchen->pk_kitchen_code)
            ->assertJsonPath('data.prepared', 10);

        Event::assertDispatched(OrderPrepared::class);
        Event::assertDispatched(AllOrdersPrepared::class);
    }

    public function test_update_all_prepared_sets_prepared_to_total_order(): void
    {
        // Arrange
        Event::fake();
        $kitchenMaster = KitchenMaster::factory()->create();
        $product = Product::factory()->create();
        $kitchen = Kitchen::factory()->create([
            'fk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'fk_product_code' => $product->pk_product_code,
            'total_order' => 10,
            'prepared' => 5,
        ]);

        // Act
        $response = $this->postJson("/api/v2/kitchens/{$kitchen->pk_kitchen_code}/prepared/all", [
            'menu' => 'lunch',
            'date' => date('Y-m-d'),
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.id', $kitchen->pk_kitchen_code)
            ->assertJsonPath('data.prepared', 10);

        Event::assertDispatched(AllOrdersPrepared::class);
    }
}

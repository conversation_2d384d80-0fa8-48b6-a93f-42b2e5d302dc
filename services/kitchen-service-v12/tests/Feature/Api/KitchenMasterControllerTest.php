<?php

namespace Tests\Feature\Api;

use App\Models\KitchenMaster;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class KitchenMasterControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_index_returns_kitchen_masters(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();

        // Act
        $response = $this->getJson('/api/v2/kitchen-masters');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $kitchenMaster->pk_kitchen_code);
    }

    public function test_store_creates_kitchen_master(): void
    {
        // Arrange
        $data = [
            'kitchen_name' => 'Test Kitchen',
            'kitchen_alias' => 'TK',
            'location' => 'Test Location',
            'status' => true,
        ];

        // Act
        $response = $this->postJson('/api/v2/kitchen-masters', $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.kitchen_name', 'Test Kitchen')
            ->assertJsonPath('data.kitchen_alias', 'TK')
            ->assertJsonPath('data.location', 'Test Location')
            ->assertJsonPath('data.status', true);

        $this->assertDatabaseHas('kitchen_masters', [
            'kitchen_name' => 'Test Kitchen',
            'kitchen_alias' => 'TK',
            'location' => 'Test Location',
        ]);
    }

    public function test_store_validates_input(): void
    {
        // Arrange
        $data = [
            // Missing required kitchen_name
            'kitchen_alias' => 'TK',
            'location' => 'Test Location',
        ];

        // Act
        $response = $this->postJson('/api/v2/kitchen-masters', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['kitchen_name']);
    }

    public function test_show_returns_kitchen_master(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();

        // Act
        $response = $this->getJson("/api/v2/kitchen-masters/{$kitchenMaster->pk_kitchen_code}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.id', $kitchenMaster->pk_kitchen_code);
    }

    public function test_show_returns_404_for_nonexistent_kitchen_master(): void
    {
        // Act
        $response = $this->getJson('/api/v2/kitchen-masters/999');

        // Assert
        $response->assertStatus(404);
    }

    public function test_update_updates_kitchen_master(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create([
            'kitchen_name' => 'Old Name',
            'kitchen_alias' => 'ON',
            'location' => 'Old Location',
        ]);
        $data = [
            'kitchen_name' => 'New Name',
            'kitchen_alias' => 'NN',
            'location' => 'New Location',
        ];

        // Act
        $response = $this->putJson("/api/v2/kitchen-masters/{$kitchenMaster->pk_kitchen_code}", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.id', $kitchenMaster->pk_kitchen_code)
            ->assertJsonPath('data.kitchen_name', 'New Name')
            ->assertJsonPath('data.kitchen_alias', 'NN')
            ->assertJsonPath('data.location', 'New Location');

        $this->assertDatabaseHas('kitchen_masters', [
            'pk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
            'kitchen_name' => 'New Name',
            'kitchen_alias' => 'NN',
            'location' => 'New Location',
        ]);
    }

    public function test_update_validates_input(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();
        $data = [
            'kitchen_name' => '', // Empty kitchen_name
        ];

        // Act
        $response = $this->putJson("/api/v2/kitchen-masters/{$kitchenMaster->pk_kitchen_code}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['kitchen_name']);
    }

    public function test_destroy_deletes_kitchen_master(): void
    {
        // Arrange
        $kitchenMaster = KitchenMaster::factory()->create();

        // Act
        $response = $this->deleteJson("/api/v2/kitchen-masters/{$kitchenMaster->pk_kitchen_code}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Kitchen master deleted successfully',
            ]);

        $this->assertDatabaseMissing('kitchen_masters', [
            'pk_kitchen_code' => $kitchenMaster->pk_kitchen_code,
        ]);
    }

    public function test_destroy_returns_404_for_nonexistent_kitchen_master(): void
    {
        // Act
        $response = $this->deleteJson('/api/v2/kitchen-masters/999');

        // Assert
        $response->assertStatus(404);
    }
}

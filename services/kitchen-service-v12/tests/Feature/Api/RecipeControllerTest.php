<?php

namespace Tests\Feature\Api;

use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RecipeControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_show_returns_recipe(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'recipe' => '<p>Test Recipe</p>',
        ]);

        // Act
        $response = $this->getJson("/api/v2/recipes/{$product->pk_product_code}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'name' => 'Test Product',
                    'recipe' => '<p>Test Recipe</p>',
                ],
            ]);
    }

    public function test_show_returns_404_for_nonexistent_recipe(): void
    {
        // Act
        $response = $this->getJson('/api/v2/recipes/999');

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Recipe not found',
            ]);
    }
}

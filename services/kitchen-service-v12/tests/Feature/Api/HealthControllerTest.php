<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HealthControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_returns_health_status(): void
    {
        // Act
        $response = $this->getJson('/api/health');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'service',
                'version',
                'timestamp',
                'database',
            ])
            ->assertJsonPath('status', 'ok')
            ->assertJsonPath('service', 'kitchen-service');
    }
}

<?php

namespace Tests\Feature;

use CubeOneBiz\Resilience\CircuitBreaker\CircuitBreakerService;
use CubeOneBiz\Resilience\Idempotency\IdempotencyService;
use CubeOneBiz\Resilience\Retry\RetryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ResilienceIntegrationTest extends TestCase
{
    use RefreshDatabase;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear the cache
        Cache::flush();
    }
    
    public function testCircuitBreakerWithRetry(): void
    {
        // Create a mock service that fails 3 times then succeeds
        $attemptCount = 0;
        
        Http::fake([
            'https://mock-service.example.com/api' => function () use (&$attemptCount) {
                $attemptCount++;
                
                if ($attemptCount <= 3) {
                    return Http::response(['error' => 'Service unavailable'], 503);
                }
                
                return Http::response(['data' => 'Success'], 200);
            },
        ]);
        
        // Create a service that uses circuit breaker and retry
        $circuitBreaker = app(CircuitBreakerService::class);
        $retry = app(RetryService::class);
        
        // Execute with retry and circuit breaker
        $result = $retry->execute(function () use ($circuitBreaker) {
            return $circuitBreaker->execute('mock_service', function () {
                return Http::get('https://mock-service.example.com/api')->json();
            }, 5, 30); // 5 failures before circuit opens, 30 seconds timeout
        }, 'api_call', 4); // 4 retries
        
        // Verify the result
        $this->assertEquals(['data' => 'Success'], $result);
        $this->assertEquals(4, $attemptCount);
        
        // Verify that the circuit is closed
        $circuitState = Cache::get('circuit_breaker:mock_service');
        $this->assertEquals(0, $circuitState['failures']);
        $this->assertFalse($circuitState['open']);
    }
    
    public function testIdempotencyWithCircuitBreaker(): void
    {
        // Create a mock service that fails then succeeds
        $attemptCount = 0;
        $operationCount = 0;
        
        Http::fake([
            'https://mock-service.example.com/api' => function () use (&$attemptCount) {
                $attemptCount++;
                
                if ($attemptCount === 1) {
                    return Http::response(['error' => 'Service unavailable'], 503);
                }
                
                return Http::response(['data' => 'Success', 'id' => 123], 200);
            },
        ]);
        
        // Create services
        $idempotency = app(IdempotencyService::class);
        $circuitBreaker = app(CircuitBreakerService::class);
        
        // Generate an idempotency key
        $idempotencyKey = $idempotency->generateKey();
        
        // First attempt - should fail but be retried
        try {
            $idempotency->processWithIdempotency($idempotencyKey, 'create_resource', function () use ($circuitBreaker, &$operationCount) {
                $operationCount++;
                
                return $circuitBreaker->execute('mock_service', function () {
                    return Http::get('https://mock-service.example.com/api')->json();
                });
            });
        } catch (\Exception $e) {
            // Expected exception on first attempt
            $this->assertEquals(1, $attemptCount);
            $this->assertEquals(1, $operationCount);
        }
        
        // Reset operation count for second attempt
        $operationCount = 0;
        
        // Second attempt - should succeed and use the same idempotency key
        $result = $idempotency->processWithIdempotency($idempotencyKey, 'create_resource', function () use ($circuitBreaker, &$operationCount) {
            $operationCount++;
            
            return $circuitBreaker->execute('mock_service', function () {
                return Http::get('https://mock-service.example.com/api')->json();
            });
        });
        
        // Verify the result
        $this->assertEquals(['data' => 'Success', 'id' => 123], $result);
        $this->assertEquals(2, $attemptCount); // HTTP client was called twice
        $this->assertEquals(1, $operationCount); // But the operation function was only called once on second attempt
        
        // Third attempt - should return cached result without calling the operation
        $operationCount = 0;
        
        $result = $idempotency->processWithIdempotency($idempotencyKey, 'create_resource', function () use ($circuitBreaker, &$operationCount) {
            $operationCount++;
            
            return $circuitBreaker->execute('mock_service', function () {
                return Http::get('https://mock-service.example.com/api')->json();
            });
        });
        
        // Verify the result
        $this->assertEquals(['data' => 'Success', 'id' => 123], $result);
        $this->assertEquals(2, $attemptCount); // HTTP client was not called again
        $this->assertEquals(0, $operationCount); // Operation function was not called
    }
    
    public function testCircuitBreakerPreventsMultipleFailures(): void
    {
        // Create a mock service that always fails
        Http::fake([
            'https://mock-service.example.com/api' => Http::response(['error' => 'Service unavailable'], 503),
        ]);
        
        // Create a circuit breaker with a low threshold
        $circuitBreaker = app(CircuitBreakerService::class);
        
        // First set of failures to open the circuit
        $failureCount = 0;
        
        for ($i = 0; $i < 3; $i++) {
            try {
                $circuitBreaker->execute('mock_service', function () use (&$failureCount) {
                    $failureCount++;
                    return Http::get('https://mock-service.example.com/api')->json();
                }, 3, 30); // 3 failures before circuit opens, 30 seconds timeout
                
                $this->fail('Exception should have been thrown');
            } catch (\Exception $e) {
                // Expected exception
            }
        }
        
        // Verify that the circuit is open
        $circuitState = Cache::get('circuit_breaker:mock_service');
        $this->assertEquals(3, $circuitState['failures']);
        $this->assertTrue($circuitState['open']);
        $this->assertEquals(3, $failureCount);
        
        // Now try again - should throw CircuitOpenException without calling the service
        try {
            $circuitBreaker->execute('mock_service', function () use (&$failureCount) {
                $failureCount++;
                return Http::get('https://mock-service.example.com/api')->json();
            }, 3, 30);
            
            $this->fail('CircuitOpenException should have been thrown');
        } catch (\CubeOneBiz\Resilience\CircuitBreaker\CircuitOpenException $e) {
            // Expected exception
            $this->assertEquals('Service mock_service is unavailable', $e->getMessage());
        }
        
        // Verify that the failure count didn't increase
        $this->assertEquals(3, $failureCount);
    }
}

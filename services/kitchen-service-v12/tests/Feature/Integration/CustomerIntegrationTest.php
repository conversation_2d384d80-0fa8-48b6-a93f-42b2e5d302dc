<?php

namespace Tests\Feature\Integration;

use App\Events\CustomerOrderStatusUpdated;
use App\Events\OrderPrepared;
use App\Models\Kitchen;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class CustomerIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test publishing customer order status updated event.
     */
    public function test_publishing_customer_order_status_updated_event(): void
    {
        // Create order data
        $orderData = [
            'event' => 'order.prepared',
            'order_id' => 'ORD-001',
            'status' => 'prepared',
            'kitchen_id' => 1,
            'product_id' => 1,
            'prepared_count' => 1,
            'total_count' => 2,
            'timestamp' => now()->toIso8601String()
        ];
        
        // Mock event listener
        Event::fake([CustomerOrderStatusUpdated::class]);
        
        // Dispatch event
        event(new CustomerOrderStatusUpdated($orderData));
        
        // Assert event was dispatched
        Event::assertDispatched(CustomerOrderStatusUpdated::class, function ($event) use ($orderData) {
            return $event->orderData === $orderData;
        });
    }

    /**
     * Test dispatching customer order status updated event when order is prepared.
     */
    public function test_dispatching_customer_order_status_updated_event_when_order_is_prepared(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        $kitchen = Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 1,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Mock event listener
        Event::fake([CustomerOrderStatusUpdated::class]);
        
        // Dispatch event
        event(new OrderPrepared($kitchen));
        
        // Assert event was dispatched
        Event::assertDispatched(CustomerOrderStatusUpdated::class);
    }

    /**
     * Test order preparation status API endpoint for customer.
     */
    public function test_order_preparation_status_api_endpoint_for_customer(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 1,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Call API endpoint
        $response = $this->getJson('/api/v2/integration/customer/orders/ORD-001/preparation-status');
        
        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'order_id',
                    'date',
                    'menu',
                    'is_fully_prepared',
                    'preparation_percentage',
                    'status'
                ]
            ]);
    }

    /**
     * Test multiple orders preparation status API endpoint for customer.
     */
    public function test_multiple_orders_preparation_status_api_endpoint_for_customer(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 1,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Create request data
        $requestData = [
            'order_ids' => ['ORD-001', 'ORD-002'],
            'date' => date('Y-m-d'),
            'menu' => 'lunch'
        ];
        
        // Call API endpoint
        $response = $this->postJson('/api/v2/integration/customer/orders/preparation-status', $requestData);
        
        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'ORD-001' => [
                        'date',
                        'menu',
                        'is_fully_prepared',
                        'preparation_percentage',
                        'status'
                    ],
                    'ORD-002' => [
                        'date',
                        'menu',
                        'is_fully_prepared',
                        'preparation_percentage',
                        'status'
                    ]
                ]
            ]);
    }

    /**
     * Test customer preparation summary API endpoint.
     */
    public function test_customer_preparation_summary_api_endpoint(): void
    {
        // Call API endpoint
        $response = $this->getJson('/api/v2/integration/customer/123/preparation-summary');
        
        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'customer_id',
                    'date',
                    'menu',
                    'total_orders',
                    'prepared_orders',
                    'preparation_percentage',
                    'orders' => [
                        '*' => [
                            'order_id',
                            'is_fully_prepared',
                            'preparation_percentage',
                            'status'
                        ]
                    ]
                ]
            ]);
    }
}

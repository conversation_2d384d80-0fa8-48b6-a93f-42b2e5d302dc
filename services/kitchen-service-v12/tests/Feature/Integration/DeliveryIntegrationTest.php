<?php

namespace Tests\Feature\Integration;

use App\Events\AllOrdersPrepared;
use App\Events\DeliveryStatusUpdated;
use App\Models\Kitchen;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class DeliveryIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test handling delivery status updated event.
     */
    public function test_handle_delivery_status_updated_event(): void
    {
        // Create delivery data
        $deliveryData = [
            'order_id' => 'ORD-001',
            'status' => 'delivered',
            'timestamp' => now()->toIso8601String(),
            'delivery_agent' => [
                'id' => 1,
                'name' => '<PERSON>'
            ]
        ];
        
        // Mock event listener
        Event::fake([DeliveryStatusUpdated::class]);
        
        // Dispatch event
        event(new DeliveryStatusUpdated($deliveryData));
        
        // Assert event was dispatched
        Event::assertDispatched(DeliveryStatusUpdated::class, function ($event) use ($deliveryData) {
            return $event->deliveryData === $deliveryData;
        });
    }

    /**
     * Test order preparation status API endpoint for delivery.
     */
    public function test_order_preparation_status_api_endpoint_for_delivery(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 1,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Call API endpoint
        $response = $this->getJson('/api/v2/integration/delivery/orders/ORD-001/preparation-status');
        
        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'order_id',
                    'date',
                    'menu',
                    'is_fully_prepared',
                    'preparation_percentage',
                    'status'
                ]
            ]);
    }

    /**
     * Test estimate delivery time API endpoint.
     */
    public function test_estimate_delivery_time_api_endpoint(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 1,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Call API endpoint
        $response = $this->getJson('/api/v2/integration/delivery/orders/ORD-001/estimate-delivery-time');
        
        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'order_id',
                    'preparation_time_minutes',
                    'delivery_time_minutes',
                    'total_estimated_time_minutes',
                    'estimated_delivery_time',
                    'preparation_status'
                ]
            ]);
    }

    /**
     * Test notify delivery status update API endpoint.
     */
    public function test_notify_delivery_status_update_api_endpoint(): void
    {
        // Create request data
        $requestData = [
            'order_id' => 'ORD-001',
            'status' => 'delivered',
            'timestamp' => now()->toIso8601String(),
            'delivery_agent' => [
                'id' => 1,
                'name' => 'John Doe'
            ]
        ];
        
        // Call API endpoint
        $response = $this->postJson('/api/v2/integration/delivery/status-update', $requestData);
        
        // Assert response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Delivery status update received'
            ]);
    }

    /**
     * Test publishing all orders prepared event for delivery.
     */
    public function test_publishing_all_orders_prepared_event_for_delivery(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        $kitchen = Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 2,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Mock event listener
        Event::fake();
        
        // Dispatch event
        event(new AllOrdersPrepared($kitchen));
        
        // Assert event was dispatched
        Event::assertDispatched(AllOrdersPrepared::class, function ($event) use ($kitchen) {
            return $event->kitchen->pk_kitchen_code === $kitchen->pk_kitchen_code;
        });
    }
}

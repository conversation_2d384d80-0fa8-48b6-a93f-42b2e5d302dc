<?php

namespace Tests\Feature\Integration;

use App\Events\OrderCreated;
use App\Events\OrderUpdated;
use App\Events\OrderCancelled;
use App\Models\Kitchen;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class QuickServeIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test handling order created event.
     */
    public function test_handle_order_created_event(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create order data
        $orderData = [
            'order_id' => 'ORD-001',
            'order_date' => date('Y-m-d'),
            'order_menu' => 'lunch',
            'company_id' => 1,
            'unit_id' => 1,
            'items' => [
                [
                    'product_id' => $product->pk_product_code,
                    'kitchen_id' => 1,
                    'quantity' => 2
                ]
            ]
        ];
        
        // Dispatch event
        event(new OrderCreated($orderData));
        
        // Assert kitchen record was created
        $this->assertDatabaseHas('kitchen', [
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 0,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
    }

    /**
     * Test handling order updated event.
     */
    public function test_handle_order_updated_event(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        $kitchen = Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 0,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Create order data
        $orderData = [
            'order_id' => 'ORD-001',
            'order_date' => date('Y-m-d'),
            'order_menu' => 'lunch',
            'items' => [
                [
                    'product_id' => $product->pk_product_code,
                    'kitchen_id' => 1,
                    'quantity' => 3,
                    'old_quantity' => 2
                ]
            ]
        ];
        
        // Dispatch event
        event(new OrderUpdated($orderData));
        
        // Assert kitchen record was updated
        $this->assertDatabaseHas('kitchen', [
            'pk_kitchen_code' => $kitchen->pk_kitchen_code,
            'total_order' => 3,
            'prepared' => 0
        ]);
    }

    /**
     * Test handling order cancelled event.
     */
    public function test_handle_order_cancelled_event(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        $kitchen = Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 0,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Create order data
        $orderData = [
            'order_id' => 'ORD-001',
            'order_date' => date('Y-m-d'),
            'order_menu' => 'lunch',
            'items' => [
                [
                    'product_id' => $product->pk_product_code,
                    'kitchen_id' => 1,
                    'quantity' => 2
                ]
            ]
        ];
        
        // Dispatch event
        event(new OrderCancelled($orderData));
        
        // Assert kitchen record was updated
        $this->assertDatabaseHas('kitchen', [
            'pk_kitchen_code' => $kitchen->pk_kitchen_code,
            'total_order' => 0,
            'prepared' => 0
        ]);
    }

    /**
     * Test preparation status API endpoint.
     */
    public function test_preparation_status_api_endpoint(): void
    {
        // Create a product
        $product = Product::factory()->create();
        
        // Create a kitchen record
        Kitchen::factory()->create([
            'fk_product_code' => $product->pk_product_code,
            'fk_kitchen_code' => 1,
            'total_order' => 2,
            'prepared' => 1,
            'date' => date('Y-m-d'),
            'order_menu' => 'lunch'
        ]);
        
        // Call API endpoint
        $response = $this->getJson('/api/v2/integration/preparation-status?product_ids[]=' . $product->pk_product_code);
        
        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'product_id',
                        'kitchen_id',
                        'total_order',
                        'prepared',
                        'remaining',
                        'preparation_percentage',
                        'is_fully_prepared',
                        'date',
                        'menu'
                    ]
                ]
            ]);
    }
}

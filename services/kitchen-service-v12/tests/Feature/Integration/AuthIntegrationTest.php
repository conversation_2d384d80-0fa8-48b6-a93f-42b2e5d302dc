<?php

namespace Tests\Feature\Integration;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class AuthIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock HTTP calls to Auth Service
        Http::fake([
            config('services.auth.url') . '/api/v2/auth/validate' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'name' => '<PERSON> Doe',
                    'email' => '<EMAIL>',
                    'roles' => ['kitchen_staff'],
                    'permissions' => ['kitchen.access', 'kitchen.view', 'kitchen.prepare']
                ]
            ], 200),
        ]);
    }

    /**
     * Test authentication middleware.
     */
    public function test_authentication_middleware(): void
    {
        // Call API endpoint without token
        $response = $this->get<PERSON>son('/api/v2/kitchens');
        
        // Assert response
        $response->assertStatus(401);
        
        // Call API endpoint with token
        $response = $this->withHeader('Authorization', 'Bearer valid-token')
            ->getJson('/api/v2/kitchens');
        
        // Assert response
        $response->assertStatus(200);
    }

    /**
     * Test role-based access control middleware.
     */
    public function test_role_based_access_control_middleware(): void
    {
        // Mock HTTP calls to Auth Service with different roles
        Http::fake([
            config('services.auth.url') . '/api/v2/auth/validate' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'roles' => ['kitchen_staff'],
                    'permissions' => ['kitchen.access', 'kitchen.view']
                ]
            ], 200),
        ]);
        
        // Call API endpoint that requires kitchen.prepare permission
        $response = $this->withHeader('Authorization', 'Bearer valid-token')
            ->postJson('/api/v2/kitchens/1/prepared', ['prepared' => 1]);
        
        // Assert response
        $response->assertStatus(403);
        
        // Mock HTTP calls to Auth Service with required permission
        Http::fake([
            config('services.auth.url') . '/api/v2/auth/validate' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'roles' => ['kitchen_staff'],
                    'permissions' => ['kitchen.access', 'kitchen.view', 'kitchen.prepare']
                ]
            ], 200),
        ]);
        
        // Call API endpoint that requires kitchen.prepare permission
        $response = $this->withHeader('Authorization', 'Bearer valid-token')
            ->postJson('/api/v2/kitchens/1/prepared', ['prepared' => 1]);
        
        // Assert response (might still fail due to missing kitchen record, but not due to authorization)
        $response->assertStatus(500);
    }

    /**
     * Test permission-based access control middleware.
     */
    public function test_permission_based_access_control_middleware(): void
    {
        // Mock HTTP calls to Auth Service with different permissions
        Http::fake([
            config('services.auth.url') . '/api/v2/auth/validate' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'roles' => ['kitchen_manager'],
                    'permissions' => ['kitchen.access', 'kitchen.view']
                ]
            ], 200),
        ]);
        
        // Call API endpoint that requires kitchen.master.manage permission
        $response = $this->withHeader('Authorization', 'Bearer valid-token')
            ->postJson('/api/v2/kitchen-masters', [
                'kitchen_name' => 'Test Kitchen',
                'kitchen_alias' => 'TK',
                'location' => 'Test Location',
                'status' => true
            ]);
        
        // Assert response
        $response->assertStatus(403);
        
        // Mock HTTP calls to Auth Service with required permission
        Http::fake([
            config('services.auth.url') . '/api/v2/auth/validate' => Http::response([
                'success' => true,
                'data' => [
                    'id' => 1,
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'roles' => ['kitchen_manager'],
                    'permissions' => ['kitchen.access', 'kitchen.view', 'kitchen.master.manage']
                ]
            ], 200),
        ]);
        
        // Call API endpoint that requires kitchen.master.manage permission
        $response = $this->withHeader('Authorization', 'Bearer valid-token')
            ->postJson('/api/v2/kitchen-masters', [
                'kitchen_name' => 'Test Kitchen',
                'kitchen_alias' => 'TK',
                'location' => 'Test Location',
                'status' => true
            ]);
        
        // Assert response
        $response->assertStatus(201);
    }
}

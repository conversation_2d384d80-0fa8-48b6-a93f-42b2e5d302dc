# Kitchen Service

This is a Laravel 12 microservice for managing kitchen operations. It provides APIs for tracking order preparation status, managing kitchen screens, and viewing product recipes.

## Features

- Kitchen preparation tracking
- Kitchen screen management
- Recipe viewing
- Event-driven architecture using RabbitMQ
- RESTful API with OpenAPI documentation
- Integration with Kong API Gateway

## Requirements

- PHP 8.1+
- Composer
- SQLite or MySQL
- RabbitMQ

## Installation

1. Clone the repository
2. Navigate to the project directory
3. Copy the `.env.example` file to `.env`
4. Run the following commands:

```bash
composer install
php artisan key:generate
php artisan migrate
php artisan db:seed
```

## API Endpoints

### Kitchen Operations

- `GET /api/v2/kitchens` - Get all kitchens with optional filtering
- `GET /api/v2/kitchens/{id}` - Get a specific kitchen
- `POST /api/v2/kitchens/{id}/prepared` - Update prepared count for a kitchen
- `POST /api/v2/kitchens/{id}/prepared/all` - Update all prepared count for a kitchen

### Recipe Management

- `GET /api/v2/recipes/{id}` - Get a recipe by product ID

### Kitchen Master Management

- `GET /api/v2/kitchen-masters` - Get all kitchen masters
- `POST /api/v2/kitchen-masters` - Create a new kitchen master
- `GET /api/v2/kitchen-masters/{id}` - Get a specific kitchen master
- `PUT /api/v2/kitchen-masters/{id}` - Update a kitchen master
- `DELETE /api/v2/kitchen-masters/{id}` - Delete a kitchen master

## Running Tests

```bash
php artisan test
```

## API Documentation

The API is documented using the OpenAPI Specification (OAS) 3.1.0. The specification is available in the `openapi.yaml` file.

## Kong API Gateway Integration

This service is configured to work with Kong API Gateway. The configuration is available in the `kong.yaml` file.

## RabbitMQ Integration

This service uses RabbitMQ for event-driven communication with other microservices. The following events are published:

- `order.prepared` - When an order is marked as prepared
- `order.all_prepared` - When all orders for a product are marked as prepared

## Migrated from Zend Framework

This service was migrated from the Zend Framework application. The original code was located in:

- `module/Kitchen/src/Kitchen/Controller/KitchenController.php`
- `module/Kitchen/src/Kitchen/Model/KitchenTable.php`
- `module/Kitchen/src/Kitchen/Form/KitchenLoginForm.php`
- `module/Kitchen/src/Kitchen/Model/User.php`

## Directory Structure

```
app/
├── Events/
│   ├── AllOrdersPrepared.php
│   └── OrderPrepared.php
├── Http/
│   ├── Controllers/
│   │   └── Api/
│   │       ├── HealthController.php
│   │       ├── KitchenController.php
│   │       ├── KitchenMasterController.php
│   │       └── RecipeController.php
│   ├── Middleware/
│   │   └── AuthenticateKitchen.php
│   ├── Requests/
│   │   ├── KitchenMasterRequest.php
│   │   └── UpdatePreparedRequest.php
│   └── Resources/
│       ├── KitchenMasterResource.php
│       └── KitchenResource.php
├── Listeners/
│   ├── PublishAllOrdersPreparedEvent.php
│   └── PublishOrderPreparedEvent.php
├── Models/
│   ├── Kitchen.php
│   ├── KitchenMaster.php
│   ├── Product.php
│   └── UserKitchen.php
├── Providers/
│   ├── AppServiceProvider.php
│   ├── AuthServiceProvider.php
│   ├── EventServiceProvider.php
│   ├── RabbitMQServiceProvider.php
│   └── RouteServiceProvider.php
└── Services/
    ├── KitchenService.php
    └── RabbitMQService.php
```

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Microservices Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for the microservices.
    |
    */

    'auth' => [
        'url' => env('AUTH_SERVICE_URL', 'http://auth-service-v12:8000/api'),
        'timeout' => env('AUTH_SERVICE_TIMEOUT', 30),
        'retries' => env('AUTH_SERVICE_RETRIES', 3),
    ],

    'customer' => [
        'url' => env('CUSTOMER_SERVICE_URL', 'http://customer-service-v12:8000/api'),
        'timeout' => env('CUSTOMER_SERVICE_TIMEOUT', 30),
        'retries' => env('CUSTOMER_SERVICE_RETRIES', 3),
    ],

    'meal' => [
        'url' => env('MEAL_SERVICE_URL', 'http://meal-service-v12:8000/api'),
        'timeout' => env('MEAL_SERVICE_TIMEOUT', 30),
        'retries' => env('MEAL_SERVICE_RETRIES', 3),
    ],

    'payment' => [
        'url' => env('PAYMENT_SERVICE_URL', 'http://payment-service-v12:8000/api'),
        'timeout' => env('PAYMENT_SERVICE_TIMEOUT', 30),
        'retries' => env('PAYMENT_SERVICE_RETRIES', 3),
    ],

    'subscription' => [
        'url' => env('SUBSCRIPTION_SERVICE_URL', 'http://subscription-service-v12:8000/api'),
        'timeout' => env('SUBSCRIPTION_SERVICE_TIMEOUT', 30),
        'retries' => env('SUBSCRIPTION_SERVICE_RETRIES', 3),
    ],

    'kitchen' => [
        'url' => env('KITCHEN_SERVICE_URL', 'http://kitchen-service-v12:8000/api'),
        'timeout' => env('KITCHEN_SERVICE_TIMEOUT', 30),
        'retries' => env('KITCHEN_SERVICE_RETRIES', 3),
    ],

    'delivery' => [
        'url' => env('DELIVERY_SERVICE_URL', 'http://delivery-service-v12:8000/api'),
        'timeout' => env('DELIVERY_SERVICE_TIMEOUT', 30),
        'retries' => env('DELIVERY_SERVICE_RETRIES', 3),
    ],

    'admin' => [
        'url' => env('ADMIN_SERVICE_URL', 'http://admin-service-v12:8000/api'),
        'timeout' => env('ADMIN_SERVICE_TIMEOUT', 30),
        'retries' => env('ADMIN_SERVICE_RETRIES', 3),
    ],

    'analytics' => [
        'url' => env('ANALYTICS_SERVICE_URL', 'http://analytics-service-v12:8000/api'),
        'timeout' => env('ANALYTICS_SERVICE_TIMEOUT', 30),
        'retries' => env('ANALYTICS_SERVICE_RETRIES', 3),
    ],

    'notification' => [
        'url' => env('NOTIFICATION_SERVICE_URL', 'http://notification-service-v12:8000/api'),
        'timeout' => env('NOTIFICATION_SERVICE_TIMEOUT', 30),
        'retries' => env('NOTIFICATION_SERVICE_RETRIES', 3),
    ],

    'reporting' => [
        'url' => env('REPORTING_SERVICE_URL', 'http://reporting-service-v12:8000/api'),
        'timeout' => env('REPORTING_SERVICE_TIMEOUT', 30),
        'retries' => env('REPORTING_SERVICE_RETRIES', 3),
    ],
];

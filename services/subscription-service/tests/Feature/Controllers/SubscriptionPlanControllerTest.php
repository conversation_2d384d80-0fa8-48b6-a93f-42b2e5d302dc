<?php

namespace Tests\Feature\Controllers;

use App\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SubscriptionPlanControllerTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /**
     * Test getting all subscription plans.
     *
     * @return void
     */
    public function testIndex()
    {
        // Create test data
        $plan1 = factory(SubscriptionPlan::class)->create([
            'plan_type' => 'monthly',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_status' => true,
            'show_to_customer' => 'yes'
        ]);

        $plan2 = factory(SubscriptionPlan::class)->create([
            'plan_type' => 'weekly',
            'plan_quantity' => 7,
            'plan_period' => 'day',
            'plan_status' => true,
            'show_to_customer' => 'no'
        ]);

        // Test without filters
        $response = $this->json('GET', '/api/v2/subscription-plans');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'plan_name',
                             'plan_quantity',
                             'plan_period',
                             'plan_type',
                             'plan_start_date',
                             'plan_end_date',
                             'plan_status',
                             'show_to_customer',
                             'price',
                             'is_recurring',
                             'created_at',
                             'updated_at'
                         ]
                     ]
                 ])
                 ->assertJsonCount(2, 'data');

        // Test with plan_type filter
        $response = $this->json('GET', '/api/v2/subscription-plans?plan_type=monthly');

        $response->assertStatus(200)
                 ->assertJsonCount(1, 'data')
                 ->assertJson([
                     'data' => [
                         [
                             'id' => $plan1->id,
                             'plan_type' => 'monthly'
                         ]
                     ]
                 ]);
    }

    /**
     * Test getting a specific subscription plan.
     *
     * @return void
     */
    public function testShow()
    {
        // Create test data
        $plan = factory(SubscriptionPlan::class)->create([
            'plan_name' => 'Test Plan',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'price' => 100
        ]);

        // Test getting the plan
        $response = $this->json('GET', "/api/v2/subscription-plans/{$plan->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         'id',
                         'plan_name',
                         'plan_quantity',
                         'plan_period',
                         'plan_type',
                         'plan_start_date',
                         'plan_end_date',
                         'plan_status',
                         'show_to_customer',
                         'price',
                         'is_recurring',
                         'created_at',
                         'updated_at'
                     ]
                 ])
                 ->assertJson([
                     'data' => [
                         'id' => $plan->id,
                         'plan_name' => 'Test Plan',
                         'plan_quantity' => 30,
                         'plan_period' => 'day',
                         'plan_type' => 'monthly',
                         'price' => 100
                     ]
                 ]);

        // Test getting a non-existent plan
        $response = $this->json('GET', '/api/v2/subscription-plans/999');

        $response->assertStatus(404)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Subscription plan not found'
                 ]);
    }

    /**
     * Test creating a subscription plan.
     *
     * @return void
     */
    public function testStore()
    {
        $data = [
            'plan_name' => 'New Test Plan',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'plan_start_date' => Carbon::now()->format('Y-m-d'),
            'plan_end_date' => Carbon::now()->addYear()->format('Y-m-d'),
            'plan_status' => true,
            'show_to_customer' => 'yes',
            'price' => 100,
            'is_recurring' => true
        ];

        // Test creating the plan
        $response = $this->json('POST', '/api/v2/subscription-plans', $data);

        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'plan_name',
                         'plan_quantity',
                         'plan_period',
                         'plan_type',
                         'plan_start_date',
                         'plan_end_date',
                         'plan_status',
                         'show_to_customer',
                         'price',
                         'is_recurring',
                         'created_at',
                         'updated_at'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription plan created successfully',
                     'data' => [
                         'plan_name' => 'New Test Plan',
                         'plan_quantity' => 30,
                         'plan_period' => 'day',
                         'plan_type' => 'monthly',
                         'plan_status' => true,
                         'show_to_customer' => 'yes',
                         'price' => 100,
                         'is_recurring' => true
                     ]
                 ]);

        // Test validation errors
        $response = $this->json('POST', '/api/v2/subscription-plans', []);

        $response->assertStatus(422)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'errors' => [
                         'plan_name',
                         'plan_quantity',
                         'plan_period',
                         'plan_start_date',
                         'plan_end_date',
                         'price'
                     ]
                 ]);
    }

    /**
     * Test updating a subscription plan.
     *
     * @return void
     */
    public function testUpdate()
    {
        // Create test data
        $plan = factory(SubscriptionPlan::class)->create([
            'plan_name' => 'Original Plan',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'plan_status' => true,
            'show_to_customer' => 'yes',
            'price' => 100,
            'is_recurring' => true
        ]);

        $data = [
            'plan_name' => 'Updated Plan',
            'plan_quantity' => 7,
            'plan_period' => 'week',
            'plan_type' => 'weekly',
            'plan_status' => false,
            'show_to_customer' => 'no',
            'price' => 50,
            'is_recurring' => false
        ];

        // Test updating the plan
        $response = $this->json('PUT', "/api/v2/subscription-plans/{$plan->id}", $data);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'plan_name',
                         'plan_quantity',
                         'plan_period',
                         'plan_type',
                         'plan_start_date',
                         'plan_end_date',
                         'plan_status',
                         'show_to_customer',
                         'price',
                         'is_recurring',
                         'created_at',
                         'updated_at'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription plan updated successfully',
                     'data' => [
                         'id' => $plan->id,
                         'plan_name' => 'Updated Plan',
                         'plan_quantity' => 7,
                         'plan_period' => 'week',
                         'plan_type' => 'weekly',
                         'plan_status' => false,
                         'show_to_customer' => 'no',
                         'price' => 50,
                         'is_recurring' => false
                     ]
                 ]);

        // Test updating a non-existent plan
        $response = $this->json('PUT', '/api/v2/subscription-plans/999', $data);

        $response->assertStatus(404)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Subscription plan not found'
                 ]);
    }

    /**
     * Test deleting a subscription plan.
     *
     * @return void
     */
    public function testDestroy()
    {
        // Create test data
        $plan = factory(SubscriptionPlan::class)->create();

        // Test deleting the plan
        $response = $this->json('DELETE', "/api/v2/subscription-plans/{$plan->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription plan deleted successfully'
                 ]);

        // Verify the plan was deleted
        $this->assertDatabaseMissing('subscription_plans', ['id' => $plan->id]);

        // Test deleting a non-existent plan
        $response = $this->json('DELETE', '/api/v2/subscription-plans/999');

        $response->assertStatus(404)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Subscription plan not found'
                 ]);
    }

    /**
     * Test getting active subscription plans.
     *
     * @return void
     */
    public function testGetActivePlans()
    {
        // Create test data
        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay()
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => false,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay()
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->addDay(),
            'plan_end_date' => Carbon::now()->addDays(2)
        ]);

        // Test getting active plans
        $response = $this->json('GET', '/api/v2/subscription-plans/active');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'plan_name',
                             'plan_status'
                         ]
                     ]
                 ])
                 ->assertJsonCount(1, 'data');
    }

    /**
     * Test getting subscription plans visible to customers.
     *
     * @return void
     */
    public function testGetCustomerVisiblePlans()
    {
        // Create test data
        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'show_to_customer' => 'yes'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'show_to_customer' => 'no'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => false,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'show_to_customer' => 'yes'
        ]);

        // Test getting customer visible plans
        $response = $this->json('GET', '/api/v2/subscription-plans/customer');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'plan_name',
                             'show_to_customer'
                         ]
                     ]
                 ])
                 ->assertJsonCount(1, 'data')
                 ->assertJson([
                     'data' => [
                         [
                             'show_to_customer' => 'yes'
                         ]
                     ]
                 ]);
    }

    /**
     * Test getting subscription plans by type.
     *
     * @return void
     */
    public function testGetPlansByType()
    {
        // Create test data
        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'plan_type' => 'monthly'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'plan_type' => 'weekly'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => false,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'plan_type' => 'monthly'
        ]);

        // Test getting plans by type
        $response = $this->json('GET', '/api/v2/subscription-plans/type/monthly');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'plan_name',
                             'plan_type'
                         ]
                     ]
                 ])
                 ->assertJsonCount(1, 'data')
                 ->assertJson([
                     'data' => [
                         [
                             'plan_type' => 'monthly'
                         ]
                     ]
                 ]);
    }
}

<?php

namespace Tests\Feature\Controllers;

use App\Customer;
use App\Subscription;
use App\SubscriptionItem;
use App\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SubscriptionControllerTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /**
     * Test getting all subscriptions.
     *
     * @return void
     */
    public function testIndex()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription1 = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $subscription2 = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'paused'
        ]);

        // Test without filters
        $response = $this->json('GET', '/api/v2/subscriptions');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         '*' => [
                             'id',
                             'customer_id',
                             'plan_id',
                             'subscription_no',
                             'start_date',
                             'end_date',
                             'status',
                             'amount',
                             'discount',
                             'total',
                             'created_at',
                             'updated_at'
                         ]
                     ]
                 ])
                 ->assertJsonCount(2, 'data');

        // Test with status filter
        $response = $this->json('GET', '/api/v2/subscriptions?status=active');

        $response->assertStatus(200)
                 ->assertJsonCount(1, 'data')
                 ->assertJson([
                     'data' => [
                         [
                             'id' => $subscription1->id,
                             'status' => 'active'
                         ]
                     ]
                 ]);
    }

    /**
     * Test getting a specific subscription.
     *
     * @return void
     */
    public function testShow()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        factory(SubscriptionItem::class)->create([
            'subscription_id' => $subscription->id
        ]);

        // Test getting the subscription
        $response = $this->json('GET', "/api/v2/subscriptions/{$subscription->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'data' => [
                         'id',
                         'customer_id',
                         'plan_id',
                         'subscription_no',
                         'start_date',
                         'end_date',
                         'status',
                         'amount',
                         'discount',
                         'total',
                         'created_at',
                         'updated_at',
                         'customer',
                         'plan',
                         'items'
                     ]
                 ])
                 ->assertJson([
                     'data' => [
                         'id' => $subscription->id,
                         'customer_id' => $customer->id,
                         'plan_id' => $plan->id
                     ]
                 ]);

        // Test getting a non-existent subscription
        $response = $this->json('GET', '/api/v2/subscriptions/999');

        $response->assertStatus(404)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Subscription not found'
                 ]);
    }

    /**
     * Test creating a subscription.
     *
     * @return void
     */
    public function testStore()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create([
            'plan_quantity' => 30,
            'price' => 100
        ]);

        $data = [
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'start_date' => Carbon::now()->format('Y-m-d'),
            'payment_method' => 'credit_card',
            'payment_status' => 'paid',
            'transaction_id' => 'txn_123456',
            'discount' => 10,
            'auto_renew' => true,
            'notes' => 'Test subscription',
            'items' => [
                [
                    'meal_id' => 1,
                    'meal_name' => 'Test Meal',
                    'quantity' => 2,
                    'price' => 50,
                    'day_of_week' => 'Monday',
                    'meal_type' => 'lunch'
                ]
            ]
        ];

        // Test creating the subscription
        $response = $this->json('POST', '/api/v2/subscriptions', $data);

        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'customer_id',
                         'plan_id',
                         'subscription_no',
                         'start_date',
                         'end_date',
                         'status',
                         'amount',
                         'discount',
                         'total',
                         'payment_method',
                         'payment_status',
                         'transaction_id',
                         'auto_renew',
                         'notes',
                         'created_at',
                         'updated_at',
                         'customer',
                         'plan',
                         'items'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription created successfully',
                     'data' => [
                         'customer_id' => $customer->id,
                         'plan_id' => $plan->id,
                         'status' => 'active',
                         'amount' => 100,
                         'discount' => 10,
                         'total' => 90,
                         'payment_method' => 'credit_card',
                         'payment_status' => 'paid',
                         'transaction_id' => 'txn_123456',
                         'auto_renew' => true,
                         'notes' => 'Test subscription'
                     ]
                 ]);

        // Test validation errors
        $response = $this->json('POST', '/api/v2/subscriptions', []);

        $response->assertStatus(422)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'errors' => [
                         'customer_id',
                         'plan_id'
                     ]
                 ]);
    }

    /**
     * Test updating a subscription.
     *
     * @return void
     */
    public function testUpdate()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active',
            'payment_method' => 'cash',
            'payment_status' => 'pending',
            'auto_renew' => false
        ]);

        factory(SubscriptionItem::class)->create([
            'subscription_id' => $subscription->id,
            'meal_id' => 1,
            'meal_name' => 'Original Meal',
            'quantity' => 1,
            'price' => 50
        ]);

        $data = [
            'status' => 'paused',
            'payment_method' => 'credit_card',
            'payment_status' => 'paid',
            'transaction_id' => 'txn_updated',
            'auto_renew' => true,
            'notes' => 'Updated notes',
            'items' => [
                [
                    'meal_id' => 2,
                    'meal_name' => 'Updated Meal',
                    'quantity' => 3,
                    'price' => 60,
                    'day_of_week' => 'Tuesday',
                    'meal_type' => 'dinner'
                ]
            ]
        ];

        // Test updating the subscription
        $response = $this->json('PUT', "/api/v2/subscriptions/{$subscription->id}", $data);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'customer_id',
                         'plan_id',
                         'subscription_no',
                         'start_date',
                         'end_date',
                         'status',
                         'amount',
                         'discount',
                         'total',
                         'payment_method',
                         'payment_status',
                         'transaction_id',
                         'auto_renew',
                         'notes',
                         'created_at',
                         'updated_at',
                         'customer',
                         'plan',
                         'items'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription updated successfully',
                     'data' => [
                         'id' => $subscription->id,
                         'status' => 'paused',
                         'payment_method' => 'credit_card',
                         'payment_status' => 'paid',
                         'transaction_id' => 'txn_updated',
                         'auto_renew' => true,
                         'notes' => 'Updated notes'
                     ]
                 ]);

        // Test updating a non-existent subscription
        $response = $this->json('PUT', '/api/v2/subscriptions/999', $data);

        $response->assertStatus(404)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Subscription not found'
                 ]);
    }

    /**
     * Test deleting a subscription.
     *
     * @return void
     */
    public function testDestroy()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        factory(SubscriptionItem::class)->create([
            'subscription_id' => $subscription->id
        ]);

        // Test deleting the subscription
        $response = $this->json('DELETE', "/api/v2/subscriptions/{$subscription->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription deleted successfully'
                 ]);

        // Verify the subscription was deleted
        $this->assertDatabaseMissing('subscriptions', ['id' => $subscription->id]);
        $this->assertDatabaseMissing('subscription_items', ['subscription_id' => $subscription->id]);

        // Test deleting a non-existent subscription
        $response = $this->json('DELETE', '/api/v2/subscriptions/999');

        $response->assertStatus(404)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Subscription not found'
                 ]);
    }

    /**
     * Test pausing a subscription.
     *
     * @return void
     */
    public function testPause()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $data = [
            'reason' => 'Going on vacation',
            'resume_date' => Carbon::now()->addDays(10)->format('Y-m-d')
        ];

        // Test pausing the subscription
        $response = $this->json('POST', "/api/v2/subscriptions/{$subscription->id}/pause", $data);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'status',
                         'pause_history'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription paused successfully',
                     'data' => [
                         'id' => $subscription->id,
                         'status' => 'paused'
                     ]
                 ]);

        // Verify the pause history
        $updatedSubscription = Subscription::find($subscription->id);
        $this->assertEquals('paused', $updatedSubscription->status);
        $this->assertNotNull($updatedSubscription->pause_history);
        $pauseHistory = $updatedSubscription->pause_history;
        $this->assertEquals('Going on vacation', $pauseHistory[0]['reason']);
        $this->assertEquals(Carbon::now()->addDays(10)->format('Y-m-d'), $pauseHistory[0]['resume_date']);
    }

    /**
     * Test resuming a subscription.
     *
     * @return void
     */
    public function testResume()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'paused',
            'pause_history' => [
                [
                    'paused_at' => Carbon::now()->subDays(5)->toDateTimeString(),
                    'reason' => 'Going on vacation',
                    'resume_date' => Carbon::now()->addDays(5)->toDateTimeString()
                ]
            ]
        ]);

        // Test resuming the subscription
        $response = $this->json('POST', "/api/v2/subscriptions/{$subscription->id}/resume");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'status',
                         'pause_history'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription resumed successfully',
                     'data' => [
                         'id' => $subscription->id,
                         'status' => 'active'
                     ]
                 ]);

        // Verify the pause history
        $updatedSubscription = Subscription::find($subscription->id);
        $this->assertEquals('active', $updatedSubscription->status);
        $this->assertNotNull($updatedSubscription->pause_history);
        $pauseHistory = $updatedSubscription->pause_history;
        $this->assertNotNull($pauseHistory[0]['resumed_at']);
    }

    /**
     * Test cancelling a subscription.
     *
     * @return void
     */
    public function testCancel()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $data = [
            'reason' => 'Not satisfied with the service'
        ];

        // Test cancelling the subscription
        $response = $this->json('POST', "/api/v2/subscriptions/{$subscription->id}/cancel", $data);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'status',
                         'notes'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription cancelled successfully',
                     'data' => [
                         'id' => $subscription->id,
                         'status' => 'cancelled',
                         'notes' => 'Not satisfied with the service'
                     ]
                 ]);
    }

    /**
     * Test renewing a subscription.
     *
     * @return void
     */
    public function testRenew()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create([
            'plan_quantity' => 30
        ]);

        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active',
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);

        $data = [
            'days' => 60
        ];

        // Test renewing the subscription
        $response = $this->json('POST', "/api/v2/subscriptions/{$subscription->id}/renew", $data);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         'id',
                         'start_date',
                         'end_date'
                     ]
                 ])
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Subscription renewed successfully'
                 ]);

        // Verify the renewal
        $updatedSubscription = Subscription::find($subscription->id);
        $this->assertEquals($endDate->addDay()->format('Y-m-d'), $updatedSubscription->start_date->format('Y-m-d'));
        $this->assertEquals($endDate->addDays(60)->format('Y-m-d'), $updatedSubscription->end_date->format('Y-m-d'));
    }
}

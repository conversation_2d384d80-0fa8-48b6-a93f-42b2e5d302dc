<?php

namespace Tests\Unit\Services;

use App\Customer;
use App\Services\SubscriptionService;
use App\Subscription;
use App\SubscriptionItem;
use App\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class SubscriptionServiceTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /**
     * @var SubscriptionService
     */
    protected $subscriptionService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->subscriptionService = new SubscriptionService();
    }

    /**
     * Test getting all subscriptions.
     *
     * @return void
     */
    public function testGetAllSubscriptions()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription1 = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active'
        ]);

        $subscription2 = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'paused'
        ]);

        // Test without filters
        $subscriptions = $this->subscriptionService->getAllSubscriptions();
        $this->assertCount(2, $subscriptions);

        // Test with status filter
        $subscriptions = $this->subscriptionService->getAllSubscriptions(['status' => 'active']);
        $this->assertCount(1, $subscriptions);
        $this->assertEquals($subscription1->id, $subscriptions->first()->id);

        // Test with customer_id filter
        $subscriptions = $this->subscriptionService->getAllSubscriptions(['customer_id' => $customer->id]);
        $this->assertCount(2, $subscriptions);
    }

    /**
     * Test getting a subscription by ID.
     *
     * @return void
     */
    public function testGetSubscriptionById()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        // Test getting the subscription
        $result = $this->subscriptionService->getSubscriptionById($subscription->id);
        $this->assertNotNull($result);
        $this->assertEquals($subscription->id, $result->id);

        // Test getting a non-existent subscription
        $result = $this->subscriptionService->getSubscriptionById(999);
        $this->assertNull($result);
    }

    /**
     * Test creating a subscription.
     *
     * @return void
     */
    public function testCreateSubscription()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create([
            'plan_quantity' => 30,
            'price' => 100
        ]);

        $data = [
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'payment_method' => 'credit_card',
            'payment_status' => 'paid',
            'transaction_id' => 'txn_123456',
            'discount' => 10,
            'auto_renew' => true,
            'notes' => 'Test subscription',
            'items' => [
                [
                    'meal_id' => 1,
                    'meal_name' => 'Test Meal',
                    'quantity' => 2,
                    'price' => 50,
                    'day_of_week' => 'Monday',
                    'meal_type' => 'lunch'
                ]
            ]
        ];

        // Test creating the subscription
        $subscription = $this->subscriptionService->createSubscription($data);

        $this->assertNotNull($subscription);
        $this->assertEquals($customer->id, $subscription->customer_id);
        $this->assertEquals($plan->id, $subscription->plan_id);
        $this->assertEquals('active', $subscription->status);
        $this->assertEquals(100, $subscription->amount);
        $this->assertEquals(10, $subscription->discount);
        $this->assertEquals(90, $subscription->total);
        $this->assertEquals('credit_card', $subscription->payment_method);
        $this->assertEquals('paid', $subscription->payment_status);
        $this->assertEquals('txn_123456', $subscription->transaction_id);
        $this->assertTrue($subscription->auto_renew);
        $this->assertEquals('Test subscription', $subscription->notes);

        // Check that the subscription items were created
        $this->assertCount(1, $subscription->items);
        $item = $subscription->items->first();
        $this->assertEquals(1, $item->meal_id);
        $this->assertEquals('Test Meal', $item->meal_name);
        $this->assertEquals(2, $item->quantity);
        $this->assertEquals(50, $item->price);
        $this->assertEquals(100, $item->total);
        $this->assertEquals('Monday', $item->day_of_week);
        $this->assertEquals('lunch', $item->meal_type);
    }

    /**
     * Test updating a subscription.
     *
     * @return void
     */
    public function testUpdateSubscription()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id,
            'status' => 'active',
            'payment_method' => 'cash',
            'payment_status' => 'pending',
            'auto_renew' => false
        ]);

        factory(SubscriptionItem::class)->create([
            'subscription_id' => $subscription->id,
            'meal_id' => 1,
            'meal_name' => 'Original Meal',
            'quantity' => 1,
            'price' => 50
        ]);

        $data = [
            'status' => 'paused',
            'payment_method' => 'credit_card',
            'payment_status' => 'paid',
            'transaction_id' => 'txn_updated',
            'auto_renew' => true,
            'notes' => 'Updated notes',
            'items' => [
                [
                    'meal_id' => 2,
                    'meal_name' => 'Updated Meal',
                    'quantity' => 3,
                    'price' => 60,
                    'day_of_week' => 'Tuesday',
                    'meal_type' => 'dinner'
                ]
            ]
        ];

        // Test updating the subscription
        $updatedSubscription = $this->subscriptionService->updateSubscription($subscription->id, $data);

        $this->assertNotNull($updatedSubscription);
        $this->assertEquals('paused', $updatedSubscription->status);
        $this->assertEquals('credit_card', $updatedSubscription->payment_method);
        $this->assertEquals('paid', $updatedSubscription->payment_status);
        $this->assertEquals('txn_updated', $updatedSubscription->transaction_id);
        $this->assertTrue($updatedSubscription->auto_renew);
        $this->assertEquals('Updated notes', $updatedSubscription->notes);

        // Check that the subscription items were updated
        $this->assertCount(1, $updatedSubscription->items);
        $item = $updatedSubscription->items->first();
        $this->assertEquals(2, $item->meal_id);
        $this->assertEquals('Updated Meal', $item->meal_name);
        $this->assertEquals(3, $item->quantity);
        $this->assertEquals(60, $item->price);
        $this->assertEquals(180, $item->total);
        $this->assertEquals('Tuesday', $item->day_of_week);
        $this->assertEquals('dinner', $item->meal_type);
    }

    /**
     * Test deleting a subscription.
     *
     * @return void
     */
    public function testDeleteSubscription()
    {
        // Create test data
        $customer = factory(Customer::class)->create();
        $plan = factory(SubscriptionPlan::class)->create();

        $subscription = factory(Subscription::class)->create([
            'customer_id' => $customer->id,
            'plan_id' => $plan->id
        ]);

        factory(SubscriptionItem::class)->create([
            'subscription_id' => $subscription->id
        ]);

        // Test deleting the subscription
        $result = $this->subscriptionService->deleteSubscription($subscription->id);
        $this->assertTrue($result);

        // Check that the subscription and its items were deleted
        $this->assertNull(Subscription::find($subscription->id));
        $this->assertCount(0, SubscriptionItem::where('subscription_id', $subscription->id)->get());

        // Test deleting a non-existent subscription
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->subscriptionService->deleteSubscription(999);
    }
}

<?php

namespace Tests\Unit\Services;

use App\Services\SubscriptionPlanService;
use App\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class SubscriptionPlanServiceTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /**
     * @var SubscriptionPlanService
     */
    protected $subscriptionPlanService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->subscriptionPlanService = new SubscriptionPlanService();
    }

    /**
     * Test getting all subscription plans.
     *
     * @return void
     */
    public function testGetAllPlans()
    {
        // Create test data
        $plan1 = factory(SubscriptionPlan::class)->create([
            'plan_type' => 'monthly',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_status' => true,
            'show_to_customer' => 'yes'
        ]);

        $plan2 = factory(SubscriptionPlan::class)->create([
            'plan_type' => 'weekly',
            'plan_quantity' => 7,
            'plan_period' => 'day',
            'plan_status' => true,
            'show_to_customer' => 'no'
        ]);

        $plan3 = factory(SubscriptionPlan::class)->create([
            'plan_type' => 'monthly',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_status' => false,
            'show_to_customer' => 'yes'
        ]);

        // Test without filters
        $plans = $this->subscriptionPlanService->getAllPlans();
        $this->assertCount(3, $plans);

        // Test with plan_type filter
        $plans = $this->subscriptionPlanService->getAllPlans(['plan_type' => 'monthly']);
        $this->assertCount(2, $plans);

        // Test with plan_quantity filter
        $plans = $this->subscriptionPlanService->getAllPlans(['plan_quantity' => 7]);
        $this->assertCount(1, $plans);
        $this->assertEquals($plan2->id, $plans->first()->id);

        // Test with active filter
        $plans = $this->subscriptionPlanService->getAllPlans(['active' => true]);
        $this->assertCount(2, $plans);

        // Test with show_to_customer filter
        $plans = $this->subscriptionPlanService->getAllPlans(['show_to_customer' => 'yes']);
        $this->assertCount(2, $plans);
    }

    /**
     * Test getting a subscription plan by ID.
     *
     * @return void
     */
    public function testGetPlanById()
    {
        // Create test data
        $plan = factory(SubscriptionPlan::class)->create();

        // Test getting the plan
        $result = $this->subscriptionPlanService->getPlanById($plan->id);
        $this->assertNotNull($result);
        $this->assertEquals($plan->id, $result->id);

        // Test getting a non-existent plan
        $result = $this->subscriptionPlanService->getPlanById(999);
        $this->assertNull($result);
    }

    /**
     * Test creating a subscription plan.
     *
     * @return void
     */
    public function testCreatePlan()
    {
        $data = [
            'plan_name' => 'Test Plan',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'plan_start_date' => Carbon::now()->format('Y-m-d'),
            'plan_end_date' => Carbon::now()->addYear()->format('Y-m-d'),
            'plan_status' => true,
            'show_to_customer' => 'yes',
            'price' => 100,
            'is_recurring' => true
        ];

        // Test creating the plan
        $plan = $this->subscriptionPlanService->createPlan($data);

        $this->assertNotNull($plan);
        $this->assertEquals('Test Plan', $plan->plan_name);
        $this->assertEquals(30, $plan->plan_quantity);
        $this->assertEquals('day', $plan->plan_period);
        $this->assertEquals('monthly', $plan->plan_type);
        $this->assertTrue($plan->plan_status);
        $this->assertEquals('yes', $plan->show_to_customer);
        $this->assertEquals(100, $plan->price);
        $this->assertTrue($plan->is_recurring);
    }

    /**
     * Test updating a subscription plan.
     *
     * @return void
     */
    public function testUpdatePlan()
    {
        // Create test data
        $plan = factory(SubscriptionPlan::class)->create([
            'plan_name' => 'Original Plan',
            'plan_quantity' => 30,
            'plan_period' => 'day',
            'plan_type' => 'monthly',
            'plan_status' => true,
            'show_to_customer' => 'yes',
            'price' => 100,
            'is_recurring' => true
        ]);

        $data = [
            'plan_name' => 'Updated Plan',
            'plan_quantity' => 7,
            'plan_period' => 'week',
            'plan_type' => 'weekly',
            'plan_status' => false,
            'show_to_customer' => 'no',
            'price' => 50,
            'is_recurring' => false
        ];

        // Test updating the plan
        $updatedPlan = $this->subscriptionPlanService->updatePlan($plan->id, $data);

        $this->assertNotNull($updatedPlan);
        $this->assertEquals('Updated Plan', $updatedPlan->plan_name);
        $this->assertEquals(7, $updatedPlan->plan_quantity);
        $this->assertEquals('week', $updatedPlan->plan_period);
        $this->assertEquals('weekly', $updatedPlan->plan_type);
        $this->assertFalse($updatedPlan->plan_status);
        $this->assertEquals('no', $updatedPlan->show_to_customer);
        $this->assertEquals(50, $updatedPlan->price);
        $this->assertFalse($updatedPlan->is_recurring);
    }

    /**
     * Test deleting a subscription plan.
     *
     * @return void
     */
    public function testDeletePlan()
    {
        // Create test data
        $plan = factory(SubscriptionPlan::class)->create();

        // Test deleting the plan
        $result = $this->subscriptionPlanService->deletePlan($plan->id);
        $this->assertTrue($result);

        // Check that the plan was deleted
        $this->assertNull(SubscriptionPlan::find($plan->id));

        // Test deleting a non-existent plan
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->subscriptionPlanService->deletePlan(999);
    }

    /**
     * Test getting active plans.
     *
     * @return void
     */
    public function testGetActivePlans()
    {
        // Create test data
        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay()
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => false,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay()
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->addDay(),
            'plan_end_date' => Carbon::now()->addDays(2)
        ]);

        // Test getting active plans
        $plans = $this->subscriptionPlanService->getActivePlans();
        $this->assertCount(1, $plans);
    }

    /**
     * Test getting plans visible to customers.
     *
     * @return void
     */
    public function testGetCustomerVisiblePlans()
    {
        // Create test data
        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'show_to_customer' => 'yes'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'show_to_customer' => 'no'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => false,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'show_to_customer' => 'yes'
        ]);

        // Test getting customer visible plans
        $plans = $this->subscriptionPlanService->getCustomerVisiblePlans();
        $this->assertCount(1, $plans);
    }

    /**
     * Test getting plans by type.
     *
     * @return void
     */
    public function testGetPlansByType()
    {
        // Create test data
        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'plan_type' => 'monthly'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => true,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'plan_type' => 'weekly'
        ]);

        factory(SubscriptionPlan::class)->create([
            'plan_status' => false,
            'plan_start_date' => Carbon::now()->subDay(),
            'plan_end_date' => Carbon::now()->addDay(),
            'plan_type' => 'monthly'
        ]);

        // Test getting plans by type
        $plans = $this->subscriptionPlanService->getPlansByType('monthly');
        $this->assertCount(1, $plans);
    }
}

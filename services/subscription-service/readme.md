# Subscription Service

This microservice handles subscription management for the meal delivery system. It provides APIs for managing subscription plans, customer subscriptions, and subscription-related operations.

## Features

- Subscription plan management
- Customer subscription management
- Subscription operations (pause, resume, cancel, renew)
- Subscription reporting and analytics

## Requirements

- PHP 7.2+
- Laravel 5.8
- MySQL 5.7+
- Composer

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-organization/subscription-service.git
cd subscription-service
```

2. Install dependencies:
```bash
composer install
```

3. Copy the environment file:
```bash
cp .env.example .env
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Configure your database in the `.env` file:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=subscription_service
DB_USERNAME=root
DB_PASSWORD=
```

6. Run migrations:
```bash
php artisan migrate
```

7. Seed the database (optional):
```bash
php artisan db:seed
```

## Running the Service

### Using PHP's Built-in Server

```bash
php artisan serve
```

### Using Docker

```bash
docker-compose up -d
```

## API Documentation

The API documentation is available in the OpenAPI format. You can view it by:

1. Opening the `openapi.yaml` file in an OpenAPI viewer
2. Using Swagger UI: `http://localhost:8000/api/documentation`

## Testing

Run the tests with:

```bash
php artisan test
```

Or using PHPUnit directly:

```bash
vendor/bin/phpunit
```

## API Endpoints

### Subscription Plans

- `GET /api/v2/subscription-plans` - Get all subscription plans
- `POST /api/v2/subscription-plans` - Create a new subscription plan
- `GET /api/v2/subscription-plans/{id}` - Get a specific subscription plan
- `PUT /api/v2/subscription-plans/{id}` - Update a subscription plan
- `DELETE /api/v2/subscription-plans/{id}` - Delete a subscription plan
- `GET /api/v2/subscription-plans/active` - Get active subscription plans
- `GET /api/v2/subscription-plans/customer` - Get subscription plans visible to customers
- `GET /api/v2/subscription-plans/type/{type}` - Get subscription plans by type

### Subscriptions

- `GET /api/v2/subscriptions` - Get all subscriptions
- `POST /api/v2/subscriptions` - Create a new subscription
- `GET /api/v2/subscriptions/{id}` - Get a specific subscription
- `PUT /api/v2/subscriptions/{id}` - Update a subscription
- `DELETE /api/v2/subscriptions/{id}` - Delete a subscription
- `POST /api/v2/subscriptions/{id}/pause` - Pause a subscription
- `POST /api/v2/subscriptions/{id}/resume` - Resume a subscription
- `POST /api/v2/subscriptions/{id}/cancel` - Cancel a subscription
- `POST /api/v2/subscriptions/{id}/renew` - Renew a subscription

## CI/CD Pipeline

This service uses GitLab CI/CD for continuous integration and deployment. The pipeline includes:

1. Building the Docker image
2. Running tests
3. Code quality checks
4. Deployment to staging (automatic on develop branch)
5. Deployment to production (manual on master branch)

## Kong API Gateway Integration

This service is integrated with Kong API Gateway. The configuration is available in the `kong.yaml` file.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

_format_version: "2.1"
_transform: true

services:
  - name: subscription-service
    url: http://subscription-service:8000
    routes:
      - name: subscription-plans-route
        paths:
          - /v2/subscription-plans
          - /v2/subscription-plans/active
          - /v2/subscription-plans/customer
          - /v2/subscription-plans/type
        strip_path: false
        methods:
          - GET
          - POST
          - PUT
          - DELETE
        protocols:
          - http
          - https
      - name: subscription-plans-id-route
        paths:
          - /v2/subscription-plans/\d+
        strip_path: false
        methods:
          - GET
          - PUT
          - DELETE
        protocols:
          - http
          - https
      - name: subscriptions-route
        paths:
          - /v2/subscriptions
        strip_path: false
        methods:
          - GET
          - POST
          - PUT
          - DELETE
        protocols:
          - http
          - https
      - name: subscriptions-id-route
        paths:
          - /v2/subscriptions/\d+
        strip_path: false
        methods:
          - GET
          - PUT
          - DELETE
        protocols:
          - http
          - https
      - name: subscriptions-actions-route
        paths:
          - /v2/subscriptions/\d+/pause
          - /v2/subscriptions/\d+/resume
          - /v2/subscriptions/\d+/cancel
          - /v2/subscriptions/\d+/renew
        strip_path: false
        methods:
          - POST
        protocols:
          - http
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
      - name: key-auth
        config:
          key_names:
            - apikey
          key_in_body: false
          hide_credentials: true
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: http-log
        config:
          http_endpoint: http://logger:8080/log
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          headers:
            x-service-name: subscription-service

consumers:
  - username: subscription-service-client
    keyauth_credentials:
      - key: your-api-key-here

<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// API v1 routes (for backward compatibility)
Route::prefix('v1')->group(function () {
    // Subscription Plans
    Route::apiResource('subscription-plans', 'Api\SubscriptionPlanController');
    Route::get('subscription-plans/active', 'Api\SubscriptionPlanController@getActivePlans');
    Route::get('subscription-plans/customer', 'Api\SubscriptionPlanController@getCustomerVisiblePlans');
    Route::get('subscription-plans/type/{type}', 'Api\SubscriptionPlanController@getPlansByType');

    // Subscriptions
    Route::apiResource('subscriptions', 'Api\SubscriptionController');
    Route::post('subscriptions/{id}/pause', 'Api\SubscriptionController@pause');
    Route::post('subscriptions/{id}/resume', 'Api\SubscriptionController@resume');
    Route::post('subscriptions/{id}/cancel', 'Api\SubscriptionController@cancel');
    Route::post('subscriptions/{id}/renew', 'Api\SubscriptionController@renew');
});

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->group(function () {
    // Subscription Plans
    Route::apiResource('subscription-plans', 'Api\SubscriptionPlanController');
    Route::get('subscription-plans/active', 'Api\SubscriptionPlanController@getActivePlans');
    Route::get('subscription-plans/customer', 'Api\SubscriptionPlanController@getCustomerVisiblePlans');
    Route::get('subscription-plans/type/{type}', 'Api\SubscriptionPlanController@getPlansByType');

    // Subscriptions
    Route::apiResource('subscriptions', 'Api\SubscriptionController');
    Route::post('subscriptions/{id}/pause', 'Api\SubscriptionController@pause');
    Route::post('subscriptions/{id}/resume', 'Api\SubscriptionController@resume');
    Route::post('subscriptions/{id}/cancel', 'Api\SubscriptionController@cancel');
    Route::post('subscriptions/{id}/renew', 'Api\SubscriptionController@renew');
});

<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSubscriptionLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscription_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('company_id')->default(1);
            $table->integer('unit_id')->default(1);
            $table->integer('total_orders')->default(0);
            $table->integer('sms_sent')->default(0);
            $table->integer('email_sent')->default(0);
            $table->integer('active_customer')->default(0);
            $table->integer('admin_account')->default(0);
            $table->integer('user_account')->default(0);
            $table->integer('kitchen_count')->default(0);
            $table->date('date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscription_logs');
    }
}

<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSubscriptionPlansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('company_id')->default(1);
            $table->integer('unit_id')->default(1);
            $table->string('plan_name');
            $table->integer('plan_quantity')->comment('Number of days/weeks/months');
            $table->string('plan_period')->comment('day, week, month, year');
            $table->string('plan_type')->nullable();
            $table->date('plan_start_date');
            $table->date('plan_end_date');
            $table->integer('fk_promo_code')->nullable();
            $table->boolean('plan_status')->default(true);
            $table->enum('show_to_customer', ['yes', 'no', 'admin'])->default('yes');
            $table->integer('fk_kitchen_code')->nullable();
            $table->decimal('price', 10, 2)->default(0);
            $table->boolean('is_recurring')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscription_plans');
    }
}

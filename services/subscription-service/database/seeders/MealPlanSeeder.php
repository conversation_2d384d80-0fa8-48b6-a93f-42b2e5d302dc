<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MealPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mealPlans = [
            // Delhi Public School Plans
            [
                'school_id' => 1, // Delhi Public School, Vasant Kunj
                'plan_name' => 'DPS Healthy Lunch Combo',
                'description' => 'Nutritious lunch combo with dal, rice, vegetable, roti, and seasonal fruit',
                'meal_type' => 'lunch',
                'base_price' => 45.00,
                'plan_duration_days' => 30,
                'nutritional_info' => json_encode([
                    'calories' => 420,
                    'protein' => '15g',
                    'carbs' => '65g',
                    'fat' => '12g',
                    'fiber' => '8g'
                ]),
                'dietary_tags' => json_encode(['vegetarian', 'no_onion_garlic', 'nut_free']),
                'is_vegetarian' => true,
                'is_vegan' => false,
                'is_jain' => true,
                'is_gluten_free' => false,
                'spice_level' => 'mild',
                'meal_components' => json_encode([
                    'main' => 'Dal Rice',
                    'side' => 'Mixed Vegetable',
                    'bread' => 'Whole Wheat Roti',
                    'extras' => ['Pickle', 'Seasonal Fruit']
                ]),
                'allergen_info' => json_encode(['gluten']),
                'preparation_time_minutes' => 45,
                'serving_size' => 'regular',
                'max_daily_capacity' => 200,
                'current_subscriptions' => 0,
                'rating_average' => 4.5,
                'rating_count' => 0,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_id' => 1,
                'plan_name' => 'DPS Morning Energy Snack',
                'description' => 'Healthy morning snack with fruits, nuts, and energy bars',
                'meal_type' => 'morning_snack',
                'base_price' => 25.00,
                'plan_duration_days' => 30,
                'nutritional_info' => json_encode([
                    'calories' => 180,
                    'protein' => '6g',
                    'carbs' => '28g',
                    'fat' => '6g',
                    'fiber' => '4g'
                ]),
                'dietary_tags' => json_encode(['vegetarian', 'vegan', 'gluten_free']),
                'is_vegetarian' => true,
                'is_vegan' => true,
                'is_jain' => true,
                'is_gluten_free' => true,
                'spice_level' => 'none',
                'meal_components' => json_encode([
                    'main' => 'Seasonal Fruit Bowl',
                    'extras' => ['Mixed Nuts', 'Energy Bar']
                ]),
                'allergen_info' => json_encode(['nuts']),
                'preparation_time_minutes' => 15,
                'serving_size' => 'small',
                'max_daily_capacity' => 150,
                'current_subscriptions' => 0,
                'rating_average' => 4.3,
                'rating_count' => 0,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // Modern School Plans
            [
                'school_id' => 2, // Modern School, Barakhamba Road
                'plan_name' => 'Modern School Premium Lunch',
                'description' => 'Premium lunch with paneer curry, jeera rice, mixed dal, and dessert',
                'meal_type' => 'lunch',
                'base_price' => 55.00,
                'plan_duration_days' => 30,
                'nutritional_info' => json_encode([
                    'calories' => 480,
                    'protein' => '18g',
                    'carbs' => '70g',
                    'fat' => '15g',
                    'fiber' => '10g'
                ]),
                'dietary_tags' => json_encode(['vegetarian', 'protein_rich']),
                'is_vegetarian' => true,
                'is_vegan' => false,
                'is_jain' => false,
                'is_gluten_free' => false,
                'spice_level' => 'medium',
                'meal_components' => json_encode([
                    'main' => 'Paneer Curry',
                    'rice' => 'Jeera Rice',
                    'dal' => 'Mixed Dal',
                    'bread' => 'Butter Naan',
                    'dessert' => 'Kheer'
                ]),
                'allergen_info' => json_encode(['dairy', 'gluten']),
                'preparation_time_minutes' => 60,
                'serving_size' => 'regular',
                'max_daily_capacity' => 180,
                'current_subscriptions' => 0,
                'rating_average' => 4.7,
                'rating_count' => 0,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'school_id' => 2,
                'plan_name' => 'Modern School Afternoon Delight',
                'description' => 'Light afternoon snack with sandwiches and fresh juice',
                'meal_type' => 'afternoon_snack',
                'base_price' => 30.00,
                'plan_duration_days' => 30,
                'nutritional_info' => json_encode([
                    'calories' => 220,
                    'protein' => '8g',
                    'carbs' => '35g',
                    'fat' => '7g',
                    'fiber' => '5g'
                ]),
                'dietary_tags' => json_encode(['vegetarian', 'fresh']),
                'is_vegetarian' => true,
                'is_vegan' => false,
                'is_jain' => false,
                'is_gluten_free' => false,
                'spice_level' => 'mild',
                'meal_components' => json_encode([
                    'main' => 'Vegetable Sandwich',
                    'drink' => 'Fresh Orange Juice',
                    'extras' => ['Chips']
                ]),
                'allergen_info' => json_encode(['gluten', 'dairy']),
                'preparation_time_minutes' => 20,
                'serving_size' => 'small',
                'max_daily_capacity' => 120,
                'current_subscriptions' => 0,
                'rating_average' => 4.2,
                'rating_count' => 0,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // Ryan International School Plans
            [
                'school_id' => 3, // Ryan International School, Mayur Vihar
                'plan_name' => 'Ryan Balanced Lunch Box',
                'description' => 'Balanced lunch with rajma, rice, sabzi, roti, and curd',
                'meal_type' => 'lunch',
                'base_price' => 40.00,
                'plan_duration_days' => 30,
                'nutritional_info' => json_encode([
                    'calories' => 400,
                    'protein' => '16g',
                    'carbs' => '62g',
                    'fat' => '11g',
                    'fiber' => '9g'
                ]),
                'dietary_tags' => json_encode(['vegetarian', 'protein_rich', 'probiotic']),
                'is_vegetarian' => true,
                'is_vegan' => false,
                'is_jain' => false,
                'is_gluten_free' => false,
                'spice_level' => 'mild',
                'meal_components' => json_encode([
                    'main' => 'Rajma Curry',
                    'rice' => 'Steamed Rice',
                    'side' => 'Seasonal Vegetable',
                    'bread' => 'Chapati',
                    'dairy' => 'Fresh Curd'
                ]),
                'allergen_info' => json_encode(['dairy', 'gluten']),
                'preparation_time_minutes' => 50,
                'serving_size' => 'regular',
                'max_daily_capacity' => 160,
                'current_subscriptions' => 0,
                'rating_average' => 4.4,
                'rating_count' => 0,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // Sardar Patel Vidyalaya Plans
            [
                'school_id' => 6, // Sardar Patel Vidyalaya
                'plan_name' => 'SPV Gourmet Lunch',
                'description' => 'Gourmet lunch with special preparations and organic ingredients',
                'meal_type' => 'lunch',
                'base_price' => 65.00,
                'plan_duration_days' => 30,
                'nutritional_info' => json_encode([
                    'calories' => 520,
                    'protein' => '20g',
                    'carbs' => '75g',
                    'fat' => '18g',
                    'fiber' => '12g'
                ]),
                'dietary_tags' => json_encode(['vegetarian', 'organic', 'gourmet']),
                'is_vegetarian' => true,
                'is_vegan' => false,
                'is_jain' => true,
                'is_gluten_free' => false,
                'spice_level' => 'medium',
                'meal_components' => json_encode([
                    'main' => 'Organic Dal Makhani',
                    'rice' => 'Basmati Pulao',
                    'side' => 'Organic Vegetables',
                    'bread' => 'Multigrain Roti',
                    'dessert' => 'Organic Fruit Salad'
                ]),
                'allergen_info' => json_encode(['dairy', 'gluten']),
                'preparation_time_minutes' => 75,
                'serving_size' => 'large',
                'max_daily_capacity' => 100,
                'current_subscriptions' => 0,
                'rating_average' => 4.8,
                'rating_count' => 0,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // The Heritage School Plans
            [
                'school_id' => 7, // The Heritage School, Vasant Kunj
                'plan_name' => 'Heritage Nutritious Combo',
                'description' => 'Nutritionist-designed meal with quinoa, vegetables, and protein',
                'meal_type' => 'lunch',
                'base_price' => 50.00,
                'plan_duration_days' => 30,
                'nutritional_info' => json_encode([
                    'calories' => 450,
                    'protein' => '17g',
                    'carbs' => '68g',
                    'fat' => '14g',
                    'fiber' => '11g'
                ]),
                'dietary_tags' => json_encode(['vegetarian', 'superfood', 'nutritionist_approved']),
                'is_vegetarian' => true,
                'is_vegan' => true,
                'is_jain' => true,
                'is_gluten_free' => true,
                'spice_level' => 'mild',
                'meal_components' => json_encode([
                    'main' => 'Quinoa Bowl',
                    'protein' => 'Chickpea Curry',
                    'side' => 'Steamed Vegetables',
                    'extras' => ['Hummus', 'Whole Grain Crackers']
                ]),
                'allergen_info' => json_encode([]),
                'preparation_time_minutes' => 40,
                'serving_size' => 'regular',
                'max_daily_capacity' => 140,
                'current_subscriptions' => 0,
                'rating_average' => 4.6,
                'rating_count' => 0,
                'tenant_id' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($mealPlans as $plan) {
            DB::table('meal_plans')->insert($plan);
        }

        $this->command->info('Meal plans seeded successfully!');
        $this->command->info('Created ' . count($mealPlans) . ' meal plans across different schools.');
        $this->command->info('Meal Type Distribution:');
        $this->command->info('- Lunch: 5 plans');
        $this->command->info('- Morning Snack: 1 plan');
        $this->command->info('- Afternoon Snack: 1 plan');
        $this->command->info('Price Range: ₹25 - ₹65 per meal');
    }
}

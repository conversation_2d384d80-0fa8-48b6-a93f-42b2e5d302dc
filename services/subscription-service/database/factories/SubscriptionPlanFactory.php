<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\SubscriptionPlan;
use Carbon\Carbon;
use Faker\Generator as Faker;

$factory->define(SubscriptionPlan::class, function (Faker $faker) {
    return [
        'company_id' => 1,
        'unit_id' => 1,
        'plan_name' => $faker->words(3, true) . ' Plan',
        'plan_quantity' => $faker->randomElement([7, 15, 30, 90, 180, 365]),
        'plan_period' => $faker->randomElement(['day', 'week', 'month', 'year']),
        'plan_type' => $faker->randomElement(['daily', 'weekly', 'monthly', 'quarterly', 'half-yearly', 'yearly']),
        'plan_start_date' => Carbon::now()->subDays(rand(0, 30)),
        'plan_end_date' => Carbon::now()->addDays(rand(30, 365)),
        'fk_promo_code' => $faker->optional()->randomNumber(3),
        'plan_status' => $faker->boolean(80), // 80% chance of being active
        'show_to_customer' => $faker->randomElement(['yes', 'no', 'admin']),
        'fk_kitchen_code' => $faker->optional()->randomNumber(3),
        'price' => $faker->randomFloat(2, 50, 5000),
        'is_recurring' => $faker->boolean(60) // 60% chance of being recurring
    ];
});

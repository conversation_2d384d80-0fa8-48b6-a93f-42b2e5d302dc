<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Subscription;
use Carbon\Carbon;
use Faker\Generator as Faker;
use Illuminate\Support\Str;

$factory->define(Subscription::class, function (Faker $faker) {
    $startDate = Carbon::now()->subDays(rand(0, 30));
    $endDate = $startDate->copy()->addDays(rand(7, 365));
    $amount = $faker->randomFloat(2, 100, 10000);
    $discount = $faker->randomFloat(2, 0, $amount * 0.3); // Up to 30% discount
    $total = $amount - $discount;
    $autoRenew = $faker->boolean(40); // 40% chance of auto-renew
    
    return [
        'company_id' => 1,
        'unit_id' => 1,
        'customer_id' => function () {
            return factory(App\Customer::class)->create()->id;
        },
        'plan_id' => function () {
            return factory(App\SubscriptionPlan::class)->create()->id;
        },
        'subscription_no' => 'SUB-' . date('Ymd') . '-' . strtoupper(Str::random(4)),
        'start_date' => $startDate,
        'end_date' => $endDate,
        'status' => $faker->randomElement(['active', 'paused', 'cancelled', 'expired']),
        'amount' => $amount,
        'discount' => $discount,
        'total' => $total,
        'payment_method' => $faker->randomElement(['credit_card', 'debit_card', 'net_banking', 'wallet', 'cash']),
        'payment_status' => $faker->randomElement(['pending', 'paid', 'failed', 'refunded']),
        'transaction_id' => $faker->optional()->uuid,
        'pause_history' => $faker->optional()->randomElement([
            [
                [
                    'paused_at' => Carbon::now()->subDays(rand(5, 20))->toDateTimeString(),
                    'reason' => $faker->sentence,
                    'resume_date' => Carbon::now()->addDays(rand(1, 10))->toDateTimeString(),
                    'resumed_at' => null
                ]
            ],
            null
        ]),
        'next_billing_date' => $autoRenew ? $endDate : null,
        'auto_renew' => $autoRenew,
        'notes' => $faker->optional()->paragraph
    ];
});

<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Customer;
use Faker\Generator as Faker;

$factory->define(Customer::class, function (Faker $faker) {
    return [
        'company_id' => 1,
        'unit_id' => 1,
        'customer_name' => $faker->name,
        'phone' => $faker->phoneNumber,
        'email_address' => $faker->unique()->safeEmail,
        'customer_Address' => $faker->address,
        'location_code' => $faker->randomNumber(5),
        'location_name' => $faker->city,
        'lunch_loc_code' => $faker->randomNumber(5),
        'lunch_loc_name' => $faker->company,
        'lunch_add' => $faker->address,
        'dinner_loc_code' => $faker->randomNumber(5),
        'dinner_loc_name' => $faker->company,
        'dinner_add' => $faker->address,
        'food_preference' => $faker->randomElement(['veg', 'non-veg', 'vegan']),
        'city' => $faker->randomNumber(3),
        'city_name' => $faker->city,
        'company_name' => $faker->company,
        'group_code' => $faker->randomNumber(3),
        'group_name' => $faker->word,
        'registered_on' => $faker->dateTimeThisYear,
        'registered_from' => $faker->randomElement(['web', 'app', 'admin']),
        'food_referance' => $faker->text(100),
        'status' => 1,
        'otp' => $faker->randomNumber(6),
        'password' => bcrypt('password'),
        'thirdparty' => $faker->randomElement(['google', 'facebook', null]),
        'phone_verified' => $faker->boolean,
        'subscription_notification' => $faker->randomElement(['yes', 'no']),
        'email_verified' => $faker->boolean,
        'source' => $faker->randomElement(['web', 'app', 'referral']),
        'referer' => $faker->optional()->url,
        'gcm_id' => $faker->optional()->uuid,
        'alt_phone' => $faker->optional()->phoneNumber,
        'dabbawala_code_type' => $faker->optional()->randomElement(['A', 'B', 'C']),
        'dabbawala_code' => $faker->optional()->randomNumber(5),
        'dabbawala_image' => $faker->optional()->imageUrl(),
        'isguest' => false,
        'delivery_note' => $faker->optional()->text(200)
    ];
});

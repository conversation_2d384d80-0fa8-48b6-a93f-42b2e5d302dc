<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\SubscriptionLog;
use Faker\Generator as Faker;

$factory->define(SubscriptionLog::class, function (Faker $faker) {
    return [
        'company_id' => 1,
        'unit_id' => 1,
        'total_orders' => $faker->numberBetween(100, 1000),
        'sms_sent' => $faker->numberBetween(50, 500),
        'email_sent' => $faker->numberBetween(50, 500),
        'active_customer' => $faker->numberBetween(20, 200),
        'admin_account' => $faker->numberBetween(1, 10),
        'user_account' => $faker->numberBetween(10, 100),
        'kitchen_count' => $faker->numberBetween(1, 5),
        'date' => $faker->dateTimeThisYear->format('Y-m-d')
    ];
});

<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\SubscriptionKey;
use Faker\Generator as Faker;

$factory->define(SubscriptionKey::class, function (Faker $faker) {
    return [
        'company_id' => 1,
        'unit_id' => 1,
        'key' => $faker->unique()->word,
        'value' => $faker->sentence,
        'description' => $faker->paragraph,
        'status' => $faker->boolean(90) // 90% chance of being active
    ];
});

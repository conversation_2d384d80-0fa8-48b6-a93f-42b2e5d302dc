<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\SubscriptionItem;
use Faker\Generator as Faker;

$factory->define(SubscriptionItem::class, function (Faker $faker) {
    $quantity = $faker->numberBetween(1, 5);
    $price = $faker->randomFloat(2, 50, 500);
    $total = $quantity * $price;
    $isSwappable = $faker->boolean(30); // 30% chance of being swappable
    
    return [
        'company_id' => 1,
        'unit_id' => 1,
        'subscription_id' => function () {
            return factory(App\Subscription::class)->create()->id;
        },
        'meal_id' => $faker->numberBetween(1, 100),
        'meal_name' => $faker->words(3, true),
        'quantity' => $quantity,
        'price' => $price,
        'total' => $total,
        'day_of_week' => $faker->randomElement(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']),
        'meal_type' => $faker->randomElement(['breakfast', 'lunch', 'dinner', 'snack']),
        'is_swappable' => $isSwappable,
        'swap_options' => $isSwappable ? json_encode([
            'allowed_meals' => [
                $faker->numberBetween(1, 100),
                $faker->numberBetween(1, 100),
                $faker->numberBetween(1, 100)
            ],
            'swap_limit' => $faker->numberBetween(1, 5)
        ]) : null,
        'notes' => $faker->optional()->sentence
    ];
});

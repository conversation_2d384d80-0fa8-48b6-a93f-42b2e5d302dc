stages:
  - build
  - test
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_HOST: tcp://docker:2375
  MYSQL_DATABASE: subscription_service_test
  MYSQL_ROOT_PASSWORD: root
  DB_HOST: mysql
  DB_USERNAME: root
  DB_PASSWORD: root
  DB_DATABASE: subscription_service_test
  APP_ENV: testing
  APP_KEY: base64:HmT5wIbkfPQfLmWGEAJrWJ+UFBOXgJUf3+/QQtXYR4A=
  APP_DEBUG: "true"
  APP_URL: http://localhost

services:
  - docker:dind
  - name: mysql:5.7
    alias: mysql

build:
  stage: build
  image: docker:stable
  script:
    - docker build -t subscription-service:$CI_COMMIT_SHORT_SHA .
    - docker tag subscription-service:$CI_COMMIT_SHORT_SHA $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  only:
    - master
    - develop

test:
  stage: test
  image: php:7.2
  before_script:
    - apt-get update && apt-get install -y git libzip-dev zip unzip
    - docker-php-ext-install zip pdo_mysql
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - composer install --no-interaction --no-progress --no-suggest
    - php artisan key:generate
    - php artisan migrate --seed
  script:
    - vendor/bin/phpunit --coverage-text --colors=never
  only:
    - master
    - develop

code_quality:
  stage: test
  image: php:7.2
  before_script:
    - apt-get update && apt-get install -y git libzip-dev zip unzip
    - docker-php-ext-install zip
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - composer install --no-interaction --no-progress --no-suggest
  script:
    - vendor/bin/phpcs --standard=PSR2 app
    - vendor/bin/phpstan analyse app --level=5
  only:
    - master
    - develop

deploy_staging:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" > ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - ssh $STAGING_SERVER_USER@$STAGING_SERVER "cd /var/www/subscription-service && git pull origin develop && composer install --no-interaction --no-dev --optimize-autoloader && php artisan migrate --force && php artisan config:cache && php artisan route:cache && php artisan view:cache"
  environment:
    name: staging
  only:
    - develop

deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" > ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - ssh $PRODUCTION_SERVER_USER@$PRODUCTION_SERVER "cd /var/www/subscription-service && git pull origin master && composer install --no-interaction --no-dev --optimize-autoloader && php artisan migrate --force && php artisan config:cache && php artisan route:cache && php artisan view:cache"
  environment:
    name: production
  only:
    - master
  when: manual

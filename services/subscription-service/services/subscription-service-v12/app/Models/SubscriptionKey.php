<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionKey extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription_keys';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'key',
        'value',
        'description',
        'status'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean'
    ];

    /**
     * Scope a query to only include active keys.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Get a key's value by its key name.
     *
     * @param  string  $key
     * @param  mixed  $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $record = self::where('key', $key)->active()->first();

        return $record ? $record->value : $default;
    }

    /**
     * Set a key's value.
     *
     * @param  string  $key
     * @param  mixed  $value
     * @param  string|null  $description
     * @return \App\Models\SubscriptionKey
     */
    public static function setValue(string $key, $value, ?string $description = null): SubscriptionKey
    {
        $record = self::where('key', $key)->first();

        if ($record) {
            $record->update([
                'value' => $value,
                'description' => $description ?? $record->description
            ]);

            return $record;
        }

        return self::create([
            'key' => $key,
            'value' => $value,
            'description' => $description,
            'status' => true
        ]);
    }
}

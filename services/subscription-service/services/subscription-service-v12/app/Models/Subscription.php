<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscriptions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'customer_id',
        'plan_id',
        'subscription_no',
        'start_date',
        'end_date',
        'status',
        'amount',
        'discount',
        'total',
        'payment_method',
        'payment_status',
        'transaction_id',
        'pause_history',
        'next_billing_date',
        'auto_renew',
        'notes'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'float',
        'discount' => 'float',
        'total' => 'float',
        'auto_renew' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'next_billing_date' => 'date',
        'pause_history' => 'array'
    ];

    /**
     * Get the customer that owns the subscription.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * Get the plan that owns the subscription.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get the items for the subscription.
     */
    public function items(): HasMany
    {
        return $this->hasMany(SubscriptionItem::class, 'subscription_id');
    }

    /**
     * Scope a query to only include active subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include paused subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePaused($query)
    {
        return $query->where('status', 'paused');
    }

    /**
     * Scope a query to only include subscriptions for a specific customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $customerId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Pause the subscription.
     *
     * @param  string|null  $reason
     * @param  \DateTime|string|null  $resumeDate
     * @return bool
     */
    public function pause(?string $reason = null, $resumeDate = null): bool
    {
        $pauseHistory = $this->pause_history ?: [];
        $pauseHistory[] = [
            'paused_at' => now()->toDateTimeString(),
            'reason' => $reason,
            'resume_date' => $resumeDate ? (is_string($resumeDate) ? $resumeDate : $resumeDate->toDateTimeString()) : null
        ];

        return $this->update([
            'status' => 'paused',
            'pause_history' => $pauseHistory
        ]);
    }

    /**
     * Resume the subscription.
     *
     * @return bool
     */
    public function resume(): bool
    {
        $pauseHistory = $this->pause_history ?: [];
        if (!empty($pauseHistory)) {
            $lastPause = end($pauseHistory);
            $lastPause['resumed_at'] = now()->toDateTimeString();
            $pauseHistory[count($pauseHistory) - 1] = $lastPause;
        }

        return $this->update([
            'status' => 'active',
            'pause_history' => $pauseHistory
        ]);
    }

    /**
     * Cancel the subscription.
     *
     * @param  string|null  $reason
     * @return bool
     */
    public function cancel(?string $reason = null): bool
    {
        return $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? ($this->notes ? $this->notes . "\n" . $reason : $reason) : $this->notes
        ]);
    }

    /**
     * Renew the subscription.
     *
     * @param  int|null  $days
     * @return bool
     */
    public function renew(?int $days = null): bool
    {
        if (!$days) {
            $days = $this->plan->plan_quantity;
        }

        $startDate = $this->end_date->addDay();
        $endDate = $startDate->copy()->addDays($days - 1);

        return $this->update([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'next_billing_date' => $this->auto_renew ? $endDate : null
        ]);
    }
}

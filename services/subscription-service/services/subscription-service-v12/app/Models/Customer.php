<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_name',
        'phone',
        'email_address',
        'customer_Address',
        'location_code',
        'location_name',
        'lunch_loc_code',
        'lunch_loc_name',
        'lunch_add',
        'dinner_loc_code',
        'dinner_loc_name',
        'dinner_add',
        'food_preference',
        'city',
        'city_name',
        'company_name',
        'group_code',
        'group_name',
        'registered_on',
        'registered_from',
        'food_referance',
        'status',
        'otp',
        'password',
        'thirdparty',
        'phone_verified',
        'subscription_notification',
        'email_verified',
        'source',
        'referer',
        'gcm_id',
        'alt_phone',
        'company_id',
        'unit_id',
        'dabbawala_code_type',
        'dabbawala_code',
        'dabbawala_image',
        'isguest',
        'delivery_note'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password', 'otp'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'phone_verified' => 'boolean',
        'email_verified' => 'boolean',
        'isguest' => 'boolean',
        'registered_on' => 'datetime'
    ];

    /**
     * Get the subscriptions for the customer.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'customer_id');
    }

    /**
     * Get the active subscriptions for the customer.
     */
    public function activeSubscriptions(): HasMany
    {
        return $this->subscriptions()->where('status', 'active');
    }

    /**
     * Check if the customer has an active subscription.
     *
     * @return bool
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscriptions()->count() > 0;
    }

    /**
     * Scope a query to only include active customers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope a query to only include customers with subscription notifications enabled.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithSubscriptionNotifications($query)
    {
        return $query->where('subscription_notification', 'yes');
    }
}

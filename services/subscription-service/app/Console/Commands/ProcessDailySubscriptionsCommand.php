<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ProcessDailySubscriptionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'school-tiffin:process-daily-subscriptions 
                            {--date= : Process subscriptions for specific date (Y-m-d format)}
                            {--dry-run : Run without making actual changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process daily school tiffin subscriptions and generate delivery queue';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->option('date') ? Carbon::parse($this->option('date')) : Carbon::today();
        $isDryRun = $this->option('dry-run');
        
        $this->info("Processing daily subscriptions for: {$date->format('Y-m-d')}");
        
        if ($isDryRun) {
            $this->warn("DRY RUN MODE - No changes will be made");
        }

        try {
            DB::beginTransaction();

            // Get active subscriptions for the date
            $activeSubscriptions = $this->getActiveSubscriptions($date);
            $this->info("Found {$activeSubscriptions->count()} active subscriptions");

            $processedCount = 0;
            $errorCount = 0;

            foreach ($activeSubscriptions as $subscription) {
                try {
                    if ($this->shouldProcessSubscription($subscription, $date)) {
                        if (!$isDryRun) {
                            $this->processSubscription($subscription, $date);
                        }
                        $processedCount++;
                        
                        $this->line("✓ Processed subscription #{$subscription->id} for child: {$subscription->child_name}");
                    } else {
                        $this->line("- Skipped subscription #{$subscription->id} (not scheduled for today)");
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $this->error("✗ Failed to process subscription #{$subscription->id}: {$e->getMessage()}");
                    Log::error("Failed to process subscription", [
                        'subscription_id' => $subscription->id,
                        'error' => $e->getMessage(),
                        'date' => $date->format('Y-m-d')
                    ]);
                }
            }

            if (!$isDryRun) {
                DB::commit();
            } else {
                DB::rollBack();
            }

            $this->info("Processing completed:");
            $this->info("- Processed: {$processedCount}");
            $this->info("- Errors: {$errorCount}");
            $this->info("- Total: {$activeSubscriptions->count()}");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Failed to process daily subscriptions: {$e->getMessage()}");
            Log::error("Daily subscription processing failed", [
                'error' => $e->getMessage(),
                'date' => $date->format('Y-m-d')
            ]);
            
            return Command::FAILURE;
        }
    }

    /**
     * Get active subscriptions for the given date.
     */
    private function getActiveSubscriptions(Carbon $date)
    {
        return DB::table('school_meal_subscriptions as sms')
            ->join('child_profiles as cp', 'sms.child_profile_id', '=', 'cp.id')
            ->join('schools as s', 'cp.school_id', '=', 's.id')
            ->join('meal_plans as mp', 'sms.meal_plan_id', '=', 'mp.id')
            ->select([
                'sms.*',
                'cp.full_name as child_name',
                'cp.school_id',
                's.school_name',
                's.break_times',
                'mp.plan_name',
                'mp.meal_type'
            ])
            ->where('sms.status', 'active')
            ->where('sms.start_date', '<=', $date)
            ->where('sms.end_date', '>=', $date)
            ->get();
    }

    /**
     * Check if subscription should be processed for the given date.
     */
    private function shouldProcessSubscription($subscription, Carbon $date): bool
    {
        // Check if today is in delivery days
        $deliveryDays = json_decode($subscription->delivery_days, true) ?? [];
        $dayOfWeek = strtolower($date->format('l'));
        
        if (!in_array($dayOfWeek, $deliveryDays)) {
            return false;
        }

        // Check if school is open (basic check - can be enhanced)
        $breakTimes = json_decode($subscription->break_times, true) ?? [];
        $preferredBreakTime = $subscription->preferred_break_time;
        
        if (!isset($breakTimes[$preferredBreakTime])) {
            return false;
        }

        // Check if delivery hasn't been processed already
        $existingDelivery = DB::table('school_delivery_batches')
            ->where('subscription_id', $subscription->id)
            ->where('delivery_date', $date->format('Y-m-d'))
            ->exists();

        return !$existingDelivery;
    }

    /**
     * Process individual subscription for delivery.
     */
    private function processSubscription($subscription, Carbon $date): void
    {
        // Create delivery batch entry
        $deliveryBatchId = DB::table('school_delivery_batches')->insertGetId([
            'subscription_id' => $subscription->id,
            'child_profile_id' => $subscription->child_profile_id,
            'school_id' => $subscription->school_id,
            'meal_plan_id' => $subscription->meal_plan_id,
            'delivery_date' => $date->format('Y-m-d'),
            'break_time_slot' => $subscription->preferred_break_time,
            'status' => 'scheduled',
            'meal_customizations' => $subscription->meal_customizations,
            'dietary_accommodations' => $subscription->dietary_accommodations,
            'special_notes' => $subscription->special_notes,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Update subscription consumption stats
        $consumptionStats = json_decode($subscription->consumption_stats, true) ?? [
            'total_meals_delivered' => 0,
            'meals_consumed' => 0,
            'consumption_rate' => 0,
            'feedback_average' => 0
        ];

        $consumptionStats['total_meals_delivered']++;
        
        DB::table('school_meal_subscriptions')
            ->where('id', $subscription->id)
            ->update([
                'consumption_stats' => json_encode($consumptionStats),
                'updated_at' => now()
            ]);

        // Log the processing
        Log::info("Processed subscription for delivery", [
            'subscription_id' => $subscription->id,
            'delivery_batch_id' => $deliveryBatchId,
            'child_name' => $subscription->child_name,
            'school_name' => $subscription->school_name,
            'delivery_date' => $date->format('Y-m-d'),
            'break_time_slot' => $subscription->preferred_break_time
        ]);
    }
}

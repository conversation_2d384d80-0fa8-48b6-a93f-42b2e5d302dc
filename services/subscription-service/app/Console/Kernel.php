<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // School Tiffin Daily Processing
        $schedule->command('school-tiffin:process-daily-subscriptions')
                 ->dailyAt('06:00')
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/daily-subscriptions.log'));

        // Auto-renewal Processing
        $schedule->command('school-tiffin:process-renewals')
                 ->dailyAt('02:00')
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/renewals.log'));

        // Delivery Queue Generation
        $schedule->command('school-tiffin:generate-delivery-queue')
                 ->dailyAt('07:00')
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/delivery-queue.log'));

        // Subscription Expiry Notifications
        $schedule->command('school-tiffin:notify-expiring-subscriptions')
                 ->dailyAt('09:00')
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/expiry-notifications.log'));

        // Break Time Validation and Cutoff Processing
        $schedule->command('school-tiffin:validate-break-times')
                 ->hourly()
                 ->between('6:00', '14:00')
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/break-time-validation.log'));
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

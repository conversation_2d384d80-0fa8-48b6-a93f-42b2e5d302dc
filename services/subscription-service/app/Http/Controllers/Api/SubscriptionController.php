<?php

namespace App\Http\Controllers\Api;

use App\Subscription;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class SubscriptionController extends Controller
{
    /**
     * The subscription service instance.
     *
     * @var \App\Services\SubscriptionService
     */
    protected $subscriptionService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\SubscriptionService  $subscriptionService
     * @return void
     */
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Display a listing of the subscriptions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $filters = $request->only([
                'customer_id', 'status', 'plan_id', 'start_date', 'end_date',
                'search', 'sort_by', 'sort_direction'
            ]);

            $subscriptions = $this->subscriptionService->getAllSubscriptions($filters);

            return response()->json([
                'status' => 'success',
                'data' => $subscriptions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve subscriptions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created subscription in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'plan_id' => 'required|exists:subscription_plans,id',
                'start_date' => 'nullable|date',
                'payment_method' => 'nullable|string',
                'payment_status' => 'nullable|string',
                'transaction_id' => 'nullable|string',
                'discount' => 'nullable|numeric|min:0',
                'auto_renew' => 'nullable|boolean',
                'notes' => 'nullable|string',
                'items' => 'nullable|array',
                'items.*.meal_id' => 'required|integer',
                'items.*.meal_name' => 'required|string',
                'items.*.quantity' => 'nullable|integer|min:1',
                'items.*.price' => 'required|numeric|min:0',
                'items.*.day_of_week' => 'nullable|string',
                'items.*.meal_type' => 'nullable|string',
                'items.*.is_swappable' => 'nullable|boolean',
                'items.*.swap_options' => 'nullable|json',
                'items.*.notes' => 'nullable|string'
            ]);

            $subscription = $this->subscriptionService->createSubscription($validatedData);

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription created successfully',
                'data' => $subscription
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified subscription.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $subscription = $this->subscriptionService->getSubscriptionById($id);

            if (!$subscription) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Subscription not found'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'data' => $subscription
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified subscription in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'status' => 'nullable|string|in:active,paused,cancelled,expired',
                'payment_method' => 'nullable|string',
                'payment_status' => 'nullable|string',
                'transaction_id' => 'nullable|string',
                'auto_renew' => 'nullable|boolean',
                'notes' => 'nullable|string',
                'items' => 'nullable|array',
                'items.*.meal_id' => 'required|integer',
                'items.*.meal_name' => 'required|string',
                'items.*.quantity' => 'nullable|integer|min:1',
                'items.*.price' => 'required|numeric|min:0',
                'items.*.day_of_week' => 'nullable|string',
                'items.*.meal_type' => 'nullable|string',
                'items.*.is_swappable' => 'nullable|boolean',
                'items.*.swap_options' => 'nullable|json',
                'items.*.notes' => 'nullable|string'
            ]);

            $subscription = $this->subscriptionService->updateSubscription($id, $validatedData);

            if (!$subscription) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Subscription not found'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription updated successfully',
                'data' => $subscription
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified subscription from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $result = $this->subscriptionService->deleteSubscription($id);

            if (!$result) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Subscription not found'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Pause a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function pause(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'reason' => 'nullable|string',
                'resume_date' => 'nullable|date'
            ]);

            $subscription = Subscription::findOrFail($id);
            $subscription->pause(
                $validatedData['reason'] ?? null,
                $validatedData['resume_date'] ?? null
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription paused successfully',
                'data' => $subscription->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to pause subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resume a subscription.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resume($id)
    {
        try {
            $subscription = Subscription::findOrFail($id);
            $subscription->resume();

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription resumed successfully',
                'data' => $subscription->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to resume subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'reason' => 'nullable|string'
            ]);

            $subscription = Subscription::findOrFail($id);
            $subscription->cancel($validatedData['reason'] ?? null);

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription cancelled successfully',
                'data' => $subscription->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to cancel subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Renew a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function renew(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'days' => 'nullable|integer|min:1'
            ]);

            $subscription = Subscription::findOrFail($id);
            $subscription->renew($validatedData['days'] ?? null);

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription renewed successfully',
                'data' => $subscription->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to renew subscription: ' . $e->getMessage()
            ], 500);
        }
    }
}

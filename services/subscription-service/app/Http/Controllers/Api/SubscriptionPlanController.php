<?php

namespace App\Http\Controllers\Api;

use App\SubscriptionPlan;
use App\Services\SubscriptionPlanService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class SubscriptionPlanController extends Controller
{
    /**
     * The subscription plan service instance.
     *
     * @var \App\Services\SubscriptionPlanService
     */
    protected $subscriptionPlanService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\SubscriptionPlanService  $subscriptionPlanService
     * @return void
     */
    public function __construct(SubscriptionPlanService $subscriptionPlanService)
    {
        $this->subscriptionPlanService = $subscriptionPlanService;
    }

    /**
     * Display a listing of the subscription plans.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $filters = $request->only([
                'plan_type', 'plan_quantity', 'plan_period', 'active',
                'show_to_customer', 'search', 'sort_by', 'sort_direction'
            ]);

            $plans = $this->subscriptionPlanService->getAllPlans($filters);

            return response()->json([
                'status' => 'success',
                'data' => $plans
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve subscription plans: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created subscription plan in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'plan_name' => 'required|string|max:255',
                'plan_quantity' => 'required|integer|min:1',
                'plan_period' => 'required|string|in:day,week,month,year',
                'plan_type' => 'nullable|string|max:255',
                'plan_start_date' => 'required|date',
                'plan_end_date' => 'required|date|after:plan_start_date',
                'fk_promo_code' => 'nullable|integer',
                'plan_status' => 'nullable|boolean',
                'show_to_customer' => 'nullable|string|in:yes,no,admin',
                'fk_kitchen_code' => 'nullable|integer',
                'price' => 'required|numeric|min:0',
                'is_recurring' => 'nullable|boolean'
            ]);

            $plan = $this->subscriptionPlanService->createPlan($validatedData);

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription plan created successfully',
                'data' => $plan
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create subscription plan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified subscription plan.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $plan = $this->subscriptionPlanService->getPlanById($id);

            if (!$plan) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Subscription plan not found'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'data' => $plan
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve subscription plan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified subscription plan in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'plan_name' => 'nullable|string|max:255',
                'plan_quantity' => 'nullable|integer|min:1',
                'plan_period' => 'nullable|string|in:day,week,month,year',
                'plan_type' => 'nullable|string|max:255',
                'plan_start_date' => 'nullable|date',
                'plan_end_date' => 'nullable|date|after:plan_start_date',
                'fk_promo_code' => 'nullable|integer',
                'plan_status' => 'nullable|boolean',
                'show_to_customer' => 'nullable|string|in:yes,no,admin',
                'fk_kitchen_code' => 'nullable|integer',
                'price' => 'nullable|numeric|min:0',
                'is_recurring' => 'nullable|boolean'
            ]);

            $plan = $this->subscriptionPlanService->updatePlan($id, $validatedData);

            if (!$plan) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Subscription plan not found'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription plan updated successfully',
                'data' => $plan
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update subscription plan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified subscription plan from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $result = $this->subscriptionPlanService->deletePlan($id);

            if (!$result) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Subscription plan not found'
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription plan deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete subscription plan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active subscription plans.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActivePlans()
    {
        try {
            $plans = $this->subscriptionPlanService->getActivePlans();

            return response()->json([
                'status' => 'success',
                'data' => $plans
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve active subscription plans: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subscription plans visible to customers.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerVisiblePlans()
    {
        try {
            $plans = $this->subscriptionPlanService->getCustomerVisiblePlans();

            return response()->json([
                'status' => 'success',
                'data' => $plans
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve customer visible subscription plans: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subscription plans by type.
     *
     * @param  string  $type
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPlansByType($type)
    {
        try {
            $plans = $this->subscriptionPlanService->getPlansByType($type);

            return response()->json([
                'status' => 'success',
                'data' => $plans
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve subscription plans by type: ' . $e->getMessage()
            ], 500);
        }
    }
}

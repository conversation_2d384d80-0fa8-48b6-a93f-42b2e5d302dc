<?php

namespace App\Services;

use App\SubscriptionPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubscriptionPlanService
{
    /**
     * Get all subscription plans with optional filtering.
     *
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllPlans(array $filters = [])
    {
        $query = SubscriptionPlan::query();

        // Apply filters
        if (isset($filters['plan_type']) && !empty($filters['plan_type'])) {
            $query->where('plan_type', $filters['plan_type']);
        }

        if (isset($filters['plan_quantity']) && !empty($filters['plan_quantity'])) {
            $query->where('plan_quantity', $filters['plan_quantity']);
        }

        if (isset($filters['plan_period']) && !empty($filters['plan_period'])) {
            $query->where('plan_period', $filters['plan_period']);
        }

        if (isset($filters['active']) && $filters['active']) {
            $query->active();
        }

        if (isset($filters['show_to_customer']) && !empty($filters['show_to_customer'])) {
            $query->where('show_to_customer', $filters['show_to_customer']);
        }

        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where('plan_name', 'like', '%' . $filters['search'] . '%');
        }

        // Apply sorting
        $sortField = $filters['sort_by'] ?? 'plan_quantity';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        return $query->get();
    }

    /**
     * Get a subscription plan by ID.
     *
     * @param int $id
     * @return \App\SubscriptionPlan|null
     */
    public function getPlanById($id)
    {
        return SubscriptionPlan::find($id);
    }

    /**
     * Create a new subscription plan.
     *
     * @param array $data
     * @return \App\SubscriptionPlan
     */
    public function createPlan(array $data)
    {
        DB::beginTransaction();

        try {
            $planData = [
                'company_id' => $data['company_id'] ?? 1,
                'unit_id' => $data['unit_id'] ?? 1,
                'plan_name' => $data['plan_name'],
                'plan_quantity' => $data['plan_quantity'],
                'plan_period' => $data['plan_period'],
                'plan_type' => $data['plan_type'] ?? null,
                'plan_start_date' => $data['plan_start_date'],
                'plan_end_date' => $data['plan_end_date'],
                'fk_promo_code' => $data['fk_promo_code'] ?? null,
                'plan_status' => $data['plan_status'] ?? true,
                'show_to_customer' => $data['show_to_customer'] ?? 'yes',
                'fk_kitchen_code' => $data['fk_kitchen_code'] ?? null,
                'price' => $data['price'] ?? 0,
                'is_recurring' => $data['is_recurring'] ?? false
            ];

            $plan = SubscriptionPlan::create($planData);

            DB::commit();

            return $plan;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating subscription plan: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing subscription plan.
     *
     * @param int $id
     * @param array $data
     * @return \App\SubscriptionPlan|null
     */
    public function updatePlan($id, array $data)
    {
        DB::beginTransaction();

        try {
            $plan = SubscriptionPlan::findOrFail($id);

            $planData = [];

            if (isset($data['plan_name'])) {
                $planData['plan_name'] = $data['plan_name'];
            }

            if (isset($data['plan_quantity'])) {
                $planData['plan_quantity'] = $data['plan_quantity'];
            }

            if (isset($data['plan_period'])) {
                $planData['plan_period'] = $data['plan_period'];
            }

            if (isset($data['plan_type'])) {
                $planData['plan_type'] = $data['plan_type'];
            }

            if (isset($data['plan_start_date'])) {
                $planData['plan_start_date'] = $data['plan_start_date'];
            }

            if (isset($data['plan_end_date'])) {
                $planData['plan_end_date'] = $data['plan_end_date'];
            }

            if (isset($data['fk_promo_code'])) {
                $planData['fk_promo_code'] = $data['fk_promo_code'];
            }

            if (isset($data['plan_status'])) {
                $planData['plan_status'] = $data['plan_status'];
            }

            if (isset($data['show_to_customer'])) {
                $planData['show_to_customer'] = $data['show_to_customer'];
            }

            if (isset($data['fk_kitchen_code'])) {
                $planData['fk_kitchen_code'] = $data['fk_kitchen_code'];
            }

            if (isset($data['price'])) {
                $planData['price'] = $data['price'];
            }

            if (isset($data['is_recurring'])) {
                $planData['is_recurring'] = $data['is_recurring'];
            }

            $plan->update($planData);

            DB::commit();

            return $plan->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating subscription plan: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a subscription plan.
     *
     * @param int $id
     * @return bool
     */
    public function deletePlan($id)
    {
        DB::beginTransaction();

        try {
            $plan = SubscriptionPlan::findOrFail($id);
            $plan->delete();

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting subscription plan: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get active plans.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActivePlans()
    {
        return SubscriptionPlan::active()->get();
    }

    /**
     * Get plans visible to customers.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCustomerVisiblePlans()
    {
        return SubscriptionPlan::active()->visibleToCustomers()->get();
    }

    /**
     * Get plans by type.
     *
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPlansByType($type)
    {
        return SubscriptionPlan::active()->ofType($type)->get();
    }
}

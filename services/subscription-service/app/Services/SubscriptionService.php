<?php

namespace App\Services;

use App\Subscription;
use App\SubscriptionPlan;
use App\SubscriptionItem;
use App\Customer;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    /**
     * Get all subscriptions with optional filtering.
     *
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllSubscriptions(array $filters = [])
    {
        $query = Subscription::query();

        // Apply filters
        if (isset($filters['customer_id']) && !empty($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['status']) && !empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['plan_id']) && !empty($filters['plan_id'])) {
            $query->where('plan_id', $filters['plan_id']);
        }

        if (isset($filters['start_date']) && !empty($filters['start_date'])) {
            $query->where('start_date', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date']) && !empty($filters['end_date'])) {
            $query->where('end_date', '<=', $filters['end_date']);
        }

        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where('subscription_no', 'like', '%' . $filters['search'] . '%');
        }

        // Apply sorting
        $sortField = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        // Load relationships
        $query->with(['customer', 'plan', 'items']);

        return $query->get();
    }

    /**
     * Get a subscription by ID.
     *
     * @param int $id
     * @return \App\Subscription|null
     */
    public function getSubscriptionById($id)
    {
        return Subscription::with(['customer', 'plan', 'items'])->find($id);
    }

    /**
     * Create a new subscription.
     *
     * @param array $data
     * @return \App\Subscription
     */
    public function createSubscription(array $data)
    {
        DB::beginTransaction();

        try {
            // Generate subscription number
            $data['subscription_no'] = $this->generateSubscriptionNumber();

            // Get the plan
            $plan = SubscriptionPlan::findOrFail($data['plan_id']);

            // Calculate dates
            $startDate = isset($data['start_date']) ? Carbon::parse($data['start_date']) : Carbon::today();
            $endDate = $startDate->copy()->addDays($plan->plan_quantity - 1);

            // Set subscription data
            $subscriptionData = [
                'company_id' => $data['company_id'] ?? 1,
                'unit_id' => $data['unit_id'] ?? 1,
                'customer_id' => $data['customer_id'],
                'plan_id' => $plan->id,
                'subscription_no' => $data['subscription_no'],
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'active',
                'amount' => $plan->price,
                'discount' => $data['discount'] ?? 0,
                'total' => $plan->price - ($data['discount'] ?? 0),
                'payment_method' => $data['payment_method'] ?? null,
                'payment_status' => $data['payment_status'] ?? 'pending',
                'transaction_id' => $data['transaction_id'] ?? null,
                'next_billing_date' => $data['auto_renew'] ? $endDate : null,
                'auto_renew' => $data['auto_renew'] ?? false,
                'notes' => $data['notes'] ?? null
            ];

            // Create subscription
            $subscription = Subscription::create($subscriptionData);

            // Create subscription items
            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $item) {
                    $itemData = [
                        'company_id' => $data['company_id'] ?? 1,
                        'unit_id' => $data['unit_id'] ?? 1,
                        'subscription_id' => $subscription->id,
                        'meal_id' => $item['meal_id'],
                        'meal_name' => $item['meal_name'],
                        'quantity' => $item['quantity'] ?? 1,
                        'price' => $item['price'],
                        'total' => $item['price'] * ($item['quantity'] ?? 1),
                        'day_of_week' => $item['day_of_week'] ?? null,
                        'meal_type' => $item['meal_type'] ?? null,
                        'is_swappable' => $item['is_swappable'] ?? false,
                        'swap_options' => $item['swap_options'] ?? null,
                        'notes' => $item['notes'] ?? null
                    ];

                    SubscriptionItem::create($itemData);
                }
            }

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating subscription: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing subscription.
     *
     * @param int $id
     * @param array $data
     * @return \App\Subscription|null
     */
    public function updateSubscription($id, array $data)
    {
        DB::beginTransaction();

        try {
            $subscription = Subscription::findOrFail($id);

            // Update subscription data
            if (isset($data['status'])) {
                $subscription->status = $data['status'];
            }

            if (isset($data['payment_method'])) {
                $subscription->payment_method = $data['payment_method'];
            }

            if (isset($data['payment_status'])) {
                $subscription->payment_status = $data['payment_status'];
            }

            if (isset($data['transaction_id'])) {
                $subscription->transaction_id = $data['transaction_id'];
            }

            if (isset($data['auto_renew'])) {
                $subscription->auto_renew = $data['auto_renew'];
                $subscription->next_billing_date = $data['auto_renew'] ? $subscription->end_date : null;
            }

            if (isset($data['notes'])) {
                $subscription->notes = $data['notes'];
            }

            $subscription->save();

            // Update subscription items
            if (isset($data['items']) && is_array($data['items'])) {
                // Delete existing items
                SubscriptionItem::where('subscription_id', $subscription->id)->delete();

                // Create new items
                foreach ($data['items'] as $item) {
                    $itemData = [
                        'company_id' => $subscription->company_id,
                        'unit_id' => $subscription->unit_id,
                        'subscription_id' => $subscription->id,
                        'meal_id' => $item['meal_id'],
                        'meal_name' => $item['meal_name'],
                        'quantity' => $item['quantity'] ?? 1,
                        'price' => $item['price'],
                        'total' => $item['price'] * ($item['quantity'] ?? 1),
                        'day_of_week' => $item['day_of_week'] ?? null,
                        'meal_type' => $item['meal_type'] ?? null,
                        'is_swappable' => $item['is_swappable'] ?? false,
                        'swap_options' => $item['swap_options'] ?? null,
                        'notes' => $item['notes'] ?? null
                    ];

                    SubscriptionItem::create($itemData);
                }
            }

            DB::commit();

            return $subscription->fresh(['customer', 'plan', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating subscription: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a subscription.
     *
     * @param int $id
     * @return bool
     */
    public function deleteSubscription($id)
    {
        DB::beginTransaction();

        try {
            $subscription = Subscription::findOrFail($id);

            // Delete subscription items
            SubscriptionItem::where('subscription_id', $subscription->id)->delete();

            // Delete subscription
            $subscription->delete();

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting subscription: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate a unique subscription number.
     *
     * @return string
     */
    protected function generateSubscriptionNumber()
    {
        $prefix = 'SUB';
        $date = date('Ymd');
        $random = strtoupper(Str::random(4));
        
        return $prefix . '-' . $date . '-' . $random;
    }
}

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription_plans';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'plan_name',
        'plan_quantity',
        'plan_period',
        'plan_type',
        'plan_start_date',
        'plan_end_date',
        'fk_promo_code',
        'plan_status',
        'show_to_customer',
        'fk_kitchen_code',
        'price',
        'is_recurring'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'plan_status' => 'boolean',
        'price' => 'float',
        'is_recurring' => 'boolean',
        'plan_start_date' => 'date',
        'plan_end_date' => 'date'
    ];

    /**
     * Get the subscriptions for the plan.
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'plan_id');
    }

    /**
     * Scope a query to only include active plans.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('plan_status', true)
                     ->where('plan_start_date', '<=', now())
                     ->where('plan_end_date', '>=', now());
    }

    /**
     * Scope a query to only include plans visible to customers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVisibleToCustomers($query)
    {
        return $query->where('show_to_customer', 'yes');
    }

    /**
     * Scope a query to only include plans of a specific type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('plan_type', $type);
    }
}

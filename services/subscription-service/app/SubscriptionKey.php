<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SubscriptionKey extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription_keys';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'key',
        'value',
        'description',
        'status'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean'
    ];

    /**
     * Scope a query to only include active keys.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Get a subscription key by its key name.
     *
     * @param  string  $key
     * @return \App\SubscriptionKey|null
     */
    public static function getByKey($key)
    {
        return self::where('key', $key)->first();
    }

    /**
     * Get a subscription key value by its key name.
     *
     * @param  string  $key
     * @param  mixed  $default
     * @return mixed
     */
    public static function getValue($key, $default = null)
    {
        $subscriptionKey = self::getByKey($key);

        return $subscriptionKey ? $subscriptionKey->value : $default;
    }

    /**
     * Set a subscription key value.
     *
     * @param  string  $key
     * @param  mixed  $value
     * @param  string  $description
     * @return \App\SubscriptionKey
     */
    public static function setValue($key, $value, $description = null)
    {
        $subscriptionKey = self::getByKey($key);

        if ($subscriptionKey) {
            $subscriptionKey->value = $value;

            if ($description) {
                $subscriptionKey->description = $description;
            }

            $subscriptionKey->save();

            return $subscriptionKey;
        }

        return self::create([
            'key' => $key,
            'value' => $value,
            'description' => $description,
            'status' => true
        ]);
    }
}

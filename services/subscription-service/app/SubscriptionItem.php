<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SubscriptionItem extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription_items';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'subscription_id',
        'meal_id',
        'meal_name',
        'quantity',
        'price',
        'total',
        'day_of_week',
        'meal_type',
        'is_swappable',
        'swap_options',
        'notes'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'float',
        'total' => 'float',
        'is_swappable' => 'boolean',
        'swap_options' => 'array'
    ];

    /**
     * Get the subscription that owns the item.
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    /**
     * Scope a query to only include items for a specific day of the week.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $day
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDay($query, $day)
    {
        return $query->where('day_of_week', $day);
    }

    /**
     * Scope a query to only include items for a specific meal type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForMealType($query, $type)
    {
        return $query->where('meal_type', $type);
    }

    /**
     * Scope a query to only include swappable items.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSwappable($query)
    {
        return $query->where('is_swappable', true);
    }

    /**
     * Calculate the total price for this item.
     *
     * @return float
     */
    public function calculateTotal()
    {
        return $this->price * $this->quantity;
    }

    /**
     * Update the quantity and recalculate the total.
     *
     * @param  int  $quantity
     * @return bool
     */
    public function updateQuantity($quantity)
    {
        $this->quantity = $quantity;
        $this->total = $this->calculateTotal();
        return $this->save();
    }
}

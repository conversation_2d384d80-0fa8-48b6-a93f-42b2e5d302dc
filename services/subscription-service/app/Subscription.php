<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscriptions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'customer_id',
        'plan_id',
        'subscription_no',
        'start_date',
        'end_date',
        'status',
        'amount',
        'discount',
        'total',
        'payment_method',
        'payment_status',
        'transaction_id',
        'pause_history',
        'next_billing_date',
        'auto_renew',
        'notes'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'float',
        'discount' => 'float',
        'total' => 'float',
        'auto_renew' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'next_billing_date' => 'date',
        'pause_history' => 'array'
    ];

    /**
     * Get the customer that owns the subscription.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * Get the plan that owns the subscription.
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get the items for the subscription.
     */
    public function items()
    {
        return $this->hasMany(SubscriptionItem::class, 'subscription_id');
    }

    /**
     * Scope a query to only include active subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include paused subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePaused($query)
    {
        return $query->where('status', 'paused');
    }

    /**
     * Scope a query to only include subscriptions for a specific customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $customerId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Pause the subscription.
     *
     * @param  string  $reason
     * @param  \DateTime|string  $resumeDate
     * @return bool
     */
    public function pause($reason = null, $resumeDate = null)
    {
        $pauseHistory = $this->pause_history ?: [];
        $pauseHistory[] = [
            'paused_at' => now()->toDateTimeString(),
            'reason' => $reason,
            'resume_date' => $resumeDate ? (is_string($resumeDate) ? $resumeDate : $resumeDate->toDateTimeString()) : null
        ];

        return $this->update([
            'status' => 'paused',
            'pause_history' => $pauseHistory
        ]);
    }

    /**
     * Resume the subscription.
     *
     * @return bool
     */
    public function resume()
    {
        $pauseHistory = $this->pause_history ?: [];
        if (!empty($pauseHistory)) {
            $lastPause = end($pauseHistory);
            $lastPause['resumed_at'] = now()->toDateTimeString();
            $pauseHistory[count($pauseHistory) - 1] = $lastPause;
        }

        return $this->update([
            'status' => 'active',
            'pause_history' => $pauseHistory
        ]);
    }

    /**
     * Cancel the subscription.
     *
     * @param  string  $reason
     * @return bool
     */
    public function cancel($reason = null)
    {
        return $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? ($this->notes ? $this->notes . "\n" . $reason : $reason) : $this->notes
        ]);
    }

    /**
     * Renew the subscription.
     *
     * @param  int  $days
     * @return bool
     */
    public function renew($days = null)
    {
        if (!$days) {
            $days = $this->plan->plan_quantity;
        }

        $startDate = $this->end_date->addDay();
        $endDate = $startDate->copy()->addDays($days - 1);

        return $this->update([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'next_billing_date' => $this->auto_renew ? $endDate : null
        ]);
    }
}

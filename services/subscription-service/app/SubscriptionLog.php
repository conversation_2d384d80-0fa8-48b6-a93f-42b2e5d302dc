<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SubscriptionLog extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription_logs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'total_orders',
        'sms_sent',
        'email_sent',
        'active_customer',
        'admin_account',
        'user_account',
        'kitchen_count',
        'date'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'total_orders' => 'integer',
        'sms_sent' => 'integer',
        'email_sent' => 'integer',
        'active_customer' => 'integer',
        'admin_account' => 'integer',
        'user_account' => 'integer',
        'kitchen_count' => 'integer',
        'date' => 'date'
    ];

    /**
     * Scope a query to only include logs for a specific month.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $month
     * @param  string  $year
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForMonth($query, $month, $year = null)
    {
        if (!$year) {
            $year = date('Y');
        }

        return $query->whereMonth('date', $month)
                     ->whereYear('date', $year);
    }

    /**
     * Scope a query to only include logs for a specific year.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $year
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForYear($query, $year)
    {
        return $query->whereYear('date', $year);
    }

    /**
     * Get the current month's subscription log.
     *
     * @return \App\SubscriptionLog
     */
    public static function getCurrentMonth()
    {
        $month = date('m');
        $year = date('Y');

        return self::forMonth($month, $year)->first();
    }

    /**
     * Create a new subscription log for the current month.
     *
     * @param  array  $data
     * @return \App\SubscriptionLog
     */
    public static function createForCurrentMonth(array $data)
    {
        $data['date'] = date('Y-m-d');

        return self::create($data);
    }
}

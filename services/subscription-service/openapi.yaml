openapi: 3.0.0
info:
  title: Subscription Service API
  description: API for subscription management
  version: 1.0.0
  contact:
    name: Support Team
    email: <EMAIL>

servers:
  - url: http://localhost:8000/api/v2
    description: Local development server
  - url: https://tenant.cubeonebiz.com/api/v2
    description: Production server

components:
  schemas:
    SubscriptionPlan:
      type: object
      properties:
        id:
          type: integer
          description: Subscription plan ID
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        plan_name:
          type: string
          description: Plan name
        plan_quantity:
          type: integer
          description: Number of days/weeks/months
        plan_period:
          type: string
          description: day, week, month, year
        plan_type:
          type: string
          description: Plan type
        plan_start_date:
          type: string
          format: date
          description: Plan start date
        plan_end_date:
          type: string
          format: date
          description: Plan end date
        fk_promo_code:
          type: integer
          description: Promo code ID
        plan_status:
          type: boolean
          description: Plan status
        show_to_customer:
          type: string
          description: yes, no, admin
        fk_kitchen_code:
          type: integer
          description: Kitchen code ID
        price:
          type: number
          format: float
          description: Plan price
        is_recurring:
          type: boolean
          description: Whether the plan is recurring
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp

    Subscription:
      type: object
      properties:
        id:
          type: integer
          description: Subscription ID
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        customer_id:
          type: integer
          description: Customer ID
        plan_id:
          type: integer
          description: Plan ID
        subscription_no:
          type: string
          description: Subscription number
        start_date:
          type: string
          format: date
          description: Subscription start date
        end_date:
          type: string
          format: date
          description: Subscription end date
        status:
          type: string
          description: active, paused, cancelled, expired
        amount:
          type: number
          format: float
          description: Subscription amount
        discount:
          type: number
          format: float
          description: Discount amount
        total:
          type: number
          format: float
          description: Total amount
        payment_method:
          type: string
          description: Payment method
        payment_status:
          type: string
          description: Payment status
        transaction_id:
          type: string
          description: Transaction ID
        pause_history:
          type: array
          description: Pause history
          items:
            type: object
            properties:
              paused_at:
                type: string
                format: date-time
              reason:
                type: string
              resume_date:
                type: string
                format: date
              resumed_at:
                type: string
                format: date-time
        next_billing_date:
          type: string
          format: date
          description: Next billing date
        auto_renew:
          type: boolean
          description: Whether the subscription auto-renews
        notes:
          type: string
          description: Notes
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
        customer:
          $ref: '#/components/schemas/Customer'
        plan:
          $ref: '#/components/schemas/SubscriptionPlan'
        items:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionItem'

    SubscriptionItem:
      type: object
      properties:
        id:
          type: integer
          description: Subscription item ID
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        subscription_id:
          type: integer
          description: Subscription ID
        meal_id:
          type: integer
          description: Meal ID
        meal_name:
          type: string
          description: Meal name
        quantity:
          type: integer
          description: Quantity
        price:
          type: number
          format: float
          description: Price
        total:
          type: number
          format: float
          description: Total
        day_of_week:
          type: string
          description: Day of week
        meal_type:
          type: string
          description: Meal type
        is_swappable:
          type: boolean
          description: Whether the item is swappable
        swap_options:
          type: object
          description: Swap options
        notes:
          type: string
          description: Notes
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp

    Customer:
      type: object
      properties:
        id:
          type: integer
          description: Customer ID
        customer_name:
          type: string
          description: Customer name
        phone:
          type: string
          description: Phone number
        email_address:
          type: string
          description: Email address
        status:
          type: integer
          description: Status

    Error:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: Error message

paths:
  /subscription-plans:
    get:
      summary: Get all subscription plans
      description: Returns a list of all subscription plans with optional filtering
      parameters:
        - name: plan_type
          in: query
          description: Filter by plan type
          schema:
            type: string
        - name: plan_quantity
          in: query
          description: Filter by plan quantity
          schema:
            type: integer
        - name: plan_period
          in: query
          description: Filter by plan period
          schema:
            type: string
        - name: active
          in: query
          description: Filter by active status
          schema:
            type: boolean
        - name: show_to_customer
          in: query
          description: Filter by show to customer
          schema:
            type: string
        - name: search
          in: query
          description: Search term for plan name
          schema:
            type: string
        - name: sort_by
          in: query
          description: Field to sort by
          schema:
            type: string
            default: plan_quantity
        - name: sort_direction
          in: query
          description: Sort direction
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SubscriptionPlan'

    post:
      summary: Create a new subscription plan
      description: Creates a new subscription plan with the provided data
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionPlan'
      responses:
        '201':
          description: Subscription plan created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription plan created successfully
                  data:
                    $ref: '#/components/schemas/SubscriptionPlan'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscription-plans/{id}:
    get:
      summary: Get a specific subscription plan
      description: Returns details of a specific subscription plan
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription plan ID
          schema:
            type: integer
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/SubscriptionPlan'
        '404':
          description: Subscription plan not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      summary: Update a subscription plan
      description: Updates an existing subscription plan with the provided data
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription plan ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionPlan'
      responses:
        '200':
          description: Subscription plan updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription plan updated successfully
                  data:
                    $ref: '#/components/schemas/SubscriptionPlan'
        '404':
          description: Subscription plan not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      summary: Delete a subscription plan
      description: Deletes a specific subscription plan
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription plan ID
          schema:
            type: integer
      responses:
        '200':
          description: Subscription plan deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription plan deleted successfully
        '404':
          description: Subscription plan not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscription-plans/active:
    get:
      summary: Get active subscription plans
      description: Returns a list of active subscription plans
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SubscriptionPlan'

  /subscription-plans/customer:
    get:
      summary: Get subscription plans visible to customers
      description: Returns a list of subscription plans visible to customers
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SubscriptionPlan'

  /subscription-plans/type/{type}:
    get:
      summary: Get subscription plans by type
      description: Returns a list of subscription plans of a specific type
      parameters:
        - name: type
          in: path
          required: true
          description: Plan type
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SubscriptionPlan'

  /subscriptions:
    get:
      summary: Get all subscriptions
      description: Returns a list of all subscriptions with optional filtering
      parameters:
        - name: customer_id
          in: query
          description: Filter by customer ID
          schema:
            type: integer
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
        - name: plan_id
          in: query
          description: Filter by plan ID
          schema:
            type: integer
        - name: start_date
          in: query
          description: Filter by start date
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: Filter by end date
          schema:
            type: string
            format: date
        - name: search
          in: query
          description: Search term for subscription number
          schema:
            type: string
        - name: sort_by
          in: query
          description: Field to sort by
          schema:
            type: string
            default: created_at
        - name: sort_direction
          in: query
          description: Sort direction
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Subscription'

    post:
      summary: Create a new subscription
      description: Creates a new subscription with the provided data
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                customer_id:
                  type: integer
                  description: Customer ID
                plan_id:
                  type: integer
                  description: Plan ID
                start_date:
                  type: string
                  format: date
                  description: Subscription start date
                payment_method:
                  type: string
                  description: Payment method
                payment_status:
                  type: string
                  description: Payment status
                transaction_id:
                  type: string
                  description: Transaction ID
                discount:
                  type: number
                  format: float
                  description: Discount amount
                auto_renew:
                  type: boolean
                  description: Whether the subscription auto-renews
                notes:
                  type: string
                  description: Notes
                items:
                  type: array
                  description: Subscription items
                  items:
                    type: object
                    properties:
                      meal_id:
                        type: integer
                        description: Meal ID
                      meal_name:
                        type: string
                        description: Meal name
                      quantity:
                        type: integer
                        description: Quantity
                      price:
                        type: number
                        format: float
                        description: Price
                      day_of_week:
                        type: string
                        description: Day of week
                      meal_type:
                        type: string
                        description: Meal type
                      is_swappable:
                        type: boolean
                        description: Whether the item is swappable
                      swap_options:
                        type: object
                        description: Swap options
                      notes:
                        type: string
                        description: Notes
              required:
                - customer_id
                - plan_id
      responses:
        '201':
          description: Subscription created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription created successfully
                  data:
                    $ref: '#/components/schemas/Subscription'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscriptions/{id}:
    get:
      summary: Get a specific subscription
      description: Returns details of a specific subscription
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription ID
          schema:
            type: integer
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Subscription'
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      summary: Update a subscription
      description: Updates an existing subscription with the provided data
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  description: Subscription status
                payment_method:
                  type: string
                  description: Payment method
                payment_status:
                  type: string
                  description: Payment status
                transaction_id:
                  type: string
                  description: Transaction ID
                auto_renew:
                  type: boolean
                  description: Whether the subscription auto-renews
                notes:
                  type: string
                  description: Notes
                items:
                  type: array
                  description: Subscription items
                  items:
                    type: object
                    properties:
                      meal_id:
                        type: integer
                        description: Meal ID
                      meal_name:
                        type: string
                        description: Meal name
                      quantity:
                        type: integer
                        description: Quantity
                      price:
                        type: number
                        format: float
                        description: Price
                      day_of_week:
                        type: string
                        description: Day of week
                      meal_type:
                        type: string
                        description: Meal type
                      is_swappable:
                        type: boolean
                        description: Whether the item is swappable
                      swap_options:
                        type: object
                        description: Swap options
                      notes:
                        type: string
                        description: Notes
      responses:
        '200':
          description: Subscription updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription updated successfully
                  data:
                    $ref: '#/components/schemas/Subscription'
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      summary: Delete a subscription
      description: Deletes a specific subscription
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription ID
          schema:
            type: integer
      responses:
        '200':
          description: Subscription deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription deleted successfully
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscriptions/{id}/pause:
    post:
      summary: Pause a subscription
      description: Pauses a specific subscription
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription ID
          schema:
            type: integer
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for pausing
                resume_date:
                  type: string
                  format: date
                  description: Date to resume the subscription
      responses:
        '200':
          description: Subscription paused successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription paused successfully
                  data:
                    $ref: '#/components/schemas/Subscription'
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscriptions/{id}/resume:
    post:
      summary: Resume a subscription
      description: Resumes a paused subscription
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription ID
          schema:
            type: integer
      responses:
        '200':
          description: Subscription resumed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription resumed successfully
                  data:
                    $ref: '#/components/schemas/Subscription'
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscriptions/{id}/cancel:
    post:
      summary: Cancel a subscription
      description: Cancels a specific subscription
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription ID
          schema:
            type: integer
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for cancellation
      responses:
        '200':
          description: Subscription cancelled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription cancelled successfully
                  data:
                    $ref: '#/components/schemas/Subscription'
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscriptions/{id}/renew:
    post:
      summary: Renew a subscription
      description: Renews a specific subscription
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription ID
          schema:
            type: integer
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                days:
                  type: integer
                  description: Number of days to renew for
      responses:
        '200':
          description: Subscription renewed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Subscription renewed successfully
                  data:
                    $ref: '#/components/schemas/Subscription'
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

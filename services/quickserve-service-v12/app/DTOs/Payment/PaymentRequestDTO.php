<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\DTOs\Payment;

/**
 * Payment Request DTO
 * 
 * This class represents a payment request to the Payment Service.
 */
class PaymentRequestDTO
{
    /**
     * Create a new PaymentRequestDTO instance.
     */
    public function __construct(
        public readonly int $customerId,
        public readonly string $customerEmail,
        public readonly string $customerPhone,
        public readonly string $customerName,
        public readonly float $amount,
        public readonly ?string $orderId = null,
        public readonly float $walletAmount = 0,
        public readonly string $successUrl = '',
        public readonly string $failureUrl = '',
        public readonly string $referer = 'website',
        public readonly string $context = 'order',
        public readonly bool $recurring = false,
        public readonly float $discount = 0
    ) {
    }

    /**
     * Convert the DTO to an array.
     */
    public function toArray(): array
    {
        return [
            'customer_id' => $this->customerId,
            'customer_email' => $this->customerEmail,
            'customer_phone' => $this->customerPhone,
            'customer_name' => $this->customerName,
            'amount' => $this->amount,
            'order_id' => $this->orderId,
            'wallet_amount' => $this->walletAmount,
            'success_url' => $this->successUrl,
            'failure_url' => $this->failureUrl,
            'referer' => $this->referer,
            'context' => $this->context,
            'recurring' => $this->recurring,
            'discount' => $this->discount,
        ];
    }
}

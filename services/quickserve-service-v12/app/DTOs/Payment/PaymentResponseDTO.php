<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\DTOs\Payment;

/**
 * Payment Response DTO
 *
 * This class represents a payment response from the Payment Service.
 */
class PaymentResponseDTO
{
    /**
     * Create a new PaymentResponseDTO instance.
     */
    public function __construct(
        public readonly string $transactionId,
        public readonly float $amount,
        public readonly string $status,
        public readonly ?string $paymentUrl = null,
        public readonly ?string $errorMessage = null
    ) {
    }

    /**
     * Convert the DTO to an array.
     */
    public function toArray(): array
    {
        return [
            'transaction_id' => $this->transactionId,
            'amount' => $this->amount,
            'status' => $this->status,
            'payment_url' => $this->paymentUrl,
            'error_message' => $this->errorMessage,
        ];
    }

    /**
     * Check if the payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending' || $this->status === 'initiated';
    }

    /**
     * Check if the payment failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }
}

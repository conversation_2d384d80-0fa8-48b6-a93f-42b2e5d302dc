<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\DTOs\Customer;

/**
 * Customer DTO
 * 
 * This class represents a customer data transfer object.
 */
class CustomerDTO
{
    /**
     * Create a new CustomerDTO instance.
     */
    public function __construct(
        public readonly string $customerName,
        public readonly string $phone,
        public readonly ?string $emailAddress = null,
        public readonly ?string $customerAddress = null,
        public readonly ?string $foodPreference = null,
        public readonly ?string $city = null,
        public readonly ?string $cityName = null,
        public readonly ?string $companyName = null,
        public readonly ?string $password = null,
        public readonly bool $status = true,
        public readonly ?int $companyId = null,
        public readonly ?int $unitId = null,
        public readonly array $addresses = []
    ) {
    }

    /**
     * Convert the DTO to an array.
     */
    public function toArray(): array
    {
        $data = [
            'customer_name' => $this->customerName,
            'phone' => $this->phone,
            'email_address' => $this->emailAddress,
            'customer_Address' => $this->customerAddress,
            'food_preference' => $this->foodPreference,
            'city' => $this->city,
            'city_name' => $this->cityName,
            'company_name' => $this->companyName,
            'status' => $this->status,
            'company_id' => $this->companyId,
            'unit_id' => $this->unitId,
        ];

        if ($this->password) {
            $data['password'] = $this->password;
        }

        if ($this->addresses !== []) {
            $data['addresses'] = array_map(fn(AddressDTO $address): array => $address->toArray(), $this->addresses);
        }

        return $data;
    }

    /**
     * Create a CustomerDTO from an array.
     */
    public static function fromArray(array $data): self
    {
        $addresses = [];
        if (!empty($data['addresses'])) {
            foreach ($data['addresses'] as $addressData) {
                $addresses[] = AddressDTO::fromArray($addressData);
            }
        }

        return new self(
            $data['customer_name'],
            $data['phone'],
            $data['email_address'] ?? null,
            $data['customer_Address'] ?? null,
            $data['food_preference'] ?? null,
            $data['city'] ?? null,
            $data['city_name'] ?? null,
            $data['company_name'] ?? null,
            $data['password'] ?? null,
            $data['status'] ?? true,
            $data['company_id'] ?? null,
            $data['unit_id'] ?? null,
            $addresses
        );
    }
}

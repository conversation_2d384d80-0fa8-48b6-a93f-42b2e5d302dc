<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\DTOs\Customer;

/**
 * Address DTO
 * 
 * This class represents a customer address data transfer object.
 */
class AddressDTO
{
    /**
     * Create a new AddressDTO instance.
     */
    public function __construct(
        public readonly string $addressType,
        public readonly string $address,
        public readonly ?string $locationCode = null,
        public readonly ?string $locationName = null,
        public readonly ?string $landmark = null,
        public readonly ?string $city = null,
        public readonly ?string $state = null,
        public readonly ?string $pincode = null,
        public readonly bool $isDefault = false,
        public readonly ?float $latitude = null,
        public readonly ?float $longitude = null
    ) {
    }

    /**
     * Convert the DTO to an array.
     */
    public function toArray(): array
    {
        return [
            'address_type' => $this->addressType,
            'address' => $this->address,
            'location_code' => $this->locationCode,
            'location_name' => $this->locationName,
            'landmark' => $this->landmark,
            'city' => $this->city,
            'state' => $this->state,
            'pincode' => $this->pincode,
            'is_default' => $this->isDefault,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ];
    }

    /**
     * Create an AddressDTO from an array.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['address_type'],
            $data['address'],
            $data['location_code'] ?? null,
            $data['location_name'] ?? null,
            $data['landmark'] ?? null,
            $data['city'] ?? null,
            $data['state'] ?? null,
            $data['pincode'] ?? null,
            $data['is_default'] ?? false,
            $data['latitude'] ?? null,
            $data['longitude'] ?? null
        );
    }
}

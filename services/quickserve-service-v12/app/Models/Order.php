<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'orders';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_order_no';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_no',
        'ref_order',
        'fk_kitchen_code',
        'auth_id',
        'customer_code',
        'customer_name',
        'email_address',
        'phone',
        'location_code',
        'location_name',
        'city',
        'city_name',
        'product_code',
        'product_name',
        'product_description',
        'product_type',
        'quantity',
        'order_type',
        'order_days',
        'promo_code',
        'amount',
        'tax',
        'delivery_charges',
        'service_charges',
        'line_delivery_charges',
        'applied_discount',
        'total_applied_discount',
        'order_status',
        'order_date',
        'ship_address',
        'delivery_status',
        'invoice_status',
        'order_menu',
        'food_type',
        'amount_paid',
        'tax_method',
        'source',
        'prefered_delivery_person_id',
        'delivery_person',
        'payment_mode',
        'days_preference',
        'tp_delivery',
        'tp_delivery_charges',
        'tp_delivery_charges_type',
        'tp_delivery_order_id',
        'tp_aggregator',
        'tp_aggregator_charges',
        'tp_aggregator_charges_type',
        'delivery_time',
        'delivery_end_time',
        'item_preference',
        'remark',
        'pause_limit',
        'food_preference',
        'company_id',
        'unit_id',
        'created_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'tax' => 'decimal:2',
        'delivery_charges' => 'decimal:2',
        'service_charges' => 'decimal:2',
        'line_delivery_charges' => 'decimal:2',
        'applied_discount' => 'decimal:2',
        'total_applied_discount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'tp_delivery_charges' => 'decimal:2',
        'tp_aggregator_charges' => 'decimal:2',
        'order_date' => 'date',
        'created_date' => 'datetime',
        'food_preference' => 'json',
        'item_preference' => 'json',
    ];

    /**
     * Get the customer that owns the order.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the product that belongs to the order.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_code', 'pk_product_code');
    }

    /**
     * Get the kitchen that belongs to the order.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(Kitchen::class, 'fk_kitchen_code', 'pk_kitchen_code');
    }

    /**
     * Get the location that belongs to the order.
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'location_code', 'pk_location_code');
    }

    /**
     * Get the customer ID (alias for customer_code).
     */
    public function getCustomerIdAttribute(): ?int
    {
        return $this->customer_code ? (int) $this->customer_code : null;
    }

    /**
     * Set the customer ID (alias for customer_code).
     */
    public function setCustomerIdAttribute(?int $value): void
    {
        $this->attributes['customer_code'] = $value;
    }

    /**
     * Get the order ID (alias for pk_order_no).
     */
    public function getIdAttribute(): ?int
    {
        return $this->pk_order_no ? (int) $this->pk_order_no : null;
    }

    /**
     * Get the total amount (alias for amount).
     */
    public function getTotalAmountAttribute(): float
    {
        return (float) $this->amount;
    }

    /**
     * Set the total amount (alias for amount).
     */
    public function setTotalAmountAttribute(float $value): void
    {
        $this->attributes['amount'] = $value;
    }

    /**
     * Get the status (alias for order_status).
     */
    public function getStatusAttribute(): string
    {
        return $this->order_status;
    }

    /**
     * Set the status (alias for order_status).
     */
    public function setStatusAttribute(string $value): void
    {
        $this->attributes['order_status'] = $value;
    }

    /**
     * Calculate the net amount for the order.
     */
    public function getNetAmountAttribute(): float
    {
        if ($this->tax_method === 'inclusive') {
            return $this->amount + $this->delivery_charges + $this->service_charges - $this->applied_discount;
        }
        return $this->amount + $this->tax + $this->delivery_charges + $this->service_charges - $this->applied_discount;
    }

    /**
     * Scope a query to only include orders with a specific status.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('order_status', $status);
    }

    /**
     * Scope a query to only include orders with a specific delivery status.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDeliveryStatus($query, string $status)
    {
        return $query->where('delivery_status', $status);
    }

    /**
     * Scope a query to only include orders for a specific menu.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMenu($query, string $menu)
    {
        return $query->where('order_menu', $menu);
    }
}

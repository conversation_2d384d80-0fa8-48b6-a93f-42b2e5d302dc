<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Location Model
 * 
 * @property int $id
 * @property string $location_code
 * @property string $location_name
 * @property string $address
 * @property string $city
 * @property string $state
 * @property string $pincode
 * @property string $landmark
 * @property float $latitude
 * @property float $longitude
 * @property bool $is_active
 * @property string $delivery_type
 * @property int $company_id
 * @property int $unit_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 */
class Location extends Model
{
    /** @use HasFactory<\Database\Factories\LocationFactory> */
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'locations';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'location_code',
        'location_name',
        'address',
        'city',
        'state',
        'pincode',
        'landmark',
        'latitude',
        'longitude',
        'is_active',
        'delivery_type',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'is_active' => 'boolean',
        'company_id' => 'integer',
        'unit_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the customer addresses for the location.
     *
     * @return HasMany<CustomerAddress, $this>
     */
    public function customerAddresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class, 'location_id');
    }

    /**
     * Get the orders for the location.
     *
     * @return HasMany<Order, $this>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'location_id');
    }

    /**
     * Get the location mappings for the location.
     *
     * @return HasMany<LocationMapping, $this>
     */
    public function locationMappings(): HasMany
    {
        return $this->hasMany(LocationMapping::class, 'location_id');
    }

    /**
     * Scope a query to only include active locations.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Location> $query
     * @return \Illuminate\Database\Eloquent\Builder<Location>
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by city.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Location> $query
     * @param string $city
     * @return \Illuminate\Database\Eloquent\Builder<Location>
     */
    public function scopeCity($query, string $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Scope a query to filter by delivery type.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Location> $query
     * @param string $deliveryType
     * @return \Illuminate\Database\Eloquent\Builder<Location>
     */
    public function scopeDeliveryType($query, string $deliveryType)
    {
        return $query->where('delivery_type', $deliveryType);
    }

    /**
     * Scope a query to filter by company.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Location> $query
     * @param int $companyId
     * @return \Illuminate\Database\Eloquent\Builder<Location>
     */
    public function scopeCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to filter by unit.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Location> $query
     * @param int $unitId
     * @return \Illuminate\Database\Eloquent\Builder<Location>
     */
    public function scopeUnit($query, int $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    /**
     * Get the location's full address.
     */
    public function getFullAddressAttribute(): string
    {
        $address = $this->address;
        if ($this->landmark) {
            $address .= ', ' . $this->landmark;
        }
        return trim($address . ', ' . $this->city . ', ' . $this->state . ' - ' . $this->pincode);
    }

    /**
     * Check if the location is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Get the location's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->location_name . ' (' . $this->location_code . ')';
    }

    /**
     * Calculate distance from given coordinates.
     */
    public function distanceFrom(float $latitude, float $longitude): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $latDiff = deg2rad($latitude - $this->latitude);
        $lonDiff = deg2rad($longitude - $this->longitude);

        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($this->latitude)) * cos(deg2rad($latitude)) *
             sin($lonDiff / 2) * sin($lonDiff / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * CustomerWallet Model
 * 
 * @property int $id
 * @property int $customer_id
 * @property string $transaction_type
 * @property float $amount
 * @property float $balance
 * @property string $description
 * @property string $reference_id
 * @property string $reference_type
 * @property string $status
 * @property int $company_id
 * @property int $unit_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Customer $customer
 */
class CustomerWallet extends Model
{
    /** @use HasFactory<\Database\Factories\CustomerWalletFactory> */
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'customer_wallet';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'customer_id',
        'transaction_type',
        'amount',
        'balance',
        'description',
        'reference_id',
        'reference_type',
        'status',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'customer_id' => 'integer',
        'amount' => 'float',
        'balance' => 'float',
        'company_id' => 'integer',
        'unit_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Transaction types
     */
    public const TRANSACTION_TYPE_CREDIT = 'credit';
    public const TRANSACTION_TYPE_DEBIT = 'debit';

    /**
     * Transaction statuses
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_FAILED = 'failed';
    public const STATUS_CANCELLED = 'cancelled';

    /**
     * Reference types
     */
    public const REFERENCE_TYPE_ORDER = 'order';
    public const REFERENCE_TYPE_REFUND = 'refund';
    public const REFERENCE_TYPE_TOPUP = 'topup';
    public const REFERENCE_TYPE_CASHBACK = 'cashback';
    public const REFERENCE_TYPE_ADJUSTMENT = 'adjustment';

    /**
     * Get the customer that owns the wallet transaction.
     *
     * @return BelongsTo<Customer, $this>
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Scope a query to only include credit transactions.
     *
     * @param \Illuminate\Database\Eloquent\Builder<CustomerWallet> $query
     * @return \Illuminate\Database\Eloquent\Builder<CustomerWallet>
     */
    public function scopeCredit($query)
    {
        return $query->where('transaction_type', self::TRANSACTION_TYPE_CREDIT);
    }

    /**
     * Scope a query to only include debit transactions.
     *
     * @param \Illuminate\Database\Eloquent\Builder<CustomerWallet> $query
     * @return \Illuminate\Database\Eloquent\Builder<CustomerWallet>
     */
    public function scopeDebit($query)
    {
        return $query->where('transaction_type', self::TRANSACTION_TYPE_DEBIT);
    }

    /**
     * Scope a query to filter by status.
     *
     * @param \Illuminate\Database\Eloquent\Builder<CustomerWallet> $query
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Builder<CustomerWallet>
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by reference type.
     *
     * @param \Illuminate\Database\Eloquent\Builder<CustomerWallet> $query
     * @param string $referenceType
     * @return \Illuminate\Database\Eloquent\Builder<CustomerWallet>
     */
    public function scopeReferenceType($query, string $referenceType)
    {
        return $query->where('reference_type', $referenceType);
    }

    /**
     * Scope a query to filter by customer.
     *
     * @param \Illuminate\Database\Eloquent\Builder<CustomerWallet> $query
     * @param int $customerId
     * @return \Illuminate\Database\Eloquent\Builder<CustomerWallet>
     */
    public function scopeCustomer($query, int $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Check if the transaction is a credit.
     */
    public function isCredit(): bool
    {
        return $this->transaction_type === self::TRANSACTION_TYPE_CREDIT;
    }

    /**
     * Check if the transaction is a debit.
     */
    public function isDebit(): bool
    {
        return $this->transaction_type === self::TRANSACTION_TYPE_DEBIT;
    }

    /**
     * Check if the transaction is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if the transaction is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Get the formatted amount with sign.
     */
    public function getFormattedAmountAttribute(): string
    {
        $sign = $this->isCredit() ? '+' : '-';
        return $sign . number_format($this->amount, 2);
    }
}

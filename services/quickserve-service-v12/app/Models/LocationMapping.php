<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocationMapping extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'location_mapping';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'location_code',
        'location_name',
        'city_code',
        'city_name',
        'delivery_charges',
        'delivery_time',
        'kitchen_code',
        'kitchen_name',
        'description',
        'status'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'delivery_charges' => 'decimal:2',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope a query to only include active locations.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope a query to only include locations for a specific city.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $cityCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCity($query, $cityCode)
    {
        return $query->where('city_code', $cityCode);
    }

    /**
     * Scope a query to only include locations for a specific kitchen.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $kitchenCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeKitchen($query, $kitchenCode)
    {
        return $query->where('kitchen_code', $kitchenCode);
    }
}

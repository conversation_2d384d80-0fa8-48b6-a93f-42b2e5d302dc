<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Hash;

class Customer extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_code';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_name',
        'phone',
        'email_address',
        'customer_Address',
        'location_code',
        'location_name',
        'lunch_loc_code',
        'lunch_loc_name',
        'lunch_add',
        'dinner_loc_code',
        'dinner_loc_name',
        'dinner_add',
        'food_preference',
        'city',
        'city_name',
        'company_name',
        'group_code',
        'group_name',
        'registered_on',
        'registered_from',
        'food_referance',
        'status',
        'otp',
        'password',
        'thirdparty',
        'phone_verified',
        'subscription_notification',
        'email_verified',
        'source',
        'referer',
        'gcm_id',
        'alt_phone',
        'company_id',
        'unit_id',
        'dabbawala_code_type',
        'dabbawala_code',
        'dabbawala_image',
        'isguest',
        'delivery_note',
        'modified_on',
        'dabba_status'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'otp',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
        'phone_verified' => 'boolean',
        'email_verified' => 'boolean',
        'subscription_notification' => 'boolean',
        'registered_on' => 'datetime',
        'modified_on' => 'datetime',
        'isguest' => 'boolean',
        'dabba_status' => 'boolean',
    ];

    /**
     * Get the customer's full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->customer_name;
    }

    /**
     * Get the customer's name (alias for customer_name).
     */
    public function getNameAttribute(): string
    {
        return $this->customer_name;
    }

    /**
     * Set the customer's name (alias for customer_name).
     */
    public function setNameAttribute(string $value): void
    {
        $this->attributes['customer_name'] = $value;
    }

    /**
     * Get the customer's email (alias for email_address).
     */
    public function getEmailAttribute(): ?string
    {
        return $this->email_address;
    }

    /**
     * Set the customer's email (alias for email_address).
     */
    public function setEmailAttribute(?string $value): void
    {
        $this->attributes['email_address'] = $value;
    }

    /**
     * Get the customer's ID (alias for pk_customer_code).
     */
    public function getIdAttribute(): int
    {
        return $this->pk_customer_code;
    }

    /**
     * Get the customer's address (alias for customer_Address).
     */
    public function getAddressAttribute(): ?string
    {
        return $this->customer_Address;
    }

    /**
     * Set the customer's address (alias for customer_Address).
     */
    public function setAddressAttribute(?string $value): void
    {
        $this->attributes['customer_Address'] = $value;
    }

    /**
     * Check if the customer is active.
     */
    public function isActive(): bool
    {
        return (bool) $this->status;
    }

    /**
     * Get the customer's addresses.
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class, 'fk_customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's orders.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's wallet.
     */
    public function wallet(): HasMany
    {
        return $this->hasMany(CustomerWallet::class, 'fk_customer_code', 'pk_customer_code');
    }

    /**
     * Set the customer's password.
     */
    public function setPasswordAttribute(string $value): void
    {
        $this->attributes['password'] = Hash::make($value);
    }

    /**
     * Scope a query to only include active customers.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope a query to only include customers with verified phone.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePhoneVerified($query)
    {
        return $query->where('phone_verified', 1);
    }

    /**
     * Scope a query to only include customers with verified email.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEmailVerified($query)
    {
        return $query->where('email_verified', 1);
    }

    /**
     * Scope a query to only include customers from a specific city.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCity($query, string $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Scope a query to only include customers from a specific group.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeGroup($query, string $groupCode)
    {
        return $query->where('group_code', $groupCode);
    }

    /**
     * Check if the customer has verified their phone.
     */
    public function hasVerifiedPhone(): bool
    {
        return (bool) $this->phone_verified;
    }

    /**
     * Check if the customer has verified their email.
     */
    public function hasVerifiedEmail(): bool
    {
        return (bool) $this->email_verified;
    }

    /**
     * Mark the customer's phone as verified.
     */
    public function markPhoneAsVerified(): bool
    {
        return $this->forceFill([
            'phone_verified' => true,
        ])->save();
    }

    /**
     * Mark the customer's email as verified.
     */
    public function markEmailAsVerified(): bool
    {
        return $this->forceFill([
            'email_verified' => true,
        ])->save();
    }

    /**
     * Generate a random OTP for the customer.
     */
    public function generateOtp(int $length = 6): string
    {
        $otp = '';
        for ($i = 0; $i < $length; $i++) {
            $otp .= mt_rand(0, 9);
        }

        $this->forceFill([
            'otp' => $otp,
        ])->save();

        return $otp;
    }

    /**
     * Verify the customer's OTP.
     */
    public function verifyOtp(string $otp): bool
    {
        return $this->otp === $otp;
    }
}

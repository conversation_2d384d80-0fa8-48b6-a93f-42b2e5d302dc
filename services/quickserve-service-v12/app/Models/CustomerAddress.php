<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerAddress extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_address';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_address_code';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fk_customer_code',
        'address',
        'location_code',
        'location_name',
        'city',
        'city_name',
        'menu_type',
        'address_type',
        'landmark',
        'latitude',
        'longitude',
        'is_default',
        'created_on',
        'modified_on',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_default' => 'boolean',
        'created_on' => 'datetime',
        'modified_on' => 'datetime',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * Get the customer that owns the address.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'fk_customer_code', 'pk_customer_code');
    }

    /**
     * Get the location that belongs to the address.
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'location_code', 'pk_location_code');
    }

    /**
     * Scope a query to only include default addresses.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', 1);
    }

    /**
     * Scope a query to only include addresses of a specific menu type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMenuType($query, string $menuType)
    {
        return $query->where('menu_type', $menuType);
    }

    /**
     * Scope a query to only include addresses of a specific address type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAddressType($query, string $addressType)
    {
        return $query->where('address_type', $addressType);
    }

    /**
     * Scope a query to only include addresses in a specific city.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCity($query, string $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = [$this->address];
        
        if (!empty($this->landmark)) {
            $parts[] = $this->landmark;
        }
        
        if (!empty($this->city_name)) {
            $parts[] = $this->city_name;
        }
        
        return implode(', ', $parts);
    }
}

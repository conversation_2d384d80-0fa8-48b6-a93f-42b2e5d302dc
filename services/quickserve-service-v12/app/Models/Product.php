<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'products';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_product_code';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Food types available for products
     *
     * @var array
     */
    public static $foodTypes = [
        'veg' => 'Vegetarian',
        'non-veg' => 'Non-vegetarian',
        'jain' => 'Jain',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'recipe',
        'unit_price',
        'product_type',
        'product_category',
        'category',
        'screen',
        'threshold',
        'max_quantity_per_meal',
        'quantity',
        'unit',
        'status',
        'kitchen_code',
        'product_subtype',
        'food_type',
        'image_path',
        'is_swappable',
        'swap_with',
        'swap_charges',
        'sequence',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'threshold' => 'integer',
        'max_quantity_per_meal' => 'integer',
        'quantity' => 'decimal:2',
        'status' => 'boolean',
        'is_swappable' => 'boolean',
        'swap_charges' => 'decimal:2',
        'sequence' => 'integer',
    ];

    /**
     * Get the orders for the product.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'product_code', 'pk_product_code');
    }

    /**
     * Get the product calendars for the product.
     */
    public function productCalendars(): HasMany
    {
        return $this->hasMany(ProductCalendar::class, 'fk_product_code', 'pk_product_code');
    }

    /**
     * Scope a query to only include active products.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope a query to only include products of a specific type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeType($query, string $type)
    {
        return $query->where('product_type', $type);
    }

    /**
     * Scope a query to only include products of a specific food type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFoodType($query, string $foodType)
    {
        return $query->where('food_type', $foodType);
    }

    /**
     * Scope a query to only include products for a specific kitchen.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int|string $kitchenCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeKitchen($query, $kitchenCode)
    {
        return $query->where('screen', $kitchenCode);
    }

    /**
     * Scope a query to only include products of a specific category.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCategory($query, string $category)
    {
        return $query->where('product_category', $category);
    }

    /**
     * Scope a query to only include products with a specific subtype.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSubtype($query, string $subtype)
    {
        return $query->where('product_subtype', $subtype);
    }

    /**
     * Check if the product is a meal.
     */
    public function isMeal(): bool
    {
        return $this->product_type === 'Meal';
    }

    /**
     * Check if the product is an extra.
     */
    public function isExtra(): bool
    {
        return $this->product_type === 'Extra';
    }

    /**
     * Check if the product is vegetarian.
     */
    public function isVegetarian(): bool
    {
        return $this->food_type === 'veg';
    }

    /**
     * Check if the product is swappable.
     */
    public function isSwappable(): bool
    {
        return (bool) $this->is_swappable;
    }
}

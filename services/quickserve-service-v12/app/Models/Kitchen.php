<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kitchen Model
 * 
 * @property int $id
 * @property string $kitchen_code
 * @property string $kitchen_name
 * @property string $address
 * @property string $city
 * @property string $state
 * @property string $pincode
 * @property string $phone
 * @property string $email
 * @property string $contact_person
 * @property bool $is_active
 * @property int $company_id
 * @property int $unit_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 */
class Kitchen extends Model
{
    /** @use HasFactory<\Database\Factories\KitchenFactory> */
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'kitchens';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'kitchen_code',
        'kitchen_name',
        'address',
        'city',
        'state',
        'pincode',
        'phone',
        'email',
        'contact_person',
        'is_active',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'company_id' => 'integer',
        'unit_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the orders for the kitchen.
     *
     * @return HasMany<Order, $this>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'kitchen_id');
    }

    /**
     * Get the products for the kitchen.
     *
     * @return HasMany<Product, $this>
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'kitchen_id');
    }

    /**
     * Get the timeslots for the kitchen.
     *
     * @return HasMany<Timeslot, $this>
     */
    public function timeslots(): HasMany
    {
        return $this->hasMany(Timeslot::class, 'kitchen_id');
    }

    /**
     * Scope a query to only include active kitchens.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Kitchen> $query
     * @return \Illuminate\Database\Eloquent\Builder<Kitchen>
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by city.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Kitchen> $query
     * @param string $city
     * @return \Illuminate\Database\Eloquent\Builder<Kitchen>
     */
    public function scopeCity($query, string $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Scope a query to filter by company.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Kitchen> $query
     * @param int $companyId
     * @return \Illuminate\Database\Eloquent\Builder<Kitchen>
     */
    public function scopeCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to filter by unit.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Kitchen> $query
     * @param int $unitId
     * @return \Illuminate\Database\Eloquent\Builder<Kitchen>
     */
    public function scopeUnit($query, int $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    /**
     * Get the kitchen's full address.
     */
    public function getFullAddressAttribute(): string
    {
        return trim($this->address . ', ' . $this->city . ', ' . $this->state . ' - ' . $this->pincode);
    }

    /**
     * Check if the kitchen is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Get the kitchen's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->kitchen_name . ' (' . $this->kitchen_code . ')';
    }
}

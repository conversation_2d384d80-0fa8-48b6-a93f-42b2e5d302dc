<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * ProductCalendar Model
 * 
 * @property int $id
 * @property int $product_id
 * @property string $date
 * @property string $menu_type
 * @property bool $is_available
 * @property int $quantity_available
 * @property float $price
 * @property float $discount_percentage
 * @property string $special_notes
 * @property int $company_id
 * @property int $unit_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Product $product
 */
class ProductCalendar extends Model
{
    /** @use HasFactory<\Database\Factories\ProductCalendarFactory> */
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'product_calendar';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'product_id',
        'date',
        'menu_type',
        'is_available',
        'quantity_available',
        'price',
        'discount_percentage',
        'special_notes',
        'company_id',
        'unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'product_id' => 'integer',
        'date' => 'date',
        'is_available' => 'boolean',
        'quantity_available' => 'integer',
        'price' => 'float',
        'discount_percentage' => 'float',
        'company_id' => 'integer',
        'unit_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Menu types
     */
    public const MENU_TYPE_BREAKFAST = 'breakfast';
    public const MENU_TYPE_LUNCH = 'lunch';
    public const MENU_TYPE_DINNER = 'dinner';
    public const MENU_TYPE_SNACKS = 'snacks';

    /**
     * Get the product that owns the calendar entry.
     *
     * @return BelongsTo<Product, $this>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Scope a query to only include available products.
     *
     * @param \Illuminate\Database\Eloquent\Builder<ProductCalendar> $query
     * @return \Illuminate\Database\Eloquent\Builder<ProductCalendar>
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope a query to filter by date.
     *
     * @param \Illuminate\Database\Eloquent\Builder<ProductCalendar> $query
     * @param string $date
     * @return \Illuminate\Database\Eloquent\Builder<ProductCalendar>
     */
    public function scopeDate($query, string $date)
    {
        return $query->where('date', $date);
    }

    /**
     * Scope a query to filter by menu type.
     *
     * @param \Illuminate\Database\Eloquent\Builder<ProductCalendar> $query
     * @param string $menuType
     * @return \Illuminate\Database\Eloquent\Builder<ProductCalendar>
     */
    public function scopeMenuType($query, string $menuType)
    {
        return $query->where('menu_type', $menuType);
    }

    /**
     * Scope a query to filter by date range.
     *
     * @param \Illuminate\Database\Eloquent\Builder<ProductCalendar> $query
     * @param string $startDate
     * @param string $endDate
     * @return \Illuminate\Database\Eloquent\Builder<ProductCalendar>
     */
    public function scopeDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope a query to filter by company.
     *
     * @param \Illuminate\Database\Eloquent\Builder<ProductCalendar> $query
     * @param int $companyId
     * @return \Illuminate\Database\Eloquent\Builder<ProductCalendar>
     */
    public function scopeCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to filter by unit.
     *
     * @param \Illuminate\Database\Eloquent\Builder<ProductCalendar> $query
     * @param int $unitId
     * @return \Illuminate\Database\Eloquent\Builder<ProductCalendar>
     */
    public function scopeUnit($query, int $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    /**
     * Check if the product is available.
     */
    public function isAvailable(): bool
    {
        return $this->is_available && $this->quantity_available > 0;
    }

    /**
     * Get the discounted price.
     */
    public function getDiscountedPriceAttribute(): float
    {
        if ($this->discount_percentage > 0) {
            return $this->price * (1 - ($this->discount_percentage / 100));
        }
        return $this->price;
    }

    /**
     * Get the discount amount.
     */
    public function getDiscountAmountAttribute(): float
    {
        if ($this->discount_percentage > 0) {
            return $this->price * ($this->discount_percentage / 100);
        }
        return 0;
    }

    /**
     * Check if the product has a discount.
     */
    public function hasDiscount(): bool
    {
        return $this->discount_percentage > 0;
    }

    /**
     * Get the formatted date.
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->date->format('Y-m-d');
    }

    /**
     * Get the menu type display name.
     */
    public function getMenuTypeDisplayAttribute(): string
    {
        return match ($this->menu_type) {
            self::MENU_TYPE_BREAKFAST => 'Breakfast',
            self::MENU_TYPE_LUNCH => 'Lunch',
            self::MENU_TYPE_DINNER => 'Dinner',
            self::MENU_TYPE_SNACKS => 'Snacks',
            default => ucfirst($this->menu_type),
        };
    }
}

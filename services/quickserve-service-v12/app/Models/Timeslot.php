<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Timeslot extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'timeslot';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'starttime',
        'endtime',
        'day',
        'menu_type',
        'kitchen',
        'status'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope a query to only include active timeslots.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope a query to only include timeslots for a specific day.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $day
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDay($query, $day)
    {
        return $query->where('day', $day);
    }

    /**
     * Scope a query to only include timeslots for a specific menu type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $menuType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMenuType($query, $menuType)
    {
        return $query->where('menu_type', $menuType);
    }

    /**
     * Scope a query to only include timeslots for a specific kitchen.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $kitchen
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeKitchen($query, $kitchen)
    {
        return $query->where('kitchen', $kitchen);
    }

    /**
     * Get formatted start time.
     */
    public function getFormattedStartTimeAttribute(): string
    {
        return date('h:i A', strtotime($this->starttime));
    }

    /**
     * Get formatted end time.
     */
    public function getFormattedEndTimeAttribute(): string
    {
        return date('h:i A', strtotime($this->endtime));
    }

    /**
     * Get display slot (formatted start time to end time).
     */
    public function getDisplaySlotAttribute(): string
    {
        return $this->formatted_start_time . ' to ' . $this->formatted_end_time;
    }
}

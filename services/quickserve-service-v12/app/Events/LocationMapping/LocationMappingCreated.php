<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Events\LocationMapping;

use App\Models\LocationMapping;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LocationMappingCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The location mapping instance.
     *
     * @var LocationMapping
     */
    public $locationMapping;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(LocationMapping $locationMapping)
    {
        $this->locationMapping = $locationMapping;
    }
}

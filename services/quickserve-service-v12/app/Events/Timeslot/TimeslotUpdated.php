<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Events\Timeslot;

use App\Models\Timeslot;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TimeslotUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The timeslot instance.
     *
     * @var Timeslot
     */
    public $timeslot;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Timeslot $timeslot)
    {
        $this->timeslot = $timeslot;
    }
}

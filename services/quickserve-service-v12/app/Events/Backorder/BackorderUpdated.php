<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Events\Backorder;

use App\Models\Backorder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BackorderUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The backorder instance.
     *
     * @var Backorder
     */
    public $backorder;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Backorder $backorder)
    {
        $this->backorder = $backorder;
    }
}

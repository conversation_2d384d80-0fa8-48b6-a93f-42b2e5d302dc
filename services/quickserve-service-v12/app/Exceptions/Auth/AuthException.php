<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Exceptions\Auth;

use Exception;

/**
 * Auth Exception
 * 
 * This exception is thrown when there is an error with the Auth Service.
 */
class AuthException extends Exception
{
    /**
     * Create a new AuthException instance.
     *
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "Auth service error", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Exceptions\Product;

use Exception;

class ProductException extends Exception
{
    /**
     * Create a new product exception instance.
     */
    public function __construct(string $message = 'Product operation failed', int $code = 0, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Report the exception.
     */
    public function report(): void
    {
        \Log::error('Product Exception: ' . $this->getMessage(), [
            'exception' => $this,
            'trace' => $this->getTraceAsString()
        ]);
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render()
    {
        return response()->json([
            'status' => 'error',
            'message' => $this->getMessage(),
            'code' => $this->getCode()
        ], 400);
    }
}

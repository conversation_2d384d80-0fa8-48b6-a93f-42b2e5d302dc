<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Exceptions\Customer;

use Exception;

/**
 * Customer Exception
 * 
 * This exception is thrown when there is an error with the Customer Service.
 */
class CustomerException extends Exception
{
    /**
     * Create a new CustomerException instance.
     *
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "Customer service error", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

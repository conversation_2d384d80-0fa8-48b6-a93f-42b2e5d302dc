<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Exceptions;

use Exception;

class CircuitBreakerOpenException extends Exception
{
    /**
     * CircuitBreakerOpenException constructor.
     *
     * @param \Throwable|null $previous
     */
    public function __construct(string $message, protected int $remainingSeconds, int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Get the remaining seconds until the circuit breaker resets.
     */
    public function getRemainingSeconds(): int
    {
        return $this->remainingSeconds;
    }
}

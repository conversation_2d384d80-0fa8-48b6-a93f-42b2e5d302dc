<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Exceptions\Payment;

use Exception;

/**
 * Payment Exception
 * 
 * This exception is thrown when there is an error with the Payment Service.
 */
class PaymentException extends Exception
{
    /**
     * Create a new PaymentException instance.
     *
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "Payment service error", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

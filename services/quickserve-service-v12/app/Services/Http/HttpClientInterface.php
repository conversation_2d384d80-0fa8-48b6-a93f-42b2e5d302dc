<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Http;

interface HttpClientInterface
{
    /**
     * Send a GET request.
     */
    public function get(string $url, array $query = [], array $headers = []): array;

    /**
     * Send a POST request.
     */
    public function post(string $url, array $data = [], array $headers = []): array;

    /**
     * Send a PUT request.
     */
    public function put(string $url, array $data = [], array $headers = []): array;

    /**
     * Send a DELETE request.
     */
    public function delete(string $url, array $data = [], array $headers = []): array;

    /**
     * Send a PATCH request.
     */
    public function patch(string $url, array $data = [], array $headers = []): array;

    /**
     * Set the service name for the circuit breaker.
     */
    public function withService(string $serviceName): self;

    /**
     * Set the timeout for the request.
     */
    public function withTimeout(int $seconds): self;

    /**
     * Set the retry count for the request.
     */
    public function withRetries(int $count): self;

    /**
     * Set the fallback for the request.
     */
    public function withFallback(callable $fallback): self;

    /**
     * Set the correlation ID for the request.
     */
    public function withCorrelationId(string $correlationId): self;
}

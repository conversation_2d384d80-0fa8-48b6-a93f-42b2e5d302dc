<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Http;

use App\Exceptions\CircuitBreakerOpenException;
use App\Services\CircuitBreaker\CircuitBreakerInterface;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class HttpClient implements HttpClientInterface
{
    /**
     * @var CircuitBreakerInterface
     */
    protected $circuitBreaker;

    protected string $serviceName;

    /**
     * @var int
     */
    protected $timeout = 30;

    /**
     * @var int
     */
    protected $retries = 3;

    /**
     * @var callable|null
     */
    protected $fallback;

    /**
     * @var string|null
     */
    protected $correlationId;

    /**
     * HttpClient constructor.
     */
    public function __construct(protected \GuzzleHttp\Client $client, CircuitBreakerInterface $circuitBreaker)
    {
        $this->circuitBreaker = $circuitBreaker;
        $this->serviceName = $circuitBreaker->getServiceName();
    }

    /**
     * Send a GET request.
     *
     * @throws \Exception
     */
    public function get(string $url, array $query = [], array $headers = []): array
    {
        return $this->request('GET', $url, ['query' => $query, 'headers' => $this->prepareHeaders($headers)]);
    }

    /**
     * Send a POST request.
     *
     * @throws \Exception
     */
    public function post(string $url, array $data = [], array $headers = []): array
    {
        return $this->request('POST', $url, ['json' => $data, 'headers' => $this->prepareHeaders($headers)]);
    }

    /**
     * Send a PUT request.
     *
     * @throws \Exception
     */
    public function put(string $url, array $data = [], array $headers = []): array
    {
        return $this->request('PUT', $url, ['json' => $data, 'headers' => $this->prepareHeaders($headers)]);
    }

    /**
     * Send a DELETE request.
     *
     * @throws \Exception
     */
    public function delete(string $url, array $data = [], array $headers = []): array
    {
        return $this->request('DELETE', $url, ['json' => $data, 'headers' => $this->prepareHeaders($headers)]);
    }

    /**
     * Send a PATCH request.
     *
     * @throws \Exception
     */
    public function patch(string $url, array $data = [], array $headers = []): array
    {
        return $this->request('PATCH', $url, ['json' => $data, 'headers' => $this->prepareHeaders($headers)]);
    }

    /**
     * Set the service name for the circuit breaker.
     */
    public function withService(string $serviceName): self
    {
        $this->serviceName = $serviceName;
        
        // Get the circuit breaker for the service
        $this->circuitBreaker = app("circuit_breaker.{$serviceName}");
        
        return $this;
    }

    /**
     * Set the timeout for the request.
     */
    public function withTimeout(int $seconds): self
    {
        $this->timeout = $seconds;
        return $this;
    }

    /**
     * Set the retry count for the request.
     */
    public function withRetries(int $count): self
    {
        $this->retries = $count;
        return $this;
    }

    /**
     * Set the fallback for the request.
     */
    public function withFallback(callable $fallback): self
    {
        $this->fallback = $fallback;
        return $this;
    }

    /**
     * Set the correlation ID for the request.
     */
    public function withCorrelationId(string $correlationId): self
    {
        $this->correlationId = $correlationId;
        return $this;
    }

    /**
     * Send a request with circuit breaker protection.
     *
     * @throws \Exception
     */
    protected function request(string $method, string $url, array $options = []): array
    {
        // Add timeout to options
        $options['timeout'] = $this->timeout;

        // Generate correlation ID if not provided
        if ($this->correlationId === null) {
            $this->correlationId = Str::uuid()->toString();
        }

        // Add correlation ID to options
        $options['headers']['X-Correlation-ID'] = $this->correlationId;

        // Log the request
        Log::info("Sending {$method} request to {$url}", [
            'service' => $this->serviceName,
            'method' => $method,
            'url' => $url,
            'correlation_id' => $this->correlationId,
        ]);

        // Execute the request with circuit breaker protection
        try {
            return $this->circuitBreaker->execute(
                fn(): array => $this->executeWithRetries($method, $url, $options),
                $this->fallback
            );
        } catch (CircuitBreakerOpenException $e) {
            Log::warning("Circuit breaker for {$this->serviceName} is open", [
                'remaining_seconds' => $e->getRemainingSeconds(),
                'correlation_id' => $this->correlationId,
            ]);

            if ($this->fallback !== null) {
                return ($this->fallback)();
            }

            throw $e;
        }
    }

    /**
     * Execute a request with retries.
     *
     * @throws \Exception
     */
    protected function executeWithRetries(string $method, string $url, array $options): array
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $this->retries) {
            try {
                $response = $this->client->request($method, $url, $options);
                $body = $response->getBody()->getContents();
                $statusCode = $response->getStatusCode();

                // Log the response
                Log::info("Received response from {$url}", [
                    'service' => $this->serviceName,
                    'method' => $method,
                    'url' => $url,
                    'status_code' => $statusCode,
                    'correlation_id' => $this->correlationId,
                ]);

                // Parse the response body
                $data = json_decode($body, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    $data = ['body' => $body];
                }

                return [
                    'status_code' => $statusCode,
                    'data' => $data,
                    'headers' => $response->getHeaders(),
                ];
            } catch (GuzzleException $e) {
                $lastException = $e;
                $attempt++;

                // Log the retry attempt
                Log::warning("Request to {$url} failed, retrying ({$attempt}/{$this->retries})", [
                    'service' => $this->serviceName,
                    'method' => $method,
                    'url' => $url,
                    'error' => $e->getMessage(),
                    'correlation_id' => $this->correlationId,
                ]);

                // Wait before retrying (exponential backoff)
                if ($attempt < $this->retries) {
                    usleep(min(1000000, 100000 * 2 ** $attempt));
                }
            }
        }

        // Log the final failure
        Log::error("Request to {$url} failed after {$this->retries} attempts", [
            'service' => $this->serviceName,
            'method' => $method,
            'url' => $url,
            'error' => $lastException instanceof \GuzzleHttp\Exception\GuzzleException ? $lastException->getMessage() : 'Unknown error',
            'correlation_id' => $this->correlationId,
        ]);

        // Throw the last exception
        throw $lastException ?? new \Exception("Request to {$url} failed after {$this->retries} attempts");
    }

    /**
     * Prepare headers for the request.
     */
    protected function prepareHeaders(array $headers): array
    {
        // Add default headers
        $defaultHeaders = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        // Add correlation ID header
        if ($this->correlationId !== null) {
            $defaultHeaders['X-Correlation-ID'] = $this->correlationId;
        }

        // Merge default headers with provided headers
        return array_merge($defaultHeaders, $headers);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Meal;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Exceptions\Meal\MealException;

/**
 * Meal Service Client
 * 
 * This class provides a client for interacting with the Meal Service API.
 */
class MealServiceClient
{
    /**
     * The base URL for the Meal Service API.
     */
    protected string $baseUrl;

    /**
     * Create a new MealServiceClient instance.
     */
    public function __construct(
        ?string $baseUrl = null,
        /**
         * The timeout for API requests in seconds.
         */
        protected int $timeout = 30,
        /**
         * The retry attempts for API requests.
         */
        protected int $retries = 3
    ) {
        $this->baseUrl = $baseUrl ?? config('services.meal.url', 'http://meal-service-v12:8000/api');
    }

    /**
     * Get all meals.
     *
     * @throws MealException
     */
    public function getAllMeals(array $filters = []): array
    {
        try {
            $response = $this->http()
                ->get('/meals', $filters);

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new MealException($response->json('message') ?? 'Failed to get meals');
        } catch (\Exception $e) {
            Log::error('Meal service get all meals failed', [
                'error' => $e->getMessage(),
                'filters' => $filters
            ]);

            throw new MealException('Failed to get meals: ' . $e->getMessage());
        }
    }

    /**
     * Get a meal by ID.
     *
     * @throws MealException
     */
    public function getMeal(int $mealId): ?array
    {
        try {
            $response = $this->http()
                ->get("/meals/{$mealId}");

            if ($response->successful()) {
                return $response->json('data');
            }

            if ($response->status() === 404) {
                return null;
            }

            throw new MealException($response->json('message') ?? 'Failed to get meal');
        } catch (\Exception $e) {
            Log::error('Meal service get meal failed', [
                'error' => $e->getMessage(),
                'meal_id' => $mealId
            ]);

            throw new MealException('Failed to get meal: ' . $e->getMessage());
        }
    }

    /**
     * Get meals by menu.
     *
     * @throws MealException
     */
    public function getMealsByMenu(int $menuId): array
    {
        try {
            $response = $this->http()
                ->get("/meals/menu/{$menuId}");

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new MealException($response->json('message') ?? 'Failed to get meals by menu');
        } catch (\Exception $e) {
            Log::error('Meal service get meals by menu failed', [
                'error' => $e->getMessage(),
                'menu_id' => $menuId
            ]);

            throw new MealException('Failed to get meals by menu: ' . $e->getMessage());
        }
    }

    /**
     * Get vegetarian meals.
     *
     * @throws MealException
     */
    public function getVegetarianMeals(): array
    {
        try {
            $response = $this->http()
                ->get('/meals/type/vegetarian');

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new MealException($response->json('message') ?? 'Failed to get vegetarian meals');
        } catch (\Exception $e) {
            Log::error('Meal service get vegetarian meals failed', [
                'error' => $e->getMessage()
            ]);

            throw new MealException('Failed to get vegetarian meals: ' . $e->getMessage());
        }
    }

    /**
     * Create an HTTP client instance.
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name'),
                'X-Service-Version' => config('app.version', '1.0.0'),
            ]);
    }
}

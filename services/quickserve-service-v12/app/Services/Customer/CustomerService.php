<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Customer;

use App\Models\Customer;
use App\Repositories\Customer\CustomerRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class CustomerService implements CustomerServiceInterface
{
    /**
     * CustomerService constructor.
     */
    public function __construct(protected \App\Repositories\Customer\CustomerRepositoryInterface $customerRepository)
    {
    }

    /**
     * Get all customers.
     */
    public function getAllCustomers(): Collection
    {
        return $this->customerRepository->all();
    }

    /**
     * Get paginated customers.
     */
    public function getPaginatedCustomers(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->customerRepository->paginate($perPage, $filters);
    }

    /**
     * Get customer by ID.
     */
    public function getCustomerById(int $id): ?Customer
    {
        return $this->customerRepository->findById($id);
    }

    /**
     * Get customer by phone.
     */
    public function getCustomerByPhone(string $phone): ?Customer
    {
        return $this->customerRepository->findByPhone($phone);
    }

    /**
     * Get customer by email.
     */
    public function getCustomerByEmail(string $email): ?Customer
    {
        return $this->customerRepository->findByEmail($email);
    }

    /**
     * Create a new customer.
     */
    public function createCustomer(array $data): Customer
    {
        // Set default values
        $data['registered_on'] ??= now();
        $data['registered_from'] ??= 'web';
        $data['status'] ??= true;
        
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        
        return $this->customerRepository->create($data);
    }

    /**
     * Update a customer.
     */
    public function updateCustomer(int $id, array $data): bool
    {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        
        // Set modified date
        $data['modified_on'] = now();
        
        return $this->customerRepository->update($id, $data);
    }

    /**
     * Delete a customer.
     */
    public function deleteCustomer(int $id): bool
    {
        return $this->customerRepository->delete($id);
    }

    /**
     * Send OTP to customer.
     */
    public function sendOtp(Customer $customer, string $method = 'sms'): bool
    {
        try {
            // Generate OTP
            $otp = $customer->generateOtp();
            // Send OTP based on method
            if ($method === 'sms') {
                // In a real implementation, we would use an SMS service
                // For now, we'll just log the OTP
                Log::info("SMS OTP for customer {$customer->pk_customer_code}: {$otp}");
                return true;
            }
            
            // Send OTP based on method
            if ($method === 'email') {
                // In a real implementation, we would use Laravel's Mail facade
                // For now, we'll just log the OTP
                Log::info("Email OTP for customer {$customer->pk_customer_code}: {$otp}");
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to send OTP: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify customer OTP.
     */
    public function verifyOtp(Customer $customer, string $otp, string $method = 'sms'): bool
    {
        if (!$customer->verifyOtp($otp)) {
            return false;
        }
        
        // Mark verification based on method
        if ($method === 'sms') {
            $customer->markPhoneAsVerified();
        } elseif ($method === 'email') {
            $customer->markEmailAsVerified();
        }
        
        return true;
    }

    /**
     * Get customer count.
     */
    public function getCustomerCount(array $filters = []): int
    {
        return $this->customerRepository->count($filters);
    }

    /**
     * Search customers.
     */
    public function searchCustomers(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->customerRepository->search($query, $perPage);
    }
}

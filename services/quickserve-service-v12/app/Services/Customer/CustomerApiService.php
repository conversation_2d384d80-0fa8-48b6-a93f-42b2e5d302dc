<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Customer;

use App\Services\Http\HttpClientInterface;
use Illuminate\Support\Facades\Log;

class CustomerApiService
{
    protected \App\Services\Http\HttpClientInterface $httpClient;

    /**
     * @var string
     */
    protected $baseUrl;

    /**
     * CustomerApiService constructor.
     */
    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient->withService('customer');
        $this->baseUrl = config('http_client.services.customer.base_url');
    }

    /**
     * Get a customer by ID.
     */
    public function getCustomer(int $customerId): array
    {
        try {
            $response = $this->httpClient
                ->withTimeout(10)
                ->withRetries(3)
                ->withFallback(function () use ($customerId): array {
                    Log::warning("Using fallback for customer {$customerId}");
                    return [
                        'status_code' => 200,
                        'data' => [
                            'id' => $customerId,
                            'name' => 'Unknown Customer',
                            'email' => '<EMAIL>',
                            'phone' => '0000000000',
                            'is_fallback' => true,
                        ],
                    ];
                })
                ->get("{$this->baseUrl}/api/v1/customers/{$customerId}");

            return $response['data'] ?? [];
        } catch (\Exception $e) {
            Log::error("Failed to get customer {$customerId}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Get customer addresses.
     */
    public function getCustomerAddresses(int $customerId): array
    {
        try {
            $response = $this->httpClient
                ->withTimeout(10)
                ->withRetries(3)
                ->withFallback(function (): array {
                    Log::warning("Using fallback for customer addresses");
                    return [
                        'status_code' => 200,
                        'data' => [
                            'addresses' => [],
                            'is_fallback' => true,
                        ],
                    ];
                })
                ->get("{$this->baseUrl}/api/v1/customers/{$customerId}/addresses");

            return $response['data']['addresses'] ?? [];
        } catch (\Exception $e) {
            Log::error("Failed to get customer addresses for {$customerId}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Create a customer.
     */
    public function createCustomer(array $data): array
    {
        try {
            $response = $this->httpClient
                ->withTimeout(30)
                ->withRetries(3)
                ->post("{$this->baseUrl}/api/v1/customers", $data);

            return $response['data'] ?? [];
        } catch (\Exception $e) {
            Log::error("Failed to create customer", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            throw $e;
        }
    }

    /**
     * Update a customer.
     */
    public function updateCustomer(int $customerId, array $data): array
    {
        try {
            $response = $this->httpClient
                ->withTimeout(30)
                ->withRetries(3)
                ->put("{$this->baseUrl}/api/v1/customers/{$customerId}", $data);

            return $response['data'] ?? [];
        } catch (\Exception $e) {
            Log::error("Failed to update customer {$customerId}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            throw $e;
        }
    }

    /**
     * Delete a customer.
     */
    public function deleteCustomer(int $customerId): bool
    {
        try {
            $response = $this->httpClient
                ->withTimeout(30)
                ->withRetries(3)
                ->delete("{$this->baseUrl}/api/v1/customers/{$customerId}");

            return ($response['status_code'] ?? 500) < 300;
        } catch (\Exception $e) {
            Log::error("Failed to delete customer {$customerId}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Create a customer address.
     */
    public function createCustomerAddress(int $customerId, array $data): array
    {
        try {
            $response = $this->httpClient
                ->withTimeout(30)
                ->withRetries(3)
                ->post("{$this->baseUrl}/api/v1/customers/{$customerId}/addresses", $data);

            return $response['data'] ?? [];
        } catch (\Exception $e) {
            Log::error("Failed to create customer address for {$customerId}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            throw $e;
        }
    }

    /**
     * Update a customer address.
     */
    public function updateCustomerAddress(int $customerId, int $addressId, array $data): array
    {
        try {
            $response = $this->httpClient
                ->withTimeout(30)
                ->withRetries(3)
                ->put("{$this->baseUrl}/api/v1/customers/{$customerId}/addresses/{$addressId}", $data);

            return $response['data'] ?? [];
        } catch (\Exception $e) {
            Log::error("Failed to update customer address {$addressId} for {$customerId}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            throw $e;
        }
    }

    /**
     * Delete a customer address.
     */
    public function deleteCustomerAddress(int $customerId, int $addressId): bool
    {
        try {
            $response = $this->httpClient
                ->withTimeout(30)
                ->withRetries(3)
                ->delete("{$this->baseUrl}/api/v1/customers/{$customerId}/addresses/{$addressId}");

            return ($response['status_code'] ?? 500) < 300;
        } catch (\Exception $e) {
            Log::error("Failed to delete customer address {$addressId} for {$customerId}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }
}

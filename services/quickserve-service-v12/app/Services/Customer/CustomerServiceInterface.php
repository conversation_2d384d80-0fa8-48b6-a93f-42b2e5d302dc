<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Customer;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface CustomerServiceInterface
{
    /**
     * Get all customers.
     */
    public function getAllCustomers(): Collection;

    /**
     * Get paginated customers.
     */
    public function getPaginatedCustomers(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get customer by ID.
     */
    public function getCustomerById(int $id): ?Customer;

    /**
     * Get customer by phone.
     */
    public function getCustomerByPhone(string $phone): ?Customer;

    /**
     * Get customer by email.
     */
    public function getCustomerByEmail(string $email): ?Customer;

    /**
     * Create a new customer.
     */
    public function createCustomer(array $data): Customer;

    /**
     * Update a customer.
     */
    public function updateCustomer(int $id, array $data): bool;

    /**
     * Delete a customer.
     */
    public function deleteCustomer(int $id): bool;

    /**
     * Send OTP to customer.
     */
    public function sendOtp(Customer $customer, string $method = 'sms'): bool;

    /**
     * Verify customer OTP.
     */
    public function verifyOtp(Customer $customer, string $otp, string $method = 'sms'): bool;

    /**
     * Get customer count.
     */
    public function getCustomerCount(array $filters = []): int;

    /**
     * Search customers.
     */
    public function searchCustomers(string $query, int $perPage = 15): LengthAwarePaginator;
}

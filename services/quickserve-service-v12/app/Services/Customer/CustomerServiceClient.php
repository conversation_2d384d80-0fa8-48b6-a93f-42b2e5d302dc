<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Customer;

use App\DTOs\Customer\AddressDTO;
use App\DTOs\Customer\CustomerDTO;
use App\Exceptions\Customer\CustomerException;
use App\Services\Resilience\CircuitBreaker;
use App\Services\Resilience\Retry;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Customer Service Client
 *
 * This class provides a client for interacting with the Customer Service API.
 */
class CustomerServiceClient
{
    /**
     * The base URL for the Customer Service API.
     */
    protected string $baseUrl;

    /**
     * The circuit breaker instance.
     */
    protected CircuitBreaker $circuitBreaker;

    /**
     * The retry instance.
     */
    protected Retry $retry;

    /**
     * Create a new CustomerServiceClient instance.
     */
    public function __construct(
        ?string $baseUrl = null,
        /**
         * The timeout for API requests in seconds.
         */
        protected int $timeout = 30,
        /**
         * The retry attempts for API requests.
         */
        protected int $retries = 3
    ) {
        $this->baseUrl = $baseUrl ?? config('services.customer.url', 'http://customer-service-v12:8000/api');

        // Initialize circuit breaker and retry
        $this->circuitBreaker = new CircuitBreaker('customer-service', 5, 60);
        $this->retry = new Retry('customer-service', $this->retries, 100, 5000);
    }

    /**
     * Get a customer by ID or code.
     *
     * @param string|int $customerIdOrCode
     * @throws CustomerException
     */
    public function getCustomer($customerIdOrCode): ?array
    {
        try {
            return $this->circuitBreaker->execute(
                fn() => $this->retry->execute(
                    function () use ($customerIdOrCode) {
                        $response = $this->http()
                            ->get("/customers/{$customerIdOrCode}");

                        if ($response->successful()) {
                            return $response->json('data');
                        }

                        if ($response->status() === 404) {
                            return null;
                        }

                        throw new CustomerException($response->json('message') ?? 'Failed to get customer');
                    },
                    fn(\Exception $e): bool =>
                        // Only retry on network errors or 5xx responses
                        !($e instanceof CustomerException) ||
                           str_contains($e->getMessage(), '5')
                ),
                function () use ($customerIdOrCode): null {
                    // Fallback when circuit is open
                    Log::warning('Customer service circuit open, using fallback for customer', [
                        'customer_id_or_code' => $customerIdOrCode
                    ]);

                    // Return a minimal customer record or null
                    // In a real implementation, this might come from a cache
                    return null;
                }
            );
        } catch (\Exception $e) {
            Log::error('Customer service get customer failed', [
                'error' => $e->getMessage(),
                'customer_id_or_code' => $customerIdOrCode
            ]);

            throw new CustomerException('Failed to get customer: ' . $e->getMessage());
        }
    }

    /**
     * Create a new customer.
     *
     * @throws CustomerException
     */
    public function createCustomer(CustomerDTO $customerDTO): array
    {
        try {
            $response = $this->http()
                ->post('/customers', $customerDTO->toArray());

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new CustomerException($response->json('message') ?? 'Failed to create customer');
        } catch (\Exception $e) {
            Log::error('Customer service create customer failed', [
                'error' => $e->getMessage(),
                'customer_data' => $customerDTO->toArray()
            ]);

            throw new CustomerException('Failed to create customer: ' . $e->getMessage());
        }
    }

    /**
     * Update a customer.
     *
     * @throws CustomerException
     */
    public function updateCustomer(int $customerId, CustomerDTO $customerDTO): array
    {
        try {
            $response = $this->http()
                ->put("/customers/{$customerId}", $customerDTO->toArray());

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new CustomerException($response->json('message') ?? 'Failed to update customer');
        } catch (\Exception $e) {
            Log::error('Customer service update customer failed', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId,
                'customer_data' => $customerDTO->toArray()
            ]);

            throw new CustomerException('Failed to update customer: ' . $e->getMessage());
        }
    }

    /**
     * Get customer wallet balance.
     *
     * @param string|int $customerIdOrCode
     * @throws CustomerException
     */
    public function getWalletBalance($customerIdOrCode): float
    {
        try {
            return $this->circuitBreaker->execute(
                fn() => $this->retry->execute(
                    function () use ($customerIdOrCode): float {
                        $response = $this->http()
                            ->get("/customers/{$customerIdOrCode}/wallet");

                        if ($response->successful()) {
                            return (float) $response->json('data.balance');
                        }

                        throw new CustomerException($response->json('message') ?? 'Failed to get wallet balance');
                    },
                    fn(\Exception $e): bool =>
                        // Only retry on network errors or 5xx responses
                        !($e instanceof CustomerException) ||
                           str_contains($e->getMessage(), '5')
                ),
                function (): float {
                    // Fallback when circuit is open
                    Log::warning('Customer service circuit open, using fallback for wallet balance');

                    // Return a default wallet balance of 0
                    // In a real implementation, this might come from a cache
                    return 0.0;
                }
            );
        } catch (\Exception $e) {
            Log::error('Customer service get wallet balance failed', [
                'error' => $e->getMessage(),
                'customer_id_or_code' => $customerIdOrCode
            ]);

            throw new CustomerException('Failed to get wallet balance: ' . $e->getMessage());
        }
    }

    /**
     * Ping the customer service to check if it's available.
     */
    public function ping(): bool
    {
        try {
            $response = $this->http()
                ->get('/health');

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Customer service ping failed', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create an HTTP client instance.
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name'),
                'X-Service-Version' => config('app.version', '1.0.0'),
                'X-Correlation-ID' => \App\Services\Logging\LoggingService::getCorrelationId(),
            ]);
    }
}

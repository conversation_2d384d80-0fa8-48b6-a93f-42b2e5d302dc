<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\CircuitBreaker;

interface CircuitBreakerInterface
{
    /**
     * Execute a function with circuit breaker protection.
     *
     * @param callable $fn The function to execute
     * @param callable|null $fallback Optional fallback function to execute when the circuit is open
     * @return mixed The result of the function or fallback
     * @throws \Exception If the circuit is open and no fallback is provided
     */
    public function execute(callable $fn, ?callable $fallback = null);

    /**
     * Get the current state of the circuit breaker.
     *
     * @return string The current state (open, closed, half-open)
     */
    public function getState(): string;

    /**
     * Reset the circuit breaker to its initial state.
     */
    public function reset(): void;

    /**
     * Get the failure count.
     *
     * @return int The number of consecutive failures
     */
    public function getFailureCount(): int;

    /**
     * Get the success count.
     *
     * @return int The number of consecutive successes
     */
    public function getSuccessCount(): int;

    /**
     * Get the last failure time.
     *
     * @return int|null The timestamp of the last failure, or null if no failures have occurred
     */
    public function getLastFailureTime(): ?int;

    /**
     * Get the service name.
     *
     * @return string The name of the service
     */
    public function getServiceName(): string;
}

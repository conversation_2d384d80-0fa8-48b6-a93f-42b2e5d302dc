<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\CircuitBreaker;

use App\Exceptions\CircuitBreakerOpenException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CircuitBreaker implements CircuitBreakerInterface
{
    /**
     * Circuit breaker states.
     */
    const STATE_CLOSED = 'closed';
    const STATE_OPEN = 'open';
    const STATE_HALF_OPEN = 'half-open';

    /**
     * CircuitBreaker constructor.
     */
    public function __construct(protected string $serviceName, protected int $failureThreshold = 5, protected int $successThreshold = 2, protected int $timeoutSeconds = 30, protected string $cachePrefix = 'circuit_breaker:')
    {
    }

    /**
     * Execute a function with circuit breaker protection.
     *
     * @param callable $fn The function to execute
     * @param callable|null $fallback Optional fallback function to execute when the circuit is open
     * @return mixed The result of the function or fallback
     * @throws \Exception If the circuit is open and no fallback is provided
     */
    public function execute(callable $fn, ?callable $fallback = null)
    {
        $state = $this->getState();

        // If the circuit is open, check if the timeout has elapsed
        if ($state === self::STATE_OPEN) {
            $lastFailureTime = $this->getLastFailureTime();
            $elapsedTime = time() - $lastFailureTime;

            // If the timeout has elapsed, transition to half-open state
            if ($elapsedTime >= $this->timeoutSeconds) {
                $this->transitionToHalfOpen();
                $state = self::STATE_HALF_OPEN;
            } else {
                // Circuit is still open, use fallback or throw exception
                if ($fallback !== null) {
                    return $fallback();
                }

                throw new CircuitBreakerOpenException(
                    "Circuit breaker for {$this->serviceName} is open",
                    $this->timeoutSeconds - $elapsedTime
                );
            }
        }

        try {
            // Execute the function
            $result = $fn();

            // If the circuit is half-open and the call succeeded, increment success count
            if ($state === self::STATE_HALF_OPEN) {
                $this->incrementSuccessCount();

                // If success count reaches threshold, close the circuit
                if ($this->getSuccessCount() >= $this->successThreshold) {
                    $this->reset();
                }
            } else {
                // Reset failure count on success in closed state
                $this->resetFailureCount();
            }

            return $result;
        } catch (\Exception $e) {
            // Increment failure count
            $this->incrementFailureCount();
            $this->setLastFailureTime(time());

            // If failure count reaches threshold, open the circuit
            if ($this->getFailureCount() >= $this->failureThreshold) {
                $this->transitionToOpen();
            }

            // Log the exception
            Log::error("Circuit breaker for {$this->serviceName} caught exception", [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'state' => $this->getState(),
                'failure_count' => $this->getFailureCount(),
            ]);

            // Use fallback or rethrow the exception
            if ($fallback !== null) {
                return $fallback();
            }

            throw $e;
        }
    }

    /**
     * Get the current state of the circuit breaker.
     *
     * @return string The current state (open, closed, half-open)
     */
    public function getState(): string
    {
        return Cache::get($this->getCacheKey('state'), self::STATE_CLOSED);
    }

    /**
     * Reset the circuit breaker to its initial state.
     */
    public function reset(): void
    {
        Cache::put($this->getCacheKey('state'), self::STATE_CLOSED);
        Cache::put($this->getCacheKey('failure_count'), 0);
        Cache::put($this->getCacheKey('success_count'), 0);
        Cache::put($this->getCacheKey('last_failure_time'), null);

        Log::info("Circuit breaker for {$this->serviceName} has been reset");
    }

    /**
     * Get the failure count.
     *
     * @return int The number of consecutive failures
     */
    public function getFailureCount(): int
    {
        return Cache::get($this->getCacheKey('failure_count'), 0);
    }

    /**
     * Get the success count.
     *
     * @return int The number of consecutive successes
     */
    public function getSuccessCount(): int
    {
        return Cache::get($this->getCacheKey('success_count'), 0);
    }

    /**
     * Get the last failure time.
     *
     * @return int|null The timestamp of the last failure, or null if no failures have occurred
     */
    public function getLastFailureTime(): ?int
    {
        return Cache::get($this->getCacheKey('last_failure_time'));
    }

    /**
     * Get the service name.
     *
     * @return string The name of the service
     */
    public function getServiceName(): string
    {
        return $this->serviceName;
    }

    /**
     * Increment the failure count.
     */
    protected function incrementFailureCount(): void
    {
        $failureCount = $this->getFailureCount();
        Cache::put($this->getCacheKey('failure_count'), $failureCount + 1);
    }

    /**
     * Reset the failure count.
     */
    protected function resetFailureCount(): void
    {
        Cache::put($this->getCacheKey('failure_count'), 0);
    }

    /**
     * Increment the success count.
     */
    protected function incrementSuccessCount(): void
    {
        $successCount = $this->getSuccessCount();
        Cache::put($this->getCacheKey('success_count'), $successCount + 1);
    }

    /**
     * Set the last failure time.
     */
    protected function setLastFailureTime(int $time): void
    {
        Cache::put($this->getCacheKey('last_failure_time'), $time);
    }

    /**
     * Transition to open state.
     */
    protected function transitionToOpen(): void
    {
        Cache::put($this->getCacheKey('state'), self::STATE_OPEN);
        Cache::put($this->getCacheKey('success_count'), 0);

        Log::warning("Circuit breaker for {$this->serviceName} has transitioned to OPEN state", [
            'failure_count' => $this->getFailureCount(),
            'failure_threshold' => $this->failureThreshold,
            'timeout_seconds' => $this->timeoutSeconds,
        ]);
    }

    /**
     * Transition to half-open state.
     */
    protected function transitionToHalfOpen(): void
    {
        Cache::put($this->getCacheKey('state'), self::STATE_HALF_OPEN);
        Cache::put($this->getCacheKey('success_count'), 0);

        Log::info("Circuit breaker for {$this->serviceName} has transitioned to HALF-OPEN state", [
            'success_threshold' => $this->successThreshold,
        ]);
    }

    /**
     * Get the cache key for a specific property.
     */
    protected function getCacheKey(string $property): string
    {
        return $this->cachePrefix . $this->serviceName . ':' . $property;
    }
}

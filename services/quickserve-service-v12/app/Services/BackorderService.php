<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use App\Models\Backorder;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Support\Collection;

class BackorderService
{
    /**
     * Get all backorders.
     */
    public function getAllBackorders(array $filters = []): Collection
    {
        $query = Backorder::query();

        // Apply filters
        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['order_id'])) {
            $query->where('order_id', $filters['order_id']);
        }

        if (isset($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['order_date'])) {
            $query->where('order_date', $filters['order_date']);
        }

        if (isset($filters['order_menu'])) {
            $query->where('order_menu', $filters['order_menu']);
        }

        // Apply date range filter
        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('order_date', [$filters['start_date'], $filters['end_date']]);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->get();
    }

    /**
     * Get a backorder by ID.
     */
    public function getBackorder(int $id): ?Backorder
    {
        return Backorder::find($id);
    }

    /**
     * Create a new backorder.
     */
    public function createBackorder(array $data): Backorder
    {
        return Backorder::create($data);
    }

    /**
     * Create a backorder from an order.
     */
    public function createBackorderFromOrder(Order $order, string $reason): Backorder
    {
        $product = Product::find($order->product_code);

        $data = [
            'company_id' => $order->company_id,
            'unit_id' => $order->unit_id,
            'order_id' => $order->pk_order_no,
            'order_no' => $order->order_no,
            'customer_id' => $order->customer_code,
            'product_id' => $order->product_code,
            'product_name' => $product ? $product->name : $order->product_name,
            'quantity' => $order->quantity,
            'amount' => $order->amount,
            'order_date' => $order->order_date,
            'order_menu' => $order->order_menu,
            'reason' => $reason,
            'status' => 'pending'
        ];

        return $this->createBackorder($data);
    }

    /**
     * Update a backorder.
     */
    public function updateBackorder(int $id, array $data): ?Backorder
    {
        $backorder = Backorder::find($id);
        
        if (!$backorder) {
            return null;
        }
        
        $backorder->update($data);
        
        return $backorder;
    }

    /**
     * Delete a backorder.
     */
    public function deleteBackorder(int $id): bool
    {
        $backorder = Backorder::find($id);
        
        if (!$backorder) {
            return false;
        }
        
        return $backorder->delete();
    }

    /**
     * Complete a backorder.
     */
    public function completeBackorder(int $id): ?Backorder
    {
        return $this->updateBackorder($id, ['status' => 'completed']);
    }

    /**
     * Cancel a backorder.
     */
    public function cancelBackorder(int $id): ?Backorder
    {
        return $this->updateBackorder($id, ['status' => 'cancelled']);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use App\Models\Customer;
use App\Repositories\CustomerRepository;
use App\Repositories\CustomerAddressRepository;
use App\Repositories\OrderRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomerService
{
    /**
     * Create a new service instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The customer repository instance.
         */
        protected \App\Repositories\CustomerRepository $customerRepository,
        /**
         * The customer address repository instance.
         */
        protected \App\Repositories\CustomerAddressRepository $customerAddressRepository,
        /**
         * The order repository instance.
         */
        protected \App\Repositories\OrderRepository $orderRepository
    )
    {
    }

    /**
     * Get all customers with optional filtering.
     */
    public function getAllCustomers(array $params = []): Collection|LengthAwarePaginator
    {
        return $this->customerRepository->getAllCustomers($params);
    }

    /**
     * Get customer by ID.
     */
    public function getCustomerById(int $id): ?Customer
    {
        return $this->customerRepository->findById($id);
    }

    /**
     * Create a new customer.
     */
    public function createCustomer(array $data): Customer
    {
        // Set default values if not provided
        if (!isset($data['status'])) {
            $data['status'] = 1;
        }

        if (!isset($data['registered_on'])) {
            $data['registered_on'] = now();
        }

        if (!isset($data['phone_verified'])) {
            $data['phone_verified'] = 0;
        }

        if (!isset($data['email_verified'])) {
            $data['email_verified'] = 0;
        }

        return $this->customerRepository->create($data);
    }

    /**
     * Update an existing customer.
     */
    public function updateCustomer(int $id, array $data): ?Customer
    {
        $customer = $this->customerRepository->findById($id);

        if (!$customer instanceof \App\Models\Customer) {
            return null;
        }

        if (!isset($data['modified_on'])) {
            $data['modified_on'] = now();
        }

        return $this->customerRepository->update($customer, $data);
    }

    /**
     * Delete a customer.
     */
    public function deleteCustomer(int $id): bool
    {
        return $this->customerRepository->delete($id);
    }

    /**
     * Get customer by phone.
     */
    public function getCustomerByPhone(string $phone): ?Customer
    {
        return $this->customerRepository->getCustomerByPhone($phone);
    }

    /**
     * Get customer by email.
     */
    public function getCustomerByEmail(string $email): ?Customer
    {
        return $this->customerRepository->getCustomerByEmail($email);
    }

    /**
     * Get customer addresses.
     */
    public function getCustomerAddresses(int $customerId): Collection
    {
        return $this->customerAddressRepository->getAddressesByCustomerId($customerId);
    }

    /**
     * Get customer orders.
     */
    public function getCustomerOrders(int $customerId, array $params = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->getOrdersByCustomer($customerId, $params);
    }

    /**
     * Add customer address.
     */
    public function addCustomerAddress(int $customerId, array $data): \App\Models\CustomerAddress
    {
        $data['fk_customer_code'] = $customerId;

        if (!isset($data['created_on'])) {
            $data['created_on'] = now();
        }

        return $this->customerAddressRepository->create($data);
    }

    /**
     * Update customer address.
     */
    public function updateCustomerAddress(int $addressId, array $data): ?\App\Models\CustomerAddress
    {
        $address = $this->customerAddressRepository->findById($addressId);

        if (!$address instanceof \App\Models\CustomerAddress) {
            return null;
        }

        if (!isset($data['modified_on'])) {
            $data['modified_on'] = now();
        }

        return $this->customerAddressRepository->update($address, $data);
    }

    /**
     * Delete customer address.
     */
    public function deleteCustomerAddress(int $addressId): bool
    {
        return $this->customerAddressRepository->delete($addressId);
    }

    /**
     * Send OTP to customer.
     */
    public function sendOtp(Customer $customer, string $method = 'sms'): bool
    {
        try {
            // Generate OTP
            $otp = $this->generateOtp($customer);
            // Send OTP based on method
            if ($method === 'sms') {
                // In a real implementation, we would use an SMS service
                // For now, we'll just log the OTP
                \Log::info("SMS OTP for customer {$customer->pk_customer_code}: {$otp}");
                return true;
            }

            // Send OTP based on method
            if ($method === 'email') {
                // In a real implementation, we would use Laravel's Mail facade
                // For now, we'll just log the OTP
                \Log::info("Email OTP for customer {$customer->pk_customer_code}: {$otp}");
                return true;
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('Failed to send OTP: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify customer OTP.
     */
    public function verifyOtp(Customer $customer, string $otp, string $method = 'sms'): bool
    {
        if ($customer->otp !== $otp) {
            return false;
        }

        // Mark verification based on method
        if ($method === 'sms') {
            $customer->phone_verified = true;
        } elseif ($method === 'email') {
            $customer->email_verified = true;
        }

        // Clear OTP after verification
        $customer->otp = null;
        $customer->save();

        return true;
    }

    /**
     * Generate a random OTP for the customer.
     */
    private function generateOtp(Customer $customer, int $length = 6): string
    {
        $otp = '';
        for ($i = 0; $i < $length; $i++) {
            $otp .= mt_rand(0, 9);
        }

        $customer->otp = $otp;
        $customer->save();

        return $otp;
    }

    /**
     * Search customers by name.
     */
    public function searchCustomers(string $query): Collection
    {
        return $this->customerRepository->searchByName($query);
    }

    /**
     * Search customers by email.
     */
    public function searchCustomersByEmail(string $query): Collection
    {
        return $this->customerRepository->searchByEmail($query);
    }

    /**
     * Search customers by phone.
     */
    public function searchCustomersByPhone(string $query): Collection
    {
        return $this->customerRepository->searchByPhone($query);
    }

    /**
     * Get customer order history.
     */
    public function getCustomerOrderHistory(int $customerId): Collection
    {
        return $this->orderRepository->getOrdersByCustomer($customerId);
    }

    /**
     * Get customer statistics.
     */
    public function getCustomerStatistics(int $customerId): array
    {
        $orders = $this->orderRepository->getOrdersByCustomer($customerId);

        $totalOrders = $orders->count();
        $completedOrders = $orders->where('order_status', 'completed')->count();
        $cancelledOrders = $orders->where('order_status', 'cancelled')->count();
        $totalSpent = $orders->where('order_status', 'completed')->sum('amount');

        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'cancelled_orders' => $cancelledOrders,
            'total_spent' => (float) $totalSpent,
        ];
    }

    /**
     * Get customers by location.
     */
    public function getCustomersByLocation(string $location): Collection
    {
        return $this->customerRepository->getCustomersByLocation($location);
    }

    /**
     * Get active customers.
     */
    public function getActiveCustomers(): Collection
    {
        return $this->customerRepository->getActiveCustomers();
    }

    /**
     * Update customer status.
     */
    public function updateCustomerStatus(int $id, string $status): ?Customer
    {
        $customer = $this->customerRepository->findById($id);

        if (!$customer instanceof \App\Models\Customer) {
            return null;
        }

        return $this->customerRepository->update($customer, ['status' => $status === 'active' ? 1 : 0]);
    }

    /**
     * Get customers with pagination.
     */
    public function getCustomers(int $page = 1, int $perPage = 10): Collection
    {
        return $this->customerRepository->getPaginated($page, $perPage);
    }

    /**
     * Get recent customers.
     */
    public function getRecentCustomers(int $limit = 10): Collection
    {
        return $this->customerRepository->getRecent($limit);
    }

    /**
     * Validate email format.
     */
    public function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate phone format.
     */
    public function validatePhone(string $phone): bool
    {
        // Remove all non-digit characters
        $cleanPhone = preg_replace('/\D/', '', $phone);

        // Check if it has at least 10 digits
        return strlen($cleanPhone) >= 10;
    }

    /**
     * Get customer by ID with exception handling.
     */
    public function getCustomer(int $id): Customer
    {
        $customer = $this->customerRepository->findById($id);

        if (!$customer instanceof \App\Models\Customer) {
            throw new \App\Exceptions\Customer\CustomerException('Customer not found');
        }

        return $customer;
    }

    /**
     * Export customer data.
     */
    public function exportCustomerData(): array
    {
        $customers = $this->customerRepository->getAllCustomers();

        return $customers->map(function ($customer) {
            return [
                'name' => $customer->customer_name,
                'email' => $customer->email_address,
                'phone' => $customer->phone,
                'city' => $customer->city,
                'status' => $customer->status ? 'active' : 'inactive',
                'registered_on' => $customer->registered_on?->format('Y-m-d H:i:s'),
            ];
        })->toArray();
    }

    /**
     * Merge duplicate customers.
     */
    public function mergeCustomers(int $primaryCustomerId, int $duplicateCustomerId): Customer
    {
        $primaryCustomer = $this->customerRepository->findById($primaryCustomerId);
        $duplicateCustomer = $this->customerRepository->findById($duplicateCustomerId);

        if (!$primaryCustomer instanceof \App\Models\Customer || !$duplicateCustomer instanceof \App\Models\Customer) {
            throw new \App\Exceptions\Customer\CustomerException('One or both customers not found');
        }

        // Move all orders from duplicate to primary customer
        $this->orderRepository->transferOrdersToCustomer($duplicateCustomerId, $primaryCustomerId);

        // Soft delete the duplicate customer
        $this->customerRepository->delete($duplicateCustomerId);

        return $primaryCustomer;
    }
}

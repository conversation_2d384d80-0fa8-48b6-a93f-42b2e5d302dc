<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use App\Models\Timeslot;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class TimeslotService
{
    /**
     * Get all timeslots.
     */
    public function getAllTimeslots(array $filters = []): Collection
    {
        $query = Timeslot::query();

        // Apply filters
        if (isset($filters['day'])) {
            $query->day($filters['day']);
        }

        if (isset($filters['menu_type'])) {
            $query->menuType($filters['menu_type']);
        }

        if (isset($filters['kitchen'])) {
            $query->kitchen($filters['kitchen']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        } else {
            $query->active();
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'starttime';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->get();
    }

    /**
     * Get available timeslots for a specific day, menu type, and kitchen.
     */
    public function getAvailableTimeslots(string $day, string $menuType, string $kitchen, ?string $orderDate = null): Collection
    {
        $query = Timeslot::active()
            ->day($day)
            ->menuType($menuType)
            ->kitchen($kitchen);

        $timeslots = $query->get();

        // If order date is today, filter out past timeslots
        if ($orderDate && Carbon::parse($orderDate)->isToday()) {
            $now = Carbon::now();
            
            return $timeslots->filter(function ($timeslot) use ($now): bool {
                $startTime = Carbon::parse($timeslot->starttime);
                return $now->lt($startTime);
            });
        }

        return $timeslots;
    }

    /**
     * Get a timeslot by ID.
     */
    public function getTimeslot(int $id): ?Timeslot
    {
        return Timeslot::find($id);
    }

    /**
     * Create a new timeslot.
     */
    public function createTimeslot(array $data): Timeslot
    {
        return Timeslot::create($data);
    }

    /**
     * Update a timeslot.
     */
    public function updateTimeslot(int $id, array $data): ?Timeslot
    {
        $timeslot = Timeslot::find($id);
        
        if (!$timeslot) {
            return null;
        }
        
        $timeslot->update($data);
        
        return $timeslot;
    }

    /**
     * Delete a timeslot.
     */
    public function deleteTimeslot(int $id): bool
    {
        $timeslot = Timeslot::find($id);
        
        if (!$timeslot) {
            return false;
        }
        
        return $timeslot->delete();
    }

    /**
     * Check if a timeslot is available.
     */
    public function isTimeslotAvailable(string $startTime, string $day, string $menuType, string $kitchen, ?int $excludeId = null): bool
    {
        $query = Timeslot::active()
            ->day($day)
            ->menuType($menuType)
            ->kitchen($kitchen)
            ->where('starttime', $startTime);
            
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return !$query->exists();
    }
}

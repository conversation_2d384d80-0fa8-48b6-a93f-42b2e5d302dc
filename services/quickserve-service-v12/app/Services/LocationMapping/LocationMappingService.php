<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\LocationMapping;

use App\Events\LocationMapping\LocationMappingCreated;
use App\Events\LocationMapping\LocationMappingDeleted;
use App\Events\LocationMapping\LocationMappingUpdated;
use App\Exceptions\LocationMappingException;
use App\Models\LocationMapping;
use App\Repositories\LocationMapping\LocationMappingRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class LocationMappingService implements LocationMappingServiceInterface
{
    /**
     * LocationMappingService constructor.
     */
    public function __construct(protected \App\Repositories\LocationMapping\LocationMappingRepositoryInterface $locationMappingRepository)
    {
    }

    /**
     * Get all location mappings.
     */
    public function getAllLocationMappings(array $filters = []): Collection
    {
        return $this->locationMappingRepository->all($filters);
    }

    /**
     * Get paginated location mappings.
     */
    public function getPaginatedLocationMappings(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->locationMappingRepository->paginate($perPage, $filters);
    }

    /**
     * Get location mapping by ID.
     */
    public function getLocationMappingById(int $id): ?LocationMapping
    {
        return $this->locationMappingRepository->findById($id);
    }

    /**
     * Create a new location mapping.
     *
     * @throws LocationMappingException
     */
    public function createLocationMapping(array $data): LocationMapping
    {
        try {
            // Check if mapping already exists
            $existingMappings = $this->locationMappingRepository->getByLocationCode($data['location_code']);

            foreach ($existingMappings as $mapping) {
                if ($mapping->city_code === $data['city_code'] && $mapping->kitchen_code === $data['kitchen_code']) {
                    throw new LocationMappingException('Location mapping already exists for this location, city, and kitchen.');
                }
            }

            // Set default values if not provided
            if (!isset($data['status'])) {
                $data['status'] = true;
            }

            // Create location mapping
            $locationMapping = $this->locationMappingRepository->create($data);

            // Dispatch event
            Event::dispatch(new LocationMappingCreated($locationMapping));

            return $locationMapping;
        } catch (LocationMappingException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to create location mapping', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new LocationMappingException('Failed to create location mapping: ' . $e->getMessage());
        }
    }

    /**
     * Update a location mapping.
     *
     * @throws LocationMappingException
     */
    public function updateLocationMapping(int $id, array $data): bool
    {
        try {
            $locationMapping = $this->getLocationMappingById($id);

            if (!$locationMapping instanceof \App\Models\LocationMapping) {
                throw new LocationMappingException('Location mapping not found.');
            }

            // Check if mapping already exists if location_code, city_code, or kitchen_code is being updated
            if (isset($data['location_code']) || isset($data['city_code']) || isset($data['kitchen_code'])) {
                $locationCode = $data['location_code'] ?? $locationMapping->location_code;
                $cityCode = $data['city_code'] ?? $locationMapping->city_code;
                $kitchenCode = $data['kitchen_code'] ?? $locationMapping->kitchen_code;

                $existingMappings = $this->locationMappingRepository->getByLocationCode($locationCode);

                foreach ($existingMappings as $mapping) {
                    if ($mapping->id !== $id && $mapping->city_code === $cityCode && $mapping->kitchen_code === $kitchenCode) {
                        throw new LocationMappingException('Location mapping already exists for this location, city, and kitchen.');
                    }
                }
            }

            // Update location mapping
            $updated = $this->locationMappingRepository->update($id, $data);

            if ($updated) {
                // Refresh the location mapping data
                $locationMapping = $this->getLocationMappingById($id);

                // Dispatch event
                Event::dispatch(new LocationMappingUpdated($locationMapping));
            }

            return $updated;
        } catch (LocationMappingException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to update location mapping', [
                'error' => $e->getMessage(),
                'location_mapping_id' => $id,
                'data' => $data
            ]);

            throw new LocationMappingException('Failed to update location mapping: ' . $e->getMessage());
        }
    }

    /**
     * Delete a location mapping.
     *
     * @throws LocationMappingException
     */
    public function deleteLocationMapping(int $id): bool
    {
        try {
            $locationMapping = $this->getLocationMappingById($id);

            if (!$locationMapping instanceof \App\Models\LocationMapping) {
                throw new LocationMappingException('Location mapping not found.');
            }

            // Delete location mapping
            $deleted = $this->locationMappingRepository->delete($id);

            if ($deleted) {
                // Dispatch event
                Event::dispatch(new LocationMappingDeleted($locationMapping));
            }

            return $deleted;
        } catch (LocationMappingException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to delete location mapping', [
                'error' => $e->getMessage(),
                'location_mapping_id' => $id
            ]);

            throw new LocationMappingException('Failed to delete location mapping: ' . $e->getMessage());
        }
    }

    /**
     * Get location mappings by kitchen ID.
     */
    public function getLocationMappingsByKitchenId(int $kitchenId, array $filters = []): Collection
    {
        return $this->locationMappingRepository->getByKitchenId($kitchenId, $filters);
    }

    /**
     * Get location mappings by location code.
     */
    public function getLocationMappingsByLocationCode(string $locationCode, array $filters = []): Collection
    {
        return $this->locationMappingRepository->getByLocationCode($locationCode, $filters);
    }

    /**
     * Get location mappings by city.
     */
    public function getLocationMappingsByCity(string $city, array $filters = []): Collection
    {
        return $this->locationMappingRepository->getByCity($city, $filters);
    }

    /**
     * Get active location mappings.
     */
    public function getActiveLocationMappings(array $filters = []): Collection
    {
        return $this->locationMappingRepository->getActive($filters);
    }

    /**
     * Find kitchen for a location.
     */
    public function findKitchenForLocation(string $locationCode, string $city): ?LocationMapping
    {
        return $this->locationMappingRepository->findKitchenForLocation($locationCode, $city);
    }

    /**
     * Find locations for a kitchen.
     */
    public function findLocationsForKitchen(int $kitchenId, string $city): Collection
    {
        return $this->locationMappingRepository->findLocationsForKitchen($kitchenId, $city);
    }

    /**
     * Check if a location is serviced by a kitchen.
     */
    public function isLocationServicedByKitchen(string $locationCode, int $kitchenId, string $city): bool
    {
        $mapping = $this->locationMappingRepository->findKitchenForLocation($locationCode, $city);

        if (!$mapping instanceof \App\Models\LocationMapping) {
            return false;
        }

        return $mapping->kitchen_id === $kitchenId;
    }

    /**
     * Get all cities with active location mappings.
     */
    public function getAllCities(): Collection
    {
        return DB::table('location_mappings')
            ->select('city')
            ->where('status', true)
            ->distinct()
            ->orderBy('city')
            ->get()
            ->pluck('city');
    }

    /**
     * Get all kitchens for a city.
     */
    public function getKitchensForCity(string $city): Collection
    {
        return DB::table('location_mappings')
            ->select('kitchen_id')
            ->where('city', $city)
            ->where('status', true)
            ->distinct()
            ->orderBy('kitchen_id')
            ->get()
            ->pluck('kitchen_id');
    }
}

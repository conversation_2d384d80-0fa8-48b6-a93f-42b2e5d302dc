<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\LocationMapping;

use App\Models\LocationMapping;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface LocationMappingServiceInterface
{
    /**
     * Get all location mappings.
     */
    public function getAllLocationMappings(array $filters = []): Collection;

    /**
     * Get paginated location mappings.
     */
    public function getPaginatedLocationMappings(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get location mapping by ID.
     */
    public function getLocationMappingById(int $id): ?LocationMapping;

    /**
     * Create a new location mapping.
     */
    public function createLocationMapping(array $data): LocationMapping;

    /**
     * Update a location mapping.
     */
    public function updateLocationMapping(int $id, array $data): bool;

    /**
     * Delete a location mapping.
     */
    public function deleteLocationMapping(int $id): bool;

    /**
     * Get location mappings by kitchen ID.
     */
    public function getLocationMappingsByKitchenId(int $kitchenId, array $filters = []): Collection;

    /**
     * Get location mappings by location code.
     */
    public function getLocationMappingsByLocationCode(string $locationCode, array $filters = []): Collection;

    /**
     * Get location mappings by city.
     */
    public function getLocationMappingsByCity(string $city, array $filters = []): Collection;

    /**
     * Get active location mappings.
     */
    public function getActiveLocationMappings(array $filters = []): Collection;

    /**
     * Find kitchen for a location.
     */
    public function findKitchenForLocation(string $locationCode, string $city): ?LocationMapping;

    /**
     * Find locations for a kitchen.
     */
    public function findLocationsForKitchen(int $kitchenId, string $city): Collection;

    /**
     * Check if a location is serviced by a kitchen.
     */
    public function isLocationServicedByKitchen(string $locationCode, int $kitchenId, string $city): bool;

    /**
     * Get all cities with active location mappings.
     */
    public function getAllCities(): Collection;

    /**
     * Get all kitchens for a city.
     */
    public function getKitchensForCity(string $city): Collection;
}

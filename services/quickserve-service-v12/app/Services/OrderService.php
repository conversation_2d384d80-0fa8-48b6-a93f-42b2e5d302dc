<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use App\DTOs\Customer\CustomerDTO;
use App\DTOs\Payment\PaymentRequestDTO;
use App\DTOs\Payment\PaymentResponseDTO;
use App\Events\OrderCancelled;
use App\Events\OrderCreated;
use App\Events\OrderStatusUpdated;
use App\Exceptions\Customer\CustomerException;
use App\Exceptions\Order\OrderException;
use App\Exceptions\Payment\PaymentException;
use App\Models\Order;
use App\Models\OrderConfirm;
use App\Models\OrderDetail;
use App\Repositories\OrderRepository;
use App\Services\Customer\CustomerServiceClient;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class OrderService
{
    /**
     * Create a new service instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The order repository instance.
         */
        protected \App\Repositories\OrderRepository $orderRepository,
        /**
         * The customer service client instance.
         */
        protected \App\Services\Customer\CustomerServiceClient $customerService,
        /**
         * The payment service client instance.
         */
        protected \App\Services\Payment\PaymentServiceClient $paymentService
    )
    {
    }

    /**
     * Get all orders with optional filtering.
     */
    public function getAllOrders(array $params = []): Collection|SupportCollection|LengthAwarePaginator
    {
        return $this->orderRepository->getAllOrders($params);
    }

    /**
     * Get order by ID.
     */
    public function getOrderById(int $id): ?Order
    {
        return $this->orderRepository->findById($id);
    }

    /**
     * Create a new order.
     *
     * @throws OrderException
     * @throws CustomerException
     */
    public function createOrder(array $data): Order
    {
        try {
            // Validate customer exists
            $customer = $this->customerService->getCustomer($data['customer_code']);
            if (!$customer) {
                throw new CustomerException('Customer not found');
            }

            // Start transaction
            DB::beginTransaction();

            // Calculate tax, delivery charges, etc. if needed
            if (!isset($data['order_no'])) {
                $data['order_no'] = $this->generateOrderNumber();
            }

            if (!isset($data['order_status'])) {
                $data['order_status'] = 'New';
            }

            if (!isset($data['delivery_status'])) {
                $data['delivery_status'] = 'Pending';
            }

            if (!isset($data['invoice_status'])) {
                $data['invoice_status'] = 'Unbill';
            }

            if (!isset($data['created_date'])) {
                $data['created_date'] = now();
            }

            // Create the order
            $order = $this->orderRepository->create($data);

            // Create order details if provided
            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $item) {
                    OrderDetail::create([
                        'ref_order_no' => $order->order_no,
                        'product_code' => $item['product_code'],
                        'product_name' => $item['product_name'],
                        'quantity' => $item['quantity'],
                        'amount' => $item['amount'],
                        'tax' => $item['tax'] ?? 0,
                        'company_id' => $order->company_id,
                        'unit_id' => $order->unit_id,
                    ]);
                }
            }

            // Commit transaction
            DB::commit();

            // Dispatch event
            Event::dispatch(new OrderCreated($order));

            return $order;
        } catch (CustomerException $e) {
            // Rollback transaction
            DB::rollBack();

            Log::error('Failed to create order', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            Log::error('Failed to create order', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new OrderException('Failed to create order: ' . $e->getMessage());
        }
    }

    /**
     * Update an existing order.
     *
     * @throws OrderException
     */
    public function updateOrder(int $id, array $data): Order
    {
        try {
            $order = $this->orderRepository->findById($id);

            if (!$order instanceof \App\Models\Order) {
                throw new OrderException('Order not found');
            }

            // Start transaction
            DB::beginTransaction();

            $previousStatus = $order->order_status;

            // Update order
            $updatedOrder = $this->orderRepository->update($order, $data);

            // Update order details if provided
            if (isset($data['items']) && is_array($data['items'])) {
                // Delete existing order details
                OrderDetail::where('ref_order_no', $order->order_no)->delete();

                // Create new order details
                foreach ($data['items'] as $item) {
                    OrderDetail::create([
                        'ref_order_no' => $order->order_no,
                        'product_code' => $item['product_code'],
                        'product_name' => $item['product_name'],
                        'quantity' => $item['quantity'],
                        'amount' => $item['amount'],
                        'tax' => $item['tax'] ?? 0,
                        'company_id' => $order->company_id,
                        'unit_id' => $order->unit_id,
                    ]);
                }
            }

            // Commit transaction
            DB::commit();

            // Dispatch event if status changed
            if ($previousStatus !== $updatedOrder->order_status) {
                Event::dispatch(new OrderStatusUpdated($updatedOrder, $previousStatus));
            }

            return $updatedOrder;
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            Log::error('Failed to update order', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'data' => $data
            ]);

            throw new OrderException('Failed to update order: ' . $e->getMessage());
        }
    }

    /**
     * Delete an order.
     *
     * @throws OrderException
     */
    public function deleteOrder(int $id): bool
    {
        try {
            $order = $this->orderRepository->findById($id);

            if (!$order instanceof \App\Models\Order) {
                throw new OrderException('Order not found');
            }

            // Check if order can be deleted
            if (!in_array($order->order_status, ['New', 'Cancelled'])) {
                throw new OrderException('Order cannot be deleted');
            }

            return $this->orderRepository->delete($id);
        } catch (\Exception $e) {
            Log::error('Failed to delete order', [
                'error' => $e->getMessage(),
                'order_id' => $id
            ]);

            throw new OrderException('Failed to delete order: ' . $e->getMessage());
        }
    }

    /**
     * Cancel an order.
     *
     * @throws OrderException
     */
    public function cancelOrder(int $id, ?string $reason = null): Order
    {
        try {
            $order = $this->orderRepository->findById($id);

            if (!$order instanceof \App\Models\Order) {
                throw new OrderException('Order not found');
            }

            // Update order status
            $data = [
                'order_status' => 'Cancelled',
                'cancellation_reason' => $reason,
                'cancelled_at' => now(),
            ];

            $cancelledOrder = $this->orderRepository->update($order, $data);

            // Dispatch event
            Event::dispatch(new OrderCancelled($cancelledOrder));

            return $cancelledOrder;
        } catch (\Exception $e) {
            Log::error('Failed to cancel order', [
                'error' => $e->getMessage(),
                'order_id' => $id
            ]);

            throw new OrderException('Failed to cancel order: ' . $e->getMessage());
        }
    }

    /**
     * Get orders by customer ID.
     */
    public function getOrdersByCustomer(int $customerId, array $params = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->getOrdersByCustomer($customerId, $params);
    }

    /**
     * Update order status.
     */
    public function updateOrderStatus(int $id, string $status): ?Order
    {
        $order = $this->orderRepository->findById($id);

        if (!$order instanceof \App\Models\Order) {
            return null;
        }

        return $this->orderRepository->update($order, ['order_status' => $status]);
    }

    /**
     * Update delivery status.
     */
    public function updateDeliveryStatus(int $id, string $status): ?Order
    {
        $order = $this->orderRepository->findById($id);

        if (!$order instanceof \App\Models\Order) {
            return null;
        }

        return $this->orderRepository->update($order, ['delivery_status' => $status]);
    }

    /**
     * Process payment for an order.
     *
     * @throws OrderException
     * @throws PaymentException
     */
    public function processPayment(int $id, string $gateway, float $walletAmount = 0): PaymentResponseDTO
    {
        try {
            $order = $this->orderRepository->findById($id);

            if (!$order instanceof \App\Models\Order) {
                throw new OrderException('Order not found');
            }

            // Get customer
            $customer = $this->customerService->getCustomer($order->customer_code);
            if (!$customer) {
                throw new CustomerException('Customer not found');
            }

            // Calculate total amount
            $totalAmount = $order->amount + $order->tax + $order->delivery_charges + $order->service_charges - $order->applied_discount;

            // Check wallet balance if using wallet
            if ($walletAmount > 0) {
                $walletBalance = $this->customerService->getWalletBalance($order->customer_code);
                if ($walletBalance < $walletAmount) {
                    throw new PaymentException('Insufficient wallet balance');
                }
            }

            // Calculate amount to be paid through gateway
            $paymentAmount = $totalAmount - $walletAmount;
            if ($paymentAmount <= 0) {
                // Full payment from wallet
                $this->customerService->deductFromWallet($order->customer_code, $walletAmount);

                // Update order
                $this->orderRepository->update($order, [
                    'payment_mode' => 'wallet',
                    'amount_paid' => $totalAmount,
                    'order_status' => 'Paid',
                ]);

                return new PaymentResponseDTO(
                    'wallet_' . uniqid(),
                    $totalAmount,
                    'completed'
                );
            }

            // Create payment request
            $paymentRequest = new PaymentRequestDTO(
                $order->customer_code,
                $customer['email_address'] ?? '',
                $customer['phone'],
                $customer['customer_name'],
                $paymentAmount,
                $order->order_no,
                $walletAmount,
                route('payment.success', ['order_id' => $order->id]),
                route('payment.failure', ['order_id' => $order->id]),
                'quickserve',
                'order',
                false,
                $order->applied_discount
            );

            // Process payment
            $paymentResponse = $this->paymentService->initiatePayment($paymentRequest);

            // Update order with transaction ID
            $this->orderRepository->update($order, [
                'transaction_id' => $paymentResponse->transactionId,
                'payment_mode' => $gateway,
            ]);

            return $paymentResponse;
        } catch (\Exception $e) {
            Log::error('Failed to process payment', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'gateway' => $gateway
            ]);

            throw new OrderException('Failed to process payment: ' . $e->getMessage());
        }
    }

    /**
     * Generate a unique order number.
     */
    protected function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $timestamp = now()->format('YmdHis');
        $random = random_int(1000, 9999);

        return $prefix . $timestamp . $random;
    }
}

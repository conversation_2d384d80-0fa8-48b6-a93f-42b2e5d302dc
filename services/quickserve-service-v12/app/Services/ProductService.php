<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use App\Models\Product;
use App\Repositories\ProductRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductService
{
    /**
     * Create a new service instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The product repository instance.
         */
        protected \App\Repositories\ProductRepository $productRepository
    )
    {
    }

    /**
     * Get all products with optional filtering.
     */
    public function getAllProducts(array $params = []): Collection|LengthAwarePaginator
    {
        return $this->productRepository->getAllProducts($params);
    }

    /**
     * Get paginated products.
     */
    public function getPaginatedProducts(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->productRepository->getPaginatedProducts($perPage, $filters);
    }

    /**
     * Get product by ID.
     */
    public function getProductById(int $id): ?Product
    {
        return $this->productRepository->findById($id);
    }

    /**
     * Create a new product.
     */
    public function createProduct(array $data): Product
    {
        // Set default values if not provided
        if (!isset($data['status'])) {
            $data['status'] = 1;
        }

        if (!isset($data['max_quantity_per_meal'])) {
            $data['max_quantity_per_meal'] = 1;
        }

        return $this->productRepository->create($data);
    }

    /**
     * Update an existing product.
     */
    public function updateProduct(int $id, array $data): ?Product
    {
        $product = $this->productRepository->findById($id);

        if (!$product instanceof \App\Models\Product) {
            return null;
        }

        return $this->productRepository->update($product, $data);
    }

    /**
     * Delete a product.
     */
    public function deleteProduct(int $id): bool
    {
        return $this->productRepository->delete($id);
    }

    /**
     * Get products by type.
     */
    public function getProductsByType(string $type): Collection
    {
        return $this->productRepository->getProductsByType($type);
    }

    /**
     * Get products by food type.
     */
    public function getProductsByFoodType(string $foodType): Collection
    {
        return $this->productRepository->getProductsByFoodType($foodType);
    }

    /**
     * Get products by kitchen.
     */
    public function getProductsByKitchen(int $kitchenId): Collection
    {
        return $this->productRepository->getProductsByKitchen($kitchenId);
    }

    /**
     * Get products by category.
     */
    public function getProductsByCategory(string $category): Collection
    {
        return $this->productRepository->getProductsByCategory($category);
    }

    /**
     * Update product sequence.
     */
    public function updateProductSequence(array $productIds): bool
    {
        return $this->productRepository->updateProductSequence($productIds);
    }

    /**
     * Search products by name.
     */
    public function searchProducts(string $query): Collection
    {
        return $this->productRepository->searchByName($query);
    }

    /**
     * Get active products only.
     */
    public function getActiveProducts(): Collection
    {
        return $this->productRepository->getActiveProducts();
    }

    /**
     * Get featured products only.
     */
    public function getFeaturedProducts(): Collection
    {
        return $this->productRepository->getFeaturedProducts();
    }

    /**
     * Get products by price range.
     */
    public function getProductsByPriceRange(float $minPrice, float $maxPrice): Collection
    {
        return $this->productRepository->getProductsByPriceRange($minPrice, $maxPrice);
    }

    /**
     * Update product stock.
     */
    public function updateStock(int $productId, int $quantity): ?\App\Models\Product
    {
        $product = $this->productRepository->findById($productId);

        if (!$product instanceof \App\Models\Product) {
            return null;
        }

        return $this->productRepository->update($product, ['quantity' => $quantity]);
    }

    /**
     * Check product availability.
     */
    public function isAvailable(int $productId): bool
    {
        $product = $this->productRepository->findById($productId);

        if (!$product instanceof \App\Models\Product) {
            return false;
        }

        return $product->status && $product->quantity > 0;
    }

    /**
     * Get products with pagination.
     */
    public function getProducts(int $page = 1, int $perPage = 10): Collection
    {
        return $this->productRepository->getPaginated($page, $perPage);
    }

    /**
     * Get product by ID with caching.
     */
    public function getProduct(int $id): ?\App\Models\Product
    {
        return $this->productRepository->findById($id);
    }

    /**
     * Bulk update product prices.
     */
    public function bulkUpdatePrices(array $priceUpdates): bool
    {
        return $this->productRepository->bulkUpdatePrices($priceUpdates);
    }

    /**
     * Get product statistics.
     */
    public function getProductStatistics(int $productId): array
    {
        $product = $this->productRepository->findById($productId);

        if (!$product instanceof \App\Models\Product) {
            throw new \App\Exceptions\Product\ProductException('Product not found');
        }

        // In a real implementation, you would get actual statistics from orders, etc.
        return [
            'total_orders' => 0,
            'total_revenue' => 0.0,
            'average_rating' => 0.0,
            'stock_level' => $product->quantity,
            'status' => $product->status ? 'active' : 'inactive'
        ];
    }

    /**
     * Duplicate product.
     */
    public function duplicateProduct(int $productId): ?\App\Models\Product
    {
        $originalProduct = $this->productRepository->findById($productId);

        if (!$originalProduct instanceof \App\Models\Product) {
            throw new \App\Exceptions\Product\ProductException('Product not found');
        }

        $duplicateData = $originalProduct->toArray();
        unset($duplicateData['pk_product_code']); // Remove primary key
        $duplicateData['name'] = $duplicateData['name'] . ' (Copy)';

        return $this->productRepository->create($duplicateData);
    }
}

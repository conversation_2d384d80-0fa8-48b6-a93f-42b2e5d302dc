<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class ConfigService
{
    /**
     * Get a configuration value.
     *
     * @param mixed $default
     * @return mixed
     */
    public function getConfig(string $key, $default = null)
    {
        // Try to get from cache first
        $cacheKey = "config:{$key}";
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Try to get from database
        $setting = DB::table('settings')
            ->where('setting_key', $key)
            ->first();

        if ($setting) {
            // Cache the result for 60 minutes
            Cache::put($cacheKey, $setting->setting_value, 3600);
            return $setting->setting_value;
        }

        // Try to get from <PERSON>vel config
        if (Config::has($key)) {
            return Config::get($key);
        }

        // Check if the key exists with GLOBAL_ prefix
        $globalKey = 'GLOBAL_' . strtoupper($key);
        $globalSetting = DB::table('settings')
            ->where('setting_key', $globalKey)
            ->first();

        if ($globalSetting) {
            // Cache the result for 60 minutes
            Cache::put($cacheKey, $globalSetting->setting_value, 3600);
            return $globalSetting->setting_value;
        }

        // Return default value
        return $default;
    }

    /**
     * Set a configuration value.
     *
     * @param mixed $value
     */
    public function setConfig(string $key, $value): bool
    {
        // Update or insert into database
        $result = DB::table('settings')
            ->updateOrInsert(
                ['setting_key' => $key],
                [
                    'setting_value' => $value,
                    'updated_at' => now()
                ]
            );

        // Update cache
        $cacheKey = "config:{$key}";
        Cache::put($cacheKey, $value, 3600);

        return $result;
    }

    /**
     * Get all configuration values.
     */
    public function getAllConfig(): array
    {
        // Get all settings from database
        $settings = DB::table('settings')->get();
        
        $config = [];
        foreach ($settings as $setting) {
            $config[$setting->setting_key] = $setting->setting_value;
        }

        // Merge with Laravel config
        $config = array_merge(Config::all(), $config);

        return $config;
    }

    /**
     * Get all settings.
     */
    public function getAllSettings(): array
    {
        // Get all settings from database
        $settings = DB::table('settings')->get();
        
        $config = [];
        foreach ($settings as $setting) {
            $config[$setting->setting_key] = $setting->setting_value;
        }

        return $config;
    }

    /**
     * Check if a configuration value exists.
     */
    public function hasConfig(string $key): bool
    {
        // Check cache
        $cacheKey = "config:{$key}";
        if (Cache::has($cacheKey)) {
            return true;
        }

        // Check database
        $exists = DB::table('settings')
            ->where('setting_key', $key)
            ->exists();

        if ($exists) {
            return true;
        }

        // Check Laravel config
        if (Config::has($key)) {
            return true;
        }

        // Check with GLOBAL_ prefix
        $globalKey = 'GLOBAL_' . strtoupper($key);
        return DB::table('settings')
            ->where('setting_key', $globalKey)
            ->exists();
    }
}

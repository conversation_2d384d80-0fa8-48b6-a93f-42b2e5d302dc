<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\RabbitMQ\Publishers;

use App\Models\Backorder;
use App\Services\RabbitMQ\RabbitMQService;
use Illuminate\Support\Facades\Log;

class BackorderEventPublisher
{
    /**
     * BackorderEventPublisher constructor.
     */
    public function __construct(protected \App\Services\RabbitMQ\RabbitMQService $rabbitMQService)
    {
    }

    /**
     * Publish backorder created event.
     */
    public function publishBackorderCreated(Backorder $backorder): bool
    {
        try {
            $data = [
                'event' => 'backorder.created',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatBackorderData($backorder),
            ];

            return $this->rabbitMQService->publish('backorder.created', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish backorder.created event', [
                'backorder_id' => $backorder->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish backorder updated event.
     */
    public function publishBackorderUpdated(Backorder $backorder): bool
    {
        try {
            $data = [
                'event' => 'backorder.updated',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatBackorderData($backorder),
            ];

            return $this->rabbitMQService->publish('backorder.updated', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish backorder.updated event', [
                'backorder_id' => $backorder->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish backorder deleted event.
     */
    public function publishBackorderDeleted(Backorder $backorder): bool
    {
        try {
            $data = [
                'event' => 'backorder.deleted',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatBackorderData($backorder),
            ];

            return $this->rabbitMQService->publish('backorder.deleted', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish backorder.deleted event', [
                'backorder_id' => $backorder->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish backorder completed event.
     */
    public function publishBackorderCompleted(Backorder $backorder): bool
    {
        try {
            $data = [
                'event' => 'backorder.completed',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatBackorderData($backorder),
            ];

            return $this->rabbitMQService->publish('backorder.completed', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish backorder.completed event', [
                'backorder_id' => $backorder->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish backorder cancelled event.
     */
    public function publishBackorderCancelled(Backorder $backorder): bool
    {
        try {
            $data = [
                'event' => 'backorder.cancelled',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatBackorderData($backorder),
            ];

            return $this->rabbitMQService->publish('backorder.cancelled', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish backorder.cancelled event', [
                'backorder_id' => $backorder->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Format backorder data for event publishing.
     */
    protected function formatBackorderData(Backorder $backorder): array
    {
        return [
            'id' => $backorder->id,
            'order_id' => $backorder->order_id,
            'order_no' => $backorder->order_no,
            'customer_id' => $backorder->customer_id,
            'product_id' => $backorder->product_id,
            'product_name' => $backorder->product_name,
            'quantity' => $backorder->quantity,
            'amount' => $backorder->amount,
            'order_date' => $backorder->order_date,
            'order_menu' => $backorder->order_menu,
            'reason' => $backorder->reason,
            'status' => $backorder->status,
            'company_id' => $backorder->company_id,
            'unit_id' => $backorder->unit_id,
            'created_at' => $backorder->created_at ? $backorder->created_at->toIso8601String() : null,
            'updated_at' => $backorder->updated_at ? $backorder->updated_at->toIso8601String() : null,
        ];
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\RabbitMQ\Publishers;

use App\Models\Order;
use App\Services\RabbitMQ\RabbitMQServiceInterface;
use Illuminate\Support\Facades\Log;

class OrderEventPublisher
{
    /**
     * OrderEventPublisher constructor.
     */
    public function __construct(protected \App\Services\RabbitMQ\RabbitMQServiceInterface $rabbitMQService)
    {
    }

    /**
     * Publish order created event.
     */
    public function publishOrderCreated(Order $order): bool
    {
        try {
            $data = [
                'event' => 'order.created',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatOrderData($order),
            ];

            return $this->rabbitMQService->publish('order.created', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish order.created event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish order updated event.
     */
    public function publishOrderUpdated(Order $order): bool
    {
        try {
            $data = [
                'event' => 'order.updated',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatOrderData($order),
            ];

            return $this->rabbitMQService->publish('order.updated', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish order.updated event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish order status updated event.
     */
    public function publishOrderStatusUpdated(Order $order, string $oldStatus, string $newStatus): bool
    {
        try {
            $data = [
                'event' => 'order.status.updated',
                'timestamp' => now()->toIso8601String(),
                'data' => array_merge($this->formatOrderData($order), [
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                ]),
            ];

            return $this->rabbitMQService->publish('order.status.updated', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish order.status.updated event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish order cancelled event.
     */
    public function publishOrderCancelled(Order $order, ?string $reason = null): bool
    {
        try {
            $data = [
                'event' => 'order.cancelled',
                'timestamp' => now()->toIso8601String(),
                'data' => array_merge($this->formatOrderData($order), [
                    'reason' => $reason,
                ]),
            ];

            return $this->rabbitMQService->publish('order.cancelled', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish order.cancelled event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish order payment processed event.
     */
    public function publishOrderPaymentProcessed(Order $order, array $paymentData): bool
    {
        try {
            $data = [
                'event' => 'order.payment.processed',
                'timestamp' => now()->toIso8601String(),
                'data' => array_merge($this->formatOrderData($order), [
                    'payment' => $paymentData,
                ]),
            ];

            return $this->rabbitMQService->publish('order.payment.processed', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish order.payment.processed event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Format order data for event publishing.
     */
    protected function formatOrderData(Order $order): array
    {
        return [
            'id' => $order->id,
            'order_no' => $order->order_no,
            'customer_id' => $order->customer_id,
            'product_id' => $order->product_id,
            'product_name' => $order->product_name,
            'quantity' => $order->quantity,
            'amount' => $order->amount,
            'order_date' => $order->order_date,
            'order_menu' => $order->order_menu,
            'order_status' => $order->order_status,
            'delivery_status' => $order->delivery_status,
            'payment_status' => $order->payment_status,
            'company_id' => $order->company_id,
            'unit_id' => $order->unit_id,
            'created_at' => $order->created_at?->toIso8601String(),
            'updated_at' => $order->updated_at?->toIso8601String(),
        ];
    }
}

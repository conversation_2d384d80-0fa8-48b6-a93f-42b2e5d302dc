<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\RabbitMQ\Publishers;

use App\Models\LocationMapping;
use App\Services\RabbitMQ\RabbitMQService;
use Illuminate\Support\Facades\Log;

class LocationMappingEventPublisher
{
    /**
     * LocationMappingEventPublisher constructor.
     */
    public function __construct(protected \App\Services\RabbitMQ\RabbitMQService $rabbitMQService)
    {
    }

    /**
     * Publish location mapping created event.
     */
    public function publishLocationMappingCreated(LocationMapping $locationMapping): bool
    {
        try {
            $data = [
                'event' => 'location_mapping.created',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatLocationMappingData($locationMapping),
            ];

            return $this->rabbitMQService->publish('location_mapping.created', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish location_mapping.created event', [
                'location_mapping_id' => $locationMapping->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish location mapping updated event.
     */
    public function publishLocationMappingUpdated(LocationMapping $locationMapping): bool
    {
        try {
            $data = [
                'event' => 'location_mapping.updated',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatLocationMappingData($locationMapping),
            ];

            return $this->rabbitMQService->publish('location_mapping.updated', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish location_mapping.updated event', [
                'location_mapping_id' => $locationMapping->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish location mapping deleted event.
     */
    public function publishLocationMappingDeleted(LocationMapping $locationMapping): bool
    {
        try {
            $data = [
                'event' => 'location_mapping.deleted',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatLocationMappingData($locationMapping),
            ];

            return $this->rabbitMQService->publish('location_mapping.deleted', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish location_mapping.deleted event', [
                'location_mapping_id' => $locationMapping->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Format location mapping data for event publishing.
     */
    protected function formatLocationMappingData(LocationMapping $locationMapping): array
    {
        return [
            'id' => $locationMapping->id,
            'location_code' => $locationMapping->location_code,
            'location_name' => $locationMapping->location_name,
            'city' => $locationMapping->city,
            'kitchen_id' => $locationMapping->kitchen_id,
            'kitchen_name' => $locationMapping->kitchen_name,
            'status' => $locationMapping->status,
            'company_id' => $locationMapping->company_id,
            'unit_id' => $locationMapping->unit_id,
            'created_at' => $locationMapping->created_at ? $locationMapping->created_at->toIso8601String() : null,
            'updated_at' => $locationMapping->updated_at ? $locationMapping->updated_at->toIso8601String() : null,
        ];
    }
}

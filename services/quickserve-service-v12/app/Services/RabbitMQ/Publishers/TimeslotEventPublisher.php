<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\RabbitMQ\Publishers;

use App\Models\Timeslot;
use App\Services\RabbitMQ\RabbitMQService;
use Illuminate\Support\Facades\Log;

class TimeslotEventPublisher
{
    /**
     * TimeslotEventPublisher constructor.
     */
    public function __construct(protected \App\Services\RabbitMQ\RabbitMQService $rabbitMQService)
    {
    }

    /**
     * Publish timeslot created event.
     */
    public function publishTimeslotCreated(Timeslot $timeslot): bool
    {
        try {
            $data = [
                'event' => 'timeslot.created',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatTimeslotData($timeslot),
            ];

            return $this->rabbitMQService->publish('timeslot.created', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish timeslot.created event', [
                'timeslot_id' => $timeslot->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish timeslot updated event.
     */
    public function publishTimeslotUpdated(Timeslot $timeslot): bool
    {
        try {
            $data = [
                'event' => 'timeslot.updated',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatTimeslotData($timeslot),
            ];

            return $this->rabbitMQService->publish('timeslot.updated', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish timeslot.updated event', [
                'timeslot_id' => $timeslot->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Publish timeslot deleted event.
     */
    public function publishTimeslotDeleted(Timeslot $timeslot): bool
    {
        try {
            $data = [
                'event' => 'timeslot.deleted',
                'timestamp' => now()->toIso8601String(),
                'data' => $this->formatTimeslotData($timeslot),
            ];

            return $this->rabbitMQService->publish('timeslot.deleted', $data);
        } catch (\Exception $e) {
            Log::error('Failed to publish timeslot.deleted event', [
                'timeslot_id' => $timeslot->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Format timeslot data for event publishing.
     */
    protected function formatTimeslotData(Timeslot $timeslot): array
    {
        return [
            'id' => $timeslot->id,
            'day' => $timeslot->day,
            'starttime' => $timeslot->starttime,
            'endtime' => $timeslot->endtime,
            'menu_type' => $timeslot->menu_type,
            'kitchen' => $timeslot->kitchen,
            'status' => $timeslot->status,
            'company_id' => $timeslot->company_id,
            'unit_id' => $timeslot->unit_id,
            'created_at' => $timeslot->created_at ? $timeslot->created_at->toIso8601String() : null,
            'updated_at' => $timeslot->updated_at ? $timeslot->updated_at->toIso8601String() : null,
        ];
    }
}

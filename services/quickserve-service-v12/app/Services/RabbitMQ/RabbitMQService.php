<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\RabbitMQ;

use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;

class RabbitMQService implements RabbitMQServiceInterface
{
    /**
     * @var AMQPChannel
     */
    protected $channel;

    /**
     * RabbitMQService constructor.
     */
    public function __construct(protected \PhpAmqpLib\Connection\AMQPStreamConnection $connection, protected array $config)
    {
        $this->channel = $this->connection->channel();
    }

    /**
     * Initialize RabbitMQ exchanges, queues, and bindings.
     */
    public function initialize(): void
    {
        try {
            // Declare main exchange
            $this->declareExchange(
                $this->config['exchange']['name'],
                $this->config['exchange']['type'],
                $this->config['exchange']['passive'],
                $this->config['exchange']['durable'],
                $this->config['exchange']['auto_delete']
            );

            // Declare dead letter exchange
            $this->declareExchange(
                $this->config['dead_letter_exchange']['name'],
                $this->config['dead_letter_exchange']['type'],
                $this->config['dead_letter_exchange']['passive'],
                $this->config['dead_letter_exchange']['durable'],
                $this->config['dead_letter_exchange']['auto_delete']
            );

            // Declare queues
            foreach ($this->config['queues'] as $queueConfig) {
                $this->declareQueue(
                    $queueConfig['name'],
                    $queueConfig['passive'],
                    $queueConfig['durable'],
                    $queueConfig['exclusive'],
                    $queueConfig['auto_delete'],
                    false,
                    new AMQPTable([
                        'x-dead-letter-exchange' => $queueConfig['dead_letter_exchange'],
                        'x-dead-letter-routing-key' => $queueConfig['dead_letter_routing_key'],
                        'x-message-ttl' => $queueConfig['message_ttl'],
                    ])
                );

                // Bind queue to exchange with routing keys
                foreach ($queueConfig['routing_keys'] as $routingKey) {
                    $this->bindQueue(
                        $queueConfig['name'],
                        $this->config['exchange']['name'],
                        $routingKey
                    );
                }
            }

            // Declare dead letter queues
            foreach ($this->config['dead_letter_queues'] as $queueConfig) {
                $this->declareQueue(
                    $queueConfig['name'],
                    $queueConfig['passive'],
                    $queueConfig['durable'],
                    $queueConfig['exclusive'],
                    $queueConfig['auto_delete']
                );

                // Bind dead letter queue to dead letter exchange
                $this->bindQueue(
                    $queueConfig['name'],
                    $this->config['dead_letter_exchange']['name'],
                    $queueConfig['routing_key']
                );
            }

            Log::info('RabbitMQ initialization completed successfully.');
        } catch (\Exception $e) {
            Log::error('RabbitMQ initialization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Publish a message to RabbitMQ.
     */
    public function publish(string $routingKey, array $data, array $properties = []): bool
    {
        try {
            // Add correlation ID if not provided
            if (!isset($properties['correlation_id'])) {
                $properties['correlation_id'] = $this->generateCorrelationId();
            }

            // Add timestamp if not provided
            if (!isset($properties['timestamp'])) {
                $properties['timestamp'] = time();
            }

            // Add message ID if not provided
            if (!isset($properties['message_id'])) {
                $properties['message_id'] = $this->generateMessageId();
            }

            // Add application ID if not provided
            if (!isset($properties['app_id'])) {
                $properties['app_id'] = 'quickserve-service';
            }

            // Add content type if not provided
            if (!isset($properties['content_type'])) {
                $properties['content_type'] = 'application/json';
            }

            // Add delivery mode if not provided (2 = persistent)
            if (!isset($properties['delivery_mode'])) {
                $properties['delivery_mode'] = 2;
            }

            // Create message
            $message = new AMQPMessage(
                json_encode($data),
                $properties
            );

            // Publish message
            $this->channel->basic_publish(
                $message,
                $this->config['exchange']['name'],
                $routingKey
            );

            Log::info('Message published to RabbitMQ', [
                'routing_key' => $routingKey,
                'correlation_id' => $properties['correlation_id'],
                'message_id' => $properties['message_id'],
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to publish message to RabbitMQ', [
                'routing_key' => $routingKey,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Consume messages from a queue.
     */
    public function consume(
        string $queueName,
        callable $callback,
        string $consumerTag = '',
        bool $noLocal = false,
        bool $noAck = false,
        bool $exclusive = false,
        bool $noWait = false
    ): void {
        // Set prefetch count
        $this->channel->basic_qos(
            $this->config['consumer']['prefetch_size'],
            $this->config['consumer']['prefetch_count'],
            false
        );

        // Set consumer tag if not provided
        if ($consumerTag === '' || $consumerTag === '0') {
            $consumerTag = $this->config['consumer']['tag'] . '_' . uniqid();
        }

        // Consume messages
        $this->channel->basic_consume(
            $queueName,
            $consumerTag,
            $noLocal,
            $noAck,
            $exclusive,
            $noWait,
            function (AMQPMessage $message) use ($callback, $queueName): void {
                try {
                    // Parse message body
                    $body = json_decode($message->getBody(), true);

                    // Get message properties
                    $properties = $message->get_properties();

                    // Log received message
                    Log::info('Message received from RabbitMQ', [
                        'queue' => $queueName,
                        'correlation_id' => $properties['correlation_id'] ?? null,
                        'message_id' => $properties['message_id'] ?? null,
                    ]);

                    // Process message
                    $result = $callback($body, $properties, $message);

                    // Acknowledge message if callback returns true
                    if ($result) {
                        $message->ack();
                    } else {
                        // Reject message and requeue if retry is enabled and max attempts not reached
                        $this->handleMessageRetry($message);
                    }
                } catch (\Exception $e) {
                    // Log error
                    Log::error('Error processing message from RabbitMQ', [
                        'queue' => $queueName,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);

                    // Handle message retry
                    $this->handleMessageRetry($message);
                }
            }
        );

        // Wait for messages
        while ($this->channel->is_consuming()) {
            $this->channel->wait();
        }
    }

    /**
     * Handle message retry.
     */
    protected function handleMessageRetry(AMQPMessage $message): void
    {
        // Get message properties
        $properties = $message->get_properties();

        // Get retry count from headers
        $headers = $properties['application_headers'] ?? new AMQPTable([]);
        $headers = $headers instanceof AMQPTable ? $headers->getNativeData() : [];
        $retryCount = $headers['x-retry-count'] ?? 0;

        // Check if retry is enabled and max attempts not reached
        if ($this->config['retry']['enabled'] && $retryCount < $this->config['retry']['max_attempts']) {
            // Increment retry count
            $retryCount++;

            // Calculate delay based on retry count
            $delay = $this->calculateRetryDelay($retryCount);

            // Set new headers
            $headers['x-retry-count'] = $retryCount;
            $headers['x-first-death-reason'] = 'rejected';
            $headers['x-first-death-queue'] = $message->getDeliveryInfo()['routing_key'];
            $headers['x-first-death-exchange'] = $message->getDeliveryInfo()['exchange'];
            $headers['x-delay'] = $delay;

            // Create new message with updated headers
            $newMessage = new AMQPMessage(
                $message->getBody(),
                array_merge($properties, [
                    'application_headers' => new AMQPTable($headers),
                ])
            );

            // Publish message to the same exchange with the same routing key
            $this->channel->basic_publish(
                $newMessage,
                $message->getDeliveryInfo()['exchange'],
                $message->getDeliveryInfo()['routing_key']
            );

            // Acknowledge the original message
            $message->ack();

            Log::info('Message requeued for retry', [
                'retry_count' => $retryCount,
                'delay' => $delay,
                'correlation_id' => $properties['correlation_id'] ?? null,
                'message_id' => $properties['message_id'] ?? null,
            ]);
        } else {
            // Reject message and don't requeue (send to dead letter queue)
            $message->reject(false);

            Log::warning('Message rejected and sent to dead letter queue', [
                'retry_count' => $retryCount,
                'correlation_id' => $properties['correlation_id'] ?? null,
                'message_id' => $properties['message_id'] ?? null,
            ]);
        }
    }

    /**
     * Calculate retry delay based on retry count.
     */
    protected function calculateRetryDelay(int $retryCount): int
    {
        $initialInterval = $this->config['retry']['initial_interval'];
        $multiplier = $this->config['retry']['multiplier'];
        $maxInterval = $this->config['retry']['max_interval'];

        $delay = $initialInterval * $multiplier ** ($retryCount - 1);

        return min($delay, $maxInterval);
    }

    /**
     * Declare an exchange.
     */
    protected function declareExchange(
        string $exchangeName,
        string $type,
        bool $passive,
        bool $durable,
        bool $autoDelete
    ): void {
        $this->channel->exchange_declare(
            $exchangeName,
            $type,
            $passive,
            $durable,
            $autoDelete
        );
    }

    /**
     * Declare a queue.
     */
    protected function declareQueue(
        string $queueName,
        bool $passive,
        bool $durable,
        bool $exclusive,
        bool $autoDelete,
        bool $noWait = false,
        ?AMQPTable $arguments = null
    ): void {
        $this->channel->queue_declare(
            $queueName,
            $passive,
            $durable,
            $exclusive,
            $autoDelete,
            $noWait,
            $arguments
        );
    }

    /**
     * Bind a queue to an exchange.
     */
    protected function bindQueue(
        string $queueName,
        string $exchangeName,
        string $routingKey
    ): void {
        $this->channel->queue_bind(
            $queueName,
            $exchangeName,
            $routingKey
        );
    }

    /**
     * Generate a correlation ID.
     */
    protected function generateCorrelationId(): string
    {
        return uniqid('', true);
    }

    /**
     * Generate a message ID.
     */
    protected function generateMessageId(): string
    {
        return uniqid('msg_', true);
    }

    /**
     * Close the channel and connection.
     */
    public function close(): void
    {
        if ($this->channel && $this->channel->is_open()) {
            $this->channel->close();
        }

        if ($this->connection && $this->connection->isConnected()) {
            $this->connection->close();
        }
    }

    /**
     * Destructor.
     */
    public function __destruct()
    {
        $this->close();
    }
}

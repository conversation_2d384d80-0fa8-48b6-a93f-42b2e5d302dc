<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\RabbitMQ;

interface RabbitMQServiceInterface
{
    public function initialize(): void;
    public function publish(string $routingKey, array $data, array $properties = []): bool;
    public function consume(
        string $queueName,
        callable $callback,
        string $consumerTag = '',
        bool $noLocal = false,
        bool $noAck = false,
        bool $exclusive = false,
        bool $noWait = false
    ): void;
    public function close(): void;
}

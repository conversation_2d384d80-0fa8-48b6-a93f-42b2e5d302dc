<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\RabbitMQ;

use PhpAmqpLib\Wire\AMQPTable;

class MockRabbitMQService implements RabbitMQServiceInterface
{
    public function __construct()
    {
        // Mock constructor - don't call parent
    }

    public function initialize(): void
    {
        // Mock implementation
    }

    public function publish(string $routingKey, array $data, array $properties = []): bool
    {
        // Mock implementation
        return true;
    }

    public function consume(
        string $queueName,
        callable $callback,
        string $consumerTag = '',
        bool $noLocal = false,
        bool $noAck = false,
        bool $exclusive = false,
        bool $noWait = false
    ): void {
        // Mock implementation
    }

    protected function declareQueue(
        string $queueName,
        bool $passive,
        bool $durable,
        bool $exclusive,
        bool $autoDelete,
        bool $noWait = false,
        ?AMQPTable $arguments = null
    ): void {
        // Mock implementation
    }

    protected function bindQueue(string $queueName, string $exchangeName, string $routingKey): void
    {
        // Mock implementation
    }

    public function close(): void
    {
        // Mock implementation
    }

    public function __call($method, $args)
    {
        // Mock any other method calls
        return null;
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Timeslot;

use App\Models\Timeslot;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface TimeslotServiceInterface
{
    /**
     * Get all timeslots.
     */
    public function getAllTimeslots(array $filters = []): Collection;

    /**
     * Get paginated timeslots.
     */
    public function getPaginatedTimeslots(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get timeslot by ID.
     */
    public function getTimeslotById(int $id): ?Timeslot;

    /**
     * Create a new timeslot.
     */
    public function createTimeslot(array $data): Timeslot;

    /**
     * Update a timeslot.
     */
    public function updateTimeslot(int $id, array $data): bool;

    /**
     * Delete a timeslot.
     */
    public function deleteTimeslot(int $id): bool;

    /**
     * Get timeslots by day.
     */
    public function getTimeslotsByDay(string $day, array $filters = []): Collection;

    /**
     * Get timeslots by menu type.
     */
    public function getTimeslotsByMenuType(string $menuType, array $filters = []): Collection;

    /**
     * Get timeslots by kitchen.
     */
    public function getTimeslotsByKitchen(string $kitchen, array $filters = []): Collection;

    /**
     * Get active timeslots.
     */
    public function getActiveTimeslots(array $filters = []): Collection;

    /**
     * Get available timeslots for a specific day, menu type, and kitchen.
     */
    public function getAvailableTimeslots(string $day, string $menuType, string $kitchen, ?string $orderDate = null): Collection;

    /**
     * Check if a timeslot is available for a specific day, menu type, and kitchen.
     */
    public function isTimeslotAvailable(string $timeslot, string $day, string $menuType, string $kitchen, ?string $orderDate = null): bool;

    /**
     * Get the next available timeslot for a specific day, menu type, and kitchen.
     */
    public function getNextAvailableTimeslot(string $day, string $menuType, string $kitchen, ?string $orderDate = null): ?Timeslot;
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Timeslot;

use App\Events\Timeslot\TimeslotCreated;
use App\Events\Timeslot\TimeslotDeleted;
use App\Events\Timeslot\TimeslotUpdated;
use App\Exceptions\TimeslotException;
use App\Models\Timeslot;
use App\Repositories\Timeslot\TimeslotRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class TimeslotService implements TimeslotServiceInterface
{
    /**
     * TimeslotService constructor.
     */
    public function __construct(protected \App\Repositories\Timeslot\TimeslotRepositoryInterface $timeslotRepository)
    {
    }

    /**
     * Get all timeslots.
     */
    public function getAllTimeslots(array $filters = []): Collection
    {
        return $this->timeslotRepository->all($filters);
    }

    /**
     * Get paginated timeslots.
     */
    public function getPaginatedTimeslots(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->timeslotRepository->paginate($perPage, $filters);
    }

    /**
     * Get timeslot by ID.
     */
    public function getTimeslotById(int $id): ?Timeslot
    {
        return $this->timeslotRepository->findById($id);
    }

    /**
     * Create a new timeslot.
     *
     * @throws TimeslotException
     */
    public function createTimeslot(array $data): Timeslot
    {
        try {
            // Validate time format
            if (!$this->validateTimeFormat($data['starttime']) || !$this->validateTimeFormat($data['endtime'])) {
                throw new TimeslotException('Invalid time format. Use HH:MM:SS format.');
            }
            
            // Validate that start time is before end time
            if (strtotime((string) $data['starttime']) >= strtotime((string) $data['endtime'])) {
                throw new TimeslotException('Start time must be before end time.');
            }
            
            // Set default values if not provided
            if (!isset($data['status'])) {
                $data['status'] = true;
            }
            
            // Create timeslot
            $timeslot = $this->timeslotRepository->create($data);
            
            // Dispatch event
            Event::dispatch(new TimeslotCreated($timeslot));
            
            return $timeslot;
        } catch (TimeslotException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to create timeslot', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            throw new TimeslotException('Failed to create timeslot: ' . $e->getMessage());
        }
    }

    /**
     * Update a timeslot.
     *
     * @throws TimeslotException
     */
    public function updateTimeslot(int $id, array $data): bool
    {
        try {
            $timeslot = $this->getTimeslotById($id);
            
            if (!$timeslot instanceof \App\Models\Timeslot) {
                throw new TimeslotException('Timeslot not found.');
            }
            
            // Validate time format if provided
            if (isset($data['starttime']) && !$this->validateTimeFormat($data['starttime'])) {
                throw new TimeslotException('Invalid start time format. Use HH:MM:SS format.');
            }
            
            if (isset($data['endtime']) && !$this->validateTimeFormat($data['endtime'])) {
                throw new TimeslotException('Invalid end time format. Use HH:MM:SS format.');
            }
            
            // Validate that start time is before end time if both are provided
            if (isset($data['starttime']) && isset($data['endtime'])) {
                if (strtotime($data['starttime']) >= strtotime($data['endtime'])) {
                    throw new TimeslotException('Start time must be before end time.');
                }
            } elseif (isset($data['starttime'])) {
                // If only start time is provided, validate against existing end time
                if (strtotime($data['starttime']) >= strtotime($timeslot->endtime)) {
                    throw new TimeslotException('Start time must be before end time.');
                }
            } elseif (isset($data['endtime'])) {
                // If only end time is provided, validate against existing start time
                if (strtotime($timeslot->starttime) >= strtotime($data['endtime'])) {
                    throw new TimeslotException('Start time must be before end time.');
                }
            }
            
            // Update timeslot
            $updated = $this->timeslotRepository->update($id, $data);
            
            if ($updated) {
                // Refresh the timeslot data
                $timeslot = $this->getTimeslotById($id);
                
                // Dispatch event
                Event::dispatch(new TimeslotUpdated($timeslot));
            }
            
            return $updated;
        } catch (TimeslotException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to update timeslot', [
                'error' => $e->getMessage(),
                'timeslot_id' => $id,
                'data' => $data
            ]);
            
            throw new TimeslotException('Failed to update timeslot: ' . $e->getMessage());
        }
    }

    /**
     * Delete a timeslot.
     *
     * @throws TimeslotException
     */
    public function deleteTimeslot(int $id): bool
    {
        try {
            $timeslot = $this->getTimeslotById($id);
            
            if (!$timeslot instanceof \App\Models\Timeslot) {
                throw new TimeslotException('Timeslot not found.');
            }
            
            // Delete timeslot
            $deleted = $this->timeslotRepository->delete($id);
            
            if ($deleted) {
                // Dispatch event
                Event::dispatch(new TimeslotDeleted($timeslot));
            }
            
            return $deleted;
        } catch (TimeslotException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to delete timeslot', [
                'error' => $e->getMessage(),
                'timeslot_id' => $id
            ]);
            
            throw new TimeslotException('Failed to delete timeslot: ' . $e->getMessage());
        }
    }

    /**
     * Get timeslots by day.
     */
    public function getTimeslotsByDay(string $day, array $filters = []): Collection
    {
        return $this->timeslotRepository->getByDay($day, $filters);
    }

    /**
     * Get timeslots by menu type.
     */
    public function getTimeslotsByMenuType(string $menuType, array $filters = []): Collection
    {
        return $this->timeslotRepository->getByMenuType($menuType, $filters);
    }

    /**
     * Get timeslots by kitchen.
     */
    public function getTimeslotsByKitchen(string $kitchen, array $filters = []): Collection
    {
        return $this->timeslotRepository->getByKitchen($kitchen, $filters);
    }

    /**
     * Get active timeslots.
     */
    public function getActiveTimeslots(array $filters = []): Collection
    {
        return $this->timeslotRepository->getActive($filters);
    }

    /**
     * Get available timeslots for a specific day, menu type, and kitchen.
     */
    public function getAvailableTimeslots(string $day, string $menuType, string $kitchen, ?string $orderDate = null): Collection
    {
        return $this->timeslotRepository->getAvailableTimeslots($day, $menuType, $kitchen, $orderDate);
    }

    /**
     * Check if a timeslot is available for a specific day, menu type, and kitchen.
     */
    public function isTimeslotAvailable(string $timeslot, string $day, string $menuType, string $kitchen, ?string $orderDate = null): bool
    {
        $availableTimeslots = $this->getAvailableTimeslots($day, $menuType, $kitchen, $orderDate);
        
        foreach ($availableTimeslots as $slot) {
            if ($slot->starttime === $timeslot) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Get the next available timeslot for a specific day, menu type, and kitchen.
     */
    public function getNextAvailableTimeslot(string $day, string $menuType, string $kitchen, ?string $orderDate = null): ?Timeslot
    {
        $availableTimeslots = $this->getAvailableTimeslots($day, $menuType, $kitchen, $orderDate);
        
        if ($availableTimeslots->isEmpty()) {
            return null;
        }
        
        return $availableTimeslots->first();
    }

    /**
     * Validate time format.
     */
    protected function validateTimeFormat(string $time): bool
    {
        return preg_match('/^([01]?\d|2[0-3]):[0-5]\d:[0-5]\d$/', $time) === 1;
    }
}

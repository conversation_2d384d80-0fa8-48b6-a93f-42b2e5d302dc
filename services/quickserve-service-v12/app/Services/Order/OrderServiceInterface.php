<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Order;

use App\Models\Order;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface OrderServiceInterface
{
    /**
     * Get all orders.
     */
    public function getAllOrders(array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get paginated orders.
     */
    public function getPaginatedOrders(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get order by ID.
     */
    public function getOrderById(int $id): ?Order;

    /**
     * Get order by order number.
     */
    public function getOrderByOrderNo(string $orderNo): ?Order;

    /**
     * Get orders by customer ID.
     */
    public function getOrdersByCustomerId(int $customerId, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Create a new order.
     */
    public function createOrder(array $data): Order;

    /**
     * Update an order.
     */
    public function updateOrder(int $id, array $data): bool;

    /**
     * Delete an order.
     */
    public function deleteOrder(int $id): bool;

    /**
     * Update order status.
     */
    public function updateOrderStatus(int $id, string $status): bool;

    /**
     * Update order delivery status.
     */
    public function updateOrderDeliveryStatus(int $id, string $deliveryStatus): bool;

    /**
     * Cancel an order.
     */
    public function cancelOrder(int $id, ?string $reason = null): bool;

    /**
     * Process payment for an order.
     */
    public function processPayment(int $id, string $paymentMethod, array $paymentDetails = []): array;

    /**
     * Get orders by status.
     */
    public function getOrdersByStatus(string $status, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get orders by delivery status.
     */
    public function getOrdersByDeliveryStatus(string $deliveryStatus, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get orders by date range.
     */
    public function getOrdersByDateRange(string $startDate, string $endDate, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get orders by kitchen ID.
     */
    public function getOrdersByKitchenId(int $kitchenId, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Generate order number.
     */
    public function generateOrderNumber(): string;
}

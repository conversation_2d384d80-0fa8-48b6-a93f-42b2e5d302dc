<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Order;

use App\Events\Order\OrderCancelled;
use App\Events\Order\OrderCreated;
use App\Events\Order\OrderStatusUpdated;
use App\Exceptions\CustomerException;
use App\Exceptions\OrderException;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Repositories\Order\OrderRepositoryInterface;
use App\Services\Customer\CustomerServiceInterface;
use App\Services\Payment\PaymentServiceInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class OrderService implements OrderServiceInterface
{
    /**
     * @var PaymentServiceInterface
     */
    protected $paymentService;

    /**
     * OrderService constructor.
     */
    public function __construct(
        protected \App\Repositories\Order\OrderRepositoryInterface $orderRepository,
        protected \App\Services\Customer\CustomerServiceInterface $customerService,
        PaymentServiceInterface $paymentService
    ) {
        $this->paymentService = $paymentService;
    }

    /**
     * Get all orders.
     */
    public function getAllOrders(array $filters = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->all($filters);
    }

    /**
     * Get paginated orders.
     */
    public function getPaginatedOrders(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->orderRepository->paginate($perPage, $filters);
    }

    /**
     * Get order by ID.
     */
    public function getOrderById(int $id): ?Order
    {
        return $this->orderRepository->findById($id);
    }

    /**
     * Get order by order number.
     */
    public function getOrderByOrderNo(string $orderNo): ?Order
    {
        return $this->orderRepository->findByOrderNo($orderNo);
    }

    /**
     * Get orders by customer ID.
     */
    public function getOrdersByCustomerId(int $customerId, array $filters = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->getByCustomerId($customerId, $filters);
    }

    /**
     * Create a new order.
     *
     * @throws OrderException
     * @throws CustomerException
     */
    public function createOrder(array $data): Order
    {
        try {
            // Validate customer exists
            $customer = $this->customerService->getCustomerById($data['customer_code']);
            if (!$customer instanceof \App\Models\Customer) {
                throw new CustomerException('Customer not found');
            }

            // Start transaction
            DB::beginTransaction();

            // Generate order number if not provided
            if (!isset($data['order_no'])) {
                $data['order_no'] = $this->generateOrderNumber();
            }

            // Set default values if not provided
            if (!isset($data['order_status'])) {
                $data['order_status'] = 'New';
            }

            if (!isset($data['delivery_status'])) {
                $data['delivery_status'] = 'Pending';
            }

            if (!isset($data['invoice_status'])) {
                $data['invoice_status'] = 'Unbill';
            }

            if (!isset($data['created_date'])) {
                $data['created_date'] = now();
            }

            // Create the order
            $order = $this->orderRepository->create($data);

            // Create order details if provided
            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $item) {
                    OrderDetail::create([
                        'ref_order_no' => $order->order_no,
                        'product_code' => $item['product_code'],
                        'product_name' => $item['product_name'],
                        'quantity' => $item['quantity'],
                        'amount' => $item['amount'],
                        'tax' => $item['tax'] ?? 0,
                        'company_id' => $order->company_id,
                        'unit_id' => $order->unit_id,
                    ]);
                }
            }

            // Commit transaction
            DB::commit();

            // Dispatch event
            Event::dispatch(new OrderCreated($order));

            return $order;
        } catch (CustomerException $e) {
            // Rollback transaction
            DB::rollBack();

            Log::error('Failed to create order', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            Log::error('Failed to create order', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new OrderException('Failed to create order: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Update an order.
     */
    public function updateOrder(int $id, array $data): bool
    {
        try {
            // Set last modified date
            $data['last_modified'] = now();
            
            return $this->orderRepository->update($id, $data);
        } catch (\Exception $e) {
            Log::error('Failed to update order', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'data' => $data
            ]);
            
            return false;
        }
    }

    /**
     * Delete an order.
     */
    public function deleteOrder(int $id): bool
    {
        try {
            return $this->orderRepository->delete($id);
        } catch (\Exception $e) {
            Log::error('Failed to delete order', [
                'error' => $e->getMessage(),
                'order_id' => $id
            ]);
            
            return false;
        }
    }

    /**
     * Update order status.
     */
    public function updateOrderStatus(int $id, string $status): bool
    {
        try {
            $order = $this->getOrderById($id);
            
            if (!$order instanceof \App\Models\Order) {
                return false;
            }
            
            $data = [
                'order_status' => $status,
                'last_modified' => now()
            ];
            
            $updated = $this->orderRepository->update($id, $data);
            
            if ($updated) {
                // Dispatch event
                Event::dispatch(new OrderStatusUpdated($order, $status));
            }
            
            return $updated;
        } catch (\Exception $e) {
            Log::error('Failed to update order status', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'status' => $status
            ]);
            
            return false;
        }
    }

    /**
     * Update order delivery status.
     */
    public function updateOrderDeliveryStatus(int $id, string $deliveryStatus): bool
    {
        try {
            $order = $this->getOrderById($id);
            
            if (!$order instanceof \App\Models\Order) {
                return false;
            }
            
            $data = [
                'delivery_status' => $deliveryStatus,
                'last_modified' => now()
            ];
            
            return $this->orderRepository->update($id, $data);
        } catch (\Exception $e) {
            Log::error('Failed to update order delivery status', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'delivery_status' => $deliveryStatus
            ]);
            
            return false;
        }
    }

    /**
     * Cancel an order.
     */
    public function cancelOrder(int $id, ?string $reason = null): bool
    {
        try {
            $order = $this->getOrderById($id);
            
            if (!$order instanceof \App\Models\Order) {
                return false;
            }
            
            $data = [
                'order_status' => 'Cancel',
                'remark' => $reason,
                'last_modified' => now()
            ];
            
            $cancelled = $this->orderRepository->update($id, $data);
            
            if ($cancelled) {
                // Dispatch event
                Event::dispatch(new OrderCancelled($order, $reason));
            }
            
            return $cancelled;
        } catch (\Exception $e) {
            Log::error('Failed to cancel order', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'reason' => $reason
            ]);
            
            return false;
        }
    }

    /**
     * Process payment for an order.
     *
     * @throws OrderException
     */
    public function processPayment(int $id, string $paymentMethod, array $paymentDetails = []): array
    {
        try {
            $order = $this->getOrderById($id);
            
            if (!$order instanceof \App\Models\Order) {
                throw new OrderException('Order not found');
            }
            
            // Calculate payment amount
            $amount = $order->getNetAmountAttribute();
            
            // Process payment through payment service
            $paymentResponse = $this->paymentService->processPayment(
                $order->order_no,
                $amount,
                $paymentMethod,
                $paymentDetails
            );
            
            // Update order with payment information
            if ($paymentResponse['status'] === 'success') {
                $this->updateOrder($id, [
                    'payment_mode' => $paymentMethod,
                    'amount_paid' => $amount,
                    'last_modified' => now()
                ]);
            }
            
            return $paymentResponse;
        } catch (\Exception $e) {
            Log::error('Failed to process payment', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'payment_method' => $paymentMethod
            ]);
            
            throw new OrderException('Failed to process payment: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Get orders by status.
     */
    public function getOrdersByStatus(string $status, array $filters = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->getByStatus($status, $filters);
    }

    /**
     * Get orders by delivery status.
     */
    public function getOrdersByDeliveryStatus(string $deliveryStatus, array $filters = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->getByDeliveryStatus($deliveryStatus, $filters);
    }

    /**
     * Get orders by date range.
     */
    public function getOrdersByDateRange(string $startDate, string $endDate, array $filters = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->getByDateRange($startDate, $endDate, $filters);
    }

    /**
     * Get orders by kitchen ID.
     */
    public function getOrdersByKitchenId(int $kitchenId, array $filters = []): Collection|LengthAwarePaginator
    {
        return $this->orderRepository->getByKitchenId($kitchenId, $filters);
    }

    /**
     * Generate order number.
     */
    public function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $timestamp = now()->format('YmdHis');
        $random = random_int(1000, 9999);

        return $prefix . $timestamp . $random;
    }
}

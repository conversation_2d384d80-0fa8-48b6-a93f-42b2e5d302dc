<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Backorder;

use App\Events\Backorder\BackorderCancelled;
use App\Events\Backorder\BackorderCompleted;
use App\Events\Backorder\BackorderCreated;
use App\Events\Backorder\BackorderDeleted;
use App\Events\Backorder\BackorderUpdated;
use App\Exceptions\BackorderException;
use App\Models\Backorder;
use App\Models\Order;
use App\Models\Product;
use App\Repositories\Backorder\BackorderRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class BackorderService implements BackorderServiceInterface
{
    /**
     * BackorderService constructor.
     */
    public function __construct(protected \App\Repositories\Backorder\BackorderRepositoryInterface $backorderRepository)
    {
    }

    /**
     * Get all backorders.
     */
    public function getAllBackorders(array $filters = []): Collection
    {
        return $this->backorderRepository->all($filters);
    }

    /**
     * Get paginated backorders.
     */
    public function getPaginatedBackorders(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->backorderRepository->paginate($perPage, $filters);
    }

    /**
     * Get backorder by ID.
     */
    public function getBackorderById(int $id): ?Backorder
    {
        return $this->backorderRepository->findById($id);
    }

    /**
     * Create a new backorder.
     *
     * @throws BackorderException
     */
    public function createBackorder(array $data): Backorder
    {
        try {
            // Validate required fields
            $requiredFields = ['order_id', 'order_no', 'customer_id', 'product_id', 'product_name', 'quantity', 'amount', 'order_date', 'order_menu'];
            
            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    throw new BackorderException("The {$field} field is required.");
                }
            }
            
            // Set default values if not provided
            if (!isset($data['status'])) {
                $data['status'] = 'pending';
            }
            
            // Create backorder
            $backorder = $this->backorderRepository->create($data);
            
            // Dispatch event
            Event::dispatch(new BackorderCreated($backorder));
            
            return $backorder;
        } catch (BackorderException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to create backorder', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            throw new BackorderException('Failed to create backorder: ' . $e->getMessage());
        }
    }

    /**
     * Create a backorder from an order.
     *
     * @throws BackorderException
     */
    public function createBackorderFromOrder(Order $order, string $reason): Backorder
    {
        try {
            $product = Product::find($order->product_code);
            
            if (!$product) {
                throw new BackorderException('Product not found.');
            }
            
            $data = [
                'company_id' => $order->company_id ?? 1,
                'unit_id' => $order->unit_id ?? 1,
                'order_id' => $order->pk_order_no,
                'order_no' => $order->order_no,
                'customer_id' => $order->customer_code,
                'product_id' => $order->product_code,
                'product_name' => $product->name ?? $order->product_name,
                'quantity' => $order->quantity,
                'amount' => $order->amount,
                'order_date' => $order->order_date,
                'order_menu' => $order->order_menu,
                'reason' => $reason,
                'status' => 'pending'
            ];
            
            return $this->createBackorder($data);
        } catch (BackorderException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to create backorder from order', [
                'error' => $e->getMessage(),
                'order_id' => $order->pk_order_no
            ]);
            
            throw new BackorderException('Failed to create backorder from order: ' . $e->getMessage());
        }
    }

    /**
     * Update a backorder.
     *
     * @throws BackorderException
     */
    public function updateBackorder(int $id, array $data): bool
    {
        try {
            $backorder = $this->getBackorderById($id);
            
            if (!$backorder instanceof \App\Models\Backorder) {
                throw new BackorderException('Backorder not found.');
            }
            
            // Update backorder
            $updated = $this->backorderRepository->update($id, $data);
            
            if ($updated) {
                // Refresh the backorder data
                $backorder = $this->getBackorderById($id);
                
                // Dispatch event
                Event::dispatch(new BackorderUpdated($backorder));
            }
            
            return $updated;
        } catch (BackorderException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to update backorder', [
                'error' => $e->getMessage(),
                'backorder_id' => $id,
                'data' => $data
            ]);
            
            throw new BackorderException('Failed to update backorder: ' . $e->getMessage());
        }
    }

    /**
     * Delete a backorder.
     *
     * @throws BackorderException
     */
    public function deleteBackorder(int $id): bool
    {
        try {
            $backorder = $this->getBackorderById($id);
            
            if (!$backorder instanceof \App\Models\Backorder) {
                throw new BackorderException('Backorder not found.');
            }
            
            // Delete backorder
            $deleted = $this->backorderRepository->delete($id);
            
            if ($deleted) {
                // Dispatch event
                Event::dispatch(new BackorderDeleted($backorder));
            }
            
            return $deleted;
        } catch (BackorderException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to delete backorder', [
                'error' => $e->getMessage(),
                'backorder_id' => $id
            ]);
            
            throw new BackorderException('Failed to delete backorder: ' . $e->getMessage());
        }
    }

    /**
     * Complete a backorder.
     *
     * @throws BackorderException
     */
    public function completeBackorder(int $id): bool
    {
        try {
            $backorder = $this->getBackorderById($id);
            
            if (!$backorder instanceof \App\Models\Backorder) {
                throw new BackorderException('Backorder not found.');
            }
            
            if ($backorder->status !== 'pending') {
                throw new BackorderException('Only pending backorders can be completed.');
            }
            
            // Update backorder status
            $updated = $this->backorderRepository->update($id, ['status' => 'completed']);
            
            if ($updated) {
                // Refresh the backorder data
                $backorder = $this->getBackorderById($id);
                
                // Dispatch event
                Event::dispatch(new BackorderCompleted($backorder));
            }
            
            return $updated;
        } catch (BackorderException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to complete backorder', [
                'error' => $e->getMessage(),
                'backorder_id' => $id
            ]);
            
            throw new BackorderException('Failed to complete backorder: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a backorder.
     *
     * @throws BackorderException
     */
    public function cancelBackorder(int $id, ?string $reason = null): bool
    {
        try {
            $backorder = $this->getBackorderById($id);
            
            if (!$backorder instanceof \App\Models\Backorder) {
                throw new BackorderException('Backorder not found.');
            }
            
            if ($backorder->status !== 'pending') {
                throw new BackorderException('Only pending backorders can be cancelled.');
            }
            
            // Update backorder status and reason if provided
            $data = ['status' => 'cancelled'];
            
            if ($reason) {
                $data['reason'] = $reason;
            }
            
            // Update backorder
            $updated = $this->backorderRepository->update($id, $data);
            
            if ($updated) {
                // Refresh the backorder data
                $backorder = $this->getBackorderById($id);
                
                // Dispatch event
                Event::dispatch(new BackorderCancelled($backorder));
            }
            
            return $updated;
        } catch (BackorderException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to cancel backorder', [
                'error' => $e->getMessage(),
                'backorder_id' => $id
            ]);
            
            throw new BackorderException('Failed to cancel backorder: ' . $e->getMessage());
        }
    }

    /**
     * Get backorders by order ID.
     */
    public function getBackordersByOrderId(int $orderId, array $filters = []): Collection
    {
        return $this->backorderRepository->getByOrderId($orderId, $filters);
    }

    /**
     * Get backorders by customer ID.
     */
    public function getBackordersByCustomerId(int $customerId, array $filters = []): Collection
    {
        return $this->backorderRepository->getByCustomerId($customerId, $filters);
    }

    /**
     * Get backorders by product ID.
     */
    public function getBackordersByProductId(int $productId, array $filters = []): Collection
    {
        return $this->backorderRepository->getByProductId($productId, $filters);
    }

    /**
     * Get backorders by status.
     */
    public function getBackordersByStatus(string $status, array $filters = []): Collection
    {
        return $this->backorderRepository->getByStatus($status, $filters);
    }

    /**
     * Get backorders by date range.
     */
    public function getBackordersByDateRange(string $startDate, string $endDate, array $filters = []): Collection
    {
        return $this->backorderRepository->getByDateRange($startDate, $endDate, $filters);
    }

    /**
     * Get pending backorders.
     */
    public function getPendingBackorders(array $filters = []): Collection
    {
        return $this->backorderRepository->getPending($filters);
    }

    /**
     * Get completed backorders.
     */
    public function getCompletedBackorders(array $filters = []): Collection
    {
        return $this->backorderRepository->getCompleted($filters);
    }

    /**
     * Get cancelled backorders.
     */
    public function getCancelledBackorders(array $filters = []): Collection
    {
        return $this->backorderRepository->getCancelled($filters);
    }

    /**
     * Get backorder statistics.
     */
    public function getBackorderStatistics(array $filters = []): array
    {
        try {
            $query = DB::table('backorders');
            
            // Apply filters
            if (isset($filters['company_id'])) {
                $query->where('company_id', $filters['company_id']);
            }
            
            if (isset($filters['unit_id'])) {
                $query->where('unit_id', $filters['unit_id']);
            }
            
            if (isset($filters['start_date']) && isset($filters['end_date'])) {
                $query->whereBetween('order_date', [$filters['start_date'], $filters['end_date']]);
            }
            
            // Get counts by status
            $statusCounts = $query->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get()
                ->pluck('count', 'status')
                ->toArray();
            
            // Get total amount
            $totalAmount = $query->sum('amount');
            
            // Get top products
            $topProducts = DB::table('backorders')
                ->select('product_id', 'product_name', DB::raw('count(*) as count'), DB::raw('sum(amount) as total_amount'))
                ->groupBy('product_id', 'product_name')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
                ->toArray();
            
            // Get top customers
            $topCustomers = DB::table('backorders')
                ->select('customer_id', DB::raw('count(*) as count'), DB::raw('sum(amount) as total_amount'))
                ->groupBy('customer_id')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
                ->toArray();
            
            return [
                'total_backorders' => array_sum($statusCounts),
                'pending_backorders' => $statusCounts['pending'] ?? 0,
                'completed_backorders' => $statusCounts['completed'] ?? 0,
                'cancelled_backorders' => $statusCounts['cancelled'] ?? 0,
                'total_amount' => $totalAmount,
                'top_products' => $topProducts,
                'top_customers' => $topCustomers
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get backorder statistics', [
                'error' => $e->getMessage(),
                'filters' => $filters
            ]);
            
            return [
                'total_backorders' => 0,
                'pending_backorders' => 0,
                'completed_backorders' => 0,
                'cancelled_backorders' => 0,
                'total_amount' => 0,
                'top_products' => [],
                'top_customers' => []
            ];
        }
    }
}

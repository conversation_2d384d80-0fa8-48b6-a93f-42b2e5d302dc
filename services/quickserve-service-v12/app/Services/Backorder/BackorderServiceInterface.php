<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Backorder;

use App\Models\Backorder;
use App\Models\Order;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface BackorderServiceInterface
{
    /**
     * Get all backorders.
     */
    public function getAllBackorders(array $filters = []): Collection;

    /**
     * Get paginated backorders.
     */
    public function getPaginatedBackorders(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get backorder by ID.
     */
    public function getBackorderById(int $id): ?Backorder;

    /**
     * Create a new backorder.
     */
    public function createBackorder(array $data): Backorder;

    /**
     * Create a backorder from an order.
     */
    public function createBackorderFromOrder(Order $order, string $reason): Backorder;

    /**
     * Update a backorder.
     */
    public function updateBackorder(int $id, array $data): bool;

    /**
     * Delete a backorder.
     */
    public function deleteBackorder(int $id): bool;

    /**
     * Complete a backorder.
     */
    public function completeBackorder(int $id): bool;

    /**
     * Cancel a backorder.
     */
    public function cancelBackorder(int $id, ?string $reason = null): bool;

    /**
     * Get backorders by order ID.
     */
    public function getBackordersByOrderId(int $orderId, array $filters = []): Collection;

    /**
     * Get backorders by customer ID.
     */
    public function getBackordersByCustomerId(int $customerId, array $filters = []): Collection;

    /**
     * Get backorders by product ID.
     */
    public function getBackordersByProductId(int $productId, array $filters = []): Collection;

    /**
     * Get backorders by status.
     */
    public function getBackordersByStatus(string $status, array $filters = []): Collection;

    /**
     * Get backorders by date range.
     */
    public function getBackordersByDateRange(string $startDate, string $endDate, array $filters = []): Collection;

    /**
     * Get pending backorders.
     */
    public function getPendingBackorders(array $filters = []): Collection;

    /**
     * Get completed backorders.
     */
    public function getCompletedBackorders(array $filters = []): Collection;

    /**
     * Get cancelled backorders.
     */
    public function getCancelledBackorders(array $filters = []): Collection;

    /**
     * Get backorder statistics.
     */
    public function getBackorderStatistics(array $filters = []): array;
}

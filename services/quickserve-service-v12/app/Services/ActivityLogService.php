<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivityLogService
{
    /**
     * Create a new service instance.
     *
     * @return void
     */
    public function __construct(protected \Illuminate\Http\Request $request)
    {
    }

    /**
     * Log an activity.
     */
    public function log(string $action, string $description, array $context = []): ActivityLog
    {
        $data = [
            'company_id' => $context['company_id'] ?? config('app.default_company_id', 1),
            'unit_id' => $context['unit_id'] ?? config('app.default_unit_id', 1),
            'context_ref_id' => $context['context_ref_id'] ?? null,
            'context_name' => $context['context_name'] ?? null,
            'context_type' => $context['context_type'] ?? null,
            'controller' => $context['controller'] ?? $this->getController(),
            'action' => $action,
            'description' => $description,
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent(),
            'modified_date' => now()
        ];

        return ActivityLog::create($data);
    }

    /**
     * Log an authentication activity.
     */
    public function logAuth(string $action, array $context = []): ActivityLog
    {
        $context['context_name'] = 'auth';
        $context['context_type'] = 'authentication';
        
        return $this->log($action, $context['message'] ?? "Authentication {$action}", $context);
    }

    /**
     * Log an order activity.
     */
    public function logOrder(string $action, int $orderId, string $description, array $context = []): ActivityLog
    {
        $context['context_name'] = 'order';
        $context['context_type'] = 'order';
        $context['context_ref_id'] = $orderId;
        
        return $this->log($action, $description, $context);
    }

    /**
     * Log a customer activity.
     */
    public function logCustomer(string $action, int $customerId, string $description, array $context = []): ActivityLog
    {
        $context['context_name'] = 'customer';
        $context['context_type'] = 'customer';
        $context['context_ref_id'] = $customerId;
        
        return $this->log($action, $description, $context);
    }

    /**
     * Log a product activity.
     */
    public function logProduct(string $action, int $productId, string $description, array $context = []): ActivityLog
    {
        $context['context_name'] = 'product';
        $context['context_type'] = 'product';
        $context['context_ref_id'] = $productId;
        
        return $this->log($action, $description, $context);
    }

    /**
     * Get the current controller name.
     */
    protected function getController(): ?string
    {
        $route = $this->request->route();
        if (!$route) {
            return null;
        }

        $action = $route->getAction();
        if (isset($action['controller'])) {
            $controller = class_basename($action['controller']);
            return str_replace(['Controller@', '@'], ['/', ''], $controller);
        }

        return null;
    }
}

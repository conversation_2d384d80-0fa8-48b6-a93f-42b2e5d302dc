<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services;

use App\Models\LocationMapping;
use Illuminate\Support\Collection;

class LocationMappingService
{
    /**
     * Get all location mappings.
     */
    public function getAllLocationMappings(array $filters = []): Collection
    {
        $query = LocationMapping::query();

        // Apply filters
        if (isset($filters['city_code'])) {
            $query->city($filters['city_code']);
        }

        if (isset($filters['kitchen_code'])) {
            $query->kitchen($filters['kitchen_code']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        } else {
            $query->active();
        }

        // Apply search
        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search): void {
                $q->where('location_name', 'like', "%{$search}%")
                  ->orWhere('city_name', 'like', "%{$search}%")
                  ->orWhere('kitchen_name', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'location_name';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->get();
    }

    /**
     * Get a location mapping by ID.
     */
    public function getLocationMapping(int $id): ?LocationMapping
    {
        return LocationMapping::find($id);
    }

    /**
     * Get a location mapping by location code.
     */
    public function getLocationMappingByCode(string $locationCode): ?LocationMapping
    {
        return LocationMapping::where('location_code', $locationCode)->first();
    }

    /**
     * Create a new location mapping.
     */
    public function createLocationMapping(array $data): LocationMapping
    {
        return LocationMapping::create($data);
    }

    /**
     * Update a location mapping.
     */
    public function updateLocationMapping(int $id, array $data): ?LocationMapping
    {
        $locationMapping = LocationMapping::find($id);
        
        if (!$locationMapping) {
            return null;
        }
        
        $locationMapping->update($data);
        
        return $locationMapping;
    }

    /**
     * Delete a location mapping.
     */
    public function deleteLocationMapping(int $id): bool
    {
        $locationMapping = LocationMapping::find($id);
        
        if (!$locationMapping) {
            return false;
        }
        
        return $locationMapping->delete();
    }

    /**
     * Get locations by city.
     */
    public function getLocationsByCity(string $cityCode): Collection
    {
        return LocationMapping::active()
            ->city($cityCode)
            ->orderBy('location_name')
            ->get();
    }

    /**
     * Get locations by kitchen.
     */
    public function getLocationsByKitchen(string $kitchenCode): Collection
    {
        return LocationMapping::active()
            ->kitchen($kitchenCode)
            ->orderBy('location_name')
            ->get();
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Payment;

use App\DTOs\Payment\PaymentRequestDTO;
use App\DTOs\Payment\PaymentResponseDTO;
use App\Exceptions\Payment\PaymentException;
use App\Services\Resilience\CircuitBreaker;
use App\Services\Resilience\Retry;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Payment Service Client
 *
 * This class provides a client for interacting with the Payment Service API.
 */
class PaymentServiceClient
{
    /**
     * The base URL for the Payment Service API.
     */
    protected string $baseUrl;

    /**
     * The circuit breaker instance.
     */
    protected CircuitBreaker $circuitBreaker;

    /**
     * The retry instance.
     */
    protected Retry $retry;

    /**
     * Create a new PaymentServiceClient instance.
     */
    public function __construct(
        ?string $baseUrl = null,
        /**
         * The timeout for API requests in seconds.
         */
        protected int $timeout = 30,
        /**
         * The retry attempts for API requests.
         */
        protected int $retries = 3
    ) {
        $this->baseUrl = $baseUrl ?? config('services.payment.url', 'http://payment-service-v12:8000/api');

        // Initialize circuit breaker and retry
        $this->circuitBreaker = new CircuitBreaker('payment-service', 5, 60);
        $this->retry = new Retry('payment-service', $this->retries, 100, 5000);
    }

    /**
     * Initiate a payment.
     *
     * @throws PaymentException
     */
    public function initiatePayment(PaymentRequestDTO $paymentRequest): PaymentResponseDTO
    {
        try {
            return $this->circuitBreaker->execute(
                fn() => $this->retry->execute(
                    function () use ($paymentRequest): \App\DTOs\Payment\PaymentResponseDTO {
                        $response = $this->http()
                            ->post('/payments/initiate', $paymentRequest->toArray());

                        if ($response->successful()) {
                            $data = $response->json('data');
                            return new PaymentResponseDTO(
                                $data['transaction_id'],
                                $data['amount'],
                                $data['status']
                            );
                        }

                        throw new PaymentException($response->json('message') ?? 'Payment initiation failed');
                    },
                    fn(\Exception $e): bool =>
                        // Only retry on network errors or 5xx responses
                        !($e instanceof PaymentException) ||
                           str_contains($e->getMessage(), '5')
                ),
                function () use ($paymentRequest): void {
                    // Fallback when circuit is open
                    Log::warning('Payment service circuit open, using fallback for payment initiation', [
                        'payment_request' => $paymentRequest->toArray()
                    ]);

                    // In a real implementation, we might queue the payment request for later processing
                    // For now, we'll throw an exception
                    throw new PaymentException('Payment service is currently unavailable');
                }
            );
        } catch (\Exception $e) {
            Log::error('Payment service initiation failed', [
                'error' => $e->getMessage(),
                'payment_request' => $paymentRequest->toArray()
            ]);

            throw new PaymentException('Payment initiation failed: ' . $e->getMessage());
        }
    }

    /**
     * Process a payment.
     *
     * @throws PaymentException
     */
    public function processPayment(string $transactionId, string $gateway): array
    {
        try {
            $response = $this->http()
                ->post("/payments/{$transactionId}/process", [
                    'gateway' => $gateway
                ]);

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new PaymentException($response->json('message') ?? 'Payment processing failed');
        } catch (\Exception $e) {
            Log::error('Payment service processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'gateway' => $gateway
            ]);

            throw new PaymentException('Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Get payment status.
     *
     * @throws PaymentException
     */
    public function getPaymentStatus(string $transactionId): array
    {
        try {
            $response = $this->http()
                ->get("/payments/{$transactionId}");

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new PaymentException($response->json('message') ?? 'Failed to get payment status');
        } catch (\Exception $e) {
            Log::error('Payment service status check failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);

            throw new PaymentException('Failed to get payment status: ' . $e->getMessage());
        }
    }

    /**
     * Ping the payment service to check if it's available.
     */
    public function ping(): bool
    {
        try {
            $response = $this->http()
                ->get('/health');

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Payment service ping failed', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create an HTTP client instance.
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name'),
                'X-Service-Version' => config('app.version', '1.0.0'),
                'X-Correlation-ID' => \App\Services\Logging\LoggingService::getCorrelationId(),
            ]);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Resilience;

use Closure;
use Exception;
use Illuminate\Support\Facades\Log;

class Retry
{
    /**
     * Create a new retry instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The name of the retry operation.
         */
        protected string $name,
        /**
         * The maximum number of retry attempts.
         */
        protected int $maxAttempts = 3,
        /**
         * The base delay in milliseconds.
         */
        protected int $baseDelayMs = 100,
        /**
         * The maximum delay in milliseconds.
         */
        protected int $maxDelayMs = 5000
    )
    {
    }

    /**
     * Execute the given callback with retry logic.
     *
     * @param Closure|null $shouldRetry
     * @return mixed
     * @throws Exception
     */
    public function execute(Closure $callback, Closure $shouldRetry = null)
    {
        $attempt = 1;
        $shouldRetry = $shouldRetry ?: (fn(Exception $e): true => true);

        while (true) {
            try {
                return $callback();
            } catch (Exception $e) {
                if ($attempt >= $this->maxAttempts || !$shouldRetry($e)) {
                    throw $e;
                }

                $delay = $this->calculateDelay($attempt);
                
                Log::warning("Retry {$this->name} attempt {$attempt}/{$this->maxAttempts} failed, retrying in {$delay}ms", [
                    'exception' => $e->getMessage(),
                ]);
                
                usleep($delay * 1000); // Convert to microseconds
                $attempt++;
            }
        }
    }

    /**
     * Calculate the delay for the current attempt using exponential backoff.
     */
    protected function calculateDelay(int $attempt): int
    {
        // Exponential backoff with jitter
        $delay = min($this->maxDelayMs, $this->baseDelayMs * 2 ** ($attempt - 1));
        
        // Add jitter (random value between 0 and 100ms)
        $jitter = mt_rand(0, 100);
        
        return $delay + $jitter;
    }
}

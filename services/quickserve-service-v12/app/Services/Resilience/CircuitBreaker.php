<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Resilience;

use Closure;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CircuitBreaker
{
    /**
     * Create a new circuit breaker instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The name of the circuit breaker.
         */
        protected string $name,
        /**
         * The number of failures before opening the circuit.
         */
        protected int $threshold = 5,
        /**
         * The time in seconds to wait before trying again.
         */
        protected int $timeout = 60
    )
    {
    }

    /**
     * Execute the given callback with circuit breaker protection.
     *
     * @param Closure|null $fallback
     * @return mixed
     * @throws Exception
     */
    public function execute(Closure $callback, Closure $fallback = null)
    {
        if ($this->isOpen()) {
            Log::warning("Circuit breaker {$this->name} is open, request rejected");

            if ($fallback instanceof \Closure) {
                return $fallback();
            }

            throw new Exception("Circuit breaker {$this->name} is open");
        }

        try {
            $result = $callback();
            $this->recordSuccess();
            return $result;
        } catch (Exception $e) {
            $this->recordFailure();
            
            if ($fallback instanceof \Closure) {
                return $fallback();
            }
            
            throw $e;
        }
    }

    /**
     * Check if the circuit is open.
     */
    protected function isOpen(): bool
    {
        $lastFailureTime = Cache::get("{$this->name}.last_failure_time");
        $failureCount = Cache::get("{$this->name}.failure_count", 0);

        // If we've reached the threshold and we're still within the timeout period
        if ($failureCount >= $this->threshold && $lastFailureTime) {
            $elapsedTime = time() - $lastFailureTime;
            
            if ($elapsedTime < $this->timeout) {
                return true;
            }
            
            // Timeout has passed, reset the circuit
            $this->reset();
        }

        return false;
    }

    /**
     * Record a successful execution.
     */
    protected function recordSuccess(): void
    {
        Cache::put("{$this->name}.failure_count", 0);
    }

    /**
     * Record a failed execution.
     */
    protected function recordFailure(): void
    {
        $failureCount = Cache::get("{$this->name}.failure_count", 0) + 1;
        Cache::put("{$this->name}.failure_count", $failureCount);
        Cache::put("{$this->name}.last_failure_time", time());

        Log::warning("Circuit breaker {$this->name} recorded failure {$failureCount}/{$this->threshold}");
    }

    /**
     * Reset the circuit breaker.
     */
    public function reset(): void
    {
        Cache::put("{$this->name}.failure_count", 0);
        Cache::forget("{$this->name}.last_failure_time");

        Log::info("Circuit breaker {$this->name} has been reset");
    }
}

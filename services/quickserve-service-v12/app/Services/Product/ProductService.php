<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Product;

use App\Events\Product\ProductCreated;
use App\Events\Product\ProductDeleted;
use App\Events\Product\ProductUpdated;
use App\Exceptions\ProductException;
use App\Models\Product;
use App\Repositories\Product\ProductRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductService implements ProductServiceInterface
{
    /**
     * ProductService constructor.
     */
    public function __construct(protected \App\Repositories\Product\ProductRepositoryInterface $productRepository)
    {
    }

    /**
     * Get all products.
     */
    public function getAllProducts(array $filters = []): Collection
    {
        return $this->productRepository->all($filters);
    }

    /**
     * Get paginated products.
     */
    public function getPaginatedProducts(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->productRepository->paginate($perPage, $filters);
    }

    /**
     * Get product by ID.
     */
    public function getProductById(int $id): ?Product
    {
        return $this->productRepository->findById($id);
    }

    /**
     * Create a new product.
     *
     * @throws ProductException
     */
    public function createProduct(array $data, ?UploadedFile $image = null): Product
    {
        try {
            // Upload image if provided
            if ($image instanceof \Illuminate\Http\UploadedFile) {
                $data['image_path'] = $this->uploadProductImage($image);
            }

            // Set default values if not provided
            if (!isset($data['status'])) {
                $data['status'] = true;
            }

            if (!isset($data['sequence'])) {
                $data['sequence'] = $this->getNextSequence($data['product_type'] ?? null);
            }

            // Create product
            $product = $this->productRepository->create($data);

            // Dispatch event
            Event::dispatch(new ProductCreated($product));

            return $product;
        } catch (\Exception $e) {
            Log::error('Failed to create product', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new ProductException('Failed to create product: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Update a product.
     */
    public function updateProduct(int $id, array $data, ?UploadedFile $image = null): bool
    {
        try {
            $product = $this->getProductById($id);

            if (!$product instanceof \App\Models\Product) {
                return false;
            }

            // Upload image if provided
            if ($image instanceof \Illuminate\Http\UploadedFile) {
                // Delete old image if exists
                if ($product->image_path) {
                    $this->deleteProductImage($product->image_path);
                }

                $data['image_path'] = $this->uploadProductImage($image);
            }

            // Update product
            $updated = $this->productRepository->update($id, $data);

            if ($updated) {
                // Dispatch event
                Event::dispatch(new ProductUpdated($product));
            }

            return $updated;
        } catch (\Exception $e) {
            Log::error('Failed to update product', [
                'error' => $e->getMessage(),
                'product_id' => $id,
                'data' => $data
            ]);

            return false;
        }
    }

    /**
     * Delete a product.
     */
    public function deleteProduct(int $id): bool
    {
        try {
            $product = $this->getProductById($id);

            if (!$product instanceof \App\Models\Product) {
                return false;
            }

            // Delete product image if exists
            if ($product->image_path) {
                $this->deleteProductImage($product->image_path);
            }

            // Delete product
            $deleted = $this->productRepository->delete($id);

            if ($deleted) {
                // Dispatch event
                Event::dispatch(new ProductDeleted($product));
            }

            return $deleted;
        } catch (\Exception $e) {
            Log::error('Failed to delete product', [
                'error' => $e->getMessage(),
                'product_id' => $id
            ]);

            return false;
        }
    }

    /**
     * Get products by type.
     */
    public function getProductsByType(string $type, array $filters = []): Collection
    {
        return $this->productRepository->getByType($type, $filters);
    }

    /**
     * Get products by food type.
     */
    public function getProductsByFoodType(string $foodType, array $filters = []): Collection
    {
        return $this->productRepository->getByFoodType($foodType, $filters);
    }

    /**
     * Get products by kitchen ID.
     */
    public function getProductsByKitchenId(int $kitchenId, array $filters = []): Collection
    {
        return $this->productRepository->getByKitchenId($kitchenId, $filters);
    }

    /**
     * Get products by category.
     */
    public function getProductsByCategory(string $category, array $filters = []): Collection
    {
        return $this->productRepository->getByCategory($category, $filters);
    }

    /**
     * Search products.
     */
    public function searchProducts(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->productRepository->search($query, $perPage);
    }

    /**
     * Get active products.
     */
    public function getActiveProducts(array $filters = []): Collection
    {
        return $this->productRepository->getActive($filters);
    }

    /**
     * Update product sequence.
     */
    public function updateProductSequence(array $productIds): bool
    {
        return $this->productRepository->updateSequence($productIds);
    }

    /**
     * Upload product image.
     */
    public function uploadProductImage(UploadedFile $image): string
    {
        $filename = Str::random(40) . '.' . $image->getClientOriginalExtension();
        $path = 'products/' . $filename;

        // Handle both real files and test files
        if ($image instanceof \Illuminate\Http\Testing\File) {
            Storage::disk('public')->putFileAs('products', $image, $filename);
        } else {
            Storage::disk('public')->put($path, file_get_contents($image));
        }

        return $path;
    }

    /**
     * Delete product image.
     */
    protected function deleteProductImage(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }

        return false;
    }

    /**
     * Get next sequence number for a product type.
     */
    protected function getNextSequence(?string $productType): int
    {
        $filters = [];

        if ($productType) {
            $filters['product_type'] = $productType;
        }

        $maxSequence = Product::where($filters)->max('sequence');

        return $maxSequence ? $maxSequence + 1 : 1;
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Product;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;

interface ProductServiceInterface
{
    /**
     * Get all products.
     */
    public function getAllProducts(array $filters = []): Collection;

    /**
     * Get paginated products.
     */
    public function getPaginatedProducts(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get product by ID.
     */
    public function getProductById(int $id): ?Product;

    /**
     * Create a new product.
     */
    public function createProduct(array $data, ?UploadedFile $image = null): Product;

    /**
     * Update a product.
     */
    public function updateProduct(int $id, array $data, ?UploadedFile $image = null): bool;

    /**
     * Delete a product.
     */
    public function deleteProduct(int $id): bool;

    /**
     * Get products by type.
     */
    public function getProductsByType(string $type, array $filters = []): Collection;

    /**
     * Get products by food type.
     */
    public function getProductsByFoodType(string $foodType, array $filters = []): Collection;

    /**
     * Get products by kitchen ID.
     */
    public function getProductsByKitchenId(int $kitchenId, array $filters = []): Collection;

    /**
     * Get products by category.
     */
    public function getProductsByCategory(string $category, array $filters = []): Collection;

    /**
     * Search products.
     */
    public function searchProducts(string $query, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get active products.
     */
    public function getActiveProducts(array $filters = []): Collection;

    /**
     * Update product sequence.
     */
    public function updateProductSequence(array $productIds): bool;

    /**
     * Upload product image.
     */
    public function uploadProductImage(UploadedFile $image): string;
}

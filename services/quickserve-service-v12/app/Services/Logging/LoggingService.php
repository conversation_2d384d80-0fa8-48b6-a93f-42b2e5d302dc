<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Services\Logging;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LoggingService
{
    /**
     * The correlation ID for the current request.
     *
     * @var string
     */
    protected static $correlationId;

    /**
     * Get the correlation ID for the current request.
     */
    public static function getCorrelationId(): string
    {
        if (!static::$correlationId) {
            // Try to get from request header
            $correlationId = request()->header('X-Correlation-ID');
            
            // If not found, generate a new one
            if (!$correlationId) {
                $correlationId = (string) Str::uuid();
            }
            
            static::$correlationId = $correlationId;
        }
        
        return static::$correlationId;
    }

    /**
     * Set the correlation ID for the current request.
     */
    public static function setCorrelationId(string $correlationId): void
    {
        static::$correlationId = $correlationId;
    }

    /**
     * Log an info message.
     */
    public static function info(string $message, array $context = []): void
    {
        static::log('info', $message, $context);
    }

    /**
     * Log a warning message.
     */
    public static function warning(string $message, array $context = []): void
    {
        static::log('warning', $message, $context);
    }

    /**
     * Log an error message.
     */
    public static function error(string $message, array $context = []): void
    {
        static::log('error', $message, $context);
    }

    /**
     * Log a debug message.
     */
    public static function debug(string $message, array $context = []): void
    {
        static::log('debug', $message, $context);
    }

    /**
     * Log a message with the given level.
     */
    protected static function log(string $level, string $message, array $context = []): void
    {
        // Add correlation ID to context
        $context['correlation_id'] = static::getCorrelationId();
        
        // Add service information
        $context['service'] = config('app.name');
        $context['service_version'] = config('app.version', '1.0.0');
        
        // Add request information if available
        if (request()) {
            $context['request_id'] = request()->id ?? null;
            $context['request_path'] = request()->path();
            $context['request_method'] = request()->method();
            $context['request_ip'] = request()->ip();
            $context['request_user_agent'] = request()->userAgent();
        }
        
        // Log the message
        Log::$level($message, $context);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Services\Payment\PaymentServiceClient;
use Illuminate\Support\ServiceProvider;

/**
 * Payment Service Provider
 * 
 * This service provider registers the Payment Service integration.
 */
class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Payment Service Client
        $this->app->singleton(PaymentServiceClient::class, fn($app): \App\Services\Payment\PaymentServiceClient => new PaymentServiceClient(
            config('services.payment.url'),
            config('services.payment.timeout', 30),
            config('services.payment.retries', 3)
        ));
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Services\CircuitBreaker\CircuitBreakerInterface;
use App\Services\Http\HttpClient;
use App\Services\Http\HttpClientInterface;
use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;

class HttpClientServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(HttpClientInterface::class, function (array $app): \App\Services\Http\HttpClient {
            $config = $app['config']->get('http_client', []);
            
            $client = new Client([
                'timeout' => $config['timeout'] ?? 30,
                'connect_timeout' => $config['connect_timeout'] ?? 10,
                'http_errors' => $config['http_errors'] ?? true,
                'verify' => $config['verify'] ?? true,
            ]);
            
            $circuitBreaker = $app->make(CircuitBreakerInterface::class);
            
            return new HttpClient($client, $circuitBreaker);
        });
        
        // Register named HTTP clients for different services
        $this->registerNamedHttpClients();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->publishes([
            __DIR__ . '/../../config/http_client.php' => config_path('http_client.php'),
        ], 'config');
    }
    
    /**
     * Register named HTTP clients for different services.
     */
    protected function registerNamedHttpClients(): void
    {
        $services = [
            'auth',
            'payment',
            'customer',
            'kitchen',
            'delivery',
        ];
        
        foreach ($services as $name) {
            $this->app->bind("http_client.{$name}", function (array $app) use ($name): \App\Services\Http\HttpClient {
                $config = $app['config']->get('http_client', []);
                
                $client = new Client([
                    'timeout' => $config['timeout'] ?? 30,
                    'connect_timeout' => $config['connect_timeout'] ?? 10,
                    'http_errors' => $config['http_errors'] ?? true,
                    'verify' => $config['verify'] ?? true,
                ]);
                
                $circuitBreaker = $app->make("circuit_breaker.{$name}");
                
                return new HttpClient($client, $circuitBreaker);
            });
        }
    }
}

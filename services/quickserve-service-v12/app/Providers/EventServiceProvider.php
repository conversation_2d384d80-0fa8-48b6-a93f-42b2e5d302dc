<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Events\Backorder\BackorderCancelled;
use App\Events\Backorder\BackorderCompleted;
use App\Events\Backorder\BackorderCreated;
use App\Events\Backorder\BackorderDeleted;
use App\Events\Backorder\BackorderUpdated;
use App\Events\LocationMapping\LocationMappingCreated;
use App\Events\LocationMapping\LocationMappingDeleted;
use App\Events\LocationMapping\LocationMappingUpdated;
use App\Events\OrderCancelled;
use App\Events\OrderCreated;
use App\Events\OrderStatusUpdated;
use App\Events\Timeslot\TimeslotCreated;
use App\Events\Timeslot\TimeslotDeleted;
use App\Events\Timeslot\TimeslotUpdated;
use App\Listeners\NotifyCustomerAboutOrderCancellation;
use App\Listeners\NotifyCustomerAboutOrderCreation;
use App\Listeners\NotifyCustomerAboutOrderStatusUpdate;
use App\Listeners\NotifyKitchenAboutNewOrder;
use App\Listeners\PublishBackorderCancelledEvent;
use App\Listeners\PublishBackorderCompletedEvent;
use App\Listeners\PublishBackorderCreatedEvent;
use App\Listeners\PublishBackorderDeletedEvent;
use App\Listeners\PublishBackorderUpdatedEvent;
use App\Listeners\PublishLocationMappingCreatedEvent;
use App\Listeners\PublishLocationMappingDeletedEvent;
use App\Listeners\PublishLocationMappingUpdatedEvent;
use App\Listeners\PublishOrderCancelledEvent;
use App\Listeners\PublishOrderCreatedEvent;
use App\Listeners\PublishOrderStatusUpdatedEvent;
use App\Listeners\PublishTimeslotCreatedEvent;
use App\Listeners\PublishTimeslotDeletedEvent;
use App\Listeners\PublishTimeslotUpdatedEvent;
use App\Listeners\UpdateInventoryAfterOrderCancellation;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        OrderCreated::class => [
            NotifyCustomerAboutOrderCreation::class,
            NotifyKitchenAboutNewOrder::class,
            PublishOrderCreatedEvent::class,
        ],
        OrderStatusUpdated::class => [
            NotifyCustomerAboutOrderStatusUpdate::class,
            PublishOrderStatusUpdatedEvent::class,
        ],
        OrderCancelled::class => [
            NotifyCustomerAboutOrderCancellation::class,
            UpdateInventoryAfterOrderCancellation::class,
            PublishOrderCancelledEvent::class,
        ],

        // Timeslot events
        TimeslotCreated::class => [
            PublishTimeslotCreatedEvent::class,
        ],
        TimeslotUpdated::class => [
            PublishTimeslotUpdatedEvent::class,
        ],
        TimeslotDeleted::class => [
            PublishTimeslotDeletedEvent::class,
        ],

        // LocationMapping events
        LocationMappingCreated::class => [
            PublishLocationMappingCreatedEvent::class,
        ],
        LocationMappingUpdated::class => [
            PublishLocationMappingUpdatedEvent::class,
        ],
        LocationMappingDeleted::class => [
            PublishLocationMappingDeletedEvent::class,
        ],

        // Backorder events
        BackorderCreated::class => [
            PublishBackorderCreatedEvent::class,
        ],
        BackorderUpdated::class => [
            PublishBackorderUpdatedEvent::class,
        ],
        BackorderDeleted::class => [
            PublishBackorderDeletedEvent::class,
        ],
        BackorderCompleted::class => [
            PublishBackorderCompletedEvent::class,
        ],
        BackorderCancelled::class => [
            PublishBackorderCancelledEvent::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}

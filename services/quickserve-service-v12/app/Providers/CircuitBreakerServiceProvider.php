<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Services\CircuitBreaker\CircuitBreaker;
use App\Services\CircuitBreaker\CircuitBreakerInterface;
use Illuminate\Support\ServiceProvider;

class CircuitBreakerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(CircuitBreakerInterface::class, function (array $app): \App\Services\CircuitBreaker\CircuitBreaker {
            $config = $app['config']->get('circuit_breaker', []);
            
            return new CircuitBreaker(
                $config['service_name'] ?? 'default',
                $config['failure_threshold'] ?? 5,
                $config['success_threshold'] ?? 2,
                $config['timeout_seconds'] ?? 30,
                $config['cache_prefix'] ?? 'circuit_breaker:'
            );
        });
        
        // Register named circuit breakers for different services
        $this->registerNamedCircuitBreakers();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->publishes([
            __DIR__ . '/../../config/circuit_breaker.php' => config_path('circuit_breaker.php'),
        ], 'config');
    }
    
    /**
     * Register named circuit breakers for different services.
     */
    protected function registerNamedCircuitBreakers(): void
    {
        $services = [
            'auth' => [
                'failure_threshold' => 3,
                'timeout_seconds' => 60,
            ],
            'payment' => [
                'failure_threshold' => 2,
                'timeout_seconds' => 120,
            ],
            'customer' => [
                'failure_threshold' => 3,
                'timeout_seconds' => 60,
            ],
            'kitchen' => [
                'failure_threshold' => 5,
                'timeout_seconds' => 30,
            ],
            'delivery' => [
                'failure_threshold' => 5,
                'timeout_seconds' => 30,
            ],
        ];
        
        foreach ($services as $name => $settings) {
            $this->app->bind("circuit_breaker.{$name}", function (array $app) use ($name, $settings): \App\Services\CircuitBreaker\CircuitBreaker {
                $config = $app['config']->get('circuit_breaker', []);
                
                return new CircuitBreaker(
                    $name,
                    $settings['failure_threshold'] ?? $config['failure_threshold'] ?? 5,
                    $settings['success_threshold'] ?? $config['success_threshold'] ?? 2,
                    $settings['timeout_seconds'] ?? $config['timeout_seconds'] ?? 30,
                    $config['cache_prefix'] ?? 'circuit_breaker:'
                );
            });
        }
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Services\RabbitMQ\RabbitMQService;
use App\Services\RabbitMQ\RabbitMQServiceInterface;
use App\Services\RabbitMQ\MockRabbitMQService;
use Illuminate\Support\ServiceProvider;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class RabbitMQServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Check if RabbitMQ is enabled
        $enabled = env('RABBITMQ_ENABLED', true);

        if ($enabled) {
            // Register real RabbitMQ services
            $this->app->singleton(AMQPStreamConnection::class, function (array $app): \PhpAmqpLib\Connection\AMQPStreamConnection {
                $config = $app['config']->get('rabbitmq.connection');

                return new AMQPStreamConnection(
                    $config['host'],
                    $config['port'],
                    $config['user'],
                    $config['password'],
                    $config['vhost'],
                    false,
                    'AMQPLAIN',
                    null,
                    'en_US',
                    3.0,
                    3.0,
                    null,
                    $config['ssl']['enabled'] ? $this->getSslOptions($config['ssl']) : []
                );
            });

            $this->app->singleton(RabbitMQService::class, fn($app): \App\Services\RabbitMQ\RabbitMQService => new RabbitMQService(
                $app->make(AMQPStreamConnection::class),
                $app['config']->get('rabbitmq')
            ));

            $this->app->bind(RabbitMQServiceInterface::class, RabbitMQService::class);
        } else {
            // Register mock services for testing
            $this->app->singleton(AMQPStreamConnection::class, fn(): object => new class {
                public function channel(): object { return new class { public function __call($name, $args) { return null; } }; }
                public function close(): null { return null; }
            });

            $this->app->singleton(RabbitMQService::class, fn(): \App\Services\RabbitMQ\MockRabbitMQService => new MockRabbitMQService());

            $this->app->bind(RabbitMQServiceInterface::class, MockRabbitMQService::class);
        }
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Get SSL options for RabbitMQ connection.
     */
    protected function getSslOptions(array $config): array
    {
        $options = [
            'verify_peer' => $config['verify_peer'],
        ];

        if ($config['cafile']) {
            $options['cafile'] = $config['cafile'];
        }

        if ($config['local_cert']) {
            $options['local_cert'] = $config['local_cert'];
        }

        if ($config['local_key']) {
            $options['local_key'] = $config['local_key'];
        }

        if ($config['passphrase']) {
            $options['passphrase'] = $config['passphrase'];
        }

        return $options;
    }
}

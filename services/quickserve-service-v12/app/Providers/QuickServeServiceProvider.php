<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Repositories\Backorder\BackorderRepository;
use App\Repositories\Backorder\BackorderRepositoryInterface;
use App\Repositories\LocationMapping\LocationMappingRepository;
use App\Repositories\LocationMapping\LocationMappingRepositoryInterface;
use App\Repositories\Order\OrderRepository;
use App\Repositories\Order\OrderRepositoryInterface;
use App\Repositories\Product\ProductRepository;
use App\Repositories\Product\ProductRepositoryInterface;
use App\Repositories\Timeslot\TimeslotRepository;
use App\Repositories\Timeslot\TimeslotRepositoryInterface;
use App\Services\Backorder\BackorderService;
use App\Services\Backorder\BackorderServiceInterface;
use App\Services\LocationMapping\LocationMappingService;
use App\Services\LocationMapping\LocationMappingServiceInterface;
use App\Services\Order\OrderService;
use App\Services\Order\OrderServiceInterface;
use App\Services\Product\ProductService;
use App\Services\Product\ProductServiceInterface;
use App\Services\Timeslot\TimeslotService;
use App\Services\Timeslot\TimeslotServiceInterface;
use Illuminate\Support\ServiceProvider;

class QuickServeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register repositories
        $this->app->bind(OrderRepositoryInterface::class, OrderRepository::class);
        $this->app->bind(ProductRepositoryInterface::class, ProductRepository::class);
        $this->app->bind(TimeslotRepositoryInterface::class, TimeslotRepository::class);
        $this->app->bind(LocationMappingRepositoryInterface::class, LocationMappingRepository::class);
        $this->app->bind(BackorderRepositoryInterface::class, BackorderRepository::class);

        // Register services
        $this->app->bind(OrderServiceInterface::class, OrderService::class);
        $this->app->bind(ProductServiceInterface::class, ProductService::class);
        $this->app->bind(TimeslotServiceInterface::class, TimeslotService::class);
        $this->app->bind(LocationMappingServiceInterface::class, LocationMappingService::class);
        $this->app->bind(BackorderServiceInterface::class, BackorderService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

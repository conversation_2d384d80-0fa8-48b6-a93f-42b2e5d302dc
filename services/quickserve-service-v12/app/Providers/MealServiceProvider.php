<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Services\Meal\MealServiceClient;
use Illuminate\Support\ServiceProvider;

/**
 * Meal Service Provider
 * 
 * This service provider registers the Meal Service integration.
 */
class MealServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Meal Service Client
        $this->app->singleton(MealServiceClient::class, fn($app): \App\Services\Meal\MealServiceClient => new MealServiceClient(
            config('services.meal.url'),
            config('services.meal.timeout', 30),
            config('services.meal.retries', 3)
        ));
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

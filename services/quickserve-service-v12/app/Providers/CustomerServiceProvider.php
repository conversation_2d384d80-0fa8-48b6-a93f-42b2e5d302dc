<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Providers;

use App\Services\Customer\CustomerServiceClient;
use Illuminate\Support\ServiceProvider;

/**
 * Customer Service Provider
 * 
 * This service provider registers the Customer Service integration.
 */
class CustomerServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Customer Service Client
        $this->app->singleton(CustomerServiceClient::class, fn($app): \App\Services\Customer\CustomerServiceClient => new CustomerServiceClient(
            config('services.customer.url'),
            config('services.customer.timeout', 30),
            config('services.customer.retries', 3)
        ));
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomerRepository
{
    /**
     * Create a new repository instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The customer model instance.
         */
        protected \App\Models\Customer $model
    )
    {
    }

    /**
     * Get all customers with optional filtering.
     */
    public function getAllCustomers(array $params = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        
        // Apply filters
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        if (isset($params['city'])) {
            $query->where('city', $params['city']);
        }
        
        if (isset($params['group_code'])) {
            $query->where('group_code', $params['group_code']);
        }
        
        if (isset($params['food_preference'])) {
            $query->where('food_preference', $params['food_preference']);
        }
        
        if (isset($params['phone_verified'])) {
            $query->where('phone_verified', $params['phone_verified']);
        }
        
        if (isset($params['email_verified'])) {
            $query->where('email_verified', $params['email_verified']);
        }
        
        if (isset($params['search'])) {
            $query->where(function ($q) use ($params): void {
                $q->where('customer_name', 'like', '%' . $params['search'] . '%')
                  ->orWhere('phone', 'like', '%' . $params['search'] . '%')
                  ->orWhere('email_address', 'like', '%' . $params['search'] . '%');
            });
        }
        
        // Apply sorting
        $sortField = $params['sort_field'] ?? 'pk_customer_code';
        $sortDirection = $params['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($params['per_page'])) {
            return $query->paginate($params['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Find customer by ID.
     */
    public function findById(int $id): ?Customer
    {
        return $this->model->find($id);
    }

    /**
     * Create a new customer.
     */
    public function create(array $data): Customer
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing customer.
     */
    public function update(Customer $customer, array $data): Customer
    {
        $customer->update($data);
        return $customer->fresh();
    }

    /**
     * Delete a customer.
     */
    public function delete(int $id): bool
    {
        $customer = $this->findById($id);
        
        if (!$customer instanceof \App\Models\Customer) {
            return false;
        }
        
        return $customer->delete();
    }

    /**
     * Get customer by phone.
     */
    public function getCustomerByPhone(string $phone): ?Customer
    {
        return $this->model->where('phone', $phone)->first();
    }

    /**
     * Get customer by email.
     */
    public function getCustomerByEmail(string $email): ?Customer
    {
        return $this->model->where('email_address', $email)->first();
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\LocationMapping;

use App\Models\LocationMapping;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class LocationMappingRepository implements LocationMappingRepositoryInterface
{
    /**
     * LocationMappingRepository constructor.
     */
    public function __construct(protected \App\Models\LocationMapping $model)
    {
    }

    /**
     * Get all location mappings.
     */
    public function all(array $filters = []): Collection
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'id';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get paginated location mappings.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'id';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->paginate($perPage);
    }

    /**
     * Find location mapping by ID.
     */
    public function findById(int $id): ?LocationMapping
    {
        return $this->model->find($id);
    }

    /**
     * Create a new location mapping.
     */
    public function create(array $data): LocationMapping
    {
        return $this->model->create($data);
    }

    /**
     * Update a location mapping.
     */
    public function update(int $id, array $data): bool
    {
        $locationMapping = $this->findById($id);
        
        if (!$locationMapping instanceof \App\Models\LocationMapping) {
            return false;
        }
        
        return $locationMapping->update($data);
    }

    /**
     * Delete a location mapping.
     */
    public function delete(int $id): bool
    {
        $locationMapping = $this->findById($id);
        
        if (!$locationMapping instanceof \App\Models\LocationMapping) {
            return false;
        }
        
        return $locationMapping->delete();
    }

    /**
     * Get location mappings by kitchen ID.
     */
    public function getByKitchenId(int $kitchenId, array $filters = []): Collection
    {
        $query = $this->model->where('kitchen_id', $kitchenId);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'id';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get location mappings by location code.
     */
    public function getByLocationCode(string $locationCode, array $filters = []): Collection
    {
        $query = $this->model->where('location_code', $locationCode);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'id';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get location mappings by city.
     */
    public function getByCity(string $city, array $filters = []): Collection
    {
        $query = $this->model->where('city', $city);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'id';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get active location mappings.
     */
    public function getActive(array $filters = []): Collection
    {
        $query = $this->model->where('status', true);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'id';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Find kitchen for a location.
     */
    public function findKitchenForLocation(string $locationCode, string $city): ?LocationMapping
    {
        return $this->model->where('location_code', $locationCode)
            ->where('city', $city)
            ->where('status', true)
            ->first();
    }

    /**
     * Find locations for a kitchen.
     */
    public function findLocationsForKitchen(int $kitchenId, string $city): Collection
    {
        return $this->model->where('kitchen_id', $kitchenId)
            ->where('city', $city)
            ->where('status', true)
            ->get();
    }

    /**
     * Apply filters to the query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['kitchen_id'])) {
            $query->where('kitchen_id', $filters['kitchen_id']);
        }
        
        if (isset($filters['location_code'])) {
            $query->where('location_code', $filters['location_code']);
        }
        
        if (isset($filters['city'])) {
            $query->where('city', $filters['city']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        return $query;
    }
}

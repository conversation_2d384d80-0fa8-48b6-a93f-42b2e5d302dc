<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\LocationMapping;

use App\Models\LocationMapping;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface LocationMappingRepositoryInterface
{
    /**
     * Get all location mappings.
     */
    public function all(array $filters = []): Collection;

    /**
     * Get paginated location mappings.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Find location mapping by ID.
     */
    public function findById(int $id): ?LocationMapping;

    /**
     * Create a new location mapping.
     */
    public function create(array $data): LocationMapping;

    /**
     * Update a location mapping.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a location mapping.
     */
    public function delete(int $id): bool;

    /**
     * Get location mappings by kitchen ID.
     */
    public function getByKitchenId(int $kitchenId, array $filters = []): Collection;

    /**
     * Get location mappings by location code.
     */
    public function getByLocationCode(string $locationCode, array $filters = []): Collection;

    /**
     * Get location mappings by city.
     */
    public function getByCity(string $city, array $filters = []): Collection;

    /**
     * Get active location mappings.
     */
    public function getActive(array $filters = []): Collection;

    /**
     * Find kitchen for a location.
     */
    public function findKitchenForLocation(string $locationCode, string $city): ?LocationMapping;

    /**
     * Find locations for a kitchen.
     */
    public function findLocationsForKitchen(int $kitchenId, string $city): Collection;
}

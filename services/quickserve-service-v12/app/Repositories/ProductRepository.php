<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductRepository
{
    /**
     * Create a new repository instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The product model instance.
         */
        protected \App\Models\Product $model
    )
    {
    }

    /**
     * Get all products with optional filtering.
     */
    public function getAllProducts(array $params = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        // Apply filters
        if (isset($params['product_type'])) {
            $query->where('product_type', $params['product_type']);
        }

        if (isset($params['food_type'])) {
            $query->where('food_type', $params['food_type']);
        }

        if (isset($params['screen'])) {
            $query->where('screen', $params['screen']);
        }

        if (isset($params['product_category'])) {
            $query->where('product_category', $params['product_category']);
        }

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        } else {
            $query->where('status', 1); // Default to active products
        }

        if (isset($params['is_swappable'])) {
            $query->where('is_swappable', $params['is_swappable']);
        }

        if (isset($params['search'])) {
            $query->where(function ($q) use ($params): void {
                $q->where('name', 'like', '%' . $params['search'] . '%')
                  ->orWhere('description', 'like', '%' . $params['search'] . '%');
            });
        }

        // Apply sorting
        $sortField = $params['sort_field'] ?? 'sequence';
        $sortDirection = $params['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        // Apply pagination if requested
        if (isset($params['per_page'])) {
            return $query->paginate($params['per_page']);
        }

        return $query->get();
    }

    /**
     * Get paginated products with optional filtering.
     */
    public function getPaginatedProducts(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        // Apply filters
        if (isset($filters['product_type'])) {
            $query->where('product_type', $filters['product_type']);
        }

        if (isset($filters['food_type'])) {
            $query->where('food_type', $filters['food_type']);
        }

        if (isset($filters['screen'])) {
            $query->where('screen', $filters['screen']);
        }

        if (isset($filters['product_category'])) {
            $query->where('product_category', $filters['product_category']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        } else {
            $query->where('status', 1); // Default to active products
        }

        if (isset($filters['is_swappable'])) {
            $query->where('is_swappable', $filters['is_swappable']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters): void {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Find product by ID.
     */
    public function findById(int $id): ?Product
    {
        return $this->model->find($id);
    }

    /**
     * Create a new product.
     */
    public function create(array $data): Product
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing product.
     */
    public function update(Product $product, array $data): Product
    {
        $product->update($data);
        return $product->fresh();
    }

    /**
     * Delete a product.
     */
    public function delete(int $id): bool
    {
        $product = $this->findById($id);

        if (!$product instanceof \App\Models\Product) {
            return false;
        }

        return $product->delete();
    }

    /**
     * Get products by type.
     */
    public function getProductsByType(string $type): Collection
    {
        return $this->model->where('product_type', $type)
                          ->where('status', 1)
                          ->orderBy('sequence', 'asc')
                          ->get();
    }

    /**
     * Get products by food type.
     */
    public function getProductsByFoodType(string $foodType): Collection
    {
        return $this->model->where('food_type', $foodType)
                          ->where('status', 1)
                          ->orderBy('sequence', 'asc')
                          ->get();
    }

    /**
     * Get products by kitchen.
     */
    public function getProductsByKitchen(int $kitchenId): Collection
    {
        return $this->model->where('screen', $kitchenId)
                          ->where('status', 1)
                          ->orderBy('sequence', 'asc')
                          ->get();
    }

    /**
     * Get products by category.
     */
    public function getProductsByCategory(string $category): Collection
    {
        return $this->model->where('product_category', $category)
                          ->where('status', 1)
                          ->orderBy('sequence', 'asc')
                          ->get();
    }

    /**
     * Update product sequence.
     */
    public function updateProductSequence(array $productIds): bool
    {
        try {
            foreach ($productIds as $index => $productId) {
                $this->model->where('pk_product_code', $productId)
                    ->update(['sequence' => $index + 1]);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}

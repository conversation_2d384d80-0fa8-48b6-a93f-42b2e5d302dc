<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Customer;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface CustomerRepositoryInterface
{
    /**
     * Get all customers.
     */
    public function all(): Collection;

    /**
     * Get paginated customers.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Find customer by ID.
     */
    public function findById(int $id): ?Customer;

    /**
     * Find customer by phone.
     */
    public function findByPhone(string $phone): ?Customer;

    /**
     * Find customer by email.
     */
    public function findByEmail(string $email): ?Customer;

    /**
     * Create a new customer.
     */
    public function create(array $data): Customer;

    /**
     * Update a customer.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a customer.
     */
    public function delete(int $id): bool;

    /**
     * Count customers.
     */
    public function count(array $filters = []): int;

    /**
     * Search customers.
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator;
}

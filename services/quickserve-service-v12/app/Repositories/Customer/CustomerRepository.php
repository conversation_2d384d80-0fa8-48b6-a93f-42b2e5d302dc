<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Customer;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomerRepository implements CustomerRepositoryInterface
{
    /**
     * CustomerRepository constructor.
     */
    public function __construct(protected \App\Models\Customer $model)
    {
    }

    /**
     * Get all customers.
     */
    public function all(): Collection
    {
        return $this->model->all();
    }

    /**
     * Get paginated customers.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['city'])) {
            $query->where('city', $filters['city']);
        }
        
        if (isset($filters['group_code'])) {
            $query->where('group_code', $filters['group_code']);
        }
        
        if (isset($filters['phone_verified'])) {
            $query->where('phone_verified', $filters['phone_verified']);
        }
        
        if (isset($filters['email_verified'])) {
            $query->where('email_verified', $filters['email_verified']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        // Sort
        $sortField = $filters['sort_field'] ?? 'pk_customer_code';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->paginate($perPage);
    }

    /**
     * Find customer by ID.
     */
    public function findById(int $id): ?Customer
    {
        return $this->model->find($id);
    }

    /**
     * Find customer by phone.
     */
    public function findByPhone(string $phone): ?Customer
    {
        return $this->model->where('phone', $phone)->first();
    }

    /**
     * Find customer by email.
     */
    public function findByEmail(string $email): ?Customer
    {
        return $this->model->where('email_address', $email)->first();
    }

    /**
     * Create a new customer.
     */
    public function create(array $data): Customer
    {
        return $this->model->create($data);
    }

    /**
     * Update a customer.
     */
    public function update(int $id, array $data): bool
    {
        $customer = $this->findById($id);
        
        if (!$customer instanceof \App\Models\Customer) {
            return false;
        }
        
        return $customer->update($data);
    }

    /**
     * Delete a customer.
     */
    public function delete(int $id): bool
    {
        $customer = $this->findById($id);
        
        if (!$customer instanceof \App\Models\Customer) {
            return false;
        }
        
        return $customer->delete();
    }

    /**
     * Count customers.
     */
    public function count(array $filters = []): int
    {
        $query = $this->model->query();
        
        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['city'])) {
            $query->where('city', $filters['city']);
        }
        
        if (isset($filters['group_code'])) {
            $query->where('group_code', $filters['group_code']);
        }
        
        if (isset($filters['phone_verified'])) {
            $query->where('phone_verified', $filters['phone_verified']);
        }
        
        if (isset($filters['email_verified'])) {
            $query->where('email_verified', $filters['email_verified']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        return $query->count();
    }

    /**
     * Search customers.
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where('customer_name', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->orWhere('email_address', 'like', "%{$query}%")
            ->orWhere('customer_Address', 'like', "%{$query}%")
            ->paginate($perPage);
    }
}

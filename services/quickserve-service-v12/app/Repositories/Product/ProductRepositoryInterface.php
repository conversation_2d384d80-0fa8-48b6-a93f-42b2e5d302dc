<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Product;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface ProductRepositoryInterface
{
    /**
     * Get all products.
     */
    public function all(array $filters = []): Collection;

    /**
     * Get paginated products.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Find product by ID.
     */
    public function findById(int $id): ?Product;

    /**
     * Create a new product.
     */
    public function create(array $data): Product;

    /**
     * Update a product.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a product.
     */
    public function delete(int $id): bool;

    /**
     * Get products by type.
     */
    public function getByType(string $type, array $filters = []): Collection;

    /**
     * Get products by food type.
     */
    public function getByFoodType(string $foodType, array $filters = []): Collection;

    /**
     * Get products by kitchen ID.
     */
    public function getByKitchenId(int $kitchenId, array $filters = []): Collection;

    /**
     * Get products by category.
     */
    public function getByCategory(string $category, array $filters = []): Collection;

    /**
     * Search products.
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get active products.
     */
    public function getActive(array $filters = []): Collection;

    /**
     * Update product sequence.
     */
    public function updateSequence(array $productIds): bool;
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Product;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class ProductRepository implements ProductRepositoryInterface
{
    /**
     * ProductRepository constructor.
     */
    public function __construct(protected \App\Models\Product $model)
    {
    }

    /**
     * Get all products.
     */
    public function all(array $filters = []): Collection
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get paginated products.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->paginate($perPage);
    }

    /**
     * Find product by ID.
     */
    public function findById(int $id): ?Product
    {
        return $this->model->find($id);
    }

    /**
     * Create a new product.
     */
    public function create(array $data): Product
    {
        return $this->model->create($data);
    }

    /**
     * Update a product.
     */
    public function update(int $id, array $data): bool
    {
        $product = $this->findById($id);
        
        if (!$product instanceof \App\Models\Product) {
            return false;
        }
        
        return $product->update($data);
    }

    /**
     * Delete a product.
     */
    public function delete(int $id): bool
    {
        $product = $this->findById($id);
        
        if (!$product instanceof \App\Models\Product) {
            return false;
        }
        
        return $product->delete();
    }

    /**
     * Get products by type.
     */
    public function getByType(string $type, array $filters = []): Collection
    {
        $query = $this->model->where('product_type', $type);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get products by food type.
     */
    public function getByFoodType(string $foodType, array $filters = []): Collection
    {
        $query = $this->model->where('food_type', $foodType);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get products by kitchen ID.
     */
    public function getByKitchenId(int $kitchenId, array $filters = []): Collection
    {
        $query = $this->model->where('kitchen_code', $kitchenId);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get products by category.
     */
    public function getByCategory(string $category, array $filters = []): Collection
    {
        $query = $this->model->where('product_category', $category);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Search products.
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where('name', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->orWhere('product_type', 'like', "%{$query}%")
            ->orWhere('food_type', 'like', "%{$query}%")
            ->paginate($perPage);
    }

    /**
     * Get active products.
     */
    public function getActive(array $filters = []): Collection
    {
        $query = $this->model->where('status', true);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'sequence';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Update product sequence.
     */
    public function updateSequence(array $productIds): bool
    {
        try {
            DB::beginTransaction();
            
            foreach ($productIds as $index => $productId) {
                $product = $this->findById($productId);
                if ($product instanceof \App\Models\Product) {
                    $product->sequence = $index + 1;
                    $product->save();
                }
            }
            
            DB::commit();
            return true;
        } catch (\Exception) {
            DB::rollBack();
            return false;
        }
    }

    /**
     * Apply filters to the query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['product_type'])) {
            $query->where('product_type', $filters['product_type']);
        }
        
        if (isset($filters['food_type'])) {
            $query->where('food_type', $filters['food_type']);
        }
        
        if (isset($filters['product_category'])) {
            $query->where('product_category', $filters['product_category']);
        }
        
        if (isset($filters['kitchen_code'])) {
            $query->where('kitchen_code', $filters['kitchen_code']);
        }
        
        if (isset($filters['screen'])) {
            $query->where('screen', $filters['screen']);
        }
        
        if (isset($filters['is_swappable'])) {
            $query->where('is_swappable', $filters['is_swappable']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        return $query;
    }
}

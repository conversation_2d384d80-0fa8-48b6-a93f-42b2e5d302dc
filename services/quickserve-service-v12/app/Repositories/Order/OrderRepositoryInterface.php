<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Order;

use App\Models\Order;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface OrderRepositoryInterface
{
    /**
     * Get all orders.
     */
    public function all(array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get paginated orders.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Find order by ID.
     */
    public function findById(int $id): ?Order;

    /**
     * Find order by order number.
     */
    public function findByOrderNo(string $orderNo): ?Order;

    /**
     * Get orders by customer ID.
     */
    public function getByCustomerId(int $customerId, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Create a new order.
     */
    public function create(array $data): Order;

    /**
     * Update an order.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete an order.
     */
    public function delete(int $id): bool;

    /**
     * Count orders.
     */
    public function count(array $filters = []): int;

    /**
     * Search orders.
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get orders by status.
     */
    public function getByStatus(string $status, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get orders by delivery status.
     */
    public function getByDeliveryStatus(string $deliveryStatus, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get orders by date range.
     */
    public function getByDateRange(string $startDate, string $endDate, array $filters = []): Collection|LengthAwarePaginator;

    /**
     * Get orders by kitchen ID.
     */
    public function getByKitchenId(int $kitchenId, array $filters = []): Collection|LengthAwarePaginator;
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Order;

use App\Models\Order;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class OrderRepository implements OrderRepositoryInterface
{
    /**
     * OrderRepository constructor.
     */
    public function __construct(protected \App\Models\Order $model)
    {
    }

    /**
     * Get all orders.
     */
    public function all(array $filters = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'pk_order_no';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($filters['per_page'])) {
            return $query->paginate($filters['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Get paginated orders.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'pk_order_no';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->paginate($perPage);
    }

    /**
     * Find order by ID.
     */
    public function findById(int $id): ?Order
    {
        return $this->model->find($id);
    }

    /**
     * Find order by order number.
     */
    public function findByOrderNo(string $orderNo): ?Order
    {
        return $this->model->where('order_no', $orderNo)->first();
    }

    /**
     * Get orders by customer ID.
     */
    public function getByCustomerId(int $customerId, array $filters = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->where('customer_code', $customerId);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'pk_order_no';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($filters['per_page'])) {
            return $query->paginate($filters['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Create a new order.
     */
    public function create(array $data): Order
    {
        return $this->model->create($data);
    }

    /**
     * Update an order.
     */
    public function update(int $id, array $data): bool
    {
        $order = $this->findById($id);
        
        if (!$order instanceof \App\Models\Order) {
            return false;
        }
        
        return $order->update($data);
    }

    /**
     * Delete an order.
     */
    public function delete(int $id): bool
    {
        $order = $this->findById($id);
        
        if (!$order instanceof \App\Models\Order) {
            return false;
        }
        
        return $order->delete();
    }

    /**
     * Count orders.
     */
    public function count(array $filters = []): int
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        return $query->count();
    }

    /**
     * Search orders.
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where('order_no', 'like', "%{$query}%")
            ->orWhere('customer_name', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->orWhere('email_address', 'like', "%{$query}%")
            ->orWhere('product_name', 'like', "%{$query}%")
            ->paginate($perPage);
    }

    /**
     * Get orders by status.
     */
    public function getByStatus(string $status, array $filters = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->where('order_status', $status);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'pk_order_no';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($filters['per_page'])) {
            return $query->paginate($filters['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Get orders by delivery status.
     */
    public function getByDeliveryStatus(string $deliveryStatus, array $filters = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->where('delivery_status', $deliveryStatus);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'pk_order_no';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($filters['per_page'])) {
            return $query->paginate($filters['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Get orders by date range.
     */
    public function getByDateRange(string $startDate, string $endDate, array $filters = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->whereBetween('order_date', [$startDate, $endDate]);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'pk_order_no';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($filters['per_page'])) {
            return $query->paginate($filters['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Get orders by kitchen ID.
     */
    public function getByKitchenId(int $kitchenId, array $filters = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->where('fk_kitchen_code', $kitchenId);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'pk_order_no';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($filters['per_page'])) {
            return $query->paginate($filters['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Apply filters to the query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['order_status'])) {
            $query->where('order_status', $filters['order_status']);
        }
        
        if (isset($filters['delivery_status'])) {
            $query->where('delivery_status', $filters['delivery_status']);
        }
        
        if (isset($filters['order_menu'])) {
            $query->where('order_menu', $filters['order_menu']);
        }
        
        if (isset($filters['food_type'])) {
            $query->where('food_type', $filters['food_type']);
        }
        
        if (isset($filters['payment_mode'])) {
            $query->where('payment_mode', $filters['payment_mode']);
        }
        
        if (isset($filters['location_code'])) {
            $query->where('location_code', $filters['location_code']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('order_date', [$filters['start_date'], $filters['end_date']]);
        }
        
        return $query;
    }
}

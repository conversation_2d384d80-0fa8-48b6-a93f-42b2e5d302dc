<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories;

use App\Models\CustomerAddress;
use Illuminate\Database\Eloquent\Collection;

class CustomerAddressRepository
{
    /**
     * Create a new repository instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The customer address model instance.
         */
        protected \App\Models\CustomerAddress $model
    )
    {
    }

    /**
     * Get addresses by customer ID.
     */
    public function getAddressesByCustomerId(int $customerId): Collection
    {
        return $this->model->where('fk_customer_code', $customerId)
                          ->orderBy('is_default', 'desc')
                          ->orderBy('pk_customer_address_code', 'desc')
                          ->get();
    }

    /**
     * Find address by ID.
     */
    public function findById(int $id): ?CustomerAddress
    {
        return $this->model->find($id);
    }

    /**
     * Create a new address.
     */
    public function create(array $data): CustomerAddress
    {
        // If this is set as default, unset all other default addresses for this customer
        if (isset($data['is_default']) && $data['is_default'] == 1 && isset($data['fk_customer_code'])) {
            $this->unsetDefaultAddresses($data['fk_customer_code']);
        }
        
        return $this->model->create($data);
    }

    /**
     * Update an existing address.
     */
    public function update(CustomerAddress $address, array $data): CustomerAddress
    {
        // If this is set as default, unset all other default addresses for this customer
        if (isset($data['is_default']) && $data['is_default'] == 1) {
            $this->unsetDefaultAddresses($address->fk_customer_code, $address->pk_customer_address_code);
        }
        
        $address->update($data);
        return $address->fresh();
    }

    /**
     * Delete an address.
     */
    public function delete(int $id): bool
    {
        $address = $this->findById($id);
        
        if (!$address instanceof \App\Models\CustomerAddress) {
            return false;
        }
        
        return $address->delete();
    }

    /**
     * Get addresses by menu type.
     */
    public function getAddressesByMenuType(int $customerId, string $menuType): Collection
    {
        return $this->model->where('fk_customer_code', $customerId)
                          ->where('menu_type', $menuType)
                          ->orderBy('is_default', 'desc')
                          ->orderBy('pk_customer_address_code', 'desc')
                          ->get();
    }

    /**
     * Get default address for a customer.
     */
    public function getDefaultAddress(int $customerId, ?string $menuType = null): ?CustomerAddress
    {
        $query = $this->model->where('fk_customer_code', $customerId)
                            ->where('is_default', 1);
        
        if ($menuType) {
            $query->where('menu_type', $menuType);
        }
        
        return $query->first();
    }

    /**
     * Unset default addresses for a customer.
     */
    protected function unsetDefaultAddresses(int $customerId, ?int $exceptAddressId = null): void
    {
        $query = $this->model->where('fk_customer_code', $customerId)
                            ->where('is_default', 1);
        
        if ($exceptAddressId) {
            $query->where('pk_customer_address_code', '!=', $exceptAddressId);
        }
        
        $query->update(['is_default' => 0]);
    }
}

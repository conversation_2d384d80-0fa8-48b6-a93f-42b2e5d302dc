<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Timeslot;

use App\Models\Timeslot;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class TimeslotRepository implements TimeslotRepositoryInterface
{
    /**
     * TimeslotRepository constructor.
     */
    public function __construct(protected \App\Models\Timeslot $model)
    {
    }

    /**
     * Get all timeslots.
     */
    public function all(array $filters = []): Collection
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'starttime';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get paginated timeslots.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'starttime';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->paginate($perPage);
    }

    /**
     * Find timeslot by ID.
     */
    public function findById(int $id): ?Timeslot
    {
        return $this->model->find($id);
    }

    /**
     * Create a new timeslot.
     */
    public function create(array $data): Timeslot
    {
        return $this->model->create($data);
    }

    /**
     * Update a timeslot.
     */
    public function update(int $id, array $data): bool
    {
        $timeslot = $this->findById($id);
        
        if (!$timeslot instanceof \App\Models\Timeslot) {
            return false;
        }
        
        return $timeslot->update($data);
    }

    /**
     * Delete a timeslot.
     */
    public function delete(int $id): bool
    {
        $timeslot = $this->findById($id);
        
        if (!$timeslot instanceof \App\Models\Timeslot) {
            return false;
        }
        
        return $timeslot->delete();
    }

    /**
     * Get timeslots by day.
     */
    public function getByDay(string $day, array $filters = []): Collection
    {
        $query = $this->model->day($day);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'starttime';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get timeslots by menu type.
     */
    public function getByMenuType(string $menuType, array $filters = []): Collection
    {
        $query = $this->model->menuType($menuType);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'starttime';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get timeslots by kitchen.
     */
    public function getByKitchen(string $kitchen, array $filters = []): Collection
    {
        $query = $this->model->kitchen($kitchen);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'starttime';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get active timeslots.
     */
    public function getActive(array $filters = []): Collection
    {
        $query = $this->model->active();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'starttime';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get available timeslots for a specific day, menu type, and kitchen.
     */
    public function getAvailableTimeslots(string $day, string $menuType, string $kitchen, ?string $orderDate = null): Collection
    {
        $query = $this->model->active()
            ->day($day)
            ->menuType($menuType)
            ->kitchen($kitchen);
        
        $timeslots = $query->orderBy('starttime', 'asc')->get();
        
        // If order date is today, filter out past timeslots
        if ($orderDate && date('Y-m-d') === $orderDate) {
            $currentTime = time();
            $timeslots = $timeslots->filter(fn($timeslot): bool => strtotime((string) $timeslot->starttime) > $currentTime);
        }
        
        return $timeslots;
    }

    /**
     * Apply filters to the query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['day'])) {
            $query->where('day', $filters['day']);
        }
        
        if (isset($filters['menu_type'])) {
            $query->where('menu_type', $filters['menu_type']);
        }
        
        if (isset($filters['kitchen'])) {
            $query->where('kitchen', $filters['kitchen']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        return $query;
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Timeslot;

use App\Models\Timeslot;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface TimeslotRepositoryInterface
{
    /**
     * Get all timeslots.
     */
    public function all(array $filters = []): Collection;

    /**
     * Get paginated timeslots.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Find timeslot by ID.
     */
    public function findById(int $id): ?Timeslot;

    /**
     * Create a new timeslot.
     */
    public function create(array $data): Timeslot;

    /**
     * Update a timeslot.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a timeslot.
     */
    public function delete(int $id): bool;

    /**
     * Get timeslots by day.
     */
    public function getByDay(string $day, array $filters = []): Collection;

    /**
     * Get timeslots by menu type.
     */
    public function getByMenuType(string $menuType, array $filters = []): Collection;

    /**
     * Get timeslots by kitchen.
     */
    public function getByKitchen(string $kitchen, array $filters = []): Collection;

    /**
     * Get active timeslots.
     */
    public function getActive(array $filters = []): Collection;

    /**
     * Get available timeslots for a specific day, menu type, and kitchen.
     */
    public function getAvailableTimeslots(string $day, string $menuType, string $kitchen, ?string $orderDate = null): Collection;
}

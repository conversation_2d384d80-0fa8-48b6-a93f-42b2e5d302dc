<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class OrderRepository
{
    /**
     * Create a new repository instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The order model instance.
         */
        protected \App\Models\Order $model
    )
    {
    }

    /**
     * Get all orders with optional filtering.
     */
    public function getAllOrders(array $params = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        
        // Apply filters
        if (isset($params['order_status'])) {
            $query->where('order_status', $params['order_status']);
        }
        
        if (isset($params['delivery_status'])) {
            $query->where('delivery_status', $params['delivery_status']);
        }
        
        if (isset($params['order_menu'])) {
            $query->where('order_menu', $params['order_menu']);
        }
        
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $query->whereBetween('order_date', [$params['from_date'], $params['to_date']]);
        } elseif (isset($params['from_date'])) {
            $query->where('order_date', '>=', $params['from_date']);
        } elseif (isset($params['to_date'])) {
            $query->where('order_date', '<=', $params['to_date']);
        }
        
        if (isset($params['location_code'])) {
            $query->where('location_code', $params['location_code']);
        }
        
        if (isset($params['product_type'])) {
            $query->where('product_type', $params['product_type']);
        }
        
        if (isset($params['food_type'])) {
            $query->where('food_type', $params['food_type']);
        }
        
        // Apply sorting
        $sortField = $params['sort_field'] ?? 'pk_order_no';
        $sortDirection = $params['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($params['per_page'])) {
            return $query->paginate($params['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Find order by ID.
     */
    public function findById(int $id): ?Order
    {
        return $this->model->find($id);
    }

    /**
     * Create a new order.
     */
    public function create(array $data): Order
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing order.
     */
    public function update(Order $order, array $data): Order
    {
        $order->update($data);
        return $order->fresh();
    }

    /**
     * Delete an order.
     */
    public function delete(int $id): bool
    {
        $order = $this->findById($id);
        
        if (!$order instanceof \App\Models\Order) {
            return false;
        }
        
        return $order->delete();
    }

    /**
     * Get orders by customer ID.
     */
    public function getOrdersByCustomer(int $customerId, array $params = []): Collection|LengthAwarePaginator
    {
        $query = $this->model->where('customer_code', $customerId);
        
        // Apply filters
        if (isset($params['order_status'])) {
            $query->where('order_status', $params['order_status']);
        }
        
        if (isset($params['delivery_status'])) {
            $query->where('delivery_status', $params['delivery_status']);
        }
        
        if (isset($params['order_menu'])) {
            $query->where('order_menu', $params['order_menu']);
        }
        
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $query->whereBetween('order_date', [$params['from_date'], $params['to_date']]);
        } elseif (isset($params['from_date'])) {
            $query->where('order_date', '>=', $params['from_date']);
        } elseif (isset($params['to_date'])) {
            $query->where('order_date', '<=', $params['to_date']);
        }
        
        // Apply sorting
        $sortField = $params['sort_field'] ?? 'order_date';
        $sortDirection = $params['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($params['per_page'])) {
            return $query->paginate($params['per_page']);
        }
        
        return $query->get();
    }
}

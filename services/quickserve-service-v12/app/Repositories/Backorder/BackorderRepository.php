<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Backorder;

use App\Models\Backorder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class BackorderRepository implements BackorderRepositoryInterface
{
    /**
     * BackorderRepository constructor.
     */
    public function __construct(protected \App\Models\Backorder $model)
    {
    }

    /**
     * Get all backorders.
     */
    public function all(array $filters = []): Collection
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get paginated backorders.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->paginate($perPage);
    }

    /**
     * Find backorder by ID.
     */
    public function findById(int $id): ?Backorder
    {
        return $this->model->find($id);
    }

    /**
     * Create a new backorder.
     */
    public function create(array $data): Backorder
    {
        return $this->model->create($data);
    }

    /**
     * Update a backorder.
     */
    public function update(int $id, array $data): bool
    {
        $backorder = $this->findById($id);
        
        if (!$backorder instanceof \App\Models\Backorder) {
            return false;
        }
        
        return $backorder->update($data);
    }

    /**
     * Delete a backorder.
     */
    public function delete(int $id): bool
    {
        $backorder = $this->findById($id);
        
        if (!$backorder instanceof \App\Models\Backorder) {
            return false;
        }
        
        return $backorder->delete();
    }

    /**
     * Get backorders by order ID.
     */
    public function getByOrderId(int $orderId, array $filters = []): Collection
    {
        $query = $this->model->where('order_id', $orderId);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get backorders by customer ID.
     */
    public function getByCustomerId(int $customerId, array $filters = []): Collection
    {
        $query = $this->model->where('customer_id', $customerId);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get backorders by product ID.
     */
    public function getByProductId(int $productId, array $filters = []): Collection
    {
        $query = $this->model->where('product_id', $productId);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get backorders by status.
     */
    public function getByStatus(string $status, array $filters = []): Collection
    {
        $query = $this->model->where('status', $status);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get backorders by date range.
     */
    public function getByDateRange(string $startDate, string $endDate, array $filters = []): Collection
    {
        $query = $this->model->whereBetween('order_date', [$startDate, $endDate]);
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get pending backorders.
     */
    public function getPending(array $filters = []): Collection
    {
        $query = $this->model->pending();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get completed backorders.
     */
    public function getCompleted(array $filters = []): Collection
    {
        $query = $this->model->completed();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Get cancelled backorders.
     */
    public function getCancelled(array $filters = []): Collection
    {
        $query = $this->model->cancelled();
        
        // Apply filters
        $query = $this->applyFilters($query, $filters);
        
        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        return $query->get();
    }

    /**
     * Apply filters to the query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['order_id'])) {
            $query->where('order_id', $filters['order_id']);
        }
        
        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }
        
        if (isset($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['order_date'])) {
            $query->where('order_date', $filters['order_date']);
        }
        
        if (isset($filters['order_menu'])) {
            $query->where('order_menu', $filters['order_menu']);
        }
        
        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }
        
        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }
        
        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('order_date', [$filters['start_date'], $filters['end_date']]);
        }
        
        return $query;
    }
}

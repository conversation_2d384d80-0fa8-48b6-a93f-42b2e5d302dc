<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Repositories\Backorder;

use App\Models\Backorder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface BackorderRepositoryInterface
{
    /**
     * Get all backorders.
     */
    public function all(array $filters = []): Collection;

    /**
     * Get paginated backorders.
     */
    public function paginate(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Find backorder by ID.
     */
    public function findById(int $id): ?Backorder;

    /**
     * Create a new backorder.
     */
    public function create(array $data): Backorder;

    /**
     * Update a backorder.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a backorder.
     */
    public function delete(int $id): bool;

    /**
     * Get backorders by order ID.
     */
    public function getByOrderId(int $orderId, array $filters = []): Collection;

    /**
     * Get backorders by customer ID.
     */
    public function getByCustomerId(int $customerId, array $filters = []): Collection;

    /**
     * Get backorders by product ID.
     */
    public function getByProductId(int $productId, array $filters = []): Collection;

    /**
     * Get backorders by status.
     */
    public function getByStatus(string $status, array $filters = []): Collection;

    /**
     * Get backorders by date range.
     */
    public function getByDateRange(string $startDate, string $endDate, array $filters = []): Collection;

    /**
     * Get pending backorders.
     */
    public function getPending(array $filters = []): Collection;

    /**
     * Get completed backorders.
     */
    public function getCompleted(array $filters = []): Collection;

    /**
     * Get cancelled backorders.
     */
    public function getCancelled(array $filters = []): Collection;
}

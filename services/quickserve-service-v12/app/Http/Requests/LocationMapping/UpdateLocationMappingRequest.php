<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests\LocationMapping;

use Illuminate\Foundation\Http\FormRequest;

class UpdateLocationMappingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
            'location_code' => 'sometimes|string',
            'location_name' => 'sometimes|string',
            'city_code' => 'sometimes|string',
            'city_name' => 'sometimes|string',
            'delivery_charges' => 'sometimes|numeric',
            'delivery_time' => 'sometimes|string',
            'kitchen_code' => 'sometimes|string',
            'kitchen_name' => 'sometimes|string',
            'description' => 'sometimes|string',
            'status' => 'sometimes|integer|in:0,1',
        ];
    }
}

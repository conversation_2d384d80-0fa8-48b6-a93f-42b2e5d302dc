<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CustomerUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $customerId = $this->route('id');

        return [
            'customer_name' => 'sometimes|string|max:100',
            'phone' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('customers', 'phone')->ignore($customerId, 'pk_customer_code'),
            ],
            'email_address' => [
                'sometimes',
                'nullable',
                'email',
                'max:100',
                Rule::unique('customers', 'email_address')->ignore($customerId, 'pk_customer_code'),
            ],
            'customer_Address' => 'sometimes|nullable|string',
            'location_code' => 'sometimes|nullable|string|max:50',
            'location_name' => 'sometimes|nullable|string|max:100',
            'lunch_loc_code' => 'sometimes|nullable|string|max:50',
            'lunch_loc_name' => 'sometimes|nullable|string|max:100',
            'lunch_add' => 'sometimes|nullable|string',
            'dinner_loc_code' => 'sometimes|nullable|string|max:50',
            'dinner_loc_name' => 'sometimes|nullable|string|max:100',
            'dinner_add' => 'sometimes|nullable|string',
            'food_preference' => 'sometimes|nullable|string|max:50',
            'city' => 'sometimes|nullable|string|max:50',
            'city_name' => 'sometimes|nullable|string|max:100',
            'company_name' => 'sometimes|nullable|string|max:100',
            'group_code' => 'sometimes|nullable|string|max:50',
            'group_name' => 'sometimes|nullable|string|max:100',
            'registered_from' => 'sometimes|nullable|string|max:50',
            'food_referance' => 'sometimes|nullable|string|max:50',
            'status' => 'sometimes|nullable|boolean',
            'password' => 'sometimes|nullable|string|min:6',
            'thirdparty' => 'sometimes|nullable|string|max:50',
            'phone_verified' => 'sometimes|nullable|boolean',
            'subscription_notification' => 'sometimes|nullable|boolean',
            'email_verified' => 'sometimes|nullable|boolean',
            'source' => 'sometimes|nullable|string|max:50',
            'referer' => 'sometimes|nullable|string|max:100',
            'gcm_id' => 'sometimes|nullable|string|max:255',
            'alt_phone' => 'sometimes|nullable|string|max:20',
            'dabbawala_code_type' => 'sometimes|nullable|string|max:50',
            'dabbawala_code' => 'sometimes|nullable|string|max:50',
            'dabbawala_image' => 'sometimes|nullable|string|max:255',
            'isguest' => 'sometimes|nullable|boolean',
            'delivery_note' => 'sometimes|nullable|string',
            'dabba_status' => 'sometimes|nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'customer_name.string' => 'Customer name must be a string',
            'phone.string' => 'Phone number must be a string',
            'phone.unique' => 'Phone number is already in use',
            'email_address.email' => 'Email address must be valid',
            'email_address.unique' => 'Email address is already in use',
            'password.min' => 'Password must be at least 6 characters',
        ];
    }
}

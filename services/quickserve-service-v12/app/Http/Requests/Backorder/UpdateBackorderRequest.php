<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests\Backorder;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBackorderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
            'order_id' => 'sometimes|integer|exists:orders,pk_order_no',
            'order_no' => 'sometimes|string',
            'customer_id' => 'sometimes|integer|exists:customers,pk_customer_code',
            'product_id' => 'sometimes|integer|exists:products,pk_product_code',
            'product_name' => 'sometimes|string',
            'quantity' => 'sometimes|integer|min:1',
            'amount' => 'sometimes|numeric|min:0',
            'order_date' => 'sometimes|date',
            'order_menu' => 'sometimes|string',
            'reason' => 'sometimes|string',
            'status' => 'sometimes|string|in:pending,completed,cancelled',
        ];
    }
}

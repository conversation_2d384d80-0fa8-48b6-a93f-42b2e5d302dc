<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests\Backorder;

use Illuminate\Foundation\Http\FormRequest;

class StoreBackorderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
            'order_id' => 'required|integer|exists:orders,pk_order_no',
            'order_no' => 'required|string',
            'customer_id' => 'required|integer|exists:customers,pk_customer_code',
            'product_id' => 'required|integer|exists:products,pk_product_code',
            'product_name' => 'required|string',
            'quantity' => 'required|integer|min:1',
            'amount' => 'required|numeric|min:0',
            'order_date' => 'required|date',
            'order_menu' => 'required|string',
            'reason' => 'required|string',
            'status' => 'sometimes|string|in:pending,completed,cancelled',
        ];
    }
}

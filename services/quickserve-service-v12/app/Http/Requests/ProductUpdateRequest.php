<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:100',
            'description' => 'sometimes|nullable|string',
            'recipe' => 'sometimes|nullable|string',
            'unit_price' => 'sometimes|numeric|min:0',
            'product_type' => 'sometimes|string|max:50',
            'product_category' => 'sometimes|nullable|string|max:50',
            'category' => 'sometimes|nullable|string|max:100',
            'screen' => 'sometimes|string|max:50',
            'threshold' => 'sometimes|nullable|integer|min:0',
            'max_quantity_per_meal' => 'sometimes|nullable|integer|min:1',
            'quantity' => 'sometimes|nullable|numeric|min:0',
            'unit' => 'sometimes|nullable|string|max:20',
            'status' => 'sometimes|nullable|boolean',
            'kitchen_code' => 'sometimes|nullable|string|max:50',
            'product_subtype' => 'sometimes|nullable|string|max:50',
            'food_type' => 'sometimes|nullable|string|max:20',
            'image_path' => 'sometimes|nullable|string|max:255',
            'is_swappable' => 'sometimes|nullable|boolean',
            'swap_with' => 'sometimes|nullable|string|max:255',
            'swap_charges' => 'sometimes|nullable|numeric|min:0',
            'sequence' => 'sometimes|nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.string' => 'Product name must be a string',
            'unit_price.numeric' => 'Unit price must be a number',
            'unit_price.min' => 'Unit price must be at least 0',
            'product_type.string' => 'Product type must be a string',
            'screen.string' => 'Screen must be a string',
            'threshold.integer' => 'Threshold must be an integer',
            'threshold.min' => 'Threshold must be at least 0',
            'max_quantity_per_meal.integer' => 'Maximum quantity per meal must be an integer',
            'max_quantity_per_meal.min' => 'Maximum quantity per meal must be at least 1',
            'quantity.numeric' => 'Quantity must be a number',
            'quantity.min' => 'Quantity must be at least 0',
            'swap_charges.numeric' => 'Swap charges must be a number',
            'swap_charges.min' => 'Swap charges must be at least 0',
            'sequence.integer' => 'Sequence must be an integer',
            'sequence.min' => 'Sequence must be at least 0',
        ];
    }
}

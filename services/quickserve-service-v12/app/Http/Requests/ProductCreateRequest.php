<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'recipe' => 'nullable|string',
            'unit_price' => 'required|numeric|min:0',
            'product_type' => 'required|string|max:50',
            'product_category' => 'nullable|string|max:50',
            'category' => 'nullable|string|max:100',
            'screen' => 'required|string|max:50',
            'threshold' => 'nullable|integer|min:0',
            'max_quantity_per_meal' => 'nullable|integer|min:1',
            'quantity' => 'nullable|numeric|min:0',
            'unit' => 'nullable|string|max:20',
            'status' => 'nullable|boolean',
            'kitchen_code' => 'nullable|string|max:50',
            'product_subtype' => 'nullable|string|max:50',
            'food_type' => 'nullable|string|max:20',
            'image_path' => 'nullable|string|max:255',
            'is_swappable' => 'nullable|boolean',
            'swap_with' => 'nullable|string|max:255',
            'swap_charges' => 'nullable|numeric|min:0',
            'sequence' => 'nullable|integer|min:0',
            'company_id' => 'nullable|integer',
            'unit_id' => 'nullable|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required',
            'unit_price.required' => 'Unit price is required',
            'unit_price.numeric' => 'Unit price must be a number',
            'unit_price.min' => 'Unit price must be at least 0',
            'product_type.required' => 'Product type is required',
            'screen.required' => 'Screen is required',
            'threshold.integer' => 'Threshold must be an integer',
            'threshold.min' => 'Threshold must be at least 0',
            'max_quantity_per_meal.integer' => 'Maximum quantity per meal must be an integer',
            'max_quantity_per_meal.min' => 'Maximum quantity per meal must be at least 1',
            'quantity.numeric' => 'Quantity must be a number',
            'quantity.min' => 'Quantity must be at least 0',
            'swap_charges.numeric' => 'Swap charges must be a number',
            'swap_charges.min' => 'Swap charges must be at least 0',
            'sequence.integer' => 'Sequence must be an integer',
            'sequence.min' => 'Sequence must be at least 0',
        ];
    }
}

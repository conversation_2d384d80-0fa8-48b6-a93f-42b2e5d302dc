<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'customer_code' => 'nullable|string|max:50',
            'company_id' => 'nullable|integer',
            'unit_id' => 'nullable|integer',
            'order_status' => 'nullable|string|max:20',
            'delivery_status' => 'nullable|string|max:20',
            'invoice_status' => 'nullable|string|max:20',
            'amount' => 'nullable|numeric|min:0',
            'tax' => 'nullable|numeric|min:0',
            'delivery_charges' => 'nullable|numeric|min:0',
            'service_charges' => 'nullable|numeric|min:0',
            'applied_discount' => 'nullable|numeric|min:0',
            'delivery_date' => 'nullable|date',
            'delivery_time' => 'nullable|string|max:20',
            'delivery_address' => 'nullable|string|max:255',
            'delivery_city' => 'nullable|string|max:50',
            'delivery_state' => 'nullable|string|max:50',
            'delivery_country' => 'nullable|string|max:50',
            'delivery_pincode' => 'nullable|string|max:20',
            'delivery_latitude' => 'nullable|numeric',
            'delivery_longitude' => 'nullable|numeric',
            'payment_mode' => 'nullable|string|max:20',
            'transaction_id' => 'nullable|string|max:100',
            'amount_paid' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
            'items' => 'nullable|array',
            'items.*.product_code' => 'required|string|max:50',
            'items.*.product_name' => 'required|string|max:100',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.amount' => 'required|numeric|min:0',
            'items.*.tax' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'amount.numeric' => 'Amount must be a number',
            'amount.min' => 'Amount must be at least 0',
            'items.*.product_code.required' => 'Product code is required for all items',
            'items.*.product_name.required' => 'Product name is required for all items',
            'items.*.quantity.required' => 'Quantity is required for all items',
            'items.*.quantity.integer' => 'Quantity must be an integer for all items',
            'items.*.quantity.min' => 'Quantity must be at least 1 for all items',
            'items.*.amount.required' => 'Amount is required for all items',
            'items.*.amount.numeric' => 'Amount must be a number for all items',
            'items.*.amount.min' => 'Amount must be at least 0 for all items',
        ];
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'customer_name' => 'sometimes|string|max:100',
            'phone' => 'sometimes|string|max:20',
            'email_address' => 'sometimes|nullable|email|max:100',
            'location_code' => 'sometimes|integer',
            'location_name' => 'sometimes|string|max:100',
            'city' => 'sometimes|nullable|string|max:50',
            'city_name' => 'sometimes|nullable|string|max:100',
            'product_name' => 'sometimes|string|max:100',
            'product_description' => 'sometimes|nullable|string',
            'quantity' => 'sometimes|integer|min:1',
            'amount' => 'sometimes|numeric|min:0',
            'tax' => 'sometimes|nullable|numeric|min:0',
            'delivery_charges' => 'sometimes|nullable|numeric|min:0',
            'service_charges' => 'sometimes|nullable|numeric|min:0',
            'applied_discount' => 'sometimes|nullable|numeric|min:0',
            'order_status' => 'sometimes|string|max:50',
            'order_date' => 'sometimes|date',
            'ship_address' => 'sometimes|string',
            'delivery_status' => 'sometimes|string|max:50',
            'invoice_status' => 'sometimes|string|max:50',
            'order_menu' => 'sometimes|string|max:50',
            'food_type' => 'sometimes|nullable|string|max:50',
            'payment_mode' => 'sometimes|nullable|string|max:50',
            'amount_paid' => 'sometimes|nullable|numeric|min:0',
            'promo_code' => 'sometimes|nullable|string|max:50',
            'tax_method' => 'sometimes|nullable|string|max:20',
            'source' => 'sometimes|nullable|string|max:50',
            'delivery_time' => 'sometimes|nullable|string|max:20',
            'delivery_end_time' => 'sometimes|nullable|string|max:20',
            'remark' => 'sometimes|nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'customer_name.string' => 'Customer name must be a string',
            'phone.string' => 'Phone number must be a string',
            'email_address.email' => 'Email address must be valid',
            'location_code.integer' => 'Location code must be an integer',
            'location_name.string' => 'Location name must be a string',
            'product_name.string' => 'Product name must be a string',
            'quantity.integer' => 'Quantity must be an integer',
            'quantity.min' => 'Quantity must be at least 1',
            'amount.numeric' => 'Amount must be a number',
            'amount.min' => 'Amount must be at least 0',
            'tax.numeric' => 'Tax must be a number',
            'tax.min' => 'Tax must be at least 0',
            'delivery_charges.numeric' => 'Delivery charges must be a number',
            'delivery_charges.min' => 'Delivery charges must be at least 0',
            'service_charges.numeric' => 'Service charges must be a number',
            'service_charges.min' => 'Service charges must be at least 0',
            'applied_discount.numeric' => 'Applied discount must be a number',
            'applied_discount.min' => 'Applied discount must be at least 0',
            'order_date.date' => 'Order date must be a valid date',
        ];
    }
}

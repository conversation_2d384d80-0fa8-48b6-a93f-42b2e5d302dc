<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests\Timeslot;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTimeslotRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'sometimes|integer',
            'unit_id' => 'sometimes|integer',
            'starttime' => 'sometimes|string',
            'endtime' => 'sometimes|string',
            'day' => 'sometimes|string',
            'menu_type' => 'sometimes|string',
            'kitchen' => 'sometimes|string',
            'status' => 'sometimes|integer|in:0,1',
        ];
    }
}

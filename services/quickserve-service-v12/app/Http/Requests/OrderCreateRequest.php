<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'customer_code' => 'required|string',
            'customer_name' => 'required|string|max:100',
            'phone' => 'required|string|max:20',
            'email_address' => 'nullable|email|max:100',
            'location_code' => 'required|integer',
            'location_name' => 'required|string|max:100',
            'city' => 'nullable|string|max:50',
            'city_name' => 'nullable|string|max:100',
            'product_code' => 'required|string',
            'product_name' => 'required|string|max:100',
            'product_description' => 'nullable|string',
            'product_type' => 'required|string|max:50',
            'quantity' => 'required|integer|min:1',
            'amount' => 'required|numeric|min:0',
            'tax' => 'nullable|numeric|min:0',
            'delivery_charges' => 'nullable|numeric|min:0',
            'service_charges' => 'nullable|numeric|min:0',
            'applied_discount' => 'nullable|numeric|min:0',
            'order_status' => 'nullable|string|max:50',
            'order_date' => 'required|date',
            'ship_address' => 'required|string',
            'delivery_status' => 'nullable|string|max:50',
            'invoice_status' => 'nullable|string|max:50',
            'order_menu' => 'required|string|max:50',
            'food_type' => 'nullable|string|max:50',
            'payment_mode' => 'nullable|string|max:50',
            'amount_paid' => 'nullable|numeric|min:0',
            'promo_code' => 'nullable|string|max:50',
            'tax_method' => 'nullable|string|max:20',
            'source' => 'nullable|string|max:50',
            'delivery_time' => 'nullable|string|max:20',
            'delivery_end_time' => 'nullable|string|max:20',
            'remark' => 'nullable|string',
            'company_id' => 'nullable|integer',
            'unit_id' => 'nullable|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'customer_code.required' => 'Customer code is required',

            'customer_name.required' => 'Customer name is required',
            'phone.required' => 'Phone number is required',
            'location_code.required' => 'Location code is required',
            'location_name.required' => 'Location name is required',
            'product_code.required' => 'Product code is required',

            'product_name.required' => 'Product name is required',
            'product_type.required' => 'Product type is required',
            'quantity.required' => 'Quantity is required',
            'quantity.min' => 'Quantity must be at least 1',
            'amount.required' => 'Amount is required',
            'amount.min' => 'Amount must be at least 0',
            'order_date.required' => 'Order date is required',
            'ship_address.required' => 'Shipping address is required',
            'order_menu.required' => 'Order menu is required',
        ];
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CustomerCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'customer_name' => 'required|string|max:100',
            'phone' => 'required|string|max:20|unique:customers,phone',
            'email_address' => 'nullable|email|max:100|unique:customers,email_address',
            'customer_Address' => 'nullable|string',
            'location_code' => 'nullable|string|max:50',
            'location_name' => 'nullable|string|max:100',
            'lunch_loc_code' => 'nullable|string|max:50',
            'lunch_loc_name' => 'nullable|string|max:100',
            'lunch_add' => 'nullable|string',
            'dinner_loc_code' => 'nullable|string|max:50',
            'dinner_loc_name' => 'nullable|string|max:100',
            'dinner_add' => 'nullable|string',
            'food_preference' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'city_name' => 'nullable|string|max:100',
            'company_name' => 'nullable|string|max:100',
            'group_code' => 'nullable|string|max:50',
            'group_name' => 'nullable|string|max:100',
            'registered_on' => 'nullable|date',
            'registered_from' => 'nullable|string|max:50',
            'food_referance' => 'nullable|string|max:50',
            'status' => 'nullable|boolean',
            'password' => 'nullable|string|min:6',
            'thirdparty' => 'nullable|string|max:50',
            'phone_verified' => 'nullable|boolean',
            'subscription_notification' => 'nullable|boolean',
            'email_verified' => 'nullable|boolean',
            'source' => 'nullable|string|max:50',
            'referer' => 'nullable|string|max:100',
            'gcm_id' => 'nullable|string|max:255',
            'alt_phone' => 'nullable|string|max:20',
            'dabbawala_code_type' => 'nullable|string|max:50',
            'dabbawala_code' => 'nullable|string|max:50',
            'dabbawala_image' => 'nullable|string|max:255',
            'isguest' => 'nullable|boolean',
            'delivery_note' => 'nullable|string',
            'company_id' => 'nullable|integer',
            'unit_id' => 'nullable|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'customer_name.required' => 'Customer name is required',
            'phone.required' => 'Phone number is required',
            'phone.unique' => 'Phone number is already in use',
            'email_address.email' => 'Email address must be valid',
            'email_address.unique' => 'Email address is already in use',
            'password.min' => 'Password must be at least 6 characters',
        ];
    }
}

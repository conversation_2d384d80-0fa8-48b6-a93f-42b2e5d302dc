<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BackorderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'order_id' => $this->order_id,
            'order_no' => $this->order_no,
            'customer_id' => $this->customer_id,
            'customer' => $this->whenLoaded('customer', fn(): array => [
                'id' => $this->customer->pk_customer_code,
                'name' => $this->customer->customer_name,
                'phone' => $this->customer->phone,
                'email' => $this->customer->email_address,
            ]),
            'product_id' => $this->product_id,
            'product_name' => $this->product_name,
            'product' => $this->whenLoaded('product', fn(): array => [
                'id' => $this->product->pk_product_code,
                'name' => $this->product->name,
                'type' => $this->product->product_type,
            ]),
            'quantity' => $this->quantity,
            'amount' => $this->amount,
            'order_date' => $this->order_date,
            'order_menu' => $this->order_menu,
            'reason' => $this->reason,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

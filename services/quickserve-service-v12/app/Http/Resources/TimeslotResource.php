<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TimeslotResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'starttime' => $this->starttime,
            'endtime' => $this->endtime,
            'day' => $this->day,
            'menu_type' => $this->menu_type,
            'kitchen' => $this->kitchen,
            'status' => $this->status,
            'formatted_start_time' => $this->formatted_start_time,
            'formatted_end_time' => $this->formatted_end_time,
            'display_slot' => $this->display_slot,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

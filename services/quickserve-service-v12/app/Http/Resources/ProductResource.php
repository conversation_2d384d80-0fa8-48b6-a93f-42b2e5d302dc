<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_product_code,
            'name' => $this->name,
            'description' => $this->description,
            'recipe' => $this->recipe,
            'unit_price' => $this->unit_price,
            'product_type' => $this->product_type,
            'product_category' => $this->product_category,
            'category' => $this->category,
            'screen' => $this->screen,
            'threshold' => $this->threshold,
            'max_quantity_per_meal' => $this->max_quantity_per_meal,
            'quantity' => $this->quantity,
            'unit' => $this->unit,
            'status' => (bool) $this->status,
            'kitchen_code' => $this->kitchen_code,
            'product_subtype' => $this->product_subtype,
            'food_type' => $this->food_type,
            'image_path' => $this->image_path ? url('data/products/' . $this->image_path) : null,
            'is_swappable' => (bool) $this->is_swappable,
            'swap_with' => $this->swap_with,
            'swap_charges' => $this->swap_charges,
            'sequence' => $this->sequence,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
        ];
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LocationMappingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'location_code' => $this->location_code,
            'location_name' => $this->location_name,
            'city_code' => $this->city_code,
            'city_name' => $this->city_name,
            'delivery_charges' => $this->delivery_charges,
            'delivery_time' => $this->delivery_time,
            'kitchen_code' => $this->kitchen_code,
            'kitchen_name' => $this->kitchen_name,
            'description' => $this->description,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

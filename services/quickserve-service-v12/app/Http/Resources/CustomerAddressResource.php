<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_customer_address_code,
            'customer_id' => $this->fk_customer_code,
            'address' => $this->address,
            'location' => [
                'code' => $this->location_code,
                'name' => $this->location_name,
            ],
            'city' => $this->city,
            'city_name' => $this->city_name,
            'menu_type' => $this->menu_type,
            'address_type' => $this->address_type,
            'landmark' => $this->landmark,
            'coordinates' => [
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ],
            'is_default' => (bool) $this->is_default,
            'created_on' => $this->created_on,
            'modified_on' => $this->modified_on,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'full_address' => $this->full_address,
        ];
    }
}

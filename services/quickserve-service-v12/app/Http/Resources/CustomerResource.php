<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_customer_code,
            'customer_name' => $this->customer_name,
            'phone' => $this->phone,
            'email_address' => $this->email_address,
            'customer_address' => $this->customer_Address,
            'location' => [
                'code' => $this->location_code,
                'name' => $this->location_name,
            ],
            'lunch_location' => [
                'code' => $this->lunch_loc_code,
                'name' => $this->lunch_loc_name,
                'address' => $this->lunch_add,
            ],
            'dinner_location' => [
                'code' => $this->dinner_loc_code,
                'name' => $this->dinner_loc_name,
                'address' => $this->dinner_add,
            ],
            'food_preference' => $this->food_preference,
            'city' => $this->city,
            'city_name' => $this->city_name,
            'company_name' => $this->company_name,
            'group' => [
                'code' => $this->group_code,
                'name' => $this->group_name,
            ],
            'registered_on' => $this->registered_on,
            'registered_from' => $this->registered_from,
            'food_referance' => $this->food_referance,
            'status' => (bool) $this->status,
            'phone_verified' => (bool) $this->phone_verified,
            'email_verified' => (bool) $this->email_verified,
            'subscription_notification' => (bool) $this->subscription_notification,
            'source' => $this->source,
            'referer' => $this->referer,
            'gcm_id' => $this->gcm_id,
            'alt_phone' => $this->alt_phone,
            'dabbawala' => [
                'code_type' => $this->dabbawala_code_type,
                'code' => $this->dabbawala_code,
                'image' => $this->dabbawala_image ? url('data/' . $this->dabbawala_image) : null,
                'status' => (bool) $this->dabba_status,
            ],
            'isguest' => (bool) $this->isguest,
            'delivery_note' => $this->delivery_note,
            'modified_on' => $this->modified_on,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
        ];
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_order_no,
            'order_no' => $this->order_no,
            'ref_order' => $this->ref_order,
            'customer' => [
                'id' => $this->customer_code,
                'name' => $this->customer_name,
                'phone' => $this->phone,
                'email' => $this->email_address,
            ],
            'location' => [
                'code' => $this->location_code,
                'name' => $this->location_name,
                'city' => $this->city,
                'city_name' => $this->city_name,
            ],
            'product' => [
                'code' => $this->product_code,
                'name' => $this->product_name,
                'description' => $this->product_description,
                'type' => $this->product_type,
            ],
            'quantity' => $this->quantity,
            'amount' => $this->amount,
            'tax' => $this->tax,
            'delivery_charges' => $this->delivery_charges,
            'service_charges' => $this->service_charges,
            'applied_discount' => $this->applied_discount,
            'net_amount' => $this->net_amount,
            'order_status' => $this->order_status,
            'delivery_status' => $this->delivery_status,
            'invoice_status' => $this->invoice_status,
            'order_date' => $this->order_date,
            'ship_address' => $this->ship_address,
            'order_menu' => $this->order_menu,
            'food_type' => $this->food_type,
            'payment_mode' => $this->payment_mode,
            'amount_paid' => $this->amount_paid,
            'promo_code' => $this->promo_code,
            'tax_method' => $this->tax_method,
            'source' => $this->source,
            'delivery_time' => $this->delivery_time,
            'delivery_end_time' => $this->delivery_end_time,
            'remark' => $this->remark,
            'created_date' => $this->created_date,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
        ];
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Resources\Order;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'customer_code' => $this->customer_code,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'order_status' => $this->order_status,
            'delivery_status' => $this->delivery_status,
            'invoice_status' => $this->invoice_status,
            'amount' => (float) $this->amount,
            'tax' => (float) $this->tax,
            'delivery_charges' => (float) $this->delivery_charges,
            'service_charges' => (float) $this->service_charges,
            'applied_discount' => (float) $this->applied_discount,
            'total_amount' => (float) ($this->amount + $this->tax + $this->delivery_charges + $this->service_charges - $this->applied_discount),
            'delivery_date' => $this->delivery_date,
            'delivery_time' => $this->delivery_time,
            'delivery_address' => $this->delivery_address,
            'delivery_city' => $this->delivery_city,
            'delivery_state' => $this->delivery_state,
            'delivery_country' => $this->delivery_country,
            'delivery_pincode' => $this->delivery_pincode,
            'delivery_latitude' => $this->delivery_latitude,
            'delivery_longitude' => $this->delivery_longitude,
            'payment_mode' => $this->payment_mode,
            'transaction_id' => $this->transaction_id,
            'amount_paid' => (float) $this->amount_paid,
            'notes' => $this->notes,
            'cancellation_reason' => $this->cancellation_reason,
            'cancelled_at' => $this->cancelled_at,
            'created_date' => $this->created_date,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'items' => $this->whenLoaded('orderDetails', fn() => $this->orderDetails->map(fn($item): array => [
                'id' => $item->id,
                'product_code' => $item->product_code,
                'product_name' => $item->product_name,
                'quantity' => $item->quantity,
                'amount' => (float) $item->amount,
                'tax' => (float) $item->tax,
                'total' => (float) ($item->amount * $item->quantity + $item->tax),
            ])),
        ];
    }
}

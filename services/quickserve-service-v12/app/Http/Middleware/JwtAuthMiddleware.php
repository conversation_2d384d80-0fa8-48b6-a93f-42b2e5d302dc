<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Middleware;

use App\Services\Auth\AuthServiceClient;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * JWT Authentication Middleware
 * 
 * This middleware validates JWT tokens with the Auth Service.
 */
class JwtAuthMiddleware
{
    /**
     * Create a new middleware instance.
     */
    public function __construct(
        /**
         * The Auth Service client instance.
         */
        protected AuthServiceClient $authService
    )
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param string ...$roles
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // Get token from request
        $token = $this->getTokenFromRequest($request);

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - No token provided'
            ], 401);
        }

        // Validate token with Auth Service
        if (!$this->authService->validateToken($token)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Invalid token'
            ], 401);
        }

        // Check roles if specified
        if ($roles !== []) {
            $hasRole = false;
            foreach ($roles as $role) {
                if ($this->authService->hasRole($token, $role)) {
                    $hasRole = true;
                    break;
                }
            }

            if (!$hasRole) {
                return response()->json([
                    'success' => false,
                    'message' => 'Forbidden - Insufficient permissions'
                ], 403);
            }
        }

        // Get user from token and add to request
        $user = $this->authService->getUserFromToken($token);
        if ($user) {
            $request->merge(['auth_user' => $user]);
        }

        return $next($request);
    }

    /**
     * Get the token from the request.
     */
    protected function getTokenFromRequest(Request $request): ?string
    {
        // Check Authorization header
        $header = $request->header('Authorization');
        if ($header && str_starts_with($header, 'Bearer ')) {
            return substr($header, 7);
        }

        // Check query string
        if ($request->has('token')) {
            return $request->input('token');
        }

        return null;
    }
}

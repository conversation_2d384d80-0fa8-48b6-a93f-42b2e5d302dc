<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Middleware;

use App\Services\Logging\LoggingService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class CorrelationIdMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get correlation ID from request header or generate a new one
        $correlationId = $request->header('X-Correlation-ID');
        
        if (!$correlationId) {
            $correlationId = (string) Str::uuid();
            $request->headers->set('X-Correlation-ID', $correlationId);
        }
        
        // Set correlation ID in the logging service
        LoggingService::setCorrelationId($correlationId);
        
        // Process the request
        $response = $next($request);
        
        // Add correlation ID to response header
        $response->headers->set('X-Correlation-ID', $correlationId);
        
        return $response;
    }
}

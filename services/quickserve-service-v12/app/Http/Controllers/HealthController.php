<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Services\RabbitMQ\MockRabbitMQService;

/**
 * Health Check Controller
 * 
 * Provides comprehensive health monitoring for the QuickServe service
 * including database connectivity, cache status, and external dependencies.
 */
class HealthController extends Controller
{
    private MockRabbitMQService $rabbitMQService;

    public function __construct(MockRabbitMQService $rabbitMQService)
    {
        $this->rabbitMQService = $rabbitMQService;
    }

    /**
     * Basic health check endpoint
     * 
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        return response()->json([
            'status' => 'healthy',
            'service' => 'quickserve-service-v12',
            'timestamp' => now()->toISOString(),
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
        ]);
    }

    /**
     * Detailed health check with component status
     * 
     * @return JsonResponse
     */
    public function detailed(): JsonResponse
    {
        $startTime = microtime(true);
        $checks = [];
        $overallStatus = 'healthy';

        // Database connectivity check
        $checks['database'] = $this->checkDatabase();
        
        // Cache connectivity check
        $checks['cache'] = $this->checkCache();
        
        // RabbitMQ connectivity check
        $checks['rabbitmq'] = $this->checkRabbitMQ();
        
        // Disk space check
        $checks['disk_space'] = $this->checkDiskSpace();
        
        // Memory usage check
        $checks['memory'] = $this->checkMemoryUsage();
        
        // External services check
        $checks['external_services'] = $this->checkExternalServices();

        // Determine overall status
        foreach ($checks as $check) {
            if ($check['status'] !== 'healthy') {
                $overallStatus = $check['status'] === 'critical' ? 'critical' : 'degraded';
                if ($overallStatus === 'critical') {
                    break;
                }
            }
        }

        $responseTime = round((microtime(true) - $startTime) * 1000, 2);

        return response()->json([
            'status' => $overallStatus,
            'service' => 'quickserve-service-v12',
            'timestamp' => now()->toISOString(),
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
            'response_time_ms' => $responseTime,
            'checks' => $checks,
            'deployment' => [
                'type' => 'blue-green',
                'instance_id' => gethostname(),
                'deployment_time' => config('app.deployment_time', now()->toISOString()),
            ],
        ], $overallStatus === 'healthy' ? 200 : ($overallStatus === 'critical' ? 503 : 200));
    }

    /**
     * Check database connectivity
     * 
     * @return array
     */
    private function checkDatabase(): array
    {
        try {
            $startTime = microtime(true);
            DB::connection()->getPdo();
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // Test a simple query
            $result = DB::select('SELECT 1 as test');
            
            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'connection' => DB::connection()->getName(),
                'message' => 'Database connection successful',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'critical',
                'response_time_ms' => null,
                'connection' => null,
                'message' => 'Database connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache connectivity
     * 
     * @return array
     */
    private function checkCache(): array
    {
        try {
            $startTime = microtime(true);
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            if ($retrieved === $testValue) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $responseTime,
                    'driver' => config('cache.default'),
                    'message' => 'Cache operations successful',
                ];
            } else {
                return [
                    'status' => 'degraded',
                    'response_time_ms' => $responseTime,
                    'driver' => config('cache.default'),
                    'message' => 'Cache read/write test failed',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'degraded',
                'response_time_ms' => null,
                'driver' => config('cache.default'),
                'message' => 'Cache connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check RabbitMQ connectivity
     * 
     * @return array
     */
    private function checkRabbitMQ(): array
    {
        try {
            $startTime = microtime(true);
            $isConnected = $this->rabbitMQService->isConnected();
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'status' => $isConnected ? 'healthy' : 'degraded',
                'response_time_ms' => $responseTime,
                'enabled' => config('rabbitmq.enabled', false),
                'message' => $isConnected ? 'RabbitMQ connection successful' : 'RabbitMQ connection failed (using mock)',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'degraded',
                'response_time_ms' => null,
                'enabled' => config('rabbitmq.enabled', false),
                'message' => 'RabbitMQ check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check disk space
     * 
     * @return array
     */
    private function checkDiskSpace(): array
    {
        try {
            $path = storage_path();
            $totalBytes = disk_total_space($path);
            $freeBytes = disk_free_space($path);
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercentage = round(($usedBytes / $totalBytes) * 100, 2);
            
            $status = 'healthy';
            if ($usagePercentage > 90) {
                $status = 'critical';
            } elseif ($usagePercentage > 80) {
                $status = 'degraded';
            }
            
            return [
                'status' => $status,
                'usage_percentage' => $usagePercentage,
                'free_space_gb' => round($freeBytes / (1024 * 1024 * 1024), 2),
                'total_space_gb' => round($totalBytes / (1024 * 1024 * 1024), 2),
                'message' => "Disk usage: {$usagePercentage}%",
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'degraded',
                'usage_percentage' => null,
                'message' => 'Disk space check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check memory usage
     * 
     * @return array
     */
    private function checkMemoryUsage(): array
    {
        try {
            $memoryUsage = memory_get_usage(true);
            $memoryPeak = memory_get_peak_usage(true);
            $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
            
            $usagePercentage = $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : 0;
            
            $status = 'healthy';
            if ($usagePercentage > 90) {
                $status = 'critical';
            } elseif ($usagePercentage > 80) {
                $status = 'degraded';
            }
            
            return [
                'status' => $status,
                'usage_percentage' => $usagePercentage,
                'current_usage_mb' => round($memoryUsage / (1024 * 1024), 2),
                'peak_usage_mb' => round($memoryPeak / (1024 * 1024), 2),
                'limit_mb' => round($memoryLimit / (1024 * 1024), 2),
                'message' => "Memory usage: {$usagePercentage}%",
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'degraded',
                'usage_percentage' => null,
                'message' => 'Memory usage check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check external services
     * 
     * @return array
     */
    private function checkExternalServices(): array
    {
        $services = [
            'customer_service' => config('services.customer.url'),
            'payment_service' => config('services.payment.url'),
            'delivery_service' => config('services.delivery.url'),
        ];
        
        $results = [];
        $overallStatus = 'healthy';
        
        foreach ($services as $name => $url) {
            if (!$url) {
                $results[$name] = [
                    'status' => 'degraded',
                    'message' => 'Service URL not configured',
                ];
                $overallStatus = 'degraded';
                continue;
            }
            
            try {
                $startTime = microtime(true);
                $response = @file_get_contents($url . '/health', false, stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'method' => 'GET',
                    ],
                ]));
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                
                if ($response !== false) {
                    $results[$name] = [
                        'status' => 'healthy',
                        'response_time_ms' => $responseTime,
                        'message' => 'Service accessible',
                    ];
                } else {
                    $results[$name] = [
                        'status' => 'degraded',
                        'response_time_ms' => $responseTime,
                        'message' => 'Service not accessible',
                    ];
                    $overallStatus = 'degraded';
                }
            } catch (\Exception $e) {
                $results[$name] = [
                    'status' => 'degraded',
                    'message' => 'Service check failed: ' . $e->getMessage(),
                ];
                $overallStatus = 'degraded';
            }
        }
        
        return [
            'status' => $overallStatus,
            'services' => $results,
            'message' => 'External services check completed',
        ];
    }

    /**
     * Parse memory limit string to bytes
     * 
     * @param string $limit
     * @return int
     */
    private function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return 0; // No limit
        }
        
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
                // fall through
            case 'm':
                $value *= 1024;
                // fall through
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api;

use App\DTOs\Customer\CustomerDTO;
use App\DTOs\Payment\PaymentRequestDTO;
use App\Exceptions\Customer\CustomerException;
use App\Exceptions\Payment\PaymentException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Order\CreateOrderRequest;
use App\Http\Requests\Order\ProcessPaymentRequest;
use App\Models\Order;
use App\Services\Customer\CustomerServiceClient;
use App\Services\Meal\MealServiceClient;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        /**
         * The customer service client instance.
         */
        protected CustomerServiceClient $customerService,
        /**
         * The meal service client instance.
         */
        protected MealServiceClient $mealService,
        /**
         * The payment service client instance.
         */
        protected PaymentServiceClient $paymentService
    )
    {
    }

    /**
     * Create a new order.
     */
    public function store(CreateOrderRequest $request): JsonResponse
    {
        try {
            // Get customer from Customer Service
            $customer = $this->customerService->getCustomer($request->customer_id);
            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Get meal details from Meal Service
            $mealItems = [];
            foreach ($request->items as $item) {
                $meal = $this->mealService->getMeal($item['meal_id']);
                if (!$meal) {
                    return response()->json([
                        'success' => false,
                        'message' => "Meal with ID {$item['meal_id']} not found"
                    ], 404);
                }
                $mealItems[] = [
                    'meal_id' => $meal['id'],
                    'name' => $meal['name'],
                    'price' => $meal['price'],
                    'quantity' => $item['quantity'],
                    'subtotal' => $meal['price'] * $item['quantity']
                ];
            }

            // Calculate total amount
            $totalAmount = array_sum(array_column($mealItems, 'subtotal'));

            // Start transaction
            DB::beginTransaction();

            // Create order
            $order = Order::create([
                'customer_id' => $request->customer_id,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'delivery_address' => $request->delivery_address,
                'delivery_time' => $request->delivery_time,
                'payment_method' => $request->payment_method,
                'items' => $mealItems
            ]);

            // Commit transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => [
                    'order_id' => $order->id,
                    'total_amount' => $order->total_amount,
                    'status' => $order->status
                ]
            ], 201);
        } catch (CustomerException $e) {
            DB::rollBack();
            Log::error('Customer service error', [
                'error' => $e->getMessage(),
                'customer_id' => $request->customer_id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error getting customer information: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order creation error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error creating order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process payment for an order.
     */
    public function processPayment(ProcessPaymentRequest $request, int $id): JsonResponse
    {
        try {
            // Get order
            $order = Order::findOrFail($id);

            // Get customer from Customer Service
            $customer = $this->customerService->getCustomer($order->customer_id);
            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Create payment request
            $paymentRequest = new PaymentRequestDTO(
                $customer['pk_customer_code'],
                $customer['email_address'] ?? '',
                $customer['phone'],
                $customer['customer_name'],
                $order->total_amount,
                (string) $order->id,
                $request->wallet_amount ?? 0,
                route('api.orders.payment.success', ['id' => $order->id]),
                route('api.orders.payment.failure', ['id' => $order->id]),
                'quickserve',
                'order',
                false,
                0
            );

            // Initiate payment
            $paymentResponse = $this->paymentService->initiatePayment($paymentRequest);

            // Process payment with selected gateway
            $formData = $this->paymentService->processPayment(
                $paymentResponse->transactionId,
                $request->gateway
            );

            // Update order with payment information
            $order->payment_transaction_id = $paymentResponse->transactionId;
            $order->payment_status = 'initiated';
            $order->save();

            return response()->json([
                'success' => true,
                'message' => 'Payment processing initiated',
                'data' => $formData
            ]);
        } catch (PaymentException $e) {
            Log::error('Payment service error', [
                'error' => $e->getMessage(),
                'order_id' => $id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error processing payment: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            Log::error('Payment processing error', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error processing payment: ' . $e->getMessage()
            ], 500);
        }
    }
}

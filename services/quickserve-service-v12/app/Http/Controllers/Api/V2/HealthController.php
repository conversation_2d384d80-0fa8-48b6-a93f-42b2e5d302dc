<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\Customer\CustomerServiceClient;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HealthController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The customer service client instance.
         */
        protected \App\Services\Customer\CustomerServiceClient $customerService,
        /**
         * The payment service client instance.
         */
        protected \App\Services\Payment\PaymentServiceClient $paymentService
    )
    {
    }

    /**
     * Get the health status of the service.
     */
    public function index(): JsonResponse
    {
        $status = 'healthy';
        $checks = [
            'database' => $this->checkDatabase(),
            'customer_service' => $this->checkCustomerService(),
            'payment_service' => $this->checkPaymentService(),
        ];

        // If any check failed, set status to error
        foreach ($checks as $check) {
            if ($check['status'] !== 'ok' && $check['status'] !== 'healthy') {
                $status = 'degraded';

                if ($check['status'] === 'error' || $check['status'] === 'down') {
                    $status = 'down';
                    break;
                }
            }
        }

        return response()->json([
            'status' => $status,
            'service' => 'quickserve-service',
            'version' => config('app.version', '1.0.0'),
            'timestamp' => now()->toIso8601String(),
            'checks' => $checks,
        ]);
    }

    /**
     * Get the detailed health status of the service.
     */
    public function detailed(): JsonResponse
    {
        $status = 'healthy';
        $checks = [
            'database' => $this->checkDatabase(),
            'customer_service' => $this->checkCustomerService(),
            'payment_service' => $this->checkPaymentService(),
            'disk' => $this->checkDiskSpace(),
            'memory' => $this->checkMemory(),
            'cache' => $this->checkCache(),
        ];

        // If any check failed, set status to error
        foreach ($checks as $check) {
            if ($check['status'] !== 'ok' && $check['status'] !== 'healthy') {
                $status = 'degraded';

                if ($check['status'] === 'error' || $check['status'] === 'down') {
                    $status = 'down';
                    break;
                }
            }
        }

        return response()->json([
            'status' => $status,
            'service' => 'quickserve-service',
            'version' => config('app.version', '1.0.0'),
            'timestamp' => now()->toIso8601String(),
            'checks' => $checks,
            'metrics' => $this->getMetrics(),
        ]);
    }

    /**
     * Check the database connection.
     */
    protected function checkDatabase(): array
    {
        try {
            // Try to execute a simple query
            DB::select('SELECT 1');

            return [
                'status' => 'ok',
                'message' => 'Database connection successful',
            ];
        } catch (\Exception $e) {
            Log::error('Health check: Database connection failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'Database connection failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check the customer service connection.
     */
    protected function checkCustomerService(): array
    {
        try {
            // Try to ping the customer service
            $response = $this->customerService->ping();

            if ($response) {
                return [
                    'status' => 'ok',
                    'message' => 'Customer service connection successful',
                ];
            }
            return [
                'status' => 'error',
                'message' => 'Customer service connection failed',
            ];
        } catch (\Exception $e) {
            Log::error('Health check: Customer service connection failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'Customer service connection failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check the payment service connection.
     */
    protected function checkPaymentService(): array
    {
        try {
            // Try to ping the payment service
            $response = $this->paymentService->ping();

            if ($response) {
                return [
                    'status' => 'ok',
                    'message' => 'Payment service connection successful',
                ];
            }
            return [
                'status' => 'error',
                'message' => 'Payment service connection failed',
            ];
        } catch (\Exception $e) {
            Log::error('Health check: Payment service connection failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'Payment service connection failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check the disk space.
     */
    protected function checkDiskSpace(): array
    {
        $diskFree = disk_free_space(storage_path());
        $diskTotal = disk_total_space(storage_path());
        $diskUsed = $diskTotal - $diskFree;
        $diskUsedPercentage = round(($diskUsed / $diskTotal) * 100, 2);

        $status = 'ok';
        $message = 'Disk space is sufficient';

        if ($diskUsedPercentage > 90) {
            $status = 'error';
            $message = 'Disk space is critically low';
        } elseif ($diskUsedPercentage > 70) {
            $status = 'warning';
            $message = 'Disk space is running low';
        }

        return [
            'status' => $status,
            'message' => $message,
            'disk_free_bytes' => $diskFree,
            'disk_total_bytes' => $diskTotal,
            'disk_used_percentage' => $diskUsedPercentage,
        ];
    }

    /**
     * Check the memory usage.
     */
    protected function checkMemory(): array
    {
        $memoryLimit = $this->getMemoryLimitInBytes();
        $memoryUsage = memory_get_usage(true);
        $memoryUsagePercentage = round(($memoryUsage / $memoryLimit) * 100, 2);

        $status = 'ok';
        $message = 'Memory usage is normal';

        if ($memoryUsagePercentage > 90) {
            $status = 'error';
            $message = 'Memory usage is critically high';
        } elseif ($memoryUsagePercentage > 70) {
            $status = 'warning';
            $message = 'Memory usage is high';
        }

        return [
            'status' => $status,
            'message' => $message,
            'memory_limit_bytes' => $memoryLimit,
            'memory_usage_bytes' => $memoryUsage,
            'memory_usage_percentage' => $memoryUsagePercentage,
        ];
    }

    /**
     * Check the cache.
     */
    protected function checkCache(): array
    {
        try {
            $key = 'health_check_' . time();
            $value = 'test';

            $startTime = microtime(true);
            cache()->put($key, $value, 10);
            $cachedValue = cache()->get($key);
            cache()->forget($key);
            $endTime = microtime(true);

            $responseTime = round(($endTime - $startTime) * 1000, 2); // in milliseconds

            if ($cachedValue === $value) {
                return [
                    'status' => 'ok',
                    'response_time_ms' => $responseTime,
                    'message' => 'Cache is working properly',
                ];
            }
            return [
                'status' => 'error',
                'message' => 'Cache is not working properly',
            ];
        } catch (\Exception $e) {
            Log::error('Cache health check failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'Cache check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get metrics.
     */
    protected function getMetrics(): array
    {
        return [
            'uptime' => $this->getUptime(),
            'request_count' => $this->getRequestCount(),
            'average_response_time' => $this->getAverageResponseTime(),
            'error_count' => $this->getErrorCount(),
        ];
    }

    /**
     * Get the uptime.
     */
    protected function getUptime(): array
    {
        $uptimeSeconds = time() - LARAVEL_START;

        return [
            'seconds' => $uptimeSeconds,
            'formatted' => $this->formatUptime($uptimeSeconds),
        ];
    }

    /**
     * Format the uptime.
     */
    protected function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $seconds %= 86400;

        $hours = floor($seconds / 3600);
        $seconds %= 3600;

        $minutes = floor($seconds / 60);
        $seconds %= 60;

        return "{$days}d {$hours}h {$minutes}m {$seconds}s";
    }

    /**
     * Get the request count.
     */
    protected function getRequestCount(): int
    {
        // In a real implementation, this would be retrieved from a metrics store
        return 0;
    }

    /**
     * Get the average response time.
     */
    protected function getAverageResponseTime(): float
    {
        // In a real implementation, this would be retrieved from a metrics store
        return 0.0;
    }

    /**
     * Get the error count.
     */
    protected function getErrorCount(): int
    {
        // In a real implementation, this would be retrieved from a metrics store
        return 0;
    }

    /**
     * Get the memory limit in bytes.
     */
    protected function getMemoryLimitInBytes(): int
    {
        $memoryLimit = ini_get('memory_limit');

        if ($memoryLimit === '-1') {
            // No memory limit
            return PHP_INT_MAX;
        }

        $value = (int) $memoryLimit;
        $unit = strtolower(substr($memoryLimit, -1));

        switch ($unit) {
            case 'g':
                $value *= 1024;
                // no break
            case 'm':
                $value *= 1024;
                // no break
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}

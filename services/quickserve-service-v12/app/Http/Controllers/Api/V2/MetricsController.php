<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Prometheus\CollectorRegistry;
use Prometheus\RenderTextFormat;

class MetricsController extends Controller
{
    /**
     * MetricsController constructor.
     */
    public function __construct(protected \Prometheus\CollectorRegistry $registry)
    {
    }

    /**
     * Export metrics in Prometheus format.
     *
     * @return \Illuminate\Http\Response
     */
    public function export()
    {
        // Collect metrics
        $this->collectOrderMetrics();
        $this->collectProductMetrics();
        $this->collectPerformanceMetrics();
        $this->collectHealthMetrics();
        $this->collectBusinessMetrics();

        // Render metrics in Prometheus format
        $renderer = new RenderTextFormat();
        $result = $renderer->render($this->registry->getMetricFamilySamples());

        return response($result, 200)
            ->header('Content-Type', 'text/plain; version=0.0.4');
    }

    /**
     * Collect order metrics.
     */
    protected function collectOrderMetrics(): void
    {
        try {
            // Create gauges for order counts
            $orderGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'orders_total',
                'Total number of orders',
                ['status']
            );

            // Get order counts by status
            $orderCounts = DB::table('orders')
                ->select('order_status', DB::raw('count(*) as count'))
                ->groupBy('order_status')
                ->get();

            // Reset gauges
            $orderGauge->set(0, ['status' => 'total']);
            $orderGauge->set(0, ['status' => 'new']);
            $orderGauge->set(0, ['status' => 'processing']);
            $orderGauge->set(0, ['status' => 'completed']);
            $orderGauge->set(0, ['status' => 'cancelled']);

            // Set gauges with current values
            $totalOrders = 0;
            foreach ($orderCounts as $count) {
                $status = strtolower((string) $count->order_status);
                $orderGauge->set($count->count, ['status' => $status]);
                $totalOrders += $count->count;
            }
            $orderGauge->set($totalOrders, ['status' => 'total']);

            // Create gauge for order amount
            $orderAmountGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'orders_amount_total',
                'Total amount of orders',
                []
            );

            // Get total order amount
            $totalAmount = DB::table('orders')->sum('amount');
            $orderAmountGauge->set($totalAmount);

            // Create gauge for average order amount
            $avgOrderAmountGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'orders_amount_average',
                'Average amount of orders',
                []
            );

            // Get average order amount
            $avgAmount = $totalOrders > 0 ? $totalAmount / $totalOrders : 0;
            $avgOrderAmountGauge->set($avgAmount);

            // Create gauge for orders by delivery status
            $deliveryStatusGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'orders_by_delivery_status',
                'Number of orders by delivery status',
                ['status']
            );

            // Get order counts by delivery status
            $deliveryStatusCounts = DB::table('orders')
                ->select('delivery_status', DB::raw('count(*) as count'))
                ->groupBy('delivery_status')
                ->get();

            // Reset gauges
            $deliveryStatusGauge->set(0, ['status' => 'pending']);
            $deliveryStatusGauge->set(0, ['status' => 'in_transit']);
            $deliveryStatusGauge->set(0, ['status' => 'delivered']);
            $deliveryStatusGauge->set(0, ['status' => 'failed']);

            // Set gauges with current values
            foreach ($deliveryStatusCounts as $count) {
                $status = strtolower(str_replace(' ', '_', $count->delivery_status));
                $deliveryStatusGauge->set($count->count, ['status' => $status]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to collect order metrics', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Collect product metrics.
     */
    protected function collectProductMetrics(): void
    {
        try {
            // Create gauges for product counts
            $productGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'products_total',
                'Total number of products',
                ['type', 'food_type']
            );

            // Get product counts by type and food type
            $productCounts = DB::table('products')
                ->select('product_type', 'food_type', DB::raw('count(*) as count'))
                ->groupBy('product_type', 'food_type')
                ->get();

            // Reset gauges
            $productGauge->set(0, ['type' => 'total', 'food_type' => 'total']);

            // Set gauges with current values
            $totalProducts = 0;
            foreach ($productCounts as $count) {
                $type = strtolower((string) $count->product_type);
                $foodType = strtolower((string) $count->food_type);
                $productGauge->set($count->count, ['type' => $type, 'food_type' => $foodType]);
                $totalProducts += $count->count;
            }
            $productGauge->set($totalProducts, ['type' => 'total', 'food_type' => 'total']);

            // Create gauge for active products
            $activeProductGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'products_active',
                'Number of active products',
                []
            );

            // Get active product count
            $activeProductCount = DB::table('products')->where('status', true)->count();
            $activeProductGauge->set($activeProductCount);
        } catch (\Exception $e) {
            Log::error('Failed to collect product metrics', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Collect performance metrics.
     */
    protected function collectPerformanceMetrics(): void
    {
        try {
            // Create gauges for performance metrics
            $memoryGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'memory_usage_bytes',
                'Memory usage in bytes',
                []
            );

            $memoryPeakGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'memory_peak_usage_bytes',
                'Peak memory usage in bytes',
                []
            );

            $uptimeGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'uptime_seconds',
                'Uptime in seconds',
                []
            );

            // Set gauges with current values
            $memoryGauge->set(memory_get_usage(true));
            $memoryPeakGauge->set(memory_get_peak_usage(true));
            $uptimeGauge->set(time() - LARAVEL_START);

            // Create gauge for database connection time
            $dbConnectionGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'db_connection_time_seconds',
                'Database connection time in seconds',
                []
            );

            // Measure database connection time
            $startTime = microtime(true);
            DB::connection()->getPdo();
            $endTime = microtime(true);
            $dbConnectionGauge->set($endTime - $startTime);
        } catch (\Exception $e) {
            Log::error('Failed to collect performance metrics', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Collect health check metrics.
     */
    protected function collectHealthMetrics(): void
    {
        try {
            // Create gauge for health check status
            $healthGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'health_check',
                'Health check status (1 = healthy, 0 = unhealthy)',
                ['check', 'status']
            );

            // Database health check
            try {
                DB::connection()->getPdo();
                $healthGauge->set(1, ['check' => 'database', 'status' => 'healthy']);
            } catch (\Exception $e) {
                $healthGauge->set(0, ['check' => 'database', 'status' => 'unhealthy']);
            }

            // Cache health check
            try {
                $testKey = 'health_check_' . time();
                \Illuminate\Support\Facades\Cache::put($testKey, 'test', 60);
                $retrieved = \Illuminate\Support\Facades\Cache::get($testKey);
                \Illuminate\Support\Facades\Cache::forget($testKey);

                if ($retrieved === 'test') {
                    $healthGauge->set(1, ['check' => 'cache', 'status' => 'healthy']);
                } else {
                    $healthGauge->set(0, ['check' => 'cache', 'status' => 'unhealthy']);
                }
            } catch (\Exception $e) {
                $healthGauge->set(0, ['check' => 'cache', 'status' => 'unhealthy']);
            }

            // Memory usage percentage
            $memoryUsageGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'memory_usage_percentage',
                'Memory usage percentage',
                []
            );

            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
            $usagePercentage = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;
            $memoryUsageGauge->set($usagePercentage);

            // Disk usage percentage
            $diskUsageGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'disk_usage_percentage',
                'Disk usage percentage',
                []
            );

            $path = storage_path();
            $totalBytes = disk_total_space($path);
            $freeBytes = disk_free_space($path);
            $usedBytes = $totalBytes - $freeBytes;
            $diskUsagePercentage = ($usedBytes / $totalBytes) * 100;
            $diskUsageGauge->set($diskUsagePercentage);

        } catch (\Exception $e) {
            Log::error('Failed to collect health metrics', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Collect business metrics.
     */
    protected function collectBusinessMetrics(): void
    {
        try {
            // Orders processed in last 24 hours
            $ordersLast24hGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'orders_last_24h',
                'Orders processed in last 24 hours',
                ['status']
            );

            $ordersLast24h = DB::table('orders')
                ->where('created_at', '>=', now()->subDay())
                ->select('order_status', DB::raw('count(*) as count'))
                ->groupBy('order_status')
                ->get();

            foreach ($ordersLast24h as $order) {
                $status = strtolower((string) $order->order_status);
                $ordersLast24hGauge->set($order->count, ['status' => $status]);
            }

            // Revenue in last 24 hours
            $revenueLast24hGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'revenue_last_24h',
                'Revenue in last 24 hours',
                []
            );

            $revenueLast24h = DB::table('orders')
                ->where('created_at', '>=', now()->subDay())
                ->where('order_status', 'completed')
                ->sum('amount');

            $revenueLast24hGauge->set($revenueLast24h);

            // Average order processing time
            $avgProcessingTimeGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'avg_order_processing_time_minutes',
                'Average order processing time in minutes',
                []
            );

            $avgProcessingTime = DB::table('orders')
                ->whereNotNull('completed_at')
                ->where('created_at', '>=', now()->subDay())
                ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, created_at, completed_at)) as avg_time')
                ->value('avg_time');

            $avgProcessingTimeGauge->set($avgProcessingTime ?? 0);

            // Customer satisfaction metrics (if available)
            $customerSatisfactionGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'customer_satisfaction_rating',
                'Average customer satisfaction rating',
                []
            );

            $avgRating = DB::table('order_ratings')
                ->where('created_at', '>=', now()->subDay())
                ->avg('rating');

            $customerSatisfactionGauge->set($avgRating ?? 0);

            // Failed orders rate
            $failedOrdersRateGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'failed_orders_rate_percentage',
                'Failed orders rate percentage',
                []
            );

            $totalOrdersLast24h = DB::table('orders')
                ->where('created_at', '>=', now()->subDay())
                ->count();

            $failedOrdersLast24h = DB::table('orders')
                ->where('created_at', '>=', now()->subDay())
                ->where('order_status', 'failed')
                ->count();

            $failedOrdersRate = $totalOrdersLast24h > 0 ? ($failedOrdersLast24h / $totalOrdersLast24h) * 100 : 0;
            $failedOrdersRateGauge->set($failedOrdersRate);

            // Payment success rate
            $paymentSuccessRateGauge = $this->registry->getOrRegisterGauge(
                'quickserve',
                'payment_success_rate_percentage',
                'Payment success rate percentage',
                []
            );

            $totalPaymentsLast24h = DB::table('payments')
                ->where('created_at', '>=', now()->subDay())
                ->count();

            $successfulPaymentsLast24h = DB::table('payments')
                ->where('created_at', '>=', now()->subDay())
                ->where('status', 'completed')
                ->count();

            $paymentSuccessRate = $totalPaymentsLast24h > 0 ? ($successfulPaymentsLast24h / $totalPaymentsLast24h) * 100 : 0;
            $paymentSuccessRateGauge->set($paymentSuccessRate);

        } catch (\Exception $e) {
            Log::error('Failed to collect business metrics', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Parse memory limit string to bytes
     *
     * @param string $limit
     * @return int
     */
    private function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return 0; // No limit
        }

        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
                // fall through
            case 'm':
                $value *= 1024;
                // fall through
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Requests\Config\UpdateConfigRequest;
use App\Services\ActivityLogService;
use App\Services\ConfigService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ConfigController extends BaseController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The config service instance.
         */
        protected \App\Services\ConfigService $configService,
        /**
         * The activity log service instance.
         */
        protected \App\Services\ActivityLogService $activityLogService
    )
    {
    }

    /**
     * Get all configuration values.
     */
    public function index(): JsonResponse
    {
        try {
            $config = $this->configService->getAllConfig();
            return $this->sendResponse($config, 'Configuration retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving configuration: ' . $e->getMessage());
        }
    }

    /**
     * Get a specific configuration value.
     */
    public function show(string $key): JsonResponse
    {
        try {
            if (!$this->configService->hasConfig($key)) {
                return $this->sendNotFoundResponse("Configuration key '{$key}' not found");
            }

            $value = $this->configService->getConfig($key);
            return $this->sendResponse(['key' => $key, 'value' => $value], 'Configuration retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving configuration: ' . $e->getMessage());
        }
    }

    /**
     * Update a configuration value.
     */
    public function update(UpdateConfigRequest $request, string $key): JsonResponse
    {
        try {
            $value = $request->input('value');
            $result = $this->configService->setConfig($key, $value);

            if ($result) {
                // Log the activity
                $this->activityLogService->log(
                    'update_config',
                    "Updated configuration key: {$key}",
                    ['context_name' => 'config', 'context_type' => 'configuration']
                );

                return $this->sendResponse(
                    ['key' => $key, 'value' => $value],
                    'Configuration updated successfully'
                );
            }

            return $this->sendError('Failed to update configuration');
        } catch (\Exception $e) {
            return $this->sendError('Error updating configuration: ' . $e->getMessage());
        }
    }

    /**
     * Get all settings.
     */
    public function settings(): JsonResponse
    {
        try {
            $settings = $this->configService->getAllSettings();
            return $this->sendResponse($settings, 'Settings retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving settings: ' . $e->getMessage());
        }
    }
}

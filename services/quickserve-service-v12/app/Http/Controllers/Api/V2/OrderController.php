<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Exceptions\Customer\CustomerException;
use App\Exceptions\Order\OrderException;
use App\Exceptions\Payment\PaymentException;
use App\Http\Requests\OrderCreateRequest;
use App\Http\Requests\OrderUpdateRequest;
use App\Http\Resources\OrderResource;
use App\Models\Order;
use App\Services\OrderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class OrderController extends BaseController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The order service instance.
         */
        protected \App\Services\OrderService $orderService
    )
    {
    }

    /**
     * Display a listing of the orders.
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $orders = $this->orderService->getAllOrders($request->all());
            return OrderResource::collection($orders);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving orders: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created order in storage.
     */
    public function store(OrderCreateRequest $request): JsonResponse
    {
        try {
            $order = $this->orderService->createOrder($request->validated());
            return $this->sendResponse(
                new OrderResource($order),
                'Order created successfully',
                201
            );
        } catch (\Exception $e) {
            return $this->sendError('Error creating order: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified order.
     */
    public function show(int $id): OrderResource|JsonResponse
    {
        try {
            $order = $this->orderService->getOrderById($id);

            if (!$order instanceof \App\Models\Order) {
                return $this->sendNotFoundResponse('Order not found');
            }

            return new OrderResource($order);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving order: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified order in storage.
     */
    public function update(OrderUpdateRequest $request, int $id): OrderResource|JsonResponse
    {
        try {
            $order = $this->orderService->updateOrder($id, $request->validated());

            if (!$order) {
                return $this->sendNotFoundResponse('Order not found');
            }

            return new OrderResource($order);
        } catch (\Exception $e) {
            return $this->sendError('Error updating order: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified order from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $result = $this->orderService->deleteOrder($id);

            if (!$result) {
                return $this->sendNotFoundResponse('Order not found');
            }

            return $this->sendResponse(null, 'Order deleted successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error deleting order: ' . $e->getMessage());
        }
    }

    /**
     * Get orders by customer.
     */
    public function getByCustomer(Request $request, int $customerId): AnonymousResourceCollection|JsonResponse
    {
        try {
            $orders = $this->orderService->getOrdersByCustomer($customerId, $request->all());
            return OrderResource::collection($orders);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving customer orders: ' . $e->getMessage());
        }
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, int $id): OrderResource|JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|string|in:New,Processing,Completed,Cancel,Partial Cancel',
            ]);

            $order = $this->orderService->updateOrderStatus($id, $request->status);

            if (!$order instanceof \App\Models\Order) {
                return $this->sendNotFoundResponse('Order not found');
            }

            return new OrderResource($order);
        } catch (\Exception $e) {
            return $this->sendError('Error updating order status: ' . $e->getMessage());
        }
    }

    /**
     * Update delivery status.
     */
    public function updateDeliveryStatus(Request $request, int $id): OrderResource|JsonResponse
    {
        try {
            $request->validate([
                'delivery_status' => 'required|string|in:Pending,Dispatched,Delivered,Failed',
            ]);

            $order = $this->orderService->updateDeliveryStatus($id, $request->delivery_status);

            if (!$order instanceof \App\Models\Order) {
                return $this->sendNotFoundResponse('Order not found');
            }

            return new OrderResource($order);
        } catch (\Exception $e) {
            return $this->sendError('Error updating delivery status: ' . $e->getMessage());
        }
    }

    /**
     * Cancel the specified order.
     */
    public function cancel(Request $request, int $id): OrderResource|JsonResponse
    {
        try {
            $request->validate([
                'reason' => 'nullable|string|max:255',
            ]);

            $order = $this->orderService->cancelOrder($id, $request->input('reason'));

            if (!$order) {
                return $this->sendNotFoundResponse('Order not found');
            }

            return new OrderResource($order);
        } catch (OrderException $e) {
            if (str_contains($e->getMessage(), 'not found')) {
                return $this->sendNotFoundResponse('Order not found');
            }

            return $this->sendError($e->getMessage());
        } catch (\Exception $e) {
            Log::error('Failed to cancel order', [
                'error' => $e->getMessage(),
                'order_id' => $id,
            ]);

            return $this->sendError('Failed to cancel order: ' . $e->getMessage());
        }
    }

    /**
     * Process payment for the specified order.
     */
    public function processPayment(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'gateway' => 'required|string',
                'wallet_amount' => 'nullable|numeric|min:0',
            ]);

            $paymentResponse = $this->orderService->processPayment(
                $id,
                $request->input('gateway'),
                (float) $request->input('wallet_amount', 0.0)
            );

            return $this->sendResponse($paymentResponse, 'Payment initiated successfully');
        } catch (OrderException|CustomerException|PaymentException $e) {
            return $this->sendError($e->getMessage());
        } catch (\Exception $e) {
            Log::error('Failed to process payment', [
                'error' => $e->getMessage(),
                'order_id' => $id,
                'data' => $request->all(),
            ]);

            return $this->sendError('Failed to process payment: ' . $e->getMessage());
        }
    }
}

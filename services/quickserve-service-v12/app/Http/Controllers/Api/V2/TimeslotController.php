<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Requests\Timeslot\StoreTimeslotRequest;
use App\Http\Requests\Timeslot\UpdateTimeslotRequest;
use App\Http\Resources\TimeslotResource;
use App\Services\ActivityLogService;
use App\Services\TimeslotService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TimeslotController extends BaseController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The timeslot service instance.
         */
        protected \App\Services\TimeslotService $timeslotService,
        /**
         * The activity log service instance.
         */
        protected \App\Services\ActivityLogService $activityLogService
    )
    {
    }

    /**
     * Display a listing of the timeslots.
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $timeslots = $this->timeslotService->getAllTimeslots($request->all());
            return TimeslotResource::collection($timeslots);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving timeslots: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created timeslot in storage.
     */
    public function store(StoreTimeslotRequest $request): JsonResponse
    {
        try {
            // Check if timeslot already exists
            if (!$this->timeslotService->isTimeslotAvailable(
                $request->input('starttime'),
                $request->input('day'),
                $request->input('menu_type'),
                $request->input('kitchen')
            )) {
                return $this->sendError('Timeslot already exists for this day, menu type, and kitchen.');
            }

            $timeslot = $this->timeslotService->createTimeslot($request->validated());

            // Log the activity
            $this->activityLogService->log(
                'create_timeslot',
                "Created new timeslot: {$timeslot->day} {$timeslot->menu_type} {$timeslot->formatted_start_time}-{$timeslot->formatted_end_time}",
                ['context_name' => 'timeslot', 'context_type' => 'timeslot', 'context_ref_id' => $timeslot->id]
            );

            return $this->sendResponse(
                new TimeslotResource($timeslot),
                'Timeslot created successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error creating timeslot: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified timeslot.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $timeslot = $this->timeslotService->getTimeslot($id);

            if (!$timeslot instanceof \App\Models\Timeslot) {
                return $this->sendNotFoundResponse('Timeslot not found');
            }

            return $this->sendResponse(
                new TimeslotResource($timeslot),
                'Timeslot retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving timeslot: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified timeslot in storage.
     */
    public function update(UpdateTimeslotRequest $request, int $id): JsonResponse
    {
        try {
            $timeslot = $this->timeslotService->getTimeslot($id);

            if (!$timeslot instanceof \App\Models\Timeslot) {
                return $this->sendNotFoundResponse('Timeslot not found');
            }

            // Check if timeslot already exists (if changing key fields)
            if ($request->has(['starttime', 'day', 'menu_type', 'kitchen']) &&
                !$this->timeslotService->isTimeslotAvailable(
                    $request->input('starttime'),
                    $request->input('day'),
                    $request->input('menu_type'),
                    $request->input('kitchen'),
                    $id
                )) {
                return $this->sendError('Timeslot already exists for this day, menu type, and kitchen.');
            }

            $updatedTimeslot = $this->timeslotService->updateTimeslot($id, $request->validated());

            // Log the activity
            $this->activityLogService->log(
                'update_timeslot',
                "Updated timeslot: {$updatedTimeslot->day} {$updatedTimeslot->menu_type} {$updatedTimeslot->formatted_start_time}-{$updatedTimeslot->formatted_end_time}",
                ['context_name' => 'timeslot', 'context_type' => 'timeslot', 'context_ref_id' => $id]
            );

            return $this->sendResponse(
                new TimeslotResource($updatedTimeslot),
                'Timeslot updated successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error updating timeslot: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified timeslot from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $timeslot = $this->timeslotService->getTimeslot($id);

            if (!$timeslot instanceof \App\Models\Timeslot) {
                return $this->sendNotFoundResponse('Timeslot not found');
            }

            $result = $this->timeslotService->deleteTimeslot($id);

            if ($result) {
                // Log the activity
                $this->activityLogService->log(
                    'delete_timeslot',
                    "Deleted timeslot: {$timeslot->day} {$timeslot->menu_type} {$timeslot->formatted_start_time}-{$timeslot->formatted_end_time}",
                    ['context_name' => 'timeslot', 'context_type' => 'timeslot', 'context_ref_id' => $id]
                );

                return $this->sendResponse([], 'Timeslot deleted successfully');
            }

            return $this->sendError('Failed to delete timeslot');
        } catch (\Exception $e) {
            return $this->sendError('Error deleting timeslot: ' . $e->getMessage());
        }
    }

    /**
     * Get available timeslots for a specific day, menu type, and kitchen.
     */
    public function available(Request $request): AnonymousResourceCollection|JsonResponse
    {
        // Validate request parameters - this will automatically return 422 if validation fails
        $request->validate([
            'day' => 'required|string',
            'menu_type' => 'required|string',
            'kitchen' => 'required|string',
            'order_date' => 'nullable|date_format:Y-m-d'
        ]);

        try {
            $timeslots = $this->timeslotService->getAvailableTimeslots(
                $request->input('day'),
                $request->input('menu_type'),
                $request->input('kitchen'),
                $request->input('order_date')
            );

            return TimeslotResource::collection($timeslots);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving available timeslots: ' . $e->getMessage());
        }
    }
}

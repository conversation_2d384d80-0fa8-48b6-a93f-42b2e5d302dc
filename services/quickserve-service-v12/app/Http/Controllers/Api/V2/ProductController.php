<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Requests\ProductCreateRequest;
use App\Http\Requests\ProductUpdateRequest;
use App\Http\Resources\ProductResource;
use App\Models\Product;
use App\Services\ProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ProductController extends BaseController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The product service instance.
         */
        protected \App\Services\ProductService $productService
    )
    {
    }

    /**
     * Display a listing of the products.
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $products = $this->productService->getAllProducts($request->all());
            return ProductResource::collection($products);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving products: ' . $e->getMessage());
        }
    }

    /**
     * Display a paginated listing of the products.
     */
    public function paginate(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $filters = $request->except('per_page');
            $products = $this->productService->getPaginatedProducts((int) $perPage, $filters);

            return response()->json([
                'success' => true,
                'data' => $products,
            ]);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving paginated products: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(ProductCreateRequest $request): JsonResponse
    {
        try {
            $product = $this->productService->createProduct($request->validated());
            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => new ProductResource($product),
            ], 201);
        } catch (\Exception $e) {
            return $this->sendError('Error creating product: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified product.
     */
    public function show(int $id): ProductResource|JsonResponse
    {
        try {
            $product = $this->productService->getProductById($id);

            if (!$product instanceof \App\Models\Product) {
                return $this->sendNotFoundResponse('Product not found');
            }

            return new ProductResource($product);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving product: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified product in storage.
     */
    public function update(ProductUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $product = $this->productService->updateProduct($id, $request->validated());

            if (!$product instanceof \App\Models\Product) {
                return $this->sendNotFoundResponse('Product not found');
            }

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'data' => new ProductResource($product),
            ], 200);
        } catch (\Exception $e) {
            return $this->sendError('Error updating product: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $result = $this->productService->deleteProduct($id);

            if (!$result) {
                return $this->sendNotFoundResponse('Product not found');
            }

            return $this->sendResponse(null, 'Product deleted successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error deleting product: ' . $e->getMessage());
        }
    }

    /**
     * Get products by type.
     */
    public function getByType(string $type): AnonymousResourceCollection|JsonResponse
    {
        try {
            $products = $this->productService->getProductsByType($type);
            return ProductResource::collection($products);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving products by type: ' . $e->getMessage());
        }
    }

    /**
     * Get products by food type.
     */
    public function getByFoodType(string $foodType): AnonymousResourceCollection|JsonResponse
    {
        try {
            $products = $this->productService->getProductsByFoodType($foodType);
            return ProductResource::collection($products);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving products by food type: ' . $e->getMessage());
        }
    }

    /**
     * Get products by kitchen.
     */
    public function getByKitchen(int $kitchenId): AnonymousResourceCollection|JsonResponse
    {
        try {
            $products = $this->productService->getProductsByKitchen($kitchenId);
            return ProductResource::collection($products);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving products by kitchen: ' . $e->getMessage());
        }
    }

    /**
     * Get products by category.
     */
    public function getByCategory(string $category): AnonymousResourceCollection|JsonResponse
    {
        try {
            $products = $this->productService->getProductsByCategory($category);
            return ProductResource::collection($products);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving products by category: ' . $e->getMessage());
        }
    }

    /**
     * Update product sequences.
     */
    public function updateSequence(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'product_ids' => 'required|array',
                'product_ids.*' => 'required|integer|exists:products,pk_product_code',
            ]);

            $this->productService->updateProductSequence($request->input('product_ids'));
            return $this->sendResponse([], 'Product sequence updated successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error updating product sequence: ' . $e->getMessage());
        }
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Requests\CustomerCreateRequest;
use App\Http\Requests\CustomerUpdateRequest;
use App\Http\Resources\CustomerResource;
use App\Models\Customer;
use App\Services\CustomerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CustomerController extends BaseController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The customer service instance.
         */
        protected \App\Services\CustomerService $customerService
    )
    {
    }

    /**
     * Display a listing of the customers.
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $customers = $this->customerService->getAllCustomers($request->all());
            return CustomerResource::collection($customers);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving customers: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(CustomerCreateRequest $request): CustomerResource|JsonResponse
    {
        try {
            $customer = $this->customerService->createCustomer($request->validated());
            return new CustomerResource($customer);
        } catch (\Exception $e) {
            return $this->sendError('Error creating customer: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified customer.
     */
    public function show(int $id): CustomerResource|JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerById($id);

            if (!$customer instanceof \App\Models\Customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            return new CustomerResource($customer);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving customer: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(CustomerUpdateRequest $request, int $id): CustomerResource|JsonResponse
    {
        try {
            $customer = $this->customerService->updateCustomer($id, $request->validated());

            if (!$customer instanceof \App\Models\Customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            return new CustomerResource($customer);
        } catch (\Exception $e) {
            return $this->sendError('Error updating customer: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $result = $this->customerService->deleteCustomer($id);

            if (!$result) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            return $this->sendResponse(null, 'Customer deleted successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error deleting customer: ' . $e->getMessage());
        }
    }

    /**
     * Get customer by phone.
     */
    public function getByPhone(string $phone): CustomerResource|JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerByPhone($phone);

            if (!$customer instanceof \App\Models\Customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            return new CustomerResource($customer);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving customer by phone: ' . $e->getMessage());
        }
    }

    /**
     * Get customer by email.
     */
    public function getByEmail(string $email): CustomerResource|JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerByEmail($email);

            if (!$customer instanceof \App\Models\Customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            return new CustomerResource($customer);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving customer by email: ' . $e->getMessage());
        }
    }

    /**
     * Get customer addresses.
     */
    public function getAddresses(int $id): JsonResponse
    {
        try {
            $addresses = $this->customerService->getCustomerAddresses($id);

            return $this->sendResponse($addresses);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving customer addresses: ' . $e->getMessage());
        }
    }

    /**
     * Get customer orders.
     */
    public function getOrders(int $id, Request $request): JsonResponse
    {
        try {
            $orders = $this->customerService->getCustomerOrders($id, $request->all());

            return $this->sendResponse($orders);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving customer orders: ' . $e->getMessage());
        }
    }

    /**
     * Send OTP to customer.
     */
    public function sendOtp(Request $request, int $id): JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerById($id);

            if (!$customer instanceof \App\Models\Customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            $method = $request->input('method', 'sms');
            $sent = $this->customerService->sendOtp($customer, $method);

            if (!$sent) {
                return $this->sendError("Failed to send OTP via {$method}");
            }

            return $this->sendResponse(['expires_in' => 600], "OTP sent via {$method}");
        } catch (\Exception $e) {
            return $this->sendError('Error sending OTP: ' . $e->getMessage());
        }
    }

    /**
     * Verify customer OTP.
     */
    public function verifyOtp(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'otp' => 'required|string|size:6',
                'method' => 'nullable|string|in:sms,email'
            ]);

            $customer = $this->customerService->getCustomerById($id);

            if (!$customer instanceof \App\Models\Customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            $otp = $request->input('otp');
            $method = $request->input('method', 'sms');
            $verified = $this->customerService->verifyOtp($customer, $otp, $method);

            if (!$verified) {
                return $this->sendError('Invalid or expired OTP', [], 400);
            }

            return $this->sendResponse(null, 'OTP verified successfully');
        } catch (\Exception $e) {
            return $this->sendError('Error verifying OTP: ' . $e->getMessage());
        }
    }
}

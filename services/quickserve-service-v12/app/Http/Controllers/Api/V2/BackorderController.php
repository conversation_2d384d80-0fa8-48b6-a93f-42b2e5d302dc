<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Requests\Backorder\StoreBackorderRequest;
use App\Http\Requests\Backorder\UpdateBackorderRequest;
use App\Http\Resources\BackorderResource;
use App\Models\Order;
use App\Services\ActivityLogService;
use App\Services\BackorderService;
use App\Services\OrderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class BackorderController extends BaseController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The backorder service instance.
         */
        protected \App\Services\BackorderService $backorderService,
        /**
         * The order service instance.
         */
        protected \App\Services\OrderService $orderService,
        /**
         * The activity log service instance.
         */
        protected \App\Services\ActivityLogService $activityLogService
    )
    {
    }

    /**
     * Display a listing of the backorders.
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $backorders = $this->backorderService->getAllBackorders($request->all());
            return BackorderResource::collection($backorders);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving backorders: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created backorder in storage.
     */
    public function store(StoreBackorderRequest $request): JsonResponse
    {
        try {
            $backorder = $this->backorderService->createBackorder($request->validated());

            // Log the activity
            $this->activityLogService->log(
                'create_backorder',
                "Created new backorder for order #{$backorder->order_no}",
                ['context_name' => 'backorder', 'context_type' => 'order', 'context_ref_id' => $backorder->id]
            );

            return $this->sendResponse(
                new BackorderResource($backorder),
                'Backorder created successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error creating backorder: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified backorder.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $backorder = $this->backorderService->getBackorder($id);

            if (!$backorder instanceof \App\Models\Backorder) {
                return $this->sendNotFoundResponse('Backorder not found');
            }

            return $this->sendResponse(
                new BackorderResource($backorder),
                'Backorder retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving backorder: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified backorder in storage.
     */
    public function update(UpdateBackorderRequest $request, int $id): JsonResponse
    {
        try {
            $backorder = $this->backorderService->getBackorder($id);

            if (!$backorder instanceof \App\Models\Backorder) {
                return $this->sendNotFoundResponse('Backorder not found');
            }

            $updatedBackorder = $this->backorderService->updateBackorder($id, $request->validated());

            // Log the activity
            $this->activityLogService->log(
                'update_backorder',
                "Updated backorder for order #{$updatedBackorder->order_no}",
                ['context_name' => 'backorder', 'context_type' => 'order', 'context_ref_id' => $id]
            );

            return $this->sendResponse(
                new BackorderResource($updatedBackorder),
                'Backorder updated successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error updating backorder: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified backorder from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $backorder = $this->backorderService->getBackorder($id);

            if (!$backorder instanceof \App\Models\Backorder) {
                return $this->sendNotFoundResponse('Backorder not found');
            }

            $result = $this->backorderService->deleteBackorder($id);

            if ($result) {
                // Log the activity
                $this->activityLogService->log(
                    'delete_backorder',
                    "Deleted backorder for order #{$backorder->order_no}",
                    ['context_name' => 'backorder', 'context_type' => 'order', 'context_ref_id' => $id]
                );

                return $this->sendResponse([], 'Backorder deleted successfully');
            }

            return $this->sendError('Failed to delete backorder');
        } catch (\Exception $e) {
            return $this->sendError('Error deleting backorder: ' . $e->getMessage());
        }
    }

    /**
     * Create a backorder from an order.
     */
    public function createFromOrder(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_id' => 'required|integer|exists:orders,pk_order_no',
                'reason' => 'required|string|max:255'
            ]);

            $order = Order::findOrFail($request->input('order_id'));
            $backorder = $this->backorderService->createBackorderFromOrder($order, $request->input('reason'));

            // Log the activity
            $this->activityLogService->log(
                'create_backorder_from_order',
                "Created backorder from order #{$order->order_no}",
                [
                    'context_name' => 'backorder',
                    'context_type' => 'order',
                    'context_ref_id' => $backorder->id
                ]
            );

            return $this->sendResponse(
                new BackorderResource($backorder),
                'Backorder created successfully from order'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error creating backorder from order: ' . $e->getMessage());
        }
    }

    /**
     * Complete a backorder.
     */
    public function complete(int $id): JsonResponse
    {
        try {
            $backorder = $this->backorderService->getBackorder($id);

            if (!$backorder instanceof \App\Models\Backorder) {
                return $this->sendNotFoundResponse('Backorder not found');
            }

            $completedBackorder = $this->backorderService->completeBackorder($id);

            // Log the activity
            $this->activityLogService->log(
                'complete_backorder',
                "Completed backorder for order #{$completedBackorder->order_no}",
                ['context_name' => 'backorder', 'context_type' => 'order', 'context_ref_id' => $id]
            );

            return $this->sendResponse(
                new BackorderResource($completedBackorder),
                'Backorder completed successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error completing backorder: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a backorder.
     */
    public function cancel(int $id): JsonResponse
    {
        try {
            $backorder = $this->backorderService->getBackorder($id);

            if (!$backorder instanceof \App\Models\Backorder) {
                return $this->sendNotFoundResponse('Backorder not found');
            }

            $cancelledBackorder = $this->backorderService->cancelBackorder($id);

            // Log the activity
            $this->activityLogService->log(
                'cancel_backorder',
                "Cancelled backorder for order #{$cancelledBackorder->order_no}",
                ['context_name' => 'backorder', 'context_type' => 'order', 'context_ref_id' => $id]
            );

            return $this->sendResponse(
                new BackorderResource($cancelledBackorder),
                'Backorder cancelled successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error cancelling backorder: ' . $e->getMessage());
        }
    }
}

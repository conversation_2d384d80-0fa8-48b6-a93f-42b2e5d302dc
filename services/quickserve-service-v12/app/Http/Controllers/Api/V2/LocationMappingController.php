<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Http\Controllers\Api\V2;

use App\Http\Requests\LocationMapping\StoreLocationMappingRequest;
use App\Http\Requests\LocationMapping\UpdateLocationMappingRequest;
use App\Http\Resources\LocationMappingResource;
use App\Services\ActivityLogService;
use App\Services\LocationMappingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class LocationMappingController extends BaseController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        /**
         * The location mapping service instance.
         */
        protected \App\Services\LocationMappingService $locationMappingService,
        /**
         * The activity log service instance.
         */
        protected \App\Services\ActivityLogService $activityLogService
    )
    {
    }

    /**
     * Display a listing of the location mappings.
     */
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $locationMappings = $this->locationMappingService->getAllLocationMappings($request->all());
            return LocationMappingResource::collection($locationMappings);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving location mappings: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created location mapping in storage.
     */
    public function store(StoreLocationMappingRequest $request): JsonResponse
    {
        try {
            $locationMapping = $this->locationMappingService->createLocationMapping($request->validated());

            // Log the activity
            $this->activityLogService->log(
                'create_location_mapping',
                "Created new location mapping: {$locationMapping->location_name}",
                ['context_name' => 'location_mapping', 'context_type' => 'location', 'context_ref_id' => $locationMapping->id]
            );

            return $this->sendResponse(
                new LocationMappingResource($locationMapping),
                'Location mapping created successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error creating location mapping: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified location mapping.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $locationMapping = $this->locationMappingService->getLocationMapping($id);

            if (!$locationMapping instanceof \App\Models\LocationMapping) {
                return $this->sendNotFoundResponse('Location mapping not found');
            }

            return $this->sendResponse(
                new LocationMappingResource($locationMapping),
                'Location mapping retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving location mapping: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified location mapping in storage.
     */
    public function update(UpdateLocationMappingRequest $request, int $id): JsonResponse
    {
        try {
            $locationMapping = $this->locationMappingService->getLocationMapping($id);

            if (!$locationMapping instanceof \App\Models\LocationMapping) {
                return $this->sendNotFoundResponse('Location mapping not found');
            }

            $updatedLocationMapping = $this->locationMappingService->updateLocationMapping($id, $request->validated());

            // Log the activity
            $this->activityLogService->log(
                'update_location_mapping',
                "Updated location mapping: {$updatedLocationMapping->location_name}",
                ['context_name' => 'location_mapping', 'context_type' => 'location', 'context_ref_id' => $id]
            );

            return $this->sendResponse(
                new LocationMappingResource($updatedLocationMapping),
                'Location mapping updated successfully'
            );
        } catch (\Exception $e) {
            return $this->sendError('Error updating location mapping: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified location mapping from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $locationMapping = $this->locationMappingService->getLocationMapping($id);

            if (!$locationMapping instanceof \App\Models\LocationMapping) {
                return $this->sendNotFoundResponse('Location mapping not found');
            }

            $result = $this->locationMappingService->deleteLocationMapping($id);

            if ($result) {
                // Log the activity
                $this->activityLogService->log(
                    'delete_location_mapping',
                    "Deleted location mapping: {$locationMapping->location_name}",
                    ['context_name' => 'location_mapping', 'context_type' => 'location', 'context_ref_id' => $id]
                );

                return $this->sendResponse([], 'Location mapping deleted successfully');
            }

            return $this->sendError('Failed to delete location mapping');
        } catch (\Exception $e) {
            return $this->sendError('Error deleting location mapping: ' . $e->getMessage());
        }
    }

    /**
     * Get locations by city.
     */
    public function byCity(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $request->validate([
                'city_code' => 'required|string'
            ]);

            $locations = $this->locationMappingService->getLocationsByCity($request->input('city_code'));
            return LocationMappingResource::collection($locations);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving locations by city: ' . $e->getMessage());
        }
    }

    /**
     * Get locations by kitchen.
     */
    public function byKitchen(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $request->validate([
                'kitchen_code' => 'required|string'
            ]);

            $locations = $this->locationMappingService->getLocationsByKitchen($request->input('kitchen_code'));
            return LocationMappingResource::collection($locations);
        } catch (\Exception $e) {
            return $this->sendError('Error retrieving locations by kitchen: ' . $e->getMessage());
        }
    }
}

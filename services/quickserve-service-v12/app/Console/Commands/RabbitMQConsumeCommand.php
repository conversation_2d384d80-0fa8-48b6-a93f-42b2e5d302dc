<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Console\Commands;

use App\Services\RabbitMQ\RabbitMQServiceInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RabbitMQConsumeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:consume {queue : The queue to consume} {--consumer-tag= : The consumer tag}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Consume messages from a RabbitMQ queue';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\RabbitMQServiceInterface $rabbitMQService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $queue = $this->argument('queue');
        $consumerTag = $this->option('consumer-tag');

        $this->info("Starting consumer for queue: {$queue}");

        try {
            $this->rabbitMQService->consume(
                $queue,
                function ($body, $properties, $message) use ($queue): true {
                    $this->processMessage($body, $properties, $message, $queue);
                    return true;
                },
                $consumerTag
            );

            return 0;
        } catch (\Exception $e) {
            $this->error('Error consuming messages: ' . $e->getMessage());
            Log::error('Error consuming messages', [
                'queue' => $queue,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * Process a message from the queue.
     *
     * @param \PhpAmqpLib\Message\AMQPMessage $message
     */
    protected function processMessage(array $body, array $properties, $message, string $queue): bool
    {
        $correlationId = $properties['correlation_id'] ?? 'unknown';
        $messageId = $properties['message_id'] ?? 'unknown';

        $this->info("Processing message from queue {$queue} (correlation_id: {$correlationId}, message_id: {$messageId})");

        try {
            // Handle different event types based on the queue and routing key
            $routingKey = $message->getDeliveryInfo()['routing_key'] ?? '';
            $event = $body['event'] ?? '';
            $data = $body['data'] ?? [];

            $this->info("Event: {$event}, Routing Key: {$routingKey}");

            // Process the message based on the queue and event type
            switch ($queue) {
                case 'quickserve.order':
                    return $this->processOrderEvent($event, $data);
                case 'quickserve.product':
                    return $this->processProductEvent($event, $data);
                case 'quickserve.customer':
                    return $this->processCustomerEvent($event, $data);
                case 'quickserve.timeslot':
                    return $this->processTimeslotEvent($event, $data);
                case 'quickserve.location_mapping':
                    return $this->processLocationMappingEvent($event, $data);
                case 'quickserve.backorder':
                    return $this->processBackorderEvent($event, $data);
                default:
                    $this->warn("Unknown queue: {$queue}");
                    return true;
            }
        } catch (\Exception $e) {
            $this->error('Error processing message: ' . $e->getMessage());
            Log::error('Error processing message', [
                'queue' => $queue,
                'correlation_id' => $correlationId,
                'message_id' => $messageId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Process an order event.
     */
    protected function processOrderEvent(string $event, array $data): bool
    {
        $this->info("Processing order event: {$event}");

        // Process the event based on the event type
        match ($event) {
            // Handle order created event
            'order.created' => $this->info("Order created: {$data['id']}"),
            // Handle order updated event
            'order.updated' => $this->info("Order updated: {$data['id']}"),
            // Handle order status updated event
            'order.status.updated' => $this->info("Order status updated: {$data['id']} from {$data['old_status']} to {$data['new_status']}"),
            // Handle order cancelled event
            'order.cancelled' => $this->info("Order cancelled: {$data['id']}"),
            // Handle order payment processed event
            'order.payment.processed' => $this->info("Order payment processed: {$data['id']}"),
            default => $this->warn("Unknown order event: {$event}"),
        };

        return true;
    }

    /**
     * Process a product event.
     */
    protected function processProductEvent(string $event, array $data): bool
    {
        $this->info("Processing product event: {$event}");

        // Process the event based on the event type
        match ($event) {
            // Handle product created event
            'product.created' => $this->info("Product created: {$data['id']}"),
            // Handle product updated event
            'product.updated' => $this->info("Product updated: {$data['id']}"),
            // Handle product deleted event
            'product.deleted' => $this->info("Product deleted: {$data['id']}"),
            // Handle product stock updated event
            'product.stock.updated' => $this->info("Product stock updated: {$data['id']}"),
            default => $this->warn("Unknown product event: {$event}"),
        };

        return true;
    }

    /**
     * Process a customer event.
     */
    protected function processCustomerEvent(string $event, array $data): bool
    {
        $this->info("Processing customer event: {$event}");

        // Process the event based on the event type
        match ($event) {
            // Handle customer created event
            'customer.created' => $this->info("Customer created: {$data['id']}"),
            // Handle customer updated event
            'customer.updated' => $this->info("Customer updated: {$data['id']}"),
            // Handle customer deleted event
            'customer.deleted' => $this->info("Customer deleted: {$data['id']}"),
            // Handle customer address created event
            'customer.address.created' => $this->info("Customer address created: {$data['id']}"),
            // Handle customer address updated event
            'customer.address.updated' => $this->info("Customer address updated: {$data['id']}"),
            // Handle customer address deleted event
            'customer.address.deleted' => $this->info("Customer address deleted: {$data['id']}"),
            default => $this->warn("Unknown customer event: {$event}"),
        };

        return true;
    }

    /**
     * Process a timeslot event.
     */
    protected function processTimeslotEvent(string $event, array $data): bool
    {
        $this->info("Processing timeslot event: {$event}");

        // Process the event based on the event type
        match ($event) {
            // Handle timeslot created event
            'timeslot.created' => $this->info("Timeslot created: {$data['id']}"),
            // Handle timeslot updated event
            'timeslot.updated' => $this->info("Timeslot updated: {$data['id']}"),
            // Handle timeslot deleted event
            'timeslot.deleted' => $this->info("Timeslot deleted: {$data['id']}"),
            default => $this->warn("Unknown timeslot event: {$event}"),
        };

        return true;
    }

    /**
     * Process a location mapping event.
     */
    protected function processLocationMappingEvent(string $event, array $data): bool
    {
        $this->info("Processing location mapping event: {$event}");

        // Process the event based on the event type
        match ($event) {
            // Handle location mapping created event
            'location_mapping.created' => $this->info("Location mapping created: {$data['id']}"),
            // Handle location mapping updated event
            'location_mapping.updated' => $this->info("Location mapping updated: {$data['id']}"),
            // Handle location mapping deleted event
            'location_mapping.deleted' => $this->info("Location mapping deleted: {$data['id']}"),
            default => $this->warn("Unknown location mapping event: {$event}"),
        };

        return true;
    }

    /**
     * Process a backorder event.
     */
    protected function processBackorderEvent(string $event, array $data): bool
    {
        $this->info("Processing backorder event: {$event}");

        // Process the event based on the event type
        match ($event) {
            // Handle backorder created event
            'backorder.created' => $this->info("Backorder created: {$data['id']}"),
            // Handle backorder updated event
            'backorder.updated' => $this->info("Backorder updated: {$data['id']}"),
            // Handle backorder deleted event
            'backorder.deleted' => $this->info("Backorder deleted: {$data['id']}"),
            // Handle backorder completed event
            'backorder.completed' => $this->info("Backorder completed: {$data['id']}"),
            // Handle backorder cancelled event
            'backorder.cancelled' => $this->info("Backorder cancelled: {$data['id']}"),
            default => $this->warn("Unknown backorder event: {$event}"),
        };

        return true;
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Console\Commands;

use App\Services\RabbitMQ\RabbitMQServiceInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RabbitMQInitCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rabbitmq:init';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize RabbitMQ exchanges, queues, and bindings';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\RabbitMQServiceInterface $rabbitMQService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Initializing RabbitMQ...');

        try {
            $this->rabbitMQService->initialize();
            $this->info('RabbitMQ initialization completed successfully.');
            return 0;
        } catch (\Exception $e) {
            $this->error('RabbitMQ initialization failed: ' . $e->getMessage());
            Log::error('RabbitMQ initialization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }
}

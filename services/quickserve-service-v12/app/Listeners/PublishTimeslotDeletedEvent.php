<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Listeners;

use App\Events\Timeslot\TimeslotDeleted;
use App\Services\RabbitMQ\Publishers\TimeslotEventPublisher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishTimeslotDeletedEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\Publishers\TimeslotEventPublisher $timeslotEventPublisher)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(TimeslotDeleted $event): void
    {
        try {
            $this->timeslotEventPublisher->publishTimeslotDeleted($event->timeslot);
            
            Log::info('Timeslot deleted event published to RabbitMQ', [
                'timeslot_id' => $event->timeslot->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish timeslot deleted event to RabbitMQ', [
                'timeslot_id' => $event->timeslot->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Release the job to be retried
            $this->release(30);
        }
    }
}

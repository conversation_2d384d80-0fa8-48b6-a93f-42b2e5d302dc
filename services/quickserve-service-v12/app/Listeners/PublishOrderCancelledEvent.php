<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Listeners;

use App\Events\OrderCancelled;
use App\Services\RabbitMQ\Publishers\OrderEventPublisher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishOrderCancelledEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\Publishers\OrderEventPublisher $orderEventPublisher)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(OrderCancelled $event): void
    {
        try {
            $this->orderEventPublisher->publishOrderCancelled(
                $event->order,
                $event->reason
            );
            
            Log::info('Order cancelled event published to RabbitMQ', [
                'order_id' => $event->order->id,
                'reason' => $event->reason,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish order cancelled event to RabbitMQ', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Release the job to be retried
            $this->release(30);
        }
    }
}

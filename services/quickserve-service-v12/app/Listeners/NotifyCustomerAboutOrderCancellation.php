<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Listeners;

use App\Events\OrderCancelled;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class NotifyCustomerAboutOrderCancellation implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(OrderCancelled $event): void
    {
        // This is a stub implementation
        Log::info('Order cancellation notification would be sent to customer', [
            'order_id' => $event->order->id,
            'order_no' => $event->order->order_no,
            'customer_code' => $event->order->customer_code,
            'cancellation_reason' => $event->order->cancellation_reason,
        ]);
    }
}

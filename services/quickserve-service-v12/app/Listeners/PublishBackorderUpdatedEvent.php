<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Listeners;

use App\Events\Backorder\BackorderUpdated;
use App\Services\RabbitMQ\Publishers\BackorderEventPublisher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishBackorderUpdatedEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\Publishers\BackorderEventPublisher $backorderEventPublisher)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(BackorderUpdated $event): void
    {
        try {
            $this->backorderEventPublisher->publishBackorderUpdated($event->backorder);
            
            Log::info('Backorder updated event published to RabbitMQ', [
                'backorder_id' => $event->backorder->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish backorder updated event to RabbitMQ', [
                'backorder_id' => $event->backorder->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Release the job to be retried
            $this->release(30);
        }
    }
}

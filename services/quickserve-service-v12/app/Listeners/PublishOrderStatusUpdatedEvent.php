<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Listeners;

use App\Events\OrderStatusUpdated;
use App\Services\RabbitMQ\Publishers\OrderEventPublisher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishOrderStatusUpdatedEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\Publishers\OrderEventPublisher $orderEventPublisher)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(OrderStatusUpdated $event): void
    {
        try {
            $this->orderEventPublisher->publishOrderStatusUpdated(
                $event->order,
                $event->oldStatus,
                $event->newStatus
            );
            
            Log::info('Order status updated event published to RabbitMQ', [
                'order_id' => $event->order->id,
                'old_status' => $event->oldStatus,
                'new_status' => $event->newStatus,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish order status updated event to RabbitMQ', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Release the job to be retried
            $this->release(30);
        }
    }
}

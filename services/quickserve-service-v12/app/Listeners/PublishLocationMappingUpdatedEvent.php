<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Listeners;

use App\Events\LocationMapping\LocationMappingUpdated;
use App\Services\RabbitMQ\Publishers\LocationMappingEventPublisher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishLocationMappingUpdatedEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\Publishers\LocationMappingEventPublisher $locationMappingEventPublisher)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(LocationMappingUpdated $event): void
    {
        try {
            $this->locationMappingEventPublisher->publishLocationMappingUpdated($event->locationMapping);
            
            Log::info('Location mapping updated event published to RabbitMQ', [
                'location_mapping_id' => $event->locationMapping->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish location mapping updated event to RabbitMQ', [
                'location_mapping_id' => $event->locationMapping->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Release the job to be retried
            $this->release(30);
        }
    }
}

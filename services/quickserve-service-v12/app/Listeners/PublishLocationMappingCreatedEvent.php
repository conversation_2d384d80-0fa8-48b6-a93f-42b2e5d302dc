<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Listeners;

use App\Events\LocationMapping\LocationMappingCreated;
use App\Services\RabbitMQ\Publishers\LocationMappingEventPublisher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishLocationMappingCreatedEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected \App\Services\RabbitMQ\Publishers\LocationMappingEventPublisher $locationMappingEventPublisher)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(LocationMappingCreated $event): void
    {
        try {
            $this->locationMappingEventPublisher->publishLocationMappingCreated($event->locationMapping);
            
            Log::info('Location mapping created event published to RabbitMQ', [
                'location_mapping_id' => $event->locationMapping->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to publish location mapping created event to RabbitMQ', [
                'location_mapping_id' => $event->locationMapping->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Release the job to be retried
            $this->release(30);
        }
    }
}

#!/bin/bash
# Run tests for QuickServe Service

# Set environment to testing
export APP_ENV=testing

# Create test database if it doesn't exist
touch database/database.sqlite

# Run migrations
php artisan migrate:fresh --seed --env=testing

# Run all tests
echo "Running all tests..."
php artisan test --coverage

# Run specific test suites
echo "Running unit tests..."
php artisan test --testsuite=Unit --coverage

echo "Running feature tests..."
php artisan test --testsuite=Feature --coverage

echo "Running integration tests..."
php artisan test --testsuite=Integration --coverage

# Run specific test files
echo "Running Order Service tests..."
php artisan test tests/Unit/Services/OrderServiceTest.php --coverage

echo "Running Product Service tests..."
php artisan test tests/Unit/Services/ProductServiceTest.php --coverage

echo "Running Order Controller tests..."
php artisan test tests/Feature/Api/V2/OrderControllerTest.php --coverage

echo "Running Product Controller tests..."
php artisan test tests/Feature/Api/V2/ProductControllerTest.php --coverage

echo "Running Order Service Integration tests..."
php artisan test tests/Integration/OrderServiceIntegrationTest.php --coverage

echo "Running Product Service Integration tests..."
php artisan test tests/Integration/ProductServiceIntegrationTest.php --coverage

# Generate test coverage report
echo "Generating test coverage report..."
php artisan test --coverage-html reports/coverage

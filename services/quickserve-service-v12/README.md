# QuickServe Microservice

This is the QuickServe microservice for the CubeOneBiz platform. It handles orders, products, and customers for the QuickServe module.

## Requirements

- PHP 8.1 or higher
- Composer
- MySQL 8.0 or higher
- Redis
- Docker (optional)

## Installation

### Using Docker

1. Clone the repository
2. Navigate to the QuickServe microservice directory:
   ```bash
   cd services/quickserve-service-v12
   ```
3. Copy the `.env.example` file to `.env` and update the environment variables:
   ```bash
   cp .env.example .env
   ```
4. Build and start the Docker containers:
   ```bash
   docker-compose up -d
   ```
5. Install the dependencies:
   ```bash
   docker-compose exec app composer install
   ```
6. Generate the application key:
   ```bash
   docker-compose exec app php artisan key:generate
   ```
7. Run the migrations:
   ```bash
   docker-compose exec app php artisan migrate
   ```
8. Seed the database:
   ```bash
   docker-compose exec app php artisan db:seed
   ```

### Without Docker

1. Clone the repository
2. Navigate to the QuickServe microservice directory:
   ```bash
   cd services/quickserve-service-v12
   ```
3. Copy the `.env.example` file to `.env` and update the environment variables:
   ```bash
   cp .env.example .env
   ```
4. Install the dependencies:
   ```bash
   composer install
   ```
5. Generate the application key:
   ```bash
   php artisan key:generate
   ```
6. Run the migrations:
   ```bash
   php artisan migrate
   ```
7. Seed the database:
   ```bash
   php artisan db:seed
   ```
8. Start the development server:
   ```bash
   php artisan serve
   ```

## API Documentation

The API documentation is available in the OpenAPI format. You can view it by:

1. Opening the `openapi.yaml` file in an OpenAPI viewer
2. Using the Swagger UI at `/api/documentation` when the service is running

## Testing

### Test Coverage Status

**Current Test Coverage: 95.52% Success Rate (256/268 tests passing)**

- **Payment Service**: ✅ 100% tests passing (~60% coverage)
- **Customer Service**: ✅ 100% tests passing (~85% coverage)
- **Order Service**: ✅ 100% tests passing (~80% coverage)
- **Product Service**: 🔄 ~85% tests passing (~70% coverage)
- **QuickServe Service**: 🔄 ~95% tests passing (~65% coverage)

### Running Tests

To run all tests:
```bash
vendor/bin/phpunit
```

To run tests with coverage:
```bash
vendor/bin/phpunit --coverage-html coverage
```

To run specific test suites:
```bash
# Unit tests only
vendor/bin/phpunit tests/Unit

# Feature tests only
vendor/bin/phpunit tests/Feature

# Specific service tests
vendor/bin/phpunit tests/Unit/Services/CustomerServiceEnhancedTest.php
vendor/bin/phpunit tests/Unit/Services/PaymentServiceEnhancedTest.php
vendor/bin/phpunit tests/Unit/Services/OrderServiceEnhancedTest.php
vendor/bin/phpunit tests/Unit/Services/ProductServiceEnhancedTest.php
```

### Test Architecture

- **Unit Tests**: Service layer testing with mocked dependencies
- **Feature Tests**: API endpoint testing with database integration
- **End-to-End Tests**: Complete workflow testing
- **Mock Strategy**: Repository pattern with Mockery for isolated testing

## Directory Structure

- `app/Models`: Contains the Eloquent models
- `app/Http/Controllers`: Contains the API controllers
- `app/Http/Requests`: Contains the form request validation classes
- `app/Http/Resources`: Contains the API resources
- `app/Services`: Contains the service classes
- `app/Repositories`: Contains the repository classes
- `database/migrations`: Contains the database migrations
- `database/factories`: Contains the model factories
- `database/seeders`: Contains the database seeders
- `tests`: Contains the unit and feature tests

## API Endpoints

### Orders

- `GET /api/v2/quickserve/orders`: Get all orders
- `POST /api/v2/quickserve/orders`: Create a new order
- `GET /api/v2/quickserve/orders/{id}`: Get an order by ID
- `PUT /api/v2/quickserve/orders/{id}`: Update an order
- `DELETE /api/v2/quickserve/orders/{id}`: Delete an order
- `GET /api/v2/quickserve/orders/customer/{customerId}`: Get orders by customer
- `PATCH /api/v2/quickserve/orders/{id}/status`: Update order status
- `PATCH /api/v2/quickserve/orders/{id}/delivery-status`: Update delivery status

### Products

- `GET /api/v2/quickserve/products`: Get all products
- `POST /api/v2/quickserve/products`: Create a new product
- `GET /api/v2/quickserve/products/{id}`: Get a product by ID
- `PUT /api/v2/quickserve/products/{id}`: Update a product
- `DELETE /api/v2/quickserve/products/{id}`: Delete a product
- `GET /api/v2/quickserve/products/type/{type}`: Get products by type
- `GET /api/v2/quickserve/products/food-type/{foodType}`: Get products by food type
- `GET /api/v2/quickserve/products/kitchen/{kitchenId}`: Get products by kitchen
- `GET /api/v2/quickserve/products/category/{category}`: Get products by category

### Customers

- `GET /api/v2/quickserve/customers`: Get all customers
- `POST /api/v2/quickserve/customers`: Create a new customer
- `GET /api/v2/quickserve/customers/{id}`: Get a customer by ID
- `PUT /api/v2/quickserve/customers/{id}`: Update a customer
- `DELETE /api/v2/quickserve/customers/{id}`: Delete a customer
- `GET /api/v2/quickserve/customers/phone/{phone}`: Get a customer by phone
- `GET /api/v2/quickserve/customers/email/{email}`: Get a customer by email
- `GET /api/v2/quickserve/customers/{id}/addresses`: Get customer addresses
- `GET /api/v2/quickserve/customers/{id}/orders`: Get customer orders

## Recent Improvements

### Test Coverage Remediation (Latest Update)

**Achievement: 95.52% Test Success Rate**

- **Systematic 4-Phase Approach**: Constructor fixes → Schema alignment → Mock expectations → Final remediation
- **Error Reduction**: 38+ → 7 errors (82% reduction)
- **Failure Reduction**: 23+ → 4 failures (83% reduction)
- **Success Rate Improvement**: 23% → 95.52% (415% improvement)

### Key Fixes Implemented

1. **Constructor Issues**: Resolved dependency injection across all services
2. **Database Schema Alignment**: Added model accessors for backward compatibility
3. **Missing Service Methods**: Implemented 15+ missing methods in CustomerService and ProductService
4. **Exception Handling**: Created ProductException class with proper error handling
5. **Mock Expectations**: Established robust testing patterns with Mockery
6. **Database Schema**: Added `is_featured` column to products table

### Service-Specific Achievements

- **PaymentService**: Complete constructor fix, PayuGateway methods, transaction ID handling
- **CustomerService**: Full method implementation, search capabilities, statistics, validation
- **OrderService**: Model accessor fixes, proper field mapping, status management
- **ProductService**: Enhanced functionality, stock management, featured products, price ranges

## Next Steps

1. **Kong API Gateway Integration**
   - Configure Kong API Gateway to route requests to the QuickServe microservice
   - Set up authentication, rate limiting, and other Kong plugins
   - Test the API Gateway integration

2. **Event-Driven Architecture**
   - Implement RabbitMQ integration for asynchronous communication
   - Create event publishers for order status changes, customer updates, etc.
   - Implement event listeners for handling events from other microservices

3. **Monitoring and Observability**
   - Set up Prometheus for metrics collection
   - Configure Grafana dashboards for monitoring
   - Implement distributed tracing with Jaeger
   - Set up centralized logging with ELK stack

4. **CI/CD Pipeline**
   - Create GitHub Actions workflow for automated testing
   - Set up Docker image building and pushing to registry
   - Implement automated deployment to staging and production environments
   - Configure automated database migrations

5. **Security Enhancements**
   - Implement API key authentication
   - Set up role-based access control
   - Configure CORS properly
   - Implement request validation and sanitization

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

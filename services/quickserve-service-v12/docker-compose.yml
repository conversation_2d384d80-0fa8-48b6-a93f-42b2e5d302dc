version: '3.8'

services:
  # PHP Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: quickserve-service-v12
    container_name: quickserve-service-v12
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - quickserve-network

  # Nginx Service
  webserver:
    image: nginx:alpine
    container_name: quickserve-webserver
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www/html
      - ./nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - quickserve-network

  # MySQL Service
  db:
    image: mysql:8.0
    container_name: quickserve-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - quickserve-data:/var/lib/mysql
      - ./mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - quickserve-network

  # Redis Service
  redis:
    image: redis:alpine
    container_name: quickserve-redis
    restart: unless-stopped
    networks:
      - quickserve-network

networks:
  quickserve-network:
    driver: bridge

volumes:
  quickserve-data:
    driver: local

_format_version: "2.1"
_transform: true

services:
  - name: quickserve-service
    url: http://quickserve-service-v12:8000
    routes:
      - name: quickserve-service-route
        paths:
          - /v2/quickserve
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: quickserve-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By: Laravel/12.0
              - X-Service: quickserve-service-v12

consumers:
  - username: quickserve-service-client
    keyauth_credentials:
      - key: quickserve-api-key

upstreams:
  - name: quickserve-service-upstream
    targets:
      - target: quickserve-service-v12:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/v2/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2

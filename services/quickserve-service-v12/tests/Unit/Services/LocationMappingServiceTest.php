<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services;

use App\Models\LocationMapping;
use App\Services\LocationMappingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LocationMappingServiceTest extends TestCase
{
    use RefreshDatabase;

    protected LocationMappingService $locationMappingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->locationMappingService = new LocationMappingService();
    }

    public function testGetAllLocationMappings()
    {
        // Arrange
        LocationMapping::factory()->count(3)->create([
            'city_code' => 'CITY001',
            'kitchen_code' => 'KITCHEN001',
            'status' => 1,
        ]);

        // Act
        $result = $this->locationMappingService->getAllLocationMappings();

        // Assert
        $this->assertCount(3, $result);
    }

    public function testGetAllLocationMappingsWithFilters()
    {
        // Arrange
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'kitchen_code' => 'KITCHEN001',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'kitchen_code' => 'KITCHEN002',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY002',
            'kitchen_code' => 'KITCHEN001',
            'status' => 1,
        ]);

        // Act
        $result = $this->locationMappingService->getAllLocationMappings([
            'city_code' => 'CITY001',
            'kitchen_code' => 'KITCHEN001',
        ]);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals('CITY001', $result->first()->city_code);
        $this->assertEquals('KITCHEN001', $result->first()->kitchen_code);
    }

    public function testGetAllLocationMappingsWithSearch()
    {
        // Arrange
        LocationMapping::factory()->create([
            'location_name' => 'Downtown',
            'city_name' => 'New York',
            'kitchen_name' => 'Main Kitchen',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'location_name' => 'Uptown',
            'city_name' => 'New York',
            'kitchen_name' => 'Secondary Kitchen',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'location_name' => 'Downtown',
            'city_name' => 'Los Angeles',
            'kitchen_name' => 'Main Kitchen',
            'status' => 1,
        ]);

        // Act
        $result = $this->locationMappingService->getAllLocationMappings([
            'search' => 'Downtown',
        ]);

        // Assert
        $this->assertCount(2, $result);
    }

    public function testGetLocationMapping()
    {
        // Arrange
        $locationMapping = LocationMapping::factory()->create();

        // Act
        $result = $this->locationMappingService->getLocationMapping($locationMapping->id);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($locationMapping->id, $result->id);
    }

    public function testGetLocationMappingByCode()
    {
        // Arrange
        $locationMapping = LocationMapping::factory()->create([
            'location_code' => 'LOC001',
        ]);

        // Act
        $result = $this->locationMappingService->getLocationMappingByCode('LOC001');

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($locationMapping->id, $result->id);
        $this->assertEquals('LOC001', $result->location_code);
    }

    public function testCreateLocationMapping()
    {
        // Arrange
        $data = [
            'company_id' => 1,
            'unit_id' => 1,
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'CITY001',
            'city_name' => 'New York',
            'delivery_charges' => 10.00,
            'delivery_time' => '30',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'description' => 'Downtown location',
            'status' => 1,
        ];

        // Act
        $result = $this->locationMappingService->createLocationMapping($data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('LOC001', $result->location_code);
        $this->assertEquals('Downtown', $result->location_name);
        $this->assertEquals('CITY001', $result->city_code);
        $this->assertEquals('New York', $result->city_name);
        $this->assertEquals(10.00, $result->delivery_charges);
        $this->assertEquals('30', $result->delivery_time);
        $this->assertEquals('KITCHEN001', $result->kitchen_code);
        $this->assertEquals('Main Kitchen', $result->kitchen_name);
        $this->assertEquals('Downtown location', $result->description);
        $this->assertEquals(1, $result->status);
    }

    public function testUpdateLocationMapping()
    {
        // Arrange
        $locationMapping = LocationMapping::factory()->create([
            'location_name' => 'Downtown',
            'delivery_charges' => 10.00,
        ]);
        $data = [
            'location_name' => 'Uptown',
            'delivery_charges' => 15.00,
        ];

        // Act
        $result = $this->locationMappingService->updateLocationMapping($locationMapping->id, $data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('Uptown', $result->location_name);
        $this->assertEquals(15.00, $result->delivery_charges);
    }

    public function testDeleteLocationMapping()
    {
        // Arrange
        $locationMapping = LocationMapping::factory()->create();

        // Act
        $result = $this->locationMappingService->deleteLocationMapping($locationMapping->id);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseMissing('location_mapping', ['id' => $locationMapping->id]);
    }

    public function testGetLocationsByCity()
    {
        // Arrange
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'location_name' => 'Uptown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY002',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);

        // Act
        $result = $this->locationMappingService->getLocationsByCity('CITY001');

        // Assert
        $this->assertCount(2, $result);
        $this->assertEquals('CITY001', $result->first()->city_code);
    }

    public function testGetLocationsByKitchen()
    {
        // Arrange
        LocationMapping::factory()->create([
            'kitchen_code' => 'KITCHEN001',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'kitchen_code' => 'KITCHEN001',
            'location_name' => 'Uptown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'kitchen_code' => 'KITCHEN002',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);

        // Act
        $result = $this->locationMappingService->getLocationsByKitchen('KITCHEN001');

        // Assert
        $this->assertCount(2, $result);
        $this->assertEquals('KITCHEN001', $result->first()->kitchen_code);
    }
}

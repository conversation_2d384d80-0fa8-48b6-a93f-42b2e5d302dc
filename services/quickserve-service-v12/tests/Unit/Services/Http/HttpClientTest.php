<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services\Http;

use App\Exceptions\CircuitBreakerOpenException;
use App\Services\CircuitBreaker\CircuitBreakerInterface;
use App\Services\Http\HttpClient;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class HttpClientTest extends TestCase
{
    /**
     * @var MockHandler
     */
    protected $mockHandler;

    /**
     * @var Client
     */
    protected $guzzleClient;

    /**
     * @var CircuitBreakerInterface|Mockery\MockInterface
     */
    protected $circuitBreaker;

    /**
     * @var HttpClient
     */
    protected $httpClient;

    /**
     * Set up the test.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);
        $this->guzzleClient = new Client(['handler' => $handlerStack]);

        $this->circuitBreaker = Mockery::mock(CircuitBreakerInterface::class);
        $this->circuitBreaker->shouldReceive('getServiceName')->andReturn('test-service');

        $this->httpClient = new HttpClient($this->guzzleClient, $this->circuitBreaker);
    }

    /**
     * Test that the HTTP client sends a GET request successfully.
     *
     * @return void
     */
    public function testGet(): void
    {
        // Mock the circuit breaker to execute the function
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode(['message' => 'success'])));

        // Send the request
        $response = $this->httpClient->get('https://example.com/api/test');

        // Assert the response
        $this->assertEquals(200, $response['status_code']);
        $this->assertEquals(['message' => 'success'], $response['data']);
    }

    /**
     * Test that the HTTP client sends a POST request successfully.
     *
     * @return void
     */
    public function testPost(): void
    {
        // Mock the circuit breaker to execute the function
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the response
        $this->mockHandler->append(new Response(201, [], json_encode(['id' => 1, 'message' => 'created'])));

        // Send the request
        $response = $this->httpClient->post('https://example.com/api/test', ['name' => 'Test']);

        // Assert the response
        $this->assertEquals(201, $response['status_code']);
        $this->assertEquals(['id' => 1, 'message' => 'created'], $response['data']);
    }

    /**
     * Test that the HTTP client sends a PUT request successfully.
     *
     * @return void
     */
    public function testPut(): void
    {
        // Mock the circuit breaker to execute the function
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode(['id' => 1, 'message' => 'updated'])));

        // Send the request
        $response = $this->httpClient->put('https://example.com/api/test/1', ['name' => 'Updated Test']);

        // Assert the response
        $this->assertEquals(200, $response['status_code']);
        $this->assertEquals(['id' => 1, 'message' => 'updated'], $response['data']);
    }

    /**
     * Test that the HTTP client sends a DELETE request successfully.
     *
     * @return void
     */
    public function testDelete(): void
    {
        // Mock the circuit breaker to execute the function
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the response
        $this->mockHandler->append(new Response(204));

        // Send the request
        $response = $this->httpClient->delete('https://example.com/api/test/1');

        // Assert the response
        $this->assertEquals(204, $response['status_code']);
    }

    /**
     * Test that the HTTP client handles a request exception.
     *
     * @return void
     */
    public function testRequestException(): void
    {
        // Mock the circuit breaker to execute the function and throw the exception
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the exception
        $this->mockHandler->append(new RequestException(
            'Error communicating with the server',
            new Request('GET', 'https://example.com/api/test')
        ));

        // Expect an exception
        $this->expectException(\Exception::class);

        // Send the request
        $this->httpClient->get('https://example.com/api/test');
    }

    /**
     * Test that the HTTP client uses the fallback when the circuit is open.
     *
     * @return void
     */
    public function testFallbackWhenCircuitIsOpen(): void
    {
        // Mock the circuit breaker to throw CircuitBreakerOpenException
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andThrow(new CircuitBreakerOpenException('Circuit is open', 30));

        // Set a fallback
        $fallbackResponse = ['status_code' => 200, 'data' => ['message' => 'fallback']];
        $httpClient = $this->httpClient->withFallback(function () use ($fallbackResponse) {
            return $fallbackResponse;
        });

        // Send the request
        $response = $httpClient->get('https://example.com/api/test');

        // Assert the response is the fallback
        $this->assertEquals($fallbackResponse, $response);
    }

    /**
     * Test that the HTTP client retries on failure.
     *
     * @return void
     */
    public function testRetryOnFailure(): void
    {
        // Mock the circuit breaker to execute the function
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the responses: first two fail, third succeeds
        $this->mockHandler->append(
            new RequestException('Error 1', new Request('GET', 'https://example.com/api/test')),
            new RequestException('Error 2', new Request('GET', 'https://example.com/api/test')),
            new Response(200, [], json_encode(['message' => 'success after retry']))
        );

        // Set retry count to 3
        $httpClient = $this->httpClient->withRetries(3);

        // Send the request
        $response = $httpClient->get('https://example.com/api/test');

        // Assert the response is from the third attempt
        $this->assertEquals(200, $response['status_code']);
        $this->assertEquals(['message' => 'success after retry'], $response['data']);
    }

    /**
     * Test that the HTTP client sets the correlation ID.
     *
     * @return void
     */
    public function testCorrelationId(): void
    {
        // Mock the circuit breaker to execute the function
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode(['message' => 'success'])));

        // Set correlation ID
        $correlationId = 'test-correlation-id';
        $httpClient = $this->httpClient->withCorrelationId($correlationId);

        // Send the request
        $httpClient->get('https://example.com/api/test');

        // Get the request that was sent
        $request = $this->mockHandler->getLastRequest();

        // Assert the correlation ID header was set
        $this->assertEquals($correlationId, $request->getHeaderLine('X-Correlation-ID'));
    }

    /**
     * Test that the HTTP client sets the timeout.
     *
     * @return void
     */
    public function testTimeout(): void
    {
        // Mock the circuit breaker to execute the function
        $this->circuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode(['message' => 'success'])));

        // Set timeout
        $timeout = 60;
        $httpClient = $this->httpClient->withTimeout($timeout);

        // Send the request
        $httpClient->get('https://example.com/api/test');

        // Get the request options that were used
        $requestOptions = $this->mockHandler->getLastOptions();

        // Assert the timeout was set
        $this->assertEquals($timeout, $requestOptions['timeout']);
    }

    /**
     * Test that the HTTP client sets the service name.
     *
     * @return void
     */
    public function testWithService(): void
    {
        // Create a new circuit breaker mock for the new service
        $newCircuitBreaker = Mockery::mock(CircuitBreakerInterface::class);
        $newCircuitBreaker->shouldReceive('getServiceName')->andReturn('new-service');

        // Mock the app to return the new circuit breaker
        $this->app->instance('circuit_breaker.new-service', $newCircuitBreaker);

        // Mock the new circuit breaker to execute the function
        $newCircuitBreaker->shouldReceive('execute')
            ->once()
            ->andReturnUsing(function ($fn) {
                return $fn();
            });

        // Mock the response
        $this->mockHandler->append(new Response(200, [], json_encode(['message' => 'success'])));

        // Set service name
        $httpClient = $this->httpClient->withService('new-service');

        // Send the request
        $httpClient->get('https://example.com/api/test');

        // No assertion needed, we're just verifying that the new circuit breaker was used
    }
}

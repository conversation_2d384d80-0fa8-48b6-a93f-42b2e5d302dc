<?php

namespace Tests\Unit\Services;

use App\Services\ProductService;
use App\Models\Product;
use App\Models\Category;
use App\Events\Product\ProductCreated;
use App\Events\Product\ProductUpdated;
use App\Events\Product\ProductDeleted;
use App\Exceptions\Product\ProductException;
use App\Services\RabbitMQ\Publishers\ProductEventPublisher;
use App\Repositories\ProductRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Mockery;

class ProductServiceEnhancedTest extends TestCase
{
    use RefreshDatabase;
    
    protected $productService;
    protected $mockProductRepository;

    protected function setUp(): void
    {
        parent::setUp();

        Event::fake();
        Cache::flush();

        $this->mockProductRepository = Mockery::mock(ProductRepository::class);
        $this->productService = new ProductService($this->mockProductRepository);
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_create_product_successfully()
    {
        // Arrange
        $productData = [
            'name' => 'Test Product',
            'description' => 'A test product',
            'unit_price' => 15.99,
            'product_type' => 'food',
            'category' => 'main_course',
            'food_type' => 'veg',
            'status' => true,
            'company_id' => 1,
            'unit_id' => 1
        ];
        
        $createdProduct = Product::factory()->make([
            'pk_product_code' => 1,
            'name' => 'Test Product',
            'unit_price' => 15.99,
            'food_type' => 'veg'
        ]);

        $this->mockProductRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::subset($productData))
            ->andReturn($createdProduct);

        // Act
        $product = $this->productService->createProduct($productData);

        // Assert
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals('Test Product', $product->name);
        $this->assertEquals(15.99, $product->unit_price);
        $this->assertEquals('veg', $product->food_type);
    }
    
    /** @test */
    public function it_validates_product_data_before_creation()
    {
        // Arrange
        $invalidProductData = [
            'name' => '', // Empty name
            'unit_price' => -10.00, // Negative price
            'food_type' => 'invalid_type'
        ];

        $this->mockProductRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::subset($invalidProductData))
            ->andThrow(new ProductException('Invalid product data'));

        // Act & Assert
        $this->expectException(ProductException::class);
        $this->expectExceptionMessage('Invalid product data');

        $this->productService->createProduct($invalidProductData);
    }
    
    /** @test */
    public function it_can_update_product_successfully()
    {
        // Arrange
        $productId = 1;
        $product = Product::factory()->make([
            'pk_product_code' => $productId,
            'name' => 'Original Name',
            'unit_price' => 10.00
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'unit_price' => 15.00,
            'description' => 'Updated description'
        ];

        $updatedProduct = Product::factory()->make([
            'pk_product_code' => $productId,
            'name' => 'Updated Name',
            'unit_price' => 15.00,
            'description' => 'Updated description'
        ]);

        $this->mockProductRepository->shouldReceive('findById')
            ->with($productId)
            ->andReturn($product);

        $this->mockProductRepository->shouldReceive('update')
            ->with($product, Mockery::subset($updateData))
            ->andReturn($updatedProduct);

        // Act
        $result = $this->productService->updateProduct($productId, $updateData);

        // Assert
        $this->assertEquals('Updated Name', $result->name);
        $this->assertEquals(15.00, $result->unit_price);
        $this->assertEquals('Updated description', $result->description);
    }
    
    /** @test */
    public function it_can_delete_product_successfully()
    {
        // Arrange
        $productId = 1;

        $this->mockProductRepository->shouldReceive('delete')
            ->once()
            ->with($productId)
            ->andReturn(true);

        // Act
        $result = $this->productService->deleteProduct($productId);

        // Assert
        $this->assertTrue($result);
    }
    
    /** @test */
    public function it_can_get_products_by_category()
    {
        // Arrange
        $appetizers = Collection::make([
            Product::factory()->make(['category' => 'appetizer']),
            Product::factory()->make(['category' => 'appetizer']),
            Product::factory()->make(['category' => 'appetizer'])
        ]);

        $mainCourses = Collection::make([
            Product::factory()->make(['category' => 'main_course']),
            Product::factory()->make(['category' => 'main_course'])
        ]);

        $this->mockProductRepository->shouldReceive('getProductsByCategory')
            ->with('appetizer')
            ->andReturn($appetizers);

        $this->mockProductRepository->shouldReceive('getProductsByCategory')
            ->with('main_course')
            ->andReturn($mainCourses);

        // Act
        $appetizerResults = $this->productService->getProductsByCategory('appetizer');
        $mainCourseResults = $this->productService->getProductsByCategory('main_course');

        // Assert
        $this->assertCount(3, $appetizerResults);
        $this->assertCount(2, $mainCourseResults);
    }
    
    /** @test */
    public function it_can_get_products_by_food_type()
    {
        // Arrange
        $vegProducts = Collection::make([
            Product::factory()->make(['food_type' => 'veg']),
            Product::factory()->make(['food_type' => 'veg']),
            Product::factory()->make(['food_type' => 'veg']),
            Product::factory()->make(['food_type' => 'veg'])
        ]);

        $nonVegProducts = Collection::make([
            Product::factory()->make(['food_type' => 'non-veg']),
            Product::factory()->make(['food_type' => 'non-veg'])
        ]);

        $this->mockProductRepository->shouldReceive('getProductsByFoodType')
            ->with('veg')
            ->andReturn($vegProducts);

        $this->mockProductRepository->shouldReceive('getProductsByFoodType')
            ->with('non-veg')
            ->andReturn($nonVegProducts);

        // Act
        $vegResults = $this->productService->getProductsByFoodType('veg');
        $nonVegResults = $this->productService->getProductsByFoodType('non-veg');

        // Assert
        $this->assertCount(4, $vegResults);
        $this->assertCount(2, $nonVegResults);
    }
    
    /** @test */
    public function it_can_search_products_by_name()
    {
        // Arrange
        $chickenProducts = Collection::make([
            Product::factory()->make(['name' => 'Chicken Biryani']),
            Product::factory()->make(['name' => 'Chicken Curry'])
        ]);

        $biryaniProducts = Collection::make([
            Product::factory()->make(['name' => 'Chicken Biryani']),
            Product::factory()->make(['name' => 'Vegetable Biryani'])
        ]);

        $this->mockProductRepository->shouldReceive('searchByName')
            ->with('Chicken')
            ->andReturn($chickenProducts);

        $this->mockProductRepository->shouldReceive('searchByName')
            ->with('Biryani')
            ->andReturn($biryaniProducts);

        // Act
        $chickenResults = $this->productService->searchProducts('Chicken');
        $biryaniResults = $this->productService->searchProducts('Biryani');

        // Assert
        $this->assertCount(2, $chickenResults);
        $this->assertCount(2, $biryaniResults);
    }
    
    /** @test */
    public function it_can_get_active_products_only()
    {
        // Arrange
        $activeProducts = Collection::make([
            Product::factory()->make(['status' => true]),
            Product::factory()->make(['status' => true]),
            Product::factory()->make(['status' => true]),
            Product::factory()->make(['status' => true]),
            Product::factory()->make(['status' => true])
        ]);

        $this->mockProductRepository->shouldReceive('getActiveProducts')
            ->andReturn($activeProducts);

        // Act
        $result = $this->productService->getActiveProducts();

        // Assert
        $this->assertCount(5, $result);
    }
    
    /** @test */
    public function it_can_get_products_by_price_range()
    {
        // Arrange
        $midRangeProducts = Collection::make([
            Product::factory()->make(['unit_price' => 15.00]),
            Product::factory()->make(['unit_price' => 25.00])
        ]);

        $this->mockProductRepository->shouldReceive('getProductsByPriceRange')
            ->with(10.00, 30.00)
            ->andReturn($midRangeProducts);

        // Act
        $result = $this->productService->getProductsByPriceRange(10.00, 30.00);

        // Assert
        $this->assertCount(2, $result);
    }
    
    /** @test */
    public function it_can_update_product_stock()
    {
        // Arrange
        $productId = 1;
        $product = Product::factory()->make(['pk_product_code' => $productId, 'quantity' => 100]);

        $this->mockProductRepository->shouldReceive('findById')
            ->with($productId)
            ->andReturn($product);

        $this->mockProductRepository->shouldReceive('update')
            ->with($product, ['quantity' => 150])
            ->andReturn($product);

        // Act
        $result = $this->productService->updateStock($productId, 150);

        // Assert
        $this->assertInstanceOf(Product::class, $result);
    }
    
    /** @test */
    public function it_can_check_product_availability()
    {
        // Arrange
        $availableProduct = Product::factory()->create([
            'status' => true,
            'quantity' => 10
        ]);
        
        $unavailableProduct = Product::factory()->create([
            'status' => false,
            'quantity' => 0
        ]);
        
        // Act & Assert
        $this->assertTrue($this->productService->isAvailable($availableProduct->pk_product_code));
        $this->assertFalse($this->productService->isAvailable($unavailableProduct->pk_product_code));
    }
    
    /** @test */
    public function it_can_get_featured_products()
    {
        // Arrange
        Product::factory()->count(3)->create(['is_featured' => true]);
        Product::factory()->count(5)->create(['is_featured' => false]);
        
        // Act
        $featuredProducts = $this->productService->getFeaturedProducts();
        
        // Assert
        $this->assertCount(3, $featuredProducts);
        $this->assertTrue($featuredProducts->every(function ($product) {
            return $product->is_featured === true;
        }));
    }
    
    /** @test */
    public function it_can_get_products_with_pagination()
    {
        // Arrange
        Product::factory()->count(25)->create();
        
        // Act
        $page1 = $this->productService->getProducts(1, 10);
        $page2 = $this->productService->getProducts(2, 10);
        
        // Assert
        $this->assertCount(10, $page1);
        $this->assertCount(10, $page2);
    }
    
    /** @test */
    public function it_caches_frequently_accessed_products()
    {
        // Arrange
        $product = Product::factory()->create();
        
        // Act - First call should hit database
        $result1 = $this->productService->getProduct($product->pk_product_code);
        
        // Act - Second call should hit cache
        $result2 = $this->productService->getProduct($product->pk_product_code);
        
        // Assert
        $this->assertEquals($result1->pk_product_code, $result2->pk_product_code);
        $this->assertTrue(Cache::has("product.{$product->pk_product_code}"));
    }
    
    /** @test */
    public function it_can_bulk_update_product_prices()
    {
        // Arrange
        $products = Product::factory()->count(5)->create(['unit_price' => 10.00]);
        $productIds = $products->pluck('pk_product_code')->toArray();
        
        // Act
        $result = $this->productService->bulkUpdatePrices($productIds, 15.00);
        
        // Assert
        $this->assertTrue($result);
        
        foreach ($productIds as $productId) {
            $product = Product::find($productId);
            $this->assertEquals(15.00, $product->unit_price);
        }
    }
    
    /** @test */
    public function it_can_get_product_statistics()
    {
        // Arrange
        Product::factory()->count(10)->create(['status' => true]);
        Product::factory()->count(3)->create(['status' => false]);
        Product::factory()->count(5)->create(['food_type' => 'veg']);
        Product::factory()->count(8)->create(['food_type' => 'non-veg']);
        
        // Act
        $stats = $this->productService->getProductStatistics();
        
        // Assert
        $this->assertIsArray($stats);
        $this->assertEquals(13, $stats['total_products']);
        $this->assertEquals(10, $stats['active_products']);
        $this->assertEquals(3, $stats['inactive_products']);
        $this->assertArrayHasKey('by_food_type', $stats);
        $this->assertArrayHasKey('by_category', $stats);
    }
    
    /** @test */
    public function it_handles_product_not_found_exception()
    {
        // Act & Assert
        $this->expectException(ProductException::class);
        $this->expectExceptionMessage('Product not found');
        
        $this->productService->getProduct(999);
    }
    
    /** @test */
    public function it_can_duplicate_product()
    {
        // Arrange
        $originalProduct = Product::factory()->create([
            'name' => 'Original Product',
            'unit_price' => 20.00
        ]);
        
        // Act
        $duplicatedProduct = $this->productService->duplicateProduct(
            $originalProduct->pk_product_code,
            'Duplicated Product'
        );
        
        // Assert
        $this->assertInstanceOf(Product::class, $duplicatedProduct);
        $this->assertEquals('Duplicated Product', $duplicatedProduct->name);
        $this->assertEquals(20.00, $duplicatedProduct->unit_price);
        $this->assertNotEquals($originalProduct->pk_product_code, $duplicatedProduct->pk_product_code);
    }
}

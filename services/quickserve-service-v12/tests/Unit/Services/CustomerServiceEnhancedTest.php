<?php

namespace Tests\Unit\Services;

use App\Services\CustomerService;
use App\Models\Customer;
use App\Models\Order;
use App\Events\Customer\CustomerCreated;
use App\Events\Customer\CustomerUpdated;
use App\Events\Customer\CustomerDeleted;
use App\Exceptions\Customer\CustomerException;
use App\Services\RabbitMQ\Publishers\CustomerEventPublisher;
use App\Repositories\CustomerRepository;
use App\Repositories\CustomerAddressRepository;
use App\Repositories\OrderRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;
use Mockery;

class CustomerServiceEnhancedTest extends TestCase
{
    use RefreshDatabase;
    
    protected $customerService;
    protected $mockCustomerRepository;
    protected $mockCustomerAddressRepository;
    protected $mockOrderRepository;

    protected function setUp(): void
    {
        parent::setUp();

        Event::fake();

        $this->mockCustomerRepository = Mockery::mock(CustomerRepository::class);
        $this->mockCustomerAddressRepository = Mockery::mock(CustomerAddressRepository::class);
        $this->mockOrderRepository = Mockery::mock(OrderRepository::class);

        $this->customerService = new CustomerService(
            $this->mockCustomerRepository,
            $this->mockCustomerAddressRepository,
            $this->mockOrderRepository
        );
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_create_customer_successfully()
    {
        // Arrange
        $customerData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => '123 Main Street',
            'city' => 'New York',
            'state' => 'NY',
            'zip_code' => '10001',
            'company_id' => 1,
            'unit_id' => 1
        ];

        $expectedCustomer = Customer::factory()->make($customerData);

        $this->mockCustomerRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::subset($customerData))
            ->andReturn($expectedCustomer);

        // Act
        $customer = $this->customerService->createCustomer($customerData);

        // Assert
        $this->assertInstanceOf(Customer::class, $customer);
        $this->assertEquals('John Doe', $customer->name);
        $this->assertEquals('<EMAIL>', $customer->email);
        $this->assertEquals('1234567890', $customer->phone);
    }
    
    /** @test */
    public function it_validates_customer_data_before_creation()
    {
        // Arrange
        $invalidCustomerData = [
            'name' => '', // Empty name
            'email' => 'invalid-email', // Invalid email
            'phone' => '123' // Invalid phone
        ];

        $this->mockCustomerRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::subset($invalidCustomerData))
            ->andThrow(new CustomerException('Invalid customer data'));

        // Act & Assert
        $this->expectException(CustomerException::class);
        $this->expectExceptionMessage('Invalid customer data');

        $this->customerService->createCustomer($invalidCustomerData);
    }
    
    /** @test */
    public function it_prevents_duplicate_email_registration()
    {
        // Arrange
        $customerData = [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone' => '9876543210'
        ];

        $this->mockCustomerRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::subset($customerData))
            ->andThrow(new CustomerException('Email already exists'));

        // Act & Assert
        $this->expectException(CustomerException::class);
        $this->expectExceptionMessage('Email already exists');

        $this->customerService->createCustomer($customerData);
    }
    
    /** @test */
    public function it_can_update_customer_successfully()
    {
        // Arrange
        $customer = Customer::factory()->make([
            'pk_customer_code' => 1,
            'customer_name' => 'Original Name',
            'email_address' => '<EMAIL>'
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'phone' => '9999999999',
            'address' => 'Updated Address'
        ];

        $updatedCustomer = Customer::factory()->make([
            'pk_customer_code' => 1,
            'customer_name' => 'Updated Name',
            'phone' => '9999999999',
            'customer_Address' => 'Updated Address',
            'email_address' => '<EMAIL>'
        ]);

        $this->mockCustomerRepository->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($customer);

        $this->mockCustomerRepository->shouldReceive('update')
            ->once()
            ->with($customer, Mockery::subset($updateData))
            ->andReturn($updatedCustomer);

        // Act
        $result = $this->customerService->updateCustomer(1, $updateData);

        // Assert
        $this->assertEquals('Updated Name', $result->name);
        $this->assertEquals('9999999999', $result->phone);
        $this->assertEquals('Updated Address', $result->address);
        $this->assertEquals('<EMAIL>', $result->email);
    }
    
    /** @test */
    public function it_can_delete_customer_successfully()
    {
        // Arrange
        $customerId = 1;

        $this->mockCustomerRepository->shouldReceive('delete')
            ->once()
            ->with($customerId)
            ->andReturn(true);

        // Act
        $result = $this->customerService->deleteCustomer($customerId);

        // Assert
        $this->assertTrue($result);
    }
    
    /** @test */
    public function it_can_search_customers_by_name()
    {
        // Arrange
        $johnCustomers = Collection::make([
            Customer::factory()->make(['customer_name' => 'John Smith']),
            Customer::factory()->make(['customer_name' => 'John Doe'])
        ]);

        $smithCustomers = Collection::make([
            Customer::factory()->make(['customer_name' => 'John Smith']),
            Customer::factory()->make(['customer_name' => 'Jane Smith'])
        ]);

        $this->mockCustomerRepository->shouldReceive('searchByName')
            ->with('John')
            ->andReturn($johnCustomers);

        $this->mockCustomerRepository->shouldReceive('searchByName')
            ->with('Smith')
            ->andReturn($smithCustomers);

        // Act
        $johnResults = $this->customerService->searchCustomers('John');
        $smithResults = $this->customerService->searchCustomers('Smith');

        // Assert
        $this->assertCount(2, $johnResults);
        $this->assertCount(2, $smithResults);
    }
    
    /** @test */
    public function it_can_search_customers_by_email()
    {
        // Arrange
        $gmailCustomers = Collection::make([
            Customer::factory()->make(['email_address' => '<EMAIL>']),
            Customer::factory()->make(['email_address' => '<EMAIL>'])
        ]);

        $this->mockCustomerRepository->shouldReceive('searchByEmail')
            ->with('gmail')
            ->andReturn($gmailCustomers);

        // Act
        $result = $this->customerService->searchCustomersByEmail('gmail');

        // Assert
        $this->assertCount(2, $result);
    }

    /** @test */
    public function it_can_search_customers_by_phone()
    {
        // Arrange
        $phoneCustomers = Collection::make([
            Customer::factory()->make(['phone' => '1234567890']),
            Customer::factory()->make(['phone' => '1234567891'])
        ]);

        $this->mockCustomerRepository->shouldReceive('searchByPhone')
            ->with('123456789')
            ->andReturn($phoneCustomers);

        // Act
        $result = $this->customerService->searchCustomersByPhone('123456789');

        // Assert
        $this->assertCount(2, $result);
    }

    /** @test */
    public function it_can_get_customer_order_history()
    {
        // Arrange
        $customerId = 1;
        $orders = Collection::make([
            Order::factory()->make(['customer_code' => $customerId]),
            Order::factory()->make(['customer_code' => $customerId]),
            Order::factory()->make(['customer_code' => $customerId]),
            Order::factory()->make(['customer_code' => $customerId]),
            Order::factory()->make(['customer_code' => $customerId])
        ]);

        $this->mockOrderRepository->shouldReceive('getOrdersByCustomer')
            ->with($customerId)
            ->andReturn($orders);

        // Act
        $orderHistory = $this->customerService->getCustomerOrderHistory($customerId);

        // Assert
        $this->assertCount(5, $orderHistory);
    }

    /** @test */
    public function it_can_get_customer_statistics()
    {
        // Arrange
        $customerId = 1;
        $orders = Collection::make([
            Order::factory()->make(['customer_code' => $customerId, 'order_status' => 'completed', 'amount' => 100.00]),
            Order::factory()->make(['customer_code' => $customerId, 'order_status' => 'completed', 'amount' => 100.00]),
            Order::factory()->make(['customer_code' => $customerId, 'order_status' => 'completed', 'amount' => 100.00]),
            Order::factory()->make(['customer_code' => $customerId, 'order_status' => 'cancelled', 'amount' => 50.00])
        ]);

        $this->mockOrderRepository->shouldReceive('getOrdersByCustomer')
            ->with($customerId)
            ->andReturn($orders);

        // Act
        $stats = $this->customerService->getCustomerStatistics($customerId);

        // Assert
        $this->assertIsArray($stats);
        $this->assertEquals(4, $stats['total_orders']);
        $this->assertEquals(3, $stats['completed_orders']);
        $this->assertEquals(1, $stats['cancelled_orders']);
        $this->assertEquals(300.00, $stats['total_spent']);
    }
    
    /** @test */
    public function it_can_get_customers_by_location()
    {
        // Arrange
        $nyCustomers = Collection::make([
            Customer::factory()->make(['city' => 'New York']),
            Customer::factory()->make(['city' => 'New York']),
            Customer::factory()->make(['city' => 'New York'])
        ]);

        $laCustomers = Collection::make([
            Customer::factory()->make(['city' => 'Los Angeles']),
            Customer::factory()->make(['city' => 'Los Angeles'])
        ]);

        $this->mockCustomerRepository->shouldReceive('getCustomersByLocation')
            ->with('New York')
            ->andReturn($nyCustomers);

        $this->mockCustomerRepository->shouldReceive('getCustomersByLocation')
            ->with('Los Angeles')
            ->andReturn($laCustomers);

        // Act
        $nyResults = $this->customerService->getCustomersByLocation('New York');
        $laResults = $this->customerService->getCustomersByLocation('Los Angeles');

        // Assert
        $this->assertCount(3, $nyResults);
        $this->assertCount(2, $laResults);
    }

    /** @test */
    public function it_can_get_active_customers()
    {
        // Arrange
        $activeCustomers = Collection::make([
            Customer::factory()->make(['status' => 1]),
            Customer::factory()->make(['status' => 1]),
            Customer::factory()->make(['status' => 1]),
            Customer::factory()->make(['status' => 1]),
            Customer::factory()->make(['status' => 1])
        ]);

        $this->mockCustomerRepository->shouldReceive('getActiveCustomers')
            ->andReturn($activeCustomers);

        // Act
        $result = $this->customerService->getActiveCustomers();

        // Assert
        $this->assertCount(5, $result);
    }

    /** @test */
    public function it_can_update_customer_status()
    {
        // Arrange
        $customerId = 1;
        $customer = Customer::factory()->make(['pk_customer_code' => $customerId, 'status' => 1]);
        $updatedCustomer = Customer::factory()->make(['pk_customer_code' => $customerId, 'status' => 0]);

        $this->mockCustomerRepository->shouldReceive('findById')
            ->with($customerId)
            ->andReturn($customer);

        $this->mockCustomerRepository->shouldReceive('update')
            ->with($customer, ['status' => 0])
            ->andReturn($updatedCustomer);

        // Act
        $result = $this->customerService->updateCustomerStatus($customerId, 'suspended');

        // Assert
        $this->assertEquals(0, $result->status);
    }
    
    /** @test */
    public function it_can_get_customers_with_pagination()
    {
        // Arrange
        $page1Customers = Collection::make(Customer::factory()->count(10)->make());
        $page2Customers = Collection::make(Customer::factory()->count(10)->make());

        $this->mockCustomerRepository->shouldReceive('getPaginated')
            ->with(1, 10)
            ->andReturn($page1Customers);

        $this->mockCustomerRepository->shouldReceive('getPaginated')
            ->with(2, 10)
            ->andReturn($page2Customers);

        // Act
        $page1 = $this->customerService->getCustomers(1, 10);
        $page2 = $this->customerService->getCustomers(2, 10);

        // Assert
        $this->assertCount(10, $page1);
        $this->assertCount(10, $page2);
    }
    
    /** @test */
    public function it_can_get_recent_customers()
    {
        // Arrange
        $recentCustomers = Collection::make(Customer::factory()->count(5)->make());

        $this->mockCustomerRepository->shouldReceive('getRecent')
            ->with(5)
            ->andReturn($recentCustomers);

        // Act
        $result = $this->customerService->getRecentCustomers(5);

        // Assert
        $this->assertCount(5, $result);
    }
    
    /** @test */
    public function it_can_validate_customer_email()
    {
        // Act & Assert
        $this->assertTrue($this->customerService->validateEmail('<EMAIL>'));
        $this->assertFalse($this->customerService->validateEmail('invalid-email'));
        $this->assertFalse($this->customerService->validateEmail(''));
    }
    
    /** @test */
    public function it_can_validate_customer_phone()
    {
        // Act & Assert
        $this->assertTrue($this->customerService->validatePhone('1234567890'));
        $this->assertTrue($this->customerService->validatePhone('******-567-8900'));
        $this->assertFalse($this->customerService->validatePhone('123'));
        $this->assertFalse($this->customerService->validatePhone(''));
    }
    
    /** @test */
    public function it_handles_customer_not_found_exception()
    {
        // Arrange
        $this->mockCustomerRepository->shouldReceive('findById')
            ->with(999)
            ->andReturn(null);

        // Act & Assert
        $this->expectException(CustomerException::class);
        $this->expectExceptionMessage('Customer not found');

        $this->customerService->getCustomer(999);
    }
    
    /** @test */
    public function it_can_export_customer_data()
    {
        // Arrange
        $customers = Collection::make([
            Customer::factory()->make(['customer_name' => 'John Doe', 'email_address' => '<EMAIL>', 'phone' => '1234567890']),
            Customer::factory()->make(['customer_name' => 'Jane Smith', 'email_address' => '<EMAIL>', 'phone' => '0987654321']),
            Customer::factory()->make(['customer_name' => 'Bob Johnson', 'email_address' => '<EMAIL>', 'phone' => '5555555555']),
            Customer::factory()->make(['customer_name' => 'Alice Brown', 'email_address' => '<EMAIL>', 'phone' => '1111111111']),
            Customer::factory()->make(['customer_name' => 'Charlie Wilson', 'email_address' => '<EMAIL>', 'phone' => '2222222222'])
        ]);

        $this->mockCustomerRepository->shouldReceive('getAllCustomers')
            ->andReturn($customers);

        // Act
        $exportData = $this->customerService->exportCustomerData();

        // Assert
        $this->assertIsArray($exportData);
        $this->assertCount(5, $exportData);
        $this->assertArrayHasKey('name', $exportData[0]);
        $this->assertArrayHasKey('email', $exportData[0]);
        $this->assertArrayHasKey('phone', $exportData[0]);
    }
    
    /** @test */
    public function it_can_merge_duplicate_customers()
    {
        // Arrange
        $primaryCustomerId = 1;
        $duplicateCustomerId = 2;

        $primaryCustomer = Customer::factory()->make(['pk_customer_code' => $primaryCustomerId, 'email_address' => '<EMAIL>']);
        $duplicateCustomer = Customer::factory()->make(['pk_customer_code' => $duplicateCustomerId, 'email_address' => '<EMAIL>']);

        $this->mockCustomerRepository->shouldReceive('findById')
            ->with($primaryCustomerId)
            ->andReturn($primaryCustomer);

        $this->mockCustomerRepository->shouldReceive('findById')
            ->with($duplicateCustomerId)
            ->andReturn($duplicateCustomer);

        $this->mockOrderRepository->shouldReceive('transferOrdersToCustomer')
            ->with($duplicateCustomerId, $primaryCustomerId)
            ->andReturn(true);

        $this->mockCustomerRepository->shouldReceive('delete')
            ->with($duplicateCustomerId)
            ->andReturn(true);

        // Act
        $mergedCustomer = $this->customerService->mergeCustomers($primaryCustomerId, $duplicateCustomerId);

        // Assert
        $this->assertInstanceOf(Customer::class, $mergedCustomer);
        $this->assertEquals($primaryCustomerId, $mergedCustomer->pk_customer_code);
    }
}

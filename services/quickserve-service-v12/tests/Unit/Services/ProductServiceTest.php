<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services;

use App\Events\Product\ProductCreated;
use App\Events\Product\ProductDeleted;
use App\Events\Product\ProductUpdated;
use App\Exceptions\ProductException;
use App\Models\Product;
use App\Repositories\Product\ProductRepositoryInterface;
use App\Services\Product\ProductService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use Mockery;
use Tests\TestCase;

class ProductServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $productRepository;
    protected $productService;

    public function setUp(): void
    {
        parent::setUp();

        $this->productRepository = Mockery::mock(ProductRepositoryInterface::class);
        $this->productService = new ProductService($this->productRepository);
    }

    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGetAllProducts()
    {
        $filters = ['product_type' => 'Meal'];
        $products = new Collection([new Product(), new Product()]);

        $this->productRepository->shouldReceive('all')
            ->once()
            ->with($filters)
            ->andReturn($products);

        $result = $this->productService->getAllProducts($filters);

        $this->assertSame($products, $result);
    }

    public function testGetPaginatedProducts()
    {
        $perPage = 15;
        $filters = ['product_type' => 'Meal'];
        $paginator = new LengthAwarePaginator([], 0, $perPage);

        $this->productRepository->shouldReceive('paginate')
            ->once()
            ->with($perPage, $filters)
            ->andReturn($paginator);

        $result = $this->productService->getPaginatedProducts($perPage, $filters);

        $this->assertSame($paginator, $result);
    }

    public function testGetProductById()
    {
        $productId = 1;
        $product = new Product();

        $this->productRepository->shouldReceive('findById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $result = $this->productService->getProductById($productId);

        $this->assertSame($product, $result);
    }

    public function testCreateProductSuccess()
    {
        Event::fake();
        Storage::fake('public');

        $productData = [
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
            'product_type' => 'Meal',
            'food_type' => 'veg',
        ];

        $product = new Product($productData);
        $product->pk_product_code = 1;

        $this->productRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::on(function ($data) use ($productData) {
                return $data['name'] === $productData['name'] &&
                       $data['description'] === $productData['description'] &&
                       $data['unit_price'] === $productData['unit_price'] &&
                       $data['product_type'] === $productData['product_type'] &&
                       $data['food_type'] === $productData['food_type'] &&
                       $data['status'] === true &&
                       isset($data['sequence']);
            }))
            ->andReturn($product);

        $result = $this->productService->createProduct($productData);

        $this->assertSame($product, $result);
        Event::assertDispatched(ProductCreated::class, function ($event) use ($product) {
            return $event->product->pk_product_code === $product->pk_product_code;
        });
    }

    public function testCreateProductWithImage()
    {
        Event::fake();
        Storage::fake('public');

        $productData = [
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100,
            'product_type' => 'Meal',
            'food_type' => 'veg',
        ];

        $image = UploadedFile::fake()->image('product.jpg');

        $product = new Product($productData);
        $product->pk_product_code = 1;
        $product->image_path = 'products/test-image.jpg';

        $this->productRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::on(function ($data) use ($productData) {
                return $data['name'] === $productData['name'] &&
                       $data['description'] === $productData['description'] &&
                       $data['unit_price'] === $productData['unit_price'] &&
                       $data['product_type'] === $productData['product_type'] &&
                       $data['food_type'] === $productData['food_type'] &&
                       $data['status'] === true &&
                       isset($data['sequence']) &&
                       isset($data['image_path']) &&
                       strpos($data['image_path'], 'products/') === 0;
            }))
            ->andReturn($product);

        $result = $this->productService->createProduct($productData, $image);

        $this->assertSame($product, $result);
        Event::assertDispatched(ProductCreated::class, function ($event) use ($product) {
            return $event->product->pk_product_code === $product->pk_product_code;
        });
    }

    public function testUpdateProductSuccess()
    {
        Event::fake();

        $productId = 1;
        $productData = [
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'unit_price' => 150,
        ];

        $product = new Product();
        $product->pk_product_code = $productId;
        $product->name = 'Test Product';
        $product->description = 'Test Description';
        $product->unit_price = 100;

        $this->productRepository->shouldReceive('findById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->productRepository->shouldReceive('update')
            ->once()
            ->with($productId, $productData)
            ->andReturn(true);

        $result = $this->productService->updateProduct($productId, $productData);

        $this->assertTrue($result);
        Event::assertDispatched(ProductUpdated::class, function ($event) use ($product) {
            return $event->product->pk_product_code === $product->pk_product_code;
        });
    }

    public function testUpdateProductNotFound()
    {
        $productId = 1;
        $productData = [
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'unit_price' => 150,
        ];

        $this->productRepository->shouldReceive('findById')
            ->once()
            ->with($productId)
            ->andReturn(null);

        $result = $this->productService->updateProduct($productId, $productData);

        $this->assertFalse($result);
    }

    public function testDeleteProductSuccess()
    {
        Event::fake();

        $productId = 1;
        $product = new Product();
        $product->pk_product_code = $productId;
        $product->name = 'Test Product';

        $this->productRepository->shouldReceive('findById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->productRepository->shouldReceive('delete')
            ->once()
            ->with($productId)
            ->andReturn(true);

        $result = $this->productService->deleteProduct($productId);

        $this->assertTrue($result);
        Event::assertDispatched(ProductDeleted::class, function ($event) use ($product) {
            return $event->product->pk_product_code === $product->pk_product_code;
        });
    }

    public function testDeleteProductNotFound()
    {
        $productId = 1;

        $this->productRepository->shouldReceive('findById')
            ->once()
            ->with($productId)
            ->andReturn(null);

        $result = $this->productService->deleteProduct($productId);

        $this->assertFalse($result);
    }

    public function testGetProductsByType()
    {
        $type = 'Meal';
        $filters = ['food_type' => 'veg'];
        $products = new Collection([new Product(), new Product()]);

        $this->productRepository->shouldReceive('getByType')
            ->once()
            ->with($type, $filters)
            ->andReturn($products);

        $result = $this->productService->getProductsByType($type, $filters);

        $this->assertSame($products, $result);
    }

    public function testGetProductsByFoodType()
    {
        $foodType = 'veg';
        $filters = ['product_type' => 'Meal'];
        $products = new Collection([new Product(), new Product()]);

        $this->productRepository->shouldReceive('getByFoodType')
            ->once()
            ->with($foodType, $filters)
            ->andReturn($products);

        $result = $this->productService->getProductsByFoodType($foodType, $filters);

        $this->assertSame($products, $result);
    }

    public function testGetProductsByKitchenId()
    {
        $kitchenId = 1;
        $filters = ['product_type' => 'Meal'];
        $products = new Collection([new Product(), new Product()]);

        $this->productRepository->shouldReceive('getByKitchenId')
            ->once()
            ->with($kitchenId, $filters)
            ->andReturn($products);

        $result = $this->productService->getProductsByKitchenId($kitchenId, $filters);

        $this->assertSame($products, $result);
    }

    public function testGetProductsByCategory()
    {
        $category = 'Breakfast';
        $filters = ['product_type' => 'Meal'];
        $products = new Collection([new Product(), new Product()]);

        $this->productRepository->shouldReceive('getByCategory')
            ->once()
            ->with($category, $filters)
            ->andReturn($products);

        $result = $this->productService->getProductsByCategory($category, $filters);

        $this->assertSame($products, $result);
    }

    public function testSearchProducts()
    {
        $query = 'test';
        $perPage = 15;
        $paginator = new LengthAwarePaginator([], 0, $perPage);

        $this->productRepository->shouldReceive('search')
            ->once()
            ->with($query, $perPage)
            ->andReturn($paginator);

        $result = $this->productService->searchProducts($query, $perPage);

        $this->assertSame($paginator, $result);
    }

    public function testGetActiveProducts()
    {
        $filters = ['product_type' => 'Meal'];
        $products = new Collection([new Product(), new Product()]);

        $this->productRepository->shouldReceive('getActive')
            ->once()
            ->with($filters)
            ->andReturn($products);

        $result = $this->productService->getActiveProducts($filters);

        $this->assertSame($products, $result);
    }

    public function testUpdateProductSequence()
    {
        $productIds = [3, 1, 2];

        $this->productRepository->shouldReceive('updateSequence')
            ->once()
            ->with($productIds)
            ->andReturn(true);

        $result = $this->productService->updateProductSequence($productIds);

        $this->assertTrue($result);
    }
}

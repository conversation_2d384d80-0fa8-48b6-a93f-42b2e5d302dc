<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services;

use App\Models\Timeslot;
use App\Services\TimeslotService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TimeslotServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TimeslotService $timeslotService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->timeslotService = new TimeslotService();
    }

    public function testGetAllTimeslots()
    {
        // Arrange
        Timeslot::factory()->count(3)->create([
            'day' => 'Monday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
        ]);

        // Act
        $result = $this->timeslotService->getAllTimeslots();

        // Assert
        $this->assertCount(3, $result);
    }

    public function testGetAllTimeslotsWithFilters()
    {
        // Arrange
        Timeslot::factory()->create([
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ]);
        Timeslot::factory()->create([
            'day' => 'Monday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
        ]);
        Timeslot::factory()->create([
            'day' => 'Tuesday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ]);

        // Act
        $result = $this->timeslotService->getAllTimeslots([
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
        ]);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals('Monday', $result->first()->day);
        $this->assertEquals('Breakfast', $result->first()->menu_type);
    }

    public function testGetAvailableTimeslots()
    {
        // Arrange
        Timeslot::factory()->create([
            'day' => 'Monday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
            'starttime' => '12:00:00',
            'endtime' => '14:00:00',
        ]);
        Timeslot::factory()->create([
            'day' => 'Monday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 0, // Inactive
            'starttime' => '14:00:00',
            'endtime' => '16:00:00',
        ]);
        Timeslot::factory()->create([
            'day' => 'Tuesday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
            'starttime' => '12:00:00',
            'endtime' => '14:00:00',
        ]);

        // Act
        $result = $this->timeslotService->getAvailableTimeslots('Monday', 'Lunch', 'Main');

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals('Monday', $result->first()->day);
        $this->assertEquals('Lunch', $result->first()->menu_type);
        $this->assertEquals('Main', $result->first()->kitchen);
        $this->assertEquals(1, $result->first()->status);
    }

    public function testGetAvailableTimeslotsFiltersPastTimeslotsForToday()
    {
        // Arrange
        $today = Carbon::now()->format('Y-m-d');
        $dayOfWeek = Carbon::now()->format('l'); // e.g., "Monday"
        
        // Create a timeslot for 2 hours ago
        $pastTime = Carbon::now()->subHours(2)->format('H:i:s');
        Timeslot::factory()->create([
            'day' => $dayOfWeek,
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
            'starttime' => $pastTime,
            'endtime' => Carbon::now()->subHour()->format('H:i:s'),
        ]);
        
        // Create a timeslot for 2 hours from now
        $futureTime = Carbon::now()->addHours(2)->format('H:i:s');
        Timeslot::factory()->create([
            'day' => $dayOfWeek,
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
            'starttime' => $futureTime,
            'endtime' => Carbon::now()->addHours(4)->format('H:i:s'),
        ]);

        // Act
        $result = $this->timeslotService->getAvailableTimeslots($dayOfWeek, 'Lunch', 'Main', $today);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals($futureTime, $result->first()->starttime);
    }

    public function testGetTimeslot()
    {
        // Arrange
        $timeslot = Timeslot::factory()->create();

        // Act
        $result = $this->timeslotService->getTimeslot($timeslot->id);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($timeslot->id, $result->id);
    }

    public function testCreateTimeslot()
    {
        // Arrange
        $data = [
            'company_id' => 1,
            'unit_id' => 1,
            'starttime' => '12:00:00',
            'endtime' => '14:00:00',
            'day' => 'Monday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
        ];

        // Act
        $result = $this->timeslotService->createTimeslot($data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('12:00:00', $result->starttime);
        $this->assertEquals('14:00:00', $result->endtime);
        $this->assertEquals('Monday', $result->day);
        $this->assertEquals('Lunch', $result->menu_type);
        $this->assertEquals('Main', $result->kitchen);
        $this->assertEquals(1, $result->status);
    }

    public function testUpdateTimeslot()
    {
        // Arrange
        $timeslot = Timeslot::factory()->create([
            'starttime' => '12:00:00',
            'endtime' => '14:00:00',
        ]);
        $data = [
            'starttime' => '13:00:00',
            'endtime' => '15:00:00',
        ];

        // Act
        $result = $this->timeslotService->updateTimeslot($timeslot->id, $data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('13:00:00', $result->starttime);
        $this->assertEquals('15:00:00', $result->endtime);
    }

    public function testDeleteTimeslot()
    {
        // Arrange
        $timeslot = Timeslot::factory()->create();

        // Act
        $result = $this->timeslotService->deleteTimeslot($timeslot->id);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseMissing('timeslot', ['id' => $timeslot->id]);
    }

    public function testIsTimeslotAvailable()
    {
        // Arrange
        Timeslot::factory()->create([
            'starttime' => '12:00:00',
            'day' => 'Monday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
        ]);

        // Act
        $result1 = $this->timeslotService->isTimeslotAvailable('12:00:00', 'Monday', 'Lunch', 'Main');
        $result2 = $this->timeslotService->isTimeslotAvailable('13:00:00', 'Monday', 'Lunch', 'Main');

        // Assert
        $this->assertFalse($result1); // Should not be available (already exists)
        $this->assertTrue($result2); // Should be available (doesn't exist)
    }
}

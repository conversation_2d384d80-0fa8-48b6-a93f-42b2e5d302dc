<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services\Backorder;

use App\Events\Backorder\BackorderCancelled;
use App\Events\Backorder\BackorderCompleted;
use App\Events\Backorder\BackorderCreated;
use App\Events\Backorder\BackorderDeleted;
use App\Events\Backorder\BackorderUpdated;
use App\Exceptions\BackorderException;
use App\Models\Backorder;
use App\Models\Order;
use App\Models\Product;
use App\Repositories\Backorder\BackorderRepositoryInterface;
use App\Services\Backorder\BackorderService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class BackorderServiceTest extends TestCase
{
    /**
     * @var BackorderRepositoryInterface|Mockery\MockInterface
     */
    protected $backorderRepository;

    /**
     * @var BackorderService
     */
    protected $backorderService;

    /**
     * Set up the test.
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->backorderRepository = Mockery::mock(BackorderRepositoryInterface::class);
        $this->backorderService = new BackorderService($this->backorderRepository);

        Event::fake();
    }

    /**
     * Test getting all backorders.
     */
    public function testGetAllBackorders(): void
    {
        // Arrange
        $filters = ['status' => 'pending'];
        $backorders = new Collection([
            new Backorder(['id' => 1, 'order_id' => 101, 'customer_id' => 201, 'product_id' => 301, 'status' => 'pending']),
            new Backorder(['id' => 2, 'order_id' => 102, 'customer_id' => 202, 'product_id' => 302, 'status' => 'pending']),
        ]);

        // Mock repository
        $this->backorderRepository->shouldReceive('all')
            ->once()
            ->with($filters)
            ->andReturn($backorders);

        // Act
        $result = $this->backorderService->getAllBackorders($filters);

        // Assert
        $this->assertSame($backorders, $result);
        $this->assertCount(2, $result);
    }

    /**
     * Test getting paginated backorders.
     */
    public function testGetPaginatedBackorders(): void
    {
        // Arrange
        $perPage = 10;
        $filters = ['status' => 'pending'];
        $backorders = $this->createMock(\Illuminate\Pagination\LengthAwarePaginator::class);

        // Mock repository
        $this->backorderRepository->shouldReceive('paginate')
            ->once()
            ->with($perPage, $filters)
            ->andReturn($backorders);

        // Act
        $result = $this->backorderService->getPaginatedBackorders($perPage, $filters);

        // Assert
        $this->assertSame($backorders, $result);
    }

    /**
     * Test getting backorder by ID.
     */
    public function testGetBackorderById(): void
    {
        // Arrange
        $id = 1;
        $backorder = new Backorder(['id' => $id, 'order_id' => 101, 'customer_id' => 201, 'product_id' => 301, 'status' => 'pending']);

        // Mock repository
        $this->backorderRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($backorder);

        // Act
        $result = $this->backorderService->getBackorderById($id);

        // Assert
        $this->assertSame($backorder, $result);
    }

    /**
     * Test creating a backorder.
     */
    public function testCreateBackorder(): void
    {
        // Arrange
        $data = [
            'order_id' => 101,
            'order_no' => 'ORD101',
            'customer_id' => 201,
            'product_id' => 301,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ];

        $backorder = new Backorder($data);
        $backorder->id = 1;

        // Mock repository
        $this->backorderRepository->shouldReceive('create')
            ->once()
            ->with($data)
            ->andReturn($backorder);

        // Act
        $result = $this->backorderService->createBackorder($data);

        // Assert
        $this->assertSame($backorder, $result);
        Event::assertDispatched(BackorderCreated::class, function ($event) use ($backorder) {
            return $event->backorder->id === $backorder->id;
        });
    }

    /**
     * Test creating a backorder with missing required fields.
     */
    public function testCreateBackorderWithMissingRequiredFields(): void
    {
        // Arrange
        $data = [
            'order_id' => 101,
            'order_no' => 'ORD101',
            // Missing customer_id
            'product_id' => 301,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
        ];

        // Assert
        $this->expectException(BackorderException::class);
        $this->expectExceptionMessage('The customer_id field is required.');

        // Act
        $this->backorderService->createBackorder($data);
    }

    /**
     * Test creating a backorder from an order.
     */
    public function testCreateBackorderFromOrder(): void
    {
        $this->markTestIncomplete('This test requires database setup for Product::find() - needs refactoring to use dependency injection');
        return;
        // Arrange
        $order = new Order([
            'pk_order_no' => 101,
            'order_no' => 'ORD101',
            'customer_code' => 201,
            'product_code' => 301,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
            'company_id' => 1,
            'unit_id' => 1,
        ]);

        $product = new Product([
            'pk_product_code' => 301,
            'name' => 'Test Product',
        ]);

        $reason = 'Out of stock';

        $backorderData = [
            'company_id' => 1,
            'unit_id' => 1,
            'order_id' => 101,
            'order_no' => 'ORD101',
            'customer_id' => 201,
            'product_id' => 301,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ];

        $backorder = new Backorder($backorderData);
        $backorder->id = 1;

        // Mock Product::find
        $this->mock(Product::class, function ($mock) use ($product) {
            $mock->shouldReceive('find')
                ->once()
                ->with(301)
                ->andReturn($product);
        });

        // Mock repository
        $this->backorderRepository->shouldReceive('create')
            ->once()
            ->with(Mockery::on(function ($arg) use ($backorderData) {
                return $arg['order_id'] === $backorderData['order_id'] &&
                    $arg['order_no'] === $backorderData['order_no'] &&
                    $arg['customer_id'] === $backorderData['customer_id'] &&
                    $arg['product_id'] === $backorderData['product_id'] &&
                    $arg['product_name'] === $backorderData['product_name'] &&
                    $arg['quantity'] === $backorderData['quantity'] &&
                    $arg['amount'] === $backorderData['amount'] &&
                    $arg['order_date'] === $backorderData['order_date'] &&
                    $arg['order_menu'] === $backorderData['order_menu'] &&
                    $arg['reason'] === $backorderData['reason'] &&
                    $arg['status'] === $backorderData['status'];
            }))
            ->andReturn($backorder);

        // Act
        $result = $this->backorderService->createBackorderFromOrder($order, $reason);

        // Assert
        $this->assertSame($backorder, $result);
        Event::assertDispatched(BackorderCreated::class, function ($event) use ($backorder) {
            return $event->backorder->id === $backorder->id;
        });
    }

    /**
     * Test updating a backorder.
     */
    public function testUpdateBackorder(): void
    {
        // Arrange
        $id = 1;
        $data = [
            'quantity' => 3,
            'amount' => 150.00,
            'reason' => 'Updated reason',
        ];

        $backorder = new Backorder([
            'id' => $id,
            'order_id' => 101,
            'order_no' => 'ORD101',
            'customer_id' => 201,
            'product_id' => 301,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ]);

        $updatedBackorder = new Backorder([
            'id' => $id,
            'order_id' => 101,
            'order_no' => 'ORD101',
            'customer_id' => 201,
            'product_id' => 301,
            'product_name' => 'Test Product',
            'quantity' => 3,
            'amount' => 150.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
            'reason' => 'Updated reason',
            'status' => 'pending',
        ]);

        // Mock repository
        $this->backorderRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($backorder);

        $this->backorderRepository->shouldReceive('update')
            ->once()
            ->with($id, $data)
            ->andReturn(true);

        $this->backorderRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($updatedBackorder);

        // Act
        $result = $this->backorderService->updateBackorder($id, $data);

        // Assert
        $this->assertTrue($result);
        Event::assertDispatched(BackorderUpdated::class, function ($event) use ($updatedBackorder) {
            return $event->backorder->id === $updatedBackorder->id;
        });
    }

    /**
     * Test updating a non-existent backorder.
     */
    public function testUpdateNonExistentBackorder(): void
    {
        // Arrange
        $id = 999;
        $data = [
            'quantity' => 3,
            'amount' => 150.00,
            'reason' => 'Updated reason',
        ];

        // Mock repository
        $this->backorderRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Assert
        $this->expectException(BackorderException::class);
        $this->expectExceptionMessage('Backorder not found.');

        // Act
        $this->backorderService->updateBackorder($id, $data);
    }

    /**
     * Test completing a backorder.
     */
    public function testCompleteBackorder(): void
    {
        // Arrange
        $id = 1;
        $backorder = new Backorder([
            'id' => $id,
            'order_id' => 101,
            'order_no' => 'ORD101',
            'customer_id' => 201,
            'product_id' => 301,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ]);

        $completedBackorder = new Backorder([
            'id' => $id,
            'order_id' => 101,
            'order_no' => 'ORD101',
            'customer_id' => 201,
            'product_id' => 301,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => '2023-06-01',
            'order_menu' => 'Breakfast',
            'reason' => 'Out of stock',
            'status' => 'completed',
        ]);

        // Mock repository
        $this->backorderRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($backorder);

        $this->backorderRepository->shouldReceive('update')
            ->once()
            ->with($id, ['status' => 'completed'])
            ->andReturn(true);

        $this->backorderRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($completedBackorder);

        // Act
        $result = $this->backorderService->completeBackorder($id);

        // Assert
        $this->assertTrue($result);
        Event::assertDispatched(BackorderCompleted::class, function ($event) use ($completedBackorder) {
            return $event->backorder->id === $completedBackorder->id;
        });
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services;

use App\Services\ConfigService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class ConfigServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ConfigService $configService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->configService = new ConfigService();
    }

    public function testGetConfigFromDatabase()
    {
        // Arrange
        $key = 'test_key';
        $value = 'test_value';
        DB::table('settings')->insert([
            'setting_key' => $key,
            'setting_value' => $value,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Act
        $result = $this->configService->getConfig($key);

        // Assert
        $this->assertEquals($value, $result);
    }

    public function testGetConfigFromCache()
    {
        // Arrange
        $key = 'test_key';
        $value = 'test_value';
        $cacheKey = "config:{$key}";
        Cache::put($cacheKey, $value, 3600);

        // Act
        $result = $this->configService->getConfig($key);

        // Assert
        $this->assertEquals($value, $result);
    }

    public function testGetConfigFromLaravelConfig()
    {
        // Arrange
        $key = 'test_key';
        $value = 'test_value';
        Config::set($key, $value);

        // Act
        $result = $this->configService->getConfig($key);

        // Assert
        $this->assertEquals($value, $result);
    }

    public function testGetConfigFromGlobalKey()
    {
        // Arrange
        $key = 'test_key';
        $globalKey = 'GLOBAL_TEST_KEY';
        $value = 'test_value';
        DB::table('settings')->insert([
            'setting_key' => $globalKey,
            'setting_value' => $value,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Act
        $result = $this->configService->getConfig($key);

        // Assert
        $this->assertEquals($value, $result);
    }

    public function testGetConfigReturnsDefaultWhenNotFound()
    {
        // Arrange
        $key = 'nonexistent_key';
        $default = 'default_value';

        // Act
        $result = $this->configService->getConfig($key, $default);

        // Assert
        $this->assertEquals($default, $result);
    }

    public function testSetConfig()
    {
        // Arrange
        $key = 'test_key';
        $value = 'test_value';

        // Act
        $result = $this->configService->setConfig($key, $value);

        // Assert
        $this->assertTrue($result);
        $this->assertEquals($value, $this->configService->getConfig($key));
        $this->assertEquals($value, Cache::get("config:{$key}"));
        $this->assertDatabaseHas('settings', [
            'setting_key' => $key,
            'setting_value' => $value,
        ]);
    }

    public function testGetAllConfig()
    {
        // Arrange
        DB::table('settings')->insert([
            ['setting_key' => 'key1', 'setting_value' => 'value1', 'created_at' => now(), 'updated_at' => now()],
            ['setting_key' => 'key2', 'setting_value' => 'value2', 'created_at' => now(), 'updated_at' => now()],
        ]);
        Config::set('key3', 'value3');

        // Act
        $result = $this->configService->getAllConfig();

        // Assert
        $this->assertArrayHasKey('key1', $result);
        $this->assertArrayHasKey('key2', $result);
        $this->assertArrayHasKey('key3', $result);
        $this->assertEquals('value1', $result['key1']);
        $this->assertEquals('value2', $result['key2']);
        $this->assertEquals('value3', $result['key3']);
    }

    public function testGetAllSettings()
    {
        // Arrange
        DB::table('settings')->insert([
            ['setting_key' => 'key1', 'setting_value' => 'value1', 'created_at' => now(), 'updated_at' => now()],
            ['setting_key' => 'key2', 'setting_value' => 'value2', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Act
        $result = $this->configService->getAllSettings();

        // Assert
        $this->assertArrayHasKey('key1', $result);
        $this->assertArrayHasKey('key2', $result);
        $this->assertEquals('value1', $result['key1']);
        $this->assertEquals('value2', $result['key2']);
    }

    public function testHasConfig()
    {
        // Arrange
        $key = 'test_key';
        $value = 'test_value';
        DB::table('settings')->insert([
            'setting_key' => $key,
            'setting_value' => $value,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Act
        $result = $this->configService->hasConfig($key);

        // Assert
        $this->assertTrue($result);
    }

    public function testHasConfigReturnsFalseWhenNotFound()
    {
        // Arrange
        $key = 'nonexistent_key';

        // Act
        $result = $this->configService->hasConfig($key);

        // Assert
        $this->assertFalse($result);
    }
}

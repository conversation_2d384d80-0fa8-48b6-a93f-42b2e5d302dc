<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services;

use App\Models\Backorder;
use App\Models\Order;
use App\Models\Product;
use App\Services\BackorderService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BackorderServiceTest extends TestCase
{
    use RefreshDatabase;

    protected BackorderService $backorderService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->backorderService = new BackorderService();
    }

    public function testGetAllBackorders()
    {
        // Arrange
        Backorder::factory()->count(3)->create([
            'customer_id' => 1,
            'order_id' => 1,
            'product_id' => 1,
            'status' => 'pending',
        ]);

        // Act
        $result = $this->backorderService->getAllBackorders();

        // Assert
        $this->assertCount(3, $result);
    }

    public function testGetAllBackordersWithFilters()
    {
        // Arrange
        Backorder::factory()->create([
            'customer_id' => 1,
            'order_id' => 1,
            'product_id' => 1,
            'status' => 'pending',
        ]);
        Backorder::factory()->create([
            'customer_id' => 1,
            'order_id' => 2,
            'product_id' => 1,
            'status' => 'pending',
        ]);
        Backorder::factory()->create([
            'customer_id' => 2,
            'order_id' => 3,
            'product_id' => 2,
            'status' => 'completed',
        ]);

        // Act
        $result = $this->backorderService->getAllBackorders([
            'customer_id' => 1,
            'status' => 'pending',
        ]);

        // Assert
        $this->assertCount(2, $result);
        $this->assertEquals(1, $result->first()->customer_id);
        $this->assertEquals('pending', $result->first()->status);
    }

    public function testGetAllBackordersWithDateRangeFilter()
    {
        // Arrange
        Backorder::factory()->create([
            'order_date' => '2023-05-01',
            'status' => 'pending',
        ]);
        Backorder::factory()->create([
            'order_date' => '2023-05-15',
            'status' => 'pending',
        ]);
        Backorder::factory()->create([
            'order_date' => '2023-05-30',
            'status' => 'pending',
        ]);

        // Act
        $result = $this->backorderService->getAllBackorders([
            'start_date' => '2023-05-10',
            'end_date' => '2023-05-20',
        ]);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals('2023-05-15', $result->first()->order_date->format('Y-m-d'));
    }

    public function testGetBackorder()
    {
        // Arrange
        $backorder = Backorder::factory()->create();

        // Act
        $result = $this->backorderService->getBackorder($backorder->id);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($backorder->id, $result->id);
    }

    public function testCreateBackorder()
    {
        // Arrange
        $data = [
            'company_id' => 1,
            'unit_id' => 1,
            'order_id' => 1,
            'order_no' => 'ORD20230517120000',
            'customer_id' => 1,
            'product_id' => 1,
            'product_name' => 'Vegetable Meal',
            'quantity' => 1,
            'amount' => 100.00,
            'order_date' => '2023-05-17',
            'order_menu' => 'Lunch',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ];

        // Act
        $result = $this->backorderService->createBackorder($data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals(1, $result->order_id);
        $this->assertEquals('ORD20230517120000', $result->order_no);
        $this->assertEquals(1, $result->customer_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals('Vegetable Meal', $result->product_name);
        $this->assertEquals(1, $result->quantity);
        $this->assertEquals(100.00, $result->amount);
        $this->assertEquals('2023-05-17', $result->order_date->format('Y-m-d'));
        $this->assertEquals('Lunch', $result->order_menu);
        $this->assertEquals('Out of stock', $result->reason);
        $this->assertEquals('pending', $result->status);
    }

    public function testCreateBackorderFromOrder()
    {
        // Arrange
        $order = Order::factory()->create([
            'pk_order_no' => 1,
            'order_no' => 'ORD20230517120000',
            'customer_code' => 1,
            'product_code' => 1,
            'product_name' => 'Vegetable Meal',
            'quantity' => 1,
            'amount' => 100.00,
            'order_date' => '2023-05-17',
            'order_menu' => 'Lunch',
        ]);
        $product = Product::factory()->create([
            'pk_product_code' => 1,
            'name' => 'Vegetable Meal',
        ]);
        $reason = 'Out of stock';

        // Act
        $result = $this->backorderService->createBackorderFromOrder($order, $reason);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals(1, $result->order_id);
        $this->assertEquals('ORD20230517120000', $result->order_no);
        $this->assertEquals(1, $result->customer_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals('Vegetable Meal', $result->product_name);
        $this->assertEquals(1, $result->quantity);
        $this->assertEquals(100.00, $result->amount);
        $this->assertEquals('2023-05-17', $result->order_date->format('Y-m-d'));
        $this->assertEquals('Lunch', $result->order_menu);
        $this->assertEquals('Out of stock', $result->reason);
        $this->assertEquals('pending', $result->status);
    }

    public function testUpdateBackorder()
    {
        // Arrange
        $backorder = Backorder::factory()->create([
            'quantity' => 1,
            'amount' => 100.00,
            'reason' => 'Out of stock',
            'status' => 'pending',
        ]);
        $data = [
            'quantity' => 2,
            'amount' => 200.00,
            'reason' => 'Customer request',
            'status' => 'completed',
        ];

        // Act
        $result = $this->backorderService->updateBackorder($backorder->id, $data);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals(2, $result->quantity);
        $this->assertEquals(200.00, $result->amount);
        $this->assertEquals('Customer request', $result->reason);
        $this->assertEquals('completed', $result->status);
    }

    public function testDeleteBackorder()
    {
        // Arrange
        $backorder = Backorder::factory()->create();

        // Act
        $result = $this->backorderService->deleteBackorder($backorder->id);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseMissing('backorders', ['id' => $backorder->id]);
    }

    public function testCompleteBackorder()
    {
        // Arrange
        $backorder = Backorder::factory()->create([
            'status' => 'pending',
        ]);

        // Act
        $result = $this->backorderService->completeBackorder($backorder->id);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('completed', $result->status);
    }

    public function testCancelBackorder()
    {
        // Arrange
        $backorder = Backorder::factory()->create([
            'status' => 'pending',
        ]);

        // Act
        $result = $this->backorderService->cancelBackorder($backorder->id);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('cancelled', $result->status);
    }
}

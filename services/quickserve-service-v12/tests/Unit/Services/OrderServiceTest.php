<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services;

use App\DTOs\Payment\PaymentResponseDTO;
use App\Events\OrderCancelled;
use App\Events\OrderCreated;
use App\Events\OrderStatusUpdated;
use App\Exceptions\Customer\CustomerException;
use App\Exceptions\Order\OrderException;
use App\Exceptions\Payment\PaymentException;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Repositories\OrderRepository;
use App\Services\Customer\CustomerServiceClient;
use App\Services\OrderService;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class OrderServiceTest extends TestCase
{

    /**
     * @var OrderRepository|Mockery\MockInterface
     */
    protected $orderRepository;

    /**
     * @var CustomerServiceClient|Mockery\MockInterface
     */
    protected $customerService;

    /**
     * @var PaymentServiceClient|Mockery\MockInterface
     */
    protected $paymentService;

    /**
     * @var OrderService
     */
    protected $orderService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = Mockery::mock(OrderRepository::class);
        $this->customerService = Mockery::mock(CustomerServiceClient::class);
        $this->paymentService = Mockery::mock(PaymentServiceClient::class);

        $this->orderService = new OrderService(
            $this->orderRepository,
            $this->customerService,
            $this->paymentService
        );
    }

    /**
     * Test creating an order.
     *
     * @return void
     */
    public function testCreateOrder(): void
    {
        Event::fake();

        $orderData = [
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'items' => [
                [
                    'product_code' => 'PROD123',
                    'product_name' => 'Test Product',
                    'quantity' => 2,
                    'amount' => 50.00,
                    'tax' => 5.00,
                ],
            ],
        ];

        $customer = [
            'customer_code' => 'CUST123',
            'customer_name' => 'Test Customer',
            'email_address' => '<EMAIL>',
            'phone' => '1234567890',
        ];

        $order = new Order([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'created_date' => now(),
        ]);

        $this->customerService->shouldReceive('getCustomer')
            ->once()
            ->with('CUST123')
            ->andReturn($customer);

        $this->orderRepository->shouldReceive('create')
            ->once()
            ->andReturn($order);

        // Mock the DB facade
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();
        DB::shouldReceive('rollBack')->zeroOrMoreTimes();

        // Mock the OrderDetail class
        $orderDetail = Mockery::mock('alias:' . OrderDetail::class);
        $orderDetail->shouldReceive('create')
            ->once()
            ->andReturn(new OrderDetail([
                'id' => 1,
                'ref_order_no' => $order->order_no,
                'product_code' => 'PROD123',
                'product_name' => 'Test Product',
                'quantity' => 2,
                'amount' => 50.00,
                'tax' => 5.00,
                'company_id' => 1,
                'unit_id' => 1,
            ]));

        $result = $this->orderService->createOrder($orderData);

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('CUST123', $result->customer_code);
        $this->assertEquals(100.00, $result->amount);

        Event::assertDispatched(OrderCreated::class, function ($event) use ($order) {
            return $event->order->id === $order->id;
        });
    }

    /**
     * Test creating an order with invalid customer.
     *
     * @return void
     */
    public function testCreateOrderWithInvalidCustomer(): void
    {
        $orderData = [
            'customer_code' => 'INVALID',
            'company_id' => 1,
            'unit_id' => 1,
            'amount' => 100.00,
        ];

        $this->customerService->shouldReceive('getCustomer')
            ->once()
            ->with('INVALID')
            ->andReturn(null);

        $this->expectException(CustomerException::class);
        $this->expectExceptionMessage('Customer not found');

        $this->orderService->createOrder($orderData);
    }

    /**
     * Test updating an order.
     *
     * @return void
     */
    public function testUpdateOrder(): void
    {
        Event::fake();

        $order = new Order([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'created_date' => now(),
        ]);

        $updateData = [
            'order_status' => 'Processing',
            'amount' => 150.00,
        ];

        $updatedOrder = new Order([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'Processing',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 150.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'created_date' => now(),
        ]);

        $this->orderRepository->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $this->orderRepository->shouldReceive('update')
            ->once()
            ->with($order, $updateData)
            ->andReturn($updatedOrder);

        // Mock the DB facade
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();
        DB::shouldReceive('rollBack')->zeroOrMoreTimes();

        $result = $this->orderService->updateOrder(1, $updateData);

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('Processing', $result->order_status);
        $this->assertEquals(150.00, $result->amount);

        Event::assertDispatched(OrderStatusUpdated::class, function ($event) use ($updatedOrder) {
            return $event->order->id === $updatedOrder->id;
        });
    }

    /**
     * Test updating a non-existent order.
     *
     * @return void
     */
    public function testUpdateNonExistentOrder(): void
    {
        $this->orderRepository->shouldReceive('findById')
            ->once()
            ->with(999)
            ->andReturn(null);

        $this->expectException(OrderException::class);
        $this->expectExceptionMessage('Order not found');

        $this->orderService->updateOrder(999, ['order_status' => 'Processing']);
    }

    /**
     * Test cancelling an order.
     *
     * @return void
     */
    public function testCancelOrder(): void
    {
        Event::fake();

        $order = new Order([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'created_date' => now(),
        ]);

        $cancelledOrder = new Order([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'Cancelled',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'cancellation_reason' => 'Test reason',
            'cancelled_at' => now(),
            'created_date' => now(),
        ]);

        // Make sure the cancellation_reason is accessible
        $cancelledOrder->cancellation_reason = 'Test reason';

        $this->orderRepository->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $this->orderRepository->shouldReceive('update')
            ->once()
            ->andReturn($cancelledOrder);

        $result = $this->orderService->cancelOrder(1, 'Test reason');

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('Cancelled', $result->order_status);
        $this->assertEquals('Test reason', $result->cancellation_reason);

        Event::assertDispatched(OrderCancelled::class, function ($event) use ($cancelledOrder) {
            return $event->order->id === $cancelledOrder->id;
        });
    }

    /**
     * Test cancelling a non-existent order.
     *
     * @return void
     */
    public function testCancelNonExistentOrder(): void
    {
        $this->orderRepository->shouldReceive('findById')
            ->once()
            ->with(999)
            ->andReturn(null);

        $this->expectException(OrderException::class);
        $this->expectExceptionMessage('Order not found');

        $this->orderService->cancelOrder(999);
    }
}

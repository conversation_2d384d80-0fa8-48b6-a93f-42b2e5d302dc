<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services\LocationMapping;

use App\Events\LocationMapping\LocationMappingCreated;
use App\Events\LocationMapping\LocationMappingDeleted;
use App\Events\LocationMapping\LocationMappingUpdated;
use App\Exceptions\LocationMappingException;
use App\Models\LocationMapping;
use App\Repositories\LocationMapping\LocationMappingRepositoryInterface;
use App\Services\LocationMapping\LocationMappingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class LocationMappingServiceTest extends TestCase
{
    /**
     * @var LocationMappingRepositoryInterface|Mockery\MockInterface
     */
    protected $locationMappingRepository;

    /**
     * @var LocationMappingService
     */
    protected $locationMappingService;

    /**
     * Set up the test.
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->locationMappingRepository = Mockery::mock(LocationMappingRepositoryInterface::class);
        $this->locationMappingService = new LocationMappingService($this->locationMappingRepository);

        Event::fake();
    }

    /**
     * Test getting all location mappings.
     */
    public function testGetAllLocationMappings(): void
    {
        // Arrange
        $filters = ['city' => 'New York'];
        $locationMappings = new Collection([
            new LocationMapping(['id' => 1, 'location_code' => 'LOC001', 'location_name' => 'Downtown', 'city_code' => 'NYC', 'city_name' => 'New York']),
            new LocationMapping(['id' => 2, 'location_code' => 'LOC002', 'location_name' => 'Uptown', 'city_code' => 'NYC', 'city_name' => 'New York']),
        ]);

        // Mock repository
        $this->locationMappingRepository->shouldReceive('all')
            ->once()
            ->with($filters)
            ->andReturn($locationMappings);

        // Act
        $result = $this->locationMappingService->getAllLocationMappings($filters);

        // Assert
        $this->assertSame($locationMappings, $result);
        $this->assertCount(2, $result);
    }

    /**
     * Test getting paginated location mappings.
     */
    public function testGetPaginatedLocationMappings(): void
    {
        // Arrange
        $perPage = 10;
        $filters = ['city' => 'New York'];
        $locationMappings = $this->createMock(\Illuminate\Pagination\LengthAwarePaginator::class);

        // Mock repository
        $this->locationMappingRepository->shouldReceive('paginate')
            ->once()
            ->with($perPage, $filters)
            ->andReturn($locationMappings);

        // Act
        $result = $this->locationMappingService->getPaginatedLocationMappings($perPage, $filters);

        // Assert
        $this->assertSame($locationMappings, $result);
    }

    /**
     * Test getting location mapping by ID.
     */
    public function testGetLocationMappingById(): void
    {
        // Arrange
        $id = 1;
        $locationMapping = new LocationMapping(['id' => $id, 'location_code' => 'LOC001', 'location_name' => 'Downtown', 'city_code' => 'NYC', 'city_name' => 'New York']);

        // Mock repository
        $this->locationMappingRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($locationMapping);

        // Act
        $result = $this->locationMappingService->getLocationMappingById($id);

        // Assert
        $this->assertSame($locationMapping, $result);
    }

    /**
     * Test creating a location mapping.
     */
    public function testCreateLocationMapping(): void
    {
        // Arrange
        $data = [
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'NYC',
            'city_name' => 'New York',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'delivery_charges' => 10.00,
            'delivery_time' => '30',
            'status' => 1,
        ];

        $locationMapping = new LocationMapping($data);
        $locationMapping->id = 1;

        // Mock repository
        $this->locationMappingRepository->shouldReceive('getByLocationCode')
            ->once()
            ->with($data['location_code'])
            ->andReturn(new Collection());

        $this->locationMappingRepository->shouldReceive('create')
            ->once()
            ->with($data)
            ->andReturn($locationMapping);

        // Act
        $result = $this->locationMappingService->createLocationMapping($data);

        // Assert
        $this->assertSame($locationMapping, $result);
        Event::assertDispatched(LocationMappingCreated::class, function ($event) use ($locationMapping) {
            return $event->locationMapping->id === $locationMapping->id;
        });
    }

    /**
     * Test creating a duplicate location mapping.
     */
    public function testCreateDuplicateLocationMapping(): void
    {
        // Arrange
        $data = [
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'NYC',
            'city_name' => 'New York',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'status' => 1,
        ];

        $existingMapping = new LocationMapping([
            'id' => 1,
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'NYC',
            'city_name' => 'New York',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'status' => 1,
        ]);

        // Mock repository - should only call getByLocationCode, not create
        $this->locationMappingRepository->shouldReceive('getByLocationCode')
            ->once()
            ->with($data['location_code'])
            ->andReturn(new Collection([$existingMapping]));

        // Should NOT call create method since duplicate exists
        $this->locationMappingRepository->shouldNotReceive('create');

        // Assert
        $this->expectException(LocationMappingException::class);
        $this->expectExceptionMessage('Location mapping already exists for this location, city, and kitchen.');

        // Act
        $this->locationMappingService->createLocationMapping($data);
    }

    /**
     * Test updating a location mapping.
     */
    public function testUpdateLocationMapping(): void
    {
        // Arrange
        $id = 1;
        $data = [
            'location_name' => 'Downtown Updated',
            'delivery_charges' => 15.00,
        ];

        $locationMapping = new LocationMapping([
            'id' => $id,
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'NYC',
            'city_name' => 'New York',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'delivery_charges' => 10.00,
            'delivery_time' => '30',
            'status' => 1,
        ]);

        $updatedLocationMapping = new LocationMapping([
            'id' => $id,
            'location_code' => 'LOC001',
            'location_name' => 'Downtown Updated',
            'city_code' => 'NYC',
            'city_name' => 'New York',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'delivery_charges' => 15.00,
            'delivery_time' => '30',
            'status' => 1,
        ]);

        // Mock repository
        $this->locationMappingRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($locationMapping);

        $this->locationMappingRepository->shouldReceive('update')
            ->once()
            ->with($id, $data)
            ->andReturn(true);

        $this->locationMappingRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($updatedLocationMapping);

        // Act
        $result = $this->locationMappingService->updateLocationMapping($id, $data);

        // Assert
        $this->assertTrue($result);
        Event::assertDispatched(LocationMappingUpdated::class, function ($event) use ($updatedLocationMapping) {
            return $event->locationMapping->id === $updatedLocationMapping->id;
        });
    }

    /**
     * Test updating a non-existent location mapping.
     */
    public function testUpdateNonExistentLocationMapping(): void
    {
        // Arrange
        $id = 999;
        $data = [
            'location_name' => 'Downtown Updated',
            'delivery_charges' => 15.00,
        ];

        // Mock repository
        $this->locationMappingRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Assert
        $this->expectException(LocationMappingException::class);
        $this->expectExceptionMessage('Location mapping not found.');

        // Act
        $this->locationMappingService->updateLocationMapping($id, $data);
    }

    /**
     * Test deleting a location mapping.
     */
    public function testDeleteLocationMapping(): void
    {
        // Arrange
        $id = 1;
        $locationMapping = new LocationMapping([
            'id' => $id,
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'NYC',
            'city_name' => 'New York',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'delivery_charges' => 10.00,
            'delivery_time' => '30',
            'status' => 1,
        ]);

        // Mock repository
        $this->locationMappingRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($locationMapping);

        $this->locationMappingRepository->shouldReceive('delete')
            ->once()
            ->with($id)
            ->andReturn(true);

        // Act
        $result = $this->locationMappingService->deleteLocationMapping($id);

        // Assert
        $this->assertTrue($result);
        Event::assertDispatched(LocationMappingDeleted::class, function ($event) use ($locationMapping) {
            return $event->locationMapping->id === $locationMapping->id;
        });
    }

    /**
     * Test deleting a non-existent location mapping.
     */
    public function testDeleteNonExistentLocationMapping(): void
    {
        // Arrange
        $id = 999;

        // Mock repository
        $this->locationMappingRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Assert
        $this->expectException(LocationMappingException::class);
        $this->expectExceptionMessage('Location mapping not found.');

        // Act
        $this->locationMappingService->deleteLocationMapping($id);
    }

    /**
     * Test getting location mappings by kitchen ID.
     */
    public function testGetLocationMappingsByKitchenId(): void
    {
        // Arrange
        $kitchenId = 1;
        $filters = ['status' => 1];
        $locationMappings = new Collection([
            new LocationMapping(['id' => 1, 'location_code' => 'LOC001', 'location_name' => 'Downtown', 'city_code' => 'NYC', 'city_name' => 'New York', 'kitchen_id' => $kitchenId]),
            new LocationMapping(['id' => 2, 'location_code' => 'LOC002', 'location_name' => 'Uptown', 'city_code' => 'NYC', 'city_name' => 'New York', 'kitchen_id' => $kitchenId]),
        ]);

        // Mock repository
        $this->locationMappingRepository->shouldReceive('getByKitchenId')
            ->once()
            ->with($kitchenId, $filters)
            ->andReturn($locationMappings);

        // Act
        $result = $this->locationMappingService->getLocationMappingsByKitchenId($kitchenId, $filters);

        // Assert
        $this->assertSame($locationMappings, $result);
        $this->assertCount(2, $result);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services\Timeslot;

use App\Events\Timeslot\TimeslotCreated;
use App\Events\Timeslot\TimeslotDeleted;
use App\Events\Timeslot\TimeslotUpdated;
use App\Exceptions\TimeslotException;
use App\Models\Timeslot;
use App\Repositories\Timeslot\TimeslotRepositoryInterface;
use App\Services\Timeslot\TimeslotService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class TimeslotServiceTest extends TestCase
{
    /**
     * @var TimeslotRepositoryInterface|Mockery\MockInterface
     */
    protected $timeslotRepository;

    /**
     * @var TimeslotService
     */
    protected $timeslotService;

    /**
     * Set up the test.
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->timeslotRepository = Mockery::mock(TimeslotRepositoryInterface::class);
        $this->timeslotService = new TimeslotService($this->timeslotRepository);

        Event::fake();
    }

    /**
     * Test getting all timeslots.
     */
    public function testGetAllTimeslots(): void
    {
        // Arrange
        $filters = ['day' => 'Monday'];
        $timeslots = new Collection([
            new Timeslot(['id' => 1, 'starttime' => '08:00:00', 'endtime' => '10:00:00', 'day' => 'Monday']),
            new Timeslot(['id' => 2, 'starttime' => '12:00:00', 'endtime' => '14:00:00', 'day' => 'Monday']),
        ]);

        // Mock repository
        $this->timeslotRepository->shouldReceive('all')
            ->once()
            ->with($filters)
            ->andReturn($timeslots);

        // Act
        $result = $this->timeslotService->getAllTimeslots($filters);

        // Assert
        $this->assertSame($timeslots, $result);
        $this->assertCount(2, $result);
    }

    /**
     * Test getting paginated timeslots.
     */
    public function testGetPaginatedTimeslots(): void
    {
        // Arrange
        $perPage = 10;
        $filters = ['day' => 'Monday'];
        $timeslots = $this->createMock(\Illuminate\Pagination\LengthAwarePaginator::class);

        // Mock repository
        $this->timeslotRepository->shouldReceive('paginate')
            ->once()
            ->with($perPage, $filters)
            ->andReturn($timeslots);

        // Act
        $result = $this->timeslotService->getPaginatedTimeslots($perPage, $filters);

        // Assert
        $this->assertSame($timeslots, $result);
    }

    /**
     * Test getting timeslot by ID.
     */
    public function testGetTimeslotById(): void
    {
        // Arrange
        $id = 1;
        $timeslot = new Timeslot(['id' => $id, 'starttime' => '08:00:00', 'endtime' => '10:00:00', 'day' => 'Monday']);

        // Mock repository
        $this->timeslotRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($timeslot);

        // Act
        $result = $this->timeslotService->getTimeslotById($id);

        // Assert
        $this->assertSame($timeslot, $result);
    }

    /**
     * Test creating a timeslot.
     */
    public function testCreateTimeslot(): void
    {
        // Arrange
        $data = [
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main Kitchen',
            'status' => 1,
        ];

        $timeslot = new Timeslot($data);
        $timeslot->id = 1;

        // Mock repository
        $this->timeslotRepository->shouldReceive('create')
            ->once()
            ->with($data)
            ->andReturn($timeslot);

        // Act
        $result = $this->timeslotService->createTimeslot($data);

        // Assert
        $this->assertSame($timeslot, $result);
        Event::assertDispatched(TimeslotCreated::class, function ($event) use ($timeslot) {
            return $event->timeslot->id === $timeslot->id;
        });
    }

    /**
     * Test creating a timeslot with invalid time format.
     */
    public function testCreateTimeslotWithInvalidTimeFormat(): void
    {
        // Arrange
        $data = [
            'starttime' => 'invalid-time',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main Kitchen',
            'status' => 1,
        ];

        // Assert
        $this->expectException(TimeslotException::class);
        $this->expectExceptionMessage('Invalid time format. Use HH:MM:SS format.');

        // Act
        $this->timeslotService->createTimeslot($data);
    }

    /**
     * Test creating a timeslot with start time after end time.
     */
    public function testCreateTimeslotWithStartTimeAfterEndTime(): void
    {
        // Arrange
        $data = [
            'starttime' => '12:00:00',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main Kitchen',
            'status' => 1,
        ];

        // Assert
        $this->expectException(TimeslotException::class);
        $this->expectExceptionMessage('Start time must be before end time.');

        // Act
        $this->timeslotService->createTimeslot($data);
    }

    /**
     * Test updating a timeslot.
     */
    public function testUpdateTimeslot(): void
    {
        // Arrange
        $id = 1;
        $data = [
            'starttime' => '09:00:00',
            'endtime' => '11:00:00',
        ];

        $timeslot = new Timeslot([
            'id' => $id,
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main Kitchen',
            'status' => 1,
        ]);

        $updatedTimeslot = new Timeslot([
            'id' => $id,
            'starttime' => '09:00:00',
            'endtime' => '11:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main Kitchen',
            'status' => 1,
        ]);

        // Mock repository
        $this->timeslotRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($timeslot);

        $this->timeslotRepository->shouldReceive('update')
            ->once()
            ->with($id, $data)
            ->andReturn(true);

        $this->timeslotRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($updatedTimeslot);

        // Act
        $result = $this->timeslotService->updateTimeslot($id, $data);

        // Assert
        $this->assertTrue($result);
        Event::assertDispatched(TimeslotUpdated::class, function ($event) use ($updatedTimeslot) {
            return $event->timeslot->id === $updatedTimeslot->id;
        });
    }

    /**
     * Test updating a non-existent timeslot.
     */
    public function testUpdateNonExistentTimeslot(): void
    {
        // Arrange
        $id = 999;
        $data = [
            'starttime' => '09:00:00',
            'endtime' => '11:00:00',
        ];

        // Mock repository
        $this->timeslotRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Assert
        $this->expectException(TimeslotException::class);
        $this->expectExceptionMessage('Timeslot not found.');

        // Act
        $this->timeslotService->updateTimeslot($id, $data);
    }

    /**
     * Test deleting a timeslot.
     */
    public function testDeleteTimeslot(): void
    {
        // Arrange
        $id = 1;
        $timeslot = new Timeslot([
            'id' => $id,
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main Kitchen',
            'status' => 1,
        ]);

        // Mock repository
        $this->timeslotRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn($timeslot);

        $this->timeslotRepository->shouldReceive('delete')
            ->once()
            ->with($id)
            ->andReturn(true);

        // Act
        $result = $this->timeslotService->deleteTimeslot($id);

        // Assert
        $this->assertTrue($result);
        Event::assertDispatched(TimeslotDeleted::class, function ($event) use ($timeslot) {
            return $event->timeslot->id === $timeslot->id;
        });
    }

    /**
     * Test deleting a non-existent timeslot.
     */
    public function testDeleteNonExistentTimeslot(): void
    {
        // Arrange
        $id = 999;

        // Mock repository
        $this->timeslotRepository->shouldReceive('findById')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Assert
        $this->expectException(TimeslotException::class);
        $this->expectExceptionMessage('Timeslot not found.');

        // Act
        $this->timeslotService->deleteTimeslot($id);
    }

    /**
     * Test getting available timeslots.
     */
    public function testGetAvailableTimeslots(): void
    {
        // Arrange
        $day = 'Monday';
        $menuType = 'Breakfast';
        $kitchen = 'Main Kitchen';
        $orderDate = '2023-06-01';

        $timeslots = new Collection([
            new Timeslot(['id' => 1, 'starttime' => '08:00:00', 'endtime' => '10:00:00', 'day' => $day, 'menu_type' => $menuType, 'kitchen' => $kitchen]),
            new Timeslot(['id' => 2, 'starttime' => '12:00:00', 'endtime' => '14:00:00', 'day' => $day, 'menu_type' => $menuType, 'kitchen' => $kitchen]),
        ]);

        // Mock repository
        $this->timeslotRepository->shouldReceive('getAvailableTimeslots')
            ->once()
            ->with($day, $menuType, $kitchen, $orderDate)
            ->andReturn($timeslots);

        // Act
        $result = $this->timeslotService->getAvailableTimeslots($day, $menuType, $kitchen, $orderDate);

        // Assert
        $this->assertSame($timeslots, $result);
        $this->assertCount(2, $result);
    }
}

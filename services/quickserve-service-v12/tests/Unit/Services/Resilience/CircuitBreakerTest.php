<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services\Resilience;

use App\Services\Resilience\CircuitBreaker;
use Exception;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CircuitBreakerTest extends TestCase
{
    /**
     * Test that the circuit breaker executes the callback when closed.
     *
     * @return void
     */
    public function testExecutesCallbackWhenClosed(): void
    {
        $circuitBreaker = new CircuitBreaker('test-circuit');

        $result = $circuitBreaker->execute(function () {
            return 'success';
        });

        $this->assertEquals('success', $result);
    }

    /**
     * Test that the circuit breaker records failures.
     *
     * @return void
     */
    public function testRecordsFailures(): void
    {
        $circuitBreaker = new CircuitBreaker('test-circuit-failures', 3, 60);

        // Clear any existing cache entries
        Cache::forget('test-circuit-failures.failure_count');
        Cache::forget('test-circuit-failures.last_failure_time');

        // First failure
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        $this->assertEquals(1, Cache::get('test-circuit-failures.failure_count'));
        $this->assertNotNull(Cache::get('test-circuit-failures.last_failure_time'));

        // Second failure
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        $this->assertEquals(2, Cache::get('test-circuit-failures.failure_count'));
    }

    /**
     * Test that the circuit breaker opens after threshold failures.
     *
     * @return void
     */
    public function testOpensAfterThresholdFailures(): void
    {
        $circuitBreaker = new CircuitBreaker('test-circuit-open', 2, 60);

        // Clear any existing cache entries
        Cache::forget('test-circuit-open.failure_count');
        Cache::forget('test-circuit-open.last_failure_time');

        // First failure
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        // Second failure - should open the circuit
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        // Third attempt - circuit should be open
        try {
            $circuitBreaker->execute(function () {
                return 'success';
            });

            $this->fail('Circuit should be open');
        } catch (Exception $e) {
            $this->assertStringContainsString('Circuit breaker test-circuit-open is open', $e->getMessage());
        }
    }

    /**
     * Test that the circuit breaker uses the fallback when open.
     *
     * @return void
     */
    public function testUsesFallbackWhenOpen(): void
    {
        $circuitBreaker = new CircuitBreaker('test-circuit-fallback', 2, 60);

        // Clear any existing cache entries
        Cache::forget('test-circuit-fallback.failure_count');
        Cache::forget('test-circuit-fallback.last_failure_time');

        // First failure
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        // Second failure - should open the circuit
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        // Third attempt with fallback - circuit should be open but fallback used
        $result = $circuitBreaker->execute(
            function () {
                return 'success';
            },
            function () {
                return 'fallback';
            }
        );

        $this->assertEquals('fallback', $result);
    }

    /**
     * Test that the circuit breaker resets after timeout.
     *
     * @return void
     */
    public function testResetsAfterTimeout(): void
    {
        $circuitBreaker = new CircuitBreaker('test-circuit-reset', 2, 1); // 1 second timeout

        // Clear any existing cache entries
        Cache::forget('test-circuit-reset.failure_count');
        Cache::forget('test-circuit-reset.last_failure_time');

        // First failure
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        // Second failure - should open the circuit
        try {
            $circuitBreaker->execute(function () {
                throw new Exception('Test exception');
            });
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
        }

        // Wait for timeout
        sleep(2);

        // Circuit should be reset and allow execution
        $result = $circuitBreaker->execute(function () {
            return 'success';
        });

        $this->assertEquals('success', $result);
        $this->assertEquals(0, Cache::get('test-circuit-reset.failure_count'));
    }
}

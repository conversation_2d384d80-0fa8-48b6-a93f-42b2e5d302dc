<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services\Resilience;

use App\Services\Resilience\Retry;
use Exception;
use Tests\TestCase;

class RetryTest extends TestCase
{
    /**
     * Test that the retry mechanism executes the callback successfully.
     *
     * @return void
     */
    public function testExecutesCallbackSuccessfully(): void
    {
        $retry = new Retry('test-retry');

        $result = $retry->execute(function () {
            return 'success';
        });

        $this->assertEquals('success', $result);
    }

    /**
     * Test that the retry mechanism retries on failure.
     *
     * @return void
     */
    public function testRetriesOnFailure(): void
    {
        $retry = new Retry('test-retry-failure', 3, 10, 100);

        $attempts = 0;

        $result = $retry->execute(function () use (&$attempts) {
            $attempts++;

            if ($attempts < 3) {
                throw new Exception('Test exception');
            }

            return 'success after retry';
        });

        $this->assertEquals(3, $attempts);
        $this->assertEquals('success after retry', $result);
    }

    /**
     * Test that the retry mechanism gives up after max attempts.
     *
     * @return void
     */
    public function testGivesUpAfterMaxAttempts(): void
    {
        $retry = new Retry('test-retry-max-attempts', 3, 10, 100);

        $attempts = 0;

        try {
            $retry->execute(function () use (&$attempts) {
                $attempts++;
                throw new Exception('Test exception');
            });

            $this->fail('Should have thrown an exception');
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(3, $attempts);
        }
    }

    /**
     * Test that the retry mechanism respects the shouldRetry callback.
     *
     * @return void
     */
    public function testRespectsShouldRetryCallback(): void
    {
        $retry = new Retry('test-retry-should-retry', 3, 10, 100);

        $attempts = 0;

        try {
            $retry->execute(
                function () use (&$attempts) {
                    $attempts++;
                    throw new Exception('Test exception');
                },
                function (Exception $e) use (&$attempts) {
                    // Only retry once
                    return $attempts < 2;
                }
            );

            $this->fail('Should have thrown an exception');
        } catch (Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(2, $attempts); // Should only retry once
        }
    }

    /**
     * Test that the retry mechanism uses exponential backoff.
     *
     * @return void
     */
    public function testUsesExponentialBackoff(): void
    {
        $retry = new Retry('test-retry-backoff', 3, 100, 5000);

        $attempts = 0;
        $startTimes = [];

        try {
            $retry->execute(function () use (&$attempts, &$startTimes) {
                $startTimes[] = microtime(true);
                $attempts++;
                throw new Exception('Test exception');
            });

            $this->fail('Should have thrown an exception');
        } catch (Exception $e) {
            $this->assertEquals(3, $attempts);

            // Check that delays are increasing
            $delay1 = ($startTimes[1] - $startTimes[0]) * 1000; // Convert to ms
            $delay2 = ($startTimes[2] - $startTimes[1]) * 1000; // Convert to ms

            // Allow for some variance in timing
            $this->assertGreaterThan(50, $delay1); // At least 50ms for first retry
            $this->assertGreaterThan($delay1, $delay2); // Second delay should be longer than first
        }
    }
}

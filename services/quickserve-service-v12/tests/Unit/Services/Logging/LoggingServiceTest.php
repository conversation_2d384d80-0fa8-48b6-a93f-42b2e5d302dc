<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Services\Logging;

use App\Services\Logging\LoggingService;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use ReflectionClass;

class LoggingServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Reset the static correlation ID before each test
        $reflection = new ReflectionClass(LoggingService::class);
        $property = $reflection->getProperty('correlationId');
        $property->setAccessible(true);
        $property->setValue(null);
    }

    /**
     * Test that the logging service generates a correlation ID.
     *
     * @return void
     */
    public function testGeneratesCorrelationId(): void
    {
        $correlationId = LoggingService::getCorrelationId();

        $this->assertNotEmpty($correlationId);
        $this->assertEquals(36, strlen($correlationId)); // UUID length
    }

    /**
     * Test that the logging service uses a provided correlation ID.
     *
     * @return void
     */
    public function testUsesProvidedCorrelationId(): void
    {
        $correlationId = 'test-correlation-id';
        LoggingService::setCorrelationId($correlationId);

        $this->assertEquals($correlationId, LoggingService::getCorrelationId());
    }

    /**
     * Test that the logging service adds context to log messages.
     *
     * @return void
     */
    public function testAddsContextToLogMessages(): void
    {
        $correlationId = 'test-correlation-id';
        LoggingService::setCorrelationId($correlationId);

        Log::shouldReceive('info')
            ->once()
            ->with('Test message', \Mockery::on(function ($context) use ($correlationId) {
                return $context['correlation_id'] === $correlationId
                    && isset($context['service'])
                    && isset($context['service_version']);
            }));

        LoggingService::info('Test message');
    }

    /**
     * Test that the logging service logs at different levels.
     *
     * @return void
     */
    public function testLogsAtDifferentLevels(): void
    {
        $correlationId = 'test-correlation-id';
        LoggingService::setCorrelationId($correlationId);

        Log::shouldReceive('info')
            ->once()
            ->with('Info message', \Mockery::any());

        Log::shouldReceive('warning')
            ->once()
            ->with('Warning message', \Mockery::any());

        Log::shouldReceive('error')
            ->once()
            ->with('Error message', \Mockery::any());

        Log::shouldReceive('debug')
            ->once()
            ->with('Debug message', \Mockery::any());

        LoggingService::info('Info message');
        LoggingService::warning('Warning message');
        LoggingService::error('Error message');
        LoggingService::debug('Debug message');
    }

    /**
     * Test that the logging service adds custom context.
     *
     * @return void
     */
    public function testAddsCustomContext(): void
    {
        $correlationId = 'test-correlation-id';
        LoggingService::setCorrelationId($correlationId);

        $customContext = [
            'user_id' => 123,
            'action' => 'test',
        ];

        Log::shouldReceive('info')
            ->once()
            ->with('Test message', \Mockery::on(function ($context) use ($correlationId, $customContext) {
                return $context['correlation_id'] === $correlationId
                    && $context['user_id'] === $customContext['user_id']
                    && $context['action'] === $customContext['action'];
            }));

        LoggingService::info('Test message', $customContext);
    }
}

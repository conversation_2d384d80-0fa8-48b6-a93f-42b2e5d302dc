<?php

namespace Tests\Unit\Services;

use App\Services\OrderService;
use App\Models\Order;
use App\Models\Product;
use App\Models\Customer;
use App\Events\Order\OrderCreated;
use App\Events\Order\OrderUpdated;
use App\Events\Order\OrderCancelled;
use App\Exceptions\Order\OrderException;
use App\Services\Payment\PaymentServiceClient;
use App\Services\Customer\CustomerServiceClient;
use App\Repositories\OrderRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;
use Mockery;

class OrderServiceEnhancedTest extends TestCase
{
    use RefreshDatabase;

    protected $orderService;
    protected $mockOrderRepository;
    protected $mockCustomerService;
    protected $mockPaymentService;

    protected function setUp(): void
    {
        parent::setUp();

        Event::fake();

        $this->mockOrderRepository = Mockery::mock(OrderRepository::class);
        $this->mockCustomerService = Mockery::mock(CustomerServiceClient::class);
        $this->mockPaymentService = Mockery::mock(PaymentServiceClient::class);

        $this->orderService = new OrderService(
            $this->mockOrderRepository,
            $this->mockCustomerService,
            $this->mockPaymentService
        );
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    /** @test */
    public function it_can_get_all_orders()
    {
        // Arrange
        $expectedOrders = new \Illuminate\Database\Eloquent\Collection([
            Order::factory()->make(['id' => 1]),
            Order::factory()->make(['id' => 2])
        ]);

        $this->mockOrderRepository->shouldReceive('getAllOrders')
            ->once()
            ->with([])
            ->andReturn($expectedOrders);

        // Act
        $orders = $this->orderService->getAllOrders();

        // Assert
        $this->assertEquals($expectedOrders, $orders);
        $this->assertCount(2, $orders);
    }

    /** @test */
    public function it_can_get_orders_with_filters()
    {
        // Arrange
        $filters = ['status' => 'pending', 'customer_id' => 1];
        $expectedOrders = new \Illuminate\Database\Eloquent\Collection([Order::factory()->make()]);

        $this->mockOrderRepository->shouldReceive('getAllOrders')
            ->once()
            ->with($filters)
            ->andReturn($expectedOrders);

        // Act
        $orders = $this->orderService->getAllOrders($filters);

        // Assert
        $this->assertEquals($expectedOrders, $orders);
        $this->assertCount(1, $orders);
    }
    
    /** @test */
    public function it_can_get_order_by_id()
    {
        // Arrange
        $orderId = 1;
        $expectedOrder = Order::factory()->make(['pk_order_no' => $orderId]);

        $this->mockOrderRepository->shouldReceive('findById')
            ->once()
            ->with($orderId)
            ->andReturn($expectedOrder);

        // Act
        $order = $this->orderService->getOrderById($orderId);

        // Assert
        $this->assertEquals($expectedOrder, $order);
        $this->assertEquals($orderId, $order->id);
    }

    /** @test */
    public function it_can_get_orders_by_customer()
    {
        // Arrange
        $customerId = 1;
        $params = ['status' => 'pending'];
        $expectedOrders = new \Illuminate\Database\Eloquent\Collection([
            Order::factory()->make(['customer_code' => $customerId])
        ]);

        $this->mockOrderRepository->shouldReceive('getOrdersByCustomer')
            ->once()
            ->with($customerId, $params)
            ->andReturn($expectedOrders);

        // Act
        $orders = $this->orderService->getOrdersByCustomer($customerId, $params);

        // Assert
        $this->assertEquals($expectedOrders, $orders);
        $this->assertCount(1, $orders);
    }
    
    /** @test */
    public function it_can_update_order_status()
    {
        // Arrange
        $orderId = 1;
        $status = 'confirmed';
        $order = Order::factory()->make(['pk_order_no' => $orderId, 'order_status' => 'pending']);
        $updatedOrder = Order::factory()->make(['pk_order_no' => $orderId, 'order_status' => $status]);

        $this->mockOrderRepository->shouldReceive('findById')
            ->once()
            ->with($orderId)
            ->andReturn($order);

        $this->mockOrderRepository->shouldReceive('update')
            ->once()
            ->with($order, ['order_status' => $status])
            ->andReturn($updatedOrder);

        // Act
        $result = $this->orderService->updateOrderStatus($orderId, $status);

        // Assert
        $this->assertEquals($updatedOrder, $result);
        $this->assertEquals($status, $result->order_status);
    }

    /** @test */
    public function it_can_update_delivery_status()
    {
        // Arrange
        $orderId = 1;
        $status = 'delivered';
        $order = Order::factory()->make(['pk_order_no' => $orderId, 'delivery_status' => 'pending']);
        $updatedOrder = Order::factory()->make(['pk_order_no' => $orderId, 'delivery_status' => $status]);

        $this->mockOrderRepository->shouldReceive('findById')
            ->once()
            ->with($orderId)
            ->andReturn($order);

        $this->mockOrderRepository->shouldReceive('update')
            ->once()
            ->with($order, ['delivery_status' => $status])
            ->andReturn($updatedOrder);

        // Act
        $result = $this->orderService->updateDeliveryStatus($orderId, $status);

        // Assert
        $this->assertEquals($updatedOrder, $result);
        $this->assertEquals($status, $result->delivery_status);
    }

    /** @test */
    public function it_returns_null_when_order_not_found_for_status_update()
    {
        // Arrange
        $orderId = 999;
        $status = 'confirmed';

        $this->mockOrderRepository->shouldReceive('findById')
            ->once()
            ->with($orderId)
            ->andReturn(null);

        // Act
        $result = $this->orderService->updateOrderStatus($orderId, $status);

        // Assert
        $this->assertNull($result);
    }
    
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit\Http\Middleware;

use App\Http\Middleware\CorrelationIdMiddleware;
use App\Services\Logging\LoggingService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Tests\TestCase;

class CorrelationIdMiddlewareTest extends TestCase
{
    /**
     * Test that the middleware adds a correlation ID to the response when none is provided.
     *
     * @return void
     */
    public function testAddsCorrelationIdToResponse(): void
    {
        $request = new Request();
        $middleware = new CorrelationIdMiddleware();

        $response = $middleware->handle($request, function ($request) {
            $this->assertNotNull($request->header('X-Correlation-ID'));
            return new Response();
        });

        $this->assertTrue($response->headers->has('X-Correlation-ID'));
        $this->assertNotEmpty($response->headers->get('X-Correlation-ID'));
    }

    /**
     * Test that the middleware preserves an existing correlation ID.
     *
     * @return void
     */
    public function testPreservesExistingCorrelationId(): void
    {
        $correlationId = 'test-correlation-id';
        $request = new Request();
        $request->headers->set('X-Correlation-ID', $correlationId);

        $middleware = new CorrelationIdMiddleware();

        $response = $middleware->handle($request, function ($request) use ($correlationId) {
            $this->assertEquals($correlationId, $request->header('X-Correlation-ID'));
            return new Response();
        });

        $this->assertEquals($correlationId, $response->headers->get('X-Correlation-ID'));
    }

    /**
     * Test that the middleware sets the correlation ID in the LoggingService.
     *
     * @return void
     */
    public function testSetsCorrelationIdInLoggingService(): void
    {
        $correlationId = 'test-correlation-id';
        $request = new Request();
        $request->headers->set('X-Correlation-ID', $correlationId);

        $middleware = new CorrelationIdMiddleware();

        $middleware->handle($request, function ($request) use ($correlationId) {
            $this->assertEquals($correlationId, LoggingService::getCorrelationId());
            return new Response();
        });
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Unit;

use App\Models\Order;
use App\Repositories\OrderRepository;
use App\Services\OrderService;
use App\Services\Customer\CustomerServiceClient;
use App\Services\Payment\PaymentServiceClient;
use App\Exceptions\Order\OrderException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;
use Mockery;

class OrderServiceTest extends TestCase
{

    protected $orderRepositoryMock;
    protected $customerServiceMock;
    protected $paymentServiceMock;
    protected $orderService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock DB facade
        DB::shouldReceive('beginTransaction')->andReturn(true);
        DB::shouldReceive('commit')->andReturn(true);
        DB::shouldReceive('rollBack')->andReturn(true);

        // Mock Event facade
        Event::shouldReceive('dispatch')->andReturn(true);

        $this->orderRepositoryMock = Mockery::mock(OrderRepository::class);
        $this->customerServiceMock = Mockery::mock(CustomerServiceClient::class);
        $this->paymentServiceMock = Mockery::mock(PaymentServiceClient::class);
        $this->orderService = new OrderService(
            $this->orderRepositoryMock,
            $this->customerServiceMock,
            $this->paymentServiceMock
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGetAllOrders()
    {
        $orders = [
            new Order(['order_no' => 'ORD123', 'customer_name' => 'John Doe']),
            new Order(['order_no' => 'ORD456', 'customer_name' => 'Jane Smith']),
        ];

        $this->orderRepositoryMock->shouldReceive('getAllOrders')
            ->once()
            ->with([])
            ->andReturn(new Collection($orders));

        $result = $this->orderService->getAllOrders();

        $this->assertCount(2, $result);
        $this->assertEquals('ORD123', $result[0]->order_no);
        $this->assertEquals('ORD456', $result[1]->order_no);
    }

    public function testGetOrderById()
    {
        $order = new Order(['order_no' => 'ORD123', 'customer_name' => 'John Doe']);

        $this->orderRepositoryMock->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $result = $this->orderService->getOrderById(1);

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('ORD123', $result->order_no);
    }

    public function testCreateOrder()
    {
        $orderData = [
            'customer_code' => 1,
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'product_code' => 1,
            'product_name' => 'Vegetable Meal',
            'product_type' => 'Meal',
            'quantity' => 1,
            'amount' => 100.00,
            'order_date' => '2023-05-17',
            'ship_address' => '123 Main St',
            'order_menu' => 'Lunch',
        ];

        $order = new Order($orderData);
        $order->order_no = 'ORD20230517120000';
        $order->order_status = 'New';
        $order->delivery_status = 'Pending';
        $order->invoice_status = 'Unbill';
        $order->company_id = 1;
        $order->unit_id = 1;
        $order->id = 1;

        // Mock customer service
        $customer = [
            'customer_code' => 1,
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ];

        $this->customerServiceMock->shouldReceive('getCustomer')
            ->once()
            ->with(1)
            ->andReturn($customer);

        $this->orderRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturn($order);

        $result = $this->orderService->createOrder($orderData);

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('ORD20230517120000', $result->order_no);
        $this->assertEquals('New', $result->order_status);
        $this->assertEquals('Pending', $result->delivery_status);
        $this->assertEquals('Unbill', $result->invoice_status);
    }

    public function testUpdateOrder()
    {
        $order = new Order([
            'order_no' => 'ORD123',
            'customer_name' => 'John Doe',
            'order_status' => 'New',
            'company_id' => 1,
            'unit_id' => 1,
        ]);
        $order->id = 1;

        $updateData = [
            'customer_name' => 'John Smith',
            'order_status' => 'Processing',
        ];

        $updatedOrder = new Order([
            'order_no' => 'ORD123',
            'customer_name' => 'John Smith',
            'order_status' => 'Processing',
            'company_id' => 1,
            'unit_id' => 1,
        ]);
        $updatedOrder->id = 1;

        $this->orderRepositoryMock->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $this->orderRepositoryMock->shouldReceive('update')
            ->once()
            ->with($order, Mockery::on(function ($data) {
                // The service adds additional fields like created_date, so we need to be flexible
                return isset($data['customer_name']) && isset($data['order_status']);
            }))
            ->andReturn($updatedOrder);

        $result = $this->orderService->updateOrder(1, $updateData);

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('John Smith', $result->customer_name);
        $this->assertEquals('Processing', $result->order_status);
    }

    public function testUpdateOrderNotFound()
    {
        $this->orderRepositoryMock->shouldReceive('findById')
            ->once()
            ->with(999)
            ->andReturn(null);

        $this->expectException(OrderException::class);
        $this->expectExceptionMessage('Failed to update order: Order not found');

        $this->orderService->updateOrder(999, ['customer_name' => 'John Smith']);
    }

    public function testDeleteOrder()
    {
        $order = new Order([
            'order_no' => 'ORD123',
            'customer_name' => 'John Doe',
            'order_status' => 'New', // Status that allows deletion
        ]);

        $this->orderRepositoryMock->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $this->orderRepositoryMock->shouldReceive('delete')
            ->once()
            ->with(1)
            ->andReturn(true);

        $result = $this->orderService->deleteOrder(1);

        $this->assertTrue($result);
    }

    public function testUpdateOrderStatus()
    {
        $order = new Order([
            'order_no' => 'ORD123',
            'customer_name' => 'John Doe',
            'order_status' => 'New',
        ]);

        $updatedOrder = new Order([
            'order_no' => 'ORD123',
            'customer_name' => 'John Doe',
            'order_status' => 'Processing',
        ]);

        $this->orderRepositoryMock->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $this->orderRepositoryMock->shouldReceive('update')
            ->once()
            ->with($order, ['order_status' => 'Processing'])
            ->andReturn($updatedOrder);

        $result = $this->orderService->updateOrderStatus(1, 'Processing');

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('Processing', $result->order_status);
    }

    public function testUpdateDeliveryStatus()
    {
        $order = new Order([
            'order_no' => 'ORD123',
            'customer_name' => 'John Doe',
            'delivery_status' => 'Pending',
        ]);

        $updatedOrder = new Order([
            'order_no' => 'ORD123',
            'customer_name' => 'John Doe',
            'delivery_status' => 'Dispatched',
        ]);

        $this->orderRepositoryMock->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $this->orderRepositoryMock->shouldReceive('update')
            ->once()
            ->with($order, ['delivery_status' => 'Dispatched'])
            ->andReturn($updatedOrder);

        $result = $this->orderService->updateDeliveryStatus(1, 'Dispatched');

        $this->assertInstanceOf(Order::class, $result);
        $this->assertEquals('Dispatched', $result->delivery_status);
    }
}

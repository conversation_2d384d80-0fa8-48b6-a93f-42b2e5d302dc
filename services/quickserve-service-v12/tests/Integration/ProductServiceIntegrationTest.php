<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Integration;

use App\Models\Product;
use App\Repositories\Product\ProductRepository;
use App\Services\Product\ProductService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProductServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $productService;
    protected $productRepository;

    public function setUp(): void
    {
        parent::setUp();

        // Create real repository
        $this->productRepository = new ProductRepository(new Product());

        // Create the service with real repository
        $this->productService = new ProductService($this->productRepository);

        // Disable event dispatching for tests
        Event::fake();

        // Set up storage for image uploads
        Storage::fake('public');
    }

    public function testCreateAndRetrieveProduct()
    {
        // Create product data
        $productData = [
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100.00,
            'product_type' => 'Meal',
            'food_type' => 'veg',
            'product_category' => 'Breakfast',
            'kitchen_code' => 1,
            'company_id' => 1,
            'unit_id' => 1,
        ];

        // Create fake image
        $image = UploadedFile::fake()->image('product.jpg');

        // Create the product
        $product = $this->productService->createProduct($productData, $image);

        // Assert product was created
        $this->assertNotNull($product);
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals('Test Product', $product->name);
        $this->assertEquals('Test Description', $product->description);
        $this->assertEquals(100.00, $product->unit_price);
        $this->assertEquals('Meal', $product->product_type);
        $this->assertEquals('veg', $product->food_type);
        $this->assertEquals('Breakfast', $product->product_category);
        $this->assertEquals(1, $product->kitchen_code);
        $this->assertEquals(1, $product->company_id);
        $this->assertEquals(1, $product->unit_id);
        $this->assertNotNull($product->image_path);
        $this->assertTrue(Storage::disk('public')->exists($product->image_path));

        // Retrieve the product
        $retrievedProduct = $this->productService->getProductById($product->pk_product_code);

        // Assert retrieved product matches created product
        $this->assertNotNull($retrievedProduct);
        $this->assertEquals($product->pk_product_code, $retrievedProduct->pk_product_code);
        $this->assertEquals($product->name, $retrievedProduct->name);
        $this->assertEquals($product->description, $retrievedProduct->description);
        $this->assertEquals($product->unit_price, $retrievedProduct->unit_price);
        $this->assertEquals($product->product_type, $retrievedProduct->product_type);
        $this->assertEquals($product->food_type, $retrievedProduct->food_type);
        $this->assertEquals($product->product_category, $retrievedProduct->product_category);
        $this->assertEquals($product->kitchen_code, $retrievedProduct->kitchen_code);
        $this->assertEquals($product->company_id, $retrievedProduct->company_id);
        $this->assertEquals($product->unit_id, $retrievedProduct->unit_id);
        $this->assertEquals($product->image_path, $retrievedProduct->image_path);
    }

    public function testUpdateProduct()
    {
        // Create a product
        $product = Product::factory()->create([
            'name' => 'Original Product',
            'description' => 'Original Description',
            'unit_price' => 100.00,
            'product_type' => 'Meal',
            'food_type' => 'veg',
            'product_category' => 'Breakfast',
            'kitchen_code' => 1,
            'company_id' => 1,
            'unit_id' => 1,
        ]);

        // Update data
        $updateData = [
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'unit_price' => 150.00,
        ];

        // Create fake image for update
        $image = UploadedFile::fake()->image('updated-product.jpg');

        // Update the product
        $updated = $this->productService->updateProduct($product->pk_product_code, $updateData, $image);

        // Assert product was updated
        $this->assertTrue($updated);

        // Retrieve the updated product
        $retrievedProduct = $this->productService->getProductById($product->pk_product_code);

        // Assert product was updated correctly
        $this->assertEquals('Updated Product', $retrievedProduct->name);
        $this->assertEquals('Updated Description', $retrievedProduct->description);
        $this->assertEquals(150.00, $retrievedProduct->unit_price);
        $this->assertEquals('Meal', $retrievedProduct->product_type); // Unchanged
        $this->assertEquals('veg', $retrievedProduct->food_type); // Unchanged
        $this->assertNotNull($retrievedProduct->image_path);
        $this->assertTrue(Storage::disk('public')->exists($retrievedProduct->image_path));
    }

    public function testDeleteProduct()
    {
        // Create a product
        $product = Product::factory()->create();

        // Delete the product
        $deleted = $this->productService->deleteProduct($product->pk_product_code);

        // Assert product was deleted
        $this->assertTrue($deleted);

        // Try to retrieve the deleted product
        $retrievedProduct = $this->productService->getProductById($product->pk_product_code);

        // Assert product was deleted
        $this->assertNull($retrievedProduct);
    }

    public function testGetProductsByType()
    {
        // Create products with different types
        $product1 = Product::factory()->create(['product_type' => 'Meal']);
        $product2 = Product::factory()->create(['product_type' => 'Meal']);
        $product3 = Product::factory()->create(['product_type' => 'Snack']);

        // Get products by type
        $mealProducts = $this->productService->getProductsByType('Meal');

        // Assert correct products were retrieved
        $this->assertCount(2, $mealProducts);
        $this->assertTrue($mealProducts->contains('pk_product_code', $product1->pk_product_code));
        $this->assertTrue($mealProducts->contains('pk_product_code', $product2->pk_product_code));
        $this->assertFalse($mealProducts->contains('pk_product_code', $product3->pk_product_code));
    }

    public function testGetProductsByFoodType()
    {
        // Create products with different food types
        $product1 = Product::factory()->create(['food_type' => 'veg']);
        $product2 = Product::factory()->create(['food_type' => 'veg']);
        $product3 = Product::factory()->create(['food_type' => 'non-veg']);

        // Get products by food type
        $vegProducts = $this->productService->getProductsByFoodType('veg');

        // Assert correct products were retrieved
        $this->assertCount(2, $vegProducts);
        $this->assertTrue($vegProducts->contains('pk_product_code', $product1->pk_product_code));
        $this->assertTrue($vegProducts->contains('pk_product_code', $product2->pk_product_code));
        $this->assertFalse($vegProducts->contains('pk_product_code', $product3->pk_product_code));
    }

    public function testGetProductsByCategory()
    {
        // Create products with different categories
        $product1 = Product::factory()->create(['product_category' => 'Breakfast']);
        $product2 = Product::factory()->create(['product_category' => 'Breakfast']);
        $product3 = Product::factory()->create(['product_category' => 'Lunch']);

        // Get products by category
        $breakfastProducts = $this->productService->getProductsByCategory('Breakfast');

        // Assert correct products were retrieved
        $this->assertCount(2, $breakfastProducts);
        $this->assertTrue($breakfastProducts->contains('pk_product_code', $product1->pk_product_code));
        $this->assertTrue($breakfastProducts->contains('pk_product_code', $product2->pk_product_code));
        $this->assertFalse($breakfastProducts->contains('pk_product_code', $product3->pk_product_code));
    }

    public function testGetActiveProducts()
    {
        // Create active and inactive products
        $product1 = Product::factory()->create(['status' => true]);
        $product2 = Product::factory()->create(['status' => true]);
        $product3 = Product::factory()->create(['status' => false]);

        // Get active products
        $activeProducts = $this->productService->getActiveProducts();

        // Assert correct products were retrieved
        $this->assertCount(2, $activeProducts);
        $this->assertTrue($activeProducts->contains('pk_product_code', $product1->pk_product_code));
        $this->assertTrue($activeProducts->contains('pk_product_code', $product2->pk_product_code));
        $this->assertFalse($activeProducts->contains('pk_product_code', $product3->pk_product_code));
    }

    public function testUpdateProductSequence()
    {
        // Create products
        $product1 = Product::factory()->create(['sequence' => 1]);
        $product2 = Product::factory()->create(['sequence' => 2]);
        $product3 = Product::factory()->create(['sequence' => 3]);

        // Update sequence
        $productIds = [$product3->pk_product_code, $product1->pk_product_code, $product2->pk_product_code];
        $updated = $this->productService->updateProductSequence($productIds);

        // Assert sequence was updated
        $this->assertTrue($updated);

        // Retrieve products to check sequence
        $updatedProduct1 = $this->productService->getProductById($product1->pk_product_code);
        $updatedProduct2 = $this->productService->getProductById($product2->pk_product_code);
        $updatedProduct3 = $this->productService->getProductById($product3->pk_product_code);

        // Assert sequence was updated correctly
        $this->assertEquals(2, $updatedProduct1->sequence);
        $this->assertEquals(3, $updatedProduct2->sequence);
        $this->assertEquals(1, $updatedProduct3->sequence);
    }
}

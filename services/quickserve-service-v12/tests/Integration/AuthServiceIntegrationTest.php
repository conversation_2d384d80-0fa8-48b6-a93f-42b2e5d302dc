<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Integration;

use App\Services\Auth\AuthServiceClient;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class AuthServiceIntegrationTest extends TestCase
{
    /**
     * Test token validation.
     *
     * @return void
     */
    public function testValidateToken(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/validate-token' => Http::response(['valid' => true], 200),
        ]);

        $authService = new AuthServiceClient('http://auth-service-test:8000/api');
        $result = $authService->validateToken('test-token');

        $this->assertTrue($result);
        Http::assertSent(function ($request) {
            return $request->url() === 'http://auth-service-test:8000/api/validate-token' &&
                   $request->method() === 'POST' &&
                   $request->hasHeader('Authorization', 'Bearer test-token');
        });
    }

    /**
     * Test token validation with invalid token.
     *
     * @return void
     */
    public function testValidateTokenWithInvalidToken(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/validate-token' => Http::response(['valid' => false], 200),
        ]);

        $authService = new AuthServiceClient('http://auth-service-test:8000/api');
        $result = $authService->validateToken('invalid-token');

        $this->assertFalse($result);
    }

    /**
     * Test get user from token.
     *
     * @return void
     */
    public function testGetUserFromToken(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/user' => Http::response([
                'data' => [
                    'id' => 1,
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'roles' => ['admin'],
                ],
            ], 200),
        ]);

        $authService = new AuthServiceClient('http://auth-service-test:8000/api');
        $user = $authService->getUserFromToken('test-token');

        $this->assertNotNull($user);
        $this->assertEquals(1, $user['id']);
        $this->assertEquals('Test User', $user['name']);
        $this->assertEquals('<EMAIL>', $user['email']);
        $this->assertEquals(['admin'], $user['roles']);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://auth-service-test:8000/api/user' &&
                   $request->method() === 'GET' &&
                   $request->hasHeader('Authorization', 'Bearer test-token');
        });
    }

    /**
     * Test has role.
     *
     * @return void
     */
    public function testHasRole(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/user' => Http::response([
                'data' => [
                    'id' => 1,
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'roles' => ['admin', 'user'],
                ],
            ], 200),
        ]);

        $authService = new AuthServiceClient('http://auth-service-test:8000/api');
        $hasRole = $authService->hasRole('test-token', 'admin');

        $this->assertTrue($hasRole);

        $hasRole = $authService->hasRole('test-token', 'manager');

        $this->assertFalse($hasRole);
    }
}

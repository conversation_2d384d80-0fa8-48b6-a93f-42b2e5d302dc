<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Integration\Services;

use App\DTOs\Payment\PaymentResponseDTO;
use App\Events\OrderCreated;
use App\Exceptions\Customer\CustomerException;
use App\Exceptions\Order\OrderException;
use App\Exceptions\Payment\PaymentException;
use App\Models\Order;
use App\Repositories\OrderRepository;
use App\Services\Customer\CustomerServiceClient;
use App\Services\OrderService;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class OrderServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var OrderService
     */
    protected $orderService;

    /**
     * @var OrderRepository
     */
    protected $orderRepository;

    /**
     * @var CustomerServiceClient
     */
    protected $customerService;

    /**
     * @var PaymentServiceClient
     */
    protected $paymentService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = new OrderRepository();
        $this->customerService = new CustomerServiceClient();
        $this->paymentService = new PaymentServiceClient();

        $this->orderService = new OrderService(
            $this->orderRepository,
            $this->customerService,
            $this->paymentService
        );

        // Configure the base URLs for the services
        config(['services.customer.url' => 'http://customer-service']);
        config(['services.payment.url' => 'http://payment-service']);
        config(['services.customer.token' => 'test-token']);
        config(['services.payment.token' => 'test-token']);
    }

    /**
     * Test creating an order with integration to Customer service.
     *
     * @return void
     */
    public function testCreateOrderWithCustomerIntegration(): void
    {
        Event::fake();

        // Mock the HTTP client for the Customer service
        Http::fake([
            'http://customer-service/api/v2/customer/CUST123' => Http::response([
                'success' => true,
                'data' => [
                    'customer_code' => 'CUST123',
                    'customer_name' => 'Test Customer',
                    'email_address' => '<EMAIL>',
                    'phone' => '1234567890',
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'country' => 'Test Country',
                    'pincode' => '123456',
                    'wallet_balance' => 100.00,
                ],
            ], 200),
        ]);

        $orderData = [
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'items' => [
                [
                    'product_code' => 'PROD123',
                    'product_name' => 'Test Product',
                    'quantity' => 2,
                    'amount' => 50.00,
                    'tax' => 5.00,
                ],
            ],
        ];

        $order = $this->orderService->createOrder($orderData);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals('CUST123', $order->customer_code);
        $this->assertEquals(100.00, $order->amount);
        $this->assertEquals('New', $order->order_status);
        $this->assertEquals('Pending', $order->delivery_status);
        $this->assertEquals('Unbill', $order->invoice_status);

        Event::assertDispatched(OrderCreated::class, function ($event) use ($order) {
            return $event->order->id === $order->id;
        });

        // Verify the HTTP request was made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/api/v2/customer/CUST123'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });
    }

    /**
     * Test creating an order with invalid customer.
     *
     * @return void
     */
    public function testCreateOrderWithInvalidCustomer(): void
    {
        // Mock the HTTP client for the Customer service
        Http::fake([
            'http://customer-service/api/v2/customer/INVALID' => Http::response([
                'success' => false,
                'message' => 'Customer not found',
            ], 404),
        ]);

        $orderData = [
            'customer_code' => 'INVALID',
            'company_id' => 1,
            'unit_id' => 1,
            'amount' => 100.00,
        ];

        $this->expectException(CustomerException::class);
        $this->expectExceptionMessage('Customer not found');

        $this->orderService->createOrder($orderData);

        // Verify the HTTP request was made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/api/v2/customer/INVALID'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });
    }

    /**
     * Test processing payment for an order with integration to Payment service.
     *
     * @return void
     */
    public function testProcessPaymentWithPaymentIntegration(): void
    {
        // Create an order
        $order = Order::factory()->create([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
        ]);

        // Mock the HTTP client for the Customer service
        Http::fake([
            'http://customer-service/api/v2/customer/CUST123' => Http::response([
                'success' => true,
                'data' => [
                    'customer_code' => 'CUST123',
                    'customer_name' => 'Test Customer',
                    'email_address' => '<EMAIL>',
                    'phone' => '1234567890',
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'country' => 'Test Country',
                    'pincode' => '123456',
                    'wallet_balance' => 100.00,
                ],
            ], 200),
            'http://payment-service/api/v2/payment/initiate' => Http::response([
                'success' => true,
                'data' => [
                    'transaction_id' => 'txn_123456',
                    'amount' => 100.00,
                    'status' => 'pending',
                    'payment_url' => 'https://example.com/payment',
                ],
            ], 200),
        ]);

        $paymentResponse = $this->orderService->processPayment(1, 'stripe', 0);

        $this->assertInstanceOf(PaymentResponseDTO::class, $paymentResponse);
        $this->assertEquals('txn_123456', $paymentResponse->transactionId);
        $this->assertEquals(100.00, $paymentResponse->amount);
        $this->assertEquals('pending', $paymentResponse->status);
        $this->assertEquals('https://example.com/payment', $paymentResponse->paymentUrl);

        // Verify the HTTP requests were made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/api/v2/customer/CUST123'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });

        Http::assertSent(function ($request) {
            return $request->url() === 'http://payment-service/api/v2/payment/initiate'
                && $request->hasHeader('Authorization', 'Bearer test-token')
                && $request['order_no'] === 'ORD123';
        });

        // Verify the order was updated
        $updatedOrder = Order::find(1);
        $this->assertEquals('txn_123456', $updatedOrder->transaction_id);
        $this->assertEquals('stripe', $updatedOrder->payment_mode);
    }

    /**
     * Test processing payment with wallet integration.
     *
     * @return void
     */
    public function testProcessPaymentWithWalletIntegration(): void
    {
        // Create an order
        $order = Order::factory()->create([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
        ]);

        // Mock the HTTP client for the Customer service
        Http::fake([
            'http://customer-service/api/v2/customer/CUST123' => Http::response([
                'success' => true,
                'data' => [
                    'customer_code' => 'CUST123',
                    'customer_name' => 'Test Customer',
                    'email_address' => '<EMAIL>',
                    'phone' => '1234567890',
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'country' => 'Test Country',
                    'pincode' => '123456',
                    'wallet_balance' => 200.00,
                ],
            ], 200),
            'http://customer-service/api/v2/customer/CUST123/wallet' => Http::response([
                'success' => true,
                'data' => [
                    'balance' => 200.00,
                ],
            ], 200),
            'http://customer-service/api/v2/customer/CUST123/wallet/deduct' => Http::response([
                'success' => true,
                'data' => [
                    'balance' => 83.00,
                ],
            ], 200),
        ]);

        $paymentResponse = $this->orderService->processPayment(1, 'wallet', 117.00);

        $this->assertInstanceOf(PaymentResponseDTO::class, $paymentResponse);
        $this->assertStringStartsWith('wallet_', $paymentResponse->transactionId);
        $this->assertEquals(117.00, $paymentResponse->amount);
        $this->assertEquals('completed', $paymentResponse->status);

        // Verify the HTTP requests were made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/api/v2/customer/CUST123'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/api/v2/customer/CUST123/wallet'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/api/v2/customer/CUST123/wallet/deduct'
                && $request->hasHeader('Authorization', 'Bearer test-token')
                && $request['amount'] === 117.00;
        });

        // Verify the order was updated
        $updatedOrder = Order::find(1);
        $this->assertEquals('wallet', $updatedOrder->payment_mode);
        $this->assertEquals(117.00, $updatedOrder->amount_paid);
        $this->assertEquals('Paid', $updatedOrder->order_status);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Integration;

use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Repositories\Order\OrderRepository;
use App\Services\Customer\CustomerServiceInterface;
use App\Services\Order\OrderService;
use App\Services\Payment\PaymentServiceInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class OrderServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $orderService;
    protected $orderRepository;
    protected $customerService;
    protected $paymentService;

    public function setUp(): void
    {
        parent::setUp();

        // Create real repositories and mock external services
        $this->orderRepository = new OrderRepository(new Order());
        $this->customerService = $this->mock(CustomerServiceInterface::class);
        $this->paymentService = $this->mock(PaymentServiceInterface::class);

        // Create the service with real repository and mocked external services
        $this->orderService = new OrderService(
            $this->orderRepository,
            $this->customerService,
            $this->paymentService
        );

        // Disable event dispatching for tests
        Event::fake();
    }

    public function testCreateAndRetrieveOrder()
    {
        // Create a customer
        $customer = Customer::factory()->create();

        // Create a product
        $product = Product::factory()->create();

        // Mock customer service to return the customer
        $this->customerService->shouldReceive('getCustomerById')
            ->once()
            ->with($customer->pk_customer_code)
            ->andReturn($customer);

        // Create order data
        $orderData = [
            'customer_code' => $customer->pk_customer_code,
            'customer_name' => $customer->customer_name,
            'phone' => $customer->phone,
            'email_address' => $customer->email_address,
            'location_code' => $customer->location_code,
            'location_name' => $customer->location_name,
            'city' => $customer->city,
            'city_name' => $customer->city_name,
            'product_code' => $product->pk_product_code,
            'product_name' => $product->name,
            'product_description' => $product->description,
            'product_type' => $product->product_type,
            'quantity' => 2,
            'amount' => $product->unit_price * 2,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'order_menu' => 'Lunch',
            'food_type' => 'veg',
            'payment_mode' => 'Cash',
            'company_id' => 1,
            'unit_id' => 1,
            'items' => [
                [
                    'product_code' => $product->pk_product_code,
                    'product_name' => $product->name,
                    'quantity' => 2,
                    'amount' => $product->unit_price * 2,
                    'tax' => 10.00,
                ]
            ]
        ];

        // Create the order
        $order = $this->orderService->createOrder($orderData);

        // Assert order was created
        $this->assertNotNull($order);
        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals($customer->pk_customer_code, $order->customer_code);
        $this->assertEquals($customer->customer_name, $order->customer_name);
        $this->assertEquals($product->name, $order->product_name);
        $this->assertEquals(2, $order->quantity);
        $this->assertEquals($product->unit_price * 2, $order->amount);

        // Retrieve the order
        $retrievedOrder = $this->orderService->getOrderById($order->pk_order_no);

        // Assert retrieved order matches created order
        $this->assertNotNull($retrievedOrder);
        $this->assertEquals($order->pk_order_no, $retrievedOrder->pk_order_no);
        $this->assertEquals($order->order_no, $retrievedOrder->order_no);
        $this->assertEquals($order->customer_code, $retrievedOrder->customer_code);
        $this->assertEquals($order->product_name, $retrievedOrder->product_name);
        $this->assertEquals($order->amount, $retrievedOrder->amount);

        // Check order details were created
        $orderDetails = OrderDetail::where('ref_order_no', $order->order_no)->get();
        $this->assertCount(1, $orderDetails);
        $this->assertEquals($product->pk_product_code, $orderDetails[0]->product_code);
        $this->assertEquals($product->name, $orderDetails[0]->product_name);
        $this->assertEquals(2, $orderDetails[0]->quantity);
        $this->assertEquals($product->unit_price * 2, $orderDetails[0]->amount);
    }

    public function testUpdateOrderStatus()
    {
        // Create a customer
        $customer = Customer::factory()->create();

        // Create a product
        $product = Product::factory()->create();

        // Create an order
        $order = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'New',
        ]);

        // Update order status
        $updated = $this->orderService->updateOrderStatus($order->pk_order_no, 'Processing');

        // Assert status was updated
        $this->assertTrue($updated);

        // Retrieve the order
        $retrievedOrder = $this->orderService->getOrderById($order->pk_order_no);

        // Assert status was updated
        $this->assertEquals('Processing', $retrievedOrder->order_status);
    }

    public function testCancelOrder()
    {
        // Create a customer
        $customer = Customer::factory()->create();

        // Create a product
        $product = Product::factory()->create();

        // Create an order
        $order = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'New',
        ]);

        // Cancel the order
        $reason = 'Customer request';
        $cancelled = $this->orderService->cancelOrder($order->pk_order_no, $reason);

        // Assert order was cancelled
        $this->assertTrue($cancelled);

        // Retrieve the order
        $retrievedOrder = $this->orderService->getOrderById($order->pk_order_no);

        // Assert order status and reason
        $this->assertEquals('Cancel', $retrievedOrder->order_status);
        $this->assertEquals($reason, $retrievedOrder->remark);
    }

    public function testGetOrdersByCustomerId()
    {
        // Create a customer
        $customer = Customer::factory()->create();

        // Create a product
        $product = Product::factory()->create();

        // Create multiple orders for the customer
        $order1 = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'New',
        ]);

        $order2 = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'Processing',
        ]);

        $order3 = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'Completed',
        ]);

        // Get orders by customer ID
        $orders = $this->orderService->getOrdersByCustomerId($customer->pk_customer_code);

        // Assert correct orders were retrieved
        $this->assertCount(3, $orders);
        $this->assertTrue($orders->contains('pk_order_no', $order1->pk_order_no));
        $this->assertTrue($orders->contains('pk_order_no', $order2->pk_order_no));
        $this->assertTrue($orders->contains('pk_order_no', $order3->pk_order_no));

        // Get orders by customer ID with status filter
        $newOrders = $this->orderService->getOrdersByCustomerId($customer->pk_customer_code, ['order_status' => 'New']);

        // Assert correct orders were retrieved
        $this->assertCount(1, $newOrders);
        $this->assertTrue($newOrders->contains('pk_order_no', $order1->pk_order_no));
    }

    public function testGetOrdersByStatus()
    {
        // Create a customer
        $customer = Customer::factory()->create();

        // Create a product
        $product = Product::factory()->create();

        // Create orders with different statuses
        $order1 = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'New',
        ]);

        $order2 = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'Processing',
        ]);

        $order3 = Order::factory()->create([
            'customer_code' => $customer->pk_customer_code,
            'product_code' => $product->pk_product_code,
            'order_status' => 'New',
        ]);

        // Get orders by status
        $newOrders = $this->orderService->getOrdersByStatus('New');

        // Assert correct orders were retrieved
        $this->assertCount(2, $newOrders);
        $this->assertTrue($newOrders->contains('pk_order_no', $order1->pk_order_no));
        $this->assertTrue($newOrders->contains('pk_order_no', $order3->pk_order_no));
        $this->assertFalse($newOrders->contains('pk_order_no', $order2->pk_order_no));
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Integration;

use App\DTOs\Payment\PaymentRequestDTO;
use App\Exceptions\Payment\PaymentException;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class PaymentServiceIntegrationTest extends TestCase
{
    /**
     * Test initiate payment.
     *
     * @return void
     */
    public function testInitiatePayment(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/payments/initiate' => Http::response([
                'success' => true,
                'message' => 'Payment initiated successfully',
                'data' => [
                    'transaction_id' => 'txn_123456',
                    'amount' => 100.00,
                    'status' => 'initiated',
                ],
            ], 201),
        ]);

        $paymentService = new PaymentServiceClient('http://payment-service-test:8000/api');
        $paymentRequest = new PaymentRequestDTO(
            1,
            '<EMAIL>',
            '1234567890',
            'Test User',
            100.00,
            'order_123',
            0,
            'https://example.com/success',
            'https://example.com/failure'
        );

        $response = $paymentService->initiatePayment($paymentRequest);

        $this->assertEquals('txn_123456', $response->transactionId);
        $this->assertEquals(100.00, $response->amount);
        $this->assertEquals('initiated', $response->status);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://payment-service-test:8000/api/payments/initiate' &&
                   $request->method() === 'POST' &&
                   $request->data()['customer_id'] === 1 &&
                   $request->data()['amount'] === 100.00;
        });
    }

    /**
     * Test initiate payment with error.
     *
     * @return void
     */
    public function testInitiatePaymentWithError(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/payments/initiate' => Http::response([
                'success' => false,
                'message' => 'Invalid payment data',
            ], 400),
        ]);

        $paymentService = new PaymentServiceClient('http://payment-service-test:8000/api');
        $paymentRequest = new PaymentRequestDTO(
            1,
            '<EMAIL>',
            '1234567890',
            'Test User',
            -100.00, // Invalid amount
            'order_123',
            0,
            'https://example.com/success',
            'https://example.com/failure'
        );

        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Payment initiation failed: Invalid payment data');

        $paymentService->initiatePayment($paymentRequest);
    }

    /**
     * Test process payment.
     *
     * @return void
     */
    public function testProcessPayment(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/payments/txn_123456/process' => Http::response([
                'success' => true,
                'message' => 'Payment processing initiated',
                'data' => [
                    'form_url' => 'https://gateway.example.com/pay',
                    'form_method' => 'POST',
                    'form_fields' => [
                        'amount' => '100.00',
                        'currency' => 'USD',
                        'description' => 'Payment for order order_123',
                    ],
                ],
            ], 200),
        ]);

        $paymentService = new PaymentServiceClient('http://payment-service-test:8000/api');
        $response = $paymentService->processPayment('txn_123456', 'stripe');

        $this->assertEquals('https://gateway.example.com/pay', $response['form_url']);
        $this->assertEquals('POST', $response['form_method']);
        $this->assertArrayHasKey('form_fields', $response);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://payment-service-test:8000/api/payments/txn_123456/process' &&
                   $request->method() === 'POST' &&
                   $request->data()['gateway'] === 'stripe';
        });
    }

    /**
     * Test get payment status.
     *
     * @return void
     */
    public function testGetPaymentStatus(): void
    {
        // Mock HTTP response
        Http::fake([
            '*/payments/txn_123456' => Http::response([
                'success' => true,
                'data' => [
                    'transaction_id' => 'txn_123456',
                    'amount' => 100.00,
                    'status' => 'completed',
                    'gateway' => 'stripe',
                    'gateway_transaction_id' => 'pi_123456',
                    'created_at' => '2023-01-01T12:00:00Z',
                ],
            ], 200),
        ]);

        $paymentService = new PaymentServiceClient('http://payment-service-test:8000/api');
        $status = $paymentService->getPaymentStatus('txn_123456');

        $this->assertEquals('txn_123456', $status['transaction_id']);
        $this->assertEquals(100.00, $status['amount']);
        $this->assertEquals('completed', $status['status']);
        $this->assertEquals('stripe', $status['gateway']);

        Http::assertSent(function ($request) {
            return $request->url() === 'http://payment-service-test:8000/api/payments/txn_123456' &&
                   $request->method() === 'GET';
        });
    }
}

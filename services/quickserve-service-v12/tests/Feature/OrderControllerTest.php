<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class OrderControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->customer = Customer::factory()->create([
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);

        $this->product = Product::factory()->create([
            'name' => 'Vegetable Meal',
            'product_type' => 'Meal',
            'unit_price' => 100.00,
        ]);
    }

    public function testIndex()
    {
        // Create some orders
        Order::factory()->count(3)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'product_code' => $this->product->pk_product_code,
        ]);

        $response = $this->getJson('/api/v2/quickserve/orders');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'order_no',
                        'customer',
                        'product',
                        'quantity',
                        'amount',
                        'order_status',
                        'delivery_status',
                        'order_date',
                    ]
                ]
            ])
            ->assertJsonCount(3, 'data');
    }

    public function testStore()
    {
        // Mock HTTP calls to customer service
        Http::fake([
            'localhost:8002/api/customers/*' => Http::response([
                'data' => [
                    'customer_code' => $this->customer->pk_customer_code,
                    'customer_name' => $this->customer->customer_name,
                    'phone' => $this->customer->phone,
                    'email_address' => $this->customer->email_address,
                ],
            ], 200),
        ]);

        $orderData = [
            'customer_code' => (string) $this->customer->pk_customer_code,
            'customer_name' => $this->customer->customer_name,
            'phone' => $this->customer->phone,
            'email_address' => $this->customer->email_address,
            'location_code' => 1,
            'location_name' => 'Office',
            'product_code' => (string) $this->product->pk_product_code,
            'product_name' => $this->product->name,
            'product_type' => $this->product->product_type,
            'quantity' => 1,
            'amount' => $this->product->unit_price,
            'order_date' => now()->format('Y-m-d'),
            'ship_address' => '123 Main St, City',
            'order_menu' => 'Lunch',
        ];

        $response = $this->postJson('/api/v2/quickserve/orders', $orderData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'order_no',
                    'customer',
                    'product',
                    'quantity',
                    'amount',
                    'order_status',
                    'delivery_status',
                    'order_date',
                ]
            ]);

        $this->assertDatabaseHas('orders', [
            'customer_code' => $this->customer->pk_customer_code,
            'product_code' => $this->product->pk_product_code,
            'quantity' => 1,
            'amount' => $this->product->unit_price,
            'order_menu' => 'Lunch',
        ]);
    }

    public function testShow()
    {
        $order = Order::factory()->create([
            'customer_code' => $this->customer->pk_customer_code,
            'product_code' => $this->product->pk_product_code,
        ]);

        $response = $this->getJson("/api/v2/quickserve/orders/{$order->pk_order_no}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'order_no',
                    'customer',
                    'product',
                    'quantity',
                    'amount',
                    'order_status',
                    'delivery_status',
                    'order_date',
                ]
            ])
            ->assertJson([
                'data' => [
                    'id' => $order->pk_order_no,
                ]
            ]);
    }

    public function testUpdate()
    {
        $order = Order::factory()->create([
            'customer_code' => $this->customer->pk_customer_code,
            'product_code' => $this->product->pk_product_code,
            'quantity' => 1,
            'amount' => 100.00,
            'order_status' => 'New',
        ]);

        $updateData = [
            'quantity' => 2,
            'amount' => 200.00,
            'order_status' => 'Processing',
        ];

        $response = $this->putJson("/api/v2/quickserve/orders/{$order->pk_order_no}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'order_no',
                    'customer',
                    'product',
                    'quantity',
                    'amount',
                    'order_status',
                    'delivery_status',
                    'order_date',
                ]
            ])
            ->assertJson([
                'data' => [
                    'id' => $order->pk_order_no,
                    'quantity' => 2,
                    'amount' => 200.00,
                    'order_status' => 'Processing',
                ]
            ]);

        $this->assertDatabaseHas('orders', [
            'pk_order_no' => $order->pk_order_no,
            'quantity' => 2,
            'amount' => 200.00,
            'order_status' => 'Processing',
        ]);
    }

    public function testDestroy()
    {
        $order = Order::factory()->create([
            'customer_code' => $this->customer->pk_customer_code,
            'product_code' => $this->product->pk_product_code,
            'order_status' => 'New', // Ensure order can be deleted
        ]);

        $response = $this->deleteJson("/api/v2/quickserve/orders/{$order->pk_order_no}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order deleted successfully',
            ]);

        $this->assertDatabaseMissing('orders', [
            'pk_order_no' => $order->pk_order_no,
        ]);
    }

    public function testUpdateStatus()
    {
        $order = Order::factory()->create([
            'customer_code' => $this->customer->pk_customer_code,
            'product_code' => $this->product->pk_product_code,
            'order_status' => 'New',
        ]);

        $response = $this->patchJson("/api/v2/quickserve/orders/{$order->pk_order_no}/status", [
            'status' => 'Processing',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'order_no',
                    'customer',
                    'product',
                    'quantity',
                    'amount',
                    'order_status',
                    'delivery_status',
                    'order_date',
                ]
            ])
            ->assertJson([
                'data' => [
                    'id' => $order->pk_order_no,
                    'order_status' => 'Processing',
                ]
            ]);

        $this->assertDatabaseHas('orders', [
            'pk_order_no' => $order->pk_order_no,
            'order_status' => 'Processing',
        ]);
    }

    public function testUpdateDeliveryStatus()
    {
        $order = Order::factory()->create([
            'customer_code' => $this->customer->pk_customer_code,
            'product_code' => $this->product->pk_product_code,
            'delivery_status' => 'Pending',
        ]);

        $response = $this->patchJson("/api/v2/quickserve/orders/{$order->pk_order_no}/delivery-status", [
            'delivery_status' => 'Dispatched',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'order_no',
                    'customer',
                    'product',
                    'quantity',
                    'amount',
                    'order_status',
                    'delivery_status',
                    'order_date',
                ]
            ])
            ->assertJson([
                'data' => [
                    'id' => $order->pk_order_no,
                    'delivery_status' => 'Dispatched',
                ]
            ]);

        $this->assertDatabaseHas('orders', [
            'pk_order_no' => $order->pk_order_no,
            'delivery_status' => 'Dispatched',
        ]);
    }
}

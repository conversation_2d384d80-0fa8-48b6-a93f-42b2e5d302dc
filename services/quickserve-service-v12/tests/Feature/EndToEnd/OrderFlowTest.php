<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\EndToEnd;

use App\Models\Order;
use App\Services\Customer\CustomerServiceClient;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class OrderFlowTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Configure the base URLs for the services
        config(['services.customer.url' => 'http://customer-service']);
        config(['services.payment.url' => 'http://payment-service']);
        config(['services.customer.token' => 'test-token']);
        config(['services.payment.token' => 'test-token']);
    }

    /**
     * Test the complete order flow from creation to payment.
     *
     * @return void
     */
    public function testCompleteOrderFlow(): void
    {
        // Mock the HTTP client for the Customer and Payment services
        Http::fake([
            'http://customer-service/customers/CUST123' => Http::response([
                'success' => true,
                'data' => [
                    'customer_code' => 'CUST123',
                    'customer_name' => 'Test Customer',
                    'email_address' => '<EMAIL>',
                    'phone' => '1234567890',
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'country' => 'Test Country',
                    'pincode' => '123456',
                    'wallet_balance' => 100.00,
                ],
            ], 200),
            'http://customer-service/customers/CUST123/wallet' => Http::response([
                'success' => true,
                'data' => [
                    'balance' => 100.00,
                ],
            ], 200),
            'http://payment-service/payments/initiate' => Http::response([
                'success' => true,
                'data' => [
                    'transaction_id' => 'txn_123456',
                    'amount' => 67.00,
                    'status' => 'pending',
                    'payment_url' => 'https://example.com/payment',
                ],
            ], 200),
            'http://payment-service/payments/txn_123456/process' => Http::response([
                'success' => true,
                'data' => [
                    'transaction_id' => 'txn_123456',
                    'amount' => 67.00,
                    'status' => 'pending',
                    'payment_url' => 'https://example.com/payment',
                ],
            ], 200),
        ]);

        // Step 1: Create an order
        $orderData = [
            'customer_code' => 'CUST123',
            'customer_name' => 'Test Customer',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Office',
            'product_code' => 'PROD123',
            'product_name' => 'Test Product',
            'product_type' => 'Food',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => now()->format('Y-m-d'),
            'ship_address' => '123 Test St, Test City',
            'order_menu' => 'Lunch',
            'company_id' => 1,
            'unit_id' => 1,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'items' => [
                [
                    'product_code' => 'PROD123',
                    'product_name' => 'Test Product',
                    'quantity' => 2,
                    'amount' => 50.00,
                    'tax' => 5.00,
                ],
            ],
        ];

        $response = $this->postJson('/api/v2/quickserve/orders', $orderData);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Order created successfully',
            ]);

        $orderId = $response->json('data.id');
        $orderNo = $response->json('data.order_no');

        // Step 2: Process payment for the order
        $paymentData = [
            'gateway' => 'stripe',
            'wallet_amount' => 50.00,
        ];

        $response = $this->postJson("/api/v2/quickserve/orders/{$orderId}/payment", $paymentData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment initiated successfully',
                'data' => [
                    'transaction_id' => 'txn_123456',
                    'amount' => 67.00,
                    'status' => 'pending',
                    'payment_url' => 'https://example.com/payment',
                ],
            ]);

        // Step 3: Verify the order was updated with payment information
        $order = Order::find($orderId);
        $this->assertEquals('txn_123456', $order->transaction_id);
        $this->assertEquals('stripe', $order->payment_mode);

        // Verify the HTTP requests were made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/customers/CUST123'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/customers/CUST123/wallet'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });

        Http::assertSent(function ($request) use ($orderNo) {
            return $request->url() === 'http://payment-service/payments/initiate'
                && $request->hasHeader('X-Service', 'QuickServe Service')
                && $request['order_no'] === $orderNo;
        });
    }

    /**
     * Test the order flow with full wallet payment.
     *
     * @return void
     */
    public function testOrderFlowWithFullWalletPayment(): void
    {
        // Mock the HTTP client for the Customer service
        Http::fake([
            'http://customer-service/customers/CUST123' => Http::response([
                'success' => true,
                'data' => [
                    'customer_code' => 'CUST123',
                    'customer_name' => 'Test Customer',
                    'email_address' => '<EMAIL>',
                    'phone' => '1234567890',
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'country' => 'Test Country',
                    'pincode' => '123456',
                    'wallet_balance' => 200.00,
                ],
            ], 200),
            'http://customer-service/customers/CUST123/wallet' => Http::response([
                'success' => true,
                'data' => [
                    'balance' => 200.00,
                ],
            ], 200),
            'http://customer-service/customers/CUST123/wallet/deduct' => Http::response([
                'success' => true,
                'data' => [
                    'balance' => 83.00,
                ],
            ], 200),
        ]);

        // Step 1: Create an order
        $orderData = [
            'customer_code' => 'CUST123',
            'customer_name' => 'Test Customer',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Office',
            'product_code' => 'PROD123',
            'product_name' => 'Test Product',
            'product_type' => 'Food',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => now()->format('Y-m-d'),
            'ship_address' => '123 Test St, Test City',
            'order_menu' => 'Lunch',
            'company_id' => 1,
            'unit_id' => 1,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'items' => [
                [
                    'product_code' => 'PROD123',
                    'product_name' => 'Test Product',
                    'quantity' => 2,
                    'amount' => 50.00,
                    'tax' => 5.00,
                ],
            ],
        ];

        $response = $this->postJson('/api/v2/quickserve/orders', $orderData);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Order created successfully',
            ]);

        $orderId = $response->json('data.id');
        $totalAmount = $response->json('data.total_amount');

        // Step 2: Process payment for the order using wallet
        $paymentData = [
            'gateway' => 'wallet',
            'wallet_amount' => $totalAmount,
        ];

        $response = $this->postJson("/api/v2/quickserve/orders/{$orderId}/payment", $paymentData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment initiated successfully',
                'data' => [
                    'status' => 'completed',
                ],
            ]);

        // Step 3: Verify the order was updated with payment information
        $order = Order::find($orderId);
        $this->assertStringStartsWith('wallet_', $order->transaction_id);
        $this->assertEquals('wallet', $order->payment_mode);
        $this->assertEquals($totalAmount, $order->amount_paid);
        $this->assertEquals('Paid', $order->order_status);

        // Verify the HTTP requests were made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/customers/CUST123'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });

        Http::assertSent(function ($request) {
            return $request->url() === 'http://customer-service/customers/CUST123/wallet'
                && $request->hasHeader('Authorization', 'Bearer test-token');
        });

        Http::assertSent(function ($request) use ($totalAmount) {
            return $request->url() === 'http://customer-service/customers/CUST123/wallet/deduct'
                && $request->hasHeader('Authorization', 'Bearer test-token')
                && $request['amount'] === $totalAmount;
        });
    }

    /**
     * Test the order cancellation flow.
     *
     * @return void
     */
    public function testOrderCancellationFlow(): void
    {
        // Mock the HTTP client for the Customer service
        Http::fake([
            'http://customer-service/customers/CUST123' => Http::response([
                'success' => true,
                'data' => [
                    'customer_code' => 'CUST123',
                    'customer_name' => 'Test Customer',
                    'email_address' => '<EMAIL>',
                    'phone' => '1234567890',
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'country' => 'Test Country',
                    'pincode' => '123456',
                    'wallet_balance' => 100.00,
                ],
            ], 200),
        ]);

        // Step 1: Create an order
        $orderData = [
            'customer_code' => 'CUST123',
            'customer_name' => 'Test Customer',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Office',
            'product_code' => 'PROD123',
            'product_name' => 'Test Product',
            'product_type' => 'Food',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => now()->format('Y-m-d'),
            'ship_address' => '123 Test St, Test City',
            'order_menu' => 'Lunch',
            'company_id' => 1,
            'unit_id' => 1,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'items' => [
                [
                    'product_code' => 'PROD123',
                    'product_name' => 'Test Product',
                    'quantity' => 2,
                    'amount' => 50.00,
                    'tax' => 5.00,
                ],
            ],
        ];

        $response = $this->postJson('/api/v2/quickserve/orders', $orderData);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Order created successfully',
            ]);

        $orderId = $response->json('data.id');

        // Step 2: Cancel the order
        $cancelData = [
            'reason' => 'Customer requested cancellation',
        ];

        $response = $this->postJson("/api/v2/quickserve/orders/{$orderId}/cancel", $cancelData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order cancelled successfully',
                'data' => [
                    'order_status' => 'Cancelled',
                    'cancellation_reason' => 'Customer requested cancellation',
                ],
            ]);

        // Step 3: Verify the order was updated with cancellation information
        $order = Order::find($orderId);
        $this->assertEquals('Cancelled', $order->order_status);
        $this->assertEquals('Customer requested cancellation', $order->cancellation_reason);
        $this->assertNotNull($order->cancelled_at);
    }
}

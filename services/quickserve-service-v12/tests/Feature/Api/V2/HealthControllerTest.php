<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\Api\V2;

use App\Services\Customer\CustomerServiceClient;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class HealthControllerTest extends TestCase
{
    /**
     * Test that the health check returns a successful response when all services are healthy.
     *
     * @return void
     */
    public function testHealthCheckReturnsSuccessfulResponse(): void
    {
        // Mock the CustomerServiceClient
        $this->mock(CustomerServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(true);
        });

        // Mock the PaymentServiceClient
        $this->mock(PaymentServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(true);
        });

        // Mock the DB facade
        DB::shouldReceive('select')
            ->once()
            ->with('SELECT 1')
            ->andReturn([1]);

        $response = $this->getJson('/api/v2/quickserve/health');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'service',
                'timestamp',
                'version',
                'checks' => [
                    'database',
                    'customer_service',
                    'payment_service',
                ],
            ])
            ->assertJson([
                'status' => 'healthy',
                'service' => 'quickserve-service',
                'version' => '1.0.0',
                'checks' => [
                    'database' => [
                        'status' => 'ok',
                    ],
                    'customer_service' => [
                        'status' => 'ok',
                    ],
                    'payment_service' => [
                        'status' => 'ok',
                    ],
                ],
            ]);
    }

    /**
     * Test that the health check returns an error response when the database is unhealthy.
     *
     * @return void
     */
    public function testHealthCheckReturnsErrorResponseWhenDatabaseIsUnhealthy(): void
    {
        // Mock the CustomerServiceClient
        $this->mock(CustomerServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(true);
        });

        // Mock the PaymentServiceClient
        $this->mock(PaymentServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(true);
        });

        // Mock the DB facade to throw an exception
        DB::shouldReceive('select')
            ->once()
            ->with('SELECT 1')
            ->andThrow(new \Exception('Database connection failed'));

        $response = $this->getJson('/api/v2/quickserve/health');

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'down',
                'service' => 'quickserve-service',
                'checks' => [
                    'database' => [
                        'status' => 'error',
                    ],
                    'customer_service' => [
                        'status' => 'ok',
                    ],
                    'payment_service' => [
                        'status' => 'ok',
                    ],
                ],
            ]);
    }

    /**
     * Test that the health check returns an error response when the customer service is unhealthy.
     *
     * @return void
     */
    public function testHealthCheckReturnsErrorResponseWhenCustomerServiceIsUnhealthy(): void
    {
        // Mock the CustomerServiceClient
        $this->mock(CustomerServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(false);
        });

        // Mock the PaymentServiceClient
        $this->mock(PaymentServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(true);
        });

        // Mock the DB facade
        DB::shouldReceive('select')
            ->once()
            ->with('SELECT 1')
            ->andReturn([1]);

        $response = $this->getJson('/api/v2/quickserve/health');

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'down',
                'service' => 'quickserve-service',
                'checks' => [
                    'database' => [
                        'status' => 'ok',
                    ],
                    'customer_service' => [
                        'status' => 'error',
                    ],
                    'payment_service' => [
                        'status' => 'ok',
                    ],
                ],
            ]);
    }

    /**
     * Test that the health check returns an error response when the payment service is unhealthy.
     *
     * @return void
     */
    public function testHealthCheckReturnsErrorResponseWhenPaymentServiceIsUnhealthy(): void
    {
        // Mock the CustomerServiceClient
        $this->mock(CustomerServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(true);
        });

        // Mock the PaymentServiceClient
        $this->mock(PaymentServiceClient::class, function ($mock) {
            $mock->shouldReceive('ping')
                ->once()
                ->andReturn(false);
        });

        // Mock the DB facade
        DB::shouldReceive('select')
            ->once()
            ->with('SELECT 1')
            ->andReturn([1]);

        $response = $this->getJson('/api/v2/quickserve/health');

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'down',
                'service' => 'quickserve-service',
                'checks' => [
                    'database' => [
                        'status' => 'ok',
                    ],
                    'customer_service' => [
                        'status' => 'ok',
                    ],
                    'payment_service' => [
                        'status' => 'error',
                    ],
                ],
            ]);
    }
}

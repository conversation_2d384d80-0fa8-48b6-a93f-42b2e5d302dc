<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\Api\V2;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class ConfigControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testIndex()
    {
        // Arrange
        DB::table('settings')->insert([
            ['setting_key' => 'key1', 'setting_value' => 'value1', 'created_at' => now(), 'updated_at' => now()],
            ['setting_key' => 'key2', 'setting_value' => 'value2', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/config');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Configuration retrieved successfully',
            ]);

        $data = $response->json('data');
        $this->assertArrayHasKey('key1', $data);
        $this->assertArrayHasKey('key2', $data);
        $this->assertEquals('value1', $data['key1']);
        $this->assertEquals('value2', $data['key2']);
    }

    public function testShow()
    {
        // Arrange
        DB::table('settings')->insert([
            'setting_key' => 'test_key',
            'setting_value' => 'test_value',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/config/test_key');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'key',
                    'value',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Configuration retrieved successfully',
                'data' => [
                    'key' => 'test_key',
                    'value' => 'test_value',
                ],
            ]);
    }

    public function testShowNotFound()
    {
        // Act
        $response = $this->getJson('/api/v2/quickserve/config/nonexistent_key');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => "Configuration key 'nonexistent_key' not found",
            ]);
    }

    public function testUpdate()
    {
        // Arrange
        DB::table('settings')->insert([
            'setting_key' => 'test_key',
            'setting_value' => 'test_value',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Act
        $response = $this->putJson('/api/v2/quickserve/config/test_key', [
            'value' => 'new_value',
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'key',
                    'value',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Configuration updated successfully',
                'data' => [
                    'key' => 'test_key',
                    'value' => 'new_value',
                ],
            ]);

        $this->assertDatabaseHas('settings', [
            'setting_key' => 'test_key',
            'setting_value' => 'new_value',
        ]);
    }

    public function testUpdateValidationError()
    {
        // Act
        $response = $this->putJson('/api/v2/quickserve/config/test_key', []);

        // Assert - Laravel validation may not include 'success' field
        $response->assertStatus(422)
            ->assertJsonStructure([
                'message',
                'errors' => [
                    'value',
                ],
            ]);

        // Check if response has success field, if so validate it
        $responseData = $response->json();
        if (isset($responseData['success'])) {
            $response->assertJson([
                'success' => false,
            ]);
        }
    }

    public function testSettings()
    {
        // Arrange
        DB::table('settings')->insert([
            ['setting_key' => 'key1', 'setting_value' => 'value1', 'created_at' => now(), 'updated_at' => now()],
            ['setting_key' => 'key2', 'setting_value' => 'value2', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/config/settings');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Settings retrieved successfully',
            ]);

        $data = $response->json('data');
        $this->assertArrayHasKey('key1', $data);
        $this->assertArrayHasKey('key2', $data);
        $this->assertEquals('value1', $data['key1']);
        $this->assertEquals('value2', $data['key2']);
    }
}

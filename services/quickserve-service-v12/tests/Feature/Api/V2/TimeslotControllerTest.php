<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\Api\V2;

use App\Models\Timeslot;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TimeslotControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testIndex()
    {
        // Arrange
        Timeslot::factory()->count(3)->create();

        // Act
        $response = $this->getJson('/api/v2/quickserve/timeslots');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'company_id',
                        'unit_id',
                        'starttime',
                        'endtime',
                        'day',
                        'menu_type',
                        'kitchen',
                        'status',
                        'formatted_start_time',
                        'formatted_end_time',
                        'display_slot',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ]);
        
        $this->assertCount(3, $response->json('data'));
    }

    public function testIndexWithFilters()
    {
        // Arrange
        Timeslot::factory()->create([
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ]);
        Timeslot::factory()->create([
            'day' => 'Monday',
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
        ]);
        Timeslot::factory()->create([
            'day' => 'Tuesday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/timeslots?day=Monday&menu_type=Breakfast');

        // Assert
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('Monday', $response->json('data.0.day'));
        $this->assertEquals('Breakfast', $response->json('data.0.menu_type'));
    }

    public function testStore()
    {
        // Arrange
        $data = [
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ];

        // Act
        $response = $this->postJson('/api/v2/quickserve/timeslots', $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'company_id',
                    'unit_id',
                    'starttime',
                    'endtime',
                    'day',
                    'menu_type',
                    'kitchen',
                    'status',
                    'formatted_start_time',
                    'formatted_end_time',
                    'display_slot',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Timeslot created successfully',
                'data' => [
                    'starttime' => '08:00:00',
                    'endtime' => '10:00:00',
                    'day' => 'Monday',
                    'menu_type' => 'Breakfast',
                    'kitchen' => 'Main',
                    'status' => 1,
                ],
            ]);
        
        $this->assertDatabaseHas('timeslot', [
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ]);
    }

    public function testStoreValidationError()
    {
        // Act
        $response = $this->postJson('/api/v2/quickserve/timeslots', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonStructure([
                'message',
                'errors' => [
                    'starttime',
                    'endtime',
                    'day',
                    'menu_type',
                    'kitchen',
                ],
            ]);
    }

    public function testStoreDuplicateTimeslot()
    {
        // Arrange
        Timeslot::factory()->create([
            'starttime' => '08:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ]);
        $data = [
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
            'day' => 'Monday',
            'menu_type' => 'Breakfast',
            'kitchen' => 'Main',
            'status' => 1,
        ];

        // Act
        $response = $this->postJson('/api/v2/quickserve/timeslots', $data);

        // Assert
        $response->assertStatus(400)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Timeslot already exists for this day, menu type, and kitchen.',
            ]);
    }

    public function testShow()
    {
        // Arrange
        $timeslot = Timeslot::factory()->create();

        // Act
        $response = $this->getJson("/api/v2/quickserve/timeslots/{$timeslot->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'company_id',
                    'unit_id',
                    'starttime',
                    'endtime',
                    'day',
                    'menu_type',
                    'kitchen',
                    'status',
                    'formatted_start_time',
                    'formatted_end_time',
                    'display_slot',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Timeslot retrieved successfully',
                'data' => [
                    'id' => $timeslot->id,
                ],
            ]);
    }

    public function testShowNotFound()
    {
        // Act
        $response = $this->getJson('/api/v2/quickserve/timeslots/999');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Timeslot not found',
            ]);
    }

    public function testUpdate()
    {
        // Arrange
        $timeslot = Timeslot::factory()->create([
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
        ]);
        $data = [
            'starttime' => '09:00:00',
            'endtime' => '11:00:00',
        ];

        // Act
        $response = $this->putJson("/api/v2/quickserve/timeslots/{$timeslot->id}", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'starttime',
                    'endtime',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Timeslot updated successfully',
                'data' => [
                    'id' => $timeslot->id,
                    'starttime' => '09:00:00',
                    'endtime' => '11:00:00',
                ],
            ]);
        
        $this->assertDatabaseHas('timeslot', [
            'id' => $timeslot->id,
            'starttime' => '09:00:00',
            'endtime' => '11:00:00',
        ]);
    }

    public function testUpdateNotFound()
    {
        // Act
        $response = $this->putJson('/api/v2/quickserve/timeslots/999', [
            'starttime' => '09:00:00',
            'endtime' => '11:00:00',
        ]);

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Timeslot not found',
            ]);
    }

    public function testDestroy()
    {
        // Arrange
        $timeslot = Timeslot::factory()->create();

        // Act
        $response = $this->deleteJson("/api/v2/quickserve/timeslots/{$timeslot->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Timeslot deleted successfully',
                'data' => [],
            ]);
        
        $this->assertDatabaseMissing('timeslot', ['id' => $timeslot->id]);
    }

    public function testDestroyNotFound()
    {
        // Act
        $response = $this->deleteJson('/api/v2/quickserve/timeslots/999');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Timeslot not found',
            ]);
    }

    public function testAvailable()
    {
        // Arrange
        $today = Carbon::now()->format('l'); // e.g., "Monday"
        Timeslot::factory()->create([
            'day' => $today,
            'menu_type' => 'Lunch',
            'kitchen' => 'Main',
            'status' => 1,
            'starttime' => Carbon::now()->addHours(2)->format('H:i:s'),
            'endtime' => Carbon::now()->addHours(4)->format('H:i:s'),
        ]);

        // Act
        $response = $this->getJson("/api/v2/quickserve/timeslots/available?day={$today}&menu_type=Lunch&kitchen=Main");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'day',
                        'menu_type',
                        'kitchen',
                        'starttime',
                        'endtime',
                        'status',
                    ],
                ],
            ]);
        
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals($today, $response->json('data.0.day'));
        $this->assertEquals('Lunch', $response->json('data.0.menu_type'));
        $this->assertEquals('Main', $response->json('data.0.kitchen'));
    }

    public function testAvailableValidationError()
    {
        // Act
        $response = $this->getJson('/api/v2/quickserve/timeslots/available');

        // Assert
        $response->assertStatus(422)
            ->assertJsonStructure([
                'message',
                'errors' => [
                    'day',
                    'menu_type',
                    'kitchen',
                ],
            ]);
    }
}

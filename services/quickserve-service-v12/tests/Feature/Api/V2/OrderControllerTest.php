<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\Api\V2;

use App\Models\Order;
use App\Services\Customer\CustomerServiceClient;
use App\Services\OrderService;
use App\Services\Payment\PaymentServiceClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\TestCase;

class OrderControllerTest extends TestCase
{
    use WithFaker;

    /**
     * @var OrderService|Mockery\MockInterface
     */
    protected $orderService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->orderService = $this->mock(OrderService::class);
    }

    /**
     * Test getting all orders.
     *
     * @return void
     */
    public function testIndex(): void
    {
        $orders = Order::factory()->count(3)->make();

        $this->orderService->shouldReceive('getAllOrders')
            ->once()
            ->andReturn($orders);

        $response = $this->getJson('/api/v2/quickserve/orders');

        $response->assertStatus(200)
            ->assertJsonCount(3, 'data');
    }

    /**
     * Test creating an order.
     *
     * @return void
     */
    public function testStore(): void
    {
        $orderData = [
            'customer_code' => 'CUST123',
            'customer_name' => 'Test Customer',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Test Location',
            'city' => 'Test City',
            'city_name' => 'Test City Name',
            'product_code' => 'PROD123',
            'product_name' => 'Test Product',
            'product_description' => 'Test Product Description',
            'product_type' => 'Meal',
            'quantity' => 2,
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'order_status' => 'New',
            'order_date' => '2023-05-18',
            'ship_address' => 'Test Address',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'order_menu' => 'Lunch',
            'food_type' => 'veg',
            'payment_mode' => 'Cash',
            'amount_paid' => 0.00,
            'tax_method' => 'inclusive',
            'source' => 'App',
            'delivery_time' => '12:00:00',
            'remark' => 'Test Remark',
            'company_id' => 1,
            'unit_id' => 1,
        ];

        $order = Order::factory()->make([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
        ]);

        $createdOrder = Order::factory()->make([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
        ]);

        $this->orderService->shouldReceive('createOrder')
            ->once()
            ->andReturn($createdOrder);

        $response = $this->postJson('/api/v2/quickserve/orders', $orderData);

        $response->assertStatus(201)
            ->assertJsonPath('data.order_no', 'ORD123')
            ->assertJsonPath('data.customer.id', 'CUST123');
    }

    /**
     * Test showing an order.
     *
     * @return void
     */
    public function testShow(): void
    {
        $order = Order::factory()->make([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
        ]);

        $this->orderService->shouldReceive('getOrderById')
            ->once()
            ->with(1)
            ->andReturn($order);

        $response = $this->getJson('/api/v2/quickserve/orders/1');

        $response->assertStatus(200)
            ->assertJsonPath('data.order_no', 'ORD123')
            ->assertJsonPath('data.customer.id', 'CUST123')
            ->assertJsonPath('data.company_id', 1)
            ->assertJsonPath('data.unit_id', 1)
            ->assertJsonPath('data.order_status', 'New')
            ->assertJsonPath('data.delivery_status', 'Pending')
            ->assertJsonPath('data.invoice_status', 'Unbill')
            ->assertJsonPath('data.amount', '100.00')
            ->assertJsonPath('data.tax', '10.00')
            ->assertJsonPath('data.delivery_charges', '5.00')
            ->assertJsonPath('data.service_charges', '2.00')
            ->assertJsonPath('data.applied_discount', '0.00');
    }

    /**
     * Test showing a non-existent order.
     *
     * @return void
     */
    public function testShowNonExistentOrder(): void
    {
        $this->orderService->shouldReceive('getOrderById')
            ->once()
            ->with(999)
            ->andReturn(null);

        $response = $this->getJson('/api/v2/quickserve/orders/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Order not found',
            ]);
    }

    /**
     * Test updating an order.
     *
     * @return void
     */
    public function testUpdate(): void
    {
        $updateData = [
            'order_status' => 'Processing',
            'amount' => 150.00,
        ];

        $order = Order::factory()->make([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'Processing',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 150.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
        ]);

        $this->orderService->shouldReceive('updateOrder')
            ->once()
            ->with(1, $updateData)
            ->andReturn($order);

        $response = $this->putJson('/api/v2/quickserve/orders/1', $updateData);

        $response->assertStatus(200)
            ->assertJsonPath('data.order_no', 'ORD123')
            ->assertJsonPath('data.order_status', 'Processing')
            ->assertJsonPath('data.amount', '150.00');
    }

    /**
     * Test updating a non-existent order.
     *
     * @return void
     */
    public function testUpdateNonExistentOrder(): void
    {
        $updateData = [
            'order_status' => 'Processing',
            'amount' => 150.00,
        ];

        $this->orderService->shouldReceive('updateOrder')
            ->once()
            ->with(999, $updateData)
            ->andThrow(new \App\Exceptions\Order\OrderException('Order not found'));

        $response = $this->putJson('/api/v2/quickserve/orders/999', $updateData);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Error updating order: Order not found',
            ]);
    }

    /**
     * Test deleting an order.
     *
     * @return void
     */
    public function testDestroy(): void
    {
        $this->orderService->shouldReceive('deleteOrder')
            ->once()
            ->with(1)
            ->andReturn(true);

        $response = $this->deleteJson('/api/v2/quickserve/orders/1');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order deleted successfully',
            ]);
    }

    /**
     * Test deleting a non-existent order.
     *
     * @return void
     */
    public function testDestroyNonExistentOrder(): void
    {
        $this->orderService->shouldReceive('deleteOrder')
            ->once()
            ->with(999)
            ->andReturn(false);

        $response = $this->deleteJson('/api/v2/quickserve/orders/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Order not found',
            ]);
    }

    /**
     * Test cancelling an order.
     *
     * @return void
     */
    public function testCancel(): void
    {
        $cancelData = [
            'reason' => 'Test cancellation reason',
        ];

        $order = Order::factory()->make([
            'id' => 1,
            'order_no' => 'ORD123',
            'customer_code' => 'CUST123',
            'company_id' => 1,
            'unit_id' => 1,
            'order_status' => 'Cancelled',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'amount' => 100.00,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'cancellation_reason' => 'Test cancellation reason',
            'cancelled_at' => now(),
        ]);

        $this->orderService->shouldReceive('cancelOrder')
            ->once()
            ->with(1, 'Test cancellation reason')
            ->andReturn($order);

        $response = $this->postJson('/api/v2/quickserve/orders/1/cancel', $cancelData);

        $response->assertStatus(200)
            ->assertJsonPath('data.order_no', 'ORD123')
            ->assertJsonPath('data.customer.id', 'CUST123')
            ->assertJsonPath('data.company_id', 1)
            ->assertJsonPath('data.unit_id', 1)
            ->assertJsonPath('data.order_status', 'Cancelled')
            ->assertJsonPath('data.delivery_status', 'Pending')
            ->assertJsonPath('data.invoice_status', 'Unbill')
            ->assertJsonPath('data.amount', '100.00')
            ->assertJsonPath('data.tax', '10.00')
            ->assertJsonPath('data.delivery_charges', '5.00')
            ->assertJsonPath('data.service_charges', '2.00')
            ->assertJsonPath('data.applied_discount', '0.00');
    }

    /**
     * Test cancelling a non-existent order.
     *
     * @return void
     */
    public function testCancelNonExistentOrder(): void
    {
        $cancelData = [
            'reason' => 'Test cancellation reason',
        ];

        $this->orderService->shouldReceive('cancelOrder')
            ->once()
            ->with(999, 'Test cancellation reason')
            ->andThrow(new \App\Exceptions\Order\OrderException('Order not found'));

        $response = $this->postJson('/api/v2/quickserve/orders/999/cancel', $cancelData);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Order not found',
            ]);
    }

    /**
     * Test processing payment for an order.
     *
     * @return void
     */
    public function testProcessPayment(): void
    {
        $paymentData = [
            'gateway' => 'stripe',
            'wallet_amount' => 50.00,
        ];

        $paymentResponse = new \App\DTOs\Payment\PaymentResponseDTO(
            'txn_123456',
            100.00,
            'pending',
            'https://example.com/payment',
            null
        );

        $this->orderService->shouldReceive('processPayment')
            ->once()
            ->with(1, 'stripe', 50.00)
            ->andReturn($paymentResponse);

        $response = $this->postJson('/api/v2/quickserve/orders/1/payment', $paymentData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment initiated successfully',
                'data' => [
                    'transactionId' => 'txn_123456',
                    'amount' => 100.0,
                    'status' => 'pending',
                    'paymentUrl' => 'https://example.com/payment',
                    'errorMessage' => null,
                ],
            ]);
    }

    /**
     * Test processing payment for a non-existent order.
     *
     * @return void
     */
    public function testProcessPaymentForNonExistentOrder(): void
    {
        $paymentData = [
            'gateway' => 'stripe',
            'wallet_amount' => 50.00,
        ];

        $this->orderService->shouldReceive('processPayment')
            ->once()
            ->with(999, 'stripe', 50.00)
            ->andThrow(new \App\Exceptions\Order\OrderException('Order not found'));

        $response = $this->postJson('/api/v2/quickserve/orders/999/payment', $paymentData);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Order not found',
            ]);
    }
}

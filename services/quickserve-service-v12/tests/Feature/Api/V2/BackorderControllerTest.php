<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\Api\V2;

use App\Models\Backorder;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BackorderControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testIndex()
    {
        // Arrange
        Backorder::factory()->count(3)->create();

        // Act
        $response = $this->getJson('/api/v2/quickserve/backorders');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'company_id',
                        'unit_id',
                        'order_id',
                        'order_no',
                        'customer_id',
                        'product_id',
                        'product_name',
                        'quantity',
                        'amount',
                        'order_date',
                        'order_menu',
                        'reason',
                        'status',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    public function testIndexWithFilters()
    {
        // Arrange
        Backorder::factory()->create([
            'customer_id' => 1,
            'order_id' => 1,
            'product_id' => 1,
            'status' => 'pending',
        ]);
        Backorder::factory()->create([
            'customer_id' => 1,
            'order_id' => 2,
            'product_id' => 1,
            'status' => 'pending',
        ]);
        Backorder::factory()->create([
            'customer_id' => 2,
            'order_id' => 3,
            'product_id' => 2,
            'status' => 'completed',
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/backorders?customer_id=1&status=pending');

        // Assert
        $response->assertStatus(200);
        $this->assertCount(2, $response->json('data'));
        $this->assertEquals(1, $response->json('data.0.customer_id'));
        $this->assertEquals('pending', $response->json('data.0.status'));
    }

    public function testStore()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'pk_customer_code' => 1,
        ]);
        $order = Order::factory()->create([
            'pk_order_no' => 1,
            'order_no' => 'ORD20230517120000',
        ]);
        $product = Product::factory()->create([
            'pk_product_code' => 1,
            'name' => 'Vegetable Meal',
        ]);
        $data = [
            'company_id' => 1,
            'unit_id' => 1,
            'order_id' => 1,
            'order_no' => 'ORD20230517120000',
            'customer_id' => 1,
            'product_id' => 1,
            'product_name' => 'Vegetable Meal',
            'quantity' => 1,
            'amount' => 100.00,
            'order_date' => '2023-05-17',
            'order_menu' => 'Lunch',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ];

        // Act
        $response = $this->postJson('/api/v2/quickserve/backorders', $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'company_id',
                    'unit_id',
                    'order_id',
                    'order_no',
                    'customer_id',
                    'product_id',
                    'product_name',
                    'quantity',
                    'amount',
                    'order_date',
                    'order_menu',
                    'reason',
                    'status',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Backorder created successfully',
                'data' => [
                    'order_id' => 1,
                    'order_no' => 'ORD20230517120000',
                    'customer_id' => 1,
                    'product_id' => 1,
                    'product_name' => 'Vegetable Meal',
                    'quantity' => 1,
                    'amount' => '100.00', // API returns string format
                    'order_menu' => 'Lunch',
                    'reason' => 'Out of stock',
                    'status' => 'pending',
                    // Note: order_date is in ISO format, so we check structure instead
                ],
            ]);

        $this->assertDatabaseHas('backorders', [
            'order_id' => 1,
            'order_no' => 'ORD20230517120000',
            'customer_id' => 1,
            'product_id' => 1,
            'product_name' => 'Vegetable Meal',
            'quantity' => 1,
            'amount' => 100.00,
            'order_date' => '2023-05-17 00:00:00', // Database stores full timestamp
            'order_menu' => 'Lunch',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ]);
    }

    public function testStoreValidationError()
    {
        // Act
        $response = $this->postJson('/api/v2/quickserve/backorders', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonStructure([
                'message',
                'errors',
            ]);
    }

    public function testCreateFromOrder()
    {
        // Arrange
        $order = Order::factory()->create([
            'pk_order_no' => 1,
            'order_no' => 'ORD20230517120000',
            'customer_code' => 1,
            'product_code' => 1,
            'product_name' => 'Vegetable Meal',
            'quantity' => 1,
            'amount' => 100.00,
            'order_date' => '2023-05-17',
            'order_menu' => 'Lunch',
        ]);
        $product = Product::factory()->create([
            'pk_product_code' => 1,
            'name' => 'Vegetable Meal',
        ]);
        $data = [
            'order_id' => 1,
            'reason' => 'Out of stock',
        ];

        // Act
        $response = $this->postJson('/api/v2/quickserve/backorders/from-order', $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'order_id',
                    'order_no',
                    'customer_id',
                    'product_id',
                    'product_name',
                    'quantity',
                    'amount',
                    'order_date',
                    'order_menu',
                    'reason',
                    'status',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Backorder created successfully from order',
                'data' => [
                    'order_id' => 1,
                    'order_no' => 'ORD20230517120000',
                    'customer_id' => 1,
                    'product_id' => 1,
                    'product_name' => 'Vegetable Meal',
                    'quantity' => 1,
                    'amount' => '100.00', // API returns string format
                    'order_menu' => 'Lunch',
                    'reason' => 'Out of stock',
                    'status' => 'pending',
                    // Note: order_date is in ISO format, so we check structure instead
                ],
            ]);

        $this->assertDatabaseHas('backorders', [
            'order_id' => 1,
            'order_no' => 'ORD20230517120000',
            'customer_id' => 1,
            'product_id' => 1,
            'product_name' => 'Vegetable Meal',
            'quantity' => 1,
            'amount' => 100.00,
            'order_date' => '2023-05-17 00:00:00', // Database stores full timestamp
            'order_menu' => 'Lunch',
            'reason' => 'Out of stock',
            'status' => 'pending',
        ]);
    }

    public function testCreateFromOrderValidationError()
    {
        // Act
        $response = $this->postJson('/api/v2/quickserve/backorders/from-order', []);

        // Assert - Accept either 400 or 422 for validation errors
        $this->assertContains($response->getStatusCode(), [400, 422]);
        $response->assertJsonStructure([
            'message',
        ]);

        // Check if it has errors structure (422) or just message (400)
        if ($response->getStatusCode() === 422) {
            $response->assertJsonStructure([
                'errors' => [
                    'order_id',
                    'reason',
                ],
            ]);
        }
    }

    public function testShow()
    {
        // Arrange
        $backorder = Backorder::factory()->create();

        // Act
        $response = $this->getJson("/api/v2/quickserve/backorders/{$backorder->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'company_id',
                    'unit_id',
                    'order_id',
                    'order_no',
                    'customer_id',
                    'product_id',
                    'product_name',
                    'quantity',
                    'amount',
                    'order_date',
                    'order_menu',
                    'reason',
                    'status',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Backorder retrieved successfully',
                'data' => [
                    'id' => $backorder->id,
                ],
            ]);
    }

    public function testShowNotFound()
    {
        // Act
        $response = $this->getJson('/api/v2/quickserve/backorders/999');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Backorder not found',
            ]);
    }

    public function testUpdate()
    {
        // Arrange
        $backorder = Backorder::factory()->create([
            'quantity' => 1,
            'amount' => 100.00,
            'reason' => 'Out of stock',
            'status' => 'pending',
        ]);
        $data = [
            'quantity' => 2,
            'amount' => 200.00,
            'reason' => 'Customer request',
            'status' => 'completed',
        ];

        // Act
        $response = $this->putJson("/api/v2/quickserve/backorders/{$backorder->id}", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'quantity',
                    'amount',
                    'reason',
                    'status',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Backorder updated successfully',
                'data' => [
                    'id' => $backorder->id,
                    'quantity' => 2,
                    'amount' => 200.00,
                    'reason' => 'Customer request',
                    'status' => 'completed',
                ],
            ]);

        $this->assertDatabaseHas('backorders', [
            'id' => $backorder->id,
            'quantity' => 2,
            'amount' => 200.00,
            'reason' => 'Customer request',
            'status' => 'completed',
        ]);
    }

    public function testUpdateNotFound()
    {
        // Act
        $response = $this->putJson('/api/v2/quickserve/backorders/999', [
            'quantity' => 2,
            'amount' => 200.00,
            'reason' => 'Customer request',
            'status' => 'completed',
        ]);

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Backorder not found',
            ]);
    }

    public function testDestroy()
    {
        // Arrange
        $backorder = Backorder::factory()->create();

        // Act
        $response = $this->deleteJson("/api/v2/quickserve/backorders/{$backorder->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Backorder deleted successfully',
                'data' => [],
            ]);

        $this->assertDatabaseMissing('backorders', ['id' => $backorder->id]);
    }

    public function testDestroyNotFound()
    {
        // Act
        $response = $this->deleteJson('/api/v2/quickserve/backorders/999');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Backorder not found',
            ]);
    }

    public function testComplete()
    {
        // Arrange
        $backorder = Backorder::factory()->create([
            'status' => 'pending',
        ]);

        // Act
        $response = $this->putJson("/api/v2/quickserve/backorders/{$backorder->id}/complete");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'status',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Backorder completed successfully',
                'data' => [
                    'id' => $backorder->id,
                    'status' => 'completed',
                ],
            ]);

        $this->assertDatabaseHas('backorders', [
            'id' => $backorder->id,
            'status' => 'completed',
        ]);
    }

    public function testCompleteNotFound()
    {
        // Act
        $response = $this->putJson('/api/v2/quickserve/backorders/999/complete');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Backorder not found',
            ]);
    }

    public function testCancel()
    {
        // Arrange
        $backorder = Backorder::factory()->create([
            'status' => 'pending',
        ]);

        // Act
        $response = $this->putJson("/api/v2/quickserve/backorders/{$backorder->id}/cancel");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'status',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Backorder cancelled successfully',
                'data' => [
                    'id' => $backorder->id,
                    'status' => 'cancelled',
                ],
            ]);

        $this->assertDatabaseHas('backorders', [
            'id' => $backorder->id,
            'status' => 'cancelled',
        ]);
    }

    public function testCancelNotFound()
    {
        // Act
        $response = $this->putJson('/api/v2/quickserve/backorders/999/cancel');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Backorder not found',
            ]);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\Api\V2;

use App\Models\LocationMapping;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LocationMappingControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testIndex()
    {
        // Arrange
        LocationMapping::factory()->count(3)->create();

        // Act
        $response = $this->getJson('/api/v2/quickserve/locations');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'company_id',
                        'unit_id',
                        'location_code',
                        'location_name',
                        'city_code',
                        'city_name',
                        'delivery_charges',
                        'delivery_time',
                        'kitchen_code',
                        'kitchen_name',
                        'description',
                        'status',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    public function testIndexWithFilters()
    {
        // Arrange
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'kitchen_code' => 'KITCHEN001',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'kitchen_code' => 'KITCHEN002',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY002',
            'kitchen_code' => 'KITCHEN001',
            'status' => 1,
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/locations?city_code=CITY001&kitchen_code=KITCHEN001');

        // Assert
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('CITY001', $response->json('data.0.city_code'));
        $this->assertEquals('KITCHEN001', $response->json('data.0.kitchen_code'));
    }

    public function testIndexWithSearch()
    {
        // Arrange
        LocationMapping::factory()->create([
            'location_name' => 'Downtown',
            'city_name' => 'New York',
            'kitchen_name' => 'Main Kitchen',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'location_name' => 'Uptown',
            'city_name' => 'New York',
            'kitchen_name' => 'Secondary Kitchen',
            'status' => 1,
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/locations?search=Downtown');

        // Assert
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('Downtown', $response->json('data.0.location_name'));
    }

    public function testStore()
    {
        // Arrange
        $data = [
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'CITY001',
            'city_name' => 'New York',
            'delivery_charges' => 10.00,
            'delivery_time' => '30',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'description' => 'Downtown location',
            'status' => 1,
        ];

        // Act
        $response = $this->postJson('/api/v2/quickserve/locations', $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'company_id',
                    'unit_id',
                    'location_code',
                    'location_name',
                    'city_code',
                    'city_name',
                    'delivery_charges',
                    'delivery_time',
                    'kitchen_code',
                    'kitchen_name',
                    'description',
                    'status',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Location mapping created successfully',
                'data' => [
                    'location_code' => 'LOC001',
                    'location_name' => 'Downtown',
                    'city_code' => 'CITY001',
                    'city_name' => 'New York',
                    'delivery_charges' => 10.00,
                    'delivery_time' => '30',
                    'kitchen_code' => 'KITCHEN001',
                    'kitchen_name' => 'Main Kitchen',
                    'description' => 'Downtown location',
                    'status' => 1,
                ],
            ]);

        $this->assertDatabaseHas('location_mapping', [
            'location_code' => 'LOC001',
            'location_name' => 'Downtown',
            'city_code' => 'CITY001',
            'city_name' => 'New York',
            'delivery_charges' => 10.00,
            'delivery_time' => '30',
            'kitchen_code' => 'KITCHEN001',
            'kitchen_name' => 'Main Kitchen',
            'description' => 'Downtown location',
            'status' => 1,
        ]);
    }

    public function testStoreValidationError()
    {
        // Act
        $response = $this->postJson('/api/v2/quickserve/locations', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonStructure([
                'message',
                'errors' => [
                    'location_code',
                    'location_name',
                ],
            ]);
    }

    public function testShow()
    {
        // Arrange
        $locationMapping = LocationMapping::factory()->create();

        // Act
        $response = $this->getJson("/api/v2/quickserve/locations/{$locationMapping->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'company_id',
                    'unit_id',
                    'location_code',
                    'location_name',
                    'city_code',
                    'city_name',
                    'delivery_charges',
                    'delivery_time',
                    'kitchen_code',
                    'kitchen_name',
                    'description',
                    'status',
                    'created_at',
                    'updated_at',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Location mapping retrieved successfully',
                'data' => [
                    'id' => $locationMapping->id,
                ],
            ]);
    }

    public function testShowNotFound()
    {
        // Act
        $response = $this->getJson('/api/v2/quickserve/locations/999');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Location mapping not found',
            ]);
    }

    public function testUpdate()
    {
        // Arrange
        $locationMapping = LocationMapping::factory()->create([
            'location_name' => 'Downtown',
            'delivery_charges' => 10.00,
        ]);
        $data = [
            'location_name' => 'Uptown',
            'delivery_charges' => 15.00,
        ];

        // Act
        $response = $this->putJson("/api/v2/quickserve/locations/{$locationMapping->id}", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'location_name',
                    'delivery_charges',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Location mapping updated successfully',
                'data' => [
                    'id' => $locationMapping->id,
                    'location_name' => 'Uptown',
                    'delivery_charges' => 15.00,
                ],
            ]);

        $this->assertDatabaseHas('location_mapping', [
            'id' => $locationMapping->id,
            'location_name' => 'Uptown',
            'delivery_charges' => 15.00,
        ]);
    }

    public function testUpdateNotFound()
    {
        // Act
        $response = $this->putJson('/api/v2/quickserve/locations/999', [
            'location_name' => 'Uptown',
            'delivery_charges' => 15.00,
        ]);

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Location mapping not found',
            ]);
    }

    public function testDestroy()
    {
        // Arrange
        $locationMapping = LocationMapping::factory()->create();

        // Act
        $response = $this->deleteJson("/api/v2/quickserve/locations/{$locationMapping->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Location mapping deleted successfully',
                'data' => [],
            ]);

        $this->assertDatabaseMissing('location_mapping', ['id' => $locationMapping->id]);
    }

    public function testDestroyNotFound()
    {
        // Act
        $response = $this->deleteJson('/api/v2/quickserve/locations/999');

        // Assert
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => false,
                'message' => 'Location mapping not found',
            ]);
    }

    public function testByCity()
    {
        // Arrange
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY001',
            'location_name' => 'Uptown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'city_code' => 'CITY002',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/locations/by-city?city_code=CITY001');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'city_code',
                        'location_name',
                    ],
                ],
            ]);

        $this->assertCount(2, $response->json('data'));
        $this->assertEquals('CITY001', $response->json('data.0.city_code'));
    }

    public function testByCityValidationError()
    {
        // Act
        $response = $this->getJson('/api/v2/quickserve/locations/by-city');

        // Assert - Accept either 400 or 422 for validation errors
        $this->assertContains($response->getStatusCode(), [400, 422]);
        $response->assertJsonStructure([
            'message',
        ]);

        // Check if it has errors structure (422) or just message (400)
        if ($response->getStatusCode() === 422) {
            $response->assertJsonStructure([
                'errors' => [
                    'city_code',
                ],
            ]);
        }
    }

    public function testByKitchen()
    {
        // Arrange
        LocationMapping::factory()->create([
            'kitchen_code' => 'KITCHEN001',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'kitchen_code' => 'KITCHEN001',
            'location_name' => 'Uptown',
            'status' => 1,
        ]);
        LocationMapping::factory()->create([
            'kitchen_code' => 'KITCHEN002',
            'location_name' => 'Downtown',
            'status' => 1,
        ]);

        // Act
        $response = $this->getJson('/api/v2/quickserve/locations/by-kitchen?kitchen_code=KITCHEN001');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'kitchen_code',
                        'location_name',
                    ],
                ],
            ]);

        $this->assertCount(2, $response->json('data'));
        $this->assertEquals('KITCHEN001', $response->json('data.0.kitchen_code'));
    }

    public function testByKitchenValidationError()
    {
        // Act
        $response = $this->getJson('/api/v2/quickserve/locations/by-kitchen');

        // Assert - Accept either 400 or 422 for validation errors
        $this->assertContains($response->getStatusCode(), [400, 422]);
        $response->assertJsonStructure([
            'message',
        ]);

        // Check if it has errors structure (422) or just message (400)
        if ($response->getStatusCode() === 422) {
            $response->assertJsonStructure([
                'errors' => [
                    'kitchen_code',
                ],
            ]);
        }
    }
}

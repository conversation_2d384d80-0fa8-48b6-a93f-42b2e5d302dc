<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests\Feature\Api\V2;

use App\Models\Product;
use App\Models\User;
use App\Services\ProductService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;
use Mockery;
use Tests\TestCase;

class ProductControllerTest extends TestCase
{
    use WithFaker, RefreshDatabase;

    /**
     * @var ProductService|Mockery\MockInterface
     */
    protected $productService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->productService = $this->mock(ProductService::class);

        // Create and authenticate a user
        $user = User::factory()->create(['role_id' => 1]); // Admin role
        Sanctum::actingAs($user);
    }

    /**
     * Test getting all products.
     *
     * @return void
     */
    public function testIndex(): void
    {
        $products = Product::factory()->count(3)->make();

        $this->productService->shouldReceive('getAllProducts')
            ->once()
            ->with([]) // Controller passes $request->all() which is empty array for GET request
            ->andReturn($products);

        $response = $this->getJson('/api/v2/quickserve/products');

        $response->assertStatus(200)
            ->assertJsonCount(3, 'data');
    }

    /**
     * Test getting paginated products.
     *
     * @return void
     */
    public function testPaginate(): void
    {
        $products = new \Illuminate\Pagination\LengthAwarePaginator(
            Product::factory()->count(3)->make(),
            10,
            3,
            1
        );

        $this->productService->shouldReceive('getPaginatedProducts')
            ->once()
            ->with(3, Mockery::any())
            ->andReturn($products);

        $response = $this->getJson('/api/v2/quickserve/products/paginate?per_page=3');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'current_page',
                    'data',
                    'first_page_url',
                    'from',
                    'last_page',
                    'last_page_url',
                    'links',
                    'next_page_url',
                    'path',
                    'per_page',
                    'prev_page_url',
                    'to',
                    'total',
                ]
            ]);
    }

    /**
     * Test showing a product.
     *
     * @return void
     */
    public function testShow(): void
    {
        $product = Product::factory()->make([
            'pk_product_code' => 1,
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100.00,
            'product_type' => 'Meal',
            'food_type' => 'veg',
            'status' => true,
        ]);

        $this->productService->shouldReceive('getProductById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $response = $this->getJson('/api/v2/quickserve/products/1');

        $response->assertStatus(200)
            ->assertJsonPath('data.name', 'Test Product')
            ->assertJsonPath('data.description', 'Test Description')
            ->assertJsonPath('data.unit_price', '100.00')
            ->assertJsonPath('data.product_type', 'Meal')
            ->assertJsonPath('data.food_type', 'veg')
            ->assertJsonPath('data.status', true);
    }

    /**
     * Test showing a non-existent product.
     *
     * @return void
     */
    public function testShowNonExistentProduct(): void
    {
        $this->productService->shouldReceive('getProductById')
            ->once()
            ->with(999)
            ->andReturn(null);

        $response = $this->getJson('/api/v2/quickserve/products/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Product not found',
            ]);
    }

    /**
     * Test creating a product.
     *
     * @return void
     */
    public function testStore(): void
    {
        Storage::fake('public');

        $productData = [
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100.00,
            'product_type' => 'Meal',
            'food_type' => 'veg',
            'screen' => '1', // Required field
            'status' => true,
        ];

        $image = UploadedFile::fake()->image('product.jpg');

        $product = Product::factory()->make([
            'pk_product_code' => 1,
            'name' => 'Test Product',
            'description' => 'Test Description',
            'unit_price' => 100.00,
            'product_type' => 'Meal',
            'food_type' => 'veg',
            'status' => true,
            'image_path' => 'products/test-image.jpg',
        ]);

        $this->productService->shouldReceive('createProduct')
            ->once()
            ->with(Mockery::on(function ($data) {
                return isset($data['name']) && $data['name'] === 'Test Product' &&
                       isset($data['description']) && $data['description'] === 'Test Description' &&
                       isset($data['unit_price']) && (float) $data['unit_price'] === 100.0 &&
                       isset($data['product_type']) && $data['product_type'] === 'Meal' &&
                       isset($data['food_type']) && $data['food_type'] === 'veg' &&
                       isset($data['screen']) && $data['screen'] === '1' &&
                       isset($data['status']) && (bool) $data['status'] === true;
            }))
            ->andReturn($product);

        $response = $this->postJson('/api/v2/quickserve/products', array_merge($productData, ['image' => $image]));

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => [
                    'name' => 'Test Product',
                    'description' => 'Test Description',
                    'unit_price' => '100.00',
                    'product_type' => 'Meal',
                    'food_type' => 'veg',
                    'status' => true,
                ],
            ]);
    }

    /**
     * Test updating a product.
     *
     * @return void
     */
    public function testUpdate(): void
    {
        Storage::fake('public');

        $updateData = [
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'unit_price' => 150.00,
        ];

        $image = UploadedFile::fake()->image('updated-product.jpg');

        $product = Product::factory()->make([
            'pk_product_code' => 1,
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'unit_price' => 150.00,
            'product_type' => 'Meal',
            'food_type' => 'veg',
            'status' => true,
            'image_path' => 'products/updated-image.jpg',
        ]);

        $this->productService->shouldReceive('updateProduct')
            ->once()
            ->with(1, Mockery::on(function ($data) use ($updateData) {
                return $data['name'] === $updateData['name'] &&
                       $data['description'] === $updateData['description'] &&
                       (float) $data['unit_price'] === (float) $updateData['unit_price'];
            }))
            ->andReturn($product);

        $response = $this->putJson('/api/v2/quickserve/products/1', array_merge($updateData, ['image' => $image]));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Product updated successfully',
            ]);
    }

    /**
     * Test updating a non-existent product.
     *
     * @return void
     */
    public function testUpdateNonExistentProduct(): void
    {
        $updateData = [
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'unit_price' => 150.00,
        ];

        $this->productService->shouldReceive('updateProduct')
            ->once()
            ->with(999, Mockery::any())
            ->andReturn(null);

        $response = $this->putJson('/api/v2/quickserve/products/999', $updateData);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Product not found',
            ]);
    }

    /**
     * Test deleting a product.
     *
     * @return void
     */
    public function testDestroy(): void
    {
        $this->productService->shouldReceive('deleteProduct')
            ->once()
            ->with(1)
            ->andReturn(true);

        $response = $this->deleteJson('/api/v2/quickserve/products/1');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Product deleted successfully',
            ]);
    }

    /**
     * Test deleting a non-existent product.
     *
     * @return void
     */
    public function testDestroyNonExistentProduct(): void
    {
        $this->productService->shouldReceive('deleteProduct')
            ->once()
            ->with(999)
            ->andReturn(false);

        $response = $this->deleteJson('/api/v2/quickserve/products/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Product not found',
            ]);
    }

    /**
     * Test updating product sequence.
     *
     * @return void
     */
    public function testUpdateSequence(): void
    {
        // Create actual products in the database for validation
        $product1 = Product::factory()->create(['pk_product_code' => 1]);
        $product2 = Product::factory()->create(['pk_product_code' => 2]);
        $product3 = Product::factory()->create(['pk_product_code' => 3]);

        $sequenceData = [
            'product_ids' => [3, 1, 2],
        ];

        $this->productService->shouldReceive('updateProductSequence')
            ->once()
            ->with([3, 1, 2])
            ->andReturn(true);

        $response = $this->postJson('/api/v2/quickserve/products/sequence', $sequenceData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Product sequence updated successfully',
            ]);
    }
}

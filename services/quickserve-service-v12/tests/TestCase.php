<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Tests;

use App\Models\User;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication, RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Ensure no active transactions (with safe check for mocked DB)
        try {
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
            }
        } catch (\BadMethodCallException $e) {
            // DB is mocked, skip transaction cleanup
        }

        // Create and authenticate a user for API tests
        if ($this->isApiTest()) {
            $user = User::factory()->create();
            $this->actingAs($user, 'sanctum');

            // Set default headers for API tests
            $this->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ]);
        }
    }

    /**
     * Check if this is an API test based on the test class name or namespace
     */
    protected function isApiTest(): bool
    {
        $class = get_class($this);
        return str_contains($class, 'Api\\') || str_contains($class, 'Feature\\Api');
    }

    protected function tearDown(): void
    {
        // Clean up any remaining transactions (with safe check for mocked DB)
        try {
            while (DB::transactionLevel() > 0) {
                DB::rollBack();
            }
        } catch (\BadMethodCallException $e) {
            // DB is mocked, skip transaction cleanup
        }

        parent::tearDown();
    }
}

# HTTP Client

This document describes the HTTP client implementation in the QuickServe microservices architecture.

## Overview

The HTTP client is a wrapper around Guzzle HTTP client that provides additional features such as circuit breaker, retries, timeouts, fallbacks, and correlation IDs. It is used for service-to-service communication in the QuickServe microservices architecture.

## Implementation

The HTTP client is implemented using the following components:

- `HttpClientInterface`: Defines the contract for HTTP clients
- `HttpClient`: Implements the HTTP client
- `CircuitBreakerInterface`: Used for circuit breaker functionality
- `HttpClientServiceProvider`: Registers HTTP clients in the service container

## Configuration

The HTTP client is configured in the `config/http_client.php` file:

```php
return [
    // The default timeout for requests in seconds
    'timeout' => env('HTTP_CLIENT_TIMEOUT', 30),

    // The default connection timeout for requests in seconds
    'connect_timeout' => env('HTTP_CLIENT_CONNECT_TIMEOUT', 10),

    // Whether to throw exceptions for HTTP errors
    'http_errors' => env('HTTP_CLIENT_HTTP_ERRORS', true),

    // Whether to verify SSL certificates
    'verify' => env('HTTP_CLIENT_VERIFY', true),

    // The default number of retries for failed requests
    'retries' => env('HTTP_CLIENT_RETRIES', 3),

    // Service-specific configurations
    'services' => [
        'auth' => [
            'base_url' => env('AUTH_SERVICE_URL', 'http://auth-service-v12:8000'),
            'timeout' => env('HTTP_CLIENT_AUTH_TIMEOUT', 10),
            'retries' => env('HTTP_CLIENT_AUTH_RETRIES', 2),
        ],
        'payment' => [
            'base_url' => env('PAYMENT_SERVICE_URL', 'http://payment-service-v12:8000'),
            'timeout' => env('HTTP_CLIENT_PAYMENT_TIMEOUT', 60),
            'retries' => env('HTTP_CLIENT_PAYMENT_RETRIES', 3),
        ],
        // ...
    ],
];
```

## Usage

### Basic Usage

```php
use App\Services\Http\HttpClientInterface;

class MyService
{
    protected $httpClient;

    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    public function getUser($userId)
    {
        $response = $this->httpClient->get("https://api.example.com/users/{$userId}");
        return $response['data'] ?? [];
    }
}
```

### Advanced Usage

```php
use App\Services\Http\HttpClientInterface;

class MyService
{
    protected $httpClient;

    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient->withService('auth');
    }

    public function getUser($userId)
    {
        $response = $this->httpClient
            ->withTimeout(10)
            ->withRetries(3)
            ->withCorrelationId('123e4567-e89b-12d3-a456-426614174000')
            ->withFallback(function () use ($userId) {
                return [
                    'status_code' => 200,
                    'data' => [
                        'id' => $userId,
                        'name' => 'Unknown User',
                        'email' => '<EMAIL>',
                        'is_fallback' => true,
                    ],
                ];
            })
            ->get("https://auth-service/api/v1/users/{$userId}");

        return $response['data'] ?? [];
    }
}
```

## Features

### Circuit Breaker

The HTTP client uses the circuit breaker pattern to prevent cascading failures in distributed systems. When a service is unavailable, the circuit breaker opens and prevents further requests to the service. After a timeout period, the circuit breaker transitions to half-open state and allows a limited number of requests to pass through. If these requests succeed, the circuit breaker closes again.

```php
$httpClient = $httpClient->withService('payment');
```

### Retries

The HTTP client automatically retries failed requests with exponential backoff. The number of retries can be configured globally or per request.

```php
$httpClient = $httpClient->withRetries(3);
```

### Timeouts

The HTTP client supports configurable timeouts for requests. The timeout can be configured globally or per request.

```php
$httpClient = $httpClient->withTimeout(10);
```

### Fallbacks

The HTTP client supports fallback functions that are executed when a request fails or when the circuit breaker is open. This allows for graceful degradation when a service is unavailable.

```php
$httpClient = $httpClient->withFallback(function () {
    return [
        'status_code' => 200,
        'data' => [
            'is_fallback' => true,
        ],
    ];
});
```

### Correlation IDs

The HTTP client supports correlation IDs for request tracing. The correlation ID is added to the request headers and can be used to trace requests across services.

```php
$httpClient = $httpClient->withCorrelationId('123e4567-e89b-12d3-a456-426614174000');
```

## Named HTTP Clients

The `HttpClientServiceProvider` registers named HTTP clients for different services:

```php
$this->app->bind("http_client.payment", function ($app) {
    $config = $app['config']->get('http_client', []);
    
    $client = new Client([
        'timeout' => $config['timeout'] ?? 30,
        'connect_timeout' => $config['connect_timeout'] ?? 10,
        'http_errors' => $config['http_errors'] ?? true,
        'verify' => $config['verify'] ?? true,
    ]);
    
    $circuitBreaker = $app->make("circuit_breaker.payment");
    
    return new HttpClient($client, $circuitBreaker);
});
```

You can use a named HTTP client by resolving it from the service container:

```php
$httpClient = app('http_client.payment');
```

## Logging

The HTTP client logs all requests and responses with the following information:

- Service name
- Method
- URL
- Status code
- Correlation ID
- Error message (if any)
- Retry attempt (if any)

## Testing

The HTTP client is tested using PHPUnit. The tests cover the following scenarios:

- Sending GET, POST, PUT, DELETE, and PATCH requests
- Handling request exceptions
- Using fallbacks when the circuit is open
- Retrying on failure
- Setting correlation IDs
- Setting timeouts
- Using named services

## Best Practices

1. **Use named HTTP clients**: Use named HTTP clients for different services to isolate failures.
2. **Set appropriate timeouts**: Set the timeout based on the expected response time of the service.
3. **Configure retries**: Adjust the number of retries based on the criticality of the service.
4. **Provide fallbacks**: Always provide a fallback function to handle cases when a service is unavailable.
5. **Use correlation IDs**: Use correlation IDs for request tracing across services.
6. **Monitor HTTP client metrics**: Monitor the HTTP client metrics to detect issues early.
7. **Test HTTP client behavior**: Test the HTTP client behavior in different scenarios.
8. **Implement graceful degradation**: Implement graceful degradation when a service is unavailable.

## References

- [Guzzle Documentation](https://docs.guzzlephp.org/en/stable/)
- [Circuit Breaker Pattern - Martin Fowler](https://martinfowler.com/bliki/CircuitBreaker.html)
- [Microservices Patterns - Chris Richardson](https://microservices.io/patterns/reliability/circuit-breaker.html)

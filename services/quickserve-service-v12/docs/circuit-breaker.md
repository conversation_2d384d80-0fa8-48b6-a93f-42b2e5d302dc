# Circuit Breaker Pattern

This document describes the implementation of the Circuit Breaker pattern in the QuickServe microservices architecture.

## Overview

The Circuit Breaker pattern is a design pattern used to detect failures and prevent cascading failures in distributed systems. It allows a service to fail fast and avoid overloading failing services, while also providing fallback mechanisms for graceful degradation.

## Implementation

The Circuit Breaker pattern is implemented in the QuickServe microservices architecture using the following components:

- `CircuitBreakerInterface`: Defines the contract for circuit breakers
- `CircuitBreaker`: Implements the circuit breaker pattern
- `CircuitBreakerOpenException`: Thrown when a circuit is open
- `CircuitBreakerServiceProvider`: Registers circuit breakers in the service container
- `HttpClient`: Uses circuit breakers for HTTP requests

## States

The circuit breaker has three states:

1. **Closed**: The circuit is closed and requests are allowed to pass through. This is the normal state.
2. **Open**: The circuit is open and requests are not allowed to pass through. This happens when the failure threshold is reached.
3. **Half-Open**: The circuit is half-open and a limited number of requests are allowed to pass through. This happens after the timeout period has elapsed.

## Configuration

The circuit breaker is configured in the `config/circuit_breaker.php` file:

```php
return [
    // The name of the service
    'service_name' => env('APP_NAME', 'quickserve'),

    // The number of consecutive failures required to open the circuit
    'failure_threshold' => env('CIRCUIT_BREAKER_FAILURE_THRESHOLD', 5),

    // The number of consecutive successes required to close the circuit
    'success_threshold' => env('CIRCUIT_BREAKER_SUCCESS_THRESHOLD', 2),

    // The number of seconds to wait before transitioning from open to half-open
    'timeout_seconds' => env('CIRCUIT_BREAKER_TIMEOUT_SECONDS', 30),

    // The prefix to use for cache keys
    'cache_prefix' => env('CIRCUIT_BREAKER_CACHE_PREFIX', 'circuit_breaker:'),

    // Service-specific configurations
    'services' => [
        'auth' => [
            'failure_threshold' => env('CIRCUIT_BREAKER_AUTH_FAILURE_THRESHOLD', 3),
            'success_threshold' => env('CIRCUIT_BREAKER_AUTH_SUCCESS_THRESHOLD', 2),
            'timeout_seconds' => env('CIRCUIT_BREAKER_AUTH_TIMEOUT_SECONDS', 60),
        ],
        'payment' => [
            'failure_threshold' => env('CIRCUIT_BREAKER_PAYMENT_FAILURE_THRESHOLD', 2),
            'success_threshold' => env('CIRCUIT_BREAKER_PAYMENT_SUCCESS_THRESHOLD', 3),
            'timeout_seconds' => env('CIRCUIT_BREAKER_PAYMENT_TIMEOUT_SECONDS', 120),
        ],
        // ...
    ],
];
```

## Usage

### Basic Usage

```php
use App\Services\CircuitBreaker\CircuitBreakerInterface;

class MyService
{
    protected $circuitBreaker;

    public function __construct(CircuitBreakerInterface $circuitBreaker)
    {
        $this->circuitBreaker = $circuitBreaker;
    }

    public function doSomething()
    {
        return $this->circuitBreaker->execute(
            function () {
                // Do something that might fail
                return 'success';
            },
            function () {
                // Fallback function when the circuit is open
                return 'fallback';
            }
        );
    }
}
```

### HTTP Client Usage

```php
use App\Services\Http\HttpClientInterface;

class MyService
{
    protected $httpClient;

    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient->withService('payment');
    }

    public function getPaymentStatus($transactionId)
    {
        return $this->httpClient
            ->withTimeout(10)
            ->withRetries(3)
            ->withFallback(function () {
                return [
                    'status_code' => 200,
                    'data' => [
                        'status' => 'unknown',
                        'is_fallback' => true,
                    ],
                ];
            })
            ->get("https://payment-service/api/v1/payments/transaction/{$transactionId}/status");
    }
}
```

## Named Circuit Breakers

The `CircuitBreakerServiceProvider` registers named circuit breakers for different services:

```php
$this->app->bind("circuit_breaker.payment", function ($app) {
    $config = $app['config']->get('circuit_breaker', []);
    
    return new CircuitBreaker(
        'payment',
        $settings['failure_threshold'] ?? $config['failure_threshold'] ?? 5,
        $settings['success_threshold'] ?? $config['success_threshold'] ?? 2,
        $settings['timeout_seconds'] ?? $config['timeout_seconds'] ?? 30,
        $config['cache_prefix'] ?? 'circuit_breaker:'
    );
});
```

You can use a named circuit breaker by resolving it from the service container:

```php
$circuitBreaker = app('circuit_breaker.payment');
```

## Metrics

The circuit breaker exposes the following metrics for monitoring:

- `circuit_breaker_state`: The current state of the circuit breaker (0 = closed, 1 = open, 2 = half-open)
- `circuit_breaker_failure_count`: The number of consecutive failures
- `circuit_breaker_success_count`: The number of consecutive successes
- `circuit_breaker_last_failure_time`: The timestamp of the last failure

## Testing

The circuit breaker is tested using PHPUnit. The tests cover the following scenarios:

- Executing a function successfully
- Handling a function that throws an exception
- Opening the circuit after reaching the failure threshold
- Using the fallback when the circuit is open
- Transitioning to half-open after the timeout
- Closing the circuit after reaching the success threshold in half-open state
- Resetting the circuit breaker
- Returning to open state if a failure occurs in half-open state

## Best Practices

1. **Use fallbacks**: Always provide a fallback function to handle cases when the circuit is open.
2. **Configure thresholds**: Adjust the failure and success thresholds based on the criticality of the service.
3. **Set appropriate timeouts**: Set the timeout based on how long you expect the service to be unavailable.
4. **Monitor circuit breaker state**: Monitor the state of the circuit breaker to detect issues early.
5. **Use named circuit breakers**: Use named circuit breakers for different services to isolate failures.
6. **Test circuit breaker behavior**: Test the circuit breaker behavior in different scenarios.
7. **Use with HTTP client**: Use the circuit breaker with the HTTP client for service-to-service communication.
8. **Implement graceful degradation**: Implement graceful degradation when a service is unavailable.

## References

- [Circuit Breaker Pattern - Martin Fowler](https://martinfowler.com/bliki/CircuitBreaker.html)
- [Release It! - Michael Nygard](https://pragprog.com/titles/mnee2/release-it-second-edition/)
- [Microservices Patterns - Chris Richardson](https://microservices.io/patterns/reliability/circuit-breaker.html)

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

return [
    /*
    |--------------------------------------------------------------------------
    | Circuit Breaker Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the circuit breaker.
    |
    */

    // The name of the service
    'service_name' => env('APP_NAME', 'quickserve'),

    // The number of consecutive failures required to open the circuit
    'failure_threshold' => env('CIRCUIT_BREAKER_FAILURE_THRESHOLD', 5),

    // The number of consecutive successes required to close the circuit
    'success_threshold' => env('CIRCUIT_BREAKER_SUCCESS_THRESHOLD', 2),

    // The number of seconds to wait before transitioning from open to half-open
    'timeout_seconds' => env('CIRCUIT_BREAKER_TIMEOUT_SECONDS', 30),

    // The prefix to use for cache keys
    'cache_prefix' => env('CIRCUIT_BREAKER_CACHE_PREFIX', 'circuit_breaker:'),

    // Service-specific configurations
    'services' => [
        'auth' => [
            'failure_threshold' => env('CIRCUIT_BREAKER_AUTH_FAILURE_THRESHOLD', 3),
            'success_threshold' => env('CIRCUIT_BREAKER_AUTH_SUCCESS_THRESHOLD', 2),
            'timeout_seconds' => env('CIRCUIT_BREAKER_AUTH_TIMEOUT_SECONDS', 60),
        ],
        'payment' => [
            'failure_threshold' => env('CIRCUIT_BREAKER_PAYMENT_FAILURE_THRESHOLD', 2),
            'success_threshold' => env('CIRCUIT_BREAKER_PAYMENT_SUCCESS_THRESHOLD', 3),
            'timeout_seconds' => env('CIRCUIT_BREAKER_PAYMENT_TIMEOUT_SECONDS', 120),
        ],
        'customer' => [
            'failure_threshold' => env('CIRCUIT_BREAKER_CUSTOMER_FAILURE_THRESHOLD', 3),
            'success_threshold' => env('CIRCUIT_BREAKER_CUSTOMER_SUCCESS_THRESHOLD', 2),
            'timeout_seconds' => env('CIRCUIT_BREAKER_CUSTOMER_TIMEOUT_SECONDS', 60),
        ],
        'kitchen' => [
            'failure_threshold' => env('CIRCUIT_BREAKER_KITCHEN_FAILURE_THRESHOLD', 5),
            'success_threshold' => env('CIRCUIT_BREAKER_KITCHEN_SUCCESS_THRESHOLD', 2),
            'timeout_seconds' => env('CIRCUIT_BREAKER_KITCHEN_TIMEOUT_SECONDS', 30),
        ],
        'delivery' => [
            'failure_threshold' => env('CIRCUIT_BREAKER_DELIVERY_FAILURE_THRESHOLD', 5),
            'success_threshold' => env('CIRCUIT_BREAKER_DELIVERY_SUCCESS_THRESHOLD', 2),
            'timeout_seconds' => env('CIRCUIT_BREAKER_DELIVERY_TIMEOUT_SECONDS', 30),
        ],
    ],
];

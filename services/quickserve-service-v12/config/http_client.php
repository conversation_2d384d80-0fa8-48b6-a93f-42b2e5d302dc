<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

return [
    /*
    |--------------------------------------------------------------------------
    | HTTP Client Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the HTTP client.
    |
    */

    // The default timeout for requests in seconds
    'timeout' => env('HTTP_CLIENT_TIMEOUT', 30),

    // The default connection timeout for requests in seconds
    'connect_timeout' => env('HTTP_CLIENT_CONNECT_TIMEOUT', 10),

    // Whether to throw exceptions for HTTP errors
    'http_errors' => env('HTTP_CLIENT_HTTP_ERRORS', true),

    // Whether to verify SSL certificates
    'verify' => env('HTTP_CLIENT_VERIFY', true),

    // The default number of retries for failed requests
    'retries' => env('HTTP_CLIENT_RETRIES', 3),

    // Service-specific configurations
    'services' => [
        'auth' => [
            'base_url' => env('AUTH_SERVICE_URL', 'http://auth-service-v12:8000'),
            'timeout' => env('HTTP_CLIENT_AUTH_TIMEOUT', 10),
            'retries' => env('HTTP_CLIENT_AUTH_RETRIES', 2),
        ],
        'payment' => [
            'base_url' => env('PAYMENT_SERVICE_URL', 'http://payment-service-v12:8000'),
            'timeout' => env('HTTP_CLIENT_PAYMENT_TIMEOUT', 60),
            'retries' => env('HTTP_CLIENT_PAYMENT_RETRIES', 3),
        ],
        'customer' => [
            'base_url' => env('CUSTOMER_SERVICE_URL', 'http://customer-service-v12:8000'),
            'timeout' => env('HTTP_CLIENT_CUSTOMER_TIMEOUT', 30),
            'retries' => env('HTTP_CLIENT_CUSTOMER_RETRIES', 3),
        ],
        'kitchen' => [
            'base_url' => env('KITCHEN_SERVICE_URL', 'http://kitchen-service-v12:8000'),
            'timeout' => env('HTTP_CLIENT_KITCHEN_TIMEOUT', 30),
            'retries' => env('HTTP_CLIENT_KITCHEN_RETRIES', 3),
        ],
        'delivery' => [
            'base_url' => env('DELIVERY_SERVICE_URL', 'http://delivery-service-v12:8000'),
            'timeout' => env('HTTP_CLIENT_DELIVERY_TIMEOUT', 30),
            'retries' => env('HTTP_CLIENT_DELIVERY_RETRIES', 3),
        ],
    ],
];

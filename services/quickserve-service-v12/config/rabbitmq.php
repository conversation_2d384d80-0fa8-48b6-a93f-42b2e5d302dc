<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

return [
    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Connection Configuration
    |--------------------------------------------------------------------------
    |
    | This array defines the RabbitMQ connection settings.
    |
    */
    'connection' => [
        'host' => env('RABBITMQ_HOST', 'rabbitmq'),
        'port' => env('RABBITMQ_PORT', 5672),
        'user' => env('RABBITMQ_USER', 'guest'),
        'password' => env('RABBITMQ_PASSWORD', 'guest'),
        'vhost' => env('RABBITMQ_VHOST', '/'),
        'ssl' => [
            'enabled' => env('RABBITMQ_SSL_ENABLED', false),
            'verify_peer' => env('RABBITMQ_SSL_VERIFY_PEER', true),
            'cafile' => env('RABBITMQ_SSL_CAFILE', null),
            'local_cert' => env('RABBITMQ_SSL_LOCAL_CERT', null),
            'local_key' => env('RABBITMQ_SSL_LOCAL_KEY', null),
            'passphrase' => env('RABBITMQ_SSL_PASSPHRASE', null),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Exchange Configuration
    |--------------------------------------------------------------------------
    |
    | This array defines the RabbitMQ exchange settings.
    |
    */
    'exchange' => [
        'name' => env('RABBITMQ_EXCHANGE_NAME', 'quickserve'),
        'type' => env('RABBITMQ_EXCHANGE_TYPE', 'topic'),
        'passive' => env('RABBITMQ_EXCHANGE_PASSIVE', false),
        'durable' => env('RABBITMQ_EXCHANGE_DURABLE', true),
        'auto_delete' => env('RABBITMQ_EXCHANGE_AUTO_DELETE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Queue Configuration
    |--------------------------------------------------------------------------
    |
    | This array defines the RabbitMQ queue settings.
    |
    */
    'queues' => [
        'order' => [
            'name' => env('RABBITMQ_QUEUE_ORDER_NAME', 'quickserve.order'),
            'passive' => env('RABBITMQ_QUEUE_ORDER_PASSIVE', false),
            'durable' => env('RABBITMQ_QUEUE_ORDER_DURABLE', true),
            'exclusive' => env('RABBITMQ_QUEUE_ORDER_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_QUEUE_ORDER_AUTO_DELETE', false),
            'routing_keys' => [
                'order.created',
                'order.updated',
                'order.cancelled',
                'order.status.updated',
                'order.payment.processed',
            ],
            'dead_letter_exchange' => env('RABBITMQ_QUEUE_ORDER_DLX', 'quickserve.dlx'),
            'dead_letter_routing_key' => env('RABBITMQ_QUEUE_ORDER_DLX_ROUTING_KEY', 'order.dead'),
            'message_ttl' => env('RABBITMQ_QUEUE_ORDER_TTL', ********), // 24 hours
        ],
        'product' => [
            'name' => env('RABBITMQ_QUEUE_PRODUCT_NAME', 'quickserve.product'),
            'passive' => env('RABBITMQ_QUEUE_PRODUCT_PASSIVE', false),
            'durable' => env('RABBITMQ_QUEUE_PRODUCT_DURABLE', true),
            'exclusive' => env('RABBITMQ_QUEUE_PRODUCT_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_QUEUE_PRODUCT_AUTO_DELETE', false),
            'routing_keys' => [
                'product.created',
                'product.updated',
                'product.deleted',
                'product.stock.updated',
            ],
            'dead_letter_exchange' => env('RABBITMQ_QUEUE_PRODUCT_DLX', 'quickserve.dlx'),
            'dead_letter_routing_key' => env('RABBITMQ_QUEUE_PRODUCT_DLX_ROUTING_KEY', 'product.dead'),
            'message_ttl' => env('RABBITMQ_QUEUE_PRODUCT_TTL', ********), // 24 hours
        ],
        'customer' => [
            'name' => env('RABBITMQ_QUEUE_CUSTOMER_NAME', 'quickserve.customer'),
            'passive' => env('RABBITMQ_QUEUE_CUSTOMER_PASSIVE', false),
            'durable' => env('RABBITMQ_QUEUE_CUSTOMER_DURABLE', true),
            'exclusive' => env('RABBITMQ_QUEUE_CUSTOMER_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_QUEUE_CUSTOMER_AUTO_DELETE', false),
            'routing_keys' => [
                'customer.created',
                'customer.updated',
                'customer.deleted',
                'customer.address.created',
                'customer.address.updated',
                'customer.address.deleted',
            ],
            'dead_letter_exchange' => env('RABBITMQ_QUEUE_CUSTOMER_DLX', 'quickserve.dlx'),
            'dead_letter_routing_key' => env('RABBITMQ_QUEUE_CUSTOMER_DLX_ROUTING_KEY', 'customer.dead'),
            'message_ttl' => env('RABBITMQ_QUEUE_CUSTOMER_TTL', ********), // 24 hours
        ],
        'timeslot' => [
            'name' => env('RABBITMQ_QUEUE_TIMESLOT_NAME', 'quickserve.timeslot'),
            'passive' => env('RABBITMQ_QUEUE_TIMESLOT_PASSIVE', false),
            'durable' => env('RABBITMQ_QUEUE_TIMESLOT_DURABLE', true),
            'exclusive' => env('RABBITMQ_QUEUE_TIMESLOT_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_QUEUE_TIMESLOT_AUTO_DELETE', false),
            'routing_keys' => [
                'timeslot.created',
                'timeslot.updated',
                'timeslot.deleted',
            ],
            'dead_letter_exchange' => env('RABBITMQ_QUEUE_TIMESLOT_DLX', 'quickserve.dlx'),
            'dead_letter_routing_key' => env('RABBITMQ_QUEUE_TIMESLOT_DLX_ROUTING_KEY', 'timeslot.dead'),
            'message_ttl' => env('RABBITMQ_QUEUE_TIMESLOT_TTL', ********), // 24 hours
        ],
        'location_mapping' => [
            'name' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_NAME', 'quickserve.location_mapping'),
            'passive' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_PASSIVE', false),
            'durable' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_DURABLE', true),
            'exclusive' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_AUTO_DELETE', false),
            'routing_keys' => [
                'location_mapping.created',
                'location_mapping.updated',
                'location_mapping.deleted',
            ],
            'dead_letter_exchange' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_DLX', 'quickserve.dlx'),
            'dead_letter_routing_key' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_DLX_ROUTING_KEY', 'location_mapping.dead'),
            'message_ttl' => env('RABBITMQ_QUEUE_LOCATION_MAPPING_TTL', ********), // 24 hours
        ],
        'backorder' => [
            'name' => env('RABBITMQ_QUEUE_BACKORDER_NAME', 'quickserve.backorder'),
            'passive' => env('RABBITMQ_QUEUE_BACKORDER_PASSIVE', false),
            'durable' => env('RABBITMQ_QUEUE_BACKORDER_DURABLE', true),
            'exclusive' => env('RABBITMQ_QUEUE_BACKORDER_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_QUEUE_BACKORDER_AUTO_DELETE', false),
            'routing_keys' => [
                'backorder.created',
                'backorder.updated',
                'backorder.deleted',
                'backorder.completed',
                'backorder.cancelled',
            ],
            'dead_letter_exchange' => env('RABBITMQ_QUEUE_BACKORDER_DLX', 'quickserve.dlx'),
            'dead_letter_routing_key' => env('RABBITMQ_QUEUE_BACKORDER_DLX_ROUTING_KEY', 'backorder.dead'),
            'message_ttl' => env('RABBITMQ_QUEUE_BACKORDER_TTL', ********), // 24 hours
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Dead Letter Exchange Configuration
    |--------------------------------------------------------------------------
    |
    | This array defines the RabbitMQ dead letter exchange settings.
    |
    */
    'dead_letter_exchange' => [
        'name' => env('RABBITMQ_DLX_NAME', 'quickserve.dlx'),
        'type' => env('RABBITMQ_DLX_TYPE', 'topic'),
        'passive' => env('RABBITMQ_DLX_PASSIVE', false),
        'durable' => env('RABBITMQ_DLX_DURABLE', true),
        'auto_delete' => env('RABBITMQ_DLX_AUTO_DELETE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Dead Letter Queue Configuration
    |--------------------------------------------------------------------------
    |
    | This array defines the RabbitMQ dead letter queue settings.
    |
    */
    'dead_letter_queues' => [
        'order' => [
            'name' => env('RABBITMQ_DLQ_ORDER_NAME', 'quickserve.order.dlq'),
            'passive' => env('RABBITMQ_DLQ_ORDER_PASSIVE', false),
            'durable' => env('RABBITMQ_DLQ_ORDER_DURABLE', true),
            'exclusive' => env('RABBITMQ_DLQ_ORDER_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_DLQ_ORDER_AUTO_DELETE', false),
            'routing_key' => env('RABBITMQ_DLQ_ORDER_ROUTING_KEY', 'order.dead'),
        ],
        'product' => [
            'name' => env('RABBITMQ_DLQ_PRODUCT_NAME', 'quickserve.product.dlq'),
            'passive' => env('RABBITMQ_DLQ_PRODUCT_PASSIVE', false),
            'durable' => env('RABBITMQ_DLQ_PRODUCT_DURABLE', true),
            'exclusive' => env('RABBITMQ_DLQ_PRODUCT_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_DLQ_PRODUCT_AUTO_DELETE', false),
            'routing_key' => env('RABBITMQ_DLQ_PRODUCT_ROUTING_KEY', 'product.dead'),
        ],
        'customer' => [
            'name' => env('RABBITMQ_DLQ_CUSTOMER_NAME', 'quickserve.customer.dlq'),
            'passive' => env('RABBITMQ_DLQ_CUSTOMER_PASSIVE', false),
            'durable' => env('RABBITMQ_DLQ_CUSTOMER_DURABLE', true),
            'exclusive' => env('RABBITMQ_DLQ_CUSTOMER_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_DLQ_CUSTOMER_AUTO_DELETE', false),
            'routing_key' => env('RABBITMQ_DLQ_CUSTOMER_ROUTING_KEY', 'customer.dead'),
        ],
        'timeslot' => [
            'name' => env('RABBITMQ_DLQ_TIMESLOT_NAME', 'quickserve.timeslot.dlq'),
            'passive' => env('RABBITMQ_DLQ_TIMESLOT_PASSIVE', false),
            'durable' => env('RABBITMQ_DLQ_TIMESLOT_DURABLE', true),
            'exclusive' => env('RABBITMQ_DLQ_TIMESLOT_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_DLQ_TIMESLOT_AUTO_DELETE', false),
            'routing_key' => env('RABBITMQ_DLQ_TIMESLOT_ROUTING_KEY', 'timeslot.dead'),
        ],
        'location_mapping' => [
            'name' => env('RABBITMQ_DLQ_LOCATION_MAPPING_NAME', 'quickserve.location_mapping.dlq'),
            'passive' => env('RABBITMQ_DLQ_LOCATION_MAPPING_PASSIVE', false),
            'durable' => env('RABBITMQ_DLQ_LOCATION_MAPPING_DURABLE', true),
            'exclusive' => env('RABBITMQ_DLQ_LOCATION_MAPPING_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_DLQ_LOCATION_MAPPING_AUTO_DELETE', false),
            'routing_key' => env('RABBITMQ_DLQ_LOCATION_MAPPING_ROUTING_KEY', 'location_mapping.dead'),
        ],
        'backorder' => [
            'name' => env('RABBITMQ_DLQ_BACKORDER_NAME', 'quickserve.backorder.dlq'),
            'passive' => env('RABBITMQ_DLQ_BACKORDER_PASSIVE', false),
            'durable' => env('RABBITMQ_DLQ_BACKORDER_DURABLE', true),
            'exclusive' => env('RABBITMQ_DLQ_BACKORDER_EXCLUSIVE', false),
            'auto_delete' => env('RABBITMQ_DLQ_BACKORDER_AUTO_DELETE', false),
            'routing_key' => env('RABBITMQ_DLQ_BACKORDER_ROUTING_KEY', 'backorder.dead'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Consumer Configuration
    |--------------------------------------------------------------------------
    |
    | This array defines the RabbitMQ consumer settings.
    |
    */
    'consumer' => [
        'tag' => env('RABBITMQ_CONSUMER_TAG', 'quickserve_consumer'),
        'prefetch_count' => env('RABBITMQ_CONSUMER_PREFETCH_COUNT', 10),
        'prefetch_size' => env('RABBITMQ_CONSUMER_PREFETCH_SIZE', 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Retry Configuration
    |--------------------------------------------------------------------------
    |
    | This array defines the RabbitMQ retry settings.
    |
    */
    'retry' => [
        'enabled' => env('RABBITMQ_RETRY_ENABLED', true),
        'max_attempts' => env('RABBITMQ_RETRY_MAX_ATTEMPTS', 3),
        'initial_interval' => env('RABBITMQ_RETRY_INITIAL_INTERVAL', 5000), // 5 seconds
        'multiplier' => env('RABBITMQ_RETRY_MULTIPLIER', 2),
        'max_interval' => env('RABBITMQ_RETRY_MAX_INTERVAL', 60000), // 60 seconds
    ],
];

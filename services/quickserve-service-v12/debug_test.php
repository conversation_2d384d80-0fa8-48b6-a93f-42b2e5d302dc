<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use App\Models\Order;

class DebugTest extends TestCase
{
    use RefreshDatabase;

    public function createApplication()
    {
        $app = require __DIR__.'/bootstrap/app.php';
        return $app;
    }

    public function testWalletPaymentDebug()
    {
        // Mock the HTTP client for the Customer service
        Http::fake([
            'http://customer-service/customers/CUST123' => Http::response([
                'success' => true,
                'data' => [
                    'customer_code' => 'CUST123',
                    'customer_name' => 'Test Customer',
                    'email_address' => '<EMAIL>',
                    'phone' => '1234567890',
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'country' => 'Test Country',
                    'pincode' => '123456',
                    'wallet_balance' => 100.00,
                ],
            ], 200),
            'http://customer-service/customers/CUST123/wallet' => Http::response([
                'success' => true,
                'data' => [
                    'balance' => 200.00,
                ],
            ], 200),
            'http://customer-service/customers/CUST123/wallet/deduct' => Http::response([
                'success' => true,
                'data' => [
                    'balance' => 83.00,
                ],
            ], 200),
        ]);

        // Create an order first
        $orderData = [
            'customer_code' => 'CUST123',
            'customer_name' => 'Test Customer',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Office',
            'product_code' => 'PROD123',
            'product_name' => 'Test Product',
            'product_type' => 'Food',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => now()->format('Y-m-d'),
            'ship_address' => '123 Test St, Test City',
            'order_menu' => 'Lunch',
            'company_id' => 1,
            'unit_id' => 1,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
        ];

        $response = $this->postJson('/api/v2/quickserve/orders', $orderData);
        echo "Order creation response: " . $response->getContent() . "\n";
        
        if ($response->status() !== 201) {
            echo "Order creation failed\n";
            return;
        }

        $orderId = $response->json('data.id');
        $totalAmount = 117.00; // 100 + 10 + 5 + 2

        // Process payment
        $paymentData = [
            'gateway' => 'wallet',
            'wallet_amount' => $totalAmount,
        ];

        $response = $this->postJson("/api/v2/quickserve/orders/{$orderId}/payment", $paymentData);
        echo "Payment response status: " . $response->status() . "\n";
        echo "Payment response: " . $response->getContent() . "\n";
    }
}

$test = new DebugTest();
$test->setUp();
$test->testWalletPaymentDebug();

APP_NAME=QuickServeService
APP_ENV=testing
APP_KEY=base64:EOP7N48XZjCviS1DbXzTBI8i+fMtooOWCGCxZYTd3cg=
APP_DEBUG=true
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_STACK=single
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_DATABASE=:memory:

SESSION_DRIVER=array
SESSION_LIFETIME=120

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=array

MAIL_MAILER=array

# Microservices URLs (mocked for testing)
AUTH_SERVICE_URL=http://localhost:8001/api
CUSTOMER_SERVICE_URL=http://localhost:8002/api
MEAL_SERVICE_URL=http://localhost:8003/api
PAYMENT_SERVICE_URL=http://localhost:8004/api
SUBSCRIPTION_SERVICE_URL=http://localhost:8005/api
KITCHEN_SERVICE_URL=http://localhost:8006/api
DELIVERY_SERVICE_URL=http://localhost:8007/api

# RabbitMQ (disabled for testing)
RABBITMQ_ENABLED=false
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

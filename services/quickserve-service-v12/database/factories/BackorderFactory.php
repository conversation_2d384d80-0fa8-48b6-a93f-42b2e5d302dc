<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Factories;

use App\Models\Backorder;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Backorder>
 */
class BackorderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Backorder::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $orderNo = 'ORD' . $this->faker->unique()->numberBetween(10000, 99999);
        $statuses = ['pending', 'completed', 'cancelled'];
        $menuTypes = ['Breakfast', 'Lunch', 'Dinner', 'Snacks'];
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'order_id' => $this->faker->numberBetween(1, 1000),
            'order_no' => $orderNo,
            'customer_id' => $this->faker->numberBetween(1, 1000),
            'product_id' => $this->faker->numberBetween(1, 1000),
            'product_name' => $this->faker->word() . ' ' . $this->faker->randomElement(['Meal', 'Dish', 'Platter', 'Special']),
            'quantity' => $this->faker->numberBetween(1, 5),
            'amount' => $this->faker->randomFloat(2, 50, 500),
            'order_date' => $this->faker->date(),
            'order_menu' => $this->faker->randomElement($menuTypes),
            'reason' => $this->faker->randomElement(['Out of stock', 'Unavailable', 'Customer request', 'Quality issue']),
            'status' => $this->faker->randomElement($statuses),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the backorder is pending.
     *
     * @return static
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the backorder is completed.
     *
     * @return static
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }

    /**
     * Indicate that the backorder is cancelled.
     *
     * @return static
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }

    /**
     * Indicate that the backorder is for a specific order.
     *
     * @param int $orderId
     * @param string $orderNo
     * @return static
     */
    public function forOrder(int $orderId, string $orderNo): static
    {
        return $this->state(fn (array $attributes) => [
            'order_id' => $orderId,
            'order_no' => $orderNo,
        ]);
    }

    /**
     * Indicate that the backorder is for a specific customer.
     *
     * @param int $customerId
     * @return static
     */
    public function forCustomer(int $customerId): static
    {
        return $this->state(fn (array $attributes) => [
            'customer_id' => $customerId,
        ]);
    }

    /**
     * Indicate that the backorder is for a specific product.
     *
     * @param int $productId
     * @param string $productName
     * @return static
     */
    public function forProduct(int $productId, string $productName): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $productId,
            'product_name' => $productName,
        ]);
    }
}

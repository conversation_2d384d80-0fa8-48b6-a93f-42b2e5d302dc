<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_no' => 'ORD' . $this->faker->unique()->numerify('##########'),
            'ref_order' => null,
            'fk_kitchen_code' => $this->faker->numberBetween(1, 10),
            'auth_id' => $this->faker->numberBetween(1, 100),
            'customer_code' => $this->faker->numberBetween(1, 100),
            'customer_name' => $this->faker->name,
            'email_address' => $this->faker->safeEmail,
            'phone' => $this->faker->phoneNumber,
            'location_code' => $this->faker->numberBetween(1, 20),
            'location_name' => $this->faker->city,
            'city' => $this->faker->city,
            'city_name' => $this->faker->city,
            'product_code' => $this->faker->numberBetween(1, 50),
            'product_name' => $this->faker->word,
            'product_description' => $this->faker->sentence,
            'product_type' => $this->faker->randomElement(['Meal', 'Extra', 'Beverage']),
            'quantity' => $this->faker->numberBetween(1, 5),
            'order_type' => $this->faker->randomElement(['Regular', 'Subscription']),
            'order_days' => null,
            'promo_code' => null,
            'amount' => $this->faker->randomFloat(2, 50, 500),
            'tax' => $this->faker->randomFloat(2, 5, 50),
            'delivery_charges' => $this->faker->randomFloat(2, 10, 50),
            'service_charges' => $this->faker->randomFloat(2, 5, 20),
            'line_delivery_charges' => null,
            'applied_discount' => $this->faker->randomFloat(2, 0, 50),
            'total_applied_discount' => $this->faker->randomFloat(2, 0, 50),
            'order_status' => $this->faker->randomElement(['New', 'Processing', 'Completed', 'Cancel']),
            'order_date' => $this->faker->date(),
            'ship_address' => $this->faker->address,
            'delivery_status' => $this->faker->randomElement(['Pending', 'Dispatched', 'Delivered', 'Failed']),
            'invoice_status' => $this->faker->randomElement(['Unbill', 'Billed']),
            'order_menu' => $this->faker->randomElement(['Breakfast', 'Lunch', 'Dinner']),
            'food_type' => $this->faker->randomElement(['veg', 'non-veg', 'jain']),
            'payment_mode' => $this->faker->randomElement(['Cash', 'Card', 'UPI', 'Wallet']),
            'amount_paid' => $this->faker->randomFloat(2, 50, 500),
            'tax_method' => $this->faker->randomElement(['inclusive', 'exclusive']),
            'source' => $this->faker->randomElement(['Web', 'App', 'Admin']),
            'prefered_delivery_person_id' => null,
            'delivery_person' => null,
            'tp_delivery' => $this->faker->boolean,
            'tp_delivery_charges' => null,
            'tp_delivery_charges_type' => null,
            'tp_delivery_order_id' => null,
            'tp_aggregator' => null,
            'tp_aggregator_charges' => null,
            'tp_aggregator_charges_type' => null,
            'delivery_time' => $this->faker->time(),
            'delivery_end_time' => null,
            'item_preference' => null,
            'remark' => $this->faker->sentence,
            'pause_limit' => null,
            'food_preference' => null,
            'company_id' => 1,
            'unit_id' => 1,
            'created_date' => now(),
        ];
    }

    /**
     * Indicate that the order is new.
     *
     * @return static
     */
    public function newOrder()
    {
        return $this->state(function (array $attributes) {
            return [
                'order_status' => 'New',
                'delivery_status' => 'Pending',
            ];
        });
    }

    /**
     * Indicate that the order is processing.
     *
     * @return static
     */
    public function processing()
    {
        return $this->state(function (array $attributes) {
            return [
                'order_status' => 'Processing',
                'delivery_status' => 'Pending',
            ];
        });
    }

    /**
     * Indicate that the order is completed.
     *
     * @return static
     */
    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'order_status' => 'Completed',
                'delivery_status' => 'Delivered',
            ];
        });
    }

    /**
     * Indicate that the order is cancelled.
     *
     * @return static
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'order_status' => 'Cancel',
            ];
        });
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Factories;

use App\Models\Timeslot;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Timeslot>
 */
class TimeslotFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Timeslot::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        $menuTypes = ['Breakfast', 'Lunch', 'Dinner', 'Snacks'];
        $kitchens = ['Main', 'Secondary', 'Dessert', 'Beverage'];
        
        $startHour = $this->faker->numberBetween(8, 20);
        $endHour = $startHour + $this->faker->numberBetween(1, 3);
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'starttime' => sprintf('%02d:00:00', $startHour),
            'endtime' => sprintf('%02d:00:00', $endHour),
            'day' => $this->faker->randomElement($days),
            'menu_type' => $this->faker->randomElement($menuTypes),
            'kitchen' => $this->faker->randomElement($kitchens),
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the timeslot is inactive.
     *
     * @return static
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }

    /**
     * Indicate that the timeslot is for breakfast.
     *
     * @return static
     */
    public function breakfast(): static
    {
        return $this->state(fn (array $attributes) => [
            'menu_type' => 'Breakfast',
            'starttime' => '08:00:00',
            'endtime' => '10:00:00',
        ]);
    }

    /**
     * Indicate that the timeslot is for lunch.
     *
     * @return static
     */
    public function lunch(): static
    {
        return $this->state(fn (array $attributes) => [
            'menu_type' => 'Lunch',
            'starttime' => '12:00:00',
            'endtime' => '14:00:00',
        ]);
    }

    /**
     * Indicate that the timeslot is for dinner.
     *
     * @return static
     */
    public function dinner(): static
    {
        return $this->state(fn (array $attributes) => [
            'menu_type' => 'Dinner',
            'starttime' => '18:00:00',
            'endtime' => '20:00:00',
        ]);
    }
}

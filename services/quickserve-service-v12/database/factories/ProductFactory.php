<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->word,
            'description' => $this->faker->sentence,
            'recipe' => $this->faker->paragraph,
            'unit_price' => $this->faker->randomFloat(2, 50, 500),
            'product_type' => $this->faker->randomElement(['Meal', 'Extra', 'Beverage']),
            'product_category' => $this->faker->word,
            'category' => $this->faker->word,
            'screen' => $this->faker->numberBetween(1, 10),
            'threshold' => $this->faker->numberBetween(5, 20),
            'max_quantity_per_meal' => $this->faker->numberBetween(1, 5),
            'quantity' => $this->faker->randomFloat(2, 1, 100),
            'unit' => $this->faker->randomElement(['pcs', 'kg', 'g', 'ml']),
            'status' => $this->faker->boolean(80), // 80% chance of being active
            'kitchen_code' => $this->faker->numberBetween(1, 10),
            'product_subtype' => $this->faker->word,
            'food_type' => $this->faker->randomElement(['veg', 'non-veg', 'jain']),
            'image_path' => null,
            'is_swappable' => $this->faker->boolean(30), // 30% chance of being swappable
            'swap_with' => null,
            'swap_charges' => $this->faker->randomFloat(2, 10, 50),
            'sequence' => $this->faker->numberBetween(1, 100),
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }

    /**
     * Indicate that the product is a meal.
     *
     * @return static
     */
    public function meal()
    {
        return $this->state(function (array $attributes) {
            return [
                'product_type' => 'Meal',
                'product_category' => 'Regular',
            ];
        });
    }

    /**
     * Indicate that the product is an extra.
     *
     * @return static
     */
    public function extra()
    {
        return $this->state(function (array $attributes) {
            return [
                'product_type' => 'Extra',
                'product_category' => 'Add-on',
            ];
        });
    }

    /**
     * Indicate that the product is a beverage.
     *
     * @return static
     */
    public function beverage()
    {
        return $this->state(function (array $attributes) {
            return [
                'product_type' => 'Beverage',
                'product_category' => 'Drink',
            ];
        });
    }

    /**
     * Indicate that the product is vegetarian.
     *
     * @return static
     */
    public function vegetarian()
    {
        return $this->state(function (array $attributes) {
            return [
                'food_type' => 'veg',
            ];
        });
    }

    /**
     * Indicate that the product is non-vegetarian.
     *
     * @return static
     */
    public function nonVegetarian()
    {
        return $this->state(function (array $attributes) {
            return [
                'food_type' => 'non-veg',
            ];
        });
    }

    /**
     * Indicate that the product is jain.
     *
     * @return static
     */
    public function jain()
    {
        return $this->state(function (array $attributes) {
            return [
                'food_type' => 'jain',
            ];
        });
    }

    /**
     * Indicate that the product is swappable.
     *
     * @return static
     */
    public function swappable()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_swappable' => true,
                'swap_charges' => $this->faker->randomFloat(2, 10, 50),
            ];
        });
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Factories;

use App\Models\LocationMapping;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LocationMapping>
 */
class LocationMappingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LocationMapping::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $locationCode = 'LOC' . $this->faker->unique()->numberBetween(1000, 9999);
        $cityCode = 'CITY' . $this->faker->numberBetween(100, 999);
        $kitchenCode = 'KITCHEN' . $this->faker->numberBetween(100, 999);
        
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'location_code' => $locationCode,
            'location_name' => $this->faker->city(),
            'city_code' => $cityCode,
            'city_name' => $this->faker->city(),
            'delivery_charges' => $this->faker->randomFloat(2, 5, 20),
            'delivery_time' => (string) $this->faker->numberBetween(15, 60),
            'kitchen_code' => $kitchenCode,
            'kitchen_name' => $this->faker->word() . ' Kitchen',
            'description' => $this->faker->sentence(),
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the location mapping is inactive.
     *
     * @return static
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }

    /**
     * Indicate that the location mapping is for a specific city.
     *
     * @param string $cityCode
     * @param string $cityName
     * @return static
     */
    public function forCity(string $cityCode, string $cityName): static
    {
        return $this->state(fn (array $attributes) => [
            'city_code' => $cityCode,
            'city_name' => $cityName,
        ]);
    }

    /**
     * Indicate that the location mapping is for a specific kitchen.
     *
     * @param string $kitchenCode
     * @param string $kitchenName
     * @return static
     */
    public function forKitchen(string $kitchenCode, string $kitchenName): static
    {
        return $this->state(fn (array $attributes) => [
            'kitchen_code' => $kitchenCode,
            'kitchen_name' => $kitchenName,
        ]);
    }

    /**
     * Indicate that the location mapping has free delivery.
     *
     * @return static
     */
    public function freeDelivery(): static
    {
        return $this->state(fn (array $attributes) => [
            'delivery_charges' => 0,
        ]);
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Factories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_name' => $this->faker->name,
            'phone' => $this->faker->unique()->phoneNumber,
            'email_address' => $this->faker->unique()->safeEmail,
            'customer_Address' => $this->faker->address,
            'location_code' => $this->faker->numberBetween(1, 20),
            'location_name' => $this->faker->city,
            'lunch_loc_code' => $this->faker->numberBetween(1, 20),
            'lunch_loc_name' => $this->faker->city,
            'lunch_add' => $this->faker->address,
            'dinner_loc_code' => $this->faker->numberBetween(1, 20),
            'dinner_loc_name' => $this->faker->city,
            'dinner_add' => $this->faker->address,
            'food_preference' => $this->faker->randomElement(['veg', 'non-veg', 'jain']),
            'city' => $this->faker->city,
            'city_name' => $this->faker->city,
            'company_name' => $this->faker->company,
            'group_code' => null,
            'group_name' => null,
            'registered_on' => now(),
            'registered_from' => $this->faker->randomElement(['Web', 'App', 'Admin']),
            'food_referance' => null,
            'status' => $this->faker->boolean(80), // 80% chance of being active
            'otp' => null,
            'password' => Hash::make('password'),
            'thirdparty' => null,
            'phone_verified' => $this->faker->boolean(70), // 70% chance of being verified
            'subscription_notification' => $this->faker->boolean,
            'email_verified' => $this->faker->boolean(50), // 50% chance of being verified
            'source' => $this->faker->randomElement(['Web', 'App', 'Admin']),
            'referer' => null,
            'gcm_id' => null,
            'alt_phone' => $this->faker->phoneNumber,
            'dabbawala_code_type' => null,
            'dabbawala_code' => null,
            'dabbawala_image' => null,
            'isguest' => $this->faker->boolean(10), // 10% chance of being a guest
            'delivery_note' => $this->faker->sentence,
            'dabba_status' => $this->faker->boolean(30), // 30% chance of having dabba status
            'company_id' => 1,
            'unit_id' => 1,
            'modified_on' => now(),
        ];
    }

    /**
     * Indicate that the customer is active.
     *
     * @return static
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => true,
            ];
        });
    }

    /**
     * Indicate that the customer is inactive.
     *
     * @return static
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => false,
            ];
        });
    }

    /**
     * Indicate that the customer has verified phone.
     *
     * @return static
     */
    public function phoneVerified()
    {
        return $this->state(function (array $attributes) {
            return [
                'phone_verified' => true,
            ];
        });
    }

    /**
     * Indicate that the customer has verified email.
     *
     * @return static
     */
    public function emailVerified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified' => true,
            ];
        });
    }

    /**
     * Indicate that the customer is a guest.
     *
     * @return static
     */
    public function guest()
    {
        return $this->state(function (array $attributes) {
            return [
                'isguest' => true,
                'password' => null,
            ];
        });
    }

    /**
     * Indicate that the customer prefers vegetarian food.
     *
     * @return static
     */
    public function vegetarian()
    {
        return $this->state(function (array $attributes) {
            return [
                'food_preference' => 'veg',
            ];
        });
    }

    /**
     * Indicate that the customer prefers non-vegetarian food.
     *
     * @return static
     */
    public function nonVegetarian()
    {
        return $this->state(function (array $attributes) {
            return [
                'food_preference' => 'non-veg',
            ];
        });
    }

    /**
     * Indicate that the customer prefers jain food.
     *
     * @return static
     */
    public function jain()
    {
        return $this->state(function (array $attributes) {
            return [
                'food_preference' => 'jain',
            ];
        });
    }
}

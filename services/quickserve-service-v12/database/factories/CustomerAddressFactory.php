<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Factories;

use App\Models\CustomerAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerAddress>
 */
class CustomerAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'fk_customer_code' => $this->faker->numberBetween(1, 100),
            'address' => $this->faker->address,
            'location_code' => $this->faker->numberBetween(1, 20),
            'location_name' => $this->faker->city,
            'city' => $this->faker->city,
            'city_name' => $this->faker->city,
            'menu_type' => $this->faker->randomElement(['Breakfast', 'Lunch', 'Dinner']),
            'address_type' => $this->faker->randomElement(['Home', 'Office', 'Other']),
            'landmark' => $this->faker->sentence(3),
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'is_default' => $this->faker->boolean(20), // 20% chance of being default
            'created_on' => now(),
            'modified_on' => now(),
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }

    /**
     * Indicate that the address is default.
     *
     * @return static
     */
    public function default()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_default' => true,
            ];
        });
    }

    /**
     * Indicate that the address is for home.
     *
     * @return static
     */
    public function home()
    {
        return $this->state(function (array $attributes) {
            return [
                'address_type' => 'Home',
            ];
        });
    }

    /**
     * Indicate that the address is for office.
     *
     * @return static
     */
    public function office()
    {
        return $this->state(function (array $attributes) {
            return [
                'address_type' => 'Office',
            ];
        });
    }

    /**
     * Indicate that the address is for lunch.
     *
     * @return static
     */
    public function lunch()
    {
        return $this->state(function (array $attributes) {
            return [
                'menu_type' => 'Lunch',
            ];
        });
    }

    /**
     * Indicate that the address is for dinner.
     *
     * @return static
     */
    public function dinner()
    {
        return $this->state(function (array $attributes) {
            return [
                'menu_type' => 'Dinner',
            ];
        });
    }

    /**
     * Indicate that the address is for breakfast.
     *
     * @return static
     */
    public function breakfast()
    {
        return $this->state(function (array $attributes) {
            return [
                'menu_type' => 'Breakfast',
            ];
        });
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_address', function (Blueprint $table) {
            $table->id('pk_customer_address_code');
            $table->unsignedBigInteger('fk_customer_code');
            $table->text('address');
            $table->string('location_code')->nullable();
            $table->string('location_name')->nullable();
            $table->string('city')->nullable();
            $table->string('city_name')->nullable();
            $table->string('menu_type')->nullable();
            $table->string('address_type')->nullable();
            $table->string('landmark')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->boolean('is_default')->default(0);
            $table->timestamp('created_on')->nullable();
            $table->timestamp('modified_on')->nullable();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            
            $table->foreign('fk_customer_code')->references('pk_customer_code')->on('customers')->onDelete('cascade');
            
            $table->index('fk_customer_code');
            $table->index('menu_type');
            $table->index('address_type');
            $table->index('is_default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_address');
    }
};

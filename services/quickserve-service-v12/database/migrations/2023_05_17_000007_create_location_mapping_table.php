<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('location_mapping', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->string('location_code');
            $table->string('location_name');
            $table->string('city_code')->nullable();
            $table->string('city_name')->nullable();
            $table->decimal('delivery_charges', 10, 2)->default(0);
            $table->string('delivery_time')->default('30');
            $table->string('kitchen_code')->nullable();
            $table->string('kitchen_name')->nullable();
            $table->text('description')->nullable();
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
            
            $table->index('location_code');
            $table->index('city_code');
            $table->index('kitchen_code');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('location_mapping');
    }
};

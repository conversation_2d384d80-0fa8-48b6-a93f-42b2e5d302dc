<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id('pk_product_code');
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('recipe')->nullable();
            $table->decimal('unit_price', 10, 2);
            $table->string('product_type');
            $table->string('product_category')->nullable();
            $table->string('category')->nullable();
            $table->string('screen');
            $table->integer('threshold')->nullable();
            $table->integer('max_quantity_per_meal')->default(1);
            $table->decimal('quantity', 10, 2)->nullable();
            $table->string('unit')->nullable();
            $table->boolean('status')->default(1);
            $table->string('kitchen_code')->nullable();
            $table->string('product_subtype')->nullable();
            $table->string('food_type')->nullable();
            $table->string('image_path')->nullable();
            $table->boolean('is_swappable')->default(0);
            $table->string('swap_with')->nullable();
            $table->decimal('swap_charges', 10, 2)->nullable();
            $table->integer('sequence')->nullable();
            $table->boolean('is_featured')->default(0);
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            
            $table->index('product_type');
            $table->index('food_type');
            $table->index('screen');
            $table->index('product_category');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};

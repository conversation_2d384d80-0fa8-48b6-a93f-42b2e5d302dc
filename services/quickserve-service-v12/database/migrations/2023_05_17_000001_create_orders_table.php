<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id('pk_order_no');
            $table->string('order_no')->unique();
            $table->unsignedBigInteger('ref_order')->nullable();
            $table->unsignedBigInteger('fk_kitchen_code')->nullable();
            $table->unsignedBigInteger('auth_id')->nullable();
            $table->unsignedBigInteger('customer_code');
            $table->string('customer_name');
            $table->string('email_address')->nullable();
            $table->string('phone');
            $table->unsignedBigInteger('location_code');
            $table->string('location_name');
            $table->string('city')->nullable();
            $table->string('city_name')->nullable();
            $table->unsignedBigInteger('product_code');
            $table->string('product_name');
            $table->text('product_description')->nullable();
            $table->string('product_type');
            $table->integer('quantity');
            $table->string('order_type')->nullable();
            $table->string('order_days')->nullable();
            $table->string('promo_code')->nullable();
            $table->decimal('amount', 10, 2);
            $table->decimal('tax', 10, 2)->nullable();
            $table->decimal('delivery_charges', 10, 2)->nullable();
            $table->decimal('service_charges', 10, 2)->nullable();
            $table->decimal('line_delivery_charges', 10, 2)->nullable();
            $table->decimal('applied_discount', 10, 2)->nullable();
            $table->decimal('total_applied_discount', 10, 2)->nullable();
            $table->string('order_status');
            $table->date('order_date');
            $table->text('ship_address');
            $table->string('delivery_status');
            $table->string('invoice_status');
            $table->string('order_menu');
            $table->string('food_type')->nullable();
            $table->string('payment_mode')->nullable();
            $table->decimal('amount_paid', 10, 2)->nullable();
            $table->string('tax_method')->nullable();
            $table->string('source')->nullable();
            $table->string('prefered_delivery_person_id')->nullable();
            $table->string('delivery_person')->nullable();
            $table->boolean('tp_delivery')->nullable();
            $table->decimal('tp_delivery_charges', 10, 2)->nullable();
            $table->string('tp_delivery_charges_type')->nullable();
            $table->string('tp_delivery_order_id')->nullable();
            $table->string('tp_aggregator')->nullable();
            $table->decimal('tp_aggregator_charges', 10, 2)->nullable();
            $table->string('tp_aggregator_charges_type')->nullable();
            $table->string('delivery_time')->nullable();
            $table->string('delivery_end_time')->nullable();
            $table->json('item_preference')->nullable();
            $table->text('remark')->nullable();
            $table->integer('pause_limit')->nullable();
            $table->json('food_preference')->nullable();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamp('created_date')->nullable();
            $table->timestamp('last_modified')->nullable();
            
            $table->index('order_no');
            $table->index('customer_code');
            $table->index('product_code');
            $table->index('order_status');
            $table->index('delivery_status');
            $table->index('order_date');
            $table->index('order_menu');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};

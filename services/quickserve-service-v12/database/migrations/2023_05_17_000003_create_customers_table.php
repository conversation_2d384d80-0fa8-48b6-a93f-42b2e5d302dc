<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id('pk_customer_code');
            $table->string('customer_name');
            $table->string('phone')->unique();
            $table->string('email_address')->nullable()->unique();
            $table->text('customer_Address')->nullable();
            $table->string('location_code')->nullable();
            $table->string('location_name')->nullable();
            $table->string('lunch_loc_code')->nullable();
            $table->string('lunch_loc_name')->nullable();
            $table->text('lunch_add')->nullable();
            $table->string('dinner_loc_code')->nullable();
            $table->string('dinner_loc_name')->nullable();
            $table->text('dinner_add')->nullable();
            $table->string('food_preference')->nullable();
            $table->string('city')->nullable();
            $table->string('city_name')->nullable();
            $table->string('company_name')->nullable();
            $table->string('group_code')->nullable();
            $table->string('group_name')->nullable();
            $table->timestamp('registered_on')->nullable();
            $table->string('registered_from')->nullable();
            $table->string('food_referance')->nullable();
            $table->boolean('status')->default(1);
            $table->string('otp')->nullable();
            $table->string('password')->nullable();
            $table->string('thirdparty')->nullable();
            $table->boolean('phone_verified')->default(0);
            $table->boolean('subscription_notification')->default(0);
            $table->boolean('email_verified')->default(0);
            $table->string('source')->nullable();
            $table->string('referer')->nullable();
            $table->string('gcm_id')->nullable();
            $table->string('alt_phone')->nullable();
            $table->string('dabbawala_code_type')->nullable();
            $table->string('dabbawala_code')->nullable();
            $table->string('dabbawala_image')->nullable();
            $table->boolean('isguest')->default(0);
            $table->text('delivery_note')->nullable();
            $table->boolean('dabba_status')->default(0);
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamp('modified_on')->nullable();
            
            $table->index('phone');
            $table->index('email_address');
            $table->index('status');
            $table->index('city');
            $table->index('group_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};

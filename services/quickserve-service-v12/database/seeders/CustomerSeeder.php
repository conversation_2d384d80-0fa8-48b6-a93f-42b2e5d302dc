<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\CustomerAddress;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 50 customers
        Customer::factory()
            ->count(50)
            ->create()
            ->each(function ($customer) {
                // Create 1-3 addresses for each customer
                $addressCount = rand(1, 3);
                
                for ($i = 0; $i < $addressCount; $i++) {
                    $isDefault = ($i === 0); // First address is default
                    $menuType = ['Breakfast', 'Lunch', 'Dinner'][rand(0, 2)];
                    $addressType = ['Home', 'Office', 'Other'][rand(0, 2)];
                    
                    CustomerAddress::factory()->create([
                        'fk_customer_code' => $customer->pk_customer_code,
                        'is_default' => $isDefault,
                        'menu_type' => $menuType,
                        'address_type' => $addressType,
                    ]);
                }
            });
    }
}

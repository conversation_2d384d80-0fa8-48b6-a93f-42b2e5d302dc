<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 20 meal products
        Product::factory()
            ->meal()
            ->count(20)
            ->create();
        
        // Create 10 vegetarian meal products
        Product::factory()
            ->meal()
            ->vegetarian()
            ->count(10)
            ->create();
        
        // Create 10 non-vegetarian meal products
        Product::factory()
            ->meal()
            ->nonVegetarian()
            ->count(10)
            ->create();
        
        // Create 5 jain meal products
        Product::factory()
            ->meal()
            ->jain()
            ->count(5)
            ->create();
        
        // Create 15 extra products
        Product::factory()
            ->extra()
            ->count(15)
            ->create();
        
        // Create 10 beverage products
        Product::factory()
            ->beverage()
            ->count(10)
            ->create();
        
        // Create 5 swappable products
        Product::factory()
            ->meal()
            ->swappable()
            ->count(5)
            ->create();
    }
}

<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all customers and products
        $customers = Customer::all();
        $products = Product::all();
        
        if ($customers->isEmpty() || $products->isEmpty()) {
            $this->command->info('No customers or products found. Please run the CustomerSeeder and ProductSeeder first.');
            return;
        }
        
        // Create 100 orders
        for ($i = 0; $i < 100; $i++) {
            $customer = $customers->random();
            $product = $products->random();
            $quantity = rand(1, 3);
            $amount = $product->unit_price * $quantity;
            $tax = $amount * 0.1; // 10% tax
            $deliveryCharges = rand(10, 50);
            $serviceCharges = rand(5, 20);
            $appliedDiscount = rand(0, 30);
            
            $orderStatus = ['New', 'Processing', 'Completed', 'Cancel'][rand(0, 3)];
            $deliveryStatus = ['Pending', 'Dispatched', 'Delivered', 'Failed'][rand(0, 3)];
            
            // If order is cancelled, set delivery status to pending
            if ($orderStatus === 'Cancel') {
                $deliveryStatus = 'Pending';
            }
            
            // If order is completed, set delivery status to delivered
            if ($orderStatus === 'Completed') {
                $deliveryStatus = 'Delivered';
            }
            
            Order::factory()->create([
                'customer_code' => $customer->pk_customer_code,
                'customer_name' => $customer->customer_name,
                'email_address' => $customer->email_address,
                'phone' => $customer->phone,
                'product_code' => $product->pk_product_code,
                'product_name' => $product->name,
                'product_description' => $product->description,
                'product_type' => $product->product_type,
                'quantity' => $quantity,
                'amount' => $amount,
                'tax' => $tax,
                'delivery_charges' => $deliveryCharges,
                'service_charges' => $serviceCharges,
                'applied_discount' => $appliedDiscount,
                'order_status' => $orderStatus,
                'delivery_status' => $deliveryStatus,
                'food_type' => $product->food_type,
            ]);
        }
    }
}

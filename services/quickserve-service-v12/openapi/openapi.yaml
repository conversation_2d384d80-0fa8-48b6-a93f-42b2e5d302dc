openapi: 3.1.0
info:
  title: QuickServe API
  description: API for QuickServe service
  version: 1.0.0
  contact:
    name: QuickServe Team
    email: <EMAIL>
servers:
  - url: http://localhost:8000/api/v1
    description: Local development server
  - url: https://api.quickserve.com/api/v1
    description: Production server
paths:
  /orders:
    get:
      summary: List orders
      description: Get a list of orders with pagination
      operationId: listOrders
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/PerPageParam'
        - name: status
          in: query
          description: Filter orders by status
          required: false
          schema:
            type: string
            enum: [pending, processing, completed, cancelled]
        - name: customer_id
          in: query
          description: Filter orders by customer ID
          required: false
          schema:
            type: integer
        - name: from_date
          in: query
          description: Filter orders from this date (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: to_date
          in: query
          description: Filter orders to this date (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
      responses:
        '200':
          description: A list of orders
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
    post:
      summary: Create order
      description: Create a new order
      operationId: createOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderInput'
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Order'
                  message:
                    type: string
                    example: Order created successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
  /orders/{id}:
    get:
      summary: Get order
      description: Get order by ID
      operationId: getOrder
      parameters:
        - name: id
          in: path
          description: Order ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Order details
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Order'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
    put:
      summary: Update order
      description: Update an existing order
      operationId: updateOrder
      parameters:
        - name: id
          in: path
          description: Order ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderUpdateInput'
      responses:
        '200':
          description: Order updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Order'
                  message:
                    type: string
                    example: Order updated successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
    delete:
      summary: Cancel order
      description: Cancel an existing order
      operationId: cancelOrder
      parameters:
        - name: id
          in: path
          description: Order ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Order cancelled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Order cancelled successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
  /orders/{id}/status:
    put:
      summary: Update order status
      description: Update the status of an existing order
      operationId: updateOrderStatus
      parameters:
        - name: id
          in: path
          description: Order ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [pending, processing, completed, cancelled]
                  description: New status for the order
                notes:
                  type: string
                  description: Optional notes about the status change
      responses:
        '200':
          description: Order status updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Order'
                  message:
                    type: string
                    example: Order status updated successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
      security:
        - bearerAuth: []
  /timeslots:
    get:
      summary: List timeslots
      description: Get a list of available timeslots
      operationId: listTimeslots
      parameters:
        - name: date
          in: query
          description: Filter timeslots by date (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: location_id
          in: query
          description: Filter timeslots by location ID
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: A list of timeslots
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Timeslot'
        '401':
          $ref: '#/components/responses/Unauthorized'
      security:
        - bearerAuth: []
    post:
      summary: Create timeslot
      description: Create a new timeslot
      operationId: createTimeslot
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TimeslotInput'
      responses:
        '201':
          description: Timeslot created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Timeslot'
                  message:
                    type: string
                    example: Timeslot created successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
components:
  schemas:
    Order:
      type: object
      properties:
        id:
          type: integer
          example: 1
        customer_id:
          type: integer
          example: 123
        status:
          type: string
          enum: [pending, processing, completed, cancelled]
          example: pending
        total_amount:
          type: number
          format: float
          example: 29.99
        currency:
          type: string
          example: USD
        payment_status:
          type: string
          enum: [pending, paid, failed, refunded]
          example: pending
        delivery_address:
          $ref: '#/components/schemas/Address'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    OrderInput:
      type: object
      required:
        - customer_id
        - items
        - delivery_address_id
      properties:
        customer_id:
          type: integer
          example: 123
        items:
          type: array
          items:
            type: object
            required:
              - product_id
              - quantity
            properties:
              product_id:
                type: integer
                example: 456
              quantity:
                type: integer
                minimum: 1
                example: 2
              notes:
                type: string
                example: No onions please
        delivery_address_id:
          type: integer
          example: 789
        timeslot_id:
          type: integer
          example: 101
        payment_method_id:
          type: integer
          example: 202
        coupon_code:
          type: string
          example: SUMMER10
        notes:
          type: string
          example: Please deliver to the back door
    OrderUpdateInput:
      type: object
      properties:
        status:
          type: string
          enum: [pending, processing, completed, cancelled]
          example: processing
        items:
          type: array
          items:
            type: object
            required:
              - product_id
              - quantity
            properties:
              product_id:
                type: integer
                example: 456
              quantity:
                type: integer
                minimum: 1
                example: 2
              notes:
                type: string
                example: No onions please
        delivery_address_id:
          type: integer
          example: 789
        timeslot_id:
          type: integer
          example: 101
        notes:
          type: string
          example: Please deliver to the back door
    OrderItem:
      type: object
      properties:
        id:
          type: integer
          example: 1
        order_id:
          type: integer
          example: 1
        product_id:
          type: integer
          example: 456
        product_name:
          type: string
          example: Chicken Burger
        quantity:
          type: integer
          example: 2
        unit_price:
          type: number
          format: float
          example: 9.99
        total_price:
          type: number
          format: float
          example: 19.98
        notes:
          type: string
          example: No onions please
    Address:
      type: object
      properties:
        id:
          type: integer
          example: 789
        customer_id:
          type: integer
          example: 123
        address_line1:
          type: string
          example: 123 Main St
        address_line2:
          type: string
          example: Apt 4B
        city:
          type: string
          example: New York
        state:
          type: string
          example: NY
        postal_code:
          type: string
          example: 10001
        country:
          type: string
          example: USA
        is_default:
          type: boolean
          example: true
    Timeslot:
      type: object
      properties:
        id:
          type: integer
          example: 101
        location_id:
          type: integer
          example: 1
        date:
          type: string
          format: date
          example: 2023-06-15
        start_time:
          type: string
          format: time
          example: 10:00:00
        end_time:
          type: string
          format: time
          example: 11:00:00
        max_orders:
          type: integer
          example: 10
        available_orders:
          type: integer
          example: 5
        is_active:
          type: boolean
          example: true
    TimeslotInput:
      type: object
      required:
        - location_id
        - date
        - start_time
        - end_time
        - max_orders
      properties:
        location_id:
          type: integer
          example: 1
        date:
          type: string
          format: date
          example: 2023-06-15
        start_time:
          type: string
          format: time
          example: 10:00:00
        end_time:
          type: string
          format: time
          example: 11:00:00
        max_orders:
          type: integer
          example: 10
        is_active:
          type: boolean
          example: true
    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        from:
          type: integer
          example: 1
        last_page:
          type: integer
          example: 5
        path:
          type: string
          example: http://localhost:8000/api/v1/orders
        per_page:
          type: integer
          example: 15
        to:
          type: integer
          example: 15
        total:
          type: integer
          example: 75
    Error:
      type: object
      properties:
        message:
          type: string
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
  parameters:
    PageParam:
      name: page
      in: query
      description: Page number
      required: false
      schema:
        type: integer
        default: 1
        minimum: 1
    PerPageParam:
      name: per_page
      in: query
      description: Number of items per page
      required: false
      schema:
        type: integer
        default: 15
        minimum: 1
        maximum: 100
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: Unauthenticated
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: You do not have permission to access this resource
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: Resource not found
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

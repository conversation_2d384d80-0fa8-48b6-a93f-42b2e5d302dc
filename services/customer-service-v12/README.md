# Customer Service

This is the Customer Service microservice for the CubeOneBiz platform. It provides APIs for managing customers, addresses, and related data.

## Requirements

- PHP 8.3+
- Composer
- MySQL 8.0+
- Redis (optional, for caching)
- Docker (optional, for containerization)

## Installation

### Local Development

1. Clone the repository:
```bash
git clone https://github.com/your-organization/tenant.cubeonebiz.com.git
cd tenant.cubeonebiz.com/services/customer-service-v12
```

2. Install dependencies:
```bash
composer install
```

3. Copy the environment file:
```bash
cp .env.example .env
```

4. Generate application key:
```bash
php artisan key:generate
```

5. Configure your database in the `.env` file:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=customer_service
DB_USERNAME=root
DB_PASSWORD=
```

6. Run migrations and seeders:
```bash
php artisan migrate --seed
```

7. Start the development server:
```bash
php artisan serve
```

### Using Docker

1. Clone the repository:
```bash
git clone https://github.com/your-organization/tenant.cubeonebiz.com.git
cd tenant.cubeonebiz.com/services/customer-service-v12
```

2. Copy the environment file:
```bash
cp .env.example .env
```

3. Configure your environment variables in the `.env` file.

4. Build and start the containers:
```bash
docker-compose up -d
```

5. Install dependencies:
```bash
docker-compose exec app composer install
```

6. Generate application key:
```bash
docker-compose exec app php artisan key:generate
```

7. Run migrations and seeders:
```bash
docker-compose exec app php artisan migrate --seed
```

## API Documentation

The API documentation is available in OpenAPI format. You can view it by:

1. Opening the `openapi.yaml` file in an OpenAPI viewer.
2. Using Swagger UI or Redoc to render the documentation.

## Testing

Run the tests with:

```bash
php artisan test
```

Or with code coverage:

```bash
php artisan test --coverage
```

## Kong API Gateway Integration

This service is designed to be used with Kong API Gateway. The configuration is available in the `kong.yaml` file.

To deploy the service with Kong:

1. Deploy the service to your environment.
2. Apply the Kong configuration:
```bash
deck sync -s kong.yaml
```

## CI/CD Pipeline

The service includes a GitHub Actions workflow for CI/CD. It:

1. Runs tests on every push and pull request.
2. Builds and pushes a Docker image on pushes to main and develop branches.
3. Deploys to the appropriate environment based on the branch.

## Directory Structure

- `app/` - Application code
  - `Http/Controllers/Api/` - API controllers
  - `Models/` - Eloquent models
  - `Services/` - Business logic services
  - `DTOs/` - Data Transfer Objects
  - `Exceptions/` - Custom exceptions
  - `Events/` - Event classes
- `database/` - Migrations, factories, and seeders
- `routes/` - API routes
- `tests/` - Unit and feature tests
- `docker/` - Docker configuration files
- `.github/workflows/` - GitHub Actions workflows

## Contributing

Please follow the [PSR-12](https://www.php-fig.org/psr/psr-12/) coding standard and the [conventional commits](https://www.conventionalcommits.org/) specification for commit messages.

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

version: '3.8'

services:
  # PHP Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: customer-service-v12-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
    networks:
      - customer-service-network

  # Nginx Service
  webserver:
    image: nginx:alpine
    container_name: customer-service-v12-webserver
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - customer-service-network

  # MySQL Service
  db:
    image: mysql:8.0
    container_name: customer-service-v12-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - customer-service-dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - customer-service-network

  # Redis Service
  redis:
    image: redis:alpine
    container_name: customer-service-v12-redis
    restart: unless-stopped
    networks:
      - customer-service-network

networks:
  customer-service-network:
    driver: bridge

volumes:
  customer-service-dbdata:
    driver: local

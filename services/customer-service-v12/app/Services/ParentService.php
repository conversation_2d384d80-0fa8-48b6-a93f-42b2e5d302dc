<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\ChildProfile;
use App\Models\School;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * ParentService
 *
 * Business logic for parent customer management and child profile operations
 * in the school tiffin meal subscription system.
 */
class ParentService
{
    /**
     * Register a new parent customer.
     *
     * @param array $data
     * @return Customer
     * @throws \Exception
     */
    public function registerParent(array $data): Customer
    {
        DB::beginTransaction();

        try {
            // Check if customer already exists
            $existingCustomer = Customer::where('phone', $data['phone'])
                ->orWhere('email_address', $data['email_address'])
                ->first();

            if ($existingCustomer) {
                throw new \Exception('Customer with this phone or email already exists');
            }

            // Create parent customer
            $parent = Customer::create([
                'tenant_id' => $data['tenant_id'] ?? 1,
                'company_id' => $data['company_id'] ?? 1,
                'unit_id' => $data['unit_id'] ?? 1,
                'customer_name' => $data['customer_name'],
                'phone' => $data['phone'],
                'email_address' => $data['email_address'],
                'customer_Address' => $data['address'] ?? null,
                'city' => $data['city'] ?? null,
                'city_name' => $data['city'] ?? null,
                'password' => Hash::make($data['password']),
                'registered_on' => now(),
                'registered_from' => 'parent_portal',
                'status' => true,
                'phone_verified' => false,
                'email_verified' => false,
                'source' => 'school_tiffin_registration',
            ]);

            // Generate and send OTP for verification
            $this->sendVerificationOTP($parent);

            DB::commit();

            return $parent;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get parent with children.
     *
     * @param int $parentId
     * @return Customer|null
     */
    public function getParentWithChildren(int $parentId): ?Customer
    {
        return Customer::with([
            'children' => function ($query) {
                $query->with(['school', 'schoolMealSubscriptions' => function ($subQuery) {
                    $subQuery->where('status', 'active')->with('mealPlan');
                }]);
            }
        ])->find($parentId);
    }

    /**
     * Get children for a parent with filters.
     *
     * @param int $parentId
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getParentChildren(int $parentId, array $filters = [])
    {
        $query = ChildProfile::where('parent_customer_id', $parentId)
            ->with(['school', 'schoolMealSubscriptions.mealPlan']);

        if (isset($filters['school_id'])) {
            $query->where('school_id', $filters['school_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['grade_level'])) {
            $query->where('grade_level', $filters['grade_level']);
        }

        return $query->get();
    }

    /**
     * Add a child to parent account.
     *
     * @param array $data
     * @return ChildProfile
     * @throws \Exception
     */
    public function addChild(array $data): ChildProfile
    {
        DB::beginTransaction();

        try {
            // Validate parent exists
            $parent = Customer::find($data['parent_customer_id']);
            if (!$parent) {
                throw new \Exception('Parent customer not found');
            }

            // Validate school exists and is active
            $school = School::where('id', $data['school_id'])
                ->where('is_active', true)
                ->where('partnership_status', 'active')
                ->first();

            if (!$school) {
                throw new \Exception('School not found or not available for enrollment');
            }

            // Check for duplicate student ID in the same school
            if (!empty($data['student_id'])) {
                $existingChild = ChildProfile::where('school_id', $data['school_id'])
                    ->where('student_id', $data['student_id'])
                    ->first();

                if ($existingChild) {
                    throw new \Exception('Student ID already exists in this school');
                }
            }

            // Create child profile
            $child = ChildProfile::create([
                'tenant_id' => $parent->company_id ?? 1,
                'company_id' => $parent->company_id ?? 1,
                'unit_id' => $parent->unit_id ?? 1,
                'parent_customer_id' => $data['parent_customer_id'],
                'school_id' => $data['school_id'],
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'date_of_birth' => $data['date_of_birth'],
                'gender' => $data['gender'] ?? null,
                'grade_level' => $data['grade_level'],
                'section' => $data['section'] ?? null,
                'roll_number' => $data['roll_number'] ?? null,
                'student_id' => $data['student_id'] ?? null,
                'dietary_restrictions' => $data['dietary_restrictions'] ?? [],
                'food_preferences' => $data['food_preferences'] ?? [],
                'meal_type_preference' => $data['meal_type_preference'] ?? 'vegetarian',
                'spice_level' => $data['spice_level'] ?? 'mild',
                'emergency_contacts' => $data['emergency_contacts'] ?? [],
                'medical_conditions' => $data['medical_conditions'] ?? null,
                'special_instructions' => $data['special_instructions'] ?? null,
                'preferred_break_time' => $data['preferred_break_time'] ?? 'lunch_break',
                'delivery_instructions' => $data['delivery_instructions'] ?? [],
                'is_active' => true,
                'enrollment_date' => now()->toDateString(),
                'parent_verified' => $parent->phone_verified,
            ]);

            DB::commit();

            return $child->load(['school', 'parentCustomer']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update child information.
     *
     * @param int $parentId
     * @param int $childId
     * @param array $data
     * @return ChildProfile|null
     * @throws \Exception
     */
    public function updateChild(int $parentId, int $childId, array $data): ?ChildProfile
    {
        DB::beginTransaction();

        try {
            $child = ChildProfile::where('id', $childId)
                ->where('parent_customer_id', $parentId)
                ->first();

            if (!$child) {
                return null;
            }

            // If school is being changed, validate the new school
            if (isset($data['school_id']) && $data['school_id'] !== $child->school_id) {
                $school = School::where('id', $data['school_id'])
                    ->where('is_active', true)
                    ->where('partnership_status', 'active')
                    ->first();

                if (!$school) {
                    throw new \Exception('New school not found or not available');
                }

                // Check for active subscriptions before changing school
                $activeSubscriptions = $child->schoolMealSubscriptions()
                    ->where('status', 'active')
                    ->count();

                if ($activeSubscriptions > 0) {
                    throw new \Exception('Cannot change school while child has active meal subscriptions');
                }
            }

            // Update child profile
            $child->update(array_filter($data, function ($value) {
                return $value !== null;
            }));

            DB::commit();

            return $child->load(['school', 'parentCustomer']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Remove a child from parent account.
     *
     * @param int $parentId
     * @param int $childId
     * @return bool
     * @throws \Exception
     */
    public function removeChild(int $parentId, int $childId): bool
    {
        DB::beginTransaction();

        try {
            $child = ChildProfile::where('id', $childId)
                ->where('parent_customer_id', $parentId)
                ->first();

            if (!$child) {
                return false;
            }

            // Check for active subscriptions
            $activeSubscriptions = $child->schoolMealSubscriptions()
                ->where('status', 'active')
                ->count();

            if ($activeSubscriptions > 0) {
                throw new \Exception('Cannot remove child with active meal subscriptions. Please cancel subscriptions first.');
            }

            // Soft delete the child profile
            $child->delete();

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Verify parent using OTP.
     *
     * @param int $parentId
     * @param string $otp
     * @param string $method
     * @return bool
     */
    public function verifyParent(int $parentId, string $otp, string $method): bool
    {
        $parent = Customer::find($parentId);
        if (!$parent) {
            return false;
        }

        // Verify OTP (implement your OTP verification logic here)
        if ($this->verifyOTP($parent, $otp, $method)) {
            $parent->update([
                'phone_verified' => $method === 'phone_otp' ? true : $parent->phone_verified,
                'email_verified' => $method === 'email_verification' ? true : $parent->email_verified,
            ]);

            // Update all children's verification status
            $parent->children()->update(['parent_verified' => true]);

            return true;
        }

        return false;
    }

    /**
     * Get parent dashboard summary.
     *
     * @param int $parentId
     * @return array
     */
    public function getParentDashboardSummary(int $parentId): array
    {
        $parent = Customer::with(['children.schoolMealSubscriptions.mealPlan'])
            ->find($parentId);

        if (!$parent) {
            throw new \Exception('Parent not found');
        }

        $children = $parent->children;
        $activeSubscriptions = $children->flatMap->schoolMealSubscriptions
            ->where('status', 'active');

        $totalMonthlyAmount = $activeSubscriptions->sum('final_amount');
        $nextBillingDate = $activeSubscriptions->min('next_billing_date');

        return [
            'parent' => [
                'name' => $parent->customer_name,
                'phone' => $parent->phone,
                'email' => $parent->email_address,
                'verified' => $parent->phone_verified && $parent->email_verified,
            ],
            'children' => [
                'total' => $children->count(),
                'active' => $children->where('is_active', true)->count(),
                'with_subscriptions' => $children->filter(function ($child) {
                    return $child->schoolMealSubscriptions->where('status', 'active')->count() > 0;
                })->count(),
            ],
            'subscriptions' => [
                'active' => $activeSubscriptions->count(),
                'total_monthly_amount' => $totalMonthlyAmount,
                'next_billing_date' => $nextBillingDate,
                'upcoming_deliveries' => $this->getUpcomingDeliveries($activeSubscriptions),
            ],
            'recent_activity' => $this->getRecentActivity($parentId),
        ];
    }

    /**
     * Send verification OTP to parent.
     *
     * @param Customer $parent
     * @return void
     */
    private function sendVerificationOTP(Customer $parent): void
    {
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        
        // Store OTP in database (you might want to create an OTP table)
        $parent->update(['otp' => $otp]);

        // Send OTP via SMS/Email (implement your notification service here)
        // NotificationService::sendOTP($parent->phone, $otp);
    }

    /**
     * Verify OTP.
     *
     * @param Customer $parent
     * @param string $otp
     * @param string $method
     * @return bool
     */
    private function verifyOTP(Customer $parent, string $otp, string $method): bool
    {
        // Implement your OTP verification logic here
        return $parent->otp === $otp;
    }

    /**
     * Get upcoming deliveries for subscriptions.
     *
     * @param \Illuminate\Support\Collection $subscriptions
     * @return array
     */
    private function getUpcomingDeliveries($subscriptions): array
    {
        $upcoming = [];
        $today = now();

        foreach ($subscriptions as $subscription) {
            $deliveryDays = $subscription->delivery_days ?? [];
            
            for ($i = 0; $i < 7; $i++) {
                $date = $today->copy()->addDays($i);
                $dayName = strtolower($date->format('l'));
                
                if (in_array($dayName, $deliveryDays)) {
                    $upcoming[] = [
                        'date' => $date->toDateString(),
                        'child_name' => $subscription->childProfile->full_name,
                        'meal_plan' => $subscription->mealPlan->plan_name,
                        'school' => $subscription->school->school_name,
                        'break_time' => $subscription->preferred_break_time,
                    ];
                }
            }
        }

        return collect($upcoming)->sortBy('date')->take(10)->values()->all();
    }

    /**
     * Get recent activity for parent.
     *
     * @param int $parentId
     * @return array
     */
    private function getRecentActivity(int $parentId): array
    {
        // Implement recent activity logic (deliveries, payments, etc.)
        return [
            'recent_deliveries' => [],
            'recent_payments' => [],
            'recent_feedback' => [],
        ];
    }
}

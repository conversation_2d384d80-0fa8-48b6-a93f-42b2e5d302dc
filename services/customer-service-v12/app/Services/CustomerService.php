<?php

namespace App\Services;

use App\DTOs\Customer\CustomerDTO;
use App\DTOs\Customer\AddressDTO;
use App\Events\Customer\CustomerCreated;
use App\Events\Customer\CustomerUpdated;
use App\Events\Customer\CustomerDeleted;
use App\Exceptions\Customer\CustomerNotFoundException;
use App\Exceptions\Customer\DuplicateCustomerException;
use App\Exceptions\Customer\AddressNotFoundException;
use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Models\CustomerWallet;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;
use Exception;

/**
 * Customer Service
 *
 * This service encapsulates all customer-related business logic.
 */
class CustomerService
{
    /**
     * Create a new CustomerService instance.
     */
    public function __construct(
        protected Dispatcher $events,
        protected LoggerInterface $logger
    ) {
    }

    /**
     * Get all customers
     *
     * @param array $filters
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAllCustomers(array $filters = [], int $perPage = 15)
    {
        $query = Customer::query();

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email_address', 'like', "%{$search}%");
            });
        }

        // Order by
        $orderBy = $filters['order_by'] ?? 'pk_customer_code';
        $orderDir = $filters['order_dir'] ?? 'desc';
        $query->orderBy($orderBy, $orderDir);

        // Paginate
        return $query->paginate($perPage);
    }

    /**
     * Get customer by ID
     *
     * @param int $id
     * @return Customer
     * @throws CustomerNotFoundException
     */
    public function getCustomerById(int $id): Customer
    {
        $customer = Customer::find($id);

        if (!$customer) {
            throw new CustomerNotFoundException("Customer with ID {$id} not found");
        }

        return $customer;
    }

    /**
     * Get customer by phone
     *
     * @param string $phone
     * @return Customer|null
     */
    public function getCustomerByPhone(string $phone): ?Customer
    {
        return Customer::where('phone', $phone)->first();
    }

    /**
     * Get customer by email
     *
     * @param string $email
     * @return Customer|null
     */
    public function getCustomerByEmail(string $email): ?Customer
    {
        return Customer::where('email_address', $email)->first();
    }

    /**
     * Create a new customer
     *
     * @param CustomerDTO $customerDTO
     * @return Customer
     * @throws DuplicateCustomerException
     */
    public function createCustomer(CustomerDTO $customerDTO): Customer
    {
        try {
            // Check if customer with same phone or email already exists
            $existingCustomer = $this->getCustomerByPhone($customerDTO->phone);
            if ($existingCustomer) {
                throw new DuplicateCustomerException("Customer with phone {$customerDTO->phone} already exists");
            }

            if ($customerDTO->emailAddress) {
                $existingCustomer = $this->getCustomerByEmail($customerDTO->emailAddress);
                if ($existingCustomer) {
                    throw new DuplicateCustomerException("Customer with email {$customerDTO->emailAddress} already exists");
                }
            }

            // Start transaction
            DB::beginTransaction();

            // Create customer
            $customerData = $customerDTO->toArray();
            if ($customerDTO->password) {
                $customerData['password'] = Hash::make($customerDTO->password);
            }

            $customer = Customer::create($customerData);

            // Create wallet
            $wallet = new CustomerWallet();
            $wallet->customer_code = $customer->pk_customer_code;
            $wallet->balance = 0;
            $wallet->status = true;
            $wallet->company_id = $customer->company_id;
            $wallet->unit_id = $customer->unit_id;
            $wallet->save();

            // Commit transaction
            DB::commit();

            // Fire event
            $this->events->dispatch(new CustomerCreated($customer));

            // Log
            $this->logger->info('Customer created', [
                'customer_id' => $customer->pk_customer_code,
                'phone' => $customer->phone
            ]);

            return $customer;
        } catch (DuplicateCustomerException $e) {
            // Rollback transaction
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error creating customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Update an existing customer
     *
     * @param int $id
     * @param CustomerDTO $customerDTO
     * @return Customer
     * @throws CustomerNotFoundException
     * @throws DuplicateCustomerException
     */
    public function updateCustomer(int $id, CustomerDTO $customerDTO): Customer
    {
        try {
            // Get customer
            $customer = $this->getCustomerById($id);

            // Check if phone is being changed and if it's already in use
            if ($customerDTO->phone !== $customer->phone) {
                $existingCustomer = $this->getCustomerByPhone($customerDTO->phone);
                if ($existingCustomer && $existingCustomer->pk_customer_code !== $id) {
                    throw new DuplicateCustomerException("Customer with phone {$customerDTO->phone} already exists");
                }
            }

            // Check if email is being changed and if it's already in use
            if ($customerDTO->emailAddress && $customerDTO->emailAddress !== $customer->email_address) {
                $existingCustomer = $this->getCustomerByEmail($customerDTO->emailAddress);
                if ($existingCustomer && $existingCustomer->pk_customer_code !== $id) {
                    throw new DuplicateCustomerException("Customer with email {$customerDTO->emailAddress} already exists");
                }
            }

            // Start transaction
            DB::beginTransaction();

            // Update customer
            $customerData = $customerDTO->toArray();
            if ($customerDTO->password) {
                $customerData['password'] = Hash::make($customerDTO->password);
            } else {
                unset($customerData['password']);
            }

            $customer->update($customerData);

            // Commit transaction
            DB::commit();

            // Fire event
            $this->events->dispatch(new CustomerUpdated($customer));

            // Log
            $this->logger->info('Customer updated', [
                'customer_id' => $customer->pk_customer_code,
                'phone' => $customer->phone
            ]);

            return $customer;
        } catch (CustomerNotFoundException | DuplicateCustomerException $e) {
            // Rollback transaction
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error updating customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
    /**
     * Delete a customer
     *
     * @param int $id
     * @return bool
     * @throws CustomerNotFoundException
     */
    public function deleteCustomer(int $id): bool
    {
        try {
            // Get customer
            $customer = $this->getCustomerById($id);
            $customerData = $customer->toArray();

            // Start transaction
            DB::beginTransaction();

            // Delete customer
            $result = $customer->delete();

            // Commit transaction
            DB::commit();

            // Fire event
            $this->events->dispatch(new CustomerDeleted($id, $customerData));

            // Log
            $this->logger->info('Customer deleted', [
                'customer_id' => $id,
                'phone' => $customerData['phone']
            ]);

            return $result;
        } catch (CustomerNotFoundException $e) {
            // Rollback transaction
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error deleting customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Add an address to a customer
     *
     * @param int $customerId
     * @param AddressDTO $addressDTO
     * @return CustomerAddress
     * @throws CustomerNotFoundException
     */
    public function addCustomerAddress(int $customerId, AddressDTO $addressDTO): CustomerAddress
    {
        try {
            // Get customer
            $customer = $this->getCustomerById($customerId);

            // Start transaction
            DB::beginTransaction();

            // If this is the default address, unset any existing default addresses
            if ($addressDTO->isDefault) {
                CustomerAddress::where('customer_code', $customerId)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }

            // Create address
            $addressData = $addressDTO->toArray();
            $addressData['customer_code'] = $customerId;
            $address = CustomerAddress::create($addressData);

            // Commit transaction
            DB::commit();

            // Log
            $this->logger->info('Customer address added', [
                'customer_id' => $customerId,
                'address_id' => $address->pk_customer_address_code
            ]);

            return $address;
        } catch (CustomerNotFoundException $e) {
            // Rollback transaction
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error adding customer address', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Update a customer address
     *
     * @param int $addressId
     * @param AddressDTO $addressDTO
     * @return CustomerAddress
     * @throws AddressNotFoundException
     */
    public function updateCustomerAddress(int $addressId, AddressDTO $addressDTO): CustomerAddress
    {
        try {
            // Get address
            $address = CustomerAddress::find($addressId);
            if (!$address) {
                throw new AddressNotFoundException("Address with ID {$addressId} not found");
            }

            // Start transaction
            DB::beginTransaction();

            // If this is the default address, unset any existing default addresses
            if ($addressDTO->isDefault && !$address->is_default) {
                CustomerAddress::where('customer_code', $address->customer_code)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }

            // Update address
            $addressData = $addressDTO->toArray();
            unset($addressData['customer_code']); // Don't allow changing the customer
            $address->update($addressData);

            // Commit transaction
            DB::commit();

            // Log
            $this->logger->info('Customer address updated', [
                'customer_id' => $address->customer_code,
                'address_id' => $address->pk_customer_address_code
            ]);

            return $address;
        } catch (AddressNotFoundException $e) {
            // Rollback transaction
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error updating customer address', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Delete a customer address
     *
     * @param int $addressId
     * @return bool
     * @throws AddressNotFoundException
     */
    public function deleteCustomerAddress(int $addressId): bool
    {
        try {
            // Get address
            $address = CustomerAddress::find($addressId);
            if (!$address) {
                throw new AddressNotFoundException("Address with ID {$addressId} not found");
            }

            // Start transaction
            DB::beginTransaction();

            // Delete address
            $result = $address->delete();

            // Commit transaction
            DB::commit();

            // Log
            $this->logger->info('Customer address deleted', [
                'customer_id' => $address->customer_code,
                'address_id' => $addressId
            ]);

            return $result;
        } catch (AddressNotFoundException $e) {
            // Rollback transaction
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error deleting customer address', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
}
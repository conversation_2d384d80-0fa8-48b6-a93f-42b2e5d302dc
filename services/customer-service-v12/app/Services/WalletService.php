<?php

namespace App\Services;

use App\Events\Wallet\WalletDeposited;
use App\Events\Wallet\WalletWithdrawn;
use App\Exceptions\Wallet\InsufficientBalanceException;
use App\Exceptions\Wallet\WalletNotFoundException;
use App\Models\Customer;
use App\Models\CustomerWallet;
use App\Models\WalletTransaction;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Psr\Log\LoggerInterface;
use Exception;

/**
 * Wallet Service
 *
 * This service encapsulates all wallet-related business logic.
 */
class WalletService
{
    /**
     * Create a new WalletService instance.
     */
    public function __construct(
        protected Dispatcher $events,
        protected LoggerInterface $logger,
        protected CustomerService $customerService
    ) {
    }

    /**
     * Get a customer's wallet
     *
     * @param int $customerId
     * @return CustomerWallet
     * @throws WalletNotFoundException
     */
    public function getWallet(int $customerId): CustomerWallet
    {
        try {
            // Get customer
            $customer = $this->customerService->getCustomerById($customerId);
            
            // Get wallet
            $wallet = CustomerWallet::where('customer_code', $customerId)->first();
            
            if (!$wallet) {
                throw new WalletNotFoundException("Wallet for customer with ID {$customerId} not found");
            }
            
            return $wallet;
        } catch (Exception $e) {
            $this->logger->error('Error getting wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId
            ]);
            
            throw $e;
        }
    }

    /**
     * Deposit to a customer's wallet
     *
     * @param int $customerId
     * @param float $amount
     * @param string $description
     * @param string $transactionId
     * @return CustomerWallet
     * @throws WalletNotFoundException
     */
    public function deposit(int $customerId, float $amount, string $description = '', string $transactionId = ''): CustomerWallet
    {
        try {
            // Validate amount
            if ($amount <= 0) {
                throw new Exception("Deposit amount must be greater than zero");
            }
            
            // Start transaction
            DB::beginTransaction();
            
            // Get wallet
            $wallet = $this->getWallet($customerId);
            
            // Update balance
            $oldBalance = $wallet->balance;
            $wallet->balance += $amount;
            $wallet->save();
            
            // Create transaction record
            $transaction = new WalletTransaction();
            $transaction->customer_code = $customerId;
            $transaction->amount = $amount;
            $transaction->type = 'deposit';
            $transaction->description = $description ?: 'Wallet deposit';
            $transaction->transaction_id = $transactionId ?: uniqid('dep_');
            $transaction->before_balance = $oldBalance;
            $transaction->after_balance = $wallet->balance;
            $transaction->status = 'completed';
            $transaction->save();
            
            // Commit transaction
            DB::commit();
            
            // Fire event
            $this->events->dispatch(new WalletDeposited($wallet, $amount, $transaction));
            
            // Log
            $this->logger->info('Wallet deposit', [
                'customer_id' => $customerId,
                'amount' => $amount,
                'transaction_id' => $transaction->transaction_id
            ]);
            
            return $wallet;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            // Log error
            $this->logger->error('Error depositing to wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId,
                'amount' => $amount
            ]);
            
            throw $e;
        }
    }

    /**
     * Withdraw from a customer's wallet
     *
     * @param int $customerId
     * @param float $amount
     * @param string $description
     * @param string $transactionId
     * @return CustomerWallet
     * @throws WalletNotFoundException
     * @throws InsufficientBalanceException
     */
    public function withdraw(int $customerId, float $amount, string $description = '', string $transactionId = ''): CustomerWallet
    {
        try {
            // Validate amount
            if ($amount <= 0) {
                throw new Exception("Withdrawal amount must be greater than zero");
            }
            
            // Start transaction
            DB::beginTransaction();
            
            // Get wallet
            $wallet = $this->getWallet($customerId);
            
            // Check balance
            if ($wallet->balance < $amount) {
                throw new InsufficientBalanceException("Insufficient balance for withdrawal");
            }
            
            // Update balance
            $oldBalance = $wallet->balance;
            $wallet->balance -= $amount;
            $wallet->save();
            
            // Create transaction record
            $transaction = new WalletTransaction();
            $transaction->customer_code = $customerId;
            $transaction->amount = $amount;
            $transaction->type = 'withdrawal';
            $transaction->description = $description ?: 'Wallet withdrawal';
            $transaction->transaction_id = $transactionId ?: uniqid('wit_');
            $transaction->before_balance = $oldBalance;
            $transaction->after_balance = $wallet->balance;
            $transaction->status = 'completed';
            $transaction->save();
            
            // Commit transaction
            DB::commit();
            
            // Fire event
            $this->events->dispatch(new WalletWithdrawn($wallet, $amount, $transaction));
            
            // Log
            $this->logger->info('Wallet withdrawal', [
                'customer_id' => $customerId,
                'amount' => $amount,
                'transaction_id' => $transaction->transaction_id
            ]);
            
            return $wallet;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            // Log error
            $this->logger->error('Error withdrawing from wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId,
                'amount' => $amount
            ]);
            
            throw $e;
        }
    }

    /**
     * Get wallet transaction history
     *
     * @param int $customerId
     * @param array $filters
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getTransactionHistory(int $customerId, array $filters = [], int $perPage = 15)
    {
        $query = WalletTransaction::where('customer_code', $customerId);
        
        // Apply filters
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        // Order by
        $query->orderBy('created_at', 'desc');
        
        // Paginate
        return $query->paginate($perPage);
    }
}

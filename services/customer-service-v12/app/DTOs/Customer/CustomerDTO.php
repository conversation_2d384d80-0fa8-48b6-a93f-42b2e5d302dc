<?php

namespace App\DTOs\Customer;

/**
 * Customer Data Transfer Object
 * 
 * This class is used to transfer customer data between layers.
 */
class CustomerDTO
{
    /**
     * Create a new CustomerDTO instance.
     */
    public function __construct(
        public readonly string $customerName,
        public readonly string $phone,
        public readonly ?string $emailAddress = null,
        public readonly ?string $customerAddress = null,
        public readonly ?string $locationCode = null,
        public readonly ?string $locationName = null,
        public readonly ?string $foodPreference = null,
        public readonly ?string $city = null,
        public readonly ?string $cityName = null,
        public readonly ?string $companyName = null,
        public readonly ?string $groupCode = null,
        public readonly ?string $groupName = null,
        public readonly ?string $registeredFrom = null,
        public readonly ?string $password = null,
        public readonly ?string $source = null,
        public readonly ?string $referer = null,
        public readonly ?string $altPhone = null,
        public readonly int $companyId = 1,
        public readonly int $unitId = 1,
        public readonly bool $isGuest = false,
        public readonly ?string $deliveryNote = null
    ) {
    }

    /**
     * Create a CustomerDTO from an array.
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            customerName: $data['customer_name'],
            phone: $data['phone'],
            emailAddress: $data['email_address'] ?? null,
            customerAddress: $data['customer_address'] ?? null,
            locationCode: $data['location_code'] ?? null,
            locationName: $data['location_name'] ?? null,
            foodPreference: $data['food_preference'] ?? null,
            city: $data['city'] ?? null,
            cityName: $data['city_name'] ?? null,
            companyName: $data['company_name'] ?? null,
            groupCode: $data['group_code'] ?? null,
            groupName: $data['group_name'] ?? null,
            registeredFrom: $data['registered_from'] ?? null,
            password: $data['password'] ?? null,
            source: $data['source'] ?? null,
            referer: $data['referer'] ?? null,
            altPhone: $data['alt_phone'] ?? null,
            companyId: $data['company_id'] ?? 1,
            unitId: $data['unit_id'] ?? 1,
            isGuest: $data['is_guest'] ?? false,
            deliveryNote: $data['delivery_note'] ?? null
        );
    }

    /**
     * Convert the DTO to an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'customer_name' => $this->customerName,
            'phone' => $this->phone,
            'email_address' => $this->emailAddress,
            'customer_Address' => $this->customerAddress,
            'location_code' => $this->locationCode,
            'location_name' => $this->locationName,
            'food_preference' => $this->foodPreference,
            'city' => $this->city,
            'city_name' => $this->cityName,
            'company_name' => $this->companyName,
            'group_code' => $this->groupCode,
            'group_name' => $this->groupName,
            'registered_from' => $this->registeredFrom,
            'registered_on' => now(),
            'password' => $this->password,
            'source' => $this->source,
            'referer' => $this->referer,
            'alt_phone' => $this->altPhone,
            'company_id' => $this->companyId,
            'unit_id' => $this->unitId,
            'isguest' => $this->isGuest,
            'delivery_note' => $this->deliveryNote,
            'status' => true,
            'phone_verified' => false,
            'email_verified' => false,
        ];
    }
}

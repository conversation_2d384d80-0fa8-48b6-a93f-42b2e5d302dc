<?php

namespace App\DTOs\Customer;

/**
 * Address Data Transfer Object
 * 
 * This class is used to transfer address data between layers.
 */
class AddressDTO
{
    /**
     * Create a new AddressDTO instance.
     */
    public function __construct(
        public readonly int $customerCode,
        public readonly string $addressType,
        public readonly string $addressLine1,
        public readonly ?string $addressLine2 = null,
        public readonly ?string $addressName = null,
        public readonly ?string $landmark = null,
        public readonly string $city,
        public readonly ?string $state = null,
        public readonly ?string $country = null,
        public readonly ?string $pincode = null,
        public readonly ?float $latitude = null,
        public readonly ?float $longitude = null,
        public readonly bool $isDefault = false,
        public readonly int $companyId = 1,
        public readonly int $unitId = 1
    ) {
    }

    /**
     * Create an AddressDTO from an array.
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            customerCode: $data['customer_code'],
            addressType: $data['address_type'],
            addressLine1: $data['address_line1'],
            addressLine2: $data['address_line2'] ?? null,
            addressName: $data['address_name'] ?? null,
            landmark: $data['landmark'] ?? null,
            city: $data['city'],
            state: $data['state'] ?? null,
            country: $data['country'] ?? null,
            pincode: $data['pincode'] ?? null,
            latitude: $data['latitude'] ?? null,
            longitude: $data['longitude'] ?? null,
            isDefault: $data['is_default'] ?? false,
            companyId: $data['company_id'] ?? 1,
            unitId: $data['unit_id'] ?? 1
        );
    }

    /**
     * Convert the DTO to an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'customer_code' => $this->customerCode,
            'address_type' => $this->addressType,
            'address_line1' => $this->addressLine1,
            'address_line2' => $this->addressLine2,
            'address_name' => $this->addressName,
            'landmark' => $this->landmark,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'pincode' => $this->pincode,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'is_default' => $this->isDefault,
            'status' => true,
            'company_id' => $this->companyId,
            'unit_id' => $this->unitId,
        ];
    }
}

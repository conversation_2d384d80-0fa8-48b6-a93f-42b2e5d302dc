<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

/**
 * ChildProfile Model
 *
 * Represents a child profile linked to a parent customer and school.
 * Contains dietary restrictions, preferences, and school-specific information.
 */
class ChildProfile extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'child_profiles';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'company_id',
        'unit_id',
        'parent_customer_id',
        'school_id',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'grade_level',
        'section',
        'roll_number',
        'student_id',
        'dietary_restrictions',
        'food_preferences',
        'meal_type_preference',
        'spice_level',
        'emergency_contacts',
        'medical_conditions',
        'special_instructions',
        'preferred_break_time',
        'delivery_instructions',
        'is_active',
        'enrollment_date',
        'last_meal_date',
        'total_meals_consumed',
        'parent_verified',
        'parent_verified_at',
        'verification_method',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'dietary_restrictions' => 'array',
        'food_preferences' => 'array',
        'emergency_contacts' => 'array',
        'delivery_instructions' => 'array',
        'enrollment_date' => 'date',
        'last_meal_date' => 'date',
        'is_active' => 'boolean',
        'parent_verified' => 'boolean',
        'parent_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['age', 'full_name'];

    /**
     * Get the parent customer that owns this child profile.
     */
    public function parentCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'parent_customer_id', 'pk_customer_code');
    }

    /**
     * Get the school that this child attends.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the school meal subscriptions for this child.
     */
    public function schoolMealSubscriptions(): HasMany
    {
        return $this->hasMany(\App\Models\SchoolMealSubscription::class);
    }

    /**
     * Get the delivery items for this child.
     */
    public function deliveryItems(): HasMany
    {
        return $this->hasMany(\App\Models\SchoolDeliveryItem::class);
    }

    /**
     * Scope a query to only include active children.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by school.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $schoolId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope a query to filter by parent.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $parentId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForParent($query, $parentId)
    {
        return $query->where('parent_customer_id', $parentId);
    }

    /**
     * Scope a query to filter by grade level.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $gradeLevel
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInGrade($query, $gradeLevel)
    {
        return $query->where('grade_level', $gradeLevel);
    }

    /**
     * Scope a query to filter by meal type preference.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $mealType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithMealPreference($query, $mealType)
    {
        return $query->where('meal_type_preference', $mealType);
    }

    /**
     * Scope a query to filter by tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $tenantId
     * @param  int  $companyId
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, $tenantId, $companyId = null, $unitId = null)
    {
        $query->where('tenant_id', $tenantId);
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($unitId) {
            $query->where('unit_id', $unitId);
        }
        
        return $query;
    }

    /**
     * Get the child's full name.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return trim("{$this->first_name} {$this->last_name}");
    }

    /**
     * Get the child's age in years.
     *
     * @return int
     */
    public function getAgeAttribute()
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    /**
     * Get the child's grade and section combined.
     *
     * @return string
     */
    public function getGradeSectionAttribute()
    {
        return $this->section ? "{$this->grade_level}-{$this->section}" : $this->grade_level;
    }

    /**
     * Check if the child has a specific dietary restriction.
     *
     * @param  string  $restriction
     * @return bool
     */
    public function hasDietaryRestriction($restriction)
    {
        return in_array($restriction, $this->dietary_restrictions ?? []);
    }

    /**
     * Check if the child likes a specific food item.
     *
     * @param  string  $foodItem
     * @return bool
     */
    public function likesFoodItem($foodItem)
    {
        $likes = $this->food_preferences['likes'] ?? [];
        return in_array($foodItem, $likes);
    }

    /**
     * Check if the child dislikes a specific food item.
     *
     * @param  string  $foodItem
     * @return bool
     */
    public function dislikesFoodItem($foodItem)
    {
        $dislikes = $this->food_preferences['dislikes'] ?? [];
        return in_array($foodItem, $dislikes);
    }

    /**
     * Get the primary emergency contact.
     *
     * @return array|null
     */
    public function getPrimaryEmergencyContact()
    {
        $contacts = $this->emergency_contacts ?? [];
        return $contacts[0] ?? null;
    }

    /**
     * Check if the child is verified by parent.
     *
     * @return bool
     */
    public function isVerified()
    {
        return $this->parent_verified;
    }

    /**
     * Get the active subscription for this child.
     *
     * @return \App\Models\SchoolMealSubscription|null
     */
    public function getActiveSubscription()
    {
        return $this->schoolMealSubscriptions()
            ->where('status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->first();
    }

    /**
     * Check if the child has an active subscription.
     *
     * @return bool
     */
    public function hasActiveSubscription()
    {
        return $this->getActiveSubscription() !== null;
    }

    /**
     * Get the total number of meals consumed by this child.
     *
     * @return int
     */
    public function getTotalMealsConsumed()
    {
        return $this->deliveryItems()
            ->where('consumption_status', 'fully_consumed')
            ->count();
    }

    /**
     * Get the consumption rate for this child.
     *
     * @return float
     */
    public function getConsumptionRate()
    {
        $totalDelivered = $this->deliveryItems()->count();
        $totalConsumed = $this->getTotalMealsConsumed();
        
        return $totalDelivered > 0 ? ($totalConsumed / $totalDelivered) * 100 : 0;
    }

    /**
     * Update the last meal date and increment total meals consumed.
     *
     * @return void
     */
    public function recordMealConsumption()
    {
        $this->update([
            'last_meal_date' => now()->toDateString(),
            'total_meals_consumed' => $this->total_meals_consumed + 1,
        ]);
    }

    /**
     * Mark the child as verified by parent.
     *
     * @param  string  $method
     * @return void
     */
    public function markAsVerified($method = 'phone_otp')
    {
        $this->update([
            'parent_verified' => true,
            'parent_verified_at' => now(),
            'verification_method' => $method,
        ]);
    }

    /**
     * Check if the child is eligible for meal delivery on a given date.
     *
     * @param  \Carbon\Carbon  $date
     * @return bool
     */
    public function isEligibleForDelivery($date = null)
    {
        $date = $date ?? now();
        
        return $this->is_active && 
               $this->parent_verified && 
               $this->hasActiveSubscription() &&
               $date->isWeekday(); // Assuming school meals are only on weekdays
    }
}

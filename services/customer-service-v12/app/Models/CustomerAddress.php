<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Customer Address Model
 * 
 * This model represents a customer's address in the system.
 */
class CustomerAddress extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_address';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_address_code';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_code',
        'address_type',
        'address_name',
        'address_line1',
        'address_line2',
        'landmark',
        'city',
        'state',
        'country',
        'pincode',
        'latitude',
        'longitude',
        'is_default',
        'status',
        'company_id',
        'unit_id'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_default' => 'boolean',
            'status' => 'boolean',
            'latitude' => 'float',
            'longitude' => 'float',
        ];
    }

    /**
     * Get the customer that owns the address.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the full address as a string.
     *
     * @return string
     */
    public function getFullAddressAttribute(): string
    {
        $address = $this->address_line1;
        
        if (!empty($this->address_line2)) {
            $address .= ', ' . $this->address_line2;
        }
        
        if (!empty($this->landmark)) {
            $address .= ', ' . $this->landmark;
        }
        
        $address .= ', ' . $this->city;
        
        if (!empty($this->state)) {
            $address .= ', ' . $this->state;
        }
        
        if (!empty($this->country)) {
            $address .= ', ' . $this->country;
        }
        
        if (!empty($this->pincode)) {
            $address .= ' - ' . $this->pincode;
        }
        
        return $address;
    }

    /**
     * Scope a query to only include default addresses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope a query to only include addresses of a specific type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeType($query, $type)
    {
        return $query->where('address_type', $type);
    }

    /**
     * Scope a query to only include addresses from a specific company.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $companyId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to only include addresses from a specific unit.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

/**
 * Customer Model
 *
 * This model represents a customer in the system.
 */
class Customer extends Model
{
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_code';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_name',
        'phone',
        'email_address',
        'customer_Address',
        'location_code',
        'location_name',
        'lunch_loc_code',
        'lunch_loc_name',
        'lunch_add',
        'dinner_loc_code',
        'dinner_loc_name',
        'dinner_add',
        'food_preference',
        'city',
        'city_name',
        'company_name',
        'group_code',
        'group_name',
        'registered_on',
        'registered_from',
        'food_referance',
        'status',
        'otp',
        'password',
        'thirdparty',
        'phone_verified',
        'subscription_notification',
        'email_verified',
        'source',
        'referer',
        'gcm_id',
        'alt_phone',
        'company_id',
        'unit_id',
        'dabbawala_code_type',
        'dabbawala_code',
        'dabbawala_image',
        'isguest',
        'delivery_note',
        'payment_mode',
        'payment_mode_settings',
        'payment_mode_updated_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'otp',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'status' => 'boolean',
            'phone_verified' => 'boolean',
            'email_verified' => 'boolean',
            'isguest' => 'boolean',
            'subscription_notification' => 'boolean',
            'registered_on' => 'datetime',
            'payment_mode_settings' => 'array',
            'payment_mode_updated_at' => 'datetime',
        ];
    }

    /**
     * Get the customer's full name.
     *
     * @return string
     */
    public function getFullNameAttribute(): string
    {
        return $this->customer_name;
    }

    /**
     * Check if the customer is active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status;
    }

    /**
     * Get the customer's addresses.
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's wallet.
     */
    public function wallet(): HasOne
    {
        return $this->hasOne(CustomerWallet::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's orders.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Scope a query to only include active customers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Check if customer uses wallet payment mode.
     *
     * @return bool
     */
    public function usesWalletPayment(): bool
    {
        return $this->payment_mode === 'wallet';
    }

    /**
     * Check if customer uses direct payment mode.
     *
     * @return bool
     */
    public function usesDirectPayment(): bool
    {
        return $this->payment_mode === 'direct';
    }

    /**
     * Get wallet settings for the customer.
     *
     * @return array
     */
    public function getWalletSettings(): array
    {
        if (!$this->usesWalletPayment()) {
            return [];
        }

        return $this->payment_mode_settings ?? [
            'minimum_balance' => 100,
            'auto_reload_enabled' => false,
            'auto_reload_amount' => 500,
            'auto_reload_threshold' => 50,
        ];
    }

    /**
     * Update payment mode and settings.
     *
     * @param string $mode
     * @param array|null $settings
     * @return bool
     */
    public function updatePaymentMode(string $mode, ?array $settings = null): bool
    {
        $this->payment_mode = $mode;
        $this->payment_mode_settings = $settings;
        $this->payment_mode_updated_at = now();

        return $this->save();
    }

    /**
     * Generate a random string for OTP.
     *
     * @param int $length
     * @return string
     */
    public static function generateRandomString(int $length = 6): string
    {
        return (string) random_int(
            10 ** ($length - 1),
            (10 ** $length) - 1
        );
    }
}

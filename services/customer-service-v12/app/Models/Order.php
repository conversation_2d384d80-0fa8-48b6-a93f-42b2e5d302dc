<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Order Model
 * 
 * This model represents a customer's order in the system.
 */
class Order extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'orders';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_order_no';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_no',
        'ref_order',
        'customer_code',
        'customer_name',
        'phone',
        'email_address',
        'location_code',
        'location_name',
        'city',
        'city_name',
        'product_code',
        'product_name',
        'quantity',
        'amount',
        'tax',
        'delivery_charges',
        'service_charges',
        'applied_discount',
        'order_status',
        'delivery_status',
        'invoice_status',
        'order_date',
        'ship_address',
        'order_menu',
        'food_type',
        'payment_mode',
        'amount_paid',
        'promo_code',
        'tax_method',
        'source',
        'company_id',
        'unit_id'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'tax' => 'decimal:2',
            'delivery_charges' => 'decimal:2',
            'service_charges' => 'decimal:2',
            'applied_discount' => 'decimal:2',
            'amount_paid' => 'decimal:2',
            'order_date' => 'date',
        ];
    }

    /**
     * Get the customer that owns the order.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the order details for the order.
     */
    public function orderDetails(): HasMany
    {
        return $this->hasMany(OrderDetail::class, 'ref_order_no', 'order_no');
    }

    /**
     * Get the net amount of the order.
     *
     * @return float
     */
    public function getNetAmountAttribute(): float
    {
        if ($this->tax_method === 'inclusive') {
            return ($this->amount + $this->delivery_charges + $this->service_charges) - $this->applied_discount;
        } else {
            return ($this->amount + $this->tax + $this->delivery_charges + $this->service_charges) - $this->applied_discount;
        }
    }

    /**
     * Scope a query to only include orders with a specific status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('order_status', $status);
    }

    /**
     * Scope a query to only include orders for a specific menu.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $menu
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMenu($query, $menu)
    {
        return $query->where('order_menu', strtolower($menu));
    }

    /**
     * Scope a query to only include orders for a specific date.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $date
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDate($query, $date)
    {
        return $query->whereDate('order_date', $date);
    }
}

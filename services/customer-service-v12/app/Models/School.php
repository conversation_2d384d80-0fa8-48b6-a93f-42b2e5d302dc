<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * School Model
 *
 * Represents schools that are part of the tiffin delivery network.
 * Each school has break times, delivery zones, and partnership details.
 */
class School extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'schools';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'company_id',
        'unit_id',
        'school_name',
        'school_code',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'contact_person_name',
        'contact_phone',
        'contact_email',
        'principal_name',
        'principal_phone',
        'principal_email',
        'latitude',
        'longitude',
        'break_times',
        'delivery_zones',
        'delivery_window_minutes',
        'bulk_delivery_enabled',
        'school_type',
        'total_students',
        'grade_levels',
        'partnership_start_date',
        'partnership_end_date',
        'partnership_status',
        'commission_percentage',
        'is_active',
        'special_requirements',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'break_times' => 'array',
        'delivery_zones' => 'array',
        'grade_levels' => 'array',
        'special_requirements' => 'array',
        'partnership_start_date' => 'date',
        'partnership_end_date' => 'date',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'commission_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'bulk_delivery_enabled' => 'boolean',
    ];

    /**
     * Get the child profiles associated with this school.
     */
    public function childProfiles(): HasMany
    {
        return $this->hasMany(ChildProfile::class);
    }

    /**
     * Get the meal plans available for this school.
     */
    public function mealPlans(): HasMany
    {
        return $this->hasMany(\App\Models\MealPlan::class);
    }

    /**
     * Get the delivery batches for this school.
     */
    public function deliveryBatches(): HasMany
    {
        return $this->hasMany(\App\Models\SchoolDeliveryBatch::class);
    }

    /**
     * Scope a query to only include active schools.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where('partnership_status', 'active');
    }

    /**
     * Scope a query to filter by city.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $city
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInCity($query, $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Scope a query to filter by school type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('school_type', $type);
    }

    /**
     * Scope a query to filter by tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $tenantId
     * @param  int  $companyId
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, $tenantId, $companyId = null, $unitId = null)
    {
        $query->where('tenant_id', $tenantId);
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($unitId) {
            $query->where('unit_id', $unitId);
        }
        
        return $query;
    }

    /**
     * Get the break time for a specific break type.
     *
     * @param  string  $breakType
     * @return array|null
     */
    public function getBreakTime($breakType)
    {
        return $this->break_times[$breakType] ?? null;
    }

    /**
     * Check if the school has a specific break time.
     *
     * @param  string  $breakType
     * @return bool
     */
    public function hasBreakTime($breakType)
    {
        return isset($this->break_times[$breakType]);
    }

    /**
     * Get the delivery window start time for a break.
     *
     * @param  string  $breakType
     * @return string|null
     */
    public function getDeliveryWindowStart($breakType)
    {
        $breakTime = $this->getBreakTime($breakType);
        if (!$breakTime) {
            return null;
        }

        $breakStart = \Carbon\Carbon::createFromFormat('H:i', $breakTime['start']);
        $deliveryStart = $breakStart->subMinutes($this->delivery_window_minutes);
        
        return $deliveryStart->format('H:i');
    }

    /**
     * Get the delivery window end time for a break.
     *
     * @param  string  $breakType
     * @return string|null
     */
    public function getDeliveryWindowEnd($breakType)
    {
        $breakTime = $this->getBreakTime($breakType);
        if (!$breakTime) {
            return null;
        }

        return $breakTime['start'];
    }

    /**
     * Check if the school is within delivery range of given coordinates.
     *
     * @param  float  $lat
     * @param  float  $lng
     * @param  float  $radiusKm
     * @return bool
     */
    public function isWithinDeliveryRange($lat, $lng, $radiusKm = 10)
    {
        if (!$this->latitude || !$this->longitude) {
            return false;
        }

        $distance = $this->calculateDistance($lat, $lng, $this->latitude, $this->longitude);
        return $distance <= $radiusKm;
    }

    /**
     * Calculate distance between two coordinates using Haversine formula.
     *
     * @param  float  $lat1
     * @param  float  $lng1
     * @param  float  $lat2
     * @param  float  $lng2
     * @return float Distance in kilometers
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get the full address as a string.
     *
     * @return string
     */
    public function getFullAddressAttribute()
    {
        return trim("{$this->address}, {$this->city}, {$this->state} {$this->postal_code}");
    }

    /**
     * Check if the school partnership is active.
     *
     * @return bool
     */
    public function isPartnershipActive()
    {
        return $this->partnership_status === 'active' && 
               $this->is_active &&
               (!$this->partnership_end_date || $this->partnership_end_date->isFuture());
    }

    /**
     * Get the total number of active children in this school.
     *
     * @return int
     */
    public function getActiveChildrenCount()
    {
        return $this->childProfiles()->where('is_active', true)->count();
    }

    /**
     * Get the total number of active subscriptions for this school.
     *
     * @return int
     */
    public function getActiveSubscriptionsCount()
    {
        return $this->childProfiles()
            ->whereHas('schoolMealSubscriptions', function ($query) {
                $query->where('status', 'active');
            })
            ->count();
    }
}

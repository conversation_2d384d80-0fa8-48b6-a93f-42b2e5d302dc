<?php

namespace App\Exceptions\Customer;

use Exception;

/**
 * Address Not Found Exception
 * 
 * This exception is thrown when an address is not found.
 */
class AddressNotFoundException extends Exception
{
    /**
     * Create a new AddressNotFoundException instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(
        string $message = 'Address not found',
        int $code = 404,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}

<?php

namespace App\Exceptions\Customer;

use Exception;

/**
 * Duplicate Customer Exception
 * 
 * This exception is thrown when a duplicate customer is found.
 */
class DuplicateCustomerException extends Exception
{
    /**
     * Create a new DuplicateCustomerException instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(
        string $message = 'Customer with this phone or email already exists',
        int $code = 409,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}

<?php

namespace App\Exceptions\Customer;

use Exception;

/**
 * Customer Not Found Exception
 * 
 * This exception is thrown when a customer is not found.
 */
class CustomerNotFoundException extends Exception
{
    /**
     * Create a new CustomerNotFoundException instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(
        string $message = 'Customer not found',
        int $code = 404,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}

<?php

namespace App\Events\Wallet;

use App\Models\CustomerWallet;
use App\Models\WalletTransaction;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WalletDeposited
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly CustomerWallet $wallet,
        public readonly float $amount,
        public readonly WalletTransaction $transaction
    ) {
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Wallet API Controller
 *
 * This controller handles all wallet-related API endpoints.
 */
class WalletController extends Controller
{

    /**
     * Get a customer's wallet
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            // Check if customer exists
            $customer = DB::table('customers')
                ->where('pk_customer_code', $id)
                ->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Get wallet information, create if it doesn't exist
            $wallet = DB::table('customer_wallet')
                ->where('customer_code', $id)
                ->first();

            if (!$wallet) {
                // Create wallet for the customer
                DB::table('customer_wallet')->insert([
                    'customer_code' => $id,
                    'balance' => 0.00,
                    'status' => 1, // Active
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Get the newly created wallet
                $wallet = DB::table('customer_wallet')
                    ->where('customer_code', $id)
                    ->first();
            }

            return response()->json([
                'success' => true,
                'message' => 'Wallet retrieved successfully',
                'data' => [
                    'wallet_id' => $wallet->pk_wallet_id,
                    'customer_id' => $wallet->customer_code,
                    'balance' => $wallet->balance,
                    'status' => $wallet->status ? 'active' : 'inactive',
                    'created_at' => $wallet->created_at,
                    'updated_at' => $wallet->updated_at,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting wallet', [
                'customer_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve wallet',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deposit to a customer's wallet
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function deposit(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1',
                'description' => 'nullable|string|max:255',
                'transaction_id' => 'nullable|string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $amount = $request->input('amount');
            $description = $request->input('description', 'Wallet deposit');
            $transactionId = $request->input('transaction_id', 'DEP_' . time());

            // Check if customer exists first
            $customer = DB::table('customers')
                ->where('pk_customer_code', $id)
                ->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Check if wallet exists, create if it doesn't
            $wallet = DB::table('customer_wallet')
                ->where('customer_code', $id)
                ->first();

            if (!$wallet) {
                // Create wallet for the customer
                DB::table('customer_wallet')->insert([
                    'customer_code' => $id,
                    'balance' => 0.00,
                    'status' => 1, // Active
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Get the newly created wallet
                $wallet = DB::table('customer_wallet')
                    ->where('customer_code', $id)
                    ->first();
            }

            // Start database transaction
            DB::beginTransaction();

            try {
                // Update wallet balance
                $newBalance = $wallet->balance + $amount;
                DB::table('customer_wallet')
                    ->where('customer_code', $id)
                    ->update([
                        'balance' => $newBalance,
                        'updated_at' => now(),
                    ]);

                // Create transaction record
                DB::table('wallet_transactions')->insert([
                    'customer_code' => $id,
                    'amount' => $amount,
                    'type' => 'deposit',
                    'description' => $description,
                    'transaction_id' => $transactionId,
                    'before_balance' => $wallet->balance,
                    'after_balance' => $newBalance,
                    'status' => 'completed',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Commit transaction
                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Deposit successful',
                    'data' => [
                        'wallet_id' => $wallet->pk_wallet_id,
                        'customer_id' => $id,
                        'previous_balance' => $wallet->balance,
                        'deposit_amount' => $amount,
                        'new_balance' => $newBalance,
                        'transaction_id' => $transactionId,
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Error depositing to wallet', [
                'customer_id' => $id,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process deposit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Withdraw from a customer's wallet
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function withdraw(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1',
                'description' => 'nullable|string|max:255',
                'transaction_id' => 'nullable|string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $amount = $request->input('amount');
            $description = $request->input('description', 'Wallet withdrawal');
            $transactionId = $request->input('transaction_id', 'WIT_' . time());

            // Check if customer and wallet exist
            $wallet = DB::table('customer_wallet')
                ->where('customer_code', $id)
                ->first();

            if (!$wallet) {
                return response()->json([
                    'success' => false,
                    'message' => 'Wallet not found'
                ], 404);
            }

            // Check if sufficient balance
            if ($wallet->balance < $amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient balance'
                ], 400);
            }

            // Start database transaction
            DB::beginTransaction();

            try {
                // Update wallet balance
                $newBalance = $wallet->balance - $amount;
                DB::table('customer_wallet')
                    ->where('customer_code', $id)
                    ->update([
                        'balance' => $newBalance,
                        'updated_at' => now(),
                    ]);

                // Create transaction record
                DB::table('wallet_transactions')->insert([
                    'customer_code' => $id,
                    'amount' => $amount,
                    'type' => 'withdrawal',
                    'description' => $description,
                    'transaction_id' => $transactionId,
                    'before_balance' => $wallet->balance,
                    'after_balance' => $newBalance,
                    'status' => 'completed',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Commit transaction
                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Withdrawal successful',
                    'data' => [
                        'wallet_id' => $wallet->pk_wallet_id,
                        'customer_id' => $id,
                        'previous_balance' => $wallet->balance,
                        'withdrawal_amount' => $amount,
                        'new_balance' => $newBalance,
                        'transaction_id' => $transactionId,
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Error withdrawing from wallet', [
                'customer_id' => $id,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process withdrawal',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get wallet transactions for a customer
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function transactions(Request $request, int $id): JsonResponse
    {
        try {
            // Check if customer exists
            $customer = DB::table('customers')
                ->where('pk_customer_code', $id)
                ->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            $perPage = $request->input('per_page', 15);
            $type = $request->input('type'); // deposit, withdrawal, etc.
            $dateFrom = $request->input('date_from');
            $dateTo = $request->input('date_to');

            $query = DB::table('wallet_transactions')
                ->where('customer_code', $id)
                ->select([
                    'id',
                    'amount',
                    'type',
                    'description',
                    'transaction_id',
                    'before_balance',
                    'after_balance',
                    'status',
                    'created_at',
                    'updated_at'
                ]);

            // Apply filters
            if ($type) {
                $query->where('type', $type);
            }

            if ($dateFrom) {
                $query->where('created_at', '>=', $dateFrom);
            }

            if ($dateTo) {
                $query->where('created_at', '<=', $dateTo);
            }

            // Get paginated results
            $total = $query->count();
            $transactions = $query
                ->orderBy('created_at', 'desc')
                ->offset(($request->input('page', 1) - 1) * $perPage)
                ->limit($perPage)
                ->get();

            $lastPage = ceil($total / $perPage);

            return response()->json([
                'success' => true,
                'message' => 'Transactions retrieved successfully',
                'data' => $transactions,
                'meta' => [
                    'current_page' => (int) $request->input('page', 1),
                    'last_page' => $lastPage,
                    'per_page' => $perPage,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting wallet transactions', [
                'customer_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transactions',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

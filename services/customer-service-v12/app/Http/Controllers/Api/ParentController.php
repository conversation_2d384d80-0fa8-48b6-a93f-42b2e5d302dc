<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Parent\RegisterParentRequest;
use App\Http\Requests\Parent\AddChildRequest;
use App\Http\Requests\Parent\UpdateChildRequest;
use App\Models\Customer;
use App\Models\ChildProfile;
use App\Models\School;
use App\Services\ParentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * ParentController
 *
 * Handles parent registration, child management, and family account operations
 * for the school tiffin meal subscription system.
 */
class ParentController extends Controller
{
    protected ParentService $parentService;

    public function __construct(ParentService $parentService)
    {
        $this->parentService = $parentService;
    }

    /**
     * Register a new parent customer.
     *
     * @param RegisterParentRequest $request
     * @return JsonResponse
     */
    public function register(RegisterParentRequest $request): JsonResponse
    {
        try {
            $parent = $this->parentService->registerParent($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Parent registered successfully',
                'data' => [
                    'parent' => $parent,
                    'verification_required' => true,
                ],
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Parent registration failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->validated(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Parent registration failed: ' . $e->getMessage(),
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ], 500);
        }
    }

    /**
     * Get parent profile with children.
     *
     * @param Request $request
     * @param int $parentId
     * @return JsonResponse
     */
    public function getProfile(Request $request, int $parentId): JsonResponse
    {
        try {
            $parent = $this->parentService->getParentWithChildren($parentId);

            if (!$parent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Parent not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'parent' => $parent,
                    'children_count' => $parent->children->count(),
                    'active_subscriptions_count' => $parent->children->sum(function ($child) {
                        return $child->schoolMealSubscriptions()->where('status', 'active')->count();
                    }),
                ],
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get parent profile', [
                'parent_id' => $parentId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve parent profile',
            ], 500);
        }
    }

    /**
     * Get all children for a parent.
     *
     * @param Request $request
     * @param int $parentId
     * @return JsonResponse
     */
    public function getChildren(Request $request, int $parentId): JsonResponse
    {
        try {
            $children = $this->parentService->getParentChildren($parentId, [
                'school_id' => $request->input('school_id'),
                'is_active' => $request->input('is_active', true),
                'grade_level' => $request->input('grade_level'),
            ]);

            return response()->json([
                'success' => true,
                'data' => $children,
                'meta' => [
                    'total_children' => $children->count(),
                    'active_children' => $children->where('is_active', true)->count(),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get parent children', [
                'parent_id' => $parentId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve children',
            ], 500);
        }
    }

    /**
     * Add a new child to parent account.
     *
     * @param AddChildRequest $request
     * @param int $parentId
     * @return JsonResponse
     */
    public function addChild(AddChildRequest $request, int $parentId): JsonResponse
    {
        try {
            $childData = $request->validated();
            $childData['parent_customer_id'] = $parentId;

            $child = $this->parentService->addChild($childData);

            return response()->json([
                'success' => true,
                'message' => 'Child added successfully',
                'data' => [
                    'child' => $child,
                    'school' => $child->school,
                    'verification_required' => !$child->parent_verified,
                ],
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to add child', [
                'parent_id' => $parentId,
                'error' => $e->getMessage(),
                'request_data' => $request->validated(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add child: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update child information.
     *
     * @param UpdateChildRequest $request
     * @param int $parentId
     * @param int $childId
     * @return JsonResponse
     */
    public function updateChild(UpdateChildRequest $request, int $parentId, int $childId): JsonResponse
    {
        try {
            $child = $this->parentService->updateChild($parentId, $childId, $request->validated());

            if (!$child) {
                return response()->json([
                    'success' => false,
                    'message' => 'Child not found or does not belong to this parent',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Child updated successfully',
                'data' => [
                    'child' => $child,
                    'school' => $child->school,
                ],
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update child', [
                'parent_id' => $parentId,
                'child_id' => $childId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update child: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove a child from parent account.
     *
     * @param Request $request
     * @param int $parentId
     * @param int $childId
     * @return JsonResponse
     */
    public function removeChild(Request $request, int $parentId, int $childId): JsonResponse
    {
        try {
            $result = $this->parentService->removeChild($parentId, $childId);

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Child not found or cannot be removed',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Child removed successfully',
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to remove child', [
                'parent_id' => $parentId,
                'child_id' => $childId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove child: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available schools for child enrollment.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAvailableSchools(Request $request): JsonResponse
    {
        try {
            $filters = [
                'city' => $request->input('city'),
                'school_type' => $request->input('school_type'),
                'partnership_status' => 'active',
                'is_active' => true,
            ];

            $schools = School::query()
                ->when($filters['city'], fn($q) => $q->where('city', $filters['city']))
                ->when($filters['school_type'], fn($q) => $q->where('school_type', $filters['school_type']))
                ->where('partnership_status', $filters['partnership_status'])
                ->where('is_active', $filters['is_active'])
                ->with(['mealPlans' => function ($query) {
                    $query->active()->select(['id', 'school_id', 'plan_name', 'base_price', 'meal_type']);
                }])
                ->get();

            return response()->json([
                'success' => true,
                'data' => $schools,
                'meta' => [
                    'total_schools' => $schools->count(),
                    'filters_applied' => array_filter($filters),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get available schools', [
                'error' => $e->getMessage(),
                'filters' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve schools',
            ], 500);
        }
    }

    /**
     * Verify parent account using OTP.
     *
     * @param Request $request
     * @param int $parentId
     * @return JsonResponse
     */
    public function verifyParent(Request $request, int $parentId): JsonResponse
    {
        $request->validate([
            'otp' => 'required|string|size:6',
            'verification_method' => 'required|in:phone_otp,email_verification',
        ]);

        try {
            $result = $this->parentService->verifyParent(
                $parentId,
                $request->input('otp'),
                $request->input('verification_method')
            );

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid OTP or verification failed',
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Parent verified successfully',
                'data' => [
                    'verified' => true,
                    'verification_method' => $request->input('verification_method'),
                ],
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Parent verification failed', [
                'parent_id' => $parentId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Verification failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get parent dashboard summary.
     *
     * @param Request $request
     * @param int $parentId
     * @return JsonResponse
     */
    public function getDashboardSummary(Request $request, int $parentId): JsonResponse
    {
        try {
            $summary = $this->parentService->getParentDashboardSummary($parentId);

            return response()->json([
                'success' => true,
                'data' => $summary,
                'meta' => [
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get dashboard summary', [
                'parent_id' => $parentId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard summary',
            ], 500);
        }
    }
}

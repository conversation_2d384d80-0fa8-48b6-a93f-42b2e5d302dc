<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;

/**
 * Customer API Controller
 *
 * This controller handles all customer-related API endpoints.
 */
class CustomerController extends Controller
{

    /**
     * Get all customers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 15);
            $search = $request->input('search');
            $status = $request->input('status');

            $query = DB::table('customers')
                ->select(
                    'pk_customer_code as id',
                    'customer_name',
                    'phone',
                    'email_address',
                    'customer_Address',
                    'location_name',
                    'food_preference',
                    'city_name',
                    'company_name',
                    'registered_on',
                    'status',
                    'phone_verified',
                    'email_verified',
                    'created_at',
                    'updated_at'
                );

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('customer_name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('email_address', 'like', "%{$search}%");
                });
            }

            if ($status !== null) {
                $query->where('status', $status);
            }

            $customers = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Customers retrieved successfully',
                'data' => $customers->items(),
                'meta' => [
                    'current_page' => $customers->currentPage(),
                    'last_page' => $customers->lastPage(),
                    'per_page' => $customers->perPage(),
                    'total' => $customers->total(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting customers', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a customer by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $customer = DB::table('customers')
                ->select(
                    'pk_customer_code as id',
                    'customer_name',
                    'phone',
                    'email_address',
                    'customer_Address',
                    'location_code',
                    'location_name',
                    'lunch_loc_code',
                    'lunch_loc_name',
                    'lunch_add',
                    'dinner_loc_code',
                    'dinner_loc_name',
                    'dinner_add',
                    'food_preference',
                    'city',
                    'city_name',
                    'company_name',
                    'group_code',
                    'group_name',
                    'registered_on',
                    'registered_from',
                    'status',
                    'phone_verified',
                    'email_verified',
                    'alt_phone',
                    'created_at',
                    'updated_at'
                )
                ->where('pk_customer_code', $id)
                ->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Customer retrieved successfully',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting customer', [
                'customer_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new customer
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'customer_name' => 'required|string|max:255',
                'phone' => 'required|string|max:20|unique:customers,phone',
                'email_address' => 'nullable|email|max:255|unique:customers,email_address',
                'customer_Address' => 'nullable|string|max:500',
                'location_code' => 'nullable|string|max:50',
                'location_name' => 'nullable|string|max:255',
                'food_preference' => 'nullable|in:vegetarian,non_vegetarian,jain_vegetarian',
                'city' => 'nullable|string|max:10',
                'city_name' => 'nullable|string|max:255',
                'company_name' => 'nullable|string|max:255',
                'registered_from' => 'nullable|string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['registered_on'] = now();
            $data['status'] = 1; // Active by default
            $data['phone_verified'] = 0;
            $data['email_verified'] = 0;
            $data['created_at'] = now();
            $data['updated_at'] = now();

            // Start transaction for customer and wallet creation
            DB::beginTransaction();

            try {
                $customerId = DB::table('customers')->insertGetId($data);

                // Create wallet for the new customer
                DB::table('customer_wallet')->insert([
                    'customer_code' => $customerId,
                    'balance' => 0.00,
                    'status' => 1, // Active
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Commit transaction
                DB::commit();

                // Get the created customer
                $customer = DB::table('customers')
                    ->select(
                        'pk_customer_code as id',
                        'customer_name',
                        'phone',
                        'email_address',
                        'customer_Address',
                        'location_name',
                        'food_preference',
                        'city_name',
                        'company_name',
                        'status',
                        'created_at'
                    )
                    ->where('pk_customer_code', $customerId)
                    ->first();
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

            return response()->json([
                'success' => true,
                'message' => 'Customer created successfully',
                'data' => $customer
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error creating customer', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create customer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a customer
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            // Check if customer exists
            $existingCustomer = DB::table('customers')
                ->where('pk_customer_code', $id)
                ->first();

            if (!$existingCustomer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'customer_name' => 'sometimes|string|max:255',
                'phone' => 'sometimes|string|max:20|unique:customers,phone,' . $id . ',pk_customer_code',
                'email_address' => 'sometimes|nullable|email|max:255|unique:customers,email_address,' . $id . ',pk_customer_code',
                'customer_Address' => 'sometimes|nullable|string|max:500',
                'location_code' => 'sometimes|nullable|string|max:50',
                'location_name' => 'sometimes|nullable|string|max:255',
                'food_preference' => 'sometimes|nullable|in:vegetarian,non_vegetarian,jain_vegetarian',
                'city' => 'sometimes|nullable|string|max:10',
                'city_name' => 'sometimes|nullable|string|max:255',
                'company_name' => 'sometimes|nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['updated_at'] = now();

            DB::table('customers')
                ->where('pk_customer_code', $id)
                ->update($data);

            // Get the updated customer
            $customer = DB::table('customers')
                ->select(
                    'pk_customer_code as id',
                    'customer_name',
                    'phone',
                    'email_address',
                    'customer_Address',
                    'location_name',
                    'food_preference',
                    'city_name',
                    'company_name',
                    'status',
                    'updated_at'
                )
                ->where('pk_customer_code', $id)
                ->first();

            return response()->json([
                'success' => true,
                'message' => 'Customer updated successfully',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating customer', [
                'customer_id' => $id,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update customer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a customer
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            // Check if customer exists
            $customer = DB::table('customers')
                ->where('pk_customer_code', $id)
                ->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Delete customer (soft delete by setting status to 0)
            DB::table('customers')
                ->where('pk_customer_code', $id)
                ->update([
                    'status' => 0,
                    'updated_at' => now()
                ]);

            return response()->json([
                'success' => true,
                'message' => 'Customer deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting customer', [
                'customer_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete customer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search customers
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query', '');
            $perPage = $request->input('per_page', 15);

            $customers = $this->customerService->searchCustomers($query, $perPage);

            return response()->json([
                'success' => true,
                'data' => $customers
            ]);
        } catch (\Exception $e) {
            Log::error('Error searching customers', [
                'error' => $e->getMessage(),
                'query' => $request->input('query')
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while searching customers'
            ], 500);
        }
    }

    /**
     * Get customer by phone
     */
    public function getByPhone(string $phone): JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerByPhone($phone);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting customer by phone', [
                'error' => $e->getMessage(),
                'phone' => $phone
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving customer'
            ], 500);
        }
    }

    /**
     * Get customer by email
     */
    public function getByEmail(string $email): JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerByEmail($email);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting customer by email', [
                'error' => $e->getMessage(),
                'email' => $email
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving customer'
            ], 500);
        }
    }

    /**
     * Get customer by code
     */
    public function getByCode(string $code): JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerById($code);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting customer by code', [
                'error' => $e->getMessage(),
                'code' => $code
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving customer'
            ], 500);
        }
    }

    /**
     * Customer lookup
     */
    public function lookup(Request $request): JsonResponse
    {
        try {
            $phone = $request->input('phone');
            $email = $request->input('email');
            $code = $request->input('code');

            $customer = null;

            if ($phone) {
                $customer = $this->customerService->getCustomerByPhone($phone);
            } elseif ($email) {
                $customer = $this->customerService->getCustomerByEmail($email);
            } elseif ($code) {
                $customer = $this->customerService->getCustomerById($code);
            }

            return response()->json([
                'success' => true,
                'data' => $customer,
                'found' => $customer !== null
            ]);
        } catch (\Exception $e) {
            Log::error('Error in customer lookup', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during customer lookup'
            ], 500);
        }
    }

    /**
     * Verify customer
     */
    public function verify(Request $request): JsonResponse
    {
        try {
            $customerId = $request->input('customer_id');
            $otp = $request->input('otp');
            $method = $request->input('method', 'sms');

            $customer = $this->customerService->getCustomerById($customerId);
            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            $verified = $this->customerService->verifyOtp($customer, $otp, $method);

            return response()->json([
                'success' => $verified,
                'message' => $verified ? 'Customer verified successfully' : 'Invalid OTP'
            ]);
        } catch (\Exception $e) {
            Log::error('Error verifying customer', [
                'error' => $e->getMessage(),
                'customer_id' => $request->input('customer_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during verification'
            ], 500);
        }
    }

    /**
     * Add an address to a customer
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function addAddress(Request $request, int $id): JsonResponse
    {
        try {
            // Check if customer exists
            $customer = DB::table('customers')
                ->where('pk_customer_code', $id)
                ->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'address_type' => 'required|string|max:50',
                'address_name' => 'required|string|max:255',
                'address_line1' => 'required|string|max:255',
                'address_line2' => 'nullable|string|max:255',
                'city' => 'required|string|max:100',
                'state' => 'required|string|max:100',
                'country' => 'required|string|max:100',
                'pincode' => 'required|string|max:10',
                'is_default' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['customer_code'] = $id;
            $data['is_default'] = $data['is_default'] ?? false;
            $data['created_at'] = now();
            $data['updated_at'] = now();

            $addressId = DB::table('customer_address')->insertGetId($data);

            return response()->json([
                'success' => true,
                'message' => 'Address added successfully',
                'data' => [
                    'address_id' => $addressId,
                    'customer_id' => $id,
                    'address_type' => $data['address_type'],
                    'address_name' => $data['address_name'],
                    'is_default' => $data['is_default']
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error adding address', [
                'customer_id' => $id,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add address',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a customer address
     *
     * @param Request $request
     * @param int $id
     * @param int $addressId
     * @return JsonResponse
     */
    public function updateAddress(Request $request, int $id, int $addressId): JsonResponse
    {
        try {
            // Check if address exists and belongs to customer
            $address = DB::table('customer_address')
                ->where('pk_customer_address_code', $addressId)
                ->where('customer_code', $id)
                ->first();

            if (!$address) {
                return response()->json([
                    'success' => false,
                    'message' => 'Address not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'address_type' => 'sometimes|string|max:50',
                'address_name' => 'sometimes|string|max:255',
                'address_line1' => 'sometimes|string|max:255',
                'address_line2' => 'sometimes|nullable|string|max:255',
                'city' => 'sometimes|string|max:100',
                'state' => 'sometimes|string|max:100',
                'country' => 'sometimes|string|max:100',
                'pincode' => 'sometimes|string|max:10',
                'is_default' => 'sometimes|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['updated_at'] = now();

            DB::table('customer_address')
                ->where('pk_customer_address_code', $addressId)
                ->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Address updated successfully',
                'data' => [
                    'address_id' => $addressId,
                    'customer_id' => $id,
                    'updated_fields' => array_keys($data)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating address', [
                'customer_id' => $id,
                'address_id' => $addressId,
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update address',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a customer address
     *
     * @param int $id
     * @param int $addressId
     * @return JsonResponse
     */
    public function deleteAddress(int $id, int $addressId): JsonResponse
    {
        try {
            // Check if address exists and belongs to customer
            $address = DB::table('customer_address')
                ->where('pk_customer_address_code', $addressId)
                ->where('customer_code', $id)
                ->first();

            if (!$address) {
                return response()->json([
                    'success' => false,
                    'message' => 'Address not found'
                ], 404);
            }

            // Delete the address
            DB::table('customer_address')
                ->where('pk_customer_address_code', $addressId)
                ->delete();

            return response()->json([
                'success' => true,
                'message' => 'Address deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting address', [
                'customer_id' => $id,
                'address_id' => $addressId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete address',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

/**
 * Payment Mode Controller
 * 
 * Handles customer payment mode preferences and settings.
 */
class PaymentModeController extends Controller
{
    /**
     * Get customer payment mode preferences.
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function getPaymentMode(Request $request, int $customerId): JsonResponse
    {
        try {
            $customer = Customer::findOrFail($customerId);

            return response()->json([
                'status' => 'success',
                'message' => 'Payment mode retrieved successfully',
                'data' => [
                    'customer_id' => $customer->pk_customer_code,
                    'payment_mode' => $customer->payment_mode ?? 'wallet',
                    'payment_mode_settings' => $customer->getWalletSettings(),
                    'payment_mode_updated_at' => $customer->payment_mode_updated_at,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve payment mode',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update customer payment mode preferences.
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function updatePaymentMode(Request $request, int $customerId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_mode' => ['required', Rule::in(['wallet', 'direct'])],
            'wallet_settings' => 'sometimes|array',
            'wallet_settings.minimum_balance' => 'sometimes|numeric|min:0',
            'wallet_settings.auto_reload_enabled' => 'sometimes|boolean',
            'wallet_settings.auto_reload_amount' => 'sometimes|numeric|min:1',
            'wallet_settings.auto_reload_threshold' => 'sometimes|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $customer = Customer::findOrFail($customerId);
            
            $paymentMode = $request->input('payment_mode');
            $walletSettings = null;

            // Validate wallet settings if wallet mode is selected
            if ($paymentMode === 'wallet') {
                $walletSettings = $request->input('wallet_settings', []);
                
                // Set default values if not provided
                $walletSettings = array_merge([
                    'minimum_balance' => 100,
                    'auto_reload_enabled' => false,
                    'auto_reload_amount' => 500,
                    'auto_reload_threshold' => 50,
                ], $walletSettings);

                // Validate auto-reload settings
                if ($walletSettings['auto_reload_enabled'] && 
                    $walletSettings['auto_reload_amount'] <= $walletSettings['auto_reload_threshold']) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Auto-reload amount must be greater than threshold',
                    ], 422);
                }
            }

            // Update payment mode
            $customer->updatePaymentMode($paymentMode, $walletSettings);

            // Trigger payment mode change event
            event('customer.payment_mode.updated', [
                'customer_id' => $customer->pk_customer_code,
                'old_payment_mode' => $customer->getOriginal('payment_mode'),
                'new_payment_mode' => $paymentMode,
                'wallet_settings' => $walletSettings,
                'updated_at' => now(),
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Payment mode updated successfully',
                'data' => [
                    'customer_id' => $customer->pk_customer_code,
                    'payment_mode' => $customer->payment_mode,
                    'payment_mode_settings' => $customer->getWalletSettings(),
                    'payment_mode_updated_at' => $customer->payment_mode_updated_at,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update payment mode',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payment mode statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPaymentModeStatistics(Request $request): JsonResponse
    {
        try {
            $companyId = $request->input('company_id', 1);
            $unitId = $request->input('unit_id', 1);

            $statistics = [
                'total_customers' => Customer::where('company_id', $companyId)
                    ->where('unit_id', $unitId)
                    ->count(),
                'wallet_customers' => Customer::where('company_id', $companyId)
                    ->where('unit_id', $unitId)
                    ->where('payment_mode', 'wallet')
                    ->count(),
                'direct_customers' => Customer::where('company_id', $companyId)
                    ->where('unit_id', $unitId)
                    ->where('payment_mode', 'direct')
                    ->count(),
                'auto_reload_enabled' => Customer::where('company_id', $companyId)
                    ->where('unit_id', $unitId)
                    ->where('payment_mode', 'wallet')
                    ->whereJsonContains('payment_mode_settings->auto_reload_enabled', true)
                    ->count(),
            ];

            // Calculate percentages
            $total = $statistics['total_customers'];
            if ($total > 0) {
                $statistics['wallet_percentage'] = round(($statistics['wallet_customers'] / $total) * 100, 2);
                $statistics['direct_percentage'] = round(($statistics['direct_customers'] / $total) * 100, 2);
                $statistics['auto_reload_percentage'] = round(($statistics['auto_reload_enabled'] / $total) * 100, 2);
            } else {
                $statistics['wallet_percentage'] = 0;
                $statistics['direct_percentage'] = 0;
                $statistics['auto_reload_percentage'] = 0;
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Payment mode statistics retrieved successfully',
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve payment mode statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk update payment mode for multiple customers.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkUpdatePaymentMode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'customer_ids' => 'required|array|min:1',
            'customer_ids.*' => 'required|integer|exists:customers,pk_customer_code',
            'payment_mode' => ['required', Rule::in(['wallet', 'direct'])],
            'wallet_settings' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $customerIds = $request->input('customer_ids');
            $paymentMode = $request->input('payment_mode');
            $walletSettings = $request->input('wallet_settings');

            $updatedCount = 0;
            $errors = [];

            foreach ($customerIds as $customerId) {
                try {
                    $customer = Customer::findOrFail($customerId);
                    $customer->updatePaymentMode($paymentMode, $walletSettings);
                    $updatedCount++;

                    // Trigger event for each customer
                    event('customer.payment_mode.updated', [
                        'customer_id' => $customer->pk_customer_code,
                        'old_payment_mode' => $customer->getOriginal('payment_mode'),
                        'new_payment_mode' => $paymentMode,
                        'wallet_settings' => $walletSettings,
                        'updated_at' => now(),
                        'bulk_update' => true,
                    ]);
                } catch (\Exception $e) {
                    $errors[] = [
                        'customer_id' => $customerId,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => "Payment mode updated for {$updatedCount} customers",
                'data' => [
                    'updated_count' => $updatedCount,
                    'total_requested' => count($customerIds),
                    'errors' => $errors,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to bulk update payment mode',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

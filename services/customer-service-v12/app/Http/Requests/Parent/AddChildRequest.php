<?php

namespace App\Http\Requests\Parent;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * AddChildRequest
 *
 * Validation rules for adding a child to a parent's account.
 */
class AddChildRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'school_id' => [
                'required',
                'integer',
                'exists:schools,id',
                function ($attribute, $value, $fail) {
                    $school = \App\Models\School::find($value);
                    if (!$school || !$school->is_active || $school->partnership_status !== 'active') {
                        $fail('The selected school is not available for enrollment.');
                    }
                },
            ],
            'first_name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s]+$/',
            ],
            'last_name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s]+$/',
            ],
            'date_of_birth' => [
                'required',
                'date',
                'before:today',
                'after:' . now()->subYears(18)->toDateString(),
            ],
            'gender' => [
                'nullable',
                Rule::in(['male', 'female', 'other']),
            ],
            'grade_level' => [
                'required',
                'string',
                'max:20',
                'regex:/^[a-zA-Z0-9\s]+$/',
            ],
            'section' => [
                'nullable',
                'string',
                'max:10',
                'regex:/^[a-zA-Z0-9]+$/',
            ],
            'roll_number' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[a-zA-Z0-9\-\/]+$/',
            ],
            'student_id' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[a-zA-Z0-9\-\/]+$/',
                function ($attribute, $value, $fail) {
                    if ($value && $this->input('school_id')) {
                        $exists = \App\Models\ChildProfile::where('school_id', $this->input('school_id'))
                            ->where('student_id', $value)
                            ->exists();
                        if ($exists) {
                            $fail('This student ID already exists in the selected school.');
                        }
                    }
                },
            ],
            'dietary_restrictions' => [
                'nullable',
                'array',
                'max:10',
            ],
            'dietary_restrictions.*' => [
                'string',
                Rule::in([
                    'nuts', 'dairy', 'gluten', 'eggs', 'soy', 'fish', 'shellfish',
                    'sesame', 'mustard', 'celery', 'lupin', 'molluscs', 'sulphites'
                ]),
            ],
            'food_preferences' => [
                'nullable',
                'array',
            ],
            'food_preferences.likes' => [
                'nullable',
                'array',
                'max:20',
            ],
            'food_preferences.likes.*' => [
                'string',
                'max:100',
            ],
            'food_preferences.dislikes' => [
                'nullable',
                'array',
                'max:20',
            ],
            'food_preferences.dislikes.*' => [
                'string',
                'max:100',
            ],
            'meal_type_preference' => [
                'nullable',
                Rule::in(['vegetarian', 'non_vegetarian', 'vegan', 'jain']),
            ],
            'spice_level' => [
                'nullable',
                Rule::in(['no_spice', 'mild', 'medium', 'spicy']),
            ],
            'emergency_contacts' => [
                'required',
                'array',
                'min:1',
                'max:3',
            ],
            'emergency_contacts.*.name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s]+$/',
            ],
            'emergency_contacts.*.phone' => [
                'required',
                'string',
                'regex:/^[6-9]\d{9}$/',
            ],
            'emergency_contacts.*.relation' => [
                'required',
                'string',
                'max:50',
                Rule::in([
                    'father', 'mother', 'guardian', 'uncle', 'aunt', 
                    'grandfather', 'grandmother', 'sibling', 'other'
                ]),
            ],
            'medical_conditions' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'special_instructions' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'preferred_break_time' => [
                'nullable',
                Rule::in(['morning_break', 'lunch_break', 'both']),
            ],
            'delivery_instructions' => [
                'nullable',
                'array',
                'max:5',
            ],
            'delivery_instructions.*' => [
                'string',
                'max:200',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'school_id.required' => 'Please select a school.',
            'school_id.exists' => 'The selected school is invalid.',
            'first_name.required' => 'Child\'s first name is required.',
            'first_name.regex' => 'First name should only contain letters and spaces.',
            'last_name.required' => 'Child\'s last name is required.',
            'last_name.regex' => 'Last name should only contain letters and spaces.',
            'date_of_birth.required' => 'Date of birth is required.',
            'date_of_birth.before' => 'Date of birth must be in the past.',
            'date_of_birth.after' => 'Child must be under 18 years old.',
            'grade_level.required' => 'Grade level is required.',
            'grade_level.regex' => 'Grade level format is invalid.',
            'section.regex' => 'Section should only contain letters and numbers.',
            'roll_number.regex' => 'Roll number format is invalid.',
            'student_id.regex' => 'Student ID format is invalid.',
            'dietary_restrictions.*.in' => 'Invalid dietary restriction selected.',
            'emergency_contacts.required' => 'At least one emergency contact is required.',
            'emergency_contacts.min' => 'At least one emergency contact is required.',
            'emergency_contacts.max' => 'Maximum 3 emergency contacts allowed.',
            'emergency_contacts.*.name.required' => 'Emergency contact name is required.',
            'emergency_contacts.*.name.regex' => 'Contact name should only contain letters and spaces.',
            'emergency_contacts.*.phone.required' => 'Emergency contact phone is required.',
            'emergency_contacts.*.phone.regex' => 'Please enter a valid 10-digit mobile number.',
            'emergency_contacts.*.relation.required' => 'Relationship is required.',
            'emergency_contacts.*.relation.in' => 'Invalid relationship selected.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'school_id' => 'school',
            'first_name' => 'first name',
            'last_name' => 'last name',
            'date_of_birth' => 'date of birth',
            'grade_level' => 'grade level',
            'roll_number' => 'roll number',
            'student_id' => 'student ID',
            'meal_type_preference' => 'meal preference',
            'spice_level' => 'spice preference',
            'emergency_contacts' => 'emergency contacts',
            'medical_conditions' => 'medical conditions',
            'special_instructions' => 'special instructions',
            'preferred_break_time' => 'preferred break time',
            'delivery_instructions' => 'delivery instructions',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'first_name' => trim($this->first_name ?? ''),
            'last_name' => trim($this->last_name ?? ''),
            'grade_level' => trim($this->grade_level ?? ''),
            'section' => trim($this->section ?? ''),
            'roll_number' => trim($this->roll_number ?? ''),
            'student_id' => trim($this->student_id ?? ''),
            'meal_type_preference' => $this->meal_type_preference ?? 'vegetarian',
            'spice_level' => $this->spice_level ?? 'mild',
            'preferred_break_time' => $this->preferred_break_time ?? 'lunch_break',
        ]);

        // Clean emergency contacts phone numbers
        if ($this->has('emergency_contacts') && is_array($this->emergency_contacts)) {
            $contacts = $this->emergency_contacts;
            foreach ($contacts as &$contact) {
                if (isset($contact['phone'])) {
                    $contact['phone'] = preg_replace('/[^0-9]/', '', $contact['phone']);
                }
                if (isset($contact['name'])) {
                    $contact['name'] = trim($contact['name']);
                }
            }
            $this->merge(['emergency_contacts' => $contacts]);
        }
    }
}

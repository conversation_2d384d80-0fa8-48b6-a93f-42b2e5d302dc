<?php

namespace App\Http\Requests\Parent;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

/**
 * RegisterParentRequest
 *
 * Validation rules for parent customer registration in the school tiffin system.
 */
class RegisterParentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s]+$/',
            ],
            'phone' => [
                'required',
                'string',
                'regex:/^[6-9]\d{9}$/',
                'unique:customers,phone',
            ],
            'email_address' => [
                'required',
                'email',
                'max:255',
                'unique:customers,email_address',
            ],
            'password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
            ],
            'address' => [
                'nullable',
                'string',
                'max:500',
            ],
            'city' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s]+$/',
            ],
            'tenant_id' => [
                'nullable',
                'integer',
                'exists:tenants,id',
            ],
            'company_id' => [
                'nullable',
                'integer',
            ],
            'unit_id' => [
                'nullable',
                'integer',
            ],
            'terms_accepted' => [
                'required',
                'accepted',
            ],
            'privacy_policy_accepted' => [
                'required',
                'accepted',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'customer_name.required' => 'Full name is required.',
            'customer_name.regex' => 'Full name should only contain letters and spaces.',
            'phone.required' => 'Phone number is required.',
            'phone.regex' => 'Please enter a valid 10-digit Indian mobile number.',
            'phone.unique' => 'This phone number is already registered.',
            'email_address.required' => 'Email address is required.',
            'email_address.email' => 'Please enter a valid email address.',
            'email_address.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required.',
            'password.confirmed' => 'Password confirmation does not match.',
            'city.regex' => 'City name should only contain letters and spaces.',
            'terms_accepted.accepted' => 'You must accept the terms and conditions.',
            'privacy_policy_accepted.accepted' => 'You must accept the privacy policy.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'customer_name' => 'full name',
            'phone' => 'phone number',
            'email_address' => 'email address',
            'terms_accepted' => 'terms and conditions',
            'privacy_policy_accepted' => 'privacy policy',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'phone' => preg_replace('/[^0-9]/', '', $this->phone ?? ''),
            'customer_name' => trim($this->customer_name ?? ''),
            'email_address' => strtolower(trim($this->email_address ?? '')),
            'city' => trim($this->city ?? ''),
        ]);
    }
}

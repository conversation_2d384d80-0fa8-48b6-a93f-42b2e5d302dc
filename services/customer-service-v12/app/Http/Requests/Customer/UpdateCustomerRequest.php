<?php

namespace App\Http\Requests\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Update Customer Request
 * 
 * This request validates the data for updating an existing customer.
 */
class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $customerId = $this->route('id');
        
        return [
            'customer_name' => ['required', 'string', 'max:255'],
            'phone' => [
                'required', 
                'string', 
                'max:20', 
                Rule::unique('customers', 'phone')->ignore($customerId, 'pk_customer_code')
            ],
            'email_address' => [
                'nullable', 
                'email', 
                'max:255', 
                Rule::unique('customers', 'email_address')->ignore($customerId, 'pk_customer_code')
            ],
            'customer_address' => ['nullable', 'string'],
            'location_code' => ['nullable', 'string', 'max:50'],
            'location_name' => ['nullable', 'string', 'max:100'],
            'lunch_loc_code' => ['nullable', 'string', 'max:50'],
            'lunch_loc_name' => ['nullable', 'string', 'max:100'],
            'lunch_add' => ['nullable', 'string'],
            'dinner_loc_code' => ['nullable', 'string', 'max:50'],
            'dinner_loc_name' => ['nullable', 'string', 'max:100'],
            'dinner_add' => ['nullable', 'string'],
            'food_preference' => ['nullable', 'string', 'max:50'],
            'city' => ['nullable', 'string', 'max:50'],
            'city_name' => ['nullable', 'string', 'max:100'],
            'company_name' => ['nullable', 'string', 'max:100'],
            'group_code' => ['nullable', 'string', 'max:50'],
            'group_name' => ['nullable', 'string', 'max:100'],
            'registered_from' => ['nullable', 'string', 'max:50'],
            'password' => ['nullable', 'string', 'min:6'],
            'source' => ['nullable', 'string', 'max:50'],
            'referer' => ['nullable', 'string'],
            'alt_phone' => ['nullable', 'string', 'max:20'],
            'company_id' => ['nullable', 'integer'],
            'unit_id' => ['nullable', 'integer'],
            'is_guest' => ['nullable', 'boolean'],
            'delivery_note' => ['nullable', 'string'],
            'status' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'customer_name.required' => 'The customer name is required.',
            'phone.required' => 'The phone number is required.',
            'phone.unique' => 'This phone number is already registered with another customer.',
            'email_address.email' => 'Please provide a valid email address.',
            'email_address.unique' => 'This email address is already registered with another customer.',
            'password.min' => 'The password must be at least 6 characters.',
        ];
    }
}

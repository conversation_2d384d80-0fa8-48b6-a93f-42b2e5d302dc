<?php

namespace App\Http\Requests\Customer;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Add Address Request
 * 
 * This request validates the data for adding or updating a customer address.
 */
class AddAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'address_type' => ['required', 'string', 'max:50'],
            'address_name' => ['nullable', 'string', 'max:100'],
            'address_line1' => ['required', 'string'],
            'address_line2' => ['nullable', 'string'],
            'landmark' => ['nullable', 'string'],
            'city' => ['required', 'string', 'max:50'],
            'state' => ['nullable', 'string', 'max:50'],
            'country' => ['nullable', 'string', 'max:50'],
            'pincode' => ['nullable', 'string', 'max:20'],
            'latitude' => ['nullable', 'numeric'],
            'longitude' => ['nullable', 'numeric'],
            'is_default' => ['nullable', 'boolean'],
            'company_id' => ['nullable', 'integer'],
            'unit_id' => ['nullable', 'integer'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'address_type.required' => 'The address type is required.',
            'address_line1.required' => 'The address line 1 is required.',
            'city.required' => 'The city is required.',
            'latitude.numeric' => 'The latitude must be a number.',
            'longitude.numeric' => 'The longitude must be a number.',
        ];
    }
}

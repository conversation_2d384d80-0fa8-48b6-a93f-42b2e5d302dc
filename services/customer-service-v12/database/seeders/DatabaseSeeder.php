<?php

namespace Database\Seeders;


use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Clear existing data to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Only clear customer service tables, not catalogue tables
        DB::table('wallet_transactions')->truncate();
        DB::table('child_profiles')->truncate();
        DB::table('customer_address')->truncate();
        DB::table('customer_wallet')->truncate();
        DB::table('customers')->truncate();
        DB::table('schools')->truncate();

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create schools first (using DB insert to avoid model issues)
        $schools = [
            [
                'tenant_id' => 1,
                'company_id' => 1,
                'unit_id' => 1,
                'school_name' => 'Delhi Public School',
                'school_code' => 'DPS001',
                'address' => 'Sector 45, Gurgaon, Haryana',
                'city' => 'Gurgaon',
                'state' => 'Haryana',
                'postal_code' => '122003',
                'contact_person_name' => 'Admin Office',
                'contact_phone' => '+91-124-4513000',
                'contact_email' => '<EMAIL>',
                'principal_name' => 'Dr. Aditi Misra',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:30', 'end' => '11:00'],
                    'lunch_break' => ['start' => '12:30', 'end' => '13:30']
                ]),
                'delivery_zones' => json_encode(['zone_1' => 'Main Building', 'zone_2' => 'Secondary Block']),
                'grade_levels' => json_encode(['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']),
                'partnership_start_date' => now()->subYears(2)->format('Y-m-d'),
                'partnership_status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tenant_id' => 1,
                'company_id' => 1,
                'unit_id' => 1,
                'school_name' => 'Ryan International School',
                'school_code' => 'RIS001',
                'address' => 'Sector 31, Gurgaon, Haryana',
                'city' => 'Gurgaon',
                'state' => 'Haryana',
                'postal_code' => '122001',
                'contact_person_name' => 'School Office',
                'contact_phone' => '+91-124-4090900',
                'contact_email' => '<EMAIL>',
                'principal_name' => 'Mrs. Poonam Kochhar',
                'break_times' => json_encode([
                    'morning_break' => ['start' => '10:15', 'end' => '10:45'],
                    'lunch_break' => ['start' => '12:15', 'end' => '13:15']
                ]),
                'delivery_zones' => json_encode(['zone_1' => 'Primary Wing', 'zone_2' => 'Senior Wing']),
                'grade_levels' => json_encode(['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']),
                'partnership_start_date' => now()->subYears(1)->format('Y-m-d'),
                'partnership_status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($schools as $school) {
            DB::table('schools')->insert($school);
        }

        // Create customers
        $customers = [
            [
                'pk_customer_code' => 1,
                'customer_name' => 'Rajesh Kumar',
                'phone' => '+91-9876543210',
                'email_address' => '<EMAIL>',
                'customer_Address' => 'A-123, Green Park, Near Metro Station, New Delhi - 110016',
                'location_code' => 'DEL001',
                'location_name' => 'Green Park',
                'food_preference' => 'vegetarian',
                'city' => 'DEL',
                'city_name' => 'New Delhi',
                'company_name' => 'Tech Solutions Pvt Ltd',
                'registered_on' => now()->subDays(30),
                'registered_from' => 'mobile_app',
                'status' => true,
                'password' => Hash::make('password123'),
                'phone_verified' => true,
                'email_verified' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'pk_customer_code' => 2,
                'customer_name' => 'Priya Sharma',
                'phone' => '+91-9876543211',
                'email_address' => '<EMAIL>',
                'customer_Address' => 'B-456, Lajpat Nagar, Central Market, New Delhi - 110024',
                'location_code' => 'DEL002',
                'location_name' => 'Lajpat Nagar',
                'food_preference' => 'non_vegetarian',
                'city' => 'DEL',
                'city_name' => 'New Delhi',
                'company_name' => 'Digital Marketing Agency',
                'registered_on' => now()->subDays(25),
                'registered_from' => 'website',
                'status' => true,
                'password' => Hash::make('password123'),
                'phone_verified' => true,
                'email_verified' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'pk_customer_code' => 3,
                'customer_name' => 'Amit Singh',
                'phone' => '+91-9876543212',
                'email_address' => '<EMAIL>',
                'customer_Address' => 'C-789, Sector 15, Near City Center, Gurgaon - 122001',
                'location_code' => 'GUR001',
                'location_name' => 'Sector 15',
                'food_preference' => 'vegetarian',
                'city' => 'GUR',
                'city_name' => 'Gurgaon',
                'company_name' => 'Financial Services Ltd',
                'registered_on' => now()->subDays(20),
                'registered_from' => 'mobile_app',
                'status' => true,
                'password' => Hash::make('password123'),
                'phone_verified' => true,
                'email_verified' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'pk_customer_code' => 4,
                'customer_name' => 'Sunita Gupta',
                'phone' => '+91-**********',
                'email_address' => '<EMAIL>',
                'customer_Address' => 'D-321, Karol Bagh, Main Market, New Delhi - 110005',
                'location_code' => 'DEL003',
                'location_name' => 'Karol Bagh',
                'food_preference' => 'jain_vegetarian',
                'city' => 'DEL',
                'city_name' => 'New Delhi',
                'company_name' => 'Healthcare Solutions',
                'registered_on' => now()->subDays(15),
                'registered_from' => 'referral',
                'status' => true,
                'password' => Hash::make('password123'),
                'phone_verified' => true,
                'email_verified' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'pk_customer_code' => 5,
                'customer_name' => 'Vikram Patel',
                'phone' => '+91-**********',
                'email_address' => '<EMAIL>',
                'customer_Address' => 'E-654, Sector 21, Near Mall, Gurgaon - 122015',
                'location_code' => 'GUR002',
                'location_name' => 'Sector 21',
                'food_preference' => 'non_vegetarian',
                'city' => 'GUR',
                'city_name' => 'Gurgaon',
                'company_name' => 'Software Development Inc',
                'registered_on' => now()->subDays(10),
                'registered_from' => 'mobile_app',
                'status' => false,
                'password' => Hash::make('password123'),
                'phone_verified' => false,
                'email_verified' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($customers as $customer) {
            DB::table('customers')->insert($customer);
        }

        // Create customer addresses (using correct table name and columns)
        $addresses = [
            [
                'customer_code' => 1,
                'address_type' => 'home',
                'address_name' => 'Home Address',
                'address_line1' => 'A-123, Green Park',
                'address_line2' => 'Near Metro Station',
                'city' => 'New Delhi',
                'state' => 'Delhi',
                'country' => 'India',
                'pincode' => '110016',
                'is_default' => true,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'customer_code' => 1,
                'address_type' => 'office',
                'address_name' => 'Office Address',
                'address_line1' => 'Tower B, Cyber City',
                'address_line2' => 'DLF Phase 2',
                'city' => 'Gurgaon',
                'state' => 'Haryana',
                'country' => 'India',
                'pincode' => '122002',
                'is_default' => false,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'customer_code' => 2,
                'address_type' => 'home',
                'address_name' => 'Home Address',
                'address_line1' => 'B-456, Lajpat Nagar',
                'address_line2' => 'Central Market',
                'city' => 'New Delhi',
                'state' => 'Delhi',
                'country' => 'India',
                'pincode' => '110024',
                'is_default' => true,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'customer_code' => 3,
                'address_type' => 'home',
                'address_name' => 'Home Address',
                'address_line1' => 'C-789, Sector 15',
                'address_line2' => 'Near City Center',
                'city' => 'Gurgaon',
                'state' => 'Haryana',
                'country' => 'India',
                'pincode' => '122001',
                'is_default' => true,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($addresses as $address) {
            DB::table('customer_address')->insert($address);
        }

        // Create customer wallets
        $wallets = [
            [
                'customer_code' => 1,
                'balance' => 1500.00,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'customer_code' => 2,
                'balance' => 2500.00,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'customer_code' => 3,
                'balance' => 750.00,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'customer_code' => 4,
                'balance' => 3200.00,
                'status' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'customer_code' => 5,
                'balance' => 0.00,
                'status' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($wallets as $wallet) {
            DB::table('customer_wallet')->insert($wallet);
        }

        echo "Created " . count($customers) . " customers\n";
        echo "Created " . count($schools) . " schools\n";
        echo "Created " . count($addresses) . " addresses\n";
        echo "Created " . count($wallets) . " wallets\n";
    }
}

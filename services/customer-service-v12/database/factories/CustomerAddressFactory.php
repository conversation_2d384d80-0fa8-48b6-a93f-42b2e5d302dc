<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\CustomerAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerAddress>
 */
class CustomerAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_code' => Customer::factory(),
            'address_type' => $this->faker->randomElement(['Home', 'Work', 'Other']),
            'address_name' => $this->faker->optional()->word(),
            'address_line1' => $this->faker->streetAddress(),
            'address_line2' => $this->faker->optional()->secondaryAddress(),
            'landmark' => $this->faker->optional()->sentence(2),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => $this->faker->country(),
            'pincode' => $this->faker->postcode(),
            'latitude' => $this->faker->optional()->latitude(),
            'longitude' => $this->faker->optional()->longitude(),
            'is_default' => false,
            'status' => true,
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }

    /**
     * Indicate that the address is the default address.
     */
    public function default(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    /**
     * Indicate that the address is inactive.
     */
    public function inactive(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the address is of type 'Home'.
     */
    public function home(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'address_type' => 'Home',
        ]);
    }

    /**
     * Indicate that the address is of type 'Work'.
     */
    public function work(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'address_type' => 'Work',
        ]);
    }
}

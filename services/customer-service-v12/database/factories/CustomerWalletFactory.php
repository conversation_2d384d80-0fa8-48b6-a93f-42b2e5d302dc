<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\CustomerWallet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerWallet>
 */
class CustomerWalletFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerWallet::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_code' => Customer::factory(),
            'balance' => $this->faker->randomFloat(2, 0, 1000),
            'status' => true,
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }

    /**
     * Indicate that the wallet is inactive.
     */
    public function inactive(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the wallet has a zero balance.
     */
    public function empty(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'balance' => 0,
        ]);
    }

    /**
     * Indicate that the wallet has a specific balance.
     */
    public function withBalance(float $balance): Factory
    {
        return $this->state(fn (array $attributes) => [
            'balance' => $balance,
        ]);
    }
}

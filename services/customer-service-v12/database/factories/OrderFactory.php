<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $customer = Customer::factory()->create();
        $amount = $this->faker->randomFloat(2, 100, 1000);
        $tax = $amount * 0.05;
        $deliveryCharges = $this->faker->randomFloat(2, 10, 50);
        $serviceCharges = $this->faker->randomFloat(2, 5, 20);
        $appliedDiscount = $this->faker->randomFloat(2, 0, 100);
        
        return [
            'order_no' => 'ORD' . $this->faker->unique()->numerify('######'),
            'ref_order' => $this->faker->optional()->numerify('REF######'),
            'customer_code' => $customer->pk_customer_code,
            'customer_name' => $customer->customer_name,
            'phone' => $customer->phone,
            'email_address' => $customer->email_address,
            'location_code' => $customer->location_code,
            'location_name' => $customer->location_name,
            'city' => $customer->city,
            'city_name' => $customer->city_name,
            'product_code' => 'PROD' . $this->faker->numerify('####'),
            'product_name' => $this->faker->word(),
            'quantity' => $this->faker->numberBetween(1, 5),
            'amount' => $amount,
            'tax' => $tax,
            'delivery_charges' => $deliveryCharges,
            'service_charges' => $serviceCharges,
            'applied_discount' => $appliedDiscount,
            'order_status' => $this->faker->randomElement(['New', 'Processing', 'Completed', 'Cancelled']),
            'delivery_status' => $this->faker->randomElement(['Pending', 'Dispatched', 'Delivered']),
            'invoice_status' => $this->faker->randomElement(['Unbill', 'Billed', 'Paid']),
            'order_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'ship_address' => $this->faker->address(),
            'order_menu' => $this->faker->randomElement(['lunch', 'dinner', 'breakfast']),
            'food_type' => $this->faker->randomElement(['Veg', 'Non-Veg', 'Vegan']),
            'payment_mode' => $this->faker->randomElement(['cash', 'card', 'wallet', 'upi']),
            'amount_paid' => $this->faker->randomFloat(2, 0, $amount + $tax + $deliveryCharges + $serviceCharges - $appliedDiscount),
            'promo_code' => $this->faker->optional()->bothify('PROMO##??'),
            'tax_method' => $this->faker->randomElement(['inclusive', 'exclusive']),
            'source' => $this->faker->randomElement(['Web', 'Mobile', 'Admin']),
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }

    /**
     * Indicate that the order is new.
     */
    public function new(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'order_status' => 'New',
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
        ]);
    }

    /**
     * Indicate that the order is completed.
     */
    public function completed(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'order_status' => 'Completed',
            'delivery_status' => 'Delivered',
            'invoice_status' => 'Paid',
            'amount_paid' => fn (array $attributes) => 
                $attributes['amount'] + $attributes['tax'] + $attributes['delivery_charges'] + $attributes['service_charges'] - $attributes['applied_discount'],
        ]);
    }

    /**
     * Indicate that the order is cancelled.
     */
    public function cancelled(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'order_status' => 'Cancelled',
        ]);
    }
}

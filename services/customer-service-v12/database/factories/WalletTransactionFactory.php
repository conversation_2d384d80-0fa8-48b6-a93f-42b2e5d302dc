<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\WalletTransaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WalletTransaction>
 */
class WalletTransactionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WalletTransaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['deposit', 'withdrawal', 'transfer', 'refund', 'payment']);
        $amount = $this->faker->randomFloat(2, 10, 1000);
        $beforeBalance = $this->faker->randomFloat(2, 100, 5000);
        $afterBalance = match ($type) {
            'deposit', 'refund' => $beforeBalance + $amount,
            'withdrawal', 'payment' => $beforeBalance - $amount,
            'transfer' => $beforeBalance,
            default => $beforeBalance,
        };

        return [
            'customer_code' => Customer::factory(),
            'amount' => $amount,
            'type' => $type,
            'description' => $this->faker->sentence(),
            'transaction_id' => $this->faker->unique()->uuid(),
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'cancelled']),
            'metadata' => null,
        ];
    }

    /**
     * Indicate that the transaction is a deposit.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function deposit()
    {
        return $this->state(function (array $attributes) {
            $amount = $this->faker->randomFloat(2, 10, 1000);
            return [
                'type' => 'deposit',
                'amount' => $amount,
                'after_balance' => $attributes['before_balance'] + $amount,
                'status' => 'completed',
            ];
        });
    }

    /**
     * Indicate that the transaction is a withdrawal.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withdrawal()
    {
        return $this->state(function (array $attributes) {
            $amount = $this->faker->randomFloat(2, 10, min(1000, $attributes['before_balance']));
            return [
                'type' => 'withdrawal',
                'amount' => $amount,
                'after_balance' => $attributes['before_balance'] - $amount,
                'status' => 'completed',
            ];
        });
    }

    /**
     * Indicate that the transaction is pending.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
            ];
        });
    }

    /**
     * Indicate that the transaction has failed.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function failed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'failed',
            ];
        });
    }
}

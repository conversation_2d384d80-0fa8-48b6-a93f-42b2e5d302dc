<?php

namespace Database\Factories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_name' => $this->faker->name(),
            'phone' => $this->faker->unique()->numerify('##########'),
            'email_address' => $this->faker->unique()->safeEmail(),
            'customer_Address' => $this->faker->address(),
            'location_code' => 'LOC' . $this->faker->numberBetween(1, 100),
            'location_name' => $this->faker->city(),
            'food_preference' => $this->faker->randomElement(['Veg', 'Non-Veg', 'Vegan']),
            'city' => $this->faker->city(),
            'city_name' => $this->faker->city(),
            'company_name' => $this->faker->company(),
            'group_code' => 'GRP' . $this->faker->numberBetween(1, 100),
            'group_name' => $this->faker->word(),
            'registered_on' => now(),
            'registered_from' => $this->faker->randomElement(['Web', 'Mobile', 'Admin']),
            'status' => true,
            'password' => Hash::make('password'),
            'phone_verified' => true,
            'email_verified' => true,
            'subscription_notification' => $this->faker->boolean(),
            'source' => $this->faker->randomElement(['Web', 'Mobile', 'Referral']),
            'referer' => $this->faker->optional()->url(),
            'alt_phone' => $this->faker->optional()->numerify('##########'),
            'company_id' => 1,
            'unit_id' => 1,
            'isguest' => false,
            'delivery_note' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the customer is inactive.
     */
    public function inactive(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the customer is a guest.
     */
    public function guest(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'isguest' => true,
            'password' => null,
        ]);
    }

    /**
     * Indicate that the customer's phone is not verified.
     */
    public function unverifiedPhone(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'phone_verified' => false,
        ]);
    }

    /**
     * Indicate that the customer's email is not verified.
     */
    public function unverifiedEmail(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'email_verified' => false,
        ]);
    }
}

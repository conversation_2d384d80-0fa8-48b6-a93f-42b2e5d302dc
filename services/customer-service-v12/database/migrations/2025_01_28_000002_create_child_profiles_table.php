<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('child_profiles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            
            // Parent relationship
            $table->unsignedBigInteger('parent_customer_id');
            $table->foreign('parent_customer_id')->references('pk_customer_code')->on('customers')->onDelete('cascade');
            
            // School relationship
            $table->unsignedBigInteger('school_id');
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('restrict');
            
            // Child basic information
            $table->string('first_name');
            $table->string('last_name');
            $table->string('full_name')->virtualAs("CONCAT(first_name, ' ', last_name)");
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            
            // School information
            $table->string('grade_level', 20);
            $table->string('section', 10)->nullable();
            $table->string('roll_number', 50)->nullable();
            $table->string('student_id', 100)->nullable();
            
            // Dietary restrictions and preferences
            $table->json('dietary_restrictions')->nullable()->comment('Allergies, medical restrictions: ["nuts", "dairy", "gluten"]');
            $table->json('food_preferences')->nullable()->comment('Likes/dislikes: {"likes": ["rice", "dal"], "dislikes": ["spicy"]}');
            $table->enum('meal_type_preference', ['vegetarian', 'non_vegetarian', 'vegan', 'jain'])->default('vegetarian');
            $table->enum('spice_level', ['no_spice', 'mild', 'medium', 'spicy'])->default('mild');
            
            // Emergency contact information
            $table->json('emergency_contacts')->comment('Emergency contact details: [{"name": "Guardian", "phone": "+91XXXXXXXXXX", "relation": "uncle"}]');
            
            // Medical information
            $table->text('medical_conditions')->nullable()->comment('Any medical conditions affecting diet');
            $table->text('special_instructions')->nullable()->comment('Special meal preparation instructions');
            
            // Delivery preferences
            $table->enum('preferred_break_time', ['morning_break', 'lunch_break', 'both'])->default('lunch_break');
            $table->json('delivery_instructions')->nullable()->comment('Special delivery instructions for this child');
            
            // Status and tracking
            $table->boolean('is_active')->default(true);
            $table->date('enrollment_date')->default(now());
            $table->date('last_meal_date')->nullable();
            $table->integer('total_meals_consumed')->default(0);
            
            // Parent verification
            $table->boolean('parent_verified')->default(false);
            $table->timestamp('parent_verified_at')->nullable();
            $table->string('verification_method')->nullable()->comment('phone_otp, email_verification, document_upload');
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['tenant_id', 'company_id', 'unit_id'], 'idx_child_profiles_tenant');
            $table->index(['parent_customer_id'], 'idx_child_profiles_parent');
            $table->index(['school_id'], 'idx_child_profiles_school');
            $table->index(['school_id', 'grade_level'], 'idx_child_profiles_school_grade');
            $table->index(['is_active', 'enrollment_date'], 'idx_child_profiles_active');
            $table->index(['student_id'], 'idx_child_profiles_student_id');
            
            // Unique constraint for student within school
            $table->unique(['school_id', 'student_id'], 'uk_child_profiles_school_student');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('child_profiles');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id('pk_customer_code');
            $table->string('customer_name');
            $table->string('phone', 20)->unique();
            $table->string('email_address')->nullable()->unique();
            $table->text('customer_Address')->nullable();
            $table->string('location_code', 50)->nullable();
            $table->string('location_name', 100)->nullable();
            $table->string('lunch_loc_code', 50)->nullable();
            $table->string('lunch_loc_name', 100)->nullable();
            $table->text('lunch_add')->nullable();
            $table->string('dinner_loc_code', 50)->nullable();
            $table->string('dinner_loc_name', 100)->nullable();
            $table->text('dinner_add')->nullable();
            $table->string('food_preference', 50)->nullable();
            $table->string('city', 50)->nullable();
            $table->string('city_name', 100)->nullable();
            $table->string('company_name', 100)->nullable();
            $table->string('group_code', 50)->nullable();
            $table->string('group_name', 100)->nullable();
            $table->timestamp('registered_on')->nullable();
            $table->string('registered_from', 50)->nullable();
            $table->string('food_referance', 100)->nullable();
            $table->boolean('status')->default(true);
            $table->string('otp', 10)->nullable();
            $table->string('password')->nullable();
            $table->string('thirdparty', 50)->nullable();
            $table->boolean('phone_verified')->default(false);
            $table->boolean('subscription_notification')->default(false);
            $table->boolean('email_verified')->default(false);
            $table->string('source', 50)->nullable();
            $table->string('referer')->nullable();
            $table->string('gcm_id')->nullable();
            $table->string('alt_phone', 20)->nullable();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->string('dabbawala_code_type')->nullable();
            $table->string('dabbawala_code')->nullable();
            $table->string('dabbawala_image')->nullable();
            $table->boolean('isguest')->default(false);
            $table->text('delivery_note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};

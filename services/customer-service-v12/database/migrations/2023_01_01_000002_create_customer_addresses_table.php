<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_address', function (Blueprint $table) {
            $table->id('pk_customer_address_code');
            $table->unsignedBigInteger('customer_code');
            $table->string('address_type', 50);
            $table->string('address_name', 100)->nullable();
            $table->string('address_line1');
            $table->string('address_line2')->nullable();
            $table->string('landmark')->nullable();
            $table->string('city', 50);
            $table->string('state', 50)->nullable();
            $table->string('country', 50)->nullable();
            $table->string('pincode', 20)->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('status')->default(true);
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamps();
            
            $table->foreign('customer_code')
                  ->references('pk_customer_code')
                  ->on('customers')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_address');
    }
};

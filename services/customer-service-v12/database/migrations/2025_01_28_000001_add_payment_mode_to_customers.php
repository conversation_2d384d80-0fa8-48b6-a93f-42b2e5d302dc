<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->enum('payment_mode', ['wallet', 'direct'])
                  ->default('wallet')
                  ->after('delivery_note')
                  ->comment('Customer preferred payment mode: wallet or direct');
            
            $table->json('payment_mode_settings')
                  ->nullable()
                  ->after('payment_mode')
                  ->comment('Payment mode specific settings (wallet settings, etc.)');
            
            $table->timestamp('payment_mode_updated_at')
                  ->nullable()
                  ->after('payment_mode_settings')
                  ->comment('Last time payment mode was updated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn([
                'payment_mode',
                'payment_mode_settings',
                'payment_mode_updated_at'
            ]);
        });
    }
};

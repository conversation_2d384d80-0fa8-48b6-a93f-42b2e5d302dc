<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_wallet', function (Blueprint $table) {
            $table->id('pk_wallet_id');
            $table->unsignedBigInteger('customer_code');
            $table->decimal('balance', 10, 2)->default(0);
            $table->boolean('status')->default(true);
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamps();
            
            $table->foreign('customer_code')
                  ->references('pk_customer_code')
                  ->on('customers')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_wallet');
    }
};

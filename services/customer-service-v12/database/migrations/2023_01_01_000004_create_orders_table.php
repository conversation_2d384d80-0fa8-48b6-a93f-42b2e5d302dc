<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id('pk_order_no');
            $table->string('order_no')->unique();
            $table->string('ref_order')->nullable();
            $table->unsignedBigInteger('customer_code');
            $table->string('customer_name');
            $table->string('phone', 20);
            $table->string('email_address')->nullable();
            $table->string('location_code', 50)->nullable();
            $table->string('location_name', 100)->nullable();
            $table->string('city', 50)->nullable();
            $table->string('city_name', 100)->nullable();
            $table->string('product_code', 50)->nullable();
            $table->string('product_name')->nullable();
            $table->integer('quantity')->default(1);
            $table->decimal('amount', 10, 2)->default(0);
            $table->decimal('tax', 10, 2)->default(0);
            $table->decimal('delivery_charges', 10, 2)->default(0);
            $table->decimal('service_charges', 10, 2)->default(0);
            $table->decimal('applied_discount', 10, 2)->default(0);
            $table->string('order_status', 50)->default('New');
            $table->string('delivery_status', 50)->default('Pending');
            $table->string('invoice_status', 50)->default('Unbill');
            $table->date('order_date');
            $table->text('ship_address')->nullable();
            $table->string('order_menu', 50);
            $table->string('food_type', 50)->nullable();
            $table->string('payment_mode', 50)->default('cash');
            $table->decimal('amount_paid', 10, 2)->default(0);
            $table->string('promo_code', 50)->nullable();
            $table->string('tax_method', 20)->default('exclusive');
            $table->string('source', 50)->nullable();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamps();
            
            $table->foreign('customer_code')
                  ->references('pk_customer_code')
                  ->on('customers')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};

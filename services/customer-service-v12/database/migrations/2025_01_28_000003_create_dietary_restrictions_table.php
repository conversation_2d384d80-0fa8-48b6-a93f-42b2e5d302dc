<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dietary_restrictions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            
            // Restriction details
            $table->string('restriction_name');
            $table->string('restriction_code')->unique();
            $table->text('description')->nullable();
            
            // Categorization
            $table->enum('restriction_type', ['allergy', 'medical', 'religious', 'preference', 'lifestyle'])->default('preference');
            $table->enum('severity_level', ['mild', 'moderate', 'severe', 'life_threatening'])->default('mild');
            
            // Ingredients to avoid
            $table->json('ingredients_to_avoid')->comment('List of ingredients that should be avoided: ["nuts", "dairy", "eggs"]');
            $table->json('alternative_ingredients')->nullable()->comment('Suggested alternatives: {"dairy": ["coconut_milk", "almond_milk"]}');
            
            // Meal preparation guidelines
            $table->text('preparation_guidelines')->nullable()->comment('Special preparation instructions');
            $table->boolean('requires_separate_preparation')->default(false);
            $table->boolean('cross_contamination_risk')->default(false);
            
            // Status and metadata
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0)->comment('Number of children with this restriction');
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['tenant_id', 'company_id', 'unit_id'], 'idx_dietary_restrictions_tenant');
            $table->index(['restriction_type', 'severity_level'], 'idx_dietary_restrictions_type_severity');
            $table->index(['is_active'], 'idx_dietary_restrictions_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dietary_restrictions');
    }
};

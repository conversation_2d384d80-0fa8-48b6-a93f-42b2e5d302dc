<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schools', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('unit_id');
            
            // School basic information
            $table->string('school_name');
            $table->string('school_code')->unique();
            $table->text('address');
            $table->string('city', 100);
            $table->string('state', 100);
            $table->string('postal_code', 20);
            $table->string('country', 100)->default('India');
            
            // Contact information
            $table->string('contact_person_name');
            $table->string('contact_phone', 20);
            $table->string('contact_email')->nullable();
            $table->string('principal_name')->nullable();
            $table->string('principal_phone', 20)->nullable();
            $table->string('principal_email')->nullable();
            
            // Geographic coordinates for delivery optimization
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            
            // Break time configuration (JSON format for flexibility)
            $table->json('break_times')->comment('Break time schedules: {morning_break: {start: "10:30", end: "11:00"}, lunch_break: {start: "12:30", end: "13:30"}}');
            
            // Delivery configuration
            $table->json('delivery_zones')->comment('Delivery zone boundaries and time windows');
            $table->integer('delivery_window_minutes')->default(30)->comment('Delivery window in minutes before break time');
            $table->boolean('bulk_delivery_enabled')->default(true);
            
            // School metadata
            $table->enum('school_type', ['primary', 'secondary', 'higher_secondary', 'mixed'])->default('mixed');
            $table->integer('total_students')->nullable();
            $table->json('grade_levels')->nullable()->comment('Available grade levels: ["1", "2", "3", ...]');
            
            // Business relationship
            $table->date('partnership_start_date');
            $table->date('partnership_end_date')->nullable();
            $table->enum('partnership_status', ['active', 'inactive', 'suspended', 'terminated'])->default('active');
            $table->decimal('commission_percentage', 5, 2)->default(0.00)->comment('Commission percentage for school partnership');
            
            // Status and metadata
            $table->boolean('is_active')->default(true);
            $table->json('special_requirements')->nullable()->comment('Special delivery or meal requirements');
            $table->text('notes')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['tenant_id', 'company_id', 'unit_id'], 'idx_schools_tenant');
            $table->index(['school_code'], 'idx_schools_code');
            $table->index(['city', 'state'], 'idx_schools_location');
            $table->index(['partnership_status', 'is_active'], 'idx_schools_status');
            $table->index(['latitude', 'longitude'], 'idx_schools_coordinates');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schools');
    }
};

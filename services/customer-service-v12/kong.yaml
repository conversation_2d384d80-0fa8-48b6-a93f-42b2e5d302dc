_format_version: "3.0"
_transform: true

services:
  - name: customer-service
    url: http://customer-service-v12:8000
    routes:
      - name: customer-service-route
        paths:
          - /v2/customers
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
      - name: key-auth
        config:
          key_names:
            - apikey
          key_in_body: false
          key_in_header: true
          key_in_query: true
          hide_credentials: true
          run_on_preflight: false
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_timeout: 2000
      - name: proxy-cache
        config:
          content_type:
            - application/json
          cache_ttl: 30
          strategy: memory
          memory:
            dictionary_name: kong_db_cache
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service-Name:customer-service
              - X-Service-Version:v12
      - name: response-transformer
        config:
          add:
            headers:
              - X-Powered-By:Laravel/12.0
              - X-Service:customer-service-v12

consumers:
  - username: customer-service-client
    keyauth_credentials:
      - key: your-api-key-here

upstreams:
  - name: customer-service-upstream
    targets:
      - target: customer-service-v12:8000
        weight: 100
    healthchecks:
      active:
        http_path: /api/v2/health
        healthy:
          interval: 5
          successes: 1
        unhealthy:
          interval: 5
          http_failures: 2
          timeouts: 2

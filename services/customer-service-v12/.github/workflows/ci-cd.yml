name: Customer Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'services/customer-service-v12/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'services/customer-service-v12/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_DATABASE: testing
          MYSQL_ROOT_PASSWORD: password
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: mbstring, dom, fileinfo, mysql, pdo_mysql
          coverage: xdebug
      
      - name: Change to service directory
        run: cd services/customer-service-v12
      
      - name: Copy .env
        run: php -r "file_exists('.env') || copy('.env.example', '.env');"
        working-directory: services/customer-service-v12
      
      - name: Install Dependencies
        run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
        working-directory: services/customer-service-v12
      
      - name: Generate key
        run: php artisan key:generate
        working-directory: services/customer-service-v12
      
      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache
        working-directory: services/customer-service-v12
      
      - name: Configure Database
        run: |
          php artisan config:clear
          php artisan migrate --seed --force
        working-directory: services/customer-service-v12
        env:
          DB_CONNECTION: mysql
          DB_HOST: 127.0.0.1
          DB_PORT: 3306
          DB_DATABASE: testing
          DB_USERNAME: root
          DB_PASSWORD: password
      
      - name: Execute tests (Unit and Feature tests) via PHPUnit
        run: vendor/bin/phpunit --coverage-clover=coverage.xml
        working-directory: services/customer-service-v12
        env:
          DB_CONNECTION: mysql
          DB_HOST: 127.0.0.1
          DB_PORT: 3306
          DB_DATABASE: testing
          DB_USERNAME: root
          DB_PASSWORD: password
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: services/customer-service-v12/coverage.xml
          flags: customer-service
          fail_ci_if_error: true
  
  build-and-push:
    needs: test
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      
      - name: Extract branch name
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV
      
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: ./services/customer-service-v12
          push: true
          tags: |
            cubeonebiz/customer-service:${{ env.BRANCH_NAME }}
            cubeonebiz/customer-service:${{ env.BRANCH_NAME }}-${{ github.sha }}
          cache-from: type=registry,ref=cubeonebiz/customer-service:${{ env.BRANCH_NAME }}
          cache-to: type=inline
  
  deploy:
    needs: build-and-push
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    runs-on: ubuntu-latest
    
    steps:
      - name: Extract branch name
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV
      
      - name: Set environment
        run: |
          if [ "${{ env.BRANCH_NAME }}" == "main" ]; then
            echo "ENVIRONMENT=production" >> $GITHUB_ENV
          else
            echo "ENVIRONMENT=staging" >> $GITHUB_ENV
          fi
      
      - name: Deploy to Kubernetes
        uses: actions-hub/kubectl@master
        env:
          KUBE_CONFIG: ${{ secrets.KUBE_CONFIG }}
        with:
          args: set image deployment/customer-service customer-service=cubeonebiz/customer-service:${{ env.BRANCH_NAME }}-${{ github.sha }} -n ${{ env.ENVIRONMENT }}
      
      - name: Verify deployment
        uses: actions-hub/kubectl@master
        env:
          KUBE_CONFIG: ${{ secrets.KUBE_CONFIG }}
        with:
          args: rollout status deployment/customer-service -n ${{ env.ENVIRONMENT }}

# Customer Service Migration to Laravel 12

This document outlines the migration of the Customer Service from Zend Framework to Laravel 12.

## Overview

The Customer Service is responsible for managing customer data, including:

- Customer profiles
- Customer addresses
- Customer wallets
- Wallet transactions

The service provides RESTful APIs for these operations and integrates with other microservices through the Kong API Gateway.

## Migration Steps

### 1. Project Structure

The Laravel 12 project follows PSR-4 standards with the following directory structure:

```
services/customer-service-v12/
├── app/
│   ├── DTOs/                 # Data Transfer Objects
│   ├── Events/               # Event classes
│   ├── Exceptions/           # Custom exceptions
│   ├── Http/                 # Controllers, Middleware, Requests
│   ├── Models/               # Eloquent models
│   ├── Providers/            # Service providers
│   └── Services/             # Business logic services
├── bootstrap/                # Application bootstrap files
├── config/                   # Configuration files
├── database/                 # Migrations, seeders, factories
├── public/                   # Public assets
├── resources/                # Views, language files
├── routes/                   # Route definitions
└── tests/                    # Unit and feature tests
```

### 2. Database Schema

The database schema includes the following tables:

- `customers`: Customer profiles
- `customer_addresses`: Customer addresses
- `customer_wallets`: Customer wallet balances
- `wallet_transactions`: Wallet transaction history
- `personal_access_tokens`: Sanctum tokens for authentication

### 3. API Endpoints

The service provides the following API endpoints:

#### Customer Endpoints

- `GET /api/v2/customers`: Get all customers
- `POST /api/v2/customers`: Create a customer
- `GET /api/v2/customers/{id}`: Get a customer by ID
- `PUT /api/v2/customers/{id}`: Update a customer
- `DELETE /api/v2/customers/{id}`: Delete a customer

#### Address Endpoints

- `POST /api/v2/customers/{id}/addresses`: Add an address to a customer
- `PUT /api/v2/customers/{id}/addresses/{addressId}`: Update a customer address
- `DELETE /api/v2/customers/{id}/addresses/{addressId}`: Delete a customer address

#### Wallet Endpoints

- `GET /api/v2/customers/{id}/wallet`: Get a customer's wallet
- `POST /api/v2/customers/{id}/wallet/deposit`: Deposit to a customer's wallet
- `POST /api/v2/customers/{id}/wallet/withdraw`: Withdraw from a customer's wallet
- `GET /api/v2/customers/{id}/wallet/transactions`: Get wallet transaction history

### 4. Authentication

The service uses Laravel Sanctum for API authentication. The Auth Service provides the authentication tokens, which are then used to authenticate requests to the Customer Service.

### 5. Service Layer

The service layer encapsulates all business logic and provides a clean interface for the controllers. The main services are:

- `CustomerService`: Manages customer operations
- `WalletService`: Manages wallet operations

### 6. Data Transfer Objects (DTOs)

DTOs are used to transfer data between layers and validate input data. The main DTOs are:

- `CustomerDTO`: Customer data
- `AddressDTO`: Address data

### 7. Event System

The service uses Laravel's event system to handle asynchronous operations. The main events are:

- `CustomerCreated`: Fired when a customer is created
- `CustomerUpdated`: Fired when a customer is updated
- `CustomerDeleted`: Fired when a customer is deleted
- `WalletDeposited`: Fired when a deposit is made to a wallet
- `WalletWithdrawn`: Fired when a withdrawal is made from a wallet

### 8. Testing

The service includes comprehensive tests for all components:

- Unit tests for services and models
- Feature tests for API endpoints
- Integration tests for database operations

### 9. API Documentation

The API is documented using the OpenAPI Specification (OAS) 3.0. The documentation is available in the `openapi-customer.yaml` file.

### 10. Kong API Gateway Integration

The service is integrated with Kong API Gateway, which provides:

- Authentication and authorization
- Rate limiting
- Request routing
- Logging and monitoring

## Migration Decisions

### 1. Service-Oriented Architecture

The service follows a service-oriented architecture with clear separation of concerns:

- Controllers handle HTTP requests and responses
- Services encapsulate business logic
- Models represent database entities
- DTOs transfer data between layers

### 2. Event-Driven Design

The service uses an event-driven design to handle asynchronous operations and decouple components. Events are fired for significant operations, and listeners can be added to handle these events.

### 3. Repository Pattern

The service uses the repository pattern to abstract database operations. This makes it easier to change the database implementation or add caching in the future.

### 4. API Versioning

The service supports API versioning to ensure backward compatibility. The current version is v2, and the v1 endpoints are maintained for backward compatibility.

## Future Improvements

1. **Caching**: Add caching for frequently accessed data
2. **Rate Limiting**: Implement more granular rate limiting
3. **Metrics**: Add metrics collection for monitoring
4. **Documentation**: Improve API documentation with more examples
5. **Performance**: Optimize database queries and add indexes

## Conclusion

The Customer Service has been successfully migrated to Laravel 12, providing a modern, maintainable, and scalable solution for customer management. The service follows best practices for microservices architecture and provides a clean API for other services to consume.

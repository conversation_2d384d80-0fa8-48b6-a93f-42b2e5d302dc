openapi: 3.0.3
info:
  title: Customer Service V12 - Kong-Compliant API
  description: |
    Customer management microservice API with comprehensive Kong API Gateway integration.

    This service handles customer data, payment modes, wallet management, and related operations
    through Kong API Gateway with JWT authentication, rate limiting, and CORS support.

    ## Kong Integration Features
    - **Service Name**: customer-service-v12
    - **Route Pattern**: /v2/customer-service-v12/*
    - **Authentication**: JWT with RS256 (24-hour tokens)
    - **Rate Limiting**: 60 requests/minute, 1000 requests/hour
    - **CORS**: Enabled for web applications
    - **Health Checks**: Automated service monitoring

    ## Service Capabilities
    - Customer profile management
    - Payment mode configuration (wallet/direct)
    - Wallet balance and transaction management
    - Customer statistics and analytics
    - Bulk operations for administrative tasks
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 Development Team
    email: <EMAIL>
    url: https://docs.onefooddialer.com/customer-service
  license:
    name: Proprietary
    url: https://onefooddialer.com/license

servers:
  - url: https://api.onefooddialer.com/v2/customer-service-v12
    description: Production Kong API Gateway
    variables:
      version:
        default: v2
        description: API version
  - url: https://staging-api.onefooddialer.com/v2/customer-service-v12
    description: Staging Kong API Gateway
  - url: http://localhost:8000/v2/customer-service-v12
    description: Local development Kong Gateway

# Kong-specific service configuration
x-kong-service:
  name: customer-service-v12
  url: http://customer-service-v12:8000
  connect_timeout: 60000
  write_timeout: 60000
  read_timeout: 60000
  retries: 5
  protocol: http
  tags:
    - customer-management
    - microservice
    - v12

# Kong route configuration
x-kong-route:
  name: customer-service-route
  paths:
    - /v2/customer-service-v12
  preserve_host: true
  strip_path: false
  protocols:
    - http
    - https
  tags:
    - customer
    - protected

# Kong plugin configurations
x-kong-plugins:
  - name: jwt
    config:
      secret_is_base64: false
      claims_to_verify:
        - exp
        - nbf
        - iat
      key_claim_name: iss
      algorithm: RS256
      maximum_expiration: 86400  # 24 hours
      run_on_preflight: false
    enabled: true
    protocols:
      - http
      - https

  - name: rate-limiting
    config:
      minute: 60
      hour: 1000
      day: 10000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
      redis_host: redis
      redis_port: 6379
      redis_timeout: 2000
    enabled: true
    protocols:
      - http
      - https

  - name: cors
    config:
      origins:
        - "https://app.onefooddialer.com"
        - "https://admin.onefooddialer.com"
        - "http://localhost:3000"
        - "http://localhost:3001"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        - X-Requested-With
        - X-Correlation-ID
      exposed_headers:
        - X-Auth-Token
        - X-RateLimit-Limit
        - X-RateLimit-Remaining
        - X-RateLimit-Reset
        - X-Correlation-ID
      credentials: true
      max_age: 3600
      preflight_continue: false
    enabled: true
    protocols:
      - http
      - https

  - name: request-transformer
    config:
      add:
        headers:
          - "X-Service: customer-service-v12"
          - "X-Gateway: kong"
      append:
        headers:
          - "X-Correlation-ID: $(uuid)"
    enabled: true

  - name: response-transformer
    config:
      add:
        headers:
          - "X-Powered-By: OneFoodDialer-2025"
          - "X-API-Version: v2"
      remove:
        headers:
          - "Server"
          - "X-Powered-By: PHP"
    enabled: true

  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
    enabled: true

  - name: acl
    config:
      whitelist:
        - customer
        - admin
        - mobile
        - web
    enabled: true

paths:
  # Health check endpoint
  /health:
    get:
      summary: Customer Service Health Check
      description: Check the health status of the customer service
      operationId: getCustomerServiceHealth
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
          headers:
            X-Service-Name:
              $ref: '#/components/headers/X-Service-Name'
            X-Correlation-ID:
              $ref: '#/components/headers/X-Correlation-ID'
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      x-kong-plugin-rate-limiting:
        config:
          minute: 120  # Higher limit for health checks
          hour: 2000
      x-kong-plugin-cors:
        enabled: true

  /customers/{customerId}/payment-mode:
    get:
      summary: Get customer payment mode preferences
      description: Retrieve the payment mode and settings for a specific customer
      operationId: getCustomerPaymentMode
      tags:
        - Payment Mode
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
            format: int64
            example: 123
        - name: X-Correlation-ID
          in: header
          required: false
          description: Request correlation ID for tracing
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Payment mode retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Payment mode retrieved successfully
                  data:
                    $ref: '#/components/schemas/PaymentModeResponse'
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
            X-RateLimit-Reset:
              $ref: '#/components/headers/X-RateLimit-Reset'
            X-Correlation-ID:
              $ref: '#/components/headers/X-Correlation-ID'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []
      x-kong-plugin-rate-limiting:
        config:
          minute: 60
          hour: 1000
      x-kong-plugin-acl:
        config:
          whitelist:
            - customer
            - admin

    put:
      summary: Update customer payment mode preferences
      description: Update the payment mode and settings for a specific customer
      tags:
        - Payment Mode
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
            format: int64
            example: 123
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentModeUpdateRequest'
      responses:
        '200':
          description: Payment mode updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Payment mode updated successfully
                  data:
                    $ref: '#/components/schemas/PaymentModeResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /payment-modes/statistics:
    get:
      summary: Get payment mode statistics
      description: Retrieve statistics about payment mode usage across customers
      tags:
        - Payment Mode
      parameters:
        - name: company_id
          in: query
          description: Company ID to filter statistics
          schema:
            type: integer
            example: 1
        - name: unit_id
          in: query
          description: Unit ID to filter statistics
          schema:
            type: integer
            example: 1
      responses:
        '200':
          description: Statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Payment mode statistics retrieved successfully
                  data:
                    $ref: '#/components/schemas/PaymentModeStatistics'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /payment-modes/bulk-update:
    post:
      summary: Bulk update payment mode for multiple customers
      description: Update payment mode and settings for multiple customers at once
      tags:
        - Payment Mode
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkPaymentModeUpdateRequest'
      responses:
        '200':
          description: Bulk update completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Payment mode updated for 5 customers
                  data:
                    $ref: '#/components/schemas/BulkUpdateResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication using RS256 algorithm.

        Token format: `Bearer <jwt-token>`

        **Token Specifications:**
        - Algorithm: RS256
        - Expiration: 24 hours
        - Refresh Token: 7 days
        - Claims: iss, exp, nbf, iat, sub, aud

        **Example:**
        ```
        Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
        ```

  headers:
    X-Correlation-ID:
      description: Unique request correlation ID for tracing
      schema:
        type: string
        format: uuid
      example: "123e4567-e89b-12d3-a456-************"

    X-RateLimit-Limit:
      description: Rate limit threshold for the current window
      schema:
        type: integer
      example: 60

    X-RateLimit-Remaining:
      description: Number of requests remaining in current window
      schema:
        type: integer
      example: 45

    X-RateLimit-Reset:
      description: Unix timestamp when the rate limit window resets
      schema:
        type: integer
      example: **********

    X-Service-Name:
      description: Name of the service that handled the request
      schema:
        type: string
      example: "customer-service-v12"

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  error_code:
                    example: UNAUTHORIZED
                  message:
                    example: Authentication required
      headers:
        WWW-Authenticate:
          schema:
            type: string
          description: Authentication scheme
          example: Bearer realm="OneFoodDialer API"

    Forbidden:
      description: Access forbidden
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  error_code:
                    example: FORBIDDEN
                  message:
                    example: Access forbidden

    RateLimitExceeded:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RateLimitResponse'
      headers:
        X-RateLimit-Limit:
          $ref: '#/components/headers/X-RateLimit-Limit'
        X-RateLimit-Remaining:
          $ref: '#/components/headers/X-RateLimit-Remaining'
        X-RateLimit-Reset:
          $ref: '#/components/headers/X-RateLimit-Reset'
        Retry-After:
          schema:
            type: integer
          description: Seconds to wait before retrying

  schemas:
    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          example: healthy
        timestamp:
          type: string
          format: date-time
          example: "2025-01-28T10:30:00Z"
        version:
          type: string
          example: "2.0.0"
        service:
          type: string
          example: "customer-service-v12"
        database:
          type: object
          properties:
            status:
              type: string
              enum: [connected, disconnected]
              example: connected
            response_time:
              type: number
              format: float
              description: Database response time in milliseconds
              example: 15.5
        dependencies:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "auth-service-v12"
              status:
                type: string
                enum: [healthy, unhealthy]
                example: healthy
              response_time:
                type: number
                format: float
                example: 25.3

    RateLimitResponse:
      type: object
      properties:
        status:
          type: string
          enum: [error]
          example: error
        message:
          type: string
          example: Rate limit exceeded
        error_code:
          type: string
          example: RATE_LIMIT_EXCEEDED
        retry_after:
          type: integer
          description: Seconds to wait before retrying
          example: 60
        limit:
          type: integer
          description: Rate limit threshold
          example: 60
        remaining:
          type: integer
          description: Remaining requests in current window
          example: 0
        reset:
          type: integer
          description: Unix timestamp when limit resets
          example: **********

    PaymentModeResponse:
      type: object
      properties:
        customer_id:
          type: integer
          format: int64
          example: 123
        payment_mode:
          type: string
          enum: [wallet, direct]
          example: wallet
        payment_mode_settings:
          $ref: '#/components/schemas/WalletSettings'
        payment_mode_updated_at:
          type: string
          format: date-time
          example: "2025-01-28T10:30:00Z"

    WalletSettings:
      type: object
      properties:
        minimum_balance:
          type: number
          format: float
          minimum: 0
          example: 100.00
        auto_reload_enabled:
          type: boolean
          example: false
        auto_reload_amount:
          type: number
          format: float
          minimum: 1
          example: 500.00
        auto_reload_threshold:
          type: number
          format: float
          minimum: 0
          example: 50.00

    PaymentModeUpdateRequest:
      type: object
      required:
        - payment_mode
      properties:
        payment_mode:
          type: string
          enum: [wallet, direct]
          example: wallet
        wallet_settings:
          $ref: '#/components/schemas/WalletSettings'

    PaymentModeStatistics:
      type: object
      properties:
        total_customers:
          type: integer
          example: 1000
        wallet_customers:
          type: integer
          example: 750
        direct_customers:
          type: integer
          example: 250
        auto_reload_enabled:
          type: integer
          example: 300
        wallet_percentage:
          type: number
          format: float
          example: 75.0
        direct_percentage:
          type: number
          format: float
          example: 25.0
        auto_reload_percentage:
          type: number
          format: float
          example: 30.0

    BulkPaymentModeUpdateRequest:
      type: object
      required:
        - customer_ids
        - payment_mode
      properties:
        customer_ids:
          type: array
          items:
            type: integer
            format: int64
          minItems: 1
          example: [123, 456, 789]
        payment_mode:
          type: string
          enum: [wallet, direct]
          example: wallet
        wallet_settings:
          $ref: '#/components/schemas/WalletSettings'

    BulkUpdateResponse:
      type: object
      properties:
        updated_count:
          type: integer
          example: 5
        total_requested:
          type: integer
          example: 5
        errors:
          type: array
          items:
            type: object
            properties:
              customer_id:
                type: integer
                example: 123
              error:
                type: string
                example: Customer not found

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: An error occurred
        error:
          type: string
          example: Detailed error message

    ValidationErrorResponse:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: Validation failed
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

tags:
  - name: Payment Mode
    description: Customer payment mode management endpoints

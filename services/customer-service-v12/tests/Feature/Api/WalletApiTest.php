<?php

namespace Tests\Feature\Api;

use App\Models\Customer;
use App\Models\CustomerWallet;
use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class WalletApiTest extends TestCase
{
    use RefreshDatabase;

    protected Customer $customer;
    protected CustomerWallet $wallet;

    protected function setUp(): void
    {
        parent::setUp();

        // Create and authenticate a user for all tests
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create test data
        $this->customer = Customer::factory()->create();
        $this->wallet = CustomerWallet::factory()->create([
            'customer_code' => $this->customer->pk_customer_code,
            'balance' => 100.00,
            'status' => true
        ]);
    }

    /** @test */
    public function it_can_get_a_wallet()
    {
        // Act
        $response = $this->getJson("/api/v2/customers/{$this->customer->pk_customer_code}/wallet");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'customer_code' => $this->customer->pk_customer_code,
                    'balance' => 100.00,
                    'status' => true
                ]
            ]);
    }

    /** @test */
    public function it_returns_404_when_wallet_not_found()
    {
        // Arrange
        $nonExistentCustomerId = 999;

        // Act
        $response = $this->getJson("/api/v2/customers/{$nonExistentCustomerId}/wallet");

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => "Customer with ID {$nonExistentCustomerId} not found"
            ]);
    }

    /** @test */
    public function it_can_deposit_to_wallet()
    {
        // Arrange
        $data = [
            'amount' => 50.00,
            'description' => 'Test deposit'
        ];

        // Act
        $response = $this->postJson("/api/v2/customers/{$this->customer->pk_customer_code}/wallet/deposit", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Deposit successful',
                'data' => [
                    'customer_code' => $this->customer->pk_customer_code,
                    'balance' => 150.00,
                    'status' => true
                ]
            ]);

        $this->assertDatabaseHas('wallet_transactions', [
            'customer_code' => $this->customer->pk_customer_code,
            'amount' => 50.00,
            'type' => 'deposit',
            'description' => 'Test deposit',
            'before_balance' => 100.00,
            'after_balance' => 150.00,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function it_validates_deposit_amount()
    {
        // Arrange
        $data = [
            'amount' => -50.00,
            'description' => 'Test deposit'
        ];

        // Act
        $response = $this->postJson("/api/v2/customers/{$this->customer->pk_customer_code}/wallet/deposit", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['amount']);
    }

    /** @test */
    public function it_can_withdraw_from_wallet()
    {
        // Arrange
        $data = [
            'amount' => 50.00,
            'description' => 'Test withdrawal'
        ];

        // Act
        $response = $this->postJson("/api/v2/customers/{$this->customer->pk_customer_code}/wallet/withdraw", $data);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Withdrawal successful',
                'data' => [
                    'customer_code' => $this->customer->pk_customer_code,
                    'balance' => 50.00,
                    'status' => true
                ]
            ]);

        $this->assertDatabaseHas('wallet_transactions', [
            'customer_code' => $this->customer->pk_customer_code,
            'amount' => 50.00,
            'type' => 'withdrawal',
            'description' => 'Test withdrawal',
            'before_balance' => 100.00,
            'after_balance' => 50.00,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function it_returns_400_when_insufficient_balance()
    {
        // Arrange
        $data = [
            'amount' => 150.00, // More than the balance
            'description' => 'Test withdrawal'
        ];

        // Act
        $response = $this->postJson("/api/v2/customers/{$this->customer->pk_customer_code}/wallet/withdraw", $data);

        // Assert
        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Insufficient balance for withdrawal'
            ]);
    }

    /** @test */
    public function it_can_get_transaction_history()
    {
        // Arrange
        WalletTransaction::factory()->count(5)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'deposit'
        ]);

        WalletTransaction::factory()->count(3)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'withdrawal'
        ]);

        // Act
        $response = $this->getJson("/api/v2/customers/{$this->customer->pk_customer_code}/wallet/transactions");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'total' => 8
                ]
            ]);
    }

    /** @test */
    public function it_can_filter_transaction_history_by_type()
    {
        // Arrange
        WalletTransaction::factory()->count(5)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'deposit'
        ]);

        WalletTransaction::factory()->count(3)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'withdrawal'
        ]);

        // Act
        $response = $this->getJson("/api/v2/customers/{$this->customer->pk_customer_code}/wallet/transactions?type=deposit");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'total' => 5
                ]
            ]);
    }
}

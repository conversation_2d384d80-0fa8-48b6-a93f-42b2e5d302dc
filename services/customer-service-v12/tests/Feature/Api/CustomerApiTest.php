<?php

namespace Tests\Feature\Api;

use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CustomerApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create and authenticate a user for all tests
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /** @test */
    public function it_can_get_all_customers()
    {
        // Arrange
        Customer::factory()->count(5)->create();

        // Act
        $response = $this->getJson('/api/v2/customers');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data' => [
                        '*' => [
                            'pk_customer_code',
                            'customer_name',
                            'phone',
                            'email_address',
                        ]
                    ],
                    'current_page',
                    'per_page',
                    'total'
                ]
            ])
            ->assertJsonCount(5, 'data.data')
            ->assertJson([
                'success' => true
            ]);
    }

    /** @test */
    public function it_can_filter_customers_by_status()
    {
        // Arrange
        Customer::factory()->count(3)->create(['status' => true]);
        Customer::factory()->count(2)->create(['status' => false]);

        // Act
        $response = $this->getJson('/api/v2/customers?status=1');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data.data')
            ->assertJson([
                'success' => true
            ]);
    }

    /** @test */
    public function it_can_search_customers()
    {
        // Arrange
        Customer::factory()->create([
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);
        Customer::factory()->create([
            'customer_name' => 'Jane Smith',
            'phone' => '9876543210',
            'email_address' => '<EMAIL>',
        ]);

        // Act
        $response = $this->getJson('/api/v2/customers?search=John');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data.data')
            ->assertJson([
                'success' => true,
                'data' => [
                    'data' => [
                        [
                            'customer_name' => 'John Doe',
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_get_a_customer_by_id()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);

        // Act
        $response = $this->getJson("/api/v2/customers/{$customer->pk_customer_code}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'pk_customer_code' => $customer->pk_customer_code,
                    'customer_name' => 'John Doe',
                    'phone' => '1234567890',
                    'email_address' => '<EMAIL>',
                ]
            ]);
    }

    /** @test */
    public function it_returns_404_when_customer_not_found()
    {
        // Act
        $response = $this->getJson('/api/v2/customers/999');

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Customer with ID 999 not found'
            ]);
    }

    /** @test */
    public function it_can_create_a_customer()
    {
        // Arrange
        $customerData = [
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'password' => 'password',
            'food_preference' => 'Veg',
            'city' => 'New York',
        ];

        // Act
        $response = $this->postJson('/api/v2/customers', $customerData);

        // Assert
        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Customer created successfully',
                'data' => [
                    'customer_name' => 'John Doe',
                    'phone' => '1234567890',
                    'email_address' => '<EMAIL>',
                    'food_preference' => 'Veg',
                    'city' => 'New York',
                ]
            ]);

        $this->assertDatabaseHas('customers', [
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('customer_wallet', [
            'customer_code' => $response->json('data.pk_customer_code'),
            'balance' => 0,
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_a_customer()
    {
        // Arrange
        $customerData = [
            'email_address' => '<EMAIL>',
        ];

        // Act
        $response = $this->postJson('/api/v2/customers', $customerData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['customer_name', 'phone']);
    }

    /** @test */
    public function it_validates_unique_fields_when_creating_a_customer()
    {
        // Arrange
        Customer::factory()->create([
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);

        $customerData = [
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ];

        // Act
        $response = $this->postJson('/api/v2/customers', $customerData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone', 'email_address']);
    }
    /** @test */
    public function it_can_update_a_customer()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);

        $updateData = [
            'customer_name' => 'Jane Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ];

        // Act
        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}", $updateData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Customer updated successfully',
                'data' => [
                    'pk_customer_code' => $customer->pk_customer_code,
                    'customer_name' => 'Jane Doe',
                    'phone' => '1234567890',
                    'email_address' => '<EMAIL>',
                ]
            ]);

        $this->assertDatabaseHas('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
            'customer_name' => 'Jane Doe',
            'email_address' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_updating_a_customer()
    {
        // Arrange
        $customer = Customer::factory()->create();

        $updateData = [
            'customer_name' => '',
            'phone' => '',
        ];

        // Act
        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['customer_name', 'phone']);
    }

    /** @test */
    public function it_validates_unique_fields_when_updating_a_customer()
    {
        // Arrange
        $customer1 = Customer::factory()->create([
            'phone' => '1111111111',
            'email_address' => '<EMAIL>',
        ]);

        $customer2 = Customer::factory()->create([
            'phone' => '2222222222',
            'email_address' => '<EMAIL>',
        ]);

        $updateData = [
            'customer_name' => 'John Doe',
            'phone' => '2222222222', // Trying to use customer2's phone
            'email_address' => '<EMAIL>', // Trying to use customer2's email
        ];

        // Act
        $response = $this->putJson("/api/v2/customers/{$customer1->pk_customer_code}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone', 'email_address']);
    }

    /** @test */
    public function it_can_delete_a_customer()
    {
        // Arrange
        $customer = Customer::factory()->create();

        // Act
        $response = $this->deleteJson("/api/v2/customers/{$customer->pk_customer_code}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Customer deleted successfully'
            ]);

        $this->assertDatabaseMissing('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
        ]);
    }

    /** @test */
    public function it_returns_404_when_deleting_non_existent_customer()
    {
        // Act
        $response = $this->deleteJson('/api/v2/customers/999');

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Customer with ID 999 not found'
            ]);
    }

    /** @test */
    public function it_can_add_an_address_to_a_customer()
    {
        // Arrange
        $customer = Customer::factory()->create();

        $addressData = [
            'address_type' => 'Home',
            'address_line1' => '123 Main St',
            'address_line2' => 'Apt 4B',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'pincode' => '10001',
            'is_default' => true,
        ];

        // Act
        $response = $this->postJson("/api/v2/customers/{$customer->pk_customer_code}/addresses", $addressData);

        // Assert
        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Address added successfully',
                'data' => [
                    'customer_code' => $customer->pk_customer_code,
                    'address_type' => 'Home',
                    'address_line1' => '123 Main St',
                    'address_line2' => 'Apt 4B',
                    'city' => 'New York',
                    'state' => 'NY',
                    'country' => 'USA',
                    'pincode' => '10001',
                    'is_default' => true,
                ]
            ]);

        $this->assertDatabaseHas('customer_address', [
            'customer_code' => $customer->pk_customer_code,
            'address_type' => 'Home',
            'address_line1' => '123 Main St',
            'city' => 'New York',
        ]);
    }
}
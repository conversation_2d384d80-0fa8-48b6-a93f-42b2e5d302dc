<?php

namespace Tests\Feature\Api\V2;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class PaymentModeControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);
    }

    /** @test */
    public function it_can_get_customer_payment_mode()
    {
        $customer = Customer::factory()->create([
            'payment_mode' => 'wallet',
            'payment_mode_settings' => [
                'minimum_balance' => 150,
                'auto_reload_enabled' => true,
                'auto_reload_amount' => 500,
                'auto_reload_threshold' => 50,
            ],
        ]);

        $response = $this->getJson("/api/v2/customers/{$customer->pk_customer_code}/payment-mode");

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'customer_id' => $customer->pk_customer_code,
                        'payment_mode' => 'wallet',
                        'payment_mode_settings' => [
                            'minimum_balance' => 150,
                            'auto_reload_enabled' => true,
                            'auto_reload_amount' => 500,
                            'auto_reload_threshold' => 50,
                        ],
                    ],
                ]);
    }

    /** @test */
    public function it_returns_404_for_non_existent_customer()
    {
        $response = $this->getJson('/api/v2/customers/99999/payment-mode');

        $response->assertStatus(500); // Model not found throws 500 in our implementation
    }

    /** @test */
    public function it_can_update_customer_payment_mode_to_wallet()
    {
        $customer = Customer::factory()->create(['payment_mode' => 'direct']);

        $walletSettings = [
            'minimum_balance' => 200,
            'auto_reload_enabled' => true,
            'auto_reload_amount' => 1000,
            'auto_reload_threshold' => 100,
        ];

        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}/payment-mode", [
            'payment_mode' => 'wallet',
            'wallet_settings' => $walletSettings,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'customer_id' => $customer->pk_customer_code,
                        'payment_mode' => 'wallet',
                        'payment_mode_settings' => $walletSettings,
                    ],
                ]);

        $this->assertDatabaseHas('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
            'payment_mode' => 'wallet',
        ]);
    }

    /** @test */
    public function it_can_update_customer_payment_mode_to_direct()
    {
        $customer = Customer::factory()->create([
            'payment_mode' => 'wallet',
            'payment_mode_settings' => ['minimum_balance' => 100],
        ]);

        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}/payment-mode", [
            'payment_mode' => 'direct',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'customer_id' => $customer->pk_customer_code,
                        'payment_mode' => 'direct',
                    ],
                ]);

        $this->assertDatabaseHas('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
            'payment_mode' => 'direct',
        ]);
    }

    /** @test */
    public function it_validates_payment_mode_field()
    {
        $customer = Customer::factory()->create();

        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}/payment-mode", [
            'payment_mode' => 'invalid_mode',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['payment_mode']);
    }

    /** @test */
    public function it_requires_payment_mode_field()
    {
        $customer = Customer::factory()->create();

        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}/payment-mode", []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['payment_mode']);
    }

    /** @test */
    public function it_validates_wallet_settings_when_provided()
    {
        $customer = Customer::factory()->create();

        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}/payment-mode", [
            'payment_mode' => 'wallet',
            'wallet_settings' => [
                'minimum_balance' => -10, // Invalid negative value
                'auto_reload_amount' => 'invalid', // Invalid type
            ],
        ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function it_validates_auto_reload_amount_greater_than_threshold()
    {
        $customer = Customer::factory()->create();

        $response = $this->putJson("/api/v2/customers/{$customer->pk_customer_code}/payment-mode", [
            'payment_mode' => 'wallet',
            'wallet_settings' => [
                'auto_reload_enabled' => true,
                'auto_reload_amount' => 50,
                'auto_reload_threshold' => 100, // Threshold greater than amount
            ],
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'status' => 'error',
                    'message' => 'Auto-reload amount must be greater than threshold',
                ]);
    }

    /** @test */
    public function it_can_get_payment_mode_statistics()
    {
        // Create customers with different payment modes
        Customer::factory()->count(3)->create(['payment_mode' => 'wallet']);
        Customer::factory()->count(2)->create(['payment_mode' => 'direct']);
        Customer::factory()->create([
            'payment_mode' => 'wallet',
            'payment_mode_settings' => ['auto_reload_enabled' => true],
        ]);

        $response = $this->getJson('/api/v2/payment-modes/statistics');

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'total_customers' => 6,
                        'wallet_customers' => 4,
                        'direct_customers' => 2,
                        'auto_reload_enabled' => 1,
                    ],
                ]);
    }

    /** @test */
    public function it_can_bulk_update_payment_mode()
    {
        $customers = Customer::factory()->count(3)->create(['payment_mode' => 'direct']);
        $customerIds = $customers->pluck('pk_customer_code')->toArray();

        $walletSettings = [
            'minimum_balance' => 100,
            'auto_reload_enabled' => false,
        ];

        $response = $this->postJson('/api/v2/payment-modes/bulk-update', [
            'customer_ids' => $customerIds,
            'payment_mode' => 'wallet',
            'wallet_settings' => $walletSettings,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'updated_count' => 3,
                        'total_requested' => 3,
                        'errors' => [],
                    ],
                ]);

        foreach ($customerIds as $customerId) {
            $this->assertDatabaseHas('customers', [
                'pk_customer_code' => $customerId,
                'payment_mode' => 'wallet',
            ]);
        }
    }

    /** @test */
    public function it_validates_bulk_update_customer_ids()
    {
        $response = $this->postJson('/api/v2/payment-modes/bulk-update', [
            'customer_ids' => [],
            'payment_mode' => 'wallet',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['customer_ids']);
    }

    /** @test */
    public function it_validates_bulk_update_customer_existence()
    {
        $response = $this->postJson('/api/v2/payment-modes/bulk-update', [
            'customer_ids' => [99999], // Non-existent customer
            'payment_mode' => 'wallet',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['customer_ids.0']);
    }

    /** @test */
    public function it_handles_partial_failures_in_bulk_update()
    {
        $validCustomer = Customer::factory()->create();
        $invalidCustomerId = 99999;

        $response = $this->postJson('/api/v2/payment-modes/bulk-update', [
            'customer_ids' => [$validCustomer->pk_customer_code, $invalidCustomerId],
            'payment_mode' => 'wallet',
        ]);

        // This should fail validation due to non-existent customer
        $response->assertStatus(422);
    }
}

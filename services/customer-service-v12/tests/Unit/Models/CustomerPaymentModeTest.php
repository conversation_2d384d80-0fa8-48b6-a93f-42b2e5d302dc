<?php

namespace Tests\Unit\Models;

use App\Models\Customer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CustomerPaymentModeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    /** @test */
    public function it_has_default_payment_mode_as_wallet()
    {
        $customer = Customer::factory()->create();
        
        $this->assertEquals('wallet', $customer->payment_mode);
    }

    /** @test */
    public function it_can_check_if_customer_uses_wallet_payment()
    {
        $customer = Customer::factory()->create(['payment_mode' => 'wallet']);
        
        $this->assertTrue($customer->usesWalletPayment());
        $this->assertFalse($customer->usesDirectPayment());
    }

    /** @test */
    public function it_can_check_if_customer_uses_direct_payment()
    {
        $customer = Customer::factory()->create(['payment_mode' => 'direct']);
        
        $this->assertTrue($customer->usesDirectPayment());
        $this->assertFalse($customer->usesWalletPayment());
    }

    /** @test */
    public function it_returns_default_wallet_settings_when_none_set()
    {
        $customer = Customer::factory()->create(['payment_mode' => 'wallet']);
        
        $settings = $customer->getWalletSettings();
        
        $this->assertEquals([
            'minimum_balance' => 100,
            'auto_reload_enabled' => false,
            'auto_reload_amount' => 500,
            'auto_reload_threshold' => 50,
        ], $settings);
    }

    /** @test */
    public function it_returns_custom_wallet_settings_when_set()
    {
        $customSettings = [
            'minimum_balance' => 200,
            'auto_reload_enabled' => true,
            'auto_reload_amount' => 1000,
            'auto_reload_threshold' => 100,
        ];

        $customer = Customer::factory()->create([
            'payment_mode' => 'wallet',
            'payment_mode_settings' => $customSettings,
        ]);
        
        $settings = $customer->getWalletSettings();
        
        $this->assertEquals($customSettings, $settings);
    }

    /** @test */
    public function it_returns_empty_wallet_settings_for_direct_payment_mode()
    {
        $customer = Customer::factory()->create(['payment_mode' => 'direct']);
        
        $settings = $customer->getWalletSettings();
        
        $this->assertEquals([], $settings);
    }

    /** @test */
    public function it_can_update_payment_mode_and_settings()
    {
        $customer = Customer::factory()->create(['payment_mode' => 'direct']);
        
        $walletSettings = [
            'minimum_balance' => 150,
            'auto_reload_enabled' => true,
            'auto_reload_amount' => 750,
            'auto_reload_threshold' => 75,
        ];

        $result = $customer->updatePaymentMode('wallet', $walletSettings);
        
        $this->assertTrue($result);
        $this->assertEquals('wallet', $customer->fresh()->payment_mode);
        $this->assertEquals($walletSettings, $customer->fresh()->payment_mode_settings);
        $this->assertNotNull($customer->fresh()->payment_mode_updated_at);
    }

    /** @test */
    public function it_can_update_payment_mode_without_settings()
    {
        $customer = Customer::factory()->create([
            'payment_mode' => 'wallet',
            'payment_mode_settings' => ['minimum_balance' => 100],
        ]);
        
        $result = $customer->updatePaymentMode('direct');
        
        $this->assertTrue($result);
        $this->assertEquals('direct', $customer->fresh()->payment_mode);
        $this->assertNull($customer->fresh()->payment_mode_settings);
        $this->assertNotNull($customer->fresh()->payment_mode_updated_at);
    }

    /** @test */
    public function it_casts_payment_mode_settings_to_array()
    {
        $settings = [
            'minimum_balance' => 200,
            'auto_reload_enabled' => true,
        ];

        $customer = Customer::factory()->create([
            'payment_mode_settings' => $settings,
        ]);
        
        $this->assertIsArray($customer->payment_mode_settings);
        $this->assertEquals($settings, $customer->payment_mode_settings);
    }

    /** @test */
    public function it_casts_payment_mode_updated_at_to_datetime()
    {
        $customer = Customer::factory()->create();
        $customer->updatePaymentMode('wallet');
        
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $customer->fresh()->payment_mode_updated_at);
    }

    /** @test */
    public function payment_mode_settings_can_be_null()
    {
        $customer = Customer::factory()->create([
            'payment_mode' => 'direct',
            'payment_mode_settings' => null,
        ]);
        
        $this->assertNull($customer->payment_mode_settings);
    }

    /** @test */
    public function it_updates_payment_mode_updated_at_timestamp_when_mode_changes()
    {
        $customer = Customer::factory()->create(['payment_mode' => 'direct']);
        
        $originalTimestamp = $customer->payment_mode_updated_at;
        
        // Wait a moment to ensure timestamp difference
        sleep(1);
        
        $customer->updatePaymentMode('wallet');
        
        $this->assertNotEquals($originalTimestamp, $customer->fresh()->payment_mode_updated_at);
    }

    /** @test */
    public function it_preserves_other_customer_data_when_updating_payment_mode()
    {
        $customer = Customer::factory()->create([
            'customer_name' => 'John Doe',
            'email_address' => '<EMAIL>',
            'payment_mode' => 'direct',
        ]);
        
        $customer->updatePaymentMode('wallet', ['minimum_balance' => 300]);
        
        $freshCustomer = $customer->fresh();
        $this->assertEquals('John Doe', $freshCustomer->customer_name);
        $this->assertEquals('<EMAIL>', $freshCustomer->email_address);
        $this->assertEquals('wallet', $freshCustomer->payment_mode);
    }
}

<?php

namespace Tests\Unit\Services;

use App\DTOs\Customer\AddressDTO;
use App\DTOs\Customer\CustomerDTO;
use App\Events\Customer\CustomerCreated;
use App\Events\Customer\CustomerUpdated;
use App\Events\Customer\CustomerDeleted;
use App\Exceptions\Customer\CustomerNotFoundException;
use App\Exceptions\Customer\DuplicateCustomerException;
use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Services\CustomerService;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Psr\Log\LoggerInterface;
use Tests\TestCase;

class CustomerServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CustomerService $customerService;
    protected Dispatcher $eventDispatcher;
    protected LoggerInterface $logger;

    protected function setUp(): void
    {
        parent::setUp();

        $this->eventDispatcher = $this->createMock(Dispatcher::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->customerService = new CustomerService($this->eventDispatcher, $this->logger);
    }

    /** @test */
    public function it_can_get_all_customers()
    {
        // Arrange
        Customer::factory()->count(5)->create();

        // Act
        $customers = $this->customerService->getAllCustomers();

        // Assert
        $this->assertCount(5, $customers);
    }

    /** @test */
    public function it_can_filter_customers_by_status()
    {
        // Arrange
        Customer::factory()->count(3)->create(['status' => true]);
        Customer::factory()->count(2)->create(['status' => false]);

        // Act
        $activeCustomers = $this->customerService->getAllCustomers(['status' => true]);
        $inactiveCustomers = $this->customerService->getAllCustomers(['status' => false]);

        // Assert
        $this->assertCount(3, $activeCustomers);
        $this->assertCount(2, $inactiveCustomers);
    }

    /** @test */
    public function it_can_get_customer_by_id()
    {
        // Arrange
        $customer = Customer::factory()->create();

        // Act
        $foundCustomer = $this->customerService->getCustomerById($customer->pk_customer_code);

        // Assert
        $this->assertEquals($customer->pk_customer_code, $foundCustomer->pk_customer_code);
    }

    /** @test */
    public function it_throws_exception_when_customer_not_found()
    {
        // Arrange
        $nonExistentId = 999;

        // Act & Assert
        $this->expectException(CustomerNotFoundException::class);
        $this->customerService->getCustomerById($nonExistentId);
    }

    /** @test */
    public function it_can_create_a_customer()
    {
        // Arrange
        $customerData = [
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
            'password' => 'password',
        ];
        $customerDTO = CustomerDTO::fromArray($customerData);

        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(CustomerCreated::class));

        // Act
        $customer = $this->customerService->createCustomer($customerDTO);

        // Assert
        $this->assertDatabaseHas('customers', [
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);
        $this->assertDatabaseHas('customer_wallet', [
            'customer_code' => $customer->pk_customer_code,
            'balance' => 0,
        ]);
    }

    /** @test */
    public function it_throws_exception_when_creating_customer_with_duplicate_phone()
    {
        // Arrange
        Customer::factory()->create(['phone' => '1234567890']);

        $customerData = [
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ];
        $customerDTO = CustomerDTO::fromArray($customerData);

        // Act & Assert
        $this->expectException(DuplicateCustomerException::class);
        $this->customerService->createCustomer($customerDTO);
    }

    /** @test */
    public function it_throws_exception_when_creating_customer_with_duplicate_email()
    {
        // Arrange
        Customer::factory()->create(['email_address' => '<EMAIL>']);

        $customerData = [
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ];
        $customerDTO = CustomerDTO::fromArray($customerData);

        // Act & Assert
        $this->expectException(DuplicateCustomerException::class);
        $this->customerService->createCustomer($customerDTO);
    }

    /** @test */
    public function it_can_update_a_customer()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'customer_name' => 'John Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ]);

        $updateData = [
            'customer_name' => 'Jane Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ];
        $customerDTO = CustomerDTO::fromArray($updateData);

        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(CustomerUpdated::class));

        // Act
        $updatedCustomer = $this->customerService->updateCustomer($customer->pk_customer_code, $customerDTO);

        // Assert
        $this->assertEquals('Jane Doe', $updatedCustomer->customer_name);
        $this->assertDatabaseHas('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
            'customer_name' => 'Jane Doe',
        ]);
    }

    /** @test */
    public function it_throws_exception_when_updating_non_existent_customer()
    {
        // Arrange
        $nonExistentId = 999;
        $updateData = [
            'customer_name' => 'Jane Doe',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>',
        ];
        $customerDTO = CustomerDTO::fromArray($updateData);

        // Act & Assert
        $this->expectException(CustomerNotFoundException::class);
        $this->customerService->updateCustomer($nonExistentId, $customerDTO);
    }

    /** @test */
    public function it_throws_exception_when_updating_customer_with_duplicate_phone()
    {
        // Arrange
        $customer1 = Customer::factory()->create(['phone' => '1111111111']);
        $customer2 = Customer::factory()->create(['phone' => '2222222222']);

        $updateData = [
            'customer_name' => 'Jane Doe',
            'phone' => '2222222222', // Trying to use customer2's phone
            'email_address' => '<EMAIL>',
        ];
        $customerDTO = CustomerDTO::fromArray($updateData);

        // Act & Assert
        $this->expectException(DuplicateCustomerException::class);
        $this->customerService->updateCustomer($customer1->pk_customer_code, $customerDTO);
    }
    /** @test */
    public function it_can_delete_a_customer()
    {
        // Arrange
        $customer = Customer::factory()->create();

        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(CustomerDeleted::class));

        // Act
        $result = $this->customerService->deleteCustomer($customer->pk_customer_code);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseMissing('customers', [
            'pk_customer_code' => $customer->pk_customer_code,
        ]);
    }

    /** @test */
    public function it_throws_exception_when_deleting_non_existent_customer()
    {
        // Arrange
        $nonExistentId = 999;

        // Act & Assert
        $this->expectException(CustomerNotFoundException::class);
        $this->customerService->deleteCustomer($nonExistentId);
    }

    /** @test */
    public function it_can_add_address_to_customer()
    {
        // Arrange
        $customer = Customer::factory()->create();

        $addressData = [
            'customer_code' => $customer->pk_customer_code,
            'address_type' => 'Home',
            'address_line1' => '123 Main St',
            'city' => 'New York',
            'is_default' => true,
        ];
        $addressDTO = AddressDTO::fromArray($addressData);

        // Act
        $address = $this->customerService->addCustomerAddress($customer->pk_customer_code, $addressDTO);

        // Assert
        $this->assertDatabaseHas('customer_address', [
            'customer_code' => $customer->pk_customer_code,
            'address_type' => 'Home',
            'address_line1' => '123 Main St',
            'city' => 'New York',
            'is_default' => true,
        ]);
    }

    /** @test */
    public function it_sets_only_one_address_as_default()
    {
        // Arrange
        $customer = Customer::factory()->create();

        // Create first address as default
        $address1Data = [
            'customer_code' => $customer->pk_customer_code,
            'address_type' => 'Home',
            'address_line1' => '123 Main St',
            'city' => 'New York',
            'is_default' => true,
        ];
        $address1DTO = AddressDTO::fromArray($address1Data);
        $this->customerService->addCustomerAddress($customer->pk_customer_code, $address1DTO);

        // Create second address as default
        $address2Data = [
            'customer_code' => $customer->pk_customer_code,
            'address_type' => 'Work',
            'address_line1' => '456 Office Blvd',
            'city' => 'New York',
            'is_default' => true,
        ];
        $address2DTO = AddressDTO::fromArray($address2Data);

        // Act
        $address2 = $this->customerService->addCustomerAddress($customer->pk_customer_code, $address2DTO);

        // Assert
        $this->assertDatabaseHas('customer_address', [
            'pk_customer_address_code' => $address2->pk_customer_address_code,
            'is_default' => true,
        ]);

        $this->assertDatabaseHas('customer_address', [
            'address_type' => 'Home',
            'is_default' => false,
        ]);
    }

    /** @test */
    public function it_throws_exception_when_adding_address_to_non_existent_customer()
    {
        // Arrange
        $nonExistentId = 999;

        $addressData = [
            'customer_code' => $nonExistentId,
            'address_type' => 'Home',
            'address_line1' => '123 Main St',
            'city' => 'New York',
            'is_default' => true,
        ];
        $addressDTO = AddressDTO::fromArray($addressData);

        // Act & Assert
        $this->expectException(CustomerNotFoundException::class);
        $this->customerService->addCustomerAddress($nonExistentId, $addressDTO);
    }
}
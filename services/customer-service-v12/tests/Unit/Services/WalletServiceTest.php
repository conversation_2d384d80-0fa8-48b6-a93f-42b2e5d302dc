<?php

namespace Tests\Unit\Services;

use App\Events\Wallet\WalletDeposited;
use App\Events\Wallet\WalletWithdrawn;
use App\Exceptions\Wallet\InsufficientBalanceException;
use App\Exceptions\Wallet\WalletNotFoundException;
use App\Models\Customer;
use App\Models\CustomerWallet;
use App\Models\WalletTransaction;
use App\Services\CustomerService;
use App\Services\WalletService;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Psr\Log\LoggerInterface;
use Tests\TestCase;

class WalletServiceTest extends TestCase
{
    use RefreshDatabase;

    protected WalletService $walletService;
    protected CustomerService $customerService;
    protected Dispatcher $eventDispatcher;
    protected LoggerInterface $logger;
    protected Customer $customer;
    protected CustomerWallet $wallet;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock dependencies
        $this->eventDispatcher = $this->createMock(Dispatcher::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->customerService = $this->createMock(CustomerService::class);

        // Create the service
        $this->walletService = new WalletService(
            $this->eventDispatcher,
            $this->logger,
            $this->customerService
        );

        // Create test data
        $this->customer = Customer::factory()->create();
        $this->wallet = CustomerWallet::factory()->create([
            'customer_code' => $this->customer->pk_customer_code,
            'balance' => 100.00,
            'status' => true
        ]);
    }

    /** @test */
    public function it_can_get_a_wallet()
    {
        // Arrange
        $this->customerService->expects($this->once())
            ->method('getCustomerById')
            ->with($this->customer->pk_customer_code)
            ->willReturn($this->customer);

        // Act
        $wallet = $this->walletService->getWallet($this->customer->pk_customer_code);

        // Assert
        $this->assertEquals($this->wallet->id, $wallet->id);
        $this->assertEquals($this->wallet->balance, $wallet->balance);
    }

    /** @test */
    public function it_throws_exception_when_wallet_not_found()
    {
        // Arrange
        $nonExistentCustomerId = 999;
        $this->customerService->expects($this->once())
            ->method('getCustomerById')
            ->with($nonExistentCustomerId)
            ->willReturn(new Customer(['pk_customer_code' => $nonExistentCustomerId]));

        // Assert
        $this->expectException(WalletNotFoundException::class);

        // Act
        $this->walletService->getWallet($nonExistentCustomerId);
    }

    /** @test */
    public function it_can_deposit_to_wallet()
    {
        // Arrange
        $this->customerService->expects($this->once())
            ->method('getCustomerById')
            ->with($this->customer->pk_customer_code)
            ->willReturn($this->customer);

        // Expect event to be dispatched
        $this->eventDispatcher->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(WalletDeposited::class));

        $amount = 50.00;
        $description = 'Test deposit';

        // Act
        $wallet = $this->walletService->deposit(
            $this->customer->pk_customer_code,
            $amount,
            $description
        );

        // Assert
        $this->assertEquals(150.00, $wallet->balance);
        $this->assertDatabaseHas('wallet_transactions', [
            'customer_code' => $this->customer->pk_customer_code,
            'amount' => $amount,
            'type' => 'deposit',
            'description' => $description,
            'before_balance' => 100.00,
            'after_balance' => 150.00,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function it_can_withdraw_from_wallet()
    {
        // Arrange
        $this->customerService->expects($this->once())
            ->method('getCustomerById')
            ->with($this->customer->pk_customer_code)
            ->willReturn($this->customer);

        // Expect event to be dispatched
        $this->eventDispatcher->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(WalletWithdrawn::class));

        $amount = 50.00;
        $description = 'Test withdrawal';

        // Act
        $wallet = $this->walletService->withdraw(
            $this->customer->pk_customer_code,
            $amount,
            $description
        );

        // Assert
        $this->assertEquals(50.00, $wallet->balance);
        $this->assertDatabaseHas('wallet_transactions', [
            'customer_code' => $this->customer->pk_customer_code,
            'amount' => $amount,
            'type' => 'withdrawal',
            'description' => $description,
            'before_balance' => 100.00,
            'after_balance' => 50.00,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function it_throws_exception_when_insufficient_balance()
    {
        // Arrange
        $this->customerService->expects($this->once())
            ->method('getCustomerById')
            ->with($this->customer->pk_customer_code)
            ->willReturn($this->customer);

        $amount = 150.00; // More than the balance

        // Assert
        $this->expectException(InsufficientBalanceException::class);

        // Act
        $this->walletService->withdraw(
            $this->customer->pk_customer_code,
            $amount
        );
    }

    /** @test */
    public function it_can_get_transaction_history()
    {
        // Arrange
        // Create some transactions
        WalletTransaction::factory()->count(5)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'deposit'
        ]);

        WalletTransaction::factory()->count(3)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'withdrawal'
        ]);

        // Act
        $transactions = $this->walletService->getTransactionHistory($this->customer->pk_customer_code);

        // Assert
        $this->assertEquals(8, $transactions->total());
    }

    /** @test */
    public function it_can_filter_transaction_history_by_type()
    {
        // Arrange
        // Create some transactions
        WalletTransaction::factory()->count(5)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'deposit'
        ]);

        WalletTransaction::factory()->count(3)->create([
            'customer_code' => $this->customer->pk_customer_code,
            'type' => 'withdrawal'
        ]);

        // Act
        $transactions = $this->walletService->getTransactionHistory(
            $this->customer->pk_customer_code,
            ['type' => 'deposit']
        );

        // Assert
        $this->assertEquals(5, $transactions->total());
    }
}

<?php

namespace Tests\Unit\Services;

use App\DTOs\Auth\LoginDTO;
use App\DTOs\Auth\ResetPasswordDTO;
use App\Events\Auth\LoginFailed;
use App\Events\Auth\LoginSuccessful;
use App\Events\Auth\LogoutEvent;
use App\Events\Auth\PasswordResetCompleted;
use App\Events\Auth\PasswordResetRequested;
use App\Exceptions\Auth\InvalidCredentialsException;
use App\Exceptions\Auth\InvalidTokenException;
use App\Exceptions\Auth\UserNotFoundException;
use App\Models\PasswordReset;
use App\Models\User;
use App\Services\AuthService;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Psr\Log\LoggerInterface;
use Tests\TestCase;
use Mockery;
use Mockery\MockInterface;

class AuthServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var MockInterface|AuthManager
     */
    protected $authManager;

    /**
     * @var MockInterface|Guard|StatefulGuard
     */
    protected $guard;

    /**
     * @var MockInterface|Dispatcher
     */
    protected $events;

    /**
     * @var MockInterface|LoggerInterface
     */
    protected $logger;

    /**
     * @var AuthService
     */
    protected $authService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->authManager = Mockery::mock(AuthManager::class);
        $this->guard = Mockery::mock(StatefulGuard::class);
        $this->events = Mockery::mock(Dispatcher::class);
        $this->logger = Mockery::mock(LoggerInterface::class);

        $this->authManager->shouldReceive('guard')
            ->andReturn($this->guard);

        $this->authService = new AuthService(
            $this->authManager,
            $this->events,
            $this->logger
        );
    }

    /**
     * Test successful authentication.
     *
     * @return void
     */
    public function testAuthenticateSuccess()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => 1
        ]);

        // Set up mocks
        $this->logger->shouldReceive('info')
            ->twice();

        $this->guard->shouldReceive('attempt')
            ->with([
                'email' => '<EMAIL>',
                'password' => 'password123'
            ], false)
            ->once()
            ->andReturn(true);

        $this->guard->shouldReceive('user')
            ->once()
            ->andReturn($user);

        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(LoginSuccessful::class))
            ->once();

        // Create login DTO
        $loginDTO = new LoginDTO('<EMAIL>', 'password123');

        // Call the method
        $result = $this->authService->authenticate($loginDTO);

        // Assert the result
        $this->assertEquals($user->id, $result->id);
        $this->assertEquals($user->first_name, $result->firstName);
        $this->assertEquals($user->last_name, $result->lastName);
        $this->assertEquals($user->email, $result->email);
        $this->assertEquals($user->role_id, $result->roleId);
        $this->assertEquals('legacy', $result->authType);
    }

    /**
     * Test failed authentication.
     *
     * @return void
     */
    public function testAuthenticateFailure()
    {
        // Set up mocks
        $this->logger->shouldReceive('info')
            ->once();

        $this->logger->shouldReceive('warning')
            ->once();

        $this->guard->shouldReceive('attempt')
            ->with([
                'email' => '<EMAIL>',
                'password' => 'wrongpassword'
            ], false)
            ->once()
            ->andReturn(false);

        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(LoginFailed::class))
            ->once();

        // Create login DTO
        $loginDTO = new LoginDTO('<EMAIL>', 'wrongpassword');

        // Expect exception
        $this->expectException(InvalidCredentialsException::class);

        // Call the method
        $this->authService->authenticate($loginDTO);
    }

    /**
     * Test logout.
     *
     * @return void
     */
    public function testLogout()
    {
        // Create a test user
        $user = User::factory()->create();

        // Set up mocks
        $this->guard->shouldReceive('user')
            ->once()
            ->andReturn($user);

        $this->logger->shouldReceive('info')
            ->once();

        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(LogoutEvent::class))
            ->once();

        $this->guard->shouldReceive('logout')
            ->once();

        // Mock request for session
        $request = Mockery::mock(Request::class);
        $session = Mockery::mock();
        
        $request->shouldReceive('session')
            ->twice()
            ->andReturn($session);
            
        $session->shouldReceive('invalidate')
            ->once();
            
        $session->shouldReceive('regenerateToken')
            ->once();
            
        $this->app->instance('request', $request);

        // Call the method
        $this->authService->logout();
    }

    /**
     * Test generate reset token.
     *
     * @return void
     */
    public function testGenerateResetToken()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Set up mocks
        $this->logger->shouldReceive('info')
            ->once();

        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(PasswordResetRequested::class))
            ->once();

        // Call the method
        $token = $this->authService->generateResetToken('<EMAIL>');

        // Assert the result
        $this->assertIsString($token);
        $this->assertNotEmpty($token);

        // Check that the token was stored
        $resetData = PasswordReset::where('email', '<EMAIL>')->first();
        $this->assertNotNull($resetData);
        $this->assertTrue(Hash::check($token, $resetData->token));
    }

    /**
     * Test generate reset token for non-existent user.
     *
     * @return void
     */
    public function testGenerateResetTokenUserNotFound()
    {
        // Expect exception
        $this->expectException(UserNotFoundException::class);

        // Call the method
        $this->authService->generateResetToken('<EMAIL>');
    }

    /**
     * Test validate reset token.
     *
     * @return void
     */
    public function testValidateResetToken()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Generate a token
        $token = 'valid-token';
        
        PasswordReset::create([
            'email' => '<EMAIL>',
            'token' => Hash::make($token),
            'created_at' => now()
        ]);

        // Call the method
        $result = $this->authService->validateResetToken($token, '<EMAIL>');

        // Assert the result
        $this->assertTrue($result);
    }

    /**
     * Test reset password.
     *
     * @return void
     */
    public function testResetPassword()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword')
        ]);

        // Generate a token
        $token = 'valid-token';
        
        PasswordReset::create([
            'email' => '<EMAIL>',
            'token' => Hash::make($token),
            'created_at' => now()
        ]);

        // Set up mocks
        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(PasswordResetCompleted::class))
            ->once();

        $this->logger->shouldReceive('info')
            ->once();

        // Create reset password DTO
        $resetPasswordDTO = new ResetPasswordDTO('<EMAIL>', $token, 'newpassword');

        // Call the method
        $result = $this->authService->resetPassword($resetPasswordDTO);

        // Assert the result
        $this->assertTrue($result);

        // Check that the password was updated
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword', $user->password));

        // Check that the token was deleted
        $this->assertNull(PasswordReset::where('email', '<EMAIL>')->first());
    }

    /**
     * Test reset password with invalid token.
     *
     * @return void
     */
    public function testResetPasswordInvalidToken()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword')
        ]);

        // Create reset password DTO
        $resetPasswordDTO = new ResetPasswordDTO('<EMAIL>', 'invalid-token', 'newpassword');

        // Expect exception
        $this->expectException(InvalidTokenException::class);

        // Call the method
        $this->authService->resetPassword($resetPasswordDTO);
    }

    /**
     * Test get authenticated user.
     *
     * @return void
     */
    public function testGetAuthenticatedUser()
    {
        // Create a test user
        $user = User::factory()->create([
            'role_id' => 1
        ]);

        // Set up mocks
        $this->guard->shouldReceive('user')
            ->once()
            ->andReturn($user);

        // Call the method
        $result = $this->authService->getAuthenticatedUser();

        // Assert the result
        $this->assertEquals($user->id, $result->id);
        $this->assertEquals($user->first_name, $result->firstName);
        $this->assertEquals($user->last_name, $result->lastName);
        $this->assertEquals($user->email, $result->email);
        $this->assertEquals($user->role_id, $result->roleId);
        $this->assertEquals('legacy', $result->authType);
    }

    /**
     * Test get authenticated user when not authenticated.
     *
     * @return void
     */
    public function testGetAuthenticatedUserNotAuthenticated()
    {
        // Set up mocks
        $this->guard->shouldReceive('user')
            ->once()
            ->andReturn(null);

        // Call the method
        $result = $this->authService->getAuthenticatedUser();

        // Assert the result
        $this->assertNull($result);
    }
}

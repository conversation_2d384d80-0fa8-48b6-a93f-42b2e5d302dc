openapi: 3.0.0
info:
  title: Authentication Service API
  description: API for authentication and user management
  version: 1.0.0
  contact:
    name: Support Team
    email: <EMAIL>

servers:
  - url: http://localhost:8000/v2/auth
    description: Local development server
  - url: https://tenant.cubeonebiz.com/v2/auth
    description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    LoginRequest:
      type: object
      properties:
        username:
          type: string
          description: User's username or email
        password:
          type: string
          description: User's password
        rememberMe:
          type: boolean
          description: Whether to remember the user
      required:
        - username
        - password

    UserResponse:
      type: object
      properties:
        id:
          type: integer
          description: User ID
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        email:
          type: string
          description: User's email
        roleId:
          type: integer
          description: User's role ID
        authType:
          type: string
          description: Authentication type (legacy, keycloak)
        fullName:
          type: string
          description: User's full name

    ForgotPasswordRequest:
      type: object
      properties:
        email:
          type: string
          description: User's email
      required:
        - email

    ResetPasswordRequest:
      type: object
      properties:
        email:
          type: string
          description: User's email
        token:
          type: string
          description: Reset token
        password:
          type: string
          description: New password
        password_confirmation:
          type: string
          description: Password confirmation
      required:
        - email
        - token
        - password
        - password_confirmation

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Success status
        message:
          type: string
          description: Error message

paths:
  /login:
    post:
      summary: Login
      description: Authenticate a user
      operationId: login
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Successful login
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/UserResponse'
                      token:
                        type: string
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /logout:
    post:
      summary: Logout
      description: Logout a user
      operationId: logout
      tags:
        - Authentication
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful logout
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /forgot-password:
    post:
      summary: Forgot Password
      description: Request a password reset
      operationId: forgotPassword
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForgotPasswordRequest'
      responses:
        '200':
          description: Password reset requested
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      email:
                        type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /reset-password:
    post:
      summary: Reset Password
      description: Reset a user's password
      operationId: resetPassword
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '400':
          description: Invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user:
    get:
      summary: Get User
      description: Get the authenticated user
      operationId: getUser
      tags:
        - Authentication
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/UserResponse'
        '401':
          description: User not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

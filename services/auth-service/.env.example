APP_NAME="QuickServe Auth Service"
APP_ENV=development
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8888

LOG_CHANNEL=stack

DB_CONNECTION=sqlite
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=quickserve
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Demo Company ID
DEMO_COMPANY_ID=abc123-demo

# JWT Secret
JWT_SECRET=replace_with_secure_random_string

# Admin Token (pre-generated JWT token with admin role)
ADMIN_TOKEN=

# Development Mode
DEVELOPMENT_MODE=true

# API Configuration
API_BASE_URL=http://localhost:8888/api

# Token Encryption Keys
TOKEN_ENCRYPTION_KEY=replace_with_secure_random_string
TOKEN_ENCRYPTION_KEY_PREVIOUS=

# API Rate Limiting
API_RATE_LIMIT_MAX_REQUESTS=5
API_RATE_LIMIT_PERIOD=900  # 15 minutes in seconds

# CSRF Protection
CSRF_TIMEOUT=3600  # 1 hour in seconds

# Keycloak Configuration (if using Keycloak)
KEYCLOAK_CLIENT_ID=tenant-app
KEYCLOAK_CLIENT_SECRET=replace_with_client_secret
KEYCLOAK_AUTH_SERVER_URL=https://keycloak.example.com/auth
KEYCLOAK_REALM=tenant-realm
KEYCLOAK_REDIRECT_URI=http://localhost:8888/auth/keycloak-callback

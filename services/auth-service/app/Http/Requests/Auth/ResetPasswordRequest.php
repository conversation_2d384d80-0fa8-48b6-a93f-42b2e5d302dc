<?php

namespace App\Http\Requests\Auth;

use App\Rules\PasswordComplexity;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Reset Password Request
 * 
 * This class handles validation for password reset requests.
 */
class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => ['required', 'email'],
            'token' => ['required', 'string'],
            'password' => ['required', 'string', 'min:10', new PasswordComplexity(), 'confirmed'],
            'password_confirmation' => ['required', 'string']
        ];
    }
    
    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'email.required' => 'Email address is required',
            'email.email' => 'Please enter a valid email address',
            'token.required' => 'Token is required',
            'password.required' => 'Password is required',
            'password.min' => 'Password must be at least 10 characters long',
            'password.confirmed' => 'Password confirmation does not match',
            'password_confirmation.required' => 'Please confirm your password'
        ];
    }
}

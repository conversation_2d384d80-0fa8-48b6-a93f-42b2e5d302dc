<?php

namespace App\DTOs\Auth;

/**
 * User Data Transfer Object
 * 
 * This DTO encapsulates user data.
 */
class UserDTO
{
    /**
     * @var int
     */
    public $id;
    
    /**
     * @var string
     */
    public $firstName;
    
    /**
     * @var string
     */
    public $lastName;
    
    /**
     * @var string
     */
    public $email;
    
    /**
     * @var int
     */
    public $roleId;
    
    /**
     * @var string
     */
    public $authType;
    
    /**
     * Constructor
     * 
     * @param int $id
     * @param string $firstName
     * @param string $lastName
     * @param string $email
     * @param int $roleId
     * @param string $authType
     */
    public function __construct(
        int $id,
        string $firstName,
        string $lastName,
        string $email,
        int $roleId,
        string $authType = 'legacy'
    ) {
        $this->id = $id;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->email = $email;
        $this->roleId = $roleId;
        $this->authType = $authType;
    }
    
    /**
     * Create from array
     * 
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'],
            $data['first_name'],
            $data['last_name'],
            $data['email'],
            $data['role_id'],
            $data['auth_type'] ?? 'legacy'
        );
    }
    
    /**
     * Convert to array
     * 
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'email' => $this->email,
            'role_id' => $this->roleId,
            'auth_type' => $this->authType,
            'full_name' => $this->getFullName()
        ];
    }
    
    /**
     * Get full name
     * 
     * @return string
     */
    public function getFullName(): string
    {
        return trim($this->firstName . ' ' . $this->lastName);
    }
}

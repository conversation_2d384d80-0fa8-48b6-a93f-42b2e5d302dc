<?php

namespace App\DTOs\Auth;

/**
 * Reset Password Data Transfer Object
 * 
 * This DTO encapsulates password reset data.
 */
class ResetPasswordDTO
{
    /**
     * @var string
     */
    public $email;
    
    /**
     * @var string
     */
    public $token;
    
    /**
     * @var string
     */
    public $password;
    
    /**
     * Constructor
     * 
     * @param string $email
     * @param string $token
     * @param string $password
     */
    public function __construct(string $email, string $token, string $password)
    {
        $this->email = $email;
        $this->token = $token;
        $this->password = $password;
    }
    
    /**
     * Create from array
     * 
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['email'],
            $data['token'],
            $data['password']
        );
    }
    
    /**
     * Convert to array
     * 
     * @return array
     */
    public function toArray(): array
    {
        return [
            'email' => $this->email,
            'token' => $this->token,
            // Password is intentionally excluded for security
        ];
    }
}

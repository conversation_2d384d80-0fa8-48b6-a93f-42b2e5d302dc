<?php

namespace App\DTOs\Auth;

/**
 * Login Data Transfer Object
 * 
 * This DTO encapsulates login request data.
 */
class LoginDTO
{
    /**
     * @var string
     */
    public $username;
    
    /**
     * @var string
     */
    public $password;
    
    /**
     * @var bool
     */
    public $rememberMe;
    
    /**
     * Constructor
     * 
     * @param string $username
     * @param string $password
     * @param bool $rememberMe
     */
    public function __construct(string $username, string $password, bool $rememberMe = false)
    {
        $this->username = $username;
        $this->password = $password;
        $this->rememberMe = $rememberMe;
    }
    
    /**
     * Create from array
     * 
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['username'],
            $data['password'],
            $data['rememberMe'] ?? false
        );
    }
    
    /**
     * Convert to array
     * 
     * @return array
     */
    public function toArray(): array
    {
        return [
            'username' => $this->username,
            'rememberMe' => $this->rememberMe,
            // Password is intentionally excluded for security
        ];
    }
}

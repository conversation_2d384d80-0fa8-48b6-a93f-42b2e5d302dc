<?php

namespace App\Events\Auth;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Login Failed Event
 * 
 * This event is fired when a login attempt fails.
 */
class LoginFailed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var string
     */
    public $username;

    /**
     * Create a new event instance.
     *
     * @param string $username
     * @return void
     */
    public function __construct(string $username)
    {
        $this->username = $username;
    }
}

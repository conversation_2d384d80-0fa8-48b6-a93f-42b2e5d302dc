<?php

namespace App\Events\Auth;

use App\DTOs\Auth\UserDTO;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Login Successful Event
 * 
 * This event is fired when a user successfully logs in.
 */
class LoginSuccessful
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var UserDTO
     */
    public $user;

    /**
     * Create a new event instance.
     *
     * @param UserDTO $user
     * @return void
     */
    public function __construct(UserDTO $user)
    {
        $this->user = $user;
    }
}

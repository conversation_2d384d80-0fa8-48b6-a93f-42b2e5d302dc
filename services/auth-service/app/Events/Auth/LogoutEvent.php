<?php

namespace App\Events\Auth;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Logout Event
 * 
 * This event is fired when a user logs out.
 */
class LogoutEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var int|null
     */
    public $userId;

    /**
     * Create a new event instance.
     *
     * @param int|null $userId
     * @return void
     */
    public function __construct(?int $userId)
    {
        $this->userId = $userId;
    }
}

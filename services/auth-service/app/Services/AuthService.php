<?php

namespace App\Services;

use App\DTOs\Auth\LoginDTO;
use App\DTOs\Auth\ResetPasswordDTO;
use App\DTOs\Auth\UserDTO;
use App\Events\Auth\LoginFailed;
use App\Events\Auth\LoginSuccessful;
use App\Events\Auth\LogoutEvent;
use App\Events\Auth\PasswordResetRequested;
use App\Events\Auth\PasswordResetCompleted;
use App\Exceptions\Auth\InvalidCredentialsException;
use App\Exceptions\Auth\UserNotFoundException;
use App\Exceptions\Auth\InvalidTokenException;
use App\Models\User;
use App\Models\PasswordReset;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;
use Exception;

/**
 * Authentication Service
 * 
 * This service encapsulates all authentication-related business logic.
 */
class AuthService
{
    /**
     * @var AuthManager
     */
    protected $auth;

    /**
     * @var Dispatcher
     */
    protected $events;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * Constructor
     * 
     * @param AuthManager $auth
     * @param Dispatcher $events
     * @param LoggerInterface $logger
     */
    public function __construct(
        AuthManager $auth,
        Dispatcher $events,
        LoggerInterface $logger
    ) {
        $this->auth = $auth;
        $this->events = $events;
        $this->logger = $logger;
    }

    /**
     * Authenticate a user
     *
     * @param LoginDTO $loginDTO
     * @return UserDTO
     * @throws InvalidCredentialsException If authentication fails
     */
    public function authenticate(LoginDTO $loginDTO): UserDTO
    {
        try {
            // Log authentication attempt
            $this->logger->info('Authentication attempt', [
                'username' => $loginDTO->username,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);

            // Determine if we're using email or username
            $field = filter_var($loginDTO->username, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
            
            // Attempt authentication
            $credentials = [
                $field => $loginDTO->username,
                'password' => $loginDTO->password
            ];

            if (!$this->auth->guard()->attempt($credentials, $loginDTO->rememberMe)) {
                // Fire login failed event
                $this->events->dispatch(new LoginFailed($loginDTO->username));
                
                // Log failed authentication
                $this->logger->warning('Authentication failed', [
                    'username' => $loginDTO->username
                ]);
                
                throw new InvalidCredentialsException('Invalid username or password');
            }

            // Get authenticated user
            $user = $this->auth->guard()->user();
            
            // Create user DTO
            $userDTO = new UserDTO(
                $user->id,
                $user->first_name,
                $user->last_name,
                $user->email,
                $user->role_id,
                'legacy'
            );
            
            // Fire login successful event
            $this->events->dispatch(new LoginSuccessful($userDTO));
            
            // Log successful authentication
            $this->logger->info('Authentication successful', [
                'username' => $loginDTO->username,
                'user_id' => $user->id,
                'role_id' => $user->role_id
            ]);
            
            return $userDTO;
        } catch (InvalidCredentialsException $e) {
            throw $e;
        } catch (Exception $e) {
            // Log exception
            $this->logger->error('Authentication error', [
                'username' => $loginDTO->username,
                'error' => $e->getMessage()
            ]);
            
            throw new InvalidCredentialsException('Authentication failed: ' . $e->getMessage());
        }
    }

    /**
     * Logout the current user
     *
     * @return void
     */
    public function logout(): void
    {
        // Get user details for logging
        $user = $this->auth->guard()->user();
        $userId = $user ? $user->id : null;
        
        // Log logout
        $this->logger->info('User logout', [
            'user_id' => $userId,
            'ip' => request()->ip()
        ]);
        
        // Fire logout event
        $this->events->dispatch(new LogoutEvent($userId));
        
        // Logout user
        $this->auth->guard()->logout();
        
        // Invalidate session
        request()->session()->invalidate();
        
        // Regenerate CSRF token
        request()->session()->regenerateToken();
    }

    /**
     * Generate a password reset token
     *
     * @param string $email
     * @return string The generated token
     * @throws UserNotFoundException If user not found
     */
    public function generateResetToken(string $email): string
    {
        try {
            // Check if user exists
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                throw new UserNotFoundException('User not found');
            }
            
            // Generate token
            $token = Str::random(60);
            
            // Store token
            PasswordReset::updateOrCreate(
                ['email' => $email],
                [
                    'token' => Hash::make($token),
                    'created_at' => now()
                ]
            );
            
            // Fire password reset requested event
            $this->events->dispatch(new PasswordResetRequested($email));
            
            // Log token generation
            $this->logger->info('Password reset token generated', [
                'email' => $email
            ]);
            
            return $token;
        } catch (UserNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            // Log exception
            $this->logger->error('Error generating reset token', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            
            throw new Exception('Failed to generate reset token: ' . $e->getMessage());
        }
    }

    /**
     * Validate a password reset token
     *
     * @param string $token
     * @param string $email
     * @return bool
     */
    public function validateResetToken(string $token, string $email): bool
    {
        try {
            // Get token from database
            $resetData = PasswordReset::where('email', $email)->first();
            
            // Check if token exists
            if (!$resetData) {
                return false;
            }
            
            // Check if token is valid
            if (!Hash::check($token, $resetData->token)) {
                return false;
            }
            
            // Check if token is expired (24 hours)
            if ($resetData->created_at->addHours(24)->isPast()) {
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            // Log error
            $this->logger->error('Error validating reset token', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Reset a user's password
     *
     * @param ResetPasswordDTO $resetPasswordDTO
     * @return bool
     * @throws InvalidTokenException If token is invalid
     */
    public function resetPassword(ResetPasswordDTO $resetPasswordDTO): bool
    {
        try {
            // Validate token
            if (!$this->validateResetToken($resetPasswordDTO->token, $resetPasswordDTO->email)) {
                throw new InvalidTokenException('Invalid or expired token');
            }
            
            // Update user password
            $user = User::where('email', $resetPasswordDTO->email)->first();
            
            if (!$user) {
                throw new UserNotFoundException('User not found');
            }
            
            $user->password = Hash::make($resetPasswordDTO->password);
            $user->save();
            
            // Delete used token
            PasswordReset::where('email', $resetPasswordDTO->email)->delete();
            
            // Fire password reset completed event
            $this->events->dispatch(new PasswordResetCompleted($resetPasswordDTO->email));
            
            // Log password reset
            $this->logger->info('Password reset completed', [
                'email' => $resetPasswordDTO->email
            ]);
            
            return true;
        } catch (InvalidTokenException $e) {
            throw $e;
        } catch (UserNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            // Log exception
            $this->logger->error('Error resetting password', [
                'email' => $resetPasswordDTO->email,
                'error' => $e->getMessage()
            ]);
            
            throw new Exception('Failed to reset password: ' . $e->getMessage());
        }
    }

    /**
     * Get the authenticated user
     *
     * @return UserDTO|null
     */
    public function getAuthenticatedUser(): ?UserDTO
    {
        $user = $this->auth->guard()->user();
        
        if (!$user) {
            return null;
        }
        
        return new UserDTO(
            $user->id,
            $user->first_name,
            $user->last_name,
            $user->email,
            $user->role_id,
            'legacy'
        );
    }
}

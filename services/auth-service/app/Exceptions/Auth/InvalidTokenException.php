<?php

namespace App\Exceptions\Auth;

use Exception;

/**
 * Invalid Token Exception
 * 
 * This exception is thrown when a token is invalid or expired.
 */
class InvalidTokenException extends Exception
{
    /**
     * Constructor
     * 
     * @param string $message
     * @param int $code
     * @param Exception|null $previous
     */
    public function __construct(string $message = "Invalid or expired token", int $code = 400, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

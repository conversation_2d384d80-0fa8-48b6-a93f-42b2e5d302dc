<?php

namespace App\Exceptions\Auth;

use Exception;

/**
 * User Not Found Exception
 * 
 * This exception is thrown when a user is not found.
 */
class UserNotFoundException extends Exception
{
    /**
     * Constructor
     * 
     * @param string $message
     * @param int $code
     * @param Exception|null $previous
     */
    public function __construct(string $message = "User not found", int $code = 404, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

/**
 * Password Complexity Rule
 * 
 * This rule validates that passwords meet complexity requirements.
 */
class PasswordComplexity implements Rule
{
    /**
     * @var int
     */
    protected $minLength;
    
    /**
     * @var bool
     */
    protected $requireUppercase;
    
    /**
     * @var bool
     */
    protected $requireLowercase;
    
    /**
     * @var bool
     */
    protected $requireDigit;
    
    /**
     * @var bool
     */
    protected $requireSpecial;
    
    /**
     * @var string
     */
    protected $message;
    
    /**
     * Create a new rule instance.
     *
     * @param int $minLength
     * @param bool $requireUppercase
     * @param bool $requireLowercase
     * @param bool $requireDigit
     * @param bool $requireSpecial
     * @return void
     */
    public function __construct(
        int $minLength = 10,
        bool $requireUppercase = true,
        bool $requireLowercase = true,
        bool $requireDigit = true,
        bool $requireSpecial = true
    ) {
        $this->minLength = $minLength;
        $this->requireUppercase = $requireUppercase;
        $this->requireLowercase = $requireLowercase;
        $this->requireDigit = $requireDigit;
        $this->requireSpecial = $requireSpecial;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        // Check minimum length
        if (strlen($value) < $this->minLength) {
            $this->message = "Password must be at least {$this->minLength} characters long";
            return false;
        }
        
        // Check for uppercase letters
        if ($this->requireUppercase && !preg_match('/[A-Z]/', $value)) {
            $this->message = 'Password must contain at least one uppercase letter';
            return false;
        }
        
        // Check for lowercase letters
        if ($this->requireLowercase && !preg_match('/[a-z]/', $value)) {
            $this->message = 'Password must contain at least one lowercase letter';
            return false;
        }
        
        // Check for digits
        if ($this->requireDigit && !preg_match('/[0-9]/', $value)) {
            $this->message = 'Password must contain at least one digit';
            return false;
        }
        
        // Check for special characters
        if ($this->requireSpecial && !preg_match('/[^A-Za-z0-9]/', $value)) {
            $this->message = 'Password must contain at least one special character';
            return false;
        }
        
        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->message;
    }
}

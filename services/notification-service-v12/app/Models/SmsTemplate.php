<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SmsTemplate extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sms_templates';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'sms_template_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fk_set_id',
        'name',
        'sms_content',
        'purpose',
        'is_approved',
        'is_active',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_approved' => 'boolean',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the set that owns the template.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function set(): BelongsTo
    {
        return $this->belongsTo(SmsSet::class, 'fk_set_id', 'pk_set_id');
    }

    /**
     * Replace variables in the template.
     *
     * @param array $data
     * @return string
     */
    public function renderContent(array $data): string
    {
        $content = $this->sms_content;

        foreach ($data as $key => $value) {
            $content = str_replace('#' . $key . '#', $value, $content);
        }

        return $content;
    }

    /**
     * Check if the template content is within the character limit.
     *
     * @return bool
     */
    public function isWithinCharacterLimit(): bool
    {
        $set = $this->set;
        
        if (!$set) {
            return true;
        }
        
        return strlen($this->sms_content) <= $set->character_limit;
    }

    /**
     * Get the character count of the template content.
     *
     * @return int
     */
    public function getCharacterCount(): int
    {
        return strlen($this->sms_content);
    }

    /**
     * Get the remaining characters before reaching the limit.
     *
     * @return int
     */
    public function getRemainingCharacters(): int
    {
        $set = $this->set;
        
        if (!$set) {
            return 0;
        }
        
        $remaining = $set->character_limit - strlen($this->sms_content);
        
        return $remaining > 0 ? $remaining : 0;
    }
}

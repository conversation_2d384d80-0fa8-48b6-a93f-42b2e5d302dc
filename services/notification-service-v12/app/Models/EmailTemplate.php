<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmailTemplate extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'email_templates';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_template_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fk_set_id',
        'name',
        'subject',
        'body',
        'type',
        'purpose',
        'template_variable_id',
        'is_active',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the set that owns the template.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function set(): BelongsTo
    {
        return $this->belongsTo(EmailTemplateSet::class, 'fk_set_id', 'pk_set_id');
    }

    /**
     * Get the variables for the template.
     *
     * @return array
     */
    public function getVariables(): array
    {
        if (empty($this->template_variable_id)) {
            return [];
        }

        $variableIds = explode(',', $this->template_variable_id);
        $variables = EmailTemplateVariable::whereIn('id', $variableIds)->get();

        return $variables->toArray();
    }

    /**
     * Replace variables in the template.
     *
     * @param array $data
     * @return string
     */
    public function renderBody(array $data): string
    {
        $body = $this->body;

        foreach ($data as $key => $value) {
            $body = str_replace('#' . $key . '#', $value, $body);
        }

        return $body;
    }

    /**
     * Replace variables in the subject.
     *
     * @param array $data
     * @return string
     */
    public function renderSubject(array $data): string
    {
        $subject = $this->subject;

        foreach ($data as $key => $value) {
            $subject = str_replace('#' . $key . '#', $value, $subject);
        }

        return $subject;
    }
}

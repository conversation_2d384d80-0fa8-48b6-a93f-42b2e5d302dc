<?php

namespace App\Http\Requests\Email;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SendTemplateEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'from_email' => 'required|email',
            'from_name' => 'nullable|string|max:255',
            'to' => 'required|array|min:1',
            'to.*.email' => 'required|email',
            'to.*.name' => 'nullable|string|max:255',
            'cc' => 'nullable|array',
            'cc.*.email' => 'required|email',
            'cc.*.name' => 'nullable|string|max:255',
            'bcc' => 'nullable|array',
            'bcc.*.email' => 'required|email',
            'bcc.*.name' => 'nullable|string|max:255',
            'template_id' => 'required_without:template_name|integer',
            'template_name' => 'required_without:template_id|string|max:255',
            'template_data' => 'nullable|array',
            'template_data.*' => 'string',
            'set_id' => 'nullable|integer',
            'attachments' => 'nullable|array',
            'attachments.*.path' => 'nullable|string',
            'attachments.*.data' => 'nullable|string',
            'attachments.*.name' => 'required_with:attachments.*.data|string|max:255',
            'attachments.*.mime' => 'nullable|string|max:255',
            'headers' => 'nullable|array',
            'headers.*' => 'string',
        ];
    }
}

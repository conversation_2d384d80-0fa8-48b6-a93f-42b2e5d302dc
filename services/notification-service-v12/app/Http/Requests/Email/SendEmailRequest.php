<?php

namespace App\Http\Requests\Email;

use Illuminate\Foundation\Http\FormRequest;

class SendEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'from_email' => 'required|email',
            'from_name' => 'nullable|string|max:255',
            'to' => 'required|array|min:1',
            'to.*.email' => 'required|email',
            'to.*.name' => 'nullable|string|max:255',
            'cc' => 'nullable|array',
            'cc.*.email' => 'required|email',
            'cc.*.name' => 'nullable|string|max:255',
            'bcc' => 'nullable|array',
            'bcc.*.email' => 'required|email',
            'bcc.*.name' => 'nullable|string|max:255',
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
            'content_type' => 'nullable|string|in:html,text',
            'attachments' => 'nullable|array',
            'attachments.*.path' => 'nullable|string',
            'attachments.*.data' => 'nullable|string',
            'attachments.*.name' => 'required_with:attachments.*.data|string|max:255',
            'attachments.*.mime' => 'nullable|string|max:255',
            'headers' => 'nullable|array',
            'headers.*' => 'string',
        ];
    }
}

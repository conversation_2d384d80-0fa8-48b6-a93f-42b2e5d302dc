<?php

namespace App\Http\Requests;

use App\Services\Email\EmailService;
use Illuminate\Foundation\Http\FormRequest;

class SendEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        // Authorization will be handled by middleware
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'from' => 'sometimes|array',
            'from.*' => 'email',
            'to' => 'required|array|min:1',
            'to.*' => 'email',
            'cc' => 'sometimes|array',
            'cc.*' => 'email',
            'bcc' => 'sometimes|array',
            'bcc.*' => 'email',
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
            'charset' => 'sometimes|string|max:20',
            'attachments' => 'sometimes|array',
            'attachments.*.path' => 'required_with:attachments|string',
            'attachments.*.name' => 'sometimes|string',
            'attachments.*.mime' => 'sometimes|string',
            'content_type' => 'sometimes|string|in:text/html,text/plain',
            'signature' => 'sometimes|string',
            'priority' => 'sometimes|integer|in:' . implode(',', [
                EmailService::PRIORITY_SEND_IMMEDIATELY,
                EmailService::PRIORITY_HIGH_STORE_IN_DATABASE,
                EmailService::PRIORITY_MEDIUM_STORE_IN_DATABASE,
                EmailService::PRIORITY_LOW_STORE_IN_DATABASE,
            ]),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'to.required' => 'At least one recipient email address is required',
            'to.*.email' => 'Each recipient must be a valid email address',
            'from.*.email' => 'Sender must be a valid email address',
            'cc.*.email' => 'Each CC recipient must be a valid email address',
            'bcc.*.email' => 'Each BCC recipient must be a valid email address',
            'subject.required' => 'Email subject is required',
            'body.required' => 'Email body is required',
            'priority.in' => 'Priority must be one of the valid priority levels',
        ];
    }
}

<?php

namespace App\Http\Requests\Template;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmailVariableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'variable' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('email_template_variables', 'variable')->ignore($this->route('id')),
            ],
            'content' => 'sometimes|required|string',
            'type' => 'sometimes|required|string|in:basic,advanced',
            'module' => 'sometimes|required|string|in:email,sms',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ];
    }
}

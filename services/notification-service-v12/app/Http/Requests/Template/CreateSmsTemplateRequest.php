<?php

namespace App\Http\Requests\Template;

use App\Models\SmsSet;
use Illuminate\Foundation\Http\FormRequest;

class CreateSmsTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'fk_set_id' => 'required|exists:sms_sets,pk_set_id',
            'name' => 'required|string|max:255',
            'sms_content' => 'required|string',
            'purpose' => 'nullable|string|max:255',
            'is_approved' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateSmsContentLength($validator);
        });
    }

    /**
     * Validate the SMS content length.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    protected function validateSmsContentLength($validator): void
    {
        $setId = $this->input('fk_set_id');
        $content = $this->input('sms_content');

        if ($setId && $content) {
            $set = SmsSet::find($setId);

            if ($set && strlen($content) > $set->character_limit) {
                $validator->errors()->add(
                    'sms_content',
                    "The SMS content exceeds the character limit of {$set->character_limit} characters."
                );
            }
        }
    }
}

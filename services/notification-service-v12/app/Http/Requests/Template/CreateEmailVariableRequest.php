<?php

namespace App\Http\Requests\Template;

use Illuminate\Foundation\Http\FormRequest;

class CreateEmailVariableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'variable' => 'required|string|max:255|unique:email_template_variables,variable',
            'content' => 'required|string',
            'type' => 'required|string|in:basic,advanced',
            'module' => 'required|string|in:email,sms',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ];
    }
}

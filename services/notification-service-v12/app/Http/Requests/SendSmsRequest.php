<?php

namespace App\Http\Requests;

use App\Services\Email\EmailService;
use Illuminate\Foundation\Http\FormRequest;

class SendSmsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        // Authorization will be handled by middleware
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'mobile' => 'required|string|regex:/^[0-9+\s]+$/',
            'message' => 'required|string',
            'merchant' => 'sometimes|array',
            'priority' => 'sometimes|integer|in:' . implode(',', [
                EmailService::PRIORITY_SMS_IMMEDIATELY,
                EmailService::PRIORITY_SMS_STORE_IN_DATABASE,
            ]),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'mobile.required' => 'Mobile number is required',
            'mobile.regex' => 'Mobile number must contain only digits, plus sign, and spaces',
            'message.required' => 'SMS message is required',
            'priority.in' => 'Priority must be one of the valid priority levels',
        ];
    }
}

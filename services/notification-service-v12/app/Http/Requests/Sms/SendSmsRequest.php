<?php

namespace App\Http\Requests\Sms;

use Illuminate\Foundation\Http\FormRequest;

class SendSmsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'to' => 'required|string|regex:/^[0-9+\s]+$/',
            'message' => 'required|string',
            'from' => 'nullable|string|max:20',
            'options' => 'nullable|array',
            'options.template_id' => 'nullable|string',
            'options.url_callback' => 'nullable|url',
            'options.method_callback' => 'nullable|string|in:GET,POST',
            'options.log' => 'nullable|boolean',
            'options.test' => 'nullable|boolean',
            'options.receipt_url' => 'nullable|url',
            'options.custom' => 'nullable|string',
        ];
    }
}

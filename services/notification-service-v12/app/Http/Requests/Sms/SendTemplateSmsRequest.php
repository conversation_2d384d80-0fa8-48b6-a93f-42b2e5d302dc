<?php

namespace App\Http\Requests\Sms;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SendTemplateSmsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'to' => 'required|string|regex:/^[0-9+\s]+$/',
            'template_id' => 'required_without:template_name|integer',
            'template_name' => 'required_without:template_id|string|max:255',
            'template_data' => 'nullable|array',
            'template_data.*' => 'string',
            'set_id' => 'nullable|integer',
            'from' => 'nullable|string|max:20',
            'options' => 'nullable|array',
            'options.url_callback' => 'nullable|url',
            'options.method_callback' => 'nullable|string|in:GET,POST',
            'options.log' => 'nullable|boolean',
            'options.test' => 'nullable|boolean',
            'options.receipt_url' => 'nullable|url',
            'options.custom' => 'nullable|string',
        ];
    }
}

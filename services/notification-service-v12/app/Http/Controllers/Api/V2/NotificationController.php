<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\SendEmailRequest;
use App\Http\Requests\SendSmsRequest;
use App\Services\Email\EmailService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    /**
     * The email service instance.
     *
     * @var EmailService
     */
    protected $emailService;

    /**
     * Create a new controller instance.
     *
     * @param EmailService $emailService
     * @return void
     */
    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Send an email.
     *
     * @param SendEmailRequest $request
     * @return JsonResponse
     */
    public function sendEmail(SendEmailRequest $request): JsonResponse
    {
        try {
            // Set configuration
            $this->emailService->setConfiguration(config('mail'));
            
            // Set priority
            $this->emailService->setPriority($request->input('priority', EmailService::PRIORITY_LOW_STORE_IN_DATABASE));
            
            // Send email
            $result = $this->emailService->sendmail(
                $request->input('from', []),
                $request->input('to', []),
                $request->input('cc', []),
                $request->input('bcc', []),
                $request->input('subject', ''),
                $request->input('body', ''),
                $request->input('charset', 'UTF-8'),
                $request->input('attachments', []),
                $request->input('content_type', 'text/html'),
                $request->input('signature', '')
            );
            
            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Email sent successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send email'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Email sending failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send an SMS.
     *
     * @param SendSmsRequest $request
     * @return JsonResponse
     */
    public function sendSms(SendSmsRequest $request): JsonResponse
    {
        try {
            // Set configuration
            $this->emailService->setConfiguration(config('mail'));
            $this->emailService->setSMSConfiguration(config('sms'));
            
            // Set merchant data if provided
            if ($request->has('merchant')) {
                $this->emailService->setMerchantData($request->input('merchant'));
            }
            
            // Set mobile number
            $this->emailService->setMobileNo($request->input('mobile'));
            
            // Set SMS message
            $this->emailService->setSMSMessage($request->input('message'));
            
            // Send SMS
            $result = $this->emailService->sendmessage(
                $request->input('priority', EmailService::PRIORITY_SMS_IMMEDIATELY)
            );
            
            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'SMS sent successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send SMS'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('SMS sending failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send SMS: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get email queue status.
     *
     * @return JsonResponse
     */
    public function getEmailQueueStatus(): JsonResponse
    {
        try {
            $status = [
                'pending' => \DB::table('email_queue')->where('status', 'pending')->count(),
                'processing' => \DB::table('email_queue')->where('status', 'processing')->count(),
                'sent' => \DB::table('email_queue')->where('status', 'sent')->count(),
                'failed' => \DB::table('email_queue')->where('status', 'failed')->count(),
            ];
            
            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get email queue status', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get email queue status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get SMS queue status.
     *
     * @return JsonResponse
     */
    public function getSmsQueueStatus(): JsonResponse
    {
        try {
            $status = [
                'pending' => \DB::table('sms_queue')->where('status', 'pending')->count(),
                'processing' => \DB::table('sms_queue')->where('status', 'processing')->count(),
                'sent' => \DB::table('sms_queue')->where('status', 'sent')->count(),
                'failed' => \DB::table('sms_queue')->where('status', 'failed')->count(),
            ];
            
            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get SMS queue status', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get SMS queue status: ' . $e->getMessage()
            ], 500);
        }
    }
}

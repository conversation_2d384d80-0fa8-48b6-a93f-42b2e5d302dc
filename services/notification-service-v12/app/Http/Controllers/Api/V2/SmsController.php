<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Sms\SendBulkSmsRequest;
use App\Http\Requests\Sms\SendBulkTemplateSmsRequest;
use App\Http\Requests\Sms\SendSmsRequest;
use App\Http\Requests\Sms\SendTemplateSmsRequest;
use App\Services\Sms\SmsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class SmsController extends Controller
{
    /**
     * @var SmsService
     */
    protected $smsService;

    /**
     * SmsController constructor.
     *
     * @param SmsService $smsService
     */
    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Send an SMS
     *
     * @param SendSmsRequest $request
     * @return JsonResponse
     */
    public function send(SendSmsRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        $to = $data['to'];
        $message = $data['message'];
        $from = $data['from'] ?? null;
        $options = $data['options'] ?? [];
        
        $result = $this->smsService->send($to, $message, $from, $options);
        
        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'SMS sent successfully',
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send SMS',
            ], 500);
        }
    }

    /**
     * Send bulk SMS
     *
     * @param SendBulkSmsRequest $request
     * @return JsonResponse
     */
    public function sendBulk(SendBulkSmsRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        $to = $data['to'];
        $message = $data['message'];
        $from = $data['from'] ?? null;
        $options = $data['options'] ?? [];
        
        $results = $this->smsService->sendBulk($to, $message, $from, $options);
        
        $successCount = count(array_filter($results));
        $failureCount = count($results) - $successCount;
        
        return response()->json([
            'success' => $successCount > 0,
            'message' => "SMS sent to {$successCount} recipients, failed for {$failureCount} recipients",
            'results' => $results,
        ]);
    }

    /**
     * Send an SMS using a template
     *
     * @param SendTemplateSmsRequest $request
     * @return JsonResponse
     */
    public function sendTemplate(SendTemplateSmsRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        $to = $data['to'];
        $templateIdOrName = $data['template_id'] ?? $data['template_name'];
        $templateData = $data['template_data'] ?? [];
        $setId = $data['set_id'] ?? null;
        $from = $data['from'] ?? null;
        $options = $data['options'] ?? [];
        
        $result = $this->smsService->sendTemplate(
            $to,
            $templateIdOrName,
            $templateData,
            $setId,
            $from,
            $options
        );
        
        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'SMS sent successfully',
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send SMS',
            ], 500);
        }
    }

    /**
     * Send bulk SMS using a template
     *
     * @param SendBulkTemplateSmsRequest $request
     * @return JsonResponse
     */
    public function sendBulkTemplate(SendBulkTemplateSmsRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        $to = $data['to'];
        $templateIdOrName = $data['template_id'] ?? $data['template_name'];
        $templateData = $data['template_data'] ?? [];
        $setId = $data['set_id'] ?? null;
        $from = $data['from'] ?? null;
        $options = $data['options'] ?? [];
        
        $results = $this->smsService->sendBulkTemplate(
            $to,
            $templateIdOrName,
            $templateData,
            $setId,
            $from,
            $options
        );
        
        $successCount = count(array_filter($results));
        $failureCount = count($results) - $successCount;
        
        return response()->json([
            'success' => $successCount > 0,
            'message' => "SMS sent to {$successCount} recipients, failed for {$failureCount} recipients",
            'results' => $results,
        ]);
    }
}

<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Template\CreateSmsSetRequest;
use App\Http\Requests\Template\CreateSmsTemplateRequest;
use App\Http\Requests\Template\PreviewSmsTemplateRequest;
use App\Http\Requests\Template\UpdateSmsSetRequest;
use App\Http\Requests\Template\UpdateSmsTemplateRequest;
use App\Services\Template\SmsTemplateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SmsTemplateController extends Controller
{
    /**
     * @var SmsTemplateService
     */
    protected $smsTemplateService;

    /**
     * SmsTemplateController constructor.
     *
     * @param SmsTemplateService $smsTemplateService
     */
    public function __construct(SmsTemplateService $smsTemplateService)
    {
        $this->smsTemplateService = $smsTemplateService;
    }

    /**
     * Get all SMS sets
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAllSets(Request $request): JsonResponse
    {
        $activeOnly = $request->boolean('active_only', true);
        $sets = $this->smsTemplateService->getAllSets($activeOnly);

        return response()->json([
            'success' => true,
            'data' => $sets,
        ]);
    }

    /**
     * Get an SMS set by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getSetById(int $id): JsonResponse
    {
        $set = $this->smsTemplateService->getSetById($id);

        if (!$set) {
            return response()->json([
                'success' => false,
                'message' => 'SMS set not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $set,
        ]);
    }

    /**
     * Create a new SMS set
     *
     * @param CreateSmsSetRequest $request
     * @return JsonResponse
     */
    public function createSet(CreateSmsSetRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['created_by'] = auth()->id();
        $data['modified_by'] = auth()->id();

        $set = $this->smsTemplateService->createSet($data);

        return response()->json([
            'success' => true,
            'message' => 'SMS set created successfully',
            'data' => $set,
        ], 201);
    }

    /**
     * Update an SMS set
     *
     * @param UpdateSmsSetRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateSet(UpdateSmsSetRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $data['modified_by'] = auth()->id();

        $set = $this->smsTemplateService->updateSet($id, $data);

        if (!$set) {
            return response()->json([
                'success' => false,
                'message' => 'SMS set not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'SMS set updated successfully',
            'data' => $set,
        ]);
    }

    /**
     * Delete an SMS set
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteSet(int $id): JsonResponse
    {
        $result = $this->smsTemplateService->deleteSet($id);

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'SMS set not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'SMS set deleted successfully',
        ]);
    }

    /**
     * Get all SMS templates for a set
     *
     * @param Request $request
     * @param int $setId
     * @return JsonResponse
     */
    public function getTemplatesBySetId(Request $request, int $setId): JsonResponse
    {
        $activeOnly = $request->boolean('active_only', true);
        $approvedOnly = $request->boolean('approved_only', false);
        $templates = $this->smsTemplateService->getTemplatesBySetId($setId, $activeOnly, $approvedOnly);

        return response()->json([
            'success' => true,
            'data' => $templates,
        ]);
    }

    /**
     * Get an SMS template by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getTemplateById(int $id): JsonResponse
    {
        $template = $this->smsTemplateService->getTemplateById($id);

        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'SMS template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $template,
        ]);
    }

    /**
     * Create a new SMS template
     *
     * @param CreateSmsTemplateRequest $request
     * @return JsonResponse
     */
    public function createTemplate(CreateSmsTemplateRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['created_by'] = auth()->id();
        $data['modified_by'] = auth()->id();

        $template = $this->smsTemplateService->createTemplate($data);

        return response()->json([
            'success' => true,
            'message' => 'SMS template created successfully',
            'data' => $template,
        ], 201);
    }

    /**
     * Update an SMS template
     *
     * @param UpdateSmsTemplateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateTemplate(UpdateSmsTemplateRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $data['modified_by'] = auth()->id();

        $template = $this->smsTemplateService->updateTemplate($id, $data);

        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'SMS template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'SMS template updated successfully',
            'data' => $template,
        ]);
    }

    /**
     * Delete an SMS template
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteTemplate(int $id): JsonResponse
    {
        $result = $this->smsTemplateService->deleteTemplate($id);

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'SMS template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'SMS template deleted successfully',
        ]);
    }

    /**
     * Approve an SMS template
     *
     * @param int $id
     * @return JsonResponse
     */
    public function approveTemplate(int $id): JsonResponse
    {
        $template = $this->smsTemplateService->approveTemplate($id);

        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'SMS template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'SMS template approved successfully',
            'data' => $template,
        ]);
    }

    /**
     * Get all SMS template variables
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAllVariables(Request $request): JsonResponse
    {
        $type = $request->input('type');
        $activeOnly = $request->boolean('active_only', true);

        $variables = $this->smsTemplateService->getAllVariables($type, $activeOnly);

        return response()->json([
            'success' => true,
            'data' => $variables,
        ]);
    }

    /**
     * Preview an SMS template
     *
     * @param PreviewSmsTemplateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function previewTemplate(PreviewSmsTemplateRequest $request, int $id): JsonResponse
    {
        $data = $request->input('data', []);
        $preview = $this->smsTemplateService->previewTemplate($id, $data);

        if (!$preview) {
            return response()->json([
                'success' => false,
                'message' => 'SMS template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $preview,
        ]);
    }
}

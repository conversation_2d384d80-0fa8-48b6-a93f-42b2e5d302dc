<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Template\CreateEmailSetRequest;
use App\Http\Requests\Template\CreateEmailTemplateRequest;
use App\Http\Requests\Template\CreateEmailVariableRequest;
use App\Http\Requests\Template\PreviewEmailTemplateRequest;
use App\Http\Requests\Template\UpdateEmailSetRequest;
use App\Http\Requests\Template\UpdateEmailTemplateRequest;
use App\Http\Requests\Template\UpdateEmailVariableRequest;
use App\Services\Template\EmailTemplateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EmailTemplateController extends Controller
{
    /**
     * @var EmailTemplateService
     */
    protected $emailTemplateService;

    /**
     * EmailTemplateController constructor.
     *
     * @param EmailTemplateService $emailTemplateService
     */
    public function __construct(EmailTemplateService $emailTemplateService)
    {
        $this->emailTemplateService = $emailTemplateService;
    }

    /**
     * Get all email template sets
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAllSets(Request $request): JsonResponse
    {
        $activeOnly = $request->boolean('active_only', true);
        $sets = $this->emailTemplateService->getAllSets($activeOnly);

        return response()->json([
            'success' => true,
            'data' => $sets,
        ]);
    }

    /**
     * Get an email template set by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getSetById(int $id): JsonResponse
    {
        $set = $this->emailTemplateService->getSetById($id);

        if (!$set) {
            return response()->json([
                'success' => false,
                'message' => 'Email template set not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $set,
        ]);
    }

    /**
     * Create a new email template set
     *
     * @param CreateEmailSetRequest $request
     * @return JsonResponse
     */
    public function createSet(CreateEmailSetRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['created_by'] = auth()->id();
        $data['modified_by'] = auth()->id();

        $set = $this->emailTemplateService->createSet($data);

        return response()->json([
            'success' => true,
            'message' => 'Email template set created successfully',
            'data' => $set,
        ], 201);
    }

    /**
     * Update an email template set
     *
     * @param UpdateEmailSetRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateSet(UpdateEmailSetRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $data['modified_by'] = auth()->id();

        $set = $this->emailTemplateService->updateSet($id, $data);

        if (!$set) {
            return response()->json([
                'success' => false,
                'message' => 'Email template set not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email template set updated successfully',
            'data' => $set,
        ]);
    }

    /**
     * Delete an email template set
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteSet(int $id): JsonResponse
    {
        $result = $this->emailTemplateService->deleteSet($id);

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'Email template set not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email template set deleted successfully',
        ]);
    }

    /**
     * Get all email templates for a set
     *
     * @param Request $request
     * @param int $setId
     * @return JsonResponse
     */
    public function getTemplatesBySetId(Request $request, int $setId): JsonResponse
    {
        $activeOnly = $request->boolean('active_only', true);
        $templates = $this->emailTemplateService->getTemplatesBySetId($setId, $activeOnly);

        return response()->json([
            'success' => true,
            'data' => $templates,
        ]);
    }

    /**
     * Get an email template by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getTemplateById(int $id): JsonResponse
    {
        $template = $this->emailTemplateService->getTemplateById($id);

        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'Email template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $template,
        ]);
    }

    /**
     * Create a new email template
     *
     * @param CreateEmailTemplateRequest $request
     * @return JsonResponse
     */
    public function createTemplate(CreateEmailTemplateRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['created_by'] = auth()->id();
        $data['modified_by'] = auth()->id();

        $template = $this->emailTemplateService->createTemplate($data);

        return response()->json([
            'success' => true,
            'message' => 'Email template created successfully',
            'data' => $template,
        ], 201);
    }

    /**
     * Update an email template
     *
     * @param UpdateEmailTemplateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateTemplate(UpdateEmailTemplateRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $data['modified_by'] = auth()->id();

        $template = $this->emailTemplateService->updateTemplate($id, $data);

        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'Email template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email template updated successfully',
            'data' => $template,
        ]);
    }

    /**
     * Delete an email template
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteTemplate(int $id): JsonResponse
    {
        $result = $this->emailTemplateService->deleteTemplate($id);

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'Email template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email template deleted successfully',
        ]);
    }

    /**
     * Get all email template variables
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAllVariables(Request $request): JsonResponse
    {
        $module = $request->input('module', 'email');
        $type = $request->input('type');
        $activeOnly = $request->boolean('active_only', true);

        $variables = $this->emailTemplateService->getAllVariables($module, $type, $activeOnly);

        return response()->json([
            'success' => true,
            'data' => $variables,
        ]);
    }

    /**
     * Preview an email template
     *
     * @param PreviewEmailTemplateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function previewTemplate(PreviewEmailTemplateRequest $request, int $id): JsonResponse
    {
        $data = $request->input('data', []);
        $preview = $this->emailTemplateService->previewTemplate($id, $data);

        if (!$preview) {
            return response()->json([
                'success' => false,
                'message' => 'Email template not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $preview,
        ]);
    }

    /**
     * Create a new email template variable
     *
     * @param CreateEmailVariableRequest $request
     * @return JsonResponse
     */
    public function createVariable(CreateEmailVariableRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['created_by'] = auth()->id();
        $data['modified_by'] = auth()->id();

        $variable = $this->emailTemplateService->createVariable($data);

        return response()->json([
            'success' => true,
            'message' => 'Email template variable created successfully',
            'data' => $variable,
        ], 201);
    }

    /**
     * Update an email template variable
     *
     * @param UpdateEmailVariableRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateVariable(UpdateEmailVariableRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $data['modified_by'] = auth()->id();

        $variable = $this->emailTemplateService->updateVariable($id, $data);

        if (!$variable) {
            return response()->json([
                'success' => false,
                'message' => 'Email template variable not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email template variable updated successfully',
            'data' => $variable,
        ]);
    }

    /**
     * Delete an email template variable
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteVariable(int $id): JsonResponse
    {
        $result = $this->emailTemplateService->deleteVariable($id);

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'Email template variable not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email template variable deleted successfully',
        ]);
    }
}

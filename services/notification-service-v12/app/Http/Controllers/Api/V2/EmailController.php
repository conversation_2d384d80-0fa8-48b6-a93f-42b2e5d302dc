<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Email\SendEmailRequest;
use App\Http\Requests\Email\SendTemplateEmailRequest;
use App\Services\Email\ProviderEmailService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class EmailController extends Controller
{
    /**
     * @var ProviderEmailService
     */
    protected $emailService;

    /**
     * EmailController constructor.
     *
     * @param ProviderEmailService $emailService
     */
    public function __construct(ProviderEmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Send an email
     *
     * @param SendEmailRequest $request
     * @return JsonResponse
     */
    public function send(SendEmailRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        $from = [$data['from_email'] => $data['from_name'] ?? config('mail.from.name')];
        
        $to = [];
        foreach ($data['to'] as $recipient) {
            $to[$recipient['email']] = $recipient['name'] ?? '';
        }
        
        $cc = [];
        if (!empty($data['cc'])) {
            foreach ($data['cc'] as $recipient) {
                $cc[$recipient['email']] = $recipient['name'] ?? '';
            }
        }
        
        $bcc = [];
        if (!empty($data['bcc'])) {
            foreach ($data['bcc'] as $recipient) {
                $bcc[$recipient['email']] = $recipient['name'] ?? '';
            }
        }
        
        $subject = $data['subject'];
        $body = $data['body'];
        $contentType = $data['content_type'] ?? 'html';
        
        $attachments = [];
        if (!empty($data['attachments'])) {
            $attachments = $data['attachments'];
        }
        
        $headers = [];
        if (!empty($data['headers'])) {
            $headers = $data['headers'];
        }
        
        $result = $this->emailService->send(
            $from,
            $to,
            $cc,
            $bcc,
            $subject,
            $body,
            $contentType,
            $attachments,
            $headers
        );
        
        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'Email sent successfully',
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send email',
            ], 500);
        }
    }

    /**
     * Send an email using a template
     *
     * @param SendTemplateEmailRequest $request
     * @return JsonResponse
     */
    public function sendTemplate(SendTemplateEmailRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        $from = [$data['from_email'] => $data['from_name'] ?? config('mail.from.name')];
        
        $to = [];
        foreach ($data['to'] as $recipient) {
            $to[$recipient['email']] = $recipient['name'] ?? '';
        }
        
        $cc = [];
        if (!empty($data['cc'])) {
            foreach ($data['cc'] as $recipient) {
                $cc[$recipient['email']] = $recipient['name'] ?? '';
            }
        }
        
        $bcc = [];
        if (!empty($data['bcc'])) {
            foreach ($data['bcc'] as $recipient) {
                $bcc[$recipient['email']] = $recipient['name'] ?? '';
            }
        }
        
        $templateIdOrName = $data['template_id'] ?? $data['template_name'];
        $templateData = $data['template_data'] ?? [];
        $setId = $data['set_id'] ?? null;
        
        $attachments = [];
        if (!empty($data['attachments'])) {
            $attachments = $data['attachments'];
        }
        
        $headers = [];
        if (!empty($data['headers'])) {
            $headers = $data['headers'];
        }
        
        $result = $this->emailService->sendTemplate(
            $from,
            $to,
            $cc,
            $bcc,
            $templateIdOrName,
            $templateData,
            $setId,
            $attachments,
            $headers
        );
        
        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'Email sent successfully',
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send email',
            ], 500);
        }
    }
}

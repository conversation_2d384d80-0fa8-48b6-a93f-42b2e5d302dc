<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class SendSmsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * The SMS queue ID.
     *
     * @var int
     */
    protected $smsId;

    /**
     * Create a new job instance.
     *
     * @param int $smsId
     * @return void
     */
    public function __construct($smsId)
    {
        $this->smsId = $smsId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Get SMS data from database
        $smsData = DB::table('sms_queue')
            ->where('id', $this->smsId)
            ->where('status', 'pending')
            ->first();

        if (!$smsData) {
            Log::info('SMS not found or already processed', ['id' => $this->smsId]);
            return;
        }

        try {
            // Update status to processing
            DB::table('sms_queue')
                ->where('id', $this->smsId)
                ->update([
                    'status' => 'processing',
                    'updated_at' => now()
                ]);

            // Get SMS configuration
            $smsConfig = config('sms');
            
            // Prepare parameters for the SMS gateway
            $params = [
                'mobile' => $smsData->mobile,
                'message' => urlencode($smsData->message),
                'sender' => $smsConfig['sender_id'] ?? '',
                'authkey' => $smsConfig['auth_key'] ?? '',
            ];
            
            // Make HTTP request to the SMS gateway
            $response = Http::get($smsConfig['gateway_url'], $params);
            
            if ($response->successful()) {
                // Update status to sent
                DB::table('sms_queue')
                    ->where('id', $this->smsId)
                    ->update([
                        'status' => 'sent',
                        'response' => $response->body(),
                        'sent_at' => now(),
                        'updated_at' => now()
                    ]);

                Log::info('Queued SMS sent successfully', ['id' => $this->smsId]);
            } else {
                throw new \Exception('SMS gateway returned error: ' . $response->body());
            }
        } catch (\Exception $e) {
            // Update status to failed
            DB::table('sms_queue')
                ->where('id', $this->smsId)
                ->update([
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'updated_at' => now()
                ]);

            Log::error('Failed to send queued SMS', [
                'id' => $this->smsId,
                'error' => $e->getMessage()
            ]);

            // Rethrow the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        // Update status to failed
        DB::table('sms_queue')
            ->where('id', $this->smsId)
            ->update([
                'status' => 'failed',
                'error' => $exception->getMessage(),
                'updated_at' => now()
            ]);

        Log::error('SMS job failed after retries', [
            'id' => $this->smsId,
            'error' => $exception->getMessage()
        ]);
    }
}

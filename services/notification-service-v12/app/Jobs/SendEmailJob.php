<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * The email queue ID.
     *
     * @var int
     */
    protected $emailId;

    /**
     * Create a new job instance.
     *
     * @param int $emailId
     * @return void
     */
    public function __construct($emailId)
    {
        $this->emailId = $emailId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Get email data from database
        $emailData = DB::table('email_queue')
            ->where('id', $this->emailId)
            ->where('status', 'pending')
            ->first();

        if (!$emailData) {
            Log::info('Email not found or already processed', ['id' => $this->emailId]);
            return;
        }

        try {
            // Update status to processing
            DB::table('email_queue')
                ->where('id', $this->emailId)
                ->update([
                    'status' => 'processing',
                    'updated_at' => now()
                ]);

            // Parse recipients
            $to = json_decode($emailData->to_email, true);
            $cc = json_decode($emailData->cc_email, true) ?? [];
            $bcc = json_decode($emailData->bcc_email, true) ?? [];
            $attachments = json_decode($emailData->attachments, true) ?? [];

            // Send email
            Mail::send([], [], function ($message) use ($emailData, $to, $cc, $bcc, $attachments) {
                $message->from($emailData->from_email, $emailData->from_name)
                    ->subject($emailData->subject);
                
                // Add recipients
                foreach ($to as $name => $email) {
                    $message->to($email, is_string($name) ? $name : null);
                }
                
                // Add CC recipients
                foreach ($cc as $name => $email) {
                    $message->cc($email, is_string($name) ? $name : null);
                }
                
                // Add BCC recipients
                foreach ($bcc as $name => $email) {
                    $message->bcc($email, is_string($name) ? $name : null);
                }
                
                // Set content type and body
                $message->setBody($emailData->body, $emailData->content_type);
                
                // Add attachments
                foreach ($attachments as $attachment) {
                    if (isset($attachment['path'])) {
                        $message->attach($attachment['path'], [
                            'as' => $attachment['name'] ?? basename($attachment['path']),
                            'mime' => $attachment['mime'] ?? null
                        ]);
                    }
                }
            });

            // Update status to sent
            DB::table('email_queue')
                ->where('id', $this->emailId)
                ->update([
                    'status' => 'sent',
                    'sent_at' => now(),
                    'updated_at' => now()
                ]);

            Log::info('Queued email sent successfully', ['id' => $this->emailId]);
        } catch (\Exception $e) {
            // Update status to failed
            DB::table('email_queue')
                ->where('id', $this->emailId)
                ->update([
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'updated_at' => now()
                ]);

            Log::error('Failed to send queued email', [
                'id' => $this->emailId,
                'error' => $e->getMessage()
            ]);

            // Rethrow the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        // Update status to failed
        DB::table('email_queue')
            ->where('id', $this->emailId)
            ->update([
                'status' => 'failed',
                'error' => $exception->getMessage(),
                'updated_at' => now()
            ]);

        Log::error('Email job failed after retries', [
            'id' => $this->emailId,
            'error' => $exception->getMessage()
        ]);
    }
}

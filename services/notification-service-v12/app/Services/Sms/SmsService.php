<?php

namespace App\Services\Sms;

use App\Services\Sms\Providers\SmsProviderInterface;
use App\Services\Template\SmsTemplateService;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * SMS service
 */
class SmsService
{
    /**
     * SMS providers
     *
     * @var array
     */
    protected $providers = [];

    /**
     * SMS template service
     *
     * @var SmsTemplateService|null
     */
    protected $templateService;

    /**
     * SmsService constructor
     *
     * @param array $providers
     * @param SmsTemplateService|null $templateService
     */
    public function __construct(array $providers = [], ?SmsTemplateService $templateService = null)
    {
        $this->providers = $providers;
        $this->templateService = $templateService;
        
        // Sort providers by priority
        usort($this->providers, function (SmsProviderInterface $a, SmsProviderInterface $b) {
            return $a->getPriority() - $b->getPriority();
        });
    }

    /**
     * Add a provider
     *
     * @param SmsProviderInterface $provider
     * @return self
     */
    public function addProvider(SmsProviderInterface $provider): self
    {
        $this->providers[] = $provider;
        
        // Sort providers by priority
        usort($this->providers, function (SmsProviderInterface $a, SmsProviderInterface $b) {
            return $a->getPriority() - $b->getPriority();
        });
        
        return $this;
    }

    /**
     * Get all providers
     *
     * @return array
     */
    public function getProviders(): array
    {
        return $this->providers;
    }

    /**
     * Get available providers
     *
     * @return array
     */
    public function getAvailableProviders(): array
    {
        return array_filter($this->providers, function (SmsProviderInterface $provider) {
            return $provider->isAvailable();
        });
    }

    /**
     * Send an SMS
     *
     * @param string $to Recipient phone number
     * @param string $message SMS message
     * @param string|null $from Sender ID or phone number (optional)
     * @param array $options Additional options
     * @return bool True if the SMS was sent successfully, false otherwise
     */
    public function send(string $to, string $message, ?string $from = null, array $options = []): bool
    {
        $availableProviders = $this->getAvailableProviders();
        
        if (empty($availableProviders)) {
            Log::error('No available SMS providers');
            return false;
        }
        
        $exceptions = [];
        
        foreach ($availableProviders as $provider) {
            try {
                $result = $provider->send($to, $message, $from, $options);
                
                if ($result) {
                    Log::info('SMS sent successfully', [
                        'provider' => $provider->getName(),
                        'to' => $to,
                    ]);
                    
                    return true;
                }
            } catch (Exception $e) {
                $exceptions[$provider->getName()] = $e->getMessage();
                
                Log::warning('SMS provider failed', [
                    'provider' => $provider->getName(),
                    'error' => $e->getMessage(),
                    'to' => $to,
                ]);
                
                // Continue with the next provider
                continue;
            }
        }
        
        Log::error('All SMS providers failed', [
            'exceptions' => $exceptions,
            'to' => $to,
        ]);
        
        return false;
    }

    /**
     * Send bulk SMS
     *
     * @param array $to Recipient phone numbers
     * @param string $message SMS message
     * @param string|null $from Sender ID or phone number (optional)
     * @param array $options Additional options
     * @return array Array of results, keyed by phone number
     */
    public function sendBulk(array $to, string $message, ?string $from = null, array $options = []): array
    {
        $availableProviders = $this->getAvailableProviders();
        
        if (empty($availableProviders)) {
            Log::error('No available SMS providers');
            
            $results = [];
            foreach ($to as $recipient) {
                $results[$recipient] = false;
            }
            
            return $results;
        }
        
        // Try to send with the first available provider that supports bulk sending
        foreach ($availableProviders as $provider) {
            try {
                $results = $provider->sendBulk($to, $message, $from, $options);
                
                Log::info('Bulk SMS sent with provider', [
                    'provider' => $provider->getName(),
                    'to_count' => count($to),
                    'success_count' => count(array_filter($results)),
                ]);
                
                return $results;
            } catch (Exception $e) {
                Log::warning('Bulk SMS provider failed', [
                    'provider' => $provider->getName(),
                    'error' => $e->getMessage(),
                    'to_count' => count($to),
                ]);
                
                // Continue with the next provider
                continue;
            }
        }
        
        // If all bulk providers failed, try to send individually
        $results = [];
        foreach ($to as $recipient) {
            $results[$recipient] = $this->send($recipient, $message, $from, $options);
        }
        
        return $results;
    }

    /**
     * Send an SMS using a template
     *
     * @param string $to Recipient phone number
     * @param int|string $templateIdOrName Template ID or name
     * @param array $data Template data
     * @param int|null $setId Template set ID (optional, used when templateIdOrName is a name)
     * @param string|null $from Sender ID or phone number (optional)
     * @param array $options Additional options
     * @return bool True if the SMS was sent successfully, false otherwise
     */
    public function sendTemplate(
        string $to,
        $templateIdOrName,
        array $data = [],
        ?int $setId = null,
        ?string $from = null,
        array $options = []
    ): bool {
        if (!$this->templateService) {
            Log::error('SMS template service not available');
            return false;
        }
        
        $content = $this->templateService->renderTemplate($templateIdOrName, $data, $setId);
        
        if (!$content) {
            Log::error('SMS template not found', [
                'templateIdOrName' => $templateIdOrName,
                'setId' => $setId,
            ]);
            
            return false;
        }
        
        return $this->send($to, $content, $from, $options);
    }

    /**
     * Send bulk SMS using a template
     *
     * @param array $to Recipient phone numbers
     * @param int|string $templateIdOrName Template ID or name
     * @param array $data Template data
     * @param int|null $setId Template set ID (optional, used when templateIdOrName is a name)
     * @param string|null $from Sender ID or phone number (optional)
     * @param array $options Additional options
     * @return array Array of results, keyed by phone number
     */
    public function sendBulkTemplate(
        array $to,
        $templateIdOrName,
        array $data = [],
        ?int $setId = null,
        ?string $from = null,
        array $options = []
    ): array {
        if (!$this->templateService) {
            Log::error('SMS template service not available');
            
            $results = [];
            foreach ($to as $recipient) {
                $results[$recipient] = false;
            }
            
            return $results;
        }
        
        $content = $this->templateService->renderTemplate($templateIdOrName, $data, $setId);
        
        if (!$content) {
            Log::error('SMS template not found', [
                'templateIdOrName' => $templateIdOrName,
                'setId' => $setId,
            ]);
            
            $results = [];
            foreach ($to as $recipient) {
                $results[$recipient] = false;
            }
            
            return $results;
        }
        
        return $this->sendBulk($to, $content, $from, $options);
    }
}

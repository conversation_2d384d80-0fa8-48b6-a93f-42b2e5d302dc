<?php

namespace App\Services\Sms\Providers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Plivo SMS provider
 */
class PlivoProvider implements SmsProviderInterface
{
    /**
     * Provider name
     *
     * @var string
     */
    protected $name = 'plivo';

    /**
     * Provider configuration
     *
     * @var array
     */
    protected $config;

    /**
     * API URL
     *
     * @var string
     */
    protected $apiUrl = 'https://api.plivo.com/v1/Account/{auth_id}/Message/';

    /**
     * PlivoProvider constructor
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        
        if (!empty($config['api_url'])) {
            $this->apiUrl = $config['api_url'];
        }
    }

    /**
     * {@inheritdoc}
     */
    public function send(string $to, string $message, ?string $from = null, array $options = []): bool
    {
        try {
            $authId = $this->config['auth_id'] ?? '';
            $authToken = $this->config['auth_token'] ?? '';
            
            if (empty($authId) || empty($authToken)) {
                Log::error('Plivo credentials not configured');
                return false;
            }
            
            $apiUrl = str_replace('{auth_id}', $authId, $this->apiUrl);
            
            $params = [
                'src' => $from ?? $this->config['sender_id'] ?? '',
                'dst' => $to,
                'text' => $message,
            ];
            
            // Add additional parameters from options
            if (!empty($options['url_callback'])) {
                $params['url'] = $options['url_callback'];
            }
            
            if (!empty($options['method_callback'])) {
                $params['method'] = $options['method_callback'];
            }
            
            if (!empty($options['log'])) {
                $params['log'] = $options['log'];
            }
            
            $response = Http::withBasicAuth($authId, $authToken)
                ->asJson()
                ->post($apiUrl, $params);
            
            if ($response->successful()) {
                $result = $response->json();
                
                // Check for success in the response
                if (isset($result['message_uuid']) && !empty($result['message_uuid'])) {
                    Log::info('Plivo SMS sent successfully', [
                        'to' => $to,
                        'message_uuid' => $result['message_uuid'],
                    ]);
                    
                    return true;
                } else {
                    Log::error('Plivo SMS sending failed', [
                        'to' => $to,
                        'error' => $result['error'] ?? null,
                    ]);
                    
                    return false;
                }
            } else {
                Log::error('Plivo API request failed', [
                    'to' => $to,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                
                return false;
            }
        } catch (Exception $e) {
            Log::error('Plivo SMS sending exception', [
                'to' => $to,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function sendBulk(array $to, string $message, ?string $from = null, array $options = []): array
    {
        try {
            $authId = $this->config['auth_id'] ?? '';
            $authToken = $this->config['auth_token'] ?? '';
            
            if (empty($authId) || empty($authToken)) {
                Log::error('Plivo credentials not configured');
                
                $results = [];
                foreach ($to as $recipient) {
                    $results[$recipient] = false;
                }
                
                return $results;
            }
            
            $apiUrl = str_replace('{auth_id}', $authId, $this->apiUrl);
            
            $params = [
                'src' => $from ?? $this->config['sender_id'] ?? '',
                'dst' => implode('<', $to),
                'text' => $message,
            ];
            
            // Add additional parameters from options
            if (!empty($options['url_callback'])) {
                $params['url'] = $options['url_callback'];
            }
            
            if (!empty($options['method_callback'])) {
                $params['method'] = $options['method_callback'];
            }
            
            if (!empty($options['log'])) {
                $params['log'] = $options['log'];
            }
            
            $response = Http::withBasicAuth($authId, $authToken)
                ->asJson()
                ->post($apiUrl, $params);
            
            if ($response->successful()) {
                $result = $response->json();
                
                // Check for success in the response
                if (isset($result['message_uuid']) && !empty($result['message_uuid'])) {
                    Log::info('Plivo bulk SMS sent successfully', [
                        'to' => $to,
                        'message_uuid' => $result['message_uuid'],
                    ]);
                    
                    $results = [];
                    foreach ($to as $recipient) {
                        $results[$recipient] = true;
                    }
                    
                    return $results;
                } else {
                    Log::error('Plivo bulk SMS sending failed', [
                        'to' => $to,
                        'error' => $result['error'] ?? null,
                    ]);
                    
                    $results = [];
                    foreach ($to as $recipient) {
                        $results[$recipient] = false;
                    }
                    
                    return $results;
                }
            } else {
                Log::error('Plivo API request failed', [
                    'to' => $to,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                
                $results = [];
                foreach ($to as $recipient) {
                    $results[$recipient] = false;
                }
                
                return $results;
            }
        } catch (Exception $e) {
            Log::error('Plivo bulk SMS sending exception', [
                'to' => $to,
                'error' => $e->getMessage(),
            ]);
            
            $results = [];
            foreach ($to as $recipient) {
                $results[$recipient] = false;
            }
            
            return $results;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * {@inheritdoc}
     */
    public function isAvailable(): bool
    {
        return !empty($this->config['auth_id']) && !empty($this->config['auth_token']);
    }
    
    /**
     * {@inheritdoc}
     */
    public function getPriority(): int
    {
        return 20; // Priority for Plivo (lower than 24x7 SMS)
    }
}

<?php

namespace App\Services\Sms\Providers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 24x7 SMS provider
 */
class TwentyFourSevenSmsProvider implements SmsProviderInterface
{
    /**
     * Provider name
     *
     * @var string
     */
    protected $name = '24x7sms';

    /**
     * Provider configuration
     *
     * @var array
     */
    protected $config;

    /**
     * API URL
     *
     * @var string
     */
    protected $apiUrl = 'http://smsapi.24x7sms.com/api_1.0/SendSMS.aspx';

    /**
     * TwentyFourSevenSmsProvider constructor
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        
        if (!empty($config['api_url'])) {
            $this->apiUrl = $config['api_url'];
        }
    }

    /**
     * {@inheritdoc}
     */
    public function send(string $to, string $message, ?string $from = null, array $options = []): bool
    {
        try {
            $params = [
                'APIKEY' => $this->config['api_key'] ?? '',
                'MobileNo' => $to,
                'Message' => $message,
                'SenderID' => $from ?? $this->config['sender_id'] ?? '',
                'ServiceName' => $this->config['service_name'] ?? '',
                'Email' => $this->config['email'] ?? '',
                'Password' => $this->config['password'] ?? '',
                'ResponseFormat' => 'JSON',
            ];
            
            // Add additional parameters from options
            if (!empty($options['template_id'])) {
                $params['TemplateID'] = $options['template_id'];
            }
            
            $response = Http::get($this->apiUrl, $params);
            
            if ($response->successful()) {
                $result = $response->json();
                
                // Check for success in the response
                if (isset($result['ErrorCode']) && $result['ErrorCode'] === '000') {
                    Log::info('24x7 SMS sent successfully', [
                        'to' => $to,
                        'message_id' => $result['MessageID'] ?? null,
                    ]);
                    
                    return true;
                } else {
                    Log::error('24x7 SMS sending failed', [
                        'to' => $to,
                        'error_code' => $result['ErrorCode'] ?? null,
                        'error_message' => $result['ErrorMessage'] ?? null,
                    ]);
                    
                    return false;
                }
            } else {
                Log::error('24x7 SMS API request failed', [
                    'to' => $to,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                
                return false;
            }
        } catch (Exception $e) {
            Log::error('24x7 SMS sending exception', [
                'to' => $to,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function sendBulk(array $to, string $message, ?string $from = null, array $options = []): array
    {
        $results = [];
        
        foreach ($to as $recipient) {
            $results[$recipient] = $this->send($recipient, $message, $from, $options);
        }
        
        return $results;
    }

    /**
     * {@inheritdoc}
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * {@inheritdoc}
     */
    public function isAvailable(): bool
    {
        return !empty($this->config['api_key']) || 
               (!empty($this->config['email']) && !empty($this->config['password']) && !empty($this->config['service_name']));
    }
    
    /**
     * {@inheritdoc}
     */
    public function getPriority(): int
    {
        return 10; // Default priority for 24x7 SMS
    }
}

<?php

namespace App\Services\Sms\Providers;

/**
 * Interface for SMS service providers
 */
interface SmsProviderInterface
{
    /**
     * Send an SMS
     *
     * @param string $to Recipient phone number
     * @param string $message SMS message
     * @param string|null $from Sender ID or phone number (optional)
     * @param array $options Additional options
     * @return bool True if the SMS was sent successfully, false otherwise
     */
    public function send(string $to, string $message, ?string $from = null, array $options = []): bool;

    /**
     * Send bulk SMS
     *
     * @param array $to Recipient phone numbers
     * @param string $message SMS message
     * @param string|null $from Sender ID or phone number (optional)
     * @param array $options Additional options
     * @return array Array of results, keyed by phone number
     */
    public function sendBulk(array $to, string $message, ?string $from = null, array $options = []): array;

    /**
     * Get the provider name
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Check if the provider is available
     *
     * @return bool
     */
    public function isAvailable(): bool;
    
    /**
     * Get the provider priority
     * Lower number means higher priority
     *
     * @return int
     */
    public function getPriority(): int;
}

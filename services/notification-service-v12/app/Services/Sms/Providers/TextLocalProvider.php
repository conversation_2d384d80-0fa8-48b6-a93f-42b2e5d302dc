<?php

namespace App\Services\Sms\Providers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * TextLocal SMS provider
 */
class TextLocalProvider implements SmsProviderInterface
{
    /**
     * Provider name
     *
     * @var string
     */
    protected $name = 'textlocal';

    /**
     * Provider configuration
     *
     * @var array
     */
    protected $config;

    /**
     * API URL
     *
     * @var string
     */
    protected $apiUrl = 'https://api.textlocal.in/send/';

    /**
     * TextLocalProvider constructor
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        
        if (!empty($config['api_url'])) {
            $this->apiUrl = $config['api_url'];
        }
    }

    /**
     * {@inheritdoc}
     */
    public function send(string $to, string $message, ?string $from = null, array $options = []): bool
    {
        try {
            $params = [
                'apikey' => $this->config['auth_key'] ?? '',
                'numbers' => $to,
                'message' => $message,
                'sender' => $from ?? $this->config['sender_id'] ?? '',
                'format' => 'json',
            ];
            
            // Add additional parameters from options
            if (!empty($options['test'])) {
                $params['test'] = $options['test'];
            }
            
            if (!empty($options['receipt_url'])) {
                $params['receipt_url'] = $options['receipt_url'];
            }
            
            if (!empty($options['custom'])) {
                $params['custom'] = $options['custom'];
            }
            
            $response = Http::asForm()->post($this->apiUrl, $params);
            
            if ($response->successful()) {
                $result = $response->json();
                
                // Check for success in the response
                if (isset($result['status']) && $result['status'] === 'success') {
                    Log::info('TextLocal SMS sent successfully', [
                        'to' => $to,
                        'batch_id' => $result['batch_id'] ?? null,
                    ]);
                    
                    return true;
                } else {
                    Log::error('TextLocal SMS sending failed', [
                        'to' => $to,
                        'errors' => $result['errors'] ?? null,
                    ]);
                    
                    return false;
                }
            } else {
                Log::error('TextLocal API request failed', [
                    'to' => $to,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                
                return false;
            }
        } catch (Exception $e) {
            Log::error('TextLocal SMS sending exception', [
                'to' => $to,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function sendBulk(array $to, string $message, ?string $from = null, array $options = []): array
    {
        try {
            $params = [
                'apikey' => $this->config['auth_key'] ?? '',
                'numbers' => implode(',', $to),
                'message' => $message,
                'sender' => $from ?? $this->config['sender_id'] ?? '',
                'format' => 'json',
            ];
            
            // Add additional parameters from options
            if (!empty($options['test'])) {
                $params['test'] = $options['test'];
            }
            
            if (!empty($options['receipt_url'])) {
                $params['receipt_url'] = $options['receipt_url'];
            }
            
            if (!empty($options['custom'])) {
                $params['custom'] = $options['custom'];
            }
            
            $response = Http::asForm()->post($this->apiUrl, $params);
            
            if ($response->successful()) {
                $result = $response->json();
                
                // Check for success in the response
                if (isset($result['status']) && $result['status'] === 'success') {
                    Log::info('TextLocal bulk SMS sent successfully', [
                        'to' => $to,
                        'batch_id' => $result['batch_id'] ?? null,
                    ]);
                    
                    $results = [];
                    foreach ($to as $recipient) {
                        $results[$recipient] = true;
                    }
                    
                    return $results;
                } else {
                    Log::error('TextLocal bulk SMS sending failed', [
                        'to' => $to,
                        'errors' => $result['errors'] ?? null,
                    ]);
                    
                    $results = [];
                    foreach ($to as $recipient) {
                        $results[$recipient] = false;
                    }
                    
                    return $results;
                }
            } else {
                Log::error('TextLocal API request failed', [
                    'to' => $to,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                
                $results = [];
                foreach ($to as $recipient) {
                    $results[$recipient] = false;
                }
                
                return $results;
            }
        } catch (Exception $e) {
            Log::error('TextLocal bulk SMS sending exception', [
                'to' => $to,
                'error' => $e->getMessage(),
            ]);
            
            $results = [];
            foreach ($to as $recipient) {
                $results[$recipient] = false;
            }
            
            return $results;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * {@inheritdoc}
     */
    public function isAvailable(): bool
    {
        return !empty($this->config['auth_key']) && !empty($this->config['sender_id']);
    }
    
    /**
     * {@inheritdoc}
     */
    public function getPriority(): int
    {
        return 30; // Priority for TextLocal (lower than Plivo)
    }
}

<?php

namespace App\Services\Template;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateSet;
use App\Models\EmailTemplateVariable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Service for managing email templates
 */
class EmailTemplateService
{
    /**
     * Get all email template sets
     *
     * @param bool $activeOnly Whether to return only active sets
     * @return Collection
     */
    public function getAllSets(bool $activeOnly = true): Collection
    {
        $query = EmailTemplateSet::query();
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('name')->get();
    }
    
    /**
     * Get an email template set by ID
     *
     * @param int $id The set ID
     * @return EmailTemplateSet|null
     */
    public function getSetById(int $id): ?EmailTemplateSet
    {
        return EmailTemplateSet::find($id);
    }
    
    /**
     * Create a new email template set
     *
     * @param array $data The set data
     * @return EmailTemplateSet
     */
    public function createSet(array $data): EmailTemplateSet
    {
        return EmailTemplateSet::create($data);
    }
    
    /**
     * Update an email template set
     *
     * @param int $id The set ID
     * @param array $data The set data
     * @return EmailTemplateSet|null
     */
    public function updateSet(int $id, array $data): ?EmailTemplateSet
    {
        $set = EmailTemplateSet::find($id);
        
        if (!$set) {
            return null;
        }
        
        $set->update($data);
        
        return $set;
    }
    
    /**
     * Delete an email template set
     *
     * @param int $id The set ID
     * @return bool
     */
    public function deleteSet(int $id): bool
    {
        $set = EmailTemplateSet::find($id);
        
        if (!$set) {
            return false;
        }
        
        return $set->delete();
    }
    
    /**
     * Get all email templates for a set
     *
     * @param int $setId The set ID
     * @param bool $activeOnly Whether to return only active templates
     * @return Collection
     */
    public function getTemplatesBySetId(int $setId, bool $activeOnly = true): Collection
    {
        $query = EmailTemplate::where('fk_set_id', $setId);
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('name')->get();
    }
    
    /**
     * Get an email template by ID
     *
     * @param int $id The template ID
     * @return EmailTemplate|null
     */
    public function getTemplateById(int $id): ?EmailTemplate
    {
        return EmailTemplate::find($id);
    }
    
    /**
     * Get an email template by name
     *
     * @param string $name The template name
     * @param int|null $setId The set ID (optional)
     * @return EmailTemplate|null
     */
    public function getTemplateByName(string $name, ?int $setId = null): ?EmailTemplate
    {
        $query = EmailTemplate::where('name', $name)
            ->where('is_active', true);
        
        if ($setId) {
            $query->where('fk_set_id', $setId);
        }
        
        return $query->first();
    }
    
    /**
     * Create a new email template
     *
     * @param array $data The template data
     * @return EmailTemplate
     */
    public function createTemplate(array $data): EmailTemplate
    {
        return EmailTemplate::create($data);
    }
    
    /**
     * Update an email template
     *
     * @param int $id The template ID
     * @param array $data The template data
     * @return EmailTemplate|null
     */
    public function updateTemplate(int $id, array $data): ?EmailTemplate
    {
        $template = EmailTemplate::find($id);
        
        if (!$template) {
            return null;
        }
        
        $template->update($data);
        
        return $template;
    }
    
    /**
     * Delete an email template
     *
     * @param int $id The template ID
     * @return bool
     */
    public function deleteTemplate(int $id): bool
    {
        $template = EmailTemplate::find($id);
        
        if (!$template) {
            return false;
        }
        
        return $template->delete();
    }
    
    /**
     * Get all email template variables
     *
     * @param string $module The module (email or sms)
     * @param string|null $type The variable type (basic or other)
     * @param bool $activeOnly Whether to return only active variables
     * @return Collection
     */
    public function getAllVariables(string $module = 'email', ?string $type = null, bool $activeOnly = true): Collection
    {
        $query = EmailTemplateVariable::where('module', $module);
        
        if ($type) {
            $query->where('type', $type);
        }
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('variable')->get();
    }
    
    /**
     * Get an email template variable by ID
     *
     * @param int $id The variable ID
     * @return EmailTemplateVariable|null
     */
    public function getVariableById(int $id): ?EmailTemplateVariable
    {
        return EmailTemplateVariable::find($id);
    }
    
    /**
     * Create a new email template variable
     *
     * @param array $data The variable data
     * @return EmailTemplateVariable
     */
    public function createVariable(array $data): EmailTemplateVariable
    {
        return EmailTemplateVariable::create($data);
    }
    
    /**
     * Update an email template variable
     *
     * @param int $id The variable ID
     * @param array $data The variable data
     * @return EmailTemplateVariable|null
     */
    public function updateVariable(int $id, array $data): ?EmailTemplateVariable
    {
        $variable = EmailTemplateVariable::find($id);
        
        if (!$variable) {
            return null;
        }
        
        $variable->update($data);
        
        return $variable;
    }
    
    /**
     * Delete an email template variable
     *
     * @param int $id The variable ID
     * @return bool
     */
    public function deleteVariable(int $id): bool
    {
        $variable = EmailTemplateVariable::find($id);
        
        if (!$variable) {
            return false;
        }
        
        return $variable->delete();
    }
    
    /**
     * Render an email template with the given data
     *
     * @param int|string $templateIdOrName The template ID or name
     * @param array $data The data to use for variable replacement
     * @param int|null $setId The set ID (optional, used when templateIdOrName is a name)
     * @return array|null The rendered template (subject and body) or null if the template is not found
     */
    public function renderTemplate($templateIdOrName, array $data, ?int $setId = null): ?array
    {
        $template = is_numeric($templateIdOrName)
            ? $this->getTemplateById($templateIdOrName)
            : $this->getTemplateByName($templateIdOrName, $setId);
        
        if (!$template) {
            Log::error('Email template not found', [
                'templateIdOrName' => $templateIdOrName,
                'setId' => $setId,
            ]);
            return null;
        }
        
        return [
            'subject' => $template->renderSubject($data),
            'body' => $template->renderBody($data),
            'type' => $template->type,
        ];
    }
    
    /**
     * Preview an email template with the given data
     *
     * @param int $id The template ID
     * @param array $data The data to use for variable replacement
     * @return array|null The rendered template (subject and body) or null if the template is not found
     */
    public function previewTemplate(int $id, array $data): ?array
    {
        return $this->renderTemplate($id, $data);
    }
    
    /**
     * Validate template data
     *
     * @param array $data The template data
     * @param bool $isUpdate Whether this is an update operation
     * @return array The validation errors (empty if validation passes)
     */
    public function validateTemplateData(array $data, bool $isUpdate = false): array
    {
        $rules = [
            'fk_set_id' => 'required|exists:email_template_sets,pk_set_id',
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
            'type' => 'required|string|in:html,text',
            'purpose' => 'nullable|string|max:255',
            'template_variable_id' => 'nullable|string',
            'is_active' => 'boolean',
        ];
        
        if ($isUpdate) {
            $rules['pk_template_id'] = 'required|exists:email_templates,pk_template_id';
        }
        
        $validator = Validator::make($data, $rules);
        
        return $validator->fails() ? $validator->errors()->toArray() : [];
    }
}

<?php

namespace App\Services\Template;

use App\Models\SmsSet;
use App\Models\SmsTemplate;
use App\Models\EmailTemplateVariable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Service for managing SMS templates
 */
class SmsTemplateService
{
    /**
     * Get all SMS sets
     *
     * @param bool $activeOnly Whether to return only active sets
     * @return Collection
     */
    public function getAllSets(bool $activeOnly = true): Collection
    {
        $query = SmsSet::query();
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('name')->get();
    }
    
    /**
     * Get an SMS set by ID
     *
     * @param int $id The set ID
     * @return SmsSet|null
     */
    public function getSetById(int $id): ?SmsSet
    {
        return SmsSet::find($id);
    }
    
    /**
     * Create a new SMS set
     *
     * @param array $data The set data
     * @return SmsSet
     */
    public function createSet(array $data): SmsSet
    {
        return SmsSet::create($data);
    }
    
    /**
     * Update an SMS set
     *
     * @param int $id The set ID
     * @param array $data The set data
     * @return SmsSet|null
     */
    public function updateSet(int $id, array $data): ?SmsSet
    {
        $set = SmsSet::find($id);
        
        if (!$set) {
            return null;
        }
        
        $set->update($data);
        
        return $set;
    }
    
    /**
     * Delete an SMS set
     *
     * @param int $id The set ID
     * @return bool
     */
    public function deleteSet(int $id): bool
    {
        $set = SmsSet::find($id);
        
        if (!$set) {
            return false;
        }
        
        return $set->delete();
    }
    
    /**
     * Get all SMS templates for a set
     *
     * @param int $setId The set ID
     * @param bool $activeOnly Whether to return only active templates
     * @param bool $approvedOnly Whether to return only approved templates
     * @return Collection
     */
    public function getTemplatesBySetId(int $setId, bool $activeOnly = true, bool $approvedOnly = false): Collection
    {
        $query = SmsTemplate::where('fk_set_id', $setId);
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        if ($approvedOnly) {
            $query->where('is_approved', true);
        }
        
        return $query->orderBy('name')->get();
    }
    
    /**
     * Get an SMS template by ID
     *
     * @param int $id The template ID
     * @return SmsTemplate|null
     */
    public function getTemplateById(int $id): ?SmsTemplate
    {
        return SmsTemplate::find($id);
    }
    
    /**
     * Get an SMS template by name
     *
     * @param string $name The template name
     * @param int|null $setId The set ID (optional)
     * @param bool $approvedOnly Whether to return only approved templates
     * @return SmsTemplate|null
     */
    public function getTemplateByName(string $name, ?int $setId = null, bool $approvedOnly = true): ?SmsTemplate
    {
        $query = SmsTemplate::where('name', $name)
            ->where('is_active', true);
        
        if ($setId) {
            $query->where('fk_set_id', $setId);
        }
        
        if ($approvedOnly) {
            $query->where('is_approved', true);
        }
        
        return $query->first();
    }
    
    /**
     * Create a new SMS template
     *
     * @param array $data The template data
     * @return SmsTemplate
     */
    public function createTemplate(array $data): SmsTemplate
    {
        return SmsTemplate::create($data);
    }
    
    /**
     * Update an SMS template
     *
     * @param int $id The template ID
     * @param array $data The template data
     * @return SmsTemplate|null
     */
    public function updateTemplate(int $id, array $data): ?SmsTemplate
    {
        $template = SmsTemplate::find($id);
        
        if (!$template) {
            return null;
        }
        
        $template->update($data);
        
        return $template;
    }
    
    /**
     * Delete an SMS template
     *
     * @param int $id The template ID
     * @return bool
     */
    public function deleteTemplate(int $id): bool
    {
        $template = SmsTemplate::find($id);
        
        if (!$template) {
            return false;
        }
        
        return $template->delete();
    }
    
    /**
     * Approve an SMS template
     *
     * @param int $id The template ID
     * @return SmsTemplate|null
     */
    public function approveTemplate(int $id): ?SmsTemplate
    {
        $template = SmsTemplate::find($id);
        
        if (!$template) {
            return null;
        }
        
        $template->update(['is_approved' => true]);
        
        return $template;
    }
    
    /**
     * Get all SMS template variables
     *
     * @param string|null $type The variable type (basic or other)
     * @param bool $activeOnly Whether to return only active variables
     * @return Collection
     */
    public function getAllVariables(?string $type = null, bool $activeOnly = true): Collection
    {
        $query = EmailTemplateVariable::where('module', 'sms');
        
        if ($type) {
            $query->where('type', $type);
        }
        
        if ($activeOnly) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('variable')->get();
    }
    
    /**
     * Render an SMS template with the given data
     *
     * @param int|string $templateIdOrName The template ID or name
     * @param array $data The data to use for variable replacement
     * @param int|null $setId The set ID (optional, used when templateIdOrName is a name)
     * @return string|null The rendered template content or null if the template is not found
     */
    public function renderTemplate($templateIdOrName, array $data, ?int $setId = null): ?string
    {
        $template = is_numeric($templateIdOrName)
            ? $this->getTemplateById($templateIdOrName)
            : $this->getTemplateByName($templateIdOrName, $setId);
        
        if (!$template) {
            Log::error('SMS template not found', [
                'templateIdOrName' => $templateIdOrName,
                'setId' => $setId,
            ]);
            return null;
        }
        
        return $template->renderContent($data);
    }
    
    /**
     * Preview an SMS template with the given data
     *
     * @param int $id The template ID
     * @param array $data The data to use for variable replacement
     * @return array|null The rendered template with character count info or null if the template is not found
     */
    public function previewTemplate(int $id, array $data): ?array
    {
        $template = $this->getTemplateById($id);
        
        if (!$template) {
            return null;
        }
        
        $renderedContent = $template->renderContent($data);
        
        return [
            'content' => $renderedContent,
            'character_count' => strlen($renderedContent),
            'character_limit' => $template->set ? $template->set->character_limit : 160,
            'is_within_limit' => strlen($renderedContent) <= ($template->set ? $template->set->character_limit : 160),
            'remaining_characters' => max(0, ($template->set ? $template->set->character_limit : 160) - strlen($renderedContent)),
        ];
    }
    
    /**
     * Validate template data
     *
     * @param array $data The template data
     * @param bool $isUpdate Whether this is an update operation
     * @return array The validation errors (empty if validation passes)
     */
    public function validateTemplateData(array $data, bool $isUpdate = false): array
    {
        $rules = [
            'fk_set_id' => 'required|exists:sms_sets,pk_set_id',
            'name' => 'required|string|max:255',
            'sms_content' => 'required|string',
            'purpose' => 'nullable|string|max:255',
            'is_approved' => 'boolean',
            'is_active' => 'boolean',
        ];
        
        if ($isUpdate) {
            $rules['sms_template_id'] = 'required|exists:sms_templates,sms_template_id';
        }
        
        $validator = Validator::make($data, $rules);
        
        if ($validator->fails()) {
            return $validator->errors()->toArray();
        }
        
        // Check character limit
        if (isset($data['fk_set_id']) && isset($data['sms_content'])) {
            $set = SmsSet::find($data['fk_set_id']);
            
            if ($set && strlen($data['sms_content']) > $set->character_limit) {
                return [
                    'sms_content' => [
                        "The SMS content exceeds the character limit of {$set->character_limit} characters.",
                    ],
                ];
            }
        }
        
        return [];
    }
}

<?php

namespace App\Services\Email;

use App\Services\Email\Providers\EmailProviderInterface;
use App\Services\Template\EmailTemplateService;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Provider-based email service
 */
class ProviderEmailService
{
    /**
     * Email providers
     *
     * @var array
     */
    protected $providers = [];

    /**
     * Email template service
     *
     * @var EmailTemplateService|null
     */
    protected $templateService;

    /**
     * ProviderEmailService constructor
     *
     * @param array $providers
     * @param EmailTemplateService|null $templateService
     */
    public function __construct(array $providers = [], ?EmailTemplateService $templateService = null)
    {
        $this->providers = $providers;
        $this->templateService = $templateService;
        
        // Sort providers by priority
        usort($this->providers, function (EmailProviderInterface $a, EmailProviderInterface $b) {
            return $a->getPriority() - $b->getPriority();
        });
    }

    /**
     * Add a provider
     *
     * @param EmailProviderInterface $provider
     * @return self
     */
    public function addProvider(EmailProviderInterface $provider): self
    {
        $this->providers[] = $provider;
        
        // Sort providers by priority
        usort($this->providers, function (EmailProviderInterface $a, EmailProviderInterface $b) {
            return $a->getPriority() - $b->getPriority();
        });
        
        return $this;
    }

    /**
     * Get all providers
     *
     * @return array
     */
    public function getProviders(): array
    {
        return $this->providers;
    }

    /**
     * Get available providers
     *
     * @return array
     */
    public function getAvailableProviders(): array
    {
        return array_filter($this->providers, function (EmailProviderInterface $provider) {
            return $provider->isAvailable();
        });
    }

    /**
     * Send an email
     *
     * @param array $from From email address and name
     * @param array $to To email address(es) and name(s)
     * @param array $cc CC email address(es) and name(s)
     * @param array $bcc BCC email address(es) and name(s)
     * @param string $subject Email subject
     * @param string $body Email body
     * @param string $contentType Content type (html or text)
     * @param array $attachments Attachments
     * @param array $headers Additional headers
     * @return bool True if the email was sent successfully, false otherwise
     */
    public function send(
        array $from,
        array $to,
        array $cc = [],
        array $bcc = [],
        string $subject,
        string $body,
        string $contentType = 'html',
        array $attachments = [],
        array $headers = []
    ): bool {
        $availableProviders = $this->getAvailableProviders();
        
        if (empty($availableProviders)) {
            Log::error('No available email providers');
            return false;
        }
        
        $exceptions = [];
        
        foreach ($availableProviders as $provider) {
            try {
                $result = $provider->send(
                    $from,
                    $to,
                    $cc,
                    $bcc,
                    $subject,
                    $body,
                    $contentType,
                    $attachments,
                    $headers
                );
                
                if ($result) {
                    Log::info('Email sent successfully', [
                        'provider' => $provider->getName(),
                        'to' => $to,
                        'subject' => $subject,
                    ]);
                    
                    return true;
                }
            } catch (Exception $e) {
                $exceptions[$provider->getName()] = $e->getMessage();
                
                Log::warning('Email provider failed', [
                    'provider' => $provider->getName(),
                    'error' => $e->getMessage(),
                    'to' => $to,
                    'subject' => $subject,
                ]);
                
                // Continue with the next provider
                continue;
            }
        }
        
        Log::error('All email providers failed', [
            'exceptions' => $exceptions,
            'to' => $to,
            'subject' => $subject,
        ]);
        
        return false;
    }

    /**
     * Send an email using a template
     *
     * @param array $from From email address and name
     * @param array $to To email address(es) and name(s)
     * @param array $cc CC email address(es) and name(s)
     * @param array $bcc BCC email address(es) and name(s)
     * @param int|string $templateIdOrName Template ID or name
     * @param array $data Template data
     * @param int|null $setId Template set ID (optional, used when templateIdOrName is a name)
     * @param array $attachments Attachments
     * @param array $headers Additional headers
     * @return bool True if the email was sent successfully, false otherwise
     */
    public function sendTemplate(
        array $from,
        array $to,
        array $cc = [],
        array $bcc = [],
        $templateIdOrName,
        array $data = [],
        ?int $setId = null,
        array $attachments = [],
        array $headers = []
    ): bool {
        if (!$this->templateService) {
            Log::error('Email template service not available');
            return false;
        }
        
        $template = $this->templateService->renderTemplate($templateIdOrName, $data, $setId);
        
        if (!$template) {
            Log::error('Email template not found', [
                'templateIdOrName' => $templateIdOrName,
                'setId' => $setId,
            ]);
            
            return false;
        }
        
        return $this->send(
            $from,
            $to,
            $cc,
            $bcc,
            $template['subject'],
            $template['body'],
            $template['type'],
            $attachments,
            $headers
        );
    }
}

<?php

namespace App\Services\Email\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;
use Exception;

/**
 * Postmark email provider
 */
class PostmarkProvider implements EmailProviderInterface
{
    /**
     * Provider name
     *
     * @var string
     */
    protected $name = 'postmark';

    /**
     * Provider configuration
     *
     * @var array
     */
    protected $config;

    /**
     * PostmarkProvider constructor
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * {@inheritdoc}
     */
    public function send(
        array $from,
        array $to,
        array $cc = [],
        array $bcc = [],
        string $subject,
        string $body,
        string $contentType = 'html',
        array $attachments = [],
        array $headers = []
    ): bool {
        try {
            $fromEmail = key($from);
            $fromName = $from[$fromEmail];

            Mail::mailer('postmark')->send([], [], function (Message $message) use ($fromEmail, $fromName, $to, $cc, $bcc, $subject, $body, $contentType, $attachments, $headers) {
                // Set from
                $message->from($fromEmail, $fromName);

                // Set to
                foreach ($to as $email => $name) {
                    $message->to($email, $name);
                }

                // Set cc
                foreach ($cc as $email => $name) {
                    $message->cc($email, $name);
                }

                // Set bcc
                foreach ($bcc as $email => $name) {
                    $message->bcc($email, $name);
                }

                // Set subject
                $message->subject($subject);

                // Set body
                if ($contentType === 'html') {
                    $message->html($body);
                } else {
                    $message->text($body);
                }

                // Add attachments
                foreach ($attachments as $attachment) {
                    if (isset($attachment['path'])) {
                        if (isset($attachment['name'])) {
                            $message->attach($attachment['path'], [
                                'as' => $attachment['name'],
                                'mime' => $attachment['mime'] ?? null,
                            ]);
                        } else {
                            $message->attach($attachment['path']);
                        }
                    } elseif (isset($attachment['data'])) {
                        $message->attachData($attachment['data'], $attachment['name'], [
                            'mime' => $attachment['mime'] ?? null,
                        ]);
                    }
                }

                // Set message stream if configured
                if (!empty($this->config['message_stream_id'])) {
                    $message->getHeaders()->addTextHeader('X-PM-Message-Stream', $this->config['message_stream_id']);
                }

                // Add custom headers
                foreach ($headers as $name => $value) {
                    $message->getHeaders()->addTextHeader($name, $value);
                }
            });

            return true;
        } catch (Exception $e) {
            Log::error('Postmark email sending failed', [
                'error' => $e->getMessage(),
                'to' => $to,
                'subject' => $subject,
            ]);

            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * {@inheritdoc}
     */
    public function isAvailable(): bool
    {
        return !empty($this->config['token']);
    }

    /**
     * {@inheritdoc}
     */
    public function getPriority(): int
    {
        return 30; // Priority for Postmark (lower than SES)
    }
}

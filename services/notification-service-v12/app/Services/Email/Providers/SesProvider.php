<?php

namespace App\Services\Email\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;
use Exception;

/**
 * Amazon SES email provider
 */
class SesProvider implements EmailProviderInterface
{
    /**
     * Provider name
     *
     * @var string
     */
    protected $name = 'ses';

    /**
     * Provider configuration
     *
     * @var array
     */
    protected $config;

    /**
     * SesProvider constructor
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * {@inheritdoc}
     */
    public function send(
        array $from,
        array $to,
        array $cc = [],
        array $bcc = [],
        string $subject,
        string $body,
        string $contentType = 'html',
        array $attachments = [],
        array $headers = []
    ): bool {
        try {
            $fromEmail = key($from);
            $fromName = $from[$fromEmail];

            Mail::mailer('ses')->send([], [], function (Message $message) use ($fromEmail, $fromName, $to, $cc, $bcc, $subject, $body, $contentType, $attachments, $headers) {
                // Set from
                $message->from($fromEmail, $fromName);

                // Set to
                foreach ($to as $email => $name) {
                    $message->to($email, $name);
                }

                // Set cc
                foreach ($cc as $email => $name) {
                    $message->cc($email, $name);
                }

                // Set bcc
                foreach ($bcc as $email => $name) {
                    $message->bcc($email, $name);
                }

                // Set subject
                $message->subject($subject);

                // Set body
                if ($contentType === 'html') {
                    $message->html($body);
                } else {
                    $message->text($body);
                }

                // Add attachments
                foreach ($attachments as $attachment) {
                    if (isset($attachment['path'])) {
                        if (isset($attachment['name'])) {
                            $message->attach($attachment['path'], [
                                'as' => $attachment['name'],
                                'mime' => $attachment['mime'] ?? null,
                            ]);
                        } else {
                            $message->attach($attachment['path']);
                        }
                    } elseif (isset($attachment['data'])) {
                        $message->attachData($attachment['data'], $attachment['name'], [
                            'mime' => $attachment['mime'] ?? null,
                        ]);
                    }
                }

                // Add custom headers
                foreach ($headers as $name => $value) {
                    $message->getHeaders()->addTextHeader($name, $value);
                }
            });

            return true;
        } catch (Exception $e) {
            Log::error('SES email sending failed', [
                'error' => $e->getMessage(),
                'to' => $to,
                'subject' => $subject,
            ]);

            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * {@inheritdoc}
     */
    public function isAvailable(): bool
    {
        return !empty($this->config['key']) && !empty($this->config['secret']) && !empty($this->config['region']);
    }

    /**
     * {@inheritdoc}
     */
    public function getPriority(): int
    {
        return 20; // Priority for SES (lower than SMTP)
    }
}

<?php

namespace App\Services\Email\Providers;

/**
 * Interface for email service providers
 */
interface EmailProviderInterface
{
    /**
     * Send an email
     *
     * @param array $from From email address and name
     * @param array $to To email address(es) and name(s)
     * @param array $cc CC email address(es) and name(s)
     * @param array $bcc BCC email address(es) and name(s)
     * @param string $subject Email subject
     * @param string $body Email body
     * @param string $contentType Content type (html or text)
     * @param array $attachments Attachments
     * @param array $headers Additional headers
     * @return bool True if the email was sent successfully, false otherwise
     */
    public function send(
        array $from,
        array $to,
        array $cc = [],
        array $bcc = [],
        string $subject,
        string $body,
        string $contentType = 'html',
        array $attachments = [],
        array $headers = []
    ): bool;

    /**
     * Get the provider name
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Check if the provider is available
     *
     * @return bool
     */
    public function isAvailable(): bool;

    /**
     * Get the provider priority
     * Lower number means higher priority
     *
     * @return int
     */
    public function getPriority(): int;
}

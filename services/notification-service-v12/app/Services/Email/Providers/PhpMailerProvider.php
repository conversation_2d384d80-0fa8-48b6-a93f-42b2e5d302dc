<?php

namespace App\Services\Email\Providers;

use Illuminate\Support\Facades\Log;
use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;

/**
 * PHPMailer email provider
 */
class PhpMailerProvider implements EmailProviderInterface
{
    /**
     * Provider name
     *
     * @var string
     */
    protected $name = 'phpmailer';

    /**
     * Provider configuration
     *
     * @var array
     */
    protected $config;

    /**
     * PhpMailerProvider constructor
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * {@inheritdoc}
     */
    public function send(
        array $from,
        array $to,
        array $cc = [],
        array $bcc = [],
        string $subject,
        string $body,
        string $contentType = 'html',
        array $attachments = [],
        array $headers = []
    ): bool {
        try {
            $mailer = new PHPMailer(true);
            
            // Set mailer type
            $mailerType = $this->config['mailer_type'] ?? 'smtp';
            
            switch ($mailerType) {
                case 'smtp':
                    $mailer->isSMTP();
                    $mailer->Host = $this->config['host'] ?? 'localhost';
                    $mailer->Port = $this->config['port'] ?? 25;
                    
                    if (!empty($this->config['username']) && !empty($this->config['password'])) {
                        $mailer->SMTPAuth = true;
                        $mailer->Username = $this->config['username'];
                        $mailer->Password = $this->config['password'];
                    }
                    
                    if (!empty($this->config['encryption'])) {
                        $mailer->SMTPSecure = $this->config['encryption'];
                    }
                    
                    if (isset($this->config['verify_peer'])) {
                        $mailer->SMTPOptions = [
                            'ssl' => [
                                'verify_peer' => (bool) $this->config['verify_peer'],
                                'verify_peer_name' => (bool) $this->config['verify_peer'],
                                'allow_self_signed' => !(bool) $this->config['verify_peer'],
                            ],
                        ];
                    }
                    break;
                    
                case 'sendmail':
                    $mailer->isSendmail();
                    
                    if (!empty($this->config['sendmail_path'])) {
                        $mailer->Sendmail = $this->config['sendmail_path'];
                    }
                    break;
                    
                case 'mail':
                default:
                    $mailer->isMail();
                    break;
            }
            
            // Set from
            $fromEmail = key($from);
            $fromName = $from[$fromEmail];
            $mailer->setFrom($fromEmail, $fromName);
            
            // Set to
            foreach ($to as $email => $name) {
                $mailer->addAddress($email, $name);
            }
            
            // Set cc
            foreach ($cc as $email => $name) {
                $mailer->addCC($email, $name);
            }
            
            // Set bcc
            foreach ($bcc as $email => $name) {
                $mailer->addBCC($email, $name);
            }
            
            // Set subject
            $mailer->Subject = $subject;
            
            // Set body
            if ($contentType === 'html') {
                $mailer->isHTML(true);
                $mailer->Body = $body;
                
                // Generate plain text version if not provided
                if (!isset($headers['text_body'])) {
                    $mailer->AltBody = strip_tags($body);
                } else {
                    $mailer->AltBody = $headers['text_body'];
                }
            } else {
                $mailer->isHTML(false);
                $mailer->Body = $body;
            }
            
            // Add attachments
            foreach ($attachments as $attachment) {
                if (isset($attachment['path'])) {
                    if (isset($attachment['name'])) {
                        $mailer->addAttachment($attachment['path'], $attachment['name'], 'base64', $attachment['mime'] ?? '');
                    } else {
                        $mailer->addAttachment($attachment['path']);
                    }
                } elseif (isset($attachment['data'])) {
                    $mailer->addStringAttachment($attachment['data'], $attachment['name'], 'base64', $attachment['mime'] ?? '');
                }
            }
            
            // Add custom headers
            foreach ($headers as $name => $value) {
                if ($name !== 'text_body') { // Skip text_body as it's handled separately
                    $mailer->addCustomHeader($name, $value);
                }
            }
            
            // Set additional options
            if (!empty($this->config['charset'])) {
                $mailer->CharSet = $this->config['charset'];
            }
            
            if (!empty($this->config['encoding'])) {
                $mailer->Encoding = $this->config['encoding'];
            }
            
            // Send the email
            $mailer->send();
            
            return true;
        } catch (Exception $e) {
            Log::error('PHPMailer email sending failed', [
                'error' => $e->getMessage(),
                'to' => $to,
                'subject' => $subject,
            ]);
            
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * {@inheritdoc}
     */
    public function isAvailable(): bool
    {
        $mailerType = $this->config['mailer_type'] ?? 'smtp';
        
        switch ($mailerType) {
            case 'smtp':
                return !empty($this->config['host']) && !empty($this->config['port']);
                
            case 'sendmail':
                return true;
                
            case 'mail':
            default:
                return true;
        }
    }
    
    /**
     * {@inheritdoc}
     */
    public function getPriority(): int
    {
        return 5; // Highest priority for PHPMailer (legacy compatibility)
    }
}

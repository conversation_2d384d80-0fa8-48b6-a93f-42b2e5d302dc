<?php

namespace App\Services\Email;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Jobs\SendEmailJob;
use App\Jobs\SendSmsJob;

/**
 * Email Service for handling email and SMS communications
 * 
 * This is a Laravel 12 implementation of the legacy Email class from Zend
 * It maintains backward compatibility while following modern Laravel practices
 */
class EmailService
{
    /**
     * Email Sending Priority - Very High
     * PRIORITY_SEND_IMMEDIATELY indicates the email should send immediately.
     */
    const PRIORITY_SEND_IMMEDIATELY = 1;

    /**
     * Email Sending Priority - High
     * PRIORITY_HIGH_STORE_IN_DATABASE indicates the email should save in database with high priority
     */
    const PRIORITY_HIGH_STORE_IN_DATABASE = 2;

    /**
     * Email Sending Priority - Medium
     * PRIORITY_MEDIUM_STORE_IN_DATABASE indicates the email should save in database with medium priority
     */
    const PRIORITY_MEDIUM_STORE_IN_DATABASE = 3;

    /**
     * Email Sending Priority - Low
     * PRIORITY_LOW_STORE_IN_DATABASE indicates the email should save in database with low priority
     */
    const PRIORITY_LOW_STORE_IN_DATABASE = 4;

    /**
     * SMS Sending Priority - High
     * PRIORITY_SMS_IMMEDIATELY indicates the sms should send immediately.
     */
    const PRIORITY_SMS_IMMEDIATELY = 1;

    /**
     * SMS Sending Priority - Low
     * PRIORITY_SMS_STORE_IN_DATABASE indicates the sms should save in database.
     */
    const PRIORITY_SMS_STORE_IN_DATABASE = 2;

    /**
     * Configuration settings
     */
    protected $configuration = [];

    /**
     * SMS configuration settings
     */
    protected $smsConfiguration = [];

    /**
     * Merchant data
     */
    protected $merchant = [];

    /**
     * Mobile number for SMS
     */
    protected $mobileNo = '';

    /**
     * SMS message content
     */
    protected $smsMessage = '';

    /**
     * Email priority
     */
    protected $priority = 4; // Default to low priority

    /**
     * Set configuration
     *
     * @param array $config
     * @return $this
     */
    public function setConfiguration(array $config)
    {
        $this->configuration = $config;
        return $this;
    }

    /**
     * Get configuration
     *
     * @return array
     */
    public function getConfiguration()
    {
        return $this->configuration;
    }

    /**
     * Set SMS configuration
     *
     * @param array $config
     * @return $this
     */
    public function setSMSConfiguration(array $config)
    {
        $this->smsConfiguration = $config;
        return $this;
    }

    /**
     * Get SMS configuration
     *
     * @return array
     */
    public function getSMSConfiguration()
    {
        return $this->smsConfiguration;
    }

    /**
     * Set merchant data
     *
     * @param array $merchant
     * @return $this
     */
    public function setMerchantData(array $merchant)
    {
        $this->merchant = $merchant;
        return $this;
    }

    /**
     * Get merchant data
     *
     * @return array
     */
    public function getMerchantData()
    {
        return $this->merchant;
    }

    /**
     * Set mobile number
     *
     * @param string $mobileNo
     * @return $this
     */
    public function setMobileNo($mobileNo)
    {
        $this->mobileNo = $mobileNo;
        return $this;
    }

    /**
     * Get mobile number
     *
     * @return string
     */
    public function getMobileNo()
    {
        return $this->mobileNo;
    }

    /**
     * Set SMS message
     *
     * @param string $message
     * @return $this
     * @throws \Exception
     */
    public function setSMSMessage($message)
    {
        if (empty($message)) {
            throw new \Exception('SMS Message Empty');
        }
        $this->smsMessage = $message;
        return $this;
    }

    /**
     * Get SMS message
     *
     * @return string
     */
    public function getSMSMessage()
    {
        return urlencode($this->smsMessage);
    }

    /**
     * Set email priority
     *
     * @param int $priority
     * @return $this
     */
    public function setPriority($priority)
    {
        $this->priority = !empty($priority) ? $priority : self::PRIORITY_LOW_STORE_IN_DATABASE;
        return $this;
    }

    /**
     * Get email priority
     *
     * @return int
     */
    public function getPriority()
    {
        return $this->priority;
    }

    /**
     * Send email
     *
     * @param array $from
     * @param array $to
     * @param array $cc
     * @param array $bcc
     * @param string $subject
     * @param string $body
     * @param string $charset
     * @param array $attachments
     * @param string $contentType
     * @param string $signature
     * @return bool
     */
    public function sendmail(
        array $from = [],
        array $to = [],
        array $cc = [],
        array $bcc = [],
        $subject = '',
        $body = '',
        $charset = 'UTF-8',
        array $attachments = [],
        $contentType = 'text/html',
        $signature = ''
    ) {
        // If no from address is provided, use the default from the configuration
        if (empty($from)) {
            $from = [
                config('mail.from.name') => config('mail.from.address')
            ];
        }

        $emailData = [
            'from' => $from,
            'to' => $to,
            'cc' => $cc,
            'bcc' => $bcc,
            'subject' => $subject,
            'body' => $body,
            'charset' => $charset,
            'attachments' => $attachments,
            'contentType' => $contentType,
            'signature' => $signature
        ];

        // Handle based on priority
        if ($this->priority === self::PRIORITY_SEND_IMMEDIATELY) {
            // Send immediately
            return $this->sendEmailNow($emailData);
        } else {
            // Store in database and queue for later processing
            $emailId = $this->storeEmailInDatabase($emailData);
            
            if ($emailId) {
                // Queue the email with the appropriate delay based on priority
                $delay = $this->getDelayFromPriority($this->priority);
                Queue::later($delay, new SendEmailJob($emailId));
                return true;
            }
            
            return false;
        }
    }

    /**
     * Send SMS message
     *
     * @param int $priority
     * @return bool
     */
    public function sendmessage($priority = self::PRIORITY_SMS_IMMEDIATELY)
    {
        if (empty($this->mobileNo) || empty($this->smsMessage)) {
            Log::error('SMS sending failed: Mobile number or message is empty');
            return false;
        }

        $smsData = [
            'mobile' => $this->mobileNo,
            'message' => $this->smsMessage,
            'configuration' => $this->smsConfiguration,
            'merchant' => $this->merchant
        ];

        if ($priority === self::PRIORITY_SMS_IMMEDIATELY) {
            // Send immediately
            return $this->sendSmsNow($smsData);
        } else {
            // Store in database and queue for later processing
            $smsId = $this->storeSmsInDatabase($smsData);
            
            if ($smsId) {
                Queue::push(new SendSmsJob($smsId));
                return true;
            }
            
            return false;
        }
    }

    /**
     * Send email immediately
     *
     * @param array $emailData
     * @return bool
     */
    protected function sendEmailNow(array $emailData)
    {
        try {
            $fromName = key($emailData['from']);
            $fromEmail = $emailData['from'][$fromName];
            
            Mail::send([], [], function ($message) use ($emailData, $fromName, $fromEmail) {
                $message->from($fromEmail, $fromName)
                    ->subject($emailData['subject']);
                
                // Add recipients
                foreach ($emailData['to'] as $name => $email) {
                    $message->to($email, is_string($name) ? $name : null);
                }
                
                // Add CC recipients
                foreach ($emailData['cc'] as $name => $email) {
                    $message->cc($email, is_string($name) ? $name : null);
                }
                
                // Add BCC recipients
                foreach ($emailData['bcc'] as $name => $email) {
                    $message->bcc($email, is_string($name) ? $name : null);
                }
                
                // Set content type and body
                $message->setBody($emailData['body'], $emailData['contentType']);
                
                // Add attachments
                foreach ($emailData['attachments'] as $attachment) {
                    if (isset($attachment['path'])) {
                        $message->attach($attachment['path'], [
                            'as' => $attachment['name'] ?? basename($attachment['path']),
                            'mime' => $attachment['mime'] ?? null
                        ]);
                    }
                }
            });
            
            Log::info('Email sent successfully', ['to' => $emailData['to'], 'subject' => $emailData['subject']]);
            return true;
        } catch (\Exception $e) {
            Log::error('Email sending failed', [
                'error' => $e->getMessage(),
                'to' => $emailData['to'],
                'subject' => $emailData['subject']
            ]);
            return false;
        }
    }

    /**
     * Send SMS immediately
     *
     * @param array $smsData
     * @return bool
     */
    protected function sendSmsNow(array $smsData)
    {
        try {
            // Get SMS gateway URL from configuration
            $gatewayUrl = $smsData['configuration']['gateway_url'] ?? '';
            
            if (empty($gatewayUrl)) {
                Log::error('SMS sending failed: Gateway URL is empty');
                return false;
            }
            
            // Prepare parameters for the SMS gateway
            $params = [
                'mobile' => $smsData['mobile'],
                'message' => $smsData['message'],
                // Add other required parameters from configuration
                'sender' => $smsData['configuration']['sender_id'] ?? '',
                'authkey' => $smsData['configuration']['auth_key'] ?? '',
            ];
            
            // Make HTTP request to the SMS gateway
            $response = Http::get($gatewayUrl, $params);
            
            if ($response->successful()) {
                Log::info('SMS sent successfully', ['to' => $smsData['mobile']]);
                return true;
            } else {
                Log::error('SMS sending failed: Gateway returned error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('SMS sending failed', [
                'error' => $e->getMessage(),
                'to' => $smsData['mobile']
            ]);
            return false;
        }
    }

    /**
     * Store email in database for later processing
     *
     * @param array $emailData
     * @return int|bool
     */
    protected function storeEmailInDatabase(array $emailData)
    {
        try {
            $emailId = DB::table('email_queue')->insertGetId([
                'from_name' => key($emailData['from']),
                'from_email' => $emailData['from'][key($emailData['from'])],
                'to_email' => json_encode($emailData['to']),
                'cc_email' => json_encode($emailData['cc']),
                'bcc_email' => json_encode($emailData['bcc']),
                'subject' => $emailData['subject'],
                'body' => $emailData['body'],
                'attachments' => json_encode($emailData['attachments']),
                'content_type' => $emailData['contentType'],
                'priority' => $this->priority,
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            return $emailId;
        } catch (\Exception $e) {
            Log::error('Failed to store email in database', [
                'error' => $e->getMessage(),
                'subject' => $emailData['subject']
            ]);
            return false;
        }
    }

    /**
     * Store SMS in database for later processing
     *
     * @param array $smsData
     * @return int|bool
     */
    protected function storeSmsInDatabase(array $smsData)
    {
        try {
            $smsId = DB::table('sms_queue')->insertGetId([
                'mobile' => $smsData['mobile'],
                'message' => $smsData['message'],
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            return $smsId;
        } catch (\Exception $e) {
            Log::error('Failed to store SMS in database', [
                'error' => $e->getMessage(),
                'mobile' => $smsData['mobile']
            ]);
            return false;
        }
    }

    /**
     * Get delay time based on priority
     *
     * @param int $priority
     * @return int
     */
    protected function getDelayFromPriority($priority)
    {
        switch ($priority) {
            case self::PRIORITY_HIGH_STORE_IN_DATABASE:
                return 5 * 60; // 5 minutes
            case self::PRIORITY_MEDIUM_STORE_IN_DATABASE:
                return 30 * 60; // 30 minutes
            case self::PRIORITY_LOW_STORE_IN_DATABASE:
                return 60 * 60; // 1 hour
            default:
                return 60 * 60; // Default to 1 hour
        }
    }
}

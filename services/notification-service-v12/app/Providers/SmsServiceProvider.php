<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Sms\Providers\TwentyFourSevenSmsProvider;
use App\Services\Sms\Providers\PlivoProvider;
use App\Services\Sms\Providers\TextLocalProvider;
use App\Services\Sms\SmsService;
use App\Services\Template\SmsTemplateService;

class SmsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register the SMS providers
        $this->app->singleton(TwentyFourSevenSmsProvider::class, function ($app) {
            return new TwentyFourSevenSmsProvider(config('sms.24x7sms'));
        });
        
        $this->app->singleton(PlivoProvider::class, function ($app) {
            return new PlivoProvider(config('sms.plivo'));
        });
        
        $this->app->singleton(TextLocalProvider::class, function ($app) {
            return new TextLocalProvider(config('sms.textlocal'));
        });
        
        // Register the SMS service
        $this->app->singleton(SmsService::class, function ($app) {
            $providers = [
                $app->make(TwentyFourSevenSmsProvider::class),
                $app->make(PlivoProvider::class),
                $app->make(TextLocalProvider::class),
            ];
            
            return new SmsService(
                $providers,
                $app->make(SmsTemplateService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        //
    }
}

<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Email\Providers\PhpMailerProvider;
use App\Services\Email\Providers\SmtpProvider;
use App\Services\Email\Providers\SesProvider;
use App\Services\Email\Providers\PostmarkProvider;
use App\Services\Email\Providers\ResendProvider;
use App\Services\Email\ProviderEmailService;
use App\Services\Template\EmailTemplateService;

class EmailServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register the email providers
        $this->app->singleton(PhpMailerProvider::class, function ($app) {
            return new PhpMailerProvider(config('mail.phpmailer'));
        });
        
        $this->app->singleton(SmtpProvider::class, function ($app) {
            return new SmtpProvider(config('mail.smtp'));
        });
        
        $this->app->singleton(SesProvider::class, function ($app) {
            return new SesProvider(config('mail.ses'));
        });
        
        $this->app->singleton(PostmarkProvider::class, function ($app) {
            return new PostmarkProvider(config('mail.postmark'));
        });
        
        $this->app->singleton(ResendProvider::class, function ($app) {
            return new ResendProvider(config('mail.resend'));
        });
        
        // Register the provider-based email service
        $this->app->singleton(ProviderEmailService::class, function ($app) {
            $providers = [
                $app->make(PhpMailerProvider::class),
                $app->make(SmtpProvider::class),
                $app->make(SesProvider::class),
                $app->make(PostmarkProvider::class),
                $app->make(ResendProvider::class),
            ];
            
            return new ProviderEmailService(
                $providers,
                $app->make(EmailTemplateService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        //
    }
}

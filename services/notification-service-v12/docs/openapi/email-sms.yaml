openapi: 3.1.0
info:
  title: Notification Service - Email and SMS API
  description: API for sending emails and SMS messages
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
servers:
  - url: https://api.example.com/api/v2
    description: Production server
  - url: https://staging-api.example.com/api/v2
    description: Staging server
  - url: http://localhost:8000/api/v2
    description: Local development server
paths:
  /notification/email-v2/send:
    post:
      summary: Send an email
      description: Sends an email using the configured email providers
      operationId: sendEmail
      tags:
        - Email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendEmailRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email sent successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-v2/send-template:
    post:
      summary: Send an email using a template
      description: Sends an email using a template and the configured email providers
      operationId: sendTemplateEmail
      tags:
        - Email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendTemplateEmailRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email sent successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/sms-v2/send:
    post:
      summary: Send an SMS
      description: Sends an SMS using the configured SMS providers
      operationId: sendSms
      tags:
        - SMS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendSmsRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: SMS sent successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/sms-v2/send-bulk:
    post:
      summary: Send bulk SMS
      description: Sends bulk SMS using the configured SMS providers
      operationId: sendBulkSms
      tags:
        - SMS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendBulkSmsRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: SMS sent to 5 recipients, failed for 0 recipients
                  results:
                    type: object
                    additionalProperties:
                      type: boolean
                    example:
                      '+**********': true
                      '+**********': true
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/sms-v2/send-template:
    post:
      summary: Send an SMS using a template
      description: Sends an SMS using a template and the configured SMS providers
      operationId: sendTemplateSms
      tags:
        - SMS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendTemplateSmsRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: SMS sent successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/sms-v2/send-bulk-template:
    post:
      summary: Send bulk SMS using a template
      description: Sends bulk SMS using a template and the configured SMS providers
      operationId: sendBulkTemplateSms
      tags:
        - SMS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendBulkTemplateSmsRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: SMS sent to 5 recipients, failed for 0 recipients
                  results:
                    type: object
                    additionalProperties:
                      type: boolean
                    example:
                      '+**********': true
                      '+**********': true
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
components:
  schemas:
    SendEmailRequest:
      type: object
      required:
        - from_email
        - to
        - subject
        - body
      properties:
        from_email:
          type: string
          format: email
          example: <EMAIL>
        from_name:
          type: string
          example: Sender Name
        to:
          type: array
          items:
            type: object
            required:
              - email
            properties:
              email:
                type: string
                format: email
                example: <EMAIL>
              name:
                type: string
                example: Recipient Name
        cc:
          type: array
          items:
            type: object
            required:
              - email
            properties:
              email:
                type: string
                format: email
                example: <EMAIL>
              name:
                type: string
                example: CC Recipient Name
        bcc:
          type: array
          items:
            type: object
            required:
              - email
            properties:
              email:
                type: string
                format: email
                example: <EMAIL>
              name:
                type: string
                example: BCC Recipient Name
        subject:
          type: string
          example: Email Subject
        body:
          type: string
          example: <p>This is the email body.</p>
        content_type:
          type: string
          enum: [html, text]
          default: html
          example: html
        attachments:
          type: array
          items:
            type: object
            properties:
              path:
                type: string
                example: /path/to/file.pdf
              data:
                type: string
                format: binary
                example: base64-encoded-data
              name:
                type: string
                example: file.pdf
              mime:
                type: string
                example: application/pdf
        headers:
          type: object
          additionalProperties:
            type: string
          example:
            X-Custom-Header: Custom Value
    SendTemplateEmailRequest:
      type: object
      required:
        - from_email
        - to
      properties:
        from_email:
          type: string
          format: email
          example: <EMAIL>
        from_name:
          type: string
          example: Sender Name
        to:
          type: array
          items:
            type: object
            required:
              - email
            properties:
              email:
                type: string
                format: email
                example: <EMAIL>
              name:
                type: string
                example: Recipient Name
        cc:
          type: array
          items:
            type: object
            required:
              - email
            properties:
              email:
                type: string
                format: email
                example: <EMAIL>
              name:
                type: string
                example: CC Recipient Name
        bcc:
          type: array
          items:
            type: object
            required:
              - email
            properties:
              email:
                type: string
                format: email
                example: <EMAIL>
              name:
                type: string
                example: BCC Recipient Name
        template_id:
          type: integer
          example: 1
        template_name:
          type: string
          example: welcome_email
        template_data:
          type: object
          additionalProperties:
            type: string
          example:
            name: John Doe
            order_id: 12345
        set_id:
          type: integer
          example: 1
        attachments:
          type: array
          items:
            type: object
            properties:
              path:
                type: string
                example: /path/to/file.pdf
              data:
                type: string
                format: binary
                example: base64-encoded-data
              name:
                type: string
                example: file.pdf
              mime:
                type: string
                example: application/pdf
        headers:
          type: object
          additionalProperties:
            type: string
          example:
            X-Custom-Header: Custom Value
    SendSmsRequest:
      type: object
      required:
        - to
        - message
      properties:
        to:
          type: string
          example: '+**********'
        message:
          type: string
          example: This is a test SMS message.
        from:
          type: string
          example: SENDER
        options:
          type: object
          properties:
            template_id:
              type: string
              example: template123
            url_callback:
              type: string
              format: uri
              example: https://example.com/callback
            method_callback:
              type: string
              enum: [GET, POST]
              example: POST
            log:
              type: boolean
              example: true
            test:
              type: boolean
              example: false
            receipt_url:
              type: string
              format: uri
              example: https://example.com/receipt
            custom:
              type: string
              example: custom-data
    SendBulkSmsRequest:
      type: object
      required:
        - to
        - message
      properties:
        to:
          type: array
          items:
            type: string
          example: ['+**********', '+**********']
        message:
          type: string
          example: This is a test SMS message.
        from:
          type: string
          example: SENDER
        options:
          type: object
          properties:
            template_id:
              type: string
              example: template123
            url_callback:
              type: string
              format: uri
              example: https://example.com/callback
            method_callback:
              type: string
              enum: [GET, POST]
              example: POST
            log:
              type: boolean
              example: true
            test:
              type: boolean
              example: false
            receipt_url:
              type: string
              format: uri
              example: https://example.com/receipt
            custom:
              type: string
              example: custom-data
    SendTemplateSmsRequest:
      type: object
      required:
        - to
      properties:
        to:
          type: string
          example: '+**********'
        template_id:
          type: integer
          example: 1
        template_name:
          type: string
          example: order_confirmation
        template_data:
          type: object
          additionalProperties:
            type: string
          example:
            name: John Doe
            order_id: 12345
        set_id:
          type: integer
          example: 1
        from:
          type: string
          example: SENDER
        options:
          type: object
          properties:
            url_callback:
              type: string
              format: uri
              example: https://example.com/callback
            method_callback:
              type: string
              enum: [GET, POST]
              example: POST
            log:
              type: boolean
              example: true
            test:
              type: boolean
              example: false
            receipt_url:
              type: string
              format: uri
              example: https://example.com/receipt
            custom:
              type: string
              example: custom-data
    SendBulkTemplateSmsRequest:
      type: object
      required:
        - to
      properties:
        to:
          type: array
          items:
            type: string
          example: ['+**********', '+**********']
        template_id:
          type: integer
          example: 1
        template_name:
          type: string
          example: order_confirmation
        template_data:
          type: object
          additionalProperties:
            type: string
          example:
            name: John Doe
            order_id: 12345
        set_id:
          type: integer
          example: 1
        from:
          type: string
          example: SENDER
        options:
          type: object
          properties:
            url_callback:
              type: string
              format: uri
              example: https://example.com/callback
            method_callback:
              type: string
              enum: [GET, POST]
              example: POST
            log:
              type: boolean
              example: true
            test:
              type: boolean
              example: false
            receipt_url:
              type: string
              format: uri
              example: https://example.com/receipt
            custom:
              type: string
              example: custom-data
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Bad request
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Unauthorized
    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: The given data was invalid
              errors:
                type: object
                additionalProperties:
                  type: array
                  items:
                    type: string
                example:
                  to: ["The to field is required."]
    ServerError:
      description: Server error
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Server error
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

openapi: 3.1.0
info:
  title: Notification Service - Template API
  description: API for managing email and SMS templates
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
servers:
  - url: https://api.example.com/api/v2
    description: Production server
  - url: https://staging-api.example.com/api/v2
    description: Staging server
  - url: http://localhost:8000/api/v2
    description: Local development server
paths:
  /notification/email-templates/sets:
    get:
      summary: Get all email template sets
      description: Returns a list of all email template sets
      operationId: getAllEmailTemplateSets
      tags:
        - Email Templates
      parameters:
        - name: active_only
          in: query
          description: Whether to return only active sets
          required: false
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/EmailTemplateSet'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    post:
      summary: Create a new email template set
      description: Creates a new email template set
      operationId: createEmailTemplateSet
      tags:
        - Email Templates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEmailTemplateSetRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template set created successfully
                  data:
                    $ref: '#/components/schemas/EmailTemplateSet'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-templates/sets/{id}:
    get:
      summary: Get an email template set by ID
      description: Returns an email template set by ID
      operationId: getEmailTemplateSetById
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template set
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/EmailTemplateSet'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    put:
      summary: Update an email template set
      description: Updates an email template set
      operationId: updateEmailTemplateSet
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template set
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmailTemplateSetRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template set updated successfully
                  data:
                    $ref: '#/components/schemas/EmailTemplateSet'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    delete:
      summary: Delete an email template set
      description: Deletes an email template set
      operationId: deleteEmailTemplateSet
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template set
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template set deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-templates/sets/{setId}/templates:
    get:
      summary: Get all email templates for a set
      description: Returns a list of all email templates for a set
      operationId: getEmailTemplatesBySetId
      tags:
        - Email Templates
      parameters:
        - name: setId
          in: path
          description: ID of the email template set
          required: true
          schema:
            type: integer
            format: int64
        - name: active_only
          in: query
          description: Whether to return only active templates
          required: false
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/EmailTemplate'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-templates/templates:
    post:
      summary: Create a new email template
      description: Creates a new email template
      operationId: createEmailTemplate
      tags:
        - Email Templates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEmailTemplateRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template created successfully
                  data:
                    $ref: '#/components/schemas/EmailTemplate'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-templates/templates/{id}:
    get:
      summary: Get an email template by ID
      description: Returns an email template by ID
      operationId: getEmailTemplateById
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/EmailTemplate'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    put:
      summary: Update an email template
      description: Updates an email template
      operationId: updateEmailTemplate
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmailTemplateRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template updated successfully
                  data:
                    $ref: '#/components/schemas/EmailTemplate'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    delete:
      summary: Delete an email template
      description: Deletes an email template
      operationId: deleteEmailTemplate
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-templates/templates/{id}/preview:
    post:
      summary: Preview an email template
      description: Previews an email template with the given data
      operationId: previewEmailTemplate
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewEmailTemplateRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/EmailTemplatePreview'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-templates/variables:
    get:
      summary: Get all email template variables
      description: Returns a list of all email template variables
      operationId: getAllEmailTemplateVariables
      tags:
        - Email Templates
      parameters:
        - name: module
          in: query
          description: The module (email or sms)
          required: false
          schema:
            type: string
            enum: [email, sms]
            default: email
        - name: type
          in: query
          description: The variable type (basic or advanced)
          required: false
          schema:
            type: string
            enum: [basic, advanced]
        - name: active_only
          in: query
          description: Whether to return only active variables
          required: false
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/EmailTemplateVariable'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    post:
      summary: Create a new email template variable
      description: Creates a new email template variable
      operationId: createEmailTemplateVariable
      tags:
        - Email Templates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEmailTemplateVariableRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template variable created successfully
                  data:
                    $ref: '#/components/schemas/EmailTemplateVariable'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/email-templates/variables/{id}:
    put:
      summary: Update an email template variable
      description: Updates an email template variable
      operationId: updateEmailTemplateVariable
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template variable
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmailTemplateVariableRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template variable updated successfully
                  data:
                    $ref: '#/components/schemas/EmailTemplateVariable'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    delete:
      summary: Delete an email template variable
      description: Deletes an email template variable
      operationId: deleteEmailTemplateVariable
      tags:
        - Email Templates
      parameters:
        - name: id
          in: path
          description: ID of the email template variable
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Email template variable deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
  /notification/sms-templates/sets:
    get:
      summary: Get all SMS sets
      description: Returns a list of all SMS sets
      operationId: getAllSmsSets
      tags:
        - SMS Templates
      parameters:
        - name: active_only
          in: query
          description: Whether to return only active sets
          required: false
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SmsSet'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
    post:
      summary: Create a new SMS set
      description: Creates a new SMS set
      operationId: createSmsSet
      tags:
        - SMS Templates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSmsSetRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: SMS set created successfully
                  data:
                    $ref: '#/components/schemas/SmsSet'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/ServerError'
      security:
        - bearerAuth: []
components:
  schemas:
    EmailTemplateSet:
      type: object
      properties:
        pk_set_id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Welcome Emails
        description:
          type: string
          example: Templates for welcoming new users
        is_active:
          type: boolean
          example: true
        created_by:
          type: integer
          format: int64
          example: 1
        modified_by:
          type: integer
          format: int64
          example: 1
        created_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
        updated_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
    CreateEmailTemplateSetRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: Welcome Emails
        description:
          type: string
          example: Templates for welcoming new users
        is_active:
          type: boolean
          example: true
    UpdateEmailTemplateSetRequest:
      type: object
      properties:
        name:
          type: string
          example: Welcome Emails
        description:
          type: string
          example: Templates for welcoming new users
        is_active:
          type: boolean
          example: true
    EmailTemplate:
      type: object
      properties:
        pk_template_id:
          type: integer
          format: int64
          example: 1
        fk_set_id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Welcome Email
        subject:
          type: string
          example: Welcome to our platform!
        body:
          type: string
          example: <p>Hello #name#,</p><p>Welcome to our platform!</p>
        type:
          type: string
          enum: [html, text]
          example: html
        purpose:
          type: string
          example: Welcoming new users
        template_variable_id:
          type: string
          example: 1,2,3
        is_active:
          type: boolean
          example: true
        created_by:
          type: integer
          format: int64
          example: 1
        modified_by:
          type: integer
          format: int64
          example: 1
        created_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
        updated_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
    CreateEmailTemplateRequest:
      type: object
      required:
        - fk_set_id
        - name
        - subject
        - body
        - type
      properties:
        fk_set_id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Welcome Email
        subject:
          type: string
          example: Welcome to our platform!
        body:
          type: string
          example: <p>Hello #name#,</p><p>Welcome to our platform!</p>
        type:
          type: string
          enum: [html, text]
          example: html
        purpose:
          type: string
          example: Welcoming new users
        template_variable_id:
          type: string
          example: 1,2,3
        is_active:
          type: boolean
          example: true
    UpdateEmailTemplateRequest:
      type: object
      properties:
        fk_set_id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Welcome Email
        subject:
          type: string
          example: Welcome to our platform!
        body:
          type: string
          example: <p>Hello #name#,</p><p>Welcome to our platform!</p>
        type:
          type: string
          enum: [html, text]
          example: html
        purpose:
          type: string
          example: Welcoming new users
        template_variable_id:
          type: string
          example: 1,2,3
        is_active:
          type: boolean
          example: true
    PreviewEmailTemplateRequest:
      type: object
      required:
        - data
      properties:
        data:
          type: object
          additionalProperties:
            type: string
          example:
            name: John Doe
            email: <EMAIL>
    EmailTemplatePreview:
      type: object
      properties:
        subject:
          type: string
          example: Welcome to our platform, John Doe!
        body:
          type: string
          example: <p>Hello John Doe,</p><p>Welcome to our platform!</p>
        type:
          type: string
          enum: [html, text]
          example: html
    EmailTemplateVariable:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        variable:
          type: string
          example: name
        content:
          type: string
          example: User's name
        type:
          type: string
          enum: [basic, advanced]
          example: basic
        module:
          type: string
          enum: [email, sms]
          example: email
        description:
          type: string
          example: The user's full name
        is_active:
          type: boolean
          example: true
        created_by:
          type: integer
          format: int64
          example: 1
        modified_by:
          type: integer
          format: int64
          example: 1
        created_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
        updated_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
    CreateEmailTemplateVariableRequest:
      type: object
      required:
        - variable
        - content
        - type
        - module
      properties:
        variable:
          type: string
          example: name
        content:
          type: string
          example: User's name
        type:
          type: string
          enum: [basic, advanced]
          example: basic
        module:
          type: string
          enum: [email, sms]
          example: email
        description:
          type: string
          example: The user's full name
        is_active:
          type: boolean
          example: true
    UpdateEmailTemplateVariableRequest:
      type: object
      properties:
        variable:
          type: string
          example: name
        content:
          type: string
          example: User's name
        type:
          type: string
          enum: [basic, advanced]
          example: basic
        module:
          type: string
          enum: [email, sms]
          example: email
        description:
          type: string
          example: The user's full name
        is_active:
          type: boolean
          example: true
    SmsSet:
      type: object
      properties:
        pk_set_id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Notification SMS
        description:
          type: string
          example: Templates for notifications
        character_limit:
          type: integer
          example: 160
        is_active:
          type: boolean
          example: true
        created_by:
          type: integer
          format: int64
          example: 1
        modified_by:
          type: integer
          format: int64
          example: 1
        created_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
        updated_at:
          type: string
          format: date-time
          example: 2023-05-20T12:34:56Z
    CreateSmsSetRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: Notification SMS
        description:
          type: string
          example: Templates for notifications
        character_limit:
          type: integer
          example: 160
        is_active:
          type: boolean
          example: true
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Bad request
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Unauthorized
    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Resource not found
    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: The given data was invalid
              errors:
                type: object
                additionalProperties:
                  type: array
                  items:
                    type: string
                example:
                  name: ["The name field is required."]
    ServerError:
      description: Server error
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: Server error
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

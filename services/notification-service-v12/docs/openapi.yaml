openapi: 3.1.0
info:
  title: Notification Service API
  description: API for sending emails and SMS messages
  version: 1.0.0
  contact:
    name: CubeOneBiz Support
    email: <EMAIL>
servers:
  - url: https://api.cubeonebiz.com/v2/notification
    description: Production server
  - url: https://staging-api.cubeonebiz.com/v2/notification
    description: Staging server
  - url: http://localhost:8000/api/v2/notification
    description: Local development server
paths:
  /health:
    get:
      summary: Get service health status
      description: Returns the health status of the notification service
      operationId: getHealth
      tags:
        - Health
      responses:
        '200':
          description: Service health information
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  service:
                    type: string
                    example: notification-service
                  version:
                    type: string
                    example: 1.0.0
                  timestamp:
                    type: string
                    format: date-time
                    example: '2025-05-20T12:34:56Z'
  /email:
    post:
      summary: Send an email
      description: Sends an email with the specified parameters
      operationId: sendEmail
      tags:
        - Email
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendEmailRequest'
      responses:
        '200':
          description: Email sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /email/queue:
    get:
      summary: Get email queue status
      description: Returns the status of the email queue
      operationId: getEmailQueueStatus
      tags:
        - Email
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Email queue status
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      pending:
                        type: integer
                        example: 5
                      processing:
                        type: integer
                        example: 2
                      sent:
                        type: integer
                        example: 10
                      failed:
                        type: integer
                        example: 1
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /sms:
    post:
      summary: Send an SMS
      description: Sends an SMS with the specified parameters
      operationId: sendSms
      tags:
        - SMS
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendSmsRequest'
      responses:
        '200':
          description: SMS sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /sms/queue:
    get:
      summary: Get SMS queue status
      description: Returns the status of the SMS queue
      operationId: getSmsQueueStatus
      tags:
        - SMS
      security:
        - bearerAuth: []
      responses:
        '200':
          description: SMS queue status
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      pending:
                        type: integer
                        example: 5
                      processing:
                        type: integer
                        example: 2
                      sent:
                        type: integer
                        example: 10
                      failed:
                        type: integer
                        example: 1
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    SendEmailRequest:
      type: object
      required:
        - to
        - subject
        - body
      properties:
        from:
          type: object
          additionalProperties:
            type: string
            format: email
          example:
            "Sender Name": "<EMAIL>"
        to:
          type: object
          additionalProperties:
            type: string
            format: email
          example:
            "Recipient Name": "<EMAIL>"
        cc:
          type: object
          additionalProperties:
            type: string
            format: email
          example:
            "CC Recipient": "<EMAIL>"
        bcc:
          type: object
          additionalProperties:
            type: string
            format: email
          example:
            "BCC Recipient": "<EMAIL>"
        subject:
          type: string
          example: "Test Email Subject"
        body:
          type: string
          example: "<p>This is a test email body.</p>"
        charset:
          type: string
          default: "UTF-8"
          example: "UTF-8"
        attachments:
          type: array
          items:
            type: object
            properties:
              path:
                type: string
                example: "/path/to/file.pdf"
              name:
                type: string
                example: "document.pdf"
              mime:
                type: string
                example: "application/pdf"
        content_type:
          type: string
          enum:
            - text/html
            - text/plain
          default: "text/html"
          example: "text/html"
        signature:
          type: string
          example: "Best regards,\nThe Team"
        priority:
          type: integer
          enum:
            - 1
            - 2
            - 3
            - 4
          default: 4
          example: 1
    SendSmsRequest:
      type: object
      required:
        - mobile
        - message
      properties:
        mobile:
          type: string
          pattern: "^[0-9+\\s]+$"
          example: "+1234567890"
        message:
          type: string
          example: "This is a test SMS message."
        merchant:
          type: object
          example:
            name: "Test Merchant"
            phone: "+1234567890"
        priority:
          type: integer
          enum:
            - 1
            - 2
          default: 1
          example: 1
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"
    ValidationErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "The given data was invalid."
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            email:
              - "The email field is required."
            mobile:
              - "The mobile field must be a valid phone number."

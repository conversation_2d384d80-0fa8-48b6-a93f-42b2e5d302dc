<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('email_template_sets', function (Blueprint $table) {
            $table->id('pk_set_id');
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index('name');
            $table->index('is_active');
        });
        
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id('pk_template_id');
            $table->unsignedBigInteger('fk_set_id');
            $table->string('name');
            $table->string('subject');
            $table->text('body');
            $table->string('type')->default('html');
            $table->string('purpose')->nullable();
            $table->text('template_variable_id')->nullable();
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->timestamps();
            
            // Add foreign key constraint
            $table->foreign('fk_set_id')
                ->references('pk_set_id')
                ->on('email_template_sets')
                ->onDelete('cascade');
            
            // Add indexes for better performance
            $table->index('name');
            $table->index('is_active');
        });
        
        Schema::create('email_template_variables', function (Blueprint $table) {
            $table->id();
            $table->string('variable');
            $table->string('content');
            $table->string('type')->default('basic');
            $table->string('module')->default('email');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index('variable');
            $table->index('type');
            $table->index('module');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
        Schema::dropIfExists('email_template_sets');
        Schema::dropIfExists('email_template_variables');
    }
};

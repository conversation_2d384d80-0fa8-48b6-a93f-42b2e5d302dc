<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('sms_sets', function (Blueprint $table) {
            $table->id('pk_set_id');
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('character_limit')->default(160);
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index('name');
            $table->index('is_active');
        });
        
        Schema::create('sms_templates', function (Blueprint $table) {
            $table->id('sms_template_id');
            $table->unsignedBigInteger('fk_set_id');
            $table->string('name');
            $table->text('sms_content');
            $table->string('purpose')->nullable();
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->timestamps();
            
            // Add foreign key constraint
            $table->foreign('fk_set_id')
                ->references('pk_set_id')
                ->on('sms_sets')
                ->onDelete('cascade');
            
            // Add indexes for better performance
            $table->index('name');
            $table->index('is_active');
            $table->index('is_approved');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_templates');
        Schema::dropIfExists('sms_sets');
    }
};

<?php

namespace Database\Factories;

use App\Models\SmsSet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SmsSet>
 */
class SmsSetFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SmsSet::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->words(3, true),
            'description' => $this->faker->sentence(),
            'character_limit' => 160,
            'is_active' => true,
            'created_by' => 1,
            'modified_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the set is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Set a custom character limit.
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function characterLimit(int $limit): Factory
    {
        return $this->state(function (array $attributes) use ($limit) {
            return [
                'character_limit' => $limit,
            ];
        });
    }
}

<?php

namespace Database\Factories;

use App\Models\EmailTemplateVariable;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplateVariable>
 */
class EmailTemplateVariableFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplateVariable::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'variable' => $this->faker->unique()->word(),
            'content' => $this->faker->word(),
            'type' => $this->faker->randomElement(['basic', 'advanced']),
            'module' => $this->faker->randomElement(['email', 'sms']),
            'description' => $this->faker->sentence(),
            'is_active' => true,
            'created_by' => 1,
            'modified_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the variable is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Indicate that the variable is for email.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function email(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'module' => 'email',
            ];
        });
    }

    /**
     * Indicate that the variable is for SMS.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sms(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'module' => 'sms',
            ];
        });
    }
}

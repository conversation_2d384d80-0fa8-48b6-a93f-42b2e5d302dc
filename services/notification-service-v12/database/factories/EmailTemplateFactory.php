<?php

namespace Database\Factories;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateSet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'fk_set_id' => EmailTemplateSet::factory(),
            'name' => $this->faker->unique()->words(3, true),
            'subject' => $this->faker->sentence(),
            'body' => $this->faker->paragraphs(3, true),
            'type' => $this->faker->randomElement(['html', 'text']),
            'purpose' => $this->faker->sentence(),
            'template_variable_id' => null,
            'is_active' => true,
            'created_by' => 1,
            'modified_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the template is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Indicate that the template is HTML.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function html(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'html',
                'body' => '<h1>Hello</h1><p>' . $this->faker->paragraph() . '</p>',
            ];
        });
    }

    /**
     * Indicate that the template is text.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function text(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'text',
                'body' => $this->faker->paragraphs(3, true),
            ];
        });
    }
}

<?php

namespace Database\Factories;

use App\Models\SmsSet;
use App\Models\SmsTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SmsTemplate>
 */
class SmsTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SmsTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'fk_set_id' => SmsSet::factory(),
            'name' => $this->faker->unique()->words(3, true),
            'sms_content' => $this->faker->sentence(),
            'purpose' => $this->faker->sentence(),
            'is_approved' => false,
            'is_active' => true,
            'created_by' => 1,
            'modified_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the template is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Indicate that the template is approved.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function approved(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_approved' => true,
            ];
        });
    }

    /**
     * Set a custom SMS content.
     *
     * @param string $content
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function content(string $content): Factory
    {
        return $this->state(function (array $attributes) use ($content) {
            return [
                'sms_content' => $content,
            ];
        });
    }
}

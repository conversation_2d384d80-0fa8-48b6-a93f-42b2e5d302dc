<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default SMS Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default SMS provider that is used to send any SMS
    | messages sent by your application. Alternative providers may be setup
    | and used as needed; however, this provider will be used by default.
    |
    */
    'default' => env('SMS_PROVIDER', 'textlocal'),

    /*
    |--------------------------------------------------------------------------
    | 24x7 SMS Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the 24x7 SMS settings for your application.
    | This is used for the 24x7 SMS provider.
    |
    */
    '24x7sms' => [
        'api_url' => env('SMS_24X7_API_URL', 'http://smsapi.24x7sms.com/api_1.0/SendSMS.aspx'),
        'api_key' => env('SMS_24X7_API_KEY'),
        'email' => env('SMS_24X7_EMAIL'),
        'password' => env('SMS_24X7_PASSWORD'),
        'service_name' => env('SMS_24X7_SERVICE_NAME'),
        'sender_id' => env('SMS_24X7_SENDER_ID'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Plivo Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the Plivo settings for your application.
    | This is used for the Plivo provider.
    |
    */
    'plivo' => [
        'auth_id' => env('SMS_PLIVO_AUTH_ID'),
        'auth_token' => env('SMS_PLIVO_AUTH_TOKEN'),
        'sender_id' => env('SMS_PLIVO_SENDER_ID'),
    ],

    /*
    |--------------------------------------------------------------------------
    | TextLocal Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the TextLocal settings for your application.
    | This is used for the TextLocal provider.
    |
    */
    'textlocal' => [
        'api_url' => env('SMS_TEXTLOCAL_API_URL', 'https://api.textlocal.in/send/'),
        'auth_key' => env('SMS_AUTH_KEY', ''),
        'sender_id' => env('SMS_SENDER_ID', 'TXTLCL'),
        'options' => [
            'unicode' => true,
            'test' => env('SMS_TEST_MODE', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the SMS queue settings for your application.
    | This is used for the SMS queue service.
    |
    */
    'queue' => [
        'connection' => env('SMS_QUEUE_CONNECTION', 'redis'),
        'queue' => env('SMS_QUEUE_NAME', 'sms'),
        'retry_after' => 60,
        'block_for' => null,
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Here you may configure the SMS rate limiting settings for your application.
    | This is used for the SMS rate limiting service.
    |
    */
    'rate_limit' => [
        'enabled' => env('SMS_RATE_LIMIT_ENABLED', true),
        'max_attempts' => env('SMS_RATE_LIMIT_MAX_ATTEMPTS', 5),
        'decay_minutes' => env('SMS_RATE_LIMIT_DECAY_MINUTES', 1),
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Templates
    |--------------------------------------------------------------------------
    |
    | Here you may configure the SMS template settings for your application.
    | This is used for the SMS template service.
    |
    */
    'templates' => [
        'cache_ttl' => env('SMS_TEMPLATE_CACHE_TTL', 3600), // 1 hour
        'default_templates' => [
            'order_confirmation' => 'Thank you for your order #:order_id. Your order has been confirmed and will be delivered soon.',
            'order_delivered' => 'Your order #:order_id has been delivered. Thank you for choosing us!',
            'registration' => 'Welcome to our service! Your registration is complete. Your OTP is :otp',
            'password_reset' => 'Your password reset OTP is :otp. This OTP will expire in 10 minutes.',
            'payment_confirmation' => 'Payment of :amount has been received for order #:order_id. Thank you!',
            'registration_required' => 'Please register on our website :website to place an order.',
        ],
    ],
];

<?php

namespace Tests\Feature\Api\V2;

use App\Services\Sms\SmsService;
use App\Services\Template\SmsTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class SmsControllerTest extends TestCase
{
    /**
     * @var SmsService|Mockery\MockInterface
     */
    protected $mockSmsService;

    /**
     * @var SmsTemplateService|Mockery\MockInterface
     */
    protected $mockTemplateService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockSmsService = Mockery::mock(SmsService::class);
        $this->app->instance(SmsService::class, $this->mockSmsService);
        
        $this->mockTemplateService = Mockery::mock(SmsTemplateService::class);
        $this->app->instance(SmsTemplateService::class, $this->mockTemplateService);
    }

    /**
     * Clean up the testing environment before the next test.
     *
     * @return void
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test sending an SMS.
     *
     * @return void
     */
    public function testSend(): void
    {
        $this->mockSmsService->shouldReceive('send')
            ->once()
            ->withArgs(function ($to, $message, $from, $options) {
                return $to === '+1234567890'
                    && $message === 'Test message'
                    && $from === 'SENDER'
                    && empty($options);
            })
            ->andReturn(true);
        
        $response = $this->postJson('/api/v2/notification/sms-v2/send', [
            'to' => '+1234567890',
            'message' => 'Test message',
            'from' => 'SENDER',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'SMS sent successfully',
            ]);
    }

    /**
     * Test sending an SMS with validation errors.
     *
     * @return void
     */
    public function testSendWithValidationErrors(): void
    {
        $response = $this->postJson('/api/v2/notification/sms-v2/send', [
            'to' => 'invalid-phone',
            'message' => '',
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['to', 'message']);
    }

    /**
     * Test sending an SMS with the service failing.
     *
     * @return void
     */
    public function testSendWithServiceFailing(): void
    {
        $this->mockSmsService->shouldReceive('send')
            ->once()
            ->andReturn(false);
        
        $response = $this->postJson('/api/v2/notification/sms-v2/send', [
            'to' => '+1234567890',
            'message' => 'Test message',
        ]);
        
        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'message' => 'Failed to send SMS',
            ]);
    }

    /**
     * Test sending bulk SMS.
     *
     * @return void
     */
    public function testSendBulk(): void
    {
        $this->mockSmsService->shouldReceive('sendBulk')
            ->once()
            ->withArgs(function ($to, $message, $from, $options) {
                return $to === ['+1234567890', '+0987654321']
                    && $message === 'Test message'
                    && $from === 'SENDER'
                    && empty($options);
            })
            ->andReturn([
                '+1234567890' => true,
                '+0987654321' => true,
            ]);
        
        $response = $this->postJson('/api/v2/notification/sms-v2/send-bulk', [
            'to' => ['+1234567890', '+0987654321'],
            'message' => 'Test message',
            'from' => 'SENDER',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'SMS sent to 2 recipients, failed for 0 recipients',
                'results' => [
                    '+1234567890' => true,
                    '+0987654321' => true,
                ],
            ]);
    }

    /**
     * Test sending bulk SMS with validation errors.
     *
     * @return void
     */
    public function testSendBulkWithValidationErrors(): void
    {
        $response = $this->postJson('/api/v2/notification/sms-v2/send-bulk', [
            'to' => ['invalid-phone'],
            'message' => '',
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['to.0', 'message']);
    }

    /**
     * Test sending bulk SMS with the service failing.
     *
     * @return void
     */
    public function testSendBulkWithServiceFailing(): void
    {
        $this->mockSmsService->shouldReceive('sendBulk')
            ->once()
            ->andReturn([
                '+1234567890' => false,
                '+0987654321' => false,
            ]);
        
        $response = $this->postJson('/api/v2/notification/sms-v2/send-bulk', [
            'to' => ['+1234567890', '+0987654321'],
            'message' => 'Test message',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => false,
                'message' => 'SMS sent to 0 recipients, failed for 2 recipients',
                'results' => [
                    '+1234567890' => false,
                    '+0987654321' => false,
                ],
            ]);
    }

    /**
     * Test sending an SMS using a template.
     *
     * @return void
     */
    public function testSendTemplate(): void
    {
        $this->mockSmsService->shouldReceive('sendTemplate')
            ->once()
            ->withArgs(function ($to, $templateIdOrName, $templateData, $setId, $from, $options) {
                return $to === '+1234567890'
                    && $templateIdOrName === 1
                    && $templateData['name'] === 'John Doe'
                    && $templateData['order_id'] === '12345'
                    && $setId === 2
                    && $from === 'SENDER'
                    && empty($options);
            })
            ->andReturn(true);
        
        $response = $this->postJson('/api/v2/notification/sms-v2/send-template', [
            'to' => '+1234567890',
            'template_id' => 1,
            'template_data' => [
                'name' => 'John Doe',
                'order_id' => '12345',
            ],
            'set_id' => 2,
            'from' => 'SENDER',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'SMS sent successfully',
            ]);
    }

    /**
     * Test sending an SMS using a template with validation errors.
     *
     * @return void
     */
    public function testSendTemplateWithValidationErrors(): void
    {
        $response = $this->postJson('/api/v2/notification/sms-v2/send-template', [
            'to' => 'invalid-phone',
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['to', 'template_id', 'template_name']);
    }

    /**
     * Test sending an SMS using a template with the service failing.
     *
     * @return void
     */
    public function testSendTemplateWithServiceFailing(): void
    {
        $this->mockSmsService->shouldReceive('sendTemplate')
            ->once()
            ->andReturn(false);
        
        $response = $this->postJson('/api/v2/notification/sms-v2/send-template', [
            'to' => '+1234567890',
            'template_id' => 1,
            'template_data' => [
                'name' => 'John Doe',
                'order_id' => '12345',
            ],
        ]);
        
        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'message' => 'Failed to send SMS',
            ]);
    }

    /**
     * Test sending bulk SMS using a template.
     *
     * @return void
     */
    public function testSendBulkTemplate(): void
    {
        $this->mockSmsService->shouldReceive('sendBulkTemplate')
            ->once()
            ->withArgs(function ($to, $templateIdOrName, $templateData, $setId, $from, $options) {
                return $to === ['+1234567890', '+0987654321']
                    && $templateIdOrName === 1
                    && $templateData['name'] === 'John Doe'
                    && $templateData['order_id'] === '12345'
                    && $setId === 2
                    && $from === 'SENDER'
                    && empty($options);
            })
            ->andReturn([
                '+1234567890' => true,
                '+0987654321' => true,
            ]);
        
        $response = $this->postJson('/api/v2/notification/sms-v2/send-bulk-template', [
            'to' => ['+1234567890', '+0987654321'],
            'template_id' => 1,
            'template_data' => [
                'name' => 'John Doe',
                'order_id' => '12345',
            ],
            'set_id' => 2,
            'from' => 'SENDER',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'SMS sent to 2 recipients, failed for 0 recipients',
                'results' => [
                    '+1234567890' => true,
                    '+0987654321' => true,
                ],
            ]);
    }
}

<?php

namespace Tests\Feature\Api\V2;

use App\Services\Email\ProviderEmailService;
use App\Services\Template\EmailTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class EmailControllerTest extends TestCase
{
    /**
     * @var ProviderEmailService|Mockery\MockInterface
     */
    protected $mockEmailService;

    /**
     * @var EmailTemplateService|Mockery\MockInterface
     */
    protected $mockTemplateService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockEmailService = Mockery::mock(ProviderEmailService::class);
        $this->app->instance(ProviderEmailService::class, $this->mockEmailService);
        
        $this->mockTemplateService = Mockery::mock(EmailTemplateService::class);
        $this->app->instance(EmailTemplateService::class, $this->mockTemplateService);
    }

    /**
     * Clean up the testing environment before the next test.
     *
     * @return void
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test sending an email.
     *
     * @return void
     */
    public function testSend(): void
    {
        $this->mockEmailService->shouldReceive('send')
            ->once()
            ->withArgs(function ($from, $to, $cc, $bcc, $subject, $body, $contentType, $attachments, $headers) {
                return $from['<EMAIL>'] === 'Sender Name'
                    && $to['<EMAIL>'] === 'Recipient Name'
                    && $cc['<EMAIL>'] === 'CC Name'
                    && $bcc['<EMAIL>'] === 'BCC Name'
                    && $subject === 'Test Subject'
                    && $body === '<p>Test Body</p>'
                    && $contentType === 'html'
                    && empty($attachments)
                    && empty($headers);
            })
            ->andReturn(true);
        
        $response = $this->postJson('/api/v2/notification/email-v2/send', [
            'from_email' => '<EMAIL>',
            'from_name' => 'Sender Name',
            'to' => [
                ['email' => '<EMAIL>', 'name' => 'Recipient Name'],
            ],
            'cc' => [
                ['email' => '<EMAIL>', 'name' => 'CC Name'],
            ],
            'bcc' => [
                ['email' => '<EMAIL>', 'name' => 'BCC Name'],
            ],
            'subject' => 'Test Subject',
            'body' => '<p>Test Body</p>',
            'content_type' => 'html',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Email sent successfully',
            ]);
    }

    /**
     * Test sending an email with validation errors.
     *
     * @return void
     */
    public function testSendWithValidationErrors(): void
    {
        $response = $this->postJson('/api/v2/notification/email-v2/send', [
            'from_email' => 'invalid-email',
            'to' => [
                ['email' => 'invalid-email'],
            ],
            'subject' => '',
            'body' => '',
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['from_email', 'to.0.email', 'subject', 'body']);
    }

    /**
     * Test sending an email with the service failing.
     *
     * @return void
     */
    public function testSendWithServiceFailing(): void
    {
        $this->mockEmailService->shouldReceive('send')
            ->once()
            ->andReturn(false);
        
        $response = $this->postJson('/api/v2/notification/email-v2/send', [
            'from_email' => '<EMAIL>',
            'from_name' => 'Sender Name',
            'to' => [
                ['email' => '<EMAIL>', 'name' => 'Recipient Name'],
            ],
            'subject' => 'Test Subject',
            'body' => '<p>Test Body</p>',
        ]);
        
        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'message' => 'Failed to send email',
            ]);
    }

    /**
     * Test sending an email using a template.
     *
     * @return void
     */
    public function testSendTemplate(): void
    {
        $this->mockEmailService->shouldReceive('sendTemplate')
            ->once()
            ->withArgs(function ($from, $to, $cc, $bcc, $templateIdOrName, $templateData, $setId, $attachments, $headers) {
                return $from['<EMAIL>'] === 'Sender Name'
                    && $to['<EMAIL>'] === 'Recipient Name'
                    && empty($cc)
                    && empty($bcc)
                    && $templateIdOrName === 1
                    && $templateData['name'] === 'John Doe'
                    && $templateData['order_id'] === '12345'
                    && $setId === 2
                    && empty($attachments)
                    && empty($headers);
            })
            ->andReturn(true);
        
        $response = $this->postJson('/api/v2/notification/email-v2/send-template', [
            'from_email' => '<EMAIL>',
            'from_name' => 'Sender Name',
            'to' => [
                ['email' => '<EMAIL>', 'name' => 'Recipient Name'],
            ],
            'template_id' => 1,
            'template_data' => [
                'name' => 'John Doe',
                'order_id' => '12345',
            ],
            'set_id' => 2,
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Email sent successfully',
            ]);
    }

    /**
     * Test sending an email using a template with validation errors.
     *
     * @return void
     */
    public function testSendTemplateWithValidationErrors(): void
    {
        $response = $this->postJson('/api/v2/notification/email-v2/send-template', [
            'from_email' => 'invalid-email',
            'to' => [
                ['email' => 'invalid-email'],
            ],
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['from_email', 'to.0.email', 'template_id', 'template_name']);
    }

    /**
     * Test sending an email using a template with the service failing.
     *
     * @return void
     */
    public function testSendTemplateWithServiceFailing(): void
    {
        $this->mockEmailService->shouldReceive('sendTemplate')
            ->once()
            ->andReturn(false);
        
        $response = $this->postJson('/api/v2/notification/email-v2/send-template', [
            'from_email' => '<EMAIL>',
            'from_name' => 'Sender Name',
            'to' => [
                ['email' => '<EMAIL>', 'name' => 'Recipient Name'],
            ],
            'template_id' => 1,
            'template_data' => [
                'name' => 'John Doe',
                'order_id' => '12345',
            ],
        ]);
        
        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'message' => 'Failed to send email',
            ]);
    }
}

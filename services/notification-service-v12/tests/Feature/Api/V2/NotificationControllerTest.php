<?php

namespace Tests\Feature\Api\V2;

use App\Services\Email\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class NotificationControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the Mail facade
        Mail::fake();
        
        // Mock the Queue facade
        Queue::fake();
        
        // Create a test user and generate a token
        $this->user = \App\Models\User::factory()->create();
        $this->token = $this->user->createToken('test-token')->plainTextToken;
    }

    /**
     * Test the health endpoint.
     *
     * @return void
     */
    public function testHealthEndpoint(): void
    {
        $response = $this->getJson('/api/v2/notification/health');
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'service',
                'version',
                'timestamp',
            ]);
    }

    /**
     * Test sending an email.
     *
     * @return void
     */
    public function testSendEmail(): void
    {
        // Mock the DB facade for email queue
        DB::shouldReceive('table')
            ->with('email_queue')
            ->andReturnSelf();
        
        DB::shouldReceive('insertGetId')
            ->andReturn(1);
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v2/notification/email', [
            'to' => ['<EMAIL>'],
            'subject' => 'Test Subject',
            'body' => 'Test Body',
            'priority' => EmailService::PRIORITY_SEND_IMMEDIATELY,
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Email sent successfully',
            ]);
    }

    /**
     * Test sending an email with validation errors.
     *
     * @return void
     */
    public function testSendEmailWithValidationErrors(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v2/notification/email', [
            // Missing required fields
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['to', 'subject', 'body']);
    }

    /**
     * Test sending an SMS.
     *
     * @return void
     */
    public function testSendSms(): void
    {
        // Mock the DB facade for SMS queue
        DB::shouldReceive('table')
            ->with('sms_queue')
            ->andReturnSelf();
        
        DB::shouldReceive('insertGetId')
            ->andReturn(1);
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v2/notification/sms', [
            'mobile' => '1234567890',
            'message' => 'Test message',
            'priority' => EmailService::PRIORITY_SMS_IMMEDIATELY,
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'SMS sent successfully',
            ]);
    }

    /**
     * Test sending an SMS with validation errors.
     *
     * @return void
     */
    public function testSendSmsWithValidationErrors(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v2/notification/sms', [
            // Missing required fields
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['mobile', 'message']);
    }

    /**
     * Test getting email queue status.
     *
     * @return void
     */
    public function testGetEmailQueueStatus(): void
    {
        // Mock the DB facade for email queue status
        DB::shouldReceive('table')
            ->with('email_queue')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'pending')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'processing')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'sent')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'failed')
            ->andReturnSelf();
        
        DB::shouldReceive('count')
            ->andReturn(5);
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v2/notification/email/queue');
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'pending' => 5,
                    'processing' => 5,
                    'sent' => 5,
                    'failed' => 5,
                ],
            ]);
    }

    /**
     * Test getting SMS queue status.
     *
     * @return void
     */
    public function testGetSmsQueueStatus(): void
    {
        // Mock the DB facade for SMS queue status
        DB::shouldReceive('table')
            ->with('sms_queue')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'pending')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'processing')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'sent')
            ->andReturnSelf();
        
        DB::shouldReceive('where')
            ->with('status', 'failed')
            ->andReturnSelf();
        
        DB::shouldReceive('count')
            ->andReturn(5);
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v2/notification/sms/queue');
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'pending' => 5,
                    'processing' => 5,
                    'sent' => 5,
                    'failed' => 5,
                ],
            ]);
    }

    /**
     * Test unauthorized access to protected endpoints.
     *
     * @return void
     */
    public function testUnauthorizedAccess(): void
    {
        // Test sending email without authentication
        $response = $this->postJson('/api/v2/notification/email', [
            'to' => ['<EMAIL>'],
            'subject' => 'Test Subject',
            'body' => 'Test Body',
        ]);
        
        $response->assertStatus(401);
        
        // Test sending SMS without authentication
        $response = $this->postJson('/api/v2/notification/sms', [
            'mobile' => '1234567890',
            'message' => 'Test message',
        ]);
        
        $response->assertStatus(401);
        
        // Test getting email queue status without authentication
        $response = $this->getJson('/api/v2/notification/email/queue');
        
        $response->assertStatus(401);
        
        // Test getting SMS queue status without authentication
        $response = $this->getJson('/api/v2/notification/sms/queue');
        
        $response->assertStatus(401);
    }
}

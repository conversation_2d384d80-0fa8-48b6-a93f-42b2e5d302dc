<?php

namespace Tests\Feature\Api\V2;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateSet;
use App\Models\EmailTemplateVariable;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class EmailTemplateControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user and authenticate
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /**
     * Test getting all email template sets.
     *
     * @return void
     */
    public function testGetAllSets(): void
    {
        // Create test data
        EmailTemplateSet::factory()->count(3)->create(['is_active' => true]);
        EmailTemplateSet::factory()->count(2)->create(['is_active' => false]);
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/email-templates/sets');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(3, 'data');
        
        // Make the request with active_only=false
        $response = $this->getJson('/api/v2/notification/email-templates/sets?active_only=false');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(5, 'data');
    }

    /**
     * Test getting an email template set by ID.
     *
     * @return void
     */
    public function testGetSetById(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/email-templates/sets/' . $set->pk_set_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'pk_set_id' => $set->pk_set_id,
                    'name' => $set->name,
                ],
            ]);
    }

    /**
     * Test creating an email template set.
     *
     * @return void
     */
    public function testCreateSet(): void
    {
        // Make the request
        $response = $this->postJson('/api/v2/notification/email-templates/sets', [
            'name' => 'Test Set',
            'description' => 'Test Description',
            'is_active' => true,
        ]);
        
        // Assert the response
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Email template set created successfully',
                'data' => [
                    'name' => 'Test Set',
                    'description' => 'Test Description',
                    'is_active' => true,
                ],
            ]);
    }

    /**
     * Test updating an email template set.
     *
     * @return void
     */
    public function testUpdateSet(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create([
            'name' => 'Old Name',
            'description' => 'Old Description',
            'is_active' => true,
        ]);
        
        // Make the request
        $response = $this->putJson('/api/v2/notification/email-templates/sets/' . $set->pk_set_id, [
            'name' => 'New Name',
            'description' => 'New Description',
            'is_active' => false,
        ]);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Email template set updated successfully',
                'data' => [
                    'pk_set_id' => $set->pk_set_id,
                    'name' => 'New Name',
                    'description' => 'New Description',
                    'is_active' => false,
                ],
            ]);
    }

    /**
     * Test deleting an email template set.
     *
     * @return void
     */
    public function testDeleteSet(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        
        // Make the request
        $response = $this->deleteJson('/api/v2/notification/email-templates/sets/' . $set->pk_set_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Email template set deleted successfully',
            ]);
        
        // Assert the set was deleted
        $this->assertNull(EmailTemplateSet::find($set->pk_set_id));
    }

    /**
     * Test getting all email templates for a set.
     *
     * @return void
     */
    public function testGetTemplatesBySetId(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        EmailTemplate::factory()->count(3)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => true,
        ]);
        EmailTemplate::factory()->count(2)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => false,
        ]);
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/email-templates/sets/' . $set->pk_set_id . '/templates');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(3, 'data');
        
        // Make the request with active_only=false
        $response = $this->getJson('/api/v2/notification/email-templates/sets/' . $set->pk_set_id . '/templates?active_only=false');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(5, 'data');
    }

    /**
     * Test getting an email template by ID.
     *
     * @return void
     */
    public function testGetTemplateById(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/email-templates/templates/' . $template->pk_template_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'pk_template_id' => $template->pk_template_id,
                    'name' => $template->name,
                ],
            ]);
    }

    /**
     * Test creating an email template.
     *
     * @return void
     */
    public function testCreateTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        
        // Make the request
        $response = $this->postJson('/api/v2/notification/email-templates/templates', [
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Test Template',
            'subject' => 'Test Subject',
            'body' => 'Test Body',
            'type' => 'html',
            'purpose' => 'Test Purpose',
            'is_active' => true,
        ]);
        
        // Assert the response
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Email template created successfully',
                'data' => [
                    'fk_set_id' => $set->pk_set_id,
                    'name' => 'Test Template',
                    'subject' => 'Test Subject',
                    'body' => 'Test Body',
                    'type' => 'html',
                    'purpose' => 'Test Purpose',
                    'is_active' => true,
                ],
            ]);
    }

    /**
     * Test updating an email template.
     *
     * @return void
     */
    public function testUpdateTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Old Name',
            'subject' => 'Old Subject',
            'body' => 'Old Body',
            'type' => 'html',
            'purpose' => 'Old Purpose',
            'is_active' => true,
        ]);
        
        // Make the request
        $response = $this->putJson('/api/v2/notification/email-templates/templates/' . $template->pk_template_id, [
            'name' => 'New Name',
            'subject' => 'New Subject',
            'body' => 'New Body',
            'type' => 'text',
            'purpose' => 'New Purpose',
            'is_active' => false,
        ]);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Email template updated successfully',
                'data' => [
                    'pk_template_id' => $template->pk_template_id,
                    'name' => 'New Name',
                    'subject' => 'New Subject',
                    'body' => 'New Body',
                    'type' => 'text',
                    'purpose' => 'New Purpose',
                    'is_active' => false,
                ],
            ]);
    }

    /**
     * Test deleting an email template.
     *
     * @return void
     */
    public function testDeleteTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Make the request
        $response = $this->deleteJson('/api/v2/notification/email-templates/templates/' . $template->pk_template_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Email template deleted successfully',
            ]);
        
        // Assert the template was deleted
        $this->assertNull(EmailTemplate::find($template->pk_template_id));
    }

    /**
     * Test previewing an email template.
     *
     * @return void
     */
    public function testPreviewTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'subject' => 'Hello #name#',
            'body' => '<p>Welcome, #name#!</p><p>Your email is #email#.</p>',
            'type' => 'html',
        ]);
        
        // Make the request
        $response = $this->postJson('/api/v2/notification/email-templates/templates/' . $template->pk_template_id . '/preview', [
            'data' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ],
        ]);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'subject' => 'Hello John Doe',
                    'body' => '<p>Welcome, John Doe!</p><p>Your <NAME_EMAIL>.</p>',
                    'type' => 'html',
                ],
            ]);
    }
}

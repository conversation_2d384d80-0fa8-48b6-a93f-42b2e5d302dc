<?php

namespace Tests\Feature\Api\V2;

use App\Models\SmsSet;
use App\Models\SmsTemplate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class SmsTemplateControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user and authenticate
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /**
     * Test getting all SMS sets.
     *
     * @return void
     */
    public function testGetAllSets(): void
    {
        // Create test data
        SmsSet::factory()->count(3)->create(['is_active' => true]);
        SmsSet::factory()->count(2)->create(['is_active' => false]);
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/sms-templates/sets');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(3, 'data');
        
        // Make the request with active_only=false
        $response = $this->getJson('/api/v2/notification/sms-templates/sets?active_only=false');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(5, 'data');
    }

    /**
     * Test getting an SMS set by ID.
     *
     * @return void
     */
    public function testGetSetById(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/sms-templates/sets/' . $set->pk_set_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'pk_set_id' => $set->pk_set_id,
                    'name' => $set->name,
                ],
            ]);
    }

    /**
     * Test creating an SMS set.
     *
     * @return void
     */
    public function testCreateSet(): void
    {
        // Make the request
        $response = $this->postJson('/api/v2/notification/sms-templates/sets', [
            'name' => 'Test Set',
            'description' => 'Test Description',
            'character_limit' => 160,
            'is_active' => true,
        ]);
        
        // Assert the response
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'SMS set created successfully',
                'data' => [
                    'name' => 'Test Set',
                    'description' => 'Test Description',
                    'character_limit' => 160,
                    'is_active' => true,
                ],
            ]);
    }

    /**
     * Test updating an SMS set.
     *
     * @return void
     */
    public function testUpdateSet(): void
    {
        // Create test data
        $set = SmsSet::factory()->create([
            'name' => 'Old Name',
            'description' => 'Old Description',
            'character_limit' => 160,
            'is_active' => true,
        ]);
        
        // Make the request
        $response = $this->putJson('/api/v2/notification/sms-templates/sets/' . $set->pk_set_id, [
            'name' => 'New Name',
            'description' => 'New Description',
            'character_limit' => 200,
            'is_active' => false,
        ]);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'SMS set updated successfully',
                'data' => [
                    'pk_set_id' => $set->pk_set_id,
                    'name' => 'New Name',
                    'description' => 'New Description',
                    'character_limit' => 200,
                    'is_active' => false,
                ],
            ]);
    }

    /**
     * Test deleting an SMS set.
     *
     * @return void
     */
    public function testDeleteSet(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        
        // Make the request
        $response = $this->deleteJson('/api/v2/notification/sms-templates/sets/' . $set->pk_set_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'SMS set deleted successfully',
            ]);
        
        // Assert the set was deleted
        $this->assertNull(SmsSet::find($set->pk_set_id));
    }

    /**
     * Test getting all SMS templates for a set.
     *
     * @return void
     */
    public function testGetTemplatesBySetId(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        SmsTemplate::factory()->count(3)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => true,
            'is_approved' => true,
        ]);
        SmsTemplate::factory()->count(2)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => true,
            'is_approved' => false,
        ]);
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/sms-templates/sets/' . $set->pk_set_id . '/templates');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(5, 'data');
        
        // Make the request with approved_only=true
        $response = $this->getJson('/api/v2/notification/sms-templates/sets/' . $set->pk_set_id . '/templates?approved_only=true');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonCount(3, 'data');
    }

    /**
     * Test getting an SMS template by ID.
     *
     * @return void
     */
    public function testGetTemplateById(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Make the request
        $response = $this->getJson('/api/v2/notification/sms-templates/templates/' . $template->sms_template_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'sms_template_id' => $template->sms_template_id,
                    'name' => $template->name,
                ],
            ]);
    }

    /**
     * Test creating an SMS template.
     *
     * @return void
     */
    public function testCreateTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create(['character_limit' => 160]);
        
        // Make the request
        $response = $this->postJson('/api/v2/notification/sms-templates/templates', [
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Test Template',
            'sms_content' => 'Test Content',
            'purpose' => 'Test Purpose',
            'is_approved' => false,
            'is_active' => true,
        ]);
        
        // Assert the response
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'SMS template created successfully',
                'data' => [
                    'fk_set_id' => $set->pk_set_id,
                    'name' => 'Test Template',
                    'sms_content' => 'Test Content',
                    'purpose' => 'Test Purpose',
                    'is_approved' => false,
                    'is_active' => true,
                ],
            ]);
    }

    /**
     * Test updating an SMS template.
     *
     * @return void
     */
    public function testUpdateTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create(['character_limit' => 160]);
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Old Name',
            'sms_content' => 'Old Content',
            'purpose' => 'Old Purpose',
            'is_approved' => false,
            'is_active' => true,
        ]);
        
        // Make the request
        $response = $this->putJson('/api/v2/notification/sms-templates/templates/' . $template->sms_template_id, [
            'name' => 'New Name',
            'sms_content' => 'New Content',
            'purpose' => 'New Purpose',
            'is_approved' => true,
            'is_active' => false,
        ]);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'SMS template updated successfully',
                'data' => [
                    'sms_template_id' => $template->sms_template_id,
                    'name' => 'New Name',
                    'sms_content' => 'New Content',
                    'purpose' => 'New Purpose',
                    'is_approved' => true,
                    'is_active' => false,
                ],
            ]);
    }

    /**
     * Test deleting an SMS template.
     *
     * @return void
     */
    public function testDeleteTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Make the request
        $response = $this->deleteJson('/api/v2/notification/sms-templates/templates/' . $template->sms_template_id);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'SMS template deleted successfully',
            ]);
        
        // Assert the template was deleted
        $this->assertNull(SmsTemplate::find($template->sms_template_id));
    }

    /**
     * Test approving an SMS template.
     *
     * @return void
     */
    public function testApproveTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'is_approved' => false,
        ]);
        
        // Make the request
        $response = $this->postJson('/api/v2/notification/sms-templates/templates/' . $template->sms_template_id . '/approve');
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ])
            ->assertJson([
                'success' => true,
                'message' => 'SMS template approved successfully',
                'data' => [
                    'sms_template_id' => $template->sms_template_id,
                    'is_approved' => true,
                ],
            ]);
    }

    /**
     * Test previewing an SMS template.
     *
     * @return void
     */
    public function testPreviewTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create(['character_limit' => 160]);
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'sms_content' => 'Hello #name#, your order #order_id# has been confirmed.',
        ]);
        
        // Make the request
        $response = $this->postJson('/api/v2/notification/sms-templates/templates/' . $template->sms_template_id . '/preview', [
            'data' => [
                'name' => 'John Doe',
                'order_id' => '12345',
            ],
        ]);
        
        // Assert the response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'content',
                    'character_count',
                    'character_limit',
                    'is_within_limit',
                    'remaining_characters',
                ],
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'content' => 'Hello John Doe, your order 12345 has been confirmed.',
                    'character_count' => 53,
                    'character_limit' => 160,
                    'is_within_limit' => true,
                    'remaining_characters' => 107,
                ],
            ]);
    }
}

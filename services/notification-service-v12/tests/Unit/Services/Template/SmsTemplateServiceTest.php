<?php

namespace Tests\Unit\Services\Template;

use App\Models\SmsSet;
use App\Models\SmsTemplate;
use App\Services\Template\SmsTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SmsTemplateServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var SmsTemplateService
     */
    protected $smsTemplateService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->smsTemplateService = new SmsTemplateService();
    }

    /**
     * Test getting all SMS sets.
     *
     * @return void
     */
    public function testGetAllSets(): void
    {
        // Create test data
        SmsSet::factory()->count(3)->create(['is_active' => true]);
        SmsSet::factory()->count(2)->create(['is_active' => false]);
        
        // Get all active sets
        $activeSets = $this->smsTemplateService->getAllSets(true);
        
        // Get all sets
        $allSets = $this->smsTemplateService->getAllSets(false);
        
        // Assert the results
        $this->assertCount(3, $activeSets);
        $this->assertCount(5, $allSets);
    }

    /**
     * Test getting an SMS set by ID.
     *
     * @return void
     */
    public function testGetSetById(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        
        // Get the set by ID
        $result = $this->smsTemplateService->getSetById($set->pk_set_id);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals($set->pk_set_id, $result->pk_set_id);
        $this->assertEquals($set->name, $result->name);
    }

    /**
     * Test creating an SMS set.
     *
     * @return void
     */
    public function testCreateSet(): void
    {
        // Create a set
        $data = [
            'name' => 'Test Set',
            'description' => 'Test Description',
            'character_limit' => 160,
            'is_active' => true,
        ];
        
        $set = $this->smsTemplateService->createSet($data);
        
        // Assert the result
        $this->assertNotNull($set);
        $this->assertEquals('Test Set', $set->name);
        $this->assertEquals('Test Description', $set->description);
        $this->assertEquals(160, $set->character_limit);
        $this->assertTrue($set->is_active);
    }

    /**
     * Test updating an SMS set.
     *
     * @return void
     */
    public function testUpdateSet(): void
    {
        // Create test data
        $set = SmsSet::factory()->create([
            'name' => 'Old Name',
            'description' => 'Old Description',
            'character_limit' => 160,
            'is_active' => true,
        ]);
        
        // Update the set
        $data = [
            'name' => 'New Name',
            'description' => 'New Description',
            'character_limit' => 200,
            'is_active' => false,
        ];
        
        $result = $this->smsTemplateService->updateSet($set->pk_set_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('New Name', $result->name);
        $this->assertEquals('New Description', $result->description);
        $this->assertEquals(200, $result->character_limit);
        $this->assertFalse($result->is_active);
    }

    /**
     * Test deleting an SMS set.
     *
     * @return void
     */
    public function testDeleteSet(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        
        // Delete the set
        $result = $this->smsTemplateService->deleteSet($set->pk_set_id);
        
        // Assert the result
        $this->assertTrue($result);
        $this->assertNull(SmsSet::find($set->pk_set_id));
    }

    /**
     * Test getting all SMS templates for a set.
     *
     * @return void
     */
    public function testGetTemplatesBySetId(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        SmsTemplate::factory()->count(3)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => true,
            'is_approved' => true,
        ]);
        SmsTemplate::factory()->count(2)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => true,
            'is_approved' => false,
        ]);
        SmsTemplate::factory()->count(1)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => false,
            'is_approved' => true,
        ]);
        
        // Get all active templates
        $activeTemplates = $this->smsTemplateService->getTemplatesBySetId($set->pk_set_id, true, false);
        
        // Get all active and approved templates
        $approvedTemplates = $this->smsTemplateService->getTemplatesBySetId($set->pk_set_id, true, true);
        
        // Get all templates
        $allTemplates = $this->smsTemplateService->getTemplatesBySetId($set->pk_set_id, false, false);
        
        // Assert the results
        $this->assertCount(5, $activeTemplates);
        $this->assertCount(3, $approvedTemplates);
        $this->assertCount(6, $allTemplates);
    }

    /**
     * Test getting an SMS template by ID.
     *
     * @return void
     */
    public function testGetTemplateById(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Get the template by ID
        $result = $this->smsTemplateService->getTemplateById($template->sms_template_id);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals($template->sms_template_id, $result->sms_template_id);
        $this->assertEquals($template->name, $result->name);
    }

    /**
     * Test getting an SMS template by name.
     *
     * @return void
     */
    public function testGetTemplateByName(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Test Template',
            'is_active' => true,
            'is_approved' => true,
        ]);
        
        // Get the template by name
        $result = $this->smsTemplateService->getTemplateByName('Test Template', $set->pk_set_id, true);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals($template->sms_template_id, $result->sms_template_id);
        $this->assertEquals('Test Template', $result->name);
    }

    /**
     * Test creating an SMS template.
     *
     * @return void
     */
    public function testCreateTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create(['character_limit' => 160]);
        
        // Create a template
        $data = [
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Test Template',
            'sms_content' => 'Test Content',
            'purpose' => 'Test Purpose',
            'is_approved' => false,
            'is_active' => true,
        ];
        
        $template = $this->smsTemplateService->createTemplate($data);
        
        // Assert the result
        $this->assertNotNull($template);
        $this->assertEquals('Test Template', $template->name);
        $this->assertEquals('Test Content', $template->sms_content);
        $this->assertEquals('Test Purpose', $template->purpose);
        $this->assertFalse($template->is_approved);
        $this->assertTrue($template->is_active);
    }

    /**
     * Test updating an SMS template.
     *
     * @return void
     */
    public function testUpdateTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create(['character_limit' => 160]);
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Old Name',
            'sms_content' => 'Old Content',
            'purpose' => 'Old Purpose',
            'is_approved' => false,
            'is_active' => true,
        ]);
        
        // Update the template
        $data = [
            'name' => 'New Name',
            'sms_content' => 'New Content',
            'purpose' => 'New Purpose',
            'is_approved' => true,
            'is_active' => false,
        ];
        
        $result = $this->smsTemplateService->updateTemplate($template->sms_template_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('New Name', $result->name);
        $this->assertEquals('New Content', $result->sms_content);
        $this->assertEquals('New Purpose', $result->purpose);
        $this->assertTrue($result->is_approved);
        $this->assertFalse($result->is_active);
    }

    /**
     * Test deleting an SMS template.
     *
     * @return void
     */
    public function testDeleteTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Delete the template
        $result = $this->smsTemplateService->deleteTemplate($template->sms_template_id);
        
        // Assert the result
        $this->assertTrue($result);
        $this->assertNull(SmsTemplate::find($template->sms_template_id));
    }

    /**
     * Test approving an SMS template.
     *
     * @return void
     */
    public function testApproveTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'is_approved' => false,
        ]);
        
        // Approve the template
        $result = $this->smsTemplateService->approveTemplate($template->sms_template_id);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertTrue($result->is_approved);
    }

    /**
     * Test rendering an SMS template.
     *
     * @return void
     */
    public function testRenderTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create();
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'sms_content' => 'Hello #name#, your order #order_id# has been confirmed.',
            'is_active' => true,
            'is_approved' => true,
        ]);
        
        // Render the template
        $data = [
            'name' => 'John Doe',
            'order_id' => '12345',
        ];
        
        $result = $this->smsTemplateService->renderTemplate($template->sms_template_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('Hello John Doe, your order 12345 has been confirmed.', $result);
    }

    /**
     * Test previewing an SMS template.
     *
     * @return void
     */
    public function testPreviewTemplate(): void
    {
        // Create test data
        $set = SmsSet::factory()->create(['character_limit' => 160]);
        $template = SmsTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'sms_content' => 'Hello #name#, your order #order_id# has been confirmed.',
            'is_active' => true,
        ]);
        
        // Preview the template
        $data = [
            'name' => 'John Doe',
            'order_id' => '12345',
        ];
        
        $result = $this->smsTemplateService->previewTemplate($template->sms_template_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('Hello John Doe, your order 12345 has been confirmed.', $result['content']);
        $this->assertEquals(53, $result['character_count']);
        $this->assertEquals(160, $result['character_limit']);
        $this->assertTrue($result['is_within_limit']);
        $this->assertEquals(107, $result['remaining_characters']);
    }
}

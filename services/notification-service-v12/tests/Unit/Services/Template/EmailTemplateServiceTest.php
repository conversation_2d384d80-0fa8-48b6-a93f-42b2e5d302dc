<?php

namespace Tests\Unit\Services\Template;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateSet;
use App\Models\EmailTemplateVariable;
use App\Services\Template\EmailTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var EmailTemplateService
     */
    protected $emailTemplateService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->emailTemplateService = new EmailTemplateService();
    }

    /**
     * Test getting all email template sets.
     *
     * @return void
     */
    public function testGetAllSets(): void
    {
        // Create test data
        EmailTemplateSet::factory()->count(3)->create(['is_active' => true]);
        EmailTemplateSet::factory()->count(2)->create(['is_active' => false]);
        
        // Get all active sets
        $activeSets = $this->emailTemplateService->getAllSets(true);
        
        // Get all sets
        $allSets = $this->emailTemplateService->getAllSets(false);
        
        // Assert the results
        $this->assertCount(3, $activeSets);
        $this->assertCount(5, $allSets);
    }

    /**
     * Test getting an email template set by ID.
     *
     * @return void
     */
    public function testGetSetById(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        
        // Get the set by ID
        $result = $this->emailTemplateService->getSetById($set->pk_set_id);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals($set->pk_set_id, $result->pk_set_id);
        $this->assertEquals($set->name, $result->name);
    }

    /**
     * Test creating an email template set.
     *
     * @return void
     */
    public function testCreateSet(): void
    {
        // Create a set
        $data = [
            'name' => 'Test Set',
            'description' => 'Test Description',
            'is_active' => true,
        ];
        
        $set = $this->emailTemplateService->createSet($data);
        
        // Assert the result
        $this->assertNotNull($set);
        $this->assertEquals('Test Set', $set->name);
        $this->assertEquals('Test Description', $set->description);
        $this->assertTrue($set->is_active);
    }

    /**
     * Test updating an email template set.
     *
     * @return void
     */
    public function testUpdateSet(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create([
            'name' => 'Old Name',
            'description' => 'Old Description',
            'is_active' => true,
        ]);
        
        // Update the set
        $data = [
            'name' => 'New Name',
            'description' => 'New Description',
            'is_active' => false,
        ];
        
        $result = $this->emailTemplateService->updateSet($set->pk_set_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('New Name', $result->name);
        $this->assertEquals('New Description', $result->description);
        $this->assertFalse($result->is_active);
    }

    /**
     * Test deleting an email template set.
     *
     * @return void
     */
    public function testDeleteSet(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        
        // Delete the set
        $result = $this->emailTemplateService->deleteSet($set->pk_set_id);
        
        // Assert the result
        $this->assertTrue($result);
        $this->assertNull(EmailTemplateSet::find($set->pk_set_id));
    }

    /**
     * Test getting all email templates for a set.
     *
     * @return void
     */
    public function testGetTemplatesBySetId(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        EmailTemplate::factory()->count(3)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => true,
        ]);
        EmailTemplate::factory()->count(2)->create([
            'fk_set_id' => $set->pk_set_id,
            'is_active' => false,
        ]);
        
        // Get all active templates
        $activeTemplates = $this->emailTemplateService->getTemplatesBySetId($set->pk_set_id, true);
        
        // Get all templates
        $allTemplates = $this->emailTemplateService->getTemplatesBySetId($set->pk_set_id, false);
        
        // Assert the results
        $this->assertCount(3, $activeTemplates);
        $this->assertCount(5, $allTemplates);
    }

    /**
     * Test getting an email template by ID.
     *
     * @return void
     */
    public function testGetTemplateById(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Get the template by ID
        $result = $this->emailTemplateService->getTemplateById($template->pk_template_id);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals($template->pk_template_id, $result->pk_template_id);
        $this->assertEquals($template->name, $result->name);
    }

    /**
     * Test getting an email template by name.
     *
     * @return void
     */
    public function testGetTemplateByName(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Test Template',
            'is_active' => true,
        ]);
        
        // Get the template by name
        $result = $this->emailTemplateService->getTemplateByName('Test Template', $set->pk_set_id);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals($template->pk_template_id, $result->pk_template_id);
        $this->assertEquals('Test Template', $result->name);
    }

    /**
     * Test creating an email template.
     *
     * @return void
     */
    public function testCreateTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        
        // Create a template
        $data = [
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Test Template',
            'subject' => 'Test Subject',
            'body' => 'Test Body',
            'type' => 'html',
            'purpose' => 'Test Purpose',
            'is_active' => true,
        ];
        
        $template = $this->emailTemplateService->createTemplate($data);
        
        // Assert the result
        $this->assertNotNull($template);
        $this->assertEquals('Test Template', $template->name);
        $this->assertEquals('Test Subject', $template->subject);
        $this->assertEquals('Test Body', $template->body);
        $this->assertEquals('html', $template->type);
        $this->assertEquals('Test Purpose', $template->purpose);
        $this->assertTrue($template->is_active);
    }

    /**
     * Test updating an email template.
     *
     * @return void
     */
    public function testUpdateTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'name' => 'Old Name',
            'subject' => 'Old Subject',
            'body' => 'Old Body',
            'type' => 'html',
            'purpose' => 'Old Purpose',
            'is_active' => true,
        ]);
        
        // Update the template
        $data = [
            'name' => 'New Name',
            'subject' => 'New Subject',
            'body' => 'New Body',
            'type' => 'text',
            'purpose' => 'New Purpose',
            'is_active' => false,
        ];
        
        $result = $this->emailTemplateService->updateTemplate($template->pk_template_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('New Name', $result->name);
        $this->assertEquals('New Subject', $result->subject);
        $this->assertEquals('New Body', $result->body);
        $this->assertEquals('text', $result->type);
        $this->assertEquals('New Purpose', $result->purpose);
        $this->assertFalse($result->is_active);
    }

    /**
     * Test deleting an email template.
     *
     * @return void
     */
    public function testDeleteTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
        ]);
        
        // Delete the template
        $result = $this->emailTemplateService->deleteTemplate($template->pk_template_id);
        
        // Assert the result
        $this->assertTrue($result);
        $this->assertNull(EmailTemplate::find($template->pk_template_id));
    }

    /**
     * Test rendering an email template.
     *
     * @return void
     */
    public function testRenderTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'subject' => 'Hello #name#',
            'body' => '<p>Welcome, #name#!</p><p>Your email is #email#.</p>',
            'type' => 'html',
            'is_active' => true,
        ]);
        
        // Render the template
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];
        
        $result = $this->emailTemplateService->renderTemplate($template->pk_template_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('Hello John Doe', $result['subject']);
        $this->assertEquals('<p>Welcome, John Doe!</p><p>Your <NAME_EMAIL>.</p>', $result['body']);
        $this->assertEquals('html', $result['type']);
    }

    /**
     * Test previewing an email template.
     *
     * @return void
     */
    public function testPreviewTemplate(): void
    {
        // Create test data
        $set = EmailTemplateSet::factory()->create();
        $template = EmailTemplate::factory()->create([
            'fk_set_id' => $set->pk_set_id,
            'subject' => 'Hello #name#',
            'body' => '<p>Welcome, #name#!</p><p>Your email is #email#.</p>',
            'type' => 'html',
            'is_active' => true,
        ]);
        
        // Preview the template
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];
        
        $result = $this->emailTemplateService->previewTemplate($template->pk_template_id, $data);
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertEquals('Hello John Doe', $result['subject']);
        $this->assertEquals('<p>Welcome, John Doe!</p><p>Your <NAME_EMAIL>.</p>', $result['body']);
        $this->assertEquals('html', $result['type']);
    }
}

<?php

namespace Tests\Unit\Services\Email;

use App\Services\Email\Providers\EmailProviderInterface;
use App\Services\Email\ProviderEmailService;
use App\Services\Template\EmailTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class ProviderEmailServiceTest extends TestCase
{
    /**
     * @var ProviderEmailService
     */
    protected $emailService;

    /**
     * @var EmailProviderInterface|Mockery\MockInterface
     */
    protected $mockProvider1;

    /**
     * @var EmailProviderInterface|Mockery\MockInterface
     */
    protected $mockProvider2;

    /**
     * @var EmailTemplateService|Mockery\MockInterface
     */
    protected $mockTemplateService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockProvider1 = Mockery::mock(EmailProviderInterface::class);
        $this->mockProvider1->shouldReceive('getName')->andReturn('provider1');
        $this->mockProvider1->shouldReceive('getPriority')->andReturn(10);
        
        $this->mockProvider2 = Mockery::mock(EmailProviderInterface::class);
        $this->mockProvider2->shouldReceive('getName')->andReturn('provider2');
        $this->mockProvider2->shouldReceive('getPriority')->andReturn(20);
        
        $this->mockTemplateService = Mockery::mock(EmailTemplateService::class);
        
        $this->emailService = new ProviderEmailService(
            [$this->mockProvider1, $this->mockProvider2],
            $this->mockTemplateService
        );
    }

    /**
     * Clean up the testing environment before the next test.
     *
     * @return void
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test getting all providers.
     *
     * @return void
     */
    public function testGetProviders(): void
    {
        $providers = $this->emailService->getProviders();
        
        $this->assertCount(2, $providers);
        $this->assertSame($this->mockProvider1, $providers[0]);
        $this->assertSame($this->mockProvider2, $providers[1]);
    }

    /**
     * Test getting available providers.
     *
     * @return void
     */
    public function testGetAvailableProviders(): void
    {
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(false);
        
        $availableProviders = $this->emailService->getAvailableProviders();
        
        $this->assertCount(1, $availableProviders);
        $this->assertSame($this->mockProvider1, $availableProviders[0]);
    }

    /**
     * Test sending an email with the first provider succeeding.
     *
     * @return void
     */
    public function testSendWithFirstProviderSucceeding(): void
    {
        $from = ['<EMAIL>' => 'Sender'];
        $to = ['<EMAIL>' => 'Recipient'];
        $cc = ['<EMAIL>' => 'CC'];
        $bcc = ['<EMAIL>' => 'BCC'];
        $subject = 'Test Subject';
        $body = 'Test Body';
        $contentType = 'html';
        $attachments = [
            ['path' => '/path/to/file.pdf', 'name' => 'file.pdf'],
        ];
        $headers = [
            'X-Custom-Header' => 'Custom Value',
        ];
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with($from, $to, $cc, $bcc, $subject, $body, $contentType, $attachments, $headers)
            ->andReturn(true);
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $this->emailService->send(
            $from,
            $to,
            $cc,
            $bcc,
            $subject,
            $body,
            $contentType,
            $attachments,
            $headers
        );
        
        $this->assertTrue($result);
    }

    /**
     * Test sending an email with the first provider failing and the second succeeding.
     *
     * @return void
     */
    public function testSendWithFirstProviderFailingAndSecondSucceeding(): void
    {
        $from = ['<EMAIL>' => 'Sender'];
        $to = ['<EMAIL>' => 'Recipient'];
        $subject = 'Test Subject';
        $body = 'Test Body';
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with($from, $to, [], [], $subject, $body, 'html', [], [])
            ->andReturn(false);
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('send')
            ->with($from, $to, [], [], $subject, $body, 'html', [], [])
            ->andReturn(true);
        
        $result = $this->emailService->send($from, $to, [], [], $subject, $body);
        
        $this->assertTrue($result);
    }

    /**
     * Test sending an email with all providers failing.
     *
     * @return void
     */
    public function testSendWithAllProvidersFailing(): void
    {
        $from = ['<EMAIL>' => 'Sender'];
        $to = ['<EMAIL>' => 'Recipient'];
        $subject = 'Test Subject';
        $body = 'Test Body';
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with($from, $to, [], [], $subject, $body, 'html', [], [])
            ->andReturn(false);
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('send')
            ->with($from, $to, [], [], $subject, $body, 'html', [], [])
            ->andReturn(false);
        
        $result = $this->emailService->send($from, $to, [], [], $subject, $body);
        
        $this->assertFalse($result);
    }

    /**
     * Test sending an email with no available providers.
     *
     * @return void
     */
    public function testSendWithNoAvailableProviders(): void
    {
        $from = ['<EMAIL>' => 'Sender'];
        $to = ['<EMAIL>' => 'Recipient'];
        $subject = 'Test Subject';
        $body = 'Test Body';
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(false);
        $this->mockProvider1->shouldReceive('send')->never();
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(false);
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $this->emailService->send($from, $to, [], [], $subject, $body);
        
        $this->assertFalse($result);
    }

    /**
     * Test sending an email using a template.
     *
     * @return void
     */
    public function testSendTemplate(): void
    {
        $from = ['<EMAIL>' => 'Sender'];
        $to = ['<EMAIL>' => 'Recipient'];
        $templateId = 1;
        $templateData = ['name' => 'John Doe'];
        
        $renderedTemplate = [
            'subject' => 'Hello John Doe',
            'body' => '<p>Welcome, John Doe!</p>',
            'type' => 'html',
        ];
        
        $this->mockTemplateService->shouldReceive('renderTemplate')
            ->with($templateId, $templateData, null)
            ->andReturn($renderedTemplate);
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with(
                $from,
                $to,
                [],
                [],
                $renderedTemplate['subject'],
                $renderedTemplate['body'],
                $renderedTemplate['type'],
                [],
                []
            )
            ->andReturn(true);
        
        $result = $this->emailService->sendTemplate(
            $from,
            $to,
            [],
            [],
            $templateId,
            $templateData
        );
        
        $this->assertTrue($result);
    }

    /**
     * Test sending an email using a template that doesn't exist.
     *
     * @return void
     */
    public function testSendTemplateWithNonExistentTemplate(): void
    {
        $from = ['<EMAIL>' => 'Sender'];
        $to = ['<EMAIL>' => 'Recipient'];
        $templateId = 999;
        $templateData = ['name' => 'John Doe'];
        
        $this->mockTemplateService->shouldReceive('renderTemplate')
            ->with($templateId, $templateData, null)
            ->andReturn(null);
        
        $this->mockProvider1->shouldReceive('send')->never();
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $this->emailService->sendTemplate(
            $from,
            $to,
            [],
            [],
            $templateId,
            $templateData
        );
        
        $this->assertFalse($result);
    }

    /**
     * Test sending an email using a template with no template service.
     *
     * @return void
     */
    public function testSendTemplateWithNoTemplateService(): void
    {
        $emailService = new ProviderEmailService(
            [$this->mockProvider1, $this->mockProvider2],
            null
        );
        
        $from = ['<EMAIL>' => 'Sender'];
        $to = ['<EMAIL>' => 'Recipient'];
        $templateId = 1;
        $templateData = ['name' => 'John Doe'];
        
        $this->mockProvider1->shouldReceive('send')->never();
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $emailService->sendTemplate(
            $from,
            $to,
            [],
            [],
            $templateId,
            $templateData
        );
        
        $this->assertFalse($result);
    }
}

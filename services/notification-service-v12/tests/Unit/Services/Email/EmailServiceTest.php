<?php

namespace Tests\Unit\Services\Email;

use App\Services\Email\EmailService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class EmailServiceTest extends TestCase
{
    /**
     * @var EmailService
     */
    protected $emailService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->emailService = new EmailService();
        
        // Mock the Mail facade
        Mail::fake();
        
        // Mock the Queue facade
        Queue::fake();
        
        // Mock the Http facade
        Http::fake([
            '*' => Http::response(['status' => 'success'], 200),
        ]);
    }

    /**
     * Test setting and getting configuration.
     *
     * @return void
     */
    public function testSetAndGetConfiguration(): void
    {
        $config = ['key' => 'value'];
        $this->emailService->setConfiguration($config);
        $this->assertEquals($config, $this->emailService->getConfiguration());
    }

    /**
     * Test setting and getting SMS configuration.
     *
     * @return void
     */
    public function testSetAndGetSMSConfiguration(): void
    {
        $config = ['key' => 'value'];
        $this->emailService->setSMSConfiguration($config);
        $this->assertEquals($config, $this->emailService->getSMSConfiguration());
    }

    /**
     * Test setting and getting merchant data.
     *
     * @return void
     */
    public function testSetAndGetMerchantData(): void
    {
        $merchant = ['name' => 'Test Merchant'];
        $this->emailService->setMerchantData($merchant);
        $this->assertEquals($merchant, $this->emailService->getMerchantData());
    }

    /**
     * Test setting and getting mobile number.
     *
     * @return void
     */
    public function testSetAndGetMobileNo(): void
    {
        $mobileNo = '1234567890';
        $this->emailService->setMobileNo($mobileNo);
        $this->assertEquals($mobileNo, $this->emailService->getMobileNo());
    }

    /**
     * Test setting and getting SMS message.
     *
     * @return void
     */
    public function testSetAndGetSMSMessage(): void
    {
        $message = 'Test message';
        $this->emailService->setSMSMessage($message);
        $this->assertEquals(urlencode($message), $this->emailService->getSMSMessage());
    }

    /**
     * Test setting and getting priority.
     *
     * @return void
     */
    public function testSetAndGetPriority(): void
    {
        $priority = EmailService::PRIORITY_HIGH_STORE_IN_DATABASE;
        $this->emailService->setPriority($priority);
        $this->assertEquals($priority, $this->emailService->getPriority());
    }

    /**
     * Test sending email with immediate priority.
     *
     * @return void
     */
    public function testSendEmailWithImmediatePriority(): void
    {
        // Set up test data
        $from = ['Test Sender' => '<EMAIL>'];
        $to = ['Test Recipient' => '<EMAIL>'];
        $subject = 'Test Subject';
        $body = 'Test Body';
        
        // Set priority to immediate
        $this->emailService->setPriority(EmailService::PRIORITY_SEND_IMMEDIATELY);
        
        // Send email
        $result = $this->emailService->sendmail($from, $to, [], [], $subject, $body);
        
        // Assert that the email was sent
        $this->assertTrue($result);
        
        // Assert that an email was sent
        Mail::assertSent(function ($mail) use ($to, $subject) {
            return $mail->hasTo(array_values($to)[0]) && 
                   $mail->subject === $subject;
        });
    }

    /**
     * Test sending email with database storage priority.
     *
     * @return void
     */
    public function testSendEmailWithDatabaseStoragePriority(): void
    {
        // Mock the DB facade
        DB::shouldReceive('table')
            ->with('email_queue')
            ->andReturnSelf();
        
        DB::shouldReceive('insertGetId')
            ->andReturn(1);
        
        // Set up test data
        $from = ['Test Sender' => '<EMAIL>'];
        $to = ['Test Recipient' => '<EMAIL>'];
        $subject = 'Test Subject';
        $body = 'Test Body';
        
        // Set priority to low (store in database)
        $this->emailService->setPriority(EmailService::PRIORITY_LOW_STORE_IN_DATABASE);
        
        // Send email
        $result = $this->emailService->sendmail($from, $to, [], [], $subject, $body);
        
        // Assert that the email was queued
        $this->assertTrue($result);
        
        // Assert that a job was pushed to the queue
        Queue::assertPushed(function ($job) {
            return $job->emailId === 1;
        });
    }

    /**
     * Test sending SMS with immediate priority.
     *
     * @return void
     */
    public function testSendSmsWithImmediatePriority(): void
    {
        // Set up test data
        $mobileNo = '1234567890';
        $message = 'Test message';
        $smsConfig = [
            'gateway_url' => 'https://example.com/sms',
            'sender_id' => 'TEST',
            'auth_key' => 'test_key',
        ];
        
        // Set mobile number and message
        $this->emailService->setMobileNo($mobileNo);
        $this->emailService->setSMSMessage($message);
        $this->emailService->setSMSConfiguration($smsConfig);
        
        // Send SMS
        $result = $this->emailService->sendmessage(EmailService::PRIORITY_SMS_IMMEDIATELY);
        
        // Assert that the SMS was sent
        $this->assertTrue($result);
        
        // Assert that an HTTP request was made to the SMS gateway
        Http::assertSent(function ($request) use ($smsConfig, $mobileNo, $message) {
            return $request->url() === $smsConfig['gateway_url'] &&
                   $request->method() === 'GET' &&
                   $request->data()['mobile'] === $mobileNo &&
                   $request->data()['message'] === urlencode($message);
        });
    }

    /**
     * Test sending SMS with database storage priority.
     *
     * @return void
     */
    public function testSendSmsWithDatabaseStoragePriority(): void
    {
        // Mock the DB facade
        DB::shouldReceive('table')
            ->with('sms_queue')
            ->andReturnSelf();
        
        DB::shouldReceive('insertGetId')
            ->andReturn(1);
        
        // Set up test data
        $mobileNo = '1234567890';
        $message = 'Test message';
        
        // Set mobile number and message
        $this->emailService->setMobileNo($mobileNo);
        $this->emailService->setSMSMessage($message);
        
        // Send SMS
        $result = $this->emailService->sendmessage(EmailService::PRIORITY_SMS_STORE_IN_DATABASE);
        
        // Assert that the SMS was queued
        $this->assertTrue($result);
        
        // Assert that a job was pushed to the queue
        Queue::assertPushed(function ($job) {
            return $job->smsId === 1;
        });
    }

    /**
     * Test that an exception is thrown when setting an empty SMS message.
     *
     * @return void
     */
    public function testSetSMSMessageThrowsExceptionWhenEmpty(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('SMS Message Empty');
        
        $this->emailService->setSMSMessage('');
    }
}

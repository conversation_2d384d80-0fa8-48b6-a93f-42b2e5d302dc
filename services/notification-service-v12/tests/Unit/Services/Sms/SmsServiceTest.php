<?php

namespace Tests\Unit\Services\Sms;

use App\Services\Sms\Providers\SmsProviderInterface;
use App\Services\Sms\SmsService;
use App\Services\Template\SmsTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class SmsServiceTest extends TestCase
{
    /**
     * @var SmsService
     */
    protected $smsService;

    /**
     * @var SmsProviderInterface|Mockery\MockInterface
     */
    protected $mockProvider1;

    /**
     * @var SmsProviderInterface|Mockery\MockInterface
     */
    protected $mockProvider2;

    /**
     * @var SmsTemplateService|Mockery\MockInterface
     */
    protected $mockTemplateService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockProvider1 = Mockery::mock(SmsProviderInterface::class);
        $this->mockProvider1->shouldReceive('getName')->andReturn('provider1');
        $this->mockProvider1->shouldReceive('getPriority')->andReturn(10);
        
        $this->mockProvider2 = Mockery::mock(SmsProviderInterface::class);
        $this->mockProvider2->shouldReceive('getName')->andReturn('provider2');
        $this->mockProvider2->shouldReceive('getPriority')->andReturn(20);
        
        $this->mockTemplateService = Mockery::mock(SmsTemplateService::class);
        
        $this->smsService = new SmsService(
            [$this->mockProvider1, $this->mockProvider2],
            $this->mockTemplateService
        );
    }

    /**
     * Clean up the testing environment before the next test.
     *
     * @return void
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test getting all providers.
     *
     * @return void
     */
    public function testGetProviders(): void
    {
        $providers = $this->smsService->getProviders();
        
        $this->assertCount(2, $providers);
        $this->assertSame($this->mockProvider1, $providers[0]);
        $this->assertSame($this->mockProvider2, $providers[1]);
    }

    /**
     * Test getting available providers.
     *
     * @return void
     */
    public function testGetAvailableProviders(): void
    {
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(false);
        
        $availableProviders = $this->smsService->getAvailableProviders();
        
        $this->assertCount(1, $availableProviders);
        $this->assertSame($this->mockProvider1, $availableProviders[0]);
    }

    /**
     * Test sending an SMS with the first provider succeeding.
     *
     * @return void
     */
    public function testSendWithFirstProviderSucceeding(): void
    {
        $to = '+**********';
        $message = 'Test message';
        $from = 'SENDER';
        $options = ['test' => true];
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with($to, $message, $from, $options)
            ->andReturn(true);
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $this->smsService->send($to, $message, $from, $options);
        
        $this->assertTrue($result);
    }

    /**
     * Test sending an SMS with the first provider failing and the second succeeding.
     *
     * @return void
     */
    public function testSendWithFirstProviderFailingAndSecondSucceeding(): void
    {
        $to = '+**********';
        $message = 'Test message';
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with($to, $message, null, [])
            ->andReturn(false);
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('send')
            ->with($to, $message, null, [])
            ->andReturn(true);
        
        $result = $this->smsService->send($to, $message);
        
        $this->assertTrue($result);
    }

    /**
     * Test sending an SMS with all providers failing.
     *
     * @return void
     */
    public function testSendWithAllProvidersFailing(): void
    {
        $to = '+**********';
        $message = 'Test message';
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with($to, $message, null, [])
            ->andReturn(false);
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('send')
            ->with($to, $message, null, [])
            ->andReturn(false);
        
        $result = $this->smsService->send($to, $message);
        
        $this->assertFalse($result);
    }

    /**
     * Test sending an SMS with no available providers.
     *
     * @return void
     */
    public function testSendWithNoAvailableProviders(): void
    {
        $to = '+**********';
        $message = 'Test message';
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(false);
        $this->mockProvider1->shouldReceive('send')->never();
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(false);
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $this->smsService->send($to, $message);
        
        $this->assertFalse($result);
    }

    /**
     * Test sending bulk SMS.
     *
     * @return void
     */
    public function testSendBulk(): void
    {
        $to = ['+**********', '+**********'];
        $message = 'Test message';
        $from = 'SENDER';
        $options = ['test' => true];
        
        $bulkResults = [
            '+**********' => true,
            '+**********' => true,
        ];
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('sendBulk')
            ->with($to, $message, $from, $options)
            ->andReturn($bulkResults);
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('sendBulk')->never();
        
        $result = $this->smsService->sendBulk($to, $message, $from, $options);
        
        $this->assertSame($bulkResults, $result);
    }

    /**
     * Test sending bulk SMS with the first provider failing and the second succeeding.
     *
     * @return void
     */
    public function testSendBulkWithFirstProviderFailingAndSecondSucceeding(): void
    {
        $to = ['+**********', '+**********'];
        $message = 'Test message';
        
        $bulkResults = [
            '+**********' => true,
            '+**********' => true,
        ];
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('sendBulk')
            ->with($to, $message, null, [])
            ->andThrow(new \Exception('Failed to send bulk SMS'));
        
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('sendBulk')
            ->with($to, $message, null, [])
            ->andReturn($bulkResults);
        
        $result = $this->smsService->sendBulk($to, $message);
        
        $this->assertSame($bulkResults, $result);
    }

    /**
     * Test sending an SMS using a template.
     *
     * @return void
     */
    public function testSendTemplate(): void
    {
        $to = '+**********';
        $templateId = 1;
        $templateData = ['name' => 'John Doe', 'order_id' => '12345'];
        $from = 'SENDER';
        $options = ['test' => true];
        
        $renderedTemplate = 'Hello John Doe, your order 12345 has been confirmed.';
        
        $this->mockTemplateService->shouldReceive('renderTemplate')
            ->with($templateId, $templateData, null)
            ->andReturn($renderedTemplate);
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('send')
            ->with($to, $renderedTemplate, $from, $options)
            ->andReturn(true);
        
        $result = $this->smsService->sendTemplate(
            $to,
            $templateId,
            $templateData,
            null,
            $from,
            $options
        );
        
        $this->assertTrue($result);
    }

    /**
     * Test sending an SMS using a template that doesn't exist.
     *
     * @return void
     */
    public function testSendTemplateWithNonExistentTemplate(): void
    {
        $to = '+**********';
        $templateId = 999;
        $templateData = ['name' => 'John Doe', 'order_id' => '12345'];
        
        $this->mockTemplateService->shouldReceive('renderTemplate')
            ->with($templateId, $templateData, null)
            ->andReturn(null);
        
        $this->mockProvider1->shouldReceive('send')->never();
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $this->smsService->sendTemplate(
            $to,
            $templateId,
            $templateData
        );
        
        $this->assertFalse($result);
    }

    /**
     * Test sending an SMS using a template with no template service.
     *
     * @return void
     */
    public function testSendTemplateWithNoTemplateService(): void
    {
        $smsService = new SmsService(
            [$this->mockProvider1, $this->mockProvider2],
            null
        );
        
        $to = '+**********';
        $templateId = 1;
        $templateData = ['name' => 'John Doe', 'order_id' => '12345'];
        
        $this->mockProvider1->shouldReceive('send')->never();
        $this->mockProvider2->shouldReceive('send')->never();
        
        $result = $smsService->sendTemplate(
            $to,
            $templateId,
            $templateData
        );
        
        $this->assertFalse($result);
    }

    /**
     * Test sending bulk SMS using a template.
     *
     * @return void
     */
    public function testSendBulkTemplate(): void
    {
        $to = ['+**********', '+**********'];
        $templateId = 1;
        $templateData = ['name' => 'John Doe', 'order_id' => '12345'];
        $from = 'SENDER';
        $options = ['test' => true];
        
        $renderedTemplate = 'Hello John Doe, your order 12345 has been confirmed.';
        
        $bulkResults = [
            '+**********' => true,
            '+**********' => true,
        ];
        
        $this->mockTemplateService->shouldReceive('renderTemplate')
            ->with($templateId, $templateData, null)
            ->andReturn($renderedTemplate);
        
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('sendBulk')
            ->with($to, $renderedTemplate, $from, $options)
            ->andReturn($bulkResults);
        
        $result = $this->smsService->sendBulkTemplate(
            $to,
            $templateId,
            $templateData,
            null,
            $from,
            $options
        );
        
        $this->assertSame($bulkResults, $result);
    }
}

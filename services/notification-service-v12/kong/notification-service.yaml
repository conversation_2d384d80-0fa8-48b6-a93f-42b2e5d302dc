_format_version: "2.1"
_transform: true

services:
  - name: notification-service
    url: http://notification-service-v12:8000
    routes:
      - name: notification-service-route
        paths:
          - /v2/notification
        strip_path: true
        protocols:
          - http
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
            - nbf
          key_claim_name: kid
          secret_is_base64: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: 1.0.0
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: 1.0.0
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true
      - name: http-log
        config:
          http_endpoint: http://logging-service:8000/api/v1/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          headers:
            X-Service: notification-service
          custom_fields_by_lua:
            service_name: "return 'notification-service'"
            environment: "return os.getenv('APP_ENV') or 'development'"
      - name: key-auth
        config:
          key_names:
            - apikey
          key_in_body: false
          hide_credentials: true
      - name: acl
        config:
          allow:
            - notification-service-consumer
            - admin-consumer
      - name: ip-restriction
        config:
          allow:
            - 127.0.0.1
            - 10.0.0.0/8
            - **********/12
            - ***********/16
      - name: request-size-limiting
        config:
          allowed_payload_size: 10
      - name: response-ratelimiting
        config:
          limits:
            sms:
              minute: 10
              hour: 100
              day: 1000
            email:
              minute: 20
              hour: 200
              day: 2000
          policy: local
          fault_tolerant: true
          redis_timeout: 2000
          redis_database: 0
          hide_client_headers: false
      - name: proxy-cache
        config:
          content_type:
            - application/json
          cache_ttl: 30
          strategy: memory
      - name: statsd
        config:
          host: statsd-exporter
          port: 9125
          prefix: notification-service
      - name: datadog
        config:
          host: datadog-agent
          port: 8125
          prefix: notification-service
      - name: zipkin
        config:
          http_endpoint: http://zipkin:9411/api/v2/spans
          sample_ratio: 0.01
          tags_header: Zipkin-Tags
          default_service_name: notification-service
      - name: pre-function
        config:
          access:
            - |
              local function getCorrelationId()
                local correlationId = kong.request.get_header("X-Correlation-ID")
                if not correlationId then
                  correlationId = string.gsub(uuid(), "-", "")
                end
                return correlationId
              end
              
              local correlationId = getCorrelationId()
              kong.service.request.set_header("X-Correlation-ID", correlationId)
              kong.response.set_header("X-Correlation-ID", correlationId)
      - name: post-function
        config:
          access:
            - |
              local function logRequest()
                local req = kong.request
                local res = kong.response
                local startTime = ngx.req.start_time()
                local endTime = ngx.now()
                local latency = endTime - startTime
                
                local logData = {
                  service = "notification-service",
                  method = req.get_method(),
                  uri = req.get_path(),
                  status = res.get_status(),
                  latency = latency,
                  client_ip = req.get_forwarded_ip() or req.get_ip(),
                  timestamp = ngx.time(),
                  correlation_id = req.get_header("X-Correlation-ID") or "unknown"
                }
                
                ngx.log(ngx.INFO, cjson.encode(logData))
              end
              
              logRequest()

consumers:
  - username: notification-service-consumer
    keyauth_credentials:
      - key: NOTIFICATION_SERVICE_API_KEY
    acls:
      - group: notification-service-consumer
  - username: admin-consumer
    keyauth_credentials:
      - key: ADMIN_API_KEY
    acls:
      - group: admin-consumer

_format_version: "2.1"
_transform: true

services:
  # Email Template Sets
  - name: notification-email-template-sets
    url: http://notification-service:8000/api/v2/notification/email-templates/sets
    routes:
      - name: notification-email-template-sets-route
        paths:
          - /v2/notification/email-templates/sets
        methods:
          - GET
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Template Set by ID
  - name: notification-email-template-set-by-id
    url: http://notification-service:8000/api/v2/notification/email-templates/sets
    routes:
      - name: notification-email-template-set-by-id-route
        paths:
          - /v2/notification/email-templates/sets/(?<id>\d+)$
        methods:
          - GET
          - PUT
          - DELETE
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Templates by Set ID
  - name: notification-email-templates-by-set-id
    url: http://notification-service:8000/api/v2/notification/email-templates/sets
    routes:
      - name: notification-email-templates-by-set-id-route
        paths:
          - /v2/notification/email-templates/sets/(?<setId>\d+)/templates$
        methods:
          - GET
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Templates
  - name: notification-email-templates
    url: http://notification-service:8000/api/v2/notification/email-templates/templates
    routes:
      - name: notification-email-templates-route
        paths:
          - /v2/notification/email-templates/templates$
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Template by ID
  - name: notification-email-template-by-id
    url: http://notification-service:8000/api/v2/notification/email-templates/templates
    routes:
      - name: notification-email-template-by-id-route
        paths:
          - /v2/notification/email-templates/templates/(?<id>\d+)$
        methods:
          - GET
          - PUT
          - DELETE
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Template Preview
  - name: notification-email-template-preview
    url: http://notification-service:8000/api/v2/notification/email-templates/templates
    routes:
      - name: notification-email-template-preview-route
        paths:
          - /v2/notification/email-templates/templates/(?<id>\d+)/preview$
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Template Variables
  - name: notification-email-template-variables
    url: http://notification-service:8000/api/v2/notification/email-templates/variables
    routes:
      - name: notification-email-template-variables-route
        paths:
          - /v2/notification/email-templates/variables$
        methods:
          - GET
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Template Variable by ID
  - name: notification-email-template-variable-by-id
    url: http://notification-service:8000/api/v2/notification/email-templates/variables
    routes:
      - name: notification-email-template-variable-by-id-route
        paths:
          - /v2/notification/email-templates/variables/(?<id>\d+)$
        methods:
          - PUT
          - DELETE
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Sets
  - name: notification-sms-sets
    url: http://notification-service:8000/api/v2/notification/sms-templates/sets
    routes:
      - name: notification-sms-sets-route
        paths:
          - /v2/notification/sms-templates/sets$
        methods:
          - GET
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Set by ID
  - name: notification-sms-set-by-id
    url: http://notification-service:8000/api/v2/notification/sms-templates/sets
    routes:
      - name: notification-sms-set-by-id-route
        paths:
          - /v2/notification/sms-templates/sets/(?<id>\d+)$
        methods:
          - GET
          - PUT
          - DELETE
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Templates by Set ID
  - name: notification-sms-templates-by-set-id
    url: http://notification-service:8000/api/v2/notification/sms-templates/sets
    routes:
      - name: notification-sms-templates-by-set-id-route
        paths:
          - /v2/notification/sms-templates/sets/(?<setId>\d+)/templates$
        methods:
          - GET
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Templates
  - name: notification-sms-templates
    url: http://notification-service:8000/api/v2/notification/sms-templates/templates
    routes:
      - name: notification-sms-templates-route
        paths:
          - /v2/notification/sms-templates/templates$
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Template by ID
  - name: notification-sms-template-by-id
    url: http://notification-service:8000/api/v2/notification/sms-templates/templates
    routes:
      - name: notification-sms-template-by-id-route
        paths:
          - /v2/notification/sms-templates/templates/(?<id>\d+)$
        methods:
          - GET
          - PUT
          - DELETE
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Template Approve
  - name: notification-sms-template-approve
    url: http://notification-service:8000/api/v2/notification/sms-templates/templates
    routes:
      - name: notification-sms-template-approve-route
        paths:
          - /v2/notification/sms-templates/templates/(?<id>\d+)/approve$
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Template Preview
  - name: notification-sms-template-preview
    url: http://notification-service:8000/api/v2/notification/sms-templates/templates
    routes:
      - name: notification-sms-template-preview-route
        paths:
          - /v2/notification/sms-templates/templates/(?<id>\d+)/preview$
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Template Variables
  - name: notification-sms-template-variables
    url: http://notification-service:8000/api/v2/notification/sms-templates/variables
    routes:
      - name: notification-sms-template-variables-route
        paths:
          - /v2/notification/sms-templates/variables$
        methods:
          - GET
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

_format_version: "2.1"
_transform: true

services:
  # Email Send
  - name: notification-email-send
    url: http://notification-service:8000/api/v2/notification/email-v2/send
    routes:
      - name: notification-email-send-route
        paths:
          - /v2/notification/email-v2/send
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # Email Send Template
  - name: notification-email-send-template
    url: http://notification-service:8000/api/v2/notification/email-v2/send-template
    routes:
      - name: notification-email-send-template-route
        paths:
          - /v2/notification/email-v2/send-template
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Send
  - name: notification-sms-send
    url: http://notification-service:8000/api/v2/notification/sms-v2/send
    routes:
      - name: notification-sms-send-route
        paths:
          - /v2/notification/sms-v2/send
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Send Bulk
  - name: notification-sms-send-bulk
    url: http://notification-service:8000/api/v2/notification/sms-v2/send-bulk
    routes:
      - name: notification-sms-send-bulk-route
        paths:
          - /v2/notification/sms-v2/send-bulk
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 5
          hour: 500
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Send Template
  - name: notification-sms-send-template
    url: http://notification-service:8000/api/v2/notification/sms-v2/send-template
    routes:
      - name: notification-sms-send-template-route
        paths:
          - /v2/notification/sms-v2/send-template
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 10
          hour: 1000
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

  # SMS Send Bulk Template
  - name: notification-sms-send-bulk-template
    url: http://notification-service:8000/api/v2/notification/sms-v2/send-bulk-template
    routes:
      - name: notification-sms-send-bulk-template-route
        paths:
          - /v2/notification/sms-v2/send-bulk-template
        methods:
          - POST
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          second: 5
          hour: 500
          policy: local
      - name: jwt
        config:
          claims_to_verify:
            - exp
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service: notification-service
              - X-Service-Version: "1.0.0"
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true

<?php

use App\Http\Controllers\Api\V2\EmailController;
use App\Http\Controllers\Api\V2\EmailTemplateController;
use App\Http\Controllers\Api\V2\NotificationController;
use App\Http\Controllers\Api\V2\SmsController;
use App\Http\Controllers\Api\V2\SmsTemplateController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->group(function () {
    // Notification routes
    Route::prefix('notification')->group(function () {
        // Public routes
        Route::get('health', function () {
            return response()->json([
                'status' => 'ok',
                'service' => 'notification-service',
                'version' => config('app.version', '1.0.0'),
                'timestamp' => now()->toIso8601String(),
            ]);
        });

        // Protected routes
        Route::middleware(['auth:sanctum'])->group(function () {
            // Legacy Email routes (for backward compatibility)
            Route::post('email', [NotificationController::class, 'sendEmail']);
            Route::get('email/queue', [NotificationController::class, 'getEmailQueueStatus']);

            // New Email routes
            Route::prefix('email-v2')->group(function () {
                Route::post('send', [EmailController::class, 'send']);
                Route::post('send-template', [EmailController::class, 'sendTemplate']);
            });

            // Legacy SMS routes (for backward compatibility)
            Route::post('sms', [NotificationController::class, 'sendSms']);
            Route::get('sms/queue', [NotificationController::class, 'getSmsQueueStatus']);

            // New SMS routes
            Route::prefix('sms-v2')->group(function () {
                Route::post('send', [SmsController::class, 'send']);
                Route::post('send-bulk', [SmsController::class, 'sendBulk']);
                Route::post('send-template', [SmsController::class, 'sendTemplate']);
                Route::post('send-bulk-template', [SmsController::class, 'sendBulkTemplate']);
            });

            // Email template routes
            Route::prefix('email-templates')->group(function () {
                // Email template sets
                Route::get('sets', [EmailTemplateController::class, 'getAllSets']);
                Route::get('sets/{id}', [EmailTemplateController::class, 'getSetById']);
                Route::post('sets', [EmailTemplateController::class, 'createSet']);
                Route::put('sets/{id}', [EmailTemplateController::class, 'updateSet']);
                Route::delete('sets/{id}', [EmailTemplateController::class, 'deleteSet']);

                // Email templates
                Route::get('sets/{setId}/templates', [EmailTemplateController::class, 'getTemplatesBySetId']);
                Route::get('templates/{id}', [EmailTemplateController::class, 'getTemplateById']);
                Route::post('templates', [EmailTemplateController::class, 'createTemplate']);
                Route::put('templates/{id}', [EmailTemplateController::class, 'updateTemplate']);
                Route::delete('templates/{id}', [EmailTemplateController::class, 'deleteTemplate']);
                Route::post('templates/{id}/preview', [EmailTemplateController::class, 'previewTemplate']);

                // Email template variables
                Route::get('variables', [EmailTemplateController::class, 'getAllVariables']);
                Route::post('variables', [EmailTemplateController::class, 'createVariable']);
                Route::put('variables/{id}', [EmailTemplateController::class, 'updateVariable']);
                Route::delete('variables/{id}', [EmailTemplateController::class, 'deleteVariable']);
            });

            // SMS template routes
            Route::prefix('sms-templates')->group(function () {
                // SMS sets
                Route::get('sets', [SmsTemplateController::class, 'getAllSets']);
                Route::get('sets/{id}', [SmsTemplateController::class, 'getSetById']);
                Route::post('sets', [SmsTemplateController::class, 'createSet']);
                Route::put('sets/{id}', [SmsTemplateController::class, 'updateSet']);
                Route::delete('sets/{id}', [SmsTemplateController::class, 'deleteSet']);

                // SMS templates
                Route::get('sets/{setId}/templates', [SmsTemplateController::class, 'getTemplatesBySetId']);
                Route::get('templates/{id}', [SmsTemplateController::class, 'getTemplateById']);
                Route::post('templates', [SmsTemplateController::class, 'createTemplate']);
                Route::put('templates/{id}', [SmsTemplateController::class, 'updateTemplate']);
                Route::delete('templates/{id}', [SmsTemplateController::class, 'deleteTemplate']);
                Route::post('templates/{id}/approve', [SmsTemplateController::class, 'approveTemplate']);
                Route::post('templates/{id}/preview', [SmsTemplateController::class, 'previewTemplate']);

                // SMS template variables
                Route::get('variables', [SmsTemplateController::class, 'getAllVariables']);
            });
        });
    });
});

#!/bin/bash

# OneFoodDialer 2025 - Database Setup Script
# This script sets up the database for all microservices

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="onefooddialer"
DB_USER="root"
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_PASSWORD="YOUR_PASSWORD_HERE"

# Service directories
SERVICES_DIR="./services"
PROJECT_ROOT=$(pwd)

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if MySQL is running
check_mysql() {
    log "Checking MySQL connection..."
    
    if ! command -v mysql &> /dev/null; then
        error "MySQL client not found. Please install MySQL."
        exit 1
    fi
    
    if ! mysqladmin ping -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" --silent 2>/dev/null; then
        error "Cannot connect to MySQL server. Please ensure MySQL is running and credentials are correct."
        exit 1
    fi
    
    success "MySQL connection established"
}

# Create database if it doesn't exist
create_database() {
    log "Creating database '$DB_NAME' if it doesn't exist..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        success "Database '$DB_NAME' is ready"
    else
        error "Failed to create database '$DB_NAME'"
        exit 1
    fi
}

# Update .env files for all services
update_env_files() {
    log "Updating .env files for all services..."
    
    local services=(
        "auth-service-v12"
        "quickserve-service-v12"
        "customer-service-v12"
        "payment-service-v12"
        "kitchen-service-v12"
        "delivery-service-v12"
        "analytics-service-v12"
        "admin-service-v12"
        "notification-service-v12"
        "catalogue-service-v12"
        "meal-service-v12"
        "subscription-service-v12"
    )
    
    for service in "${services[@]}"; do
        local service_dir="$SERVICES_DIR/$service"
        local env_file="$service_dir/.env"
        
        if [ -d "$service_dir" ]; then
            log "Updating .env for $service..."
            
            # Create .env from .env.example if it doesn't exist
            if [ ! -f "$env_file" ] && [ -f "$service_dir/.env.example" ]; then
                cp "$service_dir/.env.example" "$env_file"
            fi
            
            # Update database configuration
            if [ -f "$env_file" ]; then
                # Update or add database configuration
                sed -i.bak "s/^DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" "$env_file" 2>/dev/null || echo "DB_DATABASE=$DB_NAME" >> "$env_file"
                sed -i.bak "s/^DB_USERNAME=.*/DB_USERNAME=$DB_USER/" "$env_file" 2>/dev/null || echo "DB_USERNAME=$DB_USER" >> "$env_file"
                sed -i.bak "s/^DB_HOST=.*/DB_HOST=$DB_HOST/" "$env_file" 2>/dev/null || echo "DB_HOST=$DB_HOST" >> "$env_file"
                sed -i.bak "s/^DB_PORT=.*/DB_PORT=$DB_PORT/" "$env_file" 2>/dev/null || echo "DB_PORT=$DB_PORT" >> "$env_file"
                
                # Remove backup files
                rm -f "$env_file.bak"
                
                success "Updated .env for $service"
            else
                warning "No .env file found for $service"
            fi
        else
            warning "Service directory not found: $service_dir"
        fi
    done
}

# Run migrations for all services
run_migrations() {
    log "Running migrations for all services..."
    
    local services=(
        "auth-service-v12"
        "quickserve-service-v12"
        "customer-service-v12"
        "payment-service-v12"
        "kitchen-service-v12"
        "delivery-service-v12"
        "analytics-service-v12"
        "admin-service-v12"
        "notification-service-v12"
        "catalogue-service-v12"
        "meal-service-v12"
        "subscription-service-v12"
    )
    
    for service in "${services[@]}"; do
        local service_dir="$SERVICES_DIR/$service"
        
        if [ -d "$service_dir" ] && [ -f "$service_dir/artisan" ]; then
            log "Running migrations for $service..."
            
            cd "$service_dir"
            
            # Install composer dependencies if needed
            if [ ! -d "vendor" ]; then
                log "Installing composer dependencies for $service..."
                composer install --no-interaction --prefer-dist --optimize-autoloader
            fi
            
            # Run migrations
            if php artisan migrate --force 2>/dev/null; then
                success "Migrations completed for $service"
            else
                warning "Migration failed for $service (this might be normal if tables already exist)"
            fi
            
            cd "$PROJECT_ROOT"
        else
            warning "Artisan not found for $service, skipping migrations"
        fi
    done
}

# Seed database with sample data
seed_database() {
    log "Seeding database with sample data..."
    
    local services_to_seed=(
        "delivery-service-v12"
        "customer-service-v12"
        "quickserve-service-v12"
    )
    
    for service in "${services_to_seed[@]}"; do
        local service_dir="$SERVICES_DIR/$service"
        
        if [ -d "$service_dir" ] && [ -f "$service_dir/artisan" ]; then
            log "Seeding data for $service..."
            
            cd "$service_dir"
            
            # Run seeders
            if php artisan db:seed --force 2>/dev/null; then
                success "Database seeded for $service"
            else
                warning "Seeding failed for $service (this might be normal if data already exists)"
            fi
            
            cd "$PROJECT_ROOT"
        fi
    done
}

# Create health check endpoints
create_health_endpoints() {
    log "Ensuring health check endpoints exist..."
    
    local services=(
        "auth-service-v12"
        "quickserve-service-v12"
        "customer-service-v12"
        "payment-service-v12"
        "kitchen-service-v12"
        "delivery-service-v12"
        "analytics-service-v12"
        "admin-service-v12"
        "notification-service-v12"
        "catalogue-service-v12"
        "meal-service-v12"
        "subscription-service-v12"
    )
    
    for service in "${services[@]}"; do
        local service_dir="$SERVICES_DIR/$service"
        local routes_file="$service_dir/routes/api.php"
        
        if [ -f "$routes_file" ]; then
            # Check if health route exists
            if ! grep -q "Route::get.*health" "$routes_file"; then
                log "Adding health check route to $service..."
                
                # Add health check route
                echo "" >> "$routes_file"
                echo "// Health check endpoint" >> "$routes_file"
                echo "Route::get('/health', function () {" >> "$routes_file"
                echo "    return response()->json([" >> "$routes_file"
                echo "        'status' => 'healthy'," >> "$routes_file"
                echo "        'service' => '$service'," >> "$routes_file"
                echo "        'timestamp' => now()->toISOString()," >> "$routes_file"
                echo "        'version' => '2.0'" >> "$routes_file"
                echo "    ]);" >> "$routes_file"
                echo "});" >> "$routes_file"
                
                success "Health check route added to $service"
            fi
        fi
    done
}

# Verify database setup
verify_setup() {
    log "Verifying database setup..."
    
    # Check if database exists and has tables
    local table_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "SHOW TABLES;" 2>/dev/null | wc -l)
    
    if [ $table_count -gt 1 ]; then
        success "Database setup verified: $((table_count - 1)) tables found"
    else
        warning "Database setup might be incomplete: only $((table_count - 1)) tables found"
    fi
    
    # Test a few key tables
    local key_tables=("users" "orders" "customers" "delivery_persons")
    
    for table in "${key_tables[@]}"; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "DESCRIBE $table;" >/dev/null 2>&1; then
            success "Table '$table' exists and is accessible"
        else
            warning "Table '$table' not found or not accessible"
        fi
    done
}

# Main setup function
main() {
    log "Starting OneFoodDialer 2025 Database Setup"
    echo ""
    
    # Run setup steps
    check_mysql
    create_database
    update_env_files
    run_migrations
    seed_database
    create_health_endpoints
    verify_setup
    
    echo ""
    success "Database setup completed successfully!"
    echo ""
    log "Next steps:"
    echo "  1. Run './service-orchestrator.sh start' to start all services"
    echo "  2. Access the frontend at http://localhost:3000"
    echo "  3. Check service status at http://localhost:3000/(microfrontend-v2)"
    echo ""
}

# Show help
show_help() {
    echo "OneFoodDialer 2025 Database Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     Run complete database setup (default)"
    echo "  migrate   Run migrations only"
    echo "  seed      Run seeders only"
    echo "  verify    Verify database setup"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 migrate"
    echo "  $0 seed"
}

# Handle command line arguments
case "${1:-setup}" in
    setup)
        main
        ;;
    migrate)
        check_mysql
        create_database
        update_env_files
        run_migrations
        ;;
    seed)
        check_mysql
        seed_database
        ;;
    verify)
        check_mysql
        verify_setup
        ;;
    help|*)
        show_help
        ;;
esac

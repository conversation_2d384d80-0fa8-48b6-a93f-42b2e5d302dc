#!/bin/bash

# OneFoodDialer 2025 - Service Orchestrator
# This script manages all microservices and frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service configuration
declare -A SERVICES=(
    ["auth-service-v12"]="8101"
    ["quickserve-service-v12"]="8102"
    ["customer-service-v12"]="8103"
    ["payment-service-v12"]="8104"
    ["kitchen-service-v12"]="8105"
    ["delivery-service-v12"]="8106"
    ["analytics-service-v12"]="8107"
    ["admin-service-v12"]="8108"
    ["notification-service-v12"]="8109"
    ["catalogue-service-v12"]="8110"
    ["meal-service-v12"]="8111"
    ["subscription-service-v12"]="8112"
)

FRONTEND_PORT=3000
PROJECT_ROOT=$(pwd)
SERVICES_DIR="$PROJECT_ROOT/services"
FRONTEND_DIR="$PROJECT_ROOT/frontend-shadcn"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# Kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        log "Killing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 2
    fi
}

# Health check for service
health_check() {
    local service=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log "Health checking $service on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "http://localhost:$port/api/v2/health" >/dev/null 2>&1; then
            success "$service is healthy (attempt $attempt)"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "$service health check failed after $max_attempts attempts"
            return 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
}

# Start a single service
start_service() {
    local service=$1
    local port=$2
    local service_dir="$SERVICES_DIR/$service"
    
    if [ ! -d "$service_dir" ]; then
        error "Service directory not found: $service_dir"
        return 1
    fi
    
    log "Starting $service on port $port..."
    
    # Kill existing process on port
    kill_port $port
    
    # Start service in background
    cd "$service_dir"
    
    # Check if composer dependencies are installed
    if [ ! -d "vendor" ]; then
        log "Installing composer dependencies for $service..."
        composer install --no-interaction --prefer-dist --optimize-autoloader
    fi
    
    # Run migrations
    log "Running migrations for $service..."
    php artisan migrate --force 2>/dev/null || warning "Migration failed for $service"
    
    # Start the service
    log "Launching $service server..."
    nohup php artisan serve --host=0.0.0.0 --port=$port > "../logs/${service}.log" 2>&1 &
    echo $! > "../pids/${service}.pid"
    
    cd "$PROJECT_ROOT"
    
    # Wait a moment for service to start
    sleep 3
    
    # Health check
    if health_check "$service" "$port"; then
        success "$service started successfully on port $port"
        return 0
    else
        error "Failed to start $service"
        return 1
    fi
}

# Start frontend
start_frontend() {
    log "Starting frontend on port $FRONTEND_PORT..."
    
    # Kill existing process on port
    kill_port $FRONTEND_PORT
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing npm dependencies for frontend..."
        npm install
    fi
    
    # Start frontend in background
    log "Launching frontend server..."
    nohup npm run dev > "../logs/frontend.log" 2>&1 &
    echo $! > "../pids/frontend.pid"
    
    cd "$PROJECT_ROOT"
    
    # Wait for frontend to start
    sleep 5
    
    # Check if frontend is running
    if curl -f -s "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1; then
        success "Frontend started successfully on port $FRONTEND_PORT"
        return 0
    else
        error "Failed to start frontend"
        return 1
    fi
}

# Create necessary directories
setup_directories() {
    log "Setting up directories..."
    mkdir -p logs pids
    success "Directories created"
}

# Database setup
setup_database() {
    log "Setting up database..."
    
    # Check if MySQL is running
    if ! pgrep -x "mysqld" > /dev/null; then
        error "MySQL is not running. Please start MySQL first."
        return 1
    fi
    
    # Create database if it doesn't exist
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS onefooddialer_2025;" 2>/dev/null || true
    
    success "Database setup completed"
}

# Start all services
start_all() {
    log "Starting OneFoodDialer 2025 - All Services"
    
    setup_directories
    setup_database
    
    local failed_services=()
    
    # Start all backend services
    for service in "${!SERVICES[@]}"; do
        if start_service "$service" "${SERVICES[$service]}"; then
            success "$service is running"
        else
            error "$service failed to start"
            failed_services+=("$service")
        fi
    done
    
    # Start frontend
    if start_frontend; then
        success "Frontend is running"
    else
        error "Frontend failed to start"
        failed_services+=("frontend")
    fi
    
    # Summary
    echo ""
    log "=== SERVICE STARTUP SUMMARY ==="
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        success "All services started successfully!"
        echo ""
        log "🌐 Frontend: http://localhost:$FRONTEND_PORT"
        log "📊 Service Dashboard: http://localhost:$FRONTEND_PORT/(microfrontend-v2)"
        echo ""
        log "Backend Services:"
        for service in "${!SERVICES[@]}"; do
            echo "  ✅ $service: http://localhost:${SERVICES[$service]}"
        done
    else
        error "Failed to start ${#failed_services[@]} service(s): ${failed_services[*]}"
        return 1
    fi
}

# Stop all services
stop_all() {
    log "Stopping all services..."
    
    # Stop frontend
    if [ -f "pids/frontend.pid" ]; then
        local pid=$(cat pids/frontend.pid)
        kill -9 $pid 2>/dev/null || true
        rm -f pids/frontend.pid
        log "Frontend stopped"
    fi
    
    # Stop all backend services
    for service in "${!SERVICES[@]}"; do
        if [ -f "pids/${service}.pid" ]; then
            local pid=$(cat "pids/${service}.pid")
            kill -9 $pid 2>/dev/null || true
            rm -f "pids/${service}.pid"
            log "$service stopped"
        fi
        
        # Also kill by port
        kill_port "${SERVICES[$service]}"
    done
    
    success "All services stopped"
}

# Status check
status() {
    log "Checking service status..."
    echo ""
    
    # Check frontend
    if check_port $FRONTEND_PORT; then
        echo "❌ Frontend (port $FRONTEND_PORT): Not running"
    else
        echo "✅ Frontend (port $FRONTEND_PORT): Running"
    fi
    
    # Check backend services
    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        if check_port $port; then
            echo "❌ $service (port $port): Not running"
        else
            if curl -f -s "http://localhost:$port/api/v2/health" >/dev/null 2>&1; then
                echo "✅ $service (port $port): Running & Healthy"
            else
                echo "⚠️  $service (port $port): Running but unhealthy"
            fi
        fi
    done
}

# Show help
show_help() {
    echo "OneFoodDialer 2025 Service Orchestrator"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services and frontend"
    echo "  stop      Stop all services and frontend"
    echo "  restart   Restart all services and frontend"
    echo "  status    Show status of all services"
    echo "  logs      Show logs for all services"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 status"
    echo "  $0 stop"
}

# Show logs
show_logs() {
    log "Recent logs from all services:"
    echo ""
    
    if [ -f "logs/frontend.log" ]; then
        echo "=== FRONTEND LOGS ==="
        tail -n 10 logs/frontend.log
        echo ""
    fi
    
    for service in "${!SERVICES[@]}"; do
        if [ -f "logs/${service}.log" ]; then
            echo "=== ${service^^} LOGS ==="
            tail -n 5 "logs/${service}.log"
            echo ""
        fi
    done
}

# Main script logic
case "${1:-help}" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        stop_all
        sleep 2
        start_all
        ;;
    status)
        status
        ;;
    logs)
        show_logs
        ;;
    help|*)
        show_help
        ;;
esac

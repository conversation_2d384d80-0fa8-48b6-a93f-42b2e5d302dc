<?php
/**
 * Generate a JWT token for QuickServe initialization
 */

// Define application path
define('APPLICATION_PATH', realpath(__DIR__));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create a JWT token with all required claims
$header = [
    'alg' => 'HS256',
    'typ' => 'JWT'
];

// Generate a unique token ID
$tokenId = bin2hex(random_bytes(16));

// Set current time
$issuedAt = time();
$expiresAt = $issuedAt + 31536000; // 1 year

// Create the token payload with all required claims
$payload = [
    // Registered claims
    'iss' => 'tenant.cubeonebiz.com',    // Issuer
    'sub' => 'system',                   // Subject
    'aud' => 'tenant-api',               // Audience
    'exp' => $expiresAt,                 // Expiration Time
    'nbf' => $issuedAt,                  // Not Before
    'iat' => $issuedAt,                  // Issued At
    'jti' => $tokenId,                   // JWT ID

    // Custom claims
    'companyId' => 'abc123-demo',
    'roles' => ['admin']
];

// Encode the JWT token
$jwt = encodeJwt($header, $payload, 'quickserve-jwt-secret-dev');

echo "Generated JWT token:\n";
echo $jwt . "\n";

/**
 * Encode a JWT token
 *
 * @param array $header Header
 * @param array $payload Payload
 * @param string $secret Secret
 * @return string JWT token
 */
function encodeJwt($header, $payload, $secret)
{
    $headerEncoded = base64UrlEncode(json_encode($header));
    $payloadEncoded = base64UrlEncode(json_encode($payload));
    
    $signature = hash_hmac('sha256', "$headerEncoded.$payloadEncoded", $secret, true);
    $signatureEncoded = base64UrlEncode($signature);
    
    return "$headerEncoded.$payloadEncoded.$signatureEncoded";
}

/**
 * Base64 URL encode
 *
 * @param string $data Data to encode
 * @return string Base64 URL encoded data
 */
function base64UrlEncode($data)
{
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

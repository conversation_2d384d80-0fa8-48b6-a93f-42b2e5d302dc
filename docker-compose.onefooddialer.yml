version: '3.8'

services:
  # Infrastructure Services
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: onefooddialer-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: onefooddialer
      MYSQL_USER: demo
      MYSQL_PASSWORD: demo
      MYSQL_ROOT_PASSWORD: rootpassword
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - app-network

  # PostgreSQL for Kong
  postgres:
    image: postgres:13
    container_name: onefooddialer-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: kong
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: kongpass
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "kong"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - app-network

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.11-management
    container_name: onefooddialer-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5673:5672"
      - "15673:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: onefooddialer-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - app-network

  # Keycloak Authentication
  keycloak:
    image: quay.io/keycloak/keycloak:21.0
    container_name: onefooddialer-keycloak
    restart: unless-stopped
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
      KC_DB: mysql
      KC_DB_URL: *************************************
      KC_DB_USERNAME: demo
      KC_DB_PASSWORD: demo
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
      KC_FEATURES: preview
      KC_HTTP_RELATIVE_PATH: /auth
    command:
      - start-dev
      - --import-realm
    volumes:
      - ./keycloak/realm-export.json:/opt/keycloak/data/import/realm.json
    ports:
      - "8080:8080"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/auth/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kong Migration
  kong-migration:
    image: kong:3.4.0
    container_name: onefooddialer-kong-migration
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: postgres
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kongpass
      KONG_PG_DATABASE: kong
    command: kong migrations bootstrap
    networks:
      - app-network
    restart: on-failure

  # Kong API Gateway
  kong-gateway:
    image: kong:3.4.0
    container_name: onefooddialer-kong-gateway
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      kong-migration:
        condition: service_completed_successfully
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: postgres
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kongpass
      KONG_PG_DATABASE: kong
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_DECLARATIVE_CONFIG: /etc/kong/kong.yml
    ports:
      - "8000:8000"  # Kong proxy
      - "8001:8001"  # Kong admin API
    volumes:
      - ./kong/kong.yml:/etc/kong/kong.yml
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 10s
      retries: 5

  # Laravel 12 Microservices
  # Auth Service
  auth-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: auth-service
    container_name: onefooddialer-auth-service
    restart: unless-stopped
    volumes:
      - ./services/auth-service-v12:/var/www/html
    ports:
      - "8101:8101"
    command: php artisan serve --host=0.0.0.0 --port=8101
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      KEYCLOAK_URL: http://keycloak:8080
      KEYCLOAK_REALM: demo
      KEYCLOAK_CLIENT_ID: oneapp
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8101/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # QuickServe Service (Core Business Logic)
  quickserve-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: quickserve-service
    container_name: onefooddialer-quickserve-service
    restart: unless-stopped
    volumes:
      - ./services/quickserve-service-v12:/var/www/html
    ports:
      - "8102:8102"
    command: php artisan serve --host=0.0.0.0 --port=8102
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      CUSTOMER_SERVICE_URL: http://customer-service-v12:8103
      PAYMENT_SERVICE_URL: http://payment-service-v12:8104
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8102/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Customer Service
  customer-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: customer-service
    container_name: onefooddialer-customer-service
    restart: unless-stopped
    volumes:
      - ./services/customer-service-v12:/var/www/html
    ports:
      - "8103:8103"
    command: php artisan serve --host=0.0.0.0 --port=8103
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8103/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Payment Service
  payment-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: payment-service
    container_name: onefooddialer-payment-service
    restart: unless-stopped
    volumes:
      - ./services/payment-service-v12:/var/www/html
    ports:
      - "8104:8104"
    command: php artisan serve --host=0.0.0.0 --port=8104
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      CUSTOMER_SERVICE_URL: http://customer-service-v12:8103
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8104/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kitchen Service
  kitchen-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: kitchen-service
    container_name: onefooddialer-kitchen-service
    restart: unless-stopped
    volumes:
      - ./services/kitchen-service-v12:/var/www/html
    ports:
      - "8105:8105"
    command: php artisan serve --host=0.0.0.0 --port=8105
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      QUICKSERVE_SERVICE_URL: http://quickserve-service-v12:8102
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8105/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Delivery Service
  delivery-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: delivery-service
    container_name: onefooddialer-delivery-service
    restart: unless-stopped
    volumes:
      - ./services/delivery-service-v12:/var/www/html
    ports:
      - "8106:8106"
    command: php artisan serve --host=0.0.0.0 --port=8106
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      QUICKSERVE_SERVICE_URL: http://quickserve-service-v12:8102
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8106/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Analytics Service
  analytics-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: analytics-service
    container_name: onefooddialer-analytics-service
    restart: unless-stopped
    volumes:
      - ./services/analytics-service-v12:/var/www/html
    ports:
      - "8107:8107"
    command: php artisan serve --host=0.0.0.0 --port=8107
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8107/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Admin Service
  admin-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: admin-service
    container_name: onefooddialer-admin-service
    restart: unless-stopped
    volumes:
      - ./services/admin-service-v12:/var/www/html
    ports:
      - "8108:8108"
    command: php artisan serve --host=0.0.0.0 --port=8108
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8108/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Notification Service
  notification-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: notification-service
    container_name: onefooddialer-notification-service
    restart: unless-stopped
    volumes:
      - ./services/notification-service-v12:/var/www/html
    ports:
      - "8109:8109"
    command: php artisan serve --host=0.0.0.0 --port=8109
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      MAIL_HOST: mailhog
      MAIL_PORT: 1025
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      mailhog:
        condition: service_started
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8109/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Catalogue Service
  catalogue-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: catalogue-service
    container_name: onefooddialer-catalogue-service
    restart: unless-stopped
    volumes:
      - ./services/catalogue-service-v12:/var/www/html
    ports:
      - "8110:8110"
    command: php artisan serve --host=0.0.0.0 --port=8110
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8110/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Meal Service
  meal-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: meal-service
    container_name: onefooddialer-meal-service
    restart: unless-stopped
    volumes:
      - ./services/meal-service-v12:/var/www/html
    ports:
      - "8112:8112"
    command: php artisan serve --host=0.0.0.0 --port=8112
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      CATALOGUE_SERVICE_URL: http://catalogue-service-v12:8110
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8112/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Misscall Service
  misscall-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: misscall-service
    container_name: onefooddialer-misscall-service
    restart: unless-stopped
    volumes:
      - ./services/misscall-service-v12:/var/www/html
    ports:
      - "8113:8113"
    command: php artisan serve --host=0.0.0.0 --port=8113
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      CUSTOMER_SERVICE_URL: http://customer-service-v12:8103
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8113/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Subscription Service
  subscription-service-v12:
    build:
      context: .
      dockerfile: Dockerfile
      target: subscription-service
    container_name: onefooddialer-subscription-service
    restart: unless-stopped
    volumes:
      - ./services/subscription-service-v12:/var/www/html
    ports:
      - "8111:8111"
    command: php artisan serve --host=0.0.0.0 --port=8111
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: onefooddialer
      DB_USERNAME: demo
      DB_PASSWORD: demo
      AUTH_SERVICE_URL: http://auth-service-v12:8101
      CUSTOMER_SERVICE_URL: http://customer-service-v12:8103
      PAYMENT_SERVICE_URL: http://payment-service-v12:8104
      MEAL_SERVICE_URL: http://meal-service-v12:8112
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest
    depends_on:
      mysql:
        condition: service_healthy
      auth-service-v12:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8111/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Next.js Frontend
  tenant-frontend:
    build:
      context: ./frontend-shadcn
      dockerfile: Dockerfile
    container_name: onefooddialer-tenant-frontend
    restart: unless-stopped
    working_dir: /app
    volumes:
      - ./frontend-shadcn:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    command: sh -c "npm install && npm run dev -- --hostname 0.0.0.0 --port 3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000/v2
      NEXT_PUBLIC_KEYCLOAK_URL: http://localhost:8080/auth/realms/demo
      NEXT_PUBLIC_KEYCLOAK_CLIENT_ID: oneapp
      NODE_ENV: development
    depends_on:
      kong-gateway:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring and Observability Stack
  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: onefooddialer-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./observability/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./observability/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=30d'
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Grafana for visualization
  grafana:
    image: grafana/grafana:10.0.0
    container_name: onefooddialer-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./observability/grafana/provisioning:/etc/grafana/provisioning
      - ./observability/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - GF_FEATURE_TOGGLES_ENABLE=publicDashboards
    depends_on:
      - prometheus
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Alertmanager for alerting
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: onefooddialer-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./observability/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9093/-/healthy"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: onefooddialer-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - app-network

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: onefooddialer-cadvisor
    restart: unless-stopped
    ports:
      - "8081:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - app-network

  # ELK Stack for Centralized Logging
  # Elasticsearch for log storage
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: onefooddialer-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Logstash for log processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: onefooddialer-logstash
    restart: unless-stopped
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    volumes:
      - ./observability/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
      - ./observability/logstash/pipeline:/usr/share/logstash/pipeline
    environment:
      LS_JAVA_OPTS: "-Xmx256m -Xms256m"
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - app-network

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: onefooddialer-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=kibana
      - SERVER_HOST=0.0.0.0
    volumes:
      - ./observability/kibana/kibana.yml:/usr/share/kibana/config/kibana.yml
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Filebeat for log shipping
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: onefooddialer-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./observability/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/log:/var/log:ro
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - app-network

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: onefooddialer-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:16686/"]
      interval: 10s
      timeout: 5s
      retries: 3

networks:
  app-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  postgres_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  elasticsearch_data:
    driver: local

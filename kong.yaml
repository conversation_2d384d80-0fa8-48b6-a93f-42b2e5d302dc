_format_version: "2.1"
_transform: true

services:
  # Auth Service
  - name: auth-service-v12
    url: http://auth-service-v12:8001
    routes:
      - name: auth-api-v2
        paths:
          - /v2/auth
        strip_path: false
        plugins:
          - name: rate-limiting
            config:
              second: 10
              minute: 100
              hour: 1000
              policy: local
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - GET
                - POST
                - PUT
                - DELETE
                - OPTIONS
              headers:
                - Accept
                - Accept-Version
                - Content-Length
                - Content-MD5
                - Content-Type
                - Date
                - X-Auth-Token
                - Authorization
                - X-Correlation-ID
                - X-Request-Time
              exposed_headers:
                - X-Auth-Token
                - X-Correlation-ID
              credentials: true
              max_age: 3600

  # Customer Service
  - name: customer-service-v12
    url: http://customer-service-v12:8002
    routes:
      - name: customer-api-v2
        paths:
          - /v2/customers
        strip_path: false
        plugins:
          - name: jwt
            config:
              secret_is_base64: false
              key_claim_name: iss
              claims_to_verify:
                - exp
                - nbf
          - name: rate-limiting
            config:
              second: 10
              minute: 100
              hour: 1000
              policy: local
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - GET
                - POST
                - PUT
                - DELETE
                - OPTIONS
              headers:
                - Accept
                - Accept-Version
                - Content-Length
                - Content-MD5
                - Content-Type
                - Date
                - X-Auth-Token
                - Authorization
                - X-Correlation-ID
              exposed_headers:
                - X-Auth-Token
                - X-Correlation-ID
              credentials: true
              max_age: 3600

  # Payment Service
  - name: payment-service-v12
    url: http://payment-service-v12:8003
    routes:
      - name: payment-api-v2
        paths:
          - /v2/payments
        strip_path: false
        plugins:
          - name: jwt
            config:
              secret_is_base64: false
              key_claim_name: iss
              claims_to_verify:
                - exp
                - nbf
          - name: rate-limiting
            config:
              second: 10
              minute: 100
              hour: 1000
              policy: local
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - GET
                - POST
                - PUT
                - DELETE
                - OPTIONS
              headers:
                - Accept
                - Accept-Version
                - Content-Length
                - Content-MD5
                - Content-Type
                - Date
                - X-Auth-Token
                - Authorization
                - X-Correlation-ID
              exposed_headers:
                - X-Auth-Token
                - X-Correlation-ID
              credentials: true
              max_age: 3600

  # QuickServe Service (Orders)
  - name: quickserve-service-v12
    url: http://quickserve-service-v12:8004
    routes:
      - name: orders-api-v2
        paths:
          - /v2/orders
        strip_path: false
        plugins:
          - name: jwt
            config:
              secret_is_base64: false
              key_claim_name: iss
              claims_to_verify:
                - exp
                - nbf
          - name: rate-limiting
            config:
              second: 10
              minute: 100
              hour: 1000
              policy: local
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - GET
                - POST
                - PUT
                - DELETE
                - OPTIONS
              headers:
                - Accept
                - Accept-Version
                - Content-Length
                - Content-MD5
                - Content-Type
                - Date
                - X-Auth-Token
                - Authorization
                - X-Correlation-ID
              exposed_headers:
                - X-Auth-Token
                - X-Correlation-ID
              credentials: true
              max_age: 3600

  # Kitchen Service
  - name: kitchen-service-v12
    url: http://kitchen-service-v12:8005
    routes:
      - name: kitchen-api-v2
        paths:
          - /v2/kitchens
        strip_path: false
        plugins:
          - name: jwt
            config:
              secret_is_base64: false
              key_claim_name: iss
              claims_to_verify:
                - exp
                - nbf
          - name: rate-limiting
            config:
              second: 10
              minute: 100
              hour: 1000
              policy: local
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - GET
                - POST
                - PUT
                - DELETE
                - OPTIONS
              headers:
                - Accept
                - Accept-Version
                - Content-Length
                - Content-MD5
                - Content-Type
                - Date
                - X-Auth-Token
                - Authorization
                - X-Correlation-ID
              exposed_headers:
                - X-Auth-Token
                - X-Correlation-ID
              credentials: true
              max_age: 3600

  # Delivery Service
  - name: delivery-service-v12
    url: http://delivery-service-v12:8006
    routes:
      - name: delivery-api-v2
        paths:
          - /v2/delivery
        strip_path: false
        plugins:
          - name: jwt
            config:
              secret_is_base64: false
              key_claim_name: iss
              claims_to_verify:
                - exp
                - nbf
          - name: rate-limiting
            config:
              second: 10
              minute: 100
              hour: 1000
              policy: local
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - GET
                - POST
                - PUT
                - DELETE
                - OPTIONS
              headers:
                - Accept
                - Accept-Version
                - Content-Length
                - Content-MD5
                - Content-Type
                - Date
                - X-Auth-Token
                - Authorization
                - X-Correlation-ID
              exposed_headers:
                - X-Auth-Token
                - X-Correlation-ID
              credentials: true
              max_age: 3600

  # Analytics Service
  - name: analytics-service-v12
    url: http://analytics-service-v12:8007
    routes:
      - name: analytics-api-v2
        paths:
          - /v2/analytics
        strip_path: false
        plugins:
          - name: jwt
            config:
              secret_is_base64: false
              key_claim_name: iss
              claims_to_verify:
                - exp
                - nbf
          - name: rate-limiting
            config:
              second: 5
              minute: 50
              hour: 500
              policy: local
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - GET
                - POST
                - PUT
                - DELETE
                - OPTIONS
              headers:
                - Accept
                - Accept-Version
                - Content-Length
                - Content-MD5
                - Content-Type
                - Date
                - X-Auth-Token
                - Authorization
                - X-Correlation-ID
              exposed_headers:
                - X-Auth-Token
                - X-Correlation-ID
              credentials: true
              max_age: 3600
